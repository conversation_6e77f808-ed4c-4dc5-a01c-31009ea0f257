# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# <PERSON><PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> Bochaca <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# Arnau R<PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# AncesLatino2004, 2022
# Jonatan Gk, 2022
# marc<PERSON>, 2022
# Harcogourmet, 2022
# oscaryuu, 2022
# CristianCruz<PERSON>arra, 2022
# jabe<PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Ivan Espinola, 2023\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project
#. openerp-web
#: code:addons/project/static/src/project_sharing/components/chatter.xml:0
#, python-format
msgid "!!widget.options.res_id && widget.get('messages') || []"
msgstr "!!widget.options.res_id && widget.get('messages') || []"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"Refused\""
msgstr "\"Rebutjat\""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"This Month\""
msgstr "\"Aquest mes\""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"This Week\""
msgstr "\"Aquesta setmana\""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"Today\""
msgstr "\"Avui\""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_count
msgid "# Collaborators"
msgstr "# Col·laboradors"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr "# Valoracions"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_partner__task_count
#: model:ir.model.fields,field_description:project.field_res_users__task_count
msgid "# Tasks"
msgstr "# Tasques"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__nb_tasks
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
msgid "# of Tasks"
msgstr "# de tasques"

#. module: project
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (còpia)"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "%s Use the %s icon to organize your daily activities."
msgstr ""
"%s Utilitzeu la icona %s per organitzar les vostres activitats diàries."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "(+ %(child_count)s tasks)"
msgstr "(+ %(child_count)s tasques)"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "(+ 1 task)"
msgstr "(+ 1 tasca)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr "(venciment"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr "(última actualització del projecte),"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr "- s'ha arribat a"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__10
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__10
msgid "10"
msgstr "10"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__11
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__11
msgid "11"
msgstr "11"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__12
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__12
msgid "12"
msgstr "12"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__13
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__13
msgid "13"
msgstr "13"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__14
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__14
msgid "14"
msgstr "14"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__15
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__15
msgid "15"
msgstr "15"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__16
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__16
msgid "16"
msgstr "16"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__17
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__17
msgid "17"
msgstr "17"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__18
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__18
msgid "18"
msgstr "18"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__19
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__19
msgid "19"
msgstr "19"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__20
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__20
msgid "20"
msgstr "20"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__21
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__21
msgid "21"
msgstr "21"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__22
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__22
msgid "22"
msgstr "22"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__23
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__23
msgid "23"
msgstr "23"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__24
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__24
msgid "24"
msgstr "24"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__25
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__25
msgid "25"
msgstr "25"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__26
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__26
msgid "26"
msgstr "26"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__27
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__27
msgid "27"
msgstr "27"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__28
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__28
msgid "28"
msgstr "28"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__29
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__29
msgid "29"
msgstr "29"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__30
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__30
msgid "30"
msgstr "30"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__31
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__31
msgid "31"
msgstr "31"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr ""
"<b>Arrossega i deixa anar</b> la targeta per canviar la tasca des de "
"l'escenari."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this task won't be notified \n"
"    of the note you are logging unless you specifically tag them)</i>. Use @ <b>mentions</b> to ping a colleague \n"
"    or # <b>mentions</b> to reach an entire team."
msgstr ""
"<b>Registre notes</b> per a comunicacions internes <i> (les persones que segueixen aquesta tasca no rebran notificacions \n"
"    de la nota que esteu registrant tret que les etiqueteu específicament)</i>. Utilitzeu @ a les <b> mencions </b> per fer ping a un company \n"
"    or # a les <b>mencions</b> per arribar a tot un equip."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid ""
"<br/>\n"
"                                            <span class=\"fa fa-lock text-muted\"/><span class=\"text-muted\"> Private</span>"
msgstr ""
"<br/>\n"
"                                            <span class=\"fa fa-lock text-muted\"/><span class=\"text-muted\"> Privat</span>"

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object.rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object.rating_get_partner_id()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br/><br/>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the task \"<strong t-out=\"object.name or ''\">Planning and budget</strong>\"\n"
"            <t t-if=\"object.rating_get_rated_partner_id().name\">\n"
"                assigned to <strong t-out=\"object.rating_get_rated_partner_id().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Tell us how you feel about our service</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us to improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your task has been moved to the stage <b t-out=\"object.stage_id.name or ''\">In progress</b></span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">Weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object.rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object.rating_get_partner_id()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hola<t t-out=\"partner.name or ''\"> Brandon Freeman,</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hola,<br/><br/>\n"
"            </t>\n"
"            Preneu-vos un moment per valorar els nostres serveis relacionats amb la tasca\"<strong t-out=\"object.name or ''\">Planificació i pressupost</strong>\"\n"
"            <t t-if=\"object.rating_get_rated_partner_id().name\">\n"
"                assignat a <strong t-out=\"object.rating_get_rated_partner_id().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Explica'ns com et sembla el nostre servei</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(feu clic a un d'aquests emoticones)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"           Agraïm els vostres comentaris. Ens ajuda a millorar contínuament.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">Aquesta enquesta als clients s'ha enviat perquè la vostra tasca s'ha mogut a l'etapa <b t-out=\"object.stage_id.name or ''\">En progrés</b></span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">S'envia aquesta enquesta als clients <b t-out=\"object.project_id.rating_status_period or ''\">Setmanalment</b> sempre que la tasca estigui en l'etapa <b t-out=\"object.stage_id.name or ''\">En progrés</b> </span>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/>\n"
"    Thank you for your enquiry.<br/>\n"
"    If you have any questions, please let us know.\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    Benvolgut<t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/>\n"
"   Gràcies per la vostra consulta.<br/>\n"
"    Si teniu cap pregunta, feu-nos-ho saber.\n"
"    <br/><br/>\n"
"    Gràcies,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"Arrow\" attrs=\"{'invisible': [('date_start', '=', False), ('date', '=', False)]}\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"Arrow\" attrs=\"{'invisible': [('date_start', '=', False), ('date', '=', False)]}\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"Percentage of satisfaction\"/>"
msgstr ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"Percentage of satisfaction\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Valoracions del client</b>estan inhabilitats en els projecte(s) següents : "
"<br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"o_project_task_project_field text-danger\" attrs=\"{'invisible': "
"[('project_id', '!=', False)]}\">Private</i>"
msgstr ""
"<i class=\"o_project_task_project_field text-danger\" attrs=\"{'invisible': "
"[('project_id', '!=', False)]}\">Privat</i>"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "<p><em>Number of tasks: %(tasks_count)s</em></p>"
msgstr "<p><em>Nombre de tasques:%(tasks_count)s</em></p>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-right\">Stage:</small>"
msgstr "<small class=\"text-right\">Escena:</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<span attrs=\"{'invisible': ['|', ('repeat_show_week', '=', False), "
"('repeat_show_month', '=', False)]}\">of</span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('repeat_show_week', '=', False), "
"('repeat_show_month', '=', False)]}\">de</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o mr-2\" title=\"Dates\"/>"
msgstr "<span class=\"fa fa-clock-o mr-2\" title=\"Dates\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o mr-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""
"<span class=\"fa fa-envelope-o mr-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user mr-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr "<span class=\"fa fa-user mr-2\" aria-label=\"Partner\" title=\"Partner\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Collaborators\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Col·laboradors\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Customer Satisfaction\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Satisfacció del client\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Milestones\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Fites\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Burndown Chart\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Gràfic de Burndown\n"
"                            </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Blocking</span>"
msgstr "<span class=\"o_stat_text\">Bloqueig</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr "<span class=\"o_stat_text\">El marge brut</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">in Recurrence</span>"
msgstr "<span class=\"o_stat_text\">en Recurrència</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"oe_read_only\" attrs=\"{'invisible': [('alias_name', '!=', False)]}\">Create tasks by sending an email to </span>\n"
"                                        <span class=\"font-weight-bold oe_read_only\" attrs=\"{'invisible': [('alias_name', '=', False)]}\">Create tasks by sending an email to </span>\n"
"                                        <span class=\"font-weight-bold oe_edit_only\">Create tasks by sending an email to </span>"
msgstr ""
"<span class=\"oe_read_only\" attrs=\"{'invisible': [('alias_name', '!=', False)]}\">Crea tasques mitjançant l'enviament d'un correu electrònic a </span>\n"
"                                        <span class=\"font-weight-bold oe_read_only\" attrs=\"{'invisible': [('alias_name', '=', False)]}\">Crea tasques mitjançant l'enviament d'un correu electrònic a</span>\n"
"                                        <span class=\"font-weight-bold oe_edit_only\">Crea tasques mitjançant l'enviament d'un correu electrònic a </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "<span>Are you sure you want to delete this project ?</span>"
msgstr "<span>¿Està segur que vol eliminar aquest projecte?</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Informes</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>Vista</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid ""
"<span>You cannot delete a project containing tasks. You can either archive "
"it or first delete all of its tasks.</span>"
msgstr ""
"<span>No podeu suprimir un projecte que conté tasques. Podeu arxivar-lo o "
"suprimir totes les seves tasques.</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong class=\"d-block mb-2\">Attachments</strong>"
msgstr "<strong class=\"d-block mb-2\">Annexos</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Assignees</strong>"
msgstr "<strong>Assignats</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Customer</strong>"
msgstr "<strong>Client</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>Termini:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Description</strong>"
msgstr "<strong>Descripció</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Message and communication history</strong>"
msgstr "<strong>Historial de missatges i comunicacions</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_planned_hours_template
msgid "<strong>Planned Hours:</strong>"
msgstr "<strong>Hores planificades:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>Projecte:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr "<u>Fites</u>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr "=&gt;"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Diccionari Python que s'avaluarà per a proporcionar valors per defecte quan "
"es creïn nous registres per aquest pseudònim."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr ""
"No es pot seleccionar un col·laborador més d'una vegada a l'accés al "
"projecte de compartició. Si us plau suprimiu els duplicat(s) i torneu-ho a "
"provar."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr ""
"Una etapa personal no pot enllaçar-se a un projecte perquè només és visible "
"al seu usuari corresponent."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr "Una tasca només pot tenir una única etapa personal per usuari."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "Acceptar emails de"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_mode
msgid "Access Mode"
msgstr "Mode d'accés"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "Advertència d'accés"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "Cal fer alguna acció"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__active
#: model:ir.model.fields,field_description:project.field_project_project_stage__active
#: model:ir.model.fields,field_description:project.field_project_task__active
#: model:ir.model.fields,field_description:project.field_project_task_type__active
msgid "Active"
msgstr "Actiu"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "Activitats"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoració de l'activitat d'excepció"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipus d'activitats"

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#, python-format
msgid "Add Milestone"
msgstr "Afegir fita"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Add a description..."
msgstr "Afegir una descripció..."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add a note"
msgstr "Afegir una nota"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr ""
"Afegiu columnes per organitzar les vostres tasques <b>etapes</b> <i>p.e. Nou"
" - En curs - Fet</i>."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add contacts to share the project..."
msgstr "Afegeix contactes per compartir el projecte..."

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "Afegir contingut addicional per mostrar al correu electrònic"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Add your task once it is ready."
msgstr "Afegeix la teva tasca una vegada estigui llesta."

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "Administrador"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Agile Scrum"
msgstr "Scrum Àgil"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "Pseudònim"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "Pseudònim del contacte de seguretat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "Pseudònim"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias domain"
msgstr "Pseudònim del domini"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_value
msgid "Alias email"
msgstr "Correu electrònic de l'àlies"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "Model amb pseudònim"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Tots"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All employees"
msgstr "Tots els empleats"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__all
msgid "All tasks"
msgstr "Totes les tasques"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allow_subtasks
msgid "Allow Sub-tasks"
msgstr "Permet les subtasques"

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_id
#: model:ir.model.fields,field_description:project.field_project_task__analytic_account_id
msgid "Analytic Account"
msgstr "Compte analític"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__analytic_tag_ids
msgid "Analytic Tag"
msgstr "Etiqueta analítica"

#. module: project
#: model:ir.model,name:project.model_account_analytic_tag
msgid "Analytic Tags"
msgstr "Etiquetes analítiques"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__analytic_account_id
#: model:ir.model.fields,help:project.field_project_task__project_analytic_account_id
msgid ""
"Analytic account to which this project is linked for financial management. "
"Use an analytic account to record cost and revenue on your project."
msgstr ""
"Compte analític al que està vinculat aquest projecte per a gestió financera."
" Utilitzeu un compte analític per a enregistrar costos i ingressos del "
"projecte."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__analytic_account_id
msgid ""
"Analytic account to which this task is linked for financial management. Use "
"an analytic account to record cost and revenue on your task. If empty, the "
"analytic account of the project will be used."
msgstr ""
"Compte analític al qual aquesta tasca està vinculada per a la gestió "
"financera. Utilitzeu un compte analític per registrar el cost i els "
"ingressos de la vostra tasca. Si està buit, s'utilitzarà el compte analític "
"del projecte."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "Analitics"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "Analyze the performance of your tasks and your workers."
msgstr ""
"Analitza el rendiment de les vostres tasques i els vostres treballadors."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__april
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__april
msgid "April"
msgstr "abril"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Archive"
msgstr "Arxivar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr "Etapes de l'arxiu"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "Archive project"
msgstr "Arxiva el projecte"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Archived"
msgstr "Arxivat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "¿Estàs segur que vols continuar?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete those stages ?"
msgstr "¿Estàs segur que vols eliminar aquestes etapes?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr "Fletxa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr "Icona de la fletxa"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Assembling"
msgstr "Muntatge de"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Assign to Me"
msgstr "Assignar a mi"

#. module: project
#: model_terms:ir.ui.view,help:project.project_sharing_project_task_view_kanban
msgid " assignee"
msgstr "assignat"

#. module: project
#: model_terms:ir.ui.view,help:project.project_sharing_project_task_view_kanban
msgid " assignees"
msgstr "assignats"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Assigned"
msgstr "Assignat"

#. module: project
#: model:ir.actions.act_window,name:project.act_res_users_2_project_task_opened
msgid "Assigned Tasks"
msgstr "Tasques assignades"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "Assignat a"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Assignees"
msgstr "Assignats"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "Data d'assignació"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "Data d'assignació"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
msgid "At Risk"
msgstr "En risc"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"At each stage, employees can block tasks or mark them as ready for the next step.\n"
"                                    You can customize here the labels for each state."
msgstr ""
"En cada etapa els empleats poden bloquejar tasques o marcar-les com a preparades per al següent pas.\n"
"                                    Aquí podeu personalitzar les etiquetes de cada estat."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message."
msgstr "Adjunts que no venen d'un missatge."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__august
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__august
msgid "August"
msgstr "agost"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "Autor"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr "Genera tasques automàticament per a activitats regulars"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_kanban_state
msgid "Automatic kanban status"
msgstr "Estat del kanban automàtic"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_kanban_state
msgid ""
"Automatically modify the kanban state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the kanban state to 'ready for the new stage' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'blocked' (red bullet).\n"
msgstr ""
"Automatically modify the kanban state when the customer replies to the feedback for this stage.\n"
"* Good feedback from the customer will update the kanban state to 'ready for the new stage' (green bullet).\n"
"* Neutral or bad feedback will set the kanban state to 'blocked' (red bullet).\n"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Backlog"
msgstr "Cartera"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "Saldo"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__dependent_ids
msgid "Block"
msgstr "Bloquejar"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__blocked
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__blocked
#: model:project.task,legend_blocked:project.project_task_1
#: model:project.task,legend_blocked:project.project_task_12
#: model:project.task,legend_blocked:project.project_task_2
#: model:project.task,legend_blocked:project.project_task_20
#: model:project.task,legend_blocked:project.project_task_24
#: model:project.task,legend_blocked:project.project_task_25
#: model:project.task,legend_blocked:project.project_task_26
#: model:project.task,legend_blocked:project.project_task_3
#: model:project.task,legend_blocked:project.project_task_30
#: model:project.task,legend_blocked:project.project_task_31
#: model:project.task,legend_blocked:project.project_task_32
#: model:project.task,legend_blocked:project.project_task_33
#: model:project.task,legend_blocked:project.project_task_8
#: model:project.task,legend_blocked:project.project_task_9
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_0
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_1
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_2
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_3
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_4
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_5
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_6
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_0
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_1
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_2
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_3
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_4
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_5
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_6
#: model:project.task.type,legend_blocked:project.project_stage_0
#: model:project.task.type,legend_blocked:project.project_stage_2
#: model:project.task.type,legend_blocked:project.project_stage_3
#, python-format
msgid "Blocked"
msgstr "Bloquejat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr "Bloquejat per"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Brainstorm"
msgstr "Tempesta"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_pivot
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Burndown Chart"
msgstr "Gràfic de Burndown"

#. module: project
#: model:project.task,legend_done:project.project_task_10
#: model:project.task,legend_done:project.project_task_11
#: model:project.task,legend_done:project.project_task_19
#: model:project.task,legend_done:project.project_task_21
#: model:project.task,legend_done:project.project_task_22
#: model:project.task,legend_done:project.project_task_27
#: model:project.task,legend_done:project.project_task_28
#: model:project.task,legend_done:project.project_task_29
#: model:project.task,legend_done:project.project_task_34
#: model:project.task,legend_done:project.project_task_35
#: model:project.task,legend_done:project.project_task_36
#: model:project.task,legend_done:project.project_task_4
#: model:project.task,legend_done:project.project_task_5
#: model:project.task,legend_done:project.project_task_6
#: model:project.task,legend_done:project.project_task_7
#: model:project.task.type,legend_done:project.project_stage_1
msgid "Buzz or set as done"
msgstr "Avisar o marcar com a fet"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Cancel"
msgstr "Cancel·lar"

#. module: project
#: code:addons/project/models/project.py:0
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#, python-format
msgid "Canceled"
msgstr "Cancel·lat"

#. module: project
#: model:project.task.type,name:project.project_stage_3
msgid "Cancelled"
msgstr "Cancel·lat"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__partner_is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr ""
"Marqueu si el contacte és una empresa. En cas contrari, serà una persona."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__child_text
msgid "Child Text"
msgstr "Text fill"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the name of a customer,\n"
"     of a product, of a team, of a construction site, etc.</i>"
msgstr ""
"Tria a <b>nom</b> per al teu projecte. <i>Pot ser qualsevol cosa que vulguis: el nom d'un client,\n"
"    d'un producte, d'un equip, d'una obra, etc.</i>"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr ""
"Trieu una tasca <b>nom</b> <i>(per exemple, Disseny de lloc web, Compra "
"bons...)</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__partner_city
msgid "City"
msgstr "Ciutat"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Client Review"
msgstr "Revisió del client"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_closed
#: model:ir.model.fields,field_description:project.field_project_task_type__is_closed
msgid "Closing Stage"
msgstr "Etapa de tancament"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
msgid "Collaborator"
msgstr "Col·laborador"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_ids
#, python-format
msgid "Collaborators"
msgstr "Col·laboradors"

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "Col·laboradors en projectes compartits"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
#: model:ir.model.fields,field_description:project.field_project_update__color
msgid "Color"
msgstr "Color"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__color
#: model:ir.model.fields,field_description:project.field_project_task__color
msgid "Color Index"
msgstr "Índex de color"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__commercial_partner_id
#: model:ir.model.fields,field_description:project.field_project_task__commercial_partner_id
msgid "Commercial Entity"
msgstr "Entitat Comercial"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo "
"designs to the task, so that information flows from designers to the workers"
" who print the t-shirt. Organize priorities amongst orders using the %s "
"icon. %s"
msgstr ""
"Comuniqui's amb els clients sobre la tasca utilitzant el portal de correu "
"electrònic. Adjunti els dissenys dels logotips a la tasca, perquè la "
"informació flueixi des dels dissenyadors fins als treballadors que "
"imprimeixen la samarreta. Organitzar les prioritats entre les ordres "
"utilitzant el %sicona.%s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
msgid "Company"
msgstr "Empresa"

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustos de configuració"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "Configuració"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr "Configurar Etapes"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Confirm"
msgstr "Confirmar"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#: code:addons/project/wizard/project_task_type_delete.py:0
#, python-format
msgid "Confirmation"
msgstr "Confirmació"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Consulting"
msgstr "Consultoria"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "Contacte"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Continue Recurrence"
msgstr "Continuar recurrència"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "Redacció de textos publicitaris"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "Imatge de portada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create"
msgstr "Crear"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "Data de creació"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr "Crear un projecte"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr "Crear una nova etapa en la cadena de tasques"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr "Crea tasques mitjançant l'enviament d'un correu electrònic a"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "Creat el"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "Creat el"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Creation Date"
msgstr "Data de creació"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr "Projecte actual de la tasca"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current stage of the task"
msgstr "Etapa actual de la tasca"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "Etapa actual d'aquesta tasca"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Missatge personalitzat de rebot"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Customer"
msgstr "Client"

#. module: project
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "Customer Email"
msgstr "Correu del client"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Customer Feedback"
msgstr "Comentaris del client"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "URL del portal dels clients"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr "Valoracions del client"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr "Estat de les valoracions del client"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
msgid "Customer Ratings on Task"
msgstr "Valoracions del client a la tasca"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Customer Satisfaction"
msgstr "Satisfacció del client"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and "
"you can communicate on the task directly. Your managers decide which "
"feedback is accepted %s and which feedback is moved to the %s column. %s"
msgstr ""
"Els clients proposen comentaris per correu electrònic; Odoo crea tasques "
"automàticament i us podeu comunicar directament sobre la tasca. Els vostres "
"gestors decideixen quin comentari s'accepta %s i quin feedback es trasllada "
"a la %s columna. %s"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Customize how tasks are named according to the project and create tailor "
"made status messages for each step of the workflow. It helps to document "
"your workflow: what should be done at which step."
msgstr ""
"Personalitzeu com s'anomenen les tasques segons el projecte i creeu "
"missatges d'estat personalitzats per a cada pas del flux de treball. Ajuda a"
" documentar el vostre flux de treball: què s'ha de fer en quin pas."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "Diàriament"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
msgid "Date"
msgstr "Data"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_group_by
msgid "Date Group By"
msgstr "Data de grup per"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_month__date
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_month__date
msgid "Date of the Month"
msgstr "Data del mes"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_year__date
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_year__date
msgid "Date of the Year"
msgstr "Data de l'any"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Dates"
msgstr "Dates"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_weekday
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_weekday
msgid "Day Of The Week"
msgstr "Dia de la setmana"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_month__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_month__day
msgid "Day of the Month"
msgstr "Dia del mes"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_year__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_year__day
msgid "Day of the Year"
msgstr "Dia de l'any"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "Dies"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr "Dies fins avui límit"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Deadline"
msgstr "Data limit"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "Benvolgut/da"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__december
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__december
msgid "December"
msgstr "Desembre"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "Valors per defecte"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"Defineix els passos que s'utilitzaran al project, des de la creació de la "
"tasca fins al tancament d'aquesta o el problema. Utilitzarà aquestes etapes "
"per realitzar un seguiment del progrés en la resolució d'una tasca o un "
"problema."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""
"Persones a les quals serà visible aquest projecte i les seves tasques.\n"
"- Usuaris interns convidats: quan segueixen un projecte, els usuaris interns tindran accés a totes les seves tasques sense distinció. En cas contrari, només tindran accés a les tasques específiques que estan seguint.\n"
" Un usuari amb el nivell de dret d'accés de projecte > administrador encara pot accedir a aquest projecte i a les seves tasques, fins i tot si no formen part explícitament dels seguidors.\n"
"\n"
"- Tots els usuaris interns: tots els usuaris interns poden accedir al projecte i a totes les seves tasques sense distinció.\n"
"- Usuaris del portal convidats i tots els usuaris interns: tots els usuaris interns poden accedir al projecte i a totes les seves tasques sense distinció.\n"
"\n"
"Quan segueixen un projecte, els usuaris del portal tindran accés a totes les seves tasques sense distinció. En cas contrari, només tindran accés a les tasques específiques que estan seguint.\n"
"\n"
"Quan un projecte es comparteix en només lectura, l'usuari del portal es redirigeix ​​al seu portal. Poden veure les tasques, però no editar-les.\n"
"Quan un projecte es comparteix en edició, l'usuari del portal es redirigeix ​​a les vistes de kanban i de llista de les tasques. Poden modificar un nombre seleccionat de camps de les tasques.\n"
"\n"
"En qualsevol cas, un usuari intern sense drets d'accés al projecte encara pot accedir a una tasca, sempre que se li indiqui l'URL corresponent (i que formi part dels seguidors si el projecte és privat)."

#. module: project
#: model:ir.actions.server,name:project.unlink_project_action
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Delete"
msgstr "Eliminar"

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#, python-format
msgid "Delete Milestone"
msgstr "Eliminar la Fita"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "Delete Project"
msgstr "Esborrar Projecte"

#. module: project
#: code:addons/project/models/project.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#, python-format
msgid "Delete Stage"
msgstr "Esborrar Etapa"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Delivered"
msgstr "Lliurat"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__dependent_tasks_count
#, python-format
msgid "Dependent Tasks"
msgstr "Tasques Dependents"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_task_type__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "Descripció"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Design"
msgstr "Disseny"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr "Determina l'ordre en què s'han de realitzar les tasques"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Development"
msgstr "Desenvolupament"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "Digerir"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Digital Marketing"
msgstr "Mercat Digital"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__disabled_rating_warning
msgid "Disabled Rating Warning"
msgstr "Avís de valoració inhabilitat"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_access_mode
msgid "Display Access Mode"
msgstr "Mostra el mode d'accés"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__display_project_id
msgid "Display Project"
msgstr "Mostra el projecte"

#. module: project
#: code:addons/project/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Sense accés, omet aquesta informació per a usuaris amb correu electrònic"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Documents"
msgstr "Documents"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
#, python-format
msgid "Done"
msgstr "Fet"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Draft"
msgstr "Esborrany"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Dropdown menu"
msgstr "Menú desplegable"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "E.g: Product Launch"
msgstr "Per exemple: Llançament de productes"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__edit
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Edit"
msgstr "Editar"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_kanban.js:0
#, python-format
msgid "Edit Personal Stage"
msgstr "modificar l'etapa personal"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Edit recurring task"
msgstr "Modificar tasca recurrent"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Edit tasks' description collaboratively in real time. See each author's text"
" in a distinct color."
msgstr ""
"Editeu la descripció de les tasques de manera col·laborativa en temps real. "
"Vegeu el text de cada autor en un color diferent."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Editing"
msgstr "Edició"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__partner_email
#: model:ir.model.fields,field_description:project.field_project_task__partner_email
msgid "Email"
msgstr "Correu electrònic"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_from
msgid "Email From"
msgstr "Correu electrònic de"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "Plantilla de correu electrònic"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "cc de correu electrònic"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "End Date"
msgstr "Data de finalització"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "Data de finalització"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "Error! Project start date must be before project end date."
msgstr ""
"Error! La data d'inici del projecte ha de ser anterior a la data de "
"finalització del projecte."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr "Error! No es pot crear una jerarquia recursiva de tasques."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Everyone can propose ideas, and the Editor marks the best ones as %s. Attach"
" all documents or links to the task directly, to have all research "
"information centralized. %s"
msgstr ""
"Tothom pot proposar idees, i l'editor marca les millors com %s. Adjunta tots"
" els documents o enllaços directament a la tasca, per tenir tota la "
"informació de la investigació centralitzada. %s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "Data d'expiració "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Extended Filters"
msgstr "Filtres estesos"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "Informació extra"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__february
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__february
msgid "February"
msgstr "febrer"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Fill your Inbox easily with the email gateway. Periodically review your "
"Inbox and schedule tasks by moving them to other columns. Every day, you "
"review the %s column to move important tasks %s. Every Monday, you review "
"the %s column. %s"
msgstr ""
"Ompliu la safata d'entrada fàcilment amb la passarel·la de correu "
"electrònic. Reviseu periòdicament les vostres tasques d'entrada i "
"planificació movent-les a altres columnes. Cada dia, reviseu el %s columna "
"per moure tasques importants %s. Cada dilluns, es revisa la %s columna. %s"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Final Document"
msgstr "Document final"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__first
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__first
msgid "First"
msgstr "Primer"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr "Plegat al Kanban"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"Follow this project to automatically track the events associated to tasks "
"and issues of this project."
msgstr ""
"Seguiu aquest projecte per rastrejar automàticament els esdeveniments "
"associats a les tasques i a les incidències del mateix."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Followed"
msgstr "Seguit"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "Followed Projects"
msgstr "Projectes seguits"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "per sempre"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__fri
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__fri
msgid "Fri"
msgstr "Dv"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__fri
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__fri
msgid "Friday"
msgstr "Divendres"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "Activitats futures"

#. module: project
#: model:ir.ui.menu,name:project.menu_tasks_config
msgid "GTD"
msgstr "GTD"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr ""
"Obteniu una instantània de l'estat del vostre projecte i compartiu-ne el "
"progrés amb les parts interessades clau."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback"
msgstr "Obteniu comentaris dels clients"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Getting Things Done (GTD)"
msgstr "Fes les coses (GTD)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__sequence
msgid "Gives the sequence order when displaying a list of Projects."
msgstr "Indica l'ordre de seqüència quan es mostra una llista de projectes."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__sequence
msgid "Gives the sequence order when displaying a list of tasks."
msgstr "Indica l'ordre quan es mostra la llista de tasques."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_done
msgid "Green Kanban Label"
msgstr "Etiqueta kanban verda"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_normal
msgid "Grey Kanban Label"
msgstr "Etiqueta kanban grisa"

#. module: project
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "Gross Margin"
msgstr "El marge brut"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Group By"
msgstr "Agrupar per"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks. Use the %s and %s to signalize what is the "
"current status of your Idea. %s"
msgstr ""
"Maneja les teves idees reunint amb les tasques dels teus nous projectes i "
"discuteix-ho en la xerrada de tasques. usa el%sy%sper a senyalitzar com és "
"l'estatus actual de la teva idea.%s"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Handoff"
msgstr "Entrega"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr "Cara feliç"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__2
msgid "High"
msgstr "Alta"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "Hores"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"How to get customer feedback?\n"
"- Rating when changing stage: an email will be sent when a task is pulled to another stage.\n"
"- Periodic rating: an email will be sent periodically.\n"
"\n"
"Don't forget to set up the email templates on the stages for which you want to get customer feedback."
msgstr ""
"Com obtenir comentaris dels clients?\n"
"- Valoració en canviar d'etapa: s'enviarà un correu electrònic quan una tasca es traslladi a una altra fase.\n"
"- Valoració periòdica: s'enviarà un correu electrònic periòdicament.\n"
"\n"
"No us oblideu de configurar les plantilles de correu electrònic a les etapes per a les quals voleu rebre comentaris dels clients."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr "Com va aquest projecte?"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "I take it"
msgstr "Ho tinc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "ID"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Id del registre pare que te el pseudònim.(exemple: el projecte que conté el "
"pseudònim per la creació de tasques)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "Idees"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_milestone__message_unread
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_unread
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_unread
#: model:ir.model.fields,help:project.field_project_update__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set and if the project's rating configuration is 'Rating when changing "
"stage', then an email will be sent to the customer when the task reaches "
"this step."
msgstr ""
"Si s'estableix i si la configuració de qualificació del projecte és "
"\"Valoració en canviar d'etapa\", s'enviarà un correu electrònic al client "
"quan la tasca arribi a aquest pas."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be sent to the customer when the project reaches this "
"step."
msgstr ""
"Si s'estableix, s'enviarà un correu electrònic al client quan el projecte "
"arribi a aquest pas."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be sent to the customer when the task or issue reaches"
" this step."
msgstr ""
"Si s'estableix, s'enviarà un correu electrònic al client quan la tasca o "
"problema arribi a aquest pas."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Si està seleccionat, aquest contingut s' enviarà automàticament als usuaris "
"no autoritzats enlloc del missatge per omissió."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__active
msgid ""
"If the active field is set to False, it will allow you to hide the project "
"without removing it."
msgstr ""
"Si el camp actiu es desmarca, permet ocultar el projecte sense eliminar-ho."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
msgid "Important"
msgstr "Important"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__normal
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task,legend_normal:project.project_task_1
#: model:project.task,legend_normal:project.project_task_10
#: model:project.task,legend_normal:project.project_task_11
#: model:project.task,legend_normal:project.project_task_12
#: model:project.task,legend_normal:project.project_task_19
#: model:project.task,legend_normal:project.project_task_2
#: model:project.task,legend_normal:project.project_task_20
#: model:project.task,legend_normal:project.project_task_21
#: model:project.task,legend_normal:project.project_task_22
#: model:project.task,legend_normal:project.project_task_24
#: model:project.task,legend_normal:project.project_task_25
#: model:project.task,legend_normal:project.project_task_26
#: model:project.task,legend_normal:project.project_task_27
#: model:project.task,legend_normal:project.project_task_28
#: model:project.task,legend_normal:project.project_task_29
#: model:project.task,legend_normal:project.project_task_3
#: model:project.task,legend_normal:project.project_task_30
#: model:project.task,legend_normal:project.project_task_31
#: model:project.task,legend_normal:project.project_task_32
#: model:project.task,legend_normal:project.project_task_33
#: model:project.task,legend_normal:project.project_task_34
#: model:project.task,legend_normal:project.project_task_35
#: model:project.task,legend_normal:project.project_task_36
#: model:project.task,legend_normal:project.project_task_4
#: model:project.task,legend_normal:project.project_task_5
#: model:project.task,legend_normal:project.project_task_6
#: model:project.task,legend_normal:project.project_task_7
#: model:project.task,legend_normal:project.project_task_8
#: model:project.task,legend_normal:project.project_task_9
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_0
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_1
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_2
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_3
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_4
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_5
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_6
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_0
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_1
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_2
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_3
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_4
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_5
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_6
#: model:project.task.type,legend_normal:project.project_stage_0
#: model:project.task.type,legend_normal:project.project_stage_1
#: model:project.task.type,legend_normal:project.project_stage_2
#: model:project.task.type,legend_normal:project.project_stage_3
#: model:project.task.type,name:project.project_stage_1
#, python-format
msgid "In Progress"
msgstr "En curs"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "In development"
msgstr "En desenvolupament"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
#, python-format
msgid "Inbox"
msgstr "Bústia d'entrada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__planned_hours
msgid "Initially Planned Hours"
msgstr "Hores planificades inicialment"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"Correu electrònic intern associat amb el projecte. Els correus entrants "
"seran sincronitzats automàticament amb les tasques (o opcionalment les "
"incidències si el mòdul, rastreja incidències, està instal·lat)."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Invite People"
msgstr "Convidar a la gent"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited employees"
msgstr "Empleats convidats"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all employees"
msgstr "Usuaris del portal convidats i tots els empleats"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_exceeded
msgid "Is Deadline Exceeded"
msgstr "S'ha superat el termini"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_future
msgid "Is Deadline Future"
msgstr "És el futur dels terminis"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
msgid "Is Follower"
msgstr "És seguidor"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_private
msgid "Is Private"
msgstr "És privat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__partner_is_company
msgid "Is a Company"
msgstr "És una Empresa "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "Versió incidència"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "It seems that some tasks are part of a recurrence."
msgstr "Sembla que algunes tasques són part d'una recurrència"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "It seems that this task is part of a recurrence."
msgstr "Sembla que aquesta tasca es part d'una recurrència."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__january
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__january
msgid "January"
msgstr "Gener"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__july
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__july
msgid "July"
msgstr "Juliol"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__june
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__june
msgid "June"
msgstr "Juny"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Explicació del kanban bloquejat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Explicació del kanban en procés"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Kanban State"
msgstr "Estat kanban"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state_label
msgid "Kanban State Label"
msgstr "Etiqueta de l'estat de Kanban"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_done
msgid "Kanban Valid Explanation"
msgstr "Explicació del kanban vàlid"

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened_value
msgid "Kpi Project Task Opened Value"
msgstr "Valor obert de la tasca del projecte Kpi"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid "Label used for the tasks of the project."
msgstr "Etiqueta utilitzada per a les tasques del projecte."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__last
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__last
msgid "Last"
msgstr "Darrer"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Last 30 Days"
msgstr "Darrers 30 dies"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator____last_update
#: model:ir.model.fields,field_description:project.field_project_delete_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_milestone____last_update
#: model:ir.model.fields,field_description:project.field_project_project____last_update
#: model:ir.model.fields,field_description:project.field_project_project_stage____last_update
#: model:ir.model.fields,field_description:project.field_project_share_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_tags____last_update
#: model:ir.model.fields,field_description:project.field_project_task____last_update
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report____last_update
#: model:ir.model.fields,field_description:project.field_project_task_recurrence____last_update
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal____last_update
#: model:ir.model.fields,field_description:project.field_project_task_type____last_update
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_update____last_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "Últim mes"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#, python-format
msgid "Last Stage Update"
msgstr "Última actualització de la fase"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_id
msgid "Last Update"
msgstr "Darrera actualització"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_color
msgid "Last Update Color"
msgstr "Color de l'última actualització"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_status
msgid "Last Update Status"
msgstr "Estat de l'última actualització"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "S' ha actualitzat el"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "Activitats endarrerides"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Tasks"
msgstr "Tasques endarrerides"

#. module: project
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
#, python-format
msgid "Later"
msgstr "Més tard"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Dissatisfied"
msgstr "Puntuació més tardana: insatisfeta"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Okay"
msgstr "Puntuació més tardana: d'acord"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Satisfied"
msgstr "Puntuació més tardana: satisfeta"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>project</b>."
msgstr "Crearem el teu primer <b>projecte</b>."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr "Crearem la teva primera <b>tasca</b>."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr ""
"Tornem a la <b>visualització kanban</b> per tenir una visió general de les "
"vostres tasques següents."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's start working on your task."
msgstr "Comencem a treballar en la teva tasca."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr "Esperem que els teus clients es manifestin."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
msgid "Link"
msgstr "Enllaç"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
#: model:ir.model.fields,help:project.field_project_update__email_cc
msgid "List of cc from incoming emails."
msgstr "Llista de l' cc des de correus entrants."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Live"
msgstr "En viu"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Logo Design"
msgstr "Disseny del logotip"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Long Term"
msgstr "Terme llarg"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "Baixa"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_project__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_task__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_update__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjunt principal"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__attachment_ids
msgid "Main Attachments"
msgstr "Annexos principals"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly "
"acquired projects, assign them and use the %s and %s to define if the "
"project is ready for the next step. %s"
msgstr ""
"Gestioneu el cicle de vida del vostre projecte mitjançant la vista kanban. "
"Afegiu projectes recentment adquirits, assigneu-los i utilitzeu %s i%s per "
"definir si el projecte està preparat per al següent pas. %s"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Manufacturing"
msgstr "Fabricació"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__march
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__march
msgid "March"
msgstr "Març"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Material Sourcing"
msgstr "Proveïment de materials"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__may
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__may
msgid "May"
msgstr "Maig"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__favorite_user_ids
msgid "Members"
msgstr "Membres"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "Menú"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/right_panel/project_utils.js:0
#: model:ir.model.fields,field_description:project.field_project_project__milestone_ids
#, python-format
msgid "Milestone"
msgstr "Fita"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count
msgid "Milestone Count"
msgstr "Recompte de fites"

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#: model:ir.actions.act_window,name:project.project_milestone_all
#, python-format
msgid "Milestones"
msgstr "Fita"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Mixing"
msgstr "Mescla"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__mon
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__mon
msgid "Mon"
msgstr "Dl"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__mon
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__mon
msgid "Monday"
msgstr "Dilluns"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "Mesos"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Favorite Projects"
msgstr "Els meus projectes preferits"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "Els meus favorits"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Projects"
msgstr "Els meus projectes"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "My Tasks"
msgstr "Les meves tasques"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#, python-format
msgid "Name"
msgstr "Nom"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__name_cropped
msgid "Name Cropped"
msgstr "Nom Retallat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the tasks"
msgstr "Nom de les tasques"

#. module: project
#: model:project.task,legend_blocked:project.project_task_10
#: model:project.task,legend_blocked:project.project_task_11
#: model:project.task,legend_blocked:project.project_task_19
#: model:project.task,legend_blocked:project.project_task_21
#: model:project.task,legend_blocked:project.project_task_22
#: model:project.task,legend_blocked:project.project_task_27
#: model:project.task,legend_blocked:project.project_task_28
#: model:project.task,legend_blocked:project.project_task_29
#: model:project.task,legend_blocked:project.project_task_34
#: model:project.task,legend_blocked:project.project_task_35
#: model:project.task,legend_blocked:project.project_task_36
#: model:project.task,legend_blocked:project.project_task_4
#: model:project.task,legend_blocked:project.project_task_5
#: model:project.task,legend_blocked:project.project_task_6
#: model:project.task,legend_blocked:project.project_task_7
#: model:project.task.type,legend_blocked:project.project_stage_1
msgid "Need functional or technical help"
msgstr "Necessita ajuda funcional o tècnica"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr "Cara neutral"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#, python-format
msgid "New"
msgstr "Nou"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/right_panel/project_utils.js:0
#, python-format
msgid "New Milestone"
msgstr "Nova Fita"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Orders"
msgstr "Ordres noves"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Projects"
msgstr "Projectes nous"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Request"
msgstr "Nova sol·licitud"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "El més nou"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_activity
msgid "Next Activities"
msgstr "Activitat següent"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Next Occurrences:"
msgstr "Pròximes ocurrències:"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__next_recurrence_date
msgid "Next Recurrence Date"
msgstr "Pròxima data de recurrència"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_message
msgid "Next Recurrencies"
msgstr "Pròximes Recurrències"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "No Subject"
msgstr "Sense assumpte"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr "Encara no hi ha valoracions del client"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "Encara no hi han dades!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_milestone_all
msgid "No milestones found. Let's create one!"
msgstr "No s'han trobat fites. Creem-ne un!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr "No s'ha trobat cap projecte. Creem-ne un!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr "No s'ha trobat cap escenari. ¡Crearem una!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr "No s'ha trobat cap etiqueta. ¡Creem-ne una!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
msgid "No tasks found. Let's create one!"
msgstr "No s'ha trobat cap tasca. ¡Creem-ne un!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr "No s'ha trobat cap actualització. Creem-ne un!"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "Cap"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "Normal"
msgstr "Normal"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "Nota"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__november
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__november
msgid "November"
msgstr "Novembre"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__after
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__after
msgid "Number of Repetitions"
msgstr "Nombre de repeticions"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__tasks_count
msgid "Number of Tasks"
msgstr "Nombre de tasques"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__recurrence_left
msgid "Number of Tasks Left to Create"
msgstr "Nombre de tasques que queden per crear"

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_days_close
msgid "Number of Working Days to close the task"
msgstr "Nombre de dies laborables per a tancar la tasca"

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_days_open
msgid "Number of Working Days to open the task"
msgstr "Nombre de dies laborables per obrir la tasca"

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_hours_close
msgid "Number of Working Hours to close the task"
msgstr "Nombre d'hores de treball per tancar la tasca"

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_hours_open
msgid "Number of Working Hours to open the task"
msgstr "Nombre d'hores de treball per obrir la tasca"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__doc_count
msgid "Number of documents attached"
msgstr "Nombre de documents adjunts"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_unread_counter
#: model:ir.model.fields,help:project.field_project_project__message_unread_counter
#: model:ir.model.fields,help:project.field_project_task__message_unread_counter
#: model:ir.model.fields,help:project.field_project_update__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de missatges no llegits"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__october
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__october
msgid "October"
msgstr "Octubre"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
msgid "Off Track"
msgstr "Fora de la pista"

#. module: project
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr "Disseny d'oficina"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "Ok"
msgstr "Ok"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Old Completed Sprint"
msgstr "Esprint antic completat"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
msgid "On Hold"
msgstr "En espera"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
msgid "On Track"
msgstr "A la pista"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr "A la pista"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Open"
msgstr "Obre"

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
msgid "Open Tasks"
msgstr "Tasques obertes"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Open tasks"
msgstr "Tasques obertes"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Id opcional d'un fil (registre) al que s'adjuntaran tots els missatges "
"entrants, inclús si no en son respostes. Si s'estableix, es desactivarà "
"completament la creació de nous registres."

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Others"
msgstr "Altres"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "Tasques sobrepassades"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_blocked
#: model:ir.model.fields,help:project.field_project_task_type__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection when the task or issue is in that stage."
msgstr ""
"Sobreescriure el valor per defecte mostrat pel estat blocat de la selecció "
"del kanban quan la tasca o incidència és en aquest estat."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_done
#: model:ir.model.fields,help:project.field_project_task_type__legend_done
msgid ""
"Override the default value displayed for the done state for kanban selection"
" when the task or issue is in that stage."
msgstr ""
"Substituïu el valor predeterminat que es mostra per a l'estat fet per a la "
"selecció de kanban quan la tasca o problema es troba en aquesta etapa."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_normal
#: model:ir.model.fields,help:project.field_project_task_type__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection when the task or issue is in that stage."
msgstr ""
"Sobreescriure el valor per defecte mostrat pel estat normal de la selecció "
"del kanban quan la tasca o incidència és en aquest estat."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_user_id
msgid "Owner"
msgstr "Propietari"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Page Ideas"
msgstr "Idees de la pàgina"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "Model pare"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Id del registre pare"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#, python-format
msgid "Parent Task"
msgstr "Tasca pare"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"El model pare conte el pseudònim. El model que conte la referència del "
"pseudònim no és necessàriament el model que es dóna a través del camp "
"alias_model_id (exemple: projecto (parent_model) i tasca (model))"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Percentatge de valoracions \"feliç\""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Percentage of happy ratings over the past 30 days."
msgstr "Percentatge de les valoracions satisfetes en els últims 30 dies."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "Periodic rating"
msgstr "Valoració periòdica"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Personal Stage"
msgstr "Etapa personal"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr "Estat de l'etapa personal"

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr "Etapa de la tasca personal"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
msgid "Personal User Stage"
msgstr "Etapa d'usuari personal"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__partner_phone
#: model:ir.model.fields,field_description:project.field_project_task__partner_phone
msgid "Phone"
msgstr "Telèfon"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Plan resource allocation across projets and tasks, and estimate deadlines "
"more accurately"
msgstr ""
"Planifiqueu l'assignació de recursos entre projectes i tasques, i calculeu "
"els terminis amb més precisió"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_project_forecast
msgid "Planning"
msgstr "Planificació"

#. module: project
#: code:addons/project/models/analytic_account.py:0
#, python-format
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr ""
"Si us plau, elimini les tasques al projecte vinculades als comptes que vol "
"eliminar."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Podcast and Video Production"
msgstr "Podcast i producció de vídeo"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Política per enviar un missatge en el document utilitzant la passarel·la del correu.\n"
"-Tots: Tothom pot enviar.\n"
"-Empreses: Només les empreses autenticades.\n"
"-Seguidors: Només els seguidors del document relacionat o els membres dels canals següents.\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "URL del portal d'accés"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__portal_user_names
msgid "Portal User Names"
msgstr "Noms d'usuari del portal"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Prioritize Tasks by using the %s icon.%s Use the %s button to inform your "
"colleagues that a task is ready for the next stage.%s Use the %s to indicate"
" a problem or a need for discussion on a task.%s"
msgstr ""
"Prioritzeu les tasques utilitzant el %s icona.%s Utilitzar el %s botó per "
"informar els vostres companys que una tasca està preparada per a la següent "
"etapa.%s Utilitzar el %s per indicar un problema o una necessitat de "
"discussió sobre una tasca.%s"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Prioritize Tasks by using the %s icon.%s Use the %s button to signalize to "
"your colleagues that a task is ready for the next stage.%s Use the %s to "
"signalize a problem or a need for discussion on a task.%s"
msgstr ""
"Prioritzeu les tasques utilitzant el %s icona.%s Utilitzar el %s botó per "
"indicar als vostres companys que una tasca està preparada per a la següent "
"etapa.%s Utilitzar el %s per indicar un problema o una necessitat de "
"discussió sobre una tasca.%s"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#, python-format
msgid "Priority"
msgstr "Prioritat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Private"
msgstr "Privat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Profitability"
msgstr "Rendibilitat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "Progrés"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress_percentage
msgid "Progress Percentage"
msgstr "Percentatge del progrés"

#. module: project
#. openerp-web
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_project_update__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Project"
msgstr "Projecte"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_analytic_account_id
msgid "Project Analytic Account"
msgstr "Compte analític del projecte"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_form
msgid "Project Collaborator"
msgstr "Col·laborador del projecte"

#. module: project
#: model:ir.actions.act_window,name:project.project_collaborator_action
#: model_terms:ir.ui.view,arch_db:project.project_sharing_access_view_tree
msgid "Project Collaborators"
msgstr "Col·laboradors del projecte"

#. module: project
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_count
msgid "Project Count"
msgstr "Comte de projecte"

#. module: project
#: model:ir.model,name:project.model_project_delete_wizard
msgid "Project Delete Wizard"
msgstr "Auxiliar de supressió del projecte"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model:ir.model.fields,field_description:project.field_project_task__manager_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "Responsable de projecte"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr "Projecte Milestone"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "Nom del projecte"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__project_id
msgid "Project Shared"
msgstr "Projecte compartit"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr "Compartició de projectes"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr "Projecte Compartició: tasca"

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr "Etapa del projecte"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr "Fase del projecte canviada"

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr "Assistent de supressió de l'etapa del projecte"

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr "Etapes del projecte"

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr "Etiquetes del projecte"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "Tasques de projecte"

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr "Actualització del projecte"

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Project Updates"
msgstr "Actualitzacions del projecte"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr "Visibilitat del projecte"

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "Tasques de projecte"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_recurring_tasks_ir_actions_server
#: model:ir.cron,cron_name:project.ir_cron_recurring_tasks
#: model:ir.cron,name:project.ir_cron_recurring_tasks
msgid "Project: Create Recurring Tasks"
msgstr "Projecte: Crear tasques recurrents"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
#: model:ir.cron,cron_name:project.ir_cron_rating_project
#: model:ir.cron,name:project.ir_cron_rating_project
msgid "Project: Send rating"
msgstr "Projecte: Enviar qualificació"

#. module: project
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_ids
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Projects"
msgstr "Projectes"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__projects_archived
msgid "Projects Archived"
msgstr "Projectes arxivats"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "Projects regroup tasks on the same topic, and each has its dashboard."
msgstr ""
"Els projectes reagrupen tasques sobre el mateix tema, i cadascun té el seu "
"tauler."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Published"
msgstr "Publicat"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Publishing"
msgstr "Publicació"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "Trimestral"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Rated Tasks"
msgstr "Tasques avaluades"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Rating"
msgstr "Valoració"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
msgid "Rating (/5)"
msgstr "Valoració (/5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
msgid "Rating Average"
msgstr "Valoració mitjana"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr "Plantilla de correu electrònic de valoració"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr "Freqüència de valoració"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Valoració darrers comentaris"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "Darrera imatge de valoració"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "Darrer valor de valoració"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_request_deadline
msgid "Rating Request Deadline"
msgstr "Termini de sol·licitud de qualificació"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Valoració satisfacció"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
#, python-format
msgid "Rating Value (/5)"
msgstr "Valor de valoració (/5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "Comptador de valoracions"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "Rating when changing stage"
msgstr "Valoració en canviar d'etapa"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
msgid "Ratings"
msgstr "Valoracions"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Ratings of %s"
msgstr "Valoracions de %s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr "Assolit"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__reached_date
msgid "Reached Date"
msgstr "Data aconseguida"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__read
msgid "Readonly"
msgstr "Només lectura"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__done
#: model:project.task,legend_done:project.project_task_1
#: model:project.task,legend_done:project.project_task_12
#: model:project.task,legend_done:project.project_task_2
#: model:project.task,legend_done:project.project_task_20
#: model:project.task,legend_done:project.project_task_24
#: model:project.task,legend_done:project.project_task_25
#: model:project.task,legend_done:project.project_task_26
#: model:project.task,legend_done:project.project_task_3
#: model:project.task,legend_done:project.project_task_30
#: model:project.task,legend_done:project.project_task_31
#: model:project.task,legend_done:project.project_task_32
#: model:project.task,legend_done:project.project_task_33
#: model:project.task,legend_done:project.project_task_8
#: model:project.task,legend_done:project.project_task_9
#: model:project.task.type,legend_done:project.project_personal_stage_admin_0
#: model:project.task.type,legend_done:project.project_personal_stage_admin_1
#: model:project.task.type,legend_done:project.project_personal_stage_admin_2
#: model:project.task.type,legend_done:project.project_personal_stage_admin_3
#: model:project.task.type,legend_done:project.project_personal_stage_admin_4
#: model:project.task.type,legend_done:project.project_personal_stage_admin_5
#: model:project.task.type,legend_done:project.project_personal_stage_admin_6
#: model:project.task.type,legend_done:project.project_personal_stage_demo_0
#: model:project.task.type,legend_done:project.project_personal_stage_demo_1
#: model:project.task.type,legend_done:project.project_personal_stage_demo_2
#: model:project.task.type,legend_done:project.project_personal_stage_demo_3
#: model:project.task.type,legend_done:project.project_personal_stage_demo_4
#: model:project.task.type,legend_done:project.project_personal_stage_demo_5
#: model:project.task.type,legend_done:project.project_personal_stage_demo_6
#: model:project.task.type,legend_done:project.project_stage_0
#: model:project.task.type,legend_done:project.project_stage_2
#, python-format
msgid "Ready"
msgstr "Preparat"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__done
msgid "Ready for Next Stage"
msgstr "Preparat per propera etapa"

#. module: project
#: model:project.task.type,legend_done:project.project_stage_3
msgid "Ready to reopen"
msgstr "Llest per reobrir"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__rating_last_feedback
msgid "Reason of the rating"
msgstr "Raó de la valoració"

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr "Recepció de {{ object.name }}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "Destinataris"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Id del registre del fil"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Recording"
msgstr "Enregistrament"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurrence"
msgstr "Recurrència"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_update
msgid "Recurrence Update"
msgstr "Actualització de recurrència"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
msgid "Recurrent"
msgstr "Recurrent"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_recurring_tasks
#: model:ir.model.fields,field_description:project.field_project_task__allow_recurring_tasks
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
msgid "Recurring Tasks"
msgstr "Tasques recurrents"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_blocked
msgid "Red Kanban Label"
msgstr "Etiqueta vermella de Kanban"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Ref"
msgstr "Ref."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Refused"
msgstr "Rebutjada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "Id. del document relacionat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "Id. del document relacionat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "Model de document relacionat"

#. module: project
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr "Renovacions"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_day
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_day
msgid "Repeat Day"
msgstr "Dia de la repetició"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "Repetir cada "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_month
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_month
msgid "Repeat Month"
msgstr "Repeteix Mes"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Repeat On"
msgstr "Repetir en"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_on_month
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_on_month
msgid "Repeat On Month"
msgstr "Repeteix el mes"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_on_year
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_on_year
msgid "Repeat On Year"
msgstr "Repetir l'any"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_day
msgid "Repeat Show Day"
msgstr "Repetir el dia de l'espectacle"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_dow
msgid "Repeat Show Dow"
msgstr "Repetiu Mostra Dow"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_month
msgid "Repeat Show Month"
msgstr "Repetiu Mostra el mes"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_week
msgid "Repeat Show Week"
msgstr "ensenyar una altra vegada la setmana"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_unit
msgid "Repeat Unit"
msgstr "Repeteix la unitat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_week
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_week
msgid "Repeat Week"
msgstr "Repeteix la setmana"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_number
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_number
msgid "Repetitions"
msgstr "Repeticions"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "Informes"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research"
msgstr "Recerca"

#. module: project
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr "Recerca i desenvolupament"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research Project"
msgstr "Projecte de recerca"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Researching"
msgstr "Recerca en"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Resources Allocation"
msgstr "Assignació de recursos"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de lliurament SMS"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr "Cara trista"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__sat
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__sat
msgid "Sat"
msgstr "Ds"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__sat
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__sat
msgid "Saturday"
msgstr "Dissabte"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Script"
msgstr "Script"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr "Cerca <span class=\"nolabel\"> (al Contingut)</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "Cerca projecte"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr "Actualització de la cerca"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "Cerqueu a tots"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Assignees"
msgstr "Actualització de la cerca"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Messages"
msgstr "Cerca als missatges"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Priority"
msgstr "Cerca a la prioritat"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "Cercar a Projecte"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Ref"
msgstr "Cerca a Ref"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Stages"
msgstr "Buscar en seccions"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Status"
msgstr "Buscar en estatus"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__second
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__second
msgid "Second"
msgstr "Segon"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "Token de Seguretat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Send"
msgstr "Enviar"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__september
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__september
msgid "September"
msgstr "Setembre"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__sequence
#: model:ir.model.fields,field_description:project.field_project_project_stage__sequence
#: model:ir.model.fields,field_description:project.field_project_task__sequence
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "Ajustar la Imatge de la coberta"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr ""
"Estableix una plantilla de correu electrònic de valoració a les etapes"

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Settings"
msgstr "Configuració"

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share"
msgstr "Compartir"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Editable"
msgstr "Compartir editable"

#. module: project
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Share Project"
msgstr "Comparteix el projecte"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Readonly"
msgstr "Comparteix només lectura"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_favorite
msgid "Show Project on Dashboard"
msgstr "Mostra el projecte al tauler de control"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostra tots els registres en que la data de següent acció és abans d'avui"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr "Des de"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Software Development"
msgstr "Desenvolupament de programari"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Sorry. You can't set a task as its parent task."
msgstr "Ho sento. No podeu establir una tasca com a tasca principal."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Specifications"
msgstr "Especificacions"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Split your tasks to organize your work into sub-milestones"
msgstr ""
"Divideix les teves tasques per organitzar el teu treball en fites "
"secundàries"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Backlog"
msgstr "Sprint Backlog"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Complete"
msgstr "Sprint completat"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Sprint Summary"
msgstr "Resum de Sprint"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint in Progress"
msgstr "Sprint en curs"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Stage"
msgstr "Etapa"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "Canvi de fase"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Stage Description and Tooltips"
msgstr "Descripció de l'etapa i informació sobre eines"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr "Propietari de l'escenari"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "Canvi de fase"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stages_active
msgid "Stages Active"
msgstr "Etapes actives"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr "Etapes per esborrar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Starred"
msgstr "Destacat"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Start Date"
msgstr "Data inicial"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state
#: model:ir.model.fields,field_description:project.field_project_update__status
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#, python-format
msgid "Status"
msgstr "Estat"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Sobrepassat: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Stop Recurrence"
msgstr "Detenir la recurrència"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_count
msgid "Sub-task Count"
msgstr "Comptador de subtasca"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_subtasks
#: model:ir.model.fields,field_description:project.field_project_task__child_ids
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_subtask_project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr "Subtasques"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_planned_hours
msgid "Sub-tasks Planned Hours"
msgstr "Hores planificades per a les subtasques"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_planned_hours
msgid ""
"Sum of the time planned of all the sub-tasks linked to this task. Usually "
"less than or equal to the initially planned time of this task."
msgstr ""
"Suma del temps previst de totes les subtasques vinculades a aquesta tasca. "
"Normalment és inferior o igual al temps previst inicialment d'aquesta tasca."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__sun
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__sun
msgid "Sun"
msgstr "Dg"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__sun
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__sun
msgid "Sunday"
msgstr "Diumenge"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "T-shirt Printing"
msgstr "Impressió de samarretes"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "Tag name already exists!"
msgstr "¡El nom de l'etiqueta ja existeix!"

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tags"
msgstr "Etiquetes"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__task_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Task"
msgstr "Tasca"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr "Activitats de la tasca"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_blocked
#: model:mail.message.subtype,name:project.mt_task_blocked
msgid "Task Blocked"
msgstr "Tasca bloquejada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__task_count
#: model:ir.model.fields,field_description:project.field_project_project__task_count
msgid "Task Count"
msgstr "Comptador de tasques"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_count_with_subtasks
msgid "Task Count With Subtasks"
msgstr "Tasca comptada amb subtascas"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr "Tasca creada"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_project_task__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr "Dependències de la tasca"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_dependency_change
#: model:mail.message.subtype,name:project.mt_task_dependency_change
msgid "Task Dependency Changes"
msgstr "Canvis en la tasca dependent"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "Registres de tasques"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr "Qualificació de tasca"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_ready
#: model:mail.message.subtype,name:project.mt_task_ready
msgid "Task Ready"
msgstr "Tasca preparada"

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Recurrència de la tasca"

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "Fase de la Tasca"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "Fase de la tasca canviada"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr "Estadístiques de tasques"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr "Títol de la tasca"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "Títol de la tasca..."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_blocked
msgid "Task blocked"
msgstr "Tasca bloquejada"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task in progress. Click to block or set as done."
msgstr "Tasca en curs. Feu clic per blocar o establir com s'ha fet."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""
"La tasca està bloquejada. Feu clic per desbloquejar o establir com s'ha fet."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_ready
msgid "Task ready for Next Stage"
msgstr "Tasca llesta per la següent etapa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr "Tasca:"

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Task: Rating Request"
msgstr "Tasca: comanda de qualificació"

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Task: Reception Acknowledgment"
msgstr "Tasca: Confirmació de recepció"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
#: model:ir.model.fields,field_description:project.field_account_analytic_tag__task_ids
#: model:ir.model.fields,field_description:project.field_project_project__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.model.fields,field_description:project.field_res_partner__task_ids
#: model:ir.model.fields,field_description:project.field_res_users__task_ids
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_tree2
#, python-format
msgid "Tasks"
msgstr "Tasques"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.report_project_task_user_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "Anàlisi de tasques"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Tasks In Progress"
msgstr "Tasca en progrés"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Late"
msgstr "Tasques tard"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr "Gestió de tasques"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__type_ids
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "Fases de tasques"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
msgid "Tasks in Recurrence"
msgstr "Tasques en recurrència"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__is_closed
#: model:ir.model.fields,help:project.field_project_task_type__is_closed
msgid "Tasks in this stage are considered as closed."
msgstr "Les tasques en aquesta etapa es consideren tancades."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Tests"
msgstr "Test"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr "L'etapa personal de l'usuari actual."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr "Fase de tasca personal de l'usuari actual."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr "S'ha afegit la següent fita:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr "S'han afegit les fites següents:"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"El model (Tipus de document d'Odoo) al que correspon aquest pseudònim. "
"Qualsevol correu electrònic entrant que no sigui resposta a un registre "
"existent, causarà la creació d'un nou registre d'aquest model (per exemple, "
"una tasca de projecte)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"El nom d'aquest pseudònim de correu electrònic. Per exemple, \"jobs\", si el"
" que vol és obtenir els correus per <<EMAIL>>"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"El propietari dels registres creats al rebre correus electrònics amb aquest "
"pseudònim. Si aquest camp no esta definit, el sistema intentarà de trobar el"
" propietari correcte basat amb l'adreça de l'emissor (De), o utilitzarà la "
"compta d'administrador si no es troba un usuari per aquesta adreça."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The project cannot be shared with the recipient(s) because the privacy of "
"the project is too restricted. Set the privacy to 'Visible by following "
"customers' in order to make it accessible by the recipient(s)."
msgstr ""
"El projecte no es pot compartir amb els destinataris perquè la privadesa del"
" projecte està massa restringida. Establiu la privadesa a \"Visible per "
"seguir els clients\" per tal de fer-la accessible pel destinatari."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to 'Visible by "
"following customers' in order to make it accessible by the recipient(s)."
msgstr ""
"La tasca no es pot compartir amb els destinataris perquè la privadesa del "
"projecte està massa restringida. Estableix la privadesa del projecte a "
"\"Visible per seguir els clients\" per tal de fer-lo accessible per als "
"destinataris."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "There are no more occurrences."
msgstr "No hi ha més esdeveniments."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "No hi ha projectes."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr "Encara no hi ha qualificacions per a aquest projecte de moment"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "No hi ha tasques."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_from
msgid "These people will receive email."
msgstr "Aquestes persones rebran un correu electrònic."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__third
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__third
msgid "Third"
msgstr "Tercer"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
#, python-format
msgid "This Month"
msgstr "Aquest mes"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#, python-format
msgid "This Week"
msgstr "Aquest setmana"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__subsequent
msgid "This and following tasks"
msgstr "Aquesta i les tasques següents"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"Aquesta etapa es replega en la vista kanban quan no hi ha registres per "
"mostrar en aquesta."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid "This stage is folded in the kanban view."
msgstr "Aquesta etapa es plega a la vista kanban."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "This step is done. Click to block or set in progress."
msgstr "Aquest pas està fet. Faci clic per a bloquejar o posar en marxa."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__this
msgid "This task"
msgstr "Aquesta tasca"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr ""
"Això arxivarà les fases i totes les tasques que contenen dels següents "
"projectes:"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__thu
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__thu
msgid "Thu"
msgstr "Dj"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__thu
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__thu
msgid "Thursday"
msgstr "Dijous"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr "Gestió del temps"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__planned_hours
msgid "Time planned to achieve this task (including its sub-tasks)."
msgstr ""
"Temps previst per a fer aquesta tasca (incloses les seves subtasques)."

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Customize tasks and stages according to the project"
msgstr "Consell: personalitzeu les tasques i les etapes segons el projecte"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#, python-format
msgid "Title"
msgstr "Títol"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Title of the Update"
msgstr "Títol de l'actualització"

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr "Per fer"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "To Print"
msgstr "Per imprimir"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                    Chat in real-time or by email to collaborate efficiently."
msgstr ""
"Milloreu la productivitat usant activitats i estats a les tasques.<br>\n"
"                    Xategeu en temps real o per correu per a col·laborar de manera eficient."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""
"Milloreu la productivitat usant activitats i estats a les tasques.<br>\n"
"                Xategeu en temps real o per correu per a col·laborar de manera eficient."

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#, python-format
msgid "Today"
msgstr "Avui"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "Activitats d'avui"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr "Segueix la satisfacció dels clients en les tasques"

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#: model_terms:ir.actions.act_window,help:project.project_milestone_all
#, python-format
msgid "Track major progress points that must be reached to achieve success."
msgstr ""
"Segueix els principals punts de progrés que cal aconseguir per a aconseguir "
"l'èxit."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the costs and revenues linked to your projects"
msgstr "Segueix els costos i ingressos vinculats als vostres projectes"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr "Segueix el progrés dels vostres projectes"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Track the progress of your projects from their creation to their closing."
msgstr ""
"Feu un seguiment del progrés dels vostres projectes des de la seva creació "
"fins al seu tancament."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Track the progress of your tasks from their creation to their closing."
msgstr ""
"Segueix el progrés de les vostres tasques des de la seva creació fins al seu"
" tancament."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr "Feu un seguiment del temps dedicat a projectes i tasques"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__tue
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__tue
msgid "Tue"
msgstr "Dt"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__tue
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__tue
msgid "Tuesday"
msgstr "Dimarts"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr "Dues vegades un mes"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Unarchive"
msgstr "Desarxivar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Unassign Me"
msgstr "Desassigna"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unassigned"
msgstr "Sense assignar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Unassigned Tasks"
msgstr "Tasques sense assignar"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Unknown Analytic Account"
msgstr "Compte analític desconegut"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_unread
#: model:ir.model.fields,field_description:project.field_project_project__message_unread
#: model:ir.model.fields,field_description:project.field_project_task__message_unread
#: model:ir.model.fields,field_description:project.field_project_update__message_unread
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "Missatges pendents de llegir"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_unread_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_unread_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_unread_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Comptador de missatges no llegits"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
msgid "Until"
msgstr "Fins"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__update_ids
msgid "Update"
msgstr "Actualitza"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Use %s and %s bullets to indicate the status of a task. %s"
msgstr "Ús %s i %s pics per indicar l'estat d'una tasca.%s"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Use <b>activities</b> to organize your daily work."
msgstr "Utilitzeu<b>activititats</b> per organitzar el vostre treball diari."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_enabled
msgid "Use Email Alias"
msgstr "Usar pseudònim de correu"

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "Utilitza la valoració al projecte"

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr "Utilitza les tasques recurrents"

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr "Utilitza les escales del projecte"

#. module: project
#: model:res.groups,name:project.group_subtask_project
msgid "Use Subtasks"
msgstr "Utilitza les subtasques"

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr "Utilitza les dependències de la tasca"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "Utilitzar tasques com"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Use This For My Project"
msgstr "Utilitza això per al meu projecte"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr "Utilitzeu etiquetes per categoritzar les vostres tasques."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your customers. \n"
"    Add new people to the followers' list to make them aware of the main changes about this task."
msgstr ""
"Utilitza la xerrada per <b>enviar correus electrònics</b> i comunicar-se de manera eficient amb els seus clients.\n"
"   Afegiu persones noves a la llista de seguidors per fer-los conèixer els principals canvis d'aquesta tasca."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__user_id
#: model:res.groups,name:project.group_project_user
msgid "User"
msgstr "Usuari"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "View"
msgstr "Vista"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "View Task"
msgstr "Visualitza la tasca"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "Visibilitat"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr ""
"Busca una millor manera de <b>gestionar els teus projectes</b>? <i>Aquí "
"comença.</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_task__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "Missatges del lloc web"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Website Redesign"
msgstr "Redisseny del lloc web"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicacions del lloc web"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__wed
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__wed
msgid "Wed"
msgstr "Dc"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__wed
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__wed
msgid "Wednesday"
msgstr "Dimecres"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "Setmanalment"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "Setmanes"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__is_favorite
msgid "Whether this project should be displayed on your dashboard."
msgstr "Ja sigui qualsevol projecte hauria de ser projectat en el teu tauler."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr "Dies de treball per assignar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr "Dies feiners per tancar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr "Hores de treball a assignar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr "Hores de treball a tancar"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__resource_calendar_id
msgid "Working Time"
msgstr "Horari de treball"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr "Temps de treball a assignar"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr "Temps de treball a tancar"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Writing"
msgstr "Escriure"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "Anualment"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "Anys"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"També podeu afegir una descripció per ajudar als vostres companys de feina a"
" entendre el significat i el propòsit d'aquesta etapa."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot archive recurring tasks. Please disable the recurrence first."
msgstr ""
"No es poden arxivar tasques recurrents. Si us plau, primer desactiveu la "
"recurrència."

#. module: project
#: code:addons/project/models/analytic_account.py:0
#, python-format
msgid ""
"You cannot change the company of an analytic account if it is related to a "
"project."
msgstr ""
"No es pot canviar la companyia d'un compte analític si està vinculat a un "
"projecte."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot create cyclic dependency."
msgstr "No es pot crear una dependència cíclica."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot delete a project containing tasks. You can either archive it or "
"first delete all of its tasks."
msgstr ""
"No pots esborrar un projecte que conté tasques. pots o arxivar-ho o primer "
"esborrar totes les tasques."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot delete recurring tasks. Please disable the recurrence first."
msgstr ""
"No es poden esborraar tasques recurrents. Si us plau, primer desactiveu la "
"recurrència."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr ""
"No pots eliminar els fases que conté tasques. Pots ja sigui arxivar-los o "
"primer esborrar tots les tasques."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr ""
"No pots esborrar les fases que conté tasques. Primer hauries d'esborrar "
"totes les tasques."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot read %s fields in task."
msgstr "No podeu llegir els %s camps de la tasca."

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot write on %s fields in task."
msgstr "No podeu escriure en %s camps de la tasca."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "Heu estat assignat a %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "Ha estat assignat al"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "You have not write access of %s field."
msgstr "No teniu accés d'escriptura de %s camp."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You should at least have one personal stage. Create a new stage to which the"
" tasks can be transferred after this one is deleted."
msgstr ""
"Hauríeu de tenir almenys una etapa personal. Creeu una nova etapa a la qual "
"es puguin transferir les tasques després de suprimir aquesta."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. New Design"
msgstr "p. ex. Disseny nou"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. Office Party"
msgstr "p. ex. Festa a l'Oficina"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr "p. ex. oficina-festa"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "for customer:"
msgstr "per al client:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "for project:"
msgstr "per al projecte:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "in priority:"
msgstr "en prioridad:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "in stage:"
msgstr "en l'escenari:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "in status:"
msgstr "en estat:"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "task"
msgstr "tasca"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline of the following milestone has been updated:"
msgstr "La data limiti del següent objectiu ha estat actualitzat."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline of the following milestones has been updated:"
msgstr "s'ha actualitzat el termini de les fites següents:"

#. module: project
#. openerp-web
#: code:addons/project/static/src/burndown_chart/burndown_chart_view.xml:0
#, python-format
msgid "true"
msgstr "cert"

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid "{{ object.project_id.company_id.name }}: Satisfaction Survey"
msgstr "{{ object.project_id.company_id.name }}: enquesta de satisfacció"
