# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_mollie
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_mollie
#: model_terms:ir.ui.view,arch_db:payment_mollie.payment_acquirer_form
msgid "API Key"
msgstr "Clave API"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Canceled payment with status: %s"
msgstr "Pago cancelado con el estado: %s"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "No se pudo establecer la conexión con la API."

#. module: payment_mollie
#: model:account.payment.method,name:payment_mollie.payment_method_mollie
#: model:ir.model.fields.selection,name:payment_mollie.selection__payment_acquirer__provider__mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_acquirer__mollie_api_key
msgid "Mollie API Key"
msgstr "Clave API de Mollie"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "No se encontró ninguna transacción que coincida con la referencia %s."

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Método de pago"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_account_payment_method
msgid "Payment Methods"
msgstr "Métodos de pago"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_acquirer__provider
msgid "Provider"
msgstr "Proveedor"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "Información recibida con estado de pago inválido: %s"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""
"El proveedor de servicios de pago que se utilizará con este método de pago"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_acquirer__mollie_api_key
msgid ""
"The Test or Live API Key depending on the configuration of the acquirer"
msgstr ""
"La clave API de prueba o en vivo, dependiendo de la configuración del método"
" de pago"
