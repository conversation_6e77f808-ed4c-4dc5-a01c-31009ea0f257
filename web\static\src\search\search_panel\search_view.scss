.o_searchview {
    align-items: flex-end;
    padding: 0 20px 1px 0;
    position: relative;

    .o_searchview_input_container {
        display: flex;
        flex-flow: row wrap;
        position: relative;

        .o_searchview_facet {
            display: flex;
            flex: 0 0 auto;
            margin: 1px 3px 0 0;
            max-width: 100%;
            position: relative;
            $o-searchview-facet-remove-width: 18px;

            .o_searchview_facet_label {
                align-items: center;
                color: white;
                flex: 0 0 auto;
                padding: 0 3px;
                @include o-text-overflow($display: flex);
            }

            .o_facet_values {
                direction: ltr#{"/*rtl:ignore*/"};
                padding: 0 $o-searchview-facet-remove-width 0 5px;

                .o_facet_values_sep {
                    font-style: italic;
                    margin: 0 0.3rem;
                }
            }

            .o_facet_remove {
                align-items: center;
                cursor: pointer;
                display: flex;
                flex: 0 0 auto;
                justify-content: center;
                width: $o-searchview-facet-remove-width;
                @include o-position-absolute(0, 0, 0);
            }
        }

        .o_searchview_input {
            flex: 1 0 auto;
            width: 75px;
        }

        .o_searchview_autocomplete {
            width: 100%;
            @include o-position-absolute(100%, $left: auto);

            .o_menu_item {
                align-items: center;
                display: flex;
                padding-left: 25px;

                &.o_indent {
                    padding-left: 50px;
                }

                a {
                    &:hover {
                        background-color: inherit;
                    }

                    &.o_expand {
                        display: flex;
                        justify-content: center;
                        width: 25px;
                        @include o-position-absolute($left: 0);
                    }
                }
            }
        }
    }
}

// Filter menu
.o_filter_menu {
    .o_filter_condition {
        &.o_filter_condition_with_buttons {
            padding-right: 0;
            padding-left: $dropdown-item-padding-x*1.5;
        }

        .o_or_filter {
            @include o-position-absolute($left: $dropdown-item-padding-x*.2);
        }

        .o_generator_menu_value {
            color: $o-main-text-color; // avoid to inherit .dropdown-item style

            .datepickerbutton {
                cursor: pointer;
                @include o-position-absolute(3px, -20px);
            }
        }

        .o_generator_menu_delete {
            @include o-hover-opacity(0.8, 1);
            @include o-position-absolute($dropdown-item-padding-x*.3, $dropdown-item-padding-x*.2, auto, auto);
            
            &:hover {
                background-color: gray('100');
            }
        }
    }
    .o_add_condition {
        line-height: 1.1;

        .fa {
            font-size: $font-size-lg;
        }
    }
}
