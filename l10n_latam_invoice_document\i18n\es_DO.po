# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_latam_invoice_document
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-08 16:49+0000\n"
"PO-Revision-Date: 2020-09-08 16:49+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_document_number
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_document_number
msgid "Document Number"
msgstr "Número de Comprobante"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_invoice_report__l10n_latam_document_type_id
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_document_type_id
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_document_type_id
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_document_type_id
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_ir_sequence__l10n_latam_document_type_id
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_form
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_tree
msgid "Document Type"
msgstr "Tipo de Comprobante"

#. module: l10n_latam_invoice_document
#: model:ir.actions.act_window,name:l10n_latam_invoice_document.action_document_type
#: model:ir.ui.menu,name:l10n_latam_invoice_document.menu_document_type
msgid "Document Types"
msgstr "Tipos de Comprobantes"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_amount_untaxed
msgid "L10N Latam Amount Untaxed"
msgstr "Base imponible"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_available_document_type_ids
msgid "L10N Latam Available Document Type"
msgstr "Tipo de Comprobante Disponible"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_bank_statement_import_journal_creation__l10n_latam_company_use_documents
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_journal__l10n_latam_company_use_documents
msgid "L10N Latam Company Use Documents"
msgstr "Es Fiscal"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_price_net
msgid "L10N Latam Price Net"
msgstr "Precio Neto"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_price_subtotal
msgid "L10N Latam Price Subtotal"
msgstr "Subtotal"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_price_unit
msgid "L10N Latam Price Unit"
msgstr "Precio"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_sequence_id
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_sequence_id
msgid "L10N Latam Sequence"
msgstr "Secuencia"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_tax_ids
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_tax_ids
msgid "L10N Latam Tax"
msgstr "Impuestos"

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_l10n_latam_document_type
msgid "Latam Document Type"
msgstr "Tipo de Comprobante"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__report_name
msgid "Name that will be printed in reports, for example \"CREDIT NOTE\""
msgstr "Nombre que será impreso en los reportes, por ejemplo \"NOTA DE CREDITO\""

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "No sequence or document number linked to invoice id %s"
msgstr "No hay secuencia o número de comprobante vinculado a la factura %s"

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "Please set the document number on the following invoices %s."
msgstr ""
"Por favor asigne el número de comprobante en las siguientes facturas %s."

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__doc_code_prefix
msgid ""
"Prefix for Documents Codes on Invoices and Account Moves. For eg. 'FA ' will"
" build 'FA 0001-0000001' Document Number"
msgstr ""
"Prefijo para Códigos de Comprobante en Facturas y Asientos. Por eje: 'FA ' "
"será construirá 'FA 0001-0000001' como el numero de comprobante"

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_ir_sequence
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Show active document types"
msgstr "Mostrar tipos de comprobante activos"

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Show archived document types"
msgstr "Mostrar tipos de comprobante archivados"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__name
msgid "The document name"
msgstr "El nombre del comprobante"

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid ""
"The journal require a document type but not document type has been selected "
"on invoices %s."
msgstr ""
"El diario requiere un tipo de comprobante pero no hay tipo de documento "
"seleccionado en las facturas %s."

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__sequence
msgid ""
"To set in which order show the documents type taking into account the most "
"commonly used first"
msgstr ""
"Para seleccionar en que orden deben mostrar los tipos de comprobante teniendo "
"en cuenta los más usados comunmente"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_bank_statement_import_journal_creation__l10n_latam_use_documents
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_journal__l10n_latam_use_documents
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_use_documents
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_use_documents
msgid "Use Documents?"
msgstr "Es fiscal?"

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_journal.py:0
#, python-format
msgid ""
"You can not modify the field \"Use Documents?\" if there are validated "
"invoices in this journal!"
msgstr ""
"No puedes modificar el campo \"Es Fiscal?\" si ya existen facturas "
"validades en este diario!"

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "You can not use a %s document type with a invoice"
msgstr "No puedes utilizar el tipo de comprobante %s en una factura"

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "You can not use a %s document type with a refund invoice"
msgstr ""
"No puedes utilizar el tipo de comprobante %s en una nota de crédito"

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/wizards/account_move_reversal.py:0
#, python-format
msgid ""
"You can only reverse documents with legal invoicing documents from Latin America one at a time.\n"
"Problematic documents: %s"
msgstr ""
"Solo puede aplicar Notas de Crédito a un documento a la vez.\n"
"Documento con problemas: %s"
