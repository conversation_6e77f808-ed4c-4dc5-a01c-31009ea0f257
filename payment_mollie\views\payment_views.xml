<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="payment_acquirer_form" model="ir.ui.view">
        <field name="name"><PERSON>llie Acquirer Form</field>
        <field name="model">payment.acquirer</field>
        <field name="inherit_id" ref="payment.payment_acquirer_form"/>
        <field name="arch" type="xml">
            <xpath expr='//group[@name="acquirer"]' position='inside'>
                <group attrs="{'invisible': [('provider', '!=', 'mollie')]}">
                    <field name="mollie_api_key" string="API Key" attrs="{'required': [('provider', '=', 'mollie'), ('state', '!=', 'disabled')]}" password="True"/>
                </group>
            </xpath>
        </field>
    </record>

</odoo>
