<?xml version="1.0"?>
<odoo>

    <record id="sukuk_books_view_form" model="ir.ui.view">
        <field name="name">sukuk.management.item.form</field>
        <field name="model">sukuk.management.item</field>
        <field name="arch" type="xml">
            <form create="false" delete="false" edit="false">
                <header>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h2>
                            <div class="oe_inline">
                                <field name="bank_id"  class="oe_inline"/>
                                /
                                <field name="branch_id" class="oe_inline"/>
                            </div>

                        </h2>
                    </div>
                    <group>
                        <group>
                            <field name="person_signature"/>
                            <field name="account_no_id"/>
                            <field name="suke_book_number"/>
                        </group>
                        <group>
                            <field name="number_of_page_received" string="عدد اوراق الدفتر"/>
                            <field name="serial_no_from"/>
                            <field name="serial_no_to"/>
                        </group>
                    </group>
                    <notebook>
                    <page string="الصكوك المستخدمة">
                        <field name="sukuk_page_ids" readonly="1">
                            <tree>
                                <field name="state"/>
                                <field name="paid_to_bank_id"/>
                                <field name="paid_to_branch_id"/>
                                <field name="user_id"/>
                                <field name="person_signature"/>
                                <field name="suke_book_number"/>
                                <field name="serial_no"/>
                                <field name="amount"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="sukuk_books_view_tree" model="ir.ui.view">
        <field name="name">sukuk.management.item.tree</field>
        <field name="model">sukuk.management.item</field>
        <field name="arch" type="xml">
            <tree create="false" delete="false" edit="false">
                <field name="suke_book_number"/>
                <field name="state" widget="badge" decoration-info="state == 'draft'"
                       decoration-warning="state == 'cancel'"
                       decoration-success="state == 'confirmed'"/>
                <field name="account_no_id"/>
                <field name="bank_id"/>
                <field name="branch_id"/>
                <field name="person_signature"/>
                <field name="suke_book_number"/>
                <field name="number_of_page_received" string="عدد اوراق الدفتر"/>
                <field name="serial_no_from"/>
                <field name="serial_no_to"/>
            </tree>
        </field>
    </record>

    <record id="action_sukuk_books_view" model="ir.actions.act_window">
        <field name="name">دفاتر الصكوك</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sukuk.management.item</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                There is nothing yet!
            </p>
        </field>
    </record>


    <menuitem id="sukuk_books_main_menu"
              name="دفاتر الصكوك"
              parent="sukuk_management_main_menu"
              action="action_sukuk_books_view"
              sequence="2"/>

</odoo>
