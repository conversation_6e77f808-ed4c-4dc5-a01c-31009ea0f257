# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_de_purchase
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-06-30 07:07+0000\n"
"PO-Revision-Date: 2021-07-29 12:12+0200\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.3\n"
"Last-Translator: \n"
"Language: de\n"

#. module: l10n_de_purchase
#: code:addons/l10n_de_purchase/models/purchase.py:0
#, python-format
msgid "Cancelled Purchase Order"
msgstr "Stornierte Bestellung"

#. module: l10n_de_purchase
#: code:addons/l10n_de_purchase/models/purchase.py:0
#, python-format
msgid "Cancelled Purchase Order No."
msgstr "Stornierte Bestellnr."

#. module: l10n_de_purchase
#: model:ir.model.fields,field_description:l10n_de_purchase.field_purchase_order__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: l10n_de_purchase
#: model:ir.model.fields,field_description:l10n_de_purchase.field_purchase_order__id
msgid "ID"
msgstr "ID"

#. module: l10n_de_purchase
#: code:addons/l10n_de_purchase/models/purchase.py:0
#, python-format
msgid "Incoterm"
msgstr "Incoterm"

#. module: l10n_de_purchase
#: model:ir.model.fields,field_description:l10n_de_purchase.field_purchase_order__l10n_de_addresses
msgid "L10N De Addresses"
msgstr ""

#. module: l10n_de_purchase
#: model:ir.model.fields,field_description:l10n_de_purchase.field_purchase_order__l10n_de_document_title
msgid "L10N De Document Title"
msgstr ""

#. module: l10n_de_purchase
#: model:ir.model.fields,field_description:l10n_de_purchase.field_purchase_order__l10n_de_template_data
msgid "L10N De Template Data"
msgstr ""

#. module: l10n_de_purchase
#: model:ir.model.fields,field_description:l10n_de_purchase.field_purchase_order____last_update
msgid "Last Modified on"
msgstr "Zuletzt bearbeitet am"

#. module: l10n_de_purchase
#: code:addons/l10n_de_purchase/models/purchase.py:0
#, python-format
msgid "Order Date"
msgstr "Bestelldatum"

#. module: l10n_de_purchase
#: code:addons/l10n_de_purchase/models/purchase.py:0
#, python-format
msgid "Order Reference"
msgstr "Bestellreferenz"

#. module: l10n_de_purchase
#: code:addons/l10n_de_purchase/models/purchase.py:0
#: model:ir.model,name:l10n_de_purchase.model_purchase_order
#, python-format
msgid "Purchase Order"
msgstr "Bestellung"

#. module: l10n_de_purchase
#: code:addons/l10n_de_purchase/models/purchase.py:0
#, python-format
msgid "Purchase Order No."
msgstr "Bestellnummer"

#. module: l10n_de_purchase
#: code:addons/l10n_de_purchase/models/purchase.py:0
#, python-format
msgid "Purchase Representative"
msgstr "Bestellbeauftragte(r)"

#. module: l10n_de_purchase
#: code:addons/l10n_de_purchase/models/purchase.py:0
#, python-format
msgid "Request for Quotation"
msgstr "Angebotsanfrage"

#. module: l10n_de_purchase
#: code:addons/l10n_de_purchase/models/purchase.py:0
#, python-format
msgid "Request for Quotation No."
msgstr "Angebotsanfragenr."

#. module: l10n_de_purchase
#: code:addons/l10n_de_purchase/models/purchase.py:0
#, python-format
msgid "Shipping Address:"
msgstr "Lieferadresse:"
