<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <t t-name="ProductsWidget" owl="1">
      <div class="products-widget">
            <ProductsWidgetControlPanel mobileSearchBarIsShown="props.mobileSearchBarIsShown" breadcrumbs="breadcrumbs" subcategories="subcategories" hasNoCategories="hasNoCategories" />
            <ProductList products="productsToDisplay" searchWord="searchWord" />
        </div>
    </t>

</templates>
