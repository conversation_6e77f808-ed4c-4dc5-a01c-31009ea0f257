<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">


        <record id="hr_rule_marige" model="hr.salary.rule">
            <field name="name">مكافئة زواج</field>
            <field name="sequence" eval="5"/>
            <field name="code">MARIGE</field>
            <field name="category_id" ref="hr_payroll_community.ALW"/>
            <field name="condition_select">python</field>
            <field name="condition_python">result = payslip.look_for_marige</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = payslip.look_for_marige</field>
        </record>

        <record id="hr_car_allawance_rule" model="hr.salary.rule">
            <field name="name">حركة سيارة</field>
            <field name="sequence" eval="5"/>
            <field name="code">CARALW</field>
            <field name="category_id" ref="hr_payroll_community.ALW"/>
            <field name="condition_select">python</field>
            <field name="condition_python">result = payslip.car_allawance_amount</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = payslip.car_allawance_amount</field>
        </record>

<!--        <record id="hr_rule_overtime_masarat" model="hr.salary.rule">-->
<!--            <field name="name">OverTime</field>-->
<!--            <field name="sequence" eval="7"/>-->
<!--            <field name="code">OVR</field>-->
<!--            <field name="category_id" ref="hr_payroll_community.ALW"/>-->
<!--            <field name="amount_select">code</field>-->
<!--            <field name="amount_python_compute">total_computed_overtime_workhours = (payslip.over_time_hours_at_home)+(payslip.over_time_hours_at_work*2)+(payslip.over_time_hours_at_holiday*3)-->
<!--                result = total_computed_overtime_workhours *(contract.wage/22/payslip.work_hour_day)-->
<!--            </field>-->
<!--        </record>-->

        <record id="hr_rule_absence_masarat" model="hr.salary.rule">
            <field name="name">Absence</field>
            <field name="sequence" eval="7"/>
            <field name="code">ABS</field>
            <field name="category_id" ref="hr_payroll_community.ALW"/>
            <field name="amount_select">code</field>
           <field name="amount_python_compute">approved_absence = (payslip.approved_absence_days)*(contract.wage/22)
                                               non_approved_absence= (payslip.non_approved_absence_days)*(contract.wage/22)*3
                                               result = approved_absence + non_approved_absence
           </field>
        </record>

        <record id="hr_rule_latency_masarat" model="hr.salary.rule">
            <field name="name">Latency</field>
            <field name="sequence" eval="7"/>
            <field name="code">LAT</field>
            <field name="category_id" ref="hr_payroll_community.ALW"/>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = (payslip.unapproved_latency_minutes/60)*(contract.wage/22/8)
            </field>
        </record>

        <record id="hr_rule_mukafa_masarat" model="hr.salary.rule">
            <field name="name">mokafa</field>
            <field name="sequence" eval="7"/>
            <field name="code">mokafa</field>
            <field name="category_id" ref="hr_payroll_community.ALW"/>
            <field name="condition_select">python</field>
            <field name="condition_python">result = payslip.masarat_reward</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = payslip.masarat_reward</field>
        </record>

        <!--        <record id="hr_rule_workfromhome" model="hr.salary.rule">-->
<!--            <field name="name">خصم تكليف بعمل من المنزل</field>-->
<!--            <field name="sequence" eval="5"/>-->
<!--            <field name="code">WORKHOME</field>-->
<!--            <field name="category_id" ref="hr_payroll_community.DED"/>-->
<!--            <field name="condition_select">python</field>-->
<!--            <field name="condition_python">result=payslip.work_assighnment_deduction</field>-->
<!--            <field name="amount_select">code</field>-->
<!--            <field name="amount_python_compute">result=(payslip.work_assighnment_deduction*(contract.wage/22/payslip.work_hour_day))/2</field>-->
<!--        </record>-->

    </data>
</odoo>
