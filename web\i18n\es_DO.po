# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web
#
# Translators:
# <PERSON> <aju<PERSON><PERSON><EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015-2016
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-10 11:35+0000\n"
"PO-Revision-Date: 2016-03-21 01:11+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  es_DO.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  es_DO.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:125
#, python-format
msgid " & Close"
msgstr " & Cerrar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:481
#, python-format
msgid " [Me]"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:223
#, python-format
msgid " and "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:565
#: code:addons/web/static/src/js/chrome/search_view.js:203
#, python-format
msgid " or "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:246
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:149
#, python-format
msgid " records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/view_manager.js:307
#, python-format
msgid " view couldn't be loaded"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:649
#, python-format
msgid "# Code editor"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:107
#: code:addons/web/static/src/js/chrome/search_filters.js:342
#, python-format
msgid "%(field)s %(operator)s"
msgstr "%(field)s %(operator)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:108
#, python-format
msgid "%(field)s %(operator)s \"%(value)s\""
msgstr "%(field)s %(operator)s \"%(value)s\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/view_manager.js:106
#, python-format
msgid "%(view_type)s view"
msgstr "Vista %(view_type)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:114
#, python-format
msgid "%d days ago"
msgstr "hace %d días"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:112
#, python-format
msgid "%d hours ago"
msgstr "hace %d horas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:110
#, python-format
msgid "%d minutes ago"
msgstr "hace %d minutos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:116
#, python-format
msgid "%d months ago"
msgstr "hace %d meses"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:141
#, python-format
msgid "%d requests (%d ms) %d queries (%d ms)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:118
#, python-format
msgid "%d years ago"
msgstr "hace %d años"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.webclient_bootstrap
msgid ""
"&lt;!--[if lt IE 10]&gt;\n"
"                        &lt;body class=\"ie9\"&gt;\n"
"                    &lt;![endif]--&gt;"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.minimal_layout
msgid "&lt;!DOCTYPE html&gt;"
msgstr "&lt;!DOCTYPE html&gt;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:371
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' no es una fecha correcta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:193
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s' no es una fecha, fechahora ni hora correcta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:418
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' no es una fecha-hora correcta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:429
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' no es un decimal correcto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:499
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' no es un entero correcto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:465
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:205
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr "'%s' no es convertible a fecha, fechahora ni hora"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:77
#, python-format
msgid "(no string)"
msgstr "(sin cadena)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:10
#, python-format
msgid "(nolabel)"
msgstr "(sin etiqueta)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:244
#, python-format
msgid "1 record"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "<span class=\"oe_logo_edit\">Edit Company data</span>"
msgstr "<span class=\"oe_logo_edit\">Editar datos de empresa</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "<span>Odoo</span>"
msgstr "<span>Odoo</span>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:626
#, python-format
msgid "ALL"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:627
#, python-format
msgid "ANY"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:19
#, python-format
msgid "Access Denied"
msgstr "Acceso denegado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:15
#, python-format
msgid "Access Error"
msgstr "Error de acceso"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1341
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "Acceso a todas las Apps Enterprise"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:34
#, python-format
msgid "Action"
msgstr "Acción"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:111
#, python-format
msgid "Action ID:"
msgstr "Acción ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1617
#, python-format
msgid "Activate"
msgstr "Activar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:230
#, python-format
msgid "Activate Assets Debugging"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1616
#: code:addons/web/static/src/js/fields/basic_fields.js:1620
#: code:addons/web/static/src/js/fields/basic_fields.js:1624
#, python-format
msgid "Active"
msgstr "Activo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:1197
#: code:addons/web/static/src/xml/base.xml:1184
#: code:addons/web/static/src/xml/kanban.xml:54
#: code:addons/web/static/src/xml/kanban.xml:64
#, python-format
msgid "Add"
msgstr "Agregar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1006
#, python-format
msgid "Add Custom Filter"
msgstr "Filtro Personalizado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1010
#, python-format
msgid "Add a condition"
msgstr "Agregar condición"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:512
#, python-format
msgid "Add an item"
msgstr "Añadir un elemento"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1119
#, python-format
msgid "Add custom group"
msgstr "Añadir grupo personalizado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:634
#, python-format
msgid "Add filter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:48
#, python-format
msgid "Add new Column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:694
#, python-format
msgid "Add new value"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1541
#, python-format
msgid "Add to Favorites"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:358
#, python-format
msgid "Add..."
msgstr "Añadir..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:1220
#, python-format
msgid "Add: "
msgstr "Añadir: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:983
#, python-format
msgid "Advanced Search..."
msgstr "Búsqueda avanzada..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:209
#, python-format
msgid "Alert"
msgstr "Alerta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:616
#: code:addons/web/static/src/xml/base.xml:621
#, python-format
msgid "All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:367
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:351
#, python-format
msgid "All day"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:488
#, python-format
msgid "All users"
msgstr "Todos los usuarios"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:146
#, python-format
msgid "An error occurred"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:188
#, python-format
msgid ""
"An unknown CORS error occured. The error probably originates from a "
"JavaScript file served from a different origin."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1346
#, python-format
msgid "And more"
msgstr "Y más"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:617
#: code:addons/web/static/src/xml/base.xml:622
#, python-format
msgid "Any"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1009
#: code:addons/web/static/src/xml/base.xml:1130
#, python-format
msgid "Apply"
msgstr "Aplicar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1621
#: code:addons/web/static/src/js/views/list/list_controller.js:138
#, python-format
msgid "Archive"
msgstr "Archivar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:26
#, python-format
msgid "Archive Records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1620
#, python-format
msgid "Archived"
msgstr "Archivado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:273
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:259
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "¿Seguro que quieres eliminar este filtro?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:290
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:299
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:331
#, python-format
msgid "Attachment :"
msgstr "Adjunto:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1180
#, python-format
msgid "Available fields"
msgstr "Campos disponibles"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:509
#, python-format
msgid "Bar Chart"
msgstr "Gráfico de barras"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1345
#, python-format
msgid "Bugfixes guarantee"
msgstr "Garantiza la corrección de errores"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:74
#, python-format
msgid "Button"
msgstr "Botón"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:103
#, python-format
msgid "Button Type:"
msgstr "Tipo de Botón"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:129
#, python-format
msgid "Bytes,Kb,Mb,Gb,Tb,Pb,Eb,Zb,Yb"
msgstr "Bytes,Kb,Mb,Gb,Tb,Pb,Eb,Zb,Yb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:22
#, python-format
msgid "Calendar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:38
#, python-format
msgid "Calendar view has not defined 'date_start' attribute."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:223
#: code:addons/web/static/src/js/core/dialog.js:275
#: code:addons/web/static/src/js/fields/relational_fields.js:65
#: code:addons/web/static/src/js/fields/upgrade_fields.js:75
#: code:addons/web/static/src/js/services/crash_manager.js:203
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:56
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:267
#: code:addons/web/static/src/js/views/view_dialogs.js:364
#: code:addons/web/static/src/xml/base.xml:176
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:69
#, python-format
msgid "Cannot render chart with mode : "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:779
#: code:addons/web/controllers/main.py:781
#: code:addons/web/controllers/main.py:786
#: code:addons/web/controllers/main.py:787
#: code:addons/web/static/src/js/widgets/change_password.js:27
#: code:addons/web/static/src/xml/base.xml:175
#, python-format
msgid "Change Password"
msgstr "Cambiar la contraseña"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:49
#, python-format
msgid "Change default:"
msgstr "Cambiar por defecto:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:886
#: code:addons/web/static/src/xml/base.xml:917
#, python-format
msgid "Clear"
msgstr "Limpiar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:227
#, python-format
msgid "Clear Events"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1625
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:310
#: code:addons/web/static/src/js/views/view_dialogs.js:111
#: code:addons/web/static/src/js/widgets/data_export.js:203
#: code:addons/web/static/src/js/widgets/debug_manager.js:428
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:24
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1624
#, python-format
msgid "Closed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:52
#, python-format
msgid "Column title"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:462
#, python-format
msgid "Condition:"
msgstr "Condición:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:170
#, python-format
msgid "Confirm New Password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:234
#: code:addons/web/static/src/js/core/dialog.js:284
#, python-format
msgid "Confirmation"
msgstr "Confirmación"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:291
#, python-format
msgid "Connection lost"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:300
#, python-format
msgid "Connection restored"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:37
#: code:addons/web/static/src/xml/base.xml:90
#, python-format
msgid "Context:"
msgstr "Contexto:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:132
#, python-format
msgid "Copied !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:144
#, python-format
msgid "Copy the full error to clipboard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1199
#, python-format
msgid "Could not display the selected image."
msgstr "No se pudo mostrar la imagen seleccionada."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:432
#, python-format
msgid "Could not serialize XML"
msgstr "No pudo serializar el XML"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:43
#: code:addons/web/static/src/js/views/graph/graph_view.js:56
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:39
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:58
#: code:addons/web/static/src/xml/base.xml:505
#: code:addons/web/static/src/xml/base.xml:532
#, python-format
msgid "Count"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:44
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:224
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:45
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:80
#: code:addons/web/static/src/js/views/kanban/kanban_controller.js:343
#: code:addons/web/static/src/js/views/view_dialogs.js:370
#: code:addons/web/static/src/xml/base.xml:404
#: code:addons/web/static/src/xml/base.xml:424
#, python-format
msgid "Create"
msgstr "Crear"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:475
#, python-format
msgid "Create "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:367
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "Crear \"<strong>%s</strong>\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:41
#, python-format
msgid "Create a %s"
msgstr "Crear un %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:376
#, python-format
msgid "Create and Edit..."
msgstr "Crear y editar..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:55
#, python-format
msgid "Create and edit"
msgstr "Crear y editar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:405
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:85
#, python-format
msgid "Create: "
msgstr "Crear: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:336
#, python-format
msgid "Created by :"
msgstr "Creado por:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:290
#, python-format
msgid "Creation Date:"
msgstr "Fecha de Creación:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:286
#, python-format
msgid "Creation User:"
msgstr "Usuario de Creación:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:180
#, python-format
msgid "Custom Filter"
msgstr "Filtro Personalizado"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "Base de datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:550
#: code:addons/web/static/src/xml/web_calendar.xml:71
#, python-format
msgid "Day"
msgstr "Día"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1617
#, python-format
msgid "Deactivate"
msgstr "Desactivar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:445
#, python-format
msgid "Default:"
msgstr "Por defecto:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:297
#: code:addons/web/static/src/js/views/form/form_controller.js:155
#: code:addons/web/static/src/js/views/list/list_controller.js:148
#: code:addons/web/static/src/xml/base.xml:1225
#: code:addons/web/static/src/xml/kanban.xml:23
#, python-format
msgid "Delete"
msgstr "Suprimir"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:347
#, python-format
msgid "Delete this attachment"
msgstr "Eliminar este adjunto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:964
#, python-format
msgid "Delete this file"
msgstr "Eliminar este archivo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:111
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:31
#: code:addons/web/static/src/xml/base.xml:411
#: code:addons/web/static/src/xml/base.xml:434
#: code:addons/web/static/src/xml/kanban.xml:66
#: code:addons/web/static/src/xml/report.xml:18
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:336
#, python-format
msgid "Do you really want to delete this export template?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:132
#, python-format
msgid "Do you really want to delete this filter from favorites ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1319
#, python-format
msgid "Documentation"
msgstr "Documentación"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:36
#, python-format
msgid "Domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:501
#, python-format
msgid "Domain error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:41
#, python-format
msgid "Domain:"
msgstr "Dominio:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:16
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "No te vayas todavía,<br /> todavía está cargando..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:538
#, python-format
msgid "Download xls"
msgstr "Descargar xls"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:161
#, python-format
msgid "Duplicate"
msgstr "Duplicar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:291
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:50
#: code:addons/web/static/src/xml/base.xml:266
#: code:addons/web/static/src/xml/base.xml:420
#: code:addons/web/static/src/xml/base.xml:885
#: code:addons/web/static/src/xml/kanban.xml:65
#: code:addons/web/static/src/xml/report.xml:14
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:236
#, python-format
msgid "Edit Action"
msgstr "Editar Acción"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:286
#, python-format
msgid "Edit Column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:602
#, python-format
msgid "Edit Domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:267
#, python-format
msgid "Edit SearchView"
msgstr "Editar SearchView"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:22
#, python-format
msgid "Edit Stage"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Email"
msgstr "Email"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Email:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:86
#: code:addons/web/static/src/js/chrome/search_menus.js:93
#: code:addons/web/static/src/js/chrome/view_manager.js:307
#: code:addons/web/static/src/js/services/crash_manager.js:135
#, python-format
msgid "Error"
msgstr "Error"

#. module: web
#: code:addons/web/controllers/main.py:787
#, python-format
msgid "Error, password not changed !"
msgstr "¡Error, contraseña no modificada!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:490
#, python-format
msgid "Everybody's calendars"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:490
#, python-format
msgid "Everything"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:537
#, python-format
msgid "Expand all"
msgstr "Expandir todo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:133
#, python-format
msgid "Export"
msgstr "Exportar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:200
#, python-format
msgid "Export Data"
msgstr "Exportar información"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1173
#, python-format
msgid "Export Formats :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:202
#, python-format
msgid "Export To File"
msgstr "Exportar a fichero"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1166
#, python-format
msgid "Export Type :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1169
#, python-format
msgid "Export all Data"
msgstr "Exportar todos los datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/view_manager.js:512
#, python-format
msgid "Failed to evaluate search criterions"
msgstr "Error evaluando los criterios de búsqueda"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:45
#, python-format
msgid "False"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1135
#, python-format
msgid "Favorites"
msgstr "Favoritos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:17
#, python-format
msgid "Field:"
msgstr "Campo:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:465
#: code:addons/web/static/src/xml/base.xml:264
#, python-format
msgid "Fields View Get"
msgstr "Obtener Campos de Vista"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:248
#, python-format
msgid "Fields of %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1192
#, python-format
msgid "Fields to export"
msgstr "Campos a exportar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1118
#, python-format
msgid "File Upload"
msgstr "Subir archivo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1098
#, python-format
msgid "File upload"
msgstr "Subir Archivos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:563
#, python-format
msgid "Filter"
msgstr "Filtro"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:86
#, python-format
msgid "Filter name is required."
msgstr "Se requiere un nombre para el filtro"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:507
#, python-format
msgid "Filter on: %s"
msgstr "Filtrar por: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:93
#, python-format
msgid "Filter with same name already exists."
msgstr "Filtro con el mismo nombre ya existe."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1002
#, python-format
msgid "Filters"
msgstr "Filtros"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:536
#, python-format
msgid "Flip axis"
msgstr "Flip axis"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:20
#, python-format
msgid "Fold"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_controller.js:128
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 256 "
"columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_view.js:17
#, python-format
msgid "Form"
msgstr "Formulario"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1338
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr "Obtener esta característica y mucho más con el Odoo Enterprise!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:18
#, python-format
msgid "Global Business Error"
msgstr "Global Business Error"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:20
#, python-format
msgid "Graph"
msgstr "Gráfico"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:716
#: code:addons/web/static/src/xml/base.xml:1114
#, python-format
msgid "Group By"
msgstr "Agrupar por"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:691
#, python-format
msgid "Group by: %s"
msgstr "Agrupar por: %s"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP routing"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:250
#, python-format
msgid "I am sure about this."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:274
#, python-format
msgid "ID:"
msgstr "ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1199
#, python-format
msgid "Image"
msgstr "Imagen"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1168
#, python-format
msgid "Import-Compatible Export"
msgstr "Importación-Exportación Compatible"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1616
#, python-format
msgid "Inactive"
msgstr "Inactivo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:205
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:213
#, python-format
msgid "Invalid data"
msgstr "Argumentos no válidos"

#. module: web
#: code:addons/web/controllers/main.py:683
#: code:addons/web/controllers/main.py:697
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:600
#, python-format
msgid "Invalid domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:393
#: code:addons/web/static/src/xml/base.xml:731
#, python-format
msgid "Invalid field chain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:68
#, python-format
msgid "Invalid mode for chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:194
#, python-format
msgid "JS Tests"
msgstr "Tests JS"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_view.js:16
#, python-format
msgid "Kanban"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:794
#, python-format
msgid "Languages"
msgstr "Idiomas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:298
#, python-format
msgid "Latest Modification Date:"
msgstr "Última  fecha de modificación:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:294
#, python-format
msgid "Latest Modification by:"
msgstr "Última Modificación por:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:231
#, python-format
msgid "Leave the Developer Tools"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:510
#, python-format
msgid "Line Chart"
msgstr "Gráfico de líneas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_view.js:21
#, python-format
msgid "List"
msgstr "Lista"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:41
#, python-format
msgid "Load more... ("
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:58
#, python-format
msgid "Loading"
msgstr "Cargando"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:56
#, python-format
msgid "Loading (%d)"
msgstr "Cargando (%d)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:13
#, python-format
msgid "Loading..."
msgstr "Cargando…"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Iniciar Sesión"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1324
#, python-format
msgid "Log out"
msgstr "Cerrar sesión"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:456
#, python-format
msgid "M2O search fields do not currently handle multiple default values"
msgstr "Los campos de búsqueda M2O no soportan actualmente valores por defecto"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
msgid "Mail:"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "Gestionar Bases de datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:258
#: code:addons/web/static/src/xml/base.xml:239
#, python-format
msgid "Manage Filters"
msgstr "Gestionar Filtros"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:633
#, python-format
msgid "Match"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:640
#, python-format
msgid "Match records with"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:638
#, python-format
msgid "Match records with the following rule:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:19
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr ""
"Tal vez usted podría considerar volver a cargar la aplicación presionando "
"F5 ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:497
#: code:addons/web/static/src/xml/base.xml:524
#, python-format
msgid "Measures"
msgstr "Medidas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:342
#, python-format
msgid "Metadata (%s)"
msgstr "Metadata (%s)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:107
#, python-format
msgid "Method:"
msgstr "Método:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:16
#, python-format
msgid "Missing Record"
msgstr "Missing Record"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1343
#, python-format
msgid "Mobile support"
msgstr "Soporte móvil"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:341
#, python-format
msgid "Modified by :"
msgstr "Modificado por:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:45
#: code:addons/web/static/src/xml/base.xml:94
#, python-format
msgid "Modifiers:"
msgstr "Modificadores:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:552
#: code:addons/web/static/src/xml/web_calendar.xml:73
#, python-format
msgid "Month"
msgstr "Mes"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_renderer.js:276
#: code:addons/web/static/src/xml/base.xml:868
#, python-format
msgid "More"
msgstr "Más"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu
msgid "More <b class=\"caret\"/>"
msgstr "More <b class=\"caret\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1188
#, python-format
msgid "Move Down"
msgstr "Move Down"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1187
#, python-format
msgid "Move Up"
msgstr "Move Up"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1323
#, python-format
msgid "My Odoo.com account"
msgstr "Mi cuenta Odoo.com"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:628
#, python-format
msgid "NONE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1242
#, python-format
msgid "Name:"
msgstr "Nombre:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_model.js:491
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:166
#, python-format
msgid "New Password"
msgstr "Nueva contraseña"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1342
#, python-format
msgid "New design"
msgstr "Nuevo diseño"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:333
#, python-format
msgid "No"
msgstr "No"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:282
#, python-format
msgid "No Update:"
msgstr "No Update:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:75
#, python-format
msgid ""
"No data available for this chart. Try to add some records, or make sure that "
"there is no active filter in the search bar."
msgstr ""
"No hay datos disponibles para esta tabla. Trate de añadir algunos registros, "
"o asegurarse de que no hay un filtro activo en la barra de búsqueda."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:568
#, python-format
msgid ""
"No data available for this pivot table.  Try to add some records, or make "
"sure\n"
"        that there is at least one measure and no active filter in the "
"search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:74
#, python-format
msgid "No data to display"
msgstr "Sin datos que mostrar."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:567
#, python-format
msgid "No data to display."
msgstr "Sin datos que mostrar."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:330
#, python-format
msgid "No metadata available"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:242
#, python-format
msgid "No records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:382
#, python-format
msgid "No results to show..."
msgstr "Sin resultados que mostrar..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:618
#, python-format
msgid "None"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:252
#, python-format
msgid "Not enough data points"
msgstr "No suficientes puntos de datos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:21
#: code:addons/web/static/src/xml/base.xml:86
#, python-format
msgid "Object:"
msgstr "Objeto:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:51
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:148
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "Odoo Apps estará disponible en breve"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:186
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:194
#: code:addons/web/static/src/js/services/crash_manager.js:156
#, python-format
msgid "Odoo Client Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:86
#, python-format
msgid "Odoo Enterprise"
msgstr "Odoo Enterprise"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:118
#, python-format
msgid "Odoo Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:58
#, python-format
msgid "Odoo Session Expired"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:108
#: code:addons/web/static/src/js/services/crash_manager.js:197
#, python-format
msgid "Odoo Warning"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1633
#, python-format
msgid "Off"
msgstr "Off"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:55
#: code:addons/web/static/src/js/core/dialog.js:199
#: code:addons/web/static/src/js/core/dialog.js:217
#: code:addons/web/static/src/js/core/dialog.js:268
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:262
#: code:addons/web/static/src/xml/base.xml:1215
#, python-format
msgid "Ok"
msgstr "Aceptar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:162
#, python-format
msgid "Old Password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1632
#, python-format
msgid "On"
msgstr "On"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:53
#, python-format
msgid "On change:"
msgstr "Al cambiar:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1879
#, python-format
msgid "Only Integer Value should be valid."
msgstr "El único valor válido es un entero."

#. module: web
#: code:addons/web/controllers/main.py:489
#, python-format
msgid ""
"Only employee can access this database. Please contact the administrator."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:481
#, python-format
msgid "Only you"
msgstr "Sólo usted"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1625
#, python-format
msgid "Open"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:215
#, python-format
msgid "Open Developer Tools"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:223
#, python-format
msgid "Open View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:470
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:277
#: code:addons/web/static/src/js/views/form/form_controller.js:475
#: code:addons/web/static/src/js/views/form/form_controller.js:494
#, python-format
msgid "Open: "
msgstr "Abrir: "

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Contraseña"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Phone:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:511
#, python-format
msgid "Pie Chart"
msgstr "Gráfico de tarta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:214
#, python-format
msgid ""
"Pie chart cannot display all zero numbers.. Try to change your domain to "
"display positive results"
msgstr ""
"Gráfico circular no puede mostrar todos los números cero .. Trate de cambiar "
"su dominio a mostrar resultados positivos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:206
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""
"Gráfico circular no puede mezclar los números positivos y negativos. Trate "
"de cambiar su dominio sólo para mostrar resultados positivos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:23
#, python-format
msgid "Pivot"
msgstr "Pivot"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_renderer.js:331
#, python-format
msgid "Please click on the \"save\" button first"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:168
#, python-format
msgid "Please enter save field list name"
msgstr "Por favor, introduzca el nombre de la lista de campos a guardar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1203
#, python-format
msgid "Please note that only the selected ids will be exported."
msgstr "Tenga en cuenta que sólo se exportarán los registros seleccionados."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1202
#, python-format
msgid ""
"Please pay attention that all records matching your search filter will be "
"exported. Not only the selected ids."
msgstr ""
"Tenga en cuenta que todos los registros que cumplen el filtro de búsqueda "
"serán exportados, no sólo los seleccionados."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:455
#, python-format
msgid "Please select fields to export..."
msgstr "Por favor, seleccione los campos a exportar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:441
#, python-format
msgid "Please select fields to save export list..."
msgstr ""
"Por favor, seleccione los campos para guardar la lista de exportación..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:147
#, python-format
msgid "Please use the copy button to report the error to your support service."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "Powered by"
msgstr "Powered by"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr "Powered by <span>Odoo</span>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1322
#, python-format
msgid "Preferences"
msgstr "Preferencias"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:33
#: code:addons/web/static/src/xml/report.xml:11
#, python-format
msgid "Print"
msgstr "Imprimir"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:553
#, python-format
msgid "Quarter"
msgstr "Trimestre"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:455
#, python-format
msgid "Relation not allowed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:57
#, python-format
msgid "Relation:"
msgstr "Relación:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1185
#, python-format
msgid "Remove"
msgstr "Eliminar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1186
#, python-format
msgid "Remove All"
msgstr "Eliminar Todo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1541
#, python-format
msgid "Remove from Favorites"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:57
#, python-format
msgid "Remove this favorite from the list"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:94
#, python-format
msgid "Report"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1621
#, python-format
msgid "Restore"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:27
#, python-format
msgid "Restore Records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:222
#, python-format
msgid "Run JS Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:125
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:28
#: code:addons/web/static/src/xml/base.xml:408
#: code:addons/web/static/src/xml/base.xml:430
#: code:addons/web/static/src/xml/base.xml:1153
#: code:addons/web/static/src/xml/report.xml:17
#, python-format
msgid "Save"
msgstr "Guardar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:134
#, python-format
msgid "Save & New"
msgstr "Guardar y Nuevo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1254
#, python-format
msgid "Save As..."
msgstr "Guardar como..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1215
#, python-format
msgid "Save as:"
msgstr "Guardar como:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1141
#, python-format
msgid "Save current search"
msgstr "Guardar filtro actual"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:429
#, python-format
msgid "Save default"
msgstr "Guardar por defecto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1192
#, python-format
msgid "Save fields list"
msgstr "Guardar lista de campos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1218
#, python-format
msgid "Saved exports:"
msgstr "Exportaciones guardadas:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:361
#, python-format
msgid "Search %(field)s at: %(value)s"
msgstr "Buscar %(field)s en: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:194
#: code:addons/web/static/src/js/chrome/search_inputs.js:213
#: code:addons/web/static/src/js/chrome/search_inputs.js:402
#, python-format
msgid "Search %(field)s for: %(value)s"
msgstr "Buscar %(field)s para: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:343
#, python-format
msgid "Search More..."
msgstr "Buscar más..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:985
#, python-format
msgid "Search..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:405
#, python-format
msgid "Search: "
msgstr "Buscar: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:151
#, python-format
msgid "See details"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:377
#: code:addons/web/static/src/xml/base.xml:915
#: code:addons/web/static/src/xml/base.xml:916
#, python-format
msgid "Select"
msgstr "Seleccionar"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Select <i class=\"fa fa-database\"/>"
msgstr "Select <i class=\"fa fa-database\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:604
#, python-format
msgid "Select a model to add a filter."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:169
#, python-format
msgid "Select a view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2289
#, python-format
msgid "Selected records"
msgstr "Registros seleccionados"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:61
#, python-format
msgid "Selection:"
msgstr "Selección:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:426
#, python-format
msgid "Set Default"
msgstr "Establecer Por defecto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:259
#, python-format
msgid "Set Defaults"
msgstr "Establecer por Defecto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1150
#, python-format
msgid "Share with all users"
msgstr "Compartir con todos los usuarios"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:148
#, python-format
msgid "Showing locally available modules"
msgstr "Mostrando los módulos locales disponibles"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:33
#, python-format
msgid "Size:"
msgstr "Tamaño:"

#. module: web
#: code:addons/web/controllers/main.py:1105
#, python-format
msgid "Something horrible happened"
msgstr "Sucedió algo horrible"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:98
#, python-format
msgid "Special:"
msgstr "Especial:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:14
#, python-format
msgid "Still loading..."
msgstr "Aún cargando..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:15
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "Todavía está cargando ... <br /> Por favor, sea paciente."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:80
#, python-format
msgid "Summary:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1320
#, python-format
msgid "Support"
msgstr "Soporte"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1634
#, python-format
msgid "Switch Off"
msgstr "Apagar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1635
#, python-format
msgid "Switch On"
msgstr "Encender"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:548
#, python-format
msgid "Syntax error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:18
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr "Tome un minuto para ir por un café,<br />porque está cargando ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/switch_company_menu.js:38
#, python-format
msgid "Tap on the list to change company"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:240
#, python-format
msgid "Technical Translation"
msgstr "Traducción técnica"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
msgid "Tel:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:502
#: code:addons/web/static/src/js/widgets/domain_selector.js:548
#, python-format
msgid "The domain you entered is not properly formed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:394
#, python-format
msgid ""
"The field chain is not valid. Did you maybe use a non-existing field name or "
"followed a non-relational field?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1254
#, python-format
msgid "The field is empty, there's nothing to save !"
msgstr "Este campo esta vacio, !no hay nada que guardar!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:380
#, python-format
msgid "The following fields are invalid:"
msgstr "Los siguientes campos son inválidos:"

#. module: web
#: code:addons/web/controllers/main.py:781
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "La nueva contraseña y su confirmación deben ser idénticas."

#. module: web
#: code:addons/web/controllers/main.py:786
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr ""
"La antigua contraseña que se ha introducido no es correcta. Su contraseña no "
"se ha cambiado."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:75
#, python-format
msgid ""
"The record has been modified, your changes will be discarded. Do you want to "
"proceed?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1097
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr "El archivo seleccionado supera el tamaño máximo de %s."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:1284
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to 'ir."
"attachment' model."
msgstr ""
"El tipo de campo '%s' debe ser un campo many2many con una relación a 'ir."
"attachment' model."

#. module: web
#: code:addons/web/controllers/main.py:1442
#, python-format
msgid ""
"There are too many rows (%s rows, limit: 65535) to export as Excel 97-2003 (."
"xls) format. Consider splitting the export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1118
#, python-format
msgid "There was a problem while uploading your file"
msgstr "Hubo un problema subiendo el fichero"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:258
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr ""
"Este filtro es global y se eliminará de todos los usuarios si continúa."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1159
#, python-format
msgid ""
"This wizard will export all data that matches the current search criteria to "
"a CSV file.\n"
"        You can export all data or only the fields that can be reimported "
"after modification."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/special_fields.js:93
#, python-format
msgid ""
"Timezone Mismatch : The timezone of your browser doesn't match the selected "
"one. The time in Odoo is displayed according to your field timezone."
msgstr ""
"Discrepancia Zona horaria: La zona horaria de su navegador no coincide con "
"el seleccionado. El tiempo en Odoo se muestra de acuerdo a su zona horaria "
"campo."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:63
#, python-format
msgid "Title"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:67
#, python-format
msgid "Today"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:226
#, python-format
msgid "Toggle Timelines"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:453
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:563
#, python-format
msgid "Total"
msgstr "Total"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:196
#, python-format
msgid "Traceback:"
msgstr "Traceback:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:45
#, python-format
msgid "True"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:292
#, python-format
msgid "Trying to reconnect..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:25
#, python-format
msgid "Type:"
msgstr "Tipo:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:17
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this \n"
"system. The report will be shown in html.<br><br><a href=\"http://"
"wkhtmltopdf.org/\" target=\"_blank\">\n"
"wkhtmltopdf.org</a>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:142
#, python-format
msgid "Unarchive"
msgstr "Desarchivar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_model.js:179
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:73
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:76
#: code:addons/web/static/src/js/views/list/list_renderer.js:422
#: code:addons/web/static/src/js/views/list/list_renderer.js:425
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:689
#, python-format
msgid "Undefined"
msgstr "Indefinido"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:574
#, python-format
msgid "Unhandled widget"
msgstr "Widget no controlado"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:187
#, python-format
msgid "Unknown CORS error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/pyeval.js:1001
#, python-format
msgid "Unknown nonliteral type "
msgstr "Tipo no literal desconocido "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/_deprecated/data.js:737
#, python-format
msgid "Unnamed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:95
#, python-format
msgid "Untitled"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1301
#, python-format
msgid "Update translations"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:69
#, python-format
msgid "Upgrade now"
msgstr "Actualizar ahora"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1344
#, python-format
msgid "Upgrade to future versions"
msgstr "Actualizar a versiones futuras "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:915
#, python-format
msgid "Upload your file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:1442
#, python-format
msgid "Uploading Error"
msgstr "Error de carga"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:888
#: code:addons/web/static/src/xml/base.xml:919
#: code:addons/web/static/src/xml/base.xml:974
#, python-format
msgid "Uploading..."
msgstr "Subiendo..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1147
#, python-format
msgid "Use by default"
msgstr "Usar por defecto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:17
#, python-format
msgid "Validation Error"
msgstr "Error de validacion"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:266
#, python-format
msgid "View"
msgstr "Vista"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:238
#, python-format
msgid "View Fields"
msgstr "Ver Campos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:261
#, python-format
msgid "View Metadata"
msgstr "Ver metadatos"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:13
#: code:addons/web/static/src/js/services/crash_manager.js:14
#: code:addons/web/static/src/js/views/basic/basic_controller.js:78
#: code:addons/web/static/src/js/views/list/list_renderer.js:331
#, python-format
msgid "Warning"
msgstr "Aviso"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Web:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:551
#: code:addons/web/static/src/xml/web_calendar.xml:72
#, python-format
msgid "Week"
msgstr "Semana"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:344
#, python-format
msgid "Week "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/rainbow_man.js:32
#, python-format
msgid "Well Done!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:29
#, python-format
msgid "Widget:"
msgstr "Widget:"

#. module: web
#: code:addons/web/controllers/main.py:486
#, python-format
msgid "Wrong login/password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1879
#, python-format
msgid "Wrong value entered!"
msgstr "Valor introducido no válido"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:278
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:554
#, python-format
msgid "Year"
msgstr "Año"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:332
#: code:addons/web/static/src/xml/base.xml:49
#, python-format
msgid "Yes"
msgstr "Sí"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:301
#, python-format
msgid "You are back online"
msgstr "Usted está de nuevo en línea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:71
#, python-format
msgid "You are creating a new %s, are you sure it does not exist yet?"
msgstr "Está creando un nuevo %s, ¿está seguro de que no existe ya?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:455
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:779
#, python-format
msgid "You cannot leave any password empty."
msgstr "No puede dejar vacía ninguna contraseña"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1299
#, python-format
msgid "You have updated"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:17
#, python-format
msgid "You may not believe it,<br />but the application is actually loading..."
msgstr ""
"Usted no lo creerá,<br />pero la aplicación actualmente está cargando ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:18
#, python-format
msgid ""
"You need to start OpenERP with at least two \n"
"workers to print a pdf version of the reports."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:19
#, python-format
msgid ""
"You should upgrade your version of\n"
"Wkhtmltopdf to at least 0.12.0 in order to get a correct display of headers "
"and footers as well as\n"
"support for table-breaking between pages.<br><br><a href=\"http://"
"wkhtmltopdf.org/\" \n"
"target=\"_blank\">wkhtmltopdf.org</a>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:58
#, python-format
msgid "Your Odoo session expired. Please refresh the current web page."
msgstr "Su sesión Odoo expiró. Refresque por favor la página web actual."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:20
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html.<br><br><a href=\"http://wkhtmltopdf.org/\" target=\"_blank"
"\">wkhtmltopdf.org</a>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:199
#, python-format
msgid "[No widget %s]"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:113
#, python-format
msgid "a day ago"
msgstr "hace un día"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:109
#, python-format
msgid "about a minute ago"
msgstr "hace un minuto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:115
#, python-format
msgid "about a month ago"
msgstr "hace un mes"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:117
#, python-format
msgid "about a year ago"
msgstr "hace un año"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:111
#, python-format
msgid "about an hour ago"
msgstr "Hace una hora"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:633
#, python-format
msgid "all records"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_base
msgid "base"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:28
#, python-format
msgid "child of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:163
#: code:addons/web/static/src/js/widgets/domain_selector.js:23
#, python-format
msgid "contains"
msgstr "contiene"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:164
#, python-format
msgid "doesn't contain"
msgstr "no contiene"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:263
#: code:addons/web/static/src/js/chrome/search_filters.js:292
#, python-format
msgid "greater than"
msgstr "mayor que"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:265
#: code:addons/web/static/src/js/chrome/search_filters.js:294
#, python-format
msgid "greater than or equal to"
msgstr "mayor o igual que"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:25
#, python-format
msgid "in"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
msgid "ir.qweb.field.image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:284
#: code:addons/web/static/src/js/chrome/search_filters.js:319
#: code:addons/web/static/src/js/widgets/domain_selector.js:828
#: code:addons/web/static/src/xml/base.xml:721
#, python-format
msgid "is"
msgstr "es"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:183
#, python-format
msgid "is after"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:184
#, python-format
msgid "is before"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:185
#, python-format
msgid "is between"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:165
#: code:addons/web/static/src/js/chrome/search_filters.js:181
#: code:addons/web/static/src/js/chrome/search_filters.js:261
#: code:addons/web/static/src/js/chrome/search_filters.js:290
#, python-format
msgid "is equal to"
msgstr "es igual a"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:338
#, python-format
msgid "is false"
msgstr "es falso"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:320
#: code:addons/web/static/src/js/widgets/domain_selector.js:829
#, python-format
msgid "is not"
msgstr "no es"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:166
#: code:addons/web/static/src/js/chrome/search_filters.js:182
#: code:addons/web/static/src/js/chrome/search_filters.js:262
#: code:addons/web/static/src/js/chrome/search_filters.js:291
#, python-format
msgid "is not equal to"
msgstr "no es igual a"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:168
#: code:addons/web/static/src/js/chrome/search_filters.js:187
#: code:addons/web/static/src/js/chrome/search_filters.js:268
#: code:addons/web/static/src/js/chrome/search_filters.js:297
#: code:addons/web/static/src/js/chrome/search_filters.js:322
#: code:addons/web/static/src/js/widgets/domain_selector.js:37
#, python-format
msgid "is not set"
msgstr "no está establecida(o)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:167
#: code:addons/web/static/src/js/chrome/search_filters.js:186
#: code:addons/web/static/src/js/chrome/search_filters.js:267
#: code:addons/web/static/src/js/chrome/search_filters.js:296
#: code:addons/web/static/src/js/chrome/search_filters.js:321
#: code:addons/web/static/src/js/widgets/domain_selector.js:36
#, python-format
msgid "is set"
msgstr "está establecida(o)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:337
#, python-format
msgid "is true"
msgstr "es verdadero"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:111
#, python-format
msgid "kMGTPE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:264
#: code:addons/web/static/src/js/chrome/search_filters.js:293
#, python-format
msgid "less than"
msgstr "menor que"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:108
#, python-format
msgid "less than a minute ago"
msgstr "hace menos de un minuto"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:266
#: code:addons/web/static/src/js/chrome/search_filters.js:295
#, python-format
msgid "less than or equal to"
msgstr "menor o igual que"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:18
#: code:addons/web/static/src/xml/base.xml:723
#, python-format
msgid "not"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:232
#, python-format
msgid "not a valid integer"
msgstr "entero no válido"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:247
#, python-format
msgid "not a valid number"
msgstr "no es un número válido"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:24
#, python-format
msgid "not contains"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:26
#, python-format
msgid "not in"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:639
#, python-format
msgid "not set (false)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:642
#, python-format
msgid "of the following rules:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:657
#, python-format
msgid "of:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:714
#: code:addons/web/static/src/xml/base.xml:1082
#, python-format
msgid "or"
msgstr "o"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:29
#, python-format
msgid "parent of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:598
#, python-format
msgid "record(s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:41
#, python-format
msgid "remaining)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:723
#, python-format
msgid "set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:639
#, python-format
msgid "set (true)"
msgstr ""
