.o_web_client .o_export {
    .o-export-panel {
        height: 100%;
    }
    .o_left_panel {
        flex-flow: column nowrap;
        height: 100%;
        .o_left_field_panel {
            flex: 1;
            overflow: auto;
        }
        .o_export_tree_item {
            cursor: pointer;
            position: relative;
            padding-left: 20px;
            &.o_selected > .o_tree_column {
                background-color: $o-brand-odoo;
                color: white;
            }
            .o_expand_parent {
                @include o-position-absolute($top: 4px, $left: 5px);
                font-size: 10px;
            }
            .o_required {
                border-bottom: 2px solid $o-main-text-color;
            }
        }
    }

    .o_right_panel {
        flex-flow: column nowrap;
        height: 100%;
        .o_save_list {
            display: none;
        }
        .o_right_field_panel {
            flex: 1;
            overflow: auto;
            .o_short_field {
                @include o-grab-cursor;
            }
            .o-field-placeholder {
                border: 1px dashed $o-brand-primary;
            }
            .o_remove_field {
                cursor: pointer;
            }
        }
    }

    @include media-breakpoint-down(sm) {
        .ui-sortable-handle {
            display: none;
        }

        .o_right_field_panel .o_export_field {
            padding-left: map-get($spacers, 1);
        }

        .o_left_panel, .o_right_panel {
            height: 50%;
        }
    }
}
