# #-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_blog
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# #-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_blog
#
# Translators:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-07-17 15:04+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Bulgarian (http://www.transifex.com/odoo/odoo-9/language/"
"bg/)\n"
"Language: bg\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:117
#, python-format
msgid " Click on this button to send your blog post online."
msgstr "Натиснете този бутон за да изпратите вашите статии он-лайн."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Finally, the leading edge is being brought to the masses.\n"
"                    It will now be the turn of the big players to catch up "
"to\n"
"                    the superior technologies of the SME.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Odoo now competes on many fronts, with no real\n"
"                competition out there to knock them off the top spot.\n"
"                With the launch of their integrated CMS and Ecommerce\n"
"                systems,it only elevates their position as one of the "
"leading\n"
"                lights in the open source revolution. It will be at least 5\n"
"                years before another ERP or CMS provider will be able to\n"
"                compete at this level due to the technology currently\n"
"                employed by most industry providers.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Odoo's latest launch will allow a business to go from\n"
"                zero to trading online quicker than ever before,” Stuart\n"
"                Mackintosh, MD of Open Source specialist and Odoo\n"
"                integration partner, OpusVL, explains. “The investment\n"
"                required to have a fully automated business system is\n"
"                dramatically reduced, enabling the small and medium\n"
"                enterprise to compete at a level of functionality and\n"
"                performance previously reserved for the big IT investors.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"This is another clever and highly disruptive move by\n"
"                Odoo,which will force other technology providers to\n"
"                take another look at the value they are providing to ensure\n"
"                that their 'solutions' can still compete.\""
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "&amp;times;"
msgstr "&amp;време;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A mix of push &amp; pull: Today, people\n"
"                    are victims of what others decide to push to them.\n"
"                    Odoo differentiates:"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_about_us
msgid "About us"
msgstr "За нас"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:24
#, python-format
msgid "Add Content"
msgstr "Добави съдържание"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Adding to industry leading technology"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_history
msgid "Archives"
msgstr "Архиви"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"As it comes, there is a default website based on Bootstrap\n"
"                3, the latest industry standard for rapid development of\n"
"                multi-device websites backed by Twitter, so can be directly\n"
"                integrated with many web tools and works across all devices\n"
"                by default."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"At Odoo, we build tools to bring productivity to\n"
"                enterprises. As emails and information flows are one of\n"
"                the biggest wastes of time in companies, we have to fix\n"
"                this."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "Atom Feed"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_author_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post_create_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Автор"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_author_avatar
msgid "Avatar"
msgstr "Аватар"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_blog_id
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#: model:website.menu,name:website_blog.menu_news
msgid "Blog"
msgstr "Блог"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_name
msgid "Blog Name"
msgstr "Име на Блог"

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:283
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog Post"
msgstr "Блог Статия"

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:287
#, python-format
msgid "Blog Post <b>%s</b> seems to have a link to this page !"
msgstr "Блог статията <b>%s</b> изглежда води към тази страница !"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:37
#, python-format
msgid "Blog Post Created"
msgstr "Блог Статията е създадена"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "Заглавие на Блог Статия"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_post
#: model:ir.ui.menu,name:website_blog.menu_page
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
msgid "Blog Posts"
msgstr "Блог Статии"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "Подзаглавие на Блога"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "Таг на Блога"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
#: model:ir.ui.menu,name:website_blog.menu_blog_tag
msgid "Blog Tags"
msgstr "Тагове на Блога"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
msgid "Blogs"
msgstr "Блогове"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blue"
msgstr "Синьо"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "Building your company's website and selling your products online easy."
msgstr ""
"Създаването на сайт за компанията ви и лесното продаване на продукти он-лайн."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:58
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
#, python-format
msgid "Change Cover"
msgstr "Смени Обложка"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:54
#, python-format
msgid "Change and customize your blog post cover."
msgstr "Смени и настрой обложката на статията."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:108
#, python-format
msgid "Check Mobile Preview"
msgstr "Проверка на Изглед на Мобилен телефон"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:68
#, python-format
msgid "Choose an image"
msgstr "Избери изображение"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:69
#, python-format
msgid "Choose an image from the library."
msgstr "изображение от библиотеката."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Click on \"Content\" on the top menu to write your first blog post."
msgstr ""
"Натисни на меню \"Съдържание\" в най-горното меню за да напишеш първата си "
"статия."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:94
#, python-format
msgid "Click on '<em>Save</em>' button to record changes on the page."
msgstr "Натисни '<em>Запис</em>' за да запишеш промените по страницата."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:77
#, python-format
msgid "Click on '<em>Save</em>' to set the picture as cover."
msgstr "Натисни '<em>Запис</em>' за да настроиш снимката за обложка."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:102
#, python-format
msgid ""
"Click on the mobile icon to preview how your blog post will be displayed on "
"a mobile device."
msgstr ""
"Натисни мобилната иконка за да прегледаш как статията би изглеждала на "
"мобилно устройство."

#. module: website_blog
#: model_terms:ir.actions.act_window,help:website_blog.action_blog_post
msgid "Click to create a new blog post."
msgstr "Натисни за да създадеш нова статия."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:124
#, python-format
msgid "Close Tutorial"
msgstr "Затвори Ръководството"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Color"
msgstr "Цвят"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_about_us
msgid "Contact us"
msgstr "За контакти"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:84
#: model:ir.model.fields,field_description:website_blog.field_blog_post_content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Content"
msgstr "Съдържание"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:39
#, python-format
msgid "Continue"
msgstr "Продължи"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:60
#, python-format
msgid "Cover"
msgstr "Обложка"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_cover_properties
msgid "Cover Properties"
msgstr "Свойства на Обложката"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:14
#, python-format
msgid "Create a blog post"
msgstr "Създай Блог Статия"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_create_uid
msgid "Created by"
msgstr "Създадено от"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post_create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_create_date
msgid "Created on"
msgstr "Създадено на"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:53
#, python-format
msgid "Customize Cover"
msgstr "Настрой Обложка"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_mail_compose_message_path
#: model:ir.model.fields,field_description:website_blog.field_mail_message_path
#: model:ir.model.fields,field_description:website_blog.field_survey_mail_compose_message_path
msgid "Discussion Path"
msgstr "Път до дискусията"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post_display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Duplicate"
msgstr "Дублиране"

#. module: website_blog
#: model:ir.model,name:website_blog.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: website_blog
#: model:ir.model,name:website_blog.model_survey_mail_compose_message
msgid "Email composition wizard for Survey"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Emails are broken."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Emails make me waste my time. But I need them.\n"
"                Given the importance that emails have in our lives,\n"
"                it's incredible it's still one of the only software\n"
"                areas that did not evolve in the past 20 years!"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Focus on the Content: Everything is\n"
"                    stripped to emphasize on the real message. No more\n"
"                    welcome introductions, greetings, signatures and legal\n"
"                    notes.We standardize the layout of each message.\n"
"                    (signatures are on the profile of a contact, not in\n"
"                    every message)"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Folders and mailing lists are great tools but too\n"
"                    complex in traditional email clients. In Odoo, a\n"
"                    group of contacts that share a discussion can be\n"
"                    created with one click. Every group should have it's\n"
"                    own email address."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Follow us"
msgstr "Следвайте ни"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Full Screen"
msgstr "Цял екран"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Get Things Done: your inbox is a\n"
"                    todo list. You should be able to process (not only\n"
"                    read) the inbox and easily mark messages for future\n"
"                    actions. Every inbox should be empty after having\n"
"                    been processed; no more overload of information."
msgstr ""

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Get in touch with us"
msgstr "Свържете се с нас"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Green"
msgstr "Зелено"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Групиране по"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#, fuzzy
msgid "Here are the ideas behind the Odoo communication tools:"
msgstr "Идей зад Odoo комуникационните инструменти."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "High"
msgstr "Висок"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"However, unlike other web content management systems, it\n"
"                fully integrates into the back-end database. This means\n"
"                that when you edit a product description, image or price,\n"
"                it updates the product database in real time, providing a\n"
"                true self-service window into the business."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post_id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "Ideas behind the Odoo communication tools."
msgstr "Идей зад Odoo комуникационните инструменти."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "In"
msgstr "В"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
msgid "Integrating your CMS and E-Commerce"
msgstr "Интегриране на CMS и E-Commerce"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Keep control of what you want to receive or don't want\n"
"                    to receive. People should never receive spam. You\n"
"                    should follow/unfollow any kind of information in one\n"
"                    click."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "Последно допринесе"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog___last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post___last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post_write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag___last_update
msgid "Last Modified on"
msgstr "Последно променено на"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_write_uid
msgid "Last Updated by"
msgstr "Последно обновено от"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_write_date
msgid "Last Updated on"
msgstr "Последно обновено на"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "Latest Posts"
msgstr "Последни Статии"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:18
#, python-format
msgid "Let's go through the first steps to write beautiful blog posts."
msgstr "Да минем през първите стъпки за писане на хубави статии."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Like many modern website editors, with Odoo you can edit\n"
"                content in-line, enabling you to see exactly what you are\n"
"                changing and ensure your changes suit the context."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Low"
msgstr "Нисък"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Medium"
msgstr "Средно"

#. module: website_blog
#: model:ir.model,name:website_blog.model_mail_message
msgid "Message"
msgstr "Съобщение"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Messages \"for action\": they\n"
"                            require your immediate attention and you need\n"
"                            to process them all. This accounts for 10%\n"
"                            of your daily emails. Use the \"To: me\" menu\n"
"                            for these."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Messages \"for information\":\n"
"                            you can pull them when you need some specific\n"
"                            information; they are not required to be read\n"
"                            every day.You receive only what you decided\n"
"                            to follow.This accounts for 90% of your daily\n"
"                            emails.Use the \"Inbox\" menu for these."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:101
#, python-format
msgid "Mobile Preview"
msgstr "Мобилен Изглед"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_name
msgid "Name"
msgstr "Име"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Narrow"
msgstr "Тясно"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:17
#: code:addons/website_blog/static/src/js/website.tour.blog.js:31
#: code:addons/website_blog/static/src/js/website_blog.editor.js:21
#: model_terms:ir.ui.view,arch_db:website_blog.content_new_blogpost
#, python-format
msgid "New Blog Post"
msgstr "Нова Блог Статия"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "New Features Launched"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.header_footer_custom
msgid "News"
msgstr "Новини"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "No Cover"
msgstr "Без Обложка"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No blog post yet."
msgstr "Няма блог статии"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "No keywords defined!"
msgstr "Няма ключови думи!"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_visits
msgid "No of Views"
msgstr "Брой разглеждания"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "None"
msgstr "Няма"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:9
#, python-format
msgid "Not Published"
msgstr "Не е публикувана"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Odoo claims to be 'the Open Source software that makes\n"
"                building your company's website and selling your products\n"
"                online easy'. So how true is this statement?"
msgstr ""

#. module: website_blog
#: model:blog.post,website_meta_keywords:website_blog.blog_post_1
msgid "Odoo, email"
msgstr "Odoo, email"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Opacity"
msgstr "Прозрачност"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Our Blog"
msgstr "Нашия Блог"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_blogs
msgid "Our Blogs"
msgstr "Нашите Блогове"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Participate on our social stream."
msgstr "Участвайте в нашия социален поток."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:28
#, python-format
msgid "Please"
msgstr "Моля"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:31
#, python-format
msgid "Post"
msgstr "Публикация"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:44
#, python-format
msgid "Post Headline"
msgstr "Основно заглавие на Статията"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_post_ids
msgid "Posts"
msgstr "Статии"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Productivity is key: our smart user\n"
"                    interface does not require you to click on every mail\n"
"                    to read a thread. Reading a full thread, replying,\n"
"                    attaching documents is super fast."
msgstr ""

#. module: website_blog
#: code:addons/website_blog/controllers/main.py:268
#, python-format
msgid "Public user cannot post comments on blog post."
msgstr "Не-регистрирани потребители не могат да коментират статий."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:10
#, python-format
msgid "Published"
msgstr "Публикувана"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "Публикувани Статии"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:116
#, python-format
msgid "Publishing status"
msgstr "Статус на публикация"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Purple"
msgstr "Лилаво"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_ranking
msgid "Ranking"
msgstr "Ранк"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Read Next <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Прочети Следващ <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Reading my inbox is the most unproductive task I do\n"
"                on a daily basis. I have to spend one full hour a\n"
"                day to process my emails. All the junk flows in the\n"
"                same inbox; spams, information that doesn't matter,\n"
"                quoted answers of quoted answers, etc. At the end\n"
"                of the hour, only 10 emails actually requested an\n"
"                answer from me. With a good tool, I could have done\n"
"                my job in 10 minutes!"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Red"
msgstr "Червено"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:76
#, python-format
msgid "Save"
msgstr "Запазване"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:93
#, python-format
msgid "Save your modifications once you are done"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:109
#, python-format
msgid "Scroll to check rendering and then close the mobile preview."
msgstr "Скролирай за да видиш визулаизацията и после затвори мобилния изглед."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:61
#, python-format
msgid "Select this menu item to change blog cover."
msgstr "Избери този опция за да промениш обложка."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:32
#, python-format
msgid "Select this menu item to create a new blog post."
msgstr "Избери този опция за да създадеш обложка."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:29
#, python-format
msgid "Sign in"
msgstr "Вход"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Size"
msgstr "Размер"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:19
#, python-format
msgid "Skip"
msgstr "Пропусни"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:19
#, python-format
msgid "Start Tutorial"
msgstr "Start Tutorial"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/models/website_blog.py:91
#: code:addons/website_blog/static/src/js/website.tour.blog.js:90
#, python-format
msgid "Start writing here..."
msgstr "Започни да пишеш тук..."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:85
#, fuzzy, python-format
msgid "Start writing your story here."
msgstr "Започни да пишеш тук..."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_subtitle
msgid "Sub Title"
msgstr "Под-заглавие"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Subtitle"
msgstr "Под-заглавие"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "Етикети"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "Списък с Етикети"

#. module: website_blog
#: sql_constraint:blog.tag:0
msgid "Tag name already exists !"
msgstr "Етикет с това име вече съществува!"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_tag_ids
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_tags
msgid "Tags"
msgstr "Етикети"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Technical"
msgstr "Технически"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "The Communication Mechanism of Odoo"
msgstr ""

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
#: model:blog.post,website_meta_description:website_blog.blog_post_1
msgid "The Future of Emails"
msgstr "Бъдещето на емейлите"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:38
#, python-format
msgid "This is your new blog post. Let's edit it."
msgstr "Това е новата ти статия. Нека да я редактираме."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid ""
"This page is great to improve your <strong>Search Engine Optimization</"
"strong>;\n"
"                   You can review titles, keywords and descriptions of all "
"blogs at once."
msgstr ""
"Тази страница е чудесна за подобряване на вашата <strong>Search Engine "
"Optimization</strong>;\n"
"                   Можете да прегледате заглавия, ключови думи и описания за "
"всички статии едновременно."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"This provides a single source of data for your company and\n"
"                removes the need to create offline synchronisation between\n"
"                website and product database."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:123
#, fuzzy, python-format
msgid ""
"This tutorial is over. To discover more features and improve the content of "
"this page, go to the upper left customize menu. You can also add some cool "
"content with your text in the edit mode with the upper right button."
msgstr ""
"Този инструктаж приключи. Можете да откриете още функционалности и да "
"подобрите съдържанието на тази страница, като използвате къстамизационното "
"меню. Можете също да добавите нови блокове, като изберете \"Вмъкни Блок\" в "
"режим на редактиране."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_name
msgid "Title"
msgstr "Заглавие"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"To add to an already comprehensive set of Odoo\n"
"                    features, a website content management system (CMS\n"
"                    or WMS) has been developed and a beta release is\n"
"                    available from today, 31st January 2014."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"To disrupt emails, you need more than just another user\n"
"                interface. We need to rethink the whole communication flow."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Untitled Post"
msgstr "Неозаглавена Статия"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:25
#, fuzzy, python-format
msgid ""
"Use this button to create a new blog post like any other document (page, "
"menu, products, event, ...)."
msgstr ""
"Използвай меню <em>'Съдържание'</em> за да съдадеш нови статии или други "
"документи (страници, менюта, продукти, събития, ...)."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "Използвано във:"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_mail_compose_message_path
#: model:ir.model.fields,help:website_blog.field_mail_message_path
#: model:ir.model.fields,help:website_blog.field_survey_mail_compose_message_path
msgid ""
"Used to display messages in a paragraph-based chatter using a unique path;"
msgstr ""
"Използва се за да се показват съобщения в \"параграфно бърборене\" с "
"уникален път."

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:262
#, python-format
msgid "View Blog Post"
msgstr "Виж Статии"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
msgid "Website"
msgstr "Уеб-страница"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "Блогове"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_website_message_ids
msgid "Website Messages"
msgstr "Съобщения от уебсайта"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post_website_message_ids
msgid "Website communication history"
msgstr "История на комуникацията в уебсайта"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:26
#, python-format
msgid "Write a comment..."
msgstr "Напиши коментар..."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_about_us
msgid ""
"Write a small text here for when <b>new visitors</b> find your website\n"
"            through your <b>blog entries</b>, referenced in Google."
msgstr ""
"Напишете кратък текст за <b>нови потребители</b>, които намират сайта ви.\n"
"            чрез вашите <b>статии</b>, реферирани в Google."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:46
#, python-format
msgid "Write a title, the subtitle is optional."
msgstr "Напишете заглавие, под-заглавието не е задължително."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Yellow"
msgstr "Жълто"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid ""
"You should <strong>add a banner on the top</strong> as it is a frequent "
"landing page for new visitors.\n"
"                   <span class=\"text-muted\">This box will not be visible "
"to your visitors.</span>"
msgstr ""
"Трябва да <strong>добавите банер в горната част на страницата</strong>, "
"защото новите посетители идват често тук.\n"
"                   <span class=\"text-muted\">Тази кутийка не е видима за "
"постетители.</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
#, fuzzy
msgid "blog. Click here to access the blog :"
msgstr "Натиснете тук за да достъпите портала."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:14
#, python-format
msgid "by"
msgstr "от"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "comment"
msgstr "коментар"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "comments"
msgstr "коментари"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
#, fuzzy
msgid "has been published on the"
msgstr "Нова статия %s беше публикувана в тема %s."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "not published"
msgstr "не е публикувана"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "pull-right"
msgstr "дръпни в дясно"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:29
#, python-format
msgid "to comment."
msgstr "за коментиране."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "view"
msgstr "изглед"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "views"
msgstr "изгледи"

#~ msgid ""
#~ "<div class=\"container\">\n"
#~ "        <section class=\"mt16 mb16 readable\">\n"
#~ "            <iframe width=\"100%\" height=\"250\" src=\"http://www."
#~ "youtube.com/embed/EkbBFmIWoTE\" frameborder=\"0\" allowfullscreen></"
#~ "iframe>\n"
#~ "            <p data-chatter-id=\"counter_52500/div/section\">\n"
#~ "                Emails are broken.\n"
#~ "            </p><p data-chatter-id=\"counter_15339/div/section\">\n"
#~ "                Emails make me waste my time. But I need them.\n"
#~ "                Given the importance that emails have in our lives,\n"
#~ "                it's incredible it's still one of the only software\n"
#~ "                areas that did not evolve in the past 20 years!\n"
#~ "            </p><p data-chatter-id=\"counter_98391/div/section\">\n"
#~ "                Reading my inbox is the most unproductive task I do\n"
#~ "                on a daily basis. I have to spend one full hour a\n"
#~ "                day to process my emails. All the junk flows in the\n"
#~ "                same inbox; spams, information that doesn't matter,\n"
#~ "                quoted answers of quoted answers, etc. At the end\n"
#~ "                of the hour, only 10 emails actually requested an\n"
#~ "                answer from me. With a good tool, I could have done\n"
#~ "                my job in 10 minutes!\n"
#~ "            </p>\n"
#~ "        </section>\n"
#~ "        <section class=\"mt16 mb16 readable\">\n"
#~ "            <p data-chatter-id=\"counter_99844/div/section\">\n"
#~ "                At Odoo, we build tools to bring productivity to\n"
#~ "                enterprises. As emails and information flows are one of\n"
#~ "                the biggest wastes of time in companies, we have to fix\n"
#~ "                this.\n"
#~ "            </p><p data-chatter-id=\"counter_72514/div/section\">\n"
#~ "                To disrupt emails, you need more than just another user\n"
#~ "                interface. We need to rethink the whole communication "
#~ "flow.\n"
#~ "            </p>\n"
#~ "            <h3>The Communication Mechanism of Odoo</h3>\n"
#~ "            <p data-chatter-id=\"counter_44333/div/section\">\n"
#~ "                Here are the ideas behind the Odoo communication tools:\n"
#~ "            </p>\n"
#~ "            <ul>\n"
#~ "                <li>\n"
#~ "                    Get Things Done: your inbox is a\n"
#~ "                    todo list. You should be able to process (not only\n"
#~ "                    read) the inbox and easily mark messages for future\n"
#~ "                    actions. Every inbox should be empty after having\n"
#~ "                    been processed; no more overload of information.\n"
#~ "                    <img class=\"img-responsive\" src=\"/website_blog/"
#~ "static/src/img/mail-sc-00.png\">\n"
#~ "                </li><li>\n"
#~ "                    Keep control of what you want to receive or don't "
#~ "want\n"
#~ "                    to receive. People should never receive spam. You\n"
#~ "                    should follow/unfollow any kind of information in "
#~ "one\n"
#~ "                    click.\n"
#~ "                </li><li>\n"
#~ "                    Productivity is key: our smart user\n"
#~ "                    interface does not require you to click on every "
#~ "mail\n"
#~ "                    to read a thread. Reading a full thread, replying,\n"
#~ "                    attaching documents is super fast.\n"
#~ "                    <img class=\"img-responsive\" src=\"/website_blog/"
#~ "static/src/img/mail-sc-03.png\">\n"
#~ "                </li><li>\n"
#~ "                    A mix of push &amp; pull: Today, people\n"
#~ "                    are victims of what others decide to push to them.\n"
#~ "                    Odoo differentiates:\n"
#~ "                    <ul>\n"
#~ "                        <li>\n"
#~ "                            Messages \"for information\":\n"
#~ "                            you can pull them when you need some "
#~ "specific\n"
#~ "                            information; they are not required to be "
#~ "read\n"
#~ "                            every day.You receive only what you decided\n"
#~ "                            to follow.This accounts for 90% of your "
#~ "daily\n"
#~ "                            emails.Use the \"Inbox\" menu for these.\n"
#~ "                        </li><li>\n"
#~ "                            Messages \"for action\": they\n"
#~ "                            require your immediate attention and you "
#~ "need\n"
#~ "                            to process them all. This accounts for 10%\n"
#~ "                            of your daily emails. Use the \"To: me\" "
#~ "menu\n"
#~ "                            for these.\n"
#~ "                        </li>\n"
#~ "                    </ul>\n"
#~ "                </li><li>\n"
#~ "                    Focus on the Content: Everything is\n"
#~ "                    stripped to emphasize on the real message. No more\n"
#~ "                    welcome introductions, greetings, signatures and "
#~ "legal\n"
#~ "                    notes.We standardize the layout of each message.\n"
#~ "                    (signatures are on the profile of a contact, not in\n"
#~ "                    every message)\n"
#~ "                </li><li>\n"
#~ "                    Folders and mailing lists are great tools but too\n"
#~ "                    complex in traditional email clients. In Odoo, a\n"
#~ "                    group of contacts that share a discussion can be\n"
#~ "                    created with one click. Every group should have it's\n"
#~ "                    own email address.\n"
#~ "                </li>\n"
#~ "            </ul>\n"
#~ "        </section>\n"
#~ "    </div>\n"
#~ "\n"
#~ msgstr ""
#~ "<div class=\"container\">\n"
#~ "        <section class=\"mt16 mb16 readable\">\n"
#~ "            <iframe width=\"100%\" height=\"250\" src=\"http://www."
#~ "youtube.com/embed/EkbBFmIWoTE\" frameborder=\"0\" allowfullscreen></"
#~ "iframe>\n"
#~ "            <p data-chatter-id=\"counter_52500/div/section\">\n"
#~ "                Emails are broken.\n"
#~ "            </p><p data-chatter-id=\"counter_15339/div/section\">\n"
#~ "                Emails make me waste my time. But I need them.\n"
#~ "                Given the importance that emails have in our lives,\n"
#~ "                it's incredible it's still one of the only software\n"
#~ "                areas that did not evolve in the past 20 years!\n"
#~ "            </p><p data-chatter-id=\"counter_98391/div/section\">\n"
#~ "                Reading my inbox is the most unproductive task I do\n"
#~ "                on a daily basis. I have to spend one full hour a\n"
#~ "                day to process my emails. All the junk flows in the\n"
#~ "                same inbox; spams, information that doesn't matter,\n"
#~ "                quoted answers of quoted answers, etc. At the end\n"
#~ "                of the hour, only 10 emails actually requested an\n"
#~ "                answer from me. With a good tool, I could have done\n"
#~ "                my job in 10 minutes!\n"
#~ "            </p>\n"
#~ "        </section>\n"
#~ "        <section class=\"mt16 mb16 readable\">\n"
#~ "            <p data-chatter-id=\"counter_99844/div/section\">\n"
#~ "                At Odoo, we build tools to bring productivity to\n"
#~ "                enterprises. As emails and information flows are one of\n"
#~ "                the biggest wastes of time in companies, we have to fix\n"
#~ "                this.\n"
#~ "            </p><p data-chatter-id=\"counter_72514/div/section\">\n"
#~ "                To disrupt emails, you need more than just another user\n"
#~ "                interface. We need to rethink the whole communication "
#~ "flow.\n"
#~ "            </p>\n"
#~ "            <h3>The Communication Mechanism of Odoo</h3>\n"
#~ "            <p data-chatter-id=\"counter_44333/div/section\">\n"
#~ "                Here are the ideas behind the Odoo communication tools:\n"
#~ "            </p>\n"
#~ "            <ul>\n"
#~ "                <li>\n"
#~ "                    Get Things Done: your inbox is a\n"
#~ "                    todo list. You should be able to process (not only\n"
#~ "                    read) the inbox and easily mark messages for future\n"
#~ "                    actions. Every inbox should be empty after having\n"
#~ "                    been processed; no more overload of information.\n"
#~ "                    <img class=\"img-responsive\" src=\"/website_blog/"
#~ "static/src/img/mail-sc-00.png\">\n"
#~ "                </li><li>\n"
#~ "                    Keep control of what you want to receive or don't "
#~ "want\n"
#~ "                    to receive. People should never receive spam. You\n"
#~ "                    should follow/unfollow any kind of information in "
#~ "one\n"
#~ "                    click.\n"
#~ "                </li><li>\n"
#~ "                    Productivity is key: our smart user\n"
#~ "                    interface does not require you to click on every "
#~ "mail\n"
#~ "                    to read a thread. Reading a full thread, replying,\n"
#~ "                    attaching documents is super fast.\n"
#~ "                    <img class=\"img-responsive\" src=\"/website_blog/"
#~ "static/src/img/mail-sc-03.png\">\n"
#~ "                </li><li>\n"
#~ "                    A mix of push &amp; pull: Today, people\n"
#~ "                    are victims of what others decide to push to them.\n"
#~ "                    Odoo differentiates:\n"
#~ "                    <ul>\n"
#~ "                        <li>\n"
#~ "                            Messages \"for information\":\n"
#~ "                            you can pull them when you need some "
#~ "specific\n"
#~ "                            information; they are not required to be "
#~ "read\n"
#~ "                            every day.You receive only what you decided\n"
#~ "                            to follow.This accounts for 90% of your "
#~ "daily\n"
#~ "                            emails.Use the \"Inbox\" menu for these.\n"
#~ "                        </li><li>\n"
#~ "                            Messages \"for action\": they\n"
#~ "                            require your immediate attention and you "
#~ "need\n"
#~ "                            to process them all. This accounts for 10%\n"
#~ "                            of your daily emails. Use the \"To: me\" "
#~ "menu\n"
#~ "                            for these.\n"
#~ "                        </li>\n"
#~ "                    </ul>\n"
#~ "                </li><li>\n"
#~ "                    Focus on the Content: Everything is\n"
#~ "                    stripped to emphasize on the real message. No more\n"
#~ "                    welcome introductions, greetings, signatures and "
#~ "legal\n"
#~ "                    notes.We standardize the layout of each message.\n"
#~ "                    (signatures are on the profile of a contact, not in\n"
#~ "                    every message)\n"
#~ "                </li><li>\n"
#~ "                    Folders and mailing lists are great tools but too\n"
#~ "                    complex in traditional email clients. In Odoo, a\n"
#~ "                    group of contacts that share a discussion can be\n"
#~ "                    created with one click. Every group should have it's\n"
#~ "                    own email address.\n"
#~ "                </li>\n"
#~ "            </ul>\n"
#~ "        </section>\n"
#~ "    </div>\n"
#~ "\n"

#~ msgid ""
#~ "<section><div class=\"container\">\n"
#~ "            <div class=\"row\">\n"
#~ "                <div class=\"col-md-6 mt16\"><img class=\"img img-"
#~ "responsive mb16\" src=\"/website_blog/static/src/img/CMS_WMS_screens.jpg"
#~ "\" style=\"\"></div>\n"
#~ "\n"
#~ "                <div class=\"col-md-6 mt16\">\n"
#~ "                    <h3>New Features Launched</h3>\n"
#~ "\n"
#~ "                    <p data-chatter-id=\"counter_0/section/div/div/div"
#~ "\">To add to an already comprehensive set of Odoo\n"
#~ "                    features, a website content management system (CMS\n"
#~ "                    or WMS) has been developed and a beta release is\n"
#~ "                    available from today, 31st January 2014.</p>\n"
#~ "                </div>\n"
#~ "            </div>\n"
#~ "        \n"
#~ "        <section class=\"readable\">\n"
#~ "            <p data-chatter-id=\"counter_75695/section/div/section\">\n"
#~ "                Odoo claims to be 'the Open Source software that makes\n"
#~ "                building your company's website and selling your "
#~ "products\n"
#~ "                online easy'. So how true is this statement?\n"
#~ "            </p><p data-chatter-id=\"counter_63354/section/div/section"
#~ "\">\n"
#~ "                \"Odoo's latest launch will allow a business to go from\n"
#~ "                zero to trading online quicker than ever before,&#8221; "
#~ "Stuart\n"
#~ "                Mackintosh, MD of Open Source specialist and Odoo\n"
#~ "                integration partner, OpusVL, explains. &#8220;The "
#~ "investment\n"
#~ "                required to have a fully automated business system is\n"
#~ "                dramatically reduced, enabling the small and medium\n"
#~ "                enterprise to compete at a level of functionality and\n"
#~ "                performance previously reserved for the big IT investors."
#~ "\"\n"
#~ "            </p>\n"
#~ "            <blockquote>\n"
#~ "                <p data-chatter-id=\"counter_92702/section/div/section/"
#~ "blockquote\">\n"
#~ "                    \"Finally, the leading edge is being brought to the "
#~ "masses.\n"
#~ "                    It will now be the turn of the big players to catch "
#~ "up to\n"
#~ "                    the superior technologies of the SME.\"\n"
#~ "                </p>\n"
#~ "            </blockquote>\n"
#~ "            <p data-chatter-id=\"counter_70446/section/div/section\">\n"
#~ "                \"This is another clever and highly disruptive move by\n"
#~ "                Odoo,which will force other technology providers to\n"
#~ "                take another look at the value they are providing to "
#~ "ensure\n"
#~ "                that their 'solutions' can still compete.\"\n"
#~ "            </p><p data-chatter-id=\"counter_44493/section/div/section"
#~ "\">\n"
#~ "                \"Odoo now competes on many fronts, with no real\n"
#~ "                competition out there to knock them off the top spot.\n"
#~ "                With the launch of their integrated CMS and Ecommerce\n"
#~ "                systems,it only elevates their position as one of the "
#~ "leading\n"
#~ "                lights in the open source revolution. It will be at least "
#~ "5\n"
#~ "                years before another ERP or CMS provider will be able to\n"
#~ "                compete at this level due to the technology currently\n"
#~ "                employed by most industry providers.\"\n"
#~ "            </p>\n"
#~ "            <h4>Adding to industry leading technology</h4>\n"
#~ "            <p data-chatter-id=\"counter_41774/section/div/section\">\n"
#~ "                Like many modern website editors, with Odoo you can edit\n"
#~ "                content in-line, enabling you to see exactly what you "
#~ "are\n"
#~ "                changing and ensure your changes suit the context.\n"
#~ "            </p><p data-chatter-id=\"counter_58203/section/div/section"
#~ "\">\n"
#~ "                However, unlike other web content management systems, it\n"
#~ "                fully integrates into the back-end database. This means\n"
#~ "                that when you edit a product description, image or "
#~ "price,\n"
#~ "                it updates the product database in real time, providing "
#~ "a\n"
#~ "                true self-service window into the business.\n"
#~ "            </p><p data-chatter-id=\"counter_39750/section/div/section"
#~ "\">\n"
#~ "                This provides a single source of data for your company "
#~ "and\n"
#~ "                removes the need to create offline synchronisation "
#~ "between\n"
#~ "                website and product database.\n"
#~ "            </p><p data-chatter-id=\"counter_74064/section/div/section"
#~ "\">\n"
#~ "                As it comes, there is a default website based on "
#~ "Bootstrap\n"
#~ "                3, the latest industry standard for rapid development of\n"
#~ "                multi-device websites backed by Twitter, so can be "
#~ "directly\n"
#~ "                integrated with many web tools and works across all "
#~ "devices\n"
#~ "                by default.\n"
#~ "            </p>\n"
#~ "        </section>\n"
#~ "\n"
#~ "</div></section>"
#~ msgstr ""
#~ "<section><div class=\"container\">\n"
#~ "            <div class=\"row\">\n"
#~ "                <div class=\"col-md-6 mt16\"><img class=\"img img-"
#~ "responsive mb16\" src=\"/website_blog/static/src/img/CMS_WMS_screens.jpg"
#~ "\" style=\"\"></div>\n"
#~ "\n"
#~ "                <div class=\"col-md-6 mt16\">\n"
#~ "                    <h3>New Features Launched</h3>\n"
#~ "\n"
#~ "                    <p data-chatter-id=\"counter_0/section/div/div/div"
#~ "\">To add to an already comprehensive set of Odoo\n"
#~ "                    features, a website content management system (CMS\n"
#~ "                    or WMS) has been developed and a beta release is\n"
#~ "                    available from today, 31st January 2014.</p>\n"
#~ "                </div>\n"
#~ "            </div>\n"
#~ "        \n"
#~ "        <section class=\"readable\">\n"
#~ "            <p data-chatter-id=\"counter_75695/section/div/section\">\n"
#~ "                Odoo claims to be 'the Open Source software that makes\n"
#~ "                building your company's website and selling your "
#~ "products\n"
#~ "                online easy'. So how true is this statement?\n"
#~ "            </p><p data-chatter-id=\"counter_63354/section/div/section"
#~ "\">\n"
#~ "                \"Odoo's latest launch will allow a business to go from\n"
#~ "                zero to trading online quicker than ever before,&#8221; "
#~ "Stuart\n"
#~ "                Mackintosh, MD of Open Source specialist and Odoo\n"
#~ "                integration partner, OpusVL, explains. &#8220;The "
#~ "investment\n"
#~ "                required to have a fully automated business system is\n"
#~ "                dramatically reduced, enabling the small and medium\n"
#~ "                enterprise to compete at a level of functionality and\n"
#~ "                performance previously reserved for the big IT investors."
#~ "\"\n"
#~ "            </p>\n"
#~ "            <blockquote>\n"
#~ "                <p data-chatter-id=\"counter_92702/section/div/section/"
#~ "blockquote\">\n"
#~ "                    \"Finally, the leading edge is being brought to the "
#~ "masses.\n"
#~ "                    It will now be the turn of the big players to catch "
#~ "up to\n"
#~ "                    the superior technologies of the SME.\"\n"
#~ "                </p>\n"
#~ "            </blockquote>\n"
#~ "            <p data-chatter-id=\"counter_70446/section/div/section\">\n"
#~ "                \"This is another clever and highly disruptive move by\n"
#~ "                Odoo,which will force other technology providers to\n"
#~ "                take another look at the value they are providing to "
#~ "ensure\n"
#~ "                that their 'solutions' can still compete.\"\n"
#~ "            </p><p data-chatter-id=\"counter_44493/section/div/section"
#~ "\">\n"
#~ "                \"Odoo now competes on many fronts, with no real\n"
#~ "                competition out there to knock them off the top spot.\n"
#~ "                With the launch of their integrated CMS and Ecommerce\n"
#~ "                systems,it only elevates their position as one of the "
#~ "leading\n"
#~ "                lights in the open source revolution. It will be at least "
#~ "5\n"
#~ "                years before another ERP or CMS provider will be able to\n"
#~ "                compete at this level due to the technology currently\n"
#~ "                employed by most industry providers.\"\n"
#~ "            </p>\n"
#~ "            <h4>Adding to industry leading technology</h4>\n"
#~ "            <p data-chatter-id=\"counter_41774/section/div/section\">\n"
#~ "                Like many modern website editors, with Odoo you can edit\n"
#~ "                content in-line, enabling you to see exactly what you "
#~ "are\n"
#~ "                changing and ensure your changes suit the context.\n"
#~ "            </p><p data-chatter-id=\"counter_58203/section/div/section"
#~ "\">\n"
#~ "                However, unlike other web content management systems, it\n"
#~ "                fully integrates into the back-end database. This means\n"
#~ "                that when you edit a product description, image or "
#~ "price,\n"
#~ "                it updates the product database in real time, providing "
#~ "a\n"
#~ "                true self-service window into the business.\n"
#~ "            </p><p data-chatter-id=\"counter_39750/section/div/section"
#~ "\">\n"
#~ "                This provides a single source of data for your company "
#~ "and\n"
#~ "                removes the need to create offline synchronisation "
#~ "between\n"
#~ "                website and product database.\n"
#~ "            </p><p data-chatter-id=\"counter_74064/section/div/section"
#~ "\">\n"
#~ "                As it comes, there is a default website based on "
#~ "Bootstrap\n"
#~ "                3, the latest industry standard for rapid development of\n"
#~ "                multi-device websites backed by Twitter, so can be "
#~ "directly\n"
#~ "                integrated with many web tools and works across all "
#~ "devices\n"
#~ "                by default.\n"
#~ "            </p>\n"
#~ "        </section>\n"
#~ "\n"
#~ "</div></section>"

#~ msgid "Action Needed"
#~ msgstr "Нужно е действие"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Дата на последното съобщение, публикувано на записа."

#~ msgid "Followers"
#~ msgstr "Последователи"

#~ msgid "Followers (Channels)"
#~ msgstr "Последователи (канали)"

#~ msgid "Followers (Partners)"
#~ msgstr "Последователи (партньори)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Ако е отбелязано, новите съобщения ще изискват внимание."

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Ако е отбелязано, новите съобщения ще изискват внимание."

#~ msgid "Is Follower"
#~ msgstr "е последовател"

#~ msgid "Last Message Date"
#~ msgstr "Дата на последното съобщение"

#~ msgid "Messages"
#~ msgstr "Съобщения"

#~ msgid "Messages and communication history"
#~ msgstr "История на събщенията и комуникациите"

#~ msgid "Number of Actions"
#~ msgstr "Брой действия"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Брой съобщения, които изискват внимание"

#~ msgid "Number of unread messages"
#~ msgstr "Брой непрочетени съобщения"

#~ msgid "Save Your Blog"
#~ msgstr "Запиши Статията"

#~ msgid ""
#~ "Start writing your story here. Click on save in the upper left corner "
#~ "when you are done."
#~ msgstr ""
#~ "Започни да пишеш историята тук. Натисни \"Запис\" в горния ляв ъгъл, като "
#~ "е готово."

#~ msgid "The full URL to access the document through the website."
#~ msgstr "Пълният URL за достъп до документа през уебсайта."

#~ msgid "Unread Messages"
#~ msgstr "Непрочетени съобщения"

#~ msgid "Unread Messages Counter"
#~ msgstr "Брой непрочетени съобщения"

#~ msgid "Visible in Website"
#~ msgstr "Видим в уеб сайта"

#~ msgid "Website URL"
#~ msgstr "URL на Уебсайта"

#~ msgid "Website meta description"
#~ msgstr "Website meta description"

#~ msgid "Website meta keywords"
#~ msgstr "Website meta keywords"

#~ msgid "Website meta title"
#~ msgstr "Website meta title"

#~ msgid "on"
#~ msgstr "на"
