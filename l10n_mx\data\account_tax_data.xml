<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data>

    <record id="ieps_8_sale" model="account.tax.template">
        <field name="sequence" eval="0"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="active" eval="False"/>
        <field name="name">IEPS 8% VENTAS</field>
        <field name="description">IEPS 8%</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_ieps_8"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="include_base_amount">1</field>
        <field name="cash_basis_transition_account_id" ref="cuenta209_02"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta208_02'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta208_02'),
            }),
        ]"/>
    </record>

    <record id="ieps_8_purchase" model="account.tax.template">
        <field name="sequence" eval="1"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">IEPS 8% COMPRAS</field>
        <field name="description">IEPS 8%</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_ieps_8"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="include_base_amount">1</field>
        <field name="cash_basis_transition_account_id" ref="cuenta119_03"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta118_03'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta118_03'),
            }),
        ]"/>
    </record>

    <record id="ieps_25_sale" model="account.tax.template">
        <field name="sequence" eval="2"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="active" eval="False"/>
        <field name="name">IEPS 25% VENTAS</field>
        <field name="description">IEPS 25%</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_ieps_25"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="include_base_amount">1</field>
        <field name="cash_basis_transition_account_id" ref="cuenta209_02"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta208_02'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta208_02'),
            }),
        ]"/>
    </record>

    <record id="ieps_25_purchase" model="account.tax.template">
        <field name="sequence" eval="3"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">IEPS 25% COMPRAS</field>
        <field name="description">IEPS 25%</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_ieps_25"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="include_base_amount">1</field>
        <field name="cash_basis_transition_account_id" ref="cuenta119_03"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta118_03'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta118_03'),
            }),
        ]"/>
    </record>

    <record id="ieps_26_5_sale" model="account.tax.template">
        <field name="sequence" eval="4"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="active" eval="False"/>
        <field name="name">IEPS 26.5% VENTAS</field>
        <field name="description">IEPS 26.5%</field>
        <field name="amount">26.5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_ieps_26_5"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="include_base_amount">1</field>
        <field name="cash_basis_transition_account_id" ref="cuenta209_02"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta208_02'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta208_02'),
            }),
        ]"/>
    </record>

    <record id="ieps_26_5_purchase" model="account.tax.template">
        <field name="sequence" eval="5"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">IEPS 26.5% COMPRAS</field>
        <field name="description">IEPS 26.5%</field>
        <field name="amount">26.5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="include_base_amount">1</field>
        <field name="tax_group_id" ref="tax_group_ieps_26_5"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="cuenta119_03"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta118_03'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta118_03'),
            }),
        ]"/>
    </record>

    <record id="ieps_30_sale" model="account.tax.template">
        <field name="sequence" eval="6"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="active" eval="False"/>
        <field name="name">IEPS 30% VENTAS</field>
        <field name="description">IEPS 30%</field>
        <field name="amount">30</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_ieps_30"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="include_base_amount">1</field>
        <field name="cash_basis_transition_account_id" ref="cuenta209_02"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta208_02'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta208_02'),
            }),
        ]"/>
    </record>

    <record id="ieps_30_purchase" model="account.tax.template">
        <field name="sequence" eval="7"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">IEPS 30% COMPRAS</field>
        <field name="description">IEPS 30%</field>
        <field name="amount">30</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_ieps_30"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="include_base_amount">1</field>
        <field name="cash_basis_transition_account_id" ref="cuenta119_03"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta118_03'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta118_03'),
            }),
        ]"/>
    </record>

    <record id="ieps_53_sale" model="account.tax.template">
        <field name="sequence" eval="8"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="active" eval="False"/>
        <field name="name">IEPS 53% VENTAS</field>
        <field name="description">IEPS 53%</field>
        <field name="amount">53</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_ieps_53"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="include_base_amount">1</field>
        <field name="cash_basis_transition_account_id" ref="cuenta209_02"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta208_02'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta208_02'),
            }),
        ]"/>
    </record>

    <record id="ieps_53_purchase" model="account.tax.template">
        <field name="sequence" eval="9"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">IEPS 53% COMPRAS</field>
        <field name="description">IEPS 53%</field>
        <field name="amount">53</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_ieps_53"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="include_base_amount">1</field>
        <field name="cash_basis_transition_account_id" ref="cuenta119_03"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta118_03'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_ieps')],
                'account_id': ref('l10n_mx.cuenta118_03'),
            }),
        ]"/>
    </record>

    <record id="tax1" model="account.tax.template">
        <field name="sequence" eval="10"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">RET IVA FLETES 4%</field>
        <field name="description">RET IVA -4%</field>
        <field name="amount">-4</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_iva_ret_4"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="cuenta216_10"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_ret')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_10_20'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_ret')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_10_20'),
            }),
        ]"/>
    </record>

    <record id="tax2" model="account.tax.template">
        <field name="sequence" eval="11"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">RET IVA ARRENDAMIENTO 10%</field>
        <field name="description">RET IVA -10%</field>
        <field name="amount">-10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_iva_ret_10"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="cuenta216_10"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_ret')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_10_20'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_ret')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_10_20'),
            }),
        ]"/>
    </record>

    <record id="tax3" model="account.tax.template">
        <field name="sequence" eval="12"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">RET ISR ARRENDAMIENTO 10%</field>
        <field name="description">RET ISR -10%</field>
        <field name="amount">-10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_isr_ret_10"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_03'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_03'),
            }),
        ]"/>
    </record>

    <record id="tax5" model="account.tax.template">
        <field name="sequence" eval="13"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">RET ISR HONORARIOS 10%</field>
        <field name="description">RET ISR -10%</field>
        <field name="amount">-10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_isr_ret_10"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_04'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_04'),
            }),
        ]"/>
    </record>

    <record id="tax7" model="account.tax.template">
        <field name="sequence" eval="14"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">RET IVA ARRENDAMIENTO 10.67%</field>
        <field name="description">RET IVA -10.67%</field>
        <field name="amount">-10.67</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_iva_ret_1067"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="cuenta216_10"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_ret')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_10_20'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_ret')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_10_20'),
            }),
        ]"/>
    </record>

    <record id="tax8" model="account.tax.template">
        <field name="sequence" eval="15"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">RET IVA HONORARIOS 10.67%</field>
        <field name="description">RET IVA -10.67%</field>
        <field name="amount">-10.67</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_iva_ret_1067"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="cuenta216_10"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_ret')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_10_20'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_ret')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta216_10_20'),
            }),
        ]"/>
    </record>

    <record id="tax9" model="account.tax.template">
        <field name="sequence" eval="16"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">IVA 0% VENTAS</field>
        <field name="description">IVA 0%</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="cuenta209_01"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta208_01'),
                'tag_ids': [ref('tag_iva')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta208_01'),
                'tag_ids': [ref('tag_iva')],
            }),
        ]"/>
    </record>

    <record id="tax12" model="account.tax.template">
        <field name="sequence" eval="17"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">IVA 16% VENTAS</field>
        <field name="description">IVA 16%</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_iva_16"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="cuenta209_01"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta208_01'),
                'tag_ids': [ref('tag_iva')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta208_01'),
                'tag_ids': [ref('tag_iva')],
            }),
        ]"/>
    </record>

    <record id="tax13" model="account.tax.template">
        <field name="sequence" eval="18"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">IVA 0% COMPRAS</field>
        <field name="description">IVA 0%</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="cuenta119_01"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_0')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta118_01'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_0')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta118_01'),
            }),
        ]"/>
    </record>

    <record id="tax14" model="account.tax.template">
        <field name="sequence" eval="19"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">IVA 16% COMPRAS</field>
        <field name="description">IVA 16%</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_iva_16"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="cuenta119_01"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_16')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta118_01'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_16')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta118_01'),
            }),
        ]"/>
    </record>

    <record id="tax16" model="account.tax.template">
        <field name="sequence" eval="20"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">IVA 8% COMPRAS</field>
        <field name="description">IVA 8%</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_iva_8"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="cuenta119_01"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_8')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta118_01'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('tag_diot_8')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('cuenta118_01'),
            }),
        ]"/>
    </record>

    <record id="tax17" model="account.tax.template">
        <field name="sequence" eval="21"/>
        <field name="chart_template_id" ref="mx_coa"/>
        <field name="name">IVA 8% VENTAS</field>
        <field name="description">IVA 8%</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_iva_8"/>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="cuenta209_01"/>
        <field name="l10n_mx_tax_type">Tasa</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_iva')],
                'account_id': ref('cuenta208_01'),
                'tag_ids': [ref('tag_iva')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('l10n_mx.tag_iva')],
                'account_id': ref('cuenta208_01'),
                'tag_ids': [ref('tag_iva')],
            }),
        ]"/>
    </record>
  </data>
</odoo>
