<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.ComposerSuggestion" owl="1">
        <a class="o_ComposerSuggestion dropdown-item" t-att-class="{ 'active bg-300': props.isActive }" href="#" t-att-title="record and title()" role="menuitem" t-on-click="_onClick">
            <t t-if="record">
                <t t-if="isCannedResponse">
                    <span class="o_ComposerSuggestion_part1 text-truncate"><t t-esc="record.source"/></span>
                    <span class="o_ComposerSuggestion_part2 text-truncate"><t t-esc="record.substitution"/></span>
                </t>
                <t t-if="isChannel">
                    <span class="o_ComposerSuggestion_part1 text-truncate"><t t-esc="record.name"/></span>
                </t>
                <t t-if="isCommand">
                    <span class="o_ComposerSuggestion_part1 text-truncate"><t t-esc="record.name"/></span>
                    <span class="o_ComposerSuggestion_part2 text-truncate"><t t-esc="record.help"/></span>
                </t>
                <t t-if="isPartner">
                    <PartnerImStatusIcon
                        class="o_ComposerSuggestion_partnerImStatusIcon mr-1"
                        hasBackground="false"
                        partnerLocalId="record.localId"
                    />
                    <span class="o_ComposerSuggestion_part1 text-truncate"><t t-esc="record.nameOrDisplayName"/></span>
                    <t t-if="record.email">
                        <span class="o_ComposerSuggestion_part2 text-truncate">(<t t-esc="record.email"/>)</span>
                    </t>
                </t>
            </t>
        </a>
    </t>

</templates>
