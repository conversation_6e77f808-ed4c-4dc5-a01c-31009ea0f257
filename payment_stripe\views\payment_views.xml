<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_acquirer_form" model="ir.ui.view">
        <field name="name">Stripe Acquirer Form</field>
        <field name="model">payment.acquirer</field>
        <field name="inherit_id" ref="payment.payment_acquirer_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='acquirer']" position="before">
                <group invisible="context.get('stripe_onboarding', False)"
                       name="stripe_onboarding_group"
                       attrs="{'invisible': ['|', '|', ('provider', '!=', 'stripe'), ('stripe_secret_key', '!=', False), ('stripe_publishable_key', '!=', False)]}">
                    <button string="Connect Stripe"
                            type="object"
                            name="action_stripe_connect_account"
                            class="btn-primary"
                            attrs="{'invisible': [('state', '=', 'enabled')]}"/>
                </group>
            </xpath>
            <xpath expr="//group[@name='acquirer']" position="inside">
                <group attrs="{'invisible': [('provider', '!=', 'stripe')]}" name="stripe_credentials">
                    <field name="stripe_publishable_key" attrs="{'required':[('provider', '=', 'stripe'), ('state', '!=', 'disabled')]}" password="True"/>
                    <field name="stripe_secret_key" attrs="{'required':[('provider', '=', 'stripe'), ('state', '!=', 'disabled')]}" password="True"/>
                    <label for="stripe_webhook_secret"/>
                    <div class="o_row" col="2">
                        <field name="stripe_webhook_secret" password="True"/>
                        <button string="Generate your webhook"
                                type="object"
                                name="action_stripe_create_webhook"
                                class="btn-primary"
                                attrs="{'invisible': ['|', ('stripe_webhook_secret', '!=', False), ('stripe_secret_key', '=', False)]}"/>
                    </div>
                </group>
                <div name="stripe_keys_link"
                     invisible="not context.get('stripe_onboarding', False)"
                     attrs="{'invisible': ['|', ('provider', '!=', 'stripe'), '&amp;', ('stripe_secret_key', '!=', False), ('stripe_publishable_key', '!=', False)]}">
                    <a class="btn btn-link" role="button" href="https://dashboard.stripe.com/account/apikeys" target="_blank">
                        Get your Secret and Publishable keys
                    </a>
                </div>
            </xpath>
        </field>
    </record>

    <record id="action_payment_acquirer_onboarding" model="ir.actions.act_window">
        <field name="name">Payment Acquirers</field>
        <field name="res_model">payment.acquirer</field>
        <field name="view_mode">form</field>
        <field name="context">{'stripe_onboarding': True, 'form_view_initial_mode': 'edit'}</field>
    </record>

</odoo>
