<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">
    <!--=================-->
    <!-- Base components -->
    <!--=================-->

    <!-- Editor toolbar -->
    <t t-name="web_editor.toolbar">
        <div id="toolbar" class="oe-toolbar oe-floating">
            <div id="style" class="btn-group dropdown">
                <button type="button" class="btn dropdown-toggle"
                    data-toggle="dropdown" title="Text style" tabindex="-1"
                    data-original-title="Style" aria-expanded="false">
                    <span>Normal</span>
                </button>
                <ul class="dropdown-menu">
                    <li id="paragraph-dropdown-item">
                        <a class="dropdown-item" href="#" id="paragraph" data-call="setTag" data-arg1="p">Normal</a>
                    </li>
                    <li id="pre-dropdown-item">
                        <a class="dropdown-item" href="#" id="pre" data-call="setTag" data-arg1="pre"><pre>Code</pre></a>
                    </li>
                    <li id="heading1-dropdown-item">
                        <a class="dropdown-item" href="#" id="heading1" data-call="setTag" data-arg1="h1"><h1>Header 1</h1></a>
                    </li>
                    <li id="heading2-dropdown-item">
                        <a class="dropdown-item" href="#" id="heading2" data-call="setTag" data-arg1="h2"><h2>Header 2</h2></a>
                    </li>
                    <li id="heading3-dropdown-item">
                        <a class="dropdown-item" href="#" id="heading3" data-call="setTag" data-arg1="h3"><h3>Header 3</h3></a>
                    </li>
                    <li id="heading4-dropdown-item">
                        <a class="dropdown-item" href="#" id="heading4" data-call="setTag" data-arg1="h4"><h4>Header 4</h4></a>
                    </li>
                    <li id="heading5-dropdown-item">
                        <a class="dropdown-item" href="#" id="heading5" data-call="setTag" data-arg1="h5"><h5>Header 5</h5></a>
                    </li>
                    <li id="heading6-dropdown-item">
                        <a class="dropdown-item" href="#" id="heading6" data-call="setTag" data-arg1="h6"><h6>Header 6</h6></a>
                    </li>
                    <li id="blockquote-dropdown-item">
                        <a class="dropdown-item" href="#" id="blockquote" data-call="setTag" data-arg1="blockquote">
                            <blockquote>Quote</blockquote>
                        </a>
                    </li>
                </ul>
            </div>

            <div id="decoration" class="btn-group">
                <div id="bold" data-call="bold" title="Toggle bold" class="btn fa fa-bold fa-fw"></div>
                <div id="italic" data-call="italic" title="Toggle italic" class="btn fa fa-italic fa-fw"></div>
                <div id="underline" data-call="underline" title="Toggle underline" class="btn fa fa-underline fa-fw"></div>
                <div id="strikethrough" data-call="strikeThrough" title="Toggle strikethrough" class="btn fa fa-strikethrough fa-fw"></div>
                <div id="removeFormat" data-call="removeFormat" title="Remove format" class="btn fa fa-eraser fa-fw"></div>
            </div>

            <div id="colorInputButtonGroup" class="btn-group">
                <div class="colorpicker-group note-fore-color-preview" data-name="color">
                    <div id="oe-text-color" class="btn color-button dropdown-toggle editor-ignore"
                        data-toggle="dropdown" title="Font Color" tabindex="-1"
                        data-original-title="Font Color">
                        <i class="fa fa-font color-indicator fore-color"></i>
                    </div>
                    <ul class="dropdown-menu colorpicker-menu">
                        <li><div data-event-name="foreColor" class="colorPalette"></div></li>
                    </ul>
                </div>
                <div class="colorpicker-group note-back-color-preview" data-name="color">
                    <button id="oe-fore-color" type="button" class="btn dropdown-toggle editor-ignore"
                        data-toggle="dropdown" title="Background Color" tabindex="-1"
                        data-original-title="Background Color">
                        <i class="fa fa-paint-brush color-indicator hilite-color"></i>
                    </button>
                    <ul class="dropdown-menu colorpicker-menu">
                        <li><div data-event-name="backColor" class="colorPalette"></div></li>
                    </ul>
                </div>
            </div>

            <div id="font-size" class="btn-group dropdown">
                <button type="button" class="btn dropdown-toggle"
                    data-toggle="dropdown" title="Font size" tabindex="-1"
                    data-original-title="Font Size" aria-expanded="false">
                    <span id="fontSizeCurrentValue"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-call="setFontSize">default</a></li>
                    <li><a class="dropdown-item" href="#" data-call="setFontSize" data-arg1="8px">8</a></li>
                    <li><a class="dropdown-item" href="#" data-call="setFontSize" data-arg1="9px">9</a></li>
                    <li><a class="dropdown-item" href="#" data-call="setFontSize" data-arg1="10px">10</a></li>
                    <li><a class="dropdown-item" href="#" data-call="setFontSize" data-arg1="11px">11</a></li>
                    <li><a class="dropdown-item" href="#" data-call="setFontSize" data-arg1="12px">12</a></li>
                    <li><a class="dropdown-item" href="#" data-call="setFontSize" data-arg1="14px">14</a></li>
                    <li><a class="dropdown-item" href="#" data-call="setFontSize" data-arg1="18px">18</a></li>
                    <li><a class="dropdown-item" href="#" data-call="setFontSize" data-arg1="24px">24</a></li>
                    <li><a class="dropdown-item" href="#" data-call="setFontSize" data-arg1="36px">36</a></li>
                    <li><a class="dropdown-item" href="#" data-call="setFontSize" data-arg1="48px">48</a></li>
                    <li><a class="dropdown-item" href="#" data-call="setFontSize" data-arg1="62px">62</a></li>
                </ul>
            </div>

            <div id="justify" class="btn-group dropdown">
                <button type="button" class="btn dropdown-toggle"
                    data-toggle="dropdown" title="Text align" tabindex="-1"
                    data-original-title="Paragraph" aria-expanded="false">
                    <i id="paragraphDropdownButton" class="fa fa-align-left fa-fw"></i>
                </button>
                <div class="dropdown-menu">
                    <div class="btn-group">
                        <div class="btn" id="justifyLeft" data-call='justifyLeft'><i class="fa fa-align-left fa-fw"></i></div>
                        <div class="btn" id="justifyCenter" data-call='justifyCenter'><i class="fa fa-align-center fa-fw"></i></div>
                        <div class="btn" id="justifyRight" data-call='justifyRight'><i class="fa fa-align-right fa-fw"></i></div>
                        <div class="btn" id="justifyFull" data-call='justifyFull'><i class="fa fa-align-justify fa-fw"></i></div>
                    </div>
                </div>
            </div>

            <div id="list" class="btn-group">
                <div id="unordered" data-call="toggleList" data-arg1="UL" title="Toggle unordered list" class="oe-toggle-unordered fa fa-list-ul fa-fw btn"></div>
                <div id="ordered" data-call="toggleList" data-arg1="OL" title="Toggle ordered list" class="oe-toggle-ordered fa fa-list-ol fa-fw btn"></div>
                <div id="checklist" data-call="toggleList" data-arg1="CL" title="Toggle checklist" class="oe-toggle-checklist btn fa fa-fw">
                    <div class="small">
                        <div class="fa fa-square-o d-block small"/>
                        <div class="fa fa-check-square d-block small"/>
                    </div>
                </div>
            </div>

            <div id="table" class="btn-group">
                <div class="dropdown">
                    <button type="button" class="btn dropdown-toggle"
                        data-toggle="dropdown" title="Insert table" tabindex="-1"
                        data-original-title="Table" aria-expanded="false"
                        id="tableDropdownButton">
                        <i class="fa fa-table fa-fw"></i>
                    </button>
                    <div class="dropdown-menu">
                        <div class="btn-group oe-tablepicker-dropdown"></div>
                    </div>
                </div>
                <div class="dropdown">
                    <button type="button" class="btn dropdown-toggle toolbar-edit-table"
                        data-toggle="dropdown" title="Table tools" tabindex="-1"
                        data-original-title="Edit table" aria-expanded="false">
                        <i class="fa fa-columns fa-fw"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" id="table-add-column-left" data-call="addColumnLeft">Add a column left</a></li>
                        <li><a class="dropdown-item" href="#" id="table-add-column-right" data-call="addColumnRight">Add a column right</a></li>
                        <li><a class="dropdown-item" href="#" id="table-add-row-above" data-call="addRowAbove">Add a row above</a></li>
                        <li><a class="dropdown-item" href="#" id="table-add-row-below" data-call="addRowBelow">Add a row below</a></li>
                        <li><a class="dropdown-item" href="#" id="table-remove-column" data-call="removeColumn">Remove current column</a></li>
                        <li><a class="dropdown-item" href="#" id="table-remove-row" data-call="removeRow">Remove current row</a></li>
                        <li><a class="dropdown-item" href="#" id="table-delete-table" data-call="deleteTable">Delete current table</a></li>
                    </ul>
                </div>
            </div>

            <div id="link" class="btn-group">
                <div id="media-insert" title="Insert media" class="fa fa-file-image-o fa-fw btn editor-ignore"></div>
                <div id="create-link" title="Insert or edit link" class="fa fa-link fa-fw btn editor-ignore"></div>
                <div id="unlink" data-call="unlink" title="Remove link" class="fa fa-unlink fa-fw btn"></div>
                <a id="media-description" href="#" title="Edit media description" class="btn editor-ignore">Description</a>
            </div>

            <div id="image-shape" class="btn-group">
                <div id="rounded" title="Shape: Rounded" class="fa fa-square fa-fw btn editor-ignore"></div>
                <div id="rounded-circle" title="Shape: Circle" class="fa fa-circle-o fa-fw btn editor-ignore"></div>
                <div id="shadow" title="Shadow" class="fa fa-sun-o fa-fw btn editor-ignore"></div>
                <div id="img-thumbnail" title="Shape: Thumbnail" class="fa fa-picture-o fa-fw btn editor-ignore"></div>
            </div>

            <div id="image-padding" class="btn-group dropdown">
                <button type="button" class="btn dropdown-toggle"
                    data-toggle="dropdown" title="Image padding" tabindex="-1"
                    data-original-title="Image padding" aria-expanded="false">
                    <span class="fa fa-plus-square-o"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item editor-ignore" href="#" data-class="">None</a></li>
                    <li><a class="dropdown-item editor-ignore" href="#" data-class="padding-small">Small</a></li>
                    <li><a class="dropdown-item editor-ignore" href="#" data-class="padding-medium">Medium</a></li>
                    <li><a class="dropdown-item editor-ignore" href="#" data-class="padding-large">Large</a></li>
                    <li><a class="dropdown-item editor-ignore" href="#" data-class="padding-xl">XL</a></li>
                </ul>
            </div>

            <div id="image-width" class="btn-group">
                <div title="Resize Auto" class="btn editor-ignore">Auto</div>
                <div id="100%" title="Resize Full" class="btn editor-ignore">100%</div>
                <div id="50%" title="Resize Half" class="btn editor-ignore">50%</div>
                <div id="25%" title="Resize Quarter" class="btn editor-ignore">25%</div>
            </div>

            <div id="image-edit" class="btn-group">
                <div id="image-crop" title="Crop Image" class="btn fa fa-crop editor-ignore"></div>
                <div id="image-transform" class="btn editor-ignore fa fa-object-ungroup"
                    title="Transform the picture (click twice to reset transformation)"></div>
                <span class="oe-toolbar-separator d-flex"/>
                <div id="media-replace" title="Replace media" class="btn o_we_bg_brand_primary editor-ignore">Replace</div>
                <span class="oe-toolbar-separator d-flex"/>
                <div id="image-delete" class="btn btn-link text-danger editor-ignore fa fa-trash" title="Remove (DELETE)"></div>
            </div>

            <div id="fa-resize" class="btn-group only_fa">
                <div class="editor-ignore btn" title="Icon size 1x" data-value="1">1x</div>
                <div class="editor-ignore btn" title="Icon size 2x" data-value="2">2x</div>
                <div class="editor-ignore btn" title="Icon size 3x" data-value="3">3x</div>
                <div class="editor-ignore btn" title="Icon size 4x" data-value="4">4x</div>
                <div class="editor-ignore btn" title="Icon size 5x" data-value="5">5x</div>
            </div>

            <div class="btn-group only_fa">
                <div id="fa-spin" title="Toggle icon spin" class="editor-ignore btn fa fa-refresh"></div>
            </div>
        </div>
    </t>

    <t t-name="web_editor.toolbar.table-options">
        <we-customizeblock-options id="o-we-editor-table-container">
            <we-title>
                <span>Table Options</span>
                <div id="oe-table-delete-table" data-call="deleteTable" class="o_we_hover_danger">
                    <i class="fa fa-trash"/>
                </div>
            </we-title>
            <div id="oe-table-options">
                <div class="oe-table-row">
                    <span class="oe-table-label">Add Column</span>
                    <div class="w-100">
                        <div class="oe-table-selection btn-group w-100">
                            <button class="btn w-50" data-call="addColumnLeft">
                                <i class="small fa fa-fw fa-step-backward"/> Left
                            </button>
                            <button class="btn w-50" data-call="addColumnRight">
                                Right <i class="small fa fa-fw fa-step-forward"/>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="oe-table-row">
                    <span class="oe-table-label">Add Row</span>
                    <div class="w-100">
                        <div class="oe-table-selection btn-group w-100">
                            <button class="btn w-50" data-call="addRowAbove">
                                <i class="small fa fa-fw fa-step-backward fa-rotate-90"/> Above
                            </button>
                            <button class="btn w-50" data-call="addRowBelow">
                                Below <i class="small fa fa-fw fa-step-forward fa-rotate-90"/>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="oe-table-row mt-3">
                    <span class="oe-table-label">Remove Current</span>
                    <div class="w-100">
                        <div class="oe-table-selection btn-group w-100">
                            <button class="btn w-50" data-call="removeColumn">
                                <i class="small fa fa-fw fa-columns"/> Column
                            </button>
                            <button class="btn w-50" data-call="removeRow">
                                <i class="small fa fa-fw fa-bars"/> Row
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </we-customizeblock-options>
    </t>

    <!-- Collaboration reset dialog -->
    <t t-name="web_editor.collaboration-reset-dialog">
        <div>
            <div style="color: red;">
                <p>
                    There is a conflict between your version and the one in the database.
                </p>
                <p>
                    The version from the database will be used.
                    If you need to keep your changes, copy the content below and edit the new document.
                </p>
                <p style="font-weight: bold;">
                    Warning: after closing this dialog, the version you were working on will be discarded and will never be available anymore.
                </p>
            </div>
        </div>
    </t>

    <!--=================-->
    <!-- Snippet options -->
    <!--=================-->

    <!-- Background position option overlay -->
    <t t-name="web_editor.background_position_overlay">
        <div class="o_we_background_position_overlay oe_overlay">
            <div class="o_we_overlay_content position-absolute">
                <div class="o_overlay_background"/>
                <div class="o_we_overlay_buttons position-absolute d-flex m-1" style="top: 0">
                    <button class="btn btn-primary mr-1 o_btn_apply">Apply</button>
                    <button class="btn btn-danger o_btn_discard">Discard</button>
                </div>
            </div>
        </div>
    </t>
</templates>
