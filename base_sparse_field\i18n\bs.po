# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_sparse_field
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__boolean
msgid "Boolean"
msgstr "Logički"

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:25
#, python-format
msgid "Changing the storing system for field \"%s\" is not allowed."
msgstr "Izmjena sistema čuvanja za polje \"%s\" nije dozvoljeno."

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__char
msgid "Char"
msgstr "Znak"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__data
msgid "Data"
msgstr "Podaci"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_ir_model_fields__ttype
msgid "Field Type"
msgstr "Tip polja"

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_ir_model_fields
msgid "Fields"
msgstr "Polja"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__float
msgid "Float"
msgstr "Decimalni broj"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__id
msgid "ID"
msgstr "ID"

#. module: base_sparse_field
#: model:ir.model.fields,help:base_sparse_field.field_ir_model_fields__serialization_field_id
msgid ""
"If set, this field will be stored in the sparse structure of the "
"serialization field, instead of having its own database column. This cannot "
"be changed after creation."
msgstr ""
"Ako je postavljeno, ovo polje će biti snimljeno u proređenu strukturu "
"serijalizacijskog polja, umjesto da  ima sopstvenu kolonu u bazi podataka. "
"Ovo ne može da se promjeni nakon kreiranja."

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__integer
msgid "Integer"
msgstr "Cijeli broj"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: base_sparse_field
#: selection:sparse_fields.test,selection:0
msgid "One"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__partner
msgid "Partner"
msgstr "Partner"

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:27
#, python-format
msgid "Renaming sparse field \"%s\" is not allowed"
msgstr "Izmjena imena proređenog polja \"%s\" nije dozvoljeno"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__selection
msgid "Selection"
msgstr "Odabir"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_ir_model_fields__serialization_field_id
msgid "Serialization Field"
msgstr "Polje serijalizacije"

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:39
#, python-format
msgid "Serialization field `%s` not found for sparse field `%s`!"
msgstr ""

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_sparse_fields_test
msgid "Sparse fields Test"
msgstr ""

#. module: base_sparse_field
#: selection:sparse_fields.test,selection:0
msgid "Two"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "binary"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "boolean"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "char"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "date"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "datetime"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "float"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "html"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "integer"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "many2many"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "many2one"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "monetary"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "one2many"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "reference"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "selection"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "serialized"
msgstr ""

#. module: base_sparse_field
#: selection:ir.model.fields,ttype:0
msgid "text"
msgstr ""
