<?xml version="1.0" encoding="UTF-8" ?>
<!-- Copyright 2019 Tecnativa - Alexandre <PERSON>
     Copyright 2021 Sergey <PERSON>
     License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl). -->
<template>
    <t t-inherit="mail.AttachmentViewer" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[hasclass('o_AttachmentViewer')]" position="attributes">
            <attribute
                name="t-att-class"
                t-translation="off"
            >state.maximized ? 'o_AttachmentViewer_maximized' : ''</attribute>
        </xpath>
        <xpath
            expr="//div[hasclass('o_AttachmentViewer_headerItemButtonClose')]"
            position="before"
        >
            <div
                t-if="!state.maximized"
                class="o_AttachmentViewer_headerItem o_AttachmentViewer_headerItemButton o_AttachmentViewer_headerItemButtonMaximize"
                t-on-click="state.maximized=true"
                role="button"
                title="Maximize"
                aria-label="Maximize"
            >
                <i class="fa fa-fw fa-window-maximize" role="img" />
            </div>
            <div
                t-if="state.maximized"
                class="o_AttachmentViewer_headerItem o_AttachmentViewer_headerItemButton o_AttachmentViewer_headerItemButtonMinimize"
                t-on-click="state.maximized=false"
                role="button"
                title="Minimize"
                aria-label="Minimize"
            >
                <i class="fa fa-fw fa-window-minimize" role="img" />
            </div>
        </xpath>
    </t>
</template>
