<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="tax_report" model="account.tax.report">
        <field name="name">Tax Report</field>
        <field name="country_id" ref="base.ch"/>
    </record>

    <record id="account_tax_report_line_chiffre_af" model="account.tax.report.line">
        <field name="name">I. TURNOVER</field>
        <field name="report_id" ref="tax_report"/>
        <field name="formula">None</field>
        <field name="sequence" eval="1"/>
    </record>

    <record id="account_tax_report_line_chtax_200" model="account.tax.report.line">
        <field name="name">200 - Total amount of agreed or collected consideration incl. from supplies opted for taxation, transfer of supplies acc. to the notification procedure and supplies provided abroad (worldwide turnover)</field>
        <field name="code">tax_ch_200</field>
        <field name="formula">tax_ch_302a + tax_ch_303a + tax_ch_312a + tax_ch_313a + tax_ch_342a + tax_ch_343a + tax_ch_382a + tax_ch_383a</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_chiffre_af"/>
    </record>

    <record id="account_tax_report_line_chtax_205" model="account.tax.report.line">
        <field name="name">205 - Consideration reported in Ref. 200 from supplies exempt from the tax without credit (art. 21) where the option for their taxation according to art. 22 has been exercised</field>
        <field name="tag_name">205</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_chiffre_af"/>
    </record>

    <record id="account_tax_report_line_chtax_220_289" model="account.tax.report.line"> <!-- FIXME in master: the xml is as it is for historical reasons but it does represent box 220 only -->
        <field name="name">220 - Supplies exempt from the tax (e.g. export, art. 23) and supplies provided to institutional and individual beneficiaries that are exempt from liability for tax (art. 107 para. 1 lit. a)</field>
        <field name="code">tax_ch_220</field>
        <field name="tag_name">220</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_chiffre_af"/>
    </record>

    <record id="account_tax_report_line_chtax_221" model="account.tax.report.line">
        <field name="name">221 - Supplies provided abroad</field>
        <field name="code">tax_ch_221</field>
        <field name="tag_name">221</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_chiffre_af"/>
    </record>

    <record id="account_tax_report_line_chtax_225" model="account.tax.report.line">
        <field name="name">225 - Transfer of supplies according to the notification procedure (art. 38, please submit Form 764)</field>
        <field name="code">tax_ch_225</field>
        <field name="tag_name">225</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="5"/>
        <field name="parent_id" ref="account_tax_report_line_chiffre_af"/>
    </record>

    <record id="account_tax_report_line_chtax_230" model="account.tax.report.line">
        <field name="name">230 - Supplies provided on Swiss territory exempt from the tax without credit (art. 21) and where the option for their taxation according to art. 22 has not been exercised</field>
        <field name="code">tax_ch_230</field>
        <field name="tag_name">230</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="6"/>
        <field name="parent_id" ref="account_tax_report_line_chiffre_af"/>
    </record>

    <record id="account_tax_report_line_chtax_235" model="account.tax.report.line">
        <field name="name">235 - Reduction of consideration (discounts, rebates etc.)</field>
        <field name="code">tax_ch_235</field>
        <field name="tag_name">235</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="7"/>
        <field name="parent_id" ref="account_tax_report_line_chiffre_af"/>
    </record>

    <record id="account_tax_report_line_chtax_280" model="account.tax.report.line">
        <field name="name">280 - Miscellaneous (e.g. land value, purchase prices in case of margin taxation)</field>
        <field name="code">tax_ch_280</field>
        <field name="tag_name">280</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="8"/>
        <field name="parent_id" ref="account_tax_report_line_chiffre_af"/>
    </record>

    <record id="account_tax_report_line_chtax_289" model="account.tax.report.line">
        <field name="name">289 - Deductions (Total Ref. 220 to 280)</field>
        <field name="code">tax_ch_289</field>
        <field name="formula">tax_ch_220 + tax_ch_221 + tax_ch_225 + tax_ch_230 + tax_ch_235 + tax_ch_280</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="9"/>
        <field name="parent_id" ref="account_tax_report_line_chiffre_af"/>
    </record>

    <record id="account_tax_report_line_chtax_299" model="account.tax.report.line">
        <field name="name">299 - Taxable turnover (Ref. 200 minus Ref. 289)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="10"/>
        <field name="formula">tax_ch_200 - tax_ch_289</field>
        <field name="parent_id" ref="account_tax_report_line_chiffre_af"/>
    </record>

    <record id="account_tax_report_line_calc_impot" model="account.tax.report.line">
        <field name="name">II. TAX CALCULATION</field>
        <field name="report_id" ref="tax_report"/>
        <field name="formula">None</field>
        <field name="sequence" eval="2"/>
    </record>

    <record id="account_tax_report_line_supplies_1" model="account.tax.report.line">
        <field name="name">Supplies CHF from 01.01.2024</field>
        <field name="report_id" ref="tax_report"/>
        <field name="formula">None</field>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_303a" model="account.tax.report.line">
        <field name="name">303a - Standard rate (8,1%): Supplies CHF from 01.01.2024</field>
        <field name="code">tax_ch_303a</field>
        <field name="tag_name">303a</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_supplies_1"/>
    </record>

    <record id="account_tax_report_line_chtax_313a" model="account.tax.report.line">
        <field name="name">313a - Reduced rate (2,6%): Supplies CHF from 01.01.2024</field>
        <field name="code">tax_ch_313a</field>
        <field name="tag_name">313a</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_supplies_1"/>
    </record>

    <record id="account_tax_report_line_chtax_343a" model="account.tax.report.line">
        <field name="name">343a - Accommodation rate (3,8%): Supplies CHF from 01.01.2024</field>
        <field name="code">tax_ch_343a</field>
        <field name="tag_name">343a</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_supplies_1"/>
    </record>

    <record id="account_tax_report_line_chtax_383a" model="account.tax.report.line">
        <field name="name">383a - Acquisition tax: Supplies CHF from 01.01.2024</field>
        <field name="code">tax_ch_383a</field>
        <field name="tag_name">383a</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_supplies_1"/>
    </record>

    <record id="account_tax_report_line_supplies_2" model="account.tax.report.line">
        <field name="name">Supplies CHF to 31.12.2023</field>
        <field name="report_id" ref="tax_report"/>
        <field name="formula">None</field>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_302a" model="account.tax.report.line">
        <field name="name">302a - Standard rate (7,7%): Supplies CHF to 31.12.2023</field>
        <field name="code">tax_ch_302a</field>
        <field name="tag_name">302a</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_supplies_2"/>
    </record>

    <record id="account_tax_report_line_chtax_312a" model="account.tax.report.line">
        <field name="name">312a - Reduced rate (2,5%): Supplies CHF to 31.12.2023</field>
        <field name="code">tax_ch_312a</field>
        <field name="tag_name">312a</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_supplies_2"/>
    </record>

    <record id="account_tax_report_line_chtax_342a" model="account.tax.report.line">
        <field name="name">342a - Accommodation rate (3,7%): Supplies CHF to 31.12.2023</field>
        <field name="code">tax_ch_342a</field>
        <field name="tag_name">342a</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_supplies_2"/>
    </record>

    <record id="account_tax_report_line_chtax_382a" model="account.tax.report.line">
        <field name="name">382a - Acquisition tax: Supplies CHF to 31.12.2023</field>
        <field name="code">tax_ch_382a</field>
        <field name="tag_name">382a</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_supplies_2"/>
    </record>

    <record id="account_tax_report_line_tax_amount_1" model="account.tax.report.line">
        <field name="name">Tax amount CHF / cent. from 01.01.2024</field>
        <field name="report_id" ref="tax_report"/>
        <field name="formula">None</field>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_303b" model="account.tax.report.line">
        <field name="name">303b - Standard rate (8,1%): Tax amount CHF / cent. from 01.01.2024</field>
        <field name="code">tax_ch_303b</field>
        <field name="tag_name">303b</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_tax_amount_1"/>
    </record>

    <record id="account_tax_report_line_chtax_313b" model="account.tax.report.line">
        <field name="name">313b - Reduced rate (2,6%): Tax amount CHF / cent. from 01.01.2024</field>
        <field name="code">tax_ch_313b</field>
        <field name="tag_name">313b</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_tax_amount_1"/>
    </record>

    <record id="account_tax_report_line_chtax_343b" model="account.tax.report.line">
        <field name="name">343b - Accommodation rate (3,8%): Tax amount CHF / cent. from 01.01.2024</field>
        <field name="code">tax_ch_343b</field>
        <field name="tag_name">343b</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_tax_amount_1"/>
    </record>

    <record id="account_tax_report_line_chtax_383b" model="account.tax.report.line">
        <field name="name">383b - Acquisition tax: Tax amount CHF / cent. from 01.01.2024</field>
        <field name="code">tax_ch_383b</field>
        <field name="tag_name">383b</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_tax_amount_1"/>
    </record>

    <record id="account_tax_report_line_tax_amount_2" model="account.tax.report.line">
        <field name="name">Tax amount CHF / cent. to 31.12.2023</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_302b" model="account.tax.report.line">
        <field name="name">302b - Standard rate (7,7%): Tax amount CHF / cent. to 31.12.2023</field>
        <field name="code">tax_ch_302b</field>
        <field name="tag_name">302b</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_tax_amount_2"/>
    </record>

    <record id="account_tax_report_line_chtax_312b" model="account.tax.report.line">
        <field name="name">312b - Reduced rate (2,5%): Tax amount CHF / cent. to 31.12.2023</field>
        <field name="code">tax_ch_312b</field>
        <field name="tag_name">312b</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_tax_amount_2"/>
    </record>

    <record id="account_tax_report_line_chtax_342b" model="account.tax.report.line">
        <field name="name">342b - Accommodation rate (3,7%): Tax amount CHF / cent. to 31.12.2023</field>
        <field name="code">tax_ch_342b</field>
        <field name="tag_name">342b</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="account_tax_report_line_tax_amount_2"/>
    </record>

    <record id="account_tax_report_line_chtax_382b" model="account.tax.report.line">
        <field name="name">382b - Acquisition tax: Tax amount CHF / cent. to 31.12.2023</field>
        <field name="code">tax_ch_382b</field>
        <field name="tag_name">382b</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="account_tax_report_line_tax_amount_2"/>
    </record>

    <record id="account_tax_report_line_chtax_399" model="account.tax.report.line">
        <field name="name">399 - Total amount of tax due</field>
        <field name="code">tax_ch_399</field>
        <field name="formula">tax_ch_302b + tax_ch_303b + tax_ch_312b + tax_ch_313b + tax_ch_342b + tax_ch_343b + tax_ch_382b + tax_ch_383b</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="5"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_400" model="account.tax.report.line">
        <field name="name">400 - Input tax on cost of materials and supplies of services</field>
        <field name="code">tax_ch_400</field>
        <field name="tag_name">400</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="6"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_405" model="account.tax.report.line">
        <field name="name">405 - Input tax on investments and other operating costs</field>
        <field name="code">tax_ch_405</field>
        <field name="tag_name">405</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="7"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_410" model="account.tax.report.line">
        <field name="name">410 - De-taxation (art. 32)</field>
        <field name="code">tax_ch_410</field>
        <field name="tag_name">410</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="8"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_415" model="account.tax.report.line">
        <field name="name">415 - Correction of the input tax deduction: mixed use (art. 30), own use (art. 31)</field>
        <field name="code">tax_ch_415</field>
        <field name="tag_name">415</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="9"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_420" model="account.tax.report.line">
        <field name="name">420 - Reduction of the input tax deduction: Flow of funds, which are not deemed to be consideration, such as subsidies, tourist charges (art. 33 para. 2)</field>
        <field name="code">tax_ch_420</field>
        <field name="tag_name">420</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="10"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_479" model="account.tax.report.line">
        <field name="name">479 - Total Ref. 400 to 420</field>
        <field name="code">tax_ch_479</field>
        <field name="formula">tax_ch_400 + tax_ch_405 + tax_ch_410 - tax_ch_415 - tax_ch_420</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="11"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_500" model="account.tax.report.line">
        <field name="name">500 - Amount payable</field>
        <field name="formula">tax_ch_399 - tax_ch_479 &gt; 0 and tax_ch_399 - tax_ch_479 or 0.0</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="12"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_510" model="account.tax.report.line">
        <field name="name">510 - Credit in favour of the taxable person</field>
        <field name="formula">tax_ch_479 - tax_ch_399 &gt; 0 and tax_ch_479 - tax_ch_399 or 0.0</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="13"/>
        <field name="parent_id" ref="account_tax_report_line_calc_impot"/>
    </record>

    <record id="account_tax_report_line_chtax_autres_mouv" model="account.tax.report.line">
        <field name="name">III. OTHER CASH FLOWS</field>
        <field name="sequence" eval="3"/>
        <field name="report_id" ref="tax_report"/>
    </record>

    <record id="account_tax_report_line_chtax_900" model="account.tax.report.line">
        <field name="name">900 - Subsidies, tourist funds collected by tourist offices, contributions from cantonal water, sewage or waste funds (art. 18 para. 2 lit. a to c)</field>
        <field name="tag_name">900</field>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_chtax_autres_mouv"/>
        <field name="report_id" ref="tax_report"/>
    </record>

    <record id="account_tax_report_line_chtax_910" model="account.tax.report.line">
        <field name="name">910 - Donations, dividends, payments of damages etc. (art. 18 para. 2 lit. d to l)</field>
        <field name="tag_name">910</field>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="account_tax_report_line_chtax_autres_mouv"/>
        <field name="report_id" ref="tax_report"/>
    </record>
</odoo>
