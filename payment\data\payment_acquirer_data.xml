<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="payment_acquirer_adyen" model="payment.acquirer">
        <field name="name">Adyen</field>
        <field name="display_as">Credit Card (powered by Adyen)</field>
        <field name="image_128" type="base64" file="payment_adyen/static/src/img/adyen_icon.png"/>
        <field name="module_id" ref="base.module_payment_adyen"/>
        <field name="description" type="html">
            <p>
                A payment gateway to accept online payments via credit cards, debit cards and bank
                transfers.
            </p>
            <ul class="list-inline">
                <li class="list-inline-item"><i class="fa fa-check"/>Online Payment</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Payment Status Tracking</li>
            </ul>
        </field>
        <!-- https://www.adyen.com/payment-methods -->
        <field name="payment_icon_ids"
               eval="[(6, 0, [
                   ref('payment.payment_icon_cc_bancontact'),
                   ref('payment.payment_icon_cc_maestro'),
                   ref('payment.payment_icon_cc_mastercard'),
                   ref('payment.payment_icon_cc_visa'),
                   ref('payment.payment_icon_cc_discover'),
                   ref('payment.payment_icon_cc_diners_club_intl'),
                   ref('payment.payment_icon_cc_jcb'),
                   ref('payment.payment_icon_cc_unionpay'),
               ])]"/>
    </record>

    <record id="payment_acquirer_alipay" model="payment.acquirer">
        <field name="name">Alipay</field>
        <field name="display_as">Credit Card (powered by Alipay)</field>
        <field name="image_128" type="base64" file="payment_alipay/static/description/icon.png"/>
        <field name="module_id" ref="base.module_payment_alipay"/>
        <field name="description" type="html">
            <p>
                Alipay is the most popular online payment platform in China. Chinese consumers can
                buy online using their Alipay eWallet.
            </p>
            <ul class="list-inline">
                <li><i class="fa fa-check"/>Online Payment</li>
                <li><i class="fa fa-check"/>Payment Status Tracking</li>
            </ul>
        </field>
        <!-- https://intl.alipay.com/ihome/home/<USER>/buy.htm?topic=paymentMethods -->
        <field name="payment_icon_ids"
               eval="[(6, 0, [
                   ref('payment.payment_icon_cc_jcb'),
                   ref('payment.payment_icon_cc_mastercard'),
                   ref('payment.payment_icon_cc_western_union'),
                   ref('payment.payment_icon_cc_webmoney'),
                   ref('payment.payment_icon_cc_visa'),
               ])]"/>
    </record>

    <record id="payment_acquirer_authorize" model="payment.acquirer">
        <field name="name">Authorize.net</field>
        <field name="display_as">Credit Card (powered by Authorize)</field>
        <field name="image_128"
               type="base64"
               file="payment_authorize/static/src/img/authorize_icon.png"/>
        <field name="module_id" ref="base.module_payment_authorize"/>
        <field name="description" type="html">
            <p>
                A payment gateway to accept online payments via credit cards and e-checks.
            </p>
            <ul class="list-inline">
                <li class="list-inline-item"><i class="fa fa-check"/>Online Payment</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Payment Status Tracking</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Subscriptions</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Save Cards</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Manual Capture</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Embedded Credit Card Form</li>
            </ul>
        </field>
        <!-- https://www.authorize.net/solutions/merchantsolutions/onlinemerchantaccount/ -->
        <field name="payment_icon_ids"
               eval="[(6, 0, [
                   ref('payment.payment_icon_cc_maestro'),
                   ref('payment.payment_icon_cc_mastercard'),
                   ref('payment.payment_icon_cc_discover'),
                   ref('payment.payment_icon_cc_diners_club_intl'),
                   ref('payment.payment_icon_cc_jcb'),
                   ref('payment.payment_icon_cc_visa'),
               ])]"/>
    </record>

    <record id="payment_acquirer_buckaroo" model="payment.acquirer">
        <field name="name">Buckaroo</field>
        <field name="display_as">Credit Card (powered by Buckaroo)</field>
        <field name="image_128"
               type="base64"
               file="payment_buckaroo/static/src/img/buckaroo_icon.png"/>
        <field name="module_id" ref="base.module_payment_buckaroo"/>
        <field name="description" type="html">
            <p>
                A payment gateway to accept online payments via credit cards.
            </p>
            <ul class="list-inline">
                <li class="list-inline-item"><i class="fa fa-check"/>Online Payment</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Payment Status Tracking</li>
            </ul>
        </field>
        <!-- https://www.buckaroo-payments.com/products/payment-methods/ -->
        <field name="payment_icon_ids"
               eval="[(6, 0, [
                   ref('payment.payment_icon_cc_bancontact'),
                   ref('payment.payment_icon_cc_maestro'),
                   ref('payment.payment_icon_cc_mastercard'),
                   ref('payment.payment_icon_cc_visa'),
                   ref('payment.payment_icon_cc_american_express'),
               ])]"/>
    </record>

    <record id="payment_acquirer_mollie" model="payment.acquirer">
        <field name="name">Mollie</field>
        <field name="image_128" type="base64" file="payment_mollie/static/src/img/mollie_icon.png"/>
        <field name="module_id" ref="base.module_payment_mollie"/>
        <field name="description" type="html">
            <p>
                A payment gateway from Mollie to accept online payments.
            </p>
            <ul class="list-inline">
                <li class="list-inline-item"><i class="fa fa-check"/>Online Payment</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Payment Status Tracking</li>
            </ul>
        </field>

        <!-- https://www.mollie.com/en/payments -->
        <field name="payment_icon_ids" eval="[(6, 0, [
                ref('payment.payment_icon_cc_visa'),
                ref('payment.payment_icon_cc_american_express'),
                ref('payment.payment_icon_cc_maestro'),
                ref('payment.payment_icon_cc_mastercard'),
                ref('payment.payment_icon_cc_bancontact'),
                ref('payment.payment_icon_cc_eps'),
                ref('payment.payment_icon_cc_giropay'),
                ref('payment.payment_icon_cc_p24'),
                ref('payment.payment_icon_cc_ideal'),
                ref('payment.payment_icon_paypal'),
                ref('payment.payment_icon_apple_pay'),
                ref('payment.payment_icon_sepa'),
                ref('payment.payment_icon_kbc')
            ])]"/>

    </record>

    <record id="payment_acquirer_ogone" model="payment.acquirer">
        <field name="name">Ogone</field>
        <field name="display_as">Credit Card (powered by Ogone)</field>
        <field name="image_128"
               type="base64"
               file="payment_ogone/static/src/img/ingenico_icon.png"/>
        <field name="module_id" ref="base.module_payment_ogone"/>
        <field name="description" type="html">
            <p>
                Ogone supports a wide range of payment methods: credit cards, debit cards, bank
                transfers, Bancontact, iDeal, Giropay.
            </p>
            <ul class="list-inline">
                <li class="list-inline-item"><i class="fa fa-check"/>Online Payment</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Payment Status Tracking</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Subscriptions</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Save Cards</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Embedded Credit Card Form</li>
            </ul>
        </field>
        <field name="payment_icon_ids"
               eval="[(6, 0, [
                   ref('payment.payment_icon_cc_ideal'),
                   ref('payment.payment_icon_cc_bancontact'),
                   ref('payment.payment_icon_cc_maestro'),
                   ref('payment.payment_icon_cc_mastercard'),
                   ref('payment.payment_icon_cc_visa'),
               ])]"/>
    </record>

    <record id="payment_acquirer_paypal" model="payment.acquirer">
        <field name="name">PayPal</field>
        <field name="image_128" type="base64" file="payment_paypal/static/src/img/paypal_icon.png"/>
        <field name="module_id" ref="base.module_payment_paypal"/>
        <field name="description" type="html">
            <p>
                PayPal is the easiest way to accept payments via Paypal or credit cards.
            </p>
            <ul class="list-inline">
                <li class="list-inline-item"><i class="fa fa-check"/>Online Payment</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Payment Status Tracking</li>
            </ul>
        </field>
        <!-- https://www.paypal.com/us/selfhelp/article/Which-credit-cards-can-I-accept-with-PayPal-Merchant-Services-FAQ1525#business -->
        <field name="payment_icon_ids"
               eval="[(6, 0, [
                   ref('payment.payment_icon_cc_maestro'),
                   ref('payment.payment_icon_cc_mastercard'),
                   ref('payment.payment_icon_cc_discover'),
                   ref('payment.payment_icon_cc_diners_club_intl'),
                   ref('payment.payment_icon_cc_jcb'),
                   ref('payment.payment_icon_cc_american_express'),
                   ref('payment.payment_icon_cc_unionpay'),
                   ref('payment.payment_icon_cc_visa'),
               ])]"/>
    </record>

    <record id="payment_acquirer_payulatam" model="payment.acquirer">
        <field name="name">PayU Latam</field>
        <field name="display_as">Credit Card (powered by PayU Latam)</field>
        <field name="image_128"
               type="base64"
               file="payment_payulatam/static/src/img/payulatam_icon.png"/>
        <field name="module_id" ref="base.module_payment_payulatam"/>
        <field name="description" type="html">
            <p>
                PayU is a leading financial services provider in Colombia, Argentina, Brazil, Chile,
                Mexico, Panama, and Peru. It allows merchant to accept local payments with just one
                account and integration.
            </p>
            <ul class="list-inline">
                <li class="list-inline-item"><i class="fa fa-check"/>Online Payment</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Payment Status Tracking</li>
            </ul>
        </field>
        <!-- https://www.payulatam.com/medios-de-pago/ -->
        <field name="payment_icon_ids"
               eval="[(6, 0, [
                   ref('payment.payment_icon_cc_diners_club_intl'),
                   ref('payment.payment_icon_cc_mastercard'),
                   ref('payment.payment_icon_cc_american_express'),
                   ref('payment.payment_icon_cc_visa'),
                   ref('payment.payment_icon_cc_codensa_easy_credit'),
               ])]"/>
    </record>

    <record id="payment_acquirer_payumoney" model="payment.acquirer">
        <field name="name">PayUmoney</field>
        <field name="display_as">Credit Card (powered by PayUmoney)</field>
        <field name="image_128"
               type="base64"
               file="payment_payumoney/static/src/img/payumoney_icon.png"/>
        <field name="module_id" ref="base.module_payment_payumoney"/>
        <field name="description" type="html">
            <p>
                PayUmoney is an online payments solutions company serving the Indian market.
            </p>
            <ul class="list-inline">
                <li class="list-inline-item"><i class="fa fa-check"/>Online Payment</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Payment Status Tracking</li>
            </ul>
        </field>
        <!-- See https://www.payumoney.com/selfcare.html?userType=seller
             > Banks & Cards > What options do you have in the Credit Card payment? -->
        <field name="payment_icon_ids"
               eval="[(6, 0, [
                   ref('payment.payment_icon_cc_maestro'),
                   ref('payment.payment_icon_cc_mastercard'),
                   ref('payment.payment_icon_cc_american_express'),
                   ref('payment.payment_icon_cc_visa'),
               ])]"/>
    </record>

    <record id="payment_acquirer_sepa_direct_debit" model="payment.acquirer">
        <field name="name">SEPA Direct Debit</field>
        <field name="sequence">20</field>
        <field name="image_128"
               type="base64"
               file="base/static/img/icons/payment_sepa_direct_debit.png"/>
        <field name="module_id" ref="base.module_payment_sepa_direct_debit"/>
        <field name="description" type="html">
            <p>
                SEPA Direct Debit is a Europe-wide Direct Debit system that allows merchants to
                collect Euro-denominated payments from accounts in the 34 SEPA countries and
                associated territories.
            </p>
        </field>
    </record>

    <record id="payment_acquirer_sips" model="payment.acquirer">
        <field name="name">Sips</field>
        <field name="display_as">Credit Card (powered by Sips)</field>
        <field name="image_128" type="base64" file="payment_sips/static/src/img/sips_icon.png"/>
        <field name="module_id" ref="base.module_payment_sips"/>
        <field name="description" type="html">
            <p>
                A payment gateway from Atos Worldline to accept online payments via credit cards.
            </p>
            <ul class="list-inline">
                <li class="list-inline-item"><i class="fa fa-check"/>Online Payment</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Payment Status Tracking</li>
            </ul>
        </field>
        <!-- See http://sips.worldline.com/en-us/home/<USER>/payment-types-and-acquirers.html -->
        <field name="payment_icon_ids"
               eval="[(6, 0, [
                   ref('payment.payment_icon_cc_maestro'),
                   ref('payment.payment_icon_cc_mastercard'),
                   ref('payment.payment_icon_cc_discover'),
                   ref('payment.payment_icon_cc_diners_club_intl'),
                   ref('payment.payment_icon_cc_jcb'),
                   ref('payment.payment_icon_cc_american_express'),
                   ref('payment.payment_icon_cc_bancontact'),
                   ref('payment.payment_icon_cc_unionpay'),
                   ref('payment.payment_icon_cc_visa'),
               ])]"/>
    </record>

    <record id="payment_acquirer_stripe" model="payment.acquirer">
        <field name="name">Stripe</field>
        <field name="display_as">Credit &amp; Debit Card</field>
        <field name="image_128" type="base64" file="payment_stripe/static/src/img/stripe_icon.png"/>
        <field name="module_id" ref="base.module_payment_stripe"/>
        <field name="description" type="html">
            <p>
                A payment gateway to accept online payments via credit and debit cards.
            </p>
            <ul class="list-inline">
                <li class="list-inline-item"><i class="fa fa-check"/>Online Payment</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Payment Status Tracking</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Subscriptions</li>
                <li class="list-inline-item"><i class="fa fa-check"/>Save Cards</li>
            </ul>
        </field>
        <!--
            See https://stripe.com/payments/payment-methods-guide
            See https://support.goteamup.com/hc/en-us/articles/115002089349-Which-cards-and-payment-types-can-I-accept-with-Stripe-
        -->
        <field name="payment_icon_ids"
               eval="[(6, 0, [
                   ref('payment.payment_icon_cc_maestro'),
                   ref('payment.payment_icon_cc_mastercard'),
                   ref('payment.payment_icon_cc_discover'),
                   ref('payment.payment_icon_cc_diners_club_intl'),
                   ref('payment.payment_icon_cc_jcb'),
                   ref('payment.payment_icon_cc_american_express'),
                   ref('payment.payment_icon_cc_visa'),
               ])]"/>
    </record>

    <record id="payment_acquirer_test" model="payment.acquirer">
        <field name="name">Test</field>
        <field name="sequence">40</field>
        <field name="image_128" type="base64" file="payment_test/static/src/img/test_logo.jpg"/>
        <field name="module_id" ref="base.module_payment_test"/>
        <field name="description" type="html">
            <p>
                A testing payment gateway intended for demonstrating payment flows without the need
                of creating a seller account or providing payment details.
            </p>
        </field>
    </record>

    <record id="payment_acquirer_transfer" model="payment.acquirer">
        <field name="name">Wire Transfer</field>
        <field name="sequence">30</field>
        <field name="image_128"
               type="base64"
               file="payment_transfer/static/src/img/transfer_icon.png"/>
        <field name="module_id" ref="base.module_payment_transfer"/>
        <field name="description" type="html">
            <p>
                Provide instructions to customers so that they can pay their orders manually.
            </p>
        </field>
    </record>

</odoo>
