<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="account_tax_sale_vat1" model="account.tax.template">
        <field name="name">НӨАТ бараа</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">1</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group1"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag5')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag5')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag5')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag5')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat1" model="account.tax.template">
        <field name="name">НӨАТ-тэй худалдан авалт</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">1</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group4"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag36'),ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag36')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag36'),ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag36')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat2" model="account.tax.template">
        <field name="name">НӨАТ-гүй худалдан авалт</field>
        <field name="amount">0.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">0%</field>
        <field name="tax_group_id" ref="account_tax_group4"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat2" model="account.tax.template">
        <field name="name">НӨАТ 0% борлуулалт</field>
        <field name="amount">0.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">0%</field>
        <field name="tax_group_id" ref="account_tax_group6"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat3" model="account.tax.template">
        <field name="name">НӨАТ чөлөөлөх борлуулалт</field>
        <field name="amount">0.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">10</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">0%</field>
        <field name="tax_group_id" ref="account_tax_group6"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag2')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat4" model="account.tax.template">
        <field name="name">НӨАТ бусад барааны борлуулалт</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group1"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag6')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag6')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat5" model="account.tax.template">
        <field name="name">НӨАТ эрх борлуулалт</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group1"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag7')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag7')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat6" model="account.tax.template">
        <field name="name">НӨАТ татан буугдах үеийн борлуулалт</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group1"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag8')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag8')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag8')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag8')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat7" model="account.tax.template">
        <field name="name">НӨАТ өрийн төлбөрт өгсөн бараа</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group1"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag9')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag9')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat8" model="account.tax.template">
        <field name="name">НӨАТ нотариат үйлчилгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag11')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag11')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag11')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag11')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat9" model="account.tax.template">
        <field name="name">НӨАТ өрийн төлбөрт өгсөн үйлчилгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag12')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag12')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag12')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag12')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat10" model="account.tax.template">
        <field name="name">НӨАТ цахилгаан, дулаан, ус, шуудан, холбоо</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag13')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag13')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag13')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag13')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat11" model="account.tax.template">
        <field name="name">НӨАТ бараа түрээслүүлэх, эзэмшүүлэх</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag14')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag14')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat12" model="account.tax.template">
        <field name="name">НӨАТ байр түрээслүүлэх, эзэмшүүлэх</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag15')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag15')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag15')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag15')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat13" model="account.tax.template">
        <field name="name">НӨАТ хөрөнгө түрээслүүлэх, эзэмшүүлэх</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag16')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag16')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag16')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag16')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat14" model="account.tax.template">
        <field name="name">НӨАТ бүтээл, загвар, зохиогчийн эрх</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag17')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag17')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag17')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag17')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat15" model="account.tax.template">
        <field name="name">НӨАТ хонжворт сугалаа, таавар, бооцоо</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag18')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag18')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag18')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag18')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat16" model="account.tax.template">
        <field name="name">НӨАТ зуучлалын үйлчилгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag19')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag19')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag19')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag19')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat17" model="account.tax.template">
        <field name="name">НӨАТ авсан хүү торгууль, алданги</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag20')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag20')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag20')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag20')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat18" model="account.tax.template">
        <field name="name">НӨАТ хөрөнгийн үнэлгээний үйлчилгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag21')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag21')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag21')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag21')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat19" model="account.tax.template">
        <field name="name">НӨАТ төсвийн санхүүжилт, татаас</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag22')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag22')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag22')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag22')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat20" model="account.tax.template">
        <field name="name">НӨАТ хууль зүйн үйлчилгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag23')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag23')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag23')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag23')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat21" model="account.tax.template">
        <field name="name">НӨАТ үсчин, гоо сайхан, угаалга, цэвэрлэгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag24')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag24')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag24')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag24')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat22" model="account.tax.template">
        <field name="name">НӨАТ бусад үйлчилгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">20</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group2"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag25')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag25')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag25')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag25')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat23" model="account.tax.template">
        <field name="name">НӨАТ 0% экспорт бараа</field>
        <field name="amount">0.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">30</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">0%</field>
        <field name="tax_group_id" ref="account_tax_group3"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag28')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag28')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat24" model="account.tax.template">
        <field name="name">НӨАТ 0% экспорт үйлчилгээ</field>
        <field name="amount">0.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">30</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">0%</field>
        <field name="tax_group_id" ref="account_tax_group3"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag29')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag29')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat3" model="account.tax.template">
        <field name="name">НӨАТ импортоор авсан бараа, үйлчилгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">40</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group4"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33'),ref('vat_report_tag35')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag35')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33'),ref('vat_report_tag35')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag35')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat4" model="account.tax.template">
        <field name="name">НӨАТ төлөгчөөр бүртгүүлэх үед худалдан авсан</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">40</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group4"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33'),ref('vat_report_tag37')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag37')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33'),ref('vat_report_tag37')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag37')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat5" model="account.tax.template">
        <field name="name">НӨАТ мал аж ахуй эрхлэгчээс худалдан авсан</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">40</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group4"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33'),ref('vat_report_tag38')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag38')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33'),ref('vat_report_tag38')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag38')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat6" model="account.tax.template">
        <field name="name">НӨАТ автомашин, эд анги худалдан авалт</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">40</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group4"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag41')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag41')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat7" model="account.tax.template">
        <field name="name">НӨАТ ажлын хэрэгцээнд авсан бараа</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">40</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group4"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag42')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag42')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat8" model="account.tax.template">
        <field name="name">НӨАТ импортоор авсан үндсэн хөрөнгө</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">40</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group4"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag43')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag43')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat9" model="account.tax.template">
        <field name="name">НӨАТ чөлөөлөгдөх үйлдвэрлэлд зориулсан бараа, үйлчилгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">40</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group4"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag44')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag44')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat10" model="account.tax.template">
        <field name="name">НӨАТ хайгуулд зориулсан импортын бараа, үйлчилгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">40</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group4"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag45')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag45')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat11" model="account.tax.template">
        <field name="name">НӨАТ импортын бусад хамааралгүй бараа, үйлчилгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">40</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group4"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag46')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag33')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag46')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat25" model="account.tax.template">
        <field name="name">НӨАТ дотоодод зарсан санхүүгийн түрээс</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">50</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group5"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag48')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag48')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag48')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag48')],
            }),
        ]"/>
    </record>

    <record id="account_tax_sale_vat26" model="account.tax.template">
        <field name="name">НӨАТ Факторинг, форфайтинг хэлцлийн үйлчилгээ</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">50</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group5"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag49')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag49')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag49')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_3401_0201'),
                'tag_ids': [ref('vat_report_tag49')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat12" model="account.tax.template">
        <field name="name">НӨАТ Санхүүгийн түрээсийн худалдан авалт</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">50</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group5"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag51')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag51')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag51')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag51')],
            }),
        ]"/>
    </record>

    <record id="account_tax_purchase_vat13" model="account.tax.template">
        <field name="name">НӨАТ Факторинг, форфайтинг худалдан авалт</field>
        <field name="amount">10.0</field>
        <field name="price_include" eval="1"/>
        <field name="sequence">50</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="description">10%</field>
        <field name="tax_group_id" ref="account_tax_group5"/>
        <field name="chart_template_id" ref="mn_chart_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag52')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag52')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('vat_report_tag52')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_template_1204_0301'),
                'tag_ids': [ref('vat_report_tag52')],
            }),
        ]"/>
    </record>

</odoo>
