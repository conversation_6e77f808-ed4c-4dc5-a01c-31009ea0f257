# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_images
# 
# Translators:
# <PERSON><PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>ran <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: product_images
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
#, python-format
msgid ""
"%(matching_images_count)s matching images have been found for "
"%(product_count)s products."
msgstr ""
"%(matching_images_count)s hình ảnh trùng khớp được tìm thấy cho "
"%(product_count)s sản phẩm."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"<span attrs=\"{'invisible': [('nb_products_selected', '&lt;=', 10000)]}\">\n"
"                            As only 10,000 products can be processed per day, the remaining will be\n"
"                            done tomorrow.\n"
"                        </span>"
msgstr ""
"<span attrs=\"{'invisible': [('nb_products_selected', '&lt;=', 10000)]}\">\n"
"                            Vì chỉ có 10.000 sản phẩm cps thể được xử lý mỗi ngày, số còn lại sẽ được\n"
"                            hoàn thành vào ngày mai.\n"
"                        </span>"

#. module: product_images
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
#, python-format
msgid ""
"A task to process products in the background is already running. Please try "
"againlater."
msgstr ""
"Một nhiệm vụ xử lý sản phẩm trong nền đang chạy. Vui lòng thử lại sau."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.res_config_settings_view_form
msgid "API Key"
msgstr "Mã khóa API"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "Cancel"
msgstr "Hủy"

#. module: product_images
#: model:ir.model,name:product_images.model_res_config_settings
msgid "Config Settings"
msgstr "Thiết lập cấu hình"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__create_date
msgid "Created on"
msgstr "Thời điểm tạo"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: product_images
#: model:ir.model,name:product_images.model_product_fetch_image_wizard
msgid ""
"Fetch product images from Google Images based on the product's barcode "
"number."
msgstr ""
"Tìm nạp hình ảnh sản phẩm từ Google Hình ảnh dựa trên số mã vạch của sản "
"phẩm."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "Get Pictures"
msgstr "Lấy hình ảnh"

#. module: product_images
#: model:ir.actions.act_window,name:product_images.product_product_action_get_pic_with_barcode
#: model:ir.actions.act_window,name:product_images.product_template_action_get_pic_with_barcode
msgid "Get Pictures from Google Images"
msgstr "Lấy hình ảnh từ Google Hình ảnh"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_res_config_settings__google_custom_search_key
msgid "Google Custom Search API Key"
msgstr "Khóa API tìm kiếm tùy chỉnh của Google"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__id
msgid "ID"
msgstr "ID"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_product__image_fetch_pending
msgid "Image Fetch Pending"
msgstr "Đang chờ tìm nạp hình ảnh"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_unable_to_process
msgid "Number of product unprocessable"
msgstr "Số sản phẩm không thể xử lý"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_to_process
msgid "Number of products to process"
msgstr "Số sản phẩm cần xử lý"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__nb_products_selected
msgid "Number of selected products"
msgstr "Số sản phẩm được lựa chọn"

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"Please note that some images might not be royalty-free. You should not\n"
"                        publish these on your website."
msgstr ""
"Xin lưu ý rằng một số hình ảnh có thể không được miễn phí bản quyền. Bạn không nên\n"
"                        đăng những hình ảnh đó lên trang web."

#. module: product_images
#: model:ir.model,name:product_images.model_product_product
msgid "Product"
msgstr "Sản phẩm"

#. module: product_images
#: model:ir.actions.server,name:product_images.ir_cron_fetch_image_ir_actions_server
#: model:ir.cron,cron_name:product_images.ir_cron_fetch_image
#: model:ir.cron,name:product_images.ir_cron_fetch_image
msgid "Product Images: Get product images from Google"
msgstr "Hình ảnh sản phẩm: Lấy hình ảnh sản phẩm từ Google"

#. module: product_images
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
#, python-format
msgid "Product images"
msgstr "Hình ảnh sản phẩm"

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_product_fetch_image_wizard__products_to_process
msgid "Products To Process"
msgstr "Sản phẩm cần xử lý"

#. module: product_images
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
#, python-format
msgid ""
"Products are processed in the background. Images will be updated "
"progressively."
msgstr ""
"Sản phẩm đang được xử lý trong nền. Hình ảnh sẽ được cập nhật dần dần."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.res_config_settings_view_form
msgid "Search Engine ID"
msgstr "ID công cụ tìm kiếm"

#. module: product_images
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
#, python-format
msgid "The API Key and Search Engine ID must be set in the General Settings."
msgstr ""
"Khoá API và ID công cụ tìm kiếm phải được cài đặt trong phần Cài đặt chung."

#. module: product_images
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
#, python-format
msgid ""
"The Custom Search API is not enabled in your Google project. Please visit "
"your Google Cloud Platform project page and enable it, then retry. If you "
"enabled this API recently, please wait a few minutes and retry."
msgstr ""
"API tìm kiếm tùy chỉnh không được bật trong dự án Google của bạn. Vui lòng "
"truy cập trang dự án Google Cloud Platform của bạn và bật lên, sau đó thử "
"lại. Nếu bạn đã bật API này gần đây, vui lòng đợi vài phút và thử lại."

#. module: product_images
#: model:ir.model.fields,field_description:product_images.field_res_config_settings__google_pse_id
msgid "The identifier of the Google Programmable Search Engine"
msgstr "Mã định danh của Công cụ tìm kiếm có thể lập trình của Google"

#. module: product_images
#: model:ir.model.fields,help:product_images.field_product_fetch_image_wizard__products_to_process
msgid ""
"The list of selected products that meet the criteria (have a barcode and no "
"image)"
msgstr ""
"Danh sách sản phẩm được lựa chọn đạt tiêu chí (có mã vạch và không có hình "
"ảnh)"

#. module: product_images
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
#, python-format
msgid ""
"The scheduled action \"Product Images: Get product images from Google\" has "
"been deleted. Please contact your administrator to have the action restored "
"or to reinstall the module \"product_images\"."
msgstr ""
"Hành động được lên lịch \"Hình ảnh sản phẩm: Lấy hình ảnh sản phẩm từ "
"Google\" đã bị xóa. Vui lòng liên hệ với quản trị viên để khôi phục hành "
"động hoặc cài đặt lại module \"product_images\"."

#. module: product_images
#: code:addons/product_images/models/ir_cron_trigger.py:0
#, python-format
msgid "This action is already scheduled. Please try again later."
msgstr "Hành động này đã được lên lịch. Vui lòng thử lại sau. "

#. module: product_images
#: model:ir.model,name:product_images.model_ir_cron_trigger
msgid "Triggered actions"
msgstr "Triggered actions"

#. module: product_images
#: model:ir.model.fields,help:product_images.field_product_product__image_fetch_pending
msgid "Whether an image must be fetched for this product. Handled by a cron."
msgstr ""
"Liệu một hình ảnh có phải được tìm nạp cho sản phẩm này hay không. Được xử "
"lý bởi một cron."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "You selected"
msgstr "Bạn đã chọn"

#. module: product_images
#: code:addons/product_images/wizard/product_fetch_image_wizard.py:0
#, python-format
msgid "Your API Key or your Search Engine ID is incorrect."
msgstr "Khoá API hoặc ID công cụ tìm kiếm của bạn không chính xác."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "of which will be processed."
msgstr "mà sẽ được xử lý."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid ""
"products will not be\n"
"                            processed because they either already have an image or their barcode\n"
"                            number is not set."
msgstr ""
"sản phẩm sẽ không\n"
"                            được xử lý bởi vì chúng đã có hình ảnh hoặc mã vạch của chúng\n"
"                            không được thiết lập."

#. module: product_images
#: model_terms:ir.ui.view,arch_db:product_images.product_fetch_image_wizard_view_form
msgid "products,"
msgstr "sản phẩm,"
