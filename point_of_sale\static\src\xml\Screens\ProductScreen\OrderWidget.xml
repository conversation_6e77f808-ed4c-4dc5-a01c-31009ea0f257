<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="OrderWidget" owl="1">
        <div class="order-container" t-ref="scrollable">
            <div class="order">
                <t t-if="orderlinesArray.length === 0" >
                    <div class='order-empty'>
                        <i class='fa fa-shopping-cart' role="img" aria-label="Shopping cart"
                           title="Shopping cart"/>
                        <h1>This order is empty</h1>
                    </div>
                </t>
                <t t-else="">
                    <ul class="orderlines">
                        <t t-foreach="orderlinesArray" t-as="orderline" t-key="orderline.id">
                            <Orderline line="orderline" />
                        </t>
                    </ul>
                    <OrderSummary total="state.total" tax="state.tax" />
                </t>
            </div>
        </div>
    </t>

</templates>
