# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-03-31 15:42+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Albanian (http://www.transifex.com/odoo/odoo-9/language/sq/)\n"
"Language: sq\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"\n"
"% set access_action = object.get_access_action()\n"
"% set doc_name = 'quotation' if object.state in ('draft', 'sent') else "
"'order confirmation'\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions."
"act_url'\n"
"% set access_name = is_online and object.template_id and 'Accept and pay %s "
"online' % doc_name or 'View %s' % doc_name\n"
"% set access_url = is_online and access_action['url'] or object."
"get_signup_url()\n"
"\n"
"<p>Dear\n"
"% if object.partner_id.is_company and object.child_ids:\n"
"    ${object.partner_id.child_ids[0].name}\n"
"% else :\n"
"    ${object.partner_id.name}\n"
"% endif\n"
",</p>\n"
"<p>Thank you for your inquiry.<br />\n"
"Here is your ${doc_name} <strong>${object.name}</strong>\n"
"% if object.origin:\n"
"(with reference: ${object.origin} )\n"
"% endif\n"
"amounting <strong>${object.amount_total} ${object.pricelist_id.currency_id."
"name}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"<p style=\"margin-left: 30px; margin-top: 10 px; margin-bottom: 10px;\">\n"
"    <a href=\"${access_url}\" style=\"padding: 5px 10px; font-size: 12px; "
"line-height: 18px; color: #FFFFFF; border-color:#a24689; text-decoration: "
"none; display: inline-block; margin-bottom: 0px; font-weight: 400; text-"
"align: center; vertical-align: middle; cursor: pointer; white-space: nowrap; "
"background-image: none; background-color: #a24689; border: 1px solid "
"#a24689; border-radius:3px\" class=\"o_default_snippet_text\">${access_name}"
"</a>\n"
"</p>\n"
"<p>If you have any question, do not hesitate to contact us.</p>\n"
"<p>Best regards,</p>\n"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_sales_count
#: model:ir.model.fields,field_description:sale.field_product_template_sales_count
msgid "# Sales"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_count
msgid "# of Invoices"
msgstr "# të Invoice-ve"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_nbr
msgid "# of Lines"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_count
msgid "# of Orders"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_product_uom_qty
msgid "# of Qty"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner_sale_order_count
msgid "# of Sales Order"
msgstr ""

#. module: sale
#: model:mail.template,report_name:sale.email_template_edi_sale
msgid ""
"${(object.name or '').replace('/','_')}_${object.state == 'draft' and "
"'draft' or ''}"
msgstr ""

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"${object.company_id.name} ${object.state in ('draft', 'sent') and "
"'Quotation' or 'Order'} (Ref ${object.name or 'n/a' })"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "(update)"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"<i>Example: pre-paid service offers for which the customer have\n"
"                to buy an extra pack of hours, because he used all his "
"support\n"
"                hours.</i>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Shënimi i gjendjes fiskale :</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Payment Term:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Shipping address:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Total Without Taxes</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Total</strong>"
msgstr "<strong>Totali</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference:</strong>"
msgstr ""

#. module: sale
#: selection:sale.config.settings,sale_pricelist_setting:0
msgid "A single sale price per product"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_deposit_account_id
msgid "Account used for deposits"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_sale_delivery_address
msgid "Addresses"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_delivery_invoice_address
msgid "Addresses in Sales Orders"
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:139
#, python-format
msgid "Advance: %s"
msgstr ""

#. module: sale
#: selection:sale.config.settings,sale_pricelist_setting:0
msgid "Advanced pricing based on formula"
msgstr ""

#. module: sale
#: selection:sale.config.settings,group_discount_per_so_line:0
msgid "Allow discounts on sales order lines"
msgstr ""

#. module: sale
#: selection:sale.config.settings,auto_done_setting:0
msgid ""
"Allow to edit sales order from the 'Sales Order' menu (not from the "
"Quotation menu)"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_sale_pricelist
msgid ""
"Allows to manage different prices based on rules per category of customers.\n"
"                    Example: 10% for retailers, promotion of 5 EUR on this "
"product, etc."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_uom
msgid ""
"Allows you to select and maintain different units of measure for products."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_sales_to_invoice_amount
msgid "Amount of sales to invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_project_id
#: model:ir.model.fields,field_description:sale.field_sale_report_analytic_account_id
msgid "Analytic Account"
msgstr "Llogaria Analitike"

#. module: sale
#: model:res.groups,name:sale.group_analytic_accounting
msgid "Analytic Accounting for Sales"
msgstr ""

#. module: sale
#: model:ir.filters,name:sale.filter_isale_report_product
msgid "By Product"
msgstr ""

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_salespersons
msgid "By Salespersons"
msgstr ""

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_salesteam
msgid "By Salesteam"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_delivered_updateable
msgid "Can Edit Delivered"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Anullo"

#. module: sale
#: selection:sale.order,state:0 selection:sale.report,state:0
msgid "Cancelled"
msgstr "E Anulluar"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_use_invoices
msgid "Check this box to manage invoices in this sales team."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_use_quotations
msgid "Check this box to manage quotations in this sales team."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Click to define a team target"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_commercial_partner_id
msgid "Commercial Entity"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Kompanitë"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_company_id
#: model:ir.model.fields,field_description:sale.field_sale_report_company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Kompani"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm Sale"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Create Invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Invoices"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid "Create a Quotation, the first step of a new sale."
msgstr ""

#. module: sale
#: selection:product.template,track_service:0
msgid "Create a task and track hours"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create and View Invoices"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line_create_uid
msgid "Created by"
msgstr "Krijuar nga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line_create_date
msgid "Created on"
msgstr "Krijuar me"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_create_date
msgid "Creation Date"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Partner"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_client_order_ref
msgid "Customer Reference"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_deposit_taxes_id
msgid "Customer Taxes"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Customer portal"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_date
msgid "Date Order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Date Ordered:"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_create_date
msgid "Date on which sales order is created."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_default_invoice_policy
msgid "Default Invoicing"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company_sale_note
msgid "Default Terms and Conditions"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_deposit_product_id_setting
msgid "Default product used for payment advances"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_company_inherit_form2
msgid "Default terms & conditions..."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_delivered
msgid "Delivered"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered Quantity"
msgstr ""

#. module: sale
#: selection:product.template,invoice_policy:0
msgid "Delivered quantities"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_shipping_id
msgid "Delivery Address"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_customer_lead
msgid "Delivery Lead Time"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_partner_shipping_id
msgid "Delivery address for current sales order."
msgstr ""

#. module: sale
#: model:product.product,name:sale.advance_product_0
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_deposit_product_id_setting
msgid "Deposit Product"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Description"
msgstr "Përshkrimi"

#. module: sale
#: selection:sale.config.settings,sale_pricelist_setting:0
msgid "Different prices per customer segment"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#, fuzzy
msgid "Disc.(%)"
msgstr "Diskont (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_discount_per_so_line
msgid "Discount"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_discount
msgid "Discount (%)"
msgstr "Diskont (%)"

#. module: sale
#: model:res.groups,name:sale.group_discount_per_so_line
msgid "Discount on lines"
msgstr ""

#. module: sale
#: selection:sale.config.settings,group_sale_delivery_address:0
msgid ""
"Display 3 fields on sales orders: customer, invoice address, delivery address"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line_display_name
#: model:ir.model.fields,field_description:sale.field_sale_report_display_name
msgid "Display Name"
msgstr "Emri i paraqitur"

#. module: sale
#: model:res.groups,name:sale.group_display_incoterm
msgid "Display incoterms on Sales Order and related invoices"
msgstr ""

#. module: sale
#: selection:sale.config.settings,module_sale_margin:0
msgid "Display margins on quotations and sales orders"
msgstr ""

#. module: sale
#: selection:sale.config.settings,module_sale_layout:0
msgid "Do not personalize sale orders and invoice reports"
msgstr ""

#. module: sale
#: selection:sale.order,state:0
msgid "Done"
msgstr "E Kryer"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:83
#, python-format
msgid "Down Payment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_amount
msgid "Down Payment Amount"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_product_id
msgid "Down Payment Product"
msgstr ""

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Down payment (fixed amount)"
msgstr ""

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Down payment (percentage)"
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:80
#, python-format
msgid "Down payment of %s%%"
msgstr ""

#. module: sale
#: selection:sale.report,state:0
msgid "Draft Quotation"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_survey_mail_compose_message
msgid "Email composition wizard for Survey"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_website_portal
msgid "Enable customer portal to track orders, delivery and invoices"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_validity_date
msgid "Expiration Date"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_fiscal_position_id
msgid "Fiscal Position"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_sale_pricelist_setting
msgid ""
"Fix Price: all price manage from products sale price.\n"
"Different prices per Customer: you can assign price on buying of minimum "
"quantity in products sale tab.\n"
"Advanced pricing based on formula: You can have all the rights on pricelist"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Fully Invoiced"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_weight
msgid "Gross Weight"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Grupo Nga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_id
#: model:ir.model.fields,field_description:sale.field_sale_order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_sale_report_id
msgid "ID"
msgstr "ID"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"If a sale order is done, you cannot modify it manually anymore. However, you "
"will still be able to invoice or deliver. This is used to freeze the sale "
"order."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_deposit_account_id
msgid "Income Account"
msgstr "Llogaria e të Ardhurave"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_display_incoterm
msgid "Incoterms"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_invoice
msgid "Invoice"
msgstr "Invoice"

#. module: sale
#: code:addons/sale/sale.py:840
#, python-format
msgid "Invoice %s paid"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_invoice_id
msgid "Invoice Address"
msgstr ""

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr ""

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_line
msgid "Invoice Line"
msgstr "Linja Invoice-it"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_invoice_lines
msgid "Invoice Lines"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Invoice Order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line_invoice_status
msgid "Invoice Status"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_invoiced_target
msgid "Invoice Target"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_partner_invoice_id
msgid "Invoice address for current sales order."
msgstr ""

#. module: sale
#: selection:sale.config.settings,default_invoice_policy:0
msgid "Invoice based on costs (time and material, expenses)"
msgstr ""

#. module: sale
#: selection:sale.config.settings,default_invoice_policy:0
msgid "Invoice delivered quantities"
msgstr ""

#. module: sale
#: selection:sale.config.settings,default_invoice_policy:0
msgid "Invoice ordered quantities"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales team has "
"invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Invoiceable lines"
msgstr ""

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Invoiceable lines (deduct down payments)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_invoiced
msgid "Invoiced"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced Quantity"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_invoiced
msgid "Invoiced This Month"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_crm_team_use_invoices
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"Invoices will be created in draft so that you can update\n"
"                        them before validation."
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template_invoice_policy
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_invoice_policy
msgid "Invoicing Policy"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing address:"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing and shipping address:"
msgstr ""

#. module: sale
#: selection:sale.config.settings,group_sale_delivery_address:0
msgid ""
"Invoicing and shipping addresses are always the same (Example: services "
"companies)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "Invoicing/Progression Ratio"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv___last_update
#: model:ir.model.fields,field_description:sale.field_sale_order___last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_line___last_update
#: model:ir.model.fields,field_description:sale.field_sale_report___last_update
msgid "Last Modified on"
msgstr "Modifikimi i fundit në"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line_write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_write_uid
msgid "Last Updated by"
msgstr "Modifikuar per here te fundit nga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line_write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_write_date
msgid "Last Updated on"
msgstr "Modifikuar per here te fundit me"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_sale_contract
msgid "Manage subscriptions and recurring invoicing"
msgstr ""

#. module: sale
#: selection:product.template,track_service:0
msgid "Manually set quantities on order"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product_track_service
#: model:ir.model.fields,help:sale.field_product_template_track_service
msgid ""
"Manually set quantities on order: Invoice based on the manually entered "
"quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related "
"timesheet.\n"
"Create a task and track hours: Create a task on the sale order validation "
"and track the work hours."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_validity_date
msgid ""
"Manually set the expiration date of your quotation (offer), or it will set "
"the date automatically based on the template if online quotation is "
"installed."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_sale_margin
msgid "Margins"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr ""

#. module: sale
#: selection:sale.config.settings,auto_done_setting:0
msgid "Never allow to modify a confirmed sale order"
msgstr ""

#. module: sale
#: code:addons/sale/sale.py:101
#, python-format
msgid "New"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr ""

#. module: sale
#: selection:sale.config.settings,group_discount_per_so_line:0
msgid "No discount on sales order lines, global discount only"
msgstr ""

#. module: sale
#: selection:sale.config.settings,group_display_incoterm:0
msgid "No incoterm on reports"
msgstr ""

#. module: sale
#: selection:sale.config.settings,group_product_variant:0
msgid "No variants on products"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Note that once a Quotation becomes a Sale Order, it will be moved \n"
"                from the Quotations list to the Sales Order list."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
msgid ""
"Note that once a Quotation becomes a Sale Order, it will be moved from the "
"Quotations list to the Sales Order list."
msgstr ""

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Nothing to Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line_customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid ""
"Once the quotation is confirmed, it becomes a sales order.\n"
"                    You'll be able to invoice it and collect payments.\n"
"                    From the <i>Sales Orders</i> menu, you can track "
"delivery\n"
"                    orders or services."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_website_quote
msgid "Online Quotations"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:26
#, python-format
msgid "Only Integer Value should be valid."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_date_order
msgid "Order Date"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Order Month"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
msgid "Order Number"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_name
msgid "Order Reference"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_state
msgid "Order Status"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Ordered Qty"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Ordered Quantity"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product_invoice_policy
#: model:ir.model.fields,help:sale.field_product_template_invoice_policy
msgid ""
"Ordered Quantity: Invoice based on the quantity the customer ordered.\n"
"Delivered Quantity: Invoiced based on the quantity the vendor delivered.\n"
"Reinvoice Costs: Invoice with some additional charges (product transfer, "
"labour charges,...)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Ordered date of the sales order"
msgstr ""

#. module: sale
#: selection:product.template,invoice_policy:0
msgid "Ordered quantities"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"Orders to upsell are orders having products with an invoicing\n"
"                policy based on <i>ordered quantities</i> for which you "
"have\n"
"                delivered more than what have been ordered."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Information"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_res_partner
#: model:ir.model.fields,field_description:sale.field_sale_report_partner_id
msgid "Partner"
msgstr "Partner"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_country_id
msgid "Partner Country"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Partner's Country"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_payment_term_id
msgid "Payment Term"
msgstr ""

#. module: sale
#: selection:sale.config.settings,module_sale_layout:0
msgid ""
"Personnalize the sale orders and invoice report with separators, page-breaks "
"or subtotals"
msgstr ""

#. module: sale
#: code:addons/sale/sale.py:246
#, python-format
msgid "Please define an accounting sale journal for this company."
msgstr ""

#. module: sale
#: code:addons/sale/sale.py:703
#, python-format
msgid ""
"Please define income account for this product: \"%s\" (id:%d) - or for its "
"category: \"%s\"."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Price"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_reduce
msgid "Price Reduce"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report_pricelist_id
msgid "Pricelist"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_pricelist_id
msgid "Pricelist for current sales order."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Print"
msgstr "Print"

#. module: sale
#: selection:sale.config.settings,module_website_quote:0
msgid "Print quotes or send by email"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_procurement_order
msgid "Procurement"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_procurement_group_id
msgid "Procurement Group"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_procurement_ids
msgid "Procurements"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_id
#: model:ir.model.fields,field_description:sale.field_sale_order_product_id
#: model:ir.model.fields,field_description:sale.field_sale_report_product_id
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Produkti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_sale_report_product_tmpl_id
msgid "Product Template"
msgstr "Shëmbull i Produktit"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_product_variant
msgid "Product Variants"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Products"
msgstr ""

#. module: sale
#: selection:sale.config.settings,group_product_variant:0
msgid ""
"Products can have several attributes, defining variants (Example: size, "
"color,...)"
msgstr ""

#. module: sale
#: selection:sale.config.settings,group_uom:0
msgid "Products have only one unit of measure (easier)"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_mrp_properties
msgid "Properties on lines"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_delivered
msgid "Qty Delivered"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_invoiced
msgid "Qty Invoiced"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_to_invoice
msgid "Qty To Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quantity"
msgstr "Sasia"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
#: selection:sale.order,state:0
msgid "Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr ""

#. module: sale
#: model:ir.actions.report.xml,name:sale.report_sale_order
msgid "Quotation / Order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation Date:"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotation Number"
msgstr ""

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation Send"
msgstr ""

#. module: sale
#: selection:sale.order,state:0 selection:sale.report,state:0
msgid "Quotation Sent"
msgstr ""

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr ""

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
msgid "Quotation sent"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.model.fields,field_description:sale.field_crm_team_use_quotations
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Quotations"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Quotations & Sales"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Quotations Sent"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_origin
msgid "Reference of the document that generated this sales order request."
msgstr ""

#. module: sale
#: selection:product.template,invoice_policy:0
msgid "Reinvoice Costs"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Reporting"
msgstr ""

#. module: sale
#: selection:sale.order,state:0
msgid "Sale Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line_so_line
#: model:ir.model.fields,field_description:sale.field_procurement_order_sale_line_id
msgid "Sale Order Line"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_product_sale_list
#: model:ir.model.fields,field_description:sale.field_account_invoice_line_sale_line_ids
msgid "Sale Order Lines"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_auto_done_setting
msgid "Sale Order Modification"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Sale Price"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_sale_layout
msgid "Sale Reports Layout"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_report_product_all
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_company_inherit_form2
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales"
msgstr "Shitjet"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales Analysis"
msgstr ""

#. module: sale
#: selection:sale.report,state:0
msgid "Sales Done"
msgstr ""

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_sales_funnel
msgid "Sales Funnel"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales Information"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner_sale_order_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model:res.request.link,name:sale.req_link_sale_order
#: selection:sale.report,state:0
msgid "Sales Order"
msgstr ""

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Sales Order that haven't yet been confirmed"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Orders Statistics"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_invoice_report_team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_team_id
#: model:ir.model.fields,field_description:sale.field_sale_order_team_id
#: model:ir.model.fields,field_description:sale.field_sale_report_team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Sales to Invoice"
msgstr ""

#. module: sale
#: selection:sale.config.settings,module_sale_margin:0
msgid "Salespeople do not need to view margins when quoting"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_order_user_id
#: model:ir.model.fields,field_description:sale.field_sale_report_user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_module_website_sale_digital
msgid ""
"Sell digital products - provide downloadable content on your customer portal"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr ""

#. module: sale
#: selection:sale.config.settings,module_website_quote:0
msgid "Send online quotations based on templates (advanced)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_sequence
msgid "Sequence"
msgstr "Sekuencë"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:19
#, python-format
msgid "Set an invoicing target: "
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Done"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Setup default terms and conditions in your company settings."
msgstr ""

#. module: sale
#: selection:sale.config.settings,group_display_incoterm:0
msgid "Show incoterms on sale orders and invoices"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_product_pricelist
msgid "Show pricelists On Products"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_pricelist_item
msgid "Show pricelists to customers"
msgstr ""

#. module: sale
#: selection:sale.config.settings,group_uom:0
msgid ""
"Some products may be sold/purchased in different units of measure (advanced)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_origin
msgid "Source Document"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_state
#: model:ir.model.fields,field_description:sale.field_sale_report_state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Statusi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_config
msgid "Subscriptions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_subtotal
msgid "Subtotal"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_invoiced_target
msgid ""
"Target of invoice revenue for the current month. This is the amount the "
"sales team estimates to be able to invoice this month."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line_tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_deposit_taxes_id
msgid "Taxes used for deposits"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_note
msgid "Terms and conditions"
msgstr ""

#. module: sale
#: code:addons/sale/sale_analytic.py:56
#, python-format
msgid ""
"The Sale Order %s linked to the Analytic Account must be validated before "
"registering expenses."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_amount
msgid "The amount to be invoiced in advance, taxes excluded."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_project_id
msgid "The analytic account related to a sales order."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_display_incoterm
msgid ""
"The printed reports will display the incoterms for the sale orders and the "
"related invoices"
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:137
#, python-format
msgid ""
"The product used to invoice a down payment should be of type 'Service'. "
"Please use another product or update this product."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:135
#, python-format
msgid ""
"The product used to invoice a down payment should have an invoice policy set "
"to \"Ordered quantities\". Please update your deposit product to be able to "
"create a deposit invoice."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:77
#, python-format
msgid "The value of the down payment amount must be positive."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:73
#, python-format
msgid ""
"There is no income account defined for this product: \"%s\". You may have to "
"install a chart of account from Accounting app, settings menu."
msgstr ""

#. module: sale
#: code:addons/sale/sale.py:330 code:addons/sale/sale.py:334
#, python-format
msgid "There is no invoicable line."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "This Year"
msgstr ""

#. module: sale
#: model:web.tip,description:sale.sale_tip_1
msgid ""
"This progress bar shows the stages your quotation will go through.\n"
"                Use buttons on the left to move forward to the next stages."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman, "
"partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""

#. module: sale
#: selection:product.template,track_service:0
msgid "Timesheets on project"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_to_invoice
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "To Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_total
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_price_total
msgid "Total Price"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Included"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_track_service
#: model:ir.model.fields,field_description:sale.field_product_template_track_service
msgid "Track Service"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Unit Price"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report_product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_uom
msgid "Units of Measure"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Unread Messages"
msgstr "Mesazhe të Palexuara"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_untaxed
msgid "Untaxed Amount"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_price_subtotal
msgid "Untaxed Total Price"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "Upselling"
msgstr ""

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Upselling Opportunity"
msgstr ""

#. module: sale
#: model:web.tip,description:sale.sale_tip_2
msgid ""
"Use pivot and graph views to analyze your sales pipeline.\n"
"                Select measures, filter and group dimensions to get the "
"perfect report according to your needs."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_group_sale_pricelist
msgid "Use pricelists to adapt your price per customers"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "VAT:"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_volume
msgid "Volume"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_advance_payment_method
msgid "What do you want to invoice?"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_config_settings_group_product_variant
msgid ""
"Work with product variant allows you to define some variant of the same "
"products, an ease the product management in the ecommerce for example"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:26
#, python-format
msgid "Wrong value entered!"
msgstr ""

#. module: sale
#: code:addons/sale/sale.py:163
#, python-format
msgid ""
"You can not delete a sent quotation or a sales order! Try to cancel it "
"before."
msgstr ""

#. module: sale
#: code:addons/sale/sale.py:797
#, python-format
msgid ""
"You can not remove a sale order line.\n"
"Discard changes and try setting the quantity to 0."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch, or check\n"
"                every order and invoice them one by one."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "You will find here all orders that are ready to be invoiced."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Your next actions should flow efficiently: confirm the Quotation \n"
"                to a Sale Order, then create the Invoice and collect the "
"Payment."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
msgid ""
"Your next actions should flow efficiently: confirm the Quotation to a Sale "
"Order, then create the Invoice and collect the Payment."
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "account analytic line"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_config_settings
msgid "sale.config.settings"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_config_settings_sale_pricelist_setting
msgid "unknown"
msgstr ""

#~ msgid "Action Needed"
#~ msgstr "Veprimet e nevojshme"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Data e mesazhit të fundit të postuar në regjistër"

#~ msgid "Followers"
#~ msgstr "Ndjekesit"

#~ msgid "Followers (Channels)"
#~ msgstr "Ndjekesit (Kanalet)"

#~ msgid "Followers (Partners)"
#~ msgstr "Ndjekesit (Partnerët)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Nëqoftëse është e çekuar mesazhet e reja kërkojnë vëmëndjen tuaj."

#~ msgid "If checked, new messages require your attention."
#~ msgstr ""
#~ "Nëqoftëse është e çekuar,\n"
#~ " mesazhet e reja kërkojnë vëmëndjen tuaj."

#~ msgid "Is Follower"
#~ msgstr "Eshte Ndjekës"

#~ msgid "Last Message Date"
#~ msgstr "Data e Mesazhit të Fundit"

#~ msgid "Messages"
#~ msgstr "Mesazhet"

#~ msgid "Messages and communication history"
#~ msgstr "Historiku i mesazheve dhe komunikimeve"

#~ msgid "Number of Actions"
#~ msgstr "Numri i Veprimeve"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Numri i mesazheve që kërkojnë një veprim"

#~ msgid "Number of unread messages"
#~ msgstr "Numri i mesazheve të palexuara"

#~ msgid "Unread Messages Counter"
#~ msgstr "Numëruesi i Mesazheve të Palexuara"

#~ msgid "Website Messages"
#~ msgstr "Mesazhe të Website-it"

#~ msgid "Website communication history"
#~ msgstr "Historiku i Komunikimeve të Website-it"
