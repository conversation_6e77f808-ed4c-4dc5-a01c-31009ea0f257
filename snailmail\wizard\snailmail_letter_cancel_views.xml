<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="snailmail_letter_cancel" model="ir.ui.view">
        <field name="name">snailmail.letter.cancel.form</field>
        <field name="model">snailmail.letter.cancel</field>
        <field name="groups_id" eval="[(4,ref('base.group_user'))]"/>
        <field name="arch" type="xml">
            <form string="Cancel notification in failure">
                <field name="model" invisible='1'/>
                <field name="help_message"/>
                <p>If you want to re-send them, click Cancel now, then click on the notification and review them one by one by clicking on the red paper-plane next to each message.</p>
                <footer>  
                    <button string="Discard delivery failures" name="cancel_resend_action" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z" />
                </footer>
            </form>
        </field>
    </record>

    <record id="snailmail_letter_cancel_action" model="ir.actions.act_window">
        <field name="name">Discard snailmail delivery failures</field>
        <field name="res_model">snailmail.letter.cancel</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
