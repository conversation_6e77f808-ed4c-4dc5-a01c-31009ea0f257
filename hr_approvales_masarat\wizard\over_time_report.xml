<?xml version="1.0"?>
<odoo>
    <record id="hr_over_time_report_form" model="ir.ui.view">
        <field name="name">hr.over.time.report.wizard</field>
        <field name="model">hr.over.time.report</field>
        <field name="arch" type="xml">
            <form string="تقرير طلبات العمل الإضافي">

                <group>
                    <group>
                        <field name="date_from"/>
                        <field name="date_to"/>
                    </group>
                </group>
                <group>
                    <field name="only_approved_by_hr"/>
                </group>
                <footer>
                    <button name="get_report" string="طباعة" type="object" class="btn-primary"/>
                    <button string="الغاء الامر" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_hr_over_time_report_view" model="ir.actions.act_window">
        <field name="name">طلبات العمل الإضافي</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.over.time.report</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_over_time_report_form"/>
        <field name="target">new</field>
    </record>


    <menuitem
            id="menu_masarat_approvale_report"
            name="تقارير"
            parent="hr_masarat_approvals"
            sequence="300"/>
    <menuitem
            id="menu_masarat_overtime_report"
            name="طلبات الإضافي"
            parent="menu_masarat_approvale_report"
            action="action_hr_over_time_report_view"
            sequence="1"/>


    <template id="hr_masarat_over_time_report">
        <t t-call="web.html_container">
            <t t-call="web.basic_layout">
                <link rel="stylesheet" href="/hr_approvales_masarat/static/src/css/style.css"/>
                <div class="page" dir="rtl">
                    <h3 style="text-align: center;">طلبات الإضافي</h3>
                    <ul class="nav" style="display: flex; justify-content: center;">
                        <li>
                            <h5 style="text-align: center;">للفترة من :
                                <t t-esc="date_from"/>
                            </h5>
                        </li>
                        <li>
                            <h5 style="text-align: center;">الى :
                                <t t-esc="date_to"/>
                            </h5>
                        </li>
                    </ul>
                    <table class="table styled-table">
                        <thead>
                            <tr>
                                <td style="text-align: center;" rowspan="2">الموظف</td>
                                <td style="text-align: center;" colspan="2">من المنزل</td>
                                <td style="text-align: center;" colspan="2">من العمل</td>
                                <td style="text-align: center;" colspan="2">في العطل</td>
                                <td style="text-align: center;" rowspan="2">المجموع</td>
                            </tr>
                            <tr>
                                <td style="text-align: center;">الفعلي</td>
                                <td style="text-align: center;">المحتسب(*1)</td>
                                <td style="text-align: center;">الفعلي</td>
                                <td style="text-align: center;">المحتسب(*2)</td>
                                <td style="text-align: center;">الفعلي</td>
                                <td style="text-align: center;">المحتسب(*3)</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr t-foreach="employee_list" t-as="employee">
                                <td style="text-align: center;">
                                    <t t-esc="employee['name']"/>
                                </td>
                                <td style="text-align: center;">
                                    <t t-esc="employee['at_home']"/>
                                </td>
                                <td style="text-align: center;">
                                    <t t-esc="employee['calc_at_home']"/>
                                </td>
                                <td style="text-align: center;">
                                    <t t-esc="employee['at_work']"/>
                                </td>
                                <td style="text-align: center;">
                                    <t t-esc="employee['calc_at_work']"/>
                                </td>
                                <td style="text-align: center;">
                                    <t t-esc="employee['holidays']"/>
                                </td>
                                <td style="text-align: center;">
                                    <t t-esc="employee['calc_holidays']"/>
                                </td>
                                <td style="text-align: center;">
                                    <t t-esc="employee['total_calc']"/>
                                </td>
                            </tr>
                        </tbody>

                    </table>
                </div>
            </t>
        </t>
    </template>

    <report
            id="hr_masarat_over_time_report_x1"
            string="طلبات الإضافي"
            model="hr.over.time.report"
            report_type="qweb-pdf"
            name="hr_approvales_masarat.hr_masarat_over_time_report"
            file="hr_approvales_masarat.hr_masarat_over_time_report"
    />


</odoo>