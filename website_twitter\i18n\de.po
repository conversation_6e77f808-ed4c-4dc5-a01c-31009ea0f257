# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_twitter
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>Nesselbosch, 2021
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                Show me how to obtain the Twitter API key and Twitter API secret"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                Wie bekomme ich meinen Twitter API key und mein Twitter API secret"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Twitter Roller</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Twitter Roller</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Callback URL: </strong>Leave blank"
msgstr "<strong>Callback URL: </strong>leer lassen"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Description: </strong> Odoo Twitter Integration"
msgstr "<strong>Beschreibung: </strong> Odoo-Twitter-Integration"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Name: </strong> Odoo Twitter Integration"
msgstr "<strong>Name: </strong> Odoo-Twitter-Integration"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Website: </strong>"
msgstr "<strong>Website: </strong>"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_key
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API Key"
msgstr "API-Schlüssel"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_secret
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API secret"
msgstr "API-Geheimnis"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"Accept terms of use and click on the Create your Twitter application button "
"at the bottom"
msgstr ""
"Akzeptieren Sie die Nutzungsbedingungen und klicken Sie auf den Knopf "
"„Create your Twitter application“ unten"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Authentication credentials were missing or incorrect. Maybe screen name "
"tweets are protected."
msgstr ""
"Zugangsdaten zur Authentifizierung fehlen oder falsch. Möglicherweise sind "
"Tweets mit Bildschirmnamen geschützt."

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"Copy/Paste Consumer Key (API Key) and Consumer Secret (API Secret) keys "
"below."
msgstr ""
"Kopieren Sie den Anwendungs-Schlüssel (API key) und das geteilte Geheimnis "
"(API secret) und fügen Sie sie hier ein."

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Create a new Twitter application on"
msgstr "Erstellen Sie eine neue Twitter Anwendung"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_screen_name
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Favorites From"
msgstr "Favoriten von"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_screen_name
msgid "Get favorites from this screen name"
msgstr "Favoriten von diesem Benutzernamen erhalten"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "HTTP Error: Something is misconfigured"
msgstr "HTTP-Fehler: Etwas ist fehlerhaft konfiguriert."

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "How to configure the Twitter API access"
msgstr "Zugriff auf Twitter-API konfigurieren"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__id
msgid "ID"
msgstr "ID"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Internet connection refused: We failed to reach a twitter server."
msgstr ""
"Internetverbindung verweigert: Wir konnten keinen Twitter-Server erreichen."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid ""
"Please set a Twitter screen name to load favorites from, in the Website "
"Settings (it does not have to be yours)"
msgstr ""
"Bitte tragen Sie einen Twitter Benutzernamen in den Website-Einstellungen "
"ein, um dessen Favoriten zu laden (muss nicht zwingend der eigene sein)"

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid "Please set the Twitter API Key and Secret in the Website Settings."
msgstr ""
"Bitte tragen Sie Twitter-API-Schlüssel und -Geheimnis in den Website-"
"Einstellungen ein."

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/js/website.twitter.editor.js:0
#, python-format
msgid "Reload"
msgstr "Erneut laden"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Request cannot be served due to the applications rate limit having been "
"exhausted for the resource."
msgstr ""
"Anfrage kann nicht angenommen werden, da die Anwendungsratenbegrenzung für "
"diese Ressource erschöpft ist."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__screen_name
msgid "Screen Name"
msgstr "Benutzername"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_screen_name
msgid ""
"Screen Name of the Twitter Account from which you want to load favorites.It "
"does not have to match the API Key/Secret."
msgstr ""
"Benutzername des Twitter-Kontos für den die Favoriten geladen werden. Dieser"
" muss nicht zwingend mit API-Schlüssel/Geheimnis übereinstimmen."

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Switch to the API Keys tab: <br/>"
msgstr "Wechseln Sie zum Reiter API keys: <br/>"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The Twitter servers are up, but overloaded with requests. Try again later."
msgstr ""
"Die Twitter-Server laufen, sind aber mit Anfragen überlastet. Versuchen Sie "
"es später erneut."

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The Twitter servers are up, but the request could not be serviced due to "
"some failure within our stack. Try again later."
msgstr ""
"Die Twitter-Server laufen, die Anfrage konnte jedoch aufgrund eines Fehlers "
"unseres Stacks nicht verarbeitet werden. Versuchen Sie es später erneut."

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The request is understood, but it has been refused or access is not allowed."
" Please check your Twitter API Key and Secret."
msgstr ""
"Die Anfrage wurde verstanden, jedoch abgelehnt oder hat keine "
"Zugriffsberechtigung. Bitte überprüfen Sie Twitter-API-Schlüssel und "
"-Geheimnis."

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The request was invalid or cannot be otherwise served. Requests without "
"authentication are considered invalid and will yield this response."
msgstr ""
"Die Anfrage war ungültig oder kann anderweitig nicht verarbeitet werden. "
"Anfragen ohne Authentifizierung werden als ungültig betrachtet und erhalten "
"daher diese Antwort."

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "There was no new data to return."
msgstr "Keine neuen Daten vorhanden."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet_id
msgid "Tweet ID"
msgstr "Tweet-ID"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet
msgid "Tweets"
msgstr "Tweets"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter API Credentials"
msgstr "Twitter-API-Anmededaten"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_key
msgid "Twitter API Key"
msgstr "API-Schlüssel von Twitter"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_secret
msgid "Twitter API Secret"
msgstr "Twitter-API-Geheimnis"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_key
msgid "Twitter API key"
msgstr "Twitter-API-Schlüssel"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_key
msgid "Twitter API key you can get it from https://apps.twitter.com/"
msgstr ""
"Der Twitter-API-Schlüssel kann auf der Seite https://apps.twitter.com/ "
"abgerufen werden."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_secret
msgid "Twitter API secret"
msgstr "Twitter-API-Geheimnis"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_secret
msgid "Twitter API secret you can get it from https://apps.twitter.com/"
msgstr ""
"Das Twitter-API-Geheimnis kann auf der Seite https://apps.twitter.com/ "
"abgerufen werden."

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter Configuration"
msgstr "Twitter-Konfiguration"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Twitter authorization error! Please double-check your Twitter API Key and "
"Secret!"
msgstr ""
"Twitter-Autorisierungsfehler! Bitte überprüfen Sie Twitter-API-Schlüssel und"
" -Geheimnis!"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Twitter is down or being upgraded."
msgstr "Twitter ist down oder wird aktualisiert."

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Twitter seems broken. Please retry later. You may consider posting an issue "
"on Twitter forums to get help."
msgstr ""
"Twitter scheint nicht zu funktionieren. Bitte versuchen Sie es später "
"erneut. Schreiben Sie ggf. einen Beitrag in den Twitter-Foren, um Hilfe zu "
"erhalten."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_server_uri
msgid "Twitter server uri"
msgstr "Twitter-Server-URI"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter tutorial"
msgstr "Twitter-Anleitung"

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid ""
"Twitter user @%(username)s has less than 12 favorite tweets. Please add more"
" or choose a different screen name."
msgstr ""
"Der Twitter-Benutzer @%(username)s hat weniger als 12 favorisierte Tweets. "
"Bitte ergänzen Sie weitere Favoriten oder wählen Sie einen anderen "
"Benutzernamen."

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter's user"
msgstr "Twitter-Benutzer"

#. module: website_twitter
#: model:ir.actions.server,name:website_twitter.ir_cron_twitter_actions_ir_actions_server
#: model:ir.cron,cron_name:website_twitter.ir_cron_twitter_actions
#: model:ir.cron,name:website_twitter.ir_cron_twitter_actions
msgid "Twitter: Fetch new favorites"
msgstr "Twitter: Neue Favoriten abrufen"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__website_id
msgid "Website"
msgstr "Website"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website_twitter_tweet
msgid "Website Twitter"
msgstr "Website Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://apps.twitter.com/app/new"
msgstr "https://apps.twitter.com/app/new"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "with the following values:"
msgstr "mit den folgenden Werten:"
