<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- add picking to batch  -->
    <record id="stock_picking_to_batch_form" model="ir.ui.view">
        <field name="name">stock.picking.to.batch.form</field>
        <field name="model">stock.picking.to.batch</field>
        <field name="arch" type="xml">
            <form string="Add pickings to">
                <group>
                    <group>
                        <label for="mode" string="Add to"/>
                        <field name="mode" widget="radio" nolabel="1"/>
                        <field name="batch_id" options="{'no_create_edit': True, 'no_open': True}" attrs="{'invisible': [('mode', '=', 'new')], 'required': [('mode', '=', 'existing')]}"/>
                        <field name="user_id" options="{'no_create_edit': True}" attrs="{'invisible': [('mode', '=', 'existing')]}"/>
                    </group>
                </group>

                <footer>
                    <button name="attach_pickings" type="object" string="Confirm" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="stock_picking_to_batch_action_stock_picking" model="ir.actions.act_window">
        <field name="name">Add to batch</field>
        <field name="res_model">stock.picking.to.batch</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="binding_model_id" ref="model_stock_picking"/>
        <field name="binding_view_types">list</field>
    </record>

</odoo>
