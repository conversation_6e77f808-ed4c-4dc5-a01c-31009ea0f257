# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock
# 
# Translators:
# <PERSON> <amunif<PERSON><EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON>to The <<EMAIL>>, 2022
# <PERSON>, 2022
# whenwesober, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# Abe <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_payment
msgid "<strong>Warning!</strong>"
msgstr "<strong>Peringatan!</strong>"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__cart_qty
msgid "Cart Qty"
msgstr ""

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Continue Selling"
msgstr "Lanjutkan Penjualan"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__allow_out_of_stock_order
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__allow_out_of_stock_order
msgid "Continue selling when out-of-stock"
msgstr "Lanjutkan menjual saat habis persediaan"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid ""
"Default availability mode set on newly created storable products. This can "
"be changed at the product level."
msgstr ""
"Mode ketersediaan standar ditetapkan pada produk yang bisa disimpan yang "
"baru saja dibuat. Ini dapat diganti pada level produk."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Default visibility for custom messages."
msgstr "Visibilitas standar untuk pesan-pesan kustom."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Inventory"
msgstr "Stok Persediaan"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Only"
msgstr "Untuk tujuan internal saja"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "Out of Stock"
msgstr "Kehabisan Persediaan"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Out-of-Stock"
msgstr "Persediaan-Habis"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__out_of_stock_message
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__out_of_stock_message
msgid "Out-of-Stock Message"
msgstr "Pesan Persediaan-Habis"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_product
msgid "Product"
msgstr "Produk"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_product_template
msgid "Product Template"
msgstr "Templete Produk"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order
msgid "Sales Order"
msgstr "Order Penjualan"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Detail Order Penjualan"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Show Available Qty"
msgstr "Tunjukkan Kuantitas yang Tersedia"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__available_threshold
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__available_threshold
msgid "Show Threshold"
msgstr "Tunjukkan Ambang Bata"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_product_product__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_product_template__show_availability
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__show_availability
msgid "Show availability Qty"
msgstr "Tunjukkan ketersediaan Kuantitas"

#. module: website_sale_stock
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Some products became unavailable and your cart has been updated. We're sorry"
" for the inconvenience."
msgstr ""
"Beberapa produk sudah tidak tersedia dan keranjang Anda telah diperbarui. "
"Mohon maaf untuk ketidaknyamanan tersebut."

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Units"
msgstr "Unit"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_res_config_settings__website_warehouse_id
#: model:ir.model.fields,field_description:website_sale_stock.field_website__warehouse_id
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "Warehouse"
msgstr "Gudang"

#. module: website_sale_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_sale_order__warning_stock
#: model:ir.model.fields,field_description:website_sale_stock.field_sale_order_line__warning_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale_stock.website_sale_stock_payment
msgid "Warning"
msgstr "Peringatan"

#. module: website_sale_stock
#: model:ir.model,name:website_sale_stock.model_website
#: model:ir.model.fields,field_description:website_sale_stock.field_stock_picking__website_id
msgid "Website"
msgstr "Website"

#. module: website_sale_stock
#: model:ir.model.fields,help:website_sale_stock.field_stock_picking__website_id
msgid "Website this picking belongs to."
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "You already added"
msgstr "Anda sudah menambahkan"

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "You already added all the available product in your cart."
msgstr "Anda sudah menambahkan semua produk yang tersedia ke keranjang Anda."

#. module: website_sale_stock
#: code:addons/website_sale_stock/controllers/main.py:0
#, python-format
msgid ""
"You ask for %(quantity)s products but only %(available_qty)s is available"
msgstr ""

#. module: website_sale_stock
#: code:addons/website_sale_stock/models/sale_order.py:0
#, python-format
msgid "You ask for %s products but only %s is available"
msgstr ""

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "in your cart."
msgstr "di keranjang Anda."

#. module: website_sale_stock
#. openerp-web
#: code:addons/website_sale_stock/static/src/xml/website_sale_stock_product_availability.xml:0
#, python-format
msgid "left in stock."
msgstr "tersisa di stok."

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.product_template_form_view_inherit_website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.res_config_settings_view_form
msgid "only if below"
msgstr "hanya bila di bawah"
