# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * analytic
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Galician (https://www.transifex.com/odoo/teams/41243/gl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_currency_id
msgid "Account <PERSON><PERSON>rency"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_active
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag_active
msgid "Active"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Amount"
msgstr ""

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_account
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_account_id
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Analytic Account"
msgstr "Conta analítica"

#. module: analytic
#: model:res.groups,name:analytic.group_analytic_accounting
msgid "Analytic Accounting"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Analytic Accounts"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_tag_action
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_tree_view
msgid "Analytic Accounts Tags"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action_entries
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_graph
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_pivot
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Analytic Entries"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Analytic Entry"
msgstr ""

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_line
msgid "Analytic Line"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_line_ids
msgid "Analytic Lines"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag_name
msgid "Analytic Tag"
msgstr ""

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_tag
msgid "Analytic Tags"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Archived"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Associated Partner"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_balance
msgid "Balance"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_kanban
msgid "Balance:"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_account_form
msgid "Chart of Analytic Accounts"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_tag_action
msgid "Click to add a new tag."
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.action_analytic_account_form
msgid "Click to add an analytic account."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag_color
msgid "Color Index"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_company_id
msgid "Company"
msgstr "Compañía"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "Cost/Revenue"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action
msgid "Costs & Revenues"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Costs will be created automatically when you register supplier\n"
"                invoices, expenses or timesheets."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag_create_date
msgid "Created on"
msgstr "Creado o"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_credit
msgid "Credit"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_currency_id
msgid "Currency"
msgstr "Moeda"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_partner_id
msgid "Customer"
msgstr "Cliente"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_date
msgid "Date"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_debit
msgid "Debit"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_name
msgid "Description"
msgstr "Descrición"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag_display_name
msgid "Display Name"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Group By..."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag_id
msgid "ID"
msgstr "ID"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account_active
msgid ""
"If the active field is set to False, it will allow you to hide the account "
"without removing it."
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"In Odoo, sales orders and projects are implemented using\n"
"                analytic accounts. You can track costs and revenues to analyse\n"
"                your margins easily."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account___last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line___last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag___last_update
msgid "Last Modified on"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
msgid "No activity yet on this account."
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid "No activity yet."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_partner_id
msgid "Partner"
msgstr "Empresa"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_unit_amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Quantity"
msgstr "Cantidade"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_code
msgid "Reference"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Revenues will be created automatically when you create customer\n"
"                invoices. Customer invoices can be created based on sales orders\n"
"                (fixed price invoices), on timesheets (based on the work done) or\n"
"                on expenses (e.g. reinvoicing of travel costs)."
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Search Analytic Lines"
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_tag_active
msgid "Set active to false to hide the Analytic Tag without removing it."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account_tag_ids
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_tag_ids
msgid "Tags"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Tasks Month"
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_line_currency_id
msgid "The related account currency if not equal to the company one."
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Total"
msgstr "Total"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line_user_id
msgid "User"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "e.g. Project XYZ"
msgstr ""
