# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_automation
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>y BQ <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Vo Than<PERSON> Thuy, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: Vo Thanh Thuy, 2022\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"\"\n"
"                (ID:"
msgstr ""
"\"\n"
"                (ID:"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__help
msgid "Action Description"
msgstr "Mô tả tác vụ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__name
msgid "Action Name"
msgstr "Tên tác vụ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__state
msgid "Action To Do"
msgstr "Tác vụ cần làm"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__type
msgid "Action Type"
msgstr "Kiểu tác vụ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_type_id
msgid "Activity"
msgstr "Hoạt động"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_type
msgid "Activity User Type"
msgstr "Kiểu người dùng hoạt động"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__partner_ids
msgid "Add Followers"
msgstr "Thêm người theo dõi"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_domain
msgid "Apply on"
msgstr "Áp dụng cho"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_search
msgid "Archived"
msgstr "Đã lưu"

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__ir_actions_server__usage__base_automation
msgid "Automated Action"
msgstr "Tác vụ tự động"

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
msgid "Automated Actions"
msgstr "Tác vụ tự động"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation"
msgstr "Tự động hoá"

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
#: model:ir.cron,cron_name:base_automation.ir_cron_data_base_automation_check
#: model:ir.cron,name:base_automation.ir_cron_data_base_automation_check
msgid "Base Action Rule: check and execute"
msgstr "Quy tắc tác vụ cơ sở: kiểm tra và thực thi"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_change
msgid "Based on Form Modification"
msgstr "Dựa trên sửa đổi biểu mẫu"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time
msgid "Based on Timed Condition"
msgstr "Dựa trên điều kiện thời gian"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_pre_domain
msgid "Before Update Domain"
msgstr "Trước khi cập nhật miền"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_model_id
msgid "Binding Model"
msgstr "Mô hình gắn kết "

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_type
msgid "Binding Type"
msgstr "Loại gắn kết"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_view_types
msgid "Binding View Types"
msgstr "Kiểu xem gắn kết "

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__child_ids
msgid "Child Actions"
msgstr "Tác vụ con"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__child_ids
msgid ""
"Child server actions that will be executed. Note that the last return "
"returned action value will be used as global return value."
msgstr ""
"Tác vụ máy chủ con sẽ không được thực thi. Lưu ý rằng giá trị tác vụ được "
"trả lại cuối cùng sẽ được sử dụng làm giá trị trả lại toàn cục. "

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_date
msgid "Created on"
msgstr "Thời điểm tạo"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__day
msgid "Days"
msgstr "Ngày"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_range
msgid ""
"Delay after the trigger date.\n"
"                                    You can put a negative number if you need a delay before the\n"
"                                    trigger date, like sending a reminder 15 minutes before a meeting."
msgstr ""
"Trì hoãn sau ngày kích hoạt.\n"
"                                     Bạn có thể đặt số âm nếu bạn cần trì hoãn trước\n"
"                                     ngày kích hoạt, như gửi lời nhắc 15 phút trước cuộc họp."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range
msgid "Delay after trigger date"
msgstr "Trì hoãn sau ngày kích hoạt"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range_type
msgid "Delay type"
msgstr "Dạng trì hoãn"

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "Disable Action"
msgstr "Tắt tác vụ"

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"Disabling this automated action will enable you to continue your workflow\n"
"                but any data created after this could potentially be corrupted,\n"
"                as you are effectively disabling a customization that may set\n"
"                important and/or required fields."
msgstr ""
"Tắt tác vụ tự động hóa này sẽ cho bạn tiếp tục luồng công việc\n"
"                nhưng bất kỳ dữ liệu nào được tạo sau đây cũng có thể bị hỏng,\n"
"                vì bạn đã tắt một tùy chỉnh có thể đã đặt \n"
"                trường quan trọng và/hoặc trường bắt buộc."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range
msgid "Due Date In"
msgstr "Hạn chót"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range_type
msgid "Due type"
msgstr "Loại thời hạn"

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "Edit action"
msgstr "Sửa tác vụ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__template_id
msgid "Email Template"
msgstr "Mẫu email"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__xml_id
msgid "External ID"
msgstr "ID ngoài"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr "Các trường kích hoạt sự thay đổi."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__groups_id
msgid "Groups"
msgstr "Nhóm"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__hour
msgid "Hours"
msgstr "Giờ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__id
msgid "ID"
msgstr "ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "ID của tác vụ nếu được xác định trong tệp XML"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the action "
"rule."
msgstr ""
"Nếu có, điều kiện này phải được thỏa mãn trước khi thực hiện quy tắc tác vụ."
" "

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record."
msgstr "Nếu có, điều kiện này phải được thỏa mãn trước khi cập nhật hồ sơ."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__last_run
msgid "Last Run"
msgstr "Chạy lần cuối"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__least_delay_msg
msgid "Least Delay Msg"
msgstr "Tin nhắn ít trễ nhất"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__link_field_id
msgid "Link Field"
msgstr "Trường liên kết"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__minutes
msgid "Minutes"
msgstr "Phút"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_id
msgid "Model"
msgstr "Mô hình"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_name
msgid "Model Name"
msgstr "Tên mô hình"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__crud_model_id
msgid ""
"Model for record creation / update. Set this field only to specify a "
"different model than the base model."
msgstr ""
"Mô hình để tạo / cập nhật hồ sơ. Chỉ đặt trường này để chỉ định mô hình khác"
" với mô hình cơ sở. "

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__model_id
msgid "Model on which the server action runs."
msgstr "Mô hình chạy tác vụ máy chủ. "

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__month
msgid "Months"
msgstr "Tháng"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_note
msgid "Note"
msgstr "Ghi chú"

#. module: base_automation
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Note that this action can be trigged up to %d minutes after its schedule."
msgstr ""
"Lưu ý là tác vụ này có thể được kích hoạt lên tới %d phút sau lịch trình."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "Kích hoạt trường thay đổi"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create
msgid "On Creation"
msgstr "Khi tạo"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create_or_write
msgid "On Creation & Update"
msgstr "Khi tạo & Cập nhật"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unlink
msgid "On Deletion"
msgstr "Khi xóa"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_write
msgid "On Update"
msgstr "Khi cập nhật"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__help
msgid ""
"Optional help text for the users with a description of the target view, such"
" as its usage and purpose."
msgstr ""
"Văn bản trợ giúp tùy chọn cho người dùng có mô tả về chế độ xem mục tiêu, "
"chẳng hạn như cách sử dụng và mục đích của chế độ xem."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__link_field_id
msgid ""
"Provide the field used to link the newly created record on the record used "
"by the server action."
msgstr ""
"Cung cấp trường được sử dụng để liên kết hồ sơ mới được tạo với hồ sơ được "
"sử dụng bởi tác vụ máy chủ. "

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__code
msgid "Python Code"
msgstr "Mã Python"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_id
msgid "Responsible"
msgstr "Phụ trách"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: base_automation
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "Server Action"
msgstr "Tác vụ máy chủ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__action_server_id
msgid "Server Actions"
msgstr "Tác vụ máy chủ"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__binding_model_id
msgid ""
"Setting a value makes this action available in the sidebar for the given "
"model."
msgstr ""
"Đặt giá trị làm cho tác vụ này có sẵn trong thanh bên cho mô hình đã cho."

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Setup a new automated automation"
msgstr "Thiết lập tự động hóa mới"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_summary
msgid "Summary"
msgstr "Tóm tắt"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_id
msgid "Target Model"
msgstr "Mô hình mục tiêu"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_name
msgid "Target Model Name"
msgstr "Tên mô hình mục tiêu"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr "Tên kĩ thuật của người dùng trong hồ sơ"

#. module: base_automation
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"The \"%(trigger_value)s\" %(trigger_label)s can only be used with the "
"\"%(state_value)s\" action type"
msgstr ""
"\"%(trigger_value)s\" %(trigger_label)s chỉ có thể được dùng với loại tác vụ"
" \"%(state_value)s\" "

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trigger_field_ids
msgid ""
"The action will be triggered if and only if one of these fields is "
"updated.If empty, all fields are watched."
msgstr ""
"Tác vụ sẽ được kích hoạt nếu và chỉ nếu một trong các trường này được cập "
"nhật. Nếu để trống, mọi trường sẽ được theo dõi. "

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"The error occurred during the execution of the automated action\n"
"                \""
msgstr ""
"Lỗi đã xuất hiện trong quá trình thực thi tác vụ tự động hóa\n"
"                \""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger
msgid "Trigger"
msgstr "Kích hoạt"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_id
msgid "Trigger Date"
msgstr "Ngày kích hoạt"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger_field_ids
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Trigger Fields"
msgstr "Trường kích hoạt"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""
"Loại tác vụ của máy chủ. Dưới đây là các giá trị hiện có:\n"
"- 'Thực thi mã python': thực thi một đoạn mã python\n"
"- 'Tạo': tạo hồ sơ mới với giá trị mới\n"
"- 'Cập nhật dữ liệu': cập nhật dữ liệu\n"
"- 'Thực thi các tác vụ khác': quy định một tác vụ kích hoạt các tác vụ khác\n"
"- 'Gửi email': tự động gửi email (Thảo luận)\n"
"- 'Thêm người theo dõi': thêm người theo dõi (Thảo luận)\n"
"- 'Tạo hoạt động kế tiếp': tạo hoạt động (Thảo luận)"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__usage
msgid "Usage"
msgstr "Cách dùng"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""
"Dùng thông tin 'Người dùng cụ thể' để luôn chỉ định cùng một người dùng cho "
"hoạt động tiếp theo. Sử dụng 'Người dùng chung từ hồ sơ' để chỉ định tên "
"trường của người dùng để chọn trong hồ sơ. "

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_calendar_id
msgid "Use Calendar"
msgstr "Dùng lịch"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Use automated actions to automatically trigger actions for\n"
"                various screens. Example: a lead created by a specific user may\n"
"                be automatically set to a specific Sales Team, or an\n"
"                opportunity which still has status pending after 14 days might\n"
"                trigger an automatic reminder email."
msgstr ""
"Sử dụng các tác vụ tự động để tự động kích hoạt tác vụ trong \n"
"                các màn hình khác nhau. Ví dụ: một lead được tạo bởi một người dùng\n"
"                có thể được đặt tự động cho một Đội kinh doanh cụ thể, \n"
"                hoặc một cơ hội vẫn ở trạng thái chờ sau 14 ngày có thể\n"
"                kích hoạt email nhắc nhở tự động."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_field_name
msgid "User field name"
msgstr "Tên trường người dùng"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__fields_lines
msgid "Value Mapping"
msgstr "Sơ đồ giá trị"

#. module: base_automation
#: code:addons/base_automation/models/base_automation.py:0
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Warning"
msgstr "Cảnh báo"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possible to use a "
"calendar to compute the date based on working days."
msgstr ""
"Khi tính toán điều kiện thời gian theo ngày, có thể sử dụng lịch để tính "
"toán ngày dựa trên ngày làm việc."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__sequence
msgid ""
"When dealing with multiple actions, the execution order is based on the "
"sequence. Low number means high priority."
msgstr ""
"Khi xử lý nhiều tác vụ, thứ tự thực thi được dựa vào trình tự. Số thấp nghĩa"
" là ưu tiên cao. "

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                                  If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""
"Khi nào thì điều kiện sẽ được kích hoạt.\n"
"                                  Nếu có, sẽ được kiểm tra bởi công cụ lập kế hoạch. Nếu trống, sẽ được kiểm tra khi tạo và cập nhật. "

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr "Khi không được chọn, quy tắc sẽ bị ẩn và sẽ không được thực thi."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__code
msgid ""
"Write Python code that the action will execute. Some variables are available"
" for use; help about python expression is given in the help tab."
msgstr ""
"Viết mã Python mà tác vụ sẽ thực thi. Có thể sử dụng một số biến có sẵn; trợ"
" giúp về biểu thức python được cung cấp trong tab trợ giúp. "

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"You can ask an administrator to disable or correct this automated action."
msgstr "Bạn có thể yêu cầu quản trị viên tắt hoặc sửa tác vụ tự động hóa này."

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "You can disable this automated action or edit it to solve the issue."
msgstr ""
"Bạn có thể tắt tác vụ tự động hóa hoặc chỉnh sửa để giải quyết vấn đề này."

#. module: base_automation
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"You cannot send an email, add followers or create an activity for a deleted "
"record.  It simply does not work."
msgstr ""
"Bạn không thể gửi email, thêm người theo dõi hoặc tạo một hoạt động cho hồ "
"sơ đã xóa. Việc này là hoàn toàn không thể."
