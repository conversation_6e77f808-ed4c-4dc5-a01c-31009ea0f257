<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- sales tax included -->
    <record id="l10n_cn_sales_included_13" model="account.tax.template">
        <field name="chart_template_id" ref="l10n_chart_china_small_business"/>
        <field name="name">税收13％（含)</field>
        <field name="description">税收13％</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include" eval="1"/>
        <field name="tax_group_id" ref="l10n_cn.l10n_cn_tax_group_vat_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
    </record>
    <record id="l10n_cn_sales_included_9" model="account.tax.template">
        <field name="chart_template_id" ref="l10n_chart_china_small_business"/>
        <field name="name">税收9％（含)</field>
        <field name="description">税收9％</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include" eval="1"/>
        <field name="tax_group_id" ref="l10n_cn.l10n_cn_tax_group_vat_9"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
    </record>
    <record id="l10n_cn_sales_included_6" model="account.tax.template">
        <field name="chart_template_id" ref="l10n_chart_china_small_business"/>
        <field name="name">税收6％（含)</field>
        <field name="description">税收6％</field>
        <field name="amount">6</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include" eval="1"/>
        <field name="tax_group_id" ref="l10n_cn.l10n_cn_tax_group_vat_6"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
    </record>


    <!-- sales tax excluded -->
    <record id="l10n_cn_sales_excluded_13" model="account.tax.template">
        <field name="chart_template_id" ref="l10n_chart_china_small_business"/>
        <field name="name">税收13%</field>
        <field name="description">税收13%</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include" eval="0"/>
        <field name="tax_group_id" ref="l10n_cn.l10n_cn_tax_group_vat_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
    </record>
    <record id="l10n_cn_sales_excluded_9" model="account.tax.template">
        <field name="chart_template_id" ref="l10n_chart_china_small_business"/>
        <field name="name">税收9%</field>
        <field name="description">税收9%</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include" eval="0"/>
        <field name="tax_group_id" ref="l10n_cn.l10n_cn_tax_group_vat_9"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
    </record>
    <record id="l10n_cn_sales_excluded_6" model="account.tax.template">
        <field name="chart_template_id" ref="l10n_chart_china_small_business"/>
        <field name="name">税收6%</field>
        <field name="description">税收6％</field>
        <field name="amount">6</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include" eval="0"/>
        <field name="tax_group_id" ref="l10n_cn.l10n_cn_tax_group_vat_6"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
    </record>

    <!-- purchase tax excluded -->
    <record id="l10n_cn_purchase_excluded_13" model="account.tax.template">
        <field name="chart_template_id" ref="l10n_chart_china_small_business"/>
        <field name="name">税收13%</field>
        <field name="description">税收13%</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include" eval="0"/>
        <field name="tax_group_id" ref="l10n_cn.l10n_cn_tax_group_vat_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
    </record>
    <record id="l10n_cn_purchase_excluded_9" model="account.tax.template">
        <field name="chart_template_id" ref="l10n_chart_china_small_business"/>
        <field name="name">税收9%</field>
        <field name="description">税收9%</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include" eval="0"/>
        <field name="tax_group_id" ref="l10n_cn.l10n_cn_tax_group_vat_9"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
    </record>
    <record id="l10n_cn_purchase_excluded_6" model="account.tax.template">
        <field name="chart_template_id" ref="l10n_chart_china_small_business"/>
        <field name="name">税收6%</field>
        <field name="description">税收6％</field>
        <field name="amount">6</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include" eval="0"/>
        <field name="tax_group_id" ref="l10n_cn.l10n_cn_tax_group_vat_6"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_cn_2221'),
            }),
        ]"/>
    </record>
</odoo>
