# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_import_module
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-04-22 12:13+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Malayalam (India) (http://www.transifex.com/odoo/odoo-9/"
"language/ml_IN/)\n"
"Language: ml_IN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_create_uid
msgid "Created by"
msgstr "രൂപപ്പെടുത്തിയത്"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_create_date
msgid "Created on"
msgstr "നിർമിച്ച ദിവസം"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_display_name
msgid "Display Name"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:94
#, python-format
msgid "File '%s' exceed maximum allowed file size"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:86
#, python-format
msgid "File is not a zip file!"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_force
msgid "Force init"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module_force
msgid "Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_id
msgid "ID"
msgstr "ID"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import App"
msgstr ""

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import Module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_import_message
msgid "Import message"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module_imported
msgid "Imported Module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module___last_update
msgid "Last Modified on"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_write_uid
msgid "Last Updated by"
msgstr "അവസാനം അപ്ഡേറ്റ് ചെയ്തത്"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_write_date
msgid "Last Updated on"
msgstr "അവസാനം അപ്ഡേറ്റ് ചെയ്ത ദിവസം"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_module_file
msgid "Module .ZIP file"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Open Modules"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Select module package to import (.zip file):"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_state
msgid "Status"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:33
#, python-format
msgid "Unmet module dependencies: %s"
msgstr ""

#. module: base_import_module
#: selection:base.import.module,state:0
msgid "done"
msgstr ""

#. module: base_import_module
#: selection:base.import.module,state:0
msgid "init"
msgstr ""
