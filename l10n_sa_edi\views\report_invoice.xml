<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <template id="arabic_english_invoice" inherit_id="l10n_sa_invoice.arabic_english_invoice">

            <!--    Add Currency Exchange rate if different currency than SAR    -->
            <xpath expr="//div[hasclass('clearfix')]" position="after">
                <div t-if="o.company_id.country_id.code == 'SA' and o.currency_id != o.company_id.currency_id"
                     id="sar_amounts" class="row clearfix ml-auto my-3 text-nowrap table">
                    <t t-set="sar_rate"
                       t-value="o.env['res.currency']._get_conversion_rate(o.currency_id, o.company_id.currency_id, o.company_id, o.l10n_sa_confirmation_datetime)"/>
                    <div name="exchange_rate" class="col-auto">
                        <strong>Exchange Rate</strong>
                        <p class="m-0" t-esc="sar_rate" t-options='{"widget": "float", "precision": 5}'/>
                    </div>
                    <div name="sar_subtotal" class="col-auto">
                        <strong>Subtotal (SAR)</strong>
                        <p class="m-0" t-esc="o.amount_untaxed_signed"
                           t-options='{"widget": "monetary", "display_currency": o.company_currency_id}'/>
                    </div>
                    <div name="sar_vat_amount" class="col-auto">
                        <strong>VAT Amount (SAR)</strong>
                        <p class="m-0"
                           t-esc="o.currency_id._convert(o.amount_tax, o.company_id.currency_id, o.company_id, o.l10n_sa_confirmation_datetime)"
                           t-options='{"widget": "monetary", "display_currency": o.company_currency_id}'/>
                    </div>
                    <div name="sar_total" class="col-auto">
                        <strong>Total (SAR)</strong>
                        <p class="m-0" t-esc="o.amount_total_signed"
                           t-options='{"widget": "monetary", "display_currency": o.company_currency_id}'/>
                    </div>
                </div>
            </xpath>

            <xpath expr="//t[@t-set='address']" position="inside">
                <div t-if="o.partner_id.l10n_sa_additional_identification_scheme and o.partner_id.l10n_sa_additional_identification_number" class="text-right mt0">
                        <span t-field="o.partner_id.l10n_sa_additional_identification_scheme"/>:
                        <span t-field="o.partner_id.l10n_sa_additional_identification_number"/>
                </div>
            </xpath>
            <xpath expr="//div[hasclass('col-4')]//span[@t-if=&quot;o.move_type == &apos;out_invoice&apos; and o.state == &apos;posted&apos;&quot;]" position="replace">
                <span t-if="o.move_type == 'out_invoice' and o.state == 'posted'">
                    <t t-if="o._l10n_sa_is_simplified()">
                        Simplified Tax Invoice
                    </t>
                    <t t-else="">
                        Tax Invoice
                    </t>
                </span>
            </xpath>
            <xpath expr="//div[hasclass('col-4')][3]//span[@t-if=&quot;o.move_type == &apos;out_invoice&apos; and o.state == &apos;posted&apos;&quot;]" position="replace">
                <span t-if="o.move_type == 'out_invoice' and o.state == 'posted'">
                    <t t-if="o._l10n_sa_is_simplified()">
                        فاتورة ضريبية مبسطة
                    </t>
                    <t t-else="">
                        فاتورة ضريبية
                    </t>
                </span>
            </xpath>

        </template>

    </data>
</odoo>