# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_stripe
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:9
#, python-format
msgid "&times;"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_image_url
msgid ""
"A relative or absolute URL pointing to a square image of your brand or "
"product. As defined in your Stripe profile. See: "
"https://stripe.com/docs/checkout"
msgstr ""

#. module: payment_stripe
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr ""

#. module: payment_stripe
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr ""

#. module: payment_stripe
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr ""

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.stripe_s2s_form
msgid "CVC"
msgstr ""

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.stripe_s2s_form
msgid "Card number"
msgstr ""

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.stripe_s2s_form
msgid "Cardholder name"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_image_url
msgid "Checkout Image URL"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:15
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:8
#, python-format
msgid "Error"
msgstr "Greška"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.stripe_s2s_form
msgid "Expires (MM / YY)"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/stripe.js:33
#, python-format
msgid "Just one more second, confirming your payment..."
msgstr ""

#. module: payment_stripe
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr ""

#. module: payment_stripe
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr ""

#. module: payment_stripe
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr ""

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/stripe.js:59
#, python-format
msgid "Payment error"
msgstr ""

#. module: payment_stripe
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:174
#, python-format
msgid ""
"Perhaps the problem can be solved by double-checking your credit card "
"details, or contacting your bank?"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__provider
msgid "Provider"
msgstr ""

#. module: payment_stripe
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr ""

#. module: payment_stripe
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_secret_key
msgid "Stripe Secret Key"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:172
#, python-format
msgid "Stripe gave us the following info about the problem: '%s'"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:184
#, python-format
msgid "Stripe: %s orders found for reference %s"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:180
#, python-format
msgid "Stripe: no order found for reference %s"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:170
#, python-format
msgid "We're sorry to report that the transaction has failed."
msgstr ""

#. module: payment_stripe
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr ""
