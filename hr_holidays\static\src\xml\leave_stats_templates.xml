<?xml version="1.0" encoding="UTF-8"?>

<templates id="template" xml:space="preserve">
    <t t-name="hr_holidays.leave_per_type">
        <table class="o_group o_inner_group table-striped">
        <thead>
            <tr>
                <td colspan="2">
                    <div class="o_horizontal_separator"><t t-esc="widget.employee.data.display_name"/> in <t t-esc="widget.date.format('YYYY')"/></div>
                </td>
            </tr>
        </thead>
        <tbody>
            <t t-if="widget.leavesPerType.length === 0">
                <tr>
                    <td>None</td>
                </tr>
            </t>
            <t t-foreach="widget.leavesPerType" t-as="leave_type">
                <tr>
                    <td><t t-esc="leave_type.holiday_status_id[1]"/></td>
                    <td  class="w-50"><t t-esc="leave_type.number_of_days"/> day(s)</td>
                </tr>
            </t>
        </tbody>
        </table>
    </t>

    <t t-name="hr_holidays.department_leave">
        <table class="o_group o_inner_group table-striped">
        <thead>
            <tr>
                <td colspan="2">
                    <div class="o_horizontal_separator"><t t-esc="widget.department.data.display_name"/> in <t t-esc="widget.date.format('MMMM')"/></div>
                </td>
            </tr>
        </thead>
        <tbody>
            <t t-if="widget.departmentLeaves.length === 0">
                <tr>
                    <td>None</td>
                </tr>
            </t>
            <t t-foreach="widget.departmentLeaves" t-as="leave">
                <tr t-attf-class="{{leave.employee_id[0] === widget.employee.res_id ? 'font-weight-bold' : ''}}">
                    <td><t t-esc="leave.employee_id[1]"/>: <t t-esc="leave.number_of_days"/> day(s) </td>
                    <td class="w-50"><t t-esc="leave.date_from"/> - <t t-esc="leave.date_to"/></td>
                </tr>
            </t>
        </tbody>
        </table>
    </t>

    <div t-name="hr_holidays.leave_stats" class="o_leave_stats">
        <t t-if="widget.employee">
            <t t-if="widget.leavesPerType">
                <t t-call="hr_holidays.leave_per_type"/>
            </t>
            <t t-if="widget.departmentLeaves">
                <t t-call="hr_holidays.department_leave"/>
            </t>
        </t>
    </div>

</templates>
