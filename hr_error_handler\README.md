# HR Error Handler

## الوصف

مديول معالج أخطاء الموارد البشرية لـ Odoo 15. يحل مشاكل الحقول المفقودة في نموذج `hr.employee.public`.

## المشكلة التي يحلها

عندما تحاول الوصول إلى حقول موجودة في `hr.employee` ولكنها غير موجودة في `hr.employee.public`، تحصل على خطأ مثل:

```
ValueError: Invalid field 'passport_issue_location' on model 'hr.employee.public'
```

## الحل

هذا المديول يوسع نموذج `hr.employee.public` ويضيف الحقول المفقودة كحقول مرتبطة (related fields) من `hr.employee`.

## الحقول المضافة

- `passport_issue_location` - مكان إصدار جواز السفر
- `passport_issue_date` - تاريخ إصدار جواز السفر  
- `passport_end_date` - تاريخ انتهاء جواز السفر
- `referral_date` - تاريخ الإحالة
- `national_number` - الرقم الوطني
- `residence_place` - مكان الإقامة
- `city` - المدينة
- `neighborhood` - الحي

## التثبيت

1. انسخ مجلد `hr_error_handler` إلى مجلد addons في Odoo
2. أعد تشغيل خدمة Odoo
3. قم بتحديث قائمة التطبيقات
4. ثبت المديول من قائمة التطبيقات

## المتطلبات

- Odoo 15.0
- مديول `hr` (مثبت افتراضياً)
- مديول `hr_employees_masarat` (يجب أن يكون مثبتاً)

## الاستخدام

بعد تثبيت المديول، ستتمكن من الوصول إلى الحقول المضافة في `hr.employee.public` دون أخطاء.

## الترخيص

LGPL-3
