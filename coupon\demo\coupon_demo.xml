<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="product_product_10_percent_discount" model="product.product">
            <field name="name">10.0% discount on total amount</field>
            <field name="type">service</field>
            <field name="taxes_id" eval="False"/>
            <field name="supplier_taxes_id" eval="False"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="default_code">10PERCENTDISC</field>
            <field name="categ_id" ref="product.product_category_3"/>
        </record>

        <record id="10_percent_auto_applied" model="coupon.program">
            <field name="name">Code for 10% on orders</field>
            <field name="promo_code_usage">code_needed</field>
            <field name="promo_code">10pc</field>
            <field name="discount_apply_on">on_order</field>
            <field name="discount_type">percentage</field>
            <field name="discount_percentage">10.0</field>
            <field name="program_type">promotion_program</field>
            <field name="discount_line_product_id" ref="coupon.product_product_10_percent_discount"/>
            <field name="validity_duration">0</field>
        </record>

        <record id="product_product_free_large_cabinet" model="product.product">
            <field name="name">Free Product - Large Cabinet</field>
            <field name="type">service</field>
            <field name="taxes_id" eval="False"/>
            <field name="supplier_taxes_id" eval="False"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="default_code">FREELARGECABINET</field>
            <field name="categ_id" ref="product.product_category_3"/>
        </record>

        <record id="3_cabinets_plus_1_free" model="coupon.program">
            <field name="name">Buy 3 large cabinets, get one for free</field>
            <field name="promo_code_usage">no_code_needed</field>
            <field name="discount_apply_on">on_order</field>
            <field name="reward_type">product</field>
            <field name="program_type">promotion_program</field>
            <field name="reward_product_id" ref="product.product_product_6"></field>
            <field name="rule_min_quantity">3</field>
            <field name="rule_products_domain">[["name","ilike","large cabinet"]]</field>
            <field name="discount_line_product_id" ref="coupon.product_product_free_large_cabinet"/>
            <field name="validity_duration">0</field>
        </record>

        <record id="10_percent_coupon" model="coupon.program">
            <field name="name">10% Discount</field>
            <field name="promo_code_usage">code_needed</field>
            <field name="discount_apply_on">on_order</field>
            <field name="discount_type">percentage</field>
            <field name="discount_percentage">10.0</field>
            <field name="program_type">coupon_program</field>
        </record>
    </data>
</odoo>
