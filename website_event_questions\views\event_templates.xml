<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="registration_attendee_details_questions" inherit_id="website_event.registration_attendee_details" name="Registration Attendee Details with questions">
    <!-- Attendee specific questions -->
    <xpath expr="//t[@name='attendee_loop']//*[hasclass('modal-body')]" position="inside">
        <t t-if="event.specific_question_ids">
            <div t-foreach="event.specific_question_ids" t-as="question" class="row">
                <div class="col-lg-6 mt-2">
                    <t t-call="website_event_questions.registration_event_question">
                        <t t-set="registration_index" t-value="counter"/>
                    </t>
                </div>
            </div>
        </t>
    </xpath>
    <!-- Generic questions -->
    <xpath expr="//*[hasclass('modal-footer')]" position="before">
        <t t-if="event.general_question_ids">
            <div class="modal-body bg-light border-bottom o_wevent_registration_question_global">
                <div t-foreach="event.general_question_ids" t-as="question" class="row">
                    <div class="col-lg-6 mt-2">
                        <t t-call="website_event_questions.registration_event_question">
                            <t t-set="registration_index" t-value="0"/>
                        </t>
                    </div>
                </div>
            </div>
        </t>
    </xpath>
</template>

<template id="registration_event_question" name="Registration Event Question">
    <label t-esc="question.title"/>
    <t t-if="question.question_type == 'simple_choice'">
        <select t-attf-name="question_answer-#{registration_index}-#{question.id}" class="custom-select" required="true">
            <t t-foreach="question.answer_ids" t-as="answer">
                <option t-esc="answer.name" t-att-value="answer.id"/>
            </t>
        </select>
    </t>
    <t t-elif="question.question_type == 'text_box'">
        <textarea t-attf-name="question_answer-#{registration_index}-#{question.id}" class="col-lg-12 form-control"/>
    </t>
</template>

</odoo>
