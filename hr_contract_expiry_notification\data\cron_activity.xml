<?xml version="1.0" encoding='UTF-8'?>
<odoo>

	<record id="cron_contract_expiry" model="ir.cron">
        <field name="name">HR : Employees Contract Expiry Notification</field>
        <field name="model_id" ref="model_hr_contract_expiry_notification"/>
        <field name="state">code</field>
        <field name="code">model.cron_contract_expiry_notification()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
    </record>

    <record id="mail_activity_contract_expiry" model="mail.activity.type">
        <field name="name">HR : Employees Contract Expiry Notification</field>
        <field name="res_model">hr.contract_expiry_notification</field>
    </record>

</odoo>