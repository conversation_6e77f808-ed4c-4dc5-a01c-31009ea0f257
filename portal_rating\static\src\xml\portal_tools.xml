<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="portal_rating.rating_stars_static">
        <t t-set="val_integer" t-value="Math.floor(val)"/>
        <t t-set="val_decimal" t-value="val - val_integer"/>
        <t t-set="empty_star" t-value="5 - (val_integer+Math.ceil(val_decimal))"/>
        <div class="o_website_rating_static" t-att-style="inline_mode ? 'display:inline' : ''" t-attf-aria-label="#{val} stars on 5" t-attf-title="#{val} stars on 5">
            <t t-foreach="_.range(0, val_integer)" t-as="num">
                <i class="fa fa-star" role="img"></i>
            </t>
            <t t-if="val_decimal">
                <i class="fa fa-star-half-o" role="img"></i>
            </t>
            <t t-foreach="_.range(0, empty_star)" t-as="num">
                <i class="fa fa-star text-black-25" role="img"></i>
            </t>
        </div>
    </t>

    <t t-name="portal_rating.rating_card">
        <t t-set="two_columns" t-value="widget.options['two_columns']"/>
        <div class="row o_website_rating_card_container justify-content-center">
            <div t-attf-class="#{two_columns and 'col-lg-12' or 'col-lg-3'}" t-if="!_.isEmpty(widget.get('rating_card_values'))">
                <p t-if="!two_columns"><strong>Average</strong></p>
                <div t-attf-class="o_website_rating_avg #{two_columns and 'mb-2' or 'text-center'}">
                    <h1><t t-esc="widget.get('rating_card_values')['avg']"/></h1>
                    <t t-call="portal_rating.rating_stars_static">
                        <t t-set="val" t-value="widget.get('rating_card_values')['avg'] || 0"/>
                    </t>
                    <t t-call="portal.chatter_message_count"/>
                </div>
            </div>
            <div t-attf-class="#{two_columns and 'col-lg-12' or 'col-lg-6'}" t-if="!_.isEmpty(widget.get('rating_card_values'))">
                <hr t-if="two_columns"/>
                <p t-if="!two_columns"><strong>Details</strong></p>
                <div class="o_website_rating_progress_bars">
                    <table class="o_website_rating_progress_table">
                        <t t-foreach="widget.get('rating_card_values')['percent']" t-as="percent">
                            <tr class="o_website_rating_select" t-att-data-star="percent['num']" style="opacity: 1">
                                <td class="o_website_rating_table_star_num text-nowrap" t-att-data-star="percent['num']">
                                    <t t-esc="percent['num']"/> stars
                                </td>
                                <td class="o_website_rating_table_progress">
                                    <div class="progress">
                                        <div class="progress-bar o_rating_progressbar" role="progressbar" t-att-aria-valuenow="percent['percent']" aria-valuemin="0" aria-valuemax="100" t-att-style="'width:' + percent['percent'] + '%;'">
                                        </div>
                                    </div>
                                </td>
                                <td class="o_website_rating_table_percent">
                                    <strong><t t-esc="Math.round(percent['percent'] * 100) / 100"/>%</strong>
                                </td>
                                <td class="o_website_rating_table_reset">
                                    <button class="btn btn-link o_website_rating_select_text" t-att-data-star="percent['num']">
                                        <i t-attf-class="fa fa-times d-block #{!two_columns and 'd-sm-none' or ''}" role="img" aria-label="Remove Selection"/>
                                        <span t-attf-class="d-none #{!two_columns and 'd-sm-block' or ''}">Remove Selection</span>
                                    </button>
                                </td>
                            </tr>
                        </t>
                    </table>
                </div>
            </div>
        </div>
    </t>

    <t t-name="portal_rating.rating_star_input">
        <div class="o_rating_star_card" t-if="widget.options['display_rating']">
            <t t-set="val_integer" t-value="Math.floor(default_rating)"/>
            <t t-set="val_decimal" t-value="default_rating - val_integer"/>
            <t t-set="empty_star" t-value="5 - (val_integer+Math.ceil(val_decimal))"/>

            <div class="stars enabled">
                <t t-foreach="_.range(0, val_integer)" t-as="num">
                    <i class="fa fa-star" role="img" aria-label="One star" title="One star"></i>
                </t>
                <t t-if="val_decimal">
                    <i class="fa fa-star-half-o" role="img" aria-label="Half a star" title="Half a star"></i>
                </t>
                <t t-foreach="_.range(0, empty_star)" t-as="num" role="img" t-attf-aria-label="#{empty_star} on 5" t-attf-title="#{empty_star} on 5">
                    <i class="fa fa-star-o text-black-25"></i>
                </t>
            </div>
            <div class="rate_text">
                <span class="badge badge-info"></span>
            </div>
            <input type="hidden" readonly="readonly" name="rating_value" t-att-value="default_rating || ''"/>
        </div>
    </t>
</templates>
