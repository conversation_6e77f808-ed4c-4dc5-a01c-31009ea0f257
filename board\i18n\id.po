# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# <PERSON> <amunif<PERSON><EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <wahyuse<PERSON><EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>y Useful <<EMAIL>>, 2022
# arfa simoncelli, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: arfa simoncelli, 2022\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#, python-format
msgid "\"%s\" added to dashboard"
msgstr "\"%s\" ditambahkan ke dashboard"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"\"Add to\n"
"                Dashboard\""
msgstr ""
"\"Tambahkan ke\n"
"                Dashboard\""

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "'%s' added to dashboard"
msgstr "'%s' ditambahkan ke dashboard"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add"
msgstr "Tambah"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add to my Dashboard"
msgstr "Tambahkan ke Dashboard saya"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add to my dashboard"
msgstr "Tambah ke dashboard saya"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#, python-format
msgid "Are you sure you want to remove this item?"
msgstr "Apakah anda yakin menghapuskan butir ini?"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#: model:ir.model,name:board.model_board_board
#, python-format
msgid "Board"
msgstr "Papan"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Change Layout"
msgstr "Ubah Tata Letak"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Change Layout.."
msgstr "Ubah Tata Letak"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Choose dashboard layout"
msgstr "Pilih tata letak dashboard"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "Could not add filter to dashboard"
msgstr "Tidak bisa menambahkan filter untuk dashboard"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#, python-format
msgid "Edit Layout"
msgstr "Mengedit Tata Letak"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Layout"
msgstr "Layout"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
#, python-format
msgid "My Dashboard"
msgstr "Dashboard Saya"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "Please refresh your browser for the changes to take effect."
msgstr "Harap pulihkan browser Anda agar perubahan diterapkan."

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"To add your first report into this dashboard, go to any\n"
"                menu, switch to list or graph view, and click"
msgstr ""
"Untuk menambahkan laporan pertama Anda ke dasbor ini, buka menu \n"
"apa saja, alihkan ke tampilan daftar atau grafik, dan klik"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"You can filter and group data before inserting into the\n"
"                dashboard using the search options."
msgstr ""
"Anda dapat memfilter dan mengelompokkan data sebelum dimasukkan \n"
"ke dasbor menggunakan opsi pencarian."

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Your personal dashboard is empty"
msgstr "Dasbor pribadi Anda kosong"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "in the extended search options."
msgstr "dalam opsi pencarian yang diperluas."
