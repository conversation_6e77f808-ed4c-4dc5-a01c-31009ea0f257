# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_stock
# 
# Translators:
# <PERSON><PERSON><PERSON> <iam<PERSON><EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <ifara<PERSON><EMAIL>>, 2022
# <PERSON><PERSON> <<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "% On-Time Delivery"
msgstr "٪ تحویل به موقع"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#: code:addons/purchase_stock/models/stock_rule.py:0
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr "+ %d روز"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid ""
"<span attrs=\"{'invisible': [('on_time_rate', '&gt;=', 0)]}\">No On-time "
"Delivery Data</span>"
msgstr ""
"<span attrs=\"{'invisible': [('on_time_rate', '&gt;=', 0)]}\">بدون اطلاعات "
"تحویل به موقع</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_text\">On-time Rate</span>"
msgstr "<span class=\"o_stat_text\">نرخ سروقت</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">خریدها</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_value\">%</span>"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>اینکوترم:</strong>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>نشانی ارسال:</strong>"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"با توجه به پیکربندی محصول، مقدار دریافتی را می توان به طور خودکار توسط مکانیزم محاسبه کرد:\n"
"   - دستی: مقدار به صورت دستی روی خط تنظیم می شود\n"
"   - انتقال موجودی: مقدار از برداشت تایید شده حاصل می شود\n"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_rule__action
msgid "Action"
msgstr "عمل"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_report__avg_receipt_delay
msgid ""
"Amount of time between expected and effective receipt date. Due to a hack "
"needed to calculate this,               every record will show the same "
"average value, therefore only use this as an aggregated value with "
"group_operator=avg"
msgstr ""
"مقدار زمان بین تاریخ دریافت مورد انتظار و موثر. به دلیل هک مورد نیاز برای "
"محاسبه، هر رکورد مقدار میانگین یکسانی را نشان می دهد، بنابراین فقط از آن به "
"عنوان یک مقدار جمع شده با group_operator=avg استفاده کنید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__avg_receipt_delay
msgid "Average Receipt Delay"
msgstr "میانگین تاخیر دریافت"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock.py:0
#: model:ir.model.fields.selection,name:purchase_stock.selection__stock_rule__action__buy
#: model:stock.location.route,name:purchase_stock.route_warehouse0_buy
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_report_stock_rule
#, python-format
msgid "Buy"
msgstr "خرید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_pull_id
msgid "Buy rule"
msgstr "قاعده خرید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "Buy to Resupply"
msgstr "خرید برای تامین مجدد"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__effective_date
msgid "Completion date of the first receipt order."
msgstr "تاریخ تکمیل اولین سفارش دریافتی."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_partner
msgid "Contact"
msgstr "مخاطب"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "Corresponding receipt not found."
msgstr "رسید مربوطه یافت نشد."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__created_purchase_line_id
msgid "Created Purchase Order Line"
msgstr "سطر سفارش خرید ایجاد شده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__product_description_variants
msgid "Custom Description"
msgstr "توضیحات سفارشی"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,help:purchase_stock.field_res_config_settings__days_to_purchase
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Days needed to confirm a PO, define when a PO should be validated"
msgstr ""
"روزهای مورد نیاز برای تایید یک PO، تعیین کنید که یک PO باید چه زمانی اعتبار "
"سنجی شود"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__days_to_purchase
#, python-format
msgid "Days to Purchase"
msgstr "روز برای خرید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_type_id
msgid "Deliver To"
msgstr "تحویل به"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,help:purchase_stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"بسته به ماژول های نصب شده، این به شما امکان می دهد مسیر محصول را مشخص کنید: "
"آیا خریداری می شود، تولید می شود، بر اساس سفارش دوباره پر می شود و غیره."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Destination Location Type"
msgstr "نوع مکان مقصد"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Documentation"
msgstr "مستندات"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_dest_ids
msgid "Downstream Moves"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "دراپشیپینگ"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__effective_date
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__effective_date
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__date
msgid "Effective Date"
msgstr "تاریخ اجرا"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "Effective Date Last Year"
msgstr "تاریخ لازم الاجرا شدن در سال گذشته"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "استثنا(هایی) در سفارش(های) خرید رخ داده است:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s):"
msgstr "استثنا(ها):"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"For the product %s, the warehouse of the operation type (%s) is inconsistent"
" with the location (%s) of the reordering rule (%s). Change the operation "
"type or cancel the request for quotation."
msgstr ""
"برای محصول %s، انبار نوع عملیات (%s) با مکان (%s) قانون سفارش مجدد (%s) "
"مطابقت ندارد. نوع عملیات را تغییر دهید یا درخواست استعلام قیمت را لغو کنید."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__forecasted_issue
msgid "Forecasted Issue"
msgstr "مسئله پیش بینی شده"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Generate the draft vendor bill."
msgstr "ایجاد پیش‌نویس صورتحساب فروشنده"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Go back to the purchase order to generate the vendor bill."
msgstr "برای ایجاد صورتحساب فروشنده به سفارش خرید برگردید."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__id
msgid "ID"
msgstr "شناسه"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoming_picking_count
msgid "Incoming Shipment count"
msgstr "تعداد محموله های دریافتی"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Incoming Shipments"
msgstr "محموله ی در حال آمدن"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoterm_id
msgid "Incoterm"
msgstr "اینکوترمز"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"شرایط تجاری بین‌المللی یک سری شرایط از پیش تعریف شده است که در معاملات "
"بین‌المللی مورد استفاده قرار می‌گیرد"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__is_shipped
msgid "Is Shipped"
msgstr "ارسال شده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__is_installed_sale
msgid "Is the Sale Module Installed"
msgstr "آیا ماجول فروش نصب شده است"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_account_move
msgid "Journal Entry"
msgstr "داده روزنامه"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report____last_update
msgid "Last Modified on"
msgstr "آخرین تغییر در"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__last_purchase_date
msgid "Last Purchase"
msgstr "آخرین خرید"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Logistics"
msgstr "لجستیک"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr "سری/سریال"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Manual actions may be needed."
msgstr "ممکن است اقدامات دستی لازم باشد."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"حاشیه خطا برای زمان تحویل فروشنده. وقتی سیستم سفارش‌های خرید را برای "
"سفارش‌دهی مجدد محصولات ایجاد می‌کند، برای مقابله با تأخیرهای غیرمنتظره "
"فروشنده، آن‌ها چند روز زودتر برنامه‌ریزی می‌شوند."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "قاعده حداقل انبارداری"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Move forward expected request creation date by"
msgstr "تاریخ ایجاد درخواست مورد انتظار را به جلو ببرید"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Next transfer(s) impacted:"
msgstr "انتقال(های) بعدی تحت تاثیر قرار گرفته:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "No data yet"
msgstr "هنوز داده ای وجود ندارد"

#. module: purchase_stock
#: code:addons/purchase_stock/models/account_invoice.py:0
#, python-format
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr "Odoo قادر به تولید ورودی های آنگلوساکسون نیست. ارزش کل %s صفر است."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_view_graph
msgid "On-Time Delivery"
msgstr "تحویل به موقع"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__on_time_rate
msgid "On-Time Delivery Rate"
msgstr "نرخ تحویل به موقع"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_on_time
msgid "On-Time Quantity"
msgstr "تعداد به موقع"

#. module: purchase_stock
#: model:ir.actions.act_window,name:purchase_stock.action_purchase_vendor_delay_report
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "On-time Delivery"
msgstr "تحویل به موقع"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "On-time Rate"
msgstr "نرخ به موقع"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__orderpoint_id
msgid "Orderpoint"
msgstr "نقطه سفارش"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_users__on_time_rate
msgid ""
"Over the past 12 months; the number of products received on time divided by "
"the number of ordered products."
msgstr ""
"طی 12 ماه گذشته؛ تعداد محصولات دریافت شده به موقع تقسیم بر تعداد محصولات "
"سفارش داده شده."

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Process all the receipt quantities."
msgstr "تمام مقادیر رسید را پردازش کنید."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__group_id
msgid "Procurement Group"
msgstr "گروه تدارکات"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_product
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__product_id
msgid "Product"
msgstr "محصول"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__category_id
msgid "Product Category"
msgstr "دسته محصول"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__supplier_id
msgid "Product Supplier"
msgstr "تامین کننده محصول"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_template
msgid "Product Template"
msgstr "قالب محصول"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__propagate_cancel
msgid "Propagate cancellation"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__purchase_line_ids
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__purchase_line_ids
msgid "Purchase Lines"
msgstr "سطرهای خرید"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order
msgid "Purchase Order"
msgstr "سفارش خرید"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__purchase_order_line_ids
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__purchase_line_id
msgid "Purchase Order Line"
msgstr "سطر سفارش خرید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__purchase_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_production_lot__purchase_order_ids
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "Purchase Orders"
msgstr "سفارشات خرید"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_report
msgid "Purchase Report"
msgstr "گزارش خرید"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "Purchase Security Lead Time"
msgstr "زمان تحویل مطمئن خرید"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_production_lot__purchase_order_count
msgid "Purchase order count"
msgstr "تعداد سفارش خرید"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receipt"
msgstr "رسید"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receive Products"
msgstr "دریافت محصولات"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Receive the ordered products."
msgstr "محصولات سفارش داده شده را دریافت کنید."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "روش تعداد دریافت شده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_ids
msgid "Receptions"
msgstr "رسیدها"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Request your vendors to deliver to your customers"
msgstr "از فروشندگان بخواهید که به مشتری‌ها تحویل دهند"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_report_product_product_replenishment
msgid "Requests for quotation"
msgstr "درخواست استعلام قیمت"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_ids
msgid "Reservation"
msgstr "رزرو کردن"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "برداشت بازگشت"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:purchase_stock.field_product_template__route_ids
msgid "Routes"
msgstr "مسیرها"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"Schedule automatically generated request for quotations earlier to avoid "
"delays"
msgstr ""
"برای جلوگیری از تاخیر، درخواست تولید شده به صورت خودکار برای پیش‌فاکتور را "
"زودتر برنامه ریزی کنید"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.product_supplierinfo_replenishment_tree_view
msgid "Set as Supplier"
msgstr "تنظیم به عنوان تامین کننده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__show_set_supplier_button
msgid "Show Set Supplier Button"
msgstr "نمایش دکمه تنظیم تامین کننده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__show_supplier
msgid "Show supplier column"
msgstr "نمایش ستون تامین کننده"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_move
msgid "Stock Move"
msgstr "انتقال موجودی"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order_line__qty_received_method__stock_moves
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_line_view_form_inherit
msgid "Stock Moves"
msgstr "انتقالهای موجودی"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "گزارش جایگزینی انبار"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_rule
msgid "Stock Rule"
msgstr "قانون موجودی انبار"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "گزارش قانون موجودی انبار"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "اطلاعات تکمیلی تامین کننده موجودی"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "لیست قیمت تامین کننده"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_ids
msgid "Supplierinfo"
msgstr "اطلاعات تامین کننده"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Technical field used to display the Drop Ship Address"
msgstr "فیلد فنی مورد استفاده برای نمایش آدرس دراپشیپ"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__purchase_order_line_ids
msgid "Technical: used to compute quantities."
msgstr "فنی: برای محاسبه کمیت ها استفاده می شود."

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock.py:0
#, python-format
msgid "The following replenishment order has been generated"
msgstr ""

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"The quantities on your purchase order indicate less than billed. You should "
"ask for a refund."
msgstr ""
"مقادیر در سفارش خرید شما نشان دهنده کمتر از صورتحساب است. شما باید درخواست "
"بازپرداخت کنید."

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid ""
"There is no matching vendor price to generate the purchase order for product"
" %s (no vendor defined, minimum quantity not reached, dates not valid, ...)."
" Go on the product form and complete the list of vendors."
msgstr ""
"هیچ قیمت فروشنده منطبقی برای ایجاد سفارش خرید برای محصول %s وجود ندارد "
"(فروشنده ای تعریف نشده است، به حداقل مقدار رسیده نیست، تاریخ ها معتبر "
"نیستند، ...). به فرم محصول بروید و لیست فروشندگان را تکمیل کنید."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"این یک مسیر دراپشیپینگ را اضافه می‌کند تا روی محصولات اعمال شود تا از "
"فروشندگان درخواست کنید که به مشتریان شما تحویل دهند. محصولی که به داپشیپ "
"ارسال می‌شود، پس از تأیید سفارش فروش، درخواست خرید برای استعلام قیمت ایجاد "
"می‌کند. این یک جریان بر اساس تقاضا است. آدرس تحویل درخواستی آدرس تحویل مشتری"
" خواهد بود و نه انبار شما."

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__picking_type_id
msgid "This will determine operation type of incoming shipment"
msgstr "این نوع عملیات محموله ورودی را تعیین می کند"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"Those dates couldn’t be modified accordingly on the receipt %s which had "
"already been validated."
msgstr ""
"این تاریخ‌ها را نمی‌توان بر اساس رسید %s که قبلاً تأیید شده بود تغییر داد."

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "Those dates have been updated accordingly on the receipt %s."
msgstr "این تاریخ ها بر این اساس در رسید %s بروز شده اند."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_total
msgid "Total Quantity"
msgstr "تعداد کل"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_picking
msgid "Transfer"
msgstr "انتقال"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"Unable to cancel purchase order %s as some receptions have already been "
"done."
msgstr "امکان لغو سفارش وجود ندارد %s چون بعضی دریافتها قبلا انجام شده."

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Validate the receipt of all ordered products."
msgstr "رسید تمام محصولات سفارش داده شده را تایید کنید."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__vendor_id
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.view_warehouse_orderpoint_tree_editable_inherited_mrp
msgid "Vendor"
msgstr "تامین کننده"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_vendor_delay_report
msgid "Vendor Delay Report"
msgstr "گزارش تاخیر فروشنده"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "Vendor Lead Time"
msgstr "زمان تحویل فروشنده"

#. module: purchase_stock
#: model_terms:ir.actions.act_window,help:purchase_stock.action_purchase_vendor_delay_report
msgid "Vendor On-time Delivery analysis"
msgstr "تجزیه و تحلیل تحویل به موقع فروشنده"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse_orderpoint__vendor_id
msgid "Vendor of this product"
msgstr "فروشنده این محصول"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__picking_type_id
msgid "Warehouse"
msgstr "انبار"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "When products are bought, they can be delivered to this warehouse"
msgstr "وقتی محصولات خرید شدند، آنها می‌توانند به این انبار تحویل شوند"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> a request for quotation is "
"created to fulfill the need.<br/>Note: This rule will be used in combination"
" with the rules<br/>of the reception route(s)"
msgstr ""

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"You cannot decrease the ordered quantity below the received quantity.\n"
"Create a return first."
msgstr ""
"شما نمی توانید مقدار سفارش داده شده را کمتر از مقدار دریافتی کاهش دهید.\n"
"ابتدا یک بازگشت ایجاد کنید."

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "You must set a Vendor Location for this partner %s"
msgstr "برای این همکار باید یک مکان فروشنده تعریف کنید %s"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "روز"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "of"
msgstr "از"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "ordered instead of"
msgstr "سفارش داده شده به جای"
