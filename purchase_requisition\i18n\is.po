# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * purchase_requisition
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-08-24 09:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Call for Tender Reference:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Date</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Description</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Product UoM</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Qty</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Reference </strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Date</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Ordering Date:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Selection Type:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Source:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Vendor </strong>"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction
msgid "Action Needed"
msgstr "Þarfnast aðgerðar"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__date_end
msgid "Agreement Deadline"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__name
msgid "Agreement Reference"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__exclusive
msgid "Agreement Selection Type"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__type_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__name
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
msgid "Agreement Type"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.res_config_settings_view_form
msgid "Agreement Types"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__account_analytic_id
msgid "Analytic Account"
msgstr "Kostnaðarreikningur"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Analytic Tags"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
#: selection:purchase.requisition,state_blanket_order:0
msgid "Bid Selection"
msgstr ""

#. module: purchase_requisition
#: model:purchase.requisition.type,name:purchase_requisition.type_single
msgid "Blanket Order"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_id
msgid "Blanket order"
msgstr ""

#. module: purchase_requisition
#: model:purchase.requisition.type,name:purchase_requisition.type_multi
msgid "Call for Tender"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.report,name:purchase_requisition.action_report_purchase_requisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel"
msgstr "Hætta við"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
#: selection:purchase.requisition,state_blanket_order:0
msgid "Cancelled"
msgstr "Afpöntuð"

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:116
#, python-format
msgid "Cancelled by the agreement associated to this quotation."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Close"
msgstr "Loka"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
#: selection:purchase.requisition,state_blanket_order:0
msgid "Closed"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__company_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__company_id
msgid "Company"
msgstr "Fyrirtæki"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm"
msgstr "Staðfesta"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: selection:purchase.requisition,state:0
#: selection:purchase.requisition,state_blanket_order:0
msgid "Confirmed"
msgstr "Samþykkt"

#. module: purchase_requisition
#: selection:product.template,purchase_requisition:0
msgid "Create a draft purchase order"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_product_product__purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_product_template__purchase_requisition
msgid ""
"Create a draft purchase order: Based on your product configuration, the "
"system will create a draft purchase order.Propose a call for tender : If the"
" 'purchase_requisition' module is installed and this option is selected, the"
" system will create a draft call for tender."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__currency_id
msgid "Currency"
msgstr "Gjaldmiðill"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
msgid "Data for new quotations"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__schedule_date
msgid "Delivery Date"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__description
msgid "Description"
msgstr "Lýsing"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: purchase_requisition
#: selection:purchase.requisition.type,line_copy:0
msgid "Do not create RfQ lines automatically"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Done"
msgstr "Lokið"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__move_dest_id
msgid "Downstream Move"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: selection:purchase.requisition,state:0
#: selection:purchase.requisition,state_blanket_order:0
msgid "Draft"
msgstr "Tillaga"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "End Date"
msgstr "End Date"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"Example of purchase agreements include call for tenders and blanket orders."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_follower_ids
msgid "Followers"
msgstr "Fylgjendur"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_channel_ids
msgid "Followers (Channels)"
msgstr "Followers (Channels)"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_partner_ids
msgid "Followers (Partners)"
msgstr "Fylgjendur (viðskiptafélagar)"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"For a blanket order, you can record an agreement for a specifc period\n"
"            (e.g. a year) and you order products within this agreement, benefiting\n"
"            from the negociated prices."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Group By"
msgstr "Hópa eftir"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__id
msgid "ID"
msgstr "Auðkenni"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_unread
msgid "If checked new messages require your attention."
msgstr "Ef merkt við þá eru ný skilaboð"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction
msgid "If checked, new messages require your attention."
msgstr "If checked, new messages require your attention."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"In a call for tenders, you can record the products you need to buy\n"
"            and generate the creation of RfQs to vendors. Once the tenders have\n"
"            been registered, you can review and compare them and you can\n"
"            validate some and cancel others."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "In negotiation"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_is_follower
msgid "Is Follower"
msgstr "Is Follower"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition____last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line____last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__line_copy
msgid "Lines"
msgstr "Lines"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_ids
msgid "Messages"
msgstr "Skilaboð"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Multiple Requisitions"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "My Agreements"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New Agreements"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "New Quotation"
msgstr "New Quotation"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of Actions"
msgstr "Fjöldi aðgerða"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__order_count
msgid "Number of Orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Fjöldi skilaboð sem bíða afgreiðslu"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_unread_counter
msgid "Number of unread messages"
msgstr "Number of unread messages"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
#: selection:purchase.requisition,state_blanket_order:0
msgid "Ongoing"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__picking_type_id
msgid "Operation Type"
msgstr "Aðgerðartegund"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__qty_ordered
msgid "Ordered Quantities"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__ordering_date
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Ordering Date"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_product__purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_template__purchase_requisition
msgid "Procurement"
msgstr "Procurement"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_procurement_group
msgid "Procurement Group"
msgstr "Innkaupagrúppa"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_product
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_id
msgid "Product"
msgstr "Vara"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_template
msgid "Product Template"
msgstr "Sniðmát vöru"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_id
msgid "Product Unit of Measure"
msgstr "Mælieining vöru"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Products"
msgstr "Vörur"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__line_ids
msgid "Products to Purchase"
msgstr ""

#. module: purchase_requisition
#: selection:product.template,purchase_requisition:0
msgid "Propose a call for tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__requisition_id
msgid "Purchase Agreement"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.tender_type_action
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_type
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_tree
msgid "Purchase Agreement Types"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_pro_mgt
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_tree
msgid "Purchase Agreements"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order
msgid "Purchase Order"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_line
msgid "Purchase Order Line"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__purchase_ids
msgid "Purchase Orders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Purchase Orders with requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__user_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Purchase Representative"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition
msgid "Purchase Requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_line
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_line_id
msgid "Purchase Requisition Line"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_type
msgid "Purchase Requisition Type"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.act_res_partner_2_purchase_order
msgid "Purchase orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__is_quantity_copy
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__is_quantity_copy
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__quantity_copy
msgid "Quantities"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_qty
msgid "Quantity"
msgstr "Magn"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "RFQs/Orders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.product_template_form_view_inherit
msgid "Reordering"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_to_so
msgid "Request for Quotation"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_list
msgid "Request for Quotations"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Requests for Quotation Details"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_stock_move__requisition_line_ids
msgid "Requisition Line"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_stock_move__requistion_line_ids
msgid "Requistion Line"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reset to Draft"
msgstr "Reset to Draft"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__schedule_date
msgid "Scheduled Date"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Search Purchase Agreements"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition.type,exclusive:0
msgid "Select multiple RFQ"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition.type,exclusive:0
msgid "Select only one RFQ (exclusive)"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_type__exclusive
msgid ""
"Select only one RFQ (exclusive):  when a purchase order is confirmed, cancel the remaining purchase order.\n"
"\n"
"                    Select multiple RFQ: allows multiple purchase orders. On confirmation of a purchase order it does not cancel the remaining orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__sequence
msgid "Sequence"
msgstr "Runa"

#. module: purchase_requisition
#: selection:purchase.requisition.type,quantity_copy:0
msgid "Set quantities manually"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Source"
msgstr "Uppruni"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__origin
msgid "Source Document"
msgstr "Upprunaskjal"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "Start a new purchase agreement"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__state_blanket_order
msgid "State Blanket Order"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__state
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Status"
msgstr "Staða"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_stock_move
msgid "Stock Move"
msgstr "Stock Move"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_stock_rule
msgid "Stock Rule"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__supplier_info_ids
msgid "Supplier Info"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Terms and Conditions"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__schedule_date
msgid ""
"The expected and scheduled delivery date where all the products are received"
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:95
#, python-format
msgid ""
"There is already an open blanket order for this supplier. We suggest you to "
"use to complete this open blanket order instead of creating a new one."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Unassigned"
msgstr "Óúthlutað"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Unassigned  Requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__price_unit
msgid "Unit Price"
msgstr "Einingarverð"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_unread
msgid "Unread Messages"
msgstr "Ólesin skilaboð"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Teljari fyrir ólesin skilaboð"

#. module: purchase_requisition
#: selection:purchase.requisition.type,line_copy:0
msgid "Use lines of agreement"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition.type,quantity_copy:0
msgid "Use quantities of agreement"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Validate"
msgstr "Staðfesta"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__vendor_id
msgid "Vendor"
msgstr "Birgir"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__warehouse_id
msgid "Warehouse"
msgstr "Vöruhús"

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:94
#, python-format
msgid "Warning for %s"
msgstr "Warning for %s"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website Messages"
msgstr "Skilaboð frá vef"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website communication history"
msgstr "Website communication history"

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:178
#, python-format
msgid "You can only delete draft requisitions."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:123
#, python-format
msgid "You cannot confirm agreement '%s' because there is no product line."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:127
#: code:addons/purchase_requisition/models/purchase_requisition.py:220
#: code:addons/purchase_requisition/models/purchase_requisition.py:228
#, python-format
msgid "You cannot confirm the blanket order without price."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:129
#, python-format
msgid "You cannot confirm the blanket order without quantity."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:156
#, python-format
msgid ""
"You have to cancel or validate every RfQ before closing the purchase "
"requisition."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "e.g. PO0025"
msgstr ""
