<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Account Tags -->
    <!-- Account Taxes Tags Ventas -->
    <record id="tag_cl_sale_mnt_exe" model="account.account.tag">
        <!-- TotMntExe -->
        <field name="name">Ventas - Total Monto Exento o No Gravado</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_sale_mnt_neto" model="account.account.tag">
        <!-- TotMntNeto -->
        <field name="name">Ventas - Total Monto Neto</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_sale_valor_neto_comis" model="account.account.tag">
        <!-- TotValComNeto -->
        <field name="name">Ventas - Total Valor Neto Comisiones y Otros Cargos</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_sale_valor_comisiones_no_afecto" model="account.account.tag">
        <!-- TotValComExe -->
        <field name="name">Ventas - Total Valor Neto Comisiones y Otros Cargos No Afectos</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_sale_monto_no_facturable" model="account.account.tag">
        <!-- TotMntNoFact -->
        <field name="name">Ventas - Total Monto No Facturable</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_sale_exento_vta_pasajes_nacional" model="account.account.tag">
        <!-- TotPsjNac -->
        <field name="name">Ventas - Exento Ventas de Pasajes Nacional</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_sale_exento_vta_pasajes_internacional" model="account.account.tag">
        <!-- TotPsjInt -->
        <field name="name">Ventas - Exento Ventas de Pasajes Internacional</field>
        <field name="applicability">accounts</field>
    </record>

    <!-- Account Taxes Tags Commpras -->
    <record id="tag_cl_purchase_mnt_exe" model="account.account.tag">
        <!-- TotMntExe -->
        <field name="name">Compras - Total Monto Exento o No Gravado</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_purchase_mnt_neto" model="account.account.tag">
        <!-- TotMntNeto -->
        <field name="name">Compras - Total Monto Neto</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_purchase_mnt_neto_uso_comun" model="account.account.tag">
        <!-- TotMntNeto -->
        <field name="name">Compras - Total Monto Neto Uso Común</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_purchase_mnt_neto_no_recup" model="account.account.tag">
        <!-- TotMntNeto -->
        <field name="name">Compras - Total Monto Neto No Recuperable</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_purchase_mnt_neto_supermercado" model="account.account.tag">
        <!-- TotMntNeto -->
        <field name="name">Compras - Total Monto Neto Compras de Supermercado</field>
        <field name="applicability">accounts</field>
    </record>


    <record id="tag_cl_purchase_mnt_neto_actf" model="account.account.tag">
        <!-- TotMntActivoFijo -->
        <field name="name">Compras - Total Monto Neto Activo Fijo</field>
        <field name="applicability">accounts</field>
    </record>


    <record id="tag_cl_purchase_tab_puros" model="account.account.tag">
        <!-- TotTabPuros -->
        <field name="name">Compras - Total Tabacos Manufacturados Puros</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_purchase_tab_cigar" model="account.account.tag">
        <!-- TotTabCigarrillos -->
        <field name="name">Compras - Total Tabacos Manufacturados Cigarrillos</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_purchase_tab_elab" model="account.account.tag">
        <!-- TotTabCigarrillos -->
        <field name="name">Compras - Total Tabacos Manufacturados Elaborados</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_remanente_cf" model="account.account.tag">
        <field name="name">Remantente de Crédito Fiscal</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_cl_impuesto_unico_trabajadores" model="account.account.tag">
        <field name="name">Impuesto Unico Trabajadores</field>
        <field name="applicability">accounts</field>
    </record>

    <!-- honorarios y otros -->
    <record id="tag_cl_fees_amount" model="account.account.tag">
        <field name="name">Honorarios -  Montos Sujetos a Retención Renta 2 Categoría</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="cl_chart_template" model="account.chart.template">
        <field name="name">Chile - Plan de Cuentas</field>
        <field name="bank_account_code_prefix">1101</field>
        <field name="cash_account_code_prefix">1101</field>
        <field name="transfer_account_code_prefix">117</field>
        <field name="code_digits">6</field>
        <field name="currency_id" ref="base.CLP"/>
        <field name="use_anglo_saxon" eval="True" />
        <field name="country_id" ref="base.cl"/>
    </record>

    <record id="account_11700" model="account.account.template">
        <field name="code">117000</field>
        <field name="name">Cuenta de Transferencia</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110210" model="account.account.template">
        <field name="code">110210</field>
        <field name="name">Depósitos en Divisas</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110220" model="account.account.template">
        <field name="code">110220</field>
        <field name="name">Acciones</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110310" model="account.account.template">
        <field name="code">110310</field>
        <field name="name">Clientes</field>
        <field ref="account.data_account_type_receivable" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_11320" model="account.account.template">
        <field name="code">110320</field>
        <field name="name">Anticipo Proveedores</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_11410" model="account.account.template">
        <field name="code">110410</field>
        <field name="name">Cheques a Fecha Por Cobrar</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110420" model="account.account.template">
        <field name="code">110420</field>
        <field name="name">Deudores Varios</field>
        <field ref="account.data_account_type_receivable" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110421" model="account.account.template">
        <field name="code">110421</field>
        <field name="name">Deudores por Ventas (Pos)</field>
        <field ref="account.data_account_type_receivable" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>


    <record id="account_110430" model="account.account.template">
        <field name="code">110430</field>
        <field name="name">Boletas en Garantía</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110440" model="account.account.template">
        <field name="code">110440</field>
        <field name="name">Letras en Cartera</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110450" model="account.account.template">
        <field name="code">110450</field>
        <field name="name">Documentos Protestados</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110510" model="account.account.template">
        <field name="code">110510</field>
        <field name="name">Anticipo de Sueldo</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110520" model="account.account.template">
        <field name="code">110520</field>
        <field name="name">Préstamos otorgados</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110530" model="account.account.template">
        <field name="code">110530</field>
        <field name="name">Anticipos de Viáticos</field>
        <field ref="account.data_account_type_prepayments" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110540" model="account.account.template">
        <field name="code">110540</field>
        <field name="name">Fondos x Rendir</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110550" model="account.account.template">
        <field name="code">110550</field>
        <field name="name">Anticipo de Honorarios</field>
        <field ref="account.data_account_type_prepayments" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110560" model="account.account.template">
        <field name="code">110560</field>
        <field name="name">Anticipo de Aguinaldo</field>
        <field ref="account.data_account_type_prepayments" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110570" model="account.account.template">
        <field name="code">110570</field>
        <field name="name">Asignación Familiar</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110580" model="account.account.template">
        <field name="code">110580</field>
        <field name="name">Anticipo de Impuestos</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110585" model="account.account.template">
        <field name="code">110585</field>
        <field name="name">Alquileres Pagados por Adelantado</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110590" model="account.account.template">
        <field name="code">110590</field>
        <field name="name">Intereses Pagados por Adelantado</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110610" model="account.account.template">
        <field name="code">110610</field>
        <field name="name">Mercaderías</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110612" model="account.account.template">
        <field name="code">110612</field>
        <field name="name">Materia Prima</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110615" model="account.account.template">
        <field name="code">110615</field>
        <field name="name">Materiales</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110620" model="account.account.template">
        <field name="code">110620</field>
        <field name="name">Insumos</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110625" model="account.account.template">
        <field name="code">110625</field>
        <field name="name">Equipos</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110630" model="account.account.template">
        <field name="code">110630</field>
        <field name="name">Repuestos</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110640" model="account.account.template">
        <field name="code">110640</field>
        <field name="name">Existencias en Tránsito</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110650" model="account.account.template">
        <field name="code">110650</field>
        <field name="name">Productos Fabricados</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110660" model="account.account.template">
        <field name="code">110660</field>
        <field name="name">Productos En Proceso</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110670" model="account.account.template">
        <field name="code">110670</field>
        <field name="name">Envases</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110710" model="account.account.template">
        <field name="code">110710</field>
        <field name="name">IVA Crédito Fiscal</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110720" model="account.account.template">
        <field name="code">110720</field>
        <field name="name">Remanente Crédito Fiscal</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids" eval="[(6,0,[ref('tag_cl_remanente_cf')])]"/>
    </record>

    <record id="account_110730" model="account.account.template">
        <field name="code">110730</field>
        <field name="name">Crédito por Activo Fijo</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110740" model="account.account.template">
        <field name="code">110740</field>
        <field name="name">P.P.M. / Art 33 BIS</field>
        <field ref="account.data_account_type_receivable" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110750" model="account.account.template">
        <field name="code">110750</field>
        <field name="name">Crédito Sence</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110810" model="account.account.template">
        <field name="code">110810</field>
        <field name="name">Anticipos Comercio Exterior</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110820" model="account.account.template">
        <field name="code">110820</field>
        <field name="name">Gastos Anticipados</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110830" model="account.account.template">
        <field name="code">110830</field>
        <field name="name">Organización y Puesta en Marcha</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110910" model="account.account.template">
        <field name="code">110910</field>
        <field name="name">Retiros Socios</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_110920" model="account.account.template">
        <field name="code">110920</field>
        <field name="name">Retiros Reinversión</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_111010" model="account.account.template">
        <field name="code">111010</field>
        <field name="name">Gastos Reorganización</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_111110" model="account.account.template">
        <field name="code">111110</field>
        <field name="name">Cuentas Obligadas Socios</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_111210" model="account.account.template">
        <field name="code">111210</field>
        <field name="name">Gastos Tributarios</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_111310" model="account.account.template">
        <field name="code">111310</field>
        <field name="name">Cuenta Importaciones</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_111410" model="account.account.template">
        <field name="code">111410</field>
        <field name="name">Gastos Rechazados</field>
        <field ref="account.data_account_type_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_120110" model="account.account.template">
        <field name="code">120110</field>
        <field name="name">Deudores Morosos</field>
        <field ref="account.data_account_type_receivable" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_120120" model="account.account.template">
        <field name="code">120120</field>
        <field name="name">Deudores en Gestión Judicial</field>
        <field ref="account.data_account_type_receivable" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_121110" model="account.account.template">
        <field name="code">121110</field>
        <field name="name">Equipos y Mobiliario de Oficina</field>
        <field ref="account.data_account_type_fixed_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[ref('tag_cl_purchase_mnt_neto_actf')])]"/>
    </record>

    <record id="account_121120" model="account.account.template">
        <field name="code">121120</field>
        <field name="name">Equipos Computacionales</field>
        <field ref="account.data_account_type_fixed_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[ref('tag_cl_purchase_mnt_neto_actf')])]"/>
    </record>

    <record id="account_121130" model="account.account.template">
        <field name="code">121130</field>
        <field name="name">Vehículos</field>
        <field ref="account.data_account_type_fixed_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[ref('tag_cl_purchase_mnt_neto_actf')])]"/>
    </record>

    <record id="account_121140" model="account.account.template">
        <field name="code">121140</field>
        <field name="name">Maquinaria</field>
        <field ref="account.data_account_type_fixed_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[ref('tag_cl_purchase_mnt_neto_actf')])]"/>
    </record>

    <record id="account_121210" model="account.account.template">
        <field name="code">121210</field>
        <field name="name">Bienes Raíces</field>
        <field ref="account.data_account_type_fixed_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[ref('tag_cl_purchase_mnt_neto_actf')])]"/>
    </record>

    <record id="account_121310" model="account.account.template">
        <field name="code">121310</field>
        <field name="name">Depreciación Acum Equipos y Mob de Oficina</field>
        <field ref="account.data_account_type_fixed_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_121320" model="account.account.template">
        <field name="code">121320</field>
        <field name="name">Depreciación Acum Equipos Computacionales</field>
        <field ref="account.data_account_type_fixed_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_121330" model="account.account.template">
        <field name="code">121330</field>
        <field name="name">Depreciación Acum Otros Activos</field>
        <field ref="account.data_account_type_fixed_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[ref('tag_cl_purchase_mnt_neto_actf')])]"/>
    </record>

    <record id="account_130110" model="account.account.template">
        <field name="code">130110</field>
        <field name="name">Activo Intangible - Derecho de Llaves</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_130112" model="account.account.template">
        <field name="code">130112</field>
        <field name="name">Activo Intangible - Concesiones y Franquicias</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_130114" model="account.account.template">
        <field name="code">130114</field>
        <field name="name">Activo Intangible - Marcas y Patentes de Invención</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_130116" model="account.account.template">
        <field name="code">130116</field>
        <field name="name">Activo Intangible - (-) Amortización Acumulada</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_130120" model="account.account.template">
        <field name="code">130120</field>
        <field name="name">Derechos Otras Empresas</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_130130" model="account.account.template">
        <field name="code">130130</field>
        <field name="name">Cuentas por Cobrar a Personas y Empresas Relacionadas Largo Plazo</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_130140" model="account.account.template">
        <field name="code">130140</field>
        <field name="name">Documentos y Cuentas por Cobrar a Largo Plazo</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_130150" model="account.account.template">
        <field name="code">130150</field>
        <field name="name">Garantías de Obligaciones a Largo Plazo y de Obligaciones de Terceros</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_130160" model="account.account.template">
        <field name="code">130160</field>
        <field name="name">Títulos Patrimoniales Bolsas de Productos</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_190110" model="account.account.template">
        <field name="code">190110</field>
        <field name="name">Boletas de Garantía</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_190210" model="account.account.template">
        <field name="code">190210</field>
        <field name="name">Letras Descontadas</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_190310" model="account.account.template">
        <field name="code">190310</field>
        <field name="name">Documentos en Garantía</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_190410" model="account.account.template">
        <field name="code">190410</field>
        <field name="name">Acciones Suscritas</field>
        <field ref="account.data_account_type_non_current_assets" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210110" model="account.account.template">
        <field name="code">210110</field>
        <field name="name">Tarjeta de Crédito Corporativa</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210120" model="account.account.template">
        <field name="code">210120</field>
        <field name="name">Linea de Crédito Bancaria</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210140" model="account.account.template">
        <field name="code">210140</field>
        <field name="name">Crédito Comercial C/Plazo</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210150" model="account.account.template">
        <field name="code">210150</field>
        <field name="name">Obligaciones Leasing Corto Plazo</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210160" model="account.account.template">
        <field name="code">210160</field>
        <field name="name">Crédito Hipotecario C/Plazo</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210170" model="account.account.template">
        <field name="code">210170</field>
        <field name="name">Crédito Comercial LP Venc. C/Plazo</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210180" model="account.account.template">
        <field name="code">210180</field>
        <field name="name">Dividendos por Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210190" model="account.account.template">
        <field name="code">210190</field>
        <field name="name">Intereses a Devengar por Compras al Crédito</field>
        <field ref="account.data_account_type_payable" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210195" model="account.account.template">
        <field name="code">210195</field>
        <field name="name">Intereses a pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210210" model="account.account.template">
        <field name="code">210210</field>
        <field name="name">Proveedores</field>
        <field ref="account.data_account_type_payable" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>


    <record id="account_210220" model="account.account.template">
        <field name="code">210220</field>
        <field name="name">Anticipo de Clientes</field>
        <field ref="account.data_account_type_payable" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210230" model="account.account.template">
        <field name="code">210230</field>
        <field name="name">Facturas por Recibir</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210310" model="account.account.template">
        <field name="code">210310</field>
        <field name="name">Cheques Girados y No Cobrados</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210320" model="account.account.template">
        <field name="code">210320</field>
        <field name="name">Documentos en Garantía</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210330" model="account.account.template">
        <field name="code">210330</field>
        <field name="name">Boleta en Garantia</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210410" model="account.account.template">
        <field name="code">210410</field>
        <field name="name">AFP x Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210420" model="account.account.template">
        <field name="code">210420</field>
        <field name="name">C.C.A.F. x Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210430" model="account.account.template">
        <field name="code">210430</field>
        <field name="name">INP x Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210440" model="account.account.template">
        <field name="code">210440</field>
        <field name="name">ISAPRES x Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210450" model="account.account.template">
        <field name="code">210450</field>
        <field name="name">Mutual Seg. x Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210510" model="account.account.template">
        <field name="code">210510</field>
        <field name="name">Sueldos por Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210520" model="account.account.template">
        <field name="code">210520</field>
        <field name="name">Honorarios por Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210550" model="account.account.template">
        <field name="code">210550</field>
        <field name="name">Rendiciones por Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210560" model="account.account.template">
        <field name="code">210560</field>
        <field name="name">Provisión de Vacaciones</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210565" model="account.account.template">
        <field name="code">210565</field>
        <field name="name">Provisión por Finiquitos</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210610" model="account.account.template">
        <field name="code">210610</field>
        <field name="name">Provisión de Impuesto PPM</field>
        <field ref="account.data_account_type_non_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210620" model="account.account.template">
        <field name="code">210620</field>
        <field name="name">Otras Provisiones</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210710" model="account.account.template">
        <field name="code">210710</field>
        <field name="name">IVA Débito Fiscal</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210720" model="account.account.template">
        <field name="code">210720</field>
        <field name="name">PPM por Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210730" model="account.account.template">
        <field name="code">210730</field>
        <field name="name">Impuesto Único Trabajadores</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids" eval="[(6,0,[ref('tag_cl_impuesto_unico_trabajadores')])]"/>
    </record>

    <record id="account_210740" model="account.account.template">
        <field name="code">210740</field>
        <field name="name">Impuesto Retención Segunda Categoría</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210715" model="account.account.template">
        <field name="code">210715</field>
        <field name="name">IVA Retenido a terceros</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210750" model="account.account.template">
        <field name="code">210750</field>
        <field name="name">Impuesto Renta 1a Categoría por Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_210760" model="account.account.template">
        <field name="code">210760</field>
        <field name="name">Impuesto Mensuales por Pagar</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_220120" model="account.account.template">
        <field name="code">220120</field>
        <field name="name">Obligaciones Leasing L/Plazo</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_220130" model="account.account.template">
        <field name="code">220130</field>
        <field name="name">Crédito Comercial L/Plazo</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_220210" model="account.account.template">
        <field name="code">220210</field>
        <field name="name">Provisión Indemnización por Años de Servicio</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_220310" model="account.account.template">
        <field name="code">220310</field>
        <field name="name">Cta Corriente Empresa Relacionada</field>
        <field ref="account.data_account_type_non_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_220320" model="account.account.template">
        <field name="code">220320</field>
        <field name="name">Impuesto Diferido Largo Plazo</field>
        <field ref="account.data_account_type_non_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_220330" model="account.account.template">
        <field name="code">220330</field>
        <field name="name">Otros Pasivos</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230110" model="account.account.template">
        <field name="code">230110</field>
        <field name="name">Capital Social</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230120" model="account.account.template">
        <field name="code">230120</field>
        <field name="name">Acciones en Circulación</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230130" model="account.account.template">
        <field name="code">230130</field>
        <field name="name">Dividendos a Distribuir en Acciones</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230140" model="account.account.template">
        <field name="code">230140</field>
        <field name="name">Descuento de Emisión de Acciones</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230210" model="account.account.template">
        <field name="code">230210</field>
        <field name="name">Revalorización Capital Propio</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230220" model="account.account.template">
        <field name="code">230220</field>
        <field name="name">Otras Revalorizaciones</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230230" model="account.account.template">
        <field name="code">230230</field>
        <field name="name">Revalorización Activo Fijo</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230240" model="account.account.template">
        <field name="code">230240</field>
        <field name="name">Fluctuación de Valores</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230250" model="account.account.template">
        <field name="code">230250</field>
        <field name="name">Reserva Legal</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230260" model="account.account.template">
        <field name="code">230260</field>
        <field name="name">Reserva Estatutaria</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230270" model="account.account.template">
        <field name="code">230270</field>
        <field name="name">Reserva Facultativa</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230280" model="account.account.template">
        <field name="code">230280</field>
        <field name="name">Reserva para Renovación de Activo Fijo</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230290" model="account.account.template">
        <field name="code">230290</field>
        <field name="name">Resultados Acumulados</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230300" model="account.account.template">
        <field name="code">230300</field>
        <field name="name">Utilidades y Pérdidas del Ejercicio</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230310" model="account.account.template">
        <field name="code">230310</field>
        <field name="name">Resultados Acumulados del Ejercicio Anterior</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_230510" model="account.account.template">
        <field name="code">230510</field>
        <field name="name">Resultado del Ejercicio</field>
        <field ref="account.data_account_type_equity" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_290110" model="account.account.template">
        <field name="code">290110</field>
        <field name="name">Cuenta Puente</field>
        <field ref="account.data_account_type_non_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="True"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_290210" model="account.account.template">
        <field name="code">290210</field>
        <field name="name">Responsable Boletas Garantía</field>
        <field ref="account.data_account_type_non_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_290220" model="account.account.template">
        <field name="code">290220</field>
        <field name="name">Responsable Documentos Garantía</field>
        <field ref="account.data_account_type_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_290230" model="account.account.template">
        <field name="code">290230</field>
        <field name="name">Responsable Letras Descontadas</field>
        <field ref="account.data_account_type_non_current_liabilities" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_310110" model="account.account.template">
        <field name="code">310110</field>
        <field name="name">Ingresos por Consultoría</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_310115" model="account.account.template">
        <field name="code">310115</field>
        <field name="name">Ventas de Productos</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[
                         ref('tag_cl_sale_mnt_neto'),
                         ref('tag_cl_sale_valor_neto_comis')
               ])]"/>
    </record>

    <record id="account_310120" model="account.account.template">
        <field name="code">310120</field>
        <field name="name">Ventas de Servicios</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[
                         ref('tag_cl_sale_mnt_exe'),
                         ref('tag_cl_sale_valor_comisiones_no_afecto'),
                         ref('tag_cl_sale_exento_vta_pasajes_nacional'),
                         ref('tag_cl_sale_exento_vta_pasajes_internacional')
               ])]"/>
    </record>

    <record id="account_310125" model="account.account.template">
        <field name="code">310125</field>
        <field name="name">Ventas de Exportación</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_310130" model="account.account.template">
        <field name="code">310130</field>
        <field name="name">Comisiones Percibidas por Ventas</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320210" model="account.account.template">
        <field name="code">320210</field>
        <field name="name">Utilidad Venta Activo Fijo</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320220" model="account.account.template">
        <field name="code">320220</field>
        <field name="name">Intereses Percibidos Sobre Préstamos Otorgados</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320225" model="account.account.template">
        <field name="code">320225</field>
        <field name="name">Arriendos ganados, obtenidos, percibidos</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320230" model="account.account.template">
        <field name="code">320230</field>
        <field name="name">Descuentos ganados, obtenidos, percibidos</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320235" model="account.account.template">
        <field name="code">320235</field>
        <field name="name">Intereses sobre Inversiones</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320240" model="account.account.template">
        <field name="code">320240</field>
        <field name="name">Ganancia Venta de Acciones</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320245" model="account.account.template">
        <field name="code">320245</field>
        <field name="name">Recupero de Rezagos</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320250" model="account.account.template">
        <field name="code">320250</field>
        <field name="name">Recupero de Deudores Incobrables</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320255" model="account.account.template">
        <field name="code">320255</field>
        <field name="name">Donaciones obtenidas, ganandas, percibidas</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320260" model="account.account.template">
        <field name="code">320260</field>
        <field name="name">Ganancia Venta Inversiones Permanentes</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320265" model="account.account.template">
        <field name="code">320265</field>
        <field name="name">Diferencia tipo de cambio</field>
        <field ref="account.data_account_type_revenue" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320270" model="account.account.template">
        <field name="code">320270</field>
        <field name="name">Cŕeditos a Largo Plazo</field>
        <field ref="account.data_account_type_other_income" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320275" model="account.account.template">
        <field name="code">320275</field>
        <field name="name">Otros Ingresos</field>
        <field ref="account.data_account_type_other_income" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320280" model="account.account.template">
        <field name="code">320280</field>
        <field name="name">Corrección Monetaria Activos</field>
        <field ref="account.data_account_type_other_income" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_320285" model="account.account.template">
        <field name="code">320285</field>
        <field name="name">Corrección Monetaria Bienes Leasing</field>
        <field ref="account.data_account_type_other_income" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410110" model="account.account.template">
        <field name="code">410110</field>
        <field name="name">Remuneraciones Operación</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410115" model="account.account.template">
        <field name="code">410115</field>
        <field name="name">Aporte Patronal Operación</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410120" model="account.account.template">
        <field name="code">410120</field>
        <field name="name">Viáticos</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410125" model="account.account.template">
        <field name="code">410125</field>
        <field name="name">Capacitación</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410130" model="account.account.template">
        <field name="code">410130</field>
        <field name="name">Honorarios Pagados</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410135" model="account.account.template">
        <field name="code">410135</field>
        <field name="name">Asesorías</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410140" model="account.account.template">
        <field name="code">410140</field>
        <field name="name">Arriendos Oficina</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410145" model="account.account.template">
        <field name="code">410145</field>
        <field name="name">Arriendos Bodega</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410150" model="account.account.template">
        <field name="code">410150</field>
        <field name="name">Comunicaciones</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410155" model="account.account.template">
        <field name="code">410155</field>
        <field name="name">Servicios Legales</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410160" model="account.account.template">
        <field name="code">410160</field>
        <field name="name">Manutención y Reparación de Activos</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410165" model="account.account.template">
        <field name="code">410165</field>
        <field name="name">Papelería-Aseo-Gastos Diversos</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410170" model="account.account.template">
        <field name="code">410170</field>
        <field name="name">Remuneraciones Administración</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410175" model="account.account.template">
        <field name="code">410175</field>
        <field name="name">Aporte Patronal Administración</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410180" model="account.account.template">
        <field name="code">410180</field>
        <field name="name">Electricidad</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410185" model="account.account.template">
        <field name="code">410185</field>
        <field name="name">Gastos Comunes</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410190" model="account.account.template">
        <field name="code">410190</field>
        <field name="name">Gastos Bancarios</field>
        <field ref="account.data_account_off_sheet" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410195" model="account.account.template">
        <field name="code">410195</field>
        <field name="name">Diferencia Tipo de Cambio</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410200" model="account.account.template">
        <field name="code">410200</field>
        <field name="name">Corrección Monetaria</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410205" model="account.account.template">
        <field name="code">410205</field>
        <field name="name">Multas Fiscales</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410210" model="account.account.template">
        <field name="code">410210</field>
        <field name="name">Impuesto a la Renta 1ra Categoría</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410215" model="account.account.template">
        <field name="code">410215</field>
        <field name="name">Pérdida por Venta Activo</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410220" model="account.account.template">
        <field name="code">410220</field>
        <field name="name">Ajuste Ejercicio Anterior</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410225" model="account.account.template">
        <field name="code">410225</field>
        <field name="name">Donación</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410230" model="account.account.template">
        <field name="code">410230</field>
        <field name="name">Compras Productos 1ra Categoría</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[ref('tag_cl_purchase_mnt_neto'), ref('tag_cl_purchase_tab_puros'), ref('tag_cl_purchase_tab_cigar')])]"/>
    </record>
    <record id="account_410231" model="account.account.template">
        <field name="code">410231</field>
        <field name="name">Compras Netas (IVA Uso Común)</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[ref('tag_cl_purchase_mnt_neto_uso_comun')])]"/>
    </record>
    <record id="account_410232" model="account.account.template">
        <field name="code">410232</field>
        <field name="name">Compras Netas (IVA No Recuperable)</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[ref('tag_cl_purchase_mnt_neto_no_recup')])]"/>
    </record>
    <record id="account_410233" model="account.account.template">
        <field name="code">410233</field>
        <field name="name">Compras de Supermercado</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[ref('tag_cl_purchase_mnt_neto_supermercado')])]"/>
    </record>

    <record id="account_410235" model="account.account.template">
        <field name="code">410235</field>
        <field name="name">Costo de Mercaderías Vendidas - Prod. 1ra Categoría</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="tag_ids"
               eval="[(6,0,[ref('tag_cl_purchase_mnt_neto'), ref('tag_cl_purchase_tab_puros'), ref('tag_cl_purchase_tab_cigar')])]"/>
    </record>

    <record id="account_410240" model="account.account.template">
        <field name="code">410240</field>
        <field name="name">Importaciones</field>
        <field ref="account.data_account_type_direct_costs" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410245" model="account.account.template">
        <field name="code">410245</field>
        <field name="name">CIF</field>
        <field ref="account.data_account_type_direct_costs" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410250" model="account.account.template">
        <field name="code">410250</field>
        <field name="name">Compras de Materia Prima</field>
        <field ref="account.data_account_type_direct_costs" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_410255" model="account.account.template">
        <field name="code">410255</field>
        <field name="name">Mano de Obra Directa</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_420110" model="account.account.template">
        <field name="code">420110</field>
        <field name="name">Mercaderias Recibidas en Consignación</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_440210" model="account.account.template">
        <field name="code">440210</field>
        <field name="name">Comitente por Mercaderias Recibidas en Consignación</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_420120" model="account.account.template">
        <field name="code">420120</field>
        <field name="name">Gastos en Depreciación de Activo Fijo</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_420140" model="account.account.template">
        <field name="code">420140</field>
        <field name="name">Gastos en Amortización</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_420150" model="account.account.template">
        <field name="code">420150</field>
        <field name="name">Gastos en Siniestros</field>
        <field ref="account.data_account_type_expenses" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_420170" model="account.account.template">
        <field name="code">420170</field>
        <field name="name">Garantias Otorgadas</field>
        <field ref="account.data_account_off_sheet" name="user_type_id"/>
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="account_420220" model="account.account.template">
        <field name="code">420220</field>
        <field name="name">Gastos Otros Impuestos</field>
        <field name="user_type_id" ref="account.data_account_type_expenses" />
        <field name="reconcile" eval="False"/>
        <field name="chart_template_id" ref="cl_chart_template"/>
    </record>

    <record id="cl_chart_template" model="account.chart.template">
        <field name="property_account_receivable_id" ref="account_110310"/>
        <field name="property_account_payable_id" ref="account_210210"/>
        <field name="property_account_expense_categ_id" ref="account_410235"/>
        <field name="property_account_income_categ_id" ref="account_310115"/>
        <field name="income_currency_exchange_account_id" ref="account_410195"/>
        <field name="expense_currency_exchange_account_id" ref="account_410195"/>
        <field name="default_pos_receivable_account_id" ref="account_110421"/>
        <field name="property_stock_account_input_categ_id" ref="account_210230"/>
        <field name="property_stock_account_output_categ_id" ref="account_110640"/>
        <field name="property_stock_valuation_account_id" ref="account_110610"/>
    </record>

</odoo>
