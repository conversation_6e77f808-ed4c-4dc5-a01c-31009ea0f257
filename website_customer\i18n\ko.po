# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_customer
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr "<span class=\"fa fa-1x fa-tags\"/>전체"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"
msgstr ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__active
msgid "Active"
msgstr "활성"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "모든 국가"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Industries"
msgstr "모든 산업 분야"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Archived"
msgstr "보관됨"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__classname
msgid "Bootstrap class to customize the color"
msgstr "색상을 사용자 정의하기 위한 부트스트랩 클래스"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__can_publish
msgid "Can Publish"
msgstr "게시 가능"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__name
msgid "Category Name"
msgstr "범주명"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__classname
msgid "Class"
msgstr "클래스"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "Close"
msgstr "닫기"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Create a new contact tag"
msgstr "새 연락처 태그 만들기"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_uid
msgid "Created by"
msgstr "작성자"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_date
msgid "Created on"
msgstr "작성일자"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__display_name
msgid "Display Name"
msgstr "표시명"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__id
msgid "ID"
msgstr "ID"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "Implemented By"
msgstr "구현자"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__is_published
msgid "Is Published"
msgstr "게시 여부"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage contact tags to better classify them for tracking and analysis "
"purposes."
msgstr "연락처 태그를 추적 및 분석 목적으로 더 잘 분류하도록 관리합니다."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No result found"
msgstr "결과가 없습니다."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our References"
msgstr "우리의 참고 문헌"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr "협력사 태그"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ..."
msgstr "협력사 태그 - 이 태그는 섹터별로 고객을 찾기 위해 웹 사이트에서 사용할 수 있습니다 ..."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__partner_ids
msgid "Partners"
msgstr "협력사"

#. module: website_customer
#: code:addons/website_customer/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
#, python-format
msgid "References"
msgstr "참조"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "References by Country"
msgstr "국가별 참조"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_industry_list
msgid "References by Industries"
msgstr "산업별 참고 사례"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "References by Tag"
msgstr "태그별 참조"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "검색"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Search Partner Tag"
msgstr "협력사 태그 검색"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__website_url
msgid "The full URL to access the document through the website."
msgstr "웹사이트를 통해 문서에 접근 하는 전체 URL입니다."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Trusted by millions worldwide"
msgstr "전 세계적으로 수백만에 의해 신뢰할 수 있는"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_published
msgid "Visible on current website"
msgstr "현재 웹 사이트에 공개"

#. module: website_customer
#: model:ir.model,name:website_customer.model_website
msgid "Website"
msgstr "웹사이트"

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr "웹사이트 태그"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_url
msgid "Website URL"
msgstr "웹 사이트 URL"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner__website_tag_ids
#: model:ir.model.fields,field_description:website_customer.field_res_users__website_tag_ids
msgid "Website tags"
msgstr "웹사이트 태그"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr "세계 지도"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "reference(s))"
msgstr "참조"
