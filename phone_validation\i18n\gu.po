# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * phone_validation
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-09-21 13:18+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: phone_validation
#: selection:res.company,phone_international_format:0
msgid "Add international prefix"
msgstr ""

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_res_company__phone_international_format
msgid ""
"Always encode phone numbers using international format. Otherwise numbers "
"coming from the company's country are nationaly formatted. International "
"numbers are always using international format."
msgstr ""

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_res_company
msgid "Companies"
msgstr "કંપનીઓ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_validation_mixin__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_validation_mixin__id
msgid "ID"
msgstr "ઓળખ"

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:23
#, python-format
msgid "Impossible number %s: probably invalid number of digits"
msgstr ""

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:25
#, python-format
msgid "Invalid number %s: probably incorrect prefix"
msgstr ""

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_validation_mixin____last_update
msgid "Last Modified on"
msgstr ""

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_res_company__phone_international_format
msgid "Local Numbers"
msgstr ""

#. module: phone_validation
#: selection:res.company,phone_international_format:0
msgid "No prefix"
msgstr ""

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_validation_mixin
msgid "Phone Validation Mixin"
msgstr ""

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:44
#, python-format
msgid ""
"Unable to format %s:\n"
"%s"
msgstr ""

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:20
#, python-format
msgid ""
"Unable to parse %s:\n"
"%s"
msgstr ""
