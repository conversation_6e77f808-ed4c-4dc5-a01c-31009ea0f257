# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * gamification
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:27+0000\n"
"PO-Revision-Date: 2017-10-02 11:27+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Bolivia) (https://www.transifex.com/odoo/teams/41243/es_BO/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_BO\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: gamification
#: model:mail.template,body_html:gamification.simple_report_template
msgid ""
"\n"
"                 \n"
"% set record = ctx.get('record')\n"
"% set company = record and record.company_id or user.company_id\n"
"% set challenge_lines = ctx.get('challenge_lines', [])     \n"
"<table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"font-family: 'Helvetica'; background-color: #EEE; border-collapse: collapse;\">\n"
"<tr>\n"
"    <td valign=\"top\" align=\"center\">\n"
"        <table cellspacing=\"0\" cellpadding=\"0\" width=\"600\" style=\"margin: 0 auto; width: 600px;\">\n"
"            <tr><td>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n"
"                    <tr>\n"
"                        <td valign=\"middle\" align=\"left\" style=\"padding:30px 15px;\">\n"
"                            <img width=\"90\" src=\"/logo.png?company=${user.company_id.id}\" style=\"width:80px;\"/>\n"
"                        </td>\n"
"                        % if object.visibility_mode == 'ranking':\n"
"                            <td style=\"padding:15px;\">\n"
"                                <p style=\"font-size:20px;color:#666666;\" align=\"right\">Leaderboard</p>\n"
"                            </td>\n"
"                        % endif\n"
"                    </tr>\n"
"                </table>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" bgcolor=\"#fff\" style=\"background-color:#fff;\">\n"
"                    <tr><td style=\"padding: 15px;\">\n"
"                        % if object.visibility_mode == 'personal':\n"
"                            <span style=\"color:#666666;font-size:13px;\">Here is your current progress in the challenge <strong>${object.name}</strong>.</span>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:20px;\">\n"
"                                <tr>\n"
"                                    <td align=\"center\">\n"
"                                        <p style=\"font-size:27px;color:#666666;\">Personal Performance</p>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;color:#666666;\">\n"
"                                <thead>\n"
"                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                        <th align=\"left\" style=\"padding-bottom: 0px;width:40%;text-align:left;\">Goals</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"left\">Target</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Current</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Completeness</th>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"></td>\n"
"                                    </tr>\n"
"                                </thead>\n"
"                                <tbody>\n"
"                                % for line in challenge_lines:\n"
"                                    <tr\n"
"                                        % if line['completeness'] >= 100:\n"
"                                            style=\"font-weight:bold;\"\n"
"                                        % endif\n"
"                                        >\n"
"                                        <td style=\"padding: 20px 0;\" align=\"left\">\n"
"                                            ${line['name']}\n"
"                                            % if line['suffix'] or line['monetary']:\n"
"                                                (${line['full_suffix']})\n"
"                                            % endif\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\">${\"%.2f\" % line['target']}\n"
"                                            % if line['suffix']:\n"
"                                                ${line['suffix']}\n"
"                                            % endif\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\">${\"%.2f\" % line['current']}\n"
"                                            % if line['suffix']:\n"
"                                                ${line['suffix']}\n"
"                                            % endif\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;font-size:25px;color:#9A6C8E;\" align=\"right\"><strong>${line['completeness']| int}%</strong></td>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#e3e3e3;\"></td>\n"
"                                    </tr>\n"
"                                % endfor        \n"
"                                </tbody>\n"
"                            </table>                   \n"
"                        % else: \n"
"                            <span style=\"color:#A8A8A8;font-size:13px;\">\n"
"                                Challenge: <strong>${object.name}</strong>.\n"
"                            </span> \n"
"                            % for line in challenge_lines:\n"
"                                <!-- Header + Button table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\"style=\"margin-top:35px;\">\n"
"                                    <tr>\n"
"                                        <td width=\"50%\">\n"
"                                            <p style=\"font-size:22px;color:#666666;\">Top Achievers for goal <strong>${line['name']}</strong></p>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table>\n"
"                                <!-- Podium -->\n"
"                                % if len(line['goals'])>2:\n"
"                                    <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:10px;\">\n"
"                                        <tr><td style=\"padding:0 30px;\">\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"table-layout: fixed;\">\n"
"                                                <tr>\n"
"                                                    % set top_goals = [line['goals'][1], line['goals'][0], line['goals'][2]]\n"
"                                                    % for goal in top_goals:\n"
"                                                        <td align=\"center\" style=\"width:32%;\">\n"
"                                                            % if loop.index == 1:\n"
"                                                                % set extra_div = '<div style=\"height:40px;\"></div>'\n"
"                                                                % set heightA = 95\n"
"                                                                % set heightB = 75\n"
"                                                                % set bgColor = '#b898b0'\n"
"                                                                % set fontSize = 50\n"
"                                                                % set podiumPosition = '2'\n"
"                                                            % elif loop.index == 2:\n"
"                                                                % set extra_div = ''\n"
"                                                                % set heightA = 55\n"
"                                                                % set heightB = 115\n"
"                                                                % set bgColor = '#9A6C8E'\n"
"                                                                % set fontSize = 85\n"
"                                                                % set podiumPosition = '1'\n"
"                                                            % elif loop.index == 3:\n"
"                                                                % set extra_div = '<div style=\"height:60px;\"></div>'\n"
"                                                                % set heightA = 115\n"
"                                                                % set heightB = 55\n"
"                                                                % set bgColor = '#c8afc1'\n"
"                                                                % set fontSize = 35\n"
"                                                                % set podiumPosition = '3'\n"
"                                                            % endif\n"
"                                                            <div style=\"margin:0 3px 0 3px;height:220px;\">\n"
"                                                                <div style=\"height:${heightA}px;\">\n"
"                                                                    ${extra_div | safe}   \n"
"                                                                    <div style=\"height:55px;\">\n"
"                                                                        % set path = object.env['res.users'].browse(goal['user_id']).partner_id.image_small\n"
"                                                                        <img style=\"margin-bottom:5px;width:50px;height:50px;border-radius:50%;\" src=\"data:image/jpeg;base64,${path}\" alt=\"${goal['name']}\"/>\n"
"                                                                    </div> \n"
"                                                                    <div align=\"center\" style =\"color:${bgColor};height:20px\">\n"
"                                                                        ${goal['name']}\n"
"                                                                    </div \n"
"                                                                </div>\n"
"                                                                <div style=\"background-color:${bgColor | safe};height:${heightB}px;\">\n"
"                                                                    <strong><span style=\"color:#fff;font-size:${fontSize}px;\">${podiumPosition | safe}</span></strong>\n"
"                                                                </div>\n"
"                                                                <div style=\"height:30px;\">\n"
"                                                                    <p style=\"color:#9A6C8E;font-size:20px;margin-top:10px;\">\n"
"                                                                        ${\"%.2f\" % goal['current']}\n"
"                                                                        % if line['suffix'] or line['monetary']:\n"
"                                                                            ${line['full_suffix']}\n"
"                                                                        % endif\n"
"                                                                    </p>\n"
"                                                                </div>\n"
"                                                            </div>\n"
"                                                        </td>\n"
"                                                    % endfor\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                            </td>\n"
"                                        </tr>\n"
"                                    </table>\n"
"                                % endif\n"
"                                <!-- data table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-bottom:5px\">\n"
"                                    <tr>\n"
"                                        <td>\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;margin-bottom:5px;color:#666666;\">\n"
"                                                <thead>\n"
"                                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                                        <th style=\"width:15%;text-align:center;\">Rank</th>\n"
"                                                        <th style=\"width:25%;text-align:left;\">Name</th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Performance \n"
"                                                            % if line['suffix']:\n"
"                                                                (${line['suffix']})\n"
"                                                            % elif line['monetary']:\n"
"                                                                (${company.currency_id.symbol})\n"
"                                                            % endif\n"
"                                                        </th>\n"
"                                                        <th style=\"width:30%;text-align:right;\"\">Completeness</th>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"></td>\n"
"                                                    </tr>\n"
"                                                </thead>\n"
"                                                <tbody>\n"
"                                                    % for goal in line['goals']:\n"
"                                                        <tr>\n"
"                                                            % set tdBgColor = '#fff'\n"
"                                                            % set tdColor = 'gray'\n"
"                                                            % set mutedColor = '#AAAAAA'\n"
"                                                            % set tdPercentageColor = '#9A6C8E'\n"
"                                                            <td width=\"15%\" align=\"center\" valign=\"middle\" style=\"background-color:${tdBgColor};padding :5px 0;font-size:20px;\">${goal['rank']+1}\n"
"                                                            </td>\n"
"                                                            <td width=\"25%\" align=\"left\" valign=\"middle\" style=\"background-color:${tdBgColor};padding :5px 0;font-size:13px;\">${goal['name']}</td>\n"
"                                                            <td width=\"30%\" align=\"right\" style=\"background-color:${tdBgColor};padding:5px 0;line-height:1;\">${\"%.2f\" % goal['current']}<br/><span style=\"font-size:13px;color:${mutedColor};\">on ${\"%.2f\" % line['target']}</span>\n"
"                                                            </td>\n"
"                                                            <td width=\"30%\" style=\"color:${tdPercentageColor};background-color:${tdBgColor};padding-right:15px;font-size:22px;\" align=\"right\"><strong>${goal['completeness'] | int}%</strong></td>\n"
"                                                        </tr>\n"
"                                                        <tr>\n"
"                                                            <td colspan=\"5\" style=\"height:1px;background-color:#DADADA;\"></td>\n"
"                                                        </tr>\n"
"                                                    % endfor\n"
"                                                </tbody>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table> \n"
"                            % endfor\n"
"                        % endif\n"
"                    </td></tr>\n"
"                </table>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"padding: 15px; color: #666666;margin-bottom: 5px;\">\n"
"                    <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                            <table width=\"580\" border=\"0\" cellpadding=\"0\" style=\"min-width: 580px; font-size: 12px;\">\n"
"                                <tr>\n"
"                                    <td valign=\"middle\" align=\"left\" style=\"padding-top: 10px; padding-bottom: 10px;\">\n"
"                                        ${company.name}<br/>\n"
"                                        ${company.phone or ''}\n"
"                                    </td>\n"
"                                    <td valign=\"middle\" align=\"right\" style=\"padding-top: 10px; padding-bottom: 10px;\">\n"
"                                        % if company.email:\n"
"                                            <a href=\"mailto:${company.email}\" style=\"text-decoration:none; color: #666666;\">${company.email}</a><br/>\n"
"                                        % endif\n"
"                                        % if company.website:\n"
"                                            <a href=\"${company.website}\" style=\"text-decoration:none; color: #666666;\">\n"
"                                               ${company.website}\n"
"                                            </a>\n"
"                                        % endif\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr> \n"
"                    <tr>\n"
"                        <td align=\"center\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com\">Odoo</a>.\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </td></tr>\n"
"        </table>\n"
"    </td>\n"
"</tr>\n"
"</table>\n"
"            \n"
"\n"
msgstr ""

#. module: gamification
#: model:mail.template,body_html:gamification.email_template_goal_reminder
msgid ""
"\n"
"    <p>\n"
"        <strong>Reminder ${object.name}</strong>\n"
"    </p>\n"
"    \n"
"    <p>You have not updated your progress for the goal ${object.definition_id.name} (currently reached at ${object.completeness}%) for at least ${object.remind_update_delay} days. Do not forget to do it.</p>\n"
"            "
msgstr ""

#. module: gamification
#: code:addons/gamification/models/challenge.py:589
#, python-format
msgid "%s has joined the challenge"
msgstr ""

#. module: gamification
#: code:addons/gamification/models/challenge.py:598
#, python-format
msgid "%s has refused the challenge"
msgstr ""

#. module: gamification
#: model:mail.template,body_html:gamification.email_template_badge_received
msgid ""
"<?xml version=\"1.0\"?>\n"
"<data><div>\n"
"    <p>Congratulations ${object.user_id.name} !</p>\n"
"    <p>You just received badge <strong>${object.badge_id.name}</strong> !</p>\n"
"</div>\n"
"<div>\n"
"% if object.badge_id.description\n"
"<table cellspacing=\"0\" cellpadding=\"0\" border=\"0\" style=\"width: 600px; margin-top: 5px;\">\n"
"<tbody><tr>\n"
"    <td valign=\"center\">\n"
"        <img src=\"/web/image/gamification.badge/${object.badge_id.id}/image/80x80\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${user.company_id.name}\"/>\n"
"    </td>\n"
"    <td valign=\"center\">\n"
"        <cite>${object.badge_id.description}</cite>\n"
"    </td>\n"
"</tr></tbody>\n"
"</table>\n"
"% endif\n"
"</div>\n"
"<div>\n"
"    <p>\n"
"% if object.sender_id\n"
"        This badge was granted by <strong>${object.sender_id.name}</strong>.\n"
"% endif\n"
"    </p>\n"
"    <p>\n"
"% if object.comment\n"
"        <em>${object.comment}</em>\n"
"% endif\n"
"    </p>\n"
"</div>\n"
"</data>"
msgstr ""

#. module: gamification
#: code:addons/gamification/models/challenge.py:655
#, python-format
msgid "<br/> %(rank)d. %(user_name)s - %(reward_name)s"
msgstr ""

#. module: gamification
#: code:addons/gamification/models/challenge.py:652
#, python-format
msgid ""
"<br/>Nobody has succeeded to reach every goal, no badge is rewarded for this"
" challenge."
msgstr ""

#. module: gamification
#: code:addons/gamification/models/challenge.py:650
#, python-format
msgid "<br/>Reward (badge %s) for every succeeding user was sent to %s."
msgstr ""

#. module: gamification
#: code:addons/gamification/models/challenge.py:660
#, python-format
msgid ""
"<br/>Special rewards were sent to the top competing users. The ranking for "
"this challenge is :"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"<span class=\"o_stat_text\">Related</span>\n"
"                                <span class=\"o_stat_text\">Goals</span>"
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.badge_list_action
msgid ""
"A badge is a symbolic token granted to a user as a sign of reward.\n"
"                It can be deserved automatically when some conditions are met or manually by users.\n"
"                Some badges are harder than others to get with specific conditions."
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_definition_list_action
msgid ""
"A goal definition is a technical model of goal defining a condition to reach.\n"
"                The dates, values to reach or users are defined in goal instance."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line_condition
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_condition
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_condition_3120
msgid ""
"A goal is considered as completed when the current value is compared to the "
"value to reach"
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_list_action
msgid ""
"A goal is defined by a user and a goal definition.\n"
"                Goals can be created automatically by using challenges."
msgstr ""

#. module: gamification
#: selection:gamification.badge,rule_auth:0
msgid "A selected list of users"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_action_id
msgid "Action"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_active
msgid "Active"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Advanced Options"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_rule_auth
msgid "Allowance to Grant"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_user_domain
msgid "Alternative to a list of users"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_category
msgid "Appears in"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Assign Challenge To"
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.challenge_list_action
msgid ""
"Assign a list of goals to chosen users to evaluate them.\n"
"                The challenge can use a period (weekly, monthly...) for automatic creation of goals.\n"
"                The goals are created for the specified users or member of the group."
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_rule_auth_user_ids
msgid "Authorized Users"
msgstr ""

#. module: gamification
#: selection:gamification.goal.definition,computation_mode:0
msgid "Automatic: execute a specific Python code"
msgstr ""

#. module: gamification
#: selection:gamification.goal.definition,computation_mode:0
msgid "Automatic: number of records"
msgstr ""

#. module: gamification
#: selection:gamification.goal.definition,computation_mode:0
msgid "Automatic: sum on a field"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_badge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard_badge_id
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Badge"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Badge Description"
msgstr ""

#. module: gamification
#: model:mail.message.subtype,description:gamification.mt_badge_granted
#: model:mail.message.subtype,name:gamification.mt_badge_granted
msgid "Badge Granted"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_list_view
msgid "Badge List"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_badge_name
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Badge Name"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.badge_list_action
#: model:ir.ui.menu,name:gamification.gamification_badge_menu
msgid "Badges"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"Badges are granted when a challenge is finished. This is either at the end "
"of a running period (eg: end of the month for a monthly challenge), at the "
"end date of a challenge (if no periodicity is set) or when the challenge is "
"manually closed."
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_batch_mode
msgid "Batch Mode"
msgstr ""

#. module: gamification
#: model:gamification.badge,name:gamification.badge_idea
msgid "Brilliant"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Can not grant"
msgstr ""

#. module: gamification
#: code:addons/gamification/models/goal.py:412
#, python-format
msgid "Can not modify the configuration of a started goal"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Cancel"
msgstr "Cancelar"

#. module: gamification
#: selection:gamification.goal,state:0
msgid "Canceled"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Category"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_challenge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_challenge_id
msgid "Challenge"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_line_id
msgid "Challenge Line"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_line_list_view
msgid "Challenge Lines"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_name
msgid "Challenge Name"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_challenge_id
msgid "Challenge originating"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_challenge_id
msgid ""
"Challenge that generated the goal, assign challenge to users to generate "
"goals with a value in this field."
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.challenge_list_action
#: model:ir.ui.menu,name:gamification.gamification_challenge_menu
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Challenges"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_rule_max
msgid "Check to set a monthly limit per person of sending this badge"
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.badge_list_action
msgid "Click to create a badge."
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.challenge_list_action
msgid "Click to create a challenge."
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_definition_list_action
msgid "Click to create a goal definition."
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_list_action
msgid "Click to create a goal."
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Clickable Goals"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_closed
msgid "Closed goal"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_comment
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard_comment
msgid "Comment"
msgstr ""

#. module: gamification
#: model:gamification.challenge,name:gamification.challenge_base_discover
msgid "Complete your Profile"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_completeness
msgid "Completeness"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_computation_mode
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_computation_mode
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Computation Mode"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_new_simplified_res_users
msgid "Create User"
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.action_new_simplified_res_users
msgid ""
"Create and manage users that will connect to the system. Users can be "
"deactivated should there be a period of time during which they will/should "
"not connect to the system. You can assign them groups in order to give them "
"specific access to the applications they need to use in the system."
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_create_date
msgid "Created"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard_create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard_create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard_create_date
msgid "Created on"
msgstr "Creado en"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_create_uid
msgid "Creator"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard_current
msgid "Current"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_current
msgid "Current Value"
msgstr ""

#. module: gamification
#: selection:gamification.challenge,period:0
#: selection:gamification.challenge,report_message_frequency:0
msgid "Daily"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Data"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_field_date_id
msgid "Date Field"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_computation_mode
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_computation_mode
msgid ""
"Defined how will be computed the goals. The result of the operation will be "
"stored in the field 'Current'."
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_description_3119
msgid "Definition Description"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Depending on the Display mode, reports will be individual or shared."
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"Describe the challenge: what is does, who it targets, why it matters..."
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Describe what they did and why it matters (will be public)"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_description
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_description
msgid "Description"
msgstr "Descripción"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_visibility_mode
msgid "Display Mode"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard_display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard_display_name
msgid "Display Name"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_display
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_display_mode
msgid "Displayed as"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_batch_distinctive_field
msgid "Distinctive field for batch user"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_domain
msgid ""
"Domain for filtering records. General rule, not user depending, e.g. "
"[('state', '=', 'done')]. The expression can contain reference to 'user' "
"which is a browse record of the current user if not in batch mode."
msgstr ""

#. module: gamification
#: selection:gamification.challenge,state:0
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Done"
msgstr ""

#. module: gamification
#: selection:gamification.challenge,state:0
#: selection:gamification.goal,state:0
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Draft"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_end_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_end_date
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "End Date"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_batch_mode
msgid "Evaluate the expression in batch instead of once for each user"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_batch_user_expression
msgid "Evaluated expression for batch mode"
msgstr ""

#. module: gamification
#: selection:gamification.badge,rule_auth:0
msgid "Everyone"
msgstr ""

#. module: gamification
#: selection:gamification.goal.definition,display_mode:0
msgid "Exclusive (done or not-done)"
msgstr ""

#. module: gamification
#: selection:gamification.goal,state:0
msgid "Failed"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_field_id
msgid "Field to Sum"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_domain
msgid "Filter Domain"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_reward_first_id
msgid "For 1st user"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_reward_second_id
msgid "For 2nd user"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_reward_third_id
msgid "For 3rd user"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_reward_id
msgid "For Every Succeeding User"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Formating Options"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "From"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_full_suffix
msgid "Full Suffix"
msgstr ""

#. module: gamification
#: model:ir.ui.menu,name:gamification.gamification_menu
msgid "Gamification Tools"
msgstr ""

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge
msgid "Gamification badge"
msgstr ""

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_challenge
msgid "Gamification challenge"
msgstr ""

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_challenge_line
msgid "Gamification generic goal for challenge"
msgstr ""

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal_definition
msgid "Gamification goal definition"
msgstr ""

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal
msgid "Gamification goal instance"
msgstr ""

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge_user
msgid "Gamification user badge"
msgstr ""

#. module: gamification
#: model:ir.actions.server,name:gamification.ir_cron_check_challenge_ir_actions_server
#: model:ir.cron,cron_name:gamification.ir_cron_check_challenge
#: model:ir.cron,name:gamification.ir_cron_check_challenge
msgid "Gamification: Goal Challenge Check"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard_goal_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Goal"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_definition_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_id_3104
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_name
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Goal Definition"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goal_definition_list_action
#: model:ir.ui.menu,name:gamification.gamification_definition_menu
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_list_view
msgid "Goal Definitions"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_description
msgid "Goal Description"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Goal Failed"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_list_view
msgid "Goal List"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_condition
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_condition
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_condition_3120
msgid "Goal Performance"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Goal Reached"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.challenge_list_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Goal definitions"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goal_list_action
#: model:ir.ui.menu,name:gamification.gamification_goal_menu
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Goals"
msgstr ""

#. module: gamification
#: model:gamification.badge,name:gamification.badge_good_job
msgid "Good Job"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Grant"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_grant_wizard
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Grant Badge"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Grant Badge To"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Grant this Badge"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_user_kanban_view
msgid "Granted by"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Granting"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Group By"
msgstr "Agrupar por"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_report_message_group_id
msgid "Group that will receive a copy of the report in addition to the user"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "HR Challenges"
msgstr ""

#. module: gamification
#: model:gamification.badge,name:gamification.badge_hidden
msgid "Hidden"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "How to compute the goal?"
msgstr ""

#. module: gamification
#: selection:gamification.challenge,category:0
msgid "Human Resources / Engagement"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard_id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard_id
msgid "ID"
msgstr "ID"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_res_id_field
msgid "ID Field of user"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_remaining_sending
msgid "If a maximum is set"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user_challenge_id
msgid "If this badge was rewarded through a challenge"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_image
msgid "Image"
msgstr ""

#. module: gamification
#: selection:gamification.challenge,state:0
msgid "In Progress"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid ""
"In batch mode, the domain is evaluated globally. If enabled, do not use "
"keyword 'user' in above filter domain."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_batch_distinctive_field
msgid ""
"In batch mode, this indicates which field distinct one user form the other, "
"e.g. user_id, partner_id..."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_last_update
msgid ""
"In case of manual goal, reminders are sent if the goal as not been updated "
"for a while (defined in challenge). Ignored in case of non-manual goal or "
"goal not linked to a challenge."
msgstr ""

#. module: gamification
#: selection:gamification.goal,state:0
msgid "In progress"
msgstr ""

#. module: gamification
#: selection:gamification.challenge,visibility_mode:0
msgid "Individual Goals"
msgstr ""

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin3
#: model:gamification.goal.definition,name:gamification.definition_base_invite
msgid "Invite new Users"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge___last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user___last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard___last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge___last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line___last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal___last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition___last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard___last_update
msgid "Last Modified on"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_last_report_date
msgid "Last Report Date"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_last_update
msgid "Last Update"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard_write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard_write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard_write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard_write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: gamification
#: selection:gamification.challenge,visibility_mode:0
msgid "Leader Board (Group Ranking)"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_rule_max_number
msgid "Limitation Number"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Line List"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_ids
msgid "Lines"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line_ids
msgid "List of goals that will be set"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_user_ids
msgid "List of users participating to the challenge"
msgstr ""

#. module: gamification
#: model:gamification.goal.definition,name:gamification.definition_nbr_following
msgid "Mail Group Following"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_model_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Model"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_definition_monetary
msgid "Monetary"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_monetary
msgid "Monetary Value"
msgstr ""

#. module: gamification
#: selection:gamification.challenge,period:0
#: selection:gamification.challenge,report_message_frequency:0
msgid "Monthly"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_rule_max
msgid "Monthly Limited Sending"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_stat_this_month
msgid "Monthly total"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "My Goals"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_stat_my_monthly_sending
msgid "My Monthly Sending Total"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_stat_my_this_month
msgid "My Monthly Total"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_stat_my
msgid "My Total"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_name
msgid "Name"
msgstr ""

#. module: gamification
#: selection:gamification.challenge,report_message_frequency:0
msgid "Never"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_remind_update_delay
msgid "Never reminded if no value or zero is specified."
msgstr ""

#. module: gamification
#: model:mail.template,subject:gamification.email_template_badge_received
msgid "New badge ${object.badge_id.name} granted"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_next_report_date
msgid "Next Report Date"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "No monthly sending limit"
msgstr ""

#. module: gamification
#: model:gamification.badge,description:gamification.badge_problem_solver
msgid "No one can solve challenges like you do."
msgstr ""

#. module: gamification
#: selection:gamification.badge,rule_auth:0
msgid "No one, assigned through challenges"
msgstr ""

#. module: gamification
#: code:addons/gamification/models/challenge.py:667
#, python-format
msgid "Nobody reached the required conditions to receive special badges."
msgstr ""

#. module: gamification
#: selection:gamification.challenge,period:0
msgid "Non recurring"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_remind_update_delay
msgid "Non-updated manual goals will be reminded after"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Notification Messages"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_stat_count_distinct
msgid "Number of users"
msgstr ""

#. module: gamification
#: selection:gamification.challenge,report_message_frequency:0
msgid "On change"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_rule_auth_badge_ids
msgid "Only the people having these badges can give this badge"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_rule_auth_user_ids
msgid "Only these people can give this badge"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Optimisation"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_owner_ids
msgid "Owners"
msgstr ""

#. module: gamification
#: selection:gamification.badge,rule_auth:0
msgid "People having some badges"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Period"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_period
msgid ""
"Period of automatic goal assigment. If none is selected, should be launched "
"manually."
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_period
msgid "Periodicity"
msgstr ""

#. module: gamification
#: model:gamification.badge,name:gamification.badge_problem_solver
msgid "Problem Solver"
msgstr ""

#. module: gamification
#: selection:gamification.goal.definition,display_mode:0
msgid "Progressive (using numerical values)"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_compute_code
msgid "Python Code"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_compute_code
msgid ""
"Python code to be executed for each user. 'result' should contains the new "
"current value. Evaluated user can be access through object.user_id."
msgstr ""

#. module: gamification
#: selection:gamification.goal,state:0
msgid "Reached"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reached when current value is"
msgstr ""

#. module: gamification
#: selection:gamification.goal.definition,computation_mode:0
msgid "Recorded manually"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reference"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Refresh Challenge"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goals_from_challenge_act
msgid "Related Goals"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user_wizard_user_id
msgid "Related user name for the resource to manage its access."
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_remaining_sending
msgid "Remaining Sending Allowed"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_remind_update_delay
msgid "Remind delay"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Reminders for Manual Goals"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_report_message_frequency
msgid "Report Frequency"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_report_template_id
msgid "Report Template"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_rule_auth_badge_ids
msgid "Required Badges"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reset Completion"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_manager_id
msgid "Responsible"
msgstr ""

#. module: gamification
#: code:addons/gamification/models/challenge.py:490
#, python-format
msgid "Retrieving progress for personal challenge without user information"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Reward"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_reward_failure
msgid "Reward Bests if not Succeeded?"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_reward_realtime
msgid "Reward as soon as every goal is reached"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_challenge_ids
msgid "Reward of Challenges"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_goal_definition_ids
msgid "Rewarded by"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Rewards for challenges"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Running"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Running Challenges"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Schedule"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Search Challenges"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Search Goal Definitions"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Search Goals"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid ""
"Security rules to define who is allowed to manually grant badges. Not "
"enforced for administrator."
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Send Report"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_report_message_group_id
msgid "Send a copy to"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_sender_id
msgid "Sender"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line_sequence
msgid "Sequence number for ordering"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Set the current value you have reached for this goal"
msgstr ""

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin1
#: model:gamification.goal.definition,name:gamification.definition_base_company_data
msgid "Set your Company Data"
msgstr ""

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin2
#: model:gamification.goal.definition,name:gamification.definition_base_company_logo
msgid "Set your Company Logo"
msgstr ""

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_discover1
#: model:gamification.goal.definition,name:gamification.definition_base_timezone
msgid "Set your Timezone"
msgstr ""

#. module: gamification
#: selection:gamification.challenge,category:0
msgid "Settings / Gamification Tools"
msgstr ""

#. module: gamification
#: model:gamification.challenge,name:gamification.challenge_base_configure
msgid "Setup your Company"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Start Challenge"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_start_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_start_date
msgid "Start Date"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Start goal"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_state
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_state
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "State"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Statistics"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Subscriptions"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_definition_full_suffix
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_suffix
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition_suffix_3121
msgid "Suffix"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_invited_user_ids
msgid "Suggest to users"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_target_goal
msgid "Target Value to Reach"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "Target: less than"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_action_id
msgid "The action that will be called to update the goal value."
msgstr ""

#. module: gamification
#: code:addons/gamification/models/challenge.py:646
#, python-format
msgid "The challenge %s is finished."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line_definition_full_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_full_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_suffix_3121
msgid "The currency and suffix field"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_field_date_id
msgid "The date to use for the time period evaluated"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_end_date
msgid ""
"The day a new challenge will be automatically closed. If no periodicity is "
"set, will use this date as the goal end date."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_start_date
msgid ""
"The day a new challenge will be automatically started. If no periodicity is "
"set, will use this date as the goal start date."
msgstr ""

#. module: gamification
#: code:addons/gamification/models/goal.py:93
#, python-format
msgid ""
"The domain for the definition %s seems incorrect, please check it.\n"
"\n"
"%s"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_field_id
msgid "The field containing the value to evaluate"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_res_id_field
msgid ""
"The field name on the user profile (res.users) containing the value for "
"res_id for action."
msgstr ""

#. module: gamification
#: selection:gamification.goal.definition,condition:0
msgid "The higher the better"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_owner_ids
msgid "The list of instances of this badge granted to users"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_unique_owner_ids
msgid "The list of unique users having received this badge."
msgstr ""

#. module: gamification
#: selection:gamification.goal.definition,condition:0
msgid "The lower the better"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_rule_max_number
msgid ""
"The maximum number of time this badge can be sent per month per person."
msgstr ""

#. module: gamification
#: code:addons/gamification/models/goal.py:110
#, python-format
msgid ""
"The model configuration for the definition %s seems incorrect, please check it.\n"
"\n"
"%s not found"
msgstr ""

#. module: gamification
#: code:addons/gamification/models/goal.py:107
#, python-format
msgid ""
"The model configuration for the definition %s seems incorrect, please check it.\n"
"\n"
"%s not stored"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_model_id
msgid "The model object for the field to evaluate"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_remind_update_delay
msgid ""
"The number of days after which the user assigned to a manual goal will be "
"reminded. Never reminded if no value is specified."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_stat_my_this_month
msgid ""
"The number of time the current user has received this badge this month."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_stat_my
msgid "The number of time the current user has received this badge."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_stat_my_monthly_sending
msgid "The number of time the current user has sent this badge this month."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_stat_count_distinct
msgid "The number of time this badge has been received by unique users."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_stat_this_month
msgid "The number of time this badge has been received this month."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_stat_count
msgid "The number of time this badge has been received."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line_definition_monetary
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_monetary
msgid "The target and current value are defined in the company currency."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line_definition_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_suffix
msgid "The unit of the target and current values"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_manager_id
msgid "The user responsible for the challenge."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user_sender_id
msgid "The user who has send the badge"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_goal_definition_ids
msgid ""
"The users that have succeeded theses goals will receive automatically the "
"badge."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition_batch_user_expression
msgid ""
"The value to compare with the distinctive field. The expression can contain "
"reference to 'user' which is a browse record of the current user, e.g. "
"user.id, user.partner_id.id..."
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goals_from_challenge_act
msgid ""
"There is no goals associated to this challenge matching your search.\n"
"            Make sure that your challenge is active and assigned to at least one user."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_closed
msgid "These goals will not be recomputed."
msgstr ""

#. module: gamification
#: code:addons/gamification/models/badge.py:210
#, python-format
msgid "This badge can not be sent by users."
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_image
msgid "This field holds the image used for the badge, limited to 256x256"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "To"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_target_goal
msgid "To Reach"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_to_update
msgid "To update"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_stat_count
msgid "Total"
msgstr "Total"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_unique_owner_ids
msgid "Unique Owners"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line_definition_suffix
msgid "Unit"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Update"
msgstr ""

#. module: gamification
#: code:addons/gamification/models/goal.py:447
#, python-format
msgid "Update %s"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard_user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_user_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "User"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_user_domain
msgid "User domain"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_user_ids
msgid "Users"
msgstr ""

#. module: gamification
#: selection:gamification.challenge,category:0
msgid "Website / Forum"
msgstr ""

#. module: gamification
#: selection:gamification.challenge,period:0
#: selection:gamification.challenge,report_message_frequency:0
msgid "Weekly"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_rule_auth
msgid "Who can grant this badge"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Who would you like to reward?"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_reward_realtime
msgid ""
"With this option enabled, a user can receive a badge only once. The top 3 "
"badges are still rewarded only at the end of the challenge."
msgstr ""

#. module: gamification
#: model:gamification.badge,description:gamification.badge_idea
msgid "With your brilliant ideas, you are an inspiration to others."
msgstr ""

#. module: gamification
#: selection:gamification.challenge,period:0
#: selection:gamification.challenge,report_message_frequency:0
msgid "Yearly"
msgstr ""

#. module: gamification
#: code:addons/gamification/models/badge.py:212
#, python-format
msgid "You are not in the user allowed list."
msgstr ""

#. module: gamification
#: code:addons/gamification/wizard/grant_badge.py:24
#, python-format
msgid "You can not grant a badge to yourself"
msgstr ""

#. module: gamification
#: code:addons/gamification/models/challenge.py:196
#, python-format
msgid "You can not reset a challenge with unfinished goals."
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "You can still grant"
msgstr ""

#. module: gamification
#: model:gamification.badge,description:gamification.badge_good_job
msgid "You did great at your job."
msgstr ""

#. module: gamification
#: code:addons/gamification/models/badge.py:214
#, python-format
msgid "You do not have the required badges."
msgstr ""

#. module: gamification
#: code:addons/gamification/models/badge.py:216
#, python-format
msgid "You have already sent this badge too many time this month."
msgstr ""

#. module: gamification
#: model:gamification.badge,description:gamification.badge_hidden
msgid "You have found the hidden badge"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "badges this month"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "days"
msgstr "días"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "e.g. Monthly Sales Objectives"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. days"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid ""
"e.g. result = env['mail.followers'].search_count([('res_model', '=', "
"'mail.channel'), ('partner_id', '=', object.user_id.partner_id.id)])"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. user.partner_id.id"
msgstr ""

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge_user_wizard
msgid "gamification.badge.user.wizard"
msgstr ""

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal_wizard
msgid "gamification.goal.wizard"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "granted,"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "refresh"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "than the target."
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_user_kanban_view
msgid "the"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "this month"
msgstr ""
