# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_restaurant
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-02-18 13:56+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Colombia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CO/)\n"
"Language: es_CO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor_background_image
msgid ""
"A background image used to display a floor layout in the point of sale "
"interface"
msgstr ""
"Una imágen de fonto usada para mostrar una distribución de planta en la "
"interfaz de punto de venta"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is "
"where you can\n"
"                define and position the tables."
msgstr ""
"El piso de un restaurante representa el lugar donde se da servicio a los "
"clientes, ahí es donde\n"
"puedes definir la posición de las mesas."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_active
msgid "Active"
msgstr "Activo(a)"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/notes.js:54
#, python-format
msgid "Add Note"
msgstr "Añadir Nota"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config_iface_orderline_notes
msgid "Allow custom notes on Orderlines"
msgstr "Permitir notas personalizadas en Líneas de orden"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config_iface_printbill
msgid "Allows to print the Bill before payment"
msgstr "Permitir imprimir la Cuenta antes del pago"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table_name
msgid "An internal identification of a table"
msgstr "Una identificación interna de una mesa"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer_name
msgid "An internal identification of the printer"
msgstr "Un identificador interno de la impresora"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor_name
msgid "An internal identification of the restaurant floor"
msgstr "Una identificación interna de un piso del restaurante"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr "Apariencia"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:543
#, python-format
msgid "Are you sure ?"
msgstr "¿Está usted seguro?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:9
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:61
#, python-format
msgid "Back"
msgstr "Atrás"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_background_color
msgid "Background Color"
msgstr "Color de Fondo"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_background_image
msgid "Background Image"
msgstr "Imagen de Fondo"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_pos_config_form
msgid "Bar & Restaurant"
msgstr "Bar & Restaurante"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:31
#, python-format
msgid "Bill"
msgstr "Cuenta"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:12
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config_iface_printbill
#, python-format
msgid "Bill Printing"
msgstr "Impresión de la Cuenta"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:64
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config_iface_splitbill
#, python-format
msgid "Bill Splitting"
msgstr "Separación de la Cuenta"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:30
#, python-format
msgid "CANCELLED"
msgstr "CANCELADO"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:260
#: code:addons/pos_restaurant/static/src/js/floors.js:404
#, python-format
msgid "Changes could not be saved"
msgstr "Los cambios no pueden ser guardados"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:261
#: code:addons/pos_restaurant/static/src/js/floors.js:405
#, python-format
msgid "Check your internet connection and access rights"
msgstr "Revisar la conexión a internet y los permisos de acceso"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Click to add a Restaurant Floor."
msgstr "De click para añadir un Piso de Restaurante."

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid "Click to add a Restaurant Order Printer."
msgstr "De click para añadir una Impresora de Órdenes del Restaurante."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_color
msgid "Color"
msgstr "Color"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer_create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer_create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_create_date
msgid "Created on"
msgstr "Creado"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:91
#, python-format
msgid "Discount:"
msgstr "Descuento:"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:140
#, python-format
msgid "Discounts"
msgstr "Descuentos"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer_display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_display_name
msgid "Display Name"
msgstr "Nombre Público"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
#, fuzzy
msgid ""
"Each Order Printer has an IP Address that defines the PosBox/Hardware\n"
"                Proxy where the printer can be found, and a list of product "
"categories.\n"
"                An Order Printer will only print updates for products "
"belonging to one of\n"
"                its categories."
msgstr ""
"Cada Impresora de Órdenes tiene una dirección IP que define el PosBox/"
"Hardware\n"
"Proxy donde la impresora puede ser encontrada, y una lista de categorías de "
"productos.\n"
"Una Impresora de Órdenes solo va a imprimir cambios en los productos que "
"pertenecen a una\n"
"de esas categorías."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config_iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale"
msgstr "Habilitar la División de Cuentas en el Punto de Venta"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_floor_id
msgid "Floor"
msgstr "Piso"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_name
msgid "Floor Name"
msgstr "Nombre del Piso"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "Diseño del Piso"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:28
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_customer_count
#, python-format
msgid "Guests"
msgstr "Clientes"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:918
#, python-format
msgid "Guests ?"
msgstr "¿Clientes?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:10
#: code:addons/pos_restaurant/static/src/xml/floors.xml:21
#, fuzzy, python-format
msgid "Guests:"
msgstr "Clientes"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_height
msgid "Height"
msgstr "Altura"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_position_h
msgid "Horizontal Position"
msgstr "Posición Horizontal"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer_id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table_active
msgid ""
"If false, the table is deactivated and will not be available in the point of "
"sale"
msgstr ""
"Si es falso, la mesa es desactivada y no va a estar disponible en el punto "
"de venta"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor___last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer___last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table___last_update
msgid "Last Modified on"
msgstr "Última Modificación el"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer_write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_write_uid
msgid "Last Updated by"
msgstr "Actualizado por"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer_write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_write_date
msgid "Last Updated on"
msgstr "Actualizado"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:52
#, python-format
msgid "NEW"
msgstr "NUEVO"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:40
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:62
#, python-format
msgid "NOTE"
msgstr "NOTA"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/notes.xml:16
#, python-format
msgid "Note"
msgstr "Nota"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:476
#, python-format
msgid "Number of Seats ?"
msgstr "¿ Número de asientos ?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:13
#, python-format
msgid "Ok"
msgstr "Aceptar"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:6
#, python-format
msgid "Order"
msgstr "Orden"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_printer_form
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config_printer_ids
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_printer_all
msgid "Order Printers"
msgstr "Impresoras de Pedido"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"                order updates in the kitchen/bar when the waiter updates the "
"order."
msgstr ""
"Las Impresoras de Órdenes son usadas por los restaurantes y bares para "
"imprimir los\n"
"cambios en las ordenes en la cocina/bar cuando el mesero actualiza la orden."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config_iface_orderline_notes
msgid "Orderline Notes"
msgstr "Notas de la Línea de Pedido"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer_form
msgid "POS Printer"
msgstr "Impresora PdV"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:78
#, python-format
msgid "Payment"
msgstr "Pago"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_pos_config_id
msgid "Point of Sale"
msgstr "Punto de Venta"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_pos_order_ids
msgid "Pos Orders"
msgstr "Órdenes PdV"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:20
#, python-format
msgid "Print"
msgstr "Imprimir"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer_product_categories_ids
msgid "Printed Product Categories"
msgstr "Categorías de Producto Imprimidas"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer_name
msgid "Printer Name"
msgstr "Nombre de la Impresora"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer_proxy_ip
msgid "Proxy IP Address"
msgstr "Dirección IP del Proxy"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:544
#, python-format
msgid "Removing a table cannot be undone"
msgstr "La eliminación de una mesa no se puede deshacer"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "Piso del Restaurante"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config_floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "Pisos del Restaurante"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer
msgid "Restaurant Order Printers"
msgstr "Impresoras de Pedidos del Restaurante"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "Mesa del Restaurante"

#. module: pos_restaurant
#: selection:restaurant.table,shape:0
msgid "Round"
msgstr "Ronda"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_seats
msgid "Seats"
msgstr "Asientos"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:71
#, python-format
msgid "Served by"
msgstr "Servido por"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_shape
msgid "Shape"
msgstr "Forma"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:6
#, python-format
msgid "Split"
msgstr "Dividir"

#. module: pos_restaurant
#: selection:restaurant.table,shape:0
msgid "Square"
msgstr "Cuadrado"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:118
#, python-format
msgid "Subtotal"
msgstr "Subtotal"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:131
#, python-format
msgid "TOTAL"
msgstr "TOTAL"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_table_id
msgid "Table"
msgstr "Tabla"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_name
msgid "Table Name"
msgstr "Nombre de la Mesa"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:464
#, python-format
msgid "Table Name ?"
msgstr "¿ Nombre de la Mesa ?"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor_table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "Tablas"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:54
#, python-format
msgid "Tel:"
msgstr "Tel.:"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer_proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "La dirección IP o el nombre de host del proxy hardware de la impresora"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order_customer_count
msgid "The amount of customers that have been served by this order."
msgstr "La cantidad de clientes que van a ser servidos en está orden."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor_background_color
msgid ""
"The background color of the floor layout, (must be specified in a html-"
"compatible format)"
msgstr ""
"El color de fondo en el diseño del piso, (debe ser especificado en un "
"formato compatible con html)"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table_seats
msgid "The default number of customer served at this table."
msgstr "El número por defecto de clientes atendidos en esta mesa."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor_table_ids
msgid "The list of tables in this floor"
msgstr "La lista de mesas en este piso"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table_pos_order_ids
msgid "The orders served at this table"
msgstr "Las órdenes que son atendidas en esta mesa"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config_floor_ids
msgid "The restaurant floors served by this point of sale"
msgstr "Los pisos del restaurante atendidos en este punto de venta"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order_table_id
msgid "The table where this order was served"
msgstr "La mesa donde ésta orden fue servida"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table_color
msgid "The table's color, expressed as a valid 'background' CSS property value"
msgstr ""
"El color de la mesa, expresado como un valor de 'fondo' con propiedad CSS "
"válido"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table_height
msgid "The table's height in pixels"
msgstr "Altura de la mesa en pixeles"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table_position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in "
"pixels"
msgstr ""
"La posición horizontal de la mesa desde el lado izquierdo del centro de la "
"mesa, en pixeles"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table_position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr ""
"La posición vertical desde la parte superior hasta el centro de la mesa, en "
"pixeles"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table_width
msgid "The table's width in pixels"
msgstr "El ancho de la mesa en pixeles"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:102
#, python-format
msgid "This floor has no tables yet, use the"
msgstr "El  piso no tiene mesas aún, usa el "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:37
#, python-format
msgid "Transfer"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor_sequence
msgid "Used to sort Floors"
msgstr "Usado para organizar Pisos "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:57
#, python-format
msgid "VAT:"
msgstr "NIT/RUT:"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_position_v
msgid "Vertical Position"
msgstr "Posición Vertical "

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table_width
msgid "Width"
msgstr "Ancho"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:44
#, python-format
msgid "With a"
msgstr "Con un/una"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:36
#, python-format
msgid "at"
msgstr "en"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:6
#: code:addons/pos_restaurant/static/src/xml/floors.xml:17
#, python-format
msgid "at table"
msgstr "en la mesa"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:103
#, python-format
msgid "button in the editing toolbar to create new tables."
msgstr "botón en la barra de edición para crear nuevas mesas."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:46
#, python-format
msgid "discount"
msgstr "descuento"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "pos.config"
msgstr "pos.config"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
msgid "restaurant.floor"
msgstr "restaurant.floor"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_printer
msgid "restaurant.printer"
msgstr "restaurant.printer"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
msgid "restaurant.table"
msgstr "restaurant.table"
