<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="web.ProfilingQwebView">
    <div class="oe_form_field o_ace_view_editor oe_ace_open o_profiling_qweb_view">
        <div class="o_select_view_profiling">
            <a role="button" class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false" href="#"></a>
            <div class="dropdown-menu" role="menu">
                <t t-foreach="widget.views" t-as="view">
                    <a role="menuitem" href="#" t-att-data-id="view.id">
                        <div class="o_delay"><t t-if="view.delay" t-esc="view.delay"/> ms</div>
                        <div class="o_query"><t t-if="view.delay" t-esc="view.query"/> query</div>
                        <t t-esc="view.display_name"/>
                        <div class="o_key text-muted">(<t t-esc="view.id"/>, <t t-esc="view.key"/>)</div>
                    </a>
                </t>
            </div>
        </div>
        <div class="ace-view-editor"/>
        <small class="text-muted">
            It is possible that the "t-call" time does not correspond to the overall time of the
            template. Because the global time (in the drop down) does not take into account the
            duration which is not in the rendering (look for the template, read, inheritance,
            compilation...). During rendering, the global time also takes part of the time to make
            the profile as well as some part not logged in the function generated by the qweb.
        </small>
    </div>
</t>
<t t-name="web.ProfilingQwebView.hover">
    <div class="o_info o_detail">
        <div class="o_delay"><t t-esc="delay"/> <span>ms</span></div>
        <div class="o_query"><t t-esc="query"/> <span>query</span></div>
    </div>
</t>
<t t-name="web.ProfilingQwebView.info">
    <div class="o_info">
        <div t-if="detail" class="o_more">
            <span>*</span>
            <table class="o_detail">
                <thead>
                    <tr><th></th><th>ms</th><th>query</th></tr>
                </thead>
                <tbody>
                    <tr t-foreach="groups" t-as="directive">
                        <td><t t-esc="directive"/></td>
                        <td><t t-esc="groups[directive].delays.join(' ')"/></td>
                        <td><t t-esc="groups[directive].querys.join(' ')"/></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="o_delay"><t t-esc="delay"/></div>
        <div class="o_query"><t t-esc="query"/></div>
    </div>
</t>

</templates>
