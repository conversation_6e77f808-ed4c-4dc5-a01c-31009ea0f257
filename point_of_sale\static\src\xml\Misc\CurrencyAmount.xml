<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="CurrencyAmount" owl="1">
        <label t-att-for="props.forTarget" t-att-class="{'oe_link_icon': props.forTarget}">
            <t t-if="props.currency.position === 'before'"><t t-esc="props.currency.symbol"/><![CDATA[ ]]></t>
            <t t-esc="props.amount"/>
            <t t-if="props.currency.position === 'after'"><![CDATA[ ]]><t t-esc="props.currency.symbol"/></t>
        </label>
    </t>
</templates>
