# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_lead_enrich
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-09 12:17+0000\n"
"PO-Revision-Date: 2019-09-09 12:33+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_with_data
msgid "(Time Now)"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_with_data
msgid "<b>Phone :</b>"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_with_data
msgid "<b>Timezone : </b>"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:mail.template,body_html:crm_iap_lead_enrich.mail_template_data_iap_lead_enrich_nocredit
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p>Dear ${object.create_uid.name or 'Madam/Sir'},</p><br/>\n"
"    <p>Unfortunately, there are no more credits on your IAP Lead Enrichment account.<br/>\n"
"    You can charge it back from the Settings of the CRM app or from your IAP portal.<br/></p><br/>\n"
"    <p>Best regards,</p><br/>\n"
"    <p>Odoo S.A.</p>\n"
"</div>\n"
"        "
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_notfound
msgid ""
"<span> No company data found based on the email address or email address is "
"one of an email provider. No credit was consumed. </span>"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_email
msgid ""
"<span>Enrichment could not be done as no email address was provided.</span>"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_with_data
msgid "<span>Lead enriched based on email address</span>"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.actions.server,name:crm_iap_lead_enrich.ir_cron_lead_enrichment_ir_actions_server
#: model:ir.cron,cron_name:crm_iap_lead_enrich.ir_cron_lead_enrichment
#: model:ir.cron,name:crm_iap_lead_enrich.ir_cron_lead_enrichment
msgid "CRM: enrich leads (IAP)"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,field_description:crm_iap_lead_enrich.field_iap_enrich_api__display_name
msgid "Display Name"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,field_description:crm_iap_lead_enrich.field_crm_lead__iap_enrich_done
msgid "Enrichment done"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model,name:crm_iap_lead_enrich.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:mail.template,subject:crm_iap_lead_enrich.mail_template_data_iap_lead_enrich_nocredit
msgid "IAP Lead Enrichment Notification"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,field_description:crm_iap_lead_enrich.field_iap_enrich_api__id
msgid "ID"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,field_description:crm_iap_lead_enrich.field_iap_enrich_api____last_update
msgid "Last Modified on"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_email
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_notfound
msgid "Lead Enrichment based on email address"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_credit
msgid "Lead enriched based on email address"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model,name:crm_iap_lead_enrich.model_crm_lead
msgid "Lead/Opportunity"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,field_description:crm_iap_lead_enrich.field_crm_lead__reveal_id
msgid "Reveal ID"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_with_data
msgid "Technology Used :"
msgstr ""

#. module: crm_iap_lead_enrich
#: model:ir.model.fields,help:crm_iap_lead_enrich.field_crm_lead__iap_enrich_done
msgid ""
"Whether IAP service for lead enrichment based on email has been performed on"
" this lead."
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_credit
msgid "Your balance for Lead Enrichment is insufficient. Please go to your"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_credit
msgid "iap account"
msgstr ""

#. module: crm_iap_lead_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_enrich.mail_message_lead_enrich_no_credit
msgid "to buy credits."
msgstr ""
