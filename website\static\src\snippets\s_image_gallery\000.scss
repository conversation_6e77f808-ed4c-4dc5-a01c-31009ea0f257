
.o_gallery:not([data-vcss]) {
    &.o_grid, &.o_masonry {
        .img {
            width: 100%;
        }
    }
    &.o_grid {
        &.o_spc-none div.row {
            margin: 0;
            > div {
                padding: 0;
            }
        }
        &.o_spc-small div.row {
            margin: 5px 0;
            > div {
                padding: 0 5px;
            }
        }
        &.o_spc-medium div.row {
            margin: 10px 0;
            > div {
                padding: 0 10px;
            }
        }
        &.o_spc-big div.row {
            margin: 15px 0;
            > div {
                padding: 0 15px;
            }
        }
        &.size-auto .row {
            height: auto;
        }
        &.size-small .row {
            height: 100px;
        }
        &.size-medium .row {
            height: 250px;
        }
        &.size-big .row {
            height: 400px;
        }
        &.size-small, &.size-medium, &.size-big {
            img {
                height: 100%;
            }
        }
    }
    &.o_masonry {
        &.o_spc-none div.col {
            padding: 0;
            > img {
                margin: 0 !important;
            }
        }
        &.o_spc-small div.col {
            padding: 0 5px;
            > img {
                margin: 5px 0 !important;
            }
        }
        &.o_spc-medium div.col {
            padding: 0 10px;
            > img {
                margin: 10px 0 !important;
            }
        }
        &.o_spc-big div.col {
            padding: 0 15px;
            > img {
                margin: 15px 0 !important;
            }
        }
    }
    &.o_nomode {
        &.o_spc-none .img {
            padding: 0;
        }
        &.o_spc-small .img {
            padding: 5px;
        }
        &.o_spc-medium .img {
            padding: 10px;
        }
        &.o_spc-big .img {
            padding: 15px;
        }
    }
    &.o_slideshow {
        .carousel ul.carousel-indicators li {
            border: 1px solid #aaa;
        }
        > div:first-child {
            height: 100%;
        }
        .carousel {
            height: 100%;

            .carousel-inner {
                height: 100%;
            }
            .carousel-item.active,
            .carousel-item-next,
            .carousel-item-prev {
                display: flex;
                align-items: center;
                height: 100%;
                padding-bottom: 64px;
            }
            img {
                max-height: 100%;
                max-width: 100%;
                margin: auto;
            }
            ul.carousel-indicators {
                height: auto;
                padding: 0;
                border-width: 0;
                position: absolute;
                bottom: 0;
                width: 100%;
                margin-left: 0;
                left: 0%;
                > * {
                    list-style-image: none;
                    display: inline-block;
                    width: 40px;
                    height: 40px;
                    line-height: 40px;
                    margin: 2.5px 2.5px 2.5px 2.5px;
                    padding: 0 !important;
                    border: 1px solid #aaa;
                    text-indent: initial;
                    background-size: cover;
                    background-color: #fff;
                    border-radius: 0;
                    vertical-align: bottom;
                    flex: 0 0 40px;
                    &:not(.active) {
                        opacity: 0.8;
                        filter: grayscale(1);
                    }
                }
            }
        }
    }
    .carousel-inner .item img {
        max-width: none;
    }
}

// Note: the s_gallery_lightbox is always using the right dom and classes of the
// most recent version of the snippet as it is generated by JS after page load.
