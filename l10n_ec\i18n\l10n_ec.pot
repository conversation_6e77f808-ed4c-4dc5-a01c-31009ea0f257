# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ec
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-30 12:58+0000\n"
"PO-Revision-Date: 2021-07-30 12:58+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ec
#: code:addons/l10n_ec/models/l10n_ec_witholding.py:0
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__witholding_move_id
#, python-format
msgid "Account Move"
msgstr ""

#. module: l10n_ec
#: code:addons/l10n_ec/models/l10n_ec_witholding.py:0
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__amount
#, python-format
msgid "Amount"
msgstr ""

#. module: l10n_ec
#: code:addons/l10n_ec/models/account_move.py:0
#: model:ir.model.fields,field_description:l10n_ec.field_account_bank_statement_line__l10n_ec_access_key
#: model:ir.model.fields,field_description:l10n_ec.field_account_bank_statement_line__l10n_ec_auth_type
#: model:ir.model.fields,field_description:l10n_ec.field_account_move__l10n_ec_access_key
#: model:ir.model.fields,field_description:l10n_ec.field_account_move__l10n_ec_auth_type
#: model:ir.model.fields,field_description:l10n_ec.field_account_payment__l10n_ec_access_key
#: model:ir.model.fields,field_description:l10n_ec.field_account_payment__l10n_ec_auth_type
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_latam_document_type__l10n_ec_authorization
#, python-format
msgid "Authorization"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__base
msgid "Base"
msgstr ""

#. module: l10n_ec
#: model:l10n_latam.identification.type,name:l10n_ec.ec_dni
msgid "Ced"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_account_tax__l10n_ec_code_ats
#: model:ir.model.fields,field_description:l10n_ec.field_account_tax_template__l10n_ec_code_ats
msgid "Code ATS"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_account_tax__l10n_ec_code_applied
#: model:ir.model.fields,field_description:l10n_ec.field_account_tax_template__l10n_ec_code_applied
msgid "Code applied"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_account_tax__l10n_ec_code_base
#: model:ir.model.fields,field_description:l10n_ec.field_account_tax_template__l10n_ec_code_base
msgid "Code base"
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__company_id
msgid "Company"
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_sri_payment__create_uid
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__create_uid
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_sri_payment__create_date
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__create_date
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__create_date
msgid "Created on"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__currency_id
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__currency_id
msgid "Currency"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_type__out_invoice
msgid "Customer Document"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_type__out_refund
msgid "Customer Refund"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_type__out_withhold
msgid "Customer Withhold"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_product_product__witholding_tax_ids
#: model:ir.model.fields,field_description:l10n_ec.field_product_template__witholding_tax_ids
msgid "Customer Witholdings"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_sri_payment__code
msgid "Código"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,help:l10n_ec.field_product_product__witholding_tax_ids
#: model:ir.model.fields,help:l10n_ec.field_product_template__witholding_tax_ids
msgid "Default witholding used when selling the product."
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_sri_payment__display_name
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__display_name
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_ec
#: code:addons/l10n_ec/models/l10n_ec_witholding.py:0
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__l10n_latam_document_type_id
#, python-format
msgid "Document Type"
msgstr ""

#. module: l10n_ec
#: model_terms:ir.ui.view,arch_db:l10n_ec.view_document_type_conf_form
msgid "Ecuadorian Configuration"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_latam_document_type__l10n_ec_type
msgid "Ecuadorian Type"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,help:l10n_ec.field_account_bank_statement_line__l10n_ec_auth_type
#: model:ir.model.fields,help:l10n_ec.field_account_move__l10n_ec_auth_type
#: model:ir.model.fields,help:l10n_ec.field_account_payment__l10n_ec_auth_type
#: model:ir.model.fields,help:l10n_ec.field_l10n_latam_document_type__l10n_ec_authorization
msgid ""
"Ecuadorian tax authority requires an authorization for certain documents"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,help:l10n_ec.field_account_tax_group__l10n_ec_type
msgid "Ecuadorian taxes subtype"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,help:l10n_ec.field_account_bank_statement_line__amount_by_group_wth
#: model:ir.model.fields,help:l10n_ec.field_account_move__amount_by_group_wth
#: model:ir.model.fields,help:l10n_ec.field_account_payment__amount_by_group_wth
msgid "Edit Tax amounts if you encounter rounding issues."
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_account_bank_statement_import_journal_creation__l10n_ec_entity
#: model:ir.model.fields,field_description:l10n_ec.field_account_journal__l10n_ec_entity
msgid "Emission Entity"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_account_bank_statement_import_journal_creation__l10n_ec_emission
#: model:ir.model.fields,field_description:l10n_ec.field_account_journal__l10n_ec_emission
msgid "Emission Point"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_type__hr_advance
msgid "Employee Advance"
msgstr ""

#. module: l10n_ec
#: model:account.tax.group,name:l10n_ec.ec_tax_group_outflows
#: model:ir.model.fields.selection,name:l10n_ec.selection__account_tax_group__l10n_ec_type__outflows_tax
msgid "Exchange Outflows"
msgstr ""

#. module: l10n_ec
#: model:ir.actions.act_window,name:l10n_ec.action_account_l10n_ec_sri_payment_tree
#: model:ir.ui.menu,name:l10n_ec.menu_action_account_l10n_ec_sri_payment
msgid "Formas de pago SRI"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_sri_payment__id
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__id
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__id
msgid "ID"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,help:l10n_ec.field_l10n_latam_document_type__l10n_ec_type
msgid "Indicates the aplicability of the document"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_type__out_waybill
msgid "Issued Waybill"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_authorization__third
msgid "Issued by Third Parties"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_authorization__own
msgid "Issued by my company"
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_account_journal
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__journal_id
msgid "Journal"
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_account_bank_statement_line__l10n_ec_is_electronic
#: model:ir.model.fields,field_description:l10n_ec.field_account_move__l10n_ec_is_electronic
#: model:ir.model.fields,field_description:l10n_ec.field_account_payment__l10n_ec_is_electronic
msgid "L10N Ec Is Electronic"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_sri_payment____last_update
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding____last_update
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_sri_payment__write_uid
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__write_uid
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_sri_payment__write_date
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__write_date
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_l10n_latam_document_type
msgid "Latam Document Type"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_sri_payment__name
msgid "Nombre"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_authorization__none
msgid "None"
msgstr ""

#. module: l10n_ec
#: code:addons/l10n_ec/models/l10n_ec_witholding.py:0
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__invoice_id
#, python-format
msgid "Origin Document"
msgstr ""

#. module: l10n_ec
#: model:account.tax.group,name:l10n_ec.ec_tax_group_others
#: model:ir.model.fields.selection,name:l10n_ec.selection__account_tax_group__l10n_ec_type__other
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_type__other
msgid "Others"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__partner_id
msgid "Partner"
msgstr ""

#. module: l10n_ec
#: code:addons/l10n_ec/models/account_move.py:0
#: model:ir.model.fields,field_description:l10n_ec.field_account_bank_statement_line__l10n_ec_sri_payment_id
#: model:ir.model.fields,field_description:l10n_ec.field_account_move__l10n_ec_sri_payment_id
#: model:ir.model.fields,field_description:l10n_ec.field_account_payment__l10n_ec_sri_payment_id
#, python-format
msgid "Payment Method (SRI)"
msgstr ""

#. module: l10n_ec
#: code:addons/l10n_ec/models/l10n_ec_witholding.py:0
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__percent
#, python-format
msgid "Percent"
msgstr ""

#. module: l10n_ec
#: model:account.tax.group,name:l10n_ec.ec_tax_group_irbpnr
#: model:ir.model.fields.selection,name:l10n_ec.selection__account_tax_group__l10n_ec_type__irbpnr
msgid "Plastic Bottles (IRBPNR)"
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_product_template
msgid "Product Template"
msgstr ""

#. module: l10n_ec
#: model:account.tax.group,name:l10n_ec.ec_tax_group_withhold_income
#: model:ir.model.fields.selection,name:l10n_ec.selection__account_tax_group__l10n_ec_type__withhold_income_tax
msgid "Profit Withhold"
msgstr ""

#. module: l10n_ec
#: model:l10n_latam.identification.type,name:l10n_ec.ec_ruc
msgid "RUC"
msgstr ""

#. module: l10n_ec
#: model:ir.actions.act_window,name:l10n_ec.action_account_l10n_ec_witholding_tree
#: model:ir.ui.menu,name:l10n_ec.menu_action_account_l10n_ec_witholding
msgid "Retenciones"
msgstr ""

#. module: l10n_ec
#: model:account.tax.group,name:l10n_ec.ec_tax_group_ice
msgid "Special Consumptions (ICE)"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__account_tax_group__l10n_ec_type__ice
msgid "Special Consumptions Tax (ICE)"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_type__in_invoice
msgid "Supplier Document"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_type__in_refund
msgid "Supplier Refund"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__l10n_latam_document_type__l10n_ec_type__in_withhold
msgid "Supplier Withhold"
msgstr ""

#. module: l10n_ec
#: code:addons/l10n_ec/models/l10n_ec_witholding.py:0
#: model:ir.model,name:l10n_ec.model_account_tax
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__name
#, python-format
msgid "Tax"
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_account_tax_group
msgid "Tax Group"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,help:l10n_ec.field_account_tax__l10n_ec_code_ats
#: model:ir.model.fields,help:l10n_ec.field_account_tax_template__l10n_ec_code_ats
msgid "Tax Identification Code for the Simplified Transactional Annex"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,help:l10n_ec.field_account_tax__l10n_ec_code_base
#: model:ir.model.fields,help:l10n_ec.field_account_tax_template__l10n_ec_code_base
msgid ""
"Tax declaration code of the base amount prior to the calculation of the tax"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,help:l10n_ec.field_account_tax__l10n_ec_code_applied
#: model:ir.model.fields,help:l10n_ec.field_account_tax_template__l10n_ec_code_applied
msgid ""
"Tax declaration code of the resulting amount after the calculation of the "
"tax"
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_account_tax_template
msgid "Templates for Taxes"
msgstr ""

#. module: l10n_ec
#: model_terms:res.company,invoice_terms_html:l10n_ec.demo_company_ec
msgid "Terms &amp; Conditions"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__amount_total
msgid "Total Amount"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_account_tax_group__l10n_ec_type
msgid "Type Ecuadorian Tax"
msgstr ""

#. module: l10n_ec
#: model:l10n_latam.identification.type,name:l10n_ec.ec_unknown
msgid "Unknown"
msgstr ""

#. module: l10n_ec
#: model:account.tax.group,name:l10n_ec.ec_tax_group_vat0
#: model:ir.model.fields.selection,name:l10n_ec.selection__account_tax_group__l10n_ec_type__zero_vat
msgid "VAT 0%"
msgstr ""

#. module: l10n_ec
#: model:account.tax.group,name:l10n_ec.ec_tax_group_vat12
#: model:ir.model.fields.selection,name:l10n_ec.selection__account_tax_group__l10n_ec_type__vat12
msgid "VAT 12%"
msgstr ""

#. module: l10n_ec
#: model:account.tax.group,name:l10n_ec.ec_tax_group_vat14
#: model:ir.model.fields.selection,name:l10n_ec.selection__account_tax_group__l10n_ec_type__vat14
msgid "VAT 14%"
msgstr ""

#. module: l10n_ec
#: model:account.tax.group,name:l10n_ec.ec_tax_group_vat_excempt
msgid "VAT Excempt"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields.selection,name:l10n_ec.selection__account_tax_group__l10n_ec_type__exempt_vat
msgid "VAT Exempt"
msgstr ""

#. module: l10n_ec
#: model:account.tax.group,name:l10n_ec.ec_tax_group_vat_not_charged
#: model:ir.model.fields.selection,name:l10n_ec.selection__account_tax_group__l10n_ec_type__not_charged_vat
msgid "VAT Not Charged"
msgstr ""

#. module: l10n_ec
#: model:account.tax.group,name:l10n_ec.ec_tax_group_withhold_vat
#: model:ir.model.fields.selection,name:l10n_ec.selection__account_tax_group__l10n_ec_type__withhold_vat
msgid "VAT Withhold"
msgstr ""

#. module: l10n_ec
#: model_terms:ir.ui.view,arch_db:l10n_ec.account_witholding_form_view
msgid "Validate"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding_lines__witholding_id
msgid "Witholding"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_l10n_ec_witholding__witholding_line_ids
msgid "Witholding Line"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_account_bank_statement_line__amount_by_group_wth
#: model:ir.model.fields,field_description:l10n_ec.field_account_move__amount_by_group_wth
#: model:ir.model.fields,field_description:l10n_ec.field_account_payment__amount_by_group_wth
msgid "Witholding amount by group"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,help:l10n_ec.field_account_move_line__witholding_tax_ids
msgid "Witholding taxes that apply on the base amount"
msgstr ""

#. module: l10n_ec
#: model:ir.model.fields,field_description:l10n_ec.field_account_move_line__witholding_tax_ids
msgid "Witholdings"
msgstr ""

#. module: l10n_ec
#: model_terms:res.company,invoice_terms_html:l10n_ec.demo_company_ec
msgid "Your conditions..."
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_l10n_ec_sri_payment
msgid "l10n.ec.sri.payment"
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_l10n_ec_witholding
msgid "l10n_ec.witholding"
msgstr ""

#. module: l10n_ec
#: model:ir.model,name:l10n_ec.model_l10n_ec_witholding_lines
msgid "l10n_ec.witholding.lines"
msgstr ""
