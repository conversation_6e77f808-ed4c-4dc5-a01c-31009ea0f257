# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_geolocalize
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "(refresh)"
msgstr "(rafraîchir)"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid ""
"<br/>\n"
"                                    <span attrs=\"{'invisible': [('date_localization', '=', False)]}\">Updated on: </span>"
msgstr ""
"<br/>\n"
"                                    <span attrs=\"{'invisible': [('date_localization', '=', False)]}\">Mis à jour le : </span>"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span>, Long: </span>"
msgstr "<span>, Long: </span>"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span>Lat : </span>"
msgstr "<span>Lat : </span>"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_id
msgid "API"
msgstr "API"

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid ""
"API key for GeoCoding (Places) required.\n"
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key for more information."
msgstr ""
"Clé API pour GeoCoding (Places) requise. \n"
"Renvez-vous sur https://developers.google.com/maps/documentation/geocoding/get-api-key pour plus d'informations."

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "API:"
msgstr "API:"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_date
msgid "Created on"
msgstr "Créé le"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid "Error with geolocation server:"
msgstr "Erreur avec le serveur de géolocalisation: "

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geocoder
msgid "Geo Coder"
msgstr "Geo Coder"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geo Location"
msgstr "Géolocalisation"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geo_provider
msgid "Geo Provider"
msgstr "Geo fournisseur"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geolocation"
msgstr "Géolocalisation"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner__date_localization
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users__date_localization
msgid "Geolocation Date"
msgstr "Date de géolocalisation"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid "Google Map API Key"
msgstr "Clé API Google Map"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__id
msgid "ID"
msgstr "ID"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "Key:"
msgstr "Clé:"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__name
msgid "Name"
msgstr "Nom"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Partner Assignment"
msgstr "Assignation de partenaire"

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid "Provider %s is not implemented for geolocation service."
msgstr ""
"Le fournisseur %s n'est pas implémenté pour le service de géolocalisation."

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__tech_name
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_techname
msgid "Tech Name"
msgstr "Nom Technique"

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid ""
"Unable to geolocate, received the error:\n"
"%s\n"
"\n"
"Google made this a paid feature.\n"
"You should first enable billing on your Google account.\n"
"Then, go to Developer Console, and enable the APIs:\n"
"Geocoding, Maps Static, Maps Javascript.\n"
msgstr ""
"Impossible à géolocaliser, erreur reçue:\n"
"%s\n"
"\n"
"Google en a fait une fonctionnalité payante.\n"
"Vous devez d'abord activer la facturation sur votre compte Google.\n"
"Ensuite, rendez-vous sur la Console Développeur et activer les APIs:\n"
"Geocoding, Maps Static, Maps Javascript.\n"

#. module: base_geolocalize
#: model:ir.model.fields,help:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid ""
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key"
" for more information."
msgstr ""
"Rendez-vous sur "
"https://developers.google.com/maps/documentation/geocoding/get-api-key pour "
"plus d'informations."
