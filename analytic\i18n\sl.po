# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* analytic
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> Potočnik <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction
msgid "Action Needed"
msgstr "Potreben je ukrep"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__active
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__active
msgid "Active"
msgstr "Aktivno"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.action_account_analytic_account_form
#: model_terms:ir.actions.act_window,help:analytic.action_analytic_account_form
msgid "Add a new analytic account"
msgstr "Dodaj nov analitični konto"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_tag_action
msgid "Add a new tag"
msgstr "Dodaj novo oznako"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Amount"
msgstr "Znesek"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_account
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__account_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__account_id
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Analytic Account"
msgstr "Analitični konto"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_distribution
msgid "Analytic Account Distribution"
msgstr "Analitična porazdelitev"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_group_action
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_group_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_group_tree_view
msgid "Analytic Account Groups"
msgstr "Analitične skupine"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_res_config_settings__group_analytic_accounting
#: model:res.groups,name:analytic.group_analytic_accounting
msgid "Analytic Accounting"
msgstr "Analitično knjigovodstvo"

#. module: analytic
#: model:res.groups,name:analytic.group_analytic_tags
msgid "Analytic Accounting Tags"
msgstr "Analitične oznake"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_account_analytic_account_form
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__analytic_distribution_ids
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Analytic Accounts"
msgstr "Analitični konti"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_group
msgid "Analytic Categories"
msgstr "Analitične kategorije"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__active_analytic_distribution
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
msgid "Analytic Distribution"
msgstr "Analitična porazdelitev"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_graph
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_pivot
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Analytic Entries"
msgstr "Analitični vnosi"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Analytic Entry"
msgstr "Analitičen vnos"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action_entries
msgid "Analytic Items"
msgstr "Analitične postavke"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitična postavka"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__line_ids
msgid "Analytic Lines"
msgstr "Analitične postavke"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__name
msgid "Analytic Tag"
msgstr "Analitična oznaka"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_tag_action
#: model:ir.model,name:analytic.model_account_analytic_tag
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_tree_view
msgid "Analytic Tags"
msgstr "Analitične oznake"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_view_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Archived"
msgstr "Arhivirano"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Associated Partner"
msgstr "Povezani partner"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_attachment_count
msgid "Attachment Count"
msgstr "Število prilog"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__balance
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Balance"
msgstr "Saldo"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_kanban
msgid "Balance:"
msgstr "Stanje:"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__category
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Category"
msgstr "Kategorija"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_account_form
msgid "Chart of Analytic Accounts"
msgstr "Analitični kontni načrt"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__children_ids
msgid "Childrens"
msgstr "Podrejeni"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_group_action
msgid "Click to add a new analytic account group."
msgstr "Dodaj novo analitično skupino."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__color
msgid "Color Index"
msgstr "Barvni indeks"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__company_id
msgid "Company"
msgstr "Podjetje"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__complete_name
msgid "Complete Name"
msgstr "Celotni naziv"

#. module: analytic
#: model:ir.model,name:analytic.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr "Pretvorba med enotami mere je možna samo v isti kategoriji."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Costs will be created automatically when you register supplier\n"
"                invoices, expenses or timesheets."
msgstr ""
"Stroški bodo samodejno ustvarjeni ob knjiženju prejetih\n"
"                računov, izdatkov ali časovnic."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__credit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Credit"
msgstr "Kredit"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__currency_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__partner_id
msgid "Customer"
msgstr "Stranka"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__date
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Date"
msgstr "Datum"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__debit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Debit"
msgstr "Debet"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__description
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__name
msgid "Description"
msgstr "Opis"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_follower_ids
msgid "Followers"
msgstr "Sledilci"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledilci (partnerji)"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action
msgid "Gross Margin"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__group_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__group_id
msgid "Group"
msgstr "Skupina"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Group By..."
msgstr "Združi po..."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__has_message
msgid "Has Message"
msgstr "Ima sporočilo"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__id
msgid "ID"
msgstr "ID"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_unread
msgid "If checked, new messages require your attention."
msgstr "Če je označeno, zahtevajo nova sporočila vašo pozornost."

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Če je označeno, nekatera sporočila vsebujejo napako pri dostavi."

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__active
msgid ""
"If the active field is set to False, it will allow you to hide the account "
"without removing it."
msgstr "Neaktivni konti bodo skriti"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"In Odoo, sales orders and projects are implemented using\n"
"                analytic accounts. You can track costs and revenues to analyse\n"
"                your margins easily."
msgstr ""
"V Odoo so prodajni nalogi in projekti implementirani z uporabo\n"
"                analitičnih kontov. Sledite lahko stroškom in prihodkom za potrebe\n"
"                analize marž."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_is_follower
msgid "Is Follower"
msgstr "Je sledilec"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag____last_update
msgid "Last Modified on"
msgstr "Zadnjič spremenjeno"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna priponka"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error
msgid "Message Delivery error"
msgstr "Napaka pri dostavi sporočila"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_ids
msgid "Messages"
msgstr "Sporočila"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__name
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Name"
msgstr "Naziv"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid "No activity yet"
msgstr "Ni še aktivnosti"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
msgid "No activity yet on this account"
msgstr "Na tem kontu še ni bilo aktivnosti"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of Actions"
msgstr "Število aktivnosti"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of errors"
msgstr "Število napak"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Število sporočil, ki zahtevajo dejavnost"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Število sporočil, ki niso bila pravilno dostavljena."

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_unread_counter
msgid "Number of unread messages"
msgstr "Število neprebranih sporočil"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_line__category__other
msgid "Other"
msgstr "Drugo"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__parent_id
msgid "Parent"
msgstr "Nadrejeni"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__parent_path
msgid "Parent Path"
msgstr "Pot nadrejenega"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__tag_id
msgid "Parent tag"
msgstr "Nadrejena oznaka"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "Partner"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__percentage
msgid "Percentage"
msgstr "Odstotek"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__unit_amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Quantity"
msgstr "Količina"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__code
msgid "Reference"
msgstr "Referenca"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Revenues will be created automatically when you create customer\n"
"                invoices. Customer invoices can be created based on sales orders\n"
"                (fixed price invoices), on timesheets (based on the work done) or\n"
"                on expenses (e.g. reinvoicing of travel costs)."
msgstr ""
"Prihodki se samodejno ustvarijo ob nastanku izdanih računov.\n"
"                Izdani računi se ustvarijo na osnovi prodajnih nalogov\n"
"                (fiksna cena), časovnic (glede na opravljeno delo) ali\n"
"                po stroških (npr. zaračunavanje potnih stroškov)."

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Search Analytic Lines"
msgstr "Iskanje analitičnih vrstic"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_view_search
msgid "Search Analytic Tags"
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_tag__active
msgid "Set active to false to hide the Analytic Tag without removing it."
msgstr ""
"Nastavitev oznake aktivno kot napačno skrije analitično oznako ne da bi jo "
"izbrisali."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__tag_ids
msgid "Tags"
msgstr "Ključne besede"

#. module: analytic
#: model:ir.model.constraint,message:analytic.constraint_account_analytic_distribution_check_percentage
msgid ""
"The percentage of an analytic distribution should be between 0 and 100."
msgstr "Odstotki analitične porazdelitve bi morali biti med 0 in 100."

#. module: analytic
#: code:addons/analytic/models/analytic_account.py:0
#, python-format
msgid ""
"The selected account belongs to another company than the one you're trying "
"to create an analytic item for"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_group_action
msgid "This allows you to classify your analytic accounts."
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Total"
msgstr "Skupaj"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_id
msgid "Unit of Measure"
msgstr "Enota mere"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_unread
msgid "Unread Messages"
msgstr "Neprebrana sporočila"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Števec neprebranih sporočil"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_category_id
msgid "UoM Category"
msgstr "UoM kategorija"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__user_id
msgid "User"
msgstr "Uporabnik"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "e.g. Project XYZ"
msgstr "npr. Projekt XYZ"
