# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_timesheet
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-23 13:49+0000\n"
"PO-Revision-Date: 2019-08-26 09:14+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__billable_type
msgid ""
"* At Project Rate: All timesheets on the project will be billed at the same rate\n"
"* At Employee Rate: Timesheets will be billed at a rate defined at employee level"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "<b>Total</b>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid ""
"<span class=\"btn-link\" style=\"font-weight:normal;\" title=\"Includes the time logged into a task which is not linked to any Sales Order.\">\n"
"                                                                Non Billable Tasks\n"
"                                                            </span>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Time Billing</span>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "<span class=\"o_label\">Overview</span>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"A billable project should be linked to a Sales Order Item having a Service "
"product."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"A billable project should be linked to a Sales Order Item that does not come"
" from an expense or a vendor bill."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_create_sale_order_line_unique_employee_per_wizard
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_sale_line_employee_map_uniqueness_employee
msgid ""
"An employee cannot be selected more than once in the mapping. Please remove "
"duplicate(s) and try again."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__analytic_account_id
msgid "Analytic Account"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_create_sale_order__billable_type__employee_rate
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billable_type__employee_rate
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_task__billable_type__employee_rate
msgid "At Employee Rate"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_create_sale_order__billable_type__project_rate
msgid "At Project Rate"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billable_type__task_rate
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_task__billable_type__task_rate
msgid "At Task Rate"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "At least one line should be filled."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__billable_type
msgid ""
"At which rate timesheets will be billed:\n"
" - At task rate: each time spend on a task is billed at task rate.\n"
" - At employee rate: each employee log time billed at his rate.\n"
" - No Billable: track time without invoicing it"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#, python-format
msgid "Before"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billable_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__billable_type
msgid "Billable Type"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed at a Fixed Price"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_fixed
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Billed at a Fixed price"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_time
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Timesheets"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr ""

#. module: sale_timesheet
#: model:ir.filters,name:sale_timesheet.timesheet_filter_billing
msgid "Billing Rate"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__billable_type
msgid "Billing Type"
msgstr ""

#. module: sale_timesheet
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Rate"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Cancel"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Cancelled"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Company"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Configure your services"
msgstr ""

#. module: sale_timesheet
#: model:ir.filters,name:sale_timesheet.ir_filter_project_profitability_report_costs_and_revenues
msgid "Costs and Revenues"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#: code:addons/sale_timesheet/models/project_overview.py:0
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
#, python-format
msgid "Create Invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order_line
msgid "Create SO Line from project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order
msgid "Create SO from project"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
#, python-format
msgid "Create Sales Order"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
#, python-format
msgid "Create a Sales Order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_tracking__project_only
msgid "Create a new project but no task"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_tracking__task_global_project
msgid "Create a task in an existing project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_tracking__task_in_project
msgid "Create a task in sale order's project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
msgid "Created by"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
msgid "Created on"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__currency_id
msgid "Currency"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Customer"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__partner_id
msgid "Customer of the sales order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
msgid "Display Name"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__visible_project
msgid "Display project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_tracking__no
msgid "Don't create task"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__employee_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__employee_id
msgid "Employee that has timesheets on the project."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_employee_ids
msgid ""
"Employee/Sale Order Item Mapping:\n"
" Defines to which sales order item an employee's timesheet entry will be linked.By extension, it defines the rate at which an employee's time on the project is billed."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Fixed price services"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__project_id
msgid "Generated Project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__task_id
msgid "Generated Task"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Group By"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Hours"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Hours recorded and Profitability"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
msgid "ID"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid ""
"Includes the time logged from the Timesheet module that is linked to a "
"project, but not to a task."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid ""
"Includes the time logged into a task which is linked to a cancelled Sales "
"Order."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid ""
"Includes the time logged into tasks for which you invoice based on ordered "
"quantities or on milestones."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid ""
"Includes the time logged into tasks for which you invoice based on "
"timesheets on tasks."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
msgid "Invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Invoiced"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#, python-format
msgid "Invoices"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__is_service
msgid "Is a Service"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move
msgid "Journal Entries"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map____last_update
msgid "Last Modified on"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
msgid "Last Updated by"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
msgid "Last Updated on"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__line_ids
msgid "Lines"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Milestone services"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__delivered_manual
msgid "Milestones (manually set quantities on order)"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "My Project"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#, python-format
msgid "Name"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__billable_type__no
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_task__billable_type__no
msgid "No Billable"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#, python-format
msgid "No Sales Order"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#: code:addons/sale_timesheet/models/project_overview.py:0
#: code:addons/sale_timesheet/models/project_overview.py:0
#, python-format
msgid "No Sales Order Line"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable_project
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "No task found"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non Billable Tasks"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Non billable tasks"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_count
msgid "Number of timesheets"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__ordered_timesheet
msgid "Ordered quantities"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_cost
msgid "Other Cost"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Other costs"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_timesheet_action_client_timesheet_plan
msgid "Overview"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_product
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__product_id
msgid "Product"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product Template"
msgstr ""

#. module: sale_timesheet
#: model:ir.filters,name:sale_timesheet.ir_filter_project_profitability_report_manager_and_product
msgid "Product by Customer"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__product_id
msgid ""
"Product of the sales order item. Must be a service invoiced based on "
"timesheets on tasks."
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_default_services
msgid "Products"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Profitability"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_graph
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Profitability Analysis"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__project_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
#, python-format
msgid "Project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__company_id
msgid "Project Company"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_profitability_report_action
#: model:ir.ui.menu,name:sale_timesheet.menu_project_profitability_analysis
msgid "Project Costs and Revenues"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__currency_id
msgid "Project Currency"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__user_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Project Manager"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "Project Overview"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_profitability_report
msgid "Project Profitability Report"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__project_id
msgid "Project for which we are creating a sales order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__project_id
msgid "Project generated by the sales order item"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__project_ids
#, python-format
msgid "Projects"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Re-invoiced costs"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#, python-format
msgid "Remaining"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_search
msgid "Sale Order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
msgid "Sale Order Item"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__sale_line_id
msgid "Sale Order Line"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__sale_line_employee_ids
msgid "Sale line/Employee map"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_sale_order
msgid "Sales Order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__order_confirmation_date
msgid "Sales Order Confirmation Date"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_sale_service_inherit_form2
msgid "Sales Order Item"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__is_service
msgid ""
"Sales Order item should generate a task and/or a project, depending on the "
"product settings."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#, python-format
msgid "Sales Orders"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_id
msgid ""
"Sales order item to which the project is linked. If an employee timesheets "
"on a task that does not have a sale order item defines, and if this employee"
" is not in the 'Employee/Sales Order Item Mapping' of the project, the "
"timesheet entry will be linked to the sales order item defined on the "
"project."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_line_id
msgid ""
"Sales order item to which the task is linked. If an employee timesheets on a"
" this task, and if this employee is not in the 'Employee/Sales Order Item "
"Mapping' of the project, the timesheet entry will be linked to this sales "
"order item."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_order_id
msgid "Sales order to which the project is linked."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,help:sale_timesheet.field_product_template__project_id
msgid ""
"Select a non billable project on which tasks can be created. This setting "
"must be set for each company."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,help:sale_timesheet.field_product_template__project_template_id
msgid ""
"Select a non billable project to be the skeleton of the new created project "
"when selling the current product. Its stages and tasks will be duplicated."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__product_id
msgid "Service"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_tracking
msgid "Service Tracking"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#, python-format
msgid "Sold"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Statistics"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
msgid "Task"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:0
#, python-format
msgid ""
"Task Created (%s): <a href=# data-oe-model=project.task data-oe-id=%d>%s</a>"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__task_id
msgid "Task generated by the sales order item"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
#, python-format
msgid "Tasks"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_project_sale_order_required_if_sale_line
msgid ""
"The Project should be linked to a Sale Order to select an Sale Order Items."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid ""
"The Sales Order cannot be created because you did not enter some employees that entered timesheets on this project. Please list all the relevant employees before creating the Sales Order.\n"
"Missing employee(s): %s"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "The project is already billable."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "The project is already linked to a sales order item."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid ""
"The sales order cannot be created because some timesheets of this project "
"are already linked to another sales order."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
#, python-format
msgid "The selected Sales Order should contain something to invoice."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "There is no timesheet for now."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid ""
"This cost is based on the \"Timesheet cost\" set in the HR Settings of your "
"employees."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.project_profitability_report_action
msgid ""
"This report allows you to analyse the profitability of your projects: "
"compare the amount to invoice, the ones already invoiced and the project "
"cost (via timesheet cost of your employees)."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:0
#, python-format
msgid ""
"This task has been created from: <a href=# data-oe-model=sale.order data-oe-"
"id=%d>%s</a> (%s)"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account.py:0
#, python-format
msgid ""
"This timesheet line cannot be billed: there is no Sale Order Item defined on"
" the task, nor on the project. Please define one to save your timesheet "
"line."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Time by people"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Time-based services"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_pivot_revenue
msgid "Timesheet"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__timesheet_cost
msgid "Timesheet Cost"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_total_duration
msgid "Timesheet Total Duration"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__timesheet_unit_amount
msgid "Timesheet Unit Amount"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_count
msgid "Timesheet activities"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_ids
msgid "Timesheet activities associated to this sale"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Timesheet costs"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project_overview.py:0
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_ids
#: model:ir.model.fields.selection,name:sale_timesheet.selection__sale_order_line__qty_delivered_method__timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
#, python-format
msgid "Timesheets"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
msgid "Timesheets By Billing Rate"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Timesheets of %s"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_type__timesheet
msgid "Timesheets on project (one fare per SO/Project)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__delivered_timesheet
msgid "Timesheets on tasks"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "To invoice"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#, python-format
msgid "Total"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid ""
"Total amount to invoice on the sales order, including all items (services, "
"storables, expenses, ...)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_total_duration
msgid "Total recorded duration, expressed in the encoding UoM"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__price_unit
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__price_unit
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__price_unit
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__price_unit
msgid "Unit price of the sales order item."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__amount_untaxed_invoiced
msgid "Untaxed Amount Invoiced"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__amount_untaxed_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_amount_untaxed_to_invoice
msgid "Untaxed Amount to Re-invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_amount_untaxed_invoiced
msgid "Untaxed Re-invoiced Amount"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project_overview.py:0
#, python-format
msgid ""
"What is still to deliver based on sold hours and hours already done. Equals "
"to sold hours - done hours."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__wizard_id
msgid "Wizard"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account.py:0
#, python-format
msgid ""
"You can not modify already invoiced timesheets (linked to a Sales order "
"items invoiced on Time and material)."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "You can only apply this action from a project."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"You cannot link the order item %s - %s to this task because it is a re-"
"invoiced expense."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"You have to unlink the task from the sale order item in order to delete it."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "billable_fixed"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "billable_time"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "canceled"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "non_billable"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "non_billable_project"
msgstr ""
