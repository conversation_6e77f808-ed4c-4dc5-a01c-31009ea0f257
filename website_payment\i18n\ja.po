# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_payment
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid ""
".\n"
"                        <br/>\n"
"                        We appreciate your support for our organization as such.\n"
"                        <br/>\n"
"                        Regards."
msgstr ""
"。\n"
"                        <br/>\n"
"                       そのような私たちの組織へのご支援に感謝いたします。\n"
"                        <br/>\n"
"                        引き続き宜しくお願い致します。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Comment:</b>"
msgstr "<b>コメント：</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donation Date:</b>"
msgstr "<b>寄付日：</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Email:</b>"
msgstr "<b>寄付者Eメール：</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Name:</b>"
msgstr "<b>寄付者：</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment ID:</b>"
msgstr "<b>支払ID：</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment Method:</b>"
msgstr "<b>支払方法：</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">国...</option>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>適用可能な決済オプションは見つかりませんでした</strong><br/>\n"
"                                エラーだと思われる場合は、サイトの管理者までお問い合わせください。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>警告</strong>通貨がないか、間違っています。"

#. module: website_payment
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "A donation has been made on your website"
msgstr "ウェブサイトに寄付がされました。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "A year of cultural awakening."
msgstr "文化的覚醒の1年。"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add a description here"
msgstr "説明を追加します"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add new pre-filled option"
msgstr "新しい事前入力オプションを追加する"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#, python-format
msgid "Amount"
msgstr "金額"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Amount ("
msgstr "金額（"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Amount("
msgstr "金額（"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Caring for a baby for 1 month."
msgstr "1ヶ月の赤ちゃんの世話。"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#, python-format
msgid "Choose Your Amount"
msgstr "金額を選択"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Country"
msgstr "国"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Country\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"国\n"
"                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Country is required."
msgstr "国が必要です。"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#: model_terms:ir.ui.view,arch_db:website_payment.donation_input
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
#, python-format
msgid "Custom Amount"
msgstr "金額をカスタム"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Dear"
msgstr "Dear"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Default Amount"
msgstr "デフォルト金額"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Display Options"
msgstr "オプションを表示"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Donate Now"
msgstr "今すぐ寄付する"

#. module: website_payment
#: model:mail.template,name:website_payment.mail_template_donation
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Donation"
msgstr "寄付"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donation amount must be at least %.2f."
msgstr "寄付金額は少なくとも%.2f以上である必要があります。"

#. module: website_payment
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Donation confirmation"
msgstr "寄付確認"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Donation notification"
msgstr "寄付通知"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Email"
msgstr "Eメール"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Email\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"Eメール\n"
"                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Email is invalid"
msgstr "Eメールが無効です"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Email is required."
msgstr "Eメールが必要です。"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Field '%s' is mandatory"
msgstr "フィールド '%s' は必須です"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Input"
msgstr "入力"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_payment__is_donation
msgid "Is Donation"
msgstr "寄付か"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_transaction__is_donation
msgid "Is donation"
msgstr "寄付か"

#. module: website_payment
#: model:ir.model.fields,help:website_payment.field_account_payment__is_donation
#: model:ir.model.fields,help:website_payment.field_payment_transaction__is_donation
msgid "Is the payment a donation"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Make a Donation"
msgstr "寄付をする"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Name"
msgstr "名称"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Name\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"氏名\n"
"                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Name is required."
msgstr "氏名が必要です。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "None"
msgstr "なし"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in elementary school."
msgstr "小学校一年間。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in high school."
msgstr "高校一年間。"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "決済サービス"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Payment Details"
msgstr "支払詳細"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "決済トランザクション"

#. module: website_payment
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Payment received from donation with following details:"
msgstr "寄付を受け付けました。詳細："

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_payment
msgid "Payments"
msgstr "支払"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "Please select or enter an amount"
msgstr "金額を選択するか入力して下さい"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Pre-filled Options"
msgstr "事前入力オプション"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Recipient Email"
msgstr "受信者メール"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "サーバーエラー"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Slider"
msgstr "スライダー"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Small or large, your contribution is essential."
msgstr "大小にかかわらず、あなたの貢献は非常に重要です。"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Some information is missing to process your payment."
msgstr "支払い処理のために不足している情報があります。"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Thank you for your donation of"
msgstr "以下のご寄付を頂きありがとうございます："

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "The minimum donation amount is %s%s%s"
msgstr "最小寄付金額は以下です： %s%s%s"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "There is nothing to pay."
msgstr "支払うものはありません。"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Validation Error"
msgstr "検証エラー"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "We could not obtain payment fees."
msgstr "支払手数料を得ることができませんでした。"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_acquirer__website_id
msgid "Website"
msgstr "ウェブサイト"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Write us a comment"
msgstr "コメントをお書き下さい"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Your comment"
msgstr "あなたのコメント"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "made on"
msgstr "以下で作成された"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Descriptions"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Maximum"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Minimum"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Step"
msgstr ""
