# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* digest
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"button\" id=\"button_open_report\">Open Report</span>"
msgstr "<span class=\"button\" id=\"button_open_report\">Ouvrir le Rapport</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"odoo_link_text\">Odoo</span>"
msgstr "<span class=\"odoo_link_text\">Odoo</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span style=\"color: #8f8f8f;\">Unsubscribe</span>"
msgstr "<span style=\"color: #8f8f8f;\">Se désabonner</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Activate"
msgstr "Activer"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__activated
msgid "Activated"
msgstr "Activé"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Add new users as recipient of a periodic email with key metrics"
msgstr ""
"Ajouter de nouveaux utilisateurs comme destinataires d'un email périodique "
"comprenant des métriques clés"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__group_id
msgid "Authorized Group"
msgstr "Groupe autorisé"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__available_fields
msgid "Available Fields"
msgstr "Champs disponibles"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Choose the metrics you care about"
msgstr "Choisissez les métriques qui vous intéressent"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__company_id
msgid "Company"
msgstr "Société"

#. module: digest
#: model:ir.model,name:digest.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Configure Digest Emails"
msgstr "Configurer les digests d'emails"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Connect"
msgstr "Connecter"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected
msgid "Connected Users"
msgstr "Utilisateurs connectés"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"Create or edit the mail template: you may get computed KPI's value using "
"these fields:"
msgstr ""
"Créez ou modifiez le modèle de courrier: vous pouvez obtenir la valeur "
"calculée des KPI en utilisant ces champs:"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_date
msgid "Created on"
msgstr "Créé le"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__currency_id
msgid "Currency"
msgstr "Devise"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__daily
msgid "Daily"
msgstr "Quotidien"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Deactivate for everyone"
msgstr "Désactiver pour tout le monde"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__deactivated
msgid "Deactivated"
msgstr "Désactivé"

#. module: digest
#: model:ir.model,name:digest.model_digest_digest
msgid "Digest"
msgstr "Digest"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_id
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Digest Email"
msgstr "Email Digest"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_digest_action
#: model:ir.actions.server,name:digest.ir_cron_digest_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:digest.ir_cron_digest_scheduler_action
#: model:ir.cron,name:digest.ir_cron_digest_scheduler_action
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_emails
#: model:ir.ui.menu,name:digest.digest_menu
msgid "Digest Emails"
msgstr "Digest d'Emails"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Digest Name"
msgstr "Nom du résumé"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "Digest Subscriptions"
msgstr "Abonnements Digest"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_tip_action
#: model:ir.model,name:digest.model_digest_tip
#: model:ir.ui.menu,name:digest.digest_tip_menu
msgid "Digest Tips"
msgstr "Conseils digest"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__display_name
#: model:ir.model.fields,field_description:digest.field_digest_tip__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "General"
msgstr "Général"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Group by"
msgstr "Regrouper par"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid ""
"Have a question about a document? Click on the responsible user's picture to"
" start a conversation. If his avatar has a green dot, he is online."
msgstr ""
"Une question à propos d'un document ? Cliquez sur la photo de l'utilisateur "
"responsable pour commencer une discussion. Si une boule verte apparait, il "
"est en ligne."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "How to customize your digest?"
msgstr "Comment personnaliser votre digest ?"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__id
#: model:ir.model.fields,field_description:digest.field_digest_tip__id
msgid "ID"
msgstr "ID"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "In order to build your customized digest, follow these steps:"
msgstr "Pour construire votre digest personnalisé, suivez ces étapes:"

#. module: digest
#: code:addons/digest/controllers/portal.py:0
#, python-format
msgid "Invalid periodicity set on digest"
msgstr "Périodicité invalide sur le Récapitulatif"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__is_subscribed
msgid "Is user subscribed"
msgstr "L'utilisateur est-il inscrit"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "KPI Digest"
msgstr "KPI Digest"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_form
msgid "KPI Digest Tip"
msgstr "Astuce de Récapitulatif"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_tree
msgid "KPI Digest Tips"
msgstr "Astuces de Récapitulatif"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "KPIs"
msgstr "KPIs"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total_value
msgid "Kpi Mail Message Total Value"
msgstr "Valeur totale du message Kpi Mail"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected_value
msgid "Kpi Res Users Connected Value"
msgstr "Valeur connectée des utilisateurs Kpi Res"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 24 hours"
msgstr "Dernières 24 heures"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 30 Days"
msgstr "Les 30 derniers jours"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 7 Days"
msgstr "Les 7 derniers jours"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest____last_update
#: model:ir.model.fields,field_description:digest.field_digest_tip____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total
msgid "Messages"
msgstr "Messages"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__monthly
msgid "Monthly"
msgstr "Mensuel"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__name
#: model:ir.model.fields,field_description:digest.field_digest_tip__name
msgid "Name"
msgstr "Nom"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid ""
"New users are automatically added as recipient of the following digest "
"email."
msgstr ""
"Les nouveaux utilisateurs sont ajoutés automatiquement comme destinataire du"
" digest d'email suivant."

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__next_run_date
msgid "Next Send Date"
msgstr "Prochaine date d'envoi"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Odoo Mobile"
msgstr "Odoo Mobile"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__periodicity
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Periodicity"
msgstr "Périodicité"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Powered by"
msgstr "Fourni par"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Prefer a broader overview ?"
msgstr "Vous préférez une vue d'ensemble plus large ?"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid ""
"Press ALT in any screen to highlight shortcuts for every button in the "
"screen. It is useful to process multiple documents in batch."
msgstr ""
"Appuyez sur ALT dans n'importe quel écran pour mettre en lumière les "
"raccourcis de tous les boutons. Surtout utile si vous traitez plusieurs "
"documents d'un coup."

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__quarterly
msgid "Quarterly"
msgstr "Trimestriel"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__user_ids
#: model:ir.model.fields,field_description:digest.field_digest_tip__user_ids
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Recipients"
msgstr "Destinataires"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Run your business from anywhere with <b>Odoo Mobile</b>."
msgstr "Gérez votre entreprise n'importe où avec <b>Odoo Mobile</b>."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Select your KPIs in the KPI's tab."
msgstr "Sléctionnez vos KPIs dans l'onglet KPI. "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Send Now"
msgstr "Envoyer maintenant"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Sent by"
msgstr "Envoyé par"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Statistics"
msgstr "Statistiques"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__state
msgid "Status"
msgstr "État"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Subscribe"
msgstr "S'inscrire"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Switch to weekly Digests"
msgstr "Passer en Récapitulatifs hebdomadaires"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__tip_description
msgid "Tip description"
msgstr "Description  du pourboire"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "Astuce: Une calculatrice dans Odoo"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "Astuce: Cliquez sur son avatar pour discuter avec un utilisateur"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr "Astuce: Comment mentionner des utilisateurs dans une note interne ?"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "Astuce: La connaissance, c'est le pouvoir"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "Astuce: Accélérez votre travail grâce aux raccourcis"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "Astuce:  Une calculatrice dans Odoo"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "Astuce: Cliquez sur son avatar pour discuter avec un utilisateur"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr "Astuce: Comment mentionner des utilisateurs dans une note interne ?"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "Astuce: La connaissance, c'est le pouvoir"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "Astuce: Accélérez votre travail grâce aux raccourcis"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid ""
"Type \"@\" to notify someone in a message, or \"#\" to link to a channel. "
"Try to notify @OdooBot to test the feature."
msgstr ""
"Saisissez \"@\" pour notifier quelqu'un dans un message, ou \"#\" pour un "
"canal de discussion. Essayez de notifier @OdooBot pour tester la "
"fonctionnalité."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Unsubscribe me"
msgstr "Désinscrivez-moi"

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__sequence
msgid "Used to display digest tip in email template base on order"
msgstr ""
"Utilisé pour afficher un résumé dans la base de modèles d'e-mails sur "
"commande"

#. module: digest
#: model:ir.model,name:digest.model_res_users
msgid "Users"
msgstr "Utilisateurs"

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__user_ids
msgid "Users having already received this tip"
msgstr "Utilisateurs ayant déjà reçu ce conseil"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Want to customize this email?"
msgstr "Personnaliser cet email ?"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid ""
"We have noticed you did not connect these last few days. We have "
"automatically switched your preference to %(new_perioridicy_str)s Digests."
msgstr ""
"Nous avons remarqué que vous ne vous étiez pas connecté ces derniers jours. "
"Nous avons automatiquement changé votre préférence en Récapitulatifs "
"%(new_perioridicy_str)s."

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__weekly
msgid "Weekly"
msgstr "Hebdomadaire"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid ""
"When editing a number, you can use formulae by typing the `=` character. "
"This is useful when computing a margin or a discount on a quotation, sale "
"order or invoice."
msgstr ""
"Lorsque vous saisissez un nombre, vous pouvez utiliser des formules en "
"commençant par le caractère \"=\". Ceci est par exemple très utile lorsque "
"vous calculez une marge ou une remise sur un devis, un bon de commande ou "
"une facture."

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid ""
"When following documents, use the pencil icon to fine-tune the information you want to receive.\n"
"Follow a project / sales team to keep track of this project's tasks / this team's opportunities."
msgstr ""
"Lorsque vous êtes abonné à un document, utilisez l'icône de crayon pour affiner les informations que vous désirez recevoir.\n"
"Suivez un projet ou une équipe commerciale pour garder un œil sur leurs tâches/opportunités respectives. "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "You have been successfully unsubscribed from"
msgstr "Vous avez été désinscrit avec succès de"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "You may want to add new computed fields with Odoo Studio:"
msgstr ""
"Vous pourriez vouloir ajouter un nouveau champ calculé avec Odoo Studio:"

#. module: digest
#: model:digest.digest,name:digest.digest_digest_default
msgid "Your Odoo Periodic Digest"
msgstr "Votre Récapitulatif Périodique Odoo"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "e.g. Your Weekly Digest"
msgstr "par exemple. Votre résumé hebdomadaire"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"first create a boolean field called\n"
"                                                <code>kpi_myfield</code>\n"
"                                                and display it in the KPI's tab;"
msgstr ""
"créez d'abord un champ booléen appelé\n"
" <code>kpi_myfield</code>\n"
" et affichez-le dans l'onglet KPIs;"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "monthly"
msgstr "mensuel"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "quarterly"
msgstr "trimestriel"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"then create a computed field called\n"
"                                                <code>kpi_myfield_value</code>\n"
"                                                that will compute your customized KPI."
msgstr ""
"puis créez un champ calculé appelé\n"
"<code>kpi_myfield_value</code>\n"
"qui calculera votre KPI personnalisé."

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "weekly"
msgstr "hebdomadaire"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"you must create 2 fields on the\n"
"                                                <code>digest</code>\n"
"                                                object:"
msgstr ""
"Vous devez créer 2 champs sur l'objet\n"
"                                                <code>digest</code>\n"
"                                                :"
