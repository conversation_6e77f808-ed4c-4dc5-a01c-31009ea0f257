from odoo import models, fields, api
from odoo.exceptions import UserError
from firebase_admin import auth


class FirebaseAuth(models.Model):
    _name = "firebase.auth"
    _description = "Firebase: Authentication"
    _sql_constraints = [
        ('ac_partner_uniq',
         'unique (account_id,partner_id)',
         'A partner could be defined only one time on same account.')
    ]

    account_id = fields.Many2one(
        comodel_name="firebase.account",
        string="Account",
        ondelete="cascade",
        required=True
    )
    partner_id = fields.Many2one(
        string="Partner",
        comodel_name="res.partner",
        ondelete="cascade",
        required=True
    )
    uuid = fields.Char(
        string="Firebase UUID"
    )
    user = fields.Char(
        string="User"
    )
    code = fields.Char(
        string="Default Pass"
    )
    error_reason = fields.Char(
        string="Error"
    )

    @api.model
    def generate_custom_token(self, uid, additional_claims={}, acc_id=False):
        AccObj = self.env['firebase.account'].sudo()
        if acc_id:
            account = AccObj.browse(acc_id)
        else:
            account = AccObj.search([], limit=1)
        if not account:
            raise UserError("Not account founded.")

        app = account._get_app()
        custom_token = auth.create_custom_token(uid, additional_claims, app=app)
        return custom_token

    def cron_remote_sync(self, limit=60):
        Auth_obj = self.env[self._name].sudo()
        users = Auth_obj.search([
            ('uuid', '=', False)
        ], limit=limit)
        for user in users:
            pass_val = getattr(user.partner_id, user.account_id.auth_field_pass.name, "")
            if not pass_val or len(pass_val) < 6:
                user.write({
                    'error_reason': "Password {} is not enough strong".format(pass_val)
                })
                continue
            user.code = pass_val
            app = user.account_id._get_app()
            fb_user = auth.create_user(
                app=app,
                display_name=user.partner_id.display_name,
                email=user.user,
                password=user.code
            )
            if fb_user and fb_user.uid:
                user.write({
                    'uuid': fb_user.uid
                })

    def cron_local_sync(self):
        auth_obj = self.env[self._name]
        accounts = self.env['firebase.account'].sudo().search([
            ('auth', '!=', 'off')
        ])
        for ac in accounts:
            if not ac.auth_field_pass or not ac.auth_field_user:
                continue
            all_partners = self.env['res.partner'].sudo().search(ac._get_auth_eval_domain())
            current_partners = self.env[self._name].sudo().search([]).mapped('partner_id')
            partners = list(set(all_partners) - set(current_partners))
            for p in partners:
                user_val = "{}{}".format(
                    getattr(p, ac.auth_field_user.name, ""),
                    ac.auth_field_user_sufix if ac.auth_field_user_sufix else ""
                )
                pass_val = getattr(p, ac.auth_field_pass.name, "")
                try:
                    auth_obj.sudo().create({
                        'account_id': ac.id,
                        'partner_id': p.id,
                        'user': user_val,
                        'code': pass_val,
                    })
                except:
                    pass

    def unlink(self):
        for user in self:
            app = user.account_id._get_app()
            auth.delete_user(user.uuid, app=app)
