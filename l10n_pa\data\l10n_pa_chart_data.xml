<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Account Chart Templates -->
    <record id="l10npa_chart_template" model="account.chart.template">
        <field name="name">Panamá - Plan de Cuentas</field>
        <field name="bank_account_code_prefix">111.</field>
        <field name="cash_account_code_prefix">113.</field>
        <field name="transfer_account_code_prefix">112.</field>
        <field name="code_digits">7</field>
        <field name="currency_id" ref="base.PAB"/>
        <field name="country_id" ref="base.pa"/>
    </record>
    <!-- Account Templates -->

            <record id="114_001" model="account.account.template"><field name="name">Caja y Bancos.../ BCO. CTA CTE PAB</field><field name="code">114.001</field><field name="reconcile" eval="True"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="115" model="account.account.template"><field name="name">Caja y Bancos - Valores a Depositar </field><field name="code">115</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="116" model="account.account.template"><field name="name">Caja y Bancos - Recaudaciones a Depositar </field><field name="code">116</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="121" model="account.account.template"><field name="name">Cuentas por Cobrar / Deudores por Ventas</field><field name="code">121</field><field name="reconcile" eval="True"/><field ref="account.data_account_type_receivable" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/><field name="reconcile">1</field></record>
            <record id="121_01" model="account.account.template"><field name="name">Cuentas por Cobrar / Deudores por Ventas (PoS)</field><field name="code">121.01</field><field name="reconcile" eval="True"/><field ref="account.data_account_type_receivable" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/><field name="reconcile">1</field></record>
            <record id="122" model="account.account.template"><field name="name">Cuentas por Cobrar / Deudores Morosos</field><field name="code">122</field><field name="reconcile" eval="True"/><field ref="account.data_account_type_receivable" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/><field name="reconcile">1</field></record>
            <record id="123" model="account.account.template"><field name="name">Cuentas por Cobrar / Deudores en Gestión Judicial</field><field name="code">123</field><field name="reconcile" eval="True"/><field ref="account.data_account_type_receivable" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="124" model="account.account.template"><field name="name">Cuentas por Cobrar / Deudores Varios</field><field name="code">124</field><field name="reconcile" eval="True"/><field ref="account.data_account_type_receivable" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="125" model="account.account.template"><field name="name">Cuentas por Cobrar / (-) Previsión para Incobrables</field><field name="code">125</field><field name="reconcile" eval="True"/><field ref="account.data_account_type_receivable" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/><field name="reconcile">1</field></record>
            <record id="131" model="account.account.template"><field name="name">Otras Cuentas por Cobrar / Préstamos otorgados</field><field name="code">131</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="132" model="account.account.template"><field name="name">Otras Cuentas por Cobrar / Anticipos a Proveedores</field><field name="code">132</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="133" model="account.account.template"><field name="name">Otras Cuentas por Cobrar / Anticipo de Impuestos</field><field name="code">133</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="134" model="account.account.template"><field name="name">Otras Cuentas por Cobrar / Anticipo al Personal</field><field name="code">134</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="135" model="account.account.template"><field name="name">Otras Cuentas por Cobrar / Alquileres Pagados por Adelantado</field><field name="code">135</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="136" model="account.account.template"><field name="name">Otras Cuentas por Cobrar / Intereses Pagados por Adelantado</field><field name="code">136</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="137" model="account.account.template"><field name="name">Otras Cuentas por Cobrar / Accionistas</field><field name="code">137</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="138" model="account.account.template"><field name="name">Otras Cuentas por Cobrar / (-) Previsión para Descuentos</field><field name="code">138</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="139" model="account.account.template"><field name="name">Otras Cuentas por Cobrar / (-) Intereses (+) a Devengar</field><field name="code">139</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="141" model="account.account.template"><field name="name">Inversiones / Acciones Transitorias</field><field name="code">141</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="142" model="account.account.template"><field name="name">Inversiones / Acciones Permanentes</field><field name="code">142</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="143" model="account.account.template"><field name="name">Inversiones / Títulos Públicos</field><field name="code">143</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="144" model="account.account.template"><field name="name">Inversiones / (-) Previsión para Devalorización de Acciones</field><field name="code">144</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="151_01" model="account.account.template"><field name="name">Inventarios - Mercancias / Categoria de productos 01</field><field name="code">151.01</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="152" model="account.account.template"><field name="name">Inventarios - Mercancias en Tránsito</field><field name="code">152</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="153" model="account.account.template"><field name="name">Materias primas</field><field name="code">153</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="154" model="account.account.template"><field name="name">Productos en Curso de Elaboración</field><field name="code">154</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="155" model="account.account.template"><field name="name">Productos Elaborados</field><field name="code">155</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="156" model="account.account.template"><field name="name">Materiales Varios </field><field name="code">156</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="157" model="account.account.template"><field name="name">(-) Previsión para Desvalorización de Inventarios</field><field name="code">157</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="161" model="account.account.template"><field name="name">Activo Fijo / Inmuebles</field><field name="code">161</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="162" model="account.account.template"><field name="name">Activo Fijo / Maquinaria</field><field name="code">162</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="163" model="account.account.template"><field name="name">Activo Fijo / Equipos</field><field name="code">163</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="164" model="account.account.template"><field name="name">Activo Fijo / Material Rodante Motorizado</field><field name="code">164</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="165" model="account.account.template"><field name="name">Activo Fijo / (-) Depreciación Acumulada</field><field name="code">165</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="171" model="account.account.template"><field name="name">Activo Intangible / Derecho de Llaves</field><field name="code">171</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="172" model="account.account.template"><field name="name">Activo Intangible / Concesiones y Franquicias</field><field name="code">172</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="173" model="account.account.template"><field name="name">Activo Intangible / Marcas y Patentes de Invención</field><field name="code">173</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="174" model="account.account.template"><field name="name">Activo Intangible / (-) Amortización Acumulada</field><field name="code">174</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_assets" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="211" model="account.account.template"><field name="name">Cuentas por Pagar / Proveedores</field><field name="code">211</field><field name="reconcile" eval="True"/><field ref="account.data_account_type_payable" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/><field name="reconcile">1</field></record>
            <record id="212" model="account.account.template"><field name="name">Cuentas por Pagar / Anticipos de Clientes</field><field name="code">212</field><field name="reconcile" eval="True"/><field ref="account.data_account_type_payable" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/><field name="reconcile">1</field></record>
            <record id="213" model="account.account.template"><field name="name">Cuentas por Pagar / (-) Intereses a Devengar por Compras al Crédito</field><field name="code">213</field><field name="reconcile" eval="True"/><field ref="account.data_account_type_payable" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/><field name="reconcile">1</field></record>
            <record id="221" model="account.account.template"><field name="name">Pasivo Circulante / Adelantos en Cuenta Corriente</field><field name="code">221</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="222" model="account.account.template"><field name="name">Pasivo Circulante / Prestamos</field><field name="code">222</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="223" model="account.account.template"><field name="name">Pasivo Circulante / Obligaciones a Pagar</field><field name="code">223</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="224" model="account.account.template"><field name="name">Pasivo Circulante / Intereses a Pagar</field><field name="code">224</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="225" model="account.account.template"><field name="name">Pasivo Circulante / Debentures Emitidos</field><field name="code">225</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="231" model="account.account.template"><field name="name">Impuestos por Pagar / ITBMS a Pagar</field><field name="code">231</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="232" model="account.account.template"><field name="name">Impuestos por Pagar / Impuesto sobre la Renta a Pagar</field><field name="code">232</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="241" model="account.account.template"><field name="name">Salarios por Pagar / Sueldos a Pagar</field><field name="code">241</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="242" model="account.account.template"><field name="name">Salarios por Pagar / Cargas Sociales a Pagar</field><field name="code">242</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="243" model="account.account.template"><field name="name">Salarios por Pagar / Provisión para Sueldo Anual Complementario</field><field name="code">243</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="244" model="account.account.template"><field name="name">Salarios por Pagar / Retenciones a Depositar</field><field name="code">244</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="251" model="account.account.template"><field name="name">Otras Cuentas por Pagar / Acreedores Varios</field><field name="code">251</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="252" model="account.account.template"><field name="name">Otras Cuentas por Pagar / Dividendos a Pagar</field><field name="code">252</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="253" model="account.account.template"><field name="name">Otras Cuentas por Pagar / Cobros por Adelantado</field><field name="code">253</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="254" model="account.account.template"><field name="name">Otras Cuentas por Pagar / Honorarios Directores y Síndicos a Pagar</field><field name="code">254</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="261" model="account.account.template"><field name="name">Provisiones / Previsión Indemnización por Despidos</field><field name="code">261</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="262" model="account.account.template"><field name="name">Provisiones / Previsión para juicios Pendientes</field><field name="code">262</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="263" model="account.account.template"><field name="name">Provisiones / Previsión para Garantías por Service</field><field name="code">263</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_current_liabilities" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="311" model="account.account.template"><field name="name">Capital / Capital Propio</field><field name="code">311</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="312" model="account.account.template"><field name="name">Capital / Acciones en Circulación</field><field name="code">312</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="313" model="account.account.template"><field name="name">Capital / Dividendos a Distribuir en Acciones</field><field name="code">313</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="314" model="account.account.template"><field name="name">Capital / (-) Descuento de Emisión de Acciones</field><field name="code">314</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="321" model="account.account.template"><field name="name">Aportes No Capitalizados / Primas de Emsión</field><field name="code">321</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="322" model="account.account.template"><field name="name">Aportes No Capitalizados / Aportes Irrevocables Futura Suscripción de Acciones</field><field name="code">322</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="331" model="account.account.template"><field name="name">Ajustes al Patrimonio / Revaluo Técnico de Activo Fijo</field><field name="code">331</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="341" model="account.account.template"><field name="name">Reserva Legal</field><field name="code">341</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="342" model="account.account.template"><field name="name">Reserva Estatutaria</field><field name="code">342</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="343" model="account.account.template"><field name="name">Reserva Facultativa</field><field name="code">343</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="344" model="account.account.template"><field name="name">Reserva para Renovación de Activo Fijo</field><field name="code">344</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="351" model="account.account.template"><field name="name">Resultados Acumulados</field><field name="code">351</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="352" model="account.account.template"><field name="name">Resultados Acumulados del Ejercicio Anterior</field><field name="code">352</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="353" model="account.account.template"><field name="name">Utilidades y Pérdidas del Ejercicio</field><field name="code">353</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="354" model="account.account.template"><field name="name">Resultado del Ejercicio</field><field name="code">354</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_equity" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>

            <record id="411_01" model="account.account.template"><field name="name">Ventas - Categoria de productos 01</field><field name="code">411.01</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_revenue" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="412" model="account.account.template"><field name="name">Intereses gananados, obtenidos, percibidos</field><field name="code">412</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="413" model="account.account.template"><field name="name">Alquileres gananados, obtenidos, percibidos</field><field name="code">413</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="414" model="account.account.template"><field name="name">Comisiones gananados, obtenidos, percibidos</field><field name="code">414</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="415" model="account.account.template"><field name="name">Descuentos gananados, obtenidos, percibidos</field><field name="code">415</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="416" model="account.account.template"><field name="name">Interese sobre Inversiones</field><field name="code">416</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="417" model="account.account.template"><field name="name">Honorarios gananados, obtenidos, percibidos</field><field name="code">417</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="418" model="account.account.template"><field name="name">Ganancia Venta de Acciones</field><field name="code">418</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="421" model="account.account.template"><field name="name">Recupero de Rezagos</field><field name="code">421</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="422" model="account.account.template"><field name="name">Recupero de Deudores Incobrables</field><field name="code">422</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="423" model="account.account.template"><field name="name">Ganancia Venta de Activo Fijo</field><field name="code">423</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="424" model="account.account.template"><field name="name">Donaciones obtenidas, ganandas, percibidas</field><field name="code">424</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="425" model="account.account.template"><field name="name">Ganancia Venta Inversiones Permanentes</field><field name="code">425</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_other_income" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="512" model="account.account.template"><field name="name">Gastos en Depreciación de Activo Fijo</field><field name="code">512</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="513" model="account.account.template"><field name="name">Gastos en Amortización</field><field name="code">513</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="514" model="account.account.template"><field name="name">Gastos en Salarios</field><field name="code">514</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="515" model="account.account.template"><field name="name">Gastos en Cargas Sociales</field><field name="code">515</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="516" model="account.account.template"><field name="name">Gastos en Impuestos</field><field name="code">516</field><field name="reconcile" eval="False"/><field ref="account.data_account_off_sheet" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="517" model="account.account.template"><field name="name">Gastos Bancarios</field><field name="code">517</field><field name="reconcile" eval="False"/><field ref="account.data_account_off_sheet" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="518" model="account.account.template"><field name="name">Gastos en Servicios Públicos</field><field name="code">518</field><field name="reconcile" eval="False"/><field ref="account.data_account_off_sheet" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="519" model="account.account.template"><field name="name">Gastos de Publicidad y Propaganda</field><field name="code">519</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="521" model="account.account.template"><field name="name">Gastos en Siniestros</field><field name="code">521</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="522" model="account.account.template"><field name="name">Donaciones Cedidas, Otorgadas</field><field name="code">522</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="523" model="account.account.template"><field name="name">Pérdida Venta Activo Fijo</field><field name="code">523</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>


            <record id="61_01" model="account.account.template"><field name="name">Costo de Venta - Categoria de productos 01</field><field name="code">61.01</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>

            <record id="62_01" model="account.account.template"><field name="name">Compras - Categoria de productos 01</field><field name="code">62.01</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="63" model="account.account.template"><field name="name">Costos de Producción</field><field name="code">63</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="64" model="account.account.template"><field name="name">Gastos de Administración</field><field name="code">64</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>
            <record id="65" model="account.account.template"><field name="name">Gastos de Comercialización</field><field name="code">65</field><field name="reconcile" eval="False"/><field ref="account.data_account_type_expenses" name="user_type_id"/><field name="chart_template_id" ref="l10npa_chart_template"/></record>

            <record id="711" model="account.account.template"><field name="name">Mercaderias Recibidas en Consignación</field><field name="code">711</field><field name="reconcile" eval="False"/><field ref="account.data_account_off_sheet" name="user_type_id"/></record>
            <record id="712" model="account.account.template"><field name="name">Depósito de Valores Recibos en Garantía</field><field name="code">712</field><field name="reconcile" eval="False"/><field ref="account.data_account_off_sheet" name="user_type_id"/></record>
            <record id="713" model="account.account.template"><field name="name">Garantias Otorgadas</field><field name="code">713</field><field name="reconcile" eval="False"/><field ref="account.data_account_off_sheet" name="user_type_id"/></record>
            <record id="714" model="account.account.template"><field name="name">Documentos Descontados</field><field name="code">714</field><field name="reconcile" eval="False"/><field ref="account.data_account_off_sheet" name="user_type_id"/></record>
            <record id="715" model="account.account.template"><field name="name">Documentos Endosados</field><field name="code">715</field><field name="reconcile" eval="False"/><field ref="account.data_account_off_sheet" name="user_type_id"/></record>

            <record id="721" model="account.account.template"><field name="name">Comitente por Mercaderias Recibidas en Consignación</field><field name="code">721</field><field name="reconcile" eval="False"/><field ref="account.data_account_off_sheet" name="user_type_id"/></record>
            <record id="722" model="account.account.template"><field name="name">Acreedor por Garantías Otorgadas</field><field name="code">722</field><field name="reconcile" eval="False"/><field ref="account.data_account_off_sheet" name="user_type_id"/></record>
            <record id="723" model="account.account.template"><field name="name">Acreedor por Documentos Descontados</field><field name="code">723</field><field name="reconcile" eval="False"/><field ref="account.data_account_off_sheet" name="user_type_id"/></record>

        <record id="gain81_01" model="account.account.template">
            <field name="code">81</field>
            <field name="name">Cuenta de cambio (Ganancia)</field>
            <field name="user_type_id" ref="account.data_account_type_other_income"/>
            <field name="chart_template_id" ref="l10npa_chart_template"/>
        </record>

        <record id="loss81_01" model="account.account.template">
            <field name="code">82</field>
            <field name="name">Cuenta de cambio (Pérdida)</field>
            <field name="user_type_id" ref="account.data_account_type_expenses"/>
            <field name="chart_template_id" ref="l10npa_chart_template"/>
        </record>


    <record id="l10npa_chart_template" model="account.chart.template">
      <field name="property_account_receivable_id" ref="121"/>
      <field name="property_account_payable_id" ref="211"/>
      <field name="property_account_expense_categ_id" ref="62_01"/>
      <field name="property_account_income_categ_id" ref="411_01"/>
      <field name="income_currency_exchange_account_id" ref="gain81_01"/>
      <field name="expense_currency_exchange_account_id" ref="loss81_01"/>
      <field name="default_pos_receivable_account_id" ref="121_01" />
    </record>
</odoo>
