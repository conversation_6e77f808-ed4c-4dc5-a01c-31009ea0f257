<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.AttachmentViewer" owl="1">
        <div class="o_AttachmentViewer" t-on-click="_onClick" t-on-keydown="_onKeydown" tabindex="0">
            <t t-if="attachmentViewer">
                <div class="o_AttachmentViewer_header" t-on-click="_onClickHeader">
                    <t t-if="attachmentViewer.attachment.isViewable">
                        <div class="o_AttachmentViewer_headerItem o_AttachmentViewer_icon">
                            <t t-if="attachmentViewer.attachment.isImage">
                                <i class="fa fa-picture-o" role="img" title="Image"/>
                            </t>
                            <t t-if="attachmentViewer.attachment.isPdf">
                                <i class="fa fa-file-text" role="img" title="PDF file"/>
                            </t>
                            <t t-if="attachmentViewer.attachment.isText">
                                <i class="fa fa-file-text" role="img" title="Text file"/>
                            </t>
                            <t t-if="attachmentViewer.attachment.isVideo">
                                <i class="fa fa-video-camera" role="img" title="Video"/>
                            </t>
                        </div>
                    </t>
                    <div class="o_AttachmentViewer_headerItem o_AttachmentViewer_name">
                        <span class="o_AttachmentViewer_nameText text-truncate" t-esc="attachmentViewer.attachment.displayName"/>
                    </div>
                    <div class="o-autogrow"/>
                    <div class="o_AttachmentViewer_buttonDownload o_AttachmentViewer_headerItem o_AttachmentViewer_headerItemButton" t-on-click="_onClickDownload" role="button" title="Download">
                        <i class="o_AttachmentViewer_headerItemButtonIcon fa fa-download fa-fw" t-att-class="{ 'o-hasLabel': messaging.device.sizeClass > env.device.SIZES.MD }" role="img"/>
                        <t t-if="messaging.device.sizeClass > env.device.SIZES.MD">
                            <span>Download</span>
                        </t>
                    </div>
                    <div class="o_AttachmentViewer_headerItem o_AttachmentViewer_headerItemButton o_AttachmentViewer_headerItemButtonClose" t-on-click="_onClickClose" role="button" title="Close (Esc)" aria-label="Close">
                        <i class="fa fa-fw fa-times" role="img"/>
                    </div>
                </div>
                <div class="o_AttachmentViewer_main" t-att-class="{ o_with_img: attachmentViewer.attachment.isImage }" t-on-mousemove="_onMousemoveView">
                    <t t-if="attachmentViewer.attachment.isImage">
                        <div class="o_AttachmentViewer_zoomer" t-ref="zoomer">
                            <t t-if="attachmentViewer.isImageLoading">
                                <div class="o_AttachmentViewer_loading">
                                    <i class="fa fa-3x fa-circle-o-notch fa-fw fa-spin" role="img" title="Loading"/>
                                </div>
                            </t>
                            <img class="o_AttachmentViewer_view o_AttachmentViewer_viewImage" t-on-click="_onClickImage" t-on-mousedown="_onMousedownImage" t-on-wheel="_onWheelImage" t-on-load="_onLoadImage" t-att-src="attachmentViewer.imageUrl" t-att-style="imageStyle" draggable="false" alt="Viewer" t-key="'image_' + attachmentViewer.attachment.id" t-ref="image_{{ attachmentViewer.attachment.id }}"/>
                        </div>
                    </t>
                    <t t-if="attachmentViewer.attachment.isPdf">
                        <iframe class="o_AttachmentViewer_view o_AttachmentViewer_viewIframe o_AttachmentViewer_viewPdf" t-ref="iframeViewerPdf" t-att-class="{ 'o-isMobile': messaging.device.isMobile }" t-att-src="attachmentViewer.attachment.defaultSource"/>
                    </t>
                    <t t-if="attachmentViewer.attachment.isText">
                        <iframe class="o_AttachmentViewer_view o_AttachmentViewer_viewIframe o_text" t-att-src="attachmentViewer.attachment.defaultSource"/>
                    </t>
                    <t t-if="attachmentViewer.attachment.isUrlYoutube">
                        <iframe allow="autoplay; encrypted-media" class="o_AttachmentViewer_view o_AttachmentViewer_viewIframe o_AttachmentViewer_youtube" t-att-src="attachmentViewer.attachment.defaultSource" height="315" width="560"/>
                    </t>
                    <t t-if="attachmentViewer.attachment.isVideo">
                        <video class="o_AttachmentViewer_view o_AttachmentViewer_viewVideo" t-att-class="{ 'o-isMobile': messaging.device.isMobile }" t-on-click="_onClickVideo" controls="controls">
                            <source t-att-data-type="attachmentViewer.attachment.mimetype" t-att-src="attachmentViewer.attachment.defaultSource"/>
                        </video>
                    </t>
                </div>
                <t t-if="attachmentViewer.attachment.isImage">
                    <div class="o_AttachmentViewer_toolbar" role="toolbar">
                        <div class="o_AttachmentViewer_toolbarButton" t-on-click="_onClickZoomIn" title="Zoom In (+)" role="button">
                            <i class="fa fa-fw fa-plus" role="img"/>
                        </div>
                        <div class="o_AttachmentViewer_toolbarButton" t-att-class="{ o_disabled: attachmentViewer.scale === 1 }" t-on-click="_onClickZoomReset" role="button" title="Reset Zoom (0)">
                            <i class="fa fa-fw fa-search" role="img"/>
                        </div>
                        <div class="o_AttachmentViewer_toolbarButton" t-att-class="{ o_disabled: attachmentViewer.scale === MIN_SCALE }" t-on-click="_onClickZoomOut" title="Zoom Out (-)" role="button">
                            <i class="fa fa-fw fa-minus" role="img"/>
                        </div>
                        <div class="o_AttachmentViewer_toolbarButton" t-on-click="_onClickRotate" title="Rotate (r)" role="button">
                            <i class="fa fa-fw fa-repeat" role="img"/>
                        </div>
                        <div class="o_AttachmentViewer_toolbarButton" t-on-click="_onClickPrint" title="Print" role="button">
                            <i class="fa fa-fw fa-print" role="img"/>
                        </div>
                        <div class="o_AttachmentViewer_buttonDownload o_AttachmentViewer_toolbarButton" t-on-click="_onClickDownload" title="Download" role="button">
                            <i class="fa fa-download fa-fw" role="img"/>
                        </div>
                    </div>
                </t>
                <t t-if="attachmentViewer.attachments.length > 1">
                    <div class="o_AttachmentViewer_buttonNavigation o_AttachmentViewer_buttonNavigationPrevious" t-on-click="_onClickPrevious" title="Previous (Left-Arrow)" role="button">
                        <span class="fa fa-chevron-left" role="img"/>
                    </div>
                    <div class="o_AttachmentViewer_buttonNavigation o_AttachmentViewer_buttonNavigationNext" t-on-click="_onClickNext" title="Next (Right-Arrow)" role="button">
                        <span class="fa fa-chevron-right" role="img"/>
                    </div>
                </t>
            </t>
        </div>
    </t>

</templates>
