<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <!--     Opportunity tree view  -->
        <record id="view_report_crm_partner_assign_filter" model="ir.ui.view">
            <field name="name">crm.partner.report.assign.select</field>
            <field name="model">crm.partner.report.assign</field>
            <field name="arch" type="xml">
                <search string="Partner assigned Analysis">
                    <field name="team_id"/>
                    <field name="user_id"/>
                    <field name="grade_id"/>
                    <field name="activation"/>
                    <filter name="filter_date_partnership" date="date_partnership"/>
                    <filter name="filter_date_review" date="date_review"/>
                    <group  expand="1" string="Group By">
                        <filter string="Salesperson" name="user"
                            context="{'group_by':'user_id'}" />
                        <filter string="Sales Team" name="sales_team"
                            context="{'group_by':'team_id'}"/>
                        <filter string="Partner" name="partner"
                            context="{'group_by':'partner_id'}" />
                        <separator/>
                        <filter string="Date Partnership" name="group_date_partnership"
                            context="{'group_by':'date_partnership'}" />
                        <filter string="Date Review" name="group_date_review"
                            context="{'group_by':'date_review'}" />
                    </group>
                </search>
            </field>
        </record>

       <record id="view_report_crm_partner_assign_graph" model="ir.ui.view">
            <field name="name">crm.partner.assign.report.graph</field>
            <field name="model">crm.partner.report.assign</field>
            <field name="arch" type="xml">
                <graph string="Opportunities Assignment Analysis" sample="1" disable_linking="1">
                    <field name="grade_id"/>
                    <field name="nbr_opportunities" type="measure"/>
                    <field name="turnover" type="measure"/>
                </graph>
            </field>
       </record>

       <!-- Leads by user and team Action -->

       <record id="action_report_crm_partner_assign" model="ir.actions.act_window">
            <field name="name">Partnership Analysis</field>
            <field name="res_model">crm.partner.report.assign</field>
            <field name="context">{'group_by_no_leaf':1,'group_by':[]}</field>
            <field name="view_mode">graph</field>
            <field name="domain">[('grade_id', '!=', False)]</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No data yet!
                </p>
            </field>
        </record>

       <menuitem name="Partnerships" id="menu_report_crm_partner_assign_tree"
           parent="crm.crm_menu_report" action="action_report_crm_partner_assign" sequence="5"/>

</odoo>
