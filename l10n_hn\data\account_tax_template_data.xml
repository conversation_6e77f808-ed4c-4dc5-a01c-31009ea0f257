<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Compras e ISV por Cobrar -->

        <record id="impuestos_plantilla_isv_por_cobrar" model="account.tax.template">
            <field name="chart_template_id" ref="cuentas_plantilla"/>
            <field name="name">ISV por Cobrar</field>
            <field name="description">ISV por Cobrar</field>
            <field name="amount" eval="15"/>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include" eval="True"/>
            <field name="tax_group_id" ref="tax_group_iva_15"/>
             <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('cta110301'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('cta110301'),
                }),
            ]"/>
        </record>

        <!-- Ventas e ISV por Pagar -->

        <record id="impuestos_plantilla_isv_por_pagar" model="account.tax.template">
            <field name="chart_template_id" ref="cuentas_plantilla"/>
            <field name="name">ISV por Pagar</field>
            <field name="description">ISV por Pagar</field>
            <field name="amount" eval="15"/>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include" eval="True"/>
            <field name="tax_group_id" ref="tax_group_iva_15"/>
             <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('cta210201'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('cta210201'),
                }),
            ]"/>
        </record>

    </data>
</odoo>
