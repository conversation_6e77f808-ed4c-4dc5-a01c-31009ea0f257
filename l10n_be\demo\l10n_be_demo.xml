<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_be.a100" model="account.account.template">
        <field name="tag_ids" eval="[(4, ref('account.demo_capital_account'))]"/>
    </record>
    <record id="l10n_be.a300" model="account.account.template">
        <field name="tag_ids" eval="[(4, ref('account.demo_stock_account'))]"/>
    </record>
    <record id="l10n_be.a7600" model="account.account.template">
        <field name="tag_ids" eval="[(4, ref('account.demo_sale_of_land_account'))]"/>
    </record>
    <record id="l10n_be.a6201" model="account.account.template">
        <field name="tag_ids" eval="[(4, ref('account.demo_ceo_wages_account'))]"/>
    </record>
    <record id="l10n_be.a242" model="account.account.template">
        <field name="tag_ids" eval="[(4, ref('account.demo_office_furniture_account'))]"/>
    </record>
    <function model="account.account" name="write">
        <value model="account.account" search="[('code', 'like', obj().env.ref('l10n_be.a100').code)]"/>
        <value eval="{'tag_ids': [(4, ref('account.demo_capital_account'))]}"/>
    </function>
    <function model="account.account" name="write">
        <value model="account.account" search="[('code', 'like', obj().env.ref('l10n_be.a300').code)]"/>
        <value eval="{'tag_ids': [(4, ref('account.demo_stock_account'))]}"/>
    </function>
    <function model="account.account" name="write">
        <value model="account.account" search="[('code', 'like', obj().env.ref('l10n_be.a7600').code)]"/>
        <value eval="{'tag_ids': [(4, ref('account.demo_sale_of_land_account'))]}"/>
    </function>
    <function model="account.account" name="write">
        <value model="account.account" search="[('code', 'like', obj().env.ref('l10n_be.a6201').code)]"/>
        <value eval="{'tag_ids': [(4, ref('account.demo_ceo_wages_account'))]}"/>
    </function>
    <function model="account.account" name="write">
        <value model="account.account" search="[('code', 'like', obj().env.ref('l10n_be.a242').code)]"/>
        <value eval="{'tag_ids': [(4, ref('account.demo_office_furniture_account'))]}"/>
    </function>
</odoo>
