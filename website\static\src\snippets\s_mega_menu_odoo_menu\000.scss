
.s_mega_menu_odoo_menu:not([data-vcss]) {
    .s_mega_menu_odoo_menu_footer {
        // Apply color transparency to match with the preset used
        border-color: rgba(0, 0, 0, .05);

        .row > div:not(.o_cc) {
            // TODO this should be replaced by proper structure and color
            // classes
            @extend %s_mega_menu_gray_area;

            &::before {
                @include o-position-absolute(0, 0, 0, 0);
                width: auto;
                height: auto;
            }
        }
    }
}
