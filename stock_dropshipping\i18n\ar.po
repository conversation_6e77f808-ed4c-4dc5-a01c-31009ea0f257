# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_dropshipping
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_res_company
msgid "Companies"
msgstr "الشركات "

#. module: stock_dropshipping
#: model:stock.location.route,name:stock_dropshipping.route_drop_shipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.purchase_order_form_inherit_stock_dropshipping
#: model_terms:ir.ui.view,arch_db:stock_dropshipping.view_order_form_inherit_sale_stock
msgid "Dropship"
msgstr "إحالة الشحن "

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_purchase_order__dropship_picking_count
#: model:ir.model.fields,field_description:stock_dropshipping.field_sale_order__dropship_picking_count
msgid "Dropship Count"
msgstr "عدد إحالات الشحن "

#. module: stock_dropshipping
#: model:ir.model.fields,field_description:stock_dropshipping.field_stock_picking__is_dropship
msgid "Is a Dropship"
msgstr "إحالة شحن "

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_procurement_group
msgid "Procurement Group"
msgstr "مجموعة الشراء "

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_purchase_order
msgid "Purchase Order"
msgstr "أمر شراء"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "بند أمر الشراء"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order
msgid "Sales Order"
msgstr "أمر البيع"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_rule
msgid "Stock Rule"
msgstr "قاعدة المخزون"

#. module: stock_dropshipping
#: model:ir.model,name:stock_dropshipping.model_stock_picking
msgid "Transfer"
msgstr "الشحنة"
