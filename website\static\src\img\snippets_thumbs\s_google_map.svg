<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <linearGradient id="linearGradient-1" x1="0%" x2="100%" y1="23.231%" y2="76.769%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <path id="path-2" d="M10 6.5c0-.966-.342-1.791-1.025-2.475A3.372 3.372 0 0 0 6.5 3c-.966 0-1.791.342-2.475 1.025A3.372 3.372 0 0 0 3 6.5c0 .966.342 1.791 1.025 2.475A3.372 3.372 0 0 0 6.5 10c.966 0 1.791-.342 2.475-1.025A3.372 3.372 0 0 0 10 6.5zm3 .167c0 .946-.14 1.723-.419 2.33L7.96 19.076a1.566 1.566 0 0 1-.603.677 1.6 1.6 0 0 1-1.714 0 1.488 1.488 0 0 1-.59-.677L.419 8.997C.139 8.39 0 7.613 0 6.667c0-1.84.635-3.412 1.904-4.714C3.174.651 4.706 0 6.5 0s3.326.651 4.596 1.953S13 4.826 13 6.667z"/>
    <filter id="filter-4" width="107.7%" height="110%" x="-3.8%" y="-2.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_google_map">
      <g fill="url(#linearGradient-1)" class="image_1" opacity=".4">
        <path d="M23.033 36.864L32.38 60H0V44.348l23.033-7.484zM60.346 24.74L74.592 60H55.05L43.071 30.353l17.275-5.613zM82 17.705V60h-4.567L62.858 23.924 82 17.704zM40.56 31.169l11.65 28.83H35.22l-9.677-23.951 15.015-4.88zM27.966-.001L39.57 28.722 0 41.579V0h27.966zM50.35 0l9.006 22.293-17.274 5.613L30.807 0H50.35zM82 0v14.935l-20.132 6.54L53.191 0H82z" class="combined_shape"/>
      </g>
      <g class="group" transform="translate(17 14)">
        <mask id="mask-3" fill="#fff">
          <use xlink:href="#path-2"/>
        </mask>
        <g class="map_marker">
          <use fill="#000" filter="url(#filter-4)" xlink:href="#path-2"/>
          <use fill="#FFF" fill-opacity=".95" xlink:href="#path-2"/>
        </g>
      </g>
    </g>
  </g>
</svg>
