# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * mail
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-08-24 09:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: mail
#: code:addons/mail/models/mail_channel.py:921
#, python-format
msgid " This channel is private. People must be invited to join it."
msgstr ""
"Ovaj kanal je privatni. Ljudi moraju biti pozvani da bi se priključili."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:945
#, python-format
msgid "%d Messages"
msgstr "%d Poruka"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:89
#, python-format
msgid "%d days overdue"
msgstr "%d dan(a) prekoračenja"

#. module: mail
#: code:addons/mail/models/mail_template.py:249
#, python-format
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/thread_typing_mixin/thread_typing_mixin.js:133
#, python-format
msgid "%s and %s are typing..."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:278
#, python-format
msgid "%s created"
msgstr "%s kreiran"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/thread_typing_mixin/thread_typing_mixin.js:131
#, python-format
msgid "%s is typing..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/thread_typing_mixin/thread_typing_mixin.js:137
#, python-format
msgid "%s, %s and more are typing..."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_activity.py:341
#, python-format
msgid "%s: %s assigned to you"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:285
#, python-format
msgid "&nbsp;("
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:326
#, python-format
msgid "(from"
msgstr "(od"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:56
#, python-format
msgid ", due on"
msgstr ", prekoračeno na"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:547
#, python-format
msgid "-------- Show older messages --------"
msgstr "-------- Prikaži starije poruke --------"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:99
#, python-format
msgid "0 Future"
msgstr "0 Budućih"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:95
#, python-format
msgid "0 Late"
msgstr "0 Kasni"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:97
#, python-format
msgid "0 Today"
msgstr "0 Danas"

#. module: mail
#: code:addons/mail/models/mail_channel.py:926
#, python-format
msgid ""
"<br><br>\n"
"            Type <b>@username</b> to mention someone, and grab his attention.<br>\n"
"            Type <b>#channel</b>.to mention a channel.<br>\n"
"            Type <b>/command</b> to execute a command.<br>\n"
"            Type <b>:shortcut</b> to insert canned responses in your message.<br>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:721
#, python-format
msgid ""
"<div class=\"o_mail_notification\">%(author)s invited %(new_partner)s to <a "
"href=\"#\" class=\"o_channel_redirect\" data-oe-"
"id=\"%(channel_id)s\">#%(channel_name)s</a></div>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:828
#, python-format
msgid ""
"<div class=\"o_mail_notification\">created <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">kreiran <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:728
#: code:addons/mail/models/mail_channel.py:802
#, python-format
msgid ""
"<div class=\"o_mail_notification\">joined <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">priključen <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:294
#, python-format
msgid ""
"<div class=\"o_mail_notification\">left <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">ostalo <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/wizard/invite.py:23
#, python-format
msgid ""
"<div><p>Hello,</p><p>%s invited you to follow %s document: %s.</p></div>"
msgstr ""
"<div><p>Zdravo,</p><p>%s Vas je pozvao da pratite %s dokument: %s.</p></div>"

#. module: mail
#: code:addons/mail/wizard/invite.py:26
#, python-format
msgid ""
"<div><p>Hello,</p><p>%s invited you to follow a new document.</p></div>"
msgstr ""
"<div><p>Zdravo,</p><p>%s Vas je povao da pratite novi dokument.</p></div>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:28
#, python-format
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"<p><b>Chat-ujte sa suradnicima</b> u stvarnom vremenu koristeći direktne "
"poruke.</p><p><i>Možda prvo morate da pozovete korisnike iz menija "
"postavki.</i></p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:15
#, python-format
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p>Ovdje <b>Napišite poruku</b> članovima kanala.</p> <p>Možete da "
"notificirate nekoga sa <i>'@'</i> ili povežete drugi kanal sa  <i>'#'</i>. "
"Započnite poruku sa <i>'/'</i> da dobijete listu mogućih komandi.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:11
#, python-format
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>Kanali vam omogućavaju da na jednostavan način organizujete informacije "
"preko različitih tema i grupa.</p> <p>Pokušajte da <b>kreirate vaš prvi "
"kanal</b> (npr.: prodaj, marketing, proizvod XYZ, zabava nakon posla, "
"itd.).</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/create_mode_document_thread.js:65
#, python-format
msgid "<p>Creating a new record...</p>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email mass mailing</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">the selected records</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">the current search filter</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Followers of the document and</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    If you want to send it for all the records matching your search criterion, check this box :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    If you want to use only selected records please uncheck this selection box :\n"
"                                </span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this mail again to the recipients you did not select."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Activities</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Dodaj</span>\n"
"                                    <span class=\"o_stat_text\">Kontekstnu akciju</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Ukloni</span>\n"
"                                    <span class=\"o_stat_text\">Kontekstnu akciju</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>\n"
"                                    All records matching your current search filter will be mailed,\n"
"                                    not only the ids selected in the list view.\n"
"                                </strong><br/>\n"
"                                The email will be sent for all the records selected in the list.<br/>\n"
"                                Confirming this wizard will probably take a few minutes blocking your browser."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong> Feedback</strong>"
msgstr "<strong> Povratna informacija</strong>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>Only records checked in list view will be used.</strong><br/>\n"
"                                The email will be sent for all the records selected in the list."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "<strong>Recommended Activities</strong>"
msgstr "<strong>Predložene aktivnosti</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_channel__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Python dictionary koji će se provjeravati za zadane postave kada se kreira "
"novi zapis za ovaj alias."

#. module: mail
#: code:addons/mail/models/ir_actions.py:69
#, python-format
msgid "A next activity can only be planned on models that use the chatter"
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"A shortcode is a keyboard shortcut. For instance, you type #gm and it will "
"be transformed into \"Good Morning\"."
msgstr ""
"Kratki kod je prečica tastature. Na primjer, ukoliko unesete #gm, to će se "
"promjeniti u \"Good Morning\"."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:185
#: code:addons/mail/static/src/xml/thread.xml:412
#, python-format
msgid "Accept"
msgstr "Prihvati"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:185
#, python-format
msgid "Accept selected messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:412
#, python-format
msgid "Accept |"
msgstr ""

#. module: mail
#: selection:mail.compose.message,moderation_status:0
#: selection:mail.message,moderation_status:0
msgid "Accepted"
msgstr "Prihvaćeno"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Pristupne grupe"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
msgid "Action To Do"
msgstr "Akcija za uraditi"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Standardno aktivirano kod pretplate"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Active"
msgstr "Aktivan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__active_domain
msgid "Active domain"
msgstr "Aktivni domen"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:108
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model:mail.message.subtype,name:mail.mt_activities
#, python-format
msgid "Activities"
msgstr "Aktivnosti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_view.js:15
#: code:addons/mail/static/src/xml/chatter.xml:93
#: code:addons/mail/static/src/xml/systray.xml:74
#: selection:ir.actions.act_window.view,view_mode:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
#: selection:ir.ui.view,type:0
#, python-format
msgid "Activity"
msgstr "Aktivnost"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
msgid "Activity State"
msgstr "Status aktivnosti"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Tip aktivnosti"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Tipovi aktivnosti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
msgid "Activity User Type"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:37
#, python-format
msgid "Activity type"
msgstr "Tip aktivnosti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:111
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:63
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__channel_ids
#, python-format
msgid "Add Channels"
msgstr "Dodaj kanale"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:62
#: selection:ir.actions.server,state:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "Dodaj pratioce"

#. module: mail
#: code:addons/mail/models/ir_actions.py:63
#, python-format
msgid "Add Followers can only be done on a mail thread model"
msgstr "Dodavanje pratioca može samo da se odradi na modelu mail thread"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_mail__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_message__add_sign
msgid "Add Sign"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__user_signature
#: model:ir.model.fields,field_description:mail.field_mail_template__user_signature
msgid "Add Signature"
msgstr "Dodaj potpis"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:52
#: code:addons/mail/static/src/xml/discuss.xml:242
#, python-format
msgid "Add a channel"
msgstr "Dodaj kanal"

#. module: mail
#: code:addons/mail/models/mail_thread.py:383
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:71
#, python-format
msgid "Add a private channel"
msgstr "Dodaj privatni kanal"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address in the blacklist"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:15
#, python-format
msgid "Add attachment"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add channels to notify..."
msgstr "Dodaj kanale da obavjestite..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "Dodaj kontakte kojima ide obavijest..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:418
#, python-format
msgid "Add this email address to white list of people"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Dodatni kontakti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Napredno"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Advanced Settings"
msgstr "Napredna podešavanja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
msgid "After"
msgstr "Nakon"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:26
#, python-format
msgid "Alarm menu"
msgstr ""

#. module: mail
#: selection:mail.activity.type,decoration_type:0
msgid "Alert"
msgstr "Upozorenje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_id
#: model:ir.model.fields,field_description:mail.field_res_users__alias_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_tree
msgid "Alias"
msgstr "Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_contact
#: model:ir.model.fields,field_description:mail.field_res_users__alias_contact
msgid "Alias Contact Security"
msgstr "Sigurnosni nadimak kontakta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain
msgid "Alias Domain"
msgstr "Alias domena"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_name
msgid "Alias Name"
msgstr "Naziv nadimka"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_domain
msgid "Alias domain"
msgstr "Alias domena"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_model_id
msgid "Aliased Model"
msgstr "Zamjenski model"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_alias
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Aliasi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:20
#: code:addons/mail/static/src/xml/systray.xml:31
#, python-format
msgid "All"
msgstr "Sve"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:46
#, python-format
msgid "All pages:&nbsp;"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Allowed Emails"
msgstr ""

#. module: mail
#: selection:ir.model.fields,track_visibility:0
msgid "Always"
msgstr "Uvijek"

#. module: mail
#: selection:mail.moderation,status:0
msgid "Always Allow"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:418
#, python-format
msgid "Always Allow |"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/mail_failure.js:80
#, python-format
msgid "An error occured when sending an email"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_channel.py:925
#: code:addons/mail/static/src/js/models/messages/message.js:148
#, python-format
msgid "Anonymous"
msgstr "Anoniman"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__no_auto_thread
#: model:ir.model.fields,help:mail.field_mail_mail__no_auto_thread
#: model:ir.model.fields,help:mail.field_mail_message__no_auto_thread
msgid ""
"Answers do not go in the original document discussion thread. This has an "
"impact on the generated message-id."
msgstr ""
"Odgovori ne idu u nit razgovora originalnog dokumenta. Ovo ima uticaja na "
"generisani id poruke."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Odnosi se na"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:357
#, python-format
msgid "Apply"
msgstr "Primjeni"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
msgid "Archived"
msgstr "Arhivirano"

#. module: mail
#: code:addons/mail/wizard/mail_resend_message.py:122
#, python-format
msgid ""
"Are you sure you want to discard %s mail delivery failures. You won't be "
"able to re-send these mails later!"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:52
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#, python-format
msgid "Assigned to"
msgstr "Dodjeljeno"

#. module: mail
#: code:addons/mail/models/mail_activity.py:258
#: code:addons/mail/models/mail_activity.py:265
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "Zakači datoteku"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
msgid "Attachment"
msgstr "Zakačka"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:11
#: model:ir.model.fields,field_description:mail.field_email_template_preview__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "Prilozi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_message__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""
"Zakačke su povezane sa dokumentom preko model / res_id i sa porukom putem "
"ovog polja."

#. module: mail
#: selection:mail.alias,alias_contact:0
msgid "Authenticated Employees"
msgstr "Prijavljeni zaposlenici"

#. module: mail
#: selection:mail.alias,alias_contact:0
msgid "Authenticated Partners"
msgstr "Prijavljeni partneri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Autor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Author Signature (mass mail only)"
msgstr "Potpis autora (samo masovne poruke)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Autor poruke. Ako nije postavljen, email_from može sadržavati adresu e-pošte"
" koja nije odgovarala niti jednom partneru."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Avatar autora"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_public_id
msgid "Authorized Group"
msgstr "Ovlaštena grupa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Automatsko brisanje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__force_next
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__force_next
msgid "Auto Schedule Next Activity"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Auto Subscribe Groups"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_ids
msgid "Auto Subscription"
msgstr "Auto pretplata"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Auto pretplata"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_autovacuum
msgid "Automatic Vacuum"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_notify
msgid "Automatic notification"
msgstr "Automatsko obavještenje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:6
#: code:addons/mail/static/src/xml/followers.xml:44
#, python-format
msgid "Avatar"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:420
#, python-format
msgid "Ban"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Ban List"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:420
#, python-format
msgid "Ban this email address"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Banned Emails"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:90
#, python-format
msgid "Be careful with channels following internal notifications"
msgstr "Budite pažljivi sa kanalima koji prate interne obavjesti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Best regards,"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_mixin__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
msgid "Blacklist"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Body"
msgstr "Tijelo poruke"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
msgid "Bounce"
msgstr "Odskočen"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:398
#: selection:mail.notification,email_status:0
#, python-format
msgid "Bounced"
msgstr "Odskočeno"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Calendar"
msgstr "Kalendar"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Pozovi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:365
#: code:addons/mail/static/src/xml/activity.xml:86
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "Otkaži"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "Otkaži e-mail"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Cancel notification in failure"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:401
#: selection:mail.notification,email_status:0
#, python-format
msgid "Canceled"
msgstr "Otkazano"

#. module: mail
#: selection:mail.mail,state:0
msgid "Cancelled"
msgstr "Otkazan"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "Primatelji skrivene kopije"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_cc
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "Primaoci kopije(mogu se koristiti držači mjesta)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall
msgid "Catchall Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid "Categories may trigger specific behavior like opening calendar view"
msgstr ""
"Kategorije mogu okinuti specifična ponašanja kao što je otvaranje kalendar "
"pogleda"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Category"
msgstr "Kategorija"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
msgid "Cc"
msgstr "Cc"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Change"
msgstr "Promjeni"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:443
#, python-format
msgid "Changed"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field
msgid "Changed Field"
msgstr "Promjenjeno polje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:108
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__channel_id
#: model:ir.model.fields,field_description:mail.field_mail_moderation__channel_id
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#: selection:mail.channel,channel_type:0
#, python-format
msgid "Channel"
msgstr "Kanal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_message_ids
msgid "Channel Message"
msgstr "Poruka kanala"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_moderation_menu
msgid "Channel Moderation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Tip kanala"

#. module: mail
#: model:ir.model,name:mail.model_mail_moderation
msgid "Channel black/white list"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:144
#, python-format
msgid "Channel settings"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:47
#: code:addons/mail/static/src/xml/discuss.xml:224
#: code:addons/mail/static/src/xml/systray.xml:22
#: code:addons/mail/static/src/xml/systray.xml:39
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.ui.menu,name:mail.mail_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_tree
#, python-format
msgid "Channels"
msgstr "Kanali"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_partner_action
#: model:ir.ui.menu,name:mail.mail_channel_partner_menu
msgid "Channels/Partner"
msgstr "Kanali/Partner"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:220
#: code:addons/mail/static/src/xml/systray.xml:21
#: code:addons/mail/static/src/xml/systray.xml:35
#, python-format
msgid "Chat"
msgstr "Razgovor"

#. module: mail
#: selection:mail.channel,channel_type:0
msgid "Chat Discussion"
msgstr "Rasprava razgovora"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Chat Shortcode"
msgstr "Kratki kod razgovora"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Podređene poruke"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Choose an example"
msgstr "Odaberite neki primjer"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:50
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/abstract_thread_window.xml:67
#, python-format
msgid "Close chat window"
msgstr ""

#. module: mail
#: selection:mail.channel.partner,fold_state:0
msgid "Closed"
msgstr "Zatvoreno"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Zarezom odvojene kopije adresa primaoca"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "Zarezom odvojeni id-ovi partnera primaoca"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__partner_to
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"IDovi zarezom odvojeni partnera primatelja(mogu se koristiti držači mjesta)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Zarezom odvojenie adrese primaoca"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_to
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr "Adrese primatelja odvojene zarezom"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: selection:mail.compose.message,message_type:0
#: selection:mail.message,message_type:0
msgid "Comment"
msgstr "Komentar"

#. module: mail
#: model:ir.model,name:mail.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:378
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "Sastavi e-poštu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Mod sastavljanja"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "Konfigurišite vaše tipove aktivnosti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:130
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "Čestitamo, vaše poštansko sanduče je prazno."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:944
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "Čestitamo, vaše poštansko sanduče je prazno."

#. module: mail
#: selection:mail.notification,failure_type:0
msgid "Connection failed (outgoing mail server problem)"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "Sadržaj"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "Sadržaji"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fold_state
msgid "Conversation Fold State"
msgstr "Status skupljenosti razgovora"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_minimized
msgid "Conversation is minimized"
msgstr "Razgovor je minimiziran"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:9
#, python-format
msgid "Conversations"
msgstr "Razgovori"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Brojač odskočenih email-ova ovog kontakta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:565
#, python-format
msgid "Create %s"
msgstr "Kreiraj %s"

#. module: mail
#: selection:ir.actions.server,state:0
msgid "Create Next Activity"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:400
#, python-format
msgid "Create a new %(document)s"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:392
#, python-format
msgid "Create a new %(document)s by sending an email to %(email_link)s"
msgstr ""

#. module: mail
#: selection:ir.actions.server,state:0
msgid "Create a new Record"
msgstr "Kreiraj novi zapis"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Kreirao"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_moderation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:41
#: model:ir.model.fields,field_description:mail.field_email_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_moderation__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#, python-format
msgid "Created on"
msgstr "Kreirano"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Datum kreiranja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_user_id
msgid "Creator"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__starred
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr "Trenutni korisnik ima označenu obavijest povezanu sa ovom porukom."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__is_moderator
msgid "Current user is a moderator of the channel"
msgstr "Trenutni korisnik je moderator kanala"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__date
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Datum"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:93
#, python-format
msgid "Dates"
msgstr "Datumi"

#. module: mail
#: selection:ir.actions.server,activity_date_deadline_range_type:0
msgid "Days"
msgstr "Dani"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:46
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Deadline"
msgstr "Rok izvršenja"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Dragi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "Uobičajeno"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_next_type_id
msgid "Default Next Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__null_value
#: model:ir.model.fields,field_description:mail.field_mail_template__null_value
msgid "Default Value"
msgstr "Zadana vrijednost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_defaults
msgid "Default Values"
msgstr "Zadane vrijednosti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__use_default_to
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Zadani primaoci"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__use_default_to
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Zadani primaoci zapisa:\n"
"- partner (korisit id partnera ili partner_id polje) ili\n"
"- email (koristi email_from ili email polje)"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Define a new chat shortcode"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:87
#: code:addons/mail/static/src/xml/thread.xml:532
#, python-format
msgid "Delete"
msgstr "Obriši"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "Obriši email-ove"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_message
msgid "Delete Message Copy"
msgstr "Obriši kopiju poruke"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
msgid "Delete sent emails (mass mailing only)"
msgstr "Obriši poslate email-ove (samo masovna epošta)"

#. module: mail
#: selection:mail.mail,state:0
msgid "Delivery Failed"
msgstr "Isporuka nije uspjela"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Opis"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Opis koji će biti dodan u objavljenoj poruci za ovaj podtip. Ako nedostaje, "
"biti će dodan naziv."

#. module: mail
#: selection:ir.ui.view,type:0
msgid "Diagram"
msgstr "Dijagram"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:57
#, python-format
msgid "Direct Messages"
msgstr "Direktne poruke"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:348
#: code:addons/mail/static/src/xml/activity.xml:103
#: code:addons/mail/static/src/xml/discuss.xml:187
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Discard"
msgstr "Odbaci"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Discard delivery failures"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_cancel_action
msgid "Discard mail delivery failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:187
#, python-format
msgid "Discard selected messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:416
#, python-format
msgid "Discard |"
msgstr ""

#. module: mail
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Discuss"
msgstr "Rasprava"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel
msgid "Discussion channel"
msgstr "Kanal rasprave"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Rasprave"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_cancel
msgid "Dismiss notification for resend by model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_moderation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_thread__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Na povezanim dokumentima prikaži opciju  koja poziva čarobnjak sa ovim "
"predloškom"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__res_name
msgid "Display name of the related document."
msgstr "Prikaži naziv povezanog dokumenta."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""
"Ne čuvaj kopiju email poruke u istoriji komunikacije dokumenta (samo masovna"
" epošta)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Dokument"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Pratioci dokumenta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
msgid "Document Model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Naziv dokumenta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:101
#, python-format
msgid "Done"
msgstr "Gotovo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:107
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Launch Next"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:99
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "Završi i zakaži sljedeću"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:43
#: code:addons/mail/static/src/xml/thread.xml:77
#, python-format
msgid "Download"
msgstr "Preuzimanje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:11
#, python-format
msgid "Dropdown menu - Followers"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
msgid "Due Date"
msgstr "Datum dospijeća"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
msgid "Due Date In"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:95
#, python-format
msgid "Due in %d days"
msgstr "Zakazano za %d dana"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
msgid "Due type"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Dynamic Placeholder Generator"
msgstr "Dinamički generator placeholdera"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:83
#, python-format
msgid "Edit"
msgstr "Uredi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:354
#, python-format
msgid "Edit Subscription of "
msgstr "Uredi pretplatu"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:50
#, python-format
msgid "Edit subscription"
msgstr "Uredi pretplatu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_email
#: model:ir.model.fields,field_description:mail.field_mail_moderation__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: selection:mail.compose.message,message_type:0
#: selection:mail.message,message_type:0
msgid "Email"
msgstr "E-Mail"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
msgid "Email Address"
msgstr "E-mail adresa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Email Alias"
msgstr "Email nadimak"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "Alias e-maila"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "Postavke email-a"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Email Preview"
msgstr "Pregled e-maila"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "Pretraživanje e-mailova"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__email_status
msgid "Email Status"
msgstr "Status email-a"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
msgid "Email Template"
msgstr "Predložak email-a"

#. module: mail
#: model:ir.model,name:mail.model_email_template_preview
msgid "Email Template Preview"
msgstr "Pregled e-mail predloška"

#. module: mail
#: model:ir.model,name:mail.model_mail_template
msgid "Email Templates"
msgstr "Predlošci email poruka"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "Nit e-pošte"

#. module: mail
#: sql_constraint:mail.blacklist:0
msgid "Email address already exists!"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__alias_id
msgid ""
"Email address internally associated with this user. Incoming emails will "
"appear in the user's notifications."
msgstr ""
"E-mail adresa interno povezana s ovim korisnikom. Dolazni e-mail će se "
"pojaviti u korisnikovim obavijestima."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Email adresa pošaljioca. Ovo polje je postavljeno kada nema odgovarajućeg "
"partnera i zamjenjuje author_id polje i u chateru."

#. module: mail
#: selection:mail.notification,failure_type:0
msgid "Email address rejected by destination"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Email address to redirect replies..."
msgstr "Email adresa za preusmjerenje odgovora..."

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted means that the recipient won't receive "
"any mass mailing anymore."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Čarobnjak sastavljanja email-a"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "E-mail poruka"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "E-mail"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:14
#, python-format
msgid "Emojis"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Envelope Example"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:399
#: selection:mail.activity.type,decoration_type:0
#, python-format
msgid "Error"
msgstr "Greška"

#. module: mail
#: code:addons/mail/models/update.py:98
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr "Greška tijekom komunikacije sa serverom nosioca održavanja."

#. module: mail
#: code:addons/mail/models/mail_mail.py:317
#, python-format
msgid ""
"Error without exception. Probably due do sending an email without computed "
"recipients."
msgstr ""
"Greška bez izuzetka. Najvjerovatnije usljed slanja email-a bez izračunatih "
"primaoca."

#. module: mail
#: sql_constraint:mail.followers:0
msgid "Error, a channel cannot follow twice the same object."
msgstr "Greška, kanal nemože dva puta da prati isti objekat."

#. module: mail
#: sql_constraint:mail.followers:0
msgid "Error, a partner cannot follow twice the same object."
msgstr "Greška, partner ne može da prati dva puta isti objekat."

#. module: mail
#: sql_constraint:mail.followers:0
msgid ""
"Error: A follower must be either a partner or a channel (but not both)."
msgstr "Greška: pratioc mora biti ili partner ili kanal (ali ne oboje)"

#. module: mail
#: selection:mail.alias,alias_contact:0 selection:mail.channel,public:0
msgid "Everyone"
msgstr "Svi"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_warning
#: selection:mail.notification,email_status:0
msgid "Exception"
msgstr "Izuzetak"

#. module: mail
#: selection:ir.actions.server,state:0
msgid "Execute Python Code"
msgstr "Izvrši Python kod"

#. module: mail
#: selection:ir.actions.server,state:0
msgid "Execute several actions"
msgstr "Izvrši nekoliko akcija"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Napredni filteri..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:45
#, python-format
msgid "Extract pages:&nbsp;"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Neuspjeli email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Neuspješan"

#. module: mail
#: code:addons/mail/models/mail_template.py:338
#, python-format
msgid "Failed to render template %r using values %r"
msgstr "Neuspješno iscrtan predložak %r koristeći vrijednost %r"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Razlog neuspješnosti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Razlog neuspješnosti. Ovo je obično izuzetak bačen od strane email servera, "
"pohranjen radi lakšeg otklanjanja grešaka problema sa slanjem poruka."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Odabrao "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:316
#: model:ir.model.fields,field_description:mail.field_mail_activity__feedback
#, python-format
msgid "Feedback"
msgstr "Povratna informacija"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__model_object_field
msgid "Field"
msgstr "Polje"

#. module: mail
#: code:addons/mail/models/ir_model.py:30
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "Polje \"Email Nit\" ne može biti promjenjeno u \"Netačno\"."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_desc
msgid "Field Description"
msgstr "Opis polja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_type
msgid "Field Type"
msgstr "Tip polja"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Polja"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__copyvalue
#: model:ir.model.fields,help:mail.field_mail_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Krajnji izraz držača mjesta, koji će biti kopiran-zalijepljen na željenom "
"polju predloška."

#. module: mail
#: selection:mail.channel.partner,fold_state:0
msgid "Folded"
msgstr "Skupljeno"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:20
#, python-format
msgid "Follow"
msgstr "Prati"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr "Pratioci"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Obrazac pratitelja"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:40
#, python-format
msgid "Followers of"
msgstr "Sljedbenici od"

#. module: mail
#: selection:mail.alias,alias_contact:0
msgid "Followers only"
msgstr "Samo pratioci"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:24
#, python-format
msgid "Following"
msgstr "Pratim"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Form"
msgstr "Obrazac"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_from
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
msgid "From"
msgstr "Od"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:73
#, python-format
msgid "Full composer"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:98
#, python-format
msgid "Future"
msgstr "Buduće"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Buduće aktivnosti"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Gantt"
msgstr "Gantogram"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Gateway"
msgstr ""

#. module: mail
#: selection:ir.actions.server,activity_user_type:0
msgid "Generic User From Record"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:88
#, python-format
msgid "Go to the configuration panel"
msgstr "Idite na panel konfiguracije"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Graph"
msgstr "Dijagram"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Grupiši po"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Grupiši po..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_tree
msgid "Groups"
msgstr "Grupe"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_guidelines_msg
msgid "Guidelines"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:448
#, python-format
msgid "Guidelines of channel %s"
msgstr ""

#. module: mail
#: selection:res.users,notification_type:0
msgid "Handle by Emails"
msgstr "Upravljaj putem email-a"

#. module: mail
#: selection:res.users,notification_type:0
msgid "Handle in Odoo"
msgstr "Upravljaj u Odoo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__has_cancel
msgid "Has Cancel"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "Ima spominjanja"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has attachments"
msgstr "Ima prilog"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__has_error
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
#: model:ir.model.fields,help:mail.field_mail_compose_message__has_error
#: model:ir.model.fields,help:mail.field_mail_mail__has_error
#: model:ir.model.fields,help:mail.field_mail_message__has_error
msgid "Has error"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Zaglavlja"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "Hello"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__help_message
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Help message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Skriveno"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Sakrij podtipove u opcijama pratioca"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_channel__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_moderation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_thread__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
msgid "Icon"
msgstr "Znak"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Id resursa koji se prati"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:157
#: code:addons/mail/static/src/xml/discuss.xml:277
#, python-format
msgid "Idle"
msgstr "Na čekanju"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread
#: model:ir.model.fields,help:mail.field_res_partner__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__send_mail
msgid ""
"If checked, the partners will receive an email warning they have been added "
"in the document's followers."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__user_signature
#: model:ir.model.fields,help:mail.field_mail_template__user_signature
msgid ""
"If checked, the user's signature will be appended to the text version of the"
" message"
msgstr "Ako je odabrano, potpis korisnika biti će dodan tekst verziji poruke."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__scheduled_date
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Jinja2 placeholders may be used."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist_mixin__is_blacklisted
#: model:ir.model.fields,help:mail.field_mail_channel_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red envelope next"
" to each message."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:39
#, python-format
msgid "Image"
msgstr "Slika"

#. module: mail
#: code:addons/mail/models/mail_alias.py:130
#, python-format
msgid "Inactive Alias"
msgstr "Neaktivni nadimci"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:1232
#: code:addons/mail/static/src/xml/discuss.xml:27
#: code:addons/mail/static/src/xml/discuss.xml:205
#: code:addons/mail/static/src/xml/discuss.xml:216
#, python-format
msgid "Inbox"
msgstr "Prijemno sanduče"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:33
#, python-format
msgid "Info"
msgstr "Informacija"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model_id
msgid "Initial model"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,help:mail.field_mail_mail__parent_id
#: model:ir.model.fields,help:mail.field_mail_message__parent_id
msgid "Initial thread message."
msgstr "Inicijalna nit poruke"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Integrations"
msgstr "Integracije"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Samo interni"

#. module: mail
#: selection:mail.notification,failure_type:0
msgid "Invalid email address"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:35
#, python-format
msgid "Invalid email address %r"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:88
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Nevažeći izraz, to mora biti doslovna python definicija npr. \"{'field': "
"'value'}\""

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:126
#: code:addons/mail/models/mail_blacklist.py:129
#, python-format
msgid "Invalid primary email field on model %s"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:1968
#: code:addons/mail/models/mail_thread.py:2199
#, python-format
msgid ""
"Invalid record set: should be called as model (without records) or on "
"single-record recordset"
msgstr ""

#. module: mail
#: code:addons/mail/controllers/main.py:40
#, python-format
msgid "Invalid token in route %s"
msgstr "Nepravilan token u ruti %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:197
#, python-format
msgid "Invitation"
msgstr "Pozivnica"

#. module: mail
#: code:addons/mail/wizard/invite.py:53
#, python-format
msgid "Invitation to follow %s: %s"
msgstr "Pozivnica za praćenje %s: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:45
#: code:addons/mail/static/src/xml/discuss.xml:178
#, python-format
msgid "Invite"
msgstr "Pozovi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:171
#, python-format
msgid "Invite Follower"
msgstr "Pozovi pratioca"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:30
#: code:addons/mail/static/src/xml/discuss.xml:178
#, python-format
msgid "Invite people"
msgstr "Pozovi ljude"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:1188
#, python-format
msgid "Invite people to #%s"
msgstr "Pozovi ljude na #%s"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Čarobnjak za pozivanje"

#. module: mail
#: selection:mail.channel,public:0
msgid "Invited people only"
msgstr "Samo pozovi ljude"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Is Allowed"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Is Banned"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification
msgid "Is Notification"
msgstr "Je obavijest"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "Pročitano"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_subscribed
msgid "Is Subscribed"
msgstr "Pretplaćen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_member
msgid "Is a member"
msgstr "Član"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__is_moderator
msgid "Is moderator"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_pinned
msgid "Is pinned on the interface"
msgstr "Zakačen na interfejs"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Join"
msgstr "Pridruži se"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_action_view
msgid "Join a group"
msgstr "Priključi se grupi"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Kanban"
msgstr "Kanban"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Jezik"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_compose_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_followers____last_update
#: model:ir.model.fields,field_description:mail.field_mail_mail____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype____last_update
#: model:ir.model.fields,field_description:mail.field_mail_moderation____last_update
#: model:ir.model.fields,field_description:mail.field_mail_notification____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_shortcode____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template____last_update
#: model:ir.model.fields,field_description:mail.field_mail_thread____last_update
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value____last_update
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite____last_update
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_last_seen_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__seen_message_id
msgid "Last Seen"
msgstr "Zadnje viđeno"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_moderation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_moderation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:94
#, python-format
msgid "Late"
msgstr "Kasni"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Aktivnosti u kašnjenju"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__layout
#: model:ir.model.fields,field_description:mail.field_mail_mail__layout
#: model:ir.model.fields,field_description:mail.field_mail_message__layout
msgid "Layout"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Leave"
msgstr "Napusti"

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_channel.py:935
#: code:addons/mail/static/src/xml/discuss.xml:147
#, python-format
msgid "Leave this channel"
msgstr "Napusti kanal"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__channel_ids
msgid ""
"List of channels that will be added as listeners of the current document."
msgstr "Lista kanala koji će biti dodani kao slušaoci trenutnog dokumenta."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__partner_ids
msgid ""
"List of partners that will be added as follower of the current document."
msgstr "Lista partnera koji će biti dodani kao pratioci trenutnog dokumenta."

#. module: mail
#: code:addons/mail/models/mail_channel.py:946
#, python-format
msgid "List users in the current channel"
msgstr "Izlistaj korisnike u trenutnom kanalu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__channel_id
msgid "Listener"
msgstr "Slušalac"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_partner_ids
msgid "Listeners"
msgstr "Slušaoci"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "Pratioci kanala"

#. module: mail
#: selection:mail.channel,channel_type:0
msgid "Livechat Conversation"
msgstr "Razgovori u živo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:55
#, python-format
msgid "Loading"
msgstr "Učitavanje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:544
#, python-format
msgid "Loading older messages..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:15
#, python-format
msgid "Loading..."
msgstr "Učitavam..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/chatter_composer.js:34
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:41
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "Zabilješka"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:89
#, python-format
msgid "Log a note. Followers will not be notified."
msgstr "Zabilježi zabilješku. Pratioci neće biti obavješteni."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Log a note..."
msgstr "Zabilježi zabilješku"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Log an Activity"
msgstr "Zabilježi aktivnost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_log
msgid "Log an Internal Note"
msgstr "Zabilježi internu zabilješku"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:89
#, python-format
msgid "Log note"
msgstr "Zabilježi bilješku"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:92
#, python-format
msgid "Log or schedule an activity"
msgstr "Zabilježi ili zakaži aktivnost"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:68
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:51
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_id
#, python-format
msgid "Mail"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Tip email aktivnosti"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
msgid "Mail Blacklist"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_mixin
msgid "Mail Blacklist mixin"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:198
#, python-format
msgid "Mail Body"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Mail Channel Form"
msgstr "Forma mail kanala"

#. module: mail
#: code:addons/mail/models/mail_mail.py:385
#, python-format
msgid "Mail Delivery Failed"
msgstr "Isporuka mail-a neuspješna"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "Mail nit"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Vrijednost praćenja mail-a"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr "Mail je kreiran da obavjesti ljude o postojanju poruke"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_scheduler_action
#: model:ir.cron,name:mail.ir_cron_mail_scheduler_action
msgid "Mail: Email Queue Manager"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_notify_channel_moderators_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_notify_channel_moderators
#: model:ir.cron,name:mail.ir_cron_mail_notify_channel_moderators
msgid "Mail: Notify channel moderators"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:939
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "Poštansko sanduče nedostupno - %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Mails templates"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_partner__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:22
#: code:addons/mail/static/src/xml/thread.xml:9
#, python-format
msgid "Manage Messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:80
#, python-format
msgid "Mark Done"
msgstr "Označi kao završeno"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:179
#, python-format
msgid "Mark all as read"
msgstr "Označi sve kao pročitano"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:179
#, python-format
msgid "Mark all read"
msgstr "Označi pročitano"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Mark as Done"
msgstr "Označi kao završeno"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:298
#: code:addons/mail/static/src/xml/thread.xml:346
#, python-format
msgid "Mark as Read"
msgstr "Označi kao pročitano"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:299
#: code:addons/mail/static/src/xml/thread.xml:340
#, python-format
msgid "Mark as Todo"
msgstr "Označi sa 'Za uraditi'"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:71
#, python-format
msgid "Mark as done"
msgstr "Označi kao završeno"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
msgid "Mass Mail Blacklist"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__image_medium
msgid "Medium-sized photo"
msgstr "Slika srednje veličine"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__image_medium
msgid ""
"Medium-sized photo of the group. It is automatically resized as a 128x128px "
"image, with aspect ratio preserved. Use this field in form views or some "
"kanban views."
msgstr ""
"Slika od grupe srednje veličine. Automatski će se promijeniti veličina na "
"128x128 s očuvanim omjerom.  Koristiti ove polje u pregledima obrasca i "
"nekim kanban pogledima."

#. module: mail
#: selection:mail.activity.type,category:0
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Sastanak"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Members"
msgstr "Članovi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"Članovi tih grupa će biti automatski dodani kao pratioci. Imajte na umu da "
"će oni prema potrebi moći ručno upravljati svojom pretplatom."

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/base_partner_merge.py:13
#, python-format
msgid "Merged with the following partners:"
msgstr "Spojeno sa sljedećim partnerima:"

#. module: mail
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Message"
msgstr "Poruka"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "Poruka"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Obavještenja poruka"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Naziv zapisa poruke"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Tip poruke"

#. module: mail
#: code:addons/mail/models/mail_message.py:1277
#, python-format
msgid "Message are pending moderation"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Primaoci poruke (email-ovi)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "Reference poruke, poput identifikatora prethodnih poruka"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:165
#, python-format
msgid "Message sent in \""
msgstr "Poruka poslana u \""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"Podvrste poruka preciznije definiraju poruku, posebno za sistemske "
"obavijesti. Na primjer, to može biti obavijesti vezane uz novi zapis (nova),"
" ili na promjene faze u procesu (promjena faze). Podvrste poruka omogućuju "
"precizno podešavanje obavijesti koje korisnici žele primati na svoj zid."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Podtipovi poruka"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Podtipovi poduka koji se prate, znači podtipovi koji će biti objavljeni na "
"zidu korisnika."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Vrsta poruke: e-mail za e-mail poruke, obavjest za sistemske poruke, "
"komentari za druge poruke poput korisničkih odgovora"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_id
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "Jedinstveni identifikator poruke"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Id-poruke"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
msgid "Messages"
msgstr "Poruke"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Pretraga poruka"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:20
#, python-format
msgid "Messages can be <b>starred</b> to remind you to check back later."
msgstr ""
"Poruke mogu biti <b>označene sa zvjezdicom</b> da vas podsjete da ih "
"provjerite kasnije."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"Poruke sa internim podtipom će biti vidljive samo od strane zaposlenih, "
"odnosno pripadnika base_user grupe"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/mailbox.js:202
#, python-format
msgid "Missing domain for mailbox with ID '%s'"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Model"
msgstr "Model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "Model pratećeg resursa"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Model na koji se podtip odnosi. Ako nema, ovaj podtip odnosi se na sve "
"modele."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Modeli"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:1244
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
#, python-format
msgid "Moderate Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation
msgid "Moderate this channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__moderator_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__moderator_id
#: model:ir.model.fields,field_description:mail.field_mail_message__moderator_id
msgid "Moderated By"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_ids
msgid "Moderated Emails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__moderation_channel_ids
msgid "Moderated channels"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_count
msgid "Moderated emails count"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_moderation_action
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Moderation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_tree
msgid "Moderation Lists"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:39
#, python-format
msgid "Moderation Queue"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__moderation_status
#: model:ir.model.fields,field_description:mail.field_mail_mail__moderation_status
#: model:ir.model.fields,field_description:mail.field_mail_message__moderation_status
msgid "Moderation Status"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__moderation_counter
msgid "Moderation count"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_moderator
msgid "Moderator"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderator_ids
msgid "Moderators"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr ""

#. module: mail
#: selection:ir.actions.server,activity_date_deadline_range_type:0
msgid "Months"
msgstr "Mjeseci"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "My Activities"
msgstr "Moje aktivnosti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_channel__name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Name"
msgstr "Naziv:"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,help:mail.field_mail_mail__record_name
#: model:ir.model.fields,help:mail.field_mail_message__record_name
msgid "Name get of the related document."
msgstr "Naziv preuzet iz povezanog dokumenta"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__report_name
#: model:ir.model.fields,help:mail.field_mail_template__report_name
msgid ""
"Name to use for the generated report file (may contain placeholders)\n"
"The extension can be omitted and will then come from the report type."
msgstr ""
"Naziv za korišćenje generisanog fajla izvještaja(može da sadrži držače mjesta)\n"
"Ekstenzija može biti zanemarena i biće zamjenjena tipom izvještaja."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__needaction
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model:ir.model.fields,help:mail.field_mail_compose_message__needaction
#: model:ir.model.fields,help:mail.field_mail_mail__needaction
#: model:ir.model.fields,help:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Potrebna akcija"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__need_moderation
#: model:ir.model.fields,field_description:mail.field_mail_mail__need_moderation
#: model:ir.model.fields,field_description:mail.field_mail_message__need_moderation
msgid "Need moderation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
msgid "Needaction Recipient"
msgstr "Primaoc potrebne akcije"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:182
#, python-format
msgid "New Channel"
msgstr "Novi kanal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:181
#, python-format
msgid "New Message"
msgstr "Nova poruka"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Nova karakterna vrijednost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Nova datumsko vremenska vrijednost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Nova decimalna vrijednost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Nova integer vrijednost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_monetary
msgid "New Value Monetary"
msgstr "Nova vrijednost u novcu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Nova tekstualna vrijednost"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:935
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:112
#: code:addons/mail/static/src/xml/systray.xml:15
#: code:addons/mail/static/src/xml/systray.xml:24
#, python-format
msgid "New message"
msgstr "Nova poruka"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:429
#, python-format
msgid "New messages"
msgstr "Nove poruke"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:131
#, python-format
msgid "New messages appear here."
msgstr "Nove poruke će se pojaviti ovdje."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:104
#, python-format
msgid "New people"
msgstr "Novi ljudi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__moderation_guidelines
msgid ""
"Newcomers on this moderated channel will automatically receive the "
"guidelines."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:95
#, python-format
msgid "Next"
msgstr "Slijedeće"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Sljdedeće aktivnosti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Krajnji rok za sljedeću aktivnost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
msgid "Next Activity Summary"
msgstr "Pregled sljedeće aktivnosti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sljedeće aktivnosti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "Dostupne sljedeće aktivnosti"

#. module: mail
#: code:addons/mail/models/mail_notification.py:50
#, python-format
msgid "No Error"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:68
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:20
#, python-format
msgid "No activities planned."
msgstr "Nema planiranih aktivnosti."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:54
#, python-format
msgid "No conversation yet..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:21
#, python-format
msgid "No data to display"
msgstr "Nema podataka za prikaz"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:186
#, python-format
msgid "No follower"
msgstr "Nema pratioca"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:27
#, python-format
msgid "No matches found"
msgstr "Nije pronađeno odgovarajuće"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:17
#, python-format
msgid "No message available"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:28
#, python-format
msgid "No message matches your search. Try to change your search filters."
msgstr ""
"Nijedna poruka ne odgovara vašoj pretrazi. Pokušajte da promjenite filter "
"pretrage."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:134
#, python-format
msgid "No starred message"
msgstr "Nema zvjezdicom označenih poruka"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__no_auto_thread
#: model:ir.model.fields,field_description:mail.field_mail_mail__no_auto_thread
#: model:ir.model.fields,field_description:mail.field_mail_message__no_auto_thread
msgid "No threading for answers"
msgstr "Nema niti za odgovore"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:mail.message.subtype,name:mail.mt_note
msgid "Note"
msgstr "Zabilješka"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:304
#, python-format
msgid "Note by"
msgstr "Zabilježio"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Obavještenje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
msgid "Notification Management"
msgstr "Upravljanje notifikacijama"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_notify_msg
msgid "Notification message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
msgid "Notifications"
msgstr "Obavještenja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__notify
msgid "Notify followers"
msgstr "Obavjesti pratioce"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__notify
msgid "Notify followers of the document (mass post only)"
msgstr "Obavjesti pratioce dokumenta (samo masovni postovi)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:158
#, python-format
msgid "Offline"
msgstr "Van mreže"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Stara karakterna vrijednost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Stara datumsko vremenska vrijednost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Stara decimalna vrijednost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Stara integer vrijednost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_monetary
msgid "Old Value Monetary"
msgstr "Stara novčana vrijednost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Stara tekstualna vrijednost"

#. module: mail
#: selection:ir.model.fields,track_visibility:0
msgid "On Change"
msgstr "Pri Promjeni"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tour.js:24
#, python-format
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr ""
"Jednom kada je poruka označena sa zvjezdicom, uvijek se možete vratiti i "
"pregledati je u svako doba ovdje."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:188
#, python-format
msgid "One follower"
msgstr "Jedan pratitelj"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:156
#: code:addons/mail/static/src/xml/discuss.xml:276
#, python-format
msgid "Online"
msgstr "Na mreži"

#. module: mail
#: code:addons/mail/models/ir_model.py:28
#, python-format
msgid "Only custom models can be modified."
msgstr "Samo prilagođeni modeli mogu bit promjenjeni."

#. module: mail
#: selection:mail.channel.partner,fold_state:0
msgid "Open"
msgstr "Otvori"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Document"
msgstr "Otvori dokument"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Parent Document"
msgstr "Otvori nadređeni dokument"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:243
#, python-format
msgid "Open chat"
msgstr "Otvore razgovor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:190
#, python-format
msgid "Open document"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:192
#, python-format
msgid "Open in Discuss"
msgstr "Otvori u diskusijama"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Opcionalni ID zapisa na kojeg će biti povezane sve dolazne poruke, čak i ako"
" oni nisu odgovorili na njega. Ako je postavljeno, to će onemogućiti "
"stvaranje novih zapisa u potpunosti."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__mail_server_id
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Opcioni preferirani server za izlaznu poštu. Ako nije podešen koristiti će "
"se onaj sa najvišim prioritetom."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__report_template
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template
msgid "Optional report to print and attach"
msgstr "Opcioni izvještaj za ispis i zakačku"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. "
"${object.partner_id.lang}."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__null_value
#: model:ir.model.fields,help:mail.field_mail_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Opciona vrijednost koja će se koristiti ako je ciljano polje prazno"

#. module: mail
#: selection:mail.activity.type,category:0
msgid "Other"
msgstr "Drugo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: selection:mail.mail,state:0
msgid "Outgoing"
msgstr "Odlazeći"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Odlazni E-mail server"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Odlazni mailovi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Izlazni server e-pošte"

#. module: mail
#: selection:mail.activity,state:0
#: selection:mail.activity.mixin,activity_state:0
#: selection:res.partner,activity_state:0
msgid "Overdue"
msgstr "Dospjele"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "Zaobiđi autorov email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_user_id
msgid "Owner"
msgstr "Vlasnik"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:40
#, python-format
msgid "PDF file"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Nasljeđeni"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Nadređena poruka"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_model_id
msgid "Parent Model"
msgstr "Roditeljski model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Roditeljski zapis niti"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
msgid "Partner"
msgstr "Partner"

#. module: mail
#: code:addons/mail/models/res_partner.py:29
#, python-format
msgid "Partner Profile"
msgstr "Profil partnera"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
msgid "Partner Readonly"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additionnal information for mail resend"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__needaction_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction_partner_ids
msgid "Partners with Need Action"
msgstr "Partnere sa potrebnom akcijom"

#. module: mail
#: selection:mail.compose.message,moderation_status:0
#: selection:mail.message,moderation_status:0
msgid "Pending Moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:424
#, python-format
msgid "Pending moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:139
#, python-format
msgid "Pending moderation messages appear here."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__moderation_notify
msgid ""
"People receive an automatic notification about their message being waiting "
"for moderation."
msgstr ""
"Korisnici će primiti automatsko obavještenje o svojim porukama koje čekaju "
"na pregled."

#. module: mail
#: selection:mail.moderation,status:0
msgid "Permanent Ban"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid "Permanently delete this email after sending it, to save space"
msgstr "Trajno obriši ovaj e-mail nakon slanja, radi uštede prostora"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__image
msgid "Photo"
msgstr "Slika"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Pivot"
msgstr "Pivot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_template__copyvalue
msgid "Placeholder Expression"
msgstr "Izraz držača mjesta"

#. module: mail
#: selection:mail.activity,state:0
#: selection:mail.activity.mixin,activity_state:0
#: selection:res.partner,activity_state:0
msgid "Planned"
msgstr "Planiran"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:6
#, python-format
msgid "Planned activities"
msgstr "Planirane aktivnosti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/chatter_composer.js:123
#, python-format
msgid "Please complete customer's informations"
msgstr "Molimo završite informacije kupca"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "Please find below the guidelines of the"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:544
#, python-format
msgid "Please wait"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:242
#, python-format
msgid "Please, wait while the file is uploading."
msgstr "Molimo, pričekajte dok se datoteka ne učita"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_contact
#: model:ir.model.fields,help:mail.field_mail_channel__alias_contact
#: model:ir.model.fields,help:mail.field_res_users__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Powered by"
msgstr "Podržano od strane"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "Prošle aktivnosti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Preferred reply address"
msgstr "Preferirana adresa odgovora"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__reply_to
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid "Preferred response address (placeholders may be used here)"
msgstr "Preferirana adresa za odgovor (modu se koristiti držači mjesta)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:70
#: code:addons/mail/static/src/xml/discuss.xml:275
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:54
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "Pregled"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Preview of"
msgstr "Pregled"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:92
#, python-format
msgid "Previous"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "Tip prethodne aktivnosti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:76
#, python-format
msgid "Print"
msgstr "Ispis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__public
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Privacy"
msgstr "Privatnost"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:67
#: code:addons/mail/static/src/xml/discuss.xml:228
#, python-format
msgid "Private Channels"
msgstr "Privatni kanali"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:1348
#, python-format
msgid "Public Channels"
msgstr "Javni kanali"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_module_update_notification
#: model:ir.cron,name:mail.ir_cron_module_update_notification
msgid "Publisher: Update Notification"
msgstr ""

#. module: mail
#: selection:ir.ui.view,type:0
msgid "QWeb"
msgstr "QWeb"

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:179
#, python-format
msgid "Re:"
msgstr "Re:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:400
#, python-format
msgid "Ready"
msgstr "Spremno"

#. module: mail
#: selection:mail.notification,email_status:0
msgid "Ready to Send"
msgstr "Spremno za slanje"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: selection:mail.mail,state:0
msgid "Received"
msgstr "Primljeno"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Primalac"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Recipients"
msgstr "Primaoci"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "Predloženi tip aktivnosti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__next_type_ids
msgid "Recommended Next Activities"
msgstr "Preporučene sljedeće aktivnosti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID niti zapisa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Reference"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:186
#, python-format
msgid "Reject"
msgstr "Odbaci"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:186
#, python-format
msgid "Reject selected messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:414
#, python-format
msgid "Reject |"
msgstr ""

#. module: mail
#: selection:mail.compose.message,moderation_status:0
#: selection:mail.message,moderation_status:0
msgid "Rejected"
msgstr "Odbijeno"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "Povezani ID dokumenta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "Povezani model dokumenta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "Naziv modela povezanog modela"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "Povezana poruka"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "Povezani partner"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Relacijsko polje"

#. module: mail
#: selection:mail.activity.type,category:0
msgid "Reminder"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:414
#, python-format
msgid "Remove message with explanation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:416
#, python-format
msgid "Remove message without explanation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""
"Ukloni dodatnu akciju da se ovaj predložak koristi na povezanim dokumentima"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:53
#, python-format
msgid "Remove this follower"
msgstr "Ukloni ovog pratioca"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:343
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "Odgovori"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Email adresa odgovora. Postavljanjem reply_to zaobilazi automatsko kreiranje"
" niti."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply-To"
msgstr "Odgovori na"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__report_name
#: model:ir.model.fields,field_description:mail.field_mail_template__report_name
msgid "Report Filename"
msgstr "Naziv fajla izvještaja"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Resend mail"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Resend to selected"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:69
#, python-format
msgid "Reset Zoom"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
msgid "Responsible"
msgstr "Odgovoran"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Pokušaj ponovo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Rich-text Contents"
msgstr "Formatirani tekst"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Bogati text/HTML poruka"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:73
#, python-format
msgid "Rotate"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "SMTP server"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__res_id
msgid "Sample Document"
msgstr "Primjer dokumenta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "Spremi kao novi predložak"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as new template"
msgstr "Spremi kao novi predložak"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/abstract_thread_window.js:63
#, python-format
msgid "Say something"
msgstr "Kažite nešto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Zakaži"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:93
#, python-format
msgid "Schedule activity"
msgstr "Zakažite aktivnost"

#. module: mail
#: code:addons/mail/models/mail_activity.py:395
#, python-format
msgid "Schedule an Activity"
msgstr "Zakaži aktivnost"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:82
#, python-format
msgid "Schedule an activity"
msgstr "Zakažite aktivnost"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
msgid "Scheduled Date"
msgstr "Zakazani datum"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "Zakazani datum slanja"

#. module: mail
#: selection:ir.ui.view,type:0
msgid "Search"
msgstr "Pretraži"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Search Alias"
msgstr "Traži alias"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Search Groups"
msgstr "Traži grupe"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Search Moderation List"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:183
#, python-format
msgid "Select All"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:183
#, python-format
msgid "Select all messages to moderate"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Odaberite ciljno polje iz povezanog modela dokumenta.\n"
"Ako je ovo relaciono polje moći će te odabrati ciljano polje na odredištu relacije."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"Select the action to do on each mail and correct the email address if "
"needed. The modified address will be saved on the corresponding contact."
msgstr ""

#. module: mail
#: selection:mail.channel,public:0
msgid "Selected group of users"
msgstr "Odabrana grupa korisnika"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:49
#: code:addons/mail/static/src/js/discuss.js:148
#: code:addons/mail/static/src/xml/composer.xml:16
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "Pošalji"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Send Again"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
#: selection:ir.actions.server,state:0
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__send_mail
msgid "Send Email"
msgstr "Pošalji e-mail"

#. module: mail
#: code:addons/mail/models/mail_template.py:265
#, python-format
msgid "Send Mail (%s)"
msgstr "Pošalji e-mail (%s)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:72
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:56
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "Pošalji odmah"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:86
#, python-format
msgid "Send a message"
msgstr "Pošalji poruku"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:145
#, python-format
msgid "Send explanation to author"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Send guidelines"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_guidelines
msgid "Send guidelines to new subscribers"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:86
#, python-format
msgid "Send message"
msgstr "Pošalji poruku"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__email_send
msgid "Send messages by email"
msgstr "Pošalji poruke emailom"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_from
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Adresa pošaljioca. Ako nije podešena, koristiti će se e-mail alias autora "
"ukoliko je podešen, ili e-mail adresa."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_field.js:91
#, python-format
msgid "Sending Error"
msgstr "Greška slanja"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:141
#, python-format
msgid "Sends messages by email"
msgstr "Šalje poruke email-om"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:397
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: selection:mail.mail,state:0 selection:mail.notification,email_status:0
#, python-format
msgid "Sent"
msgstr "Poslano"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "Sent by"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_email
msgid "Sent by Email"
msgstr "Poslano email-om"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Serverska akcija"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "Kratke šifre"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "Prečica"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "Prikaži sve zapise koji imaju datum sljedeće akcije prije danas"

#. module: mail
#: code:addons/mail/models/mail_channel.py:914
#, python-format
msgid "Show an helper message"
msgstr "Prikaži pomoćnu poruku"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__ref_ir_act_window
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Akcija sidebar-a"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__ref_ir_act_window
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Akcija sidebar-a za postavljanje ovog prijedloga dostupnog na akcijama "
"povezanog modela dokumenta"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_avatar
#: model:ir.model.fields,help:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,help:mail.field_mail_message__author_avatar
msgid ""
"Small-sized image of this contact. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Mala slika ovog kontakta. Veličina je automatski promijenjena na 64x64 px "
"sliku. sa očuvanim proporcijama. Koristite ovo polje gdje god je potrebna "
"mala slika."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__image_small
msgid "Small-sized photo"
msgstr "Fotografija malog formata"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__image_small
msgid ""
"Small-sized photo of the group. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Slika grupe malog formata. Automatski će se promijeniti veličina na 64x64, s"
" očuvanim omjerom. Koristite ovo polje gdje je potrebna mala slika."

#. module: mail
#: selection:ir.actions.server,activity_user_type:0
msgid "Specific User"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_id
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:47
#, python-format
msgid "Split"
msgstr "Razdvoji"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:1237
#: code:addons/mail/static/src/xml/discuss.xml:33
#: code:addons/mail/static/src/xml/discuss.xml:208
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__starred
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "Sa zvjezdicom"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
msgid "State"
msgstr "Rep./Fed."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_moderation__status
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Status"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_model_object_field
msgid "Sub-field"
msgstr "Podpolje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_object
msgid "Sub-model"
msgstr "Podmodel"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:8
#: code:addons/mail/static/src/xml/discuss.xml:197
#: model:ir.model.fields,field_description:mail.field_email_template_preview__subject
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#, python-format
msgid "Subject"
msgstr "Tema"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__subject
#: model:ir.model.fields,help:mail.field_mail_template__subject
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Subject (placeholders may be used here)"
msgstr "Tema(mogu se koristiti držači mjesta)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Subject..."
msgstr "Naslov..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:365
#, python-format
msgid "Subject:"
msgstr "Tema:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "Zamjena"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Podtip"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Podtipovi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Summary"
msgstr "Sažetak"

#. module: mail
#: selection:mail.compose.message,message_type:0
#: selection:mail.message,message_type:0
msgid "System notification"
msgstr "Sistemska obavijest"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__has_recommended_activities
msgid "Technical field for UX purpose"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_change
msgid "Technical field for UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model_id
msgid ""
"Technical field to keep trace of the model at the beginning of the edition "
"for UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.wizard_email_template_preview
msgid "Template Preview"
msgstr "Pregled predloška"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Predlošci"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
msgid "Thank you!"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "!"

#. module: mail
#: code:addons/mail/models/ir_actions.py:51
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr ""

#. module: mail
#: sql_constraint:mail.moderation:0
msgid "The email address must be unique per channel !"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid "The escaped html code replacing the shortcut"
msgstr "Izbjegnuti html kod mjenjajući prečicu"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_channel__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Vlasnik zapisa kreiranih nakon primitka e-mailova na taj alias. Ako ovo "
"polje nije postavljeno sustav će pokušati pronaći pravog vlasnika na temelju"
" adrese pošiljatelja (od), ili će koristiti administratorski račun ako ne "
"pronađe sistemskog korisnika za tu adresu."

#. module: mail
#: code:addons/mail/models/mail_activity.py:243
#: code:addons/mail/models/mail_message.py:704
#: code:addons/mail/models/mail_message.py:874
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"Zahtjevana operacija nije mogla biti izvršena zbog sigurnosnih ograničenja. Molimo Vas da kontaktirate vašeg sistemskog administratora.\n"
"\n"
"(Tip dokumenta: %s, Operacija: %s)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid "The shortcut which must be replaced in the Chat Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__model_id
#: model:ir.model.fields,help:mail.field_mail_template__model_id
msgid "The type of document this template can be used with"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "Ovo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:10
#, python-format
msgid "This action will send an email."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__image
msgid ""
"This field holds the image used as photo for the group, limited to "
"1024x1024px."
msgstr ""
"Ovo polje sadrži sliku koja se koristi za grupu, ograničeno na 1014x1024 px."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__public
msgid ""
"This group is visible by non members. Invisible groups can add members "
"through the invite button."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Nit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
msgid "To"
msgstr "Za"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "Za (E-mailovi)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__partner_to
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "Za (Partneri)"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To Do"
msgstr "Za uraditi"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:39
#: code:addons/mail/static/src/xml/thread_window.xml:10
#, python-format
msgid "To:"
msgstr "Za:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:83
#: code:addons/mail/static/src/js/models/messages/abstract_message.js:107
#: code:addons/mail/static/src/xml/systray.xml:96
#: selection:mail.activity,state:0
#: selection:mail.activity.mixin,activity_state:0
#: selection:res.partner,activity_state:0
#, python-format
msgid "Today"
msgstr "Danas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:93
#, python-format
msgid "Tomorrow"
msgstr "Sutra"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Topics discussed in this group..."
msgstr "Teme raspravljane u grupi..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__track_visibility
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Tracking"
msgstr "Praćenje"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "Vrijednost praćenja"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "Vrijednosti praćenja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__track_sequence
msgid "Tracking field sequence"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "Vrijednosti praćenja"

#. module: mail
#: selection:ir.actions.act_window.view,view_mode:0
#: selection:ir.ui.view,type:0
msgid "Tree"
msgstr "Stablo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:23
#, python-format
msgid ""
"Try to add some activity on records, or make sure that\n"
"                there is no active filter in the search bar."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Tip"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create or Copy a new Record': create a new record with new values, or copy an existing record in your database\n"
"- 'Write on a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Add Followers': add followers to a record (available in Discuss)\n"
"- 'Send Email': automatically send an email (available in email_template)"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#: code:addons/mail/models/mail_mail.py:241
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "Nije se moguće spojiti na SMTP Server"

#. module: mail
#: code:addons/mail/models/mail_thread.py:2207
#, python-format
msgid "Unable to log message, please configure the sender's email address."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:2172
#, python-format
msgid "Unable to notify message, please configure the sender's email address."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:34
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/abstract_thread_window.js:163
#, python-format
msgid "Undefined"
msgstr "Nedefinisano"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:22
#, python-format
msgid "Unfollow"
msgstr "Ne slijedi više"

#. module: mail
#: sql_constraint:mail.alias:0
msgid ""
"Unfortunately this email alias is already used, please choose a unique one"
msgstr ""
"Nažalost ovaj e-mail alias se već koristi, molimo odaberite jedinstveni"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Unit"
msgstr "Jedinica"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_notification.py:52
#: selection:mail.notification,failure_type:0
#, python-format
msgid "Unknown error"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/abstract_thread_window.xml:35
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
#, python-format
msgid "Unread messages"
msgstr "Nepročitane poruke"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:184
#, python-format
msgid "Unselect All"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:184
#, python-format
msgid "Unselect all messages to moderate"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:180
#, python-format
msgid "Unstar all"
msgstr "Skini zvjezdice sa svih"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:180
#, python-format
msgid "Unstar all messages"
msgstr "Skini zvjezdice sa svih poruka"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:341
#, python-format
msgid "Unsubscribe"
msgstr "Odjavi pretplatu"

#. module: mail
#: code:addons/mail/models/mail_template.py:473
#, python-format
msgid "Unsupported report type %s found."
msgstr "Nepodržani tip izvještaja %s pronađen."

#. module: mail
#: code:addons/mail/models/mail_message.py:188
#, python-format
msgid "Unsupported search filter on moderation status"
msgstr ""

#. module: mail
#: selection:ir.actions.server,state:0
msgid "Update the Record"
msgstr "Ažuriraj zapis"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:531
#, python-format
msgid "Uploaded"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:523
#, python-format
msgid "Uploading"
msgstr "Učitavanje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:242
#, python-format
msgid "Uploading error"
msgstr "Greška pri slanju"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_active_domain
msgid "Use active domain"
msgstr "Koristi aktivni domen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Koristi predložak"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use your own email servers"
msgstr "Koristi sopstvene email servere"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Koristi se za redosljed podtipova"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:61
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "User"
msgstr "Korisnik"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
msgid "User field name"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:62
#: code:addons/mail/static/src/xml/thread_window.xml:11
#, python-format
msgid "User name"
msgstr "Korisničko ime"

#. module: mail
#: model:ir.model,name:mail.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: mail
#: code:addons/mail/models/mail_channel.py:959
#, python-format
msgid "Users in this channel: %s %s and you."
msgstr "Korisnici u ovom kanalu: %s %s i vi."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:41
#, python-format
msgid "Video"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:747
#: model:ir.model,name:mail.model_ir_ui_view
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
#, python-format
msgid "View"
msgstr "Pregled"

#. module: mail
#: code:addons/mail/models/mail_thread.py:745
#, python-format
msgid "View %s"
msgstr "Pogledaj %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Vrsta pregleda"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:99
#, python-format
msgid "View all the attachments of the current record"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:57
#, python-format
msgid "Viewer"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:85
#, python-format
msgid "Warning"
msgstr "Upozorenje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:243
#, python-format
msgid ""
"Warning! \n"
" If you remove a follower, he won't be notified of any email or discussion on this document.\n"
" Do you really want to remove this follower ?"
msgstr ""

#. module: mail
#: selection:ir.actions.server,activity_date_deadline_range_type:0
msgid "Weeks"
msgstr "Sedmice"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Kada je relaciono polje izabrano kao prvo polje, ovo polje Vam dozvoljava da"
" odaberete ciljno polje u sklopu odredišnok dokumentnog modela (podmodela)"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__sub_object
#: model:ir.model.fields,help:mail.field_mail_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Kada je relaciono polje odabrano kao prvo polje, ovo polje prikazuje model "
"dokumenta kojem relacija odlazi."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__track_visibility
msgid ""
"When set, every modification to this field will be tracked in the chatter."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr "Dali je poruka interna zabilješka (samo mod komentarisanja)"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_thread
msgid "Whether this model supports messages and notifications."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Who can follow the group's activities?"
msgstr "Ko može da prati aktivnosti grupe?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:96
#, python-format
msgid "Write Feedback"
msgstr "Napiši povratnu informaciju..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:12
#, python-format
msgid "Write something..."
msgstr "Napiši nešto..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:87
#: code:addons/mail/static/src/js/models/messages/abstract_message.js:109
#, python-format
msgid "Yesterday"
msgstr "Jučer"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:101
#, python-format
msgid "You added <b>%s</b> to the conversation."
msgstr "Dodali ste <b>%s</b> razgovoru."

#. module: mail
#: code:addons/mail/models/mail_channel.py:956
#, python-format
msgid "You are alone in this channel."
msgstr "Sami ste na ovom kanalu."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:367
#, python-format
msgid "You are going to ban: %s. Do you confirm the action?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:390
#, python-format
msgid "You are going to discard %s messages. Do you confirm the action?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:394
#, python-format
msgid "You are going to discard 1 message. Do you confirm the action?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid ""
"You are going to send the guidelines to all the subscribers. Do you confirm "
"the action?"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:925
#, python-format
msgid "You are in a private conversation with <b>@%s</b>."
msgstr "U privatnom ste razgovoru sa <b>@%s</b>."

#. module: mail
#: code:addons/mail/models/mail_channel.py:919
#, python-format
msgid "You are in channel <b>#%s</b>."
msgstr "Nalazite se u kanalu <b>#%s</b>."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:338
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to "
"unsubscribe?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:135
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:87
#, python-format
msgid ""
"You cannot create a new user from here.\n"
" To create new user please go to configuration panel."
msgstr ""
"Ne možete odavde da kreirate novog korisnika.\n"
"Ako želite da kreirate novog korisnika, molimo Vas da odete u konfiguracijski panel."

#. module: mail
#: code:addons/mail/models/mail_channel.py:241
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"Ne možete da obrišete ove grupe, jer je grupa Cijela Kompanija potrebna "
"drugim modulima."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "Bili ste dodjeljeni"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:198
#, python-format
msgid "You have been invited to: "
msgstr "Bili ste pozvani na:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
msgid "You have messages to moderate, please go for the proceedings."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:138
#, python-format
msgid "You have no message to moderate"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Možete priložiti datoteke ovom predlošku, one će biti dodate svim "
"e-mailovima kreiranim iz ovog predloška"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:444
#, python-format
msgid "You unpinned your conversation with <b>%s</b>."
msgstr "Otkačili ste razgovor sa <b>%s</b>."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:439
#, python-format
msgid "You unsubscribed from <b>%s</b>."
msgstr "Odjavili ste se sa <b>%s</b>."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:291
#, python-format
msgid "You:"
msgstr "Ti:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Your"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_field.js:91
#, python-format
msgid "Your message has not been sent."
msgstr "Vaša poruka nije poslana"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:424
#, python-format
msgid "Your message is pending moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:198
#, python-format
msgid "Your message was rejected by moderator."
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_actions.py:57
#, python-format
msgid "Your template should define email_from"
msgstr "Vaš predložak bi trebao da definiše email_from"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:68
#, python-format
msgid "Zoom In"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:70
#, python-format
msgid "Zoom Out"
msgstr ""

#. module: mail
#: selection:mail.activity.type,delay_from:0
msgid "after previous activity deadline"
msgstr ""

#. module: mail
#: selection:mail.activity.type,delay_from:0
msgid "after validation date"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:1074
#, python-format
msgid "alias %s: %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "assigned you an activity"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_2
msgid "board-meetings"
msgstr "sastanci rukovodstva"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:43
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "by"
msgstr "od"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "channel."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "created"
msgstr "kreiran"

#. module: mail
#: selection:mail.activity.type,delay_unit:0
msgid "days"
msgstr "Dani"

#. module: mail
#: code:addons/mail/models/mail_thread.py:357
#, python-format
msgid "document"
msgstr "dokument"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid ""
"done\n"
"        by"
msgstr ""
"uradio\n"
"        "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:45
#, python-format
msgid "e.g. 1-5, 7, 8-9"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "npr.: Raspravi prijedlog"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:190
#, python-format
msgid "followers"
msgstr "pratioci"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:30
#, python-format
msgid "for"
msgstr "za"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "from:"
msgstr "od:"

#. module: mail
#: model:mail.channel,name:mail.channel_all_employees
msgid "general"
msgstr "opšte"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been"
msgstr "je bio"

#. module: mail
#: code:addons/mail/models/mail_alias.py:272
#, python-format
msgid "incorrectly configured alias"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:268
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:1051
#, python-format
msgid "model %s does not accept document creation"
msgstr "model %s ne prihvata kreiranje dokumenata"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1046
#, python-format
msgid "model %s does not accept document update"
msgstr "model %s ne prihvata ažuriranje dokumenata"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1039
#, python-format
msgid ""
"model %s does not accept document update, fall back on document creation"
msgstr ""
"model %s ne prihvata ažuriranja dokumenata, vraćamo se na kreiranje "
"dokumenta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "modified"
msgstr "promjenjeno"

#. module: mail
#: selection:mail.activity.type,delay_unit:0
msgid "months"
msgstr "mjeseci"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "mycompany.odoo.com"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/utils.js:108
#, python-format
msgid "now"
msgstr "sada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:323
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "na"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:71
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:55
#, python-format
msgid "or"
msgstr "ili"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1021
#, python-format
msgid ""
"posting a message without model should be with a null res_id (private "
"message), received %s"
msgstr ""
"objavljivanje poruka bez modela bi trebao biti sa null res_id (privatna "
"poruka), primljena %s"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1025
#, python-format
msgid ""
"posting a message without model should be with a parent_id (private message)"
msgstr ""
"objavljivanje poruka bez modela bi trebao biti sa parent_id (privatna "
"poruka)"

#. module: mail
#: model:mail.channel,name:mail.channel_3
msgid "rd"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_widget.js:20
#, python-format
msgid "read less"
msgstr "čitaj manje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_widget.js:19
#, python-format
msgid "read more"
msgstr "pročitaj više"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "record:"
msgstr "zapis:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1043
#, python-format
msgid "reply to missing document (%s,%s)"
msgstr "odgovor za nedostajući dokument (%s,%s)"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1036
#, python-format
msgid "reply to missing document (%s,%s), fall back on new document creation"
msgstr ""
"odgovor na nedostajući dokument (%s,%s), vraćanje na kreiranje novog "
"dokumenta"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1021
#, python-format
msgid "resetting thread_id"
msgstr "ponovno postavljanje thread_id"

#. module: mail
#: code:addons/mail/models/mail_channel.py:407
#, python-format
msgid "restricted to channel members"
msgstr "ograničeno na članove kanala"

#. module: mail
#: code:addons/mail/models/mail_alias.py:277
#, python-format
msgid "restricted to followers"
msgstr "ograničeno na pratioce"

#. module: mail
#: code:addons/mail/models/mail_alias.py:281
#, python-format
msgid "restricted to known authors"
msgstr "ograničeno na poznate autore"

#. module: mail
#: model:mail.channel,name:mail.channel_1
msgid "sales"
msgstr "prodaje"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1025
#: code:addons/mail/models/mail_thread.py:1043
#: code:addons/mail/models/mail_thread.py:1046
#: code:addons/mail/models/mail_thread.py:1051
#: code:addons/mail/models/mail_thread.py:1074
#, python-format
msgid "skipping"
msgstr "preskakanje"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:45
#, python-format
msgid "this document"
msgstr "ovaj dokument"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "to close for"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:1074
#, python-format
msgid "unknown error"
msgstr "nepoznata greška"

#. module: mail
#: code:addons/mail/models/mail_thread.py:1014
#, python-format
msgid "unknown target model %s"
msgstr "nepoznat ciljni model %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "using"
msgstr ""

#. module: mail
#: selection:mail.activity.type,delay_unit:0
msgid "weeks"
msgstr ""
