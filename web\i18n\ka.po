# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
# 
# Translators:
# <PERSON><PERSON> <skhmal<PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# Temur, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2022\n"
"Language-Team: Georgian (https://app.transifex.com/odoo/teams/41243/ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid " records"
msgstr " ჩანაწერები"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "# Code editor"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d days ago"
msgstr "%d დღის წინ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d hours ago"
msgstr "%d საათის წინ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d minutes ago"
msgstr "%d წუთის წინ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d months ago"
msgstr "%d თვის წინ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d years ago"
msgstr "%d წლის წინ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_mixin.js:0
#, python-format
msgid "%s Files"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "%s days ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' არ არის კორექტული თარიღი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/dates.js:0
#, python-format
msgid "'%s' is not a correct date or datetime"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' არ არის კორექტული თარიღი/საათი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' არ არის კორექტული მცოცავწერტილიანი რიცხვი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' არ არის კორექტული მთელი რიცხვი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr "'%s' არ არის გარდაქმნადი თარიღში, თარიღი/დრო-ში ან დროში"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid "'%s' is unsynchronized with '%s'."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#, python-format
msgid "(%s/%sMb)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "(change)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(count)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "(no result)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(no string)"
msgstr "(მწკრივი არ არის)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(nolabel)"
msgstr "(იარლიყის გარეშე)"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "07/08/2020"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "08/07/2020"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "1 record"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">$ 2,887.50</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">11,750.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$<span "
"class=\"oe_currency_value\">11,750.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">1,500.00</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">2,350.00</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">Tax 15%</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"                                                   Rd<br/>Pleasant Hill CA 94523<br/>United States</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span id=\"line_tax_ids\">15.00%</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span itemprop=\"name\">Deco Addict</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>5.000</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Amount</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Description</span>"
msgstr "<span>აღწერა</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>Invoice</span>\n"
"                           <span>INV/2020/07/0003</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Payment terms: 30 Days</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Quantity</span>"
msgstr "<span>რაოდენობა</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Taxes</span>"
msgstr "<span>გადასახადები</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Unit Price</span>"
msgstr "<span>ერთეულის ღირებულება</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8220] Four Person Desk<br/>\n"
"                                       Four person modern office workstation</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8999] Three-Seat Sofa<br/>\n"
"                                       Three Seater Sofa with Lounger in Steel Grey Colour</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Due Date:</strong>"
msgstr "დაფარვის თარიღი :"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Invoice Date:</strong>"
msgstr "ინვოისის თარიღი:"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Subtotal</strong>"
msgstr "ჯამი"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Total</strong>"
msgstr "<strong>თანხა სულ</strong>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A filter with same name already exists."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A name for your favorite filter is required."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ALL"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ANY"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Denied"
msgstr "წვდომა აკრძალულია"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Access to all Enterprise Apps"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Action"
msgstr "მოქმედება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Action ID:"
msgstr "მოქმედების ID:"

#. module: web
#: model:ir.model,name:web.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Assets Debugging"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Tests Assets Debugging"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Activate debug mode"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add"
msgstr "დამატება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Add %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add Custom Filter"
msgstr "ფილტრის დამატება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#, python-format
msgid "Add Custom Group"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add a Column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add a condition"
msgstr "პირობის დამატება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Add a line"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add branch"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add filter"
msgstr "ფილტრის დამატება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add new value"
msgstr "ახალი მნიშვნელობის დამატება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Add qweb directive context"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add tag"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Add to Favorites"
msgstr "რჩეულებში დამატება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Additional actions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Add: "
msgstr "დამატება: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt Your Signature"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt and Sign"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "Alert"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/search_panel_model_extension.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_arch_parser.js:0
#, python-format
msgid "All"
msgstr "ყველა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "All day"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "All users"
msgstr "ყველა მომხმარებელი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Alt"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Among the"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "An error occurred"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "And more"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Any"
msgstr ""

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_header
msgid ""
"Appears by default on the top right corner of your printed documents (report"
" header)."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#, python-format
msgid "Apply"
msgstr "გააქტიურება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Archive"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Archive All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#, python-format
msgid "Are you sure that you want to archive this record?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "ნამდვილად გსურთ აღნიშნული ჩანაწერის წაშლა?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Are you sure you want to perform the following update on those"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Ascending"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Attach"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Attachment"
msgstr "დანართი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Auto"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Available fields"
msgstr "ხელმისაწვდომი ველები"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Avatar"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background_image
msgid "Background Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#, python-format
msgid "Badge"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Badges"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Bar Chart"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "ძირითადი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Become Superuser"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Binary file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Bugfixes guarantee"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Button"
msgstr "ღილაკი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Button Type:"
msgstr "ღილაკის სახეობა:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid ""
"By clicking Adopt and Sign, I agree that the chosen signature/initials will "
"be a valid electronic representation of my hand-written signature/initials "
"for all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar"
msgstr "კალენდარი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Calendar toolbar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar view has not defined 'date_start' attribute."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Cancel"
msgstr "გაუქმება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/file_upload_progress_bar.xml:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_bar.xml:0
#, python-format
msgid "Cancel Upload"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Card color: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/change_password.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Change Password"
msgstr "პაროლის შეცვლა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Change default:"
msgstr "შეცვალე ნაგულისხმები:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Change graph"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/_deprecated/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#, python-format
msgid "Checkbox"
msgstr "მოსანიშნი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Checkboxes"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Choose"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/file_input/file_input.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Choose File"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Choose a debug command..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Clear"
msgstr "გასუფთავება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Clear Signature"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/notifications/notification.xml:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/dialog.xml:0
#, python-format
msgid "Close"
msgstr "დახურვა"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Colors"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Column %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Column title"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Command"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_res_company
msgid "Companies"
msgstr "კომპანიები"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_id
msgid "Company"
msgstr "კომპანია"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_details
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Company Details"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_base_document_layout
msgid "Company Document Layout"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo
msgid "Company Logo"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__name
msgid "Company Name"
msgstr "კომპანიის სახელი"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_header
msgid "Company Tagline"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/comparison_menu/comparison_menu.xml:0
#, python-format
msgid "Comparison"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Condition:"
msgstr "პირობა:"

#. module: web
#: model:ir.actions.act_window,name:web.action_base_document_layout_configurator
msgid "Configure your document layout"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "დადასტურება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection lost. Trying to reconnect..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection restored. You are back online."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Context:"
msgstr "კონტექსტი:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Control"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Control panel buttons"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Copied !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Copy"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "Copy the full error to clipboard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Copy to Clipboard"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Copyright &amp;copy;"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid "Could not connect to the server"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "Could not display the selected image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Could not display the specified image url."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Could not serialize XML"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid ""
"Could not set the cover image: incorrect field (\"%s\") is provided in the "
"view."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/helpers/utils.js:0
#, python-format
msgid "Count"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__country_id
msgid "Country"
msgstr "ქვეყანა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Create"
msgstr "შექმნა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Create "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Create a new record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create and Edit..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Create record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create: %s"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_uid
msgid "Created by"
msgstr "შემქმნელი"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_date
msgid "Created on"
msgstr "შექმნის თარიღი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation Date:"
msgstr "შექმნის თარიღი:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation User:"
msgstr "შემქმნელი:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Current state"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__custom_colors
msgid "Custom Colors"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark purple"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "მონაცემთა ბაზა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Date"
msgstr "თარიღ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Date & Time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Day"
msgstr "დღე"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Deactivate debug mode"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.debug_icon
msgid ""
"Debug mode is activated#{debug_mode_help}. Click here to exit debug mode."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Debug tools..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Decimal"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Default"
msgstr "ნაგულისხმევი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Default:"
msgstr "ნაგულისხმევი:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Delete"
msgstr "წაშლა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Delete item"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Delete node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Delete row "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Descending"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Description"
msgstr "აღწერა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Discard"
msgstr "გაუქმება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Discard a record modification"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Discard record"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__display_name
msgid "Display Name"
msgstr "სახელი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_bar.js:0
#, python-format
msgid "Do you really want to cancel the upload of %s?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Do you really want to delete this export template?"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__external_report_layout_id
msgid "Document Template"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Documentation"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain"
msgstr "დომენი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not properly formed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not supported"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain:"
msgstr "დომენი:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Don't leave yet,"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "ნუ წახვალთ ჯერ,<br />ეს ისევ იტვირთება..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Download"
msgstr "გადმოწერა"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Download PDF Preview"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Download xlsx"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Draw"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dropdown menu"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#, python-format
msgid "Duplicate"
msgstr "გააკეთე დუბლირება."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Edit"
msgstr "შეცვლა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Edit Action"
msgstr "მოქმედების რედაქტირება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Edit Column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit ControlPanelView"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Edit Domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Edit Stage"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit View: "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Edit a record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Edit record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__email
#: model_terms:ir.ui.view,arch_db:web.login
#, python-format
msgid "Email"
msgstr "ელ.ფოსტა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Enable profiling"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Error, password not changed !"
msgstr "შეცდომა, პაროლი არ არის შეცვლილი !"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Esc to discard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everybody's calendars"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everything"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Expand all"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Export"
msgstr "ექსპორტი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Export All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Export Data"
msgstr "მონაცემების ექსპორტი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Export Format:"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Exporting grouped data to csv is not supported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "External ID"
msgstr "გარე იდენტიფიკატორი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "External link"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/control_panel_model_extension.js:0
#, python-format
msgid "Failed to evaluate search context"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "False"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Favorites"
msgstr "ფავორიტები"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Field:"
msgstr "ველი:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Fields View Get"
msgstr "ველების ხედის მიღება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Fields to export"
msgstr "დასაექსპორტებელი ველები"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "File"
msgstr "ფაილი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "File upload"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#, python-format
msgid "Filter with same name already exists."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/filter_menu.xml:0
#, python-format
msgid "Filters"
msgstr "ფილტრები"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Flip axis"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Fold"
msgstr "ჩაკეცვა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Followed by"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__font
msgid "Font"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Footer"
msgstr ""

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_controller.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 16384 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_view.js:0
#, python-format
msgid "Form"
msgstr "ფორმა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/qweb/qweb_view.js:0
#, python-format
msgid "Freedom View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Full Name"
msgstr "სრული სახელი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Fushia"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/graph/graph_view.js:0
#, python-format
msgid "Graph"
msgstr "გრაფიკი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Green"
msgstr "მწვანე"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_controller.js:0
#: code:addons/web/static/src/search/group_by_menu/group_by_menu.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Group By"
msgstr "დაჯგუფება"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Handle"
msgstr ""

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__company_details
msgid "Header text displayed at the top of all reports."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hide in Kanban"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to CREATE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to SAVE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "I am sure about this."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "I want to update data (import-compatible export)"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__id
msgid "ID"
msgstr "იდენტიფიკატორი/ID"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "ID:"
msgstr "ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid ""
"If you change %s or %s, the synchronization will be reapplied and the data "
"will be modified."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Image"
msgstr "გამოსახულება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "In %s days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Integer"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Interval"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Invalid data"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Invalid domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Invalid field chain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid ""
"Invalid field chain. You may have used a non-existing field name or followed"
" a non-relational field."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Invalid fields:"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Invalid inherit mode. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid ""
"It is possible that the \"t-call\" time does not correspond to the overall time of the\n"
"            template. Because the global time (in the drop down) does not take into account the\n"
"            duration which is not in the rendering (look for the template, read, inheritance,\n"
"            compilation...). During rendering, the global time also takes part of the time to make\n"
"            the profile as well as some part not logged in the function generated by the qweb."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_view.js:0
#, python-format
msgid "Kanban"
msgstr "კანბანი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Kanban Examples"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Kanban: no action for type: "
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Languages"
msgstr "ენები"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout____last_update
msgid "Last Modified on"
msgstr "ბოლოს განახლებულია"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_uid
msgid "Last Updated by"
msgstr "ბოლოს განაახლა"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_date
msgid "Last Updated on"
msgstr "ბოლოს განახლდა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification Date:"
msgstr "განახლების თარიღი:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification by:"
msgstr "ბოლოს განახლდა:"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Layout"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background
msgid "Layout Background"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Leave the Developer Tools"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Light blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Line Chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_view.js:0
#, python-format
msgid "List"
msgstr "სია"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Load"
msgstr "ჩატვირთვა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Load more... ("
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/webclient/loading_indicator/loading_indicator.xml:0
#, python-format
msgid "Loading"
msgstr "იტვირთება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Loading, please wait..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Loading..."
msgstr "იტვირთება...."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "შესვლა"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Log out"
msgstr "გასვლა"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "ლოგო"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_primary_color
msgid "Logo Primary Color"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_secondary_color
msgid "Logo Secondary Color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Mac"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "MailDeliveryException"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Main actions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Manage Attachments"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "მონაცემთა ბაზების მართვა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Manage Filters"
msgstr "მართე ფილტრები"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Many2many"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Many2one"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with the following rule:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr "იქნებ გეცადათ აპლიკაციის გადატვირთვა F5 ღილაკზე დაჭერის გზით..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view.xml:0
#, python-format
msgid "Measures"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Medium blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "Meeting Subject"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Meeting Subject:"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_ui_menu
msgid "Menu"
msgstr "მენიუ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Method:"
msgstr "მეთოდი:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Missing Record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Mobile support"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Model Record Rules"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Modifiers:"
msgstr "მოდიფიკატორი:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Module %s not loaded or inexistent, or templates of addon being loaded (%s) "
"are misordered"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Monetary"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Month"
msgstr "თვე"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "More"
msgstr "მეტი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Multiline Text"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "My Odoo.com account"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "NONE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid "New"
msgstr "ახალი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "New %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "New Event"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New Password"
msgstr "ახალი პაროლი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New Password (Confirmation)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New design"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New template"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Next"
msgstr "შემდეგი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Next page"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "No"
msgstr "არა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "No Update:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/client_actions.js:0
#, python-format
msgid "No action with id '%s' could be found"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "No color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/default_providers.js:0
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "No commands found"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/views/graph/graph_renderer.js:0
#, python-format
msgid "No data"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/helpers/no_content_helpers.xml:0
#, python-format
msgid "No data to display"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "No match found."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "No menu found"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "No records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "No records found!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_palette.js:0
#, python-format
msgid "No results found"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "No template found to inherit from. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "No valid record to save"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "No view of type '%s' could be found in the current action."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "None"
msgstr "არცერთი"

#. module: web
#: code:addons/web/models/models.py:0 code:addons/web/models/models.py:0
#: code:addons/web/models/models.py:0
#, python-format
msgid "Not Set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state, click to change it"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Object:"
msgstr "ობიექტი:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Odoo Apps will be available soon"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Client Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#, python-format
msgid "Odoo Enterprise"
msgstr "Odoo Enterprise"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Network Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Server Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Odoo Session Expired"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Warning"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid ""
"Of the %d records selected, only the first %d have been archived/unarchived."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "ოკ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Old Password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "On change:"
msgstr "ცვლილების დროს:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "One2many"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Only employees can access this database. Please contact the administrator."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Only the first %d records have been deleted (out of %d selected)"
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for category (found type "
"%(field_type)s)"
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for filter (found type "
"%(field_type)s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Only you"
msgstr "მხოლოდ შენ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open Command Palette"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Open View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open the next record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open the previous record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open to kanban view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open to list view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Open: "
msgstr "ღია: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Open: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#, python-format
msgid "Optional columns"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Orange"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_progressbar.js:0
#, python-format
msgid "Other"
msgstr "სხვა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "PDF Viewer"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "PDF controls"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Pager"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__paperformat_id
msgid "Paper format"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__partner_id
msgid "Partner"
msgstr "პარტნიორი"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "პაროლი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage Pie"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__phone
#, python-format
msgid "Phone"
msgstr "ტელეფონი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Pick a color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Pie Chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Pie chart cannot mix positive and negative numbers. "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Pivot"
msgstr ""

#. module: web
#: code:addons/web/controllers/pivot.py:0
#, python-format
msgid "Pivot %(title)s (%(model_name)s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Pivot settings"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Please be patient."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#, python-format
msgid "Please click on the \"save\" button first"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Please enter a numerical value"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please enter save field list name"
msgstr "გთხოვთ განსაზღვროთ შესანახი ველის სიის სახელი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Please save before attaching a file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to export..."
msgstr "გთხოვთ აირჩიოთ ველები ექსპორტისთვის..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to save export list..."
msgstr "გთხოვთ აირჩიოთ ველები შესანახი სიის ექსპორტისთვის"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Please update translations of :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"Please use the following communication for your payment : <b><span>\n"
"                           INV/2020/07/0003</span></b>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Preferences"
msgstr "პარამეტრები"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview
msgid "Preview"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview_logo
msgid "Preview logo"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Previous"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Period"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Year"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous menu"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous page"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__primary_color
msgid "Primary Color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/report.xml:0
#: code:addons/web/static/src/legacy/xml/report.xml:0
#, python-format
msgid "Print"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/report.xml:0
#, python-format
msgid "Printing options"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Priority"
msgstr "პრიორიტეტი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_card.xml:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_card.xml:0
#, python-format
msgid "Processing..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Progress Bar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Purple"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q1"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q2"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q3"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q4"
msgstr ""

#. module: web
#: model:ir.model.fields.selection,name:web.selection__ir_actions_act_window_view__view_mode__qweb
msgid "QWeb"
msgstr "QWeb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Quarter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Quick add"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Quick search: %s"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
#: model:ir.model,name:web.model_ir_qweb_field_image_url
msgid "Qweb Field Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGB"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGBA"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Radio"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record qweb"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record sql"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record traces"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Red"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Refresh"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Regenerate Assets Bundles"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid "Relation not allowed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Relation to follow"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Relation:"
msgstr "რელაცია:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Remaining Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Remove"
msgstr "მოცილება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Remove Cover Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Remove field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Remove from Favorites"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Remove tag"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Remove this favorite from the list"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "Report"
msgstr "რეპორტი"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_footer
msgid "Report Footer"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_layout_id
msgid "Report Layout"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_layout_preview
msgid "Report Layout Preview"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Request timeout"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Reset to logo colors"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/clickbot/clickbot_loader.js:0
#, python-format
msgid "Run Click Everywhere Test"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Mobile Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "SIGNATURE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Salmon pink"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Save"
msgstr "შენახვა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Save & Close"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Save & New"
msgstr "შეინახე და ახალი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Save a record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save as :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Save current search"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Save default"
msgstr "ნაგულისხმებად შენახვა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Search"
msgstr "ძიება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Search More..."
msgstr "მეტის ძებნა..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_service.js:0
#, python-format
msgid "Search for a command..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Search for records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_palette.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Search..."
msgstr "ძებნა..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Search: %s"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__secondary_color
msgid "Secondary Color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "See details"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "See examples"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select"
msgstr "არჩევა"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select Signature Style"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select a model to add a filter."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Select a view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select all"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Selected records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Selection"
msgstr "მონიშვნა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Selection:"
msgstr "არჩეული:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Set Default"
msgstr "ნაგულისხმები"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Set Defaults"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Set a Cover Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a kanban state..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a priority..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid "Set a timezone on your user"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set kanban state..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set priority..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Settings"
msgstr "პარამეტრები"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Share with all users"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Shortcuts"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Show sub-fields"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Showing locally available modules"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "Signature"
msgstr "ხელმოწერა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Size:"
msgstr "ზომა:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid ""
"Something happened while trying to contact the server, check that the server"
" is online and that you still have a working network connection."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Something horrible happened"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Sort graph"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Special:"
msgstr "სპეციალური:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Stacked"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Start typing..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading..."
msgstr "ჯერ კიდევ იტვირთება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "ჯერ კიდევ იტვირთება... <br />გთხოვთ გამოიჩინოთ მოთმინება..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Style"
msgstr "სტილი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Styles"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Support"
msgstr "მხარდაჭერა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Syntax error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Tags"
msgstr "ტეგები"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Take a minute to get a coffee,"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr "შეგიძლიათ ყავა მიირთვათ ერთი წუთით, <br />რადგან ეს ისევ იტვირთება..."

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__vat
msgid "Tax ID"
msgstr "საგადასახადო იდენტიფიკატორი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Technical Translation"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
msgid "Tel:"
msgstr "ტელ:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Template %s already exists in module %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Template:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Text"
msgstr "ტექსტი"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The content of this cell is too long for an XLSX file (more than %s "
"characters). Please use the CSV format for this export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The field is empty, there's nothing to save."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "პაროლი და მისი დადასტურება უნდა იყოს იდენტური."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr ""
"ძველი პაროლი რაც თქვენ შეიყვანეთ არასწორია, თქვენი პაროლი არ შეიცვალა."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 "
"(.xlsx) format. Consider splitting the export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "There is no available image to be set as cover."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "There was a problem while uploading your file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/date_picker.js:0
#, python-format
msgid "This date is in the future. Make sure this is what you expect."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/datepicker/datepicker.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "This domain is not supported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "This file is invalid. Please select an image."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "This update will only consider the records of the current page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Today"
msgstr "დღეს"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Toggle"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Tomorrow"
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid "Too many items to display."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Total"
msgstr "ჯამი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#, python-format
msgid "Translate: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "True"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/helpers/no_content_helpers.xml:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is no\n"
"                    active filter in the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Try to change your domain to only display positive results"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Type:"
msgstr "სახეობა:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "URL"
msgstr "URL მისამართი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Unarchive All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught CORS Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Javascript Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Promise"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_model.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "Undefined"
msgstr "განუსაზღვრელი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Unfold"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid ""
"Unknown CORS error\n"
"\n"
"An unknown CORS error occured.\n"
"The error probably originates from a JavaScript file served from a different origin.\n"
"(Opening your browser console might give you a hint on the error.)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/py_utils.js:0
#, python-format
msgid "Unknown nonliteral type "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Unlink row "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/_deprecated/data.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#, python-format
msgid "Unnamed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Untitled"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Update to:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#, python-format
msgid "Upgrade now"
msgstr "გაუმჯობესება"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Upgrade to enterprise"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Upgrade to future versions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Upload and Set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_bar.js:0
#, python-format
msgid "Upload cancelled"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Upload your file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploaded"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploading"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Uploading Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploading..."
msgstr "მიმდინარეობს ატვირთვა ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#, python-format
msgid "Uploading... (%s%%)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Use This For My Kanban"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Use by default"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu.xml:0
#, python-format
msgid "User"
msgstr "მომხმარებელი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "User Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Validation Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_renderer.js:0
#, python-format
msgid "Values set here are company-specific."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Variation"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Access Rights"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Fields"
msgstr "ველების ნახვა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "View Metadata"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Record Rules"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_ir_actions_act_window_view__view_mode
msgid "View Type"
msgstr "ხედის ტიპი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View switcher"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid "Warning"
msgstr "გაფრთხილება"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__website
msgid "Website Link"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Week"
msgstr "კვირა"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Widget:"
msgstr "ვიჯეთი:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Windows/Linux"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid "Wrap raw html within an iframe"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Wrong login/password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Year"
msgstr "წელი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Yellow"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "Yes"
msgstr "დიახ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Yesterday"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You cannot leave any password empty."
msgstr "თქვენ არ შეგიძლიათ დატოვოთ არცერთი პაროლი ცარიელი"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "You may not believe it,"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr "თქვენ იქნებ არ გჯერათ ეს, <br />მაგრამ აპლიკაცია ამჟამად იტვირთება..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"You need to save this new record before editing the translation. Do you want"
" to proceed?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Your Odoo session expired. The current page is about to be refreshed."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/name_and_signature.js:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Your name"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "[No widget %s]"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "a day ago"
msgstr "ერთი დღის წინ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a minute ago"
msgstr "დაახლოებით ერთი წუთის წინ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a month ago"
msgstr "დაახლოებით ერთი თვის წინ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a year ago"
msgstr "დაახლოებით ერთი წლის წინ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about an hour ago"
msgstr "დაახლოებით ერთი საათის წინ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "all records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "are valid for this update."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "as a new"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "at:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "because it's loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "but the application is actually loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "child of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "contains"
msgstr "შეიცავს"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "date"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "does not contain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "doesn't contain"
msgstr "არ შეიცავს"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/network/download.js:0
#, python-format
msgid "downloading..."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "e.g. Global Business Solutions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "for:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than"
msgstr "მეტია ვიდრე"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "hex"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "in"
msgstr "ში"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is"
msgstr "არის"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is between"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is equal to"
msgstr "უდრის"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is false"
msgstr "არის სიცრუე"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not"
msgstr "არ არის"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "is not ="
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not equal to"
msgstr "არ უდრის"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is true"
msgstr "არის სიმართლე"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "it's still loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/fields/formatters.js:0
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "kMGTPE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than"
msgstr "ნაკლებია ვიდრე"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "less than a minute ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_mixin.js:0
#, python-format
msgid "message: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "ms"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "no description provided"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "not"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not in"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not set (false)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of the following rules:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/action_model.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/search_model.js:0
#, python-format
msgid "or"
msgstr "ან"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "parent of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#, python-format
msgid "props.fields"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "query"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "record(s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "records ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "remaining)"
msgstr "დარჩენილი)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "search"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "selected"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "selected records,"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "set (true)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/effects/effect_service.js:0
#, python-format
msgid "well Done!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
#, python-format
msgid "{\"o_act_window\": actionType === \"ir.actions.act_window\"}"
msgstr ""
