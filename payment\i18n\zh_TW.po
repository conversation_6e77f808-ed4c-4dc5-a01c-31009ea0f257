# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-10 14:27+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "獲取的資料"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Amount:</b>"
msgstr "<b>金額</b>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Reference:</b>"
msgstr "<b>編碼</b>"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %s</li><li>Account Number: "
"%s</li><li>Account Holder: %s</li></ul>"
msgstr ""
"<h3>請依已下資訊付款：</h3><ul><li>銀行： %s</li><li>帳號： %s</li><li>戶名： %s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr "<i class=\"fa fa-arrow-circle-right\"/> 返回我的帳戶"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_breadcrumb
msgid "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"
msgstr "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.manage
msgid "<i class=\"fa fa-trash\"/> Delete"
msgstr "<i class=\"fa fa-trash\"/>刪除"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Enterprise</span>"
msgstr ""
"<span class=\"badge badge-primary oe_inline o_enterprise_label\">企業</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Saved Payment Methods</span>"
msgstr "<span class=\"o_stat_text\">儲存的付款方式</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "<span><i class=\"fa fa-arrow-right\"/> Get my Stripe keys</span>"
msgstr "<span><i class=\"fa fa-arrow-right\"/>獲取我的 Stripe 密鑰</i></span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span><i class=\"fa fa-arrow-right\"/> How to configure your PayPal "
"account</span>"
msgstr "<span><i class=\"fa fa-arrow-right\"/>如何配置您的 PayPal 帳戶</i></span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span>Start selling directly without an account; an email will be sent by "
"Paypal to create your new account and collect your payments.</span>"
msgstr "<span>在沒有帳戶的情況下開始直接銷售;Paypal 將發送一封電子郵件，以建立您的新帳戶並收取您的付款。</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid "<strong>No suitable payment acquirer could be found.</strong>"
msgstr "<strong>沒有找到合適的收款服務商。</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>找不到合適的付款方式.</strong><br/>\n"
"                                如果您認為這是一個錯誤，請聯繫網站管理員"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment acquirer configuration."
msgstr ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment acquirer configuration."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid ""
"<strong>Warning</strong> Creating a payment acquirer from the <em>CREATE</em> button is not supported.\n"
"                        Please use the <em>Duplicate</em> action instead."
msgstr ""
"<strong>警告</strong>不支援從<em>CREATE</em>按鈕建立付款服務商。\n"
"                        請改用<em>複制</em>操作。"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>Warning</strong> Make sure your are logged in as the right partner "
"before making this payment."
msgstr "<strong>警告</strong>在付款之前，請確保您以正確的合作夥伴身份登入。"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>警告:</strong>貨別未填或不正確。"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr "<strong>警告</strong>您必須登入後才能付款。"

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A payment transaction with reference %s already exists."
msgstr "使用 %s 的付款交易已存在。"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A refund request of %(amount)s has been sent. The payment will be created "
"soon. Refund transaction reference: %(ref)s (%(acq_name)s)."
msgstr "已發送 %(amount)s 的退款請求。將很快進行處理。退款交易參考：%(ref)s (%(acq_name)s)。"

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr "建立新的支付交易需要一個代碼(token)。"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated (%(acq_name)s)."
msgstr "引用 %(ref)s 的交易已啟動 (%(acq_name)s)。"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated using the payment "
"method %(token_name)s (%(acq_name)s)."
msgstr "已使用付款方式 %(token_name)s (%(acq_name)s) 發起了號碼為 %(ref)s 的交易。"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__access_token
msgid "Access Token"
msgstr "存取代碼 (token)"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Account"
msgstr "帳戶"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "帳戶號碼"

#. module: payment
#: code:addons/payment/models/account_payment_method.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
#, python-format
msgid "Acquirer"
msgstr "服務商"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_id
msgid "Acquirer Account"
msgstr "服務商帳號"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_ref
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_reference
msgid "Acquirer Reference"
msgstr "服務商編碼"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__acquirer_ids
msgid "Acquirers"
msgstr "支付方式"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Acquirers list"
msgstr "支付方式列表"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Activate"
msgstr "啟動"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Activate Stripe"
msgstr "啟用 Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "啟用"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_active
msgid "Add Extra Fees"
msgstr "添加額外的費用"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Address"
msgstr "地址"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_alipay
msgid "Alipay"
msgstr "支付宝"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__allow_tokenization
msgid "Allow Saving Payment Methods"
msgstr "允許儲存付款方式"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Amount"
msgstr "總額"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr "可退款金額"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "最大金額"

#. module: payment
#. openerp-web
#: code:addons/payment/controllers/portal.py:0
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "An error occurred during the processing of this payment."
msgstr "處理此付款時出錯"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Apply"
msgstr "套用"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Archived"
msgstr "歸檔"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Are you sure you want to delete this payment method?"
msgstr "確定要刪除此付款方式嗎？"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr "請問您是否確定要取消授權交易嗎？此操作經確定後無法撤消。"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_authorization
msgid "Authorize Mechanism Supported"
msgstr "批准支援的機制"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__auth_msg
msgid "Authorize Message"
msgstr "授權消息"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_authorize
msgid "Authorize.net"
msgstr "Authorize.net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "授權"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "已授權的交易"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Availability"
msgstr "可用"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Bank"
msgstr "銀行"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Bank Accounts"
msgstr "銀行帳戶"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "銀行名稱"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_model_id
msgid "Callback Document Model"
msgstr "回調單據模型"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_is_done
msgid "Callback Done"
msgstr "Callback Done"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_hash
msgid "Callback Hash"
msgstr "回調哈希函數"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_method
msgid "Callback Method"
msgstr "回調方法"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_res_id
msgid "Callback Record ID"
msgstr "Callback Record ID"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "已取消"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__cancel_msg
msgid "Canceled Message"
msgstr "已取消訊息"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Cancelled payments"
msgstr "取消付款"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__capture_manually
msgid "Capture Amount Manually"
msgstr "手動獲取金額"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Capture Transaction"
msgstr "獲取交易"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Check here"
msgstr "檢查這裡"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_open_payment_onboarding_payment_acquirer_wizard
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "選擇付款方式"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "City"
msgstr "城市"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Click here to be redirected to the confirmation page."
msgstr "點選此處可重導向到確認頁面。"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
msgid "Close"
msgstr "關閉"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "代號"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__color
msgid "Color"
msgstr "顏色"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Communication"
msgstr "溝通歷程"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "公司"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__company_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__company_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Company"
msgstr "公司"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Configuration"
msgstr "配置"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Configure"
msgstr "設定"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Confirm Deletion"
msgstr "確認刪除"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
msgid "Confirmed"
msgstr "已確認"

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_id
msgid "Corresponding Module"
msgstr "對應模組"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__country_ids
msgid "Countries"
msgstr "國家"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Country"
msgstr "國家"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__tokenize
msgid "Create Token"
msgstr "建立密鑰"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_acquirer
msgid "Create a new payment acquirer"
msgstr "建立新的付款服務商"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_token
msgid "Create a new payment token"
msgstr "建立一個新的支付密鑰"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_icon
msgid "Create a payment icon"
msgstr "建立付款圖示"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "創立者"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "建立於"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Credentials"
msgstr "授權認證"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_stripe
msgid "Credit & Debit Card"
msgstr ""

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_adyen
msgid "Credit Card (powered by Adyen)"
msgstr "信用卡（由 Adyen 支援運行）"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_alipay
msgid "Credit Card (powered by Alipay)"
msgstr "信用卡（由 Alipay 支付寶支援運行）"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_authorize
msgid "Credit Card (powered by Authorize)"
msgstr "信用卡（由 Authorize 支援運行）"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_buckaroo
msgid "Credit Card (powered by Buckaroo)"
msgstr "信用卡（由 Buckaroo 支援運行）"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_ogone
msgid "Credit Card (powered by Ogone)"
msgstr "信用卡（由 Ogone 提供支持）"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payulatam
msgid "Credit Card (powered by PayU Latam)"
msgstr "信用卡（由 PayU Latam 支援運行）"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payumoney
msgid "Credit Card (powered by PayUmoney)"
msgstr "信用卡（由 PayUmoney 支援運行）"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_sips
msgid "Credit Card (powered by Sips)"
msgstr "信用卡（由 Sips 支援運行）"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__stripe
msgid "Credit card (via Stripe)"
msgstr "信用卡（通過Stripe支付）"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "幣別"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "自訂付款說明"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "客戶"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__sequence
msgid "Define the display order"
msgstr "定義資料排序"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__description
msgid "Description"
msgstr "說明"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__display_as
msgid "Description of the acquirer for customers"
msgstr "服務商對客戶的描述"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Disabled"
msgstr "停用"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_status
msgid "Dismiss"
msgstr "解除"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_icon__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_as
msgid "Displayed as"
msgstr "顯示為"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__done
msgid "Done"
msgstr "完成"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__done_msg
msgid "Done Message"
msgstr "完成的訊息"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "草稿"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Email"
msgstr "電郵"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Enable credit &amp; debit card payments supported by Stripe"
msgstr "啟用以 Stripe 支援的信用卡及扣賬卡付款"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__enabled
msgid "Enabled"
msgstr "啟用"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "錯誤"

#. module: payment
#. openerp-web
#: code:addons/payment/models/payment_transaction.py:0
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Error: %s"
msgstr "錯誤: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__fees
#: model_terms:ir.ui.view,arch_db:payment.checkout
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Fees"
msgstr "費用"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_fees_computation
msgid "Fees Computation Supported"
msgstr "支援費用計算"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_fixed
msgid "Fixed domestic fees"
msgstr "國內固定費用"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_fixed
msgid "Fixed international fees"
msgstr "固定的手續費"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__acquirer_id
msgid "Force Payment Acquirer"
msgstr "強制付款服務商"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_link_wizard__acquirer_id
msgid ""
"Force the customer to pay via the specified payment acquirer. Leave empty to"
" allow the customer to choose among all acquirers."
msgstr "強制客戶通過指定的服務商付款。留空以允許客戶在所有服務商中進行選擇。"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "From"
msgstr "由"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__support_refund__full_only
msgid "Full Only"
msgstr "只支援全額"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "生成付款連結"

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "生成付款連結"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "產生付款網址"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Group By"
msgstr "分組依據"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__has_multiple_acquirers
msgid "Has Multiple Acquirers"
msgstr "有多個服務商"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr "有待處理的退款"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__is_post_processed
msgid "Has the payment been post-processed"
msgstr "付款是否經過後期處理"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pre_msg
msgid "Help Message"
msgstr "幫助訊息"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__new_user
msgid "I don't have a Paypal account"
msgstr "我沒有 Paypal帳戶"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__existing_user
msgid "I have a Paypal account"
msgstr "我有一個 Paypal帳戶"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_icon__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "If not defined, the acquirer name will be used."
msgstr "如果未定義，將使用第三方金流平台名稱。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "If the payment hasn't been confirmed you can contact us."
msgstr "如果付款尚未確認，您可以聯繫我們。"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr "如果您認為這是一個錯誤，請聯繫網站管理員。"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__image_128
#: model:ir.model.fields,field_description:payment.field_payment_icon__image
msgid "Image"
msgstr "圖像"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__image_payment_form
msgid "Image displayed on the payment form"
msgstr "在付款表單上顯示的圖像"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment_method_line__payment_acquirer_state
#: model:ir.model.fields,help:payment.field_payment_acquirer__state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the acquirer."
msgstr ""
"在測試模式下，通過測試支付接口處理虛擬支付。\n"
"設置服務商時建議使用此模式。"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__inline_form_view_id
msgid "Inline Form Template"
msgstr "內部表單模板"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Install"
msgstr "安裝"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_state
msgid "Installation State"
msgstr "安裝狀態"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
msgid "Installed"
msgstr "已安裝"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Internal server error"
msgstr "內部伺服器錯誤"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "應收憑單"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "應收憑單"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr "無效憑單數量"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_post_processed
msgid "Is Post-processed"
msgstr "是否後處理"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "It is currently linked to the following documents:"
msgstr "它目前連接到以下文件："

#. module: payment
#: model:ir.model,name:payment.model_account_journal
msgid "Journal"
msgstr "日記帳"

#. module: payment
#: model:ir.model,name:payment.model_account_move
msgid "Journal Entry"
msgstr "日記帳分錄"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__just_done
msgid "Just done"
msgstr "完成"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__landing_route
msgid "Landing Route"
msgstr "登陸路線"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "語言"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_icon____last_update
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__last_state_change
msgid "Last State Change Date"
msgstr "最後狀態更改日期"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Leave empty to allow all acquirers"
msgstr "留空以允許所有服務商"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_meth_link
msgid "Manage payment methods"
msgstr "管理付款方法"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "手動"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr "允許的最大退款"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "商戶帳戶 ID"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Message"
msgstr "消息"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Messages"
msgstr "訊息"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "方法"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Multiple payment options selected"
msgstr "選擇了多種付款方式"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__name
#: model:ir.model.fields,field_description:payment.field_payment_icon__name
#: model:ir.model.fields,field_description:payment.field_payment_token__name
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Name"
msgstr "名稱"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__provider__none
msgid "No Provider Set"
msgstr "無服務商"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Acquirer menu."
msgstr "找不到該公司的手動付款條件。請從付款獲取者功能表建立一個。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "No payment has been processed."
msgstr "未處理任何付款。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "No payment option selected"
msgstr "未選擇付款方式"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__not_done
msgid "Not done"
msgstr "尚未完成"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "Not verified"
msgstr "未驗證"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from acquirers allowing to capture the amount are "
"available."
msgstr "請注意，只有來自允許獲取金額的收單方的密鑰才可用。"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from acquirers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr "請注意，來自設定為僅授權交易（而非獲取金額）的收購方的標記不可用。"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "Odoo 企業版專屬模組"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__offline
msgid "Offline payment by token"
msgstr "密鑰離線支付"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_ogone
msgid "Ogone"
msgstr "Ogone"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
#, python-format
msgid "Ok"
msgstr "確定"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Online Payments"
msgstr "網上付款"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_direct
msgid "Online direct payment"
msgstr "線上直接支付"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_token
msgid "Online payment by token"
msgstr "密鑰線上支付"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_redirect
msgid "Online payment with redirection"
msgstr "帶重定向的線上支付"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Online payments enabled"
msgstr "網上付款已啟用"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Only administrators can access this data."
msgstr "只有系統管理員可以存取此筆資料。"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be captured."
msgstr "只能抓取授權交易。"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be voided."
msgstr "只有授權交易才能作廢。"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only confirmed transactions can be refunded."
msgstr "只有已確認的交易才能退款。"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__operation
msgid "Operation"
msgstr "製程"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "其他"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__other
msgid "Other payment acquirer"
msgstr "其他付款平台"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "PDT 標識 Token"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__support_refund__partial
msgid "Partial"
msgstr "支援未足額 / 非完整款項"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Partner"
msgstr "業務夥伴"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "合作夥伴名稱"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.checkout
msgid "Pay"
msgstr "付款"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
#: model:payment.acquirer,name:payment.payment_acquirer_paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payulatam
msgid "PayU Latam"
msgstr "PayU Latam"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payumoney
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "付款"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer
#: model:ir.model.fields,field_description:payment.field_account_payment_method_line__payment_acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Acquirer"
msgstr "付款服務商"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_acquirer
#: model:ir.ui.menu,name:payment.payment_acquirer_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_list
msgid "Payment Acquirers"
msgstr "支付方式"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__available_acquirer_ids
msgid "Payment Acquirers Available"
msgstr "可用的付款服務商"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "付款金額"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Followup"
msgstr "付款追蹤"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Form"
msgstr "付款表单"

#. module: payment
#: model:ir.model,name:payment.model_payment_icon
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Payment Icon"
msgstr "支付圖示"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_icon
#: model:ir.ui.menu,name:payment.payment_icon_menu
msgid "Payment Icons"
msgstr "支付圖示"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "支付說明"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__journal_id
msgid "Payment Journal"
msgstr "付款日記帳"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "付款連結"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "付款方法"

#. module: payment
#: model:ir.model,name:payment.model_account_payment_method_line
msgid "Payment Methods"
msgstr "付款方法"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__description
msgid "Payment Ref"
msgstr "付款编号"

#. module: payment
#: model:ir.model,name:payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr "付款退款引導"

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__token_id
msgid "Payment Token"
msgstr "付款代碼(token)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Payment Token Count"
msgstr "支付密鑰數"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_token
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model:ir.ui.menu,name:payment.payment_token_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_list
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Payment Tokens"
msgstr "付款代碼(token)"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__transaction_id
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.model.fields,field_description:payment.field_payment_token__transaction_ids
#: model:ir.ui.menu,name:payment.payment_transaction_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_list
msgid "Payment Transactions"
msgstr "付款交易"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction_linked_to_token
msgid "Payment Transactions Linked To Token"
msgstr "與密鑰相關的支付交易"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer_onboarding_wizard
msgid "Payment acquire onboarding wizard"
msgstr "付款獲得onboarding嚮導"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__payment_acquirer_selection
msgid "Payment acquirer selected"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_account_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
msgid "Payments"
msgstr "付款"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Payments failed"
msgstr "付款失敗"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Payments received"
msgstr "付款已收到"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "Paypal用戶類型"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "暫停"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pending_msg
msgid "Pending Message"
msgstr "待定消息"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "電話"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Please select a payment option."
msgstr "請選擇付款方式。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Please select only one payment option."
msgstr "請僅選擇一種付款方式。"

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "Please set an amount smaller than %s."
msgstr "請設置小於 %s 的金額。"

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "Please switch to company '%s' to make this payment."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please use the following transfer details"
msgstr "請使用以下轉帳詳細資訊"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please use the order name as communication reference."
msgstr "請使用訂單名稱作為備註參考。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Please wait ..."
msgstr "請稍候..."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "處理人"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment.field_payment_token__provider
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
msgid "Provider"
msgstr "服務商"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Reason:"
msgstr "原因："

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Reason: %s"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__redirect_form_view_id
msgid "Redirect Form Template"
msgstr "重定向表單模板"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Reference"
msgstr "編號"

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "引用必須唯一!"

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#: code:addons/payment/models/account_payment.py:0
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__refund
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.view_account_payment_form_inherit_payment
#, python-format
msgid "Refund"
msgstr "折讓"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr "退款金額"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr "退款金額"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "退款"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__refunds_count
#: model:ir.model.fields,field_description:payment.field_payment_transaction__refunds_count
msgid "Refunds Count"
msgstr "退款次數"

#. module: payment
#: model:ir.model,name:payment.model_account_payment_register
msgid "Register Payment"
msgstr "登記付款"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "相關單據編號"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "相關的單據模型"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "SEPA 直接扣賬"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_account_journal_form
msgid "SETUP"
msgstr "設定"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.manage
msgid "Save Payment Method"
msgstr "儲存付款方式"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.checkout
msgid "Save my payment details"
msgstr "儲存我的付款詳情"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr "儲存的付款密鑰"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr "儲存的付款密鑰"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Select countries. Leave empty to use everywhere."
msgstr "選擇國家/地區。留空以隨處使用。"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr "選擇付款方式"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__sequence
#: model:ir.model.fields,field_description:payment.field_payment_icon__sequence
msgid "Sequence"
msgstr "序號"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Server Error"
msgstr "伺服器錯誤"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Server error:"
msgstr "伺服器錯誤:"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_allow_tokenization
msgid "Show Allow Tokenization"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_auth_msg
msgid "Show Auth Msg"
msgstr "顯示驗證消息"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_cancel_msg
msgid "Show Cancel Msg"
msgstr "顯示取消消息"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_credentials_page
msgid "Show Credentials Page"
msgstr "顯示驗證頁面"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_done_msg
msgid "Show Done Msg"
msgstr "顯示完成消息"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_payment_icon_ids
msgid "Show Payment Icon"
msgstr "顯示付款圖示"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_pending_msg
msgid "Show Pending Msg"
msgstr "顯示待處理消息"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_pre_msg
msgid "Show Pre Msg"
msgstr "顯示預消息"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sips
msgid "Sips"
msgstr "Sips"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr "來源付款"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__source_transaction_id
msgid "Source Transaction"
msgstr "來源交易"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_method_line__payment_acquirer_state
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__state
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_state_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "State"
msgstr "狀態"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_acquirer_onboarding_state
msgid "State of the onboarding payment acquirer step"
msgstr "初始支付服務商的狀態"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Status"
msgstr "狀態"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.acquirer,name:payment.payment_acquirer_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Stripe 公鑰"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Stripe 密鑰"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr "合適的支付密鑰"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__payment_icon_ids
msgid "Supported Payment Icons"
msgstr "支援的支付圖示"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,help:payment.field_account_payment_register__use_electronic_payment_method
msgid "Technical field used to hide or show the payment_token_id if needed."
msgstr "如果需要，用於隱藏或顯示 payment_token_id 的技術欄位。"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_test
msgid "Test"
msgstr "測試"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__test
#: model_terms:ir.ui.view,arch_db:payment.checkout
#: model_terms:ir.ui.view,arch_db:payment.manage
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Test Mode"
msgstr "測試模式"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__provider
#: model:ir.model.fields,help:payment.field_payment_token__provider
#: model:ir.model.fields,help:payment.field_payment_transaction__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "與此服務商一起使用的支付服務供應商"

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "The access token is invalid."
msgstr "存取代碼(token)無效。"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__acquirer_ref
msgid "The acquirer reference of the token of the transaction"
msgstr "交易密鑰的服務商編號"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__acquirer_reference
msgid "The acquirer reference of the transaction"
msgstr "交易的服務商編號"

#. module: payment
#: code:addons/payment/wizards/payment_refund_wizard.py:0
#, python-format
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr "要退還的金額必須為正數且不能高於 %s。"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__name
msgid "The anonymized acquirer reference of the payment method"
msgstr "付款方式的匿名服務商參考"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__color
msgid "The color of the card in kanban view"
msgstr "看板視圖中卡片的顏色"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "The complementary information message about the state"
msgstr "關於狀態的補充資訊消息"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__country_ids
msgid ""
"The countries for which this payment acquirer is available.\n"
"If none is set, it is available for all countries."
msgstr ""
"此付款服務商適用的國家/地區。\n"
"如果未設置，則適用於所有國家/地區。"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__description
msgid "The description shown in the card in kanban view "
msgstr "看板視圖中卡片中顯示的描述"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__fees
msgid "The fees amount; set by the system as it depends on the acquirer"
msgstr "費用金額；由系統設置，因為它取決於服務商"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The following fields must be filled: %s"
msgstr "必須填寫以下欄位：%s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "The internal reference of the transaction"
msgstr "交易編號"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__journal_id
msgid "The journal in which the successful transactions are posted"
msgstr "過賬成功交易的日記賬"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__acquirer_ids
msgid "The list of acquirers supporting this payment icon"
msgstr "支持此付款圖示的服務商列表"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__auth_msg
msgid "The message displayed if payment is authorized"
msgstr "授權付款時顯示的消息"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__cancel_msg
msgid ""
"The message displayed if the order is canceled during the payment process"
msgstr "付款過程中訂單取消時顯示的消息"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__done_msg
msgid ""
"The message displayed if the order is successfully done after the payment "
"process"
msgstr "付款過程後訂單成功完成時顯示的消息"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pending_msg
msgid "The message displayed if the order pending after the payment process"
msgstr "付款過程後訂單待處理時顯示的消息"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pre_msg
msgid "The message displayed to explain and help the payment process"
msgstr "顯示的消息解釋和幫助支付過程"

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr "付款應該是直接的、重定向的或通過密鑰進行的。"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "The related payment is posted: %s"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__landing_route
msgid "The route the user is redirected to after the transaction"
msgstr "交易後用戶被重定向到的路由"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr "相關退款款項來源支付"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__source_transaction_id
msgid "The source transaction of related refund transactions"
msgstr "關聯退款交易的來源交易"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__redirect_form_view_id
msgid ""
"The template rendering a form submitted to redirect the user when making a "
"payment"
msgstr "呈現表單的模板，用於在付款時重定向用戶"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a direct payment"
msgstr "進行直接付款時呈現內部付款表格的模板"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(acq_name)s)."
msgstr ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(acq_name)s)."
msgstr ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(acq_name)s)."
msgstr ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s is canceled "
"(%(acq_name)s)."
msgstr ""
"The transaction with reference %(ref)s for %(amount)s is canceled "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s is pending "
"(%(acq_name)s)."
msgstr ""
"The transaction with reference %(ref)s for %(amount)s is pending "
"(%(acq_name)s)."

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "The value of the payment amount must be positive."
msgstr "付款金額的值必須為正數。"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "There are no transactions to show"
msgstr "沒有交易可顯示"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "There is nothing to pay."
msgstr "沒有須付款項目。"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__allow_tokenization
msgid ""
"This controls whether customers can save their payment methods as payment tokens.\n"
"A payment token is an anonymous link to the payment method details saved in the\n"
"acquirer's database, allowing the customer to reuse it for a next purchase."
msgstr ""
"這控制客戶是否可以將他們的付款方式儲存為付款密鑰。\n"
"支付密鑰是一個匿名連接，指向儲存在\n"
"服務商的資料庫，允許客戶在下次購買時重複使用它。"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__image
#: model:ir.model.fields,help:payment.field_payment_icon__image_payment_form
msgid ""
"This field holds the image used for this payment icon, limited to 64x64 px"
msgstr "此欄位包含用於此付款圖示的圖像，限制為 64x64 像素"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment "
"acquirers. Setting an email for this partner is advised."
msgstr "該合作夥伴沒有電子郵件，這可能會導致某些付款服務商出現問題。建議為此合作夥伴設置電子郵件。"

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "This payment has not been processed yet."
msgstr "此付款尚未處理。"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "This payment method has been verified by our system."
msgstr "此付款方式已通過我們的系統驗證。"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "This payment method has not been verified by our system."
msgstr "我們的系統尚未驗證此付款方式。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "This transaction has been cancelled."
msgstr "該交易已被取消。"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_tokenization
msgid "Tokenization Supported"
msgstr "支援代碼化處理"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"Transaction authorization is not supported by the following payment "
"acquirers: %s"
msgstr "以下付款服務商不支持交易授權：%s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__transaction_ids
msgid "Transactions"
msgstr "交易"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_refund
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__support_refund
msgid "Type of Refund Supported"
msgstr "支援的退款類型"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Unable to contact the Odoo server."
msgstr "無法連接到Odoo伺服器。"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Upgrade"
msgstr "升級"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr "使用電子支付方式"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__validation
msgid "Validation of the payment method"
msgstr "驗證付款方式"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr "動態內部費用(百分比)"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Variable fees must always be positive and below 100%."
msgstr "可變費用必須始終為正且低於 100%。"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_var
msgid "Variable international fees (in percents)"
msgstr "可變的交易費用（百分比）"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__verified
msgid "Verified"
msgstr "已驗證"

#. module: payment
#: model:ir.model,name:payment.model_ir_ui_view
msgid "View"
msgstr "檢視"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Void Transaction"
msgstr "無效交易"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Waiting for payment"
msgstr "等待付款"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Warning!"
msgstr "警告！"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "We are not able to delete your payment method."
msgstr "我們無法刪除您的付款方式。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are not able to find your payment, but don't worry."
msgstr "我們找不到您的付款，但別擔心。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr "我們無法處理您的付款。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "We are not able to save your payment method."
msgstr "我們無法儲存您的付款方式。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/post_processing.js:0
#, python-format
msgid "We are processing your payment, please wait ..."
msgstr "我們正在處理您的付款，請稍候..."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are waiting for the payment acquirer to confirm the payment."
msgstr "我們正在等待付款服務商確認付款。"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__tokenize
msgid ""
"Whether a payment token should be created when post-processing the "
"transaction"
msgstr "交易後處理時是否應建立支付密鑰"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__callback_is_done
msgid "Whether the callback has already been executed"
msgstr "回調是否已經執行"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_transfer
msgid "Wire Transfer"
msgstr "電匯"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You can click here to be redirected to the confirmation page."
msgstr "您可以點選這裡然後被重導向到確認頁面。"

#. module: payment
#: code:addons/payment/models/account_journal.py:0
#, python-format
msgid ""
"You can't delete a payment method that is linked to an acquirer in the enabled or test state.\n"
"Linked acquirer(s): %s"
msgstr ""
"您無法刪除與處於啟用或測試狀態的收單行關聯的付款方式。\n"
"關聯服務商：%s"

#. module: payment
#: code:addons/payment/models/ir_ui_view.py:0
#, python-format
msgid "You cannot delete a view that is used by a payment acquirer."
msgstr "您不能刪除付款服務商使用的視圖。"

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "You do not have access to this payment token."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You should receive an email confirming your payment in a few minutes."
msgstr "您應該在幾分鐘內收到一封確認付款的電子信件。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You will be notified when the payment is confirmed."
msgstr "付款確認後會通知您。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You will be notified when the payment is fully confirmed."
msgstr "付款完全確認後，會通知您。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your order has been processed."
msgstr "您的訂單已經處理完畢。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your order is being processed, please wait ..."
msgstr "您的訂單正在處理中，請稍候…"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been authorized."
msgstr "您的付款已獲授權。"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been cancelled."
msgstr "您的付款已被取消。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your payment has been received but need to be confirmed manually."
msgstr "您的付款已經收到，但需要手工確認。"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_test
#, python-format
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "您的付款已成功處理，但正在等待批准。"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been successfully processed. Thank you!"
msgstr "交易完成，您的線上付款已成功處理完畢。 感謝您的訂購。"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your payment is in pending state."
msgstr "您的付款處於待處理狀態。"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "ZIP"
msgstr "郵編"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
msgid "Zip"
msgstr "郵政編碼"

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
#: model:ir.cron,cron_name:payment.cron_post_process_payment_tx
#: model:ir.cron,name:payment.cron_post_process_payment_tx
msgid "payment: post-process transactions"
msgstr "付款：後處理交易"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.icon_list
msgid "show less"
msgstr "顯示部分"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.icon_list
msgid "show more"
msgstr "顯示更多"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "to choose another payment method."
msgstr "選擇另一種付款方式。"
