<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="view_l10n_latam_identification_type_tree" model="ir.ui.view">
        <field name="name">l10n_latam.identification.type.tree</field>
        <field name="model">l10n_latam.identification.type</field>
        <field name="type">tree</field>
        <field name="arch" type="xml">
            <tree decoration-muted="(not active)" create="0" edit="0">
                <field name="name"/>
                <field name="description"/>
                <field name="country_id"/>
                <field name="active" widget="boolean_toggle"/>
            </tree>
        </field>
    </record>

    <record id="view_l10n_latam_identification_type_search" model="ir.ui.view">
        <field name="name">l10n_latam.identification.type.search</field>
        <field name="model">l10n_latam.identification.type</field>
        <field name="type">search</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="description"/>
                <field name="country_id"/>
                <filter name="active" string="Active" domain="[('active','=',True)]" help="Show active identification types"/>
                <filter name="inactive" string="Archived" domain="[('active','=',False)]" help="Show archived identification types"/>
            </search>
        </field>
    </record>

    <record id="action_l10n_latam_identification_type" model="ir.actions.act_window">
        <field name="name">Identification Type</field>
        <field name="res_model">l10n_latam.identification.type</field>
        <field name="view_mode">tree</field>
        <field name="search_view_id" ref="view_l10n_latam_identification_type_search"/>
        <field name="domain">['|', ('active', '=', True), ('active', '=', False)]</field>
        <field name="context">{"search_default_active":1}</field>
    </record>

    <menuitem action="action_l10n_latam_identification_type"
            id="menu_l10n_latam_identification_type"
            parent="contacts.res_partner_menu_config"/>

</odoo>
