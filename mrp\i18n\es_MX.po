# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__state
msgid ""
" * Draft: The MO is not confirmed yet.\n"
" * Confirmed: The MO is confirmed, the stock rules and the reordering of the components are trigerred.\n"
" * In Progress: The production has started (on the MO or on the WO).\n"
" * To Close: The production is done, the MO has to be closed.\n"
" * Done: The MO is closed, the stock moves are posted. \n"
" * Cancelled: The MO has been cancelled, can't be confirmed anymore."
msgstr ""
"* Borrador: La orden de fabricación aún no está confirmada.\n"
"* Confirmada: La orden de fabricación está confirmada, las reglas de existencias y el reordenamiento de los componentes se activan.\n"
"* En curso: la producción ha comenzado (en la orden de fabricación o en la orden de trabajo).\n"
"* Por cerrar: La producción ha terminado, se tiene que cerrar la orden de fabricación.\n"
"* Listo: se ha cerrado la orden de fabricación, los movimientos de existencias se registran.\n"
"* Cancelado: se ha cancelado la orden de fabricación, ya no se puede confirmar."

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid " <br/><br/> The components will be taken from <b>%s</b>."
msgstr " <br/><br/> Los componentes se tomarán de <b>%s</b>."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__all_available
msgid " When all components are available"
msgstr "Cuando todos los componentes están disponibles"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_count
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_count
msgid "# Bill of Material"
msgstr "# Lista de materiales"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__used_in_bom_count
msgid "# BoM Where Used"
msgstr "# Lista de materiales donde se utilizó"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_ready_count
msgid "# Read Work Orders"
msgstr "# Leer órdenes de trabajo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_count
msgid "# Work Orders"
msgstr "# Órdenes de trabajo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_template__used_in_bom_count
msgid "# of BoM Where is Used"
msgstr "# de lista de materiales donde se utiliza"

#. module: mrp
#: code:addons/mrp/models/mrp_routing.py:0
#, python-format
msgid "%i work orders"
msgstr "%i órdenes de trabajo"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "%s %s unbuilt in"
msgstr "%s %s desmantelado en"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "%s (new) %s"
msgstr "%s (nuevo) %s"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "%s Child MO's"
msgstr "%s Órdenes de fabricación hijas"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "%s cannot be deleted. Try to cancel them before."
msgstr "%s no se pueden eliminar, intente cancelarlas antes."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "&amp; Cost"
msgstr "&amp; Costo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "&gt;"
msgstr "&gt;"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_bom_structure
msgid "'Bom Structure - %s' % object.display_name"
msgstr "'Estructura de LdM - %s' % object.display_name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_finished_product
msgid "'Finished products - %s' % object.name"
msgstr "'Productos terminados - %s' % object.name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_production_order
msgid "'Production Order - %s' % object.name"
msgstr "'Orden de producción - %s' % object.name"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr "+ %d día(s)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            Puede que se necesiten acciones manuales."

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_leg
#: model_terms:product.template,description:mrp.product_product_computer_desk_leg_product_template
msgid "18″ x 2½″ Square Leg"
msgstr "Pata cuadrada de 18 \"x 2½\""

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ": Insufficient Quantity To Unbuild"
msgstr ": cantidad insuficiente para desmantelar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Gestionar\" "
"title=\"Gestionar\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"
msgstr "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pausa\" title=\"Pausa\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-play\" role=\"img\" aria-label=\"Run\" title=\"Run\"/>"
msgstr "<i class=\"fa fa-play\" role=\"img\" aria-label=\"Ejecutar\" title=\"Ejecutar\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"
msgstr "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Detener\" title=\"Detener\"/>"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your product\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Suba archivos a su producto\n"
"                    </p><p>\n"
"                        Use esta función para guardar cualquier archivo, como diagramas o especificaciones.\n"
"                    </p>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Los valores establecidos aquí"
" son específicos de la empresa.\" role=\"img\" aria-label=\"Los valores "
"establecidos aquí son específicos de la empresa.\" "
"groups=\"base.group_multi_company\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"font-weight-bold\">To Produce</span>"
msgstr "<span class=\"font-weight-bold\">Para producir</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Backorders</span>"
msgstr "<span class=\"o_stat_text\">Órdenes parciales</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Child MO</span>"
msgstr "<span class=\"o_stat_text\">Orden de fabricación secundaria</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Load</span>"
msgstr "<span class=\"o_stat_text\">Cargar</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Lost</span>"
msgstr "<span class=\"o_stat_text\">Perdidos</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "<span class=\"o_stat_text\">Manufactured</span>"
msgstr "<span class=\"o_stat_text\">Fabricados</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">OEE</span>"
msgstr "<span class=\"o_stat_text\">OEE</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Performance</span>"
msgstr "<span class=\"o_stat_text\">Rendimiento </span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">Routing<br/>Performance</span>"
msgstr "<span class=\"o_stat_text\">Rendimiento del<br/>enrutamiento</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span class=\"o_stat_text\">Scraps</span>"
msgstr "<span class=\"o_stat_text\">Desechos </span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Source MO</span>"
msgstr "<span class=\"o_stat_text\">Orden de fabricación de origen</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "<span><strong>Unit Cost</strong></span>"
msgstr "<span><strong>Costo unitario</strong></span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Actions</span>"
msgstr "<span>Acciones</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "<span>Generate</span>"
msgstr "<span>Generar</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_operation_line
msgid "<span>Minutes</span>"
msgstr "<span>Minutos</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>New</span>"
msgstr "<span>Nuevo</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>Orders</span>"
msgstr "<span>Órdenes</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>PLAN ORDERS</span>"
msgstr "<span>PLANEAR ÓRDENES</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_report_delivery_no_kit_section
msgid "<span>Products not associated with a kit</span>"
msgstr "<span>Productos no asociados con un kit</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Reportes</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>WORK ORDERS</span>"
msgstr "<span>ÓRDENES DE TRABAJO</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>minutes</span>"
msgstr "<span>minutos</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<strong class=\"mr8 oe_inline\">to</strong>"
msgstr "<strong class=\"mr8 oe_inline\">a</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Description:</strong><br/>"
msgstr "<strong>Descripción:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Effectiveness Category: </strong>"
msgstr "<strong>Categoría de eficiencia: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Finished Product:</strong><br/>"
msgstr "<strong>Producto terminado:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Is a Blocking Reason? </strong>"
msgstr "<strong>¿Es una razón de bloqueo? </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>No. Of Minutes</strong>"
msgstr "<strong>Número de minutos</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Operation</strong>"
msgstr "<strong>Operación</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity to Produce:</strong><br/>"
msgstr "<strong>Cantidad por producir:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Reason: </strong>"
msgstr "<strong>Razón: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Responsible:</strong><br/>"
msgstr "<strong>Responsable:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source Document:</strong><br/>"
msgstr "<strong>Documento de origen:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Start Date: </strong>"
msgstr "<strong>Fecha de inicio:</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>Fecha de finalización:</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>WorkCenter</strong>"
msgstr "<strong>Centro de trabajo</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Workcenter: </strong>"
msgstr "<strong>Centro de trabajo: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? Esto puede causar inconsistencias en su inventario."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "A BoM of type kit is used to split the product into its components."
msgstr ""
"Se utiliza una lista de materiales de tipo kit para dividir el producto "
"entre sus componentes."

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "A Manufacturing Order is already done or cancelled."
msgstr "Una orden de fabricación ya ha sido terminada o cancelada."

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid ""
"A product with a kit-type bill of materials can not have a reordering rule."
msgstr ""
"Un producto con una lista de materiales tipo kit no puede tener una regla de"
" reordenamiento. "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__access_token
msgid "Access Token"
msgstr "Token de acceso"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_rule__action
msgid "Action"
msgstr "Acción"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__active
#: model:ir.model.fields,field_description:mrp.field_mrp_document__active
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__active
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__active
msgid "Active"
msgstr "Activo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de actividad de excepción"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actividad"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Add a description..."
msgstr "Agregar una descripción.."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Add by-products to bills of materials. This can be used to get several "
"finished products as well. Without this option you only do: A + B = C. With "
"the option: A + B = C + D."
msgstr ""
"Agregue subproductos a las listas de materiales. Esto se puede usar para "
"obtener varios productos terminados al mismo tiempo. Sin esta opción, solo "
"puede hacer:  A + B = C. Con ella: A + B = C + D."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Add quality checks to your work orders"
msgstr "Agregue controles de calidad a sus ordenes de trabajo"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_tag
msgid "Add tag for the workcenter"
msgstr "Agregar etiqueta para el centro de trabajo"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_manager
msgid "Administrator"
msgstr "Administrador"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "All"
msgstr "Todos"

#. module: mrp
#: code:addons/mrp/controller/main.py:0
#, python-format
msgid "All files uploaded"
msgstr "Todos los archivos subidos"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_line_bom_qty_zero
msgid ""
"All product quantities must be greater or equal to 0.\n"
"Lines with 0 quantities can be used as optional lines. \n"
"You should install the mrp_byproduct module if you want to manage extra products on BoMs !"
msgstr ""
"Todas las cantidades de producto deben ser mayores o iguales a 0.\n"
"Se pueden utilizar las líneas con cantidades en 0 como líneas opcionales. \n"
"¡Debe instalar el módulo mrp_byproduct si desea gestionar productos adicionales en las listas de materiales!"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Allow manufacturing users to modify quantities to consume, without the need "
"for prior approval"
msgstr ""
"Permitir a los usuarios de fabricación modificar las cantidades por consumir"
" sin aprobación previa"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,help:mrp.field_stock_picking_type__use_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr "Permita crear nuevos números de lote/de serie para los componentes"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__flexible
msgid "Allowed"
msgstr "Permitido"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reserve_visible
msgid "Allowed to Reserve Production"
msgstr "Se permite reservar producción"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr "Se permite anular reserva de producción"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__warning
msgid "Allowed with warning"
msgstr "Permitido con advertencia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid "Alternative Workcenters"
msgstr "Centros de trabajo alternativos"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid ""
"Alternative workcenters that can be substituted to this one in order to "
"dispatch production"
msgstr ""
"Centros de trabajo alternativos que se pueden sustituir por este para "
"despachar la producción"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid ""
"An unbuild order is used to break down a finished product into its "
"components."
msgstr ""
"Una orden de desmantelamiento se utiliza para deshacer un producto terminado"
" en sus componentes."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Apply"
msgstr "Aplicar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "Apply on Variants"
msgstr "Aplicar en variantes"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Approve"
msgstr "Aprobar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Archived"
msgstr "Archivado"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_assign_serial_numbers_production
msgid "Assign Serial Numbers"
msgstr "Asignar números de serie"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "At the creation of a Manufacturing Order."
msgstr "En la creación de una orden de fabricación."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "At the creation of a Stock Transfer."
msgstr "En la creación de un traslado de existencias."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "Attached To"
msgstr "Adjunto a"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__local_url
msgid "Attachment URL"
msgstr "URL del archivo adjunto"

#. module: mrp
#. openerp-web
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/static/src/js/mrp_bom_report.js:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
#, python-format
msgid "Attachments"
msgstr "Archivos adjuntos"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__attachments_count
msgid "Attachments Count"
msgstr "Número de archivos adjuntos"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__availability
msgid "Availability"
msgstr "Disponibilidad"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Availability Losses"
msgstr "Pérdidas de disponibilidad"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__available
#, python-format
msgid "Available"
msgstr "Disponible"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "Avatar"
msgstr "Avatar"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_product_product__produce_delay
#: model:ir.model.fields,help:mrp.field_product_template__produce_delay
msgid ""
"Average lead time in days to manufacture this product. In the case of multi-"
"level BOM, the manufacturing lead times of the components will be added."
msgstr ""
"Promedio de plazo de entrega en días para fabricar este producto. En el caso"
" de la lista de materiales de varios niveles, los plazos de fabricación de "
"los componentes se agregarán."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__variant_bom_ids
msgid "BOM Product Variants"
msgstr "Variantes de la lista de materiales del producto"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "BOM Product Variants needed to apply this line."
msgstr ""
"Se necesitan variantes de listas de materiales de producto para aplicar esta"
" línea."

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_bom_structure
msgid "BOM Structure Report"
msgstr "Reporte de estructura de lista de materiales"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_line_ids
msgid "BOM lines of the referred bom"
msgstr "Lineas de la lista de materiales de la lista de materiales referida"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder_line
msgid "Backorder Confirmation Line"
msgstr "Línea de confirmación de órdenes parciales"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_backorder_line_ids
msgid "Backorder Confirmation Lines"
msgstr "Líneas de confirmación de órdenes parciales"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Backorder MO"
msgstr "Orden de fabricación parcial"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Backorder MO's"
msgstr "Órdenes de fabricación parciales"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__backorder_sequence
msgid "Backorder Sequence"
msgstr "Secuencia de orden parcial"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__backorder_sequence
msgid ""
"Backorder sequence, if equals to 0 means there is not related backorder"
msgstr ""
"Secuencia de orden parcial, si es igual a 0 significa que no está "
"relacionada a una orden parcial."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Barcode"
msgstr "Código de barras"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode_batch
msgid "Based on"
msgstr "Basado en"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom
#: model:ir.model.fields,field_description:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Bill of Material"
msgstr "Lista de materiales"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Línea de lista de materiales"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
msgid "Bill of Material line"
msgstr "Línea de lista de materiales"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_bom_id
msgid "Bill of Material used on the Production Order"
msgstr "Lista de materiales que se utiliza en la orden de producción"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_open_bom
#: model:ir.actions.act_window,name:mrp.template_open_bom
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_ids
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Bill of Materials"
msgstr "Lista de materiales"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__mo_bom_id
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_bom_id
msgid ""
"Bill of Materials allow you to define the list of required components to "
"make a finished product."
msgstr ""
"La lista de materiales le permite definir la lista de componentes necesarios"
" para hacer un producto terminado."

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_bom_form_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom_form_action
msgid "Bills of Materials"
msgstr "Lista de materiales"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Bills of materials allow you to define the list of required raw\n"
"                materials used to make a finished product; through a manufacturing\n"
"                order or a pack of products."
msgstr ""
"Las listas de materiales le permiten definir la lista de materia prima\n"
"                requerida para fabricar el producto terminado. A través de una orden de fabricación\n"
"                o un paquete de productos."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block"
msgstr "Bloquear"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter_wo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block Workcenter"
msgstr "Bloquear centro de trabajo"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__blocked
msgid "Blocked"
msgstr "Bloqueado"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked Time"
msgstr "Tiempo bloqueado"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked hours over the last month"
msgstr "Horas bloqueadas durante el último mes"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__name
msgid "Blocking Reason"
msgstr "Motivo de bloqueo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM"
msgstr "LdM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_line_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_line_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "BoM Components"
msgstr "Componentes de LdM"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Cost"
msgstr "Costo de LdM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__bom_line_id
msgid "BoM Line"
msgstr "Línea de LdM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__bom_line_ids
msgid "BoM Lines"
msgstr "Líneas de LdM"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#: model:ir.actions.report,name:mrp.action_report_bom_structure
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "BoM Structure"
msgstr "Estructura de LdM"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#: model:ir.actions.client,name:mrp.action_report_mrp_bom
#, python-format
msgid "BoM Structure & Cost"
msgstr "Estructura y costo de LdM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__type
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "BoM Type"
msgstr "Tipo de LdM"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "BoM line product %s should not be the same as BoM product."
msgstr ""
"El producto de la linea de la lista de materiales %s no debe ser el mismo "
"que el producto de la lista de materiales."

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_bolt
#: model:product.template,name:mrp.product_product_computer_desk_bolt_product_template
msgid "Bolt"
msgstr "Tornillo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
msgid "By-Products"
msgstr "Subproductos"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_id
msgid "By-product"
msgstr "Subproducto"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "By-product %s should not be the same as BoM product."
msgstr ""
"El subproducto %s no debe ser el mismo que el producto de la lista de "
"materiales."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__byproduct_id
msgid "By-product line that generated the move in a manufacturing order"
msgstr ""
"Línea de subproductos que generó el movimiento en una orden de fabricación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__byproduct_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__byproduct_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "By-products"
msgstr "Subproductos"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "By-products cost shares must be positive."
msgstr "Los repartos de costos de subproductos deben ser positivos."

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_byproduct
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_byproduct_form_view
msgid "Byproduct"
msgstr "Subproducto"

#. module: mrp
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#, python-format
msgid "Byproducts"
msgstr "Subproductos"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any production location."
msgstr "No se puede encontrar ninguna ubicación de producción."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Cancel"
msgstr "Cancelar"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__cancel
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__cancel
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Cancelled"
msgstr "Cancelado"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Cannot delete a manufacturing order in done state."
msgstr "No se puede eliminar una orden de fabricación en estado hecho."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__capacity
msgid "Capacity"
msgstr "Capacidad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__loss_type
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Category"
msgstr "Categoría"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Change Product Qty"
msgstr "Cambiar cantidad de producto"

#. module: mrp
#: model:ir.model,name:mrp.model_change_production_qty
msgid "Change Production Qty"
msgstr "Cambiar cantidad de producción"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_change_production_qty
msgid "Change Quantity To Produce"
msgstr "Cambiar cantidad por producir"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Check availability"
msgstr "Comprobar la disponibilidad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__checksum
msgid "Checksum/SHA1"
msgstr "Suma de verificación/SHA1"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_stop
msgid "Cleanup Time"
msgstr "Hora de limpieza"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__code
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view_kanban
msgid "Code"
msgstr "Código"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__color
msgid "Color"
msgstr "Color"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__color
msgid "Color Index"
msgstr "Índice de colores"

#. module: mrp
#: model:ir.model,name:mrp.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_document__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__company_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Company"
msgstr "Empresa"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Component"
msgstr "Componente"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Component Lots must be unique for mass production. Please review reservation"
" for:\n"
msgstr ""
"Los lotes de componentes deben ser únicos para la producción en masa. "
"Compruebe la reservación para:\n"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability
msgid "Component Status"
msgstr "Estado del componente"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Component of Draft MO"
msgstr "Componente de borrador de orden de facturación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_raw_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Components"
msgstr "Componentes"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability_state
msgid "Components Availability State"
msgstr "Estado de disponibilidad de componentes"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_src_id
msgid "Components Location"
msgstr "Ubicación de los componentes"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__priority
msgid ""
"Components will be reserved first for the MO with the highest priorities."
msgstr ""
"Primero se reservarán los componentes para la orden de fabricación de mayor "
"prioridad."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__auto
msgid "Compute based on tracked time"
msgstr "Calcular según el tiempo registrado"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_computed_on
msgid "Computed on last"
msgstr "Calculado el último"

#. module: mrp
#: model:ir.model,name:mrp.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_configuration
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Configuration"
msgstr "Configuración"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Confirm"
msgstr "Confirmar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__confirm_cancel
msgid "Confirm Cancel"
msgstr "Confirmar cancelación"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Confirmed"
msgstr "Confirmado"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "Consume"
msgstr "Consumir"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_consumed_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Consumed"
msgstr "Consumido"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__consume_line_ids
msgid "Consumed Disassembly Lines"
msgstr "Líneas de desmantelamiento consumidas"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__consume_unbuild_id
msgid "Consumed Disassembly Order"
msgstr "Orden de desmantelamiento consumida"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Consumed Products"
msgstr "Productos consumidos"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__operation_id
msgid "Consumed in Operation"
msgstr "Consumido en la operación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_production__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__consumption
msgid "Consumption"
msgstr "Consumo"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_consumption_warning
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Consumption Warning"
msgstr "Advertencia de consumo"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_production__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"La conversión entre las unidades de medidas solo puede ocurrir si pertenecen"
" a la misma categoría. La conversión se hará según las cambios establecidos."

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_field_one2many_with_copy.js:0
#, python-format
msgid "Copy Existing Operations"
msgstr "Copiar operaciones existentes"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_copy_to_bom_tree_view
msgid "Copy selected operations"
msgstr "Copiar las operaciones seleccionadas"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__cost_share
#: model:ir.model.fields,field_description:mrp.field_stock_move__cost_share
msgid "Cost Share (%)"
msgstr "Reparto de costos (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__costs_hour
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__costs_hour
msgid "Cost per hour"
msgstr "Costo por hora"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Costing Information"
msgstr "Información de costos"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_backorder_count
msgid "Count of linked backorder"
msgstr "Número de órdenes parciales vinculadas"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Create Backorder"
msgstr "Crear orden parcial"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__use_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr "Crear nuevos números de lotes o de serie para componentes"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create a Backorder"
msgstr "Crear una orden parcial"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid ""
"Create a backorder if you expect to process the remaining products later. Do"
" not create a backorder if you will not process the remaining products."
msgstr ""
"Cree una orden parcial si espera procesar los productos restantes más tarde."
" No cree una orden parcial si no procesará los productos restantes."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_report
msgid "Create a new manufacturing order"
msgstr "Crear una nueva orden de fabricación"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid "Create a new operation"
msgstr "Crear una nueva operación"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid "Create a new work center"
msgstr "Crear un nuevo centro de trabajo"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_workcenter_report
msgid "Create a new work orders performance"
msgstr "Crear un nuevo rendimiento de órdenes de trabajo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create backorder"
msgstr "Crear orden parcial"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "Cree hojas de trabajo personalizables para sus controles de calidad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__created_production_id
msgid "Created Production Order"
msgstr "Orden de producción creada"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_date
msgid "Created on"
msgstr "Creado el"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Creates a new serial/lot number"
msgstr "Crea un nuevo número de serie o lote"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "Creation"
msgstr "Creación"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Cantidad actual de los productos.\n"
"En un contexto de una sola ubicación de existencias, esto incluye los artículos almacenados en esta ubicación, o cualquier ubicación secundaria.\n"
"En un contexto de un solo almacén, esto incluye los artículos almacenados en la ubicación de existencias de ese almacén, o cualquier almacén secundario.\n"
"En cualquier otro caso, esto incluye los artículos almacenados en cualquier ubicación de existencias de tipo 'Interna'."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_producing
msgid "Currently Produced Quantity"
msgstr "Cantidad producida actualmente"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_description_variants
msgid "Custom Description"
msgstr "Descripción personalizada"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__db_datas
msgid "Database Data"
msgstr "Datos de la base de datos"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Date"
msgstr "Fecha"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_planned_finished
msgid "Date at which you plan to finish the production."
msgstr "Fecha en la que planea terminar la producción."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_planned_start
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_date
msgid "Date at which you plan to start the production."
msgstr "Fecha en la que planea comenzar la producción."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_start
msgid "Date of the WO"
msgstr "Fecha de la orden de trabajo"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_finished
msgid "Date when the MO has been close"
msgstr "Fecha en la que se cerró la orden de trabajo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_deadline
msgid "Deadline"
msgstr "Fecha límite"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Default Duration"
msgstr "Duración predeterminada"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr "Plazo de fabricación predeterminado"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_consumption_warning_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""
"Unidad de medida predeterminada que se utiliza para todas las operaciones de"
" existencias."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid ""
"Define the components and finished products you wish to use in\n"
"                bill of materials and manufacturing orders."
msgstr ""
"Defina los componentes y los productos terminados que desea utilizar en\n"
"                las listas de materiales y las órdenes de fabricación."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Defina la programación del recurso."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__consumption
msgid ""
"Defines if you can consume more or less components than the quantity defined on the BoM:\n"
"  * Allowed: allowed for all manufacturing users.\n"
"  * Allowed with warning: allowed for all manufacturing users with summary of consumption differences when closing the manufacturing order.\n"
"  * Blocked: only a manager can close a manufacturing order when the BoM consumption is not respected."
msgstr ""
"Define si puede consumir más o menos componentes que la cantidad definida en la lista de materiales:\n"
"  * Permitido: está permitido para todos los usuarios de fabricación.\n"
"  * Permitido con advertencia: permitido para todos los usuarios de fabricación, con un resumen de las diferencias de consumo al cerrar la orden de fabricación.\n"
"  * Bloqueado: solamente un gerente puede cerrar una orden de fabricación cuando no se ha seguido la lista de materiales."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_type
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_type
msgid "Defines if you want to use a PDF or a Google Slide as work sheet."
msgstr ""
"Define si quiere utilizar PDF o una Presentación de Google como hoja de "
"trabajo."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__ready_to_produce
msgid ""
"Defines when a Manufacturing Order is considered as ready to be started"
msgstr ""
"Define cuándo se considera que una orden de fabricación está lista para ser "
"iniciada"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delay_alert_date
msgid "Delay Alert Date"
msgstr "Fecha de alerta de retraso"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Delete"
msgstr "Eliminar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delivery_count
msgid "Delivery Orders"
msgstr "Órdenes de entrega"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__description
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__description
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_note
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description"
msgstr "Descripción"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__note
msgid "Description of the Work Center."
msgstr "Descripción del centro de trabajo."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description of the work center..."
msgstr "Descripción del centro de trabajo."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_dest_id
msgid "Destination Location"
msgstr "Ubicación de destino"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unbuild_id
msgid "Disassembly Order"
msgstr "Orden de desmantelamiento"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Discard"
msgstr "Descartar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_document__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__display_name
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_serial_mass_produce
msgid "Display the serial mass product wizard action moves"
msgstr ""
"Mostrar los movimientos de acción del asistente de productos en serie en "
"masa"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lot_ids
msgid "Display the serial number shortcut on the moves"
msgstr "Mostrar el atajo de número de serie en los movimientos"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "Do you confirm you want to unbuild"
msgstr "¿Confirma que desea desmantelar?"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Document"
msgstr "Documento"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Documentation"
msgstr "Documentación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__is_done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Done"
msgstr "Hecho"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Download"
msgstr "Descargar"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__draft
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__draft
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Draft"
msgstr "Borrador"

#. module: mrp
#: model:product.product,name:mrp.product_product_drawer_drawer
#: model:product.template,name:mrp.product_product_drawer_drawer_product_template
msgid "Drawer Black"
msgstr "Cajón negro"

#. module: mrp
#: model:product.product,name:mrp.product_product_drawer_case
#: model:product.template,name:mrp.product_product_drawer_case_product_template
msgid "Drawer Case Black"
msgstr "Cajonera negra"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_drawer_drawer
#: model_terms:product.template,description:mrp.product_product_drawer_drawer_product_template
msgid "Drawer on casters for great usability."
msgstr "Cajón con ruedas para una gran usabilidad."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Dropdown menu"
msgstr "Menú desplegable"

#. module: mrp
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "Duplicate Serial Numbers (%s)"
msgstr "Duplicar números de serie (%s)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__duration
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Duration"
msgstr "Duración"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Duration (minutes)"
msgstr "Duración (minutos)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode
msgid "Duration Computation"
msgstr "Cálculo de duración"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_percent
msgid "Duration Deviation (%)"
msgstr "Desviación de duración (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_unit
msgid "Duration Per Unit"
msgstr "Duración por unidad"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Edit"
msgstr "Editar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_type
msgid "Effectiveness"
msgstr "Eficiencia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_type
msgid "Effectiveness Category"
msgstr "Categoría de eficiencia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_end
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_finished
msgid "End Date"
msgstr "Fecha de finalización"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,help:mrp.field_mrp_workorder__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Asegure la trazabilidad de un producto almacenable en su almacén"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_documents_controller_mixin.js:0
#, python-format
msgid "Error"
msgstr "Error"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s) occurred on the manufacturing order(s):"
msgstr "Excepciones ocurridas en las órdenes de fabricación:"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s):"
msgstr "Excepciones:"

#. module: mrp
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "Existing Serial Numbers (%s)"
msgstr "Números de serie existentes (%s)"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Exp %s"
msgstr "Esp %s"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__expected
msgid "Expected"
msgstr "Esperado"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_duration_expected
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_expected
msgid "Expected Duration"
msgstr "Duración esperada"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Expected Duration (minutes)"
msgstr "Duración esperada (minutos)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__expected_qty
msgid "Expected Quantity"
msgstr "Cantidad esperada"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__duration_expected
msgid "Expected duration (in minutes)"
msgstr "Duración esperada (en minutos)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__datas
msgid "File Content (base64)"
msgstr "Contenido del archivo (base64)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__raw
msgid "File Content (raw)"
msgstr "Contenido del archivo (sin procesar)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__file_size
msgid "File Size"
msgstr "Tamaño del archivo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Files attached to the product"
msgstr "Archivos adjuntos al producto"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Filters"
msgstr "Filtros"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__done
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Finished"
msgstr "Terminado"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__order_finished_lot_ids
msgid "Finished Lot/Serial Number"
msgstr "Número de lote o serie terminado"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_finished_ids
msgid "Finished Moves"
msgstr "Movimientos terminados"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__finished_move_line_ids
msgid "Finished Product"
msgstr "Producto terminado"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_finished_product
msgid "Finished Product Label (PDF)"
msgstr "Etiqueta de producto terminado (PDF)"

#. module: mrp
#: model:ir.actions.report,name:mrp.label_manufacture_template
msgid "Finished Product Label (ZPL)"
msgstr "Etiqueta de productos finalizados (ZPL)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_finished_ids
msgid "Finished Products"
msgstr "Productos terminados"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_dest_id
msgid "Finished Products Location"
msgstr "Ubicación de productos terminados"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__consumption
msgid "Flexible Consumption"
msgstr "Consumo flexible"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (partners)"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Force"
msgstr "Forzar"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Cantidad pronosticada (se calcula como cantidad a la mano- saliente + entrante)\n"
"En un contexto de una sola ubicación de existencias, esto incluye los artículos almacenados en esta ubicación, o cualquier ubicación secundaria.\n"
"En un contexto de un solo almacén, esto incluye los artículos almacenados en la ubicación de existencias de ese almacén, o cualquier almacén secundario.\n"
"En cualquier otro caso, esto incluye los artículos almacenados en cualquier ubicación de existencias de tipo 'Interna'."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Forecasted"
msgstr "Pronosticado"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__forecasted_issue
msgid "Forecasted Issue"
msgstr "Emisión pronosticada"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "From"
msgstr "Desde"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Fully Productive"
msgstr "Totalmente productivo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "General Information"
msgstr "Información general"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Generate Serial Numbers"
msgstr "Generar números de serie"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
msgid "Get statistics about the work orders duration related to this routing."
msgstr ""
"Obtenga estadísticas sobre la duración de las órdenes de trabajo "
"relacionadas a este enrutamiento."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__priority
msgid "Gives the sequence order when displaying a list of MRP documents."
msgstr ""
"Indica el orden de la secuencia cuando se muestra una lista de documentos "
"MRP."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__sequence
msgid "Gives the sequence order when displaying a list of bills of material."
msgstr ""
"Indica el orden de secuencia cuando se muestra una lista de listas de "
"materiales."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__sequence
msgid ""
"Gives the sequence order when displaying a list of routing Work Centers."
msgstr ""
"Indica el orden de secuencia cuando se muestra una lista de enrutamientos de"
" centros de producción."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__sequence
msgid "Gives the sequence order when displaying a list of work centers."
msgstr ""
"Indica el orden de secuencia al mostrar una lista de centros de trabajo."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__sequence
msgid "Gives the sequence order when displaying."
msgstr "Indica el orden de secuencia cuando se visualiza."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__google_slide
msgid "Google Slide"
msgstr "Presentaciones de Google"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Google Slide Link"
msgstr "Enlace de Presentación de Google"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Group By"
msgstr "Agrupar por"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Group By..."
msgstr "Agrupar por..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Group by..."
msgstr "Agrupar por..."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_produced
msgid "Has Been Produced"
msgstr "Se ha producido"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__has_kits
msgid "Has Kits"
msgstr "Tiene kits"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_production__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__2
msgid "High"
msgstr "Alta"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "History"
msgstr "Historial"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Hours"
msgstr "Horas"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_document__id
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__id
msgid "ID"
msgstr "ID"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_id
msgid ""
"If a product variant is defined the BOM is available only for this product."
msgstr ""
"Si se define una variante de producto, la LdM sólo estará disponible para "
"ese producto."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_unread
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_production__message_unread
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__propagate_cancel
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr ""
"Si se encuentra seleccionado, cuando el movimiento previo a este (generado "
"por un siguiente aprovisionamiento) se cancele o divida, el movimiento "
"generado por este también lo será"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__active
msgid ""
"If the active field is set to False, it will allow you to hide the bills of "
"material without removing it."
msgstr ""
"Si el campo activo se establece a False, permite ocultar las listas de "
"material sin eliminarlas."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Si el campo activo se establece como False, permite ocultar el registro del "
"recurso sin eliminarlo."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_height
msgid "Image Height"
msgstr "Altura de la imagen"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_src
msgid "Image Src"
msgstr "Fuente de la imagen"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_width
msgid "Image Width"
msgstr "Ancho de la imagen"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Image is a link"
msgstr "La imagen es un enlace"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_immediate_production
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__immediate_production_id
msgid "Immediate Production"
msgstr "Producción inmediata"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_immediate_production_line
msgid "Immediate Production Line"
msgstr "Línea de producción inmediata"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__immediate_production_line_ids
msgid "Immediate Production Lines"
msgstr "Líneas de producción inmediata"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Immediate Production?"
msgstr "¿Producción inmediata?"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Immediate production?"
msgstr "¿Producción inmediata?"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Impacted Transfer(s):"
msgstr "Traslados impactados:"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "Import Template for Bills of Materials"
msgstr "Importar plantilla para lista de materiales"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Impossible to plan the workorder. Please check the workcenter "
"availabilities."
msgstr ""
"No fue posible planear la orden de trabajo, verifique la disponibilidad del "
"centro de trabajo."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__progress
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__progress
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "In Progress"
msgstr "En progreso"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__index_content
msgid "Indexed Content"
msgstr "Contenido indexado"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_deadline
msgid ""
"Informative date allowing to define when the manufacturing order should be "
"processed at the latest to fulfill delivery on time."
msgstr ""
"Fecha informativa que permite definir cuándo se debe procesar la orden de "
"fabricación a más tardar para cumplir con la entrega a tiempo."

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_moves
msgid "Inventory Moves"
msgstr "Movimientos de inventario"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr ""
"Movimientos de inventario para los que debe escanear un número de lote en "
"esta orden de trabajo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__is_kits
#: model:ir.model.fields,field_description:mrp.field_product_template__is_kits
msgid "Is Kits"
msgstr "Es un kit"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_locked
msgid "Is Locked"
msgstr "Está bloqueado"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__manual
msgid "Is a Blocking Reason"
msgstr "Es un motivo de bloqueo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "Is a Blocking Reason?"
msgstr "¿Es un motivo de bloqueo?"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__public
msgid "Is public document"
msgstr "Es un documento público"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_user_working
msgid "Is the Current User Working"
msgstr "¿El usuario actual está trabajando?"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "It has already been unblocked."
msgstr "Ya ha sido desbloqueado."

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"It is not possible to unplan one single Work Order. You should unplan the "
"Manufacturing Order instead in order to unplan all the linked operations."
msgstr ""
"No es posible anular la planeación de una sola orden de trabajo. En su "
"lugar, debe anular la planeación de la orden de fabricación para anular la "
"todas las operaciones vinculadas."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_planned
msgid "Its Operations are Planned"
msgstr "Sus operaciones están planeadas"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__json_popover
msgid "JSON data for the popover widget"
msgstr "Datos JSON para el widget de ventana emergente"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__key
msgid "Key"
msgstr "Clave"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__description_bom_line
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__phantom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Kit"
msgstr "Kit"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_document____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder____last_update
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__last_working_user_id
msgid "Last user that worked on this work order."
msgstr "Último usuario que trabajó en esta orden de trabajo."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__late
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Late"
msgstr "Atrasado"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Activities"
msgstr "Actividades atrasadas"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late MO or Late delivery of components"
msgstr "Orden de fabricación atrasada o entrega atrasada de componentes"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__components_availability
msgid ""
"Latest component availability status for this MO. If green, then the MO's "
"readiness status is ready, as per BOM configuration."
msgstr ""
"Estado de disponibilidad de componente más reciente para esta orden de "
"fabricación. Si es verde, el estado de preparación está listo, según la "
"configuración de la LdM."

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_wood_ply
#: model_terms:product.template,description:mrp.product_product_wood_ply_product_template
msgid "Layers that are stick together to assemble wood panels."
msgstr "Capas que se unen para ensamblar paneles de madera."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__leave_id
msgid "Leave"
msgstr "Abandonar"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning_line
msgid "Line of issue consumption"
msgstr "Línea de problema de consumo"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Lines need to be deleted, but can not as you still have some quantities to "
"consume in them. "
msgstr ""
"Las líneas se deben eliminar, pero no puede, ya que todavía tiene algunas "
"cantidades por consumir en ellas."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__location_id
msgid "Location"
msgstr "Ubicación"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_id
msgid "Location where the product you want to unbuild is."
msgstr "Ubicación donde se encuentra el producto que desea desarmar."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_src_id
msgid "Location where the system will look for components."
msgstr "Ubicación donde el sistema buscará los componentes."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "Ubicación donde el sistema almacenará los productos finalizados."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_dest_id
msgid ""
"Location where you want to send the components resulting from the unbuild "
"order."
msgstr ""
"Ubicación donde desea enviar los componentes resultantes de la orden "
"desensamblada."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lock"
msgstr "Bloquear"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Lock the manufacturing order to prevent changes to what has been consumed or"
" produced."
msgstr ""
"Bloquee esta orden de fabricación para prevenir cambios en lo que se ha "
"consumido o producido."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_id
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Loss Reason"
msgstr "Motivo de pérdida"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Lote/serie "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__lot_producing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__finished_lot_id
msgid "Lot/Serial Number"
msgstr "Número de lote/serie"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__lot_id
msgid "Lot/Serial Number of the product to unbuild."
msgstr "Número de lote o serie del producto que se va a desmantelar."

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_traceability
msgid "Lots/Serial Numbers"
msgstr "Números de lote/serie"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__1
msgid "Low"
msgstr "Baja"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_backorder_id
msgid "MO Backorder"
msgstr "Orden de fabricación pendiente"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "MO Generated by %s"
msgstr "Orden de fabricación generada por %s"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reservation_state
msgid "MO Readiness"
msgstr "Preparación de la orden de fabricación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_routings
msgid "MRP Work Orders"
msgstr "Órdenes de trabajo de MRP"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss_type
msgid "MRP Workorder productivity losses"
msgstr "Pérdidas de productividad en la orden de trabajo de MRP"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_main_attachment_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_main_attachment_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_main_attachment_id
msgid "Main Attachment"
msgstr "Archivo adjunto principal"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "Fabricación sobre pedido"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Make sure enough quantities of these components are reserved to carry on "
"production:\n"
msgstr ""
"Asegúrese de que se reservan cantidades suficientes de estos componentes "
"para continuar con la producción:\n"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Makes confirmed manufacturing orders locked rather than unlocked by default."
" This only applies to new manufacturing orders, not previously created ones."
msgstr ""
"Hace que las órdenes de fabricación estén bloqueadas de forma "
"predeterminada. Esto se aplica solamente a órdenes de fabricación nuevas, no"
" a órdenes creadas previamente."

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_routings
msgid "Manage Work Order Operations"
msgstr "Gestionar operaciones de órdenes de trabajo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid "Manual Duration"
msgstr "Duración manual "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "Manuf. Lead Time"
msgstr "Plazo de fabricación"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_steps
#: model:ir.model.fields.selection,name:mrp.selection__stock_rule__action__manufacture
#: model:stock.location.route,name:mrp.route_warehouse0_manufacture
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_stock_rule
#, python-format
msgid "Manufacture"
msgstr "Fabricación"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__mrp_one_step
#, python-format
msgid "Manufacture (1 step)"
msgstr "Fabricación (1 paso)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_mto_pull_id
msgid "Manufacture MTO Rule"
msgstr "Regla de fabricación sobre pedido"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_pull_id
msgid "Manufacture Rule"
msgstr "Regla de fabricación"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "Manufacture Security Lead Time"
msgstr "Plazo de entrega de seguridad de fabricación"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__normal
msgid "Manufacture this product"
msgstr "Fabricar este producto"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_to_resupply
msgid "Manufacture to Resupply"
msgstr "Fabricación para reabastecimiento"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__mrp_product_qty
#: model:ir.model.fields,field_description:mrp.field_product_template__mrp_product_qty
msgid "Manufactured"
msgstr "Fabricado"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "Manufactured Products"
msgstr "Productos fabricados"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Manufactured in the last 365 days"
msgstr "Fabricado en los últimos 365 días"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__code__mrp_operation
#: model:ir.ui.menu,name:mrp.menu_mrp_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#, python-format
msgid "Manufacturing"
msgstr "Fabricación"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#: model:ir.model.fields,field_description:mrp.field_product_product__produce_delay
#: model:ir.model.fields,field_description:mrp.field_product_template__produce_delay
#: model:ir.model.fields,field_description:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__manufacturing_lead
#, python-format
msgid "Manufacturing Lead Time"
msgstr "Plazo de fabricación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manu_type_id
msgid "Manufacturing Operation Type"
msgstr "Tipo de operación de fabricación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__production_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Manufacturing Order"
msgstr "Orden de fabricación"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production_workcenter
#: model:ir.actions.act_window,name:mrp.action_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_production_action
#: model:ir.actions.act_window,name:mrp.mrp_production_action_picking_deshboard
#: model:ir.actions.act_window,name:mrp.mrp_production_report
#: model:ir.ui.menu,name:mrp.menu_mrp_production_action
#: model:ir.ui.menu,name:mrp.menu_mrp_production_report
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_production_calendar
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_production_pivot
msgid "Manufacturing Orders"
msgstr "Órdenes de producción"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are in confirmed state."
msgstr "Órdenes de fabricación que están en estado confirmado."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__ready_to_produce
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manufacturing Readiness"
msgstr "Preparación para la fabricación"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Manufacturing Reference"
msgstr "Referencia de fabricación"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc."
msgstr ""
"Las operaciones de fabricación se procesan en centros de trabajo. Un centro de trabajo se compone de\n"
"                trabajadores y/o maquinaria, se utilizan para calcular costos, programar actividades, planear capacidad, etc."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.\n"
"                They can be defined via the configuration menu."
msgstr ""
"Las operaciones de fabricación se procesan en centros de trabajo. Un centro de trabajo se puede componer de\n"
"                trabajadores y/o maquinaria, se utilizan para calcular costos, programar actividades, planear la capacidad, etc.\n"
"                Se pueden definir a través del menú de configuración."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reservation_state
msgid ""
"Manufacturing readiness for this MO, as per bill of material configuration:\n"
"            * Ready: The material is available to start the production.\n"
"            * Waiting: The material is not available to start the production.\n"
msgstr ""
"La preparación de fabricación para esta orden, según la configuración de la lista de materiales:\n"
"            * Listo: el material está disponible para empezar la producción.\n"
"            * En espera: el material no está disponible para empezar la producción.\n"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_mark_done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Mark as Done"
msgstr "Marcar como hecho"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Mass Produce"
msgstr "Producir en masa"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_mps
msgid "Master Production Schedule"
msgstr "Programa maestro de producción"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Material Availability"
msgstr "Disponibilidad de material"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error
msgid "Message Delivery error"
msgstr "Error al enviar el mensaje"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__mimetype
msgid "Mime Type"
msgstr "Tipo MIME"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Regla de inventario mínimo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Minutes"
msgstr "Minutos"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Miscellaneous"
msgstr "Varios"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__mo_ids
msgid "Mo"
msgstr "Orden de fabricación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_byproduct_ids
msgid "Move Byproduct"
msgstr "Mover subproducto"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "Movimientos por rastrear"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_consumption_warning_line_ids
msgid "Mrp Consumption Warning Line"
msgstr "Advertencia de línea de consumo de fabricación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_procurement_group__mrp_production_ids
msgid "Mrp Production"
msgstr "Producción de fabricación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_count
msgid "Mrp Production Count"
msgstr "Número de producción de fabricación"

#. module: mrp
#: model:ir.actions.server,name:mrp.production_order_server_action
msgid "Mrp: Plan Production Orders"
msgstr "MRP: planear las órdenes de producción"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__name
msgid "Name"
msgstr "Nombre"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Next Activity"
msgstr "Siguiente actividad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__next_work_order_id
msgid "Next Work Order"
msgstr "Siguiente orden de trabajo "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "No Backorder"
msgstr "Sin órdenes parciales"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid "No bill of materials found. Let's create one!"
msgstr "No se encontró ninguna lista de materiales. ¡Creemos una!"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "No data available."
msgstr "Datos no disponibles."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_report
msgid "No data yet!"
msgstr "¡No hay datos aún!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "No manufacturing order found. Let's create one."
msgstr "No se encontró ninguna orden de fabricación, creemos una."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid "No product found. Let's create one!"
msgstr "No se encontró ningún producto. ¡Creemos uno!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_blocked
msgid "No productivity loss for this equipment"
msgstr "No hay pérdida de productividad para este equipo"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid "No unbuild order found"
msgstr "No se encontró ninguna orden de desmantelamiento"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid "No work orders to do!"
msgstr "No hay órdenes de trabajo por realizar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid ""
"No workorder currently in progress. Click to mark work center as blocked."
msgstr ""
"No hay ninguna orden de trabajo en progreso actualmente. Haga clic para "
"marcar el centro de trabajo como bloqueado."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__normal
msgid "Normal"
msgstr "Normal"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Not Available"
msgstr "No disponible"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_scrap__workorder_id
msgid "Not to restrict or prefer quants, but informative."
msgstr "Informativo, mas no para restringir o preferir quants."

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid ""
"Note that archived work center(s): '%s' is/are still linked to active Bill "
"of Materials, which means that operations can still be planned on it/them. "
"To prevent this, deletion of the work center is recommended instead."
msgstr ""
"Tome en cuenta que los centros de trabajo archivados: \"%s\" aún están "
"vinculados a listas de materiales activas, lo que significa que aún se "
"pueden planear operaciones en ellos. Se recomienda eliminar los centros de "
"trabajo para prevenir esto."

#. module: mrp
#: code:addons/mrp/models/product.py:0 code:addons/mrp/models/product.py:0
#, python-format
msgid ""
"Note that product(s): '%s' is/are still linked to active Bill of Materials, "
"which means that the product can still be used on it/them."
msgstr ""
"Tome en cuenta que los productos: \"%s\" siguen vinculados a una lista de "
"materiales activa, lo que significa que el producto aún se puede utilizar en"
" ello. "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_late
msgid "Number of Manufacturing Orders Late"
msgstr "Número de órdenes de fabricación atrasadas"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_waiting
msgid "Number of Manufacturing Orders Waiting"
msgstr "Número de órdenes de fabricación en espera"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_todo
msgid "Number of Manufacturing Orders to Process"
msgstr "Número de órdenes de fabricación por procesar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_child_count
msgid "Number of generated MO"
msgstr "Número de órdenes de fabricación generadas"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__capacity
msgid ""
"Number of pieces (in product UoM) that can be produced in parallel  (at the "
"same time) at this work center. For example: the capacity is 5 and you need "
"to produce 10 units, then the operation time listed on the BOM will be "
"multiplied by two. However, note that both time before and after production "
"will only be counted once. "
msgstr ""
"El número de piezas (en la unidad de medida del producto) que se pueden "
"producir en paralelo (al mismo tiempo) en este centro de trabajo. Por "
"ejemplo: la capacidad es 5 y necesita producir 10 unidades, por lo que el "
"tiempo de operación que se indica en la LdM se multiplicará por 2. Sin "
"embargo, tome en cuenta que ambos tiempos, el anterior y el posterior a "
"producción, se contarán solo una vez."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_qty
msgid ""
"This should be the smallest quantity that this product can be produced in. "
"If the BOM contains operations, make sure the work center capacity is "
"accurate. "
msgstr ""
"Esta debería ser la cantidad más pequeña que se puede producir de este "
"producto. Si la LdM contiene operaciones, asegúrese de que la capacidad del "
"centro de trabajo sea precisa."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_source_count
msgid "Number of source MO"
msgstr "Número de órdenes de fabricación de origen"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_unread_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_unread_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes sin leer"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "OEE"
msgstr "OEE"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee_target
msgid "OEE Target"
msgstr "Objetivo de OEE"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee
msgid "Oee"
msgstr "OEE"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "On Hand"
msgstr "A la mano"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Operation"
msgstr "Operación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__operation_id
msgid "Operation To Consume"
msgstr "Operación por consumir"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__picking_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_type_id
msgid "Operation Type"
msgstr "Tipo de operación"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid ""
"Operation define that need to be done to realize a Work Order.\n"
"                Each operation is done at a specific Work Center and has a specific duration."
msgstr ""
"Las operaciones definen lo que se necesita hacer para realizar una orden de trabajo.\n"
"                Cada operación se realiza en un centro de trabajo en específico y tiene una duración específica."

#. module: mrp
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model:ir.actions.act_window,name:mrp.mrp_routing_action
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__allowed_operation_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_manufacturing
#: model:ir.ui.menu,name:mrp.menu_mrp_routing_action
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_calendar
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
#, python-format
msgid "Operations"
msgstr "Operaciones"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Done"
msgstr "Operaciones terminadas"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Planned"
msgstr "Operaciones planeadas"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Operations Search Filters"
msgstr "Filtros de búsqueda de operaciones"

#. module: mrp
#: model:ir.actions.report,name:mrp.label_production_order
msgid "Order Label"
msgstr "Etiqueta de la orden"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__orderpoint_id
msgid "Orderpoint"
msgstr "Punto de orden"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__order_ids
msgid "Orders"
msgstr "Órdenes"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "Archivo adjunto original (sin optimizar ni ajustar tamaño)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_production
msgid "Original Production Quantity"
msgstr "Cantidad original de producción"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee_target
msgid "Overall Effective Efficiency Target in percentage"
msgstr "Objetivo de eficiencia efectiva general en porcentaje"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_oee
#: model:ir.ui.menu,name:mrp.menu_mrp_workcenter_productivity_report
msgid "Overall Equipment Effectiveness"
msgstr "Eficiencia general del equipo"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee
msgid "Overall Equipment Effectiveness, based on the last month"
msgstr "Eficiencia general del equipo, basada en el último mes"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_oee
msgid "Overall Equipment Effectiveness: no working or blocked time"
msgstr "Eficiencia general del equipo: sin tiempo de trabajo o bloqueo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_id
msgid "Parent BoM"
msgstr "LdM principal"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__parent_product_tmpl_id
msgid "Parent Product Template"
msgstr "Plantilla de producto principal"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_consumption_warning_id
msgid "Parent Wizard"
msgstr "Asistente principal"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr ""
"Pegue la URL de su Presentación de Google. Asegúrese de que el acceso al "
"documento sea público."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Pause"
msgstr "Pausa"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Pending"
msgstr "Pendiente"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__performance
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__performance
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Performance"
msgstr "Rendimiento"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Performance Losses"
msgstr "Pérdidas de rendimiento"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__performance
msgid "Performance over the last month"
msgstr "Rendimiento en el último mes"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pick Components"
msgstr "Elegir componentes"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pick components and then manufacture"
msgstr "Elegir componentes y luego fabricar"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm
msgid "Pick components and then manufacture (2 steps)"
msgstr "Elegir componentes y luego fabricar (2 pasos)"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm_sam
#, python-format
msgid "Pick components, manufacture and then store products (3 steps)"
msgstr ""
"Elegir componentes, fabricar y luego almacenar los productos (3 pasos)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_mto_pull_id
msgid "Picking Before Manufacturing MTO Rule"
msgstr "Recolección antes de la regla de fabricación sobre pedido"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_type_id
msgid "Picking Before Manufacturing Operation Type"
msgstr "Recolección antes del tipo de operación de fabricación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_route_id
msgid "Picking Before Manufacturing Route"
msgstr "Recolección antes de la ruta de fabricación"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipo de recolección"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_ids
msgid "Picking associated to this manufacturing order"
msgstr "Recolección asociada a esta orden de fabricación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_loc_id
msgid "Picking before Manufacturing Location"
msgstr "Recolección antes de la ubicación de fabricación"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Plan"
msgstr "Plan"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Plan Orders"
msgstr "Planear órdenes"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Plan manufacturing or purchase orders based on forecasts"
msgstr "Planear órdenes de fabricación o de compra basadas en pronósticos"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planned"
msgstr "Planeado"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Planned Date"
msgstr "Fecha planeada"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Planned at the same time as other workorder(s) at %s"
msgstr "Planeado al mismo tiempo que otras órdenes de trabajo en %s"

#. module: mrp
#: model:ir.ui.menu,name:mrp.mrp_planning_menu_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Planning"
msgstr "Planeación"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planning Issues"
msgstr "Problemas de planeación"

#. module: mrp
#: model:product.product,name:mrp.product_product_plastic_laminate
#: model:product.template,name:mrp.product_product_plastic_laminate_product_template
msgid "Plastic Laminate"
msgstr "Plástico laminado"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_field_one2many_with_copy.js:0
#, python-format
msgid "Please click on the \"save\" button first"
msgstr "Primero haga clic en guardar"

#. module: mrp
#: model:product.product,name:mrp.product_product_wood_ply
#: model:product.template,name:mrp.product_product_wood_ply_product_template
msgid "Ply Layer"
msgstr "Capa"

#. module: mrp
#: model:product.product,name:mrp.product_product_ply_veneer
#: model:product.template,name:mrp.product_product_ply_veneer_product_template
msgid "Ply Veneer"
msgstr "Capa de chapa"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__json_popover
msgid "Popover Data JSON"
msgstr "Datos JSON de ventana emergente"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__possible_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__possible_bom_product_template_attribute_value_ids
msgid "Possible Product Template Attribute Value"
msgstr "Posible valor de atributo de plantilla de producto"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Post-Production"
msgstr "Posproducción"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pre-Production"
msgstr "Preproducción"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Print"
msgstr "Imprimir"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Print All Variants"
msgstr "Imprimir todas las variantes"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Print Unfolded"
msgstr "imprimir desplegados"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__priority
#: model:ir.model.fields,field_description:mrp.field_mrp_production__priority
msgid "Priority"
msgstr "Prioridad"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process operations at specific work centers"
msgstr "Procese operaciones en centros de trabajo específicos"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__produce_line_ids
msgid "Processed Disassembly Lines"
msgstr "Líneas de desmantelamiento procesadas"

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_group
#: model:ir.model.fields,field_description:mrp.field_mrp_production__procurement_group_id
msgid "Procurement Group"
msgstr "Grupo de aprovisionamiento"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_steps
msgid ""
"Produce : Move the components to the production location        directly and start the manufacturing process.\n"
"Pick / Produce : Unload        the components from the Stock to Input location first, and then        transfer it to the Production location."
msgstr ""
"Producir: mover los componentes a la ubicación de producción directamente para comenzar el proceso de fabricación.\n"
"Recolectar/producir: primero se deben descargar los componentes de las existencias a la ubicación de entrada para luego trasladarlos a la ubicación de producción."

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_byproducts
msgid "Produce residual products"
msgstr "Producir productos residuales"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Produce residual products (A + B -&gt; C + D)"
msgstr "Producir productos residuales (A + B -> C + D)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "Produced"
msgstr "Producido"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__produced_qty
msgid "Produced Quantity"
msgstr "Cantidad producida"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__serial_numbers
msgid "Produced Serial Numbers"
msgstr "Números de serie producidos"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__operation_id
msgid "Produced in Operation"
msgstr "Producido en la operación"

#. module: mrp
#: model:ir.model,name:mrp.model_product_product
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Product"
msgstr "Producto"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Product Attachments"
msgstr "Productos adjuntos"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Product Cost"
msgstr "Costo del producto"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_virtual_available
msgid "Product Forecasted Quantity"
msgstr "Cantidad pronosticada de producto"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr ""
"Gestión del ciclo de vida del producto (PLM, por sus siglas en inglés)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Product Moves"
msgstr "Movimientos de productos"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Movimientos de producto (línea de movimiento de existencias)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_qty_available
msgid "Product On Hand Quantity"
msgstr "Cantidad de productos a la mano"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Product Quantity"
msgstr "Cantidad de producto"

#. module: mrp
#: model:ir.model,name:mrp.model_product_template
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tmpl_id
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_id
msgid "Product Unit of Measure"
msgstr "Unidad de medida del producto"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_id
msgid "Product Variant"
msgstr "Variante de producto"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_product_variant_action
#: model:ir.ui.menu,name:mrp.product_variant_mrp
msgid "Product Variants"
msgstr "Variantes de producto"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__production_id
msgid "Production"
msgstr "Producción"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_date
msgid "Production Date"
msgstr "Fecha de producción"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_document
msgid "Production Document"
msgstr "Documento de producción"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Production Information"
msgstr "Información de producción"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_location_id
msgid "Production Location"
msgstr "Ubicación de producción"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_production_order
#: model:ir.model,name:mrp.model_mrp_production
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__production_id
msgid "Production Order"
msgstr "Orden de producción"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__raw_material_production_id
msgid "Production Order for components"
msgstr "Orden de producción para componentes"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__production_id
msgid "Production Order for finished products"
msgstr "Orden de producción de productos terminados"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_state
msgid "Production State"
msgstr "Estado de producción"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Production Workcenter"
msgstr "Centro de trabajo de producción"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Production of Draft MO"
msgstr "Producción de borrador de orden de fabricación"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Production started late"
msgstr "Producción iniciada con retraso"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__productive
msgid "Productive"
msgstr "Productivo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__productive_time
msgid "Productive Time"
msgstr "Tiempo productivo"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__productive_time
msgid "Productive hours over the last month"
msgstr "Horas productivas durante el último mes."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Productivity"
msgstr "Productividad"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_blocked
msgid "Productivity Losses"
msgstr "Pérdidas de productividad"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_template_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom
#: model:ir.ui.menu,name:mrp.menu_mrp_product_form
msgid "Products"
msgstr "Productos"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Products to Consume"
msgstr "Productos a consumir"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__progress
msgid "Progress Done (%)"
msgstr "Progreso realizado (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__propagate_cancel
msgid "Propagate cancel and split"
msgstr "Propagar cancelación y división"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__quality
msgid "Quality"
msgstr "Calidad"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Quality Losses"
msgstr "Pérdidas de calidad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "Hoja de trabajo de calidad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quant_ids
msgid "Quant"
msgstr "Quant"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_produced
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quantity
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Quantity"
msgstr "Cantidad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Quantity Produced"
msgstr "Cantidad producida"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_producing
msgid "Quantity Producing"
msgstr "Cantidad siendo producida"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_remaining
msgid "Quantity To Be Produced"
msgstr "Cantidad por producir"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__should_consume_qty
msgid "Quantity To Consume"
msgstr "Cantidad por consumir"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_qty
msgid "Quantity To Produce"
msgstr "Cantidad por producir"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Quantity:"
msgstr "Cantidad:"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_raw_ids
msgid "Raw Moves"
msgstr "Movimientos en bruto"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__assigned
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__ready
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Ready"
msgstr "Listo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_real_duration
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration
msgid "Real Duration"
msgstr "Duración real"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"Recursion error!  A product with a Bill of Material should not have itself "
"in its BoM or child BoMs!"
msgstr ""
"Ocurrió un error de recurrencia. Recuerde que un producto con una lista de "
"materiales no puede incluirse a si mismo en su LdM o en la sublista."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__code
#: model:ir.model.fields,field_description:mrp.field_mrp_production__name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__name
msgid "Reference"
msgstr "Referencia"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_name_uniq
msgid "Reference must be unique per Company!"
msgstr "La referencia debe ser única por empresa."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__origin
msgid ""
"Reference of the document that generated this production order request."
msgstr ""
"Referencia al documento que generó esta solicitud de orden de producción."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Reference:"
msgstr "Referencia:"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__ir_attachment_id
msgid "Related attachment"
msgstr "Archivo adjunto relacionado"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Replan"
msgstr "Replanear"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Report:"
msgstr "Reporte:"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_reporting
msgid "Reporting"
msgstr "Reportes"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Reserve"
msgstr "Reserva"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Reserved"
msgstr "Reservado"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_id
msgid "Resource"
msgstr "Recurso"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_field
msgid "Resource Field"
msgstr "Campo del recurso"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_id
msgid "Resource ID"
msgstr "ID del recurso"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_model
msgid "Resource Model"
msgstr "Modelo del recurso"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_name
msgid "Resource Name"
msgstr "Nombre del recurso"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__user_id
msgid "Responsible"
msgstr "Responsable"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__routing_line_ids
msgid "Routing Lines"
msgstr "Líneas de enrutamiento"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
msgid "Routing Work Centers"
msgstr "Enrutamiento de centros de producción"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_procurement_compute_mrp
msgid "Run Scheduler"
msgstr "Ejecutar planificador"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error en el envío del SMS"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Schedule manufacturing orders earlier to avoid delays"
msgstr "Planee antes las órdenes de fabricación para evitar atrasos"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_planned_start
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Scheduled Date"
msgstr "Fecha programada"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Scheduled Date by Month"
msgstr "Fecha programada por mes"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_planned_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_planned_finished
msgid "Scheduled End Date"
msgstr "Fecha de finalización programada"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_planned_start
msgid "Scheduled Start Date"
msgstr "Fecha de inicio programada"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"Scheduled before the previous work order, planned from %(start)s to %(end)s"
msgstr ""
"Programada antes de la orden de trabajo anterior, planeada desde %(start)s a"
" %(end)s"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_workorder_popover.js:0
#, python-format
msgid "Scheduling Information"
msgstr "Información de programación"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
#: model:ir.model,name:mrp.model_stock_scrap
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_scrap
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#, python-format
msgid "Scrap"
msgstr "Desechar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_count
msgid "Scrap Move"
msgstr "Desechar movimiento"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_ids
msgid "Scraps"
msgstr "Desechos"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_screw
#: model:product.template,name:mrp.product_product_computer_desk_screw_product_template
msgid "Screw"
msgstr "Tornillo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Search"
msgstr "Buscar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Search Bill Of Material"
msgstr "Buscar lista de materiales"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Search Production"
msgstr "Buscar producción"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Search Work Orders"
msgstr "Buscar órdenes de trabajo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Search for mrp workcenter"
msgstr "Buscar por centro de producción mrp"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Security Lead Time"
msgstr "Plazo de seguridad"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,help:mrp.field_res_config_settings__manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr "Días de seguridad para cada operación de fabricación."

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_field_one2many_with_copy.js:0
#, python-format
msgid "Select Operations to Copy"
msgstr "Seleccione operaciones para copiar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence picking before manufacturing"
msgstr "Secuencia de recolección antes de fabricación"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence production"
msgstr "Secuencia de producción"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence stock after manufacturing"
msgstr "Secuencia de existencias después de fabricación"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Serial Mass Produce"
msgstr "Producir serie en masa"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__manual
msgid "Set duration manually"
msgstr "Establecer la duración manualmente"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_configuration
#: model:ir.ui.menu,name:mrp.menu_mrp_config
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Settings"
msgstr "Ajustes"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_start
msgid "Setup Time"
msgstr "Configurar tiempo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__show_apply
msgid "Show Apply"
msgstr "Mostrar aplicar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__show_backorders
msgid "Show Backorders"
msgstr "Mostrar órdenes parciales"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__show_bom
msgid "Show BoM column"
msgstr "Mostrar columna de lista de materiales"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_final_lots
msgid "Show Final Lots"
msgstr "Mostrar lotes finales"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lock
msgid "Show Lock/unlock buttons"
msgstr "Mostrar los botones de bloquear y desbloquear"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__show_json_popover
msgid "Show Popover?"
msgstr "¿Mostrar ventana emergente?"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__show_productions
msgid "Show Productions"
msgstr "Mostrar producciones"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros cuya fecha de siguiente actividad es antes de "
"hoy"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__show_backorder_lines
msgid "Show backorder lines"
msgstr "Mostrar líneas de órdenes parciales"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__leave_id
msgid "Slot into workcenter calendar once planned"
msgstr "Espacio en el calendario del espacio de trabajo una vez planeado"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_head
#: model_terms:product.template,description:mrp.product_product_computer_desk_head_product_template
msgid "Solid wood is a durable natural material."
msgstr "La madera sólida es un material natural duradero."

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk
#: model_terms:product.template,description:mrp.product_product_computer_desk_product_template
msgid "Solid wood table."
msgstr "Mesa de madera sólida."

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ""
"Some of your byproducts are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct byproducts."
msgstr ""
"Algunos de sus subproductos están rastreados, debe especificar una orden de "
"fabricación para recuperar los subproductos correctos."

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ""
"Some of your components are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct components."
msgstr ""
"Algunos de sus componentes están rastreados, debe especificar una orden de "
"fabricación para recuperar los componentes correctos."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Some product moves have already been confirmed, this manufacturing order "
"can't be completely cancelled. Are you still sure you want to process ?"
msgstr ""
"Algunos movimientos de productos ya se confirmaron, esta orden de "
"fabricación no se puede cancelar por completo. ¿Todavía está seguro de que "
"desea procesar?"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Some work orders are already done, you cannot unplan this manufacturing "
"order."
msgstr ""
"Algunas órdenes de trabajo ya están hechas, no puede anular la planeación de"
" esta orden de fabricación."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Some work orders have already started, you cannot unplan this manufacturing "
"order."
msgstr ""
"Algunas órdenes de trabajo ya han comenzado, no puede anular la planeación "
"de esta orden de fabricación."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__origin
msgid "Source"
msgstr "Origen"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_id
msgid "Source Location"
msgstr "Ubicación de origen"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__costs_hour
msgid "Specify cost of work center per hour."
msgstr "Especifique el costo/hora del centro de trabajo."

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_screw
#: model_terms:product.template,description:mrp.product_product_computer_desk_screw_product_template
msgid "Stainless steel screw"
msgstr "Tornillo de acero inoxidable"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_bolt
#: model_terms:product.template,description:mrp.product_product_computer_desk_bolt_product_template
msgid "Stainless steel screw full (dia - 5mm, Length - 10mm)"
msgstr ""
"Tornillo de acero inoxidable completo (diámetro - 5mm, longitud - 10mm)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Starred"
msgstr "Destacado"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Start"
msgstr "Iniciar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_start
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__state
msgid "State"
msgstr "Estado"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__state
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Status"
msgstr "Estado"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado según las actividades\n"
"Vencida: ya pasó la fecha límite\n"
"Hoy: hoy es la fecha de la actividad\n"
"Planeada: futuras actividades."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_type_id
msgid "Stock After Manufacturing Operation Type"
msgstr "Tipo de existencias después de la operación de fabricación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_rule_id
msgid "Stock After Manufacturing Rule"
msgstr "Regla de existencias después de la producción"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_assign_serial
msgid "Stock Assign Serial Numbers"
msgstr "Asignar números de serie a existencias"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_availability
msgid "Stock Availability"
msgstr "Disponibilidad de existencias"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move
msgid "Stock Move"
msgstr "Movimiento de existencias"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_dest_ids
msgid "Stock Movements of Produced Goods"
msgstr "Movimientos de existencias de los productos producidos"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_unbuild_moves
msgid "Stock Moves"
msgstr "Movimientos de existencias"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "Reporte de recepción de existencias"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "Reporte de reabastecimiento de existencias"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_rule
msgid "Stock Rule"
msgstr "Regla de existencias"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_loc_id
msgid "Stock after Manufacturing Location"
msgstr "Ubicación de existencias después de la fabricación"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Reporte de regla de existencias"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Store Finished Product"
msgstr "Almacenar producto terminado"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__store_fname
msgid "Stored Filename"
msgstr "Nombre del archivo almacenado"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Structure & Cost"
msgstr "Estructura y costo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_bom_id
msgid "Sub BoM"
msgstr "Sub LdM"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Subcontract the production of some products"
msgstr "Subcontratar la producción de algunos productos."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_subcontracting
msgid "Subcontracting"
msgstr "Subcontratación"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk
#: model:product.template,name:mrp.product_product_computer_desk_product_template
msgid "Table"
msgstr "Mesa"

#. module: mrp
#: model:product.product,name:mrp.product_product_table_kit
#: model:product.template,name:mrp.product_product_table_kit_product_template
msgid "Table Kit"
msgstr "Kit de mesa"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_leg
#: model:product.template,name:mrp.product_product_computer_desk_leg_product_template
msgid "Table Leg"
msgstr "Pata de mesa"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_head
#: model:product.template,name:mrp.product_product_computer_desk_head_product_template
msgid "Table Top"
msgstr "Superficie de la mesa"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_table_kit
#: model_terms:product.template,description:mrp.product_product_table_kit_product_template
msgid "Table kit"
msgstr "Kit de mesa"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tag_ids
msgid "Tag"
msgstr "Etiqueta"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__name
msgid "Tag Name"
msgstr "Nombre de etiqueta"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__is_done
msgid "Technical Field to order moves"
msgstr "Campo técnico para movimientos de ordenes"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__is_user_working
msgid "Technical field indicating whether the current user is working. "
msgstr "Campo técnico que indica si el usuario actual está trabajando."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr "Campo técnico a seleccionar cuando podemos reservar cantidades"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr "Campo técnico para verificar cuándo podemos anular la reserva"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_assign_serial__show_apply
msgid "Technical field to show the Apply button"
msgstr "Campo técnico para mostrar el botón de aplicar"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_assign_serial__show_backorders
msgid "Technical field to show the Create Backorder and No Backorder buttons"
msgstr ""
"Campo técnico para mostrar los botones de Crear orden parcial y Sin orden "
"parcial"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__costs_hour
msgid ""
"Technical field to store the hourly cost of workcenter at time of work order"
" completion (i.e. to keep a consistent cost)."
msgstr ""
"Campo técnico para almacenar el costo por hora del centro de trabajo en el "
"momento de la finalización de la orden de trabajo (es decir, para mantener "
"un costo consistente)."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__key
msgid ""
"Technical field used to resolve multiple attachments in a multi-website "
"environment."
msgstr ""
"Campo técnico que se utiliza para resolver múltiples archivos adjuntos en un"
" ambiente de múltiples sitios web"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_availability
msgid "Technical: used in views and domains only."
msgstr "Técnico: se utiliza en vistas y dominios solamente."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__working_state
msgid "Technical: used in views only"
msgstr "Técnico: se utiliza solo en vistas"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_state
msgid "Technical: used in views only."
msgstr "Técnico: se utiliza solo en vistas"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__text
msgid "Text"
msgstr "Texto"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,help:mrp.field_mrp_workorder__operation_note
msgid "Text worksheet description"
msgstr "Texto de descripción en la hoja de trabajo"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_id
msgid "The Bill of Material this operation is linked to"
msgstr "La lista de materiales vinculada a esta operación"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0 code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The Product Unit of Measure you chose has a different category than in the "
"product form."
msgstr ""
"La unidad de medida del producto que eligió tiene una categoría diferente "
"que la del formulario de producto."

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "The Workorder (%s) cannot be started twice!"
msgstr "¡La orden de trabajo (%s) no se puede iniciar dos veces!"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The attribute value %(attribute)s set on product %(product)s does not match "
"the BoM product %(bom_product)s."
msgstr ""
"El valor del atributo %(attribute)s establecido en el producto %(product)s "
"no coincide con el producto en la lista de materiales %(bom_product)s."

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "The capacity must be strictly positive."
msgstr "La capacidad debe ser estrictamente positiva."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "The component %s should not be the same as the product to produce."
msgstr "El componente %s no debe ser el mismo que el del producto a fabricar."

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The current configuration is incorrect because it would create a cycle "
"between these products: %s."
msgstr ""
"La configuración actual no es correcta, crearía un ciclo entre estos "
"productos: %s."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__res_model
msgid "The database object this attachment will be attached to."
msgstr "Objeto de la base de datos a la que se agregará este archivo adjunto."

#. module: mrp
#: code:addons/mrp/models/stock_orderpoint.py:0
#, python-format
msgid "The following replenishment order has been generated"
msgstr "Se generó la siguiente orden de reabastecimiento"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_produced
msgid "The number of products already handled by this work order"
msgstr "El número de productos ya manejados en esta orden de trabajo"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__operation_id
msgid ""
"The operation where the components are consumed, or the finished products "
"created."
msgstr ""
"La operación en la que se consumen los componentes o los productos "
"terminados creados."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__cost_share
msgid ""
"The percentage of the final production cost for this by-product line "
"(divided between the quantity produced).The total of all by-products' cost "
"share must be less than or equal to 100."
msgstr ""
"El porcentaje del costo de producción final para esta línea de subproducto "
"(dividido entre la cantidad producida). El total de reparto de costos de "
"subproductos debe ser menor o igual a 100."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__cost_share
msgid ""
"The percentage of the final production cost for this by-product. The total "
"of all by-products' cost share must be smaller or equal to 100."
msgstr ""
"El porcentaje del costo de producción final para este subproducto. El total "
"de reparto de costos de subproductos debe ser menor o igual a 100."

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"The planned end date of the work order cannot be prior to the planned start "
"date, please correct this to save the work order."
msgstr ""
"La fecha de finalización planeada de la orden de trabajo no puede ser "
"anterior a la fecha de inicio planeada, corríjala para guardar la orden de "
"trabajo."

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The product has already been used at least once, editing its structure may "
"lead to undesirable behaviours. You should rather archive the product and "
"create a new one with a new bill of materials."
msgstr ""
"El producto ya se usó al menos una vez, editar su estructura puede llevar a "
"comportamientos no deseados. Archive el producto y cree uno nuevo con una "
"nueva lista de materiales."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "The quantity produced of by-products must be positive."
msgstr "La cantidad producida de subproductos debe ser positiva."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_qty_positive
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_qty_positive
#, python-format
msgid "The quantity to produce must be positive!"
msgstr "¡La cantidad a producir debe ser positiva!"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__res_id
msgid "The record id this is attached to."
msgstr "El ID de registro a lo que se adjuntó esto."

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ""
"The selected serial number does not correspond to the one used in the "
"manufacturing order, please select another one."
msgstr ""
"El número de serie seleccionado no corresponde con el que se utilizó en la "
"orden de fabricación, seleccione otro."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The serial number %(number)s used for byproduct %(product_name)s has already"
" been produced"
msgstr ""
"Ya se procesó el número de serie %(number)s utilizado para el subproducto "
"%(product_name)s"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The serial number %(number)s used for component %(component)s has already "
"been consumed"
msgstr ""
"Ya se consumió el número de serie %(number)s utilizado para el componente "
"%(component)s"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_tag_tag_name_unique
msgid "The tag name must be unique."
msgstr "El nombre de etiqueta debe ser único."

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "The total cost share for a BoM's by-products cannot exceed 100."
msgstr ""
"El reparto de costos total para los subproductos de una lista de materiales "
"no puede ser mayor a 100."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The total cost share for a manufacturing order's by-products cannot exceed "
"100."
msgstr ""
"El reparto de costos total para los subproductos de una orden de fabricación"
" no puede ser mayor a 100."

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "The work order should have already been processed."
msgstr "Esta orden de trabajo ya debería haber sido procesada."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__theme_template_id
msgid "Theme Template"
msgstr "Plantilla del tema"

#. module: mrp
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "There are more Serial Numbers than the Quantity to Produce"
msgstr "Hay más números de serie que cantidad por producir"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"There are no components to consume. Are you still sure you want to continue?"
msgstr ""
"No hay componentes para consumir. ¿Está seguro de que desea continuar?"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid "There's no product move yet"
msgstr "No hay movimiento de producto aún"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Este campo se utiliza para definir en qué zona horaria funcionarán los "
"recursos."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"Este campo se utiliza para calcular la duración esperada de una orden de "
"trabajo en este centro de trabajo. Por ejemplo, si una orden de trabajo "
"tarda una hora y su factor de eficiencia es del 100%, entonces la duración "
"esperada será de una hora. Sin embargo, si el factor de eficiencia es del "
"200%, la duración esperada será de 30 minutos."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
msgid "This is a BoM of type Kit!"
msgstr "¡Esta es una LdM de tipo kit!"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid ""
"This is the cost based on the BoM of the product. It is computed by summing "
"the costs of the components and operations needed to build the product."
msgstr ""
"Este es el costo basado en la lista de materiales del producto. Se calcula "
"mediante la suma de los costos de los componentes y operaciones necesarios "
"para construir el producto."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "This is the cost defined on the product."
msgstr "Este es el costo definido en el producto."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                You can filter on the product to see all the past movements for the product."
msgstr ""
"Este menú le da trazabilidad total a las operaciones de inventario en productos específicos.\n"
"                Puede filtrar en el producto para ver todos los movimientos pasados para el producto."

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "This production order has been created from Replenishment Report."
msgstr ""
"Esta orden de producción se creó desde un reporte de reabastecimiento."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "This serial number for product %s has already been produced"
msgstr "Ya se produjo este número de serie para el producto %s"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__time_ids
msgid "Time"
msgstr "Tiempo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_efficiency
msgid "Time Efficiency"
msgstr "Eficacia de tiempo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_ids
msgid "Time Logs"
msgstr "Registros de tiempo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Time Tracking"
msgstr "Seguimiento de tiempo"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Time Tracking: %(user)s"
msgstr "Registro de tiempo: %(user)s"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_stop
msgid "Time in minutes for the cleaning."
msgstr "Tiempo en minutos para la limpieza."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_start
msgid "Time in minutes for the setup."
msgstr "Tiempo en minutos para la configuración."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid ""
"Time in minutes:- In manual mode, time used- In automatic mode, supposed "
"first time when there aren't any work orders yet"
msgstr ""
"Tiempo en minutos:- En el modo manual, tiempo utilizado- en el modo "
"automático, primer tiempo supuesto cuando no hay órdenes de trabajo aún"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tz
msgid "Timezone"
msgstr "Zona horaria"

#. module: mrp
#: model:digest.tip,name:mrp.digest_tip_mrp_0
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid "Tip: Use tablets in the shop to control manufacturing"
msgstr "Consejo: utilice tablets en la tienda para controlar la fabricación"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "To"
msgstr "Para"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__to_backorder
msgid "To Backorder"
msgstr "Para hacer una orden parcial"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__to_close
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Close"
msgstr "Por cerrar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_expected_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "To Consume"
msgstr "Por consumir"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Do"
msgstr "Por hacer"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "To Launch"
msgstr "Por lanzar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__to_immediate
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "To Process"
msgstr "Por procesar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "To Produce"
msgstr "Por producir"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_wood_wear
#: model_terms:product.template,description:mrp.product_product_wood_wear_product_template
msgid "Top layer of a wood panel."
msgstr "Capa superior de un panel de madera."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Total Duration"
msgstr "Duración total"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_late_count
msgid "Total Late Orders"
msgstr "Total de órdenes atrasadas"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_pending_count
msgid "Total Pending Orders"
msgstr "Total de órdenes pendientes"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Qty"
msgstr "Cant. total"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_qty
msgid "Total Quantity"
msgstr "Cantidad total"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_progress_count
msgid "Total Running Orders"
msgstr "Total de órdenes en ejecución"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Total To Consume"
msgstr "Total por consumir"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Total duration"
msgstr "Duración total"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total expected duration"
msgstr "Duración total esperada"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_duration_expected
msgid "Total expected duration (in minutes)"
msgstr "Duración total esperada (en minutos)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total real duration"
msgstr "Duración total real"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_real_duration
msgid "Total real duration (in minutes)"
msgstr "Duración total real (en minutos)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Traceability"
msgstr "Trazabilidad"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_traceability_report
msgid "Traceability Report"
msgstr "Reporte de trazabilidad"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_tracking
msgid "Tracking"
msgstr "Seguimiento"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking
msgid "Transfer"
msgstr "Traslado"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Transfers"
msgstr "Traslados"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__type
msgid "Type"
msgstr "Tipo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "Tipo de operación"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Unable to split with more than the quantity to produce."
msgstr "No se puede dividir con más de la cantidad para producir."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Unblock"
msgstr "Desbloquear"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__unbuild_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild"
msgstr "Desmantelar"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild Order"
msgstr "Orden de desmantelamiento"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "Unbuild Order product quantity has to be strictly positive."
msgstr ""
"La cantidad de producto de la orden de desmantelamiento debe ser "
"estrictamente positiva."

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_unbuild
#: model:ir.ui.menu,name:mrp.menu_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild Orders"
msgstr "Órdenes de desmantelamiento"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Unbuild: %s"
msgstr "Desmantelar: %s"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
msgid "Unfold"
msgstr "Desplegar"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unit_factor
msgid "Unit Factor"
msgstr "Factor unitario"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_uom_name
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Unit of Measure"
msgstr "Unidad de medida"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_id
msgid ""
"Unit of Measure (Unit of Measure) is the unit of measurement for the "
"inventory control"
msgstr ""
"La unidad de medida (unidad de medida) es la unidad de medición para el "
"control del inventario"

#. module: mrp
#: model:product.product,uom_name:mrp.product_product_computer_desk
#: model:product.product,uom_name:mrp.product_product_computer_desk_bolt
#: model:product.product,uom_name:mrp.product_product_computer_desk_head
#: model:product.product,uom_name:mrp.product_product_computer_desk_leg
#: model:product.product,uom_name:mrp.product_product_computer_desk_screw
#: model:product.product,uom_name:mrp.product_product_drawer_case
#: model:product.product,uom_name:mrp.product_product_drawer_drawer
#: model:product.product,uom_name:mrp.product_product_plastic_laminate
#: model:product.product,uom_name:mrp.product_product_ply_veneer
#: model:product.product,uom_name:mrp.product_product_table_kit
#: model:product.product,uom_name:mrp.product_product_wood_panel
#: model:product.product,uom_name:mrp.product_product_wood_ply
#: model:product.product,uom_name:mrp.product_product_wood_wear
#: model:product.template,uom_name:mrp.product_product_computer_desk_bolt_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_head_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_leg_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_screw_product_template
#: model:product.template,uom_name:mrp.product_product_drawer_case_product_template
#: model:product.template,uom_name:mrp.product_product_drawer_drawer_product_template
#: model:product.template,uom_name:mrp.product_product_plastic_laminate_product_template
#: model:product.template,uom_name:mrp.product_product_ply_veneer_product_template
#: model:product.template,uom_name:mrp.product_product_table_kit_product_template
#: model:product.template,uom_name:mrp.product_product_wood_panel_product_template
#: model:product.template,uom_name:mrp.product_product_wood_ply_product_template
#: model:product.template,uom_name:mrp.product_product_wood_wear_product_template
msgid "Units"
msgstr "Unidades"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unlock"
msgstr "Desbloquear"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_unlocked_by_default
msgid "Unlock Manufacturing Orders"
msgstr "Desbloquear órdenes de fabricación"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Unlock the manufacturing order to adjust what has been consumed or produced."
msgstr ""
"Desbloquee la orden de fabricación para ajustar lo que se ha consumido o "
"producido."

#. module: mrp
#: model:res.groups,name:mrp.group_unlocked_by_default
msgid "Unlocked by default"
msgstr "Desbloqueado de forma predeterminada"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unplan"
msgstr "Anular planeación"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_unread
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_unread
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_unread_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_unread_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Número de mensajes sin leer"

#. module: mrp
#: model:ir.actions.server,name:mrp.mrp_production_action_unreserve_tree
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Unreserve"
msgstr "Anular reserva"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "UoM"
msgstr "UdM"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp_document_template.xml:0
#, python-format
msgid "Upload"
msgstr "Subir"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Upload your PDF file."
msgstr "Suba su archivo PDF."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__1
msgid "Urgent"
msgstr "Urgente"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__url
msgid "Url"
msgstr "URL"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Used In"
msgstr "Utilizado en"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__user_id
#: model:res.groups,name:mrp.group_mrp_user
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "User"
msgstr "Usuario"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Using a MPS report to schedule your reordering and manufacturing operations "
"is useful if you have long lead time and if you produce based on sales "
"forecasts."
msgstr ""
"Usar un reporte de plan maestro de producción es útil para planear sus "
"operaciones de reordenamiento y fabricación si el plazo de entrega es largo "
"y si produce según el pronóstico de ventas."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Validate"
msgstr "Validar"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Variant:"
msgstr "Variante:"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__3
msgid "Very High"
msgstr "Muy alta"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Waiting"
msgstr "En espera"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__waiting
msgid "Waiting Another Operation"
msgstr "En espera de otra operación"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Waiting Availability"
msgstr "En espera de disponibilidad"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp.js:0
#, python-format
msgid "Waiting Materials"
msgstr "En espera de materiales "

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__pending
msgid "Waiting for another WO"
msgstr "En espera de otra orden de trabajo"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__waiting
msgid "Waiting for components"
msgstr "En espera de componentes"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Waiting the previous work order, planned from %(start)s to %(end)s"
msgstr ""
"En espera de la orden de trabajo anterior, planeada de %(start)s a %(end)s"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse
msgid "Warehouse"
msgstr "Almacén"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warn_insufficient_qty_unbuild
msgid "Warn Insufficient Unbuild Quantity"
msgstr "Advertir cantidad insuficiente de desmantelamiento"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0 code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/stock_scrap.py:0
#, python-format
msgid "Warning"
msgstr "Advertencia"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Warnings"
msgstr "Advertencias"

#. module: mrp
#: model:product.product,name:mrp.product_product_wood_wear
#: model:product.template,name:mrp.product_product_wood_wear_product_template
msgid "Wear Layer"
msgstr "Capa protectora"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__website_id
msgid "Website"
msgstr "Sitio web"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__website_url
msgid "Website URL"
msgstr "URL del sitio web"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__picking_type_id
msgid ""
"When a procurement has a ‘produce’ route with a operation type set, it will "
"try to create a Manufacturing Order for that product using a BoM of the same"
" operation type. That allows to define stock rules which trigger different "
"manufacturing orders with different BoMs."
msgstr ""
"Cuando un aprovisionamiento tiene una ruta de \"producción\" con un tipo de "
"operación establecido, intentará crear una orden de fabricación para ese "
"producto utilizando una LdM del mismo tipo de operación. Eso permite definir"
" reglas de almacén que activan diferentes órdenes de fabricación con "
"diferentes LdM."

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__asap
msgid "When components for 1st operation are available"
msgstr "Cuando los componentes para la primera operación están disponibles"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_to_resupply
msgid ""
"When products are manufactured, they can be manufactured in this warehouse."
msgstr ""
"Cuando se fabrican los productos, pueden ser fabricados en este almacén."

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> a manufacturing order is "
"created to fulfill the need."
msgstr ""
"Cuando los productos son necesarios en <b>%s</b>, <br/> se genera una orden "
"de fabricación para cumplir con la necesidad."

#. module: mrp
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid ""
"With the Odoo work center control panel, your worker can start work orders "
"in the shop and follow instructions of the worksheet. Quality tests are "
"perfectly integrated into the process. Workers can trigger feedback loops, "
"maintenance alerts, scrap products, etc."
msgstr ""
"Con el panel de control del centro de trabajo de Odoo, su trabajador puede "
"iniciar órdenes de trabajo en la tienda y seguir instrucciones en las hojas "
"de trabajo. Las pruebas de calidad están perfectamente integradas en el "
"proceso. Los trabajadores pueden activar circuitos de retroalimentación, "
"alertas de mantenimiento, descartar productos, etc."

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""
"Asistente en caso de advertencia de que se ha utilizado más del componente "
"en una orden de fabricación (relacionado a la lista de materiales)"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder
msgid "Wizard to mark as done or create back order"
msgstr "Asistente para marcar como hecho o crear una orden parcial"

#. module: mrp
#: model:product.product,name:mrp.product_product_wood_panel
#: model:product.template,name:mrp.product_product_wood_panel_product_template
msgid "Wood Panel"
msgstr "Panel de madera"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Work Center"
msgstr "Centro de trabajo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workcenter_load
msgid "Work Center Load"
msgstr "Carga de centro de trabajo"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Work Center Loads"
msgstr "Cargas de centro de trabajo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Work Center Name"
msgstr "Nombre del centro de trabajo"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Uso del centro de producción"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
msgid "Work Center load"
msgstr "Carga de centro de trabajo"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_action
#: model:ir.ui.menu,name:mrp.menu_view_resource_search_mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Centers"
msgstr "Centros de trabajo"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_kanban_action
msgid "Work Centers Overview"
msgstr "Información general de los centros de trabajo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Work Instruction"
msgstr "Instrucciones de trabajo"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workorder_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__name
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__workorder_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__workorder_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Work Order"
msgstr "Orden de trabajo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Work Order Operations allow you to create and manage the manufacturing "
"operations that should be followed within your work centers in order to "
"produce a product. They are attached to bills of materials that will define "
"the required components."
msgstr ""
"Las operaciones de órdenes de trabajo le permiten crear y administrar las "
"operaciones de fabricación que deben seguirse dentro de sus centros de "
"trabajo para producir un producto. Se adjuntan a las listas de materiales "
"que definirán los componentes requeridos."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__workorder_id
msgid "Work Order To Consume"
msgstr "Orden de trabajo por consumir"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_routing_time
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production_specific
#: model:ir.actions.act_window,name:mrp.action_work_orders
#: model:ir.actions.act_window,name:mrp.mrp_workorder_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_workorder_report
#: model:ir.actions.act_window,name:mrp.mrp_workorder_todo
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_workorder
#: model:ir.ui.menu,name:mrp.menu_mrp_work_order_report
#: model:ir.ui.menu,name:mrp.menu_mrp_workorder_todo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Orders"
msgstr "Órdenes de trabajo"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workorder_workcenter_report
msgid "Work Orders Performance"
msgstr "Rendimiento de órdenes de trabajo"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_workcenter
msgid "Work Orders Planning"
msgstr "Planeación de órdenes de trabajo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_type
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Work Sheet"
msgstr "Hoja de trabajo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Work center"
msgstr "Centro de trabajo"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"                    Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"Las órdenes de trabajo son operaciones que se hacen como parte de una orden de fabricación.\n"
"                    Las operaciones se definen en la lista de materiales o se agregan directamente en la orden de fabricación."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"Las órdenes de trabajo son operaciones que se hacen como parte de una orden de fabricación.\n"
"            Las operaciones se definen en la lista de materiales o se agregan directamente en la orden de fabricación."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Work orders in progress. Click to block work center."
msgstr ""
"Órdenes de trabajo en curso. Haga clic para bloquear el centro de trabajo."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Workcenter"
msgstr "Centro de trabajo"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "Workcenter %s cannot be an alternative of itself."
msgstr "El centro de trabajo %s no puede ser una alternativa de sí mismo."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Workcenter Productivity"
msgstr "Productividad del centro de trabajo"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "Registro de productividad del centro de trabajo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Workcenter Productivity Loss"
msgstr "Pérdida de productividad del centro de trabajo"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss
msgid "Workcenter Productivity Losses"
msgstr "Pérdidas de productividad en el centro de trabajo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__working_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_state
msgid "Workcenter Status"
msgstr "Estado del centro de trabajo"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Workcenter blocked, click to unblock."
msgstr "Centro de trabajo bloqueado, haga clic para desbloquear."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Working Hours"
msgstr "Horas laborables"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_user_ids
msgid "Working user on this work order."
msgstr "Usuario que trabaja en esta orden de trabajo."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet
msgid "Worksheet"
msgstr "Hoja de trabajo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_type
msgid "Worksheet Type"
msgstr "Tipo de hoja de trabajo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_google_slide
msgid "Worksheet URL"
msgstr "URL de hoja de trabajo"

#. module: mrp
#: code:addons/mrp/models/stock_production_lot.py:0
#, python-format
msgid ""
"You are not allowed to create or edit a lot or serial number for the "
"components with the operation type \"Manufacturing\". To change this, go on "
"the operation type and tick the box \"Create New Lots/Serial Numbers for "
"Components\"."
msgstr ""
"No tiene permitido crear o editar un número de lote o de serie para los "
"componentes con el tipo de operación \"fabricación\". Para cambiar esto, "
"vaya al tipo de operación y seleccione la casilla \"crear nuevos números de "
"lotes o de serie para componentes\"."

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Puede tanto subir un archivo de su computadora como copiar y pegar un enlace"
" de internet a su archivo."

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You can not create a kit-type bill of materials for products that have at "
"least one reordering rule."
msgstr ""
"No puede crear una lista de materiales de tipo kit para productos que tienen"
" por lo menos una regla de reordenamiento."

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You can not delete a Bill of Material with running manufacturing orders.\n"
"Please close or cancel it first."
msgstr ""
"No puede eliminar una lista de materiales con órdenes de fabricación en ejecución.\n"
"Por favor, cierre o cancélela primero."

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You cannot change the workcenter of a work order that is in progress or "
"done."
msgstr ""
"No puede cambiar el centro de trabajo de una orden de trabajo que está en "
"progreso o terminada."

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "You cannot create a new Bill of Material from here."
msgstr "No puede crear una nueva lista de materiales desde aquí."

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You cannot delete an unbuild order if the state is 'Done'."
msgstr ""
"No puede eliminar una orden de desmantelamiento si el estado es 'hecho'."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "You cannot have %s  as the finished product and in the Byproducts"
msgstr "No puede tener %s  como un producto terminado y como un subproducto"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You cannot link this work order to another manufacturing order."
msgstr "No puede vincular esta orden de trabajo a otra orden de fabricación."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "You cannot move a manufacturing order once it is cancelled or done."
msgstr ""
"No puede mover una orden de fabricación una vez que se cancela o termina."

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You cannot produce the same serial number twice."
msgstr "No puede producir el mismo número de serie dos veces."

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You cannot unbuild a undone manufacturing order."
msgstr "No puede desmantelar una orden de fabricación deshecha."

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You cannot use the 'Apply on Variant' functionality and simultaneously "
"create a BoM for a specific variant."
msgstr ""
"No puede utilizar la funcionalidad de \"aplicar en la variante\" y de manera"
" simultánea crear una lista de materiales para una variante específica."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid ""
"You consumed a different quantity than expected for the following products.\n"
"                        <b attrs=\"{'invisible': [('consumption', '=', 'strict')]}\">\n"
"                            Please confirm it has been done on purpose.\n"
"                        </b>\n"
"                        <b attrs=\"{'invisible': [('consumption', '!=', 'strict')]}\">\n"
"                            Please review your component consumption or ask a manager to validate \n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '!=', 1)]}\">this manufacturing order</span>\n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '=', 1)]}\">these manufacturing orders</span>.\n"
"                        </b>"
msgstr ""
"Consumió una cantidad diferente a la esperada para los siguientes productos.\n"
"                        <b attrs=\"{'invisible': [('consumption', '=', 'strict')]}\">\n"
"                            Por favor, confirme que lo hizo a propósito.\n"
"                        </b>\n"
"                        <b attrs=\"{'invisible': [('consumption', '!=', 'strict')]}\">\n"
"                            Por favor, revise su consumo de componentes o solicite que un gerente valide \n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '!=', 1)]}\">esta orden de facturación</span>\n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '=', 1)]}\">estas órdenes de facturación</span>.\n"
"                        </b>"

#. module: mrp
#: code:addons/mrp/wizard/change_production_qty.py:0
#, python-format
msgid ""
"You have already processed %(quantity)s. Please input a quantity higher than"
" %(minimum)s "
msgstr "Ya procesó %(quantity)s. Ingrese una cantidad mayor a %(minimum)s "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid ""
"You have entered less serial numbers than the quantity to produce.<br/>\n"
"                        Create a backorder if you expect to process the remaining quantities later.<br/>\n"
"                        Do not create a backorder if you will not process the remaining products."
msgstr ""
"Ingresó menos números de serie que la cantidad por producir.<br/>\n"
"                        Cree una orden parcial si espera procesar las cantidades restantes más tarde.<br/>\n"
"                        No cree una orden parcial si no procesará los productos restantes."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid ""
"You have not recorded <i>produced</i> quantities yet, by clicking on "
"<i>apply</i> Odoo will produce all the finished products and consume all "
"components."
msgstr ""
"No tienen ninguna cantidad <i>producida</i> registrada aún. Al hacer clic en"
" <i>aplicar</i> Odoo producirá todos los productos terminados y consumirá "
"todos los componentes."

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"You must indicate a non-zero amount consumed for at least one of your "
"components"
msgstr ""
"Debe indicar una cantidad consumida diferente de cero para al menos uno de "
"sus componentes"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You need to define at least one productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"Debe definir al menos una pérdida de productividad en la categoría "
"\"rendimiento\". Cree una en Fabricación/Configuración/Pérdidas de "
"productividad."

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You need to define at least one productivity loss in the category "
"'Productivity'. Create one from the Manufacturing app, menu: Configuration /"
" Productivity Losses."
msgstr ""
"Debe definir al menos una pérdida de productividad en la categoría "
"\"rendimiento\". Cree una en Fabricación/Configuración/Pérdidas de "
"productividad."

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You need to define at least one unactive productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"Debe definir al menos una pérdida de productividad por inactividad en la "
"categoría \"rendimiento\". Cree una en Fabricación/Configuración/Pérdidas de"
" productividad."

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You need to provide a lot for the finished product."
msgstr "Debe proporcionar un lote para el producto terminado."

#. module: mrp
#: code:addons/mrp/wizard/mrp_immediate_production.py:0
#, python-format
msgid "You need to supply Lot/Serial Number for products:"
msgstr ""
"Necesita proporcionar un número de serie o de lote para los productos:"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_backorder
msgid "You produced less than initial demand"
msgstr "Produjo menos que la demanda inicial"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You should provide a lot number for the final product."
msgstr "Debe proporcionar un número de lote para el producto final."

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "and build finished products using"
msgstr "y construir productos terminados utilizando"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "bills of materials"
msgstr "lista de materiales"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "cancelled"
msgstr "cancelado"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "components"
msgstr "componentes"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "copy paste a list and/or use Generate"
msgstr "copie y pegue una lista y/o utilice Generar"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "days"
msgstr "días"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "expected duration"
msgstr "duración esperada"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "from location"
msgstr "desde la ubicación"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "last"
msgstr "último"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "manufacturing order"
msgstr "orden de fabricación"

#. module: mrp
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#, python-format
msgid "minutes"
msgstr "minutos"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "of"
msgstr "de"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "on"
msgstr "en"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "ordered instead of"
msgstr "ordenado en lugar de"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "quantity has been updated."
msgstr "se actualizó la cantidad."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "real duration"
msgstr "duración real"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_documents_controller_mixin.js:0
#, python-format
msgid "status code: %s, message: %s"
msgstr "código de estado: %s, mensaje: %s"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "work orders"
msgstr "órdenes de trabajo"
