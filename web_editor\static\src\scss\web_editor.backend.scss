.oe_form_field_html {
    position: relative;
    word-wrap: break-word;
    overflow: hidden;

    #codeview-btn-group {
        position: absolute;
        top: 0;
        right: 0;
    }

    .note-editable {
        font: inherit !important;
        font-family: inherit !important;
        line-height: inherit !important;
        color: inherit !important;
    }

    > iframe {
        display: block;
        width: 100%;
        margin: 0;
        padding: 0;
        ul > li > p {
            margin: 0px;
        }
        min-height: 300px;
        min-height: -webkit-calc(100vh - 170px);
        min-height: calc(100vh - 170px);

        &.o_readonly {
            border: none;
        }
    }
    .rounded {
        border-radius: .25rem !important;
    }
    table.table.table-bordered {
        table-layout: fixed;
    }
    a:not(.btn) {
        // Ensure non-button links are visible enough in the editor.
        color: #008f8c;
    }
}

.o_field_widgetTextHtml_fullscreen {
    .oe_form_field_html.o_form_fullscreen_ancestor iframe {
        position: absolute !important;
        left: 0 !important;
        right: 0 !important;
        top: 0 !important;
        bottom: 0 !important;
        width: 100% !important;
        min-height: 100% !important;
        z-index: $zindex-modal-backdrop + 1 !important;
        border: 0;
    }
    .tooltip-field-info {
        display: none;
    }
    .o_form_fullscreen_ancestor {
        display: block !important;
        position: static !important;
        top: 0 !important;
        left: 0 !important;
        width: auto !important;
        overflow: hidden !important;
        transform: none !important;
    }
}
