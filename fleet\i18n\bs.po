# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * fleet
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-08-24 09:18+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>g/km</span>"
msgstr "<span>g/km</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>kW</span>"
msgstr "<span>kW</span>"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_1
msgid "A/C Compressor Replacement"
msgstr "Zamjena kompresora klime"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_2
msgid "A/C Condenser Replacement"
msgstr "Zamjena kondensa klime"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_3
msgid "A/C Diagnosis"
msgstr "Dijagnostika klime"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_4
msgid "A/C Evaporator Replacement"
msgstr "Zamjena isparivača klime"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_5
msgid "A/C Recharge"
msgstr "Dopuna klime"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Activation Cost"
msgstr "Trošak aktivacije"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__active
msgid "Active"
msgstr "Aktivan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_state
msgid "Activity State"
msgstr "Status aktivnosti"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.mail_activity_type_action_config_fleet
#: model:ir.ui.menu,name:fleet.fleet_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipovi aktivnosti"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_tag_action
msgid "Add a new tag"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Additional Details"
msgstr "Dodatni opis"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Additional Properties"
msgstr "Opcije"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_6
msgid "Air Filter Replacement"
msgstr "Zamjena zračnog filtera"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "All vehicles"
msgstr "Sva vozila"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_7
msgid "Alternator Replacement"
msgstr "Zamjena alternatora"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__cost_amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__cost_amount
msgid "Amount"
msgstr "Iznos"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Archived"
msgstr "Arhivirano"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_drivers
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_assignation_log_view_list
msgid "Assignation Logs"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_9
msgid "Assistance"
msgstr "Podrška"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Attention: renewal overdue"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle,transmission:0
msgid "Automatic"
msgstr "Automatski"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__auto_generated
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__auto_generated
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__auto_generated
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__auto_generated
msgid "Automatically Generated"
msgstr "Automatski generisano"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_8
msgid "Ball Joint Replacement"
msgstr "Zamjena remena"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_9
msgid "Battery Inspection"
msgstr "Provjera akumulatora"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_10
msgid "Battery Replacement"
msgstr "Zamjena akumulatora"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_11
msgid "Brake Caliper Replacement"
msgstr "Zamjena pločica kočnica"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_12
msgid "Brake Inspection"
msgstr "Provjera kočnica"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_13
msgid "Brake Pad(s) Replacement"
msgstr "Zamjena kočionih pločica"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__brand_id
msgid "Brand"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_brand
msgid "Brand of the vehicle"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_break
msgid "Break"
msgstr "Kočnica"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__co2
msgid "CO2 Emissions"
msgstr "Emisija CO2"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__co2
msgid "CO2 emissions of the vehicle"
msgstr "Emisija CO2 vozila"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_1
msgid "Calculation Benefit In Kind"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_14
msgid "Car Wash"
msgstr "Pranje vozila"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__car_value
msgid "Catalog Value (VAT Incl.)"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_15
msgid "Catalytic Converter Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__category
msgid "Category"
msgstr "Kategorija"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__cost_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__cost_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__cost_type
msgid "Category of the cost"
msgstr "Kategorija troškova"

#. module: fleet
#: model:mail.message.subtype,description:fleet.mt_fleet_driver_updated
#: model:mail.message.subtype,name:fleet.mt_fleet_driver_updated
msgid "Changed Driver"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_16
msgid "Charging System Diagnosis"
msgstr "Dijagnostika sistema za punjenje"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vin_sn
msgid "Chassis Number"
msgstr "Broj šasije"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__state
msgid "Choose whether the contract is still valid or not"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_service_type__category
msgid ""
"Choose whether the service refer to contracts, vehicle services or both"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Close Contract"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,state:0
msgid "Closed"
msgstr "Zatvoreno"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__color
msgid "Color"
msgstr "Boja"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__color
msgid "Color Index"
msgstr "Indeks boje"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__color
msgid "Color of the vehicle"
msgstr "Boja vozila"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_compact
msgid "Compact"
msgstr "Kompaktni"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__company_id
msgid "Company"
msgstr "Kompanija"

#. module: fleet
#: model:ir.ui.menu,name:fleet.fleet_configuration
msgid "Configuration"
msgstr "Konfiguracija"

#. module: fleet
#: selection:fleet.service.type,category:0
#: selection:fleet.vehicle.cost,cost_type:0
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__contract_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__contract_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__contract_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__contract_id
msgid "Contract"
msgstr "Ugovor"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_graph
msgid "Contract Costs Per Month"
msgstr "Trošak ugovora po mjesecima"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_count
msgid "Contract Count"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid "Contract Expiration Date"
msgstr "Datum isteka ugovora"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__ins_ref
msgid "Contract Reference"
msgstr "Referenca ugovora"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Contract Start Date"
msgstr "Datum početka ugovora"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_contract_types_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_contract_types_menu
msgid "Contract Types"
msgstr "Tipovi ugovora"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost__contract_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__contract_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel__contract_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__contract_id
msgid "Contract attached to this cost"
msgstr "Ugovor povezan sa ovim troškom"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Contract details"
msgstr "Detalji ugovora"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_contract
msgid "Contract information on a vehicle"
msgstr "Informacije ugovora vozila"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_tree
msgid "Contract logs"
msgstr "Zabilješke ugovora"

#. module: fleet
#: model:mail.activity.type,name:fleet.mail_act_fleet_contract_to_renew
msgid "Contract to Renew"
msgstr "Ugovor za obnovu"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Contract(s)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_contracts
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contracts"
msgstr "Ugovori"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_convertible
msgid "Convertible"
msgstr "Kabriolet"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__cost_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__cost_id
msgid "Cost"
msgstr "Cijena (Koštanje)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__description
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__description
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__description
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__description
msgid "Cost Description"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_form
msgid "Cost Details"
msgstr "Detalji troškova"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Cost Subtype"
msgstr "Podtip troška"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Cost Type"
msgstr "Tip troška"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_cost
msgid "Cost related to a vehicle"
msgstr "Troškovi povezani sa vozilom"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cost that is paid only once at the creation of the contract"
msgstr "Troškovi koji su plaćeni samo jednom pri kreiranju ugovora"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost__cost_subtype_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel__cost_subtype_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__cost_subtype_id
msgid "Cost type purchased with this cost"
msgstr "Tip troška kupljeni sa ovim troškom"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__cost_count
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting_costs
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Costs"
msgstr "Troškovi"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_costs_reporting_action
msgid "Costs Analysis"
msgstr "Analiza troškova"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_graph
msgid "Costs Per Month"
msgstr "Trošak po mjesecima"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_generated
msgid ""
"Costs paid at regular intervals, depending on the cost frequency. If the "
"cost frequency is set to unique, the cost will be logged at the start date"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid "Create a new contract"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid ""
"Create a new contract automatically with all the same informations except "
"for the date that will start at the end of current contract"
msgstr ""
"Kreirajte novi ugovor automatski sa svim istim informacijama osim datuma "
"koji će počinjati sa datumom kraja trenutnog ugovora"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_costs_action
msgid "Create a new cost"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_fuel_action
msgid "Create a new fuel log"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_brand_action
msgid "Create a new make"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "Create a new model"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "Create a new odometer log"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid "Create a new service entry"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_contract_types_action
msgid "Create a new type of contract"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Create a new type of service"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Create a new vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid "Create a new vehicle status"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__state_id
msgid "Current state of the vehicle"
msgstr "Trenutno stanje vozila"

#. module: fleet
#: selection:fleet.vehicle.log.contract,cost_frequency:0
msgid "Daily"
msgstr "Dnevno"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__date
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Date"
msgstr "Datum"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost__date
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel__date
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__date
msgid "Date when the cost has been executed"
msgstr "Datum kada je trošak bio otvoren"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Date when the coverage of the contract begins"
msgstr "Datum kada pokriće ugovora počinje"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid ""
"Date when the coverage of the contract expirates (by default, one year after"
" begin date)"
msgstr ""
"Datum kada pokriće ugovora ističe ( zadano jedna godina od početnog datuma )"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__acquisition_date
msgid "Date when the vehicle has been immatriculated"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_2
msgid "Depreciation and Interests"
msgstr "Amortizacija i kamate"

#. module: fleet
#: selection:fleet.vehicle,fuel_type:0
msgid "Diesel"
msgstr "Dizel"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_17
msgid "Door Window Motor/Regulator Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__doors
msgid "Doors Number"
msgstr "Broj vrata"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_downgraded
msgid "Downgraded"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__driver_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Driver"
msgstr "Vozač"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer__driver_id
msgid "Driver of the vehicle"
msgstr "Vozač vozila"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Drivers History"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Each contract (e.g.: leasing) may include several services\n"
"            (reparation, insurances, periodic maintenance)."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Each service can used in contracts, as a standalone service or both."
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Effective Costs"
msgstr "Efektivni troškovi"

#. module: fleet
#: selection:fleet.vehicle,fuel_type:0
msgid "Electric"
msgstr "Električni"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_17
msgid "Emissions"
msgstr "Emisija"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_leasing
msgid "Employee Car"
msgstr "Vozilo zaposlenog"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle_cost.py:44
#, python-format
msgid "Emptying the odometer value of a vehicle is not allowed."
msgstr "Pražnjenje vrijednosti kilometraže vozila nije dozvoljeno."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_end
msgid "End Date"
msgstr "Datum Završetka"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_18
msgid "Engine Belt Inspection"
msgstr "Pregled remena motora"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_19
msgid "Engine Coolant Replacement"
msgstr "Zamjena tečnosti motora"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Engine Options"
msgstr "Opcije motora"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_20
msgid "Engine/Drive Belt(s) Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_13
msgid "Entry into service tax"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_21
msgid "Exhaust Manifold Replacement"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,state:0
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Expired"
msgstr "Istekao"

#. module: fleet
#: selection:fleet.vehicle.log.contract,state:0
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Expiring Soon"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__first_contract_date
msgid "First Contract Date"
msgstr ""

#. module: fleet
#: model:ir.module.category,name:fleet.module_fleet_category
#: model:ir.ui.menu,name:fleet.menu_root
msgid "Fleet"
msgstr "Vozni park"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_service_type
msgid "Fleet Service Type"
msgstr ""

#. module: fleet
#: model:ir.actions.server,name:fleet.ir_cron_contract_costs_generator_ir_actions_server
#: model:ir.cron,cron_name:fleet.ir_cron_contract_costs_generator
#: model:ir.cron,name:fleet.ir_cron_contract_costs_generator
msgid "Fleet: Generate contracts costs based on costs frequency"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_channel_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost__cost_type
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_type
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel__cost_type
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__cost_type
msgid "For internal purpose only"
msgstr "Samo za internu upotrebu"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_frequency
msgid "Frequency of the recuring cost"
msgstr "Učestalost ponavljanja troška"

#. module: fleet
#: selection:fleet.vehicle.cost,cost_type:0
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Fuel"
msgstr "Gorivo"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_graph
msgid "Fuel Costs Per Month"
msgstr "Trošak goriva po mjesecima"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_22
msgid "Fuel Injector Replacement"
msgstr "Zamjena ubrizgivača goriva"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__fuel_logs_count
msgid "Fuel Log Count"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_fuel
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_tree
msgid "Fuel Logs"
msgstr "Evidencija goriva"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_23
msgid "Fuel Pump Replacement"
msgstr "Zamjena pumpe goriva"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__fuel_type
msgid "Fuel Type"
msgstr "Tip goriva"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__fuel_type
msgid "Fuel Used by the vehicle"
msgstr "Gorivo koje koristi vozilo"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_fuel
msgid "Fuel log for vehicles"
msgstr "Evidencija goriva vozila"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Future Activities"
msgstr "Buduće aktivnosti"

#. module: fleet
#: selection:fleet.vehicle,fuel_type:0
msgid "Gasoline"
msgstr "Benzin"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "General Properties"
msgstr "Opšta svojstva"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__generated_cost_ids
msgid "Generated Costs"
msgstr "Generisani troškovi"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Generated Recurring Costs"
msgstr "Generisani ponavljajući troškovi"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Group By"
msgstr "Grupiši po"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Has Alert(s)"
msgstr "Ima upozorenj(ea)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_overdue
msgid "Has Contracts Overdue"
msgstr "Ima preteklih ugovora"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_due_soon
msgid "Has Contracts to renew"
msgstr "Ima ugovora za obnavljanje"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_24
msgid "Head Gasket(s) Replacement"
msgstr "Zamjena glave motora"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_25
msgid "Heater Blower Motor Replacement"
msgstr "Zamjena ventilatora grijanja"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_26
msgid "Heater Control Valve Replacement"
msgstr "Zamjena kontrolnog senzora grijanja"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_27
msgid "Heater Core Replacement"
msgstr "Zamjena jezgra grijanja"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_28
msgid "Heater Hose Replacement"
msgstr "Zamjena cijevi grijanja"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_fuel_action
msgid "Here you can add refuelling entries for all vehicles."
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower
msgid "Horsepower"
msgstr "Konjskih snaga"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower_tax
msgid "Horsepower Taxation"
msgstr "Oporezivanje konjskih snaga"

#. module: fleet
#: selection:fleet.vehicle,fuel_type:0
msgid "Hybrid"
msgstr "Hibrid"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__id
msgid "ID"
msgstr "ID"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_unread
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_29
msgid "Ignition Coil Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__acquisition_date
msgid "Immatriculation Date"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,state:0
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "In Progress"
msgstr "U Toku"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__cost_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__cost_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__cost_ids
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Included Services"
msgstr "Uračunati servis"

#. module: fleet
#: selection:fleet.vehicle.log.contract,state:0
msgid "Incoming"
msgstr "Ulazni"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Indicative Cost"
msgstr "Indikativni trošak"

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting_indicative_costs
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Indicative Costs"
msgstr "Indikativni troškovi"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_costs_reporting_non_effective_action
msgid "Indicative Costs Analysis"
msgstr "Analiza indikativnih troškova"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__sum_cost
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Indicative Costs Total"
msgstr "Ukupno indikativnih troškova"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_30
msgid "Intake Manifold Gasket Replacement"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Invoice Date"
msgstr "Datum Fakture"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__inv_ref
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__inv_ref
msgid "Invoice Reference"
msgstr "Referenca Fakture"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_junior
msgid "Junior"
msgstr "Junior"

#. module: fleet
#: selection:fleet.vehicle,odometer_unit:0
msgid "Kilometers"
msgstr "Kilometara"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_kanban
msgid "Km"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer
msgid "Last Odometer"
msgstr "Zadnja kilometraža"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Late Activities"
msgstr "Aktivnosti u kašnjenju"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_leasing
msgid "Leasing"
msgstr "Lokacija"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__license_plate
msgid "License Plate"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__license_plate
msgid "License plate number of the vehicle (i = plate number for a car)"
msgstr "Broj registarskih tablica vozila (i = broj tablice vazila)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__liter
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_kanban
msgid "Liter"
msgstr "Litar"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__location
msgid "Location"
msgstr "Lokacija"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__location
msgid "Location of the vehicle (garage, ...)"
msgstr "Lokacija vozila (garaža,...)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__image
msgid "Logo"
msgstr "Logo"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image_medium
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image_medium
msgid "Logo (medium)"
msgstr "Logo (srednji)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image_small
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image_small
msgid "Logo (small)"
msgstr "Logo (mali)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_main_attachment_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__brand_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__name
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Make"
msgstr "Marka"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__brand_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__brand_id
msgid "Make of the vehicle"
msgstr "Marka vozila"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Manage all your contracts (leasing, insurances, etc.) with\n"
"            their related services, costs. Odoo will automatically warn\n"
"            you when some contracts have to be renewed."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_costs_action
msgid ""
"Manage the costs for your different vehicles.\n"
"                Costs are created automatically from services, contracts and fuel logs."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid ""
"Manage your fleet by keeping track of the contracts, services, odometers and"
" fuel logs associated to each vehicle."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_11
msgid "Management Fee"
msgstr "Provizija za upravljanje"

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_manager
msgid "Manager"
msgstr "Upravitelj"

#. module: fleet
#: selection:fleet.vehicle,transmission:0
msgid "Manual"
msgstr "Ručno"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__image_medium
msgid "Medium-sized image"
msgstr "Slika srednje veličine"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__image_medium
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__image_medium
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_brand__image_medium
msgid ""
"Medium-sized logo of the brand. It is automatically resized as a 128x128px "
"image, with aspect ratio preserved. Use this field in form views or some "
"kanban views."
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: fleet
#: selection:fleet.vehicle,odometer_unit:0
msgid "Miles"
msgstr "Milje"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Model"
msgstr "Model"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_tree
msgid "Model Make"
msgstr "Model marke"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_year
msgid "Model Year"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_brand_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_brand_menu
msgid "Model make of Vehicle"
msgstr "Model marke vozila"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__name
msgid "Model name"
msgstr "Model"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model
msgid "Model of a vehicle"
msgstr "Model vozila"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__model_id
msgid "Model of the vehicle"
msgstr "Model vozila"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
msgid "Models"
msgstr "Modeli"

#. module: fleet
#: selection:fleet.vehicle.log.contract,cost_frequency:0
msgid "Monthly"
msgstr "Mjesečno"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__name
msgid "Name"
msgstr "Naziv:"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_name
msgid "Name of contract to renew soon"
msgstr "Naziv ugovora koji je potrebno uskoro obnoviti"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_new_request
msgid "New Request"
msgstr "Novi zahtjev"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Krajnji rok za sljedeću aktivnost"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_summary
msgid "Next Activity Summary"
msgstr "Pregled sljedeće aktivnosti"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sljedeće aktivnosti"

#. module: fleet
#: selection:fleet.vehicle.log.contract,cost_frequency:0
msgid "No"
msgstr "Ne"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:85
#, python-format
msgid "No Plate"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_non_effective_action
msgid "No data for analysis"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__notes
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Notes"
msgstr "Zabilješke"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__doors
msgid "Number of doors of the vehicle"
msgstr "Broj vrata vozila"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__seats
msgid "Number of seats of the vehicle"
msgstr "Broj sjedišta vozila"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_unread_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__odometer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__odometer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__odometer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Odometer"
msgstr "Kilometraža"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Odometer Details"
msgstr "Detalji kilometraže"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_tree
msgid "Odometer Logs"
msgstr "Zabilješke kilometraže"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_unit
msgid "Odometer Unit"
msgstr "Jedinica kilometraže"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__odometer
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__odometer
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__value
msgid "Odometer Value"
msgstr "Vrijednost kilometraže"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_graph
msgid "Odometer Values Per Vehicle"
msgstr "Vrijednost kilometraže po vozilima"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__odometer
msgid "Odometer at creation"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Odometer details"
msgstr "Detalji kilometraže"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr "Zabilješke kilometraže za vozilo"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__odometer
msgid "Odometer measure of the vehicle at the moment of the contract creation"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost__odometer_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__odometer_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel__odometer_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer_id
msgid "Odometer measure of the vehicle at the moment of this log"
msgstr "Izmjerena kilometraža vozila u trenutku ove zabilješke"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_non_effective_action
msgid ""
"Odoo helps you managing the costs for your different vehicles\n"
"          Costs are generally created from services and contract and appears here."
msgstr ""
"Odoo vam pomaže da upravljate troškovima različitih vozila.\n"
"Troškovi su najčešće kreirani iz servisa i ugovora i pojavljuju se ovdje."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_31
msgid "Oil Change"
msgstr "Zamjena ulja"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_32
msgid "Oil Pump Replacement"
msgstr "Zamjena uljne pumpe"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_omnium
msgid "Omnium"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Open"
msgstr "Otvori"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_16
msgid "Options"
msgstr "Opcije"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_ordered
msgid "Ordered"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.cost,cost_type:0
msgid "Other"
msgstr "Drugo"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_33
msgid "Other Maintenance"
msgstr "Ostala održavanja"

#. module: fleet
#: selection:fleet.vehicle,activity_state:0
#: selection:fleet.vehicle.log.contract,activity_state:0
msgid "Overdue"
msgstr "Dospjele"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_34
msgid "Oxygen Sensor Replacement"
msgstr "Zamjena senzora kiseonika"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__parent_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__parent_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__parent_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__parent_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Parent"
msgstr "Nasljeđeni"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost__parent_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__parent_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel__parent_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__parent_id
msgid "Parent cost to this current cost"
msgstr "Nadređeni trošak do ovog trenutnog troška"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__purchaser_id
msgid "Person to which the contract is signed for"
msgstr "Osoba koju je ugovor potpisan"

#. module: fleet
#: selection:fleet.vehicle,activity_state:0
#: selection:fleet.vehicle.log.contract,activity_state:0
msgid "Planned"
msgstr "Planiran"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__power
msgid "Power"
msgstr "Eksponent"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_35
msgid "Power Steering Hose Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_36
msgid "Power Steering Pump Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__power
msgid "Power in kW of the vehicle"
msgstr "Snaga vozila u kW"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_tree
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Price"
msgstr "Cijena"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__price_per_liter
msgid "Price Per Liter"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_purchased
msgid "Purchased"
msgstr "Kupljeno"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__purchaser_id
msgid "Purchaser"
msgstr "Kupac"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_37
msgid "Radiator Repair"
msgstr "Popravak hladnjaka"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_generated
msgid "Recurring Cost Amount"
msgstr "Iznos ponavljajućeg troška"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_frequency
msgid "Recurring Cost Frequency"
msgstr "Učestalost ponavljanja troška"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_refueling
msgid "Refueling"
msgstr "Točenje goriva"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
msgid "Refueling Details"
msgstr "Detalji točenja goriva"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_registered
msgid "Registered"
msgstr ""

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle_cost.py:209
#: model:ir.actions.act_window,name:fleet.act_renew_contract
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#, python-format
msgid "Renew Contract"
msgstr "Obnovi ugovor"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_12
msgid "Rent (Excluding VAT)"
msgstr "Iznajmljivanje (bez PDVa)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_8
msgid "Repair and maintenance"
msgstr "Popravka i održavanje"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_repairing
msgid "Repairing"
msgstr "Popravljanje"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_10
msgid "Replacement Vehicle"
msgstr "Zamjensko vozilo"

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting
msgid "Reporting"
msgstr "Izvještavanje"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_reserve
msgid "Reserve"
msgstr "Rezerviši"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__residual_value
msgid "Residual Value"
msgstr "Preostala vrijednost"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_15
msgid "Residual value (Excluding VAT)"
msgstr "Preostala vrijednost (bez PDV-a)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_19
msgid "Residual value in %"
msgstr "Preostala vrijednost u %"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__user_id
msgid "Responsible"
msgstr "Odgovoran"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_38
msgid "Resurface Rotors"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_39
msgid "Rotate Tires"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_40
msgid "Rotor Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__seats
msgid "Seats Number"
msgstr "Broj sjedišta"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_sedan
msgid "Sedan"
msgstr "Sedan"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_senior
msgid "Senior"
msgstr "Senior"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: fleet
#: selection:fleet.service.type,category:0
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Service"
msgstr "Usluga"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Service Type"
msgstr "Tip servisa"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_service_types_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_service_types_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_service_types_view_tree
msgid "Service Types"
msgstr "Tipovi servisa"

#. module: fleet
#: selection:fleet.vehicle.cost,cost_type:0
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__service_count
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Services"
msgstr "Usluge"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_graph
msgid "Services Costs Per Month"
msgstr "Troškovi servisa po mjesecima"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Services Details"
msgstr "Detalji servisa"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_services
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Services Logs"
msgstr "Evidencija goriva"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Servisi za vozila"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Set Contract In Progress"
msgstr "Postavi ugovor u toku"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Show all records which has next action date is before today"
msgstr "Prikaži sve zapise koji imaju datum sljedeće akcije prije danas"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__image_small
msgid "Small-sized image"
msgstr "Male slike"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__image_small
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__image_small
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_brand__image_small
msgid ""
"Small-sized logo of the brand. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Mali logo marke. Automatski je promjenjena veličina na 64x64px, sa\n"
"očuvanom razmjerom. Koristite ovo polje gdje je god potrebna mala slika."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_6
msgid "Snow tires"
msgstr "Zimske gume"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_41
msgid "Spark Plug Replacement"
msgstr "Zamjena svjećica"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_start
msgid "Start Date"
msgstr "Datum početka"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_42
msgid "Starter Replacement"
msgstr "Zamjena startera"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__state_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_state_view_tree
msgid "State"
msgstr "Rep./Fed."

#. module: fleet
#: sql_constraint:fleet.vehicle.state:0
msgid "State name already exists"
msgstr "Naziv stanja već postoji"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__state
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Status"
msgstr "Status"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_5
#: model:fleet.service.type,name:fleet.type_service_service_7
msgid "Summer tires"
msgstr "Ljetne gume"

#. module: fleet
#: sql_constraint:fleet.vehicle.tag:0
msgid "Tag name already exists !"
msgstr "Naziv oznake već postoji!"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__tag_ids
msgid "Tags"
msgstr "Oznake"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_3
msgid "Tax roll"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Terms and Conditions"
msgstr "Pravila i Uslovi"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_non_effective_action
msgid ""
"Thanks to the different filters, Odoo can only print the effective\n"
"          costs, sort them by type and by vehicle."
msgstr ""
"Zahvaljujući različitim filterima, Odoo može da ispisuje samo efektivne\n"
"troškove, sortirajući ih po tipu i vozilu."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_43
msgid "Thermostat Replacement"
msgstr "Zamjena termostata"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__image
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__image
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_brand__image
msgid ""
"This field holds the image used as logo for the brand, limited to "
"1024x1024px."
msgstr ""
"Ovo polje sadrži sliku koja se koristi kao marka, limitirana na 1024x1024px."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_44
msgid "Tie Rod End Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_45
msgid "Tire Replacement"
msgstr "Zamjena guma"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_46
msgid "Tire Service"
msgstr "Servis guma"

#. module: fleet
#: selection:fleet.vehicle,activity_state:0
#: selection:fleet.vehicle.log.contract,activity_state:0
msgid "Today"
msgstr "Danas"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Total"
msgstr "Ukupno"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__amount
msgid "Total Price"
msgstr "Ukupna Cijena"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_14
msgid "Total expenses (Excluding VAT)"
msgstr "Ukupni troškovi (bez PDV-a)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_total
msgid "Total of contracts due or overdue minus one"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_18
msgid "Touring Assistance"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid ""
"Track all the services done on your vehicle.\n"
"            Services can be of many types: occasional repair, fixed maintenance, etc."
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__transmission
msgid "Transmission"
msgstr "Prenos"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_47
msgid "Transmission Filter Replacement"
msgstr "Zamjena filtera prenosa"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_48
msgid "Transmission Fluid Replacement"
msgstr "Zamjena tečnosti prenosa"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_49
msgid "Transmission Replacement"
msgstr "Zamjena prenosa"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__transmission
msgid "Transmission Used by the vehicle"
msgstr "Prenos koje vozilo koristi"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__cost_subtype_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__cost_subtype_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__cost_subtype_id
msgid "Type"
msgstr "Tip"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__vin_sn
msgid "Unique number written on the vehicle motor (VIN/SN number)"
msgstr "Jedinstveni broj zapisan na motoru vozila (VIN/SN broj)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__unit
msgid "Unit"
msgstr "Jedinica"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost__odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel__odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer__unit
msgid "Unit of the odometer "
msgstr "Jedinica kilometraže"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_unread
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_unread_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_state__sequence
msgid "Used to order the note stages"
msgstr ""

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_user
msgid "User"
msgstr "Korisnik"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__car_value
msgid "Value of the bought vehicle"
msgstr "Vrijednost kupljenog vozila"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__vehicle_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "Vehicle"
msgstr "Vozilo"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_cost_view_tree
msgid "Vehicle Costs"
msgstr "Troškovi vozila"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Vehicle Costs by Date"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
msgid "Vehicle Details"
msgstr "Detalji vozila"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_state_action
#: model:ir.model,name:fleet.model_fleet_vehicle_state
#: model:ir.ui.menu,name:fleet.fleet_vehicle_state_menu
msgid "Vehicle Status"
msgstr "Status vozila"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_tag
msgid "Vehicle Tag"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_tag_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_tag_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_form
msgid "Vehicle Tags"
msgstr "Oznake vozila"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost__vehicle_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__vehicle_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel__vehicle_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__vehicle_id
msgid "Vehicle concerned by this log"
msgstr "Vozilo kojeg se tiču ove zabilješke"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_form
msgid "Vehicle costs"
msgstr "Troškovi vozila"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_menu
#: model:ir.ui.menu,name:fleet.fleet_vehicles
msgid "Vehicles"
msgstr "Vozila"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_contract_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_contract_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vehicles Contracts"
msgstr "Ugovori vozila"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_costs_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_costs_menu
msgid "Vehicles Costs"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_fuel_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_fuel_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_search
msgid "Vehicles Fuel Logs"
msgstr "Zabilješke točenja goriva vozila"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_menu
msgid "Vehicles Model"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_odometer_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_odometer_menu
msgid "Vehicles Odometer"
msgstr "Kilometraža vozila"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_services_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_services_menu
msgid "Vehicles Services Logs"
msgstr "Zabilješke servisa vozila"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_cost_indicative_view_graph
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_cost_indicative_view_pivot
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_cost_view_graph
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_cost_view_pivot
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Vehicles costs"
msgstr "Troškovi vozila"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
msgid "Vehicles odometers"
msgstr "Kilometraže vozila"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__insurer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel__vendor_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vendor_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vendor"
msgstr "Dobavljač"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vendors
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vendors"
msgstr "Dobavljači"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_waiting_list
msgid "Waiting List"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__days_left
msgid "Warning Date"
msgstr "Datum upozorenja"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Warning: renewal due soon"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_50
msgid "Water Pump Replacement"
msgstr "Zamjena vodene pumpe"

#. module: fleet
#: selection:fleet.vehicle.log.contract,cost_frequency:0
msgid "Weekly"
msgstr "Sedmično"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_51
msgid "Wheel Alignment"
msgstr "Balans točkova"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_52
msgid "Wheel Bearing Replacement"
msgstr "Zamjena ležaja točka"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_53
msgid "Windshield Wiper(s) Replacement"
msgstr "Zamjena brisača šajbe"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Write here all other information relative to this contract"
msgstr "Ovdje upišite sve ostale informacije vezane za ovaj ugovor"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__notes
msgid "Write here all supplementary information relative to this contract"
msgstr "Ovdje upišite sve dopunjujuće podatke koje se tiču ovog ugovora"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
msgid "Write here any other information"
msgstr "Ovdje zapišite bilo kakvu drugu informaciju"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Write here any other information related to the service completed."
msgstr ""
"Zabilježite ovdje bilo koju drugu informaciju vezanu za završetak servisa."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__model_year
msgid "Year of the model"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,cost_frequency:0
msgid "Yearly"
msgstr "Godišnje"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "You can add various odometer entries for all vehicles."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid ""
"You can customize available status to track the evolution of\n"
"            each vehicle. Example: active, being repaired, sold."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "You can define several models (e.g. A3, A4) for each make (Audi)."
msgstr ""
"Možete definisati nekoliko modela (npr.: A3, A4) za svaku marku (Audi)"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "amount"
msgstr "iznos"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. Model S"
msgstr "npr.: Model S"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. PAE 326"
msgstr "npr.: PAE 326"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "e.g. Tesla"
msgstr "npr.: Tesla"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show all the costs for this vehicle"
msgstr "prikaži sve troškove za ovo vozilo"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the contract for this vehicle"
msgstr "prikaži ugovor za ovo vozilo"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the fuel logs for this vehicle"
msgstr "prikaži zabilješke goriva za ovo vozilo"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the odometer logs for this vehicle"
msgstr "prikaži zabilješke kilometraže za ovo vozilo"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the services logs for this vehicle"
msgstr "prikaži zabilješke servisiranja ovog vozila"
