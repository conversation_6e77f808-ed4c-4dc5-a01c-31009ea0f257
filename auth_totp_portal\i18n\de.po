# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_portal
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>Nesselbosch, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_totp_portal
#. openerp-web
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid " Copy"
msgstr " Kopieren"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "(Disable two-factor authentication)"
msgstr "(Zwei-Faktor-Authentifizierung deaktivieren)"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                        Two-factor authentication not enabled"
msgstr ""
"<i class=\"fa fa-warning\"/>\n"
"                        Zwei-Faktor-Authentifizierung nicht aktiviert"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<span class=\"text-success\">\n"
"                        <i class=\"fa fa-check-circle\"/>\n"
"                        Two-factor authentication enabled\n"
"                    </span>"
msgstr ""
"<span class=\"text-success\">\n"
"                        <i class=\"fa fa-check-circle\"/>\n"
"                        Zwei-Faktor-Authentifizierung aktiviert\n"
"                    </span>"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Added On</strong>"
msgstr "<strong>Hinzugefügt am</strong>"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Trusted Device</strong>"
msgstr "<strong>Vertrauenswürdiges Gerät</strong>"

#. module: auth_totp_portal
#. openerp-web
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid "Copied !"
msgstr "Kopiert !"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Enable two-factor authentication"
msgstr "Zwei-Faktor-Authentifizierung aktivieren"

#. module: auth_totp_portal
#. openerp-web
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid "Operation failed for unknown reason."
msgstr "Vorgang aus unbekanntem Grund fehlgeschlagen."

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Revoke All"
msgstr "Alle widerrufen"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Two-factor authentication"
msgstr "Zwei-Faktor-Authentifizierung"

#. module: auth_totp_portal
#: model:ir.model,name:auth_totp_portal.model_res_users
msgid "Users"
msgstr "Benutzer"
