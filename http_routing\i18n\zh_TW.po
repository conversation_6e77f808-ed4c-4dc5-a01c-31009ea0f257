# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* http_routing
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.403
msgid "403: Forbidden"
msgstr "403: 禁止存取"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid ""
"<b>Don't panic.</b> If you think it's our mistake, please send us a message "
"on"
msgstr "<b>別緊張~ </b>如果您認為這是系統錯誤，請給我們留言"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.error_message
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "<strong>Error message:</strong>"
msgstr "<strong>錯誤訊息：</strong>"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.500
msgid "Back"
msgstr "返回"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "Error"
msgstr "錯誤"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "Error 404"
msgstr "錯誤 404"

#. module: http_routing
#: model:ir.model,name:http_routing.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
#: model_terms:ir.ui.view,arch_db:http_routing.500
msgid "Home"
msgstr "首頁"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "Maybe you were looking for one of these <b>popular pages?</b>"
msgstr "也許您正在尋找這些<b>popular pages?</b>"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.400
msgid "Oops! Something went wrong."
msgstr "哎呀！出了一點問題~"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "QWeb"
msgstr "QWeb"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.400
msgid "Take a look at the error message below."
msgstr "查看以下錯誤訊息。"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "The error occurred while rendering the template"
msgstr "繪製範本時發生錯誤"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.403
msgid "The page you were looking for could not be authorized."
msgstr "您想找的頁面未被授權。"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "Traceback"
msgstr "追溯"

#. module: http_routing
#: model:ir.model,name:http_routing.model_ir_ui_view
msgid "View"
msgstr "檢視"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "We couldn't find the page you're looking for!"
msgstr "我們找不到您要找的頁面！"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "and evaluating the following expression:"
msgstr "以及評估下列表達式："

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "this page"
msgstr "此頁"
