# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_authorize
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_login
msgid "API Login Id"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_transaction_key
msgid "API Transaction Key"
msgstr ""

#. module: payment_authorize
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr ""

#. module: payment_authorize
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.net Profile ID"
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:177
#, python-format
msgid ""
"Authorize: received data with missing reference (%s) or trans_id (%s) or "
"fingerprint (%s)"
msgstr ""

#. module: payment_authorize
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.authorize_s2s_form
msgid "CVC"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.authorize_s2s_form
msgid "Card number"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.authorize_s2s_form
msgid "Cardholder name"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.authorize_s2s_form
msgid "Expires (MM / YY)"
msgstr ""

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.acquirer_form_authorize
msgid "How to get paid with Authorize.Net"
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:64
#, python-format
msgid ""
"If you don't have any account, please ask your salesperson to update your "
"profile. "
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:261
#, python-format
msgid ""
"Invalid token found: the Authorize profile is missing.Please make sure the "
"token has a valid acquirer reference."
msgstr ""

#. module: payment_authorize
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr ""

#. module: payment_authorize
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr ""

#. module: payment_authorize
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr ""

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_authorize
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:66
#, python-format
msgid "Please complete your profile. "
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:61
#, python-format
msgid "Please sign in to complete your profile."
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__provider
msgid "Provider"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__save_token
msgid "Save Cards"
msgstr ""

#. module: payment_authorize
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr ""

#. module: payment_authorize
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment.py:365
#, python-format
msgid "The Customer Profile creation in Authorize.NET failed."
msgstr ""

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:58
#, python-format
msgid ""
"The transaction cannot be processed because some contact details are missing"
" or invalid: "
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"This contains the unique reference for this partner/payment token "
"combination in the Authorize.net backend"
msgstr ""

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__save_token
msgid ""
"This option allows customers to save their credit card as a payment token "
"and to reuse it for a later purchase. If you manage subscriptions (recurring"
" invoicing), you need it to automatically charge the customer when you issue"
" an invoice."
msgstr ""

#. module: payment_authorize
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr ""
