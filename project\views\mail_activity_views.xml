<?xml version="1.0"?>
<odoo>
    <!-- Activity types config -->
    <record id="mail_activity_type_action_config_project_types" model="ir.actions.act_window">
        <field name="name">Activity Types</field>
        <field name="res_model">mail.activity.type</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">['|', ('res_model', '=', False), ('res_model', '=', 'project.task')]</field>
        <field name="context">{'default_res_model': 'project.task'}</field>
    </record>
    <menuitem id="project_menu_config_activity_type"
        action="mail_activity_type_action_config_project_types"
        parent="menu_project_config"/>
</odoo>