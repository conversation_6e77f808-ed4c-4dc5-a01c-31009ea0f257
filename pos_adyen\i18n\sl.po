# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_adyen
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Slovenian (https://www.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.pos_config_view_form
msgid "Add tip through payment terminal (Adyen)"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_api_key
msgid "Adyen API key"
msgstr ""

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "Adyen Error"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_latest_diagnosis
msgid "Adyen Latest Diagnosis"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_latest_response
msgid "Adyen Latest Response"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "Adyen Terminal Identifier"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Adyen Test Mode"
msgstr ""

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "An unexpected error occurred. Message from Adyen: %s"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_config__adyen_ask_customer_for_tip
msgid "Ask Customers For Tip"
msgstr ""

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "Authentication failed. Please check your Adyen credentials."
msgstr ""

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid ""
"Cancelling the payment failed. Please cancel it manually on the payment "
"terminal."
msgstr ""

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "Cannot process transactions with negative amount."
msgstr ""

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "Message from Adyen: %s"
msgstr ""

#. module: pos_adyen
#: code:addons/pos_adyen/models/pos_config.py:0
#, python-format
msgid ""
"Please configure a tip product for POS %s to support tipping with Adyen."
msgstr ""

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Nastavitve POS-blagajne"

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_config__adyen_ask_customer_for_tip
msgid "Prompt the customer to tip."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Run transactions in the test environment."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_latest_response
msgid ""
"Technical field used to buffer the latest asynchronous notification from "
"Adyen."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_latest_diagnosis
msgid "Technical field used to determine if the terminal is still connected."
msgstr ""

#. module: pos_adyen
#: code:addons/pos_adyen/models/pos_payment_method.py:0
#, python-format
msgid "Terminal %s is already used on payment method %s."
msgstr ""

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid ""
"The connection to your payment terminal failed. Please check if it is still "
"connected to the internet."
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_api_key
msgid ""
"Used when connecting to Adyen: https://docs.adyen.com/user-management/how-"
"to-get-the-api-key/#description"
msgstr ""

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "[Terminal model]-[Serial number], for example: P400Plus-123456789"
msgstr ""
