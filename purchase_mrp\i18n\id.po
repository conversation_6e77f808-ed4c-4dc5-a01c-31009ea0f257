# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_mrp
# 
# Translators:
# <PERSON>, 2022
# <PERSON>to The <<EMAIL>>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.purchase_order_form_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">Manufaktur</span>"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.mrp_production_form_view_purchase
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">Purchase</span>"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_purchase_order__mrp_production_count
msgid "Count of MO Source"
msgstr "Jumlah Sumber MO"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_production__purchase_order_count
msgid "Count of generated PO"
msgstr "Jumlah SPB yang dibuat"

#. module: purchase_mrp
#: code:addons/purchase_mrp/models/purchase.py:0
#, python-format
msgid "Manufacturing Source of %s"
msgstr "Sumber Manufaktur untuk %s"

#. module: purchase_mrp
#: code:addons/purchase_mrp/models/stock_move.py:0
#, python-format
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr ""
"Odoo tidak dapat membuat entri anglo saxon. Valuasi total %s adalah nol."

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_production
msgid "Production Order"
msgstr "Order Produksi"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order
msgid "Purchase Order"
msgstr "Order Pembelian"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Baris Order Pembelian"

#. module: purchase_mrp
#: code:addons/purchase_mrp/models/mrp_production.py:0
#, python-format
msgid "Purchase Order generated from %s"
msgstr "Purchase Order dibuat dari %s"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_stock_move
msgid "Stock Move"
msgstr "Pergerakan Stok"
