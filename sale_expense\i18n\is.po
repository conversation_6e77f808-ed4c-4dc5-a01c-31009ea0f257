# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_expense
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-08-24 09:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_count
msgid "# of Expenses"
msgstr ""

#. module: sale_expense
#: model_terms:digest.tip,tip_description:sale_expense.digest_tip_sale_expense_0
msgid "<strong style=\"font-size: 16px;\">Submit expenses by email</strong>"
msgstr ""

#. module: sale_expense
#: model_terms:digest.tip,tip_description:sale_expense.digest_tip_sale_expense_0
msgid "Activate Expense Emails"
msgstr ""

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytic Line"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense
msgid "Expense"
msgstr "Expense"

#. module: sale_expense
#: model:ir.actions.act_window,name:sale_expense.hr_expense_action_from_sale_order
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_ids
#: model_terms:ir.ui.view,arch_db:sale_expense.sale_order_form_view_inherit
msgid "Expenses"
msgstr "Expenses"

#. module: sale_expense
#: model_terms:ir.ui.view,arch_db:sale_expense.product_product_view_form_inherit_sale_expense
msgid "Invoicing"
msgstr "Reikningagerð"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_sale_order
msgid "Quotation"
msgstr "Tilboð"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense__sale_order_id
msgid "Sale Order"
msgstr ""

#. module: sale_expense
#: model_terms:digest.tip,tip_description:sale_expense.digest_tip_sale_expense_0
msgid ""
"Take a snapshot of your expenses and submit your expenses by email.<br>"
msgstr ""
