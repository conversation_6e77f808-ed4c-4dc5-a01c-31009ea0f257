# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_buckaroo
# 
# Translators:
# <PERSON>, 2021
# krn<PERSON><PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-17 16:38+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> Nagy <<EMAIL>>, 2021\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
#, python-format
msgid ""
"An error occurred during processing of your payment (code %s). Please try "
"again."
msgstr ""

#. module: payment_buckaroo
#: model:account.payment.method,name:payment_buckaroo.payment_method_buckaroo
#: model:ir.model.fields.selection,name:payment_buckaroo.selection__payment_acquirer__provider__buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__buckaroo_secret_key
msgid "Buckaroo Secret Key"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
#, python-format
msgid "Invalid shasign: received %(sign)s, computed %(check)s"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Fizetési szolgáltató"

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_account_payment_method
msgid "Payment Methods"
msgstr "Fizetési módok"

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_transaction
msgid "Payment Transaction"
msgstr "Fizetési tranzakció"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__provider
msgid "Provider"
msgstr "Szolgáltató"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference (%(ref)s) or shasign (%(sign)s)"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing transaction keys"
msgstr ""

#. module: payment_buckaroo
#: model_terms:ir.ui.view,arch_db:payment_buckaroo.payment_acquirer_form
msgid "Secret Key"
msgstr "Titkos kulcs"

#. module: payment_buckaroo
#: model:ir.model.fields,help:payment_buckaroo.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,help:payment_buckaroo.field_payment_acquirer__buckaroo_website_key
msgid "The key solely used to identify the website with Buckaroo"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
#, python-format
msgid "Unknown status code: %s"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__buckaroo_website_key
msgid "Website Key"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
#, python-format
msgid "Your payment was refused (code %s). Please try again."
msgstr ""
