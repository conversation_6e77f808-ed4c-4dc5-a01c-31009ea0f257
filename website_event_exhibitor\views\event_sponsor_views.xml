<?xml version="1.0"?>
<odoo>
<data>
    <!-- EVENTS/CONFIGURATION/EVENT Sponsor Levels -->
    <record id="event_sponsor_type_view_form" model="ir.ui.view">
        <field name="name">Sponsor Levels</field>
        <field name="model">event.sponsor.type</field>
        <field name="arch" type="xml">
            <form string="Event Sponsor Levels">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="display_ribbon_style"/>
                        <field name="sequence" groups="base.group_no_one"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="event_sponsor_type_view_tree" model="ir.ui.view">
        <field name="name">Sponsor Levels</field>
        <field name="model">event.sponsor.type</field>
        <field name="arch" type="xml">
            <tree editable="bottom" string="Event Sponsor Level">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="display_ribbon_style"/>
            </tree>
        </field>
    </record>

    <record id="event_sponsor_type_action" model="ir.actions.act_window">
        <field name="name">Sponsor Levels</field>
        <field name="res_model">event.sponsor.type</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Sponsor Level
            </p><p>
                Rank your sponsors based on your own grading system (e.g. "Gold, Silver, Bronze").
            </p>
        </field>
    </record>

    <record id="event_sponsor_view_search" model="ir.ui.view">
        <field name="name">event.sponsor.search</field>
        <field name="model">event.sponsor</field>
        <field name="arch" type="xml">
            <search string="Event Sponsors">
                <field name="partner_id"/>
                <field name="event_id"/>
                <field name="name"/>
                <field name="email"/>
                <field name="phone"/>
                <filter string="Published" name="filter_published" domain="[('website_published', '=', True)]"/>
                <separator/>
                <filter string="Archived" name="archived" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Exhibitors" name="filter_is_exhibitor" domain="[('exhibitor_type', 'in', ['exhibitor', 'online'])]"/>
                <filter string="Online" name="filter_is_exhibitor" domain="[('exhibitor_type', '=', 'online')]"/>
                <group string="Group By" expand="0">
                    <filter string="Event" name="group_by_event_id" domain="[]" context="{'group_by': 'event_id'}"/>
                    <filter string="Level" name="group_by_sponsor_type_id" domain="[]" context="{'group_by': 'sponsor_type_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="event_sponsor_view_form" model="ir.ui.view">
        <field name="name">event.sponsor.view.form</field>
        <field name="model">event.sponsor</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <field name="website_url" invisible="1"/>
                        <field name="is_published" widget="website_redirect_button"
                            attrs="{'invisible': [('exhibitor_type', '=', 'sponsor')]}"/>
                    </div>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <field name="active" invisible="1"/>
                    <field name="image_512" widget="image" class="oe_avatar"/>
                    <div class="oe_title">
                        <label for="name" string="Sponsor Name"/>
                        <h1><field name="name" placeholder="e.g. : OpenWood Decoration"/></h1>
                        <div class="oe_title">
                            <label for="subtitle"/>
                            <field name="subtitle" placeholder="e.g. Your best choice for your home"/>
                        </div>
                    </div>
                    <group>
                        <group>
                            <field name="partner_id" string="Partner"/>
                            <field name="email" widget="email" string="Email"
                                placeholder="e.g. : <EMAIL>"/>
                            <field name="phone" widget="phone" string="Phone" options="{'enable_sms': True}"/>
                            <field name="mobile" widget="phone" string="Mobile" options="{'enable_sms': True}"/>
                            <field name="url" widget="url" string="Website"
                                placeholder="e.g. : https://www.odoo.com"/>
                        </group>
                        <group>
                            <field name="event_id"/>
                            <field name="sponsor_type_id"/>
                            <field name="exhibitor_type" required="1"/>
                            <!-- Use website_published because is_published already used and widget conflicts -->
                            <field name="website_published" widget="boolean_toggle"
                                string="Display in footer"
                                attrs="{'invisible': [('exhibitor_type', '!=', 'sponsor')]}"/>
                            <label for="hour_from" string="Opening Hours"
                                attrs="{'invisible': [('exhibitor_type', '=', 'sponsor')]}"/>
                            <div class="o_row" attrs="{'invisible': [('exhibitor_type', '=', 'sponsor')]}">
                                <field name="hour_from" widget="float_time" nolabel="1" class="oe_inline"/>
                                <i class="fa fa-long-arrow-right mx-2" aria-label="Arrow icon" title="Arrow"/>
                                <field name="hour_to" widget="float_time" nolabel="1" class="oe_inline"/>
                                <field name="event_date_tz" nolabel="1" class="oe_inline"/>
                            </div>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description"
                            attrs="{'invisible': [('exhibitor_type', '=', 'sponsor')]}">
                            <field name="website_description" nolabel="1" options="{'noVideos': False}"/>
                        </page>
                        <page string="Online"
                            attrs="{'invisible': [('exhibitor_type', '!=', 'online')]}">
                            <group>
                                <group>
                                    <field name="room_name" attrs="{'required': [('exhibitor_type', '=', 'online')]}" string="Jitsi Name"/>
                                    <field name="room_lang_id"/>
                                    <field name="room_max_capacity" attrs="{'required': [('exhibitor_type', '=', 'online')]}"/>
                                    <field name="chat_room_id" groups="base.group_no_one"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" groups="base.group_user"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="event_sponsor_view_tree" model="ir.ui.view">
        <field name="name">event.sponsor.view.tree</field>
        <field name="model">event.sponsor</field>
        <field name="arch" type="xml">
            <tree multi_edit="1" sample="1">
                <field name="sequence" widget="handle"/>
                <field name="partner_id" readonly="1"/>
                <field name="name"/>
                <field name="email"/>
                <field name="phone"/>
                <field name="mobile"/>
                <field name="url" string="Website"/>
                <field name="sponsor_type_id"/>
                <field name="is_published" optional="show"/>
                <field name="exhibitor_type"/>
            </tree>
        </field>
    </record>

    <record id="event_sponsor_view_kanban" model="ir.ui.view">
        <field name="name">event.sponsor.view.kanban</field>
        <field name="model">event.sponsor</field>
        <field name="arch" type="xml">
            <kanban sample="1">
                <field name="id"/>
                <field name="url"/>
                <field name="partner_email"/>
                <field name="sponsor_type_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click overflow-hidden">
                            <div class="d-flex align-items-center">
                                <div class="w-25 d-flex justify-content-center">
                                    <img t-att-src="kanban_image('event.sponsor', 'image_128', record.id.raw_value)" class="img-fluid" alt="Sponsor image"/>
                                </div>
                                <div class="w-75 d-flex flex-column ml-3">
                                    <h1 class="o_kanban_record_title"><field name="name"/></h1>
                                    <strong>Level: <field name="sponsor_type_id"/></strong>
                                    <span class="text-muted"><field name="exhibitor_type"/></span>
                                    <span class="o_text_overflow" t-esc="record.partner_email.value"/>
                                    <span class="o_text_overflow" t-esc="record.url.value"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="event_sponsor_action" model="ir.actions.act_window">
        <field name="name">Event Sponsors</field>
        <field name="res_model">event.sponsor</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="help" type="html">
<p class="o_view_nocontent_smiling_face">
    Create a Sponsor / Exhibitor
</p><p>
    Sponsors are advertised on your event pages.<br />
    Exhibitors have a dedicated page a with chat room for people to connect with them.
</p>
        </field>
    </record>

    <record id="event_sponsor_action_from_event" model="ir.actions.act_window">
        <field name="name">Event Sponsors</field>
        <field name="res_model">event.sponsor</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="context">{'search_default_event_id': active_id, 'default_event_id': active_id}</field>
        <field name="help" type="html">
<p class="o_view_nocontent_smiling_face">
    Create a Sponsor / Exhibitor
</p><p>
    Sponsors are advertised on your event pages.<br />
    Exhibitors have a dedicated page a with chat room for people to connect with them.
</p>
        </field>
    </record>

</data>
</odoo>
