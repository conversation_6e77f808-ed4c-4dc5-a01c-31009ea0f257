<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
  <defs>
    <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
      <use xlink:href="#filterPath" fill="none"></use>
    </clipPath>
    <path id="filterPath"
      d="M0.0814,0.6292l0.0883-0.4105a0.3491,0.2598,0,0,1,0.2461-0.2081h0A0.349,0.2597,0,0,1,0.8597,0.2225l0.087,0.4468a0.3491,0.2597,0,0,1-0.2018,0.2739L0.6442,0.977a0.3489,0.2596,0,0,1-0.4175-0.0757L0.1521,0.8312A0.3492,0.2599,0,0,1,0.0814,0.6292Z">
    </path>
  </defs><svg viewBox="68.3936538696289 35.054168701171875 176.3720703125 211.69915771484375"
    preserveAspectRatio="none">
    <path class="background"
      d="M183.21,72.46C294.62,227.17,225,264.54,146.75,232.89c-31.16-12.62-32.39-28-56.92-68.26C33,71.32,124.18-9.51,183.21,72.46Z"
      fill="none" stroke="#7C6576" stroke-miterlimit="10" stroke-width="7"></path>
  </svg><svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
    <use xlink:href="#filterPath" fill="darkgrey"></use>
  </svg>
  <image xlink:href="" clip-path="url(#clip-path)"></image>
</svg>
