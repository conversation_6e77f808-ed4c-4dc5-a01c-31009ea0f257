<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="product_product_1_product_template" model="product.template">
            <field name="name">Chair floor protection</field>
            <field name="categ_id" ref="product.product_category_5"/>
            <field name="list_price">12.0</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="description_sale">Office chairs can harm your floor: protect it.</field>
            <field name="image_1920" type="base64" file="sale/static/img/floor_protection-image.png"/>
        </record>

        <record id="product.product_product_4_product_template" model="product.template">
            <field name="optional_product_ids" eval="[(6,0,[ref('product.product_product_11_product_template')])]"/>
        </record>
        <record id="product.product_product_11_product_template" model="product.template">
            <field name="optional_product_ids" eval="[(6,0,[ref('product_product_1_product_template')])]"/>
        </record>
        <record id="product.product_product_13_product_template" model="product.template">
            <field name="optional_product_ids" eval="[(6,0,[ref('product.product_product_11_product_template')])]"/>
        </record>
    </data>
</odoo>
