// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_ChatterContainer {
    display: flex;
    flex: 1 1 auto;
    width: map-get($sizes, 100);
}

.o_ChatterContainer_noChatter {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.o_ChatterContainer_noChatterIcon {
    margin-right: map-get($spacers, 2);
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

