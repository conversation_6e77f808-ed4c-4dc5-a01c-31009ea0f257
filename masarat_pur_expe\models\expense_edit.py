# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime
from odoo.exceptions import ValidationError


class HrExpense(models.Model):
    _inherit = "hr.expense"

    # product_categury_masarat = fields.Selection(selection=[
    #     ('type1', 'شراء أجهزة و معدات(أصول)')
    #     ,('type2', 'مشتريات متنوعة')
    #     ,('type3', 'اغلاق عهد')],
    #     string="فئة المشتريات")
    #
    # department_id = fields.Many2one('hr.department', related="employee_id.department_id")

    requirement_nature = fields.Selection(selection=[('periodec', 'دوري'), ('normal', 'اعتيادي'), ('ergent', 'عاجل')],
                                          string="طبيعة الاحتياج")

    # price_editable = fields.Boolean(compute="get_price_editable")
    #
    # unit_amount = fields.Float("Unit Price", compute='_compute_from_product_id_company_id', store=True, required=False, copy=True,states={'draft': [('readonly', False)], 'reported': [('readonly', False)], 'refused': [('readonly', False)]}, digits='Product Price')
    #
    # price_set = fields.Boolean(default=False)
    #
    # def confirm_price(self):
    #     self.price_set = True
    #
    # @api.depends('product_categury_masarat','product_id')
    # def get_price_editable(self):
    #     user_can_edit = self.env.user.has_group('masarat_pur_expe.group_expense_masarat_type2') or self.env.user.has_group('masarat_pur_expe.group_expense_masarat_type1')
    #     for elem in self:
    #         elem.price_editable = True
    #         if not elem.price_set and (elem.product_categury_masarat in ('type1','type2')) and not user_can_edit:
    #             elem.unit_amount = 0.0
    #             elem.price_editable = False
    #
    # def send_email(self,recrode_id,type):
    #     group_to_search = 'masarat_pur_expe.group_expense_masarat_type1' if type == 'type1' else 'masarat_pur_expe.group_expense_masarat_type2'
    #     emails_to = self.env.ref(group_to_search).users
    #     for line in emails_to:
    #         user_id = self._context.get('uid')
    #         email_from = self.env['hr.employee'].search([('user_id', '=', user_id)])
    #
    #         web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
    #         web_base_url += '/web#id=%d&view_type=form&model=%s' % (recrode_id, self._name)
    #
    #         body = """
    #                 <div dir="rtl">
    #                 <p><font style="font-size: 14px;"> لديك طلب موافقة على طلب شراء </font></p>
    #                           <p><font style="font-size: 14px;"> مقدم الطلب """ + str(email_from.name) + """</font></p>
    #                           <a href="%s">Request Link</a>
    #                 </div>""" % (web_base_url)
    #
    #
    #         template_id = self.env['mail.mail'].create({
    #             'subject': 'طلب شراء',
    #             'email_from': email_from.work_email,
    #             'email_to': line.login,
    #             'body_html': body
    #         })
    #         #### freaa
    #         template_id.send()
    #
    #
    # @api.model
    # def create(self, vals_list):
    #     res = super(HrExpense, self).create(vals_list)
    #     recrode_id = res.id
    #     if vals_list['product_categury_masarat'] in ('type1','type2'):
    #         self.sudo().send_email(recrode_id, vals_list['product_categury_masarat'])
    #     #####
    #     return res