# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * im_livechat_mail_bot
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:05+0000\n"
"PO-Revision-Date: 2018-10-02 10:05+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: im_livechat_mail_bot
#: selection:res.users,odoobot_state:0
msgid "Disabled"
msgstr ""

#. module: im_livechat_mail_bot
#: code:addons/im_livechat_mail_bot/models/mail_bot.py:18
#, python-format
msgid ""
"Good, you can customize canned responses in the live chat "
"application.<br/><br/><b>It's the end of this overview</b>, enjoy "
"discovering Odoo!"
msgstr ""

#. module: im_livechat_mail_bot
#: selection:res.users,odoobot_state:0
msgid "Idle"
msgstr ""

#. module: im_livechat_mail_bot
#: model:ir.model,name:im_livechat_mail_bot.model_mail_bot
msgid "Mail Bot"
msgstr ""

#. module: im_livechat_mail_bot
#: selection:res.users,odoobot_state:0
msgid "Not initialized"
msgstr ""

#. module: im_livechat_mail_bot
#: code:addons/im_livechat_mail_bot/models/mail_bot.py:21
#, python-format
msgid ""
"Not sure wat you are doing. Please press : and wait for the propositions. "
"Select one of them and press enter."
msgstr ""

#. module: im_livechat_mail_bot
#: model:ir.model.fields,field_description:im_livechat_mail_bot.field_res_users__odoobot_state
msgid "OdooBot Status"
msgstr ""

#. module: im_livechat_mail_bot
#: selection:res.users,odoobot_state:0
msgid "Onboarding attachement"
msgstr ""

#. module: im_livechat_mail_bot
#: selection:res.users,odoobot_state:0
msgid "Onboarding canned"
msgstr ""

#. module: im_livechat_mail_bot
#: selection:res.users,odoobot_state:0
msgid "Onboarding command"
msgstr ""

#. module: im_livechat_mail_bot
#: selection:res.users,odoobot_state:0
msgid "Onboarding emoji"
msgstr ""

#. module: im_livechat_mail_bot
#: selection:res.users,odoobot_state:0
msgid "Onboarding ping"
msgstr ""

#. module: im_livechat_mail_bot
#: code:addons/im_livechat_mail_bot/models/mail_bot.py:15
#, python-format
msgid "That's me! 🎉<br/>Try to type \":\" to use canned responses."
msgstr ""

#. module: im_livechat_mail_bot
#: model:ir.model,name:im_livechat_mail_bot.model_res_users
msgid "Users"
msgstr "អ្នកប្រើ"
