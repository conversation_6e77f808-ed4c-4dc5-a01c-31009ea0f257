# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: lhmflexerp <<EMAIL>>, 2023\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_work_entry
#: code:addons/hr_work_entry/models/hr_employee.py:0
#, python-format
msgid "%s work entries"
msgstr ""

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "<span class=\"ml8\">Hours</span>"
msgstr "<span class=\"ml8\">Timer</span>"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__active
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__active
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__active
msgid "Active"
msgstr "Aktiv"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Archived"
msgstr "Arkiveret"

#. module: hr_work_entry
#: model:hr.work.entry.type,name:hr_work_entry.work_entry_type_attendance
msgid "Attendance"
msgstr "Tilstedeværelse"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_tree
msgid "Beginning"
msgstr "Begyndelse"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__cancelled
msgid "Cancelled"
msgstr "Annulleret"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__code
msgid ""
"Carefull, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__code
msgid "Code"
msgstr "Kode"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__color
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__color
msgid "Color"
msgstr "Farve"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__company_id
msgid "Company"
msgstr "Virksomhed"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__conflict
msgid "Conflict"
msgstr "Konflikt"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Conflicting"
msgstr "Modstridende"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__conflict
msgid "Conflicts"
msgstr "Konflikter"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__create_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__create_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__create_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__create_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Current Month"
msgstr "Indeværende måned"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Date"
msgstr "Dato"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__department_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Department"
msgstr "Afdeling"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__display_name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__display_name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__draft
msgid "Draft"
msgstr "Udkast"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_kanban
msgid "Dropdown menu"
msgstr "Dropdown menu"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_employee
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__employee_id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__employee_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Employee"
msgstr "Medarbejder"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_tree
msgid "End"
msgstr ""

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__date_start
msgid "From"
msgstr "Fra"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR arbejdspostering"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "HR arbejdsposteringstype"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__id
msgid "ID"
msgstr "ID"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the work "
"entry type without removing it."
msgstr ""
"Hvis det aktive felt er angivet til falsk, vil det gøre det muligt for dig "
"at skjule typen på arbejdsregistreringen, uden at fjerne den."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee____last_update
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry____last_update
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__write_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__write_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__write_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__write_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__user_id
msgid "Me"
msgstr "Mig"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__name
msgid "Name"
msgstr "Navn"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Note: Validated work entries cannot be modified."
msgstr ""

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__duration
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Period"
msgstr "Periode"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Resource fritid detaljer"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Search Work Entry"
msgstr "Søg arbejdsregistreringer"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_search
msgid "Search Work Entry Type"
msgstr "Søg arbejdsregistrering type"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: hr_work_entry
#. openerp-web
#: code:addons/hr_work_entry/static/src/xml/work_entry_templates.xml:0
#, python-format
msgid "Solve conflicts first"
msgstr "Løs konflikter først"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Start Date"
msgstr "Start dato"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entry_start_before_end
msgid "Starting time should be before end time."
msgstr "Start tid bør være før slut tid."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__state
msgid "State"
msgstr "Status"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry_type_unique_work_entry_code
msgid "The same code cannot be associated to multiple work entry types."
msgstr ""
"Den samme kode kan ikke associeres med flere arbejdsregistrerings typer."

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
msgid "Time Off Options"
msgstr "Fri muligheder"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__date_stop
msgid "To"
msgstr "Til"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Type"
msgstr "Type"

#. module: hr_work_entry
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
#, python-format
msgid "Undefined"
msgstr "Udefineret"

#. module: hr_work_entry
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
#, python-format
msgid "Undefined Type"
msgstr ""

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__validated
msgid "Validated"
msgstr "Valideret"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entries_no_validated_conflict
msgid "Validated work entries cannot overlap"
msgstr ""

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Arbejds detaljer"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_employee_view_form
msgid "Work Entries"
msgstr "Arbejdsposter"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_user_work_entry_employee
msgid "Work Entries Employees"
msgstr "Arbejdsregistreringer ansatte"

#. module: hr_work_entry
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_action
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_action_conflict
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Work Entry"
msgstr "Arbejdsregistrering"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Work Entry Name"
msgstr "Arbejdsregistrering navn"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__work_entry_type_id
#: model:ir.model.fields,field_description:hr_work_entry.field_resource_calendar_attendance__work_entry_type_id
#: model:ir.model.fields,field_description:hr_work_entry.field_resource_calendar_leaves__work_entry_type_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_work_entry.resource_calendar_leaves_view_search_inherit
msgid "Work Entry Type"
msgstr "Arbejdsposterings type"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
msgid "Work Entry Type Name"
msgstr "Arbejdsregistrering type navn"

#. module: hr_work_entry
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_type_action
msgid "Work Entry Types"
msgstr "Arbejdsposterings typer"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entry_has_end
msgid "Work entry must end. Please define an end date or a duration."
msgstr ""
"Arbejdsregistrering skal slutte. Vær venlig at definere en slut dato eller "
"en varighed."

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_user_work_entry_employee_user_id_employee_id_unique
msgid "You cannot have the same employee twice."
msgstr "Du kan ikke have den samme medarbejdere to gange."
