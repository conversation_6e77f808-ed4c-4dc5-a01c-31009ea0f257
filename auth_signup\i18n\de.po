# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_signup
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "%s connected"
msgstr "%s verbunden"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "<i title=\"Close\" class=\"small fa fa-times\"/>"
msgstr "<i title=\"Schließen\" class=\"small fa fa-times\"/>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid ""
"<strong>A password reset has been requested for this user. An email "
"containing the following link has been sent:</strong>"
msgstr ""
"<strong>Für diesen Benutzer wurde eine Passwortzurücksetzung beantragt. Eine"
" E-Mail mit dem folgenden Link wurde gesendet:</strong>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid ""
"<strong>An invitation email containing the following subscription link has "
"been sent:</strong>"
msgstr ""
"<strong>Eine Einladungsmail mit dem folgenden Bestätigungslink wurde "
"versendet:</strong>"

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_data_unregistered_users
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <t t-set=\"invited_users\" t-value=\"ctx.get('invited_users', [])\"/>\n"
"                <td style=\"text-align : left\">\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        Pending Invitations\n"
"                    </span><br/><br/>\n"
"                </td>\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Mitchell Admin</t>,<br/> <br/>\n"
"                        You added the following user(s) to your database but they haven't registered yet:\n"
"                        <ul>\n"
"                            <t t-foreach=\"invited_users\" t-as=\"invited_user\">\n"
"                                <li t-out=\"invited_user or ''\"><EMAIL></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                        Follow up with them so they can access your database and start working with you.\n"
"                        <br/><br/>\n"
"                        Have a nice day!<br/>\n"
"                        --<br/>The <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- INHALT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <t t-set=\"invited_users\" t-value=\"ctx.get('invited_users', [])\"/>\n"
"                <td style=\"text-align : left\">\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        Ausstehende Einladungen\n"
"                    </span><br/><br/>\n"
"                </td>\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Hallo <t t-out=\"object.name or ''\">Mitchell Admin</t>,<br/> <br/>\n"
"                        Sie haben folgende(n) Benutzer zu Ihrer Datenbank hinzugefügt, eine Registrierung hat noch nicht stattgefunden:\n"
"                        <ul>\n"
"                            <t t-foreach=\"invited_users\" t-as=\"invited_user\">\n"
"                                <li t-out=\"invited_user or ''\"><EMAIL></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                        Setzen Sie sich mit ihnen in Verbindung, damit sie Zugang zu Ihrer Datenbank erhalten und mit Ihnen zusammenarbeiten können..\n"
"                        <br/><br/>\n"
"                        Einen schönen Tag!<br/>\n"
"                        --<br/>Das <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.set_password_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Welcome to Odoo</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        You have been invited by <t t-out=\"object.create_uid.name or ''\">OdooBot</t> of <t t-out=\"object.company_id.name or ''\">YourCompany</t> to connect on Odoo.\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"object.signup_url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Accept invitation\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-set=\"website_url\" t-value=\"object.get_base_url()\"/>\n"
"                        Your Odoo domain is: <b><a t-att-href=\"website_url\" t-out=\"website_url or ''\">http://yourcompany.odoo.com</a></b><br/>\n"
"                        Your sign in email is: <b><a t-attf-href=\"/web/login?login={{ object.email }}\" target=\"_blank\" t-out=\"object.email or ''\"><EMAIL></a></b><br/><br/>\n"
"                        Never heard of Odoo? It’s an all-in-one business software loved by 7+ million users. It will considerably improve your experience at work and increase your productivity.\n"
"                        <br/><br/>\n"
"                        Have a look at the <a href=\"https://www.odoo.com/page/tour?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo Tour</a> to discover the tool.\n"
"                        <br/><br/>\n"
"                        Enjoy Odoo!<br/>\n"
"                        --<br/>The <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-att-href=\"'mailto:%s' % object.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-att-href=\"'%s' % object.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- KOPFZEILE -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Willkommen bei Odoo</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- INHALT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Hallo <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        <t t-out=\"object.create_uid.name or ''\">OdooBot</t> von<t t-out=\"object.company_id.name or ''\">YourCompany</t> lädt Sie ein, sich auf Odoo zu vernetzen.\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"object.signup_url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Einladung annehmen\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-set=\"website_url\" t-value=\"object.get_base_url()\"/>\n"
"                        Ihre Odoo-Domain ist: <b><a t-att-href=\"website_url\" t-out=\"website_url or ''\">http://yourcompany.odoo.com</a></b><br/>\n"
"                        Ihre Anmelde-E-Mail ist: <b><a t-attf-href=\"/web/login?login={{ object.email }}\" target=\"_blank\" t-out=\"object.email or ''\"><EMAIL></a></b><br/><br/>\n"
"                        Noch nie von Odoo gehört? Es ist eine All-in-One-Business-Software, die von mehr als 7 Millionen Nutzern verwendet wird. Sie wird Ihre Arbeitserfahrung erheblich verbessern und Ihre Produktivität steigern.\n"
"                        <br/><br/>\n"
"                        Machen Sie die <a href=\"https://www.odoo.com/page/tour?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo Tour</a> um uns kennenzulernen.\n"
"                        <br/><br/>\n"
"                        Viel Spaß mit Odoo!<br/>\n"
"                        --<br/>Das <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FUßZEILE -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-att-href=\"'mailto:%s' % object.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-att-href=\"'%s' % object.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.reset_password_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        A password reset was requested for the Odoo account linked to this email.\n"
"                        You may change your password by following this link which will remain valid during 24 hours:<br/>\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"object.signup_url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Change password\n"
"                            </a>\n"
"                        </div>\n"
"                        If you do not expect this, you can safely ignore this email.<br/><br/>\n"
"                        Thanks,\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-att-href=\"'mailto:%s' % object.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-att-href=\"'%s' % object.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- KOPFZEILE -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Ihr Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- INHALT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Hallo <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        Für das mit dieser E-Mail verknüpfte Odoo-Konto wurde ein neues Passwort angefordert.\n"
"Sie können Ihr Passwort ändern, indem Sie diesem Link folgen, der 24 Stunden lang gültig bleibt:<br/>\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"object.signup_url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Passwort ändern\n"
"                            </a>\n"
"                        </div>\n"
"                        Andernfalls können Sie diese E-Mail getrost ignorieren.<br/><br/>\n"
"                        Danke,\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FUßZEILE -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-att-href=\"'mailto:%s' % object.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-att-href=\"'%s' % object.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_user_signup_account_created
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        Your account has been successfully created!<br/>\n"
"                        Your login is <strong><t t-out=\"object.email or ''\"><EMAIL></t></strong><br/>\n"
"                        To gain access to your account, you can use the following link:\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-attf-href=\"/web/login?auth_login={{object.email}}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Go to My Account\n"
"                            </a>\n"
"                        </div>\n"
"                        Thanks,<br/>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\"><t t-out=\"object.company_id.email or ''\"><EMAIL></t></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\">\n"
"                            <t t-out=\"object.company_id.website or ''\">http://www.example.com</t>\n"
"                        </a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- KOPFZEILE -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Ihr Konto</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- INHALT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Hallo <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        Ihr Konto wurde erfolgreich erstellt!<br/>\n"
"                        Ihr Login ist <strong><t t-out=\"object.email or ''\"><EMAIL></t></strong><br/>\n"
"                        Um Zugang zu Ihrem Konto zu erhalten, klicken Sie auf folgenden Link:\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-attf-href=\"/web/login?auth_login={{object.email}}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Zu meinem Konto gehen\n"
"                            </a>\n"
"                        </div>\n"
"                        Vielen Dank<br/>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FUßZEILE -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\"><t t-out=\"object.company_id.email or ''\"><EMAIL></t></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\">\n"
"                            <t t-out=\"object.company_id.website or ''\">http://www.example.com</t>\n"
"                        </a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Already have an account?"
msgstr "Haben Sie schon ein Konto?"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "An email has been sent with credentials to reset your password"
msgstr ""
"Es wurde eine E-Mail mit Zugangsdaten zur Änderung Ihres Passworts gesendet"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Another user is already registered using this email address."
msgstr ""
"Ein anderer Benutzer ist bereits mit dieser E-Mail-Adresse registriert"

#. module: auth_signup
#: model:mail.template,name:auth_signup.mail_template_user_signup_account_created
msgid "Auth Signup: Odoo Account Created"
msgstr "Auth-Anmeldung: Odoo Konto erstellt"

#. module: auth_signup
#: model:mail.template,name:auth_signup.set_password_email
msgid "Auth Signup: Odoo Connection"
msgstr "Auth-Anmeldung: Odoo Verbindung"

#. module: auth_signup
#: model:mail.template,name:auth_signup.reset_password_email
msgid "Auth Signup: Reset Password"
msgstr "Auth-Anmeldung: Passwort zurücksetzen"

#. module: auth_signup
#: model:mail.template,name:auth_signup.mail_template_data_unregistered_users
msgid "Auth Signup: Unregistered Users"
msgstr "Auth-Anmeldung: Nicht registrierte Benutzer"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Authentication Failed."
msgstr "Authentifizierung fehlgeschlagen"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Back to Login"
msgstr "Zurück zur Anmeldung"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Cannot send email: user %s has no email address."
msgstr ""
"E-Mail kann nicht versendet werden: Benutzer %s hat keine E-Mail-Adresse"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Close"
msgstr "Schließen"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Confirm"
msgstr "Bestätigen"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Confirm Password"
msgstr "Passwort bestätigen"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__active
msgid "Confirmed"
msgstr "Bestätigt"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Could not create a new account."
msgstr "Neues Konto konnte nicht angelegt werden."

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Could not reset your password"
msgstr "Ihr Passwort konnte nicht zurückgesetzt werden"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr "Kundenkonto"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "Standard-Zugriffsrechte"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login
msgid "Don't have an account?"
msgstr "Noch kein Benutzerkonto?"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_reset_password
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Enable password reset from Login page"
msgstr ""
"Erlauben Sie es, dass Passwörter über die Anmeldeseite zurückgesetzt werden"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "Kostenlose Anmeldung"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-Routing"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Invalid signup token"
msgstr "Ungültiges Registrierungstoken"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Kunden können sich anmelden, um ihre Dokumente zu sehen"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Multiple accounts found for this email"
msgstr "Mehrere Konten für diese E-Mail gefunden"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__new
msgid "Never Connected"
msgstr "Nie verbunden"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "No login provided."
msgstr "Keine Anmeldung vorhanden."

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "Auf Einladung"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Password"
msgstr "Passwort"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Password Reset"
msgstr "Passwort zurücksetzen"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.reset_password_email
msgid "Password reset"
msgstr "Passwortzurücksetzung"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Passwords do not match; please retype them."
msgstr "Passwörter stimmen nicht überein; bitte geben Sie sie erneut ein."

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_data_unregistered_users
msgid "Reminder for unregistered users"
msgstr "Erinnerung für nichtregistrierte Benutzer"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login
msgid "Reset Password"
msgstr "Passwort zurücksetzen"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Reset password: invalid username or email"
msgstr "Passwort zurücksetzen: Ungültiger Benutzername oder E-Mail"

#. module: auth_signup
#: model:ir.actions.server,name:auth_signup.action_send_password_reset_instructions
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send Password Reset Instructions"
msgstr "Anweisungen zum Zurücksetzen des Passworts versenden"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send an Invitation Email"
msgstr "Eine Einladungsmail versenden"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Sign up"
msgstr "Registrieren"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_expiration
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_expiration
msgid "Signup Expiration"
msgstr "Ablauf der Registrierung"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_token
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_token
msgid "Signup Token"
msgstr "Registrierungstoken"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_type
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_type
msgid "Signup Token Type"
msgstr "Typ des Registrierungstokens"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_valid
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_valid
msgid "Signup Token is Valid"
msgstr "Registrierungstoken ist gültig"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_url
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_url
msgid "Signup URL"
msgstr "Registrierungs-URL"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup is not allowed for uninvited users"
msgstr ""
"Die Registrierung ist für Benutzer, die nicht eingeladen sind, nicht "
"erlaubt."

#. module: auth_signup
#: code:addons/auth_signup/models/res_partner.py:0
#, python-format
msgid "Signup token '%s' is no longer valid"
msgstr "Registrierungstoken „%s“ ist nicht mehr gültig"

#. module: auth_signup
#: code:addons/auth_signup/models/res_partner.py:0
#, python-format
msgid "Signup token '%s' is not valid"
msgstr "Registrierungstoken „%s“ ist nicht gültig"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup: invalid template user"
msgstr "Registrierung: ungültiger Vorlagenbenutzer"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup: no login given for new user"
msgstr "Registrierung: Für neue Benutzer wird kein Login vergeben"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup: no name or partner given for new user"
msgstr ""
"Registrierung: Für neuen Benutzer wird kein Name oder Partner angegeben"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users__state
msgid "Status"
msgstr "Status"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_template_user_id
msgid "Template user for new users created through signup"
msgstr ""
"Vorlagenbenutzer für neue Benutzer, die über die Registrierung erstellt "
"werden"

#. module: auth_signup
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "The form was not properly filled in."
msgstr "Das Formular wurde nicht ordnungsgemäß ausgefüllt."

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "This is their first connection. Wish them luck."
msgstr "Dies ist ihre erste Verbindung. Wünschen Sie ihnen Glück."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Um Einladungen im B2B-Modus zu versenden, öffnen Sie einen Kontakt oder "
"wählen Sie in der Listenansicht mehrere aus und klicken Sie im Drop-down-"
"Menü *Aktion* auf die Option „Portal-Zugriffsverwaltung“."

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_users
msgid "Users"
msgstr "Benutzer"

#. module: auth_signup
#: model:ir.actions.server,name:auth_signup.ir_cron_auth_signup_send_pending_user_reminder_ir_actions_server
#: model:ir.cron,cron_name:auth_signup.ir_cron_auth_signup_send_pending_user_reminder
#: model:ir.cron,name:auth_signup.ir_cron_auth_signup_send_pending_user_reminder
msgid "Users: Notify About Unregistered Users"
msgstr "Benutzer: Benachrichtigung über nichtregistrierte Benutzer"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_user_signup_account_created
msgid "Welcome to {{ object.company_id.name }}!"
msgstr "Willkommen bei {{ object.company_id.name }}!"

#. module: auth_signup
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "You cannot perform this action on an archived user."
msgstr ""
"Sie können diese Aktion nicht für einen archivierten Benutzer durchführen."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Your Email"
msgstr "Ihre E-Mail"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Your Name"
msgstr "Ihr Name"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "e.g. John Doe"
msgstr "z. B. Lieschen Müller"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.set_password_email
msgid ""
"{{ object.create_uid.name }} from {{ object.company_id.name }} invites you "
"to connect to Odoo"
msgstr ""
"{{ object.create_uid.name }} von {{ object.company_id.name }} lädt Sie ein, "
"sich mit Odoo zu verbinden"
