# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_plugin
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <brencic<PERSON><EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <jaro.b<PERSON><PERSON>@ekoenergo.sk>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <alexandra.brenci<PERSON><PERSON>@gmail.com>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "%s employees"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Add Contact To Database"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Address"
msgstr "Adresa"

#. module: mail_plugin
#: model_terms:ir.ui.view,arch_db:mail_plugin.app_auth
msgid "Allow"
msgstr "Povoliť"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "An error has occurred when trying to fetch translations."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Annual Revenue"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid ""
"Attachments could not be logged in Odoo because their total size exceeded "
"the allowed maximum."
msgstr ""

#. module: mail_plugin
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#, python-format
msgid "Bad Email."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Buy More"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Buy new credits"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Can not save the contact"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Clear Translations Cache"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Close"
msgstr "Zatvoriť"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Company"
msgstr "Spoločnosť"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Company Created."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Company Insights"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Company Type"
msgstr "Typ Spoločnosti"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Company created"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Company updated"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: model:ir.model,name:mail_plugin.model_res_partner
#, python-format
msgid "Contact"
msgstr "Kontakt"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Contact Details"
msgstr "Kontaktné údaje"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Contact created"
msgstr ""

#. module: mail_plugin
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#, python-format
msgid "Contact has no valid email"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Contacts Found (%(count)s)"
msgstr ""

#. module: mail_plugin
#: model:ir.model.fields,help:mail_plugin.field_res_partner_iap__partner_id
msgid "Corresponding partner"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Could not autocomplete the company. Internal error. Try again later..."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Could not connect to database. Try to log out and in."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Could not connect to your database. Please try again."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Could not display image %(attachmentName)s, size is over limit."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Create"
msgstr "Vytvoriť"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Create a Company"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Create a company"
msgstr ""

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Debug"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Debug Zone"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Debug zone for development purpose."
msgstr ""

#. module: mail_plugin
#: model_terms:ir.ui.view,arch_db:mail_plugin.app_auth
msgid "Deny"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Description"
msgstr "Popis"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: mail_plugin
#: model:ir.model.fields,help:mail_plugin.field_res_partner_iap__iap_search_domain
msgid "Domain used to find the company"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Email already logged on the contact"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Employees"
msgstr "Zamestnanci"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Enrich Company"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Error during enrichment"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Founded Year"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "From : %(email)s"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "From:"
msgstr "Od:"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Gmail Inbox"
msgstr ""

#. module: mail_plugin
#: model:ir.model,name:mail_plugin.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP smerovanie"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner__iap_enrich_info
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__iap_enrich_info
#: model:ir.model.fields,field_description:mail_plugin.field_res_users__iap_enrich_info
msgid "IAP Enrich Info"
msgstr ""

#. module: mail_plugin
#: model:ir.actions.act_window,name:mail_plugin.res_partner_iap_action
#: model_terms:ir.ui.view,arch_db:mail_plugin.res_partner_iap_view_form
#: model_terms:ir.ui.view,arch_db:mail_plugin.res_partner_iap_view_tree
msgid "IAP Partner"
msgstr ""

#. module: mail_plugin
#: model:ir.ui.menu,name:mail_plugin.res_partner_iap_menu
msgid "IAP Partners"
msgstr ""

#. module: mail_plugin
#: model:ir.model.fields,help:mail_plugin.field_res_partner__iap_enrich_info
#: model:ir.model.fields,help:mail_plugin.field_res_partner_iap__iap_enrich_info
#: model:ir.model.fields,help:mail_plugin.field_res_users__iap_enrich_info
msgid "IAP response stored as a JSON string"
msgstr ""

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__id
msgid "ID"
msgstr "ID"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Industry"
msgstr "Priemysel"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Invalid URL"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Keywords"
msgstr "Kľúčové slová"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: mail_plugin
#: model_terms:ir.ui.view,arch_db:mail_plugin.app_auth
msgid "Let"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Log Email Into Contact"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Log email"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Logged from"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Login"
msgstr "Login"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Logout"
msgstr "Odhlásiť sa"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No additional insights were found for this company"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No company attached to this contact"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "No company attached to this contact."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No company linked to this contact could be enriched"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No company linked to this contact could be enriched or found in Odoo"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "No contact found."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No data found for this email address."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No extra information found"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "No insights for this company."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "No insights found for this address."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Not enough credits to enrich."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Odoo Access Token"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Odoo Server URL"
msgstr ""

#. module: mail_plugin
#: model:ir.model.constraint,message:mail_plugin.constraint_res_partner_iap_unique_partner_id
msgid "Only one partner IAP is allowed for one partner"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid ""
"Oops, looks like you have exhausted your free enrichment requests. Please "
"log in to try again."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Our IAP server is down, please come back later."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Outlook Inbox"
msgstr ""

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__partner_id
msgid "Partner"
msgstr "Partner"

#. module: mail_plugin
#: model:ir.model,name:mail_plugin.model_res_partner_iap
msgid "Partner IAP"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Phone"
msgstr "Telefón"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Phones"
msgstr "Telefóny"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Read more"
msgstr "Čítaj viac"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Refresh"
msgstr "Obnoviť"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Refresh Contact"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Revenues"
msgstr "Tržby"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Save in Odoo"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Save the contact to create the company"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Search"
msgstr "Vyhľadávanie"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner__iap_search_domain
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__iap_search_domain
#: model:ir.model.fields,field_description:mail_plugin.field_res_users__iap_search_domain
msgid "Search Domain / Email"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Search In Database"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Search In Odoo"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Search contact"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Search contact in Odoo..."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Something bad happened. Please, try again later."
msgstr ""

#. module: mail_plugin
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#, python-format
msgid "The partner already has a company related to him"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "This company has no email address and could not be enriched."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "This contact does not exist in the Odoo database."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "This contact has no email address, no company could be enriched."
msgstr ""

#. module: mail_plugin
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#, python-format
msgid "This partner does not exist"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid ""
"Warning: Attachments could not be logged in Odoo because their total size "
"exceeded the allowed maximum."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid ""
"Warning: Could not fetch the attachments %(attachments)s as their sizes are "
"bigger then the maximum size of %(size)sMB per each attachment."
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Website"
msgstr "Webstránka"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Year founded"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "You don't have enough credit to enrich."
msgstr ""

#. module: mail_plugin
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#, python-format
msgid "You need to specify at least the partner_id or the name and the email"
msgstr ""

#. module: mail_plugin
#: model_terms:ir.ui.view,arch_db:mail_plugin.app_auth
msgid "access your Odoo database?"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "employees"
msgstr "zamestnanci"
