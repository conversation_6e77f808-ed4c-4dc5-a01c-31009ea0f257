<?xml version="1.0"?>
<odoo>
    <data noupdate="1">
        <function model="res.company" name="create_missing_dropship_sequence"/>
        <function model="res.company" name="create_missing_dropship_picking_type"/>

        <record id="route_drop_shipping" model='stock.location.route'>
            <field name="name">Dropship</field>
            <field name="sequence">20</field>
            <field name="company_id"></field>
            <field name="sale_selectable" eval="True"/>
            <field name="product_selectable" eval="True"/>
            <field name="product_categ_selectable" eval="True"/>
        </record>

        <function model="res.company" name="create_missing_dropship_sequence"/>
        <function model="res.company" name="create_missing_dropship_picking_type"/>
        <function model="res.company" name="create_missing_dropship_rule"/>
    </data>
</odoo>
