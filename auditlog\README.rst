=========
Audit Log
=========

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:0f44f8b1816ceb724c8ee7e4c19496e340734ca80c641bb0b1cadd874fad7195
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fserver--tools-lightgray.png?logo=github
    :target: https://github.com/OCA/server-tools/tree/15.0/auditlog
    :alt: OCA/server-tools
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/server-tools-15-0/server-tools-15-0-auditlog
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/server-tools&target_branch=15.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module allows the administrator to log user operations performed on data
models such as ``create``, ``read``, ``write`` and ``delete``.

**Table of contents**

.. contents::
   :local:

Usage
=====

Go to `Settings / Technical / Audit / Rules` to subscribe rules. A rule defines
which operations to log for a given data model.

.. image:: https://raw.githubusercontent.com/OCA/server-tools/15.0/auditlog/static/description/rule.png

Then, check logs in the `Settings / Technical / Audit / Logs` menu. You can
group them by user sessions, date, data model or HTTP requests:

.. image:: https://raw.githubusercontent.com/OCA/server-tools/15.0/auditlog/static/description/logs.png

Get the details:

.. image:: https://raw.githubusercontent.com/OCA/server-tools/15.0/auditlog/static/description/log.png

A scheduled action exists to delete logs older than 6 months (180 days)
automatically but is not enabled by default.
To activate it and/or change the delay, go to the
`Configuration / Technical / Automation / Scheduled Actions` menu and edit the
`Auto-vacuum audit logs` entry:

.. image:: https://raw.githubusercontent.com/OCA/server-tools/15.0/auditlog/static/description/autovacuum.png

In case you're having trouble with the amount of records to delete per run,
you can pass the amount of records to delete for one model per run as the second
parameter, the default is to delete all records in one go.

There are two possible groups configured to which one may belong. The first
is the Auditlog User group. This group has read-only access to the auditlogs of
individual records through the `View Logs` action. The second group is the
Auditlog Manager group. This group additionally has the right to configure the
auditlog configuration rules.

Known issues / Roadmap
======================

 * log only operations triggered by some users (currently it logs all users)
 * log read operations does not work on all data models, need investigation

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/server-tools/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/server-tools/issues/new?body=module:%20auditlog%0Aversion:%2015.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* ABF OSIELL

Contributors
~~~~~~~~~~~~

* Sebastien Alix <<EMAIL>>
* Holger Brunn <<EMAIL>>
* Holden Rehg <<EMAIL>>
* Eric Lembregts <<EMAIL>>
* Pieter Paulussen <<EMAIL>>
* Alan Ramos <<EMAIL>>
* Stefan Rijnhart <<EMAIL>>
* Bhavesh Odedra <<EMAIL>>
* Hardik Suthar <<EMAIL>>
* Kitti U. <<EMAIL>>
* Bogdan Valentin Gabor <<EMAIL>>

Other credits
~~~~~~~~~~~~~

* Icon: built with different icons from the `Oxygen theme <https://en.wikipedia.org/wiki/Oxygen_Project>`_ (LGPL)

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/server-tools <https://github.com/OCA/server-tools/tree/15.0/auditlog>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
