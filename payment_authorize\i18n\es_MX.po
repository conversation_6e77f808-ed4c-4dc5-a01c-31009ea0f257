# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_authorize
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "ABA Routing Number"
msgstr "Número de enrutamiento ABA"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_client_key
msgid "API Client Key"
msgstr "Clave de cliente API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_login
msgid "API Login ID"
msgstr "Id de inicio de sesión API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_signature_key
msgid "API Signature Key"
msgstr "Clave de firma API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_transaction_key
msgid "API Transaction Key"
msgstr "API de la clave de transacción"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Account Number"
msgstr "Número de cuenta"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_payment_method_type
msgid "Allow Payments From"
msgstr "Permitir pagos de"

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "An error occurred when displayed this payment form."
msgstr "Ocurrió un error al mostrar este formulario de pago."

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_currency_id
msgid "Authorize Currency"
msgstr "Autorizar divisa"

#. module: payment_authorize
#: model:account.payment.method,name:payment_authorize.payment_method_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__provider__authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_payment_method_type
msgid "Authorize.Net Payment Type"
msgstr "Tipo de pago authorize.net"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.Net Profile ID"
msgstr "ID de perfil Authorize.Net"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "Bank (powered by Authorize)"
msgstr "Banco (con tecnología de Authorize)"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__authorize_payment_method_type__bank_account
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_token__authorize_payment_method_type__bank_account
msgid "Bank Account (USA Only)"
msgstr "Cuenta bancaria (solo EE. UU.)"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Account Type"
msgstr "Tipo de cuenta bancaria"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Name"
msgstr "Nombre del banco"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Business Checking"
msgstr "Revisión de negocio"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Code"
msgstr "Código de tarjeta"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Number"
msgstr "Número de tarjeta"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"Could not fetch merchant details:\n"
"%s"
msgstr ""
"No pudimos obtener los detalles del comerciante:\n"
"%s"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__authorize_payment_method_type__credit_card
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_token__authorize_payment_method_type__credit_card
msgid "Credit Card"
msgstr "Tarjeta de crédito"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "Credit Card (powered by Authorize)"
msgstr "Tarjeta de crédito (con tecnología de Authorize)"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Currency"
msgstr "Divisa"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_payment_method_type
msgid "Determines with what payment method the customer can pay."
msgstr "Determina con qué método de pago puede pagar el cliente."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Expiration"
msgstr "Vencimiento"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"Failed to authenticate.\n"
"%s"
msgstr ""
"No se pudo autenticar.\n"
"%s"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Generate Client Key"
msgstr "Generar clave de cliente"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "How to get paid with Authorize.Net"
msgstr "Cómo recibir pagos con Authorize.Net"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "MM"
msgstr "MM"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Name On Account"
msgstr "Nombre en la cuenta"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "No se encontró ninguna transacción que coincida con la referencia %s."

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Método de pago"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_account_payment_method
msgid "Payment Methods"
msgstr "Métodos de pago"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr "Token de pago"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Personal Checking"
msgstr "Revisión personal"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Personal Savings"
msgstr "Ahorros personales"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__provider
msgid "Provider"
msgstr "Proveedor"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "Received data with status code \"%(status)s\" and error code \"%(error)s\""
msgstr ""
"Se recibió información con el código de estado \"%(status)s\" y código de "
"error \"%(error)s\""

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Received tampered payment request data."
msgstr "Se han recibido datos de solicitud de pago alterados."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_token.py:0
#, python-format
msgid "Saved payment methods cannot be restored once they have been deleted."
msgstr ""
"No se pueden restaurar los métodos de pago guardados una vez que se hayan "
"eliminado."

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Error en el servidor"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Set Account Currency"
msgstr "Configurar la divisa de la cuenta"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_login
msgid "The ID solely used to identify the account with Authorize.Net"
msgstr "El ID que se usa solo para identificar la cuenta con Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""
"El proveedor de servicios de pago que se utilizará con este método de pago"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_client_key
msgid ""
"The public client key. To generate directly from Odoo or from Authorize.Net "
"backend."
msgstr ""
"La llave de cliente pública. Para generarla directamente desde Odoo o desde "
"el backend de Authorize.Net."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "La transacción no está vinculada a un token."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_payment_method_type
msgid "The type of payment method this token is linked to."
msgstr "El tipo de método de pago al que está vinculado este Token."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"The unique reference for the partner/token combination in the Authorize.net "
"backend."
msgstr ""
"Esto contiene la única referencia para esta combinación de partner/token el "
"backend de Authorize.net"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"There are active tokens linked to this acquirer. To change the payment "
"method type, please disable the acquirer and duplicate it. Then, change the "
"payment method type on the duplicated acquirer."
msgstr ""
"Hay tokens activos que están vinculados a este método de pago. Para cambiar "
"el tipo de método de pago, deshabilite el método y duplíquelo. Después, "
"cambie el tipo de método de pago en el que duplicó."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "This action cannot be performed while the acquirer is disabled."
msgstr "Esta acción no se puede realizar cuando el método está desactivado."

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr "No podemos procesar su pago."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "YY"
msgstr "YY"
