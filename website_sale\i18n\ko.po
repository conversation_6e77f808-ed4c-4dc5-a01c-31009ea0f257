# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale
# 
# Translators:
# nle_odoo, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# Sarah <PERSON>, 2023
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-26 11:51+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "데이터를 가져왔습니다"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "\" category."
msgstr "\" 범주."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.option_collapse_categories_recursive
msgid "#{'Unfold' if c.id in category.parents_and_self.ids else 'Fold'}"
msgstr "#{'Unfold' if c.id in category.parents_and_self.ids else 'Fold'}"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s review"
msgstr "%s 리뷰"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s reviews"
msgstr "%s 리뷰"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "&amp; Shipping"
msgstr "&amp; 선적"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "'. Showing results for '"
msgstr "'. 다음에 대한 결과 표시 중 '"

#. module: website_sale
#: model:product.template.attribute.value,name:website_sale.product_1_attribute_3_value_1
msgid "1 year"
msgstr "1년"

#. module: website_sale
#: model:product.template.attribute.value,name:website_sale.product_1_attribute_3_value_2
msgid "2 year"
msgstr "2년"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr "<b>의사소통 : </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<b>Shipping: </b>"
msgstr "<b>선적 : </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "<b>Your order: </b>"
msgstr "<b>귀하의 주문 : </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid ""
"<br/>\n"
"                30-day money-back guarantee<br/>\n"
"                Shipping: 2-3 Business Days"
msgstr ""
"<br/>\n"
"                30일 내 환불 보장<br/>\n"
"                배송: 영업일 기준 2 - 3일"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<i class=\"fa fa-arrow-right\"/> Add payment acquirers"
msgstr "<i class=\"fa fa-arrow-right\"/> 결제 대행사 추가"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_buy_now
msgid "<i class=\"fa fa-bolt mr-2\"/>BUY NOW"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "<i class=\"fa fa-check\"/> Ship to this address"
msgstr "<i class=\"fa fa-check\"/> 이 주소로 배송"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                            <span>Back</span>"
msgstr ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                            <span>돌아가기</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                    <span>Return to Cart</span>"
msgstr ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                    <span>장바구니로 돌아가기</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_footer
msgid "<i class=\"fa fa-chevron-left\"/> Return to Cart"
msgstr "<i class=\"fa fa-chevron-left\"/> 장바구니로 돌아가기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<i class=\"fa fa-edit\"/> Edit"
msgstr "<i class=\"fa fa-edit\"/> 편집"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "<i class=\"fa fa-eraser mr-1\"/><u>Clear filters</u>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<i class=\"fa fa-plus-square\"/>\n"
"                                                        <span>Add an address</span>"
msgstr ""
"<i class=\"fa fa-plus-square\"/>\n"
"                                                        <span>주소 추가</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> 인쇄"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "<i class=\"fa fa-shopping-cart mr-2\"/>ADD TO CART"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">국가</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">State / Province...</option>"
msgstr "<option value=\"\">시/도</option>"

#. module: website_sale
#: code:addons/website_sale/models/crm_team.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        취소한 장바구니를 여기에서 모두 찾을 수 있습니다. 즉, 웹사이트 방문자들이 한 시간 전에 승인하지 않은 장바구니입니다.</p>\n"
"                        <p>취소한 물품들의 구매를 촉진하기 위해 고객에게 이메일을 보내야 합니다!</p>\n"
"                    "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"<small class=\"form-text text-muted\">Changing company name or VAT number is"
" not allowed once document(s) have been issued for your account. Please "
"contact us directly for this operation.</small>"
msgstr ""
"<small class=\"form-text text-muted\">계정 관련된 문서 발급이 완료된 이후에는 회사 이름 또는 VAT "
"번호를 변경할 수 없습니다. 관련된 내용은 직접 연락주시기 바랍니다. </small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"\">Process Checkout</span>\n"
"                                        <span class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span class=\"\">결제 진행</span>\n"
"                                        <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"\">Process Checkout</span>\n"
"                                    <span class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span class=\"\">결제 진행</span>\n"
"                                    <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid ""
"<span class=\"d-none d-lg-inline font-weight-bold text-muted\">Sort "
"By:</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid ""
"<span class=\"fa fa-chevron-down fa-border float-right\" role=\"img\" aria-"
"label=\"Details\" title=\"Details\"/>"
msgstr ""
"<span class=\"fa fa-chevron-down fa-border float-right\" role=\"img\" aria-"
"label=\"Details\" title=\"Details\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"fa fa-chevron-left fa-2x\" role=\"img\" aria-"
"label=\"Previous\" title=\"Previous\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                        <span class=\"\">Continue Shopping</span>"
msgstr ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                        <span class=\"\">쇼핑 계속하기</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                    Continue<span class=\"d-none d-md-inline\"> Shopping</span>"
msgstr ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                    <span class=\"d-none d-md-inline\"> 쇼핑</span> 계속하기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"fa fa-chevron-left\"/> Previous"
msgstr "<span class=\"fa fa-chevron-left\"/> 이전"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"fa fa-chevron-right fa-2x\" role=\"img\" aria-label=\"Next\" "
"title=\"Next\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Abandoned Carts</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">취소한 장바구니</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Assignment</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Confirmation Email</span>"
msgstr "<span class=\"o_form_label\">확인 이메일</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Invoicing Policy</span>"
msgstr "<span class=\"o_form_label\">청구 기준</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Give us your feedback</span>"
msgstr "<span class=\"s_website_form_label_content\">고객님의 의견을 들려주세요</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Upload a document</span>"
msgstr "<span class=\"s_website_form_label_content\">자료 업로드</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Your Reference</span>"
msgstr "<span class=\"s_website_form_label_content\">참조</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': [('product_variant_count', "
"'&lt;=', 1)]}\">Based on variants</span>"
msgstr ""
"<span class=\"text-muted\" attrs=\"{'invisible': [('product_variant_count', "
"'&lt;=', 1)]}\">속성 기준</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<span>Confirm</span>\n"
"                                    <i class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span>확인</span>\n"
"                                    <i class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Confirmed</span>"
msgstr "<span>확인함</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<span>Next</span>\n"
"                                            <i class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span>다음</span>\n"
"                                            <i class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr "<span>주문</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.short_cart_summary
msgid "<span>Process Checkout</span>"
msgstr "<span>결제 진행</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid ""
"<span>Unfortunately your order can not be confirmed as the amount of your payment does not match the amount of your cart.\n"
"                        Please contact the responsible of the shop for more information.</span>"
msgstr ""
"<span>결제 금액이 장바구니 금액과 일치하지 않아 주문을 확정할 수 없습니다.\n"
"                        자세한 내용은 매장 담당 직원에게 문의하시기 바랍니다.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "<span>Video Preview</span>"
msgstr "<span>동영상 미리보기</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<span>Your payment has been authorized.</span>"
msgstr "<span>귀하의 결제가 승인되었습니다.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form_web_terms
msgid "<strong class=\"align-top\">URL: </strong>"
msgstr "<strong class=\"align-top\">URL: </strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid ""
"<strong class=\"o_categories_collapse_title text-"
"uppercase\">Categories</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "<strong>Add to Cart</strong>"
msgstr "<strong>장바구니에 담기</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                    If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>해당되는 결제 옵션을 찾을 수 없습니다.</strong><br/>\n"
"오류가 발생한 경우, 웹사이트 관리자에게 문의하시기 바랍니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Payment Information:</strong>"
msgstr "<strong>결제 정보 :</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<strong>Total:</strong>"
msgstr "<strong>소계 :</strong>"

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Cart</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">S00060</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    Would you like to complete your purchase?<br/><br/>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr/>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Desk Combination</strong><br/><t t-out=\"line.name or ''\">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Units</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr/>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Resume order\n"
"                        </a>\n"
"                    </div>\n"
"                    <div style=\"text-align: center;\"><strong>Thank you for shopping with <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\" t-out=\"company.name or ''\">My Company (San Francisco)</td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"company.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"company.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings__terms_url
msgid "A preview will be available at this URL."
msgstr "이 URL에서는 미리보기를 사용할 수 있습니다."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid ""
"A product can be either a physical product or a service that you sell to "
"your customers."
msgstr "상품은 실제 상품이거나 고객에게 판매하는 서비스일 수 있습니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "A short description that will also appear on documents."
msgstr "문서에 표시되는 간단한 설명입니다."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "AT A GLANCE"
msgstr "한눈에"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Abandoned"
msgstr "취소함"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__is_abandoned_cart
msgid "Abandoned Cart"
msgstr "취소한 장바구니"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/models/crm_team.py:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_view_abandoned_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
#, python-format
msgid "Abandoned Carts"
msgstr "취소한 장바구니"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_view_kanban_dashboard
msgid "Abandoned Carts to Recover"
msgstr "복구된 취소한 장바구니"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_abandoned_delay
#: model:ir.model.fields,field_description:website_sale.field_website__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr "취소 지연"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Abandoned carts are all carts left unconfirmed by website visitors. You can "
"find them in *Website > Orders > Abandoned Carts*. From there you can send "
"recovery emails to visitors who entered their contact details."
msgstr ""
"취소한 장바구니는 웹 사이트 방문자가 승인하지 않은 모든 장바구니입니다. *웹사이트 >  주문 > 취소한 장바구니*에서 찾을 수 "
"있습니다. 여기에서 연락처 세부 정보를 입력한 방문자에게는 복구 이메일을 보낼 수 있습니다."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_snippet_filter__product_cross_selling
msgid "About cross selling products"
msgstr "교차 판매 품목 정보"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Acceptable file size"
msgstr "허용되는 파일 크기"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_accessories
msgid "Accessories for Product"
msgstr "품목별 액세서리"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before payment "
"(cross-sell strategy)."
msgstr "고객이 결제 전에 장바구니를 검토하면 액세서리가 표시됩니다 (교차 판매 전략)."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__accessory_product_ids
msgid "Accessory Products"
msgstr "엑세서리 제품"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "계정 과목"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Add a Media"
msgstr "이미지나 동영상 추가"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Add one"
msgstr "하나 추가하기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add price per base unit of measure on your Products"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
msgid "Add to Cart"
msgstr "장바구니에 담기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
msgid "Add to Cart <i class=\"fa fa-fw fa-shopping-cart\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Address"
msgstr "주소"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "All Products"
msgstr "모든 품목"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__all_pricelist_ids
msgid "All pricelists"
msgstr "전체 가격표"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_utils.xml:0
#, python-format
msgid "All results"
msgstr "전체 결과"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr "구매자가 제품 속성을 기반으로 제품을 비교할 수 있도록 허용"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__selectable
msgid "Allow the end user to choose this price list"
msgstr "최종 사용자가 이 가격표를 선택할 수 있게 허용"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__alternative_product_ids
msgid "Alternative Products"
msgstr "대체 제품"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.recommended_products
msgid "Alternative Products:"
msgstr "대체 제품 :"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr "취소된 장바구니의 금액"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "적용"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr "판매 주문 내역에 수동으로 할인을 적용하거나 가격표에서 계산된 할인을 표시합니다(가격표 환경 설정에서 활성화하는 옵션)."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Apply specific prices per country, discounts, etc."
msgstr "국가별 가격, 할인 등 특정 가격 적용"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment of online orders"
msgstr "온라인 주문 배정"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Attributes"
msgstr "속성"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Average Order"
msgstr "평균 주문"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "은행명"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_count
msgid "Base Unit Count"
msgstr "기본 단위 수"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_name
msgid "Base Unit Name"
msgstr "기본 유닛 이름"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_show_uom_price
msgid "Base Unit Price"
msgstr "기본 단위 가격"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.base_unit_action
#: model:ir.ui.menu,name:website_sale.website_base_unit_menu
msgid "Base Units"
msgstr "기본 단위"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Be aware!"
msgstr "알아 두세요!"

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr "베네룩스"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr "베네룩스"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Best Sellers"
msgstr "베스트셀러"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Billing"
msgstr "청구"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing Address"
msgstr "청구지 주소"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Bin"
msgstr "휴지통"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins
msgid "Bins"
msgstr "쓰레기통"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""
"프로모션과 쿠폰코드, 이 두가지의 할인 프로그램으로 여러분의 판매를 촉진하세요. 특별한 조건이 세팅됩니다(품목, 고객, 최소 구매금액, "
"기간). 할인(% 또는 금액) 또는 무료 상품증정으로 보상할 수 있습니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Bottom"
msgstr "맨 아래"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Box"
msgstr "상자"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes
msgid "Boxes"
msgstr "박스"

#. module: website_sale
#: model:product.attribute,name:website_sale.product_attribute_brand
msgid "Brand"
msgstr "상표"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets
msgid "Cabinets"
msgstr "캐비넷"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Campaigns"
msgstr "캠페인"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "1024 이미지로 확대 가능"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__can_publish
msgid "Can Publish"
msgstr "게시 가능"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Can be used as payment toward future orders."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Capture order payments when the delivery is completed."
msgstr "배송이 완료되면 주문 결제를 캡처합니다."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr "장바구니 수량"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_recovery_mail_template
#: model:ir.model.fields,field_description:website_sale.field_website__cart_recovery_mail_template_id
msgid "Cart Recovery Email"
msgstr "장바구니 복구 이메일"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Cart is abandoned after"
msgstr "다음 이후는 장바구니가 취소됩니다"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr "장바구니 복구 이메일로 이미 전송되었습니다"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Carts"
msgstr "장바구니"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Carts are flagged as abandoned after this delay."
msgstr "이 지연 이후에는 장바구니는 취소된 것으로 표시됩니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Categories"
msgstr "범주"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""
"범주는 터치 스크린 인터페이스를 통해 상품을\n"
"            탐색하는데 사용됩니다."

#. module: website_sale
#: code:addons/website_sale/models/product_template.py:0
#, python-format
msgid "Categories:"
msgstr "카테고리:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Category"
msgstr "카테고리"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_description
msgid "Category Description"
msgstr "범주 설명"

#. module: website_sale
#: code:addons/website_sale/models/product_template.py:0
#, python-format
msgid "Category:"
msgstr "카테고리:"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Chair"
msgstr "의자"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_chairs
msgid "Chairs"
msgstr "의자"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr "계정에 대한 문서가 발급되면 VAT 번호를 변경할 수 없습니다. 이 경우 당사에 직접 연락해 주십시오."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr "귀하의 계정에 대한 문서가 발급되면 회사 이름을 변경할 수 없습니다. 이 경우 당사에 직접 연락해 주십시오."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once documents have been issued for your "
"account. Please contact us directly for this operation."
msgstr "사용자 계정으로 문서가 발행된 후에는 이름을 변경할 수 없습니다. 이 작업은 저희에게 직접 문의 주세요."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__child_id
msgid "Children Categories"
msgstr "하위 범주"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr "계정에 청구서를 발행하고 나면 이름을 변경할 수 없습니다. 이 경우, 당사에 직접 연락해주십시오."

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_open_website_sale_onboarding_payment_acquirer_wizard
msgid "Choose a payment method"
msgstr "결제 방법 선택"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr "크리스마스"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "시/군/구"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr "첫 번째 제품을 만들려면 오른쪽 상단의 \"새로 만들기\"를 클릭하십시오."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr "여기를 클릭하세요"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on <em>Continue</em> to create the product."
msgstr "<em>다음</em>를 클릭하여 상품을 만듭니다."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on this button so your customers can see it."
msgstr "고객이 볼 수 있도록 이 버튼을 클릭하십시오."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Comma-separated list of parts of product names"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_company
msgid "Companies"
msgstr "회사"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
#, python-format
msgid "Company Name"
msgstr "회사명"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_components
msgid "Components"
msgstr "구성 요소"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Easypost"
msgstr "Easypost와 연동하여 배송 비용과 운송건을 계산합니다"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "DHL로 배송비 계산 및 배송"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "FedEx로 배송비 계산 및 배송"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "UPS로 배송비 계산 및 배송"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "USPS로 배송비 계산 및 배송"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "bpost로 배송비 계산 및 배송"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "주문에 따른 배송 비용을 계산합니다"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Confirm Order"
msgstr "주문 확인"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Confirm orders when you get paid."
msgstr "결제 후에 주문 확인."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed"
msgstr "확인됨"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Confirmed Orders"
msgstr "확인된 주문"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Conversion"
msgstr "변환"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_couches
msgid "Couches"
msgstr "소파"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "국가"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid "Create a new product"
msgstr "신규 품목 만들기"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__create_uid
msgid "Created by"
msgstr "작성자"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__create_date
msgid "Created on"
msgstr "작성일자"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Creation Date"
msgstr "작성일자"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Current Category or All"
msgstr "현재 카테고리 또는 전체"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_id
msgid "Custom Unit of Measure"
msgstr "맞춤 측정 단위"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer"
msgstr "고객"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer Country"
msgstr "고객 국가"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "Customer Reviews"
msgstr "고객 리뷰"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
msgid "Customers"
msgstr "고객"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Express Connector"
msgstr "DHL 익스프레스 배송업체"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__currency_id
msgid "Default Currency"
msgstr "기본 통화"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_id
msgid "Default Pricelist"
msgstr "기본 가격표"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_id
#: model:ir.model.fields,help:website_sale.field_website_base_unit__name
msgid ""
"Define a custom unit to display in the price per unit of measure field."
msgstr "측정 단위 당 가격 필드에 표시할 사용자 지정 단위를 설정합니다."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Define a new category"
msgstr "새 범주 정의"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Delete"
msgstr "삭제"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__internal
msgid ""
"Delivery methods are only used internally: the customer doesn't pay for "
"shipping costs"
msgstr "배송 방법 내부 전용 : 고객이 배송비를 지불하지 않습니다"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__website
msgid ""
"Delivery methods are selectable on the website: the customer pays for "
"shipping costs"
msgstr "배송 방법 웹 사이트에서 선택 가능 : 고객이 배송비를 지불합니다"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Description"
msgstr "설명"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_description
msgid "Description for the website"
msgstr "웹 사이트에 대한 설명"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks
msgid "Desks"
msgstr "책상"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr "웹사이트 전자 상거래 표시 순서 결정"

#. module: website_sale
#: model:ir.model,name:website_sale.model_digest_digest
msgid "Digest"
msgstr "요약"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_digital
msgid "Digital Content"
msgstr "디지털 컨텐츠"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__display_name
msgid "Display Name"
msgstr "표시명"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display a prompt with optional products when adding to cart"
msgstr "장바구니에 추가할 때 옵션 제품과 함께 화면에 표시"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_count
msgid ""
"Display base unit price on your eCommerce pages. Set to 0 to hide it for "
"this product."
msgstr "이커머스 페이지에 기본 단가를 표시합니다. 이 품목에 대해서 내용을 숨기려면 값을 0으로 설정하십시오."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Displayed in bottom of product pages"
msgstr "관련 품목 페이지의 하단에 표시됩니다"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_name
msgid ""
"Displays the custom unit for the products if defined or the selected unit of"
" measure otherwise."
msgstr "설정한 경우에는 품목에 사용자가 정의한 단위를 표시하고 그렇지 않은 경우 선택한 측정 단위를 표시합니다."

#. module: website_sale
#: code:addons/website_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "사용 권한 없음, 이 데이터는 사용자 요약 이메일에서 무시됩니다"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__done
msgid "Done"
msgstr "완료"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Double click here to set an image describing your product."
msgstr "제품을 설명하는 이미지를 설정하려면 여기를 두 번 클릭하십시오."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Drag building blocks here to customize the header for \""
msgstr "다음에 대한 헤더를 사용자 정의하려면 제작 블록을 여기에 끌어다 놓습니다 \""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr "이 웹 사이트 블록을 끌어서 귀하의 페이지에 놓으세요."

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Drawer"
msgstr "서랍장"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers
msgid "Drawers"
msgstr "서랍"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_extra_field_ids
msgid "E-Commerce Extra Fields"
msgstr "이커머스 추가 필드"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_extra_field
msgid "E-Commerce Extra Info Shown on product page"
msgstr "품목 페이지에 표시되어 있는 이커머스 추가 정보"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__code
msgid "E-commerce Promotional Code"
msgstr "전자 상거래 프로모션 코드"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr "유로"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost"
msgstr "Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Edit"
msgstr "편집하기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form_web_terms
msgid "Edit in Website Builder"
msgstr "웹사이트 빌더에서 편집하기"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr "금액을 클릭하여 이 상품의 가격을 편집하십시오."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr "주소 편집하기"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_email_account
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Email"
msgstr "이메일"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email Template"
msgstr "이메일 서식"

#. module: website_sale
#: model:ir.model,name:website_sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "이메일 구성 마법사"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email sent to the customer after the checkout"
msgstr "결제 후 이메일이 고객에게 전송됩니다"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__embed_code
msgid "Embed Code"
msgstr "퍼가기 코드"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Enter a name for your new product"
msgstr "새 상품의 이름을 입력하십시오"

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "오류! 재귀적 분류를 작성할 수 없습니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
msgid "Extra Info"
msgstr "추가 정보"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_template_image_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__product_template_image_ids
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Extra Product Media"
msgstr "추가 제품 미디어"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_variant_image_ids
msgid "Extra Variant Images"
msgstr "추가 변형 이미지"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
msgid "Extra Variant Media"
msgstr "추가 변형 미디어"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Featured"
msgstr "추천"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr "FedEx"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__field_id
msgid "Field"
msgstr "필드"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__label
msgid "Field Label"
msgstr "필드 꼬리표"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__name
msgid "Field Name"
msgstr "필드명"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr "웹 사이트에서"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures
msgid "Furnitures"
msgstr "가구"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Generate an invoice from orders ready for invoicing."
msgstr "청구서를 받을 준비가 된 주문에서 청구서를 생성합니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "온라인 결제가 확인되면 자동으로 청구서 생성"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_gift_card
msgid "Gift Card"
msgstr "기프트 카드"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "상품 분류 목록을 표시할 때 주문 순서를 제공합니다."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Go to cart"
msgstr "장바구니로 이동"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "판매 주문 내역에 할인 적용"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
msgid "Grid"
msgstr "그리드"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Group By"
msgstr "그룹별"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 라우팅"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__hidden
msgid "Hidden"
msgstr "숨김"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Huge file size. The image should be optimized/reduced."
msgstr "파일 크기가 너무 큽니다. 이미지를 최적화/축소해야 합니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "I agree to the"
msgstr "대해 동의합니다"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "I have a promo code"
msgstr "프로모션 코드가 있습니다"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__id
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__id
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__id
msgid "ID"
msgstr "ID"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1920
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Image"
msgstr "이미지"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1024
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1024
msgid "Image 1024"
msgstr "1024 이미지"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_128
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_128
msgid "Image 128"
msgstr "128 이미지"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_256
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_256
msgid "Image 256"
msgstr "256 이미지"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_512
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_512
msgid "Image 512"
msgstr "512 이미지"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr "이미지 이름"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"If you are ordering for an external person, please place your order via the "
"backend. If you wish to change your name or email address, please do so in "
"the account settings or contact your administrator."
msgstr ""
"외부인을 위한 주문은 백엔드를 통해 생성할 수 있습니다. 이름이나 이메일 주소를 수정하려면 계정 설정에서 변경하거나 관리자에게 문의하시기"
" 바랍니다."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "이메일이 잘못되었습니다! 유효한 이메일 주소를 입력하세요."

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_invoices_ecommerce
msgid "Invoices"
msgstr "청구서"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_account
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "청구서 발행"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_published
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_website_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Is Published"
msgstr "게시 여부"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr "고객에게 청구서 발행"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "It is forbidden to modify a sales order which is not in draft status."
msgstr "초안 상태가 아닌 판매 주문을 수정하는 것은 금지되어 있습니다."

#. module: website_sale
#: model:ir.model,name:website_sale.model_account_move
msgid "Journal Entry"
msgstr "분개"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__just_done
msgid "Just done"
msgstr "즉시 완료"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total_value
msgid "Kpi Website Sale Total Value"
msgstr "Kpi 웹사이트 판매 총액"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lamp"
msgstr "스탠드"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps
msgid "Lamps"
msgstr "램프"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image____last_update
#: model:ir.model.fields,field_description:website_sale.field_product_public_category____last_update
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Month"
msgstr "전 월"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner__last_website_so_id
#: model:ir.model.fields,field_description:website_sale.field_res_users__last_website_so_id
msgid "Last Online Sales Order"
msgstr "최근 온라인 판매 주문"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Week"
msgstr "지난 주"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Year"
msgstr "지난 해"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Left"
msgstr "왼쪽"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let returning shoppers save products in a wishlist"
msgstr "재 방문자가 위시리스트에 제품을 저장하도록 허용"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a shipping address"
msgstr "고객이 배송 주소를 입력하게 합니다"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Let's create your first product."
msgstr "첫 번째 상품을 만들어 보겠습니다."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Let's now take a look at your administration dashboard to get your eCommerce"
" website ready in no time."
msgstr "전자상 거래 웹 사이트를 즉시 준비하기 위해 이제 관리 현황판을 봅니다."

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lightbulb sold separately"
msgstr "전구 별도 판매"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "연결된 주문 명세"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
msgid "List"
msgstr "목록"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Log In"
msgstr "로그인"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr "프로모션 &amp; 쿠폰프로그램 관리"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Medium"
msgstr "중간"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "판매자 계정의 ID"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "방법"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia
msgid "Multimedia"
msgstr "멀티미디어"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
#, python-format
msgid "My Cart"
msgstr "나의 장바구니"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__name
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Name"
msgstr "이름"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__name_short
msgid "Name Short"
msgstr "단축 이름"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/controllers/main.py:0
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "New Product"
msgstr "새 상품"

#. module: website_sale
#: model:product.ribbon,html:website_sale.new_ribbon
msgid "New!"
msgstr "신규!"

#. module: website_sale
#: model:ir.filters,name:website_sale.dynamic_snippet_newest_products_filter
#: model:website.snippet.filter,name:website_sale.dynamic_filter_newest_products
msgid "Newest Products"
msgstr "최신 상품"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Newest arrivals"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Next <span class=\"fa fa-chevron-right\"/>"
msgstr "다음 <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "No abandoned carts found"
msgstr "취소한 장바구니가 없습니다"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined"
msgstr "정의된 상품이 없습니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined in category \""
msgstr "다음 범주로 정의된 상품이 없습니다 \""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.website_sale_visitor_product_action
msgid "No product views yet for this visitor"
msgstr "이 방문자에 대한 제품 보기가 아직 없습니다"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "No redirect when the user adds a product to cart."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results"
msgstr "결과가 없습니다"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results for \""
msgstr "다음에 대한 결과가 없습니다 \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results found for '"
msgstr "' 에 대한 결과를 찾을 수 없습니다."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_utils.xml:0
#, python-format
msgid "No results found. Please try another search."
msgstr "검색 결과가 없습니다. 다른 검색을 시도하십시오."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__none
msgid "No shipping management on website"
msgstr "웹 사이트에서 배송관리 없음"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "None"
msgstr "없음"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__not_done
msgid "Not done"
msgstr "미완료"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr "취소된 장바구니의 수"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Number of Columns"
msgstr "단 수"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppr
msgid "Number of grid columns on the shop"
msgstr "상점의 그리드 열 수"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings__cart_abandoned_delay
msgid "Number of hours after which the cart is considered abandoned."
msgstr "장바구니가 취소된 것으로 간주되는 시간"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Number of products"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppg
msgid "Number of products in the grid on the shop"
msgstr "상점의 그리드에 있는 제품 수"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "On wheels"
msgstr "차량"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr "<b>저장</b>을 클릭하면 상품이 업데이트됩니다."

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_report_sales
msgid "Online Sales"
msgstr "온라인 판매"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
msgid "Online Sales Analysis"
msgstr "온라인 판매 분석"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__only_services
msgid "Only Services"
msgstr "서비스 전용"

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid ""
"Only the company's websites are allowed.\n"
"Leave the Company field empty or select a website from that company."
msgstr ""
"회사 웹사이트만 사용할 수 있습니다.\n"
"회사 필드를 공란으로 두거나 해당 회사의 웹사이트를 선택하세요."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Open Source eCommerce"
msgstr "전자상거래 소스 열기"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Open your website app here."
msgstr "여기에 웹 사이트 앱을 여십시오."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid ""
"Optimization required! Reduce the image size or increase your compression "
"settings."
msgstr "최적화 필요! 이미지 크기를 줄이거나 압축 설정을 높이십시오."

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Option for: %s"
msgstr "선택 사항 : %s"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Option: %s"
msgstr "선택사항 : %s"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Optional Products"
msgstr "옵션 품목"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__option_line_ids
msgid "Options Linked"
msgstr "연결 옵션"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr "아니면 은행앱으로 스캔하십시오."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_unpaid
msgid "Order Date"
msgstr "주문일"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_order_line
msgid "Order Lines displayed on Website"
msgstr "웹 사이트에 주문 라인 표시"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_order_line
msgid ""
"Order Lines to be displayed on the website. They should not be used for "
"computation purpose."
msgstr "웹사이트에 주문 라인이 표시됩니다. 계산 목적으로 사용 되어서는 안됩니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.short_cart_summary
msgid "Order Total"
msgstr "주문 합계"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
#, python-format
msgid "Orders"
msgstr "주문"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Orders Followup"
msgstr "주문 후속 조치"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr "송장 주문서"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Orders to Invoice"
msgstr "청구서 발행할 주문"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Orders/Day"
msgstr "주문/일"

#. module: website_sale
#: model:product.ribbon,html:website_sale.out_of_stock_ribbon
msgid "Out of stock"
msgstr "품절"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "PDT 식별 토큰"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_page
msgid "Page"
msgstr "페이지"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_id
msgid "Parent Category"
msgstr "상위 분류"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_path
msgid "Parent Path"
msgstr "상위 경로"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parents_and_self
msgid "Parents And Self"
msgstr "상위 또는 자신"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay Now"
msgstr "지금 결제"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay with"
msgstr "결제 방법"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "결제 매입사"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_acquirers
msgid "Payment Acquirers"
msgstr "결제 매입사"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_icons
msgid "Payment Icons"
msgstr "결제 아이콘"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "결제 안내"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "결제 방법"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_tokens
msgid "Payment Tokens"
msgstr "결제 토큰"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_transactions
msgid "Payment Transactions"
msgstr "결제 거래"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Payments to Capture"
msgstr "캡처 할 지불"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "페이팔 사용자 유형"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Pedal-based opening system"
msgstr "페달형 개방 시스템"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "전화번호"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Phone Number"
msgstr "전화번호"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Please enter a valid Video URL."
msgstr "유효한 비디오 URL을 입력하십시오."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr "현재 장바구니를 진행하십시요."

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Month"
msgstr "지난 달"

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Week"
msgstr "지난 주"

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Year"
msgstr "지난 해"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_price
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price"
msgstr "가격"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price (high to low)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price (low to high)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Price - High to Low"
msgstr "높은 가격 순"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Price - Low to High"
msgstr "낮은 가격 순"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_price
msgid "Price Per Unit"
msgstr "단위 당 가격"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "이 전자 상거래/웹 사이트에서 사용할 수있는 가격표"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "가격표"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_product_pricelist3
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "가격표"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "가격 책정"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Print"
msgstr "인쇄"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "Process the order once the payment is received."
msgstr "결제가 완료되면 주문을 처리합니다."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.model,name:website_sale.model_product_product
#: model:ir.model.fields,field_description:website_sale.field_website_track__product_id
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#, python-format
msgid "Product"
msgstr "품목"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_accessories_action
msgid "Product Accessories"
msgstr "상품 액세서리"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "품목 속성"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Product Category"
msgstr "품목 카테고리"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_comparison
msgid "Product Comparison Tool"
msgstr "상품 비교 도구"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "Product Image"
msgstr "품목 이미지"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr "상품 이미지"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.product
#, python-format
msgid "Product Name"
msgstr "품목 명"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_website_sale_website_form
msgid "Product Page Extra Fields"
msgstr "상품 페이지 추가 필드"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr "상품 가격"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr "상품 일반 범주"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_tmpl_id
msgid "Product Template"
msgstr "품목 양식"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "품목 양식 속성 내역"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__product_tmpl_ids
msgid "Product Tmpl"
msgstr "상품 서식"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_variant_id
msgid "Product Variant"
msgstr "파생 품목"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_variants
msgid "Product Variants"
msgstr "파생 품목"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__visitor_product_count
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
msgid "Product Views"
msgstr "상품 조회수"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_visitor_product_action
msgid "Product Views History"
msgstr "상품 조회수 기록"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Product names"
msgstr "상품명"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product prices displaying in web catalog"
msgstr "웹 상품안내서에 표시되는 상품 가격"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_ribbon
msgid "Product ribbon"
msgstr "상품 리본"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_settings
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.products_breadcrumb
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_tree
msgid "Products"
msgstr "품목"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_recently_sold_with_action
msgid "Products Recently Sold With"
msgstr "최근에 판매된 품목"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_recently_sold_with
msgid "Products Recently Sold With Product"
msgstr "제품과 함께 최근에 판매된 제품"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_count
msgid "Products Views"
msgstr "제품 조회수"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Provide customers with product-specific links or downloadable content in the"
" confirmation page of the checkout process if the payment gets through. To "
"do so, attach some files to a product using the new Files button and publish"
" them."
msgstr ""
"결제가 끝나면 결제 절차의 승인 페이지에서 제품별 링크 또는 다운로드할 수있는 내용을 고객에게 제공하십시오.이렇게 하려면, 새 파일 "
"버튼을 사용하여 일부 파일을 제품에 첨부하고 게시하십시오."

#. module: website_sale
#: code:addons/website_sale/models/product_image.py:0
#, python-format
msgid ""
"Provided video URL for '%s' is not valid. Please enter a valid video URL."
msgstr "제공된 동영상 URL '%s'이 유효하지 않습니다. 유효한 동영상 URL을 입력하십시오."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
msgid "Published"
msgstr "게시 됨"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push down"
msgstr "아래로 내리기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to bottom"
msgstr "맨아래로 내리기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to top"
msgstr "맨위로 올리기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push up"
msgstr "위로 올리기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Qty:"
msgstr "수량 :"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#, python-format
msgid "Quantity"
msgstr "수량"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "REVENUE BY"
msgstr "수익"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_ids
msgid "Rating"
msgstr "평가"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg
msgid "Rating Average"
msgstr "평균 평가"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "최근 의견의 평가"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_image
msgid "Rating Last Image"
msgstr "최근 이미지 평가"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_value
msgid "Rating Last Value"
msgstr "최근 값 평가"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_count
msgid "Rating count"
msgstr "평가 수 "

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__rating_last_feedback
msgid "Reason of the rating"
msgstr "평가 사유"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_sold_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_sold_products
msgid "Recently Sold Products"
msgstr "최근 판매된 품목"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_viewed_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_viewed_products
msgid "Recently Viewed Products"
msgstr "최근 본 품목"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email Sent"
msgstr "복구 이메일로 보내기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email to Send"
msgstr "복구 할 이메일 보내기"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Reinforced for heavy loads"
msgstr "물량이 과다할 경우 보충"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "Remove from cart"
msgstr "장바구니에서 제거하기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Remove one"
msgstr "하나만 제거하기"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_reporting
msgid "Reporting"
msgstr "보고"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_id
#: model:ir.model.fields,help:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,help:website_sale.field_product_template__website_id
msgid "Restrict publishing to this website."
msgstr "이 웹 사이트로의 게시를 제한합니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Review Order"
msgstr "주문 검토"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_ribbon_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_ribbon_id
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Ribbon"
msgstr "리본"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__bg_color
msgid "Ribbon background color"
msgstr "리본 배경 색상"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html_class
msgid "Ribbon class"
msgstr "리본 종류"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html
msgid "Ribbon html"
msgstr "리본 HTML"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__text_color
msgid "Ribbon text color"
msgstr "리본 텍스트 색상"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Right"
msgstr "오른쪽"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__is_seo_optimized
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO 최적화"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sale_ribbon
msgid "Sale"
msgstr "매출"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Analysis"
msgstr "판매 분석"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model_terms:ir.ui.view,arch_db:website_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#, python-format
msgid "Sales"
msgstr "판매"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Analysis"
msgstr "판매 분석"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "판매 분석 보고서"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "판매 주문"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "판매 주문 내역"

#. module: website_sale
#: model:mail.template,name:website_sale.mail_template_sale_cart_recovery
msgid "Sales Order: Cart Recovery Email"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Month"
msgstr "지난 달 이후 판매"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Week"
msgstr "지난 주 이후 판매"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Year"
msgstr "작년 이후 판매"

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "영업팀"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesperson_id
msgid "Salesperson"
msgstr "영업사원"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_1
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_image
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_name
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_price
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "Sample"
msgstr "견본"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Search Abandoned Sales Orders"
msgstr "취소한 장바구니 주문서 검색"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr "<b>새 상품</b>을 선택하여 만들고 속성을 관리하여 매출을 높입니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Select this address"
msgstr "이 주소 선택"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__selectable
msgid "Selectable"
msgstr "선택 가능"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell content to download or URL links"
msgstr "다운로드 할 컨텐츠 또는 URL 링크 판매"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr "속성 (크기, 색상 등)을 사용하여 파생 품목 판매"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_actions_server_sale_cart_recovery_email
msgid "Send a Cart Recovery Email"
msgstr "장바구니 복구 이메일 보내기"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr "복구 이메일 보내기"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Send a recovery email to visitors who haven't completed their order."
msgstr "주문을 완료하지 않은 방문객에게 복구 이메일을 보냅니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send a recovery email when a cart is abandoned"
msgstr "장바구니가 취소되면 복구 이메일로 보내기"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__seo_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__seo_name
msgid "Seo name"
msgstr "Seo 이름"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__sequence
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__sequence
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Sequence"
msgstr "순서"

#. module: website_sale
#: model:product.public.category,name:website_sale.services
msgid "Services"
msgstr "서비스"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Ship to the same address\n"
"                                                    <span class=\"ship_to_other text-muted\" style=\"display: none\">&amp;nbsp;(<i>Your shipping address will be requested later) </i></span>"
msgstr ""
"같은 주소로 배송\n"
"                                                    <span class=\"ship_to_other text-muted\" style=\"display: none\">&amp;nbsp;(<i>배송지 주소는 나중에 요청합니다) </i></span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping"
msgstr "배송"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_delivery_invoice_address
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping Address"
msgstr "배송 주소"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr "배송 비용"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__sale_delivery_settings
msgid "Shipping Management"
msgstr "배송 관리"

#. module: website_sale
#: model:website.menu,name:website_sale.menu_shop
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Shop"
msgstr "쇼핑"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "쇼핑 - 결제"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "쇼핑 - 확인"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Acquirer"
msgstr "Shop - 결제방식 선택"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_add_to_cart
msgid "Shopping cart"
msgstr "쇼핑한 장바구니"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show Empty Cart"
msgstr "빈 장바구니 표시"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid "Show categories"
msgstr "범주 표시"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "Show options"
msgstr "선택 사항 표시"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr "가입"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Sit comfortably"
msgstr "편하게 앉아 있으세요"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Size"
msgstr "사이즈"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_x
msgid "Size X"
msgstr "사이즈 X"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_y
msgid "Size Y"
msgstr "사이즈 Y"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Slanted"
msgstr "비스듬하게 놓임"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sold"
msgstr "판매됨"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sold_out_ribbon
msgid "Sold out"
msgstr "품절"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Some required fields are empty."
msgstr "일부 필수 입력란은 비어 있습니다."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sources"
msgstr "출처"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr "시 / 도"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_company__website_sale_onboarding_payment_acquirer_state
msgid "State of the website sale onboarding payment acquirer step"
msgstr "웹사이트 판매 온보딩 결제 매입사 단계 상태"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Status"
msgstr "상태"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_add_on_page
#: model:ir.model.fields,field_description:website_sale.field_website__cart_add_on_page
msgid "Stay on page after adding to cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street 2"
msgstr "거리 2"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street <span class=\"d-none d-md-inline\"> and Number</span>"
msgstr "거리명 <span class=\"d-none d-md-inline\"> 및 번호</span>"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Stripe 공개 가능 키"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Stripe 비밀 키"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Subtotal:"
msgstr "소계 :"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__alternative_product_ids
msgid ""
"Suggest alternatives to your customer (upsell strategy). Those products show"
" up on the product page."
msgstr "고객에게 보다 비싼 대안을 제시합니다(연쇄판매 전략). 해당 상품은 상품 페이지에 표시됩니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested Accessories:"
msgstr "추천 액새서리:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Suggested accessories in the eCommerce cart"
msgstr "이커머스 장바구니에서 추천되는 액세서리"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid "VAT"
msgstr "부가가치세"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tag"
msgstr "태그"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Taxes:"
msgstr "세금 :"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website__all_pricelist_ids
msgid "Technical: Used to recompute pricelist_ids"
msgstr "기술 : pricelist_ids를 다시 계산하는 데 사용"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid "Terms and Conditions"
msgstr "이용 약관"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "구매해주셔서 감사합니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "The #1"
msgstr "#1"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The access token is invalid."
msgstr "유효하지 않은 액세스 토큰입니다."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has been updated. Please refresh the page."
msgstr "장바구니가 업데이트되었습니다. 페이지를 새로고침 하세요."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_url
#: model:ir.model.fields,help:website_sale.field_product_template__website_url
msgid "The full URL to access the document through the website."
msgstr "웹사이트를 통해 문서에 접근 하는 전체 URL입니다."

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given combination does not exist therefore it cannot be added to cart."
msgstr "주어진 조합이 존재하지 않으므로 장바구니에 추가할 수 없습니다."

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "The given product does not exist therefore it cannot be added to cart."
msgstr "주어진 상품이 존재하지 않으므로 장바구니에 추가할 수 없습니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr "여기에서 선택한 모드는 새로 생성 된 상품의 청구서 발행 정책으로 적용되지만 이미 생성 된 상품에는 적용되지 않습니다."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The order has been canceled."
msgstr "주문이 취소되었습니다."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template__public_categ_ids
msgid ""
"The product will be available in each mentioned eCommerce category. Go to "
"Shop > Customize and enable 'eCommerce categories' to view all eCommerce "
"categories."
msgstr ""
"이 상품은 언급된 각 전자 상거래 범주에서 사용할 수 있습니다. 쇼핑 > 사용자 정의로 이동하여 '전자 상거래 범주'를 활성화하여 모든 "
"전자 상거래 범주를 보십시오."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "The time to mark a cart as abandoned can be changed in the settings."
msgstr "장바구니가 취소된 것으로 표시하는 시간은 설정에서 변경할 수 있습니다."

#. module: website_sale
#: code:addons/website_sale/models/product_product.py:0
#, python-format
msgid ""
"The value of Base Unit Count must be greater than 0. Use 0 to hide the price"
" per unit on this product."
msgstr "기본 단위 수긔 값은 0보다 커야 합니다. 이 품목의 단위 당 가격을 숨기려면 0을 입력하십시오."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "There is no confirmed order from the website"
msgstr "웹사이트에서 확인된 주문이 없습니다"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "There is no recent confirmed order."
msgstr "최근 확인 된 주문이 없습니다."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "There is no unpaid order from the website yet"
msgstr "웹사이트에서 아직 지불하지 않은 주문이 없습니다."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "There isn't any UTM tag detected in orders"
msgstr "주문서에서 UTM 태그가 감지되지 않습니다"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr "이 조합은 존재하지 않습니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"This email template is suggested by default when you send a recovery email."
msgstr "이 이메일 서식은 복구 이메일을 보낼 때 기본적으로 제안됩니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr "현재 장바구니에 담으신 내용입니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product has no valid combination."
msgstr "이 품목에는 유효한 조합이 없습니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product is no longer available."
msgstr "이 상품은 더 이상 제공되지 않습니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "This product is unpublished."
msgstr "이 상품은 게시되지 않았습니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available."
msgstr "이 프로모션 코드는 사용할 수 없습니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Thumbnails Position"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__product_count
msgid "Total number of product viewed"
msgstr "상품 총조회수"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__visitor_product_count
msgid "Total number of views on products"
msgstr "제품 총조회수"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "True"
msgstr "참"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_snippet_filter__product_cross_selling
msgid ""
"True only for product filters that require a product_id because they relate "
"to cross selling"
msgstr "교차 판매와 관련되어 있으므로 product_id가 필수인 품목을 필터하는 경우에만 True 입니다."

#. module: website_sale
#: model:res.groups,name:website_sale.group_show_uom_price
msgid "UOM Price Display for eCommerce"
msgstr "이커머스용 측정 단위 당 가격"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr "UPS"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__terms_url
msgid "URL"
msgstr "URL"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_image__video_url
msgid "URL of a video for showcasing your product."
msgstr "상품을 소개하기 위한 동영상의 URL입니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr "USPS"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_base_unit
msgid "Unit of Measure for price per unit on eCommerce products."
msgstr "이커머스 품목의 단위 당 가격에 대한 측정 단위입니다."

#. module: website_sale
#: model:product.product,uom_name:website_sale.product_product_1
#: model:product.product,uom_name:website_sale.product_product_1b
#: model:product.template,uom_name:website_sale.product_product_1_product_template
msgid "Units"
msgstr "단위"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Unpaid"
msgstr "무급 휴가"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_unpaid_quotation_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
#, python-format
msgid "Unpaid Orders"
msgstr "미지불 주문"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Unpublished"
msgstr "게시 안 함"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Untaxed Total Sold"
msgstr "비과세의 판매액 합계"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Upload a file from your local library."
msgstr "로컬 라이브러리에서 파일을 업로드하십시오."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Validate"
msgstr "승인"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__video_url
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Video URL"
msgstr "동영상 URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "View Cart ("
msgstr "장바구니 보기 ("

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
msgid "View Product"
msgstr "품목 보기"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute__visibility
msgid "Visibility"
msgstr "표시"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__visible
msgid "Visible"
msgstr "표시 가능"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_published
msgid "Visible on current website"
msgstr "현재 웹 사이트에 공개"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_track
msgid "Visited Pages"
msgstr "방문한 페이지"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_kanban
msgid "Visited Products"
msgstr "살펴본 제품"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_graph
msgid "Visitor Product Views"
msgstr "방문자 상품 조회수"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_tree
msgid "Visitor Product Views History"
msgstr "방문자 상품 조회수 기록"

#. module: website_sale
#: model:product.product,name:website_sale.product_product_1
#: model:product.product,name:website_sale.product_product_1b
#: model:product.template,name:website_sale.product_product_1_product_template
msgid "Warranty"
msgstr "보증"

#. module: website_sale
#: model:product.product,description_sale:website_sale.product_product_1
#: model:product.product,description_sale:website_sale.product_product_1b
#: model:product.template,description_sale:website_sale.product_product_1_product_template
msgid ""
"Warranty, issued to the purchaser of an article by its manufacturer, "
"promising to repair or replace it if necessary within a specified period of "
"time."
msgstr "보증서. 제조사에 의해 구매자에게 문서로 발행됩니다. 지정된 기간 내에 필요한 경우 수리하거나 교환하는 것을 약속합니다."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_validate.js:0
#, python-format
msgid "We are waiting for confirmation from the bank or the payment provider"
msgstr "은행 또는 결제대행업체의 승인 대기 중입니다."

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_move__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_payment__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_report__website_id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__website_id
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Website"
msgstr "웹사이트"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_payment_acquirer_onboarding_wizard
msgid "Website Payment acquire onboarding wizard"
msgstr "웹사이트 결제 매입 온보딩 마법사"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__public_categ_ids
msgid "Website Product Category"
msgstr "웹 사이트 상품 카테고리"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "웹사이트 일반 범주"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_sequence
msgid "Website Sequence"
msgstr "웹 사이트 순서"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "웹 사이트 쇼핑"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "웹사이트 스니펫 필터"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_url
msgid "Website URL"
msgstr "웹 사이트 URL"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_visitor
msgid "Website Visitor"
msgstr "웹사이트 방문자"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_description
msgid "Website meta description"
msgstr "웹사이트 메타 설명"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_keywords
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_keywords
msgid "Website meta keywords"
msgstr "웹사이트 메타 핵심어"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_title
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_title
msgid "Website meta title"
msgstr "웹사이트 메타 제목"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_og_img
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_og_img
msgid "Website opengraph image"
msgstr "웹사이트 오픈그래프 이미지"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,help:website_sale.field_account_move__website_id
#: model:ir.model.fields,help:website_sale.field_account_payment__website_id
msgid "Website through which this invoice was created."
msgstr "이 청구서가 생성된 웹사이트입니다."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_id
msgid "Website through which this order was placed."
msgstr "주문이 완료된 웹사이트입니다."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__website_ids
msgid "Websites"
msgstr "웹 사이트"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_crm_team__website_ids
msgid "Websites using this Sales Team"
msgstr "이 영업 팀을 사용하는 웹사이트"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Whiteboard"
msgstr "화이트보드"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr "위시리스트"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"With the first mode you can set several prices in the product config form "
"(from Sales tab). With the second one, you set prices and computation rules "
"from Pricelists."
msgstr ""
"첫 번째 모드에서는 제품 설정 양식에서(판매 탭) 여러 가격을 설정할 수 있습니다. 두 번째 경우 가격 및 계산 규칙을 가격표에서 "
"설정합니다."

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid "With this action, '%s' website would not have any pricelist available."
msgstr "이 작업을 수행하면 '%s' 웹 사이트에 사용 가능한 가격표가 없습니다."

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "With three feet"
msgstr "3 피트"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"You are editing your <b>billing and shipping</b> addresses at the same time!<br/>\n"
"                                            If you want to modify your shipping address, create a"
msgstr ""
"<b>청구서 주소와 발송 주소</b>를 동시에 편집하고 있습니다!<br/>\n"
"                                         배송 주소를 수정하려면 다음을 작성하십시오."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website"
msgstr "웹사이트에서 주문한 것이 없습니다."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
msgid "You don't have any order to invoice from the website"
msgstr "웹 사이트에서 송장 주문이 없습니다"

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart!"
msgstr "장바구니에 물건이 남아 있습니다!"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid ""
"You'll find here all the carts abandoned by your visitors.\n"
"                If they completed their address, you should send them a recovery email!"
msgstr ""
"여기에 방문자가 취소한 모든 장바구니가 있습니다. \n"
"                 그들이 주소를 완성했다면 복구 이메일을 보내야합니다!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Your Address"
msgstr "귀하의 주소"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Your Address\n"
"                                        <small> or </small>"
msgstr ""
"귀하의 주소\n"
"                                        <small> or </small>"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Email"
msgstr "이메일"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Name"
msgstr "성명"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "Your cart is empty!"
msgstr "장바구니가 비어 있습니다!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr "이전 장바구니가 이미 완료 되었습니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr "우편번호"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr "bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "code..."
msgstr "코드..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "e.g. lamp,bin"
msgstr "예: 스탠드, 휴지통"

#. module: website_sale
#: code:addons/website_sale/models/website.py:0
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
#, python-format
msgid "eCommerce"
msgstr "전자 상거래"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
msgid "eCommerce Categories"
msgstr "전자 상거래 카테고리"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_attribute_view_form
msgid "eCommerce Filter Visibility"
msgstr "이커머스 필터 가시성"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total
msgid "eCommerce Sales"
msgstr "전자 상거래 판매"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_delivery
msgid "eCommerce Shipping Costs"
msgstr "전자 상거래 선적 비용"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce Shop"
msgstr "이커머스 온라인 상점"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.search_count_box
msgid "found)"
msgstr "찾음)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "hours."
msgstr "시간."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr "이전 장바구니를 현재 장바구니에 병합하려는 경우."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr "이전 장바구니를 복원하려는 경우 현재 카트가 이전 카트로 바뀝니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "in category \""
msgstr "다음 범주에서 \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "item(s))"
msgstr "아이템"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "new address"
msgstr "새 주소"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "terms &amp; conditions"
msgstr "이용 &amp; 약관"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "to follow your order."
msgstr "귀하의 주문에 따릅니다."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Background"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Mode"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Position"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Text"
msgstr ""
