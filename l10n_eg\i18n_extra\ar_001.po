# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_eg
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-18 10:29+0000\n"
"PO-Revision-Date: 2022-02-18 10:29+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_sale_base_fourteen
msgid "1. Standard Rated 14% (Base)"
msgstr "1. المبيعات الخاضعة لنسبة أساسية (أساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_sale_tax_fourteen
msgid "1. Standard Rated 14% (Tax)"
msgstr "1. المبيعات الخاضعة لنسبة أساسية (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_sale_base_fourteen
msgid "1. VAT 14% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_sale_tax_fourteen
msgid "1. VAT 14% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report,name:l10n_eg.tax_report_vat_return
msgid "1. VAT Return"
msgstr "اقرار ضريبة القيمة المضافة"

#. module: l10n_eg
#: model:account.tax.report,name:l10n_eg.tax_report_withholding_tax
msgid "2. Withholding Tax"
msgstr "ضرائب خصم المنبع"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_sale_base_zero
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_sale_base_zero
msgid "2. Zero Rated (Base)"
msgstr "2. المبيعات المحلية الخاضعة للنسبة الصفرية (أساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_sale_tax_zero
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_sale_tax_zero
msgid "2. Zero Rated (Tax)"
msgstr "2. المبيعات المحلية الخاضعة للنسبة الصفرية (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_sale_base_exempt
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_sale_base_exempt
msgid "3. Exempt Sales (Base)"
msgstr "3. المبيعات معفاة من الضريبة (أساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_sale_tax_exempt
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_sale_tax_exempt
msgid "3. Exempt Sales (Tax)"
msgstr "3. المبيعات معفاة من الضريبة (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report,name:l10n_eg.tax_report_schedule_tax
msgid "3. Schedule Tax"
msgstr "ضرائب الجدول"

#. module: l10n_eg
#: model:account.tax.report,name:l10n_eg.tax_report_other_taxes
msgid "4. Other Taxes"
msgstr "ضرائب اخرى"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_expense_base_fourteen
msgid "5. Standard Rated 14% Expenses (Base)"
msgstr "5. ضريبة القيمة المضافة على المشتريات (أساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_expense_tax_fourteen
msgid "5. Standard Rated 14% Expenses (Tax)"
msgstr "5. ضريبة القيمة المضافة على المشتريات (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_expense_base_fourteen
msgid "5. VAT 14% Expenses (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_expense_tax_fourteen
msgid "5. VAT 14% Expenses (Tax)"
msgstr "5. ضريبة القيمة المضافة على المشتريات (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_expense_base_zero
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_expense_base_zero
msgid "6. Zero Rated (Base)"
msgstr "2. المشتريات المحلية الخاضعة للنسبة الصفرية (أساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_expense_tax_zero
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_expense_tax_zero
msgid "6. Zero Rated (Tax)"
msgstr "6. المشتريات الخاضعة للنسبة الصفرية (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_expense_base_exempt
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_expense_base_exempt
msgid "7. Exempt Expenses (Base)"
msgstr "7. المشتريات معفاة من الضريبة (أساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_expense_tax_exempt
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_vat_return_expense_tax_exempt
msgid "7. Exempt Expenses (Tax)"
msgstr "7. المشتريات معفاة من الضريبة (ضريبة)"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_106009
#: model:account.account,name:l10n_eg.2_egy_account_106009
#: model:account.account.template,name:l10n_eg.egy_account_106009
msgid "Acc. Depreciation of Motor Vehicles"
msgstr "مجمع اهتلاك السيارات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_106008
#: model:account.account,name:l10n_eg.2_egy_account_106008
#: model:account.account.template,name:l10n_eg.egy_account_106008
msgid "Acc. Deprn.Computer Hardware & Software"
msgstr " مجمع اهتلاك الكمبيوترات و قطع الغيار و البرمجيات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_106007
#: model:account.account,name:l10n_eg.2_egy_account_106007
#: model:account.account.template,name:l10n_eg.egy_account_106007
msgid "Acc.Deprn.of Furniture & Office Equipment"
msgstr "مجمع اهتلاك اثاث و معدات المكتب"

#. module: l10n_eg
#: model:ir.model,name:l10n_eg.model_account_chart_template
msgid "Account Chart Template"
msgstr "نموذج مخطط الحساب "

#. module: l10n_eg
#: model:ir.model,name:l10n_eg.model_account_tax_report
msgid "Account Tax Report"
msgstr "حساب تقرير الضرائب"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_102011
#: model:account.account,name:l10n_eg.2_egy_account_102011
#: model:account.account.template,name:l10n_eg.egy_account_102011
msgid "Accounts Receivable"
msgstr "الذمم المدينة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_102012
#: model:account.account,name:l10n_eg.2_egy_account_102012
#: model:account.account.template,name:l10n_eg.egy_account_102012
msgid "Accounts Receivable (PoS)"
msgstr "ذمم مدينة (PoS)"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201014
#: model:account.account,name:l10n_eg.2_egy_account_201014
#: model:account.account.template,name:l10n_eg.egy_account_201014
msgid "Accrued - Audit Fees"
msgstr "اتعاب تدقيق مستحقة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201007
#: model:account.account,name:l10n_eg.2_egy_account_201007
#: model:account.account.template,name:l10n_eg.egy_account_201007
msgid "Accrued - Commissions"
msgstr "عمولة مستحقة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201015
#: model:account.account,name:l10n_eg.2_egy_account_201015
#: model:account.account.template,name:l10n_eg.egy_account_201015
msgid "Accrued - Office Rent"
msgstr "ايجار مكتب مستحق"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201004
#: model:account.account,name:l10n_eg.2_egy_account_201004
#: model:account.account.template,name:l10n_eg.egy_account_201004
msgid "Accrued - Salaries"
msgstr "الرواتب المستحقة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201013
#: model:account.account,name:l10n_eg.2_egy_account_201013
#: model:account.account.template,name:l10n_eg.egy_account_201013
msgid "Accrued - Sponsorship"
msgstr "تكفل مستحق"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201012
#: model:account.account,name:l10n_eg.2_egy_account_201012
#: model:account.account.template,name:l10n_eg.egy_account_201012
msgid "Accrued - Telephone"
msgstr "نتكاليف هاتف مستحقة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201011
#: model:account.account,name:l10n_eg.2_egy_account_201011
#: model:account.account.template,name:l10n_eg.egy_account_201011
msgid "Accrued - Utilities"
msgstr "فواتير مستحقة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201010
#: model:account.account,name:l10n_eg.2_egy_account_201010
#: model:account.account.template,name:l10n_eg.egy_account_201010
msgid "Accrued Other Personnel Cost"
msgstr "تكاليف موظفين مستحقة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201016
#: model:account.account,name:l10n_eg.2_egy_account_201016
#: model:account.account.template,name:l10n_eg.egy_account_201016
msgid "Accrued Others"
msgstr "اخرى مستحقة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201008
#: model:account.account,name:l10n_eg.2_egy_account_201008
#: model:account.account.template,name:l10n_eg.egy_account_201008
msgid "Accrued Salaries Increment"
msgstr "راتب اضافي مستحق"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201009
#: model:account.account,name:l10n_eg.2_egy_account_201009
#: model:account.account.template,name:l10n_eg.egy_account_201009
msgid "Accrued-Staff Bonus"
msgstr "مكافأة مستحقة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_100102
#: model:account.account,name:l10n_eg.2_egy_account_100102
#: model:account.account.template,name:l10n_eg.egy_account_100102
msgid "Accumulated Depreciation right use asset (IFRS 16)"
msgstr "الاستهلاك المتراكم استخدام حق الأصول (IFRS 16)"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500005
#: model:account.account,name:l10n_eg.2_egy_account_500005
#: model:account.account.template,name:l10n_eg.egy_account_500005
msgid "Advertising Income"
msgstr "دخل الإعلانات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400060
#: model:account.account,name:l10n_eg.2_egy_account_400060
#: model:account.account.template,name:l10n_eg.egy_account_400060
msgid "Air Miles Card Charges"
msgstr "مصروف رسوم بطاقة Air Miles "

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400024
#: model:account.account,name:l10n_eg.2_egy_account_400024
#: model:account.account.template,name:l10n_eg.egy_account_400024
msgid "Air tickets"
msgstr "مصاريف تذاكر طيران"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400037
#: model:account.account,name:l10n_eg.2_egy_account_400037
#: model:account.account.template,name:l10n_eg.egy_account_400037
msgid "Amortisation of Preoperating Expenses"
msgstr "مصروف إطفاء مصاريف "

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_106006
#: model:account.account,name:l10n_eg.2_egy_account_106006
#: model:account.account.template,name:l10n_eg.egy_account_106006
msgid "Amortisation on Leasehold Improvement"
msgstr "اطفاء على تحسين المستأجرات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400063
#: model:account.account,name:l10n_eg.2_egy_account_400063
#: model:account.account.template,name:l10n_eg.egy_account_400063
msgid "Amortization on Leasehold Improvement"
msgstr "مصروف إطفاء تحسينات المستأجرة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400029
#: model:account.account,name:l10n_eg.2_egy_account_400029
#: model:account.account.template,name:l10n_eg.egy_account_400029
msgid "Audit Fees"
msgstr " مصروف اتعاب تدقيق"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400074
#: model:account.account,name:l10n_eg.2_egy_account_400074
#: model:account.account.template,name:l10n_eg.egy_account_400074
msgid "Bad Debts"
msgstr " مصروف ديون معدومة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400059
#: model:account.account,name:l10n_eg.2_egy_account_400059
#: model:account.account.template,name:l10n_eg.egy_account_400059
msgid "Bank Finance & Loan Charges"
msgstr "مصروف بنك التمويل والقروض"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201001
#: model:account.account,name:l10n_eg.2_egy_account_201001
#: model:account.account.template,name:l10n_eg.egy_account_201001
msgid "Bank Suspense Account"
msgstr "حساب البنك المعلق"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400003
#: model:account.account,name:l10n_eg.2_egy_account_400003
#: model:account.account.template,name:l10n_eg.egy_account_400003
msgid "Basic Salary"
msgstr "مصروف الراتب الاساسي"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500006
#: model:account.account,name:l10n_eg.2_egy_account_500006
#: model:account.account.template,name:l10n_eg.egy_account_500006
msgid "Branding Income"
msgstr "دخل علامات تجارية"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500010
#: model:account.account,name:l10n_eg.2_egy_account_500010
#: model:account.account.template,name:l10n_eg.egy_account_500010
msgid "Capital Gain"
msgstr "مكاسب رأس المال"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_999002
#: model:account.account,name:l10n_eg.2_egy_account_999002
#: model:account.account.template,name:l10n_eg.egy_account_999002
msgid "Cash Difference Gain"
msgstr "مكاسب الفرق النقدي"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_999001
#: model:account.account,name:l10n_eg.2_egy_account_999001
#: model:account.account.template,name:l10n_eg.egy_account_999001
msgid "Cash Difference Loss"
msgstr "خسارة الفرق النقدي"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400038
#: model:account.account,name:l10n_eg.2_egy_account_400038
#: model:account.account.template,name:l10n_eg.egy_account_400038
msgid "Cash Shortage"
msgstr "مصروف نقص نقدي"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400044
#: model:account.account,name:l10n_eg.2_egy_account_400044
#: model:account.account.template,name:l10n_eg.egy_account_400044
msgid "Cleaning"
msgstr "مصروف تنظيف"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400069
#: model:account.account,name:l10n_eg.2_egy_account_400069
#: model:account.account.template,name:l10n_eg.egy_account_400069
msgid "Closing Account"
msgstr "حساب ختامي"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_106011
#: model:account.account,name:l10n_eg.2_egy_account_106011
#: model:account.account.template,name:l10n_eg.egy_account_106011
msgid "Computer Card Renewal"
msgstr "بطاقة تجديد كمبيوتر"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_106003
#: model:account.account,name:l10n_eg.2_egy_account_106003
#: model:account.account.template,name:l10n_eg.egy_account_106003
msgid "Computer Hardware & Software"
msgstr "الكمبيوترات و قطع الغيار و البرمجيات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400067
#: model:account.account,name:l10n_eg.2_egy_account_400067
#: model:account.account.template,name:l10n_eg.egy_account_400067
msgid "Consultancy Fees"
msgstr "رسوم الاستشارات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400049
#: model:account.account,name:l10n_eg.2_egy_account_400049
#: model:account.account.template,name:l10n_eg.egy_account_400049
msgid "Convoyance Expenses"
msgstr "مصروف نقل اصول"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400002
#: model:account.account,name:l10n_eg.2_egy_account_400002
#: model:account.account.template,name:l10n_eg.egy_account_400002
msgid "Cost Of Goods Sold I/C Sales"
msgstr "تكلفة البضاعة المباعة مع المبيعات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400001
#: model:account.account,name:l10n_eg.2_egy_account_400001
#: model:account.account.template,name:l10n_eg.egy_account_400001
msgid "Cost of Goods Sold in Trading"
msgstr "تكلفة البضاعة المباعة في التجارة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400021
#: model:account.account,name:l10n_eg.2_egy_account_400021
#: model:account.account.template,name:l10n_eg.egy_account_400021
msgid "Courrier"
msgstr "مصروف شحن"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400058
#: model:account.account,name:l10n_eg.2_egy_account_400058
#: model:account.account.template,name:l10n_eg.egy_account_400058
msgid "Credit Card Charges"
msgstr "مصروف رسوم بطاقات الائتمان"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400061
#: model:account.account,name:l10n_eg.2_egy_account_400061
#: model:account.account.template,name:l10n_eg.egy_account_400061
msgid "Credit Card Swipe Charges"
msgstr "مصروف رسوم بطاقات الائتمان"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201003
#: model:account.account,name:l10n_eg.2_egy_account_201003
#: model:account.account.template,name:l10n_eg.egy_account_201003
msgid "Credit Notes to Customers"
msgstr "اشعار دائن للعملاء"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201023
#: model:account.account,name:l10n_eg.2_egy_account_201023
#: model:account.account.template,name:l10n_eg.egy_account_201023
msgid "Customer Provision"
msgstr "مخصص الديون المشكوك في تحصيله"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201018
#: model:account.account,name:l10n_eg.2_egy_account_201018
#: model:account.account.template,name:l10n_eg.egy_account_201018
msgid "Deferred income"
msgstr "الإيرادات مؤجلة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104037
#: model:account.account,name:l10n_eg.2_egy_account_104037
#: model:account.account.template,name:l10n_eg.egy_account_104037
msgid "Deposit - Office Rent"
msgstr "رسوم تأمين - ايجار مكتبي"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104040
#: model:account.account,name:l10n_eg.2_egy_account_104040
#: model:account.account.template,name:l10n_eg.egy_account_104040
msgid "Deposit Others"
msgstr "رسوم تأمين - اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104038
#: model:account.account,name:l10n_eg.2_egy_account_104038
#: model:account.account.template,name:l10n_eg.egy_account_104038
msgid "Deposits - Customs"
msgstr "رسوم تأمين - جمارك"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400065
#: model:account.account,name:l10n_eg.2_egy_account_400065
#: model:account.account.template,name:l10n_eg.egy_account_400065
msgid "Depreciation Of Computer Hard & Soft"
msgstr "مصروف الاستهلاك اجهزة الكمبيوتر"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400064
#: model:account.account,name:l10n_eg.2_egy_account_400064
#: model:account.account.template,name:l10n_eg.egy_account_400064
msgid "Depreciation Of Furniture & Office Equipment"
msgstr "مصروف الاستهلاك  الأثاث"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400066
#: model:account.account,name:l10n_eg.2_egy_account_400066
#: model:account.account.template,name:l10n_eg.egy_account_400066
msgid "Depreciation Of Motor Vehicles"
msgstr "مصروف استهلاك المركبات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400070
#: model:account.account,name:l10n_eg.2_egy_account_400070
#: model:account.account.template,name:l10n_eg.egy_account_400070
msgid "Depreciation on right of use asset (IFRS 16)"
msgstr "الاستهلاك في حق الأصول استخدام (IFRS 16)"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400054
#: model:account.account,name:l10n_eg.2_egy_account_400054
#: model:account.account.template,name:l10n_eg.egy_account_400054
msgid "Disposal of Business Branch"
msgstr "مصروف وقف فرع من الاعمال"

#. module: l10n_eg
#: model:ir.model.fields,field_description:l10n_eg.field_account_tax__l10n_eg_eta_code
#: model:ir.model.fields,field_description:l10n_eg.field_account_tax_template__l10n_eg_eta_code
#: model:ir.model.fields,field_description:l10n_eg.field_l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code
msgid "ETA Code (Egypt)"
msgstr ""

#. module: l10n_eg
#: model:account.fiscal.position,name:l10n_eg.1_account_fiscal_position_egypt
#: model:account.fiscal.position,name:l10n_eg.2_account_fiscal_position_egypt
#: model:account.fiscal.position.template,name:l10n_eg.account_fiscal_position_egypt
msgid "Egypt"
msgstr ""

#. module: l10n_eg
#: model:account.chart.template,name:l10n_eg.egypt_chart_template_standard
msgid "Egypt Chart of Accounts - Standard"
msgstr ""

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400008
#: model:account.account,name:l10n_eg.2_egy_account_400008
#: model:account.account.template,name:l10n_eg.egy_account_400008
msgid "End Of Service Indemnity"
msgstr "مصروف نهاية الخدمة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_202001
#: model:account.account,name:l10n_eg.2_egy_account_202001
#: model:account.account.template,name:l10n_eg.egy_account_202001
msgid "End of Service Provision"
msgstr "مخصص نهاية الخدمة"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_exempt_purchase
#: model:account.tax,description:l10n_eg.1_eg_exempt_sale
#: model:account.tax,description:l10n_eg.2_eg_exempt_purchase
#: model:account.tax,description:l10n_eg.2_eg_exempt_sale
#: model:account.tax,name:l10n_eg.1_eg_exempt_purchase
#: model:account.tax,name:l10n_eg.1_eg_exempt_sale
#: model:account.tax,name:l10n_eg.2_eg_exempt_purchase
#: model:account.tax,name:l10n_eg.2_eg_exempt_sale
#: model:account.tax.template,description:l10n_eg.eg_exempt_purchase
#: model:account.tax.template,description:l10n_eg.eg_exempt_sale
#: model:account.tax.template,name:l10n_eg.eg_exempt_purchase
#: model:account.tax.template,name:l10n_eg.eg_exempt_sale
msgid "Exempt"
msgstr "معفاة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_106002
#: model:account.account,name:l10n_eg.2_egy_account_106002
#: model:account.account.template,name:l10n_eg.egy_account_106002
msgid "Furniture and Equipment"
msgstr "أثاث و معدات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500011
#: model:account.account,name:l10n_eg.2_egy_account_500011
#: model:account.account.template,name:l10n_eg.egy_account_500011
msgid "Gain On Difference Of Exchange"
msgstr "ربح فرق عملات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_101008
#: model:account.account,name:l10n_eg.2_egy_account_101008
#: model:account.account.template,name:l10n_eg.egy_account_101008
msgid "Gateway Credit Cards"
msgstr "بطاقات الائتمان Gateway"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400046
#: model:account.account,name:l10n_eg.2_egy_account_400046
#: model:account.account.template,name:l10n_eg.egy_account_400046
msgid "Gifts & Donations"
msgstr "مصروف هدايا و هبات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_103019
#: model:account.account,name:l10n_eg.2_egy_account_103019
#: model:account.account.template,name:l10n_eg.egy_account_103019
msgid "Handling Difference in Inventory"
msgstr "فرق المخزون"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400025
#: model:account.account,name:l10n_eg.2_egy_account_400025
#: model:account.account.template,name:l10n_eg.egy_account_400025
msgid "Hotel"
msgstr "مصاريف فندق"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400004
#: model:account.account,name:l10n_eg.2_egy_account_400004
#: model:account.account.template,name:l10n_eg.egy_account_400004
msgid "Housing Allowance"
msgstr "مصروف بدل سكن"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400055
#: model:account.account,name:l10n_eg.2_egy_account_400055
#: model:account.account.template,name:l10n_eg.egy_account_400055
msgid "Income Tax"
msgstr "مصروف ضريبة الدخل"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201027
#: model:account.account,name:l10n_eg.2_egy_account_201027
#: model:account.account.template,name:l10n_eg.egy_account_201027
msgid "Income Tax payable to Authority - Deducted from employee's salaries"
msgstr "تأمين اجتماعي دائن - مقتطع من الموظفين"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400040
#: model:account.account,name:l10n_eg.2_egy_account_400040
#: model:account.account.template,name:l10n_eg.egy_account_400040
msgid "Insurance"
msgstr "مصروف تأمين"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400072
#: model:account.account,name:l10n_eg.2_egy_account_400072
#: model:account.account.template,name:l10n_eg.egy_account_400072
msgid "Interest Expense"
msgstr "مصروف فائدة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500009
#: model:account.account,name:l10n_eg.2_egy_account_500009
#: model:account.account.template,name:l10n_eg.egy_account_500009
msgid "Interest Revenue"
msgstr "ايراد فائدة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_103020
#: model:account.account,name:l10n_eg.2_egy_account_103020
#: model:account.account.template,name:l10n_eg.egy_account_103020
msgid "Items Delivered to Customs on temprary Base"
msgstr "بنود في الجمارك"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400047
#: model:account.account,name:l10n_eg.2_egy_account_400047
#: model:account.account.template,name:l10n_eg.egy_account_400047
msgid "Kitchen and Buffet Expenses"
msgstr "مصروف المطبخ وبوفيه"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_106001
#: model:account.account,name:l10n_eg.2_egy_account_106001
#: model:account.account.template,name:l10n_eg.egy_account_106001
msgid "Leasehold Improvement"
msgstr "تحسين المستأجرات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201006
#: model:account.account,name:l10n_eg.2_egy_account_201006
#: model:account.account.template,name:l10n_eg.egy_account_201006
msgid "Leave Days Provision"
msgstr "مخصص ايام اجازة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400007
#: model:account.account,name:l10n_eg.2_egy_account_400007
#: model:account.account.template,name:l10n_eg.egy_account_400007
msgid "Leave Salary"
msgstr "مصروف اجازة موظفين"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400006
#: model:account.account,name:l10n_eg.2_egy_account_400006
#: model:account.account.template,name:l10n_eg.egy_account_400006
msgid "Leave Ticket"
msgstr "مصروف تذاكر موظفين"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201005
#: model:account.account,name:l10n_eg.2_egy_account_201005
#: model:account.account.template,name:l10n_eg.egy_account_201005
msgid "Leave Tickets Provision"
msgstr "مخصص تذاكر"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201021
#: model:account.account,name:l10n_eg.2_egy_account_201021
#: model:account.account.template,name:l10n_eg.egy_account_201021
msgid "Legal Reserve"
msgstr "احتياطى قانوني"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400031
#: model:account.account,name:l10n_eg.2_egy_account_400031
#: model:account.account.template,name:l10n_eg.egy_account_400031
msgid "Legal fees"
msgstr "مصروف رسوم قانونية"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400010
#: model:account.account,name:l10n_eg.2_egy_account_400010
#: model:account.account.template,name:l10n_eg.egy_account_400010
msgid "Life Insurance"
msgstr "مصروف تأمين على الحياة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egypt_chart_template_standard_liquidity_transfer
#: model:account.account,name:l10n_eg.2_egypt_chart_template_standard_liquidity_transfer
#: model:account.account.template,name:l10n_eg.egypt_chart_template_standard_liquidity_transfer
msgid "Liquidity Transfer"
msgstr "تحويل السيولة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400052
#: model:account.account,name:l10n_eg.2_egy_account_400052
#: model:account.account.template,name:l10n_eg.egy_account_400052
msgid "Loss On Fixed Assets Disposal"
msgstr "مصروف خسارة بيع و تخلص من اصول"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400053
#: model:account.account,name:l10n_eg.2_egy_account_400053
#: model:account.account.template,name:l10n_eg.egy_account_400053
msgid "Loss on Difference on Exchange"
msgstr "مصروف خسارة على الفرق العملات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_101005
#: model:account.account,name:l10n_eg.2_egy_account_101005
#: model:account.account.template,name:l10n_eg.egy_account_101005
msgid "Main Safe"
msgstr "خزينة رئيسية"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_101006
#: model:account.account,name:l10n_eg.2_egy_account_101006
#: model:account.account.template,name:l10n_eg.egy_account_101006
msgid "Main Safe - Foreign Currency"
msgstr "خزينة رئيسية - عملات اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400042
#: model:account.account,name:l10n_eg.2_egy_account_400042
#: model:account.account.template,name:l10n_eg.egy_account_400042
msgid "Maintenance"
msgstr "مصروف صيانة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500003
#: model:account.account,name:l10n_eg.2_egy_account_500003
#: model:account.account.template,name:l10n_eg.egy_account_500003
msgid "Management Consultancy Fees"
msgstr "مكاسب استشارات ادارية"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_101009
#: model:account.account,name:l10n_eg.2_egy_account_101009
#: model:account.account.template,name:l10n_eg.egy_account_101009
msgid "Manual Visa & Master Cards"
msgstr "فيزا وماستر بطاقات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400026
#: model:account.account,name:l10n_eg.2_egy_account_400026
#: model:account.account.template,name:l10n_eg.egy_account_400026
msgid "Meals"
msgstr "مصاريف فندق"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400009
#: model:account.account,name:l10n_eg.2_egy_account_400009
#: model:account.account.template,name:l10n_eg.egy_account_400009
msgid "Medical Insurance"
msgstr "مصروف تأمين طبي"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_106004
#: model:account.account,name:l10n_eg.2_egy_account_106004
#: model:account.account.template,name:l10n_eg.egy_account_106004
msgid "Motor Vehicles"
msgstr "السيارات"

#. module: l10n_eg
#: model:ir.model.fields,field_description:l10n_eg.field_account_tax_report__name
msgid "Name"
msgstr "الاسم"

#. module: l10n_eg
#: model:ir.model.fields,help:l10n_eg.field_account_tax_report__name
msgid "Name of this tax report"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_net
msgid "Net VAT Due"
msgstr "صافي الضريبة المستحق"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_net_3
msgid "Net VAT due (or reclaimed) for the period"
msgstr "صافي ضريبة القيمة المستحقة الواجب توريدها (استرجاعها)"

#. module: l10n_eg
#: model:account.fiscal.position,name:l10n_eg.1_account_fiscal_position_non_egypt
#: model:account.fiscal.position,name:l10n_eg.2_account_fiscal_position_non_egypt
#: model:account.fiscal.position.template,name:l10n_eg.account_fiscal_position_non_egypt
msgid "Non-Egypt"
msgstr ""

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400016
#: model:account.account,name:l10n_eg.2_egy_account_400016
#: model:account.account.template,name:l10n_eg.egy_account_400016
msgid "Office Rent"
msgstr "مصروف اجار مكتب"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400034
#: model:account.account,name:l10n_eg.2_egy_account_400034
#: model:account.account.template,name:l10n_eg.egy_account_400034
msgid "Other - Advertising Expenses"
msgstr "مصروف أخرى مصاريف الإعلان"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400051
#: model:account.account,name:l10n_eg.2_egy_account_400051
#: model:account.account.template,name:l10n_eg.egy_account_400051
msgid "Other Bank Charges"
msgstr "مصروف الرسوم المصرفية الأخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_102015
#: model:account.account,name:l10n_eg.2_egy_account_102015
#: model:account.account.template,name:l10n_eg.egy_account_102015
msgid "Other Debtors"
msgstr "مدينون اخرون"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500013
#: model:account.account,name:l10n_eg.2_egy_account_500013
#: model:account.account.template,name:l10n_eg.egy_account_500013
msgid "Other Income"
msgstr "دخول اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400057
#: model:account.account,name:l10n_eg.2_egy_account_400057
#: model:account.account.template,name:l10n_eg.egy_account_400057
msgid "Other Non Operating Expenses"
msgstr "المصاريف غير التشغيلية"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104035
#: model:account.account,name:l10n_eg.2_egy_account_104035
#: model:account.account.template,name:l10n_eg.egy_account_104035
msgid "Other Prepayments"
msgstr "دفعات مقدمة أخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_102014
#: model:account.account,name:l10n_eg.2_egy_account_102014
#: model:account.account.template,name:l10n_eg.egy_account_102014
msgid "Other Receivable"
msgstr "ذمم مدينة اخرى"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_other
msgid "Other Taxes"
msgstr "ضرائب اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400019
#: model:account.account,name:l10n_eg.2_egy_account_400019
#: model:account.account.template,name:l10n_eg.egy_account_400019
msgid "Other Utility Cahrges"
msgstr "مصروف خدمات اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400028
#: model:account.account,name:l10n_eg.2_egy_account_400028
#: model:account.account.template,name:l10n_eg.egy_account_400028
msgid "Others"
msgstr "مصاريف اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400023
#: model:account.account,name:l10n_eg.2_egy_account_400023
#: model:account.account.template,name:l10n_eg.egy_account_400023
msgid "Others - Communication"
msgstr "مصاريف اتصالات اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400050
#: model:account.account,name:l10n_eg.2_egy_account_400050
#: model:account.account.template,name:l10n_eg.egy_account_400050
msgid "Others - Office Various Expenses"
msgstr "مصاريف مكتب اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400033
#: model:account.account,name:l10n_eg.2_egy_account_400033
#: model:account.account.template,name:l10n_eg.egy_account_400033
msgid "Others - Professional Fees"
msgstr "مصروف أخرى الرسوم الفنية"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400039
#: model:account.account,name:l10n_eg.2_egy_account_400039
#: model:account.account.template,name:l10n_eg.egy_account_400039
msgid "Others - Provision & Write off"
msgstr "مخصصات و فروقات اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_105003
#: model:account.account,name:l10n_eg.2_egy_account_105003
#: model:account.account.template,name:l10n_eg.egy_account_105003
msgid "Outstanding Payments"
msgstr "المدفوعات المستحقة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_101004
#: model:account.account,name:l10n_eg.2_egy_account_101004
#: model:account.account.template,name:l10n_eg.egy_account_101004
msgid "Outstanding Receipts"
msgstr "الوصولات المدفوعة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_101010
#: model:account.account,name:l10n_eg.2_egy_account_101010
#: model:account.account.template,name:l10n_eg.egy_account_101010
msgid "PayPal Account"
msgstr "Paypal رصيد"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400062
#: model:account.account,name:l10n_eg.2_egy_account_400062
#: model:account.account.template,name:l10n_eg.egy_account_400062
msgid "PayPal Charges"
msgstr "Paypal مصروف رسوم  "

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201002
#: model:account.account,name:l10n_eg.2_egy_account_201002
#: model:account.account.template,name:l10n_eg.egy_account_201002
msgid "Payables"
msgstr "الذمم الدائنة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400027
#: model:account.account,name:l10n_eg.2_egy_account_400027
#: model:account.account.template,name:l10n_eg.egy_account_400027
msgid "Per Diem"
msgstr "مصاريف يومية"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400015
#: model:account.account,name:l10n_eg.2_egy_account_400015
#: model:account.account.template,name:l10n_eg.egy_account_400015
msgid "Personnel Cost Others"
msgstr "مصروف موظفين - اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_102013
#: model:account.account,name:l10n_eg.2_egy_account_102013
#: model:account.account.template,name:l10n_eg.egy_account_102013
msgid "Post Dated Cheques Received"
msgstr "شيكات مؤجلة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104033
#: model:account.account,name:l10n_eg.2_egy_account_104033
#: model:account.account.template,name:l10n_eg.egy_account_104033
msgid "PrePaid Advertisement Expenses"
msgstr " دعاية و الإعلان مدفوعة مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104034
#: model:account.account,name:l10n_eg.2_egy_account_104034
#: model:account.account.template,name:l10n_eg.egy_account_104034
msgid "Prepaid Bank Guarantee"
msgstr " ضمان بنكي مدفوع مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104030
#: model:account.account,name:l10n_eg.2_egy_account_104030
#: model:account.account.template,name:l10n_eg.egy_account_104030
msgid "Prepaid Consultancy Fees"
msgstr "رسوم استشارات مدفوعة مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104028
#: model:account.account,name:l10n_eg.2_egy_account_104028
#: model:account.account.template,name:l10n_eg.egy_account_104028
msgid "Prepaid Employees Housing"
msgstr "بدل سكن للموظفين مدفوع مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104036
#: model:account.account,name:l10n_eg.2_egy_account_104036
#: model:account.account.template,name:l10n_eg.egy_account_104036
msgid "Prepaid Finance charge for Loans"
msgstr "تكاليف تمويل قروض مدفوعة مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104031
#: model:account.account,name:l10n_eg.2_egy_account_104031
#: model:account.account.template,name:l10n_eg.egy_account_104031
msgid "Prepaid Legal Fees"
msgstr "الرسوم القانونية مدفوعة مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104025
#: model:account.account,name:l10n_eg.2_egy_account_104025
#: model:account.account.template,name:l10n_eg.egy_account_104025
msgid "Prepaid License Fees"
msgstr "رسوم ترخيص مدفوعة مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104022
#: model:account.account,name:l10n_eg.2_egy_account_104022
#: model:account.account.template,name:l10n_eg.egy_account_104022
msgid "Prepaid Life Insurance"
msgstr "تأمين على الحياة مدفوع مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104026
#: model:account.account,name:l10n_eg.2_egy_account_104026
#: model:account.account.template,name:l10n_eg.egy_account_104026
msgid "Prepaid Maintenance"
msgstr "رسوم صيانة مدفوعة مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104021
#: model:account.account,name:l10n_eg.2_egy_account_104021
#: model:account.account.template,name:l10n_eg.egy_account_104021
msgid "Prepaid Medical Insurance"
msgstr "تأمين طبي مدفوع مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104023
#: model:account.account,name:l10n_eg.2_egy_account_104023
#: model:account.account.template,name:l10n_eg.egy_account_104023
msgid "Prepaid Office Rent"
msgstr "ايجار مكتب مدفوع مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104024
#: model:account.account,name:l10n_eg.2_egy_account_104024
#: model:account.account.template,name:l10n_eg.egy_account_104024
msgid "Prepaid Other Insurance"
msgstr "تأمينات اخرى مدفوعة مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104029
#: model:account.account,name:l10n_eg.2_egy_account_104029
#: model:account.account.template,name:l10n_eg.egy_account_104029
msgid "Prepaid Schooling Fees"
msgstr "بدل رسوم تعليم مدرسي مدفوع مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104027
#: model:account.account,name:l10n_eg.2_egy_account_104027
#: model:account.account.template,name:l10n_eg.egy_account_104027
msgid "Prepaid Site Hosting Fees"
msgstr "رسوم استضافة موقع مدفوعة مسبقا"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400056
#: model:account.account,name:l10n_eg.2_egy_account_400056
#: model:account.account.template,name:l10n_eg.egy_account_400056
msgid "Previous Year Adjustments Account"
msgstr "مصروف حساب تسويات السنة السابقة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400068
#: model:account.account,name:l10n_eg.2_egy_account_400068
#: model:account.account.template,name:l10n_eg.egy_account_400068
msgid "Provision for Doubtful Debts"
msgstr "مخصص الديون المعدومة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_106010
#: model:account.account,name:l10n_eg.2_egy_account_106010
#: model:account.account.template,name:l10n_eg.egy_account_106010
msgid "Registration of Trademarks"
msgstr "تسجيل العلامات التجارية"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_202002
#: model:account.account,name:l10n_eg.2_egy_account_202002
#: model:account.account.template,name:l10n_eg.egy_account_202002
msgid "Reservations"
msgstr "احتياطات و حجوزات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_100101
#: model:account.account,name:l10n_eg.2_egy_account_100101
#: model:account.account.template,name:l10n_eg.egy_account_100101
msgid "Right of use Asset (IFRS 16)"
msgstr "حق استخدام الأصول (IFRS 16)"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_05_purchase
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_05_sale
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_05_purchase
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_05_sale
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_05_purchase
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_05_sale
msgid "SCHD 0.5%"
msgstr "الجدول %0.5"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_1_purchase
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_1_sale
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_1_purchase
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_1_sale
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_1_purchase
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_1_sale
msgid "SCHD 1%"
msgstr "الجدول %1"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_10_purchase
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_10_sale
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_10_purchase
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_10_sale
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_10_purchase
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_10_sale
msgid "SCHD 10%"
msgstr "الجدول %10"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_15_purchase
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_15_sale
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_15_purchase
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_15_sale
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_15_purchase
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_15_sale
msgid "SCHD 15%"
msgstr "الجدول %15"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_30_purchase
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_30_sale
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_30_purchase
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_30_sale
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_30_purchase
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_30_sale
msgid "SCHD 30%"
msgstr "الجدول %30"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_5_purchase
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_5_sale
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_5_purchase
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_5_sale
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_5_purchase
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_5_sale
msgid "SCHD 5%"
msgstr "الجدول %5"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_8_purchase
#: model:account.tax,description:l10n_eg.1_eg_schedule_tax_8_sale
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_8_purchase
#: model:account.tax,description:l10n_eg.2_eg_schedule_tax_8_sale
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_8_purchase
#: model:account.tax.template,description:l10n_eg.eg_schedule_tax_8_sale
msgid "SCHD 8%"
msgstr "الجدول %8"

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_half
msgid "SCHD Purchases 0.5% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_half
msgid "SCHD Purchases 0.5% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_one
msgid "SCHD Purchases 1% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_one
msgid "SCHD Purchases 1% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_ten
msgid "SCHD Purchases 10% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_ten
msgid "SCHD Purchases 10% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_fifteen
msgid "SCHD Purchases 15% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_fifteen
msgid "SCHD Purchases 15% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_thirty
msgid "SCHD Purchases 30% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_thirty
msgid "SCHD Purchases 30% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_five
msgid "SCHD Purchases 5% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_five
msgid "SCHD Purchases 5% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_eight
msgid "SCHD Purchases 8% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_eight
msgid "SCHD Purchases 8% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_half
msgid "SCHD Sales 0.5% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_half
msgid "SCHD Sales 0.5% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_one
msgid "SCHD Sales 1% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_one
msgid "SCHD Sales 1% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_ten
msgid "SCHD Sales 10% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_ten
msgid "SCHD Sales 10% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_fifteen
msgid "SCHD Sales 15% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_fifteen
msgid "SCHD Sales 15% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_thirty
msgid "SCHD Sales 30% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_thirty
msgid "SCHD Sales 30% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_five
msgid "SCHD Sales 5% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_five
msgid "SCHD Sales 5% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_eight
msgid "SCHD Sales 8% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_eight
msgid "SCHD Sales 8% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500001
#: model:account.account,name:l10n_eg.2_egy_account_500001
#: model:account.account.template,name:l10n_eg.egy_account_500001
msgid "Sales Account"
msgstr "مبيعات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400011
#: model:account.account,name:l10n_eg.2_egy_account_400011
#: model:account.account.template,name:l10n_eg.egy_account_400011
msgid "Sales Commission"
msgstr "مصروف عمولة مبيعات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500004
#: model:account.account,name:l10n_eg.2_egy_account_500004
#: model:account.account.template,name:l10n_eg.egy_account_500004
msgid "Sales from Other Region"
msgstr "مبيعات مناطق اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500002
#: model:account.account,name:l10n_eg.2_egy_account_500002
#: model:account.account.template,name:l10n_eg.egy_account_500002
msgid "Sales of I/C"
msgstr " مبيعات شركات تابعة"

#. module: l10n_eg
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_05_purchase
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_05_sale
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_05_purchase
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_05_sale
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_05_purchase
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_05_sale
msgid "Schedule 0.5%"
msgstr "الجدول %0.5"

#. module: l10n_eg
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_1_purchase
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_1_sale
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_1_purchase
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_1_sale
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_1_purchase
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_1_sale
msgid "Schedule 1%"
msgstr "الجدول %1"

#. module: l10n_eg
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_10_purchase
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_10_sale
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_10_purchase
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_10_sale
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_10_purchase
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_10_sale
msgid "Schedule 10%"
msgstr "الجدول %10"

#. module: l10n_eg
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_15_purchase
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_15_sale
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_15_purchase
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_15_sale
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_15_purchase
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_15_sale
msgid "Schedule 15%"
msgstr "الجدول %15"

#. module: l10n_eg
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_30_purchase
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_30_sale
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_30_purchase
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_30_sale
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_30_purchase
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_30_sale
msgid "Schedule 30%"
msgstr "الجدول %30"

#. module: l10n_eg
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_5_purchase
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_5_sale
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_5_purchase
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_5_sale
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_5_purchase
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_5_sale
msgid "Schedule 5%"
msgstr "الجدول %5"

#. module: l10n_eg
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_8_purchase
#: model:account.tax,name:l10n_eg.1_eg_schedule_tax_8_sale
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_8_purchase
#: model:account.tax,name:l10n_eg.2_eg_schedule_tax_8_sale
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_8_purchase
#: model:account.tax.template,name:l10n_eg.eg_schedule_tax_8_sale
msgid "Schedule 8%"
msgstr "الجدول %8"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_schedule_half
msgid "Schedule Tax 0.5%"
msgstr " ضرائب الجدول %0.5"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_schedule_1
msgid "Schedule Tax 1%"
msgstr " ضرائب الجدول %1"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_schedule_10
msgid "Schedule Tax 10%"
msgstr " ضرائب الجدول %10"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_schedule_15
msgid "Schedule Tax 15%"
msgstr " ضرائب الجدول %15"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_schedule_30
msgid "Schedule Tax 30%"
msgstr " ضرائب الجدول %30"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_schedule_5
msgid "Schedule Tax 5%"
msgstr " ضرائب الجدول %5"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_schedule_8
msgid "Schedule Tax 8%"
msgstr " ضرائب الجدول %8"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400075
#: model:account.account,name:l10n_eg.2_egy_account_400075
#: model:account.account.template,name:l10n_eg.egy_account_400075
msgid "Schedule Tax Expense"
msgstr "مصروف ضريبة الجدول"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201024
#: model:account.account,name:l10n_eg.2_egy_account_201024
#: model:account.account.template,name:l10n_eg.egy_account_201024
msgid "Schedule Tax collected & payable"
msgstr "ضريبة جدول دائنة"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base
msgid "Schedule Tax on Purchases (Base)"
msgstr "مشترات ضريبة الجدول (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax
msgid "Schedule Tax on Purchases (Tax)"
msgstr "مشتريات ضريبة الجدول (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_half
msgid "Schedule Tax on Purchases 0.5% (Base)"
msgstr "مشتريات ضريبة الجدول %0.5 (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_half
msgid "Schedule Tax on Purchases 0.5% (Tax)"
msgstr "مشتريات ضريبة الجدول %0.5 (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_one
msgid "Schedule Tax on Purchases 1% (Base)"
msgstr "مشتريات ضريبة الجدول %1 (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_one
msgid "Schedule Tax on Purchases 1% (Tax)"
msgstr "مشتريات ضريبة الجدول %1 (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_ten
msgid "Schedule Tax on Purchases 10% (Base)"
msgstr "مشتريات ضريبة الجدول %10 (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_ten
msgid "Schedule Tax on Purchases 10% (Tax)"
msgstr "مشتريات ضريبة الجدول %10 (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_fifteen
msgid "Schedule Tax on Purchases 15% (Base)"
msgstr "مشتريات ضريبة الجدول %15 (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_fifteen
msgid "Schedule Tax on Purchases 15% (Tax)"
msgstr "مشتريات ضريبة الجدول %15 (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_thirty
msgid "Schedule Tax on Purchases 30% (Base)"
msgstr "مشتريات ضريبة الجدول %30 (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_thirty
msgid "Schedule Tax on Purchases 30% (Tax)"
msgstr "مشتريات ضريبة الجدول %30 (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_five
msgid "Schedule Tax on Purchases 5% (Base)"
msgstr "مشتريات ضريبة الجدول %5 (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_five
msgid "Schedule Tax on Purchases 5% (Tax)"
msgstr "مشتريات ضريبة الجدول %5 (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_base_eight
msgid "Schedule Tax on Purchases 8% (Base)"
msgstr "مشتريات ضريبة الجدول %8 (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_purchase_tax_eight
msgid "Schedule Tax on Purchases 8% (Tax)"
msgstr "مشتريات ضريبة الجدول %8 (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base
msgid "Schedule Tax on Sales (Base)"
msgstr "مبيعات ضريبة الجدول ( اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax
msgid "Schedule Tax on Sales (Tax)"
msgstr "مبيعات ضريبة الجدول (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_half
msgid "Schedule Tax on Sales 0.5% (Base)"
msgstr "مبيعات ضريبة الجدول %0.5 ( اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_half
msgid "Schedule Tax on Sales 0.5% (Tax)"
msgstr "مبيعات ضريبة الجدول %0.5 ( ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_one
msgid "Schedule Tax on Sales 1% (Base)"
msgstr "مبيعات ضريبة الجدول %1 ( اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_one
msgid "Schedule Tax on Sales 1% (Tax)"
msgstr "مبيعات ضريبة الجدول %1 ( ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_ten
msgid "Schedule Tax on Sales 10% (Base)"
msgstr "مبيعات ضريبة الجدول %10 ( اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_ten
msgid "Schedule Tax on Sales 10% (Tax)"
msgstr "مبيعات ضريبة الجدول %10 ( ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_fifteen
msgid "Schedule Tax on Sales 15% (Base)"
msgstr "مبيعات ضريبة الجدول %15 ( اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_fifteen
msgid "Schedule Tax on Sales 15% (Tax)"
msgstr "مبيعات ضريبة الجدول %15 ( ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_thirty
msgid "Schedule Tax on Sales 30% (Base)"
msgstr "مبيعات ضريبة الجدول %30 ( اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_thirty
msgid "Schedule Tax on Sales 30% (Tax)"
msgstr "مبيعات ضريبة الجدول %30 ( ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_five
msgid "Schedule Tax on Sales 5% (Base)"
msgstr "مبيعات ضريبة الجدول %5 ( اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_five
msgid "Schedule Tax on Sales 5% (Tax)"
msgstr "مبيعات ضريبة الجدول %5 ( ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_base_eight
msgid "Schedule Tax on Sales 8% (Base)"
msgstr "مبيعات ضريبة الجدول %8 ( اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_schedule_tax_schedule_tax_sale_tax_eight
msgid "Schedule Tax on Sales 8% (Tax)"
msgstr "مبيعات ضريبة الجدول %8 ( ضريبة)"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400043
#: model:account.account,name:l10n_eg.2_egy_account_400043
#: model:account.account.template,name:l10n_eg.egy_account_400043
msgid "Security & Guard"
msgstr "مصروف حراسة و امن"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500008
#: model:account.account,name:l10n_eg.2_egy_account_500008
#: model:account.account.template,name:l10n_eg.egy_account_500008
msgid "Service Income"
msgstr "دخل خدمات"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_103016
#: model:account.account,name:l10n_eg.2_egy_account_103016
#: model:account.account.template,name:l10n_eg.egy_account_103016
msgid "Shipment Insurance"
msgstr "تأمين الشحن"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_103018
#: model:account.account,name:l10n_eg.2_egy_account_103018
#: model:account.account.template,name:l10n_eg.egy_account_103018
msgid "Shipment Other Charges"
msgstr "رسوم شحنات اخرى"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_103017
#: model:account.account,name:l10n_eg.2_egy_account_103017
#: model:account.account.template,name:l10n_eg.egy_account_103017
msgid "Shipments Documentation Charges"
msgstr "رسوم"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400078
#: model:account.account,name:l10n_eg.2_egy_account_400078
#: model:account.account.template,name:l10n_eg.egy_account_400078
msgid "Social Contibution - Company portion expense"
msgstr "مصروف تأمينات اجتماعية - من الشركة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201026
#: model:account.account,name:l10n_eg.2_egy_account_201026
#: model:account.account.template,name:l10n_eg.egy_account_201026
msgid "Social Contribution - Payable to authorities"
msgstr "تأمين اجتماعي دائن"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_500007
#: model:account.account,name:l10n_eg.2_egy_account_500007
#: model:account.account.template,name:l10n_eg.egy_account_500007
msgid "Space Rental Income"
msgstr "دخل تأجير"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400012
#: model:account.account,name:l10n_eg.2_egy_account_400012
#: model:account.account.template,name:l10n_eg.egy_account_400012
msgid "Staff Other Allowances"
msgstr "مصروف بدلات اخرى للموظفين"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_stamp_tax_20_purchase
#: model:account.tax,description:l10n_eg.1_eg_stamp_tax_20_sale
#: model:account.tax,description:l10n_eg.2_eg_stamp_tax_20_purchase
#: model:account.tax,description:l10n_eg.2_eg_stamp_tax_20_sale
#: model:account.tax,name:l10n_eg.1_eg_stamp_tax_20_purchase
#: model:account.tax,name:l10n_eg.1_eg_stamp_tax_20_sale
#: model:account.tax,name:l10n_eg.2_eg_stamp_tax_20_purchase
#: model:account.tax,name:l10n_eg.2_eg_stamp_tax_20_sale
#: model:account.tax.template,description:l10n_eg.eg_stamp_tax_20_purchase
#: model:account.tax.template,description:l10n_eg.eg_stamp_tax_20_sale
#: model:account.tax.template,name:l10n_eg.eg_stamp_tax_20_purchase
#: model:account.tax.template,name:l10n_eg.eg_stamp_tax_20_sale
msgid "Stamp"
msgstr "الدمغة"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_stamp
msgid "Stamp Tax 20%"
msgstr "ضريبة الدمغة"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_other_taxes_stamp_purchase_tax_base
msgid "Stamp Tax Purchases (Base)"
msgstr "مشتريات ضريبة الدمغة (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_other_taxes_stamp_purchase_tax_tax
msgid "Stamp Tax Purchases (Tax)"
msgstr "مشتريات ضريبة الدمغة (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_other_taxes_stamp_purchase_tax_base_purchase
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_other_taxes_stamp_purchase_tax_base_purchase
msgid "Stamp Tax Purchases 20% (Base)"
msgstr "مشتريات ضريبة الدمغة %20 (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_other_taxes_stamp_purchase_tax_tax_purchase
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_other_taxes_stamp_purchase_tax_tax_purchase
msgid "Stamp Tax Purchases 20% (Tax)"
msgstr "مشتريات ضريبة الدمغة %20 (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_other_taxes_stamp_tax_base
msgid "Stamp Tax Sales (Base)"
msgstr "مبيعات ضريبة الدمغة (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_other_taxes_stamp_tax_tax
msgid "Stamp Tax Sales (Tax)"
msgstr "مبيعات ضريبة الدمغة (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_other_taxes_stamp_tax_base_sales
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_other_taxes_stamp_tax_base_sales
msgid "Stamp Tax Sales 20% (Base)"
msgstr "مبيعات ضريبة الدمغة %20 (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_other_taxes_stamp_tax_tax_sales
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_other_taxes_stamp_tax_tax_sales
msgid "Stamp Tax Sales 20% (Tax)"
msgstr "مبيعات ضريبة الدمغة %20 (ضريبة)"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201025
#: model:account.account,name:l10n_eg.2_egy_account_201025
#: model:account.account.template,name:l10n_eg.egy_account_201025
msgid "Stamp Tax payable"
msgstr "ضريبة الدمغة دائنة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400077
#: model:account.account,name:l10n_eg.2_egy_account_400077
#: model:account.account.template,name:l10n_eg.egy_account_400077
msgid "Stamp tax expense"
msgstr "مصروف ضريبة الدمغة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400045
#: model:account.account,name:l10n_eg.2_egy_account_400045
#: model:account.account.template,name:l10n_eg.egy_account_400045
msgid "Subscriptions"
msgstr "مصروف الاشتراكات"

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t1_v001
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t1_v001
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t1_v001
msgid "T1 - V001 - Export"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t1_v002
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t1_v002
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t1_v002
msgid "T1 - V002 - Export to free areas and other areas"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t1_v003
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t1_v003
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t1_v003
msgid "T1 - V003 - Exempted good or service"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t1_v004
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t1_v004
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t1_v004
msgid "T1 - V004 - A non-taxable good or service"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t1_v005
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t1_v005
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t1_v005
msgid "T1 - V005 - Exemptions for diplomats, consulates and embassies"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t1_v006
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t1_v006
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t1_v006
msgid "T1 - V006 - Defence and National security Exemptions"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t1_v007
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t1_v007
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t1_v007
msgid "T1 - V007 - Agreements exemptions"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t1_v008
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t1_v008
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t1_v008
msgid "T1 - V008 - Special Exemptios and other reasons"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t1_v009
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t1_v009
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t1_v009
msgid "T1 - V009 - General Item sales"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t1_v010
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t1_v010
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t1_v010
msgid "T1 - V010 - Other Rates"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t10_mn01
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t10_mn01
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t10_mn01
msgid "T10 - Mn01 - Municipality Fees (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t10_mn02
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t10_mn02
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t10_mn02
msgid "T10 - Mn02 - Municipality Fees (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t11_mi01
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t11_mi01
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t11_mi01
msgid "T11 - MI01 - Medical insurance fee (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t11_mi02
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t11_mi02
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t11_mi02
msgid "T11 - MI02 - Medical insurance fee (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t12_of01
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t12_of01
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t12_of01
msgid "T12 - OF01 - Other fees (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t12_of02
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t12_of02
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t12_of02
msgid "T12 - OF02 - Other fees (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t13_st03
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t13_st03
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t13_st03
msgid "T13 - ST03 - Stamping tax (percentage)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t14_st04
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t14_st04
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t14_st04
msgid "T14 - ST04 - Stamping Tax (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t15_ent03
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t15_ent03
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t15_ent03
msgid "T15 - Ent03 - Entertainment tax (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t15_ent04
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t15_ent04
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t15_ent04
msgid "T15 - Ent04 - Entertainment tax (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t16_rd03
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t16_rd03
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t16_rd03
msgid "T16 - RD03 - Resource development fee (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t16_rd04
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t16_rd04
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t16_rd04
msgid "T16 - RD04 - Resource development fee (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t17_sc03
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t17_sc03
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t17_sc03
msgid "T17 - SC03 - Service charges (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t17_sc04
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t17_sc04
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t17_sc04
msgid "T17 - SC04 - Service charges (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t18_mn03
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t18_mn03
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t18_mn03
msgid "T18 - Mn03 - Municipality Fees (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t18_mn04
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t18_mn04
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t18_mn04
msgid "T18 - Mn04 - Municipality Fees (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t19_mi03
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t19_mi03
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t19_mi03
msgid "T19 - MI03 - Medical insurance fee (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t19_mi04
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t19_mi04
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t19_mi04
msgid "T19 - MI04 - Medical insurance fee (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t2_tbl01
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t2_tbl01
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t2_tbl01
msgid "T2 - Tbl01 - Table tax (percentage)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t20_of03
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t20_of03
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t20_of03
msgid "T20 - OF03 - Other fees (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t20_of04
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t20_of04
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t20_of04
msgid "T20 - OF04 - Other fees (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t3_tbl02
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t3_tbl02
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t3_tbl02
msgid "T3 - Tbl02 - Table tax (Fixed Amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w001
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w001
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w001
msgid "T4 - W001 - Contracting"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w002
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w002
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w002
msgid "T4 - W002 - Supplies"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w003
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w003
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w003
msgid "T4 - W003 - Purachases"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w004
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w004
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w004
msgid "T4 - W004 - Services"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w005
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w005
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w005
msgid ""
"T4 - W005 - Sums paid by the cooperative societies for car transportation to"
" their members"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w006
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w006
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w006
msgid "T4 - W006 - Commissionagency & brokerage"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w007
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w007
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w007
msgid ""
"T4 - W007 - Discounts & grants & additional exceptional incentives(smoke, "
"cement companies)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w008
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w008
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w008
msgid ""
"T4 - W008 - All discounts & grants & commissions (petroleum, "
"telecommunications, other companies)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w009
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w009
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w009
msgid "T4 - W009 - Supporting export subsidies"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w010
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w010
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w010
msgid "T4 - W010 - Professional fees"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w011
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w011
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w011
msgid "T4 - W011 - Commission & brokerage _A_57"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w012
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w012
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w012
msgid "T4 - W012 - Hospitals collecting from doctors"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w013
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w013
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w013
msgid "T4 - W013 - Royalties"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w014
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w014
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w014
msgid "T4 - W014 - Customs clearance"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w015
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w015
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w015
msgid "T4 - W015 - Exemption"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t4_w016
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t4_w016
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t4_w016
msgid "T4 - W016 - advance payments"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t5_st01
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t5_st01
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t5_st01
msgid "T5 - ST01 - Stamping tax (percentage)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t6_st02
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t6_st02
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t6_st02
msgid "T6 - ST02 - Stamping Tax (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t7_ent01
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t7_ent01
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t7_ent01
msgid "T7 - Ent01 - Entertainment tax (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t7_ent02
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t7_ent02
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t7_ent02
msgid "T7 - Ent02 - Entertainment tax (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t8_rd01
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t8_rd01
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t8_rd01
msgid "T8 - RD01 - Resource development fee (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t8_rd02
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t8_rd02
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t8_rd02
msgid "T8 - RD02 - Resource development fee (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t9_sc01
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t9_sc01
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t9_sc01
msgid "T9 - SC01 - Service charges (rate)"
msgstr ""

#. module: l10n_eg
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax__l10n_eg_eta_code__t9_sc02
#: model:ir.model.fields.selection,name:l10n_eg.selection__account_tax_template__l10n_eg_eta_code__t9_sc02
#: model:ir.model.fields.selection,name:l10n_eg.selection__l10n_eg_eta_account_tax_mixin__l10n_eg_eta_code__t9_sc02
msgid "T9 - SC02 - Service charges (amount)"
msgstr ""

#. module: l10n_eg
#: model:ir.model,name:l10n_eg.model_account_tax
msgid "Tax"
msgstr "الضريبة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201022
#: model:account.account,name:l10n_eg.2_egy_account_201022
#: model:account.account.template,name:l10n_eg.egy_account_201022
msgid "Taxes Provision"
msgstr " مخصص ضرائب ورسوم متنازع عليها"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400020
#: model:account.account,name:l10n_eg.2_egy_account_400020
#: model:account.account.template,name:l10n_eg.egy_account_400020
msgid "Telephone"
msgstr "مصروف هاتف"

#. module: l10n_eg
#: model:ir.model,name:l10n_eg.model_account_tax_template
msgid "Templates for Taxes"
msgstr "قوالب الضرائب"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_net_1
msgid "Total value of due tax for the period"
msgstr "إجمالي ضريبة القيمة المستحقة للفترة الحالية"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_net_2
msgid "Total value of recoverable tax for the period"
msgstr "اجمالي الضريبة القيمة المضافة المدفوعة مقدما"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400032
#: model:account.account,name:l10n_eg.2_egy_account_400032
#: model:account.account.template,name:l10n_eg.egy_account_400032
msgid "Trade License Fees"
msgstr "مصروف رسوم الرخصة التجارية"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400041
#: model:account.account,name:l10n_eg.2_egy_account_400041
#: model:account.account.template,name:l10n_eg.egy_account_400041
msgid "Training"
msgstr "مصروف تدريب"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400005
#: model:account.account,name:l10n_eg.2_egy_account_400005
#: model:account.account.template,name:l10n_eg.egy_account_400005
msgid "Transportation Allowance"
msgstr "مصروف بدل نقل"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_999999
#: model:account.account,name:l10n_eg.2_egy_account_999999
#: model:account.account.template,name:l10n_eg.egy_account_999999
msgid "Undistributed Profits/Losses"
msgstr "ارباح / خسائر غير موزعة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400013
#: model:account.account,name:l10n_eg.2_egy_account_400013
#: model:account.account.template,name:l10n_eg.egy_account_400013
msgid "Uniform"
msgstr "مصروف زي موحد"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_standard_purchase_14
#: model:account.tax,description:l10n_eg.1_eg_standard_sale_14
#: model:account.tax,description:l10n_eg.2_eg_standard_purchase_14
#: model:account.tax,description:l10n_eg.2_eg_standard_sale_14
#: model:account.tax,name:l10n_eg.1_eg_standard_purchase_14
#: model:account.tax,name:l10n_eg.1_eg_standard_sale_14
#: model:account.tax,name:l10n_eg.2_eg_standard_purchase_14
#: model:account.tax,name:l10n_eg.2_eg_standard_sale_14
#: model:account.tax.group,name:l10n_eg.eg_tax_vat
#: model:account.tax.template,description:l10n_eg.eg_standard_purchase_14
#: model:account.tax.template,description:l10n_eg.eg_standard_sale_14
#: model:account.tax.template,name:l10n_eg.eg_standard_purchase_14
#: model:account.tax.template,name:l10n_eg.eg_standard_sale_14
msgid "VAT 14%"
msgstr "قيمة مضافة %14"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104041
#: model:account.account,name:l10n_eg.2_egy_account_104041
#: model:account.account.template,name:l10n_eg.egy_account_104041
msgid "VAT Input"
msgstr "مدخلات ضريبة القيمة المضافة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201017
#: model:account.account,name:l10n_eg.2_egy_account_201017
#: model:account.account.template,name:l10n_eg.egy_account_201017
msgid "VAT Output"
msgstr "مخرجات ضريبة القيمة المضافة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_202003
#: model:account.account,name:l10n_eg.2_egy_account_202003
#: model:account.account.template,name:l10n_eg.egy_account_202003
msgid "VAT Payable"
msgstr "ضريبة القيمة المضافة المستحقة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_100103
#: model:account.account,name:l10n_eg.2_egy_account_100103
#: model:account.account.template,name:l10n_eg.egy_account_100103
msgid "VAT Receivable"
msgstr "ضريبة القيمة المضافة المدينة"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_expense_base
msgid "VAT on Expenses and all other Inputs (Base)"
msgstr "ضريبة القيمة المضافة على المشتريات (أساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_expense_tax
msgid "VAT on Expenses and all other Inputs (Tax)"
msgstr "ضريبة القيمة المضافة على المشتريات (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_sale_base
msgid "VAT on Sales and all other Outputs (Base)"
msgstr "ضريبة القيمة المضافة على المبيعات (أساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_vat_return_sale_tax
msgid "VAT on Sales and all other Outputs (Tax)"
msgstr "ضريبة القيمة المضافة على المبيعات (ضريبة)"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400048
#: model:account.account,name:l10n_eg.2_egy_account_400048
#: model:account.account.template,name:l10n_eg.egy_account_400048
msgid "Vehicle Expenses"
msgstr "مصروف سيارة"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_101007
#: model:account.account,name:l10n_eg.2_egy_account_101007
#: model:account.account.template,name:l10n_eg.egy_account_101007
msgid "Visa & Master Credit Cards"
msgstr "بطاقات الائتمان فيزا وماستر"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400014
#: model:account.account,name:l10n_eg.2_egy_account_400014
#: model:account.account.template,name:l10n_eg.egy_account_400014
msgid "Visa Expenses"
msgstr "مصروف تأشيرة"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_withholding_05_purchase
#: model:account.tax,description:l10n_eg.1_eg_withholding_05_sale
#: model:account.tax,description:l10n_eg.2_eg_withholding_05_purchase
#: model:account.tax,description:l10n_eg.2_eg_withholding_05_sale
#: model:account.tax.template,description:l10n_eg.eg_withholding_05_purchase
#: model:account.tax.template,description:l10n_eg.eg_withholding_05_sale
msgid "WH -0.5%"
msgstr "الصناعة و التجارة %0.5"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_withholding_1_purchase
#: model:account.tax,description:l10n_eg.1_eg_withholding_1_sale
#: model:account.tax,description:l10n_eg.2_eg_withholding_1_purchase
#: model:account.tax,description:l10n_eg.2_eg_withholding_1_sale
#: model:account.tax.template,description:l10n_eg.eg_withholding_1_purchase
#: model:account.tax.template,description:l10n_eg.eg_withholding_1_sale
msgid "WH -1%"
msgstr "الصناعة و التجارة %1"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_withholding_3_purchase
#: model:account.tax,description:l10n_eg.1_eg_withholding_3_sale
#: model:account.tax,description:l10n_eg.2_eg_withholding_3_purchase
#: model:account.tax,description:l10n_eg.2_eg_withholding_3_sale
#: model:account.tax.template,description:l10n_eg.eg_withholding_3_purchase
#: model:account.tax.template,description:l10n_eg.eg_withholding_3_sale
msgid "WH -3%"
msgstr "الصناعة و التجارة %3"

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_withholding_5_purchase
#: model:account.tax,description:l10n_eg.1_eg_withholding_5_sale
#: model:account.tax,description:l10n_eg.2_eg_withholding_5_purchase
#: model:account.tax,description:l10n_eg.2_eg_withholding_5_sale
#: model:account.tax.template,description:l10n_eg.eg_withholding_5_purchase
#: model:account.tax.template,description:l10n_eg.eg_withholding_5_sale
msgid "WH -5%"
msgstr "الصناعة و التجارة %5"

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_purchase_base_half
msgid "WH Purchases -0.5% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_purchase_tax_half
msgid "WH Purchases -0.5% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_purchase_base_one
msgid "WH Purchases -1% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_purchase_tax_one
msgid "WH Purchases -1% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_purchase_base_three
msgid "WH Purchases -3% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_purchase_tax_three
msgid "WH Purchases -3% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_purchase_base_five
msgid "WH Purchases -5% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_purchase_tax_five
msgid "WH Purchases -5% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_sale_base_half
msgid "WH Sales -0.5% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_sale_tax_half
msgid "WH Sales -0.5% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_sale_tax_one
msgid "WH Sales -1% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_sale_tax_three
msgid "WH Sales -3% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_sale_tax_five
msgid "WH Sales -5% (Tax)"
msgstr ""

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400076
#: model:account.account,name:l10n_eg.2_egy_account_400076
#: model:account.account.template,name:l10n_eg.egy_account_400076
msgid "WH Tax Expense"
msgstr "مصروف ضريبة خصم المنبع "

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_sale_base_one
msgid "WH on Sales -1% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_sale_base_three
msgid "WH on Sales -3% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.tax.report.line,tag_name:l10n_eg.tax_report_withholding_tax_sale_base_five
msgid "WH on Sales -5% (Base)"
msgstr ""

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_104042
#: model:account.account,name:l10n_eg.2_egy_account_104042
#: model:account.account.template,name:l10n_eg.egy_account_104042
msgid "WH tax Advance with Customers - On behalf of my company"
msgstr "ضريبة خصم المنبع مدفوعة عن طريق العملاء"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_201020
#: model:account.account,name:l10n_eg.2_egy_account_201020
#: model:account.account.template,name:l10n_eg.egy_account_201020
msgid "WHTax Payable - On behalf of suppliers"
msgstr "ضريبة خصم المنبع للدفع عن الموردين"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400017
#: model:account.account,name:l10n_eg.2_egy_account_400017
#: model:account.account.template,name:l10n_eg.egy_account_400017
msgid "Warehouse Rent"
msgstr "مصروف ايجار مستودع"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400018
#: model:account.account,name:l10n_eg.2_egy_account_400018
#: model:account.account.template,name:l10n_eg.egy_account_400018
msgid "Water & Electricity"
msgstr "مصروف مياه و كهرباء"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400022
#: model:account.account,name:l10n_eg.2_egy_account_400022
#: model:account.account.template,name:l10n_eg.egy_account_400022
msgid "Web Site Hosting Fees"
msgstr "رسوم استضافة موقع"

#. module: l10n_eg
#: model:account.tax,name:l10n_eg.1_eg_withholding_05_purchase
#: model:account.tax,name:l10n_eg.1_eg_withholding_05_sale
#: model:account.tax,name:l10n_eg.2_eg_withholding_05_purchase
#: model:account.tax,name:l10n_eg.2_eg_withholding_05_sale
#: model:account.tax.template,name:l10n_eg.eg_withholding_05_purchase
#: model:account.tax.template,name:l10n_eg.eg_withholding_05_sale
msgid "Withholding -0.5%"
msgstr "الصناعة و التجارة %0.5"

#. module: l10n_eg
#: model:account.tax,name:l10n_eg.1_eg_withholding_1_purchase
#: model:account.tax,name:l10n_eg.1_eg_withholding_1_sale
#: model:account.tax,name:l10n_eg.2_eg_withholding_1_purchase
#: model:account.tax,name:l10n_eg.2_eg_withholding_1_sale
#: model:account.tax.template,name:l10n_eg.eg_withholding_1_purchase
#: model:account.tax.template,name:l10n_eg.eg_withholding_1_sale
msgid "Withholding -1%"
msgstr "الصناعة و التجارة %1"

#. module: l10n_eg
#: model:account.tax,name:l10n_eg.1_eg_withholding_3_purchase
#: model:account.tax,name:l10n_eg.1_eg_withholding_3_sale
#: model:account.tax,name:l10n_eg.2_eg_withholding_3_purchase
#: model:account.tax,name:l10n_eg.2_eg_withholding_3_sale
#: model:account.tax.template,name:l10n_eg.eg_withholding_3_purchase
#: model:account.tax.template,name:l10n_eg.eg_withholding_3_sale
msgid "Withholding -3%"
msgstr "الصناعة و التجارة %3"

#. module: l10n_eg
#: model:account.tax,name:l10n_eg.1_eg_withholding_5_purchase
#: model:account.tax,name:l10n_eg.1_eg_withholding_5_sale
#: model:account.tax,name:l10n_eg.2_eg_withholding_5_purchase
#: model:account.tax,name:l10n_eg.2_eg_withholding_5_sale
#: model:account.tax.template,name:l10n_eg.eg_withholding_5_purchase
#: model:account.tax.template,name:l10n_eg.eg_withholding_5_sale
msgid "Withholding -5%"
msgstr "الصناعة و التجارة %5"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_withholding_half
msgid "Withholding Tax -0.5%"
msgstr "-ضرائب الصناعة و التجارة %0.5"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_withholding_1
msgid "Withholding Tax -1%"
msgstr "-ضرائب الصناعة و التجارة %1"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_withholding_3
msgid "Withholding Tax -3%"
msgstr "-ضرائب الصناعة و التجارة %3"

#. module: l10n_eg
#: model:account.tax.group,name:l10n_eg.eg_tax_group_withholding_5
msgid "Withholding Tax -5%"
msgstr "-ضرائب الصناعة و التجارة %5"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_purchase_base
msgid "Withholding Tax on Purchases (Base)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المشتريات (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_purchase_tax
msgid "Withholding Tax on Purchases (Tax)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المشتريات (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_purchase_base_half
msgid "Withholding Tax on Purchases -0.5% (Base)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المشتريات %0.5- (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_purchase_tax_half
msgid "Withholding Tax on Purchases -0.5% (Tax)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المشتريات %0.5- (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_purchase_base_one
msgid "Withholding Tax on Purchases -1% (Base)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المشتريات %1- (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_purchase_tax_one
msgid "Withholding Tax on Purchases -1% (Tax)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المشتريات %1- (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_purchase_base_three
msgid "Withholding Tax on Purchases -3% (Base)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المشتريات %3- (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_purchase_tax_three
msgid "Withholding Tax on Purchases -3% (Tax)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المشتريات %3- (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_purchase_base_five
msgid "Withholding Tax on Purchases -5% (Base)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المشتريات %5- (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_purchase_tax_five
msgid "Withholding Tax on Purchases -5% (Tax)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المشتريات %5- (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_sale_base
msgid "Withholding Tax on Sales (Base)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المبيعات (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_sale_tax
msgid "Withholding Tax on Sales (Tax)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المبيعات (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_sale_base_half
msgid "Withholding Tax on Sales -0.5% (Base)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المبيعات %0.5- (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_sale_tax_half
msgid "Withholding Tax on Sales -0.5% (Tax)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المبيعات %0.5- (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_sale_base_one
msgid "Withholding Tax on Sales -1% (Base)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المبيعات %1- (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_sale_tax_one
msgid "Withholding Tax on Sales -1% (Tax)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المبيعات %1- (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_sale_base_three
msgid "Withholding Tax on Sales -3% (Base)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المبيعات %3- (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_sale_tax_three
msgid "Withholding Tax on Sales -3% (Tax)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المبيعات %3- (ضريبة)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_sale_base_five
msgid "Withholding Tax on Sales -5% (Base)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المبيعات %5- (اساسي)"

#. module: l10n_eg
#: model:account.tax.report.line,name:l10n_eg.tax_report_withholding_tax_sale_tax_five
msgid "Withholding Tax on Sales -5% (Tax)"
msgstr "ضريبة الصناعة و التجارة (خصم المنبع) على المبيعات %5- (ضريبة)"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400036
#: model:account.account,name:l10n_eg.2_egy_account_400036
#: model:account.account.template,name:l10n_eg.egy_account_400036
msgid "Write Off Inventory"
msgstr "مصروف فرق المخزون"

#. module: l10n_eg
#: model:account.account,name:l10n_eg.1_egy_account_400035
#: model:account.account,name:l10n_eg.2_egy_account_400035
#: model:account.account.template,name:l10n_eg.egy_account_400035
msgid "Write Off Receivables & Payables"
msgstr "مصروف شطب المدين و الذمم الدائنة "

#. module: l10n_eg
#: model:account.tax,description:l10n_eg.1_eg_zero_purchase_0
#: model:account.tax,description:l10n_eg.1_eg_zero_sale_0
#: model:account.tax,description:l10n_eg.2_eg_zero_purchase_0
#: model:account.tax,description:l10n_eg.2_eg_zero_sale_0
#: model:account.tax,name:l10n_eg.1_eg_zero_purchase_0
#: model:account.tax,name:l10n_eg.1_eg_zero_sale_0
#: model:account.tax,name:l10n_eg.2_eg_zero_purchase_0
#: model:account.tax,name:l10n_eg.2_eg_zero_sale_0
#: model:account.tax.template,description:l10n_eg.eg_zero_purchase_0
#: model:account.tax.template,description:l10n_eg.eg_zero_sale_0
#: model:account.tax.template,name:l10n_eg.eg_zero_purchase_0
#: model:account.tax.template,name:l10n_eg.eg_zero_sale_0
msgid "Zero Rated 0%"
msgstr "صفرية %0"

#. module: l10n_eg
#: model:ir.model,name:l10n_eg.model_l10n_eg_eta_account_tax_mixin
msgid "l10n_eg.eta.account.tax.mixin"
msgstr ""
