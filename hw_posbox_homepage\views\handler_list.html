{% extends "layout.html" %}
{% from "loading.html" import loading_block_ui %}
{% block head %}
    <script>
        $(document).ready(function () {
            $('#load_handler_btn').on('click', function(e){
                e.preventDefault();
                $('.loading-block').removeClass('o_hide');
                $.ajax({
                    url: '/load_iot_handlers',
                }).done(function () {
                    $('.message-status').html('Handlers loaded successfully <br> Refreshing page');
                    setTimeout(function () {
                        location.reload(true);
                    }, 25000);
                }).fail(function () {
                    setTimeout(function () {
                        location.reload(true);
                    }, 25000);
                });
            });
        });
    </script>
{% endblock %}
{% block content %}
    <h2 class="text-center text-green">Logging</h2>
    <form method="post">
        <div style="text-align: left;display:block;">
            <label for="iot-logging-root">Root log level (Current value: {{root_logger_log_level}}):</label>
            <select name="iot-logging-root" id="iot-logging-root">
            {% for log_level in available_log_levels %}
                <option value="{{ log_level }}" {{ "selected" if log_level == root_logger_log_level }}>
                    {{ log_level | capitalize }} {{ "(Recommended)" if log_level == recommended_log_level }}
                </option>
            {% endfor %}
            </select>

            <label for="iot-logging-odoo">Odoo log level (Current value: {{odoo_current_log_level}}):</label>
            <select name="iot-logging-odoo" id="iot-logging-odoo">
            {% for log_level in available_log_levels %}
                <option value="{{ log_level }}" {{ "selected" if log_level == odoo_current_log_level }}>
                    {{ log_level | capitalize }} {{ "(Recommended)" if log_level == recommended_log_level }}
                </option>
            {% endfor %}
            </select>
        </div>

        <h2 class="text-center text-green">Interfaces list</h2>
        <table align="center" width="80%" cellpadding="3">
            <tr>
                <th>Name</th>
                <th>Log Level</th>
            </tr>
            {% for interface in interfaces_list -%}
                <tr>
                    <td>{{ interface }}</td>
                    <td>
                        {% set interface_logger_info = interfaces_logger_info[interface] %}
                        {% if interface_logger_info != False %}
                            <select name="iot-logging-interface-{{interface}}">
                                <option value="parent" {{ "selected" if interface_logger_info.is_using_parent_level }}>
                                    Same as {{ interface_logger_info.parent_name | capitalize }} ({{ interface_logger_info.parent_level | capitalize }})
                                </option>
                                <option style="font-size: 1pt; background-color: black;" disabled>&nbsp;</option>
                                {% for log_level in available_log_levels %}
                                <option value="{{ log_level }}" {{ "selected" if not interface_logger_info.is_using_parent_level and log_level == interface_logger_info.level }}>
                                    {{ log_level | capitalize }}
                                </option>
                                {% endfor %}
                            </select>
                        {% else %}
                            <span class="font-small">Logger uninitialised</span>
                        {% endif %}
                    </td>
                </tr>
            {%- endfor %}
        </table>

        <h2 class="text-center text-green">Drivers list</h2>
        <table align="center" width="80%" cellpadding="3">
            <tr>
                <th>Name</th>
                <th>Log Level</th>
            </tr>
            {% for driver in drivers_list -%}
                <tr>
                    <td>{{ driver }}</td>
                    <td>
                        {% set driver_logger_info = drivers_logger_info[driver] %}
                        {% if driver_logger_info != False %}
                            <select name="iot-logging-driver-{{driver}}">
                                <option value="parent" {{ "selected" if driver_logger_info.is_using_parent_level }}>
                                    Same as {{ driver_logger_info.parent_name | capitalize }} ({{ driver_logger_info.parent_level | capitalize }})
                                </option>
                                <option style="font-size: 1pt; background-color: black;" disabled>&nbsp;</option>
                                {% for log_level in available_log_levels %}
                                <option value="{{ log_level }}" {{ "selected" if not driver_logger_info.is_using_parent_level and log_level == driver_logger_info.level }}>
                                    {{ log_level | capitalize }}
                                </option>
                                {% endfor %}
                            </select>
                        {% else %}
                            <span class="font-small">Logger uninitialised</span>
                        {% endif %}
                    </td>
                </tr>
            {%- endfor %}
        </table>

        <div style="margin-top: 20px;" class="text-center">
            {% if server %}
                <a id="load_handler_btn" class="btn" href='/load_iot_handlers'>Load handlers</a>
            {% endif %}
            <input class="btn" type="submit" value="Update Logs Level Value"/>
        </div>
    </form>
    {% if server %}
        <div class="text-center font-small" style="margin: 10px auto;">
            You can clear the handlers configuration
            <form style="display: inline-block;margin-left: 4px;" action='/handlers_clear'>
                <input class="btn btn-sm" type="submit" value="Clear"/>
            </form>
        </div>
    {% endif %}
    {{ loading_block_ui('Loading Handlers') }}
{% endblock %}
