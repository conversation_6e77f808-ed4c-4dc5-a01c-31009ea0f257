# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* survey
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>yuhada <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# whenwesober, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Febrasari <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# Wahyu <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON>to The <<EMAIL>>, 2022
# Abe <PERSON>o, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_4
msgid "$100"
msgstr "$100"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_1
msgid "$20"
msgstr "$20"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_5
msgid "$200"
msgstr "$200"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_6
msgid "$300"
msgstr "$300"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_2
msgid "$50"
msgstr "$50"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_3
msgid "$80"
msgstr "$80"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "% completed"
msgstr "% selesai"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (salinan)"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s certification passed"
msgstr "Lulus %s sertifikasi "

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s challenge certification"
msgstr "%s sertifikasi challenge"

#. module: survey
#: model:ir.actions.report,print_report_name:survey.certification_report
msgid "'Certification - %s' % (object.survey_id.display_name)"
msgstr "'Sertifikasi - %s' % (object.survey_id.display_name)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug2
msgid "10 kg"
msgstr "10 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug2
msgid "100 years"
msgstr "100 tahun"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug3
msgid "1055"
msgstr "1055"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug3
msgid "116 years"
msgstr "116 tahun"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug1
msgid "1227"
msgstr "1227"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug4
msgid "127 years"
msgstr "127 tahun"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug2
msgid "1324"
msgstr "1324"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug1
msgid "1450 km"
msgstr "1450 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug3
msgid "16.2 kg"
msgstr "16.2 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug2
msgid "3700 km"
msgstr "3700 km"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "403: Forbidden"
msgstr "403: Dilarang"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug4
msgid "47 kg"
msgstr "47 kg"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "4812"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug1
msgid "5.7 kg"
msgstr "5.7 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug3
msgid "6650 km"
msgstr "6650 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug1
msgid "99 years"
msgstr "99 tahun"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"<b>Certificate</b>\n"
"                            <br/>"
msgstr ""
"<b>Sertifikat</b>\n"
"                            <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<br/>\n"
"                        <span>Score:</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "<br/>by"
msgstr "<br/>oleh"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q6
msgid ""
"<div class=\"text-center\">\n"
"                <div class=\"media_iframe_video\" data-oe-expression=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" style=\"width: 50%;\">\n"
"                    <div class=\"css_editable_mode_display\"/>\n"
"                    <div class=\"media_iframe_video_size\" contenteditable=\"false\"/>\n"
"                    <iframe src=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" frameborder=\"0\" contenteditable=\"false\"/>\n"
"                </div><br/>\n"
"            </div>\n"
"        "
msgstr ""

#. module: survey
#: model:mail.template,body_html:survey.mail_template_certification
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certification: <t t-out=\"object.survey_id.display_name or ''\">Feedback Form</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Dear <span t-out=\"object.partner_id.name or 'participant'\">participant</span></p>\n"
"                <p>\n"
"                    Here is, in attachment, your certification document for\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Feedback Form</strong>\n"
"                </p>\n"
"                <p>Congratulations for succeeding the test!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model:mail.template,body_html:survey.mail_template_user_input_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant'\">participant</t><br/><br/>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            You have been invited to take a new certification.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            We are conducting a survey and your response would be appreciated.\n"
"        </t>\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Start Certification\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Start Survey\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the survey for <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br/><br/>\n"
"        </t>\n"
"        Thank you for your participation.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123..</i>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple "
"lines\" title=\"Multiple Lines\"/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "<i class=\"fa fa-bar-chart\"/> Graph"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-bar-chart\"/> Results"
msgstr "<i class=\"fa fa-bar-chart\"/> Hasil"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg\"/> jawaban"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg\"/> answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg\"/> jawaban"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"
msgstr ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Tidak diperiksa\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-close\"/> Close"
msgstr "<i class=\"fa fa-close\"/> Tutup"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"
msgstr ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Diperiksa\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg\"/> jawaban"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Unduh sertifikasi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<i class=\"fa fa-list-alt\"/> All Data"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "<i class=\"fa fa-list-alt\"/> Data"
msgstr "<i class=\"fa fa-list-alt\"/> Data"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<i class=\"fa fa-list-ol\"/> Most Common"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" "
"title=\"Single Line\"/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-square-o fa-lg\"/> jawaban"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "<i class=\"fa fa-times\"/> Clear All Filters"
msgstr "<i class=\"fa fa-times\"/> Hapus Semua Penyaring"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q3
msgid ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/coniferous.jpg\"/><br/>\n"
"            </p>\n"
"        "
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q4
msgid ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/pinus_sylvestris.jpg\" style=\"width: 100%;\"/><br/>\n"
"            </p>\n"
"        "
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4
msgid ""
"<p>\n"
"                We like to say that the apple doesn't fall far from the tree, so here are trees.\n"
"            </p>\n"
"        "
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3
msgid "<p>An apple a day keeps the doctor away.</p>"
msgstr ""

#. module: survey
#: model:survey.survey,description:survey.survey_demo_burger_quiz
msgid ""
"<p>Choose your favourite subject and show how good you are. Ready ?</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p1_q4
msgid "<p>Just to categorize your answers, don't worry.</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p1
msgid ""
"<p>Some general information about you. It will be used internally for "
"statistics only.</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p2
msgid "<p>Some questions about our company. Do you really know us?</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_3
msgid "<p>Test your knowledge of our policies.</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_2
msgid "<p>Test your knowledge of our prices.</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_1
msgid "<p>Test your knowledge of your products!</p>"
msgstr ""

#. module: survey
#: model:survey.survey,description:survey.vendor_certification
msgid "<p>Test your vendor skills!</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_feedback_p1
msgid ""
"<p>This section is about general information about you. Answering them helps"
" qualifying your answers.</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_feedback_p2
msgid "<p>This section is about our eCommerce experience itself.</p>"
msgstr ""

#. module: survey
#: model:survey.survey,description:survey.survey_demo_quiz
msgid ""
"<p>This small quiz will test your knowledge about our Company. Be prepared "
"!</p>"
msgstr ""

#. module: survey
#: model:survey.survey,description:survey.survey_feedback
msgid ""
"<p>This survey allows you to give a feedback about your experience with our products.\n"
"    Filling it helps us improving your experience.</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p5
msgid "<p>We may be interested by your input.</p>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span attrs=\"{'invisible': [('is_time_limited', '=', False)]}\"> "
"seconds</span>"
msgstr ""
"<span attrs=\"{'invisible': [('is_time_limited', '=', False)]}\"> detik-"
"detik</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid ""
"<span class=\"badge badge-primary only_left_radius\"><i class=\"fa fa-"
"filter\" role=\"img\" aria-label=\"Filter\" title=\"Filter\"/></span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Average </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Maximum </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Minimum </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "<span class=\"fa fa-filter\"/>  Filters"
msgstr "<span class=\"fa fa-filter\"/>  Penyaring"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\"> or "
"press Enter</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\">or press"
" Enter</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&lt;', 2)]}\">Sertifikasi-Sertifikasi</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&gt;', 1)]}\">Sertifikasi</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&lt;', 2)]}\">Sertifikasi-Sertifikasi</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&gt;', 1)]}\">Sertifikasi</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid ""
"<span class=\"o_survey_enter font-weight-bold text-muted ml-2\">or press "
"Enter</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_selection_key
msgid ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"left py-0 pl-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Key</span></span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"
msgstr ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid ""
"<span class=\"o_survey_session_error_invalid_code d-none\">Code is incorrect.</span>\n"
"                                    <span class=\"o_survey_session_error_closed d-none\">Session is finished.</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "<span class=\"text-muted\">Answers</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "<span class=\"text-muted\">Success</span>"
msgstr "<span class=\"text-muted\">Sukses</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q1
msgid ""
"<span>\"Red\" is not a category, I know what you are trying to do ;)</span>"
msgstr ""
"<span>\"Merah\" bukan merupakan kategori, saya tahu apa yang Anda coba "
"lakukan ;)</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q6
msgid "<span>Best time to do it, is the right time to do it.</span>"
msgstr "<span>Best time to do it, is the right time to do it.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "<span>Date</span>"
msgstr "<span>Tanggal</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p5_q1
msgid ""
"<span>If you don't like us, please try to be as objective as "
"possible.</span>"
msgstr ""
"<span>Bila Anda tidak menyukai kami, mohon tetap coba untuk objektif sebisa "
"mungkin.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "<span>Number of attemps left</span>:"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p2_q1
msgid "<span>Our famous Leader !</span>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q3
msgid "<span>Our sales people have an advantage, but you can do it !</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span>Right answer:</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "<span>Time limit for this survey: </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "<span>Waiting for attendees...</span>"
msgstr "<span>Menunggu peserta...</span>"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q4
msgid "A \"Citrus\" could give you ..."
msgstr "\"Jeruk\" dapat memberikan Anda ..."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "A label must be attached to only one question."
msgstr "Label harus dilampirkan hanya ke satu pertanyaan."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_max
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_min
msgid "A length must be positive!"
msgstr "Panjang harus positif!"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_4
msgid "A little bit overpriced"
msgstr "Agak terlalu mahal"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_5
msgid "A lot overpriced"
msgstr "Sangat mahal"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__answer_score
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""
"Nilai positif mengindikasikan pilihan yang benar; nilai negatif atau nol "
"mengindikasikan jawaban yang salah"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "A problem has occurred"
msgstr "Terjadi masalah"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "A question can either be skipped or answered, not both."
msgstr "Pertanyaan dapat dilewati atau dijawab, tidak keduanya."

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2
msgid "About our ecommerce"
msgstr "Mengenai ecommerce kami"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1
msgid "About you"
msgstr "Mengenai Anda"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_access_mode
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_mode
msgid "Access Mode"
msgstr "Mode Akses"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_token
msgid "Access Token"
msgstr "Token Akses"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_access_token_unique
msgid "Access token should be unique"
msgstr "Token akses harus unik"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
msgid "Action Needed"
msgstr "Perlu Tindakan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "Aktif"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
msgid "Activities"
msgstr "Aktivitas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorasi Pengecualian Aktivitas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
msgid "Activity State"
msgstr "Status Aktivitas"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon Jenis Aktifitas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Add a list of email of recipients (will not be converted into contacts). "
"Separated by commas, semicolons or newline..."
msgstr ""
"Tambahkan daftar email penerima (tidak ada konversi kontak). Dipisahkan "
"dengan koma, titik koma atau baris baru..."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add a new survey"
msgstr "Tambahkan survei baru"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Add a question"
msgstr "Tambahkan pertanyaan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Add a section"
msgstr "Add a section"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Add existing contacts..."
msgstr "Tambahkan kontak yang sudah ada..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__emails
msgid "Additional emails"
msgstr "Email-email tambahan"

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Administrator"
msgstr "Administrator"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug1
msgid "Africa"
msgstr "Afrika"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q6
msgid ""
"After watching this video, will you swear that you are not going to "
"procrastinate to trim your hedge this year ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col3
msgid "Agree"
msgstr "Setuju"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_date_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Date\" questions "
"need an answer"
msgstr ""
"Semua pertanyaan \"Is a scored question = True\" dan \"Question Type: Date\""
" membutuhkan jawaban"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_datetime_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Datetime\" "
"questions need an answer"
msgstr ""
"Semua pertanyaan \"Is a scored question = True\" dan \"Question Type: "
"Datetime\" membutuhkan jawaban"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__all
msgid "All questions"
msgstr "Semua pertanyaan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "All surveys"
msgstr "Semua survei"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Allow Comments"
msgstr "Izinkan Komentar"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug2
msgid "Amenhotep"
msgstr "Amenhotep"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_user_input_unique_token
msgid "An access token must be unique!"
msgstr "Token akses harus unik!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_answer_score
msgid "An answer score for a non-multiple choice question cannot be negative!"
msgstr ""
"Nilai jawaban untuk pertanyaan yang bukan pertanyaan pilihan ganda tidak "
"boleh negatif!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_leaderboard
msgid "Anonymous"
msgstr "Anonymous"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Answer"
msgstr "Jawaban"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr "Jenis"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__deadline
msgid "Answer deadline"
msgstr "Deadline jawaban"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_answer_id
msgid "Answer that will trigger the display of the current question."
msgstr "Jawaban yang akan memicu penampilan pertanyaan saat ini."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Answered"
msgstr "Dijawab"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Answers"
msgstr "Semua Jawaban"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_answer_count
msgid "Answers Count"
msgstr "Jumlah Jawaban"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__public
msgid "Anyone with the link"
msgstr "Siapapun dengan link"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Tampil di"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug1
msgid "Apple Trees"
msgstr "Pohon Apel"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row1
msgid "Apples"
msgstr "Apel-Apel"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug4
msgid "Art & Culture"
msgstr "Seni & Kultur"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug1
msgid "Arthur B. McDonald"
msgstr "Arthur B. McDonald"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug2
msgid "Asia"
msgstr "Asia"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__attachment_ids
msgid "Attachments"
msgstr "Lampiran"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_number
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempt n°"
msgstr "Percobaan n°"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_done_count
msgid "Attempts"
msgstr "Percobaan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Attempts Limit"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__nickname
msgid ""
"Attendee nickname, mainly used to identify him in the survey session "
"leaderboard."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Attendees are answering the question..."
msgstr "Peserta sedang menjawab pertanyaan..."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating
msgid "Attendees get more points if they answer quickly"
msgstr ""
"Peserta-peserta mendapatkan lebih banyak poin bila mereka menjawab dengan "
"cepat"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__author_id
msgid "Author"
msgstr "Penulis"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__author_id
msgid "Author of the message."
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__certification_mail_template_id
msgid ""
"Automated email sent to the user when he succeeds the certification, "
"containing his certification document."
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug3
msgid "Autumn"
msgstr "Musim Gugur"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_duration_avg
msgid "Average Duration"
msgstr "Durasi Rata-Rata"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__answer_duration_avg
msgid "Average duration of the survey (in hours)"
msgstr "Durasi rata-rata survei (dalam jam)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_score_avg
msgid "Avg Score %"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug3
msgid "Avicii"
msgstr "Avicii"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Back Button"
msgstr "Tombol Kembali"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image
msgid "Background Image"
msgstr "Gambar Latar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge"
msgstr "Lencana"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug3
msgid "Baobab Trees"
msgstr "Pohon Baobab"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug1
msgid "Bees"
msgstr "Lebah"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug4
msgid "Bricks"
msgstr "Batu Bata"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_burger_quiz
msgid "Burger Quiz"
msgstr "Quiz Burger"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_3
msgid "Cabinet with Doors"
msgstr "Kabinet dengan Pintu"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row1
msgid "Cactus"
msgstr "Kaktus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__can_edit_body
msgid "Can Edit Body"
msgstr "Dapat Mengedit Badan"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q3
msgid "Can Humans ever directly see a photon ?"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Cancel"
msgstr "Batal"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Candidates"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
msgid "Certification"
msgstr "Sertifikat"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id
msgid "Certification Badge"
msgstr "Lencana Sertifikasi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id_dummy
msgid "Certification Badge "
msgstr "Lencana Sertifikasi "

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Certification Badge is not configured for the survey %(survey_name)s"
msgstr "Lencana Sertifikasi tidak dikonfigurasi untuk survei %(survey_name)s"

#. module: survey
#: model:mail.template,report_name:survey.mail_template_certification
msgid "Certification Document"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "Certification Failed"
msgstr "Sertifikasi Gagal"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Certification Template"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "Certification n°"
msgstr "Sertifikasi n°"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_report_layout
msgid "Certification template"
msgstr "Templat Sertifikasi"

#. module: survey
#: model:mail.template,subject:survey.mail_template_certification
msgid "Certification: {{ object.survey_id.display_name }}"
msgstr "Sertifikasi: {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.report,name:survey.certification_report
#: model:ir.model.fields.selection,name:survey.selection__gamification_challenge__challenge_category__certification
msgid "Certifications"
msgstr "Sertifikat-Sertifikat"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_count
msgid "Certifications Count"
msgstr "Jumlah Sertifikasi"

#. module: survey
#: model:ir.actions.act_window,name:survey.res_partner_action_certifications
msgid "Certifications Succeeded"
msgstr "Sertifikasi Berhasil"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Certified"
msgstr "Bersertifikasi"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_1
msgid "Chair floor protection"
msgstr "Perlindungan lantai kursi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Chart"
msgstr "Bagan"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,help:survey.field_survey_user_input__is_attempts_limited
msgid "Check this option if you want to limit the number of attempts per user"
msgstr "Centang opsi ini bila Anda ingin membatasi jumlah percobaan per user"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug2
msgid "China"
msgstr "China"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "Pilihan"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_blue
msgid "Classic Blue"
msgstr "Biru Klasik"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_gold
msgid "Classic Gold"
msgstr "Emas Klasi"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_purple
msgid "Classic Purple"
msgstr "Ungu Klasi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row3
msgid "Clementine"
msgstr "Jeruk Clementine"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug4
msgid "Cliff Burton"
msgstr "Cliff Burton"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Close"
msgstr "Tutup"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Close Live Session"
msgstr "Tutup Sesi Live"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_1
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Color"
msgstr "Warna"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr "Indeks Warna"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_comments
msgid "Comment"
msgstr "Komentar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment Field is an Answer Choice"
msgstr "Komentar Lapangan adalah Jawaban Pilihan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr "Komentar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_company_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_company_count
msgid "Company Certifications Count"
msgstr "Jumlah Sertifikasi Perusahaan"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__done
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Completed"
msgstr "Selesai"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Compose Email"
msgstr "Buat Email"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "Computing score requires a question in arguments."
msgstr "Menghitung nilai membutuhkan pertanyaan dalam argument."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_conditional
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Conditional Display"
msgstr "Tampilan Kondisional"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_3
msgid "Conference chair"
msgstr "Kursi konferensi"

#. module: survey
#: model_terms:gamification.badge,description:survey.vendor_certification_badge
msgid "Congratulations, you are now official vendor of MyCompany"
msgstr "Selamat, Anda sekarang vendor resmi untuk PerusahaanSaya"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Congratulations, you have passed the test!"
msgstr "Selamat, Anda lulus test!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "Kendala"

#. module: survey
#: model:ir.model,name:survey.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_conditional_questions
msgid "Contains conditional questions"
msgstr "Memiliki pertanyaan kondisional"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body
msgid "Contents"
msgstr "Konten"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Continue"
msgstr "Lanjutkan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "Continue here"
msgstr "Lanjutkan di sini"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug4
msgid "Cookies"
msgstr "Cookies"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#, python-format
msgid "Copied !"
msgstr "Disalin !"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug3
msgid "Cornaceae"
msgstr "Cornaceae"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_1
msgid "Corner Desk Right Sit"
msgstr "Meja Sudut Kanan"

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_is_correct
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#, python-format
msgid "Correct"
msgstr "Benar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Correct Answer"
msgstr "Jawaban Benar"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_datetime
msgid "Correct date and time answer for this question."
msgstr "Jawaban tanggal dan waktu yang benar untuk pertanyaan ini."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_date
msgid "Correct date answer"
msgstr "Jawaban tanggal yang benar"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_date
msgid "Correct date answer for this question."
msgstr "Jawaban tanggal yang benar untuk pertanyaan ini."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_datetime
msgid "Correct datetime answer"
msgstr "Jawaban tanggalwaktu yang benar"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_numerical_box
msgid "Correct number answer for this question."
msgstr "Jawaban angka yang benar untuk pertanyaan ini."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_numerical_box
msgid "Correct numerical answer"
msgstr "Jawaban numerik yang benar"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_3
msgid "Correctly priced"
msgstr "DIberikan harga yang benar"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug3
msgid "Cosmic rays"
msgstr "Cosmic rays"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Create Live Session"
msgstr "Buat Sesi Live"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating test token is not allowed for you."
msgstr "Anda tidak diizinkan membuat token ujian."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for anybody else than employees is not allowed for internal "
"surveys."
msgstr ""
"Membuat token untuk siapapun selain karyawan tidak diizinkan untuk survei "
"internal."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating token for closed/archived surveys is not allowed."
msgstr "Membuat token untuk survei yang ditutup/diarsip tidak diizinkan."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for external people is not allowed for surveys requesting "
"authentication."
msgstr ""
"Membuat token untuk orang eksternal tidak diizinkan untuk survei yang "
"memerlukan autentikasi."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_id
msgid "Current Question"
msgstr "Pertanyaan Saat Ini"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_start_time
msgid "Current Question Start Time"
msgstr "Waktu Mulai Pertanyaan Saat Ini"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_start_time
msgid "Current Session Start Time"
msgstr "Waktu Mulai Sesi Saat Ini"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_time_limited
msgid "Currently only supported for live sessions."
msgstr "Saat ini hanya mendukung sesi live."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Customers will receive a new token and be able to completely retake the "
"survey."
msgstr "Pelanggan akan menerima token baru dan dapat mengisi ulang survei."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Customers will receive the same token."
msgstr "Pelanggan akan menerima token yang sama."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_5
msgid "Customizable Lamp"
msgstr "Lampu yang Dapat Dikustomisasi"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__date
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__date
msgid "Date"
msgstr "Tanggal"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr "Jenis"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__datetime
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__datetime
msgid "Datetime"
msgstr "Datetime"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_datetime
msgid "Datetime answer"
msgstr "Jawaban tanggalwaktu"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Datetime until customer can open the survey and submit answers"
msgstr ""
"Tanggalwaktu sampai pelanggan dapat membuka survei dan mengisi jawaban"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr "Batas waktu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__null_value
msgid "Default Value"
msgstr "Nilai Baku"

#. module: survey
#: model:ir.model.fields,help:survey.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Menentukan visibilitas tantangan melalui menu"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Delete"
msgstr "Hapus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Description"
msgstr "Deskripsi"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Design easily your survey, send invitations and analyze answers."
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_2
msgid "Desk Combination"
msgstr "Kombinasi Meja"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_user_input_line_action
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "Detailed Answers"
msgstr "Jawaban Detail"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col2
msgid "Disagree"
msgstr "Berselisih"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Display"
msgstr "Tampilan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__allow_value_image
msgid ""
"Display images in addition to answer label. Valid only for simple / multiple"
" choice questions."
msgstr ""

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_1
msgid "Do we sell Acoustic Bloc Screens?"
msgstr "Apakah Anda menjual Layar Acoustic Bloc?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q3
msgid "Do you have any other comments, questions, or concerns ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_5
msgid "Do you think we have missing products in our catalog? (not rated)"
msgstr ""
"Apakah Anda merasa kita kurang produk dalam katalog kami? (tidak dirating)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug2
msgid "Dogs"
msgstr "Anjing"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q1
msgid "Dogwood is from which family of trees ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug1
msgid "Douglas Fir"
msgstr "Douglas Fir"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_4
msgid "Drawer"
msgstr "Laci"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Dropdown menu"
msgstr "Menu dropdown"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Edit Survey"
msgstr "Sunting Survei"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "Edit in backend"
msgstr "Edit di backend"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Email"
msgstr "Email"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_mail_template_id
msgid "Email Template"
msgstr "Template Email"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__email_from
msgid "Email address of the sender."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__description_done
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "End Message"
msgstr "Tutup Pesan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__end_datetime
msgid "End date and time"
msgstr "Tanggal dan waktu akhir"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Enter Session Code"
msgstr "Masukkan Kode Sesi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr "Pesan Error"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug3
msgid "Europe"
msgstr "Eropa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug3
msgid "European Yew"
msgstr "European Yew"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Except Test Entries"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_partner_ids
msgid "Existing Partner"
msgstr "Partner Tersedia"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_emails
msgid "Existing emails"
msgstr "Email yang ada"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug2
msgid "Eyjafjallajökull (Iceland)"
msgstr "Eyjafjallajökull (Iceland)"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_2
msgid "Fanta"
msgstr "Fanta"

#. module: survey
#: model:survey.survey,title:survey.survey_feedback
msgid "Feedback Form"
msgstr "Formulir Feedback"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row2
msgid "Ficus"
msgstr "Ficus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__model_object_field
msgid "Field"
msgstr "Kolom"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Filter question"
msgstr ""

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#, python-format
msgid "Final Leaderboard"
msgstr "Leaderboard Final"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Ekspresi placeholder akhir, akan disalin pada kolom contoh yang diinginkan."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Finished surveys"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
msgid "Followers"
msgstr "Pengikut"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pengikut (Rekanan)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikon font awesome, misalnya fa-tasks"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__text_box
msgid "Free Text"
msgstr "Teks Gratis"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text_box
msgid "Free Text answer"
msgstr "Gratis Teks Jawaban"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__email_from
msgid "From"
msgstr "Dari"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q4
msgid "From which continent is native the Scots pine (pinus sylvestris) ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug1
msgid "Fruits"
msgstr "Buah-Buah"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3
msgid "Fruits and vegetables"
msgstr "Buah dan sayuran"

#. module: survey
#: model:ir.model,name:survey.model_gamification_badge
msgid "Gamification Badge"
msgstr "Lencana Gamifikasi"

#. module: survey
#: model:ir.model,name:survey.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Tantangan Gamifikasi"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug1
msgid "Geography"
msgstr "Geografi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_give_badge
msgid "Give Badge"
msgstr "Berikan Lencana"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q3
msgid "Give the list of all types of wood we sell."
msgstr "Berikan daftar semua tipe kayu yang kita jual."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "Go to"
msgstr "Pergi ke"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug1
msgid "Good"
msgstr "Baik"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug4
msgid "Good value for money"
msgstr "Good value for money"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug2
msgid "Grapefruits"
msgstr "Grapefruit"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Graph"
msgstr "Grafik"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Handle existing"
msgstr "Tangani yang ada"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug1
msgid "Hard"
msgstr "Susah"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_2
msgid "Height"
msgstr "Tinggi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug3
msgid "Hemiunu"
msgstr "Hemiunu"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug1
msgid "High quality"
msgstr "Kualitas tinggi"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug2
msgid "History"
msgstr "Riwayat"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q3
msgid "How frequently do you buy products online ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q1
msgid "How long is the White Nile river?"
msgstr "Berapa panjang sungai Nil Putih?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_6
msgid ""
"How many chairs do you think we should aim to sell in a year (not rated)?"
msgstr ""
"Menurut Anda berapa banyak kursi yang seharusnya kita jual dalam setahun "
"(tidak dirating)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_1
msgid "How many days is our money-back guarantee?"
msgstr "Berapa banyak hari adalah jaminan uang-kembali kami?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q4
msgid "How many times did you order products on our website ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_4
msgid "How many versions of the Corner Desk do we have?"
msgstr "Berapa banyak versi Meja Sudut yang kami miliki?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q3
msgid "How many years did the 100 years war last ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_1
msgid "How much do we sell our Cable Management Box?"
msgstr "Berapa harga jual Kotak Manajemen Kabel kami?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q5
msgid "How often should you water those plants"
msgstr "Seberapa sering Anda sebaiknya menyirami tanaman tersebut"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q4
msgid "How old are you ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug4
msgid ""
"I actually don't like thinking. I think people think I like to think a lot. "
"And I don't. I do not like to think at all."
msgstr ""
"Saya sebenarnya tidak suka berpikir. Saya pikir orang berpikir saya suka "
"banyak berpikir. Dan saya tidak suka. Saya sama sekali tidak suka berpikir."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug2
msgid ""
"I am fascinated by air. If you remove the air from the sky, all the birds "
"would fall to the ground. And all the planes, too."
msgstr ""
"Saya sangat tertarik mengenai udara. Bila Anda menghapus udara dari langit, "
"semua burung akan jatuh ke tanah. Dan semua pesawat, juga."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row5
msgid "I have added products to my wishlist"
msgstr "Saya telah menambahkan produk ke wishlist saya"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug4
msgid "I have no idea, I'm a dog!"
msgstr "Saya tidak tahu sama sekali, saya hanya anjing!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug3
msgid "I've been noticing gravity since I was very young !"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon untuk menunjukkan sebuah aktivitas pengecualian."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__access_token
msgid "Identification token"
msgstr "Identifikasi tanda"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__progression_mode
msgid ""
"If Number is selected, it will display the number of questions answered on "
"the total number of question to answer."
msgstr ""
"Bila Angka dipilih, akan menampilkan jumlah angka pertanyaan yang dijawab "
"pada jumlah total pertanyaan untuk dijawab."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_3
msgid ""
"If a customer purchases a 1 year warranty on 6 January 2020, when do we "
"expect the warranty to expire?"
msgstr ""
"Bila pelanggan membeli garansi 1 tahun pada 6 Januari 2020, pada tanggal "
"kapan kami mengharapkan garansi tersebut berakhir?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_2
msgid ""
"If a customer purchases a product on 6 January 2020, what is the latest day "
"we expect to ship it?"
msgstr ""
"Bila pelanggan membeli produk pada 6 Januari 2020, kapan hari terakhir kita "
"diharapkan untuk mengirimkan produk tersebut?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_email
msgid ""
"If checked, this option will save the user's answer as its email address."
msgstr ""
"Bila dicentang, opsi ini akan menyimpan jawaban user sebagai alamat "
"emailnya."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_nickname
msgid "If checked, this option will save the user's answer as its nickname."
msgstr ""
"Bila dicentang, opsi ini akan menyimpan jawaban user sebagai nama "
"panggilannya."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_conditional
msgid ""
"If checked, this question will be displayed only \n"
"        if the specified conditional answer have been selected in a previous question"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr "Jika diperiksa, pengguna dapat kembali ke halaman sebelumnya."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,help:survey.field_survey_survey__users_login_required
msgid ""
"If checked, users have to login before answering even with a valid token."
msgstr ""
"Bila dicentang, user harus login sebelum menjawab bahkan dengan token valid."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q6
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q6
#: model:survey.question,comments_message:survey.survey_demo_quiz_p5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p5_q1
#: model:survey.question,comments_message:survey.survey_feedback_p1
#: model:survey.question,comments_message:survey.survey_feedback_p1_q1
#: model:survey.question,comments_message:survey.survey_feedback_p1_q2
#: model:survey.question,comments_message:survey.survey_feedback_p1_q3
#: model:survey.question,comments_message:survey.survey_feedback_p1_q4
#: model:survey.question,comments_message:survey.survey_feedback_p2
#: model:survey.question,comments_message:survey.survey_feedback_p2_q1
#: model:survey.question,comments_message:survey.survey_feedback_p2_q2
#: model:survey.question,comments_message:survey.survey_feedback_p2_q3
#: model:survey.question,comments_message:survey.vendor_certification_page_1
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_4
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_5
#: model:survey.question,comments_message:survey.vendor_certification_page_2
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_4
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_5
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_6
#, python-format
msgid "If other, please specify:"
msgstr "Jika tidak ada, silahkan sebutkan:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__questions_selection
msgid ""
"If randomized is selected, add the number of random questions next to the "
"section."
msgstr ""
"Bila pengacak dipilih, tambahkan jumlah angka pertanyaan yang diacak di "
"sebelah bagian. "

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__questions_selection
msgid ""
"If randomized is selected, you can configure the number of random questions "
"by section. This mode is ignored in live session."
msgstr ""
"Bila pengacak dipilih, Anda dapat mengonfigurasi jumlah pertanyaan yang "
"diacak berdasarkan bagian. Mode ini diabaikan di sesi live."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "If you wish, you can"
msgstr "Jika Anda ingin, Anda dapat"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image
msgid "Image"
msgstr "Gambar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__allow_value_image
msgid "Images on answers"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug1
msgid "Imhotep"
msgstr "Imhotep"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug6
msgid "Impractical"
msgstr "Tidak praktis"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__in_progress
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__in_progress
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "In Progress"
msgstr "Dalam Proses"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q5
msgid "In the list below, select all the coniferous."
msgstr "Di daftar di bawah, pilih semua yang bersifat coniferous."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q2
msgid "In which country did the bonsai technique develop ?"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_scored_question
msgid ""
"Include this question as part of quiz scoring. Requires an answer and answer"
" score to be taken into account."
msgstr ""
"Masukkan pertanyaan ini sebagai bagian dari penilaian quiz. Membutuhkan "
"jawaban dan nilai jawaban sebelum dipertimbangkan."

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Incorrect"
msgstr "Salah"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug7
msgid "Ineffective"
msgstr "Tidak efektif"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr "Input harus email"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__invite_token
msgid "Invite token"
msgstr "Token undangan"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__token
msgid "Invited people only"
msgstr "Khusus untuk pengikut yang diundang"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "Is Editor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
msgid "Is Follower"
msgstr "Pengikut"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification
msgid "Is a Certification"
msgstr "Apakah Sertifikasi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__is_correct
msgid "Is a correct answer"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_page
msgid "Is a page?"
msgstr "Apakah halaman?"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_session_answer
msgid "Is in a Session"
msgstr "Apakah dalam Sesi"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__is_session_answer
msgid "Is that user input part of a survey session or not."
msgstr "Apakah input user bagian dari sesi survei atau tidak."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q3
msgid "Is the wood of a coniferous hard or soft ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug4
msgid "Istanbul"
msgstr "Istanbul"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row2
msgid "It is easy to find the product that I want"
msgstr "Gampang untuk mencari produk yang saya ingin"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug3
msgid "Iznogoud"
msgstr "Iznogoud"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug1
msgid ""
"I’ve never really wanted to go to Japan. Simply because I don’t like eating "
"fish. And I know that’s very popular out there in Africa."
msgstr ""
"Saya sebenarnya tidak pernah ingin ke Jepang. Hanya karena saya tidak suka "
"ikan. Dan saya tahu ikan sangat populer di Afrika."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug1
msgid "Japan"
msgstr "Japan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Join Session"
msgstr "Ikuti Sesi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug2
msgid "Kim Jong-hyun"
msgstr "Kim Jong-hyun"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug1
msgid "Kurt Cobain"
msgstr "Kurt Cobain"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__sequence
msgid "Label Sequence order"
msgstr "Label rangka Urutan"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__matrix_row_ids
msgid "Labels used for proposed choices: rows of matrix"
msgstr ""
"Label-label yang digunakan untuk pilihan yang diajukan: baris-baris matriks"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__suggested_answer_ids
msgid ""
"Labels used for proposed choices: simple choice, multiple choice and columns"
" of matrix"
msgstr ""
"Label-label yang digunakan untuk pilihan yang diajukan: pilihan simpel, "
"ganda, dan kolom-kolom matriks"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__lang
msgid "Language"
msgstr "Bahasa"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_4
msgid "Large Desk"
msgstr "Meja Besar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question_answer____last_update
#: model:ir.model.fields,field_description:survey.field_survey_survey____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed question/page"
msgstr "Pertanyaan/halaman yang ditampilkan terakhir"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr "Aktifitas terakhir"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_layout
msgid "Layout"
msgstr "Layout"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Leaderboard"
msgstr "Leaderboard"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_4
msgid "Legs"
msgstr "Kursi-Kursi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug2
msgid "Lemon Trees"
msgstr "Pohon Lemon"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_attempts_limited
msgid "Limited number of attempts"
msgstr "Jumlah percobaan yang terbatas"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Live Session"
msgstr "Sesi Live"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Live Sessions"
msgstr "Sesi-Sesi Live"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_login_required
msgid "Login Required"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "Login required"
msgstr "Masuk diwajibkan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__template_id
msgid "Mail Template"
msgstr "Templat Email"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_main_attachment_id
msgid "Main Attachment"
msgstr "Lampiran Utama"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "Wajib penggunaan template."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__matrix
msgid "Matrix"
msgstr "Matriks"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_row_ids
msgid "Matrix Rows"
msgstr "Baris-Baris Matriks"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr "Matrix Jenis"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_date
msgid "Max date cannot be smaller than min date!"
msgstr "Tanggal Max tidak bisa lebih kecil dari min tanggal!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_datetime
msgid "Max datetime cannot be smaller than min datetime!"
msgstr "Tanggalwaktu maks tidak bisa lebih kecil dari tanggalwaktu min!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_length
msgid "Max length cannot be smaller than min length!"
msgstr "Panjang maksimal tidak bisa lebih kecil dari panjang min!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_float
msgid "Max value cannot be smaller than min value!"
msgstr "Nilai Max tidak bisa lebih kecil dari nilai min!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr "Jumlah penghapusan maksimum"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_datetime
msgid "Maximum Datetime"
msgstr "Tanggalwaktu Maksimum"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr "Maksimum Teks Panjang"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr "Nilai maksimum"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "Maybe you were looking for"
msgstr "Mungkin Anda mencari"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
msgid "Messages"
msgstr "Pesan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr "Minimum Tanggal"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_datetime
msgid "Minimum Datetime"
msgstr "Tanggalwaktu Minimum"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr "Minimum Teks Panjang"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr "Nilai minimum"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Missed"
msgstr "Tidak dijawab"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_blue
msgid "Modern Blue"
msgstr "Biru Modern"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_gold
msgid "Modern Gold"
msgstr "Emas Modern"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_purple
msgid "Modern Purple"
msgstr "Ungu Moder"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug3
msgid "Mooses"
msgstr "Rubah Besar"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug4
msgid "Mount Elbrus (Russia)"
msgstr "Mount Elbrus (Russia)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug3
msgid "Mount Etna (Italy - Sicily)"
msgstr "Mount Etna (Italy - Sicily)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug1
msgid "Mount Teide (Spain - Tenerife)"
msgstr "Mount Teide (Spain - Tenerife)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug4
msgid "Mountain Pine"
msgstr "Mountain Pine"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__text_box
msgid "Multiple Lines Text Box"
msgstr "Kotak Teks Beberapa Baris"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr "Pilihan ganda dengan jawaban ganda"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr "Pilihan ganda dengan satu jawaban"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__multiple_choice
msgid "Multiple choice: multiple answers allowed"
msgstr "Pilihan ganda: beberapa jawaban diperbolehkan"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__simple_choice
msgid "Multiple choice: only one answer"
msgstr "Pilihan ganda: hanya satu jawaban"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__multiple
msgid "Multiple choices per row"
msgstr "Beberapa pilihan per baris"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline Kegiatan Saya"

#. module: survey
#: model:gamification.badge,name:survey.vendor_certification_badge
msgid "MyCompany Vendor"
msgstr "Vendor PerusahaanSaya"

#. module: survey
#: model:survey.survey,title:survey.vendor_certification
msgid "MyCompany Vendor Certification"
msgstr "Sertifikasi Vendor PerusahaanSaya"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "New"
msgstr "Baru"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug3
msgid "New York"
msgstr "New York"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__new
msgid "New invite"
msgstr "Undangan baru"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kalender Acara Aktivitas Berikutnya"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Batas Waktu Aktivitas Berikutnya"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
msgid "Next Activity Summary"
msgstr "Ringkasan Aktivitas Berikutnya"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
msgid "Next Activity Type"
msgstr "Tipe Aktivitas Berikutnya"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__nickname
msgid "Nickname"
msgstr "Nama panggilan"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug2
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_1
msgid "No"
msgstr "Tidak"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "No attempts left."
msgstr "Tidak ada percobaan lagi yang tersisa."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "No question yet, come back later."
msgstr "Belum ada pertanyaan, kembali lagi nanti."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No questions found"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__no_scoring
msgid "No scoring"
msgstr "Tidak ada nilai"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_question_answer_action
msgid "No survey labels found"
msgstr "Tidak ada label survei yang ditemuka"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_user_input_line_action
msgid "No user input lines found"
msgstr "Tidak ada baris input user yang ditemukan"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug2
msgid "No, it's to small for the human eye."
msgstr "Tidak, itu terlalu kecil untuk mata manusia."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid "Nobody has replied to your surveys yet"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug2
msgid "Norway Spruce"
msgstr "Norway Spruce"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug2
msgid "Not Good, Not Bad"
msgstr "Tidak Bagus, Tidak Jelek"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__new
msgid "Not started yet"
msgstr "Belum dimulai"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__number
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__numerical_box
msgid "Number"
msgstr "Nomor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Tindakan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__attempts_limit
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_limit
msgid "Number of attempts"
msgstr "Jumlah percobaan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__column_nb
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Number of columns"
msgstr "Jumlah kolom"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_5
msgid "Number of drawers"
msgstr "Jumlah laci"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Jumlah pesan yang butuh tindakan"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah dari pesan dengan kesalahan pengiriman"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread_counter
msgid "Number of unread messages"
msgstr "Jumlah pesan yang belum dibaca"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__numerical_box
msgid "Numerical Value"
msgstr "Bilangan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_numerical_box
msgid "Numerical answer"
msgstr "Jawaban Pilihan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "Occurrence"
msgstr "Occurrence"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_5
msgid "Office Chair Black"
msgstr "Kursi Kantor Hitam"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug1
msgid "Once a day"
msgstr "Sekali setiap hari"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug1
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug3
msgid "Once a month"
msgstr "Sekali Sebulan"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug2
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug2
msgid "Once a week"
msgstr "Sekali setiap minggu"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug4
msgid "Once a year"
msgstr "Sekali setiap tahun"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__simple
msgid "One choice per row"
msgstr "Satu pilihan per baris"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_question
msgid "One page per question"
msgstr "Satu halaman per pertanyaan"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_section
msgid "One page per section"
msgstr "Satu halaman per bagian"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__one_page
msgid "One page with all the questions"
msgstr "Satu halaman dengan semua pertanyaan"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Only survey users can manage sessions."
msgstr "Hanya user-user survei yang dapat mengelola sesi."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Open Session Manager"
msgstr "Buka Manajer Sesi"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Bahasa terjemahan opsional (kode ISO) yang dipilih ketika mengirim email. "
"Bila tidak diatur, versi Bahasa Inggris akan digunakan. Ini seharusnya "
"merupakan expression placeholder yang menyediakan bahasa yang sesuai, "
"misalnya {{ object.partner_id.lang }}."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Nilai optional yang digunakan jika kolom target kosong"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Options"
msgstr "Opsi"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "Other (see comments)"
msgstr "Lainnya (lihat komentar)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2
msgid "Our Company in a few questions ..."
msgstr "Perusahaan Saya dalam beberapa pertanyaan ..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__mail_server_id
msgid "Outgoing mail server"
msgstr "Server email keluar"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Overall Performance"
msgstr "Performa Keseluruhan"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug5
msgid "Overpriced"
msgstr "Terlalu Mahal"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Page"
msgstr "Laman"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
msgid "Pages"
msgstr "Halaman"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug4
msgid "Papyrus"
msgstr "Papyrus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Partial"
msgstr "Sebagian"

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Partially"
msgstr "Sebagian"

#. module: survey
#: model:mail.template,subject:survey.mail_template_user_input_invite
msgid "Participate to {{ object.survey_id.display_name }} survey"
msgstr "Berpartisipasi dalam survei {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model:ir.ui.menu,name:survey.survey_menu_user_inputs
msgid "Participations"
msgstr "Partisipasi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partner"
msgstr "Rekanan"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
#, python-format
msgid "Passed"
msgstr "Lolo"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Pay attention to the host screen until the next question."
msgstr "Perhatikan layar host sampai pertanyaan berikutnya."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__percent
msgid "Percentage"
msgstr "Persentase"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Performance by Section"
msgstr "Performa berdasarkan Bagian"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug3
msgid "Perhaps"
msgstr "Mungkin"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug2
msgid "Peter W. Higgs"
msgstr "Peter W. Higgs"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1_q1
msgid "Pick a subject"
msgstr "Pilih subjek"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Pie Graph"
msgstr "Grafik Lingkaran"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug1
msgid "Pinaceae"
msgstr "Pinaceae"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__copyvalue
msgid "Placeholder Expression"
msgstr "Ekspresi Placeholder"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Please enter at least one valid recipient."
msgstr "Silakan masukkan sedikitnya satu penerima valid."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid ""
"Please make sure you have at least one question in your survey. You also "
"need at least one section if you chose the \"Page per section\" layout.<br/>"
msgstr ""
"Mohon pastikan Anda menjawab setidaknya satu pertanyaan di survei Anda. Anda"
" juga membutuhkan setidaknyas atu bagian bila Anda memilih layout \"Halaman "
"per bagian\".<br/>"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3
msgid "Policies"
msgstr "Kebijakan"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug1
msgid "Pomelos"
msgstr "Pomelos"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug8
msgid "Poor quality"
msgstr "Kualitas rendah"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__predefined_question_ids
msgid "Predefined Questions"
msgstr "Pertanyaan yang Didefinisikan Sebelumnya"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Preview"
msgstr "Pratinjau"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2
msgid "Prices"
msgstr "Harga"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Print"
msgstr "Cetak"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1
msgid "Products"
msgstr "Produk"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__progression_mode
msgid "Progression Mode"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question"
msgstr "Pertanyaan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__matrix_question_id
msgid "Question (as matrix row)"
msgstr "Pertanyaan (sebagai baris matriks)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_answer_count
msgid "Question Answers Count"
msgstr "Jumlah Jawaban Pertanyaan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question Time Limit"
msgstr "Batas Waktu Pertanyaan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__question_time_limit_reached
msgid "Question Time Limit Reached"
msgstr "Batas Waktu Pertanyaan Tercapai"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_type
msgid "Question Type"
msgstr "Tipe Pertanyaan"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_question_id
msgid ""
"Question containing the triggering answer to display the current question."
msgstr ""
"Pertanyaan memiliki jawaban pemicu untuk menampilkan pertanyaan berikutnya."

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_question__question_ids
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
#: model:ir.ui.menu,name:survey.survey_menu_questions
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Questions"
msgstr "Pertanyaan"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_quiz
msgid "Quiz about our Company"
msgstr "Quiz mengenai Perusahaan kami"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_success
msgid "Quizz Passed"
msgstr "Quiz yang Anda Sudah Lulus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Quizz passed"
msgstr "Quiz yang Anda sudah lulus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__random_questions_count
msgid "Random questions count"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__random
msgid "Randomized per section"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__ready
msgid "Ready"
msgstr "Siap"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__partner_ids
msgid "Recipients"
msgstr "Penerima"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_count
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Registered"
msgstr "Terdaftar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__render_model
msgid "Rendering Model"
msgstr "Model Rendering"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Reopen"
msgstr "Buka kembali"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_text
msgid "Resend Comment"
msgstr "Kirim Ulang Komentar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Resend Invitation"
msgstr "Kirim Ulang Undanga"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__resend
msgid "Resend invite"
msgstr "Kirim Ulang undangan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_id
msgid "Responsible"
msgstr "Penanggung Jawab"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
msgid "Responsible User"
msgstr "Tanggung-jawab"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Results Overview"
msgstr "Gambaran Umum Hasil"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Retry"
msgstr "Ulangi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating
msgid "Reward quick answers"
msgstr "Berikan hadiah untuk jawaban cepat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Rewards for challenges"
msgstr "Hadiah untuk tantangan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Right answer:"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Right answers:"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__matrix_row_id
msgid "Row answer"
msgstr "Row jawaban"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "Baris1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "Baris2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "Baris3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "Baris"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Kesalahan Pengiriman SMS"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug4
msgid "Salicaceae"
msgstr "Salicaceae"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_email
msgid "Save as user email"
msgstr "Simpan sebagai email user"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_nickname
msgid "Save as user nickname"
msgstr "Simpan sebagai nama panggilan user"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug3
msgid "Sciences"
msgstr "Sains"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_score
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Score"
msgstr "Score"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_percentage
msgid "Score (%)"
msgstr "Nilai (%)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__answer_score
msgid "Score for this choice"
msgstr "Skor untuk pilihan ini"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_score
msgid "Score value for a correct answer to this question."
msgstr "Value nilai untuk jawaban benar untuk pertanyaan ini"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_scored_question
msgid "Scored"
msgstr "Dinilai"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_type
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Scoring"
msgstr "Menilai"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scoring_type
msgid "Scoring Type"
msgstr "Tipe Nilai"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers
msgid "Scoring with answers at the end"
msgstr "Menilai dengan jawaban di akhir"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_without_answers
msgid "Scoring without answers at the end"
msgstr "Menilai tanpa jawaban di akhir"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
msgid "Search Label"
msgstr "Label"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "Cari Pelanggan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Search Survey"
msgstr "Cari proyek"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "Search User input lines"
msgstr "Cari Pengguna masukan baris"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Section"
msgstr "Bagian"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_and_page_ids
msgid "Sections and Questions"
msgstr "Bagian dan Pertanyaan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "See results"
msgstr "Lihat hasil"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_3
msgid "Select all the available customizations for our Customizable Desk"
msgstr "Pilih semua kustomisasi tersedia untuk Customizable Desk kami"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_2
msgid "Select all the existing products"
msgstr "Pilih semua produk yang ada"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_2
msgid "Select all the products that sell for $100 or more"
msgstr "Pilih semua produk yang dijual untuk $100 atau lebih"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Pilih kolom target dari model dokumen terkait.\n"
"Jika berupa kolom relasi, Anda dapat memilih kolom target pada tujuan relasi."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q3
msgid "Select trees that made more than 20K sales this year"
msgstr "Pilih pohon yang membuat lebih dari 20K sales tahun ini"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__questions_selection
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_selection
msgid "Selection"
msgstr "Seleksi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send"
msgstr "Kirim"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_sequence
msgid "Sequence"
msgstr "Urutan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_code
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Session Code"
msgstr "Kode Sesi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_link
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Session Link"
msgstr "Link Sesi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_state
msgid "Session State"
msgstr "Status Sesi"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_code_unique
msgid "Session code should be unique"
msgstr "Kode sesi harus uni"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug1
msgid "Shanghai"
msgstr "Shanghai"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Share"
msgstr "Bagikan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr "Tampilkan Komentar Lapangan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_show_leaderboard
msgid "Show Session Leaderboard"
msgstr "Tunjukkan Leaderboard Sesi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr "Tampilkan semua dokumen dengan aksi berikut sebelum hari ini"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__char_box
msgid "Single Line Text Box"
msgstr "Kotak Teks Baris Tunggal"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Skipped"
msgstr "Sebuah pertanyaan tidak bisa terjawab dan melewatkan"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug2
msgid "Soft"
msgstr "Lembut"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Some emails you just entered are incorrect: %s"
msgstr "Beberapa email yang Anda masukkan tidak benar: %s"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Sorry, no one answered this survey yet."
msgstr "Maaf, belum ada yang menjawab survei ini."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Sorry, you have not been fast enough."
msgstr "Maaf, Anda tidak cukup cepat."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug4
msgid "South America"
msgstr "South America"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug4
msgid "South Korea"
msgstr "Korea Selatan"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug3
msgid "Space stations"
msgstr "Stasiun ruang angkasa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug1
msgid "Spring"
msgstr "Musim Semi"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1
msgid "Start"
msgstr "Mulai"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Certification"
msgstr "Mulai Sertifikasi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Survey"
msgstr "Mulai Survei"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__start_datetime
msgid "Start date and time"
msgstr "Tanggal dan waktu mulai"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr "Status"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status berdasarkan aktivitas\n"
"Terlambat: Batas waktu telah terlewati\n"
"Hari ini: Tanggal aktivitas adalah hari ini\n"
"Direncanakan: Aktivitas yang akan datang."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row2
msgid "Strawberries"
msgstr "Stroberi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__sub_model_object_field
msgid "Sub-field"
msgstr "Sub-kolom"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__sub_object
msgid "Sub-model"
msgstr "Sub-model"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__subject
msgid "Subject"
msgstr "Subjek"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Subject..."
msgstr "Judul"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Submit"
msgstr "Menyerahkan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_count
msgid "Success"
msgstr "Sukses"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_success_min
msgid "Success %"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_ratio
msgid "Success Ratio"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Success rate:"
msgstr "Tingkat kesuksesan:"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_question_answer_action
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Suggested Values"
msgstr "Value-Value yang Disarankan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__suggested_answer_id
msgid "Suggested answer"
msgstr "Disarankan jawaban"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value
msgid "Suggested value"
msgstr "Pengurus yang disarankan"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__suggestion
msgid "Suggestion"
msgstr "Saran"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug2
msgid "Summer"
msgstr "Musim Panas"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Survey"
msgstr "Penelitian"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_view_tree
msgid "Survey Answer Line"
msgstr "Jawaban survei"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_ids
msgid "Survey Ids"
msgstr "Id Survei"

#. module: survey
#: model:ir.model,name:survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "Wizard Undangan Survei"

#. module: survey
#: model:ir.model,name:survey.model_survey_question_answer
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
msgid "Survey Label"
msgstr "Label"

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "Sebuah pertanyaan tidak bisa terjawab dan melewatkan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Survey Time Limit"
msgstr "Batas Waktu Survei"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_time_limit_reached
msgid "Survey Time Limit Reached"
msgstr "Batas Waktu Limit Tercapai"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
msgid "Survey Title"
msgstr "Judul Survei"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_start_url
msgid "Survey URL"
msgstr "URL Survei"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Pengguna"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr "Baris pengguna"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_tree
msgid "Survey User inputs"
msgstr "Input survei Pengguna"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "Survey filter"
msgstr ""

#. module: survey
#: model:ir.actions.server,name:survey.survey_action_server_clean_test_answers
msgid "Survey: Clean test answers"
msgstr "Survei: Bersihkan jawaban tes"

#. module: survey
#: model:mail.template,name:survey.mail_template_user_input_invite
msgid "Survey: Invite"
msgstr "Survei: Undang"

#. module: survey
#: model:mail.template,name:survey.mail_template_certification
msgid "Survey: Send certification by email"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "Semua survei"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug3
msgid "Takaaki Kajita"
msgstr "Takaaki Kajita"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Test"
msgstr "Tes"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Test Entries"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
msgid "Test Entry"
msgstr "Tes Input"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__char_box
msgid "Text"
msgstr "Teks"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_char_box
msgid "Text answer"
msgstr "Teks"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Thank you!"
msgstr ""
"<p>Dear ${object.partner_name atau 'pelamar'},</p>\n"
"        <p>Kami terima kasih atas minat Anda pada perusahaan kami dan untuk aplikasi Anda.\n"
"        Sayangnya, profil Anda tidak cocok dengan kebutuhan kita atau kampanye rekrutmen kami telah mencapai istilah.</p>\n"
"        <p>Jika Anda ingin informasi lebih lanjut, jangan ragu untuk menghubungi kami melalui telepon.</p>\n"
"        <p>Salam</p>\n"
"        <br>${object.user_id dan object.user_id.signature | aman atau ''}"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "The answer must be in the right type"
msgstr "Jawabannya harus dalam jenis yang tepat"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q6
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q6
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p5_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q3
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q4
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_4
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_5
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_4
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_5
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_6
#, python-format
msgid "The answer you entered is not valid."
msgstr "Jawaban yang Anda masukkan tidak valid."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_attempts_limit_check
msgid ""
"The attempts limit needs to be a positive number if the survey has a limited"
" number of attempts."
msgstr ""
"Batas percobaan membutuhkan angka positif bila survei memiliki jumlah "
"batasan percobaan."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_badge_uniq
msgid "The badge for each survey should be unique!"
msgstr "Lencana untuk setiap survei harus unik!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row4
msgid "The checkout process is clear and secure"
msgstr "Proses checkout jelas dan aman"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_id
msgid "The current question of the survey session."
msgstr "Pertanyaan saat ini untuk sesi survei ini."

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "The date you selected is greater than the maximum date: "
msgstr "Tanggal yang Anda pilih lebih besar dari tanggal maksimum:"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "The date you selected is lower than the minimum date: "
msgstr "Tanggal yang Anda pilih lebih dini dari tanggal minimum:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid ""
"The description will be displayed on the home page of the survey. You can "
"use this to give the purpose and guidelines to your candidates before they "
"start it."
msgstr ""
"Keterangan akan ditampilkan pada beranda survei. Anda dapat menggunakan ini "
"untuk memberikan tujuan dan pedoman ke kandidat Anda sebelum mereka memulai."

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following customers have already received an invite"
msgstr "Pelanggan-pelanggan berikut sudah menerima undangan"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following emails have already received an invite"
msgstr "Email-email berikut telah menerima undangan"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external signup in configuration."
msgstr ""
"Penerima berikut tidak memiliki akun user: %s. Anda harus membuat akun user "
"untuk mereka atau mengizinkan pendaftaran eksternal di konfigurasi,"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row1
msgid "The new layout and design is fresh and up-to-date"
msgstr "Layout dan design baru terlihat rapih dan up-to-date"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "The page you were looking for could not be authorized."
msgstr "Halaman yang Anda cari tidak dapat disahkan."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_scoring_success_min_check
msgid "The percentage of success has to be defined between 0 and 100."
msgstr "Persentase kesuksesan harus didefinisikan di antara 0 dan 100."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_limited
msgid "The question is limited in time"
msgstr "Pertanyaan dibatasi waktu"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "The session will begin automatically when the host starts."
msgstr "Sesi akan dimulai secara otomatis saat host memulai."

#. module: survey
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "The survey has already started."
msgstr "Survei sudah dimulai."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_time_limited
msgid "The survey is limited in time"
msgstr "Survei dibatasi waktu"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_start_time
msgid ""
"The time at which the current question has started, used to handle the timer"
" for attendees."
msgstr ""
"Waktu pada mana pertanyaan yang saat ini sudah dimulai, digunakan untuk "
"menangani timer untuk peserta."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_time_limit_check
msgid ""
"The time limit needs to be a positive number if the survey is time limited."
msgstr "Batas waktu membutuhkan angka positif bila survei dibatasi waktu."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row3
msgid "The tool to compare the products is useful to make a choice"
msgstr ""
"Tool untuk membandingkan produk-produk sangat berguna untuk membuat "
"keputusan"

#. module: survey
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "The user has not succeeded the certification"
msgstr "User tidak lulus sertifikasi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "There was an error during the validation of the survey."
msgstr "Terdapat error selama validasi survei"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__column_nb
msgid ""
"These options refer to col-xx-[12|6|4|3|2] classes in Bootstrap for "
"dropdown-based simple and multiple choice questions."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This answer must be an email address"
msgstr "Jawaban ini harus alamat email"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "This answer must be an email address."
msgstr "Jawaban harus merupakan alamat email."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
msgid "This certificate is presented to"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"This certificate is presented to\n"
"                                <br/>"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_code
msgid ""
"This code will be used by your attendees to reach your session. Feel free to"
" customize it however you like!"
msgstr ""
"Code ini akan digunakan oleh peserta Anda untuk memasuki sesi Anda. SIlakan "
"customize sesuai keinginan Anda!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "This is a test survey."
msgstr ""

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/static/src/js/survey_form.js:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a date"
msgstr "Input harus tanggal"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a number"
msgstr "Nomor"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__emails
msgid ""
"This list of emails of recipients will not be converted in contacts.        "
"Emails must be separated by commas, semicolons or newline."
msgstr ""
"Daftar email penerima ini tidak akan dimasukkan ke daftar kontak.        "
"Email harus dipisahkan oleh koma, titik koma, atau baris baru."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description_done
msgid "This message will be displayed when survey is completed"
msgstr "Pesan ini akan ditampilkan saat survei selesai"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "This question depends on another question's answer."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q6
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q6
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p5_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q3
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q4
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_4
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_5
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_4
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_5
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_6
#, python-format
msgid "This question requires an answer."
msgstr "Pertanyaan ini membutuhkan jawaban."

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"This survey does not allow external people to participate. You should create"
" user accounts or update survey access mode accordingly."
msgstr ""
"Survei ini tidak mengizinkan orang eksternal untuk berpartisipasi. Anda "
"harus membuat akun user atau mengupdate mode akses survei."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "This survey is now closed. Thank you for your interest !"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "This survey is open only to registered people. Please"
msgstr "Survei ini terbuka hanya untuk orang yang terdaftar. mohon untuk"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__time_limit
msgid "Time limit (minutes)"
msgstr "Batas waktu (menit)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__time_limit
msgid "Time limit (seconds)"
msgstr "Batas waktu (detik)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__title
msgid "Title"
msgstr "Judul"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid ""
"To take this survey, please close all other tabs on <strong class=\"text-"
"danger\"/>."
msgstr ""
"Untuk mengikuti survei ini, mohon tutup semua tab lain pada <strong "
"class=\"text-danger\"/>."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr "Aktivitas Hari ini"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug2
msgid "Tokyo"
msgstr "Tokyo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_total
msgid "Total Score"
msgstr "Nilai Total"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col4
msgid "Totally agree"
msgstr "Setuju"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col1
msgid "Totally disagree"
msgstr "Benar-benar tidak setuju"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4
msgid "Trees"
msgstr "Pohon-Pohon"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_answer_id
msgid "Triggering Answer"
msgstr "Jawaban Pemicu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_question_id
msgid "Triggering Question"
msgstr "Pertanyaan Pemicu"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "Jenis"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Jenis dari aktivitas pengecualian pada rekaman data."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__suggested_answer_ids
msgid "Types of answers"
msgstr "Jenis barcode"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug2
msgid "Ulmaceae"
msgstr "Ulmaceae"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"Tidak dapat memposting pesan, mohon konfigurasi alamat email pengirim."

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Unanswered"
msgstr "Belum terjawab"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "Uncategorized"
msgstr "Tidak Berkategori"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_2
msgid "Underpriced"
msgstr "Di bawah harga "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Unfortunately, you have failed the test."
msgstr "Sangat disayangkan, namun Anda tidak lulus ujian."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug3
msgid "Unique"
msgstr "Uni"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread
msgid "Unread Messages"
msgstr "Pesan Belum Dibaca"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Penghitung Pesan yang Belum Dibaca"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr "Aktivitas akan datang"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid ""
"Use this field to add additional explanations about your question or to "
"illustrate it with pictures or a video"
msgstr ""
"Gunakan field ini untuk menambahkan penjelasan tambahan mengenai pertanyaan "
"Anda atau ilustrasikan dengan gambar atau video"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__random_questions_count
msgid ""
"Used on randomized sections to take X random questions from all the "
"questions of that section."
msgstr ""
"Gunakan bagian yang acak untuk mengambil X pertanyaan acak dari semua "
"pertanyaan pada bagian tersebut."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug2
msgid "Useful"
msgstr "Berguna"

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr "Pengguna"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "User Choice"
msgstr "Pilihan User"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "User Input"
msgstr "User Input"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "User Responses"
msgstr "Tanggapan pengguna"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_form
msgid "User input line details"
msgstr "Pengguna rincian baris masukan"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr "Tanggapan pengguna"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr "Pengguna bisa kembali"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_can_signup
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_signup
msgid "Users can signup"
msgstr "User dapat mendaftar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr "Validasi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error message"
msgstr "Pesan Eror Validasi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug2
msgid "Vegetables"
msgstr "Vegetables"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_1
msgid "Very underpriced"
msgstr "Sangat di bawah hagra"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug3
msgid "Vietnam"
msgstr "Vietnam"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"We have registered your answer! Please wait for the host to go to the next "
"question."
msgstr ""
"Kami telah mendaftarkan jawaban Anda! Mohon tunggu host untuk memulai "
"pertanyaan berikutnya."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
msgid "Website Messages"
msgstr "Pesan Website"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
msgid "Website communication history"
msgstr "Sejarah komunikasi website"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_5
msgid ""
"What day and time do you think most customers are most likely to call "
"customer service (not rated)?"
msgstr ""
"Hari dan tanggal apa yang Anda pikir merupakan waktu di mana pelanggan "
"paling mungkin menelepon customer service (tidak dirating)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_4
msgid ""
"What day to you think is best for us to start having an annual sale (not "
"rated)?"
msgstr ""
"Hari apa yang Anda pikir merupakan waktu terbaik bagi kami untuk memulai "
"sale tahunan (tidak dirating)?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q2
msgid "What do you think about our new eCommerce ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_3
msgid "What do you think about our prices (not rated)?"
msgstr "Apa pemikiran Anda mengenai harga kami (tidak dirating)?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5_q1
msgid "What do you think about this survey ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q2
msgid "What is the biggest city in the world ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q1
msgid "What is your email ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q2
msgid "What is your nickname ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q2
msgid "What is, approximately, the critical mass of plutonium-239 ?"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Ketika kolom relasi dipilih sebagai kolom pertama, kolom ini memungkinkan "
"Anda untuk memilih kolom target dari model dokumen tujuan (sub-model)."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Ketika kolom relasi dipilih sebagai kolom pertama, kolom ini menampilkan "
"model dokumen dari kolom relasi."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q1
msgid "When did Genghis Khan die ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q2
msgid "When did precisely Marc Demo crop its first apple tree ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q6
msgid "When do you harvest those fruits"
msgstr "Kapan Anda memanen buah-buah tersebut"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q1
msgid "When is Mitchell Admin born ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q2
msgid "When is your date of birth ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q3
msgid "Where are you from ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q1
msgid "Where do you live ?"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_show_leaderboard
msgid ""
"Whether or not we want to show the attendees leaderboard for this survey."
msgstr ""
"Apakah kita ingin menunjukkan leaderboard ke peserta survei ini atau tidak."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q1
msgid "Which Musician is not in the 27th Club ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q1
msgid "Which category does a tomato belong to"
msgstr "Tomat berada di kategori mana "

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q3
msgid "Which is the highest volcano in Europe ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q1
msgid "Which of the following words would you use to describe our products ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q2
msgid "Which of the following would you use to pollinate"
msgstr "Apa dari yang berikut yang akan Anda gunakan untuk polinasi"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q2
msgid "Which painting/drawing was not made by Pablo Picasso ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q3
msgid "Which quote is from Jean-Claude Van Damme"
msgstr "Kutipan mana yang dibuat Jean-Claude Van Damme"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1
msgid "Who are you ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q2
msgid "Who is the architect of the Great Pyramid of Giza ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q1
msgid ""
"Who received a Nobel prize in Physics for the discovery of neutrino "
"oscillations, which shows that neutrinos have mass ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_3
msgid "Width"
msgstr "Lebar"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug4
msgid "Willard S. Boyle"
msgstr "Willard S. Boyle"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug4
msgid "Winter"
msgstr "Musim Dingin"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug1
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_2
msgid "Yes"
msgstr "Ya"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug1
msgid "Yes, that's the only thing a human eye can see."
msgstr "Ya, hal tersebut satu-satunya yang mata manusia dapat lihat."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid ""
"You can create surveys for different purposes: customer opinion, services "
"feedback, recruitment interviews, employee's periodical evaluations, "
"marketing campaigns, etc."
msgstr ""
"Anda dapat membuat survei dengan beberapa tujuan: pendapat pelanggan, "
"feedback servis, interview perekrutan, KPI karyawan, promo marketing, dll."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_certification_check
msgid ""
"You can only create certifications for surveys that have a scoring "
"mechanism."
msgstr ""
"Anda hanya dapat membuat sertifikasi untuk survei yang memiliki mekanisme "
"penilaian."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid ""
"You cannot delete questions from surveys \"%(survey_names)s\" while live "
"sessions are in progress."
msgstr ""
"Anda tidak dapat menghapus pertanyaan dari survei \"%(survey_names)s\" "
"selagi sesi live masih berlangsung."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey has no sections."
msgstr ""
"Anda tidak dapat mengirimkan undangan untuk survei \"Satu halaman per "
"bagian\" bila survei tidak memiliki bagian."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey only contains empty sections."
msgstr ""
"Anda tidak dapat mengirimkan undangan untuk survei \"Satu halaman per "
"bagian\" bila survei hanya memilki bagian-bagian kosong."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send an invitation for a survey that has no questions."
msgstr ""
"Anda tidak dapat mengirim undangan untuk survei yang tidak memiliki "
"pertanyaan."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send invitations for closed surveys."
msgstr "Anda tidak dapat mengirim undangan untuk survei tertutup."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You received the badge"
msgstr "Anda menerima lencana"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You scored"
msgstr "Anda mencetak"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5
msgid "Your feeling"
msgstr "Perasaan Anda"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr "ans"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "answered"
msgstr "dijawab"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "attempts"
msgstr "percobaan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. No one can solve challenges like you do"
msgstr "contoh, Tidak ada yang dapat menyelesaikan tantangan seperti Anda."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. Problem Solver"
msgstr "contoh Pemecah Masalah"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "e.g. Satisfaction Survey"
msgstr "contoh, Survei Kepuasan"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"for successfully completing\n"
"                                <br/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "log in"
msgstr "Login Member"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "minutes"
msgstr "menit"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "of"
msgstr "dari"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "of achievement"
msgstr "dari pencapaian"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "or press CTRL+Enter"
msgstr "atau pencet CTRL+Enter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "or press Enter"
msgstr "atau pencet Enter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "pages"
msgstr "halaman"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "review your answers"
msgstr "meninjau jawaban Anda"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "survey expired"
msgstr "survei kadaluwarsa"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "survey is empty"
msgstr "survei kosong"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "this page"
msgstr "halaman ini"
