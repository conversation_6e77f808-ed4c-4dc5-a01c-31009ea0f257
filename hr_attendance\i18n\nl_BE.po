# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_attendance
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-16 08:08+0000\n"
"PO-Revision-Date: 2017-11-16 08:08+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Dutch (Belgium) (https://www.transifex.com/odoo/teams/41243/nl_BE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl_BE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:55
#, python-format
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:29
#, python-format
msgid "%(empl_name)s from %(check_in)s"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:34
#, python-format
msgid "%(empl_name)s from %(check_in)s to %(check_out)s"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Attendance</span>"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:117
#, python-format
msgid "An apple a day keeps the doctor away"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:106
#, python-format
msgid "Another good day's work! See you soon!"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_my_attendances
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_attendance_ids
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_attendance_state
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_graph
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Attendance"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_graph
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_graph_filtered
msgid "Attendance Analysis"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_employee
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_kiosk_mode
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_barcode
msgid "Badge ID"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:85
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:72
#: code:addons/hr_attendance/models/hr_attendance.py:98
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:108
#, python-format
msgid "Cannot perform check in or check out on multiple employees."
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:122
#, python-format
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_check_in
msgid "Check In"
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_my_attendances
msgid "Check In / Check Out"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_check_out
msgid "Check Out"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:53
#, python-format
msgid "Check in"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:49
#, python-format
msgid "Check out"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Check-In/Out"
msgstr ""

#. module: hr_attendance
#: selection:hr.employee,attendance_state:0
msgid "Checked in"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:109
#, python-format
msgid "Checked in at"
msgstr ""

#. module: hr_attendance
#: selection:hr.employee,attendance_state:0
msgid "Checked out"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:97
#, python-format
msgid "Checked out at"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:27
#, python-format
msgid "Click to check in"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:31
#, python-format
msgid "Click to check out"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:12
#: model_terms:ir.ui.view,arch_db:hr_attendance.print_employee_badge
#, python-format
msgid "Company Logo"
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_settings
msgid "Configuration"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Create a few employees to be able to select an employee here and perform his"
" check in / check out."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Current Month"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_department_id
msgid "Department"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:123
#, python-format
msgid "Early to bed and early to rise, makes a man healthy, wealthy and wise"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:115
#, python-format
msgid "Eat breakfast as a king, lunch as a merchant and supper as a beggar"
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Employee"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.print_employee_badge
msgid "Employee Image"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings_group_attendance_use_pin
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Employee PIN"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Employee attendances"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_kanban_inherit_hr_attendance
msgid "Employee's Name"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_employee_attendance_action_kanban
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_employees_kanban
msgid "Employees"
msgstr ""

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_use_pin
msgid "Enable PIN use"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/my_attendances.js:29
#, python-format
msgid "Error : Could not find employee linked to user"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:35
#, python-format
msgid "Error : Could not find employee linked to user."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:81
#, python-format
msgid "Error: could not find corresponding employee."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:70
#, python-format
msgid "First come, first served"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:85
#, python-format
msgid "Glad to have you back, it's been a while!"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:76
#, python-format
msgid "Good afternoon"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:78
#, python-format
msgid "Good evening"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:73
#, python-format
msgid "Good morning"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:64
#: code:addons/hr_attendance/static/src/js/greeting_message.js:80
#, python-format
msgid "Good night"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:100
#, python-format
msgid "Goodbye"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Group By"
msgstr "Groeperen op"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:120
#, python-format
msgid "Have a good afternoon"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:111
#, python-format
msgid "Have a good day!"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:125
#, python-format
msgid "Have a good evening"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:113
#, python-format
msgid "Have a nice lunch!"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Hr Attendance Search"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_id
msgid "ID"
msgstr "ID"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee_barcode
msgid "ID used for employee identification."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:88
#, python-format
msgid "If a job is worth doing, it is worth doing well!"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:120
#, python-format
msgid "Invalid request, please return to the main menu."
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_kiosk_mode
msgid "Kiosk Mode"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_last_attendance_id
msgid "Last Attendance"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance___last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_manage_attendances
msgid "Manage Attendances"
msgstr ""

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_manager
msgid "Manager"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_manual_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance
msgid "Manual Attendance"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_greeting_message
msgid "Message"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Month"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Attendances"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "No Check Out"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
msgid "No attendance records to display."
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:72
#, python-format
msgid "No employee corresponding to barcode %(barcode)s"
msgstr ""

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_user
msgid "Officer"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_pin
msgid "PIN"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee_pin
msgid "PIN used to Check In/Out in Kiosk Mode (if enabled in Configuration)."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:62
#, python-format
msgid "Please enter your PIN to check in"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:59
#, python-format
msgid "Please enter your PIN to check out"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
msgid ""
"Please make sure you're using the correct filter if you expected to see any."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:82
#, python-format
msgid "Please return to the main menu."
msgstr ""

#. module: hr_attendance
#: model:ir.actions.report,name:hr_attendance.hr_employee_print_badge
msgid "Print Badge"
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_report
msgid "Reporting"
msgstr "Rapportages"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:13
#, python-format
msgid "Scan your badge"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:15
#, python-format
msgid "Select Employee"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Set PIN codes in the employee detail form (in HR Settings tab)."
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_settings
msgid "Settings"
msgstr ""

#. module: hr_attendance
#: sql_constraint:hr.employee:0
msgid ""
"The Badge ID must be unique, this one is already assigned to another "
"employee."
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:63
#, python-format
msgid "The PIN must be a sequence of digits."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
msgid "The attendance records of your employees will be displayed here."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:68
#, python-format
msgid "The early bird catches the worm"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee_manual_attendance
msgid ""
"The employee will have access to the \"My Attendances\" menu to check in and"
" out from his session"
msgstr ""

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance
msgid ""
"The user will gain access to the human resources attendance menu, enabling "
"him to manage his own attendance."
msgstr ""

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_use_pin
msgid ""
"The user will have to enter his PIN to check in and out manually at the "
"company screen."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid "To create employees go to the Employees menu."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Today"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes to check in in Kiosk Mode"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:102
#, python-format
msgid ""
"Warning! Last check in was over 12 hours ago.<br/>If this isn't right, "
"please contact Human Resources."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:25
#: code:addons/hr_attendance/static/src/xml/attendance.xml:46
#: code:addons/hr_attendance/static/src/xml/attendance.xml:112
#, python-format
msgid "Welcome"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:11
#, python-format
msgid "Welcome to"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_worked_hours
msgid "Worked Hours"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:79
#, python-format
msgid "Wrong PIN"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:105
#, python-format
msgid "You cannot duplicate an attendance."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee_attendance_ids
msgid "list of attendances for the employee"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:123
#, python-format
msgid "ok"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:14
#, python-format
msgid "or"
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_config_settings
msgid "res.config.settings"
msgstr ""
