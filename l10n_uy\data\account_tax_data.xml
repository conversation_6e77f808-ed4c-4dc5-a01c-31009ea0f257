<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Account Tax Template -->
        <record id="vat1" model="account.tax.template">
            <field name="chart_template_id" ref="uy_chart_template"/>
            <field name="name">IVA Ventas (22%)</field>
            <field name="description">IVA Ventas (22%)</field>
            <field name="amount">22</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_iva_22"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_base_impb_vnts_22')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('uy_code_5202'),
                    'plus_report_line_ids': [ref('account_tax_report_iva_vnts_22')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_impb_vnts')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('uy_code_5202'),
                    'minus_report_line_ids': [ref('account_tax_report_vnts_prcbdo')],
                }),
            ]"/>
        </record>

        <record id="vat2" model="account.tax.template">
            <field name="chart_template_id" ref="uy_chart_template"/>
            <field name="name">IVA Ventas (10%)</field>
            <field name="description">IVA Ventas (10%)</field>
            <field name="amount">10</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_iva_10"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_base_impb_vnts_10')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('uy_code_5201'),
                    'plus_report_line_ids': [ref('account_tax_report_iva_vnts_10')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_impb_vnts')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('uy_code_5201'),
                    'minus_report_line_ids': [ref('account_tax_report_vnts_prcbdo')],
                }),
            ]"/>
        </record>

        <record id="vat3" model="account.tax.template">
            <field name="chart_template_id" ref="uy_chart_template"/>
            <field name="name">Ventas Exentos IVA</field>
            <field name="description">Ventas Exentos IVA</field>
            <field name="amount">0</field>
            <field name="amount_type">fixed</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_exenton"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_base_impb_vnts_0')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
              (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_impb_vnts')],
              }),
              (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
              }),
            ]"/>
        </record>

        <record id="vat4" model="account.tax.template">
            <field name="chart_template_id" ref="uy_chart_template"/>
            <field name="name">IVA Compras (22%)</field>
            <field name="description">IVA Compras (22%)</field>
            <field name="amount">22</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_22"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_base_impb_cmprs_22')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('uy_code_11502'),
                    'plus_report_line_ids': [ref('account_tax_report_iva_cmprs_22')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_impb_cmprs')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('uy_code_11502'),
                    'minus_report_line_ids': [ref('account_tax_report_cmprs_pagdo')],
                }),
            ]"/>
        </record>

        <record id="vat5" model="account.tax.template">
            <field name="chart_template_id" ref="uy_chart_template"/>
            <field name="name">IVA Compras (10%)</field>
            <field name="description">IVA Compras (10%)</field>
            <field name="amount">10</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_iva_10"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_base_impb_cmprs_10')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('uy_code_11501'),
                    'plus_report_line_ids': [ref('account_tax_report_iva_cmprs_10')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_impb_cmprs')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('uy_code_11501'),
                    'minus_report_line_ids': [ref('account_tax_report_cmprs_pagdo')],
                }),
            ]"/>
        </record>

        <record id="vat6" model="account.tax.template">
            <field name="chart_template_id" ref="uy_chart_template"/>
            <field name="name">Compras Exentos IVA</field>
            <field name="description">Compras Exentos IVA</field>
            <field name="amount">0</field>
            <field name="amount_type">fixed</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_exenton"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('account_tax_report_base_impb_cmprs_0')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('account_tax_report_impb_cmprs')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

    </data>
</odoo>