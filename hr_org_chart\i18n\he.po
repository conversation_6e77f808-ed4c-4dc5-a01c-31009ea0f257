# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_org_chart
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_base
msgid "Basic Employee"
msgstr "עובד רגיל"

#. module: hr_org_chart
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Direct and indirect subordinates"
msgstr "כפיפים ישירים ועקיפים"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "Direct subordinates"
msgstr "כפיפים ישירים"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee
msgid "Employee"
msgstr "עובד"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "In order to get an organigram, set a manager and save the record."
msgstr "על מנת לקבל מבנה ארגוני, הגדר מנהל ושמור את הרשומה."

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_all_count
msgid "Indirect Subordinates Count"
msgstr "מס' פקודים עקיפים "

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "Indirect subordinates"
msgstr "כפיפים עקיפים"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "More managers"
msgstr "מנהלים נוספים"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "No hierarchy position."
msgstr "אין מיקום היררכי."

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_public_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.res_users_view_form
msgid "Organization Chart"
msgstr "תרשים ארגוני"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_public
msgid "Public Employee"
msgstr "עובד ציבור"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "Redirect"
msgstr "הפנייה מחדש"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:108
#, python-format
msgid "See All"
msgstr ""

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Subordinates"
msgstr "כפיפים"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/js/hr_org_chart.js:181
#, python-format
msgid "Team"
msgstr "צוות"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "This employee has no manager or subordinate."
msgstr "לעובד זה אין מנהל או כפיף."

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "Total"
msgstr "סה\"כ"
