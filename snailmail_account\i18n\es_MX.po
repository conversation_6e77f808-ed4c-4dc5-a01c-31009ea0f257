# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail_account
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: snailmail_account
#: code:addons/snailmail_account/wizard/account_invoice_send.py:0
#, python-format
msgid "%s of the selected invoice(s) had an invalid address and were not sent"
msgstr ""
"%s de las facturas seleccionadas no tenían una dirección válida y no se "
"enviaron"

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.account_invoice_send_inherit_account_wizard_form
msgid ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Warning\" title=\"Make sure you have enough Stamps on your account.\"/>\n"
"                                )"
msgstr ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Warning\" title=\"Make sure you have enough Stamps on your account.\"/>\n"
"                                )"

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Los valores establecidos aquí"
" son específicos de la empresa.\"/>"

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.account_invoice_send_inherit_account_wizard_form
msgid ""
"<span class=\"text-danger\">\n"
"                                    Some customer addresses are incomplete.\n"
"                                </span>"
msgstr ""
"<span class=\"text-danger\">\n"
"                                    Algunas direcciones de clientes están incompletas.\n"
"                                </span>"

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.account_invoice_send_inherit_account_wizard_form
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': [('invalid_addresses', '!=', 0)]}\"> to: </span>\n"
"                                <span class=\"text-danger\" attrs=\"{'invisible': [('invalid_addresses', '=', 0)]}\"> The customer's address is incomplete: </span>"
msgstr ""
"<span class=\"text-muted\" attrs=\"{'invisible': [('invalid_addresses', '!=', 0)]}\"> para: </span>\n"
"                                <span class=\"text-danger\" attrs=\"{'invisible': [('invalid_addresses', '=', 0)]}\"> La dirección del cliente está incompleta: </span>"

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_account_invoice_send
msgid "Account Invoice Send"
msgstr "Enviar factura"

#. module: snailmail_account
#: model:ir.model.fields,help:snailmail_account.field_account_invoice_send__snailmail_is_letter
msgid ""
"Allows to send the document by Snailmail (conventional posting delivery "
"service)"
msgstr "Le permite mandar un documento por correo postal"

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.account_invoice_send_inherit_account_wizard_form
msgid "Contacts"
msgstr "Contactos"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__create_date
msgid "Created on"
msgstr "Creado el"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__id
msgid "ID"
msgstr "ID"

#. module: snailmail_account
#: code:addons/snailmail_account/wizard/account_invoice_send.py:0
#: code:addons/snailmail_account/wizard/account_invoice_send.py:0
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__invalid_partner_ids
#, python-format
msgid "Invalid Addresses"
msgstr "Direcciones no válidas"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__invalid_addresses
msgid "Invalid Addresses Count"
msgstr "Número de direcciones no válidas"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__invalid_invoices
msgid "Invalid Invoices Count"
msgstr "Número de facturas no válidas"

#. module: snailmail_account
#: code:addons/snailmail_account/wizard/account_invoice_send.py:0
#, python-format
msgid "Invoice"
msgstr "Facturas de clientes"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__invoice_send_id
msgid "Invoice Send"
msgstr "Factura enviada"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_snailmail_confirm_invoice__model_name
msgid "Model Name"
msgstr "Nombre del modelo"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__snailmail_is_letter
#: model:ir.model.fields,field_description:snailmail_account.field_res_company__invoice_is_snailmail
#: model:ir.model.fields,field_description:snailmail_account.field_res_config_settings__invoice_is_snailmail
msgid "Send by Post"
msgstr "Enviar por correo postal"

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_snailmail_confirm_invoice
msgid "Snailmail Confirm Invoice"
msgstr "Factura de confirmación de correo postal"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__snailmail_cost
msgid "Stamp(s)"
msgstr "Timbre(s)"

#. module: snailmail_account
#: code:addons/snailmail_account/wizard/account_invoice_send.py:0
#, python-format
msgid "You cannot send an invoice which has no partner assigned."
msgstr "No puede enviar una factura que no tenga un contacto asignado."
