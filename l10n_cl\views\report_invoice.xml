<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- this header can be used on any Chilean report -->
    <template id="custom_header">

        <t t-set="report_date" t-value="o.invoice_date"/>
        <t t-set="report_number" t-value="int(o.l10n_latam_document_number)"/>
        <t t-set="pre_printed_report" t-value="report_type == 'pdf'"/>
        <t t-set="report_name" t-value="o.l10n_latam_document_type_id.name"/>
        <t t-set="header_address" t-value="o.company_id.partner_id"/>
        <t t-set="custom_footer">
            <t t-call="l10n_cl.custom_footer"/>
        </t>

        <div>
            <div class="row">
                <div name="left-upper-side" class="col-8">
                    <img t-if="o.company_id.logo" t-att-src="image_data_uri(o.company_id.logo)"
                         style="max-height: 45px;" alt="Logo"/>
                    <br/>
                    <strong>
                        <span t-field="o.company_id.partner_id.name"/>
                    </strong>
                    <br/>
                    <span name="company_activity" class="font-italic" t-field="o.company_id.report_header"/>
                    <div/>
                    <t t-esc="' - '.join([item for item in [
                        ', '.join([item for item in [header_address.street, header_address.street2] if item]),
                        header_address.city,
                        header_address.state_id and header_address.state_id.name,
                        header_address.zip,
                        header_address.country_id and header_address.country_id.name] if item])"/>
                    <span t-if="header_address.phone">
                        <br/>
                    </span>
                    <span t-if="header_address.phone" style="white-space: nowrap;"
                          t-esc="'Tel: ' + header_address.phone"/>
                    <span t-if="header_address.website">
                        <span t-att-style="'color: %s;' % o.company_id.primary_color"
                              t-esc="'- Web: %s' %' - '.join([item for item in [header_address.website.replace('https://', '').replace('http://', ''), header_address.email] if item])"/>
                    </span>
                </div>
                <div name="right-upper-side" class="col-4">
                    <div class="row">
                        <div name="right-upper-side" class="col-12">
                            <div class="row border border-dark">
                                <div class="col-12 text-center">
                                    <h6 t-att-style="'color: %s;' % o.company_id.primary_color">
                                        <strong t-att-style="'color: %s;' % o.company_id.primary_color">
                                            <br/>
                                            <span style="line-height: 180%;">RUT:</span>
                                            <t t-if="o.company_id.partner_id.vat">
                                                <span t-esc="o.company_id.partner_id._format_dotted_vat_cl(o.company_id.partner_id.vat)"/>
                                            </t>
                                            <br/>
                                            <span class="text-uppercase" t-esc="report_name"/>
                                            <br/>
                                            <span>Nº:</span>
                                            <span style="line-height: 200%;" t-esc="report_number"/>
                                        </strong>
                                    </h6>
                                </div>
                            </div>
                            <div class="row text-center">
                                <div class="col-12 text-center" t-att-style="'color: %s;' % o.company_id.primary_color"
                                     name="regional-office"/>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </template>


    <template id="informations">
        <div id="informations" class="row mt8 mb8">
            <div class="col-6">
                <strong>
                    <span t-att-style="'color: %s;' % o.company_id.secondary_color">Date:</span>
                </strong>
                <span t-esc="o.invoice_date" t-options='{"widget": "date"}'/>
                <br/>

                <strong>Customer:</strong>
                <span t-field="o.partner_id.name"/>
                <br/>

                <t t-if="o.partner_id.vat and o.partner_id.l10n_latam_identification_type_id">
                    <strong>
                        <t t-esc="o.partner_id.l10n_latam_identification_type_id.name or o.company_id.account_fiscal_country_id.vat_label" id="inv_tax_id_label"/>:
                    </strong>
                    <span t-esc="o.partner_id.vat"/>
                    <br/>
                </t>
                <strong>GIRO:</strong>
                <span t-esc="o.partner_id.industry_id.name or ''"/>
                <br/>
            </div>
            <div class="col-6">
                <strong>Due Date:</strong>
                <span t-esc="o.invoice_date_due" t-options='{"widget": "date"}'/>
                <br/>
                <strong>Address:</strong>
                <span t-field="o.partner_id"
                      t-options="{'widget': 'contact', 'fields': ['address'], 'no_marker': true, 'no_tag_br': True}"/>

                <strong>Payment Terms:</strong>
                <span t-esc="o.invoice_payment_term_id.name or ''"/>


                <t t-if="o.invoice_incoterm_id">
                    <br/>
                    <strong>Incoterm:</strong>
                    <span t-field="o.invoice_incoterm_id.name"/>
                </t>

            </div>
        </div>
        <div id="references" class="row">
            <div name="references" class="col-12 text-center"/>
        </div>
    </template>

    <template id="custom_footer">
        <div name="footer_left_column" class="col-8 text-center"/>
    </template>

    <template id="report_invoice_document" inherit_id="account.report_invoice_document" primary="True">

        <t t-set="o" position="after">
            <t t-set="custom_header" t-value="'l10n_cl.custom_header'"/>
        </t>

        <!-- remove default partner address -->
        <t t-set="address" position="replace"/>

        <xpath expr="//h2" position="replace"/>

        <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal" position="before">
            <t t-set="l10n_cl_values" t-value="line._l10n_cl_prices_and_taxes()"/>
        </t>

        <xpath expr="//span[@t-field='line.price_unit']" position="attributes">
            <attribute name="t-field"></attribute>
            <attribute name="t-esc">line.price_unit</attribute>
            <attribute name="t-options">{"widget": "monetary", "display_currency": o.currency_id}</attribute>
        </xpath>

        <xpath expr="//span[@id='line_tax_ids']" position="attributes">
            <attribute name="t-esc">', '.join(map(lambda x: (x.description or x.name), line.tax_lines))</attribute>
        </xpath>

        <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal" position="attributes">
            <attribute name="t-value">current_subtotal + l10n_cl_values['price_subtotal']</attribute>
        </t>

        <xpath expr="//th[@name='th_subtotal']/span[@groups='account.group_show_line_subtotals_tax_included']" position="replace">
            <span groups="account.group_show_line_subtotals_tax_included">Amount</span>
        </xpath>

        <span t-field="line.price_subtotal" position="attributes">
            <attribute name="t-field"></attribute>
            <attribute name="t-esc">l10n_cl_values['price_subtotal']</attribute>
            <attribute name="t-options">{"widget": "monetary", "display_currency": o.currency_id}</attribute>
        </span>

        <t t-set="tax_totals" position="attributes">
            <attribute name="t-value">o._l10n_cl_get_invoice_totals_for_report()</attribute>
        </t>

        <xpath expr="//th[@name='th_taxes']" position="replace"/>
        <xpath expr="//span[@id='line_tax_ids']/.." position="replace"/>

        <p name="payment_term" position="replace"/>
        <xpath expr="//span[@t-field='o.payment_reference']/../.." position="replace"/>

        <!-- replace information section and usage chilean style -->
        <div id="informations" position="replace">
            <t t-call="l10n_cl.informations"/>
        </div>

        <!--  we remove the ml auto and also give more space to avoid multiple lines on tax detail -->
        <xpath expr="//div[@id='total']/div" position="attributes">
            <attribute name="t-attf-class">#{'col-6' if report_type != 'html' else 'col-sm-7 col-md-6'}</attribute>
        </xpath>

        <xpath expr="//div[@id='total']/div" position="before">
            <div t-attf-class="#{'col-6' if report_type != 'html' else 'col-sm-7 col-md-6'}"/>
        </xpath>

        <xpath expr="//div[@id='total']" position="after">
            <div class="row">
                <div name="stamp" class="col-4 text-center"/>
                <div name="transferable-table" class="col-4"/>
                <div name="transferable-legend" class="col-4 pull-right"/>
            </div>
        </xpath>

    </template>

    <!-- FIXME: Temp fix to allow fetching invoice_documemt in Studio Reports with localisation -->
    <template id="report_invoice" inherit_id="account.report_invoice">
        <xpath expr='//t[@t-call="account.report_invoice_document"]' position="after">
            <t t-if="o._get_name_invoice_report() == 'l10n_cl.report_invoice_document'"
                t-call="l10n_cl.report_invoice_document" t-lang="lang"/>
        </xpath>
    </template>

    <template id="report_invoice_with_payments" inherit_id="account.report_invoice_with_payments">
        <xpath expr='//t[@t-call="account.report_invoice_document"]' position="after">
            <t t-if="o._get_name_invoice_report() == 'l10n_cl.report_invoice_document'"
                t-call="l10n_cl.report_invoice_document" t-lang="lang"/>
        </xpath>
    </template>

</odoo>
