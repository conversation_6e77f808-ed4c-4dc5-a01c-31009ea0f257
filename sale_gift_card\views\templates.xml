<?xml version="1.0" ?>
<odoo>

    <template id="sale_gift_card.used_gift_card">
        <div class="text-muted d-none d-md-block small" t-if="line.gift_card_id">
            <span>
                Code:
                <t t-esc="line.gift_card_id.code[-4:].rjust(14, '&#8902;')"/>
            </span>
            <br/>
            <span>
                Expired Date:
                <t t-esc="line.gift_card_id.expired_date"/>
            </span>
        </div>
    </template>

    <template id="sale_order_portal_content_inherit" name="Gift Card Products Portal" inherit_id="sale.sale_order_portal_content">
        <xpath expr="//section[@id='details']//td[@id='product_name']/*[last()]" position="after">
            <t t-if="line.gift_card_id" t-call="sale_gift_card.used_gift_card"/>
            <t t-if="line.generated_gift_card_ids" t-call="sale_gift_card.sale_purchased_gift_card">
                <t t-set="order" t-value="sale_order"/>
                <t t-set="hide_intro" t-value="1"/>
            </t>
        </xpath>
    </template>

    <template id="sale_purchased_gift_card">
        <div class="card mt-3 " t-if="order.gift_card_count != 0">
            <div class="card-body">
                <span t-if="not hide_intro">You will find below your gift cards code. An email has been sent with it. You can use it starting right now.</span>
                <table class="table text-center table-borderless">
                    <thead>
                        <tr>
                            <th class="font-weight-normal">Gift Card Code</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="order.order_line" t-as="order_line">
                            <tr t-foreach="order_line.generated_gift_card_ids" t-as="gift_card">
                                <td class="o_purchased_gift_card">
                                    Gift #<t t-esc="gift_card.id"/>
                                    (<span t-field="gift_card.initial_amount" style="white-space: nowrap;" t-options="{'widget': 'monetary', 'display_currency': gift_card.currency_id}"/>)
                                    <strong t-esc="gift_card.code"/>
                                    <button class="btn btn-sm btn-secondary copy-to-clipboard" t-att-data-clipboard-text="gift_card.code">
                                        <span class="fa fa-clipboard"/> Copy
                                    </button>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </table>
            </div>
        </div>
    </template>
</odoo>
