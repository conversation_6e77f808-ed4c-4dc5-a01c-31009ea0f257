# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mass_mailing_sms
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>esselbosch, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid ""
"+32 495 85 85 77\n"
"+33 545 55 55 55"
msgstr ""
"+32 495 85 85 77\n"
"+33 545 55 55 55"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "24H Stats of %(mailing_type)s \"%(mailing_name)s\""
msgstr "24H-Statistik von %(mailing_type)s „%(mailing_name)s“"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"canceled_text_sms\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">SMS Text Message have been canceled and will not be "
"sent.</span>"
msgstr ""
"<span name=\"canceled_text_sms\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">SMS-Nachrichten wurden storniert und werden nicht "
"gesendet.</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"failed_text_sms\" attrs=\"{'invisible': [('mailing_type', '!=',"
" 'sms')]}\">SMS Text Message could not be sent.</span>"
msgstr ""
"<span name=\"failed_text_sms\" attrs=\"{'invisible': [('mailing_type', '!=',"
" 'sms')]}\">SMS-Nachricht konnte nicht gesendet werden.</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"next_departure_text\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">This SMS marketing is scheduled for </span>"
msgstr ""
"<span name=\"next_departure_text\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">Diese SMS-Marketingkampagne ist geplant für</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"scheduled_text_sms\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">SMS Text Message are in queue and will be sent soon.</span>"
msgstr ""
"<span name=\"scheduled_text_sms\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">SMS sind in der Warteschlange und werden in Kürze "
"verschickt.</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"sent_sms\" attrs=\"{'invisible': [('mailing_type', '!=', "
"'sms')]}\">SMS Text Message have been sent.</span>"
msgstr ""
"<span name=\"sent_sms\" attrs=\"{'invisible': [('mailing_type', '!=', "
"'sms')]}\">SMS wurde versendet.</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<span widget=\"statinfo\">Open Recipient</span>"
msgstr "<span widget=\"statinfo\">Empfänger öffnen</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_list_view_kanban
msgid "<span>Valid SMS Recipients</span>"
msgstr "<span>Gültige SMS-Empfänger</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<strong>\n"
"                            It appears you don't have enough IAP credits. Click here to buy credits.\n"
"                        </strong>"
msgstr ""
"<strong>\n"
"                             Es scheint, dass Sie nicht genug IAP-Guthaben haben. Klicken Sie hier, um Guthaben zu kaufen.\n"
"                        </strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<strong>\n"
"                            It appears your SMS account is not registered. Click here to set up your account.\n"
"                        </strong>"
msgstr ""
"<strong>\n"
"                            Es scheint, dass Ihr SMS-Konto nicht registriert ist. Klicken Sie hier, um Ihr Konto einzurichten.\n"
"                        </strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This SMS could not be sent.</strong>"
msgstr "<strong>Diese SMS konnte nicht gesendet werden.</strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This number appears to be invalid.</strong>"
msgstr "<strong>Diese Nummer scheint ungültig zu sein.</strong>"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "A/B Test: %s"
msgstr "A/B-Test: %s"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "BOUNCED (%i)"
msgstr "UNZUSTELLBAR (%i)"

#. module: mass_mailing_sms
#: model:utm.tag,name:mass_mailing_sms.mailing_tag_0
msgid "Bioutifoul SMS"
msgstr "Bioutifoul SMS"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/controllers/main.py:0
#, python-format
msgid ""
"Blacklist through SMS Marketing unsubscribe (mailing ID: %s - model: %s)"
msgstr ""
"Schwarze Liste durch Abmeldung von SMS-Marketing (Versand-ID: %s - Modell: "
"%s)"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "Auf der schwarzen Liste"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "Telefon auf der schwarzen Liste ist Mobiltelefon"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.phone_blacklist_menu
msgid "Blacklisted Phone Numbers"
msgstr "Telefonnummern auf der schwarzen Liste"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "Telefon auf der schwarzen Liste Ist Festnetz"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "CLICKED (%i)"
msgstr "ANGEKLICKT (%i)"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
msgid "Campaign"
msgstr "Kampagne"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.menu_email_campaigns
msgid "Campaigns"
msgstr "Kampagnen"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Cancel"
msgstr "Abbrechen"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_sms_test__numbers
msgid "Carriage-return-separated list of phone numbers"
msgstr "Liste der Telefonnummern, getrennt durch Zeilenumbruch."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_code
msgid "Code"
msgstr "Code"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_trace_report_action_sms
msgid ""
"Come back once some SMS Mailings are sent to check out aggregated results."
msgstr ""
"Kommen Sie wieder, sobald einige SMS-Sendungen verschickt wurden, um die "
"gebündelten Ergebnisse zu prüfen."

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_configuration
msgid "Configuration"
msgstr "Konfiguration"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_list_action_sms
msgid "Create a Mailing List"
msgstr "Eine Mailingliste erstellen"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_mailing_action_sms
msgid "Create a SMS Marketing Mailing"
msgstr "Eine Sendung für SMS-Marketing erstellen"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_contact_action_sms
msgid "Create a mailing contact"
msgstr "Einen Mailingkontakt erstellen"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_duplicate
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Duplicate"
msgstr "Duplizieren"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/res_users.py:0
#, python-format
msgid "Email Marketing"
msgstr "E-Mail-Marketing"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_search
msgid "Exclude Blacklisted Phone"
msgstr "Telefonnummern auf der schwarzen Liste ausschließen"

#. module: mass_mailing_sms
#: model:mailing.mailing,name:mass_mailing_sms.mailing_sms_1
#: model:mailing.mailing,sms_subject:mass_mailing_sms.mailing_sms_1
#: model:utm.source,name:mass_mailing_sms.mailing_sms_1_utm_source
msgid "Extra Promo"
msgstr "Extra-Promo"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__failure_type
msgid "Failure type"
msgstr "Fehlertyp"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"Feld zum Speichern der bereinigten Rufnummer. Hilft, die Suche und den "
"Vergleich zu beschleunigen."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"For an Email, Subject your Recipients will see in their inbox.\n"
"                    For an SMS Text Message, internal Title of the Message."
msgstr ""
"Bei einer E-Mail der Betreff, den die Empfänger in ihrem Posteingang sehen.\n"
"Bei einer SMS-Nachricht den internen Titel der Nachricht."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_subject
msgid ""
"For an email, the subject your recipients will see in their inbox.\n"
"For an SMS, the internal title of the message."
msgstr ""
"Bei einer E-Mail der Betreff, den die Empfänger in ihrem Posteingang sehen.\n"
"Bei einer SMS: der interne Titel der Nachricht."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__utm_campaign__ab_testing_sms_winner_selection__clicks_ratio
msgid "Highest Click Rate"
msgstr "Höchste Klickrate"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__id
msgid "ID"
msgstr "ID"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_trace__sms_sms_id_int
msgid ""
"ID of the related sms.sms. This field is an integer field because the "
"related sms.sms can be deleted separately from its statistics. However the "
"ID is needed for several action and controllers."
msgstr ""
"ID der zugehörigen sms.sms. Dieses Feld ist eine Ganzzahl, weil die "
"zugehörige sms.sms separat von dessen Statistiken gelöscht werden kann. "
"Diese ID wird allerdings für verschiedene Aktionen und Controller benötigt."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_needaction
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_unread
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_error
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"Wenn die bereinigte Rufnummer auf der schwarzen Liste steht, erhält der "
"Kontakt keine Massen-SMS mehr, egal von welcher Liste"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_force_send
msgid ""
"Immediately send the SMS Mailing instead of queuing up. Use at your own "
"risk."
msgstr ""
"Senden Sie die SMS sofort, anstatt sie in die Warteschlange zu stellen. Auf "
"eigene Gefahr."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_allow_unsubscribe
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__mass_sms_allow_unsubscribe
msgid "Include opt-out link"
msgstr "Abmeldelink einfügen"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Zeigt an, ob eine bereinigte Rufnummer auf der schwarzen Liste eine "
"Mobilfunknummer ist. Hilft bei der Unterscheidung, welche Nummer auf der "
"schwarzen Liste steht, wenn es in einem Modell sowohl ein Handy- als auch "
"ein Telefonfeld gibt."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Zeigt an, ob eine bereinigte Rufnummer auf der schwarzen Liste eine "
"Teleofnnummer ist. Hilft bei der Unterscheidung, welche Nummer auf der "
"schwarzen Liste steht, wenn es in einem Modell sowohl ein Handy- als auch "
"ein Telefonfeld gibt."

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "Unzureichendes Guthaben"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_has_insufficient_credit
msgid "Insufficient IAP credits"
msgstr "Unzureichendes IAP-Guthaben"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_kanban_sms
msgid "Insufficient credits"
msgstr "Unzureichendes Guthaben"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.link_tracker_menu
msgid "Link Tracker"
msgstr "Link-Tracker"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__mailing_id
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Mailing"
msgstr "Mailing"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_contact
msgid "Mailing Contact"
msgstr "Mailingkontakt"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_list
msgid "Mailing List"
msgstr "Mailingliste"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_contact_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mailing_contact_menu_sms
msgid "Mailing List Contacts"
msgstr "Mailinglistenkontakte"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_list_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mailing_list_menu_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_contacts
msgid "Mailing Lists"
msgstr "Mailinglisten"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_trace
msgid "Mailing Statistics"
msgstr "Mailing-Statistiken"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__mailing_type
msgid "Mailing Type"
msgstr "Mailingtyp"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_contact_action_sms
msgid ""
"Mailing contacts allow you to separate your marketing audience from your "
"contact directory."
msgstr ""
"Mailingkontakte ermöglichen es Ihnen, Ihr Marketing-Publikum von Ihrem "
"Kontaktverzeichnis zu trennen."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hauptanhang"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__utm_campaign__ab_testing_sms_winner_selection__manual
msgid "Manual"
msgstr "Manuell"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Marketing"
msgstr "Marketing"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_mailing
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_sms__mailing_id
msgid "Mass Mailing"
msgstr "Massenmailing"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__mailing_sms_ids
msgid "Mass SMS"
msgstr "Massen-SMS"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "Fehlende Nummer"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__mobile
msgid "Mobile"
msgstr "Mobil"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_search_sms
msgid "My SMS Marketing"
msgstr "Mein SMS-Marketing"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_trace_report_action_sms
msgid "No data yet!"
msgstr "Noch keine Daten vorhanden!"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_list_action_sms
msgid ""
"No need to import mailing lists, you can send SMS Text Messages to contacts "
"saved in other Odoo apps."
msgstr ""
"Sie müssen keine Mailinglisten importieren, sondern können SMS an Kontakte "
"senden, die in anderen Odoo-Anwendungen gespeichert sind."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_number
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Number"
msgstr "Nummer"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/controllers/main.py:0
#, python-format
msgid "Number %s not found"
msgstr "Nummer %s nicht gefunden"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__mailing_sms_count
msgid "Number of Mass SMS"
msgstr "Anzahl Massen-SMS"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_unread_counter
msgid "Number of unread messages"
msgstr "Anzahl ungelesener Nachrichten"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__numbers
msgid "Number(s)"
msgstr "Nummer(n)"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_tree_sms
msgid "Open Recipient"
msgstr "Empfänger öffnen"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_optout
msgid "Opted Out"
msgstr "Abgemeldet"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "Ausgehende SMS"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Telefon auf der schwarzen Liste"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_mobile_search
msgid "Phone/Mobile"
msgstr "Telefon/Mobil"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Please enter your phone number"
msgstr "Bitte geben Sie Ihre Telefonnummer an"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "RECEIVED (%i)"
msgstr "ERHALTEN (%i)"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
msgid "Recipients"
msgstr "Empfänger"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "Report for %(expected)i %(mailing_type)s Sent"
msgstr "Bericht für %(expected)i%(mailing_type)s gesendet"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_reporting
msgid "Reporting"
msgstr "Berichtswesen"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_sms_id
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_mailing__mailing_type__sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__trace_type__sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_kanban
msgid "SMS"
msgstr "SMS"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__body_plaintext
msgid "SMS Body"
msgstr "SMS-Textkörper"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_list__contact_count_sms
msgid "SMS Contacts"
msgstr "SMS-Kontakte"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "SMS Content"
msgstr "SMS-Inhalt"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-Zustellungsfehler"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "SMS ID"
msgstr "SMS-ID"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_sms_id_int
msgid "SMS ID (tech)"
msgstr "SMS-ID (tech)"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/res_users.py:0
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_mailing_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_mass_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#, python-format
msgid "SMS Marketing"
msgstr "SMS-Marketing"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_trace_report_action_sms
msgid "SMS Marketing Analysis"
msgstr "SMS-Marketing-Analyse"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "SMS Subscription"
msgstr "SMS-Abonnement"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_template_id
msgid "SMS Template"
msgstr "SMS-Vorlage"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "SMS Text Message"
msgstr "SMS-Textnachricht"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "SMS Trace"
msgstr "SMS-Verfolgung"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_tree_sms
msgid "SMS Traces"
msgstr "SMS-Verfolgungen"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__ab_testing_sms_winner_selection
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__ab_testing_sms_winner_selection
msgid "SMS Winner Selection"
msgstr "SMS-Gewinnerauswahl"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/sms_composer.py:0
#, python-format
msgid "STOP SMS : %s"
msgstr "STOP SMS : %s"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_sanitized
msgid "Sanitized Number"
msgstr "Bereinigte Nummer"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
msgid "Scheduled"
msgstr "Geplant"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send"
msgstr "Senden"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_force_send
msgid "Send Directly"
msgstr "Direkt senden"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Send Now"
msgstr "Jetzt senden"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Send SMS"
msgstr "SMS versenden"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "Assistent zum Senden von SMS"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send a Sample SMS"
msgstr "Eine Test-SMS versenden"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid ""
"Send a sample SMS for testing purpose to the numbers below (carriage-return-"
"separated list)."
msgstr ""
"Senden Sie zu Testzwecken eine Beispiel-SMS an die nachstehenden Nummern "
"(durch Zeilenumbruch getrennte Liste)."

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_server
msgid "Server Error"
msgstr "Serverfehler"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_sms__mailing_trace_ids
msgid "Statistics"
msgstr "Statistik"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Status"
msgstr "Status"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_sms_test
msgid "Test SMS Mailing"
msgstr "Test-SMS-Mailing"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_sms_test_action
msgid "Test SMS Marketing"
msgstr "SMS-Marketing testen"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "Test SMS could not be sent to %s:<br>%s"
msgstr "Test-SMS konnte nicht gesendet werden an %s:<br>%s"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "Test SMS marketing"
msgstr "SMS-Marketing testen"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "Test SMS successfully sent to %s"
msgstr "Test-SMS erfolgreich gesendet an %s"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "The following numbers are not correctly encoded: %s"
msgstr "Die folgenden Zahlen sind nicht korrekt codiert: %s"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "There was an error when trying to unsubscribe"
msgstr "Beim Versuch, sich abzumelden, ist ein Fehler aufgetreten"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr ""
"Diese Rufnummer steht auf der schwarzen Liste für SMS-Marketing. Klicken, um"
" sie von der schwarzen Liste zu entfernen."

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"This will send SMS to all recipients now. Do you still want to proceed ?"
msgstr "Die SMS wird an alle Empfänger gesendet. Möchten Sie fortfahren?"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "This will send SMS to all recipients. Do you still want to proceed ?"
msgstr "SMS wird an alle Empfänger gesendet. Möchten Sie fortfahren?"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_subject
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Title"
msgstr "Titel"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__trace_type
msgid "Type"
msgstr "Typ"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM-Kampagne"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_has_unregistered_account
msgid "UX Field to propose to Register the SMS IAP account"
msgstr ""
"Benutzererlebnisfeld für den Vorschlag zur Registrierung des SMS-IAP-Kontos"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_has_insufficient_credit
msgid "UX Field to propose to buy IAP credits"
msgstr "Benutzererlebnisfeld, um vorzuschlagen, IAP-Guthaben zu kaufen"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_unread
msgid "Unread Messages"
msgstr "Ungelesene Nachrichten"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Anzahl ungelesener Nachrichten"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_acc
msgid "Unregistered Account"
msgstr "Nichtregistriertes Konto"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_has_unregistered_account
msgid "Unregistered IAP account"
msgstr "Nichtregistriertes IAP-Konto"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_kanban_sms
msgid "Unregistered account"
msgstr "Nichtregistriertes Konto"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Unsubscribe me"
msgstr "Mich abmelden"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "Unsupported %s for mass SMS"
msgstr "%s nicht unterstützt für Massen-SMS"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_res_users
msgid "Users"
msgstr "Benutzer"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_search
msgid "Valid SMS Recipients"
msgstr "Gültiger SMS-Empfänger"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationshistorie"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Winner Selection"
msgstr "Gewinnerauswahl"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_mailing_action_sms
msgid ""
"Write an appealing SMS Text Message, define recipients and track its "
"results."
msgstr ""
"Schreiben Sie eine ansprechende SMS, definieren Sie Empfänger und verfolgen "
"Sie die Ergebnisse."

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "Falsches Zahlenformat"

#. module: mass_mailing_sms
#: model:mailing.mailing,name:mass_mailing_sms.mailing_sms_0
#: model:mailing.mailing,sms_subject:mass_mailing_sms.mailing_sms_0
#: model:utm.campaign,name:mass_mailing_sms.utm_campaign_0
#: model:utm.source,name:mass_mailing_sms.mailing_sms_0_utm_source
msgid "XMas Promo"
msgstr "Weihnachtspromo"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "e.g. Black Friday SMS coupon"
msgstr "z. B. SMS-Gutschein für Black Friday"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "has been successfully blacklisted"
msgstr "wurde erfolgreich auf die schwarze Liste gesetzt"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "has been successfully removed from"
msgstr "wurde erfolgreich entfernt aus"
