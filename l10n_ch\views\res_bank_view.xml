<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="isr_partner_bank_form" model="ir.ui.view">
            <field name="name">l10n_ch.res.partner.bank.form</field>
            <field name="model">res.partner.bank</field>
            <field name="inherit_id" ref="base.view_partner_bank_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='acc_number']" position="after">
                    <field name="l10n_ch_qr_iban" attrs="{'invisible': [('l10n_ch_show_subscription', '=', False)]}"/>
                    <label for="l10n_ch_postal" string="ISR Client Identification Number" attrs="{'invisible': [('l10n_ch_show_subscription', '=', False)]}"/>
                    <field name="l10n_ch_postal" nolabel="1" attrs="{'invisible': [('l10n_ch_show_subscription', '=', False)]}"/>
                    <field name="l10n_ch_postal" attrs="{'invisible': [('l10n_ch_show_subscription', '=', True)]}"/>
                    <field name="l10n_ch_show_subscription" invisible="1"/>
                    <field name="l10n_ch_isr_subscription_chf" attrs="{'invisible': [('l10n_ch_show_subscription', '=', False)]}"/>
                    <field name="l10n_ch_isr_subscription_eur" attrs="{'invisible': [('l10n_ch_show_subscription', '=', False)]}"/>
                </xpath>
            </field>
        </record>

        <record id="isr_partner_bank_tree" model="ir.ui.view">
            <field name="name">l10n_ch.res.partner.bank.tree</field>
            <field name="model">res.partner.bank</field>
            <field name="inherit_id" ref="base.view_partner_bank_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='acc_number']" position="after">
                    <field name="l10n_ch_postal" invisible="1"/>
                </xpath>
            </field>
        </record>

        <record id="isr_partner_property_bank_tree" model="ir.ui.view">
            <field name="name">l10n_ch.res.partner.property.form</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="account.view_partner_property_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='acc_number']" position="after">
                    <field name="l10n_ch_postal" invisible="1"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
