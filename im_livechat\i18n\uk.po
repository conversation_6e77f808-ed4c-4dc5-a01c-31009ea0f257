# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2022
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 01:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "# Messages"
msgstr "К-сть повідомлень"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr "К-сть оцінювань"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "К-сть сесій"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr "К-сть спікерів"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% Задоволених"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr "% вдоволення"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s та %s набирає текст..."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s набирає текст..."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s і більше набирають текст..."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Display the button' displays the chat button on the pages.\n"
"* 'Auto popup' displays the button and automatically open the conversation pane.\n"
"* 'Hide the button' hides the chat button on the pages."
msgstr ""
"* \"Відображення кнопки\" відображає кнопку чату на сторінках.\n"
"* \"Автоматично спливає\" показує кнопку та автоматично відкриває панель розмови.\n"
"* \"Приховати кнопку\" приховує кнопку чату на сторінках."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr ", на"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "-------- Show older messages --------"
msgstr "-------- Показати старіші повідомлення --------"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "<i class=\"fa fa-user\" role=\"img\" aria-label=\"User\" title=\"User\"/>"
msgstr "<i class=\"fa fa-user\" role=\"img\" aria-label=\"User\" title=\"User\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"<span class=\"text-muted\">Define rules for your live support channel. You "
"can apply an action for the given URL, and per country.<br/>To identify the "
"country, GeoIP must be installed on your server, otherwise, the countries of"
" the rule will not be taken into account.</span>"
msgstr ""
"<span class=\"text-muted\">Визначте правила для каналу підтримки. Ви можете "
"застосувати дію для вказаної URL-адреси та для кожної країни.<br/>Щоби "
"визначити країну, на ваш сервер необхідно встановити GeoIP, в іншому "
"випадку, країни цього правила не будуть враховані.</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr "<span style=\"font-size: 10px;\">Розмова живого чату</span><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr "<span>З найкращими побажаннями,</span><br/><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr "<span>Вітаємо,</span><br/>Це копія вашої розмови з"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""
"Сесія не відповідає, якщо оператор не відповів.\n"
"                                      Якщо відвідувач також є оператором, то сесія завжди буде відповідати."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Action"
msgstr "Дія"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr "Адміністратор"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Anonymous"
msgstr "Анонім"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__anonymous_name
msgid "Anonymous Name"
msgstr "Ім'я аноніма"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "Ви всередині матриці?"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Ask something ..."
msgstr "Запитайте що-небудь..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Attendees"
msgstr "Учасники"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Auto popup"
msgstr "Автоматично спливаюче вікно"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Auto popup timer"
msgstr "Таймер автоматичного спливаючого вікна"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Avatar"
msgstr "Аватар"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "Середня тривалість"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "Середнє повідомлення"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr "Середній час у секундах, щоб дати першу відповідь відвідувачу"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "Середній час надання першої відповіді відвідувачу"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Bad"
msgstr "Погано"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Bounced"
msgstr "Повернено"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr "Фоновий колір кнопки"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr "Колір тексту кнопки"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Canceled"
msgstr "Скасовано"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_canned_response_action
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "Запитувані відповіді"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""
"Запитані відповіді дозволяють вставити попередньо записані відповіді у\n"
"ваші повідомлення, набравши <i>:shortcut</i>. Ярлик\n"
"замінено безпосередньо у вашому повідомленні, так що ви все одно можете редагувати\n"
"це перед відправленням."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Changed"
msgstr "Змінено"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Channel"
msgstr "Канал"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr "Колір хедеру каналу"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "Назва каналу"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "Правило каналу"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "Правила каналу"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Тип каналу"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
msgid "Channels"
msgstr "Канали"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr "Наповнювач поля вводу"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"Чат приватний та унікальний між 2 особами. Група є приватною серед "
"запрошених осіб. Канал можна вільно приєднувати (залежно від його "
"конфігурації)."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Chat with one of our collaborators"
msgstr "Спілкуйтеся з одним із наших співробітників"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close"
msgstr "Закрити"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close chat window"
msgstr "Закрити вікно чату"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close conversation"
msgstr "Закрити розмову"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "Код"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "Налаштування"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "Розмова"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Conversation Sent"
msgstr "Розмова надіслана"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Conversation with %s"
msgstr "Розмова з %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr "Оброблені розмови"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr ""
"Скопіюйте та вставте цей код на свій веб-сайт у межах тегу &lt;head&gt; :"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__country_id
msgid "Country"
msgstr "Країна"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr "Країна відвідувача"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__country_id
msgid "Country of the visitor of the channel"
msgstr "Країна відвідувача каналу"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr "Створіть канал і почніть спілкуватися, щоб заповнити історію."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid "Create a new canned response"
msgstr "Створіть нову автоматичну відповідь"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "Створив"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "Створено на"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Creation Date"
msgstr "Дата створення"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr "Дата створення"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "Дата створення (година)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "Оцінювання клієнта"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr "Номер дня"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day number of the session (1 is Monday, 7 is Sunday)"
msgstr "Номер дня сесії (1 це понеділок, 7 це неділя)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr "Дні дій"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr "Типовий фоновий колір кнопки Живого чату"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr "Типовий фоновий колір хедеру каналу під час відкриття чату"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr "Типовий колір тексту кнопки Живого чату"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr "Типовий тест для відображення на кнопці живого чату"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr "Типовий колір заголовка каналу під час відкриття чату"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr "Визначіть новий канал живого чату веб-сайту"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Auto popup' otherwise this parameter will not be "
"taken into account."
msgstr ""
"Затримка (у секундах) для автоматичного відкриття вікна розмови. Примітка: "
"вибрана дія повинна бути \"Автоматично спливаюче\", інакше цей параметр не "
"буде врахований."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Delete"
msgstr "Видалити"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Did we correctly answer your question ?"
msgstr "Чи правильно ми відповіли на ваше запитання?"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr "Огляд"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel
msgid "Discussion Channel"
msgstr "Канал обговорення"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Display the button"
msgstr "Відображати кнопку"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Document not downloadable"
msgstr "Документ не завантажується"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Download"
msgstr "Завантажити"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "Тривалість розмови (в секундах)"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Error"
msgstr "Помилка"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Explain your note"
msgstr "Поясніть свою примітку"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, go to Website &gt; Configuration &gt; "
"Settings and select the Website Live Chat Channel you want to add to your "
"website."
msgstr ""
"Для веб-сайтів, створених на Odoo CMS, перейдіть у Веб-сайт &gt; "
"Налаштування &gt; Налаштування та оберіть Канал живого чату веб-сайту, який "
"ви хочете додати до вашого веб-сайту."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr ""
"З урахуванням порядку, щоб знайти відповідне правило. Якщо 2 правила "
"відповідають даній URL-адресі/країні, буде вибрано той, який має найнижчу "
"послідовність."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Good"
msgstr "Добре"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Group By..."
msgstr "Групувати за..."

#. module: im_livechat
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: model:im_livechat.channel,button_text:im_livechat.im_livechat_channel_data
#, python-format
msgid "Have a Question? Chat with us."
msgstr "Є запитання? Напишіть нам."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr "Фоновий колір хедеру"

#. module: im_livechat
#: model:im_livechat.channel,default_message:im_livechat.im_livechat_channel_data
msgid "Hello, how may I help you?"
msgstr "Вітаю, чим я можу допомогти?"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide the button"
msgstr "Приховати кнопку"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "History"
msgstr "Історія"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr "Час початку дати сессії"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "How may I help you?"
msgstr "Чим я можу вам допомогти?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr "Як використовувати віджет живого чату на сайті?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "ID"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Idle"
msgstr "Не задіяний"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
#, python-format
msgid "Image"
msgstr "Зображення"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Invalid email address"
msgstr "Невірна адреса електронної пошти"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__is_discuss_sidebar_category_livechat_open
msgid "Is category livechat open"
msgstr "Чи відкрита категорія чату"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr "Живий чат триває?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr "Чи відвідувач анонім"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_3
msgid "Joel Willis, Marc Demo"
msgstr "Joel Willis, Marc Demo"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_2
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_6
msgid "Joel Willis, Mitchell Admin"
msgstr "Joel Willis, Mitchell Admin"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "Приєднатися"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "Приєднатися до каналу"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr "Значення Kpi розмов живого чату"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr "Значення Kpi оцінювання живого чату"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr "Значення Kpi відповіді живого чату"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "Останні 24 години"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator____last_update
msgid "Last Modified on"
msgstr "Останні зміни на"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "Відпустка"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "Покинути канал"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "Учасники каналу"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr "Живий чат"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr "Пошук каналу живого чату"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/components/discuss/discuss.js:0
#: code:addons/im_livechat/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/im_livechat/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#, python-format
msgid "Livechat"
msgstr "Живий чат"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr "Кнопка живого чату"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr "Колір кнопки живого чату"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "Канал живого чату"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr "Правила каналу чату"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__mail_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr "Розмова у живому чаті"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_mail_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr "ID оператора живого чату необхідний для каналу типу живого чату."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr "Звіт каналу підтримки живого чату"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audiance, the duration of a session, etc."
msgstr ""
"Статистика каналів підтримки живого чату дозволяє вам легко перевірити та "
"проаналізувати сесію живого чату вашої компанії. Витягувати інформацію про "
"пропущені сесії, аудиторію, тривалість сесії тощо."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr "Звіт оператора підтримки живого чату"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "Статистика підтримки живого чату"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
msgid "Livechat Username"
msgstr "Ім'я користувача живого чату"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr "Вікно живого чату"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_active
msgid "Livechat session is active until visitor leave the conversation."
msgstr "Сесія живого чату активна, поки відвідувач не залишить розмову."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Loading"
msgstr "Завантаження"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Loading older messages..."
msgstr "Завантаження старіших повідомлень..."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as Read"
msgstr "Позначити як прочитане"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as Todo"
msgstr "Позначити як Зробити"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as todo"
msgstr "Позначити як зробити"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr "Порядок відповідності"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
msgid "Message"
msgstr "Повідомлення"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr "Пропущені сессії"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
msgid "Name"
msgstr "Ім'я"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "New messages"
msgstr "Нові повідомлення"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Next"
msgstr "Наступний"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "No available collaborator, please try again later."
msgstr "Немає доступного співробітника, спробуйте знову пізніше."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr "Жодних рейтингів клієнтів на сеансі живого чату ще немає"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr "Ще немає даних!"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "No history found"
msgstr "Історія не знайдена"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Note by"
msgstr "Автор примітки"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "Number of conversation"
msgstr "Кількість розмов"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr "Кількість днів після першого сеансу оператора"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "Кількість різних спікерів"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "Кількість повідомлень розмови"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "OK"
msgstr "Ок"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr "Odoo"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Offline"
msgstr "Офлайн"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Online"
msgstr "Онлайн"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr "Назва онлайн-чату"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Oops! Something went wrong."
msgstr "От халепа! Щось пішло не так."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_operator_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "Оператор"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "Аналіз оператора"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_operator_id
msgid "Operator for this specific channel"
msgstr "Оператор для цього конкретного каналу"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "Оператори"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"Operators\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"
msgstr ""
"Оператори\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr ""
"Оператори, які не показують ніякої активності в Odoo протягом більше ніж 30 "
"хвилин вважатимуться відключеними."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "Опції"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "PDF file"
msgstr "PDF-файл"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Відсоток щасливих оцінювань"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please check your internet connection."
msgstr "Перевірте з'єднання з інтернетом."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please wait"
msgstr "Будь ласка, зачекайте"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please wait..."
msgstr "Будь ласка, зачекайте..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr "Зроблено"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Previous"
msgstr "Попередній"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Print"
msgstr "Друк"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_form_livechat
msgid "Rating"
msgstr "Оцінка"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_avg
msgid "Rating Average"
msgstr "Середнє оцінювання"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Останній зворотній зв'язок оцінки"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Останнє зображення оцінювання"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Останнє значення оцінювання"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Оцінювання вдоволеності"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_count
msgid "Rating count"
msgstr "Підрахунок оцінювання"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Rating: %s"
msgstr "Рейтинг: %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
msgid "Ratings"
msgstr "Оцінювання"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr "Рейтинги для каналу живого чату"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Ready"
msgstr "Підготовлено"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__rating_last_feedback
msgid "Reason of the rating"
msgstr "Причина оцінювання"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Receive a copy of this conversation"
msgstr "Отримайте копію цієї розмови"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Received by Everyone"
msgstr "Отримано усіма"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Received by:"
msgstr "Отримано:"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr ""
"Регулярний вираз зі зазначенням веб-сторінок буде застосований на це "
"правило."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Reply"
msgstr "Відповісти"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "Звіт"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Reset Zoom"
msgstr "Скинути масштаб"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Reset to default colors"
msgstr "Скинути до типових кольорів"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Rotate"
msgstr "Повернути"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "Правила"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr "Рівень задоволення"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr "Збережіть ваш канал, щоби отримати віджет налаштування."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Say something"
msgstr "Скажіть щось"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "Сценарій (зовнішній)"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Search history"
msgstr "Пошук історії"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "Пошук звіту"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "See 15 last visited pages"
msgstr "Дивитися 15 останніх відвідуваних сторінок"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Seen by Everyone"
msgstr "Переглянуто усіма"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Seen by:"
msgstr "Переглянуто:"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Send"
msgstr "Надіслати"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Sent"
msgstr "Надіслано"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Session Date"
msgstr "Дата сессії"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Session Form"
msgstr "Форма сессії"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "Статистика сесії"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Session expired... Please refresh and try again."
msgstr "Сесія закінчилася... Оновіть та спробуйте ще раз."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr "Сесію не оцінено"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr "Сесія(ї) без відповіді"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "Сесії"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "Історія сесій"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "Дата початку сесії"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr "Година початку сесії"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start date of the conversation"
msgstr "Дата початку розмови"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start hour of the conversation"
msgstr "Година початку розмови"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "Текст кнопки"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr "Текст, який пропонує користувачеві ініціювати чат."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Thank you for your feedback"
msgstr "Дякуємо за ваш відгук"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr "Канал правила"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__name
msgid "The name of the channel"
msgstr "Назва каналу"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide Button', "
"the chat button will be hidden on the specified URL from the visitors "
"located in these 2 countries. This feature requires GeoIP installed on your "
"server."
msgstr ""
"Правило застосовуватиметься лише для цих країн. Приклад: якщо ви виберете "
"\"Бельгія\" та \"США\" і встановите дію на \"Приховати кнопку\", кнопка чату"
" буде прихована на вказаній URL-адресі від відвідувачів, розташованих у цих "
"двох країнах. Ця функція вимагає установки GeoIP на вашому сервері."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr "Користувач зможе видалити канали підтримки."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "Користувач зможе об'єднати канали підтримки."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr "На даний момент немає рейтингу для цього каналу"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr "Цього тижня"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr ""
"Це автоматичне повідомлення \"ласкаво просимо\", яке ваш відвідувач побачить"
" під час ініціації нової бесіди."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr ""
"Це ім'я користувача буде використовуватися як ваше ім'я в каналах живого "
"чату."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr "Час для відповіді"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr "Час на відповідь (сек)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_digest_digest__kpi_livechat_response
msgid "Time to answer the user in second."
msgstr "Час відповіді користувачу у секундах."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
msgid "Time to answer(sec)"
msgstr "Час на відповідь(сек)"

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr "Примітка: використовуйте готові відповіді, для швидшого спілкування"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr "Колір заголовка"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Today"
msgstr "Сьогодні"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr "Оброблені сесії"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Try again"
msgstr "Спробуйте знову"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Type <b>:shortcut</b> to insert a canned response in your message.<br>"
msgstr ""
"Тип <b>:shortcut</b> щоб вставити готову відповідь у своє повідомлення.<br>"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "URL Regex"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr ""
"URL-адреса статичної сторінки, за якою клієнт може обговорити з оператором "
"каналу."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Undefined"
msgstr "Невизначений"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Uploaded"
msgstr "Завантажено"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Uploading"
msgstr "Завантаження"

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr ""
"Використовуйте готові відповіді для визначення шаблонів повідомлень у модулі"
" живого чату. Щоби завантажити готову відповідь, почніть ваше речення з ':' "
"та оберіть шаблон."

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr "Користувач"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr "Ім'я користувача живого чату"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr "Налаштування користувача"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is idle"
msgstr "Користувач незайнятий"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is offline"
msgstr "Користувач офлайн"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is online"
msgstr "Користувач онлайн"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
msgid "Users"
msgstr "Користувачі"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Video"
msgstr "Відео"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Viewer"
msgstr "Глядач"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Visitor"
msgstr "Відвідувач"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_0
msgid "Visitor #234, Mitchell Admin"
msgstr "Відвідувач #234, Mitchell Admin"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_1
msgid "Visitor #323, Marc Demo"
msgstr "Відвідувач #323, Marc Demo"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_4
msgid "Visitor #532, Mitchell Admin"
msgstr "Відвідувач #532, Mitchell Admin"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_5
msgid "Visitor #649, Mitchell Admin"
msgstr "Відвідувач #649, Mitchell Admin"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_7
msgid "Visitor #722, Marc Demo"
msgstr "Відвідувач #722, Marc Demo"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Visitor has left the conversation."
msgstr "Відвідувач покинув розмову."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr "Відвідувач щасливий"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.mail_channel_livechat_1
msgid "Visitor, Mitchell Admin"
msgstr "Відвідувач, Mitchell Admin"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "Веб-сторінка"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "Чат-канали веб-сайту"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "Привітальне повідомлення"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr "Віджет"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Yesterday"
msgstr "Вчора"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""
"Ви можете створювати канали для кожного веб-сайту, на якому ви хочете\n"
"інтегрувати віджет живого чату веб-сайту, дозволяючи вашим відвідувачам веб-сайту\n"
"спілкуватися в режимі реального часу з вашими операторами."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Your chatter history is empty"
msgstr "Ваша історія чату пуста"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Zoom In"
msgstr "Збільшити"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Zoom Out"
msgstr "Зменшити"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr "напр. /contactus"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "напр. Вітаю, чим я можу допомогти?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "напр. YourWebsite.com"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "from"
msgstr "від"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "on"
msgstr "на"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr ""
"або скопіюйте цю URL-адресу та надішліть її електронною поштою своїм "
"клієнтам або постачальникам:"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "read less"
msgstr "приховати"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "read more"
msgstr "читати далі"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "секунди"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "unnamed"
msgstr "без назви"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr "{{author_name}}"
