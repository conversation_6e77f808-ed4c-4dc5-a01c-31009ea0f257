# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid " (Estimated Cost: %s )"
msgstr "(Cost estimat: %s)"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_quant_package_weight_form
msgid "(computed:"
msgstr "(calculat:"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "<i class=\"fa fa-arrow-right mr-1\"/>Get rate"
msgstr "<i class=\"fa fa-arrow-right mr-1\"/>Obține preț"

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent\">\n"
"                    Buy Odoo Enterprise now to get more providers.\n"
"                </p>"
msgstr ""
"<p class=\"o_view_nocontent\">\n"
"                   Cumpărați Odoo Enterprise acum pentru a obține mai mulți furnizori.\n"
"                </p>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"o_warning_text\">Test</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"o_warning_text\">Testare</span>\n"
"                                    <span class=\"o_stat_text\">Mediu </span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-danger\">No debug</span>"
msgstr "<span class=\"text-danger\">Fără depanare</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-success\">Debug requests</span>"
msgstr "<span class=\"text-success\">Solicitări de depanare</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"text-success\">Production</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"text-success\">Producție</span>\n"
"                                    <span class=\"o_stat_text\">Mediu</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight (estimated): </span>"
msgstr "<span> - Greutate (estimat): </span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_stock_report_delivery_no_package_section_line
#: model_terms:ir.ui.view,arch_db:delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight: </span>"
msgstr "<span> - Greutate: </span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_small_delivery
msgid "<span>Shipping Weight: </span>"
msgstr "<span>Transport Greutate: </span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid "<strong>Carrier:</strong>"
msgstr "<strong>Transportator:</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid "<strong>HS Code</strong>"
msgstr "<strong>Cod HS</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_delivery
msgid ""
"<strong>Shipping Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>Transport Greutate:</strong>\n"
"                <br/>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid ""
"<strong>Total Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>Greutate Totală:</strong>\n"
"                <br/>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid "<strong>Tracking Number:</strong>"
msgstr "<strong>Număr urmărire:</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid ""
"<strong>Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>Greutate:</strong>\n"
"                <br/>"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__integration_level
msgid "Action while validating Delivery Orders"
msgstr "Acțiune în timpul validării comenzilor de livrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__active
msgid "Active"
msgstr "Activ"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Add"
msgstr "Adaugă"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "Add a shipping method"
msgstr "Adăugați o metodă de expediere"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Add shipping"
msgstr "Adăugați transportul"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__amount
msgid "Amount"
msgstr "Valoare"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__amount
msgid ""
"Amount of the order to benefit from a free shipping, expressed in the "
"company currency"
msgstr ""
"Cantitatea comenzii pentru a beneficia de o livrare gratuita, exprimata in "
"moneda companiei"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Archived"
msgstr "Arhivat"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__available_carrier_ids
msgid "Available Carriers"
msgstr "Transportatori disponibili"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__base_on_rule
msgid "Based on Rules"
msgstr "Bazat pe reguli"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr "Masă brută"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__can_generate_return
msgid "Can Generate Return"
msgstr "Poate genera returnare"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Cancel"
msgstr "Anulează"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__carrier_id
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__carrier_id
#: model:ir.model.fields,field_description:delivery.field_stock_package_type__package_carrier_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_tree
msgid "Carrier"
msgstr "Transportator"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_package_type__shipper_package_code
msgid "Carrier Code"
msgstr "Cod transportator"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_choose_delivery_carrier__carrier_id
msgid "Choose the method to deliver your goods"
msgstr "Alegeți metoda de livrare a bunurilor"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__company_id
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__company_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__company_id
msgid "Company"
msgstr "Companie"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Condition"
msgstr "Condiție"

#. module: delivery
#: model:ir.model,name:delivery.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_price
msgid "Cost"
msgstr "Cost"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__country_ids
msgid "Countries"
msgstr "Țări"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_date
msgid "Created on"
msgstr "Creat în"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__partner_id
msgid "Customer"
msgstr "Client"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__debug_logging
msgid "Debug logging"
msgstr "Jurnal de depanare"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,help:delivery.field_res_users__property_delivery_carrier_id
msgid "Default delivery method used in sales orders."
msgstr "Metoda de livrare implicită utilizată în comenzile de vânzare."

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid "Define a new delivery method"
msgstr "Definiți o nouă metodă de livrare"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Delivery Carrier"
msgstr "Transportator"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Expert de selecție a operatorului de livrare"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Delivery Cost"
msgstr "Costul de livrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_message
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_message
msgid "Delivery Message"
msgstr "Mesaj livrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__name
#: model:ir.model.fields,field_description:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_res_users__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_sale_order__carrier_id
msgid "Delivery Method"
msgstr "Metodă de livrare"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "Expert de selecție a pachetului de livrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__delivery_package_type_id
msgid "Delivery Package Type"
msgstr "Tipul pachetului de livrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_price
msgid "Delivery Price"
msgstr "Preț de livrare"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_price_rule
msgid "Delivery Price Rules"
msgstr "Reguli de preț de livrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__product_id
msgid "Delivery Product"
msgstr "Livrare produs"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_rating_success
msgid "Delivery Rating Success"
msgstr "Evaluare de livrare cu succes"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_set
msgid "Delivery Set"
msgstr "Set de livrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__recompute_delivery_price
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__recompute_delivery_price
msgid "Delivery cost should be recomputed"
msgstr "Costul de livrare ar trebui recalculat"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.vpicktree_view_tree
msgid "Destination"
msgstr "Destinație"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Destination Availability"
msgstr "Destinații Disponibile"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,field_description:delivery.field_stock_picking__destination_country_code
msgid "Destination Country"
msgstr "Țara destinație"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__sequence
msgid "Determine the display order"
msgstr "Determină ordinea de afișare"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Discard"
msgstr "Abandonează"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.act_delivery_trackers_url
msgid "Display tracking links"
msgstr "Afișați linkuri de urmărire"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"Each carrier (e.g. UPS) can have several delivery methods (e.g.\n"
"                UPS Express, UPS Standard) with a set of pricing rules attached\n"
"                to each method."
msgstr ""
"Fiecare transportator (de exemplu, UPS) poate avea mai multe metode de "
"livrare (de exemplu, UPS Express, UPS Standard) cu un set de reguli de "
"prețuri atașate la fiecare metodă."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__prod_environment
msgid "Environment"
msgstr "Mediu"

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:0
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "Error: this delivery method is not available for this address."
msgstr ""
"Eroare: această metodă de livrare nu este disponibilă pentru această adresă."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of the shipping will be updated on the SO after the delivery."
msgstr ""
"Cost estimat: clientului i se va factura costul estimat al transportului.\n"
"Cost real: clientului i se va factura costul real al transportului, costul transportului va fi actualizat pe SO după livrare."

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__estimated
msgid "Estimated cost"
msgstr "Cost estimat"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_sale_order__carrier_id
msgid "Fill this field if you plan to invoice the shipping based on picking."
msgstr ""
"Completați acest câmp dacă intenționați să facturați expedierea pe baza "
"alegerii."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Filling this form allows you to filter delivery carriers according to the "
"delivery address of your customer."
msgstr ""
"Completarea acestui formular vă permite să filtrați operatorii de transport "
"în funcție de adresa de livrare a clientului dvs."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__fixed
msgid "Fixed Price"
msgstr "Preț fixat"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid "Free Shipping"
msgstr "Livrare gratuită"

#. module: delivery
#: model:delivery.carrier,name:delivery.free_delivery_carrier
#: model:product.product,name:delivery.product_product_delivery
#: model:product.template,name:delivery.product_product_delivery_product_template
msgid "Free delivery charges"
msgstr "Fără taxă de livrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__free_over
msgid "Free if order amount is above"
msgstr "Gratuit dacă valoarea comenzii este mai mare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__return_label_on_delivery
msgid "Generate Return Label"
msgstr "Generați eticheta de returnare"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate
msgid "Get Rate"
msgstr "Obține preț"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate_and_ship
msgid "Get Rate and Create Shipment"
msgstr "Obține preț și creează livrare"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Group By"
msgstr "Grupează după"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_product__hs_code
#: model:ir.model.fields,field_description:delivery.field_product_template__hs_code
msgid "HS Code"
msgstr "Cod HS"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__id
msgid "ID"
msgstr "ID"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__free_over
msgid ""
"If the order total amount (shipping excluded) is above or equal to this "
"value, the customer benefits from a free shipping"
msgstr ""
"Dacă suma totală a comenzii (cu excepția livrării) este mai mare sau egală "
"cu această valoare, clientul beneficiază de o livrare gratuită"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Install more Providers"
msgstr "Instalează alți furnizori"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__integration_level
msgid "Integration Level"
msgstr "Nivel de integrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__invoicing_message
msgid "Invoicing Message"
msgstr "Mesaj de facturare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "Politică de facturare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__is_return_picking
msgid "Is Return Picking"
msgstr "Este returnare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__is_delivery
msgid "Is a Delivery"
msgstr "Este o livrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier____last_update
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package____last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier____last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__debug_logging
msgid "Log requests in order to ease debugging"
msgstr "Înregistrați solicitările pentru a ușura depanarea"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__margin
msgid "Margin"
msgstr "Marja"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_margin_not_under_100_percent
msgid "Margin cannot be lower than -100%"
msgstr "Marja nu poate fi mai mică de -100%"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Margin on Rate"
msgstr "Marja pe tarif"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__max_value
msgid "Maximum Value"
msgstr "Valoarea maximă"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__name
msgid "Name"
msgstr "Nume"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__stock_package_type__package_carrier_type__none
msgid "No carrier integration"
msgstr "Nicio integrare a operatorului de transport"

#. module: delivery
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "No price rule matching this order; delivery cost cannot be computed."
msgstr ""
"Nicio regulă de preț care să corespundă acestei comenzi; costul de livrare "
"nu poate fi calculat."

#. module: delivery
#: model:delivery.carrier,name:delivery.normal_delivery_carrier
#: model:product.product,name:delivery.product_product_delivery_normal
#: model:product.template,name:delivery.product_product_delivery_normal_product_template
msgid "Normal Delivery Charges"
msgstr "Taxă obișnuită de livrare"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "OK"
msgstr "OK"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__operator
msgid "Operator"
msgstr "Operator"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__order_id
msgid "Order"
msgstr "Comandă"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Package"
msgstr "Pachet"

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid "Package Details"
msgstr "Detalii pachet"

#. module: delivery
#: code:addons/delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid "Package too heavy!"
msgstr "Pachetul este prea greu!"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_quant_package
#: model:ir.model.fields,field_description:delivery.field_stock_picking__package_ids
msgid "Packages"
msgstr "Pachete"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__picking_id
msgid "Picking"
msgstr "Ridicare"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__price
msgid "Price"
msgstr "Preț"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_tree
msgid "Price Rules"
msgstr "Reguli de preț"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Pricing"
msgstr "Prețuri"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__price_rule_ids
msgid "Pricing Rules"
msgstr "Reguli de preț"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale_delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Print Return Label"
msgstr "Imprimați eticheta de returnare"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Mișcări de produs (linie mișcare stoc)"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__product_qty
msgid "Product Qty"
msgstr "Cantitate produs"

#. module: delivery
#: model:ir.model,name:delivery.model_product_template
msgid "Product Template"
msgstr "Șablon produs"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking__delivery_type
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Provider"
msgstr "Furnizor"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__quantity
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__quantity
msgid "Quantity"
msgstr "Cantitate"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__real
msgid "Real cost"
msgstr "Cost real"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__return_label_ids
msgid "Return Label"
msgstr "Etichetă Returnare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__get_return_label_from_portal
msgid "Return Label Accessible from Customer Portal"
msgstr "Etichetă de returnare accesibilă din portalul clienților"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_return_picking
msgid "Return Picking"
msgstr "Retur Ridicare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_base_price
msgid "Sale Base Price"
msgstr "Preț de bază"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_price
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__sale_price
msgid "Sale Price"
msgstr "Preț vânzare"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order
msgid "Sales Order"
msgstr "Comandă de vânzare"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Linie comandă vânzare"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Save"
msgstr "Salvează"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Send to Shipper"
msgstr "Trimite la Curier"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__sequence
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__sequence
msgid "Sequence"
msgstr "Secvență"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__is_all_service
msgid "Service Product"
msgstr "Produs de service"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__prod_environment
msgid "Set to True if your credentials are certified for production."
msgstr ""
"Setați la True dacă acreditările dvs. sunt certificate pentru producție."

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Shipment sent to carrier %(carrier_name)s for shipping with tracking number "
"%(ref)s<br/>Cost: %(price).2f %(currency)s"
msgstr ""
"Expediere trimisă către curierul %(carrier_name)s pentru expediere cu "
"numărul de urmărire %(ref)s<br/>Cost: %(price).2f %(currency)s"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_price
msgid "Shipping Cost"
msgstr "Cost livrare"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Shipping Information"
msgstr "Informații de livrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Shipping Method"
msgstr "Metodă de expediere"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_carrier_form
#: model:ir.model,name:delivery.model_delivery_carrier
#: model:ir.ui.menu,name:delivery.menu_action_delivery_carrier_form
#: model:ir.ui.menu,name:delivery.sale_menu_action_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Metode de expediere"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__shipping_weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr "Greutate livrare"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_product_product__hs_code
#: model:ir.model.fields,help:delivery.field_product_template__hs_code
msgid ""
"Standardized code for international shipping and goods declaration. At the "
"moment, only used for the FedEx shipping provider."
msgstr ""
"Cod standardizat pentru expediția internațională și declarația mărfurilor. "
"În acest moment, este utilizat numai pentru furnizorul de transport FedEx."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__state_ids
msgid "States"
msgstr "Județe"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move
msgid "Stock Move"
msgstr "Mișcare stoc"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_package_type
msgid "Stock package type"
msgstr "Tip pachet stoc"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,help:delivery.field_stock_picking__destination_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Codul ISO al țării din două caractere.\n"
"Puteți utiliza acest câmp pentru căutări rapide."

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_carrier
#: model:product.product,name:delivery.product_product_delivery_poste
#: model:product.template,name:delivery.product_product_delivery_poste_product_template
msgid "The Poste"
msgstr "Poșta"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__get_return_label_from_portal
msgid ""
"The return label can be downloaded by the customer from the customer portal."
msgstr ""
"Eticheta de returnare poate fi descărcată de client de pe portalul pentru "
"clienți."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__return_label_on_delivery
msgid "The return label is automatically generated at the delivery."
msgstr "Eticheta de returnare este generată automat la livrare."

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid "The shipping is free since the order amount exceeds %.2f."
msgstr "Expedierea este gratuită, deoarece suma comenzii depășește %.2f."

#. module: delivery
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "The shipping price will be set once the delivery is done."
msgstr "Prețul de expediere va fi stabilit după finalizarea livrării."

#. module: delivery
#: code:addons/delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid ""
"The weight of your package is higher than the maximum weight authorized for "
"this package type. Please choose another package type."
msgstr ""
"Masa ambalajului este mai mare decât masa maximă autorizată pentru acest tip"
" de ambalaj. Alegeți un alt tip de pachet."

#. module: delivery
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "There is no matching delivery rule."
msgstr "Nu există o regulă de livrare potrivită."

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"These methods allow to automatically compute the delivery price\n"
"                according to your settings; on the sales order (based on the\n"
"                quotation) or the invoice (based on the delivery orders)."
msgstr ""
"Aceste metode permit calcularea automată a prețului de livrare în funcție de"
" setările dvs.; cu privire la comanda de vânzări (în baza cotei) sau la "
"factură (în baza ordinelor de livrare)."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__margin
msgid "This percentage will be added to the shipping price."
msgstr "Procentajul va fi adăugat la prețul de livrare"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_quant_package__weight
msgid "Total weight of all the products contained in the package."
msgstr "Greutatea totală a tuturor produselor conținute în pachet."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__shipping_weight
msgid ""
"Total weight of packages and products not in a package. Packages with no "
"shipping weight specified will default to their products' total weight. This"
" is the weight used to compute the cost of the shipping."
msgstr ""
"Greutatea totală a pachetelor și a produselor care nu sunt în pachet. "
"Pachetele fără greutatea de livrare specificată vor fi implicite la "
"greutatea totală a produselor lor. Aceasta este greutatea utilizată pentru a"
" calcula costul transportului."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__weight_bulk
msgid "Total weight of products which are not in a package."
msgstr "Greutatea totală a produselor care nu se află într-un pachet."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_quant_package__shipping_weight
msgid "Total weight of the package."
msgstr "Greutatea totală a ambalajului."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__weight
msgid "Total weight of the products in the picking."
msgstr "Greutatea totală a produselor din livrare."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "Trackers URL"
msgstr "URL de urmărire"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Tracking"
msgstr "Trasabilitate"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_tracking_ref
msgid "Tracking Reference"
msgstr "Referință urmărire"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_tracking_url
msgid "Tracking URL"
msgstr "URL de urmărire"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale_delivery
msgid "Tracking:"
msgstr "Urmărire:"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: delivery
#: model:product.product,uom_name:delivery.product_product_delivery
#: model:product.product,uom_name:delivery.product_product_delivery_normal
#: model:product.product,uom_name:delivery.product_product_delivery_poste
#: model:product.template,uom_name:delivery.product_product_delivery_normal_product_template
#: model:product.template,uom_name:delivery.product_product_delivery_poste_product_template
#: model:product.template,uom_name:delivery.product_product_delivery_product_template
msgid "Units"
msgstr "buc"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Update"
msgstr "Actualizare"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
#, python-format
msgid "Update shipping cost"
msgstr "Actualizare cost livrare"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable
msgid "Variable"
msgstr "Variabil(a)"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable_factor
msgid "Variable Factor"
msgstr "Factor variabil"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__volume
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__volume
msgid "Volume"
msgstr "Volum"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_move__weight
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__weight
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Weight"
msgstr "Masă"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__wv
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__wv
msgid "Weight * Volume"
msgstr "Masă * Volum"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr "Masă de livrat"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Weight for shipping"
msgstr "Masă de livrat"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__weight_uom_name
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight_uom_name
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Eticheta unității de măsură a greutății"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid ""
"You can not update the shipping costs on an order where it was already invoiced!\n"
"\n"
"The following delivery lines (product, invoiced quantity and price) have already been processed:\n"
"\n"
msgstr ""
"Nu puteți actualiza costurile de livrare pentru o comandă unde acestea au fost deja facturate!\n"
"\n"
"Următoarele linii de livrare (produs, cantitate și preț facturate) au fost deja procesate:\n"
"\n"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "You have multiple tracker links, they are available in the chatter."
msgstr ""
"Aveți link-uri multiple de urmărire, acestea sunt disponibile în chat."

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."
msgstr ""
"Metoda de livrare nu are nicio redirecționare pe site-ul furnizorului de "
"curierat pentru a urmări această comandă."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_from
msgid "Zip From"
msgstr "De la codul poștal"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_to
msgid "Zip To"
msgstr "La codul poștal"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "e.g. UPS Express"
msgstr "de exemplu: UPS Express"
