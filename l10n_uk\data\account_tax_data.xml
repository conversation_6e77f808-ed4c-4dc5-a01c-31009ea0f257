<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="ST0" model="account.tax.template">
        <field name="description">ST0</field>
        <field name="chart_template_id" ref="l10n_uk"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Zero rated sales</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
		<field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_exd_vat_box6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_exd_vat_box6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="ST2" model="account.tax.template">
        <field name="description">ST2</field>
        <field name="chart_template_id" ref="l10n_uk"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Exempt sales</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
		<field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_exd_vat_box6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_exd_vat_box6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="PT0" model="account.tax.template">
        <field name="description">PT0</field>
        <field name="chart_template_id" ref="l10n_uk"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Zero rated purchases</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
		<field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="PT2" model="account.tax.template">
        <field name="description">PT2</field>
        <field name="chart_template_id" ref="l10n_uk"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Exempt purchases</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
		<field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="PT8" model="account.tax.template">
        <field name="description">PT8</field>
        <field name="chart_template_id" ref="l10n_uk"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Standard rated purchases from EC</field>
        <field name="amount_type">percent</field>
        <field name="amount">20</field>
        <field name="tax_group_id" ref="tax_group_0"/>
		<field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_exd_vat_box9'), ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2201'),
                'plus_report_line_ids': [ref('account_tax_report_line_vat_box2')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2201'),
                'minus_report_line_ids': [ref('account_tax_report_line_vat_box4')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_exd_vat_box9'), ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2201'),
                'minus_report_line_ids': [ref('account_tax_report_line_vat_box2')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('2201'),
                'plus_report_line_ids': [ref('account_tax_report_line_vat_box4')],
            }),
        ]"/>
    </record>

    <record id="PT5" model="account.tax.template">
        <field name="description">PT5</field>
        <field name="chart_template_id" ref="l10n_uk"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Lower rate purchases (5%)</field>
        <field name="amount_type">percent</field>
        <field name="amount">5</field>
        <field name="tax_group_id" ref="tax_group_5"/>
		<field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2201'),
                'plus_report_line_ids': [ref('account_tax_report_line_vat_box4')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2201'),
                'minus_report_line_ids': [ref('account_tax_report_line_vat_box4')],
            }),
        ]"/>
    </record>

	<record id="ST5" model="account.tax.template">
        <field name="description">ST5</field>
        <field name="chart_template_id" ref="l10n_uk"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Lower rate sales (5%)</field>
        <field name="amount_type">percent</field>
        <field name="amount">5</field>
        <field name="tax_group_id" ref="tax_group_0"/>
		<field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_exd_vat_box6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2200'),
                'plus_report_line_ids': [ref('account_tax_report_line_vat_box1')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_exd_vat_box6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2200'),
                'minus_report_line_ids': [ref('account_tax_report_line_vat_box1')],
            }),
        ]"/>
    </record>

    <record id="ST4" model="account.tax.template">
        <field name="description">ST4</field>
        <field name="chart_template_id" ref="l10n_uk"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Sales to customers in EC</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
		<field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_exd_vat_box8'), ref('account_tax_report_line_exd_vat_box6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_exd_vat_box8'), ref('account_tax_report_line_exd_vat_box6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="PT7" model="account.tax.template">
        <field name="description">PT7</field>
        <field name="chart_template_id" ref="l10n_uk"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Zero rated purchases from EC</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
		<field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_exd_vat_box9'), ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_exd_vat_box9'), ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="ST11" model="account.tax.template">
        <field name="description">ST11</field>
        <field name="chart_template_id" ref="l10n_uk"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Standard rate sales (20%)</field>
        <field name="amount_type">percent</field>
        <field name="amount">20</field>
        <field name="tax_group_id" ref="tax_group_20"/>
		<field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_exd_vat_box6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2200'),
                'plus_report_line_ids': [ref('account_tax_report_line_vat_box1')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_exd_vat_box6')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2200'),
                'minus_report_line_ids': [ref('account_tax_report_line_vat_box1')],
            }),
        ]"/>
    </record>

    <record id="PT11" model="account.tax.template">
        <field name="description">PT11</field>
        <field name="chart_template_id" ref="l10n_uk"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Standard rate purchases (20%)</field>
        <field name="amount_type">percent</field>
        <field name="amount">20</field>
        <field name="tax_group_id" ref="tax_group_20"/>
		<field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2201'),
                'plus_report_line_ids': [ref('account_tax_report_line_vat_box4')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_exd_vat_box7')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('2201'),
                'minus_report_line_ids': [ref('account_tax_report_line_vat_box4')],
            }),
        ]"/>
    </record>

</odoo>