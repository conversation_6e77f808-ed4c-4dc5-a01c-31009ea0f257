# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_password_policy
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON>ery CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: auth_password_policy
#: model:ir.model.fields,field_description:auth_password_policy.field_res_config_settings__minlength
msgid "Minimum Password Length"
msgstr " 最小密码长度"

#. module: auth_password_policy
#: model:ir.model.fields,help:auth_password_policy.field_res_config_settings__minlength
msgid ""
"Minimum number of characters passwords must contain, set to 0 to disable."
msgstr "密码至少包含字符数，设定0为禁用此设置。"

#. module: auth_password_policy
#: code:addons/auth_password_policy/models/res_users.py:0
#, python-format
msgid "Passwords must have at least %d characters, got %d."
msgstr "密码需至少%d个字符，现有%d个字符。"

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:0
#, python-format
msgid ""
"Required: %s.\n"
"\n"
"Hint: increase length, use multiple words and use non-letter characters to increase your password's strength."
msgstr ""
"要求：%s\n"
"\n"
"提示：增加长度，使用多个文字以及非书写字符去增强您的密码强度。"

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_users
msgid "Users"
msgstr "用户"

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:0
#, python-format
msgid "at least %d character classes"
msgstr "至少%d个字符类别"

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:0
#, python-format
msgid "at least %d characters"
msgstr "至少%d个字符"

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:0
#, python-format
msgid "at least %d words"
msgstr "至少%d个字"

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:0
#, python-format
msgid "no requirements"
msgstr "无要求"
