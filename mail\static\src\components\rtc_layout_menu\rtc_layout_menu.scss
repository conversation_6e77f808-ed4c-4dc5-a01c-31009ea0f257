// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_RtcLayoutMenu {
    display: flex;
    flex-direction: column;
    padding: map-get($spacers, 1);
}

.o_RtcLayoutMenu_inputText  {
    @include text-truncate();
}

.o_RtcLayoutMenu_separator {
    width: 100%;
}

.o_RtcLayoutMenu_radio {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 1.3em;
}

.o_RtcLayoutMenu_radioIcon {
    width: 5rem;
    height: 5rem;
    margin-inline-end: map-get($spacers, 1);
}

.o_RtcLayoutMenu_radioInput {
    margin: map-get($spacers, 3);
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_RtcLayoutMenu_radio {
    cursor: pointer;
    border-radius: 5px;

    &:hover {
        background-color: gray('100');
        box-shadow: 0px 0px 1px 1px gray('300') inset;
    }

    &:active {
        background-color: gray('200');
        box-shadow: 0px 0px 1px 1px gray('400') inset;
    }
}

.o_RtcLayoutMenu_separator {
    background-color: $border-color;
}
