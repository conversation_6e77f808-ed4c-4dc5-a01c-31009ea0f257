# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock
# 
# Translators:
# <PERSON><PERSON> <zab<PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <jan.<PERSON><PERSON>@centrum.cz>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# karolína schusterov<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Aleš Fiala <<EMAIL>>, 2023\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"\n"
"%s --> Product UoM is %s (%s) - Move UoM is %s (%s)"
msgstr ""
"\n"
"\n"
"%s --> MJ Produktu je %s (%s) - MJ pohybu je %s (%s)"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"\n"
"Blocking: %s"
msgstr ""
"\n"
"\n"
"Blokování: %s"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"\n"
"Transfers %s: You cannot validate these transfers if no quantities are reserved nor done. To force these transfers, switch in edit more and encode the done quantities."
msgstr ""
"\n"
"\n"
"Převody %s: Tyto převody nelze potvrdit, pokud není rezervováno ani provedeno žádné množství. Chcete-li tyto převody vynutit, přepněte se do editace více a zakódujte hotová množství."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"\n"
"Transfers %s: You need to supply a Lot/Serial number for products %s."
msgstr ""
"\n"
"\n"
"Převody %s: U výrobků je třeba uvést výrobní číslo / sériové číslo %s."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"(%s) exists in location %s"
msgstr ""
"\n"
"(%s) existuje v místě %s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__state
msgid ""
" * Draft: The transfer is not confirmed yet. Reservation doesn't apply.\n"
" * Waiting another operation: This transfer is waiting for another operation before being ready.\n"
" * Waiting: The transfer is waiting for the availability of some products.\n"
"(a) The shipping policy is \"As soon as possible\": no product could be reserved.\n"
"(b) The shipping policy is \"When all products are ready\": not all the products could be reserved.\n"
" * Ready: The transfer is ready to be processed.\n"
"(a) The shipping policy is \"As soon as possible\": at least one product has been reserved.\n"
"(b) The shipping policy is \"When all products are ready\": all product have been reserved.\n"
" * Done: The transfer has been processed.\n"
" * Cancelled: The transfer has been cancelled."
msgstr ""
" * Návrh: Převod zatím není potvrzen. Rezervace se neuplatňuje.\n"
" * Čeká se na další operaci: Tento převod čeká na jinou operaci, než bude připraven.\n"
" * Čeká: Tento převod čeká na dostupnost některých produktů.\n"
"(a) Zásada přepravy je \"Co nejdříve\": Žádný výrobek nemohl být rezervován.\n"
"(b) Zásada přepravy je \"Až budou všechny produkty připraveny\": Ne všechny produkty mohly být rezervovány.\n"
" * Připraveno: Převod je připraven ke zpracování.\n"
"(a) Zásada přepravy je \"Co nejdříve\": alespoň jeden výrobek byl rezervován.\n"
"(b) Zásada přepravy je \"Až budou všechny produkty připraveny\": všechny produkty byly rezervovány.\n"
" * Hotovo: Převod byl zpracován.\n"
" * Zrušeno: Převod byl zrušen."

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid " - Product: %s, Serial Number: %s"
msgstr " - Produkt: %s, Sériové Číslo: %s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__cyclic_inventory_frequency
msgid ""
" When different than 0, inventory count date for products stored at this "
"location will be automatically set at the defined frequency."
msgstr ""
" Pokud se liší od 0, datum inventury produktů uložených na tomto místě se "
"automaticky nastaví v definované frekvenci."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "%(warehouse)s: Supply Product from %(supplier)s"
msgstr "%(warehouse)s: Dodání produktu z %(supplier)s"

#. module: stock
#: code:addons/stock/models/stock_package_type.py:0
#: code:addons/stock/models/stock_storage_category.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopie)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"%s use default source or destination locations from warehouse %s that will "
"be archived."
msgstr ""
"%s používá výchozí zdrojové nebo cílové umístění ze skladu %s, které bude "
"archivováno."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "&gt;"
msgstr "&gt;"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_inventory
msgid "'Count Sheet'"
msgstr "'Počet listů'"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_delivery
msgid ""
"'Delivery Slip - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr "'Dodací list - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_location_barcode
msgid "'Location - %s' % object.name"
msgstr "'Umístění - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_lot_label
msgid "'Lot-Serial - %s' % object.name"
msgstr "'Šarže-série - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_type_label
msgid "'Operation-type - %s' % object.name"
msgstr "'Typ-operace - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking
msgid ""
"'Picking Operations - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'Úkon vyzvednutí - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "(copy of) %s"
msgstr "(kopie) %s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__state
#: model:ir.model.fields,help:stock.field_stock_move_line__state
msgid ""
"* New: When the stock move is created and not yet confirmed.\n"
"* Waiting Another Move: This state can be seen when a move is waiting for another one, for example in a chained flow.\n"
"* Waiting Availability: This state is reached when the procurement resolution is not straight forward. It may need the scheduler to run, a component to be manufactured...\n"
"* Available: When products are reserved, it is set to 'Available'.\n"
"* Done: When the shipment is processed, the state is 'Done'."
msgstr ""
"* Novinka: Když je přesun akcie vytvořen a ještě nebyl potvrzen.\n"
"* Waiting Another Move: Tento stav lze vidět, když se  čeká na další krok, například v řetězovém toku.\n"
"* Čekající dostupnost: Tento stav je dosažen, když rozhodnutí k nákupu není přímé. Možná bude nutné spustit plánovač, vyrobit komponentu ...\n"
"* Dostupné: Pokud jsou produkty rezervovány, je nastaveno na „Dostupné“.\n"
"* Hotovo: Po zpracování zásilky je stav „Hotovo“."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__usage
msgid ""
"* Vendor Location: Virtual location representing the source location for products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your warehouse, aggregating its child locations ; can't directly contain products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory operations used to correct stock levels (Physical inventories)\n"
"* Production: Virtual counterpart location for production operations: this location consumes the components and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-company or inter-warehouses operations"
msgstr ""
"* Umístění Prodejce: Virtuální umístění představující zdrojové umístění pro produkty pocházející od vašich prodejců\n"
"* Pohled: Virtuální umístění slouží k vytvoření hierarchických struktur pro váš sklad a agregaci jeho podřízených umístění; nemůže přímo obsahovat produkty\n"
"* Interní umístění: Fyzická umístění ve vašich vlastních skladech,\n"
"* Umístění zákazníka: Virtuální umístění představující cílové umístění produktů zasílaných vašim zákazníkům\n"
"* Ztráta zásob: Virtuální lokace sloužící jako protějšek pro operace zásob používané k opravě úrovní zásob (fyzické zásoby) \n"
"* Výroba: Virtuální umístění protějšku pro výrobní operace: toto umístění spotřebovává komponenty a vyrábí hotové výrobky\n"
"* Místo tranzitu: Místo protistrany, které by mělo být použito v mezipodnikových nebo meziskladových operacích"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr "+ %d dní"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid ", max:"
msgstr ", max:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "-&gt;"
msgstr "-&gt;"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
" Ruční akce mohou být potřeba."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__day
msgid "1 Day"
msgstr "1 Den"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__month
msgid "1 Month"
msgstr "1 měsíc"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__week
msgid "1 Week"
msgstr "1 Týden"

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#, python-format
msgid ": Insufficient Quantity To Scrap"
msgstr ": Nedostatečné množství do odpadu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"<br/>\n"
"                    <strong>Current Inventory: </strong>"
msgstr ""
"<br/>\n"
"                    <strong>Aktuální zásoby: </strong>"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"<br>A need is created in <b>%s</b> and a rule will be triggered to fulfill "
"it."
msgstr ""
"<br>Je vytvořena potřeba v <b>%s</b> a bude spuštěno pravidlo k jejímu "
"splnění."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"<br>If the products are not available in <b>%s</b>, a rule will be triggered"
" to bring products in this location."
msgstr ""
"<br>Pokud produkty nejsou k dispozici v <b>%s</b>, bude spuštěno pravidlo, "
"které dodá produkty na toto místo."

#. module: stock
#: model:mail.template,body_html:stock.mail_template_data_delivery_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        We are glad to inform you that your order has been shipped.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Your tracking reference is\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Please find your delivery order attached for more details.<br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dobrý den <t t-out=\"object.partner_id.name or ''\">Brandone Freemane</t>,<br/><br/>\n"
"        S radostí Vám oznamujeme, že Vaše objednávka byla odeslána.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Váš odkaz pro sledování je\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Další podrobnosti naleznete v příloze objednávky doručení.<br/><br/>\n"
"        Děkujeme,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Datum\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" "
"title=\"Manage\"/>Prodej"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                All products could not be reserved. Click on the \"Check Availability\" button to try to reserve products."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"Všechny produkty nelze rezervovat. Kliknutím na tlačítko „Zkontrolovat dostupnost“ se pokusíte rezervovat produkty."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_mrp_line
msgid ""
"<i class=\"fa fa-fw fa-caret-right\" role=\"img\" aria-label=\"Unfold\" "
"title=\"Unfold\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-caret-right\" role=\"img\" aria-label=\"Unfold\" "
"title=\"Rozbalit\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid ""
"<span attrs=\"{'invisible': ['|', ('state', '=', 'done'), "
"('from_immediate_transfer', '=', True)]}\"> / </span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('state', '=', 'done'), "
"('from_immediate_transfer', '=', True)]}\"> / </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid ""
"<span class=\"d-none d-sm-block o_print_label_text\">Print Label</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"
msgstr ""
"<span class=\"d-none d-sm-block o_print_label_text\">Tisk štítku</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid ""
"<span class=\"d-none d-sm-block o_print_label_text\">Print Labels</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"
msgstr ""
"<span class=\"d-none d-sm-block o_print_label_text\">Tisk štítků</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr "<span class=\"o_stat_text\">Předpovězeno</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">In:</span>\n"
"                                <span class=\"o_stat_text\">Out:</span>"
msgstr ""
"<span class=\"o_stat_text\">Příjem:</span>\n"
"                                <span class=\"o_stat_text\">Výdej:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min:</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""
"<span class=\"o_stat_text\">Min:</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">On Hand</span>"
msgstr "<span class=\"o_stat_text\">Po ruce</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">Provoz</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Transfers</span>"
msgstr "<span class=\"o_stat_text\">Přesuny</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_mrp_line
msgid ""
"<span role=\"img\" class=\"o_stock_reports_stream\" title=\"Traceability "
"Report\" aria-label=\"Traceability Report\"><i class=\"fa fa-fw fa-level-up "
"fa-rotate-270\"/></span>"
msgstr ""
"<span role=\"img\" class=\"o_stock_reports_stream\" title=\"Traceability "
"Report\" aria-label=\"Traceability Report\"><i class=\"fa fa-fw fa-level-up "
"fa-rotate-270\"/></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Customer Address:</strong></span>"
msgstr "<span><strong>Adresa zákazníka:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr "<span><strong>Doručovací adresa:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Vendor Address:</strong></span>"
msgstr "<span><strong>Adresa dodavatele:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Warehouse Address:</strong></span>"
msgstr "<span><strong>Adresa skladu:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "<span>Assign Serial Numbers</span>"
msgstr "<span>Přiřadit sériová čísla</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "<span>Clear All</span>"
msgstr "<span>Vyčistit vše</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "<span>LN/SN:</span>"
msgstr "<span>LN/SN:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>New</span>"
msgstr "<span>Nový</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "<span>Package Type: </span>"
msgstr "<span>Typ balení: </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_delivery_no_package_section_line
msgid "<span>Products with no package assigned</span>"
msgstr "<span>Produkty bez přiřazeného balení</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<span>Remaining quantities not yet delivered:</span>"
msgstr "<span>Zbývající množství, které nebylo doručeno:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "<span>kg</span>"
msgstr "<span>kg</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid ""
"<strong>\n"
"                The done move line has been corrected.\n"
"            </strong>"
msgstr ""
"<strong>\n"
"               Řádek provedeného přesunu byl opraven.\n"
"            </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Available Quantity</strong>"
msgstr "<strong>Dostupné množství</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Counted Quantity</strong>"
msgstr "<strong>Vypočítané množství</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Delivered</strong>"
msgstr "<strong>Doručeno</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"<strong>Due to some stock moves done between your initial update of the "
"quantity and now, the difference of quantity is not consistent "
"anymore.</strong>"
msgstr ""
"<strong>Kvůli některým skladovým pohybům, které byly provedeny mezi vaší "
"původní aktualizací množství a nyní, již rozdíl v množství není "
"konzistentní.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>From</strong>"
msgstr "<strong>Od</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Location</strong>"
msgstr "<strong>Místo</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>produktové / sériové číslo</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Max qty :</strong>"
msgstr "<strong>Max. množství:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Min qty :</strong>"
msgstr "<strong>Min množství :</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>On hand Quantity</strong>"
msgstr "<strong>Množství po ruce</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order:</strong>"
msgstr "<strong>Příkaz:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Ordered</strong>"
msgstr "<strong>Objednáno</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Package Type:</strong>"
msgstr "<strong>Typ balení:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Package</strong>"
msgstr "<strong>Balení</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>Čárový kód produktu</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr "<strong>Produkt</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr "<strong>Množství</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date:</strong>"
msgstr "<strong>Plánované datum:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Shipping Date:</strong>"
msgstr "<strong>Datum odeslání:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Signature</strong>"
msgstr "<strong>Podpis</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Status:</strong>"
msgstr "<strong>Stav:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid "<strong>The initial demand has been updated.</strong>"
msgstr "<strong>Počáteční poptávka byla aktualizována.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>To</strong>"
msgstr "<strong>Komu</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "<strong>Tracked product(s):</strong>"
msgstr "<strong>Sledovaný produkt (produkty):</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid "<strong>Where do you want to send the products ?</strong>"
msgstr "<strong>Kam chcete produkty odeslat?</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? To může vést k nesrovnalostem v inventáři."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_barcode_uniq
msgid "A barcode can only be assigned to one package type !"
msgstr "Čárový kód lze přiřadit pouze jednomu typu balení!"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "A done move line should never have a reserved quantity."
msgstr "Řádek provedeného pohybu by nikdy neměl mít rezervované množství."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__detailed_type
#: model:ir.model.fields,help:stock.field_product_template__detailed_type
#: model:ir.model.fields,help:stock.field_stock_move__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Skladovatelný produkt je výrobek, pro který spravujete sklad. Musí být nainstalována aplikace Inventory. \n"
"Spotřební materiál je výrobek, pro který není spravován sklad.\n"
"Služba je nehmotný produkt, který poskytujete."

#. module: stock
#: model:res.groups,name:stock.group_warning_stock
msgid "A warning can be set on a partner (Stock)"
msgstr "Varování lze nastavit u partnera (Sklad)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__action
msgid "Action"
msgstr "Akce"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction
msgid "Action Needed"
msgstr "Vyžaduje akci"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__active
#: model:ir.model.fields,field_description:stock.field_stock_location_route__active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__active
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__active
msgid "Active"
msgstr "Aktivní"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_ids
msgid "Activities"
msgstr "Aktivity"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_decoration
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorace výjimky aktivity"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_state
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_state
msgid "Activity State"
msgstr "Stav aktivity"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_icon
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typu aktivity"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Add a Product"
msgstr "Přidat produkt"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Add a lot/serial number"
msgstr "Přidat produktové / sériové číslo"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Add a new location"
msgstr "Přidat novou lokalitu"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Add a new route"
msgstr "Přidat novou trasu"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_storage_category
msgid "Add a new storage category"
msgstr "Přidání nové kategorie úložiště"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"Add an internal note that will be printed on the Picking Operations sheet"
msgstr "Přidejte interní poznámku, která bude vytištěna na přebíracím listu"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Přidejte a přizpůsobte operace tras pro zpracování pohybů produktů ve vašem skladu (skladech): např. vyložit > kontrola kvality > zásoby pro příchozí produkty, vybrat > balení > odeslat pro odchozí produkty.\n"
"Můžete také nastavit odchozí strategie na skladových mítech, abyste mohli okamžitě odesílat příchozí produkty na konkrétní podřízená místa (např. na konkrétní zásobníky, stojany)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. You can also set putaway "
"strategies on warehouse locations in order to send incoming products into "
"specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Přidání a přizpůsobení operací trasy při zpracování přesunů produktů ve "
"vašem skladu (skladech): např. vykládka > kontrola kvality > zásoba pro "
"příchozí produkty, výběr > balení > přeprava pro odchozí produkty. Můžete "
"také nastavit strategie umístění na skladech, abyste mohli okamžitě odeslat "
"příchozí produkty do konkrétních míst  (například konkrétní koše, regály)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Add quality checks to your transfer operations"
msgstr "Přidejte ke svým operacím kontrolu kvality"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr "Doplňující informace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "Dodatečné informace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__partner_id
msgid "Address"
msgstr "Adresa"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__partner_address_id
msgid "Address where goods should be delivered. Optional."
msgstr "Adresa, kam má být zboží dodáno. Volitelný."

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Administrator"
msgstr "Administrátor"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Advanced Scheduling"
msgstr "Pokročilé plánování"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_order
msgid "Advanced: Apply Procurement Rules"
msgstr "Pokročilé: Použijte pravidla pro veřejné zakázky"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "Vše"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
msgid "All Transfers"
msgstr "Všechny přesuny"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__one
msgid "All at once"
msgstr "Vše najednou"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"All our contractual relations will be governed exclusively by United States "
"law."
msgstr ""
"Veškeré naše smluvní vztahy se budou řídit výhradně zákony Spojených států."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__returned_move_ids
msgid "All returned moves"
msgstr "Všechny zpětné přesuny"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Allocation"
msgstr "Přidělení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__allow_new_product
msgid "Allow New Product"
msgstr "Povolit nový produkt"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__mixed
msgid "Allow mixed products"
msgstr "Povolení smíšených produktů"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__allowed_location_ids
msgid "Allowed Location"
msgstr "Povolené umístění"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Annual Inventory Day and Month"
msgstr "Den a měsíc roční inventury"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_month
msgid "Annual Inventory Month"
msgstr "Měsíc roční inventury"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_month
msgid ""
"Annual inventory month for products not in a location with a cyclic "
"inventory date. Set to no month if no automatic annual inventory."
msgstr ""
"Měsíc roční inventury pro produkty, které nejsou v místě s datem cyklické "
"inventury. Nastavte na žádný měsíc, pokud se neprovádí automatická roční "
"inventura."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Applicability"
msgstr "Použitelnost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr "Použitelný na"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__packaging_selectable
msgid "Applicable on Packaging"
msgstr "Platí pro obaly"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_selectable
msgid "Applicable on Product"
msgstr "Použitelný na produkt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_categ_selectable
msgid "Applicable on Product Category"
msgstr "Použitelný na kategorii produktů"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_selectable
msgid "Applicable on Warehouse"
msgstr "Použitelný na sklad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Apply"
msgstr "Použít"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__route_ids
msgid ""
"Apply specific route(s) for the replenishment instead of product's default "
"routes."
msgstr ""
"Místo výchozí trasy produktu použijte specifické cesty(u) pro doplnění."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__4
msgid "April"
msgstr "Duben"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Archived"
msgstr "Archivováno"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__direct
msgid "As soon as possible"
msgstr "Co nejdříve"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_reception.js:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Assign"
msgstr "Přiřadit"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Assign All"
msgstr "Přiřadit vše"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__owner_id
msgid "Assign Owner"
msgstr "Přidělený vlastník"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Assign Serial Numbers"
msgstr "Přiřazení sériových čísel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr "Přiřazené pohyby"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__user_id
msgid "Assigned To"
msgstr "Přiřazeno k"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__at_confirm
msgid "At Confirmation"
msgstr "Při potvrzení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_attachment_count
msgid "Attachment Count"
msgstr "Počet příloh"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Attributes"
msgstr "Vlastnosti"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__8
msgid "August"
msgstr "Srpen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__auto
msgid "Auto"
msgstr "Automaticky"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Automate Orders"
msgstr "Automatizovat objednávky"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__auto
msgid "Automatic Move"
msgstr "Automatický přesun"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__transparent
msgid "Automatic No Step Added"
msgstr "Automaticky nebyl přidán žádný krok"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Automatically open reception report when a receipt is validated."
msgstr "Automaticky otevřít výkaz o příjmu, když je účtenka ověřena."

#. module: stock
#. openerp-web
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__assigned
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__available
#, python-format
msgid "Available"
msgstr "Dostupný"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Available Products"
msgstr "Dostupné produkty"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__available_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Available Quantity"
msgstr "Dostupné množství"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Available quantity should be set to zero before changing type"
msgstr "Dostupné množství by mělo být před změnou typu nastaveno na nulu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_id
msgid "Back Order of"
msgstr "Externí objednávka"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Back Orders"
msgstr "Zpětné obědnávky"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "Potvrzení zpětné objednávky"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation_line
msgid "Backorder Confirmation Line"
msgstr "Řádek pro potvrzení zpětné objednávky"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__backorder_confirmation_line_ids
msgid "Backorder Confirmation Lines"
msgstr "Řádky pro potvrzení zpětné objednávky"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr "Vytvoření zpětné objednávky"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr "Předobjednávka"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__barcode
#: model:ir.model.fields,field_description:stock.field_stock_package_type__barcode
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Barcode"
msgstr "Čárový kód"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "Nomenklatury čárových kódů"

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "Barcode Rule"
msgstr "Pravidlo čárového kódu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr "Čtečka čárových kódů"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_picking_batch
msgid "Batch Transfers"
msgstr "Hromadné přesuny"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__by_date
msgid "Before scheduled date"
msgstr "Před plánovaným datem"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Below text serves as a suggestion and doesn’t engage Odoo S.A. "
"responsibility."
msgstr ""
"Níže uvedený text slouží jako návrh a nezavazuje společnost Odoo S.A. k "
"zodpovědnosti."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__block
msgid "Blocking Message"
msgstr "Blokující zpráva"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__quant_ids
msgid "Bulk Content"
msgstr "Celkový obsah"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__lot
msgid "By Lots"
msgstr "Podle šarže"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__serial
msgid "By Unique Serial Number"
msgstr "Podle jedinečného seriového čísla"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""
"Ve výchozím nastavení bude systém vycházet ze zásoby v místě zdroje a "
"pasivně čekat na dostupnost. Druhá možnost vám umožní přímo vytvořit nákup "
"na zdrojovém místě (a tím ignorovat jeho aktuální zásobu), abyste získali "
"produkty. Pokud chceme provést řetězové pohyby a nechat ten, aby čekal na "
"předchozí, měla by být zvolena druhá možnost."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr "Odškrtnutím aktivního pole, můžete skrýt umístění bez jeho smazání."

#. module: stock
#: model:product.product,name:stock.product_cable_management_box
#: model:product.template,name:stock.product_cable_management_box_product_template
msgid "Cable Management Box"
msgstr "Box pro správu kabelů"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "Zobrazení kalendáře"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any customer or supplier location."
msgstr "Nelze nalézt umístění zákazníka či dodavatele."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any generic route %s."
msgstr "Nelze najít žádnou obecnou trasu %s."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Cancel"
msgstr "Zrušit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_cancel
msgid "Cancel Next Move"
msgstr "Zrušit další krok"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__cancel
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Cancelled"
msgstr "Zrušeno"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Cancelled Moves"
msgstr "Zrušené přesuny"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"Cannot set the done quantity from this stock move, work directly with the "
"move lines."
msgstr ""
"Nelze nastavit hotové množství z tohoto pohybu ve skladu, pracujte přímo s "
"trasami pohybu."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__capacity_ids
msgid "Capacity"
msgstr "Kapacita"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Package"
msgstr "Kapacita podle balení"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Product"
msgstr "Kapacita podle produktu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Categorize your locations for smarter putaway rules"
msgstr "Kategorizace míst pro chytřejší pravidla odkládání"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Category"
msgstr "Kategorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_from_categ_ids
msgid "Category Routes"
msgstr "Kategorie tras"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can My "
"Company (Chicago) become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to My Company "
"(Chicago) in its entirety and does not include any costs relating to the "
"legislation of the country in which the client is located."
msgstr ""
"Některé země uplatňují srážku u zdroje z částky faktur v souladu se svými "
"vnitrostátními právními předpisy. Případnou srážku u zdroje odvede klient "
"finančnímu úřadu. Společnost My Company (Chicago) se v žádném případě nemůže"
" podílet na nákladech souvisejících s legislativou dané země. Částka na "
"faktuře proto bude společnosti My Company (Chicago) náležet v plné výši a "
"nezahrnuje žádné náklady související s legislativou země, ve které se klient"
" nachází."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__move_dest_exists
msgid "Chained Move Exists"
msgstr "Existuje zřetězená trasa pohybu"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_change_product_quantity
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr "Změnit množství výrobků"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__move_id
msgid "Change to a better name"
msgstr "Změnit na lepší název"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Changing %s is restricted, you can't do this operation."
msgstr "Změny %s jsou omezeny, tuto operaci nelze provést."

#. module: stock
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_orderpoint.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_production_lot.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"Changing the company of this record is forbidden at this point, you should "
"rather archive it and create a new one."
msgstr ""
"Změna společnosti tohoto záznamu je v tuto chvíli zakázána, měli byste jej "
"raději archivovat a vytvořit nový."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Changing the operation type of this record is forbidden at this point."
msgstr "Změna typu operace tohoto záznamu je v tomto okamžiku zakázána."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "Changing the product is only allowed in 'Draft' state."
msgstr "Změna produktu je povolena pouze ve stavu \"Návrh\"."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Check Availability"
msgstr "Kontrola dostupnosti"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_packages
msgid "Check the existence of destination packages on move lines"
msgstr "Zkontrolujte existenci cílových balíčků na trasách pohybu"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_line_exist
msgid "Check the existence of pack operation on the picking"
msgstr "Kontrola existence operace balení při vychystávání"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__return_location
msgid "Check this box to allow using this location as a return location."
msgstr ""
"Toto políčko zaškrtněte, chcete-li povolit použití tohoto umístění jako "
"místo návratu."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__scrap_location
#: model:ir.model.fields,help:stock.field_stock_move__scrapped
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr ""
"Zaškrtněte toto políčku k povolení použití tohoto umístění k odkládání "
"zmetkového/poškozeného zboží."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Choose Labels Layout"
msgstr "Zvolit rozložení štítků"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history__inventory_datetime
#: model:ir.model.fields,help:stock.field_stock_request_count__inventory_date
msgid "Choose a date to get the inventory at that date"
msgstr "Vyberte si datum, kdy chcete získat inventář k tomuto datu"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Choose destination location"
msgstr "Zvolte cílové umístění"

#. module: stock
#: model:ir.model,name:stock.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "Vyberte rozložení stránky pro tisk štítků"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Choose your date"
msgstr "Zvolte datum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Clear"
msgstr "Vymazat"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Clear Lines"
msgstr "Vymazat položky"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
#, python-format
msgid "Close"
msgstr "Zavřít"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_code
msgid "Code"
msgstr "Kód"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__color
msgid "Color"
msgstr "Barva"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__company_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_route__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__company_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__company_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Company"
msgstr "Firma"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs"
msgstr "Vypočíst náklady na dopravu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Spočítej dopravní náklady a odešli DHL"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Spočítat náklady na dopravu a expedovat s Easypost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Spočítej dopravní náklady a odešli přes FedEx"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Spočítat dopravní náklady a odeslat přes UPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Spočítej dopravní náklady a odešli přes USPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Spočítej dopravní náklady a odešli přes bpost"

#. module: stock
#: model:ir.model,name:stock.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Configuration"
msgstr "Konfigurace"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Confirm"
msgstr "Potvrdit"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__confirmed
msgid "Confirmed"
msgstr "Potvrzeno"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_conflict
msgid "Conflict in Inventory"
msgstr "Konflikt při inventarizaci"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Conflict in Inventory Adjustment"
msgstr "Konflikt při úpravě zásob"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_to_fix_ids
msgid "Conflicts"
msgstr "Konflikty"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr "Zásilka"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__consume_line_ids
msgid "Consume Line"
msgstr "Řádek spotřeby"

#. module: stock
#: model:ir.model,name:stock.model_res_partner
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Contact"
msgstr "Kontakt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_ids
msgid "Contains"
msgstr "Obsahuje"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "Obsah"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Continue"
msgstr "Pokračovat"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_scrap__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Převod mezi měrnými jednotkami lze uskutečnit jen když patří do stejné "
"kategorie. Převod bude proveden na základě vzájemných poměrů."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posx
msgid "Corridor (X)"
msgstr "Chodba (X)"

#. module: stock
#: code:addons/stock/wizard/stock_immediate_transfer.py:0
#, python-format
msgid ""
"Could not reserve all requested products. Please use the 'Mark as Todo' "
"button to handle the reservation manually."
msgstr ""
"Nelze rezervovat všechny požadované produkty. Pro ruční zpracování rezervace"
" použijte tlačítko \"Označit ke zpracování\"."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__set_count
msgid "Count"
msgstr "Počet"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking
msgid "Count Picking"
msgstr "Počet vyskladnění"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_backorders
msgid "Count Picking Backorders"
msgstr "Počet naskladnění doobjednávek"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_draft
msgid "Count Picking Draft"
msgstr "Návrh počtu vyskladnění"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_late
msgid "Count Picking Late"
msgstr "Počet opožděných vyskladnění"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_ready
msgid "Count Picking Ready"
msgstr "Počet připravených vyskladnění"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_waiting
msgid "Count Picking Waiting"
msgstr "Počet čekajících vyskladnění"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_inventory
msgid "Count Sheet"
msgstr "Počet listů"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Counted Quantity"
msgstr "Započítané množství"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Counterpart Locations"
msgstr "Lokality protistrany"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr "Vytvořit zpětnou objednávku"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Create Backorder?"
msgstr "Vytvořit zpětnou objednávku?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_create_lots
msgid "Create New Lots/Serial Numbers"
msgstr "Vytvořit nová produktová / seriová čísla"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder if you expect to process the remaining\n"
"                        products later. Do not create a backorder if you will not\n"
"                        process the remaining products."
msgstr ""
"Pokud čekáte, že zbývající produkty zpracujete později, vytvořte zpětnou "
"objednávku. Pokud nezpracujete zbývající produkty, nevytvářejte novou  "
"zpětnou objednávku."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Create a new operation type"
msgstr "Vytvořit nový typ operace"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid "Create a new package"
msgstr "Vytvořit nový balíček"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "Vytváření přizpůsobitelných pracovních listů pro kontroly kvality"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid ""
"Create new putaway rules to dispatch automatically specific products to "
"their appropriate destination location upon receptions."
msgstr ""
"Vytvořte nová pravidla pro okamžité odesílání, abyste na recepcích "
"automaticky odesílali konkrétní produkty do jejich příslušného cílového "
"umístění."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__create_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_date
#: model:ir.model.fields,field_description:stock.field_product_removal__create_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_date
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__create_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"Creating a new warehouse will automatically activate the Storage Locations "
"setting"
msgstr ""
"Vytvoření nového skladu automaticky aktivuje nastavení Umístění úložiště"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation Date"
msgstr "Datum vytvoření"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date
msgid "Creation Date, usually the time of the order"
msgstr "Datum vytvoření, obvykle čas objednávky"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Cross-Dock"
msgstr "Mezi-překládka"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__crossdock_route_id
msgid "Crossdock Route"
msgstr "Trasa mezipřekládky"

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr "Současný sklad"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Aktuální množství produktů.\n"
"V kontextu s jedním umístěním zásob, toto zahrnuje i zboží uložené na tomto místě, nebo kterémkoliv z jeho potomků.\n"
"V kontextu s jedním skladem, toto zahrnuje i zboží uložené v sortimentu tohoto skladu, nebo některém z jeho potomků.\n"
"Uložené v Umístění zásob  Skladu tohoto Obchodu, nebo v některém z jeho potomků.\n"
"V opačném případě, toto zahrnuje zboží uložené v kterémkoliv Umístění zásob typu \"interní\"."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__picking_quantity__custom
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__custom
msgid "Custom"
msgstr "Vlastní"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer"
msgstr "Zákazník"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template__sale_delay
msgid "Customer Lead Time"
msgstr "Čekací lhůta zákazníka"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_customer
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__customer
msgid "Customer Location"
msgstr "Umístění zákazníka"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr "Umístění zákazníků"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Cyclic Inventory"
msgstr "Cyklický inventář"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL Express konektor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__date_done
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Date"
msgstr "Datum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Processing"
msgstr "Zpracování data"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date_deadline
#: model:ir.model.fields,help:stock.field_stock_picking__date_deadline
msgid "Date Promise to the customer on the top level document (SO/PO)"
msgstr "Datum slibu zákazníkovi v dokumentu nejvyšší úrovně (SO / PO)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Scheduled"
msgstr "Datum naplánováno"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__date_planned
msgid "Date at which the replenishment should take place."
msgstr "Datum, kdy by mělo dojít k doplnění."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date_done
msgid "Date at which the transfer has been processed or cancelled."
msgstr "Datum, kdy byl převod zpracován nebo zrušen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__next_inventory_date
msgid "Date for next planned inventory based on cyclic schedule."
msgstr "Datum příští plánované inventury na základě cyklického plánu."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_done
msgid "Date of Transfer"
msgstr "Datum přesunu"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__last_inventory_date
msgid "Date of the last inventory at this location."
msgstr "Datum posledního inventáře na tomto místě."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reservation_date
msgid "Date to Reserve"
msgstr "Datum rezervace"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Day and month that annual inventory counts should occur."
msgstr "Den a měsíc, kdy by mělo dojít k roční inventuře."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_day
msgid "Day of the month"
msgstr "Den měsíce"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_day
msgid ""
"Day of the month when the annual inventory should occur. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""
"Den v měsíci, kdy má být provedena roční inventura. Pokud je nulový nebo záporný, bude místo něj vybrán první den v měsíci.\n"
"Pokud je větší než poslední den měsíce, bude místo toho vybrán poslední den měsíce."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before
msgid "Days"
msgstr "Dny"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before_priority
msgid "Days when starred"
msgstr "Dny s hvězdičkou"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_deadline
msgid "Deadline"
msgstr "Uzávěrka"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deadline exceed or/and by the scheduled"
msgstr "Termín překračuje nebo/i do plánovaného"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "Deadline updated due to delay on %s"
msgstr "Termín aktualizován z důvodu zpoždění %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__12
msgid "December"
msgstr "Prosinec"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_dest_id
msgid "Default Destination Location"
msgstr "Předvolená cílová lokace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_src_id
msgid "Default Source Location"
msgstr "Předvolená zdrojová lokace"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__reception_steps
msgid "Default incoming route to follow"
msgstr "Předvolená vstupní trasa na sledování"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__delivery_steps
msgid "Default outgoing route to follow"
msgstr "Předvolená výstupní trasa na sledování"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,help:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_uom
msgid "Default unit of measure used for all stock operations."
msgstr "Výchozí měrná jednotka používaná pro všechny akciové operace."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_stock
msgid "Default: Take From Stock"
msgstr "Předvoleno: vzít ze zásob"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__route_ids
msgid "Defaults routes through the warehouse"
msgstr "Předvolené trasy skrz sklad"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid ""
"Define a minimum stock rule so that Odoo creates automatically requests for "
"quotations or confirmed manufacturing orders to resupply your stock."
msgstr ""
"Definovat pravidlo minimálních zásob tak, aby Odoo automaticky vytvářel "
"požadavky na nabídky nebo potvrzené výrobní zakázky, aby znovu zásobil váš "
"sklad."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Define a new warehouse"
msgstr "Definovat nový sklad"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"            organization. Odoo is able to manage physical locations\n"
"            (warehouses, shelves, bin, etc), partner locations (customers,\n"
"            vendors) and virtual locations which are the counterpart of\n"
"            the stock operations like the manufacturing orders\n"
"            consumptions, inventories, etc."
msgstr ""
"Definujte umístění tak, aby odrážely strukturu a organizaci skladu. Odoo je "
"schopen spravovat fyzická místa (sklady, police, přihrádky atd.), Partnerská"
" místa (zákazníci, prodejci) a virtuální místa, která jsou protějškem "
"skladových operací, jako jsou spotřeby výrobních objednávek, zásoby atd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) where to take the products from, which lot etc. for this location. This method can be enforced at the product category level, and a fallback is made on the parent locations if none is set here.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""
"Definuje výchozí metodu používanou pro navrhování přesného umístění (police), odkud mají být produkty odebrány, ze které části atd. pro toto umístění. Tuto metodu lze vynutit na úrovni kategorie produktu a v nadřazených umístěních se provede nouzová situace, pokud zde není nastavena žádná.\n"
"\n"
"FIFO: produkty/šarže, které byly naskladněny jako první, budou přesunuty jako první.\n"
"LIFO: produkty/šarže, které byly naskladněny jako poslední, budou přesunuty jako první.\n"
"Umístění skříně: produkty/šarže nejblíže cílovému umístění budou přesunuty jako první.\n"
"FEFO: produkty/šarže s nejbližším datem odebrání budou přesunuty jako první (dostupnost této metody závisí na nastavení „Data vypršení platnosti“)."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__delay_alert_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__delay_alert_date
msgid "Delay Alert Date"
msgstr "Datum upozornění na zpoždění"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "Delay on %s"
msgstr "Prodlení na %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__ship_only
msgid "Deliver goods directly (1 step)"
msgstr "Doručení zboží přímo (1 krok)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 1 step (ship)"
msgstr "Dodání v 1 kroku (přeprava)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 2 steps (pick + ship)"
msgstr "Dodání ve 2 krocích (vyzvednutí + přeprava)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 3 steps (pick + pack + ship)"
msgstr "Dodání ve 3 krocích (vyzvedntí + zabalení + doručení)"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Delivered Qty"
msgstr "Doručené množ."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__outgoing
#: model:ir.ui.menu,name:stock.menu_delivery
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Delivery"
msgstr "Dodání"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Delivery Address"
msgstr "Doručovací adresa"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "Způsoby dodání"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.chi_picking_type_out
#: model:stock.picking.type,name:stock.picking_type_out
#, python-format
msgid "Delivery Orders"
msgstr "Příkaz dodání"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_route_id
msgid "Delivery Route"
msgstr "Směr doručení"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_delivery
msgid "Delivery Slip"
msgstr "Dodací stvrzenka"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__move_type
msgid "Delivery Type"
msgstr "Typ dodání"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__sale_delay
#: model:ir.model.fields,help:stock.field_product_template__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr ""
"Dodací lhůta, ve dnech. Je to počet dní, které byly zákazníkovi slíbeny, "
"mezi potvrzením prodejní objednávky a dodávkou."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__delivery_count
msgid "Delivery order count"
msgstr "Počet dodacích příkazů"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "Delivery orders of %s"
msgstr "Dodací příkazy %s"

#. module: stock
#: model:mail.template,name:stock.mail_template_data_delivery_confirmation
msgid "Delivery: Send by Email"
msgstr "Doručení: Poslat e-mailem"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Demand"
msgstr "Poptávka"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_packaging__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product in this packaging: whether it will be bought, manufactured, "
"replenished on order, etc."
msgstr ""
"V závislosti na nainstalovaných modulech umožňuje definovat cestu výrobku v "
"tomto balení: zda se bude nakupovat, vyrábět, doplňovat na objednávku atd."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__route_ids
#: model:ir.model.fields,help:stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"V závislosti na nainstalovaných modulech vám to umožní definovat trasu "
"produktu: zda bude zakoupen, vyroben, doplněn na objednávku atd."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__note
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Description"
msgstr "Popis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Delivery Orders"
msgstr "Popis příkazů k dodání"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Internal Transfers"
msgstr "Popis pro interní přesuny"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Receipts"
msgstr "Popis účtenek"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__description_picking
msgid "Description of Picking"
msgstr "Poznámka vyzvednutí"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingout
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingout
msgid "Description on Delivery Orders"
msgstr "Popis příkazů k dodání"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_picking
#: model:ir.model.fields,field_description:stock.field_product_template__description_picking
msgid "Description on Picking"
msgstr "Poznámka k vyzvednutí"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingin
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingin
msgid "Description on Receptions"
msgstr "Popis na příjmech"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__description_picking
msgid "Description picking"
msgstr "Poznámka vyzvednutí"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__partner_id
msgid "Destination Address "
msgstr "Cílová adresa "

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#, python-format
msgid "Destination Location"
msgstr "Cílové umístění"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Location:"
msgstr "Místo určení:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_dest_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Destination Moves"
msgstr "Cílové pohyby"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__result_package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Destination Package"
msgstr "Cílový balíček"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Package :"
msgstr "Cílový balíček:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__location_dest_id
msgid "Destination location"
msgstr "Cílové umístění"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__route_ids
msgid "Destination route"
msgstr "Cílová trasa"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Detailed Operations"
msgstr "Podrobné operace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_details_visible
msgid "Details Visible"
msgstr "Podrobnosti Viditelné"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_diff_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Difference"
msgstr "Rozdíl"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Discard"
msgstr "Zrušit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Discard and manually resolve the conflict"
msgstr "Zahodit a ručně vyřešit konflikt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_assign_serial
msgid "Display Assign Serial"
msgstr "Zobrazit přiřazení sériového čísla"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__display_complete
msgid "Display Complete"
msgstr "Zobrazit Dokončeno"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers on Delivery Slips"
msgstr "Zobrazení šarží a sériových čísel na dodacích listech"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__display_name
#: model:ir.model.fields,field_description:stock.field_product_removal__display_name
#: model:ir.model.fields,field_description:stock.field_product_replenish__display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_route__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_level__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__display_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__display_name
#: model:ir.model.fields,field_description:stock.field_stock_request_count__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: stock
#: model:res.groups,name:stock.group_auto_reception_report
msgid "Display Reception Report at Validation"
msgstr "Zobrazit zprávu o příjmu při ověření"

#. module: stock
#: model:res.groups,name:stock.group_lot_on_delivery_slip
msgid "Display Serial & Lot Number in Delivery Slips"
msgstr "Zobrazit sériové číslo a číslo šarže v dodacích listech"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_tree_view_picking
msgid "Display package content"
msgstr "Zobrazit obsah balíku"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__disposable
msgid "Disposable Box"
msgstr "Jednorázová krabice"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "Do you confirm you want to scrap"
msgstr "Potvrzujete, že chcete vyřadit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__qty_done
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_done
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__done
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Done"
msgstr "Hotovo"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__draft
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft"
msgstr "Návrh"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr "Koncept pohybů"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr " Easypost konektor"

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Edit Product"
msgstr "Upravit produkt"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Editing quantities in an Inventory Adjustment location is forbidden,those "
"locations are used as counterpart when correcting the quantities."
msgstr ""
"Úpravy veličin v místě úpravy inventáře jsou zakázány, tato místa se "
"používají jako protějšek při opravách veličin."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Effective Date"
msgstr "Datum účinnosti"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Confirmation"
msgstr "potvrzení e-mailem"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_move_email_validation
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_move_email_validation
msgid "Email Confirmation picking"
msgstr "Potvrzení výběru e-mailem"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Template"
msgstr "Šablona e-mailu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_mail_confirmation_template_id
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_mail_confirmation_template_id
msgid "Email Template confirmation picking"
msgstr "Výběr potvrzení šablony e-mailu"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__stock_mail_confirmation_template_id
#: model:ir.model.fields,help:stock.field_res_config_settings__stock_mail_confirmation_template_id
msgid "Email sent to the customer once the order is done."
msgstr "E-mail odeslaný zákazníkovi po dokončení objednávky."

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid ""
"Enjoy a quick-paced experience with the Odoo barcode app. It is blazing fast"
" and works even without a stable internet connection. It supports all flows:"
" inventory adjustments, batch picking, moving lots or pallets, low inventory"
" checks, etc. Go to the \"Apps\" menu to activate the barcode interface."
msgstr ""
"Vychutnejte si rychlý zážitek s aplikací čárových kódů Odoo. Je bleskurychlá"
" a funguje i bez stabilního připojení k internetu. Podporuje všechny toky: "
"úpravy zásob, vychystávání dávek, přesuny šarží nebo palet, kontroly nízkých"
" zásob atd. Přejděte do nabídky \"Aplikace\" a aktivujte rozhraní čárového "
"kódu."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__tracking
#: model:ir.model.fields,help:stock.field_product_template__tracking
#: model:ir.model.fields,help:stock.field_stock_move__has_tracking
#: model:ir.model.fields,help:stock.field_stock_move_line__tracking
#: model:ir.model.fields,help:stock.field_stock_quant__tracking
#: model:ir.model.fields,help:stock.field_stock_scrap__tracking
#: model:ir.model.fields,help:stock.field_stock_track_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Zajistěte sledovatelnost skladovatelného produktu ve vašem skladu."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#, python-format
msgid "Error"
msgstr "Chyba"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"            location to another one.  For instance, if you receive products\n"
"            from a vendor, Odoo will move products from the Vendor\n"
"            location to the Stock location. Each report can be performed on\n"
"            physical, partner or virtual locations."
msgstr ""
"Každá skladová operace v Odoo přesouvá produkty z jednoho místa na druhé. "
"Pokud například obdržíte produkty od dodavatele, Odoo bude přesouvat "
"produkty z místa dodavatele do místa skladu. Každá sestava může být "
"prováděna na fyzických, partnerských nebo virtuálních místech."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s) occurred on the picking"
msgstr "Při vyskladnění došlo k výjimce (vyjímkám)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s):"
msgstr "Výjimka(y):"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"Existing Serial Numbers (%s). Please correct the serial numbers encoded."
msgstr ""
"Stávající sériová čísla (%s). Opravte prosím zakódovaná sériová čísla."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "Existing Serial numbers. Please correct the serial numbers encoded:"
msgstr "Stávající sériová čísla. Opravte prosím zakódovaná sériová čísla:"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#, python-format
msgid "Exp"
msgstr "Exp"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Exp %s"
msgstr "Exp %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__expected
msgid "Expected"
msgstr "Očekávaný"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Expected Delivery:"
msgstr "Očekávané doručení:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr "Data expirace"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr "Externí poznámka..."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal__method
msgid "FIFO, LIFO..."
msgstr "FIFO, LIFO..."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__2
msgid "February"
msgstr "Únor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "FedEx konektor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__filtered_location
msgid "Filtered Location"
msgstr "Filtrované umístění"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Filters"
msgstr "Filtry"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__next_serial_number
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial
msgid "First SN"
msgstr "První SN"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__fixed
msgid "Fixed"
msgstr "Pevné"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_id
msgid "Fixed Procurement Group"
msgstr "Skupina pro pevné nákupy"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#, python-format
msgid "Fold"
msgstr "Sbalit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_follower_ids
msgid "Followers"
msgstr "Sledující"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledující (partneři)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_type_icon
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Skvělá ikona písma, např. fa-úkoly"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__removal_strategy_id
msgid "Force Removal Strategy"
msgstr "Vynutit strategii odběru"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_forecast
msgid "Forecast"
msgstr "Předpověď"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_availability
msgid "Forecast Availability"
msgstr "Předpověď dostupnosti"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "Forecast Description"
msgstr "Popis Prognózy"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Prognóza množství (vypočteno jako množství po ruce - odchozí + příchozí)\n"
"V souvislosti s jediným umístěním na skladě to zahrnuje zboží uložené v tomto umístění nebo jakékoli jeho děti.\n"
"V souvislosti s jediným skladem se jedná o zboží uložené na skladovém místě tohoto skladu nebo kterékoli z jeho podřazených skladů.\n"
"V opačném případě to zahrnuje zboží uložené na jakémkoli místě skladu s „interním“ typem."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Předpokládané množství (vypočtené jako množství na skladě - rezervované množství)\n"
"V kontextu s jedním skladovým místem zahrnuje zboží uložené v tomto místě nebo v jakémkoli z jeho podřízených míst.\n"
"V kontextu s jedním skladem zahrnuje zboží uložené ve skladovém umístění tohoto skladu nebo v jakémkoli jeho podřízeném umístění.\n"
"V ostatních případech zahrnuje zboží uložené v libovolném Skladovém umístění s typem \"interní\"."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "Forecasted"
msgstr "Předpokládané"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Forecasted Date"
msgstr "Předpokládané datum"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__out
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Deliveries"
msgstr "Předpokládané dodávky"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_expected_date
msgid "Forecasted Expected date"
msgstr "Předpokládané očekávané datum"

#. module: stock
#: model:ir.actions.act_window,name:stock.report_stock_quantity_action
#: model:ir.actions.act_window,name:stock.report_stock_quantity_action_product
#: model:ir.ui.menu,name:stock.menu_forecast_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Forecasted Inventory"
msgstr "Prognóza zásob"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model:ir.model.fields,field_description:stock.field_product_product__virtual_available
#: model:ir.model.fields,field_description:stock.field_product_template__virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move__availability
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
#, python-format
msgid "Forecasted Quantity"
msgstr "Očekávané množství"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__in
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Receipts"
msgstr "Předpokládané účetní doklady"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model:ir.actions.client,name:stock.stock_replenishment_product_product_action
#: model:ir.actions.report,name:stock.stock_replenishment_report_product_product_action
#: model:ir.actions.report,name:stock.stock_replenishment_report_product_template_action
#, python-format
msgid "Forecasted Report"
msgstr "Předpokládané výkazy"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__forecast
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Stock"
msgstr "Předpokládané zásoby"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__forecast_weight
msgid "Forecasted Weight"
msgstr "Předpokládaná hmotnost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Forecasted with Pending"
msgstr "Předpokládané s čekajícími"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "Forecasted<br/>+ Pending"
msgstr "Předpokládáno<br/>+ Čekající"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__print_format
msgid "Format"
msgstr "Formát"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Free Stock"
msgstr "Volné zásoby"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__free_qty
msgid "Free To Use Quantity "
msgstr "Zdarma k použití množství "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "From"
msgstr "Od"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__owner_id
msgid "From Owner"
msgstr "Od vlastníka"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_reserved_availability
msgid "From Supplier"
msgstr "Od dodavatele"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__complete_name
msgid "Full Location Name"
msgstr "Úplný název místa"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Future Activities"
msgstr "Budoucí činnosti"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Deliveries"
msgstr "Budoucí dodávky"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future P&L"
msgstr "Budoucí P&L"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Productions"
msgstr "Budoucí výroba"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Receipts"
msgstr "Budoucí účtenky"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get a full traceability from vendors to customers"
msgstr "Získejte plnou sledovatelnost od dodavatelů k zákazníkům"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get informative or blocking warnings on partners"
msgstr "Získání informativních nebo blokačních upozornění o partnerech"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_putaway_rule__sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top"
" of the list."
msgstr ""
"Přidej více specializovanou kategorii, vyšší prioritu, aby zůstaly v horní "
"části seznamu."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__sequence
msgid "Gives the sequence of this line when displaying the warehouses."
msgstr "Udává pořadí tohoto řádku při zobrazení skladů."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "Seskupit podle"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Group by..."
msgstr "Seskupit podle..."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_picking_wave
msgid "Group your move operations in wave transfer to process them together"
msgstr ""
"Seskupte své operace pohybu ve skupinovém přenosu a zpracujte je společně"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_message
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__has_message
#: model:ir.model.fields,field_description:stock.field_stock_scrap__has_message
msgid "Has Message"
msgstr "Má zprávu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_exist
msgid "Has Pack Operations"
msgstr "Má balící operace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_packages
msgid "Has Packages"
msgstr "Má balíky"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_scrap_move
msgid "Has Scrap Moves"
msgstr "Má vyřazovací pohyby"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_tracking
msgid "Has Tracking"
msgstr "Má sledování"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_has_variants
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_has_variants
msgid "Has variants"
msgstr "Má varianty"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Having Category"
msgstr "S kategorií"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__height
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Height"
msgstr "Výška"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posz
msgid "Height (Z)"
msgstr "Výška (Z)"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_height
msgid "Height must be positive"
msgstr "Výška musí být kladné číslo"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Hidden until next scheduler."
msgstr "Skryto do dalšího plánovače."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__hide_picking_type
msgid "Hide Picking Type"
msgstr "Skrýt typ skladové operace"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#, python-format
msgid "History"
msgstr "Historie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_method
msgid "How products in transfers of this operation type should be reserved."
msgstr "Jak by měly být produkty v převodech tohoto typu operace rezervovány."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__id
#: model:ir.model.fields,field_description:stock.field_product_removal__id
#: model:ir.model.fields,field_description:stock.field_product_replenish__id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__id
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__id
#: model:ir.model.fields,field_description:stock.field_stock_location__id
#: model:ir.model.fields,field_description:stock.field_stock_location_route__id
#: model:ir.model.fields,field_description:stock.field_stock_move__id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__id
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__id
#: model:ir.model.fields,field_description:stock.field_stock_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_quant__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__id
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__id
#: model:ir.model.fields,field_description:stock.field_stock_request_count__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__id
#: model:ir.model.fields,field_description:stock.field_stock_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__id
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__id
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__id
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__id
msgid "ID"
msgstr "ID"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_icon
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_icon
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona označuje vyjímečnou aktivitu."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, My Company (Chicago) reserves the right to call on the "
"services of a debt recovery company. All legal expenses will be payable by "
"the client."
msgstr ""
"Pokud je platba stále neuhrazena více než šedesát (60) dní po datu "
"splatnosti, společnost My Company (Chicago) si vyhrazuje právo využít služeb"
" společnosti pro vymáhání pohledávek. Veškeré právní výlohy hradí klient."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__same
msgid "If all products are same"
msgstr "Pokud jsou všechny produkty stejné"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,help:stock.field_stock_picking__message_unread
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_needaction
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_unread
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction
#: model:ir.model.fields,help:stock.field_stock_scrap__message_unread
msgid "If checked, new messages require your attention."
msgstr "Pokud je zaškrtnuto, nové zprávy vyžadují vaši pozornost."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_error
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Pokud je zaškrtnuto, některé zprávy mají chybu při doručení."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__propagate_cancel
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr ""
"Když dáme zkontrolovat, pokud je tento krok zrušen tak navazující kroky "
"budou zrušeny také"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__result_package_id
msgid "If set, the operations are packed into this package"
msgstr "Pokud je nastaveno, operace jsou zabaleny do tohoto balíčku"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr ""
"Pokud je pole aktivní nastaveno na Nepravda, umožní vám skrýt úrovně pro "
"doplnění bez jejich odebrání."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr ""
"Pokud je pole nastaveno na Nepravda, tak umožní skrýt směrování bez jeho "
"odebrání."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__empty
msgid "If the location is empty"
msgstr "Pokud je umístění prázdné"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"If the picking is unlocked you can edit initial demand (for a draft picking)"
" or done quantities (for a done picking)."
msgstr ""
"Pokud je výběr odemčen, můžete upravit počáteční požadavek (pro návrh "
"výběru) nebo hotové množství (pro provedený výběr)."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_reserved
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_reserved
msgid ""
"If this checkbox is ticked, Odoo will automatically pre-fill the detailed "
"operations with the corresponding products, locations and lot/serial "
"numbers."
msgstr ""
"If this checkbox is ticked, Odoo will automatically pre-fill the detailed "
"operations with the corresponding products, locations and lot/serial "
"numbers."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__print_label
msgid "If this checkbox is ticked, label will be print in this operation."
msgstr ""
"Pokud je toto políčko zaškrtnuto, bude se při této operaci tisknout štítek."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_operations
msgid ""
"If this checkbox is ticked, the pickings lines will represent detailed stock"
" operations. If not, the picking lines will represent an aggregate of "
"detailed stock operations."
msgstr ""
"Pokud je toto zaškrtávací políčko zaškrtnuto, řádky pro vyskladnění budou "
"zobrazovat podrobné operace skladu. Pokud tomu tak není, řádky vyskladnění "
"budou představovat jen souhrn podrobných skladových operací."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Lots/Serial "
"Numbers, so you can provide them in a text field. "
msgstr ""
"Pokud je toto políčko zaškrtnuto, bude předpokládat, že budete chtít "
"vytvořit nová produktová čísla / sériová čísla, takže je můžete zadat v "
"textovém poli."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""
"Pokud je tato volba zaškrtnuta, budete mít možnost zvolit si šíslo šarže /  "
"Sériová čísla. Můžete také rozhodnout, že v tomto typu operace nebudete "
"dávat šarži. Znamená to, že vytvoří zásoby bez šarže nebo neudělí omezení na"
" přijatou šarži."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr ""
"Pokud byla dodávka rozdělena, potom toto pole odkazuje na dodávku, která "
"obsahuje již zpracovanou část."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_entire_packs
msgid "If ticked, you will be able to select entire packages to move"
msgstr ""
"Pokud je zaškrtnuto, budete moci vybrat celé balíčky, které chcete přesunout"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__active
msgid "If unchecked, it will allow you to hide the rule without removing it."
msgstr "Pokud není zaškrtnuto, umožní vám skrýt pravidlo bez jeho odebrání."

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__backorder_confirmation_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__immediate_transfer_id
#: model:ir.model.fields,field_description:stock.field_stock_move__from_immediate_transfer
#: model:ir.model.fields,field_description:stock.field_stock_picking__immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Immediate Transfer"
msgstr "Okamžitý převod"

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer_line
msgid "Immediate Transfer Line"
msgstr "Položka okamžitého přesunu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__immediate_transfer_line_ids
msgid "Immediate Transfer Lines"
msgstr "Položky okamžitého přesunu"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Immediate Transfer?"
msgstr "Okamžitý přesun?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "Immediate transfer?"
msgstr "Okamžitý přesun?"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Import"
msgstr "Import"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__in_type_id
msgid "In Type"
msgstr "Typ vstupu"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"In order for it to be admissible, My Company (Chicago) must be notified of "
"any claim by means of a letter sent by recorded delivery to its registered "
"office within 8 days of the delivery of the goods or the provision of the "
"services."
msgstr ""
"Aby to bylo přípustné, moje společnost (Chicago) o případné reklamaci musí "
"být informována doporučeným dopisem na adresu sídla společnosti do 8 dnů od "
"doručení zboží nebo poskytnutí služby."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template__incoming_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Incoming"
msgstr "Příchozí"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__in_date
msgid "Incoming Date"
msgstr "Příchozí datum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Incoming Draft Transfer"
msgstr "Převod příchozích konceptů"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__incoming_move_line_ids
msgid "Incoming Move Line"
msgstr "Příchozí pohyb položek"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_steps
msgid "Incoming Shipments"
msgstr "Příchozí zásilky"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_diff_quantity
msgid ""
"Indicates the gap between the product's theoretical quantity and its counted"
" quantity."
msgstr ""
"Udává rozdíl mezi teoretickým množstvím výrobku a jeho počítaným množstvím."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Initial Demand"
msgstr "Počáteční poptávka"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Input"
msgstr "Vstup"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_input_stock_loc_id
msgid "Input Location"
msgstr "Umístění vstupu"

#. module: stock
#: code:addons/stock/models/res_company.py:0
#, python-format
msgid "Inter-warehouse transit"
msgstr "Meziskladový tranzit"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal"
msgstr "Vnitřní"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__internal
msgid "Internal Location"
msgstr "Vnitropodnikové umístění"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr "Vnitropodniková umístění"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__ref
msgid "Internal Reference"
msgstr "Interní reference"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__internal
msgid "Internal Transfer"
msgstr "Interní převod"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.picking_type_internal
#, python-format
msgid "Internal Transfers"
msgstr "Vnitřní převody"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__internal_transit_location_id
msgid "Internal Transit Location"
msgstr "Místo pro vnitrofiremní transport"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__int_type_id
msgid "Internal Type"
msgstr "Vnitřní typ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_internal_location_ids
msgid "Internal locations amoung descendants"
msgstr "Vnitřní umístění mezi potomky"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__ref
msgid ""
"Internal reference number in case it differs from the manufacturer's "
"lot/serial number"
msgstr ""
"Interní referenční číslo v případě, že se liší od výrobního sériového čísla"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain left operand %s"
msgstr "Neplatný levý operand domény %s"

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain operator %s"
msgstr "Neplatný operátor domény %s"

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain right operand '%s'. It must be of type Integer/Float"
msgstr "Neplatný pravý operand domény '%s'. Musí být typu Integer/Float"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"Invalid rule's configuration, the following rule causes an endless loop: %s"
msgstr ""
"Neplatná konfigurace pravidla, následující pravidlo způsobuje nekonečnou "
"smyčku: %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_auto_apply
msgid "Inventoried Quantity"
msgstr "Skladované množství"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_inventory_tree
#: model:ir.actions.server,name:stock.action_view_quants
#: model:ir.model.fields,field_description:stock.field_stock_move__is_inventory
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_inventory
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr "Inventář"

#. module: stock
#: code:addons/stock/wizard/stock_inventory_adjustment_name.py:0
#, python-format
msgid "Inventory Adjustment"
msgstr "Úprava inventáře"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__inventory_adjustment_name
msgid "Inventory Adjustment Name"
msgstr "Název inventury"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_inventory_adjustement_name
#: model:ir.model,name:stock.model_stock_inventory_adjustment_name
msgid "Inventory Adjustment Reference / Reason"
msgstr "Reference / důvod inventury"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_warning
msgid "Inventory Adjustment Warning"
msgstr "Inventurní varování"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.ui.menu,name:stock.menu_action_inventory_tree
#, python-format
msgid "Inventory Adjustments"
msgstr "Úpravy inventáře"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Inventory Count Sheet"
msgstr "List inventury"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__inventory_date
msgid "Inventory Date"
msgstr "Datum inventáře"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__cyclic_inventory_frequency
msgid "Inventory Frequency (Days)"
msgstr "Frekvence inventury (Dny)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_inventory
msgid "Inventory Location"
msgstr "Umístění inventáře"

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr "Umístění (množ.) inventáře"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__inventory
msgid "Inventory Loss"
msgstr "Ztráty zásob"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Inventory On Hand"
msgstr "Inventář po ruce"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
msgid "Inventory Overview"
msgstr "Přehled skladu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_set
msgid "Inventory Quantity Set"
msgstr "Nastavení množství zásob"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid "Inventory Reference / Reason"
msgstr "Reference / důvod inventáře"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_valuation
msgid "Inventory Report"
msgstr "Výkaz inventáře"

#. module: stock
#: model:ir.model,name:stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "Trasy inventáře"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
msgid "Inventory Valuation"
msgstr "Ocenění zásob"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/inventory_report.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__inventory_datetime
#, python-format
msgid "Inventory at Date"
msgstr "Zásoby ke dni"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_is_follower
msgid "Is Follower"
msgstr "Je sledující"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_fresh_package
msgid "Is Fresh Package"
msgstr "Je čerstvé balení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_locked
msgid "Is Locked"
msgstr "Zamčeno"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_signed
msgid "Is Signed"
msgstr "Je podepsáno"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__return_location
msgid "Is a Return Location?"
msgstr "Je místo návratu?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__scrap_location
msgid "Is a Scrap Location?"
msgstr "Je to místo na zmetky?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_initial_demand_editable
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_initial_demand_editable
msgid "Is initial demand editable"
msgstr "Je počáteční poptávka upravitelná"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_deadline_issue
msgid "Is late"
msgstr "Má zpoždění"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_deadline_issue
msgid "Is late or will be late depending on the deadline and scheduled date"
msgstr "má nebo bude mít zpoždění v závislosti na termínu a plánovaném datu."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_quantity_done_editable
msgid "Is quantity done editable"
msgstr "Je provedeno upravitelné množství"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"It is not allowed to import reserved quantity, you have to use the quantity "
"directly."
msgstr ""
"Není povoleno importovat rezervované množství, musíte použít množství přímo."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"It is not possible to reserve more products of %s than you have in stock."
msgstr "Není možné rezervovat více produktů %s, než máte na skladě."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"It is not possible to unreserve more products of %s than you have in stock."
msgstr "Není možné zrušit rezervaci více produktům %s, než máte na skladě."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr "Určuje, zda má být zboží dodáno po částech nebo naráz"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__json_popover
msgid "JSON data for the popover widget"
msgstr "Data JSON pro widget popover"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__1
msgid "January"
msgstr "Leden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_lead_days
msgid "Json Lead Days"
msgstr "Dodací dny Json"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_replenishment_history
msgid "Json Replenishment History"
msgstr "Historie doplňování Json"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__7
msgid "July"
msgstr "Červen"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__6
msgid "June"
msgstr "Červenec"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Counted Quantity"
msgstr "Zachovat započítané množství"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Difference"
msgstr "Zachovat rozdíl"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Counted Quantity</strong> (the Difference will be updated)"
msgstr ""
"Zachovat <strong>započítané množství</strong> (rozdíl bude aktualizován)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Difference</strong> (the Counted Quantity will be updated "
"to reflect the same difference as when you counted)"
msgstr ""
"Ponechat <strong>rozdíl</strong> (spočítané množství bude aktualizováno tak,"
" aby odráželo stejný rozdíl jako při počítání)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 12 Months"
msgstr "Posledních 12 měsíců"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 3 Months"
msgstr "Poslední 3 měsíce"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 30 Days"
msgstr "Posledních 30 dní"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__last_delivery_partner_id
msgid "Last Delivery Partner"
msgstr "Poslední partner dodání"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__last_inventory_date
msgid "Last Effective Inventory"
msgstr "Poslední platná inventura"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group____last_update
#: model:ir.model.fields,field_description:stock.field_product_removal____last_update
#: model:ir.model.fields,field_description:stock.field_product_replenish____last_update
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity____last_update
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial____last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation____last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty____last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer____last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning____last_update
#: model:ir.model.fields,field_description:stock.field_stock_location____last_update
#: model:ir.model.fields,field_description:stock.field_stock_location_route____last_update
#: model:ir.model.fields,field_description:stock.field_stock_move____last_update
#: model:ir.model.fields,field_description:stock.field_stock_move_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_destination____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_level____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_type____last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking____last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking_type____last_update
#: model:ir.model.fields,field_description:stock.field_stock_production_lot____last_update
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant_package____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history____last_update
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info____last_update
#: model:ir.model.fields,field_description:stock.field_stock_request_count____last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking____last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_rule____last_update
#: model:ir.model.fields,field_description:stock.field_stock_rules_report____last_update
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute____last_update
#: model:ir.model.fields,field_description:stock.field_stock_scrap____last_update
#: model:ir.model.fields,field_description:stock.field_stock_storage_category____last_update
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity____last_update
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report____last_update
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation____last_update
#: model:ir.model.fields,field_description:stock.field_stock_track_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__write_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_date
#: model:ir.model.fields,field_description:stock.field_product_removal__write_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_date
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__write_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__late
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late"
msgstr "Zpožděné"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Activities"
msgstr "Zpožděné činnosti"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr "Zpožděné přesuny"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__products_availability
msgid "Latest product availability status of the picking"
msgstr "Nejnovější stav dostupnosti produktu při dodání"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__lead_days_date
msgid "Lead Days Date"
msgstr "Datum dodacích dnů"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__delay
msgid "Lead Time"
msgstr "Dodací lhůta"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Lead Times"
msgstr "Dodací lhůty"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__empty
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__none
msgid "Leave Empty"
msgstr "Nechat prázdné"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__company_id
#: model:ir.model.fields,help:stock.field_stock_rule__route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr ""
"Pokud je tato trasa sdílena mezi všemi společnostmi, ponechte toto pole "
"prázdné"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Legend"
msgstr "Legenda"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__packaging_length
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Length"
msgstr "Délka"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_length
msgid "Length must be positive"
msgstr "Délka musí být kladné číslo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__length_uom_name
msgid "Length unit of measure label"
msgstr "Štítek měrné jednotky délky"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__company_id
#: model:ir.model.fields,help:stock.field_stock_quant__company_id
msgid "Let this field empty if this location is shared between companies"
msgstr ""
"Pokud je toto umístění sdíleno mezi společnostmi, zanechte toto pole volné."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Linked Moves"
msgstr "Vstažené přesuny"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of operations"
msgstr "Zobrazení seznamu operací"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__location_id
#: model:ir.model.fields,field_description:stock.field_product_template__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__location_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__location_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__location_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__location
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Location"
msgstr "Místo"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_location_barcode
msgid "Location Barcode"
msgstr "Čárový kód umístění"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__name
msgid "Location Name"
msgstr "Název místa"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__lot_stock_id
msgid "Location Stock"
msgstr "Umístění zásob"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__usage
msgid "Location Type"
msgstr "Typ umístění"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "Umístění, kde systém naskladní hotové výrobky."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: Store to"
msgstr "Lokace: Uložit do"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: When arrives to"
msgstr "Lokace: Kdy dorazí do"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.actions.act_window,name:stock.action_prod_inv_location_form
#: model:ir.actions.act_window,name:stock.action_storage_category_locations
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model_terms:ir.ui.view,arch_db:stock.report_location_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr "Umístění"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Lock"
msgstr "Zamknout"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr "Logistika"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "Dávka"

#. module: stock
#: model:ir.model,name:stock.model_stock_production_lot
#: model:ir.model.fields,field_description:stock.field_stock_scrap__lot_id
msgid "Lot/Serial"
msgstr "Šarže / seriové číslo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "Lot/Serial #"
msgstr "Šarže / Seriové číslo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Lot/Serial :"
msgstr "Produktové / Sériové číslo : "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__name
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Lot/Serial Number"
msgstr "Šarže / Seriové číslo"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_lot_label
msgid "Lot/Serial Number (PDF)"
msgstr "Produktové / Sériové číslo (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_lot_template
msgid "Lot/Serial Number (ZPL)"
msgstr "Produktové / Seriové  číslo (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_name
msgid "Lot/Serial Number Name"
msgstr "Název produktového / seriového čísla"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Lot/Serial Numbers"
msgstr "Šarže / Sériová čísla"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr "Produktová a seriová čísla"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Lots &amp; Serial numbers will appear on the delivery slip"
msgstr "Na dodacím listu budou uvedena čísla šarží a sériová čísla."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lots_visible
msgid "Lots Visible"
msgstr "Šarže viditelné"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Lots or serial numbers were not provided for tracked products"
msgstr "U sledovaných produktů nebyla uvedena čísla šarží nebo sériová čísla."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Lots/Serial Numbers"
msgstr "Šarže / Seriová čísla"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"Lots/Serial numbers help you tracking the path followed by your products.\n"
"            From their traceability report you will see the full history of their use, as well as their composition."
msgstr ""
"Čísla šarží/sériová čísla vám pomohou sledovat cestu, kterou vaše produkty urazily.\n"
"Ze zprávy o jejich sledovatelnosti uvidíte celou historii jejich použití i složení."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__mto_pull_id
msgid "MTO rule"
msgstr "Pravidlo MTO - Na objednávku"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_main_attachment_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_main_attachment_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hlavní příloha"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "Na objednávku - MTO"

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr "Spravuj různé majitele skladu"

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr "Správa produktových / sériových čísel"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage Multiple Stock Locations"
msgstr "Správa více lokací skladů"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage Multiple Warehouses"
msgstr "Správa více skladů"

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr "Spravuj balíky"

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr "Správa skladových toků Push a Pull"

#. module: stock
#: model:res.groups,name:stock.group_stock_storage_categories
msgid "Manage Storage Categories"
msgstr "Spravovat uskladňovací kategorie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage product packagings (e.g. pack of 6 bottles, box of 10 pieces)"
msgstr ""
"Spravujte obaly produktů (např. balení 6 lahví, krabička po 10 kusech)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__manual
msgid "Manual"
msgstr "Ruční"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__manual
msgid "Manual Operation"
msgstr "Manuální operace"

#. module: stock
#: code:addons/stock/wizard/product_replenish.py:0
#: code:addons/stock/wizard/product_replenish.py:0
#, python-format
msgid "Manual Replenishment"
msgstr "Ruční doplňování"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__manual
msgid "Manually"
msgstr "Ručně"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Manufacturing"
msgstr "Výrobní"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__3
msgid "March"
msgstr "Březen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr "Označit jako 'úkol'"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid "Max Quantity"
msgstr "Maximální množství"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__max_weight
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__max_weight
msgid "Max Weight"
msgstr "Maximální hmotnost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Max Weight (kg)"
msgstr "Maximální hmotnost (kg)"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_max_weight
msgid "Max Weight must be positive"
msgstr "Maximální váha musí být kladné číslo"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_positive_max_weight
msgid "Max weight should be a positive number."
msgstr "Maximální hmotnost by měla být kladné číslo."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before_priority
msgid ""
"Maximum number of days before scheduled date that priority picking products "
"should be reserved."
msgstr ""
"Maximální počet dní před plánovaným datem, kdy by měly být produkty "
"přednostního vyzvednutí rezervovány."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before
msgid ""
"Maximum number of days before scheduled date that products should be "
"reserved."
msgstr ""
"Maximální počet dní před plánovaným datem, kdy by měly být produkty "
"rezervovány."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__max_weight
msgid "Maximum weight shippable in this packaging"
msgstr "Maximální hmotnost zásilky v tomto balení"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__5
msgid "May"
msgstr "Květen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error
msgid "Message Delivery error"
msgstr "Chyba při doručování zpráv"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn_msg
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "Zpráva pro vyzvednutí zboží"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_ids
msgid "Messages"
msgstr "Zprávy"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__method
msgid "Method"
msgstr "Metoda"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid "Min Quantity"
msgstr "Minimální množství"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Pravidlo minimálních skladových zásob"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "Pravidlo minimálních zásob"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__move_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_id
msgid "Move"
msgstr "Přesunout"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Move Detail"
msgstr "Detail přesunu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_entire_packs
msgid "Move Entire Packages"
msgstr "Pohyb celého balíku"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_line_ids
msgid "Move Line"
msgstr "Řadek pohybu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_nosuggest_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_nosuggest_ids
msgid "Move Line Nosuggest"
msgstr "Položka záznamu bez návrhů"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
msgid "Move Lines"
msgstr "Položky pohybu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_lines_count
msgid "Move Lines Count"
msgstr "Počet položek pohybu"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__origin_returned_move_id
msgid "Move that created the return move"
msgstr "Pohyb, který vytvořil vlastní pohyb"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Moves"
msgstr "Pohyby"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group."
" If none is given, the moves generated by stock rules will be grouped into "
"one big picking."
msgstr ""
"Přesuny vytvořené tímto objednávacím bodem budou zařazeny do této skupiny "
"nákupu. Není-li zadán žádný, pohyby generované skladovými pravidly budou "
"seskupeny do jednoho velkého výběru."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr "Vícekrokové trasy"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid "Multiple Quantity"
msgstr "Vícenásobné množství"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_package_type
msgid "Multiple capacity rules for one package type."
msgstr "Vícenásobná pravidla kapacity pro jeden typ balíku."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_product
msgid "Multiple capacity rules for one product."
msgstr "Vícenásobná pravidla kapacity pro jeden produkt."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__my_activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Termín mé aktivity"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"My Company (Chicago) undertakes to do its best to supply performant services"
" in due time in accordance with the agreed timeframes. However, none of its "
"obligations can be considered as being an obligation to achieve results. My "
"Company (Chicago) cannot under any circumstances, be required by the client "
"to appear as a third party in the context of any claim for damages filed "
"against the client by an end consumer."
msgstr ""
"Moje společnost (Chicago) se zavazuje, že udělá vše pro to, aby poskytovala "
"výkonné služby včas a v souladu s dohodnutými časovými rámci. Žádnou z "
"jejích povinností však nelze považovat za povinnost dosahovat výsledků. Moje"
" společnost (Chicago) nemůže být za žádných okolností požadována klientem, "
"aby vystupovala jako třetí strana v souvislosti s jakýmkoli nárokem na "
"náhradu škody vzneseným proti klientovi konečným spotřebitelem."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "My Counts"
msgstr "Moje počty"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "My Transfers"
msgstr "Moje přesuny"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__name
msgid "Name"
msgstr "Jméno"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_in
msgid "Nbr Moves In"
msgstr "Počet pohybů dovnitř"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_out
msgid "Nbr Moves Out"
msgstr "Počet pohybů ven"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Negative Forecasted Quantity"
msgstr "Záporné předpokládané množství"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative Stock"
msgstr "Záporné zásoby"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__net_weight
msgid "Net Weight"
msgstr "Čistá hmotnost"

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__new
#, python-format
msgid "New"
msgstr "Nové"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "New Move:"
msgstr "Nový přesun:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__new_quantity
msgid "New Quantity on Hand"
msgstr "Nové dostupné množství"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr "Nový přesun"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_calendar_event_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Další událost z kalendáře aktivit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Termín další aktivity"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_summary
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_summary
msgid "Next Activity Summary"
msgstr "Souhrn další aktivity"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_type_id
msgid "Next Activity Type"
msgstr "Další typ aktivity"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__next_inventory_date
msgid "Next Expected Inventory"
msgstr "Příští očekávaná inventura"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_date
msgid "Next date the On Hand Quantity should be counted."
msgstr "Příští den by mělo být spočítáno množství na skladě."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Next transfer(s) impacted:"
msgstr "Další ovlivněné převody:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr "Žádné zpětné objednávky"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__no-message
msgid "No Message"
msgstr "Žádná zpráva"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__none
msgid "No Tracking"
msgstr "Žádné sledování"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "No allocation need found for incoming products."
msgstr "Nebyla nalezena žádná potřebná alokace pro příchozí produkty."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "No negative quantities allowed"
msgstr "Nejsou povolena negativní množství"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "No operation made on this lot."
msgstr "Na tomto dílu se neprovádějí žádné operace."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
msgid "No operations found. Let's create a tranfer !"
msgstr "Nebyly nalezeny žádné operace. Vytvořme si jednu!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "No product found. Let's create one!"
msgstr "Nebyl nalezen žádný produkt. Vytvořme si jeden!"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)."
msgstr ""
"Žádné produkty k vrácení (vrátit lze pouze položky ve stavu Hotovo a dosud "
"ne zcela vrácené)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid "No putaway rule found. Let's create one!"
msgstr "Nebylo nalezeno žádné pravidlo pro odložení. Pojďme ho vytvořit!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid "No reordering rule found"
msgstr "Žádné pravidlo přeobjednání nenalezeno"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"No rule has been found to replenish \"%s\" in \"%s\".\n"
"Verify the routes configuration on the product."
msgstr ""
"Nebylo nalezeno žádné pravidlo pro doplnění \"%s\" v \"%s\".\n"
"Ověřte konfiguraci tras produktu."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "No source location defined on stock rule: %s!"
msgstr "Zdrojová lokace není definovaná pro pravidlo: %s!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid "No stock move found"
msgstr "Nenalezen žádný přesun zásob"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "No Stock On Hand"
msgstr "Žádné zásoby na skladě"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "No transfer found. Let's create one!"
msgstr "Nebyly nalezeny žádné převody. Vytvořme si jeden!"

#. module: stock
#: code:addons/stock/report/report_stock_reception.py:0
#, python-format
msgid "No transfers selected or a delivery order selected"
msgstr "Nenalezeny žádné přesuny nebo objednávka doručení"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__0
msgid "Normal"
msgstr "Normální"

#. module: stock
#. openerp-web
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#, python-format
msgid "Not Available"
msgstr "Není k dispozici"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Not Snoozed"
msgstr "Není odloženo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Note"
msgstr "Poznámka"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__note
msgid "Notes"
msgstr "Poznámky"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Nothing to check the availability for."
msgstr "Není pro co kontrolovat dostupnost."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__11
msgid "November"
msgstr "Listopad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcí"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__next_serial_count
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial_count
msgid "Number of SN"
msgstr "Počet sériových čísel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chyb"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_in
msgid "Number of incoming stock moves in the past 12 months"
msgstr "Počet příchozích pohybů zásob za posledních 12 měsíců"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Počet zpráv, které vyžadují akci"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet zpráv s chybou při doručení"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_out
msgid "Number of outgoing stock moves in the past 12 months"
msgstr "Počet odchozích pohybů zásob za posledních 12 měsíců"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_unread_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_unread_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_unread_counter
msgid "Number of unread messages"
msgstr "Počet nepřečtených zpráv"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__10
msgid "October"
msgstr "Říjen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__on_hand
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_on_hand
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "On Hand"
msgstr "Po ruce"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "On Hand Quantity"
msgstr "Pohotové množství"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__available_quantity
msgid ""
"On hand quantity which hasn't been reserved on a transfer, in the default "
"unit of measure of the product"
msgstr ""
"Množství na skladě, které nebylo rezervováno při převodu, ve výchozí měrné "
"jednotce produktu."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr "K dispozici:"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Only a stock manager can validate an inventory adjustment."
msgstr "Opravu inventury může ověřit pouze vedoucí skladu."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#, python-format
msgid "Operation Type"
msgstr "Typ operace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__return_picking_type_id
msgid "Operation Type for Returns"
msgstr "Typ úkonu pro vracení"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking_type_label
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Operation Types"
msgstr "Typy úkonů"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Operation not supported"
msgstr "Provoz není podporován"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_id
msgid "Operation type"
msgstr "Typ úkonu"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking_type_label
msgid "Operation type (PDF)"
msgstr "Typ operace (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking_type
msgid "Operation type (ZPL)"
msgstr "Typ úkonu (ZPL)"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Operations"
msgstr "Úkony"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr "Typy úkonů"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "Operace bez balení"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr ""
"Doplňková adresa, kde bude zboží doručeno, speciálně použito pro rozdělování"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__posx
#: model:ir.model.fields,help:stock.field_stock_location__posy
#: model:ir.model.fields,help:stock.field_stock_location__posz
msgid "Optional localization details, for information purpose only"
msgstr "Volitelné podrobnosti umístění, pouze pro informační účely"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr "Nepovinné: všechny vrácené pohyby vytvořené z tohoto pohybu."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_dest_ids
msgid "Optional: next stock move when chaining them"
msgstr "Volitelné: další pohyb zásob, když jsou zřetězeny"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr "Volitelné: předchozí pohyb zásob při jejich řetězení"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Options"
msgstr "Možnosti"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Order"
msgstr "Objednávka"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Order Once"
msgstr "Objednat jednou"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Order signed"
msgstr "Objednávka podepsána"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Order signed by %s"
msgstr "Objednávka podepsána od %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__orderpoint_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__orderpoint_id
msgid "Orderpoint"
msgstr "Orderpoint"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin"
msgstr "Původ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin Moves"
msgstr "Původní pohyby"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin_returned_move_id
msgid "Origin return move"
msgstr "Původní vrácený pohyb"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__original_location_id
msgid "Original Location"
msgstr "Původní umístění"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_orig_ids
msgid "Original Move"
msgstr "Původní pohyb"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__orderpoint_id
msgid "Original Reordering Rule"
msgstr "Originální doplňovací pravidlo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Other Information"
msgstr "Jiné informace"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, My Company (Chicago) reserves the right to "
"request a fixed interest payment amounting to 10% of the sum remaining due. "
"My Company (Chicago) will be authorized to suspend any provision of services"
" without prior warning in the event of late payment."
msgstr ""
"Naše faktury jsou splatné do 21 pracovních dnů, pokud není na faktuře nebo "
"objednávce uvedena jiná lhůta splatnosti. V případě nezaplacení do data "
"splatnosti si společnost My Company (Chicago) vyhrazuje právo požadovat "
"pevný úrok ve výši 10 % ze zbývající dlužné částky. Společnost My Company "
"(Chicago) bude oprávněna v případě pozdní platby pozastavit poskytování "
"jakýchkoli služeb bez předchozího upozornění."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__out_type_id
msgid "Out Type"
msgstr "Typ výstupu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template__outgoing_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Outgoing"
msgstr "Odchozí"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Outgoing Draft Transfer"
msgstr "Převod odchozích konceptů"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__outgoing_move_line_ids
msgid "Outgoing Move Line"
msgstr "Odchozí pohyb položek"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_steps
msgid "Outgoing Shipments"
msgstr "Odchozí zásilky"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Output"
msgstr "Výstup"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_output_stock_loc_id
msgid "Output Location"
msgstr "Umístění výstupu"

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Overview"
msgstr "Přehled"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Owner"
msgstr "Majitel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__restrict_partner_id
msgid "Owner "
msgstr "Vlastník"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Owner :"
msgstr "Majitel :"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "P&L Qty"
msgstr "P&L množ."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#, python-format
msgid "PRINT"
msgstr "TISK"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Pack"
msgstr "Sada"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pack_type_id
msgid "Pack Type"
msgstr "Druh balíku"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_pack_ship
msgid "Pack goods, send goods in output and then deliver (3 steps)"
msgstr "Zabalit zboží, odeslat zboží na výdej a poté doručit (3 kroky)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__package_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__package_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__package_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__package
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
msgid "Package"
msgstr "Balení"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode_small
msgid "Package Barcode (PDF)"
msgstr "Čárový kód balení (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_package_template
msgid "Package Barcode (ZPL)"
msgstr "Čárový kód balení (ZPL)"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode
msgid "Package Barcode with Content"
msgstr "Čárový kód s obsahem"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__package_capacity_ids
msgid "Package Capacity"
msgstr "Kapacita balení"

#. module: stock
#: code:addons/stock/models/stock_package_level.py:0
#, python-format
msgid "Package Content"
msgstr "Obsah balení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids
msgid "Package Level"
msgstr "Úroveň balení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids_details
msgid "Package Level Ids Details"
msgstr "ID podrobnosti úrovně balení"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr "Název balení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__name
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr "Odkaz na balení"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr "Přesuny balení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_packaging__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__package_type_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__package_type_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Package Type"
msgstr "Typ balení"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_type_view
#: model:ir.ui.menu,name:stock.menu_packaging_types
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_tree
msgid "Package Types"
msgstr "Typy zásilek"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_use
msgid "Package Use"
msgstr "Použití balíku"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Package type"
msgstr "Typ balíků"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.model,name:stock.model_stock_quant_package
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_lot
#: model:ir.ui.menu,name:stock.menu_package
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packages"
msgstr "Balíky"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created via transfers (during pack operation) and can contain different products.\n"
"                Once created, the whole package can be moved at once, or products can be unpacked and moved as single units again."
msgstr ""
"Balíčky se obvykle vytvářejí prostřednictvím přesunů (během operace balení) a mohou obsahovat různé produkty.\n"
"Po vytvoření lze celý balík přesunout najednou nebo lze produkty rozbalit a opět přesunout jako jednotlivé jednotky."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_id
msgid "Packaging"
msgstr "Balení"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__height
msgid "Packaging Height"
msgstr "Výška balení"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__packaging_length
msgid "Packaging Length"
msgstr "Délka balení"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__width
msgid "Packaging Width"
msgstr "Šířka balení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__packaging_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Packagings"
msgstr "Balení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_pack_stock_loc_id
msgid "Packing Location"
msgstr "Místo balení"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Packing Zone"
msgstr "Balící zóna"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Parameters"
msgstr "Parametry"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__location_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__parent_location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr "Nadřazené umístění"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__parent_path
msgid "Parent Path"
msgstr "Nadřazená cesta"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__direct
msgid "Partial"
msgstr "Částečný"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__partially_available
msgid "Partially Available"
msgstr "Částečně dostupné"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "Partner"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__partner_address_id
msgid "Partner Address"
msgstr "Adresa kontaktu"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__pick_ids
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__pick_ids
#, python-format
msgid "Pick"
msgstr "Dodání"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pick_type_id
msgid "Pick Type"
msgstr "Druh dodeje"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__picking_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "Dodání"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr "Dodací list"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking
msgid "Picking Operations"
msgstr "Úkon vyzvednutí"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "Picking Type"
msgstr "Druh dodeje"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_code_domain
msgid "Picking Type Code Domain"
msgstr "Kód domény typu skladové operace"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "Dodací list"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings already processed"
msgstr "Již zpracovaná dodání"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Planned Transfer"
msgstr "Plánovaný přenos"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/stock_rescheduling_popover.js:0
#, python-format
msgid "Planning Issue"
msgstr "Plánování problému"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Planning Issues"
msgstr "Plánování problémů"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Please add 'Done' quantities to the picking to create a new pack."
msgstr "Pro vytvoření nového balení přidejte do výběru množství \"Hotovo\"."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Please add some items to move."
msgstr "Přidejte některé položky, které chcete přesunout."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Please specify at least one non-zero quantity."
msgstr "Prosím zadejete alespoň nenulové množství."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_reserved
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_reserved
msgid "Pre-fill Detailed Operations"
msgstr "Předvyplňte podrobné operace"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/popover_widget.xml:0
#, python-format
msgid "Preceding operations"
msgstr "Předcházející operace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__route_id
msgid "Preferred Route"
msgstr "Preferovaná cesta"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__route_ids
msgid "Preferred Routes"
msgstr "Preferované cesty"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__route_ids
msgid "Preferred route"
msgstr "Preferovaná trasa"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Press the CREATE button to define quantity for each product in your stock or"
" import them from a spreadsheet throughout Favorites"
msgstr ""
"Stisknutím tlačítka VYTVOŘIT definujte množství pro každý produkt na skladě "
"nebo je importujte z tabulky v Oblíbených"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "Tisk"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__print_label
msgid "Print Label"
msgstr "Tisk štítku"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Print Labels"
msgstr "Tisk štítků"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__printed
msgid "Printed"
msgstr "Vytisknutý"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__priority
#: model:ir.model.fields,field_description:stock.field_stock_picking__priority
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sequence
msgid "Priority"
msgstr "Priorita"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__delay_alert_date
msgid "Process at this date to be on time"
msgstr "Proces k tomuto datu, aby byl včas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations faster with barcodes"
msgstr "Zpracujte operace rychleji s čárovými kódy"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations in wave transfers"
msgstr "Zpracujte operace ve vlnách přesunů"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process transfers in batch per worker"
msgstr "Procesy přesunů v dávce na pracovníka"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
#: model:ir.model.fields,field_description:stock.field_stock_move__group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__group_id
msgid "Procurement Group"
msgstr "Skupina zásobování"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Procurement group"
msgstr "Skupina nákupu"

#. module: stock
#: model:ir.actions.server,name:stock.ir_cron_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:stock.ir_cron_scheduler_action
#: model:ir.cron,name:stock.ir_cron_scheduler_action
msgid "Procurement: run scheduler"
msgstr "Zásobování: spustit plánovač"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__produce_line_ids
msgid "Produce Line"
msgstr "Výrobní linka"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Produced Qty"
msgstr "Vyrobené množ."

#. module: stock
#: model:ir.model,name:stock.model_product_product
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__product_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Product"
msgstr "Produkt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability
msgid "Product Availability"
msgstr "Dostupnost produktu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__product_capacity_ids
msgid "Product Capacity"
msgstr "Kapacita produktu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Product Categories"
msgstr "Kategorie produktů"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Product Category"
msgstr "Kategorie výrobku"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_product
msgid "Product Label (ZPL)"
msgstr "Štítek produktu (ZPL)"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_product_product_view
msgid "Product Label Report"
msgstr "Dokument produktových štítků"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr "Filtr výrobních dávek"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_line_action
#: model:ir.ui.menu,name:stock.stock_move_line_menu
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Product Moves"
msgstr "Přesuny produktu"

#. module: stock
#: model:ir.model,name:stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Pohyby produktu (trasa pohybu zboží)"

#. module: stock
#: model:ir.model,name:stock.model_product_packaging
msgid "Product Packaging"
msgstr "Balení produktu"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_packaging
msgid "Product Packaging (ZPL)"
msgstr "Balení produktu (ZPL)"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_product_packagings
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Product Packagings"
msgstr "Obaly produktů"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Product Quantity Confirmed"
msgstr "Potvrzeno množství produktu"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Product Quantity Updated"
msgstr "Aktualizováno množství produktu"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_forecasted.js:0
#: model:ir.model,name:stock.model_product_replenish
#, python-format
msgid "Product Replenish"
msgstr "Doplnění produktu"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_stock_rule
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Product Routes Report"
msgstr "Výkaz tras produktu"

#. module: stock
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_tmpl_id
msgid "Product Template"
msgstr "Šablona produktu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_tmpl_id
msgid "Product Tmpl"
msgstr "Produktová šablona"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__tracking
msgid "Product Tracking"
msgstr "Sledování produktu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__detailed_type
#: model:ir.model.fields,field_description:stock.field_product_template__detailed_type
#: model:ir.model.fields,field_description:stock.field_stock_move__product_type
msgid "Product Type"
msgstr "Typ výrobku"

#. module: stock
#: model:ir.model,name:stock.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Měrná jednotka výrobku"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_product_normal_action
#: model:ir.ui.menu,name:stock.product_product_menu
msgid "Product Variants"
msgstr "Varianty výrobku"

#. module: stock
#: code:addons/stock/report/product_label_report.py:0
#, python-format
msgid "Product model not defined, Please contact your administrator."
msgstr "Model produktu není definován. Obraťte se na svého správce."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid ""
"Product this lot/serial number contains. You cannot change it anymore if it "
"has already been moved."
msgstr ""
"Produkt, který toto produktové / sériové číslo obsahuje. Pokud již byl "
"přesunut, nemůžete jej změnit."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom_name
msgid "Product unit of measure label"
msgstr "Štítek měrné jednotky produktu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__has_tracking
msgid "Product with Tracking"
msgstr "Produkt se sledováním"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__production
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production"
msgstr "Výroba"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_production
msgid "Production Location"
msgstr "Umístění výroby"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production Locations"
msgstr "Umístění výroby"

#. module: stock
#: code:addons/stock/wizard/stock_quantity_history.py:0
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.actions.act_window,name:stock.product_template_action_product
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#, python-format
msgid "Products"
msgstr "Produkty"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability_state
msgid "Products Availability State"
msgstr "Stav dostupnosti produktu"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__priority
msgid ""
"Products will be reserved first for the transfers with the highest "
"priorities."
msgstr ""
"Produkty budou rezervovány nejprve pro převody s nejvyššími prioritami."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Products: %(location)s"
msgstr "Produkty: %(location)s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__propagate
msgid "Propagate"
msgstr "Propagovat"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__propagate_cancel
msgid "Propagate cancel and split"
msgstr "Propagovat zrušení a rozdělení"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Propagation"
msgstr "Propagace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_propagation_option
msgid "Propagation of Procurement Group"
msgstr "Propagace skupiny pro zadávání zakázek"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_carrier
msgid "Propagation of carrier"
msgstr "Propagovat kaliéru"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull_push
msgid "Pull & Push"
msgstr "Pull & Push"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull
msgid "Pull From"
msgstr "Vytáhnout"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Pull Rule"
msgstr "Pravidlo vytažení"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Push Rule"
msgstr "Push pravidlo"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__push
msgid "Push To"
msgstr "Tlačit do"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Put in Pack"
msgstr "Vložit do balení"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Put your products in packs (e.g. parcels, boxes) and track them"
msgstr "Vložte své výrobky do balíčků (např. Balíků, krabic) a sledujte je"

#. module: stock
#: model:ir.model,name:stock.model_stock_putaway_rule
msgid "Putaway Rule"
msgstr "Pravidlo odložení"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model:ir.actions.act_window,name:stock.category_open_putaway
#: model:ir.actions.act_window,name:stock.location_open_putaway
#: model:ir.model.fields,field_description:stock.field_product_category__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_product_product__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__putaway_rule_ids
#: model:ir.ui.menu,name:stock.menu_putaway
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#, python-format
msgid "Putaway Rules"
msgstr "Pravidla odkládání"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Putaway:"
msgstr "Dát pryč:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_putaway_tree
msgid "Putaways Rules"
msgstr "Pravidla odkládání"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_qty_multiple_check
msgid "Qty Multiple must be greater than or equal to zero."
msgstr "Qty Multiple musí být větší nebo rovno nule."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control
msgid "Quality"
msgstr "Jakost"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Quality Control"
msgstr "Kontrola kvality"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr "Umístnění kontroly kvality"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "Formulář kontroly kvality"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_request_count__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quant_ids
msgid "Quant"
msgstr "Množství"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Quants are auto-deleted when appropriate. If you must manually delete them, "
"please ask a stock manager to do it."
msgstr ""
"Množství se v případě potřeby automaticky odstraní. Pokud jej musíte "
"odstranit ručně, požádejte o to vedoucího skladu."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quant's creation is restricted, you can't do this operation."
msgstr "Vytvoření množství je omezeno, tuto operaci nelze provést."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quant's editing is restricted, you can't do this operation."
msgstr "Úprava množství je omezena, tuto operaci nelze provést."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quantities Already Set"
msgstr "Množství již nastaveno"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quantities To Reset"
msgstr "Množství pro obnovení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__quantity
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_quant__quantity
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_qty
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quantity
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Quantity"
msgstr "Množství"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Quantity :"
msgstr "Množství:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__quantity_done
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
msgid "Quantity Done"
msgstr "Hotové množství"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr "Násobek množství"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__qty_available
#: model:ir.model.fields,field_description:stock.field_product_template__qty_available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity On Hand"
msgstr "Množství na skladě"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reserved_availability
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity Reserved"
msgstr "Rezervované množství"

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:0
#, python-format
msgid "Quantity cannot be negative."
msgstr "Množství nemůže být záporné."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_outdated
msgid "Quantity has been moved since last count"
msgstr "Množství bylo přesunuto od posledního počtu"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr "Množství na skladě které může být rezervováno pro tento pohyb"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_qty
msgid "Quantity in the default UoM of the product"
msgstr "Množství ve výchozí MJ produktu"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""
"Množství plánovaných příchozích produktů.\n"
"V kontextu s jedním skladovým místem zahrnuje zboží, které přichází do tohoto místa nebo do kteréhokoli z jeho podřízených míst.\n"
"V kontextu s jedním skladem zahrnuje zboží, které přichází do skladového umístění tohoto skladu nebo do kteréhokoli z jeho podřízených umístění.\n"
"Jinak sem patří zboží, které přichází do jakéhokoli skladového místa s typem \"interní\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""
"Množství plánovaných odchozích produktů.\n"
"V souvislosti s jednou skladovou lokalitou se jedná o zboží opouštějící toto místo nebo o jakékoli jeho podřazené zboží.\n"
"V kontextu s jedním skladem to zahrnuje zboží opouštějící skladové umístění tohoto skladu nebo některé z jeho podřazeného zboží.\n"
"V opačném případě se jedná o zboží opouštějící jakoukoli skladovou polohu s typovým označením „vnitřní“ ."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__quantity
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr "Počet produktů v tomto množství ve výchozí měrné jednotce produktu"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__reserved_quantity
msgid ""
"Quantity of reserved products in this quant, in the default unit of measure "
"of the product"
msgstr ""
"Množství vyhrazených produktů v tomto množství, ve výchozí měrné jednotce "
"produktu"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_positive_quantity
msgid "Quantity should be a positive number."
msgstr "Hmotnost by měla být kladné číslo."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reserved_availability
msgid "Quantity that has already been reserved for this move"
msgstr "Množství na skladě které již bylo rezervováno pro tento pohyb"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__picking_quantity
msgid "Quantity to print"
msgstr "Množství k tisku"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__quant_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Quants"
msgstr "Množství"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quants cannot be created for consumables or services."
msgstr "Množství nelze vytvořit pro spotřební materiál nebo služby."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Ready"
msgstr "Připraveno"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_qty
msgid "Real Quantity"
msgstr "Skutečné množství"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_qty
msgid "Real Reserved Quantity"
msgstr "Skutečné rezervované množství"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__incoming
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Receipt"
msgstr "Účtenka"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_route_id
msgid "Receipt Route"
msgstr "Trasa příjmu"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.chi_picking_type_in
#: model:stock.picking.type,name:stock.picking_type_in
#, python-format
msgid "Receipts"
msgstr "Účtenky"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Receive From"
msgstr "Přijmout ze"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__one_step
msgid "Receive goods directly (1 step)"
msgstr "Přijmout zboží přímo (1 krok)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__two_steps
msgid "Receive goods in input and then stock (2 steps)"
msgstr "Přijmout zboží na vstup a poté na sklad (2 kroky)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__three_steps
msgid "Receive goods in input, then quality and then stock (3 steps)"
msgstr ""
"Přijmout zboží na vstup, poté kontrola kvality a poté na sklad (3 kroky)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 1 step (stock)"
msgstr "Přijmout v 1 kroku (sklad)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 2 steps (input + stock)"
msgstr "Přijmout ve 2 krocích (vstup + sklad)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 3 steps (input + quality + stock)"
msgstr "Přijmout ve 3 krocích (vstup + kvalita + sklad)"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Received Qty"
msgstr "Obdržené množ."

#. module: stock
#: model:ir.actions.client,name:stock.stock_reception_action
#: model:ir.actions.report,name:stock.stock_reception_report_action
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_reception_report
msgid "Reception Report"
msgstr "Výkaz příjmu"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking
msgid "Reception Report Label"
msgstr "Štítek výkazu příjmu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__name
#: model:ir.model.fields,field_description:stock.field_stock_move__reference
#: model:ir.model.fields,field_description:stock.field_stock_move_line__reference
#: model:ir.model.fields,field_description:stock.field_stock_picking__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__name
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Reference"
msgstr "Reference"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_id
msgid "Reference Sequence"
msgstr "Referenční sekvence"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_picking_name_uniq
msgid "Reference must be unique per company!"
msgstr "Odkaz musí být jedinečný pro každou společnost!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__origin
msgid "Reference of the document"
msgstr "Značka dokumentu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Reference:"
msgstr "Reference:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Register lots, packs, location"
msgstr "Registrace šarže, balení a lokace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__stock_move_ids
msgid "Related Stock Moves"
msgstr "Související přesuny zásob"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr "Zbylé části částečně zpracované dodávky"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr "Odběr"

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location__removal_strategy_id
msgid "Removal Strategy"
msgstr "Strategie odběru"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Removal strategy %s not implemented."
msgstr "Strategie vyřazení %s není implementována."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_max_qty
msgid "Reordering Max Qty"
msgstr "Přeskupení maximálního množství"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_min_qty
msgid "Reordering Min Qty"
msgstr "Přeskupení minimálního množství"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rule"
msgstr "Doplňovací pravidlo"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_reordering_rules
#: model:ir.ui.menu,name:stock.menu_reordering_rules_config
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "Reordering Rules"
msgstr "Pravidla znovuobjednání"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr "Vyhledávání pravidel znovuobjednání"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#: model:ir.actions.act_window,name:stock.action_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#, python-format
msgid "Replenish"
msgstr "Doplnit"

#. module: stock
#: model:stock.location.route,name:stock.route_warehouse0_mto
msgid "Replenish on Order (MTO)"
msgstr "Doplnění na objednávku (MTO)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "Replenish wizard"
msgstr "Průvodce doplněním"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint_replenish
#: model:ir.actions.server,name:stock.action_replenishment
#: model:ir.ui.menu,name:stock.menu_reordering_rules_replenish
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Replenishment"
msgstr "Doplňování"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_replenishment_info
msgid "Replenishment Information"
msgstr "Informace o doplnění"

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Replenishment Information for %s in %s"
msgstr "Informace o doplnění pro %s v %s"

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Replenishment Report"
msgstr "Zpráva o doplnění"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Replenishment Report Search"
msgstr "Vyhledávání výkazu o doplnění"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Report Quantity"
msgstr "Výkaz o množství"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
msgid "Reporting"
msgstr "Přehledy"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_request_count
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Request a Count"
msgstr "Vyžádat počet"

#. module: stock
#: model:res.groups,name:stock.group_stock_sign_delivery
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Require a signature on your delivery orders"
msgstr "Vyžaduje podpis na váš příkaz dodání"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_method
msgid "Reservation Method"
msgstr "Způsob rezervace"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Reservations"
msgstr "Rezervace"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Reserve"
msgstr "Vyjímka"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__full
msgid "Reserve Only Full Packagings"
msgstr "Rezervovat pouze celá balení"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__packaging_reserve_method
msgid ""
"Reserve Only Full Packagings: will not reserve partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then only 1000 will be reserved\n"
"Reserve Partial Packagings: allow reserving partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then 1600 will be reserved"
msgstr ""
"Rezervovat pouze plná balení: nebude rezervovat částečná balení. Pokud si zákazník objedná 2 palety po 1000 kusech a vy máte na skladě pouze 1600, bude rezervováno pouze 1000\n"
"Rezervovat částečné obaly: umožňuje rezervaci částečných obalů. Pokud si zákazník objedná 2 palety po 1000 kusech a vy máte na skladě pouze 1600, pak bude rezervováno 1600"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__packaging_reserve_method
msgid "Reserve Packagings"
msgstr "Rezervovat balení"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__partial
msgid "Reserve Partial Packagings"
msgstr "Rezervovat částečná balení"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reserve before scheduled date"
msgstr "Rezervovat před plánovaným datem"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_qty
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Reserved"
msgstr "Rezervováno"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__reserved_quantity
msgid "Reserved Quantity"
msgstr "Rezervované množství"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Reserved from stock"
msgstr "Rezervováno ze zásob"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "Reserving a negative quantity is not allowed."
msgstr "Rezervace záporného množství není dovolená."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__responsible_id
#: model:ir.model.fields,field_description:stock.field_product_template__responsible_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__user_id
msgid "Responsible"
msgstr "Odpovědný"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_user_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_user_id
msgid "Responsible User"
msgstr "Zodpovědný uživatel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Resupply"
msgstr "Zásobování"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_wh_ids
msgid "Resupply From"
msgstr "Znovu dodání od"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_route_ids
msgid "Resupply Routes"
msgstr "Zásobovací trasy"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr "Návrat"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__location_id
msgid "Return Location"
msgstr "Lokace návratu"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Vrátit dodávku"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "Položka vratkového přesunu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__return_type_id
msgid "Return Type"
msgstr "Typ vrácení"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Return of %s"
msgstr "Návrat %s"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Returned Picking"
msgstr "Vrácené dodání"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Returns"
msgstr "Vrací se"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__reusable
msgid "Reusable Box"
msgstr "Znovu použitelná krabice"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__package_use
msgid ""
"Reusable boxes are used for batch picking and emptied afterwards to be reused. In the barcode application, scanning a reusable box will add the products in this box.\n"
"        Disposable boxes aren't reused, when scanning a disposable box in the barcode application, the contained products are added to the transfer."
msgstr ""
"Opakovaně použitelné krabice se používají pro vychystávání dávek a poté se vyprázdní a znovu použijí. V aplikaci čárových kódů se po naskenování opakovaně použitelné krabice přidají produkty do této krabice.\n"
"Jednorázové krabice se znovu nepoužívají, při naskenování jednorázové krabice v aplikaci čárových kódů se obsažené výrobky přidají k přenosu."

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
msgid "Reverse Transfer"
msgstr "Obrátit přesun"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Route"
msgstr "Pracovní postup"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_company_id
msgid "Route Company"
msgstr "Trasa společnosti"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_sequence
msgid "Route Sequence"
msgstr "Sekvence trasy"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.actions.server,name:stock.action_open_routes
#: model:ir.model.fields,field_description:stock.field_product_category__route_ids
#: model:ir.model.fields,field_description:stock.field_product_packaging__route_ids
#: model:ir.model.fields,field_description:stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr "Trasy"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__has_available_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__has_available_route_ids
msgid "Routes can be selected on this product"
msgstr "U tohoto produktu lze vybrat trasy"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_wh_ids
msgid ""
"Routes will be created automatically to resupply this warehouse from the "
"warehouses ticked"
msgstr ""
"Trasy budou vytvořeny automaticky, aby sklad doplnil zásoby z vybraných "
"skladů"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them"
" on products and product categories"
msgstr ""
"Pro tyto sklady budou vytvořeny trasy a můžete je vybrat na výrobcích a "
"kategoriích výrobků"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__rule_message
msgid "Rule Message"
msgstr "Zpráva pravidla"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_rules_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route__rule_ids
#: model:ir.ui.menu,name:stock.menu_action_rules_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_tree
msgid "Rules"
msgstr "Pravidla"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Categories"
msgstr "Pravidla pro kategorie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Products"
msgstr "Pravidla na produkty"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__rule_ids
msgid "Rules used"
msgstr "Pravidla použita"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_procurement_compute
#: model:ir.ui.menu,name:stock.menu_procurement_compute
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Run Scheduler"
msgstr "Spustit plánovač"

#. module: stock
#: model:ir.model,name:stock.model_stock_scheduler_compute
msgid "Run Scheduler Manually"
msgstr "Spustit plánovač manuálně"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Run the scheduler"
msgstr "Spustit plánovač"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_sms
msgid "SMS Confirmation"
msgstr "SMS potvrzení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Chyba doručení SMS"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "STANDARD TERMS AND CONDITIONS OF SALE"
msgstr "STANDARDNÍ OBCHODNÍ PODMÍNKY PRODEJE"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Sales History"
msgstr "Historie prodeje"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__date_planned
#: model:ir.model.fields,field_description:stock.field_stock_picking__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Scheduled Date"
msgstr "Plánované datum"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date
msgid "Scheduled date until move is done, then date of actual move processing"
msgstr ""
"Naplánované datum do dokončení přesunu, poté datum skutečného zpracování "
"přesunu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled or processing date"
msgstr "Plánovaný datum na zpracování"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__scheduled_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr ""
"Plánovaný čas pro první část zásilky, která má být zpracována. Ruční "
"nastavení hodnoty zde nastaví jako očekávané datum pro všechny skladové "
"pohyby."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model,name:stock.model_stock_scrap
#: model:ir.model.fields,field_description:stock.field_stock_move__scrap_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__scrap_id
#: model:ir.ui.menu,name:stock.menu_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Scrap"
msgstr "Zmetek"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Scrap Location"
msgstr "Umístění zmetku"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__move_id
msgid "Scrap Move"
msgstr "Přesun zmetku"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_scrap
msgid "Scrap Orders"
msgstr "Vyřazovací objednávky"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid "Scrap products"
msgstr "Vyřazené produkty"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrapped
msgid "Scrapped"
msgstr "Označené jako zmetky"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid ""
"Scrapping a product will remove it from your stock. The product will\n"
"                end up in a scrap location that can be used for reporting purpose."
msgstr ""
"Vyřazením produktu jej odstraníte ze skladu. Produkt skončí na vyřazovací\n"
"                lokaci, která se může použít pro účely výkazu."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Scraps"
msgstr "Zmetky"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Search Procurement"
msgstr "Hledat zásobování"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Search Scrap"
msgstr "Vyhledat vyřazení"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_category_id
msgid "Select category for the current product"
msgstr "Vyberte kategorii pro tento výrobek"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr "Vyberte místa, kde lze tuto trasu zvolit"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__picking_warn
#: model:ir.model.fields,help:stock.field_res_users__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Výběr položky \"Varování\" pošle oznámení uživateli, výběrem \"Blokující "
"zpráva\" vyvoláte výjimku s oznámením a zastavíte provádění. Zpráva musí být"
" napsána v následujícícm poli."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Prodej a nákup produktů v různých měrných jednotkách"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Send an automatic confirmation SMS Text Message when Delivery Orders are "
"done"
msgstr ""
"Po dokončení doručovacích objednávek odešlete automatické potvrzovací SMS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Send an automatic confirmation email when Delivery Orders are done"
msgstr "Po odeslání objednávky odeslat e-mail s automatickým potvrzením"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lead_mass_mail
msgid "Send email"
msgstr "Poslat e-mailem"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_ship
msgid "Send goods in output and then deliver (2 steps)"
msgstr "Zašlete zboží na výdej a poté doručte (2 kroky)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__9
msgid "September"
msgstr "Září"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields,field_description:stock.field_stock_location_route__sequence
#: model:ir.model.fields,field_description:stock.field_stock_move__sequence
#: model:ir.model.fields,field_description:stock.field_stock_package_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_rule__sequence
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#, python-format
msgid "Sequence"
msgstr "Číselná řada"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence in"
msgstr "Sekvence dovnitř"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence internal"
msgstr "Interní sekvence"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence out"
msgstr "Sekvence ven"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence packing"
msgstr "Sekvenční balení"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence picking"
msgstr "Sekvenční vychystávání"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence return"
msgstr "Sekvence návratu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__lot_ids
msgid "Serial Numbers"
msgstr "Seriová čísla"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"Serial number (%s) already exists in location(s): %s. Please correct the "
"serial number encoded."
msgstr ""
"Sériové číslo (%s) již existuje v lokacích: %s. Opravte zakódované sériové "
"číslo."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Serial number (%s) is not located in %s, but is located in location(s): %s.\n"
"\n"
"Please correct this to prevent inconsistent data."
msgstr ""
"Sériové číslo (%s) není umístěné v %s, ale je umístěné v lokacích: %s\n"
"\n"
"Opravte to, abyste předešli nekonzistentním údajům."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Serial number (%s) is not located in %s, but is located in location(s): %s.\n"
"\n"
"Source location for this move will be changed to %s"
msgstr ""
"Sériové číslo (%s) není umístěné v %s, ale je umístěné v lokacích: %s\n"
"\n"
"Zdrojové umístění tohoto přesunu bude změněno na %s"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_quants_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Set"
msgstr "Nastavit"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__set
msgid "Set Current Value"
msgstr "Nastavit aktuální hodnotu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Warehouse Routes"
msgstr "Nastavit trasy skladu"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source location for this product category.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""
"Nastavte konkrétní strategii odstranění, která bude použita bez ohledu na zdrojové umístění pro tuto kategorii produktů.\n"
"\n"
"FIFO: produkty/šarže, které byly naskladněny jako první, budou přesunuty jako první.\n"
"LIFO: produkty/šarže, které byly naskladněny jako poslední, budou přesunuty jako první.\n"
"Umístění skříně: produkty/šarže nejblíže cílovému umístění budou přesunuty jako první.\n"
"FEFO: produkty/šarže s nejbližším datem odebrání budou přesunuty jako první (dostupnost této metody závisí na nastavení „Data vypršení platnosti“)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set expiration dates on lots &amp; serial numbers"
msgstr "Nastavte data vypršení platnosti u šarží a sériových čísel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set owner on stored products"
msgstr "Nastavit vlastníka na uložené produkty"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to manage variants"
msgstr ""
"Nastavte atributy produktu (například barvu, velikost) pro správu variant"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Set quantities"
msgstr "Nastavení množství"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_id
msgid ""
"Sets a location if you produce at a fixed location. This can be a partner "
"location if you subcontract the manufacturing operations."
msgstr ""
"Nastavuje umístění pokud výrobek vyrábíte na pevném umístění. Může jít o "
"umístění partnera pokud pro vás výrobek vyrábí subdodavatel."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
msgid "Settings"
msgstr "Nastavení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posy
msgid "Shelves (Y)"
msgstr "Regál (Y)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Shipments"
msgstr "Zásilky"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping"
msgstr "Doprava"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping Connectors"
msgstr "Konektory na dopravce"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_type
msgid "Shipping Policy"
msgstr "Přepravní politika"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Shipping connectors allow to compute accurate shipping costs, print shipping"
" labels and request carrier picking at your warehouse to ship to the "
"customer. Apply shipping connector from delivery methods."
msgstr ""
"Přepravní konektory umožňují vypočítat přesné přepravní náklady, vytisknout "
"přepravní štítky a vyžádat si vychystání dopravce ve vašem skladu k odeslání"
" zákazníkovi. Použijte přepravní konektor z dodacích metod."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__code
msgid "Short Name"
msgstr "Krátký název"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__code
msgid "Short name used to identify your warehouse"
msgstr "Krátký název sloužící k identifikaci vašeho skladu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_allocation
msgid "Show Allocation"
msgstr "Zobrazit alokaci"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_check_availability
msgid "Show Check Availability"
msgstr "Zobrazit kontrolu dostupnosti"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_operations
msgid "Show Detailed Operations"
msgstr "Zobrazit podrobné operace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_forecasted_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_forecasted_qty_status_button
msgid "Show Forecasted Qty Status Button"
msgstr "Zobrazit tlačítko stavu předpověděného množství"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_m2o
msgid "Show Lots M2O"
msgstr "Zobrazit číšlo šarže u zboží na objednávku"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_text
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_lots_text
msgid "Show Lots Text"
msgstr "Zobrazit text šarže"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_mark_as_todo
msgid "Show Mark As Todo"
msgstr "Zobrazit značku jako úkol"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_on_hand_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_on_hand_qty_status_button
msgid "Show On Hand Qty Status Button"
msgstr "Zobrazit tlačítko stavu množství po ruce"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_operations
msgid "Show Operations"
msgstr "Zobrazit operace"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_auto_reception_report
msgid "Show Reception Report at Validation"
msgstr "Zobrazit zprávu o příjmu při ověření"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__show_transfers
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__show_transfers
msgid "Show Transfers"
msgstr "Zobrazit přesuny"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_validate
msgid "Show Validate"
msgstr "Zobrazit ověření"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Zobrazit všechny záznamy, které mají následující datum akce před dneškem"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rules_report__warehouse_ids
msgid "Show the routes that apply on selected warehouses."
msgstr "Zobrazit trasy, které platí pro vybrané sklady."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__show_info
msgid "Show warning"
msgstr "Zobrazit varování"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Sign"
msgstr "e-podpis"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_sign_delivery
#: model:ir.model.fields,field_description:stock.field_stock_picking__signature
#: model:ir.model.fields,help:stock.field_stock_picking__signature
msgid "Signature"
msgstr "Podpis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Signed"
msgstr "Podepsán"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size"
msgstr "Velikost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size: Length × Width × Height"
msgstr "Velikost: Délka × šířka × výška"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#, python-format
msgid "Snooze"
msgstr "Odložit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__snoozed_until
msgid "Snooze Date"
msgstr "Odložit datum"

#. module: stock
#: model:ir.model,name:stock.model_stock_orderpoint_snooze
msgid "Snooze Orderpoint"
msgstr "Odložit doplňovací bod"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__predefined_date
msgid "Snooze for"
msgstr "Odložit pro"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Snoozed"
msgstr "Odloženo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid ""
"Some products of the inventory adjustment are tracked. Are you sure you "
"don't want to specify a serial or lot number for them?"
msgstr ""
"Některé produkty úpravy zásob jsou sledovány. Opravdu nechcete pro ně "
"specifikovat sériové číslo nebo produktové číslo?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Some selected lines already have quantities set, they will be ignored."
msgstr ""
"Některé vybrané položky již mají nastavená množství, budou ignorovány."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid ""
"Some selected lines don't have any quantities set, they will be ignored."
msgstr "Některé vybrané položky nemají nastavená množství, budou ignorovány."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__origin
msgid "Source"
msgstr "Zdroj"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin
#: model:ir.model.fields,field_description:stock.field_stock_picking__origin
#: model:ir.model.fields,field_description:stock.field_stock_scrap__origin
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Source Document"
msgstr "Zdrojový dokument"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#, python-format
msgid "Source Location"
msgstr "Zdrojové umístění"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Location:"
msgstr "Zdrojové umístění:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Source Package"
msgstr "Zdrojový balík"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Package :"
msgstr "Zdrojový balík :"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Starred"
msgstr "Označeno hvězdičkou"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__state
#: model:ir.model.fields,field_description:stock.field_stock_package_level__state
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "State"
msgstr "Stav"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__state
#: model:ir.model.fields,field_description:stock.field_stock_move_line__state
#: model:ir.model.fields,field_description:stock.field_stock_picking__state
#: model:ir.model.fields,field_description:stock.field_stock_scrap__state
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "Stav"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_state
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stav na základě aktivit\n"
"Vypršeno: Datum již uplynulo\n"
"Dnes: Datum aktivity je dnes\n"
"Plánováno: Budoucí aktivity."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Stock"
msgstr "Sklad"

#. module: stock
#: model:ir.model,name:stock.model_stock_assign_serial
msgid "Stock Assign Serial Numbers"
msgstr "Přiřazení sériových čísel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr "Umístění skladu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr "Umístění zásob"

#. module: stock
#: model:ir.model,name:stock.model_stock_move
#: model:ir.model.fields,field_description:stock.field_product_product__stock_move_ids
#: model:ir.model.fields,field_description:stock.field_stock_move_line__move_id
msgid "Stock Move"
msgstr "Pohyb zásob"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_action
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_lines
#: model:ir.ui.menu,name:stock.stock_move_menu
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Stock Moves"
msgstr "Pohyby zásob"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr "Analýza skladových pohybů"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.actions.act_window,name:stock.dashboard_open_quants
#, python-format
msgid "Stock On Hand"
msgstr "Skladem "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Stock Operation"
msgstr "Skladová operace"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "Cílové balení"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_level
msgid "Stock Package Level"
msgstr "Historie balení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn
msgid "Stock Picking"
msgstr "Výdej ze skladu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__stock_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_view_graph
msgid "Stock Quant"
msgstr "Skladové množství"

#. module: stock
#: model:ir.model,name:stock.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "Historie skladových zásob"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_quantity
msgid "Stock Quantity Report"
msgstr "Skladové zásoby"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "Výkaz příjmu zboží"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_product_product_replenishment
#: model:ir.model,name:stock.model_report_stock_report_product_template_replenishment
msgid "Stock Replenishment Report"
msgstr "Zpráva o doplnění zásob"

#. module: stock
#: model:ir.model,name:stock.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "Požadavek na inventuru"

#. module: stock
#: model:ir.model,name:stock.model_stock_rule
#: model:ir.model.fields,field_description:stock.field_stock_move__rule_id
msgid "Stock Rule"
msgstr "Pravidlo zásob"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_rules_report
msgid "Stock Rules Report"
msgstr "Výkaz zásobovacích pravidel"

#. module: stock
#: model:ir.model,name:stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Výkaz zásobovacích pravidel"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_confirmation
msgid "Stock Track Confirmation"
msgstr "Potvrzení sledovaného zboží"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_line
msgid "Stock Track Line"
msgstr "Položka sledovaného zboží"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids_without_package
msgid "Stock moves not in package"
msgstr "Skladové pohyby nejsou v balení"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr "Pohyby zásob, které jsou dostupné (připravené ke zpracování)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr "Pohyby zásob, které jsou potvrzené, dostupné nebo čekající"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr "Pohyby zásob, které byly zpracovány"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_type
msgid "Stock package type"
msgstr "Typ balení"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Přehled skladových pravidel"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "Informace o dodavateli doplnění"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__detailed_type__product
#: model:ir.model.fields.selection,name:stock.selection__product_template__type__product
msgid "Storable Product"
msgstr "Skladovatelný produkt"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"Storable products are physical items for which you manage the inventory "
"level."
msgstr ""
"Skladovatelné produkty jsou fyzické položky, u kterých spravujete úroveň "
"zásob."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Storage Capacities"
msgstr "Kapacita skladu"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_storage_categories
#: model:ir.ui.menu,name:stock.menu_storage_categoty_config
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Storage Categories"
msgstr "Uskladňovací kategorie"

#. module: stock
#: model:ir.model,name:stock.model_stock_storage_category
#: model:ir.model.fields,field_description:stock.field_stock_location__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__storage_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Storage Category"
msgstr "Uskladňovací kategorie"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category_capacity
#: model:ir.model,name:stock.model_stock_storage_category_capacity
#: model:ir.model.fields,field_description:stock.field_product_product__storage_category_capacity_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_type__storage_category_capacity_ids
#: model:ir.ui.menu,name:stock.menu_storage_categoty_capacity_config
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_capacity_tree
msgid "Storage Category Capacity"
msgstr "Kapacita uskladňovací kategorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr "Místa skladování"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_multi_locations
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr ""
"Skladujte produkty ve specifických lokacích skladiště (např. police, skříně)"
" a podle toho sledujte inventuru."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_out_id
msgid "Store to sublocation"
msgstr "Skladovat na dílčí místo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__supplied_wh_id
msgid "Supplied Warehouse"
msgstr "Zásobovaný sklad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__procure_method
#: model:ir.model.fields,field_description:stock.field_stock_rule__procure_method
msgid "Supply Method"
msgstr "Metoda naskladnění"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__supplier_wh_id
msgid "Supplying Warehouse"
msgstr "Zásobování skladu"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_stock
msgid "Take From Stock"
msgstr "Vzít ze zásob"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__mts_else_mto
msgid "Take From Stock, if unavailable, Trigger Another Rule"
msgstr "Převzít ze skladu, pokud není k dispozici, spustit další pravidlo"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__procure_method
msgid ""
"Take From Stock: the products will be taken from the available stock of the source location.\n"
"Trigger Another Rule: the system will try to find a stock rule to bring the products in the source location. The available stock will be ignored.\n"
"Take From Stock, if Unavailable, Trigger Another Rule: the products will be taken from the available stock of the source location.If there is no stock available, the system will try to find a  rule to bring the products in the source location."
msgstr ""
"Vzít ze skladu: produkty budou odebrány z dostupných zásob ve zdrojové lokaci\n"
"Spustit další pravidlo: systém se pokusí najít skladové pravidlo, které produkty přenese do zdrojové lokace. Dostupné zásoby budou ignorovány.\n"
"Odebrat ze skladu, není-li k dispozici, Spustit další pravidlo: produkty budou odebrány z dostupných zásob ve zdrojové lokaci. Pokud nejsou k dispozici žádné zásoby, systém se pokusí najít pravidlo, jak produkty přenést do zdrojové lokace."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr ""
"Technické pole používané k výpočtu, zda má být zobrazeno tlačítko "
"\"Alokace\"."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr "Technické informace"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__warehouse_id
msgid ""
"Technical field depicting the warehouse to consider for the route selection "
"on the next procurement (if any)."
msgstr ""
"Technické pole zobrazující sklad, který je třeba vzít v úvahu při výběru "
"trasy při příštím nákupu (pokud existuje)."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__internal_transit_location_id
msgid ""
"Technical field used for resupply routes between warehouses that belong to "
"this company"
msgstr ""
"Technické pole používané pro zásobovací trasy mezi sklady, které patří této "
"společnosti"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_check_availability
msgid ""
"Technical field used to compute whether the button \"Check Availability\" "
"should be displayed."
msgstr ""
"Technické pole používané k výpočtu, zda má být zobrazeno tlačítko "
"\"Zkontrolovat dostupnost\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_mark_as_todo
msgid ""
"Technical field used to compute whether the button \"Mark as Todo\" should "
"be displayed."
msgstr ""
"Technické pole používané k výpočtu, zda má být zobrazeno tlačítko \"Označit "
"jako 'úkol'\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_validate
msgid ""
"Technical field used to decide whether the button \"Validate\" should be "
"displayed."
msgstr ""
"Technické pole používané k výpočtu, zda má být zobrazeno tlačítko "
"\"Potvrdit\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__restrict_partner_id
msgid ""
"Technical field used to depict a restriction on the ownership of quants to "
"consider when marking this move as 'done'"
msgstr ""
"Technické pole sloužící k zobrazení omezení vlastnictví kvantifikátorů, "
"které je třeba vzít v úvahu při označování tohoto kroku jako \"hotový\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__price_unit
msgid ""
"Technical field used to record the product cost set by the user during a "
"picking confirmation (when costing method used is 'average price' or "
"'real'). Value given in company currency and in product uom."
msgstr ""
"Technické pole, které se používá k zaznamenávání nákladů na produkt "
"nastavených uživatelem během potvrzení vyzvednutí (při použití metody "
"kalkulace „průměrná cena“ nebo „skutečná“). Hodnota uvedená v měně "
"společnosti a v měrné jednotce produktu."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__produce_line_ids
msgid "Technical link to see which line was produced with this. "
msgstr "Technický odkaz k zobrazení, kterou položku to vytvořilo."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__consume_line_ids
msgid "Technical link to see who consumed what. "
msgstr "Technický odkaz k zobrazení, kdo spotřeboval co."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_tmpl_id
msgid "Technical: used in views"
msgstr "Technické: používá se v zobrazeních"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__stock_move_ids
#: model:ir.model.fields,help:stock.field_product_product__stock_quant_ids
msgid "Technical: used to compute quantities."
msgstr "Technické: používá se k výpočtu množství."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__incoming_move_line_ids
#: model:ir.model.fields,help:stock.field_stock_location__outgoing_move_line_ids
msgid "Technical: used to compute weight."
msgstr "Technické: používá se k výpočtu hmotnosti."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_tmpl_id
msgid "Template"
msgstr "Předloha"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__auto
msgid ""
"The 'Manual Operation' value will create a stock move after the current one."
" With 'Automatic No Step Added', the location is replaced in the original "
"move."
msgstr ""
"Hodnota 'Manuální operace' vytvoří pohyb zásob po aktuálním. S 'Automaticky "
"nebyl přidán žádný krok' je lokace v původním pohybem nahrazena."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"The Serial Number (%s) is already used in these location(s): %s.\n"
"\n"
"Is this expected? For example this can occur if a delivery operation is validated before its corresponding receipt operation is validated. In this case the issue will be solved automatically once all steps are completed. Otherwise, the serial number should be corrected to prevent inconsistent data."
msgstr ""
"Sériové číslo (%s) se již používá v těchto lokacích: %s.\n"
"\n"
"Očekává se to? Například k tomu může dojít, pokud je operace doručení ověřena před ověřením její odpovídající operace příjmu. V tomto případě bude problém vyřešen automaticky po dokončení všech kroků. V opačném případě by mělo být sériové číslo opraveno, aby se zabránilo nekonzistentním údajům."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"The backorder <a href=# data-oe-model=stock.picking data-oe-id=%d>%s</a> has"
" been created."
msgstr ""
"Doobjednávka <a href=# data-oe-model=stock.picking data-oe-id=%d>%s</a> byla"
" vytvořena."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_barcode_company_uniq
msgid "The barcode for a location must be unique per company !"
msgstr "Čárový kód pro umístění musí být pro každou společnost jedinečný!"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"The client explicitly waives its own standard terms and conditions, even if "
"these were drawn up after these standard terms and conditions of sale. In "
"order to be valid, any derogation must be expressly agreed to in advance in "
"writing."
msgstr ""
"Klient se výslovně vzdává svých vlastních standardních obchodních podmínek, "
"a to i v případě, že tyto byly sepsány až po těchto standardních obchodních "
"podmínkách. Aby byla jakákoli odchylka platná, musí být předem výslovně "
"písemně odsouhlasena."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_product_location_check
msgid "The combination of product and location must be unique."
msgstr "Kombinace produktu a místa musí být jedinečná."

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"The combination of serial number and product must be unique across a company.\n"
"Following combination contains duplicates:\n"
msgstr ""
"Kombinace sériového čísla a produktu musí být unikátní napříč společností.\n"
"Následující kombinace obsahuje duplikáty:\n"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__company_id
msgid "The company is automatically set from your user preferences."
msgstr "Společnost je automaticky nastavena z vašich uživatelských předvoleb."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"The deadline has been automatically updated due to a delay on <a href='#' "
"data-oe-model='%s' data-oe-id='%s'>%s</a>."
msgstr ""
"Termín bude automaticky aktualizován z důvodu prodlení na <a href='#' data-"
"oe-model='%s' data-oe-id='%s'>%s</a>."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__delay
msgid ""
"The expected date of the created transfer will be computed based on this "
"lead time."
msgstr ""
"Očekávané datum vytvořeného převodu bude vypočítáno na základě tohoto "
"dodacího času."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__sequence
msgid "The first in the sequence is the default one."
msgstr "První v pořadí je výchozí."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "The forecasted stock on the"
msgstr "Předpokládané zásoby na"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_inventory_freq_nonneg
msgid "The inventory frequency (days) for a location must be non-negative"
msgstr "Frekvence inventury (dny) pro umístění nesmí být záporná"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "The lot name must contain at least one digit."
msgstr "Název šarže musí obsahovat alespoň jednu číslici."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_name_uniq
msgid "The name of the warehouse must be unique per company!"
msgstr "Název skladu musí být pro každou společnost jedinečný!"

#. module: stock
#: code:addons/stock/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "The number of Serial Numbers to generate must be greater than zero."
msgstr "Počet generovaných sériových čísel musí být větší než nula."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"                operation a specific type which will alter its views accordingly.\n"
"                On the operation type you could e.g. specify if packing is needed by default,\n"
"                if it should show the customer."
msgstr ""
"Operace typu Systém vám umožňuje přidělit každé skladové\n"
"                operaci specifický typ, který adekvátně upraví její zobrazení.\n"
"                Na typu výběru můžete např. specifikovat, které balení je potřeba standardně,\n"
"                nebo zda by mělo být ukázané zákazníkovi."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__package_id
msgid "The package containing this quant"
msgstr "Balení obsahující toto množství"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr ""
"Nadřazené umístění, které zahrnuje toto umístění. Příklad: \"Dispečerská "
"zóna\" je nadřazená lokace \"Brána 1\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid ""
"The procurement quantity will be rounded up to this multiple.  If it is 0, "
"the exact quantity will be used."
msgstr ""
"Množství zakázky bude zaokrouhleno na tento násobek. Pokud je 0, použije se "
"přesné množství."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "The product is not available in sufficient quantity"
msgstr "Produkt není dostupný v dostatečném množství"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_quantity
msgid "The product's counted quantity."
msgstr "Počítané množství produktu."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"The quantity done for the product \"%s\" doesn't respect the rounding "
"precision defined on the unit of measure \"%s\". Please change the quantity "
"done or the rounding precision of your unit of measure."
msgstr ""
"Množství provedené pro produkt \"%s\" nerespektuje přesnost zaokrouhlení "
"definovanou v měrné jednotce \"%s\". Změňte prosím provedené množství nebo "
"přesnost zaokrouhlení vaší měrné jednotky."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""
"Požadovanou operaci nelze zpracovat z důvodu programovací chyby při "
"nastavení pole `product_qty` namísto` product_uom_qty`."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"The selected Inventory Frequency (Days) creates a date too far into the "
"future."
msgstr ""
"Vybraná frekvence inventury (dny) vytváří datum příliš vzdálené do "
"budoucnosti."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"The serial number has already been assigned: \n"
" Product: %s, Serial Number: %s"
msgstr ""
"Sériové číslo bylo již přiřazeno:\n"
" Produkt: %s, sériové číslo: %s"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_code_uniq
msgid "The short name of the warehouse must be unique per company!"
msgstr "Krátký název skladu musí být pro každou společnost jedinečný!"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,help:stock.field_res_users__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr ""
"Lokace skladu používaná jako cíl při dodání výrobků tomuto partnerovi."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,help:stock.field_res_users__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr ""
"Lokace skladu používaná jako zdroj při dodání výrobků od tohoto partnera."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr "Skladová operace, kde bylo provedeno balení"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__rule_id
msgid "The stock rule that created this stock move"
msgstr "Pravidla skladu, které vytvořilo tento pohyb zásob"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid ""
"The stock will be reserved for operations waiting for availability and the "
"reordering rules will be triggered."
msgstr ""
"Akcie budou vyčleněny na operace čekající na dostupnost a budou spuštěny "
"pravidla pro přesměrování."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""
"Sklad, který se má zřídit na vytvořeném přesunu / veřejné zakázce, který se "
"může lišit od skladu, pro který je toto pravidlo určeno (např. pro pravidla "
"doplňování z jiného skladu)"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid "There's no product move yet"
msgstr "Zatím není žádný produkt"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"This analysis gives you an overview of the current stock level of your "
"products."
msgstr ""
"Tato analýza vám poskytne přehled o aktuálním stavu zásob vašich produktů."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__name
msgid "This field will fill the packing origin and the name of its moves"
msgstr "Toto pole vyplní původ balení a jméno jeho pohybu"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reservation_date
msgid ""
"This is a technical field for calculating when a move should be reserved"
msgstr "Toto je technické pole pro výpočet, kde by měl být přesun rezervován"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_dest_id
msgid ""
"This is the default destination location when you create a picking manually "
"with this operation type. It is possible however to change it or that the "
"routes put another location. If it is empty, it will check for the customer "
"location on the partner. "
msgstr ""
"Toto je výchozí cílové umístění, když vytváříte vychystávání ručně s tímto "
"typem operace. Je však možné ji změnit, nebo že cesty změní na jiné místo. "
"Pokud je prázdné, zkontroluje polohu zákazníka u partnera."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_src_id
msgid ""
"This is the default source location when you create a picking manually with "
"this operation type. It is possible however to change it or that the routes "
"put another location. If it is empty, it will check for the supplier "
"location on the partner. "
msgstr ""
"Toto je výchozí umístění zdroje, když manuálně vybíráte tento typ operace. "
"Je však možné jej změnit nebo že trasy mají jinou polohu. Je-li prázdný, "
"zkontroluje umístění dodavatele u partnera."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__owner_id
msgid "This is the owner of the quant"
msgstr "Toto je vlastník kvanta"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_qty
msgid ""
"This is the quantity of products from an inventory point of view. For moves "
"in the state 'done', this is the quantity of products that were actually "
"moved. For other moves, this is the quantity of product that is planned to "
"be moved. Lowering this quantity does not generate a backorder. Changing "
"this quantity on assigned moves affects the product reservation, and should "
"be done with care."
msgstr ""
"Jedná se o množství produktů z hlediska zásob. Pro pohyby ve stavu "
"\"hotovo\" se jedná o množství produktů, které byly skutečně přesunuty. U "
"jiných pohybů se jedná o množství produktu, které má být přesunuto. "
"Snižování tohoto množství nevytváří zpětnou objednávku. Změna tohoto "
"množství při přiřazených pohybech ovlivňuje rezervaci produktu a měla by se "
"provádět opatrně."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__child_internal_location_ids
msgid ""
"This location (if it's internal) and all its descendants filtered by "
"type=Internal."
msgstr ""
"Tato lokace (pokud je interní) a všechny její potomky filtrovány podle "
"type=Internal."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"This location's usage cannot be changed to view as it contains products."
msgstr "Použití tohoto umístění nelze změnit, protože obsahuje produkty."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"This lot %(lot_name)s is incompatible with this product %(product_name)s"
msgstr ""
"Tato šarže %(lot_name)s je nekompatibilní s tímto produktem %(product_name)s"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the product\n"
"                to see all the past or future movements for the product."
msgstr ""
"Toto menu vám dává plnou sledovatelnost operací inventáře na konkrétním "
"produktu. Můžete filtrovat produkt a zobrazit všechny minulé nebo budoucí "
"pohyby produktu."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                    You can filter on the product to see all the past movements for the product."
msgstr ""
"Tato nabídka vám poskytuje úplnou sledovatelnost inventarizačních operací u konkrétního produktu.\n"
"                       Můžete filtrovat produkt, abyste viděli všechny minulé pohyby produktu."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "This note is added to delivery orders."
msgstr "Tato poznámka se přidává k dodacím objednávkám."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to internal transfer orders (e.g. where to pick the "
"product in the warehouse)."
msgstr ""
"Tato poznámka se přidává k interním převodním objednávkám (např. kde si "
"vybrat produkt ve skladu)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to receipt orders (e.g. where to store the product in the"
" warehouse)."
msgstr ""
"Tato poznámka se přidává k přijímacím objednávkám (např. kam uložit produkt "
"ve skladu)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid ""
"This picking appears to be chained with another operation. Later, if you "
"receive the goods you are returning now, make sure to <b>reverse</b> the "
"returned picking in order to avoid logistic rules to be applied again (which"
" would create duplicated operations)"
msgstr ""
"Zdá se, že tento výběr je zřetězený s jinou operací. Pokud později obdržíte "
"zboží, které nyní vracíte, ujistěte se, že jste vrácenou vychystávku "
"<b>obrátili</b>, abyste se vyhnuli opětovnému použití logistických pravidel "
"(což by vytvořilo duplicitní operace)."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product has been used in at least one inventory movement. It is not "
"advised to change the Product Type since it can lead to inconsistencies. A "
"better solution could be to archive the product and create a new one "
"instead."
msgstr ""
"Tento produkt byl použit alespoň v jednom pohybu zásob. Nedoporučuje se "
"měnit typ produktu, protože to může vést k nesrovnalostem. Lepším řešením by"
" mohlo být archivovat produkt a místo toho vytvořit nový."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product's company cannot be changed as long as there are quantities of "
"it belonging to another company."
msgstr ""
"Společnost tohoto produktu nelze změnit, pokud existuje množství patřící "
"jiné společnosti."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product's company cannot be changed as long as there are stock moves of"
" it belonging to another company."
msgstr ""
"Společnost tohoto produktu nelze změnit, pokud existuje pohyb zásob patřící "
"jiné společnosti."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr "Množství je vyjádřeno ve výchozí měrné jednotce výrobku."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/inventory_singleton_list_controller.js:0
#, python-format
msgid "This record already exists."
msgstr "Tento záznam již existuje."

#. module: stock
#: code:addons/stock/report/report_stock_reception.py:0
#, python-format
msgid ""
"This report cannot be used for done and not done transfers at the same time"
msgstr "Tento výkaz nelze použít pro provedené a neprovedené převody současně"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_production
#: model:ir.model.fields,help:stock.field_product_template__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""
"Toto umístění zásob bude použito místo výchozího jako zdrojové umístění pro "
"pohyby zásob vytvořené na základě výrobních příkazů."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""
"Toto umístění zásob bude použito místo výchozího jako zdrojové umístění "
"pohybů vytvořených na základě inventury."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__responsible_id
#: model:ir.model.fields,help:stock.field_product_template__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr ""
"Tento uživatel bude zodpovědný za další činnosti související s logistickými "
"operacemi pro tento produkt."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
msgid "This will discard all unapplied counts, do you want to proceed?"
msgstr "Tímto zahodíte všechny neuplatněné počty, chcete pokračovat?"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_0
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid "Tip: Speed up inventory operations with barcodes"
msgstr "Tip: Zrychlete operace se zásobami využitím čárových kódů"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "To"
msgstr "Do"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Apply"
msgstr "K použití"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__to_backorder
msgid "To Backorder"
msgstr "K doobjednání"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Count"
msgstr "K počtu"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "To Do"
msgstr "K udělání"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order
msgid "To Order"
msgstr "K objednání"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__to_immediate
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Process"
msgstr "Zpracovat"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "To Reorder"
msgstr "Ke změně pořadí"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Today"
msgstr "Dnes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today Activities"
msgstr "Dnešní činnosti"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__total_route_ids
msgid "Total routes"
msgstr "Celkem trasy"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Traceability"
msgstr "Sledovatelnost"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/stock_traceability_report_widgets.js:0
#: model:ir.actions.client,name:stock.action_stock_report
#: model:ir.model,name:stock.model_stock_traceability_report
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#, python-format
msgid "Traceability Report"
msgstr "Zpráva o sledovatelnosti"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr ""
"Sledujte následující data u produktových a sériových čísel: minimální trvanlivost, odstranění, konec životnosti, upozornění. \n"
"Taková data jsou nastavena automaticky při tvorbě produktového / sériového čísla na základě hodnot stanovených v produktu (ve dnech)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of"
" life, alert. Such dates are set automatically at lot/serial number creation"
" based on values set on the product (in days)."
msgstr ""
"Sledujte následující data u produktových  a sériových čísel: minimální "
"trvanlivost, odstranění, konec životnosti, upozornění. Taková data jsou "
"nastavena automaticky při tvorbě produktového / sériového čísla na základě "
"hodnot stanovených v produktu (ve dnech)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Track product location in your warehouse"
msgstr "Sledujte umístění produktu ve vašem skladu"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "Track your stock quantities by creating storable products."
msgstr "Sledujte své skladové zásoby vytvářením skladovatelných produktů."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Tracked Products in Inventory Adjustment"
msgstr "Sledované produkty v úpravě zásob"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__tracking
#: model:ir.model.fields,field_description:stock.field_product_template__tracking
#: model:ir.model.fields,field_description:stock.field_stock_move_line__tracking
#: model:ir.model.fields,field_description:stock.field_stock_quant__tracking
#: model:ir.model.fields,field_description:stock.field_stock_track_line__tracking
msgid "Tracking"
msgstr "Sledování"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__tracking_line_ids
msgid "Tracking Line"
msgstr "Sledovací čára"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Transfer"
msgstr "Převod"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__picking_quantity__picking
msgid "Transfer Quantities"
msgstr "Převést množství"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Transfer to"
msgstr "Přenést do"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__delivery_ids
#: model:ir.ui.menu,name:stock.all_picking
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Transfers"
msgstr "Přesuny"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Transfers %s: Please add some items to move."
msgstr "Přesuny %s: Přidejte některé položky, které chcete přesunout."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "Transfers allow you to move products from one location to another."
msgstr "Převody umožňují přesouvat produkty z jednoho místa na druhé."

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Transfers for Groups"
msgstr "Přesuny pro skupiny"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid ""
"Transfers that are late on scheduled time or one of pickings will be late"
msgstr ""
"Přesuny, které se zpozdí v plánovaném čase nebo některý z dodání, budou mít "
"zpoždění"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__transit
msgid "Transit Location"
msgstr "tranzitní umístění"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr "Lokace přesunu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__trigger
msgid "Trigger"
msgstr "Spouštěč"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_order
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule"
msgstr "Spustit další pravidlo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule If No Stock"
msgstr "Spustit další pravidlo pokud nejsou zásoby"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_forecasted.js:0
#, python-format
msgid "Try to add some incoming or outgoing transfers."
msgstr "Zkuste přidat některé příchozí nebo odchozí přenosy."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_barcode_rule__type
#: model:ir.model.fields,field_description:stock.field_product_product__type
#: model:ir.model.fields,field_description:stock.field_product_template__type
msgid "Type"
msgstr "Typ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__code
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Type of Operation"
msgstr "Typ operace"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_decoration
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ zaznamenané výjimečné aktivity."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "UPS konektor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "USPS konektor"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_reception.js:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Unassign"
msgstr "Nepřiřazovat"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#, python-format
msgid "Unfold"
msgstr "Rozbalit"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__name
msgid "Unique Lot/Serial Number"
msgstr "Jedinečné číslo šarže / sériové číslo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__price_unit
msgid "Unit Price"
msgstr "Jednotková cena"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_uom_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_uom_name
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Unit of Measure"
msgstr "Měrná jednotka"

#. module: stock
#: model:product.product,uom_name:stock.product_cable_management_box
#: model:product.template,uom_name:stock.product_cable_management_box_product_template
msgid "Units"
msgstr "Jednotky"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Units Of Measure"
msgstr "Měrné jednotky"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
msgid "Units of Measure"
msgstr "Měrné jednotky"

#. module: stock
#: model:ir.ui.menu,name:stock.product_uom_menu
msgid "Units of Measures"
msgstr "Měrné jednotky"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_id
msgid "Unit of measure"
msgstr "Měrná jednotka"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Unknown Pack"
msgstr "Neznámé balení"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Unknown stream."
msgstr "Neznámý stream."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Unlock"
msgstr "Odemknout"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr "Rozbalit"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_unread
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_unread
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_unread
msgid "Unread Messages"
msgstr "Nepřečtené zprávy"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_unread_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_unread_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Počítadlo nepřečtených zpráv"

#. module: stock
#: model:ir.actions.server,name:stock.action_unreserve_picking
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Unreserve"
msgstr "Zrušit rezervaci"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "UoM"
msgstr "UoM"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "UoM Categories"
msgstr "Kategorie MJ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr "Aktualizovat množství výrobků"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#, python-format
msgid "Update Quantity"
msgstr "Aktualizovat množství"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__1
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__1
msgid "Urgent"
msgstr "Naléhavé"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr "Použít existující šarže / sériová čísla"

#. module: stock
#: model:res.groups,name:stock.group_reception_report
msgid "Use Reception Report"
msgstr "Použít výkaz příjmu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid ""
"Use this assistant to replenish your stock.\n"
"                Depending on your product configuration, launching a replenishment may trigger a request for quotation,\n"
"                a manufacturing order or a transfer."
msgstr ""
"Použijte tohoto pomocníka k doplnění zásob.\n"
"                V závislosti na nastavení vašeho produktu může spuštění doplňování vyvolat požadavek na cenovou nabídku,\n"
"                výrobní zakázku nebo přesun."

#. module: stock
#: model:res.groups,name:stock.group_stock_picking_wave
msgid "Use wave pickings"
msgstr "Použít skupinové vyzvednutí"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Use your own routes"
msgstr "Použijte vlastní trasy"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Used by"
msgstr "Použito od"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr "Používané pro objednání 'Všechny operace' kanban zobrazení"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__user_id
#: model:res.groups,name:stock.group_stock_user
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "User"
msgstr "Uživatel"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__user_id
msgid "User assigned to do product count."
msgstr "Uživatel přiřazený k počítání produktů."

#. module: stock
#: model:ir.actions.server,name:stock.action_validate_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "Ověřit"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/inventory_lines.xml:0
#, python-format
msgid "Validate Inventory"
msgstr "Ověřit inventář"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_variant_count
msgid "Variant Count"
msgstr "Počet variant"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr "Dodavatel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_supplier
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__supplier
msgid "Vendor Location"
msgstr "Umístění dodavatele"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr "Umístění dodavatele"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__view
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "View"
msgstr "Pohled"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "View Diagram"
msgstr "Zobrazit diagram"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__view_location_id
msgid "View Location"
msgstr "Zobrazit umístění"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "View and allocate received quantities."
msgstr "Zobrazit a alokovat přijatá množství."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__confirmed
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting"
msgstr "Čekající"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__waiting
msgid "Waiting Another Move"
msgstr "Očekáván další pohyb"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__waiting
msgid "Waiting Another Operation"
msgstr "Očekávána další operace"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__confirmed
msgid "Waiting Availability"
msgstr "Čeká se na dostupnost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr "Čekající pohyby"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr "Čekající přesuny"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_product_product__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_replenish__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template__warehouse_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "Sklad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr "Nastavení skladu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_domain_ids
msgid "Warehouse Domain"
msgstr "Doména skladu"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "Správa skladů"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_view_ids
msgid "Warehouse View"
msgstr "Zobrazení skladu"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr "Sklad na propagování"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse view location"
msgstr "Zobrazení lokace skladu"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Warehouse's Routes"
msgstr "Trasy skladu"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#, python-format
msgid "Warehouse:"
msgstr "Sklad:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_ids
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Warehouses"
msgstr "Sklady"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty
msgid "Warn Insufficient Quantity"
msgstr "Varovat při nedostatečném množství"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty_scrap
msgid "Warn Insufficient Scrap Quantity"
msgstr "Varování při nedostatečném vyřazovaném množství"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_quant.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__warning
#, python-format
msgid "Warning"
msgstr "Varování"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Warning on the Picking"
msgstr "Upozornění na výdejce"

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Warning!"
msgstr "Varování!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Warnings"
msgstr "Varování"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr "Upozornění pro skladové zásoby"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_picking_wave
msgid "Wave Transfers"
msgstr "Skupinové přesuny"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__website_message_ids
msgid "Website Messages"
msgstr "Zprávy webstránky"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_production_lot__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_scrap__website_message_ids
msgid "Website communication history"
msgstr "Historie komunikace webstránky"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Štítek měrné jednotky hmotnosti"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Vážený produkt"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse."
msgstr ""
"Když je pro tuto trasu vybrán sklad, měla by být tato trasa považována za "
"výchozí trasu, pokud produkty procházejí tímto skladem."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__one
msgid "When all products are ready"
msgstr "Když jsou všechny produkty připraveny"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form."
msgstr ""
"Po zaškrtnutí bude možné trasu vybrat v záložce Inventář ve formuláři "
"Produkt."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__product_categ_selectable
msgid "When checked, the route will be selectable on the Product Category."
msgstr "Po zaškrtnutí bude možné trasu vybrat v Kategorii produktu."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__packaging_selectable
msgid "When checked, the route will be selectable on the Product Packaging."
msgstr "Po zaškrtnutí bude možné zvolit trasu v Balení produktu."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_in_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "When product arrives in"
msgstr "Kdy produkt dorazí do"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> <b>%s</b> are created from "
"<b>%s</b> to fulfill the need."
msgstr ""
"Když jsou produkty potřeba v <b>%s</b>, <br/>vytvoří se <b>%s</b> z "
"<b>%s</b> ke splnění objednávky."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products arrive in <b>%s</b>, <br/> <b>%s</b> are created to send them "
"in <b>%s</b>."
msgstr ""
"Když produkty dorazí do <b>%s</b>, <br/>bude vytvořeno <b>%s</b> na zaslání "
"do <b>%s</b>."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__is_locked
msgid ""
"When the picking is not done this allows changing the initial demand. When "
"the picking is done this allows changing the done quantities."
msgstr ""
"Pokud není vyskladnění provedeno, umožní to změnu počáteční poptávky. Po "
"vyskladnění to umožňuje změnu provedených množství."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field,"
" Odoo generates a procurement to bring the forecasted quantity to the Max "
"Quantity."
msgstr ""
"Když virtuální zásoba klesne pod minimální množství určené pro toto pole, "
"Odoo generuje zakázku, aby přivedl odhadované množství na maximální "
"množství."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity to the Quantity specified as "
"Max Quantity."
msgstr ""
"Když virtuální zásoba klesne pod Min Množství, Odoo vygeneruje zakázku, "
"která uvede předpokládané množství na Množství určené jako Max Množství."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_carrier
msgid "When ticked, carrier of shipment will be propgated."
msgstr "Po zaškrtnutí bude přepravce zásilky propagován."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_cancel
msgid ""
"When ticked, if the move created by this rule is cancelled, the next move "
"will be cancelled too."
msgstr ""
"Když zaškrtnete, pokud je přesun vytvořený tímto pravidlem zrušen, bude "
"zrušen i další přesun."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__owner_id
msgid ""
"When validating the transfer, the products will be assigned to this owner."
msgstr "Po ověření převodu budou produkty přiděleny tomuto vlastníkovi."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__owner_id
msgid ""
"When validating the transfer, the products will be taken from this owner."
msgstr "Po ověření převodu budou produkty převzaty od tohoto vlastníka."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__additional
msgid "Whether the move was added after the picking's confirmation"
msgstr "Zda byl přesun přidán po potvrzení vyzvednutí"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__width
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Width"
msgstr "Šířka"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_width
msgid "Width must be positive"
msgstr "Šířka musí být kladné číslo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__wizard_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__wizard_id
msgid "Wizard"
msgstr "Průvodce"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Write your SN/LN one by one or copy paste a list."
msgstr "Napište své SN/LN jeden po druhém nebo zkopírujte a vložte seznam."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid "You are good, no replenishment to perform!"
msgstr "Jste dobří, žádné doplňování k výkonu!"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"You are not allowed to change the product linked to a serial or lot number "
"if some stock moves have already been created with that number. This would "
"lead to inconsistencies in your stock."
msgstr ""
"Není povoleno měnit produkt spojený se sériovým číslem nebo číslem šarže, "
"pokud již byly s tímto číslem vytvořeny některé pohyby zásob. To by vedlo k "
"nesrovnalostem ve vašich zásobách."

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"You are not allowed to create a lot or serial number with this operation "
"type. To change this, go on the operation type and tick the box \"Create New"
" Lots/Serial Numbers\"."
msgstr ""
"Při tomto typu operace není možné vytvářet produktové  nebo sériové číslo. "
"Chcete-li to změnit, přejděte na typ operace a zaškrtněte políčko „Vytvořit "
"nové produktové  / sériové číslo“."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid ""
"You are trying to put products going to different locations into the same "
"package"
msgstr "Pokoušíte se zadat produkty na rozdílné lokace do jednoho balíku"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You are using a unit of measure smaller than the one you are using in order "
"to stock your product. This can lead to rounding problem on reserved "
"quantity. You should use the smaller unit of measure possible in order to "
"valuate your stock or change its rounding precision to a smaller value "
"(example: 0.00001)."
msgstr ""
"Používáte měrnou jednotku, která je menší než ta, kterou používáte k nákupu "
"produktu. To může vést k problémům zaokrouhlení na vyhrazené množství. Měli "
"byste použít menší měrnou jednotku, aby bylo možné vyhodnotit vaše skladové "
"zásoby nebo změnit její přesnost zaokrouhlením na menší hodnotu (příklad: "
"0.00001)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                your warehouses and that define the flows of your products. These\n"
"                routes can be assigned to a product, a product category or be fixed\n"
"                on procurement or sales order."
msgstr ""
"Zde můžete definovat hlavní trasy, které vedou přes\n"
"                vaše sklady a které definují vaše produkty. Tyto trasy mohou\n"
"                být přiděleny produktu, kategorii produktu nebo být fixované na\n"
"                nákup nebo prodejní objednávku."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "You can either :"
msgstr "Můžete buď:"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You can not change the type of a product that is currently reserved on a "
"stock move. If you need to change the type, you should first unreserve the "
"stock move."
msgstr ""
"Nemůžete změnit typ produktu, který je v současné době rezervován v pohybu "
"zásob. Pokud potřebujete změnit typ, měli byste nejdříve rezervovat v pohybu"
" zásob."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "You can not change the type of a product that was already used."
msgstr "Nemůžete změnit typ produktu, který byl již použit."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You can not delete product moves if the picking is done. You can only "
"correct the done quantities."
msgstr ""
"Nemůžete smazat pohyby produktu, pokud byl již dodán. Můžete opravit pouze "
"provedené množství."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "You can not enter negative quantities."
msgstr "Nemůžete zadat záporná množství."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You can only delete draft moves."
msgstr "Můžete smazat pouze návrhové přesuny."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "You can only process 1.0 %s of products with unique serial number."
msgstr ""
"Můžete zpracovat pouze 1.0 %s produktů s jednoznačným sériovým číslem."

#. module: stock
#: code:addons/stock/models/res_config_settings.py:0
#, python-format
msgid ""
"You can't desactivate the multi-location if you have more than once "
"warehouse by company"
msgstr ""
"Pokud máte více než jeden sklad podle společnosti, nemůžete deaktivovat více"
" lokalit."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid "You cannot archive the location %s as it is used by your warehouse %s"
msgstr "Umístění nemůžete archivovat %s jako to používá váš sklad %s"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot cancel a stock move that has been set to 'Done'. Create a return "
"in order to reverse the moves which took place."
msgstr ""
"Nemůžete zrušit akciový přesun, který byl nastaven na 'Hotovo'. Vytvořte "
"návrat, abyste zvrátili provedené pohyby."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "You cannot change the Scheduled Date on a done or cancelled transfer."
msgstr ""
"Naplánované datum nemůžete změnit u provedeného nebo zrušeného převodu."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot change the UoM for a stock move that has been set to 'Done'."
msgstr "Nemůžete změnit MJ u pohybu zásob, která byla nastavena na \"Hotovo\"."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"You cannot change the location type or its use as a scrap location as there "
"are products reserved in this location. Please unreserve the products first."
msgstr ""
"Nelze změnit typ umístění ani jeho použití jako odkladiště, protože v tomto "
"místě jsou rezervovány produkty. Nejdříve prosím u produktů zrušte "
"rezervaci."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You cannot change the ratio of this unit of measure as some products with "
"this UoM have already been moved or are currently reserved."
msgstr ""
"Poměr této měrné jednotky nemůžete změnit, protože některé produkty s touto "
"MJ již byly přesunuty nebo jsou aktuálně rezervovány."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You cannot change the unit of measure as there are already stock moves for "
"this product. If you want to change the unit of measure, you should rather "
"archive this product and create a new one."
msgstr ""
"Nemůžete změnit měrnou jednotku, protože pro tento produkt již existují "
"pohyby zásob. Pokud chcete změnit měrnou jednotku, měli byste tento produkt "
"spíše archivovat a vytvořit nový."

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#, python-format
msgid "You cannot delete a scrap which is done."
msgstr "Nemůžete odstranit zmetek, který je hotový."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "You cannot modify inventory loss quantity"
msgstr "Nelze upravit množství úbytku zásob"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot move the same package content more than once in the same transfer"
" or split the same package into two location."
msgstr ""
"Nelze přesunout stejný obsah balíku více než jednou ve stejném přenosu nebo "
"rozdělit stejný balíček do dvou umístění."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot perform the move because the unit of measure has a different "
"category as the product unit of measure."
msgstr ""
"Přesun nemůžete provést, protože měrná jednotka má jinou kategorii než měrná"
" jednotka produktu."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr "Nelze rozdělit návrh přesunu. Nejprve je třeba ho potvrdit."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot split a stock move that has been set to 'Done' or 'Cancel'."
msgstr ""
"Nemůžete rozdělit rezervaci u změn ve skladu, která byla nastavena na "
"\"Hotovo\" nebo \"Zrušit\"."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"You cannot take products from or deliver products to a location of type "
"\"view\" (%s)."
msgstr ""
"Nemůžete odebírat produkty ani je doručovat do umístění typu „zobrazení“ "
"(%s)."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot unreserve a stock move that has been set to 'Done'."
msgstr ""
"Nemůžete zrušit rezervaci u změn ve skladu, která byla nastavena na "
"\"Hotovo\"."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot change a cancelled stock move, create a new line instead."
msgstr "Nemůžete měnit zrušený pohyb zásob, místo toho vytvořte nový řádek."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You cannot use the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr "Stejné sériové číslo nemůžete použít dvakrát. Opravte sériové číslo."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You cannot validate a transfer if no quantities are reserved nor done. To "
"force the transfer, switch in edit mode and encode the done quantities."
msgstr ""
"Tento převod nelze potvrdit, pokud není rezervováno ani provedeno žádné "
"množství. Chcete-li tento převod vynutit, přepněte se do režimu editace a "
"zakódujte hotová množství."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid ""
"You have manually created product lines, please delete them to proceed."
msgstr "Ručně jste vytvořili produktové trasy, pokračujte prosím odstraněním."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid ""
"You have not recorded <i>done</i> quantities yet, by clicking on "
"<i>apply</i> Odoo will process all the quantities."
msgstr ""
"Ještě jste nezaznamenali <i>provedená</i> množství, kliknutím na "
"<i>použít</i> Odoo zpracuje všechna množství."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr "Zpracovali jste méně produktů než je původní poptávka."

#. module: stock
#: code:addons/stock/models/res_config_settings.py:0
#, python-format
msgid ""
"You have product(s) in stock that have lot/serial number tracking enabled. \n"
"Switch off tracking on all the products before switching off this setting."
msgstr ""
"Máte na skladě produkty, které mají povoleno sledování šarží/sériových čísel. \n"
"Před vypnutím tohoto nastavení vypněte sledování u všech produktů."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You have product(s) in stock that have no lot/serial number. You can assign "
"lot/serial numbers by doing an inventory adjustment."
msgstr ""
"Máte na skladě produkt(y), který nemá číslo šarže / sériové číslo. Čísla "
"šarží / sériových čísel můžete přiřadit úpravou zásob."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You have to define a groupby and sorted method and pass them as arguments."
msgstr ""
"Musíte definovat skupinovou a třídící metodu a zpracovat je jako argumenty."

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid ""
"You have to select a product unit of measure that is in the same category as"
" the default unit of measure of the product"
msgstr ""
"Musíte vybrat měrnou jednotku produktu, která je ve stejné kategorii jako je"
" výchozí měrná jednotka produktu"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/inventory_singleton_list_controller.js:0
#, python-format
msgid ""
"You have tried to create a record which already exists. The existing record "
"has been modified instead."
msgstr ""
"Pokusili jste se vytvořit záznam, který již existuje. Místo toho byl změněn "
"stávající záznam."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "You may only return Done pickings."
msgstr "Můžete vrátit pouze dokončené výběry."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "You may only return one picking at a time."
msgstr "Vrátit můžete pouze jedno vychystávání najednou."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You need to activate storage locations to be able to do internal operation "
"types."
msgstr "Pro použití interních převodů je potřeba aktivovat lokaci skladu."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You need to set a Serial Number before generating more."
msgstr "Než vygenerujete další, musíte nastavit sériové číslo."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You need to supply a Lot/Serial Number for product: \n"
" - "
msgstr ""
"U výrobku je třeba uvést výrobní číslo / sériové číslo: \n"
" - "

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "You need to supply a Lot/Serial number for products %s."
msgstr "U výrobků je třeba uvést výrobní číslo / sériové číslo %s."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "You should update this document to reflect your T&amp;C."
msgstr "Tento dokument byste měli aktualizovat tak, aby odrážel vaše T&amp;C."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "You still have ongoing operations for picking types %s in warehouse %s"
msgstr "Stále máte probíhající operace pro vyskladnění typů %s ve skladu %s"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You still have some active reordering rules on this product. Please archive "
"or delete them first."
msgstr ""
"Na tomto produktu stále existují některá aktivní pravidla přeskupování. "
"Nejdříve je prosím archivujte nebo smažte."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid "You still have some product in locations %s"
msgstr "V lokaci %s se stále nacházejí produkty"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid ""
"You'll find here smart replenishment propositions based on inventory forecasts.\n"
"            Choose the quantity to buy or manufacture and launch orders in a click.\n"
"            To save time in the future, set the rules as \"automated\"."
msgstr ""
"Najdete zde návrhy chytrého doplňování založené na prognózách zásob.\n"
"            Vyberte množství, které chcete koupit nebo vyrobit, a spusťte objednávky jediným kliknutím.\n"
"            Chcete-li v budoucnu ušetřit čas, nastavte pravidla jako „automatizovaná“."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Your stock is currently empty"
msgstr "Vaše zásoby jsou aktuálně prázdné"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zpl
msgid "ZPL Labels"
msgstr "Štítky ZPL"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zplxprice
msgid "ZPL Labels with price"
msgstr "Štítky ZPL s cenou"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "]<br/>min:"
msgstr "]<br/>min:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "_Cancel"
msgstr "_Zrušit"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "below the inventory"
msgstr "pod inventářem"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "bpost konektor"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "days"
msgstr "dnů"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before when starred"
msgstr "dny předtím, pokud byly s hvězdičkou"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before/"
msgstr "dnů před/"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. CW"
msgstr "např. CW"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. Central Warehouse"
msgstr "např. Centrální sklad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr "např. LOT/0001/20121"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "e.g. PACK0000007"
msgstr "např. PACK0000007"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr "např. PO0032"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Physical Locations"
msgstr "např. Fyzické umístění"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Spare Stock"
msgstr "např. Náhradní sklad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "e.g. Two-steps reception"
msgstr "např. Příjem ve dvou krocích"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "from location"
msgstr "z místa"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "in"
msgstr "v "

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "is"
msgstr "je"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "manually to trigger the reordering rules right now."
msgstr "Teď hned ručně spustit pravidla přesměrování."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "minimum of"
msgstr "minimum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "of"
msgstr "z "

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/popover_widget.xml:0
#, python-format
msgid "planned on"
msgstr "plánováno na"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "processed instead of"
msgstr "zpracováno namísto "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_graph
msgid "report_stock_quantity_graph"
msgstr "report_stock_quantity_graph"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "should be replenished"
msgstr "mělo by být doplněno"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "to reach the maximum of"
msgstr "dosažení maxima"

#. module: stock
#: model:mail.template,report_name:stock.mail_template_data_delivery_confirmation
msgid "{{ (object.name or '').replace('/','_') }}"
msgstr "{{ (object.name or '').replace('/','_') }}"

#. module: stock
#: model:mail.template,subject:stock.mail_template_data_delivery_confirmation
msgid ""
"{{ object.company_id.name }} Delivery Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} Objednávka (Ref {{ object.name or 'n/a' }})"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "Global Visibility Days"
msgstr "Dny globální viditelnosti"
