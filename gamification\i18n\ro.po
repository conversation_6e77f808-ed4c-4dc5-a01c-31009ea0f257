# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* gamification
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:55+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__rank_users_count
msgid "# Users"
msgstr "# Utilizatori"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "%s has joined the challenge"
msgstr "%ss-a alăturat provocării"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "%s has refused the challenge"
msgstr "%sa refuzat provocarea"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "<br/> %(rank)d. %(user_name)s - %(reward_name)s"
msgstr "<br/> %(rank)d. %(user_name)s - %(reward_name)s"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid ""
"<br/>Nobody has succeeded to reach every goal, no badge is rewarded for this"
" challenge."
msgstr ""
"<br/>Nimeni nu a reușit să atingă fiecare obiectiv, nici o insignă nu este "
"recompensată pentru această provocare."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid ""
"<br/>Reward (badge %(badge_name)s) for every succeeding user was sent to "
"%(users)s."
msgstr ""
"<br/>Recompensă (insignă%(badge_name)s) pentru fiecare utilizator succesor a"
" fost trimis la %(users)s."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid ""
"<br/>Special rewards were sent to the top competing users. The ranking for "
"this challenge is :"
msgstr ""
"<br/>Recompense speciale au fost trimise utilizatorilor concurenți de top. "
"Clasamentul pentru această provocare este:"

#. module: gamification
#: model:mail.template,body_html:gamification.mail_template_data_new_rank_reached
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"<table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"    <tbody>\n"
"        <tr>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>\n"
"                    Congratulations\n"
"                    <span t-out=\"object.name or ''\">Joel Willis</span>!\n"
"                </p>\n"
"                <p>\n"
"                    You just reached a new rank : <strong t-out=\"object.rank_id.name or ''\">Newbie</strong>\n"
"                </p>\n"
"                <t t-if=\"object.next_rank_id.name\">\n"
"                    <p>Continue your work to become a <strong t-out=\"object.next_rank_id.name or ''\">Student</strong> !</p>\n"
"                </t>\n"
"                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                    <t t-set=\"gamification_redirection_data\" t-value=\"object.get_gamification_redirection_data()\"/>\n"
"                    <t t-foreach=\"gamification_redirection_data\" t-as=\"data\">\n"
"                        <t t-set=\"url\" t-value=\"data['url']\"/>\n"
"                        <t t-set=\"label\" t-value=\"data['label']\"/>\n"
"                        <a t-att-href=\"url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-out=\"label or ''\">LABEL</a>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p style=\"text-align: center;\">\n"
"                    <img t-attf-src=\"/web/image/gamification.karma.rank/{{ object.rank_id.id }}/image_128\"/>\n"
"                </p>\n"
"            </td>\n"
"        </tr>\n"
"        <tr t-if=\"user.signature\">\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"            </td>\n"
"        </tr>\n"
"    </tbody>\n"
" </table>\n"
"</div>"
msgstr ""

#. module: gamification
#: model:mail.template,body_html:gamification.email_template_goal_reminder
msgid ""
"<div>\n"
"    <strong>Reminder</strong><br/>\n"
"    You have not updated your progress for the goal <t t-out=\"object.definition_id.name or ''\"/> (currently reached at <t t-out=\"object.completeness or ''\"/>%) for at least <t t-out=\"object.remind_update_delay or ''\"/> days. Do not forget to do it.\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.challenge_id.manager_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.challenge_id.manager_id.signature or ''\"/>\n"
"    </t>\n"
"</div>"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"fa fa-clock-o fa-3x\" title=\"Goal in Progress\" "
"aria-label=\"Goal in Progress\"/>"
msgstr ""
"<i role=\"img\" class=\"fa fa-clock-o fa-3x\" title=\"Goal in Progress\" "
"aria-label=\"Goal in Progress\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"o_green fa fa-check fa-3x\" title=\"Goal Reached\" "
"aria-label=\"Goal Reached\"/>"
msgstr ""
"<i role=\"img\" class=\"o_green fa fa-check fa-3x\" title=\"Goal Reached\" "
"aria-label=\"Goal Reached\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"o_red fa fa-times fa-3x\" title=\"Goal Failed\" "
"aria-label=\"Goal Failed\"/>"
msgstr ""
"<i role=\"img\" class=\"o_red fa fa-times fa-3x\" title=\"Goal Failed\" "
"aria-label=\"Goal Failed\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"<span class=\"o_stat_text\">Related</span>\n"
"                                <span class=\"o_stat_text\">Goals</span>"
msgstr ""
"<span class=\"o_stat_text\">Obiective</span>\n"
"<span class=\"o_stat_text\">Asociate</span>\n"
"                               "

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "<span class=\"o_stat_text\">Users</span>"
msgstr "<span class=\"o_stat_text\">Utilizatori</span>"

#. module: gamification
#: model:mail.template,body_html:gamification.email_template_badge_received
msgid ""
"<table border=\"0\" cellpadding=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" width=\"590\" cellpadding=\"0\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Badge</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.badge_id.name or ''\"/>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Congratulations <t t-out=\"object.user_id.name or ''\"/> !<br/>\n"
"                        You just received badge <strong t-out=\"object.badge_id.name or ''\"/> !<br/>\n"
"                        <table t-if=\"not is_html_empty(object.badge_id.description)\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" style=\"width: 560px; margin-top: 5px;\">\n"
"                            <tbody><tr>\n"
"                                <td valign=\"center\">\n"
"                                    <img t-attf-src=\"/web/image/gamification.badge/{{ object.badge_id.id }}/image_128/80x80\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                                </td>\n"
"                                <td valign=\"center\">\n"
"                                    <cite t-out=\"object.badge_id.description or ''\"/>\n"
"                                </td>\n"
"                            </tr></tbody>\n"
"                        </table>\n"
"                        <br/>\n"
"                        <t t-if=\"object.sender_id\">\n"
"                            This badge was granted by <strong t-out=\"object.sender_id.name or ''\"/>.\n"
"                        </t>\n"
"                        <br/>\n"
"                        <t t-if=\"object.comment\" t-out=\"object.comment or ''\"/>\n"
"                        <br/><br/>\n"
"                        Thank you,\n"
"                        <t t-if=\"object.sender_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.sender_id.signature or ''\"/>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; font-size: 12px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=gamification\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""

#. module: gamification
#: model:mail.template,body_html:gamification.simple_report_template
msgid ""
"<table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"background-color: #EEE; border-collapse: collapse;\">\n"
"<tr>\n"
"    <td valign=\"top\" align=\"center\">\n"
"        <t t-set=\"object_ctx\" t-value=\"ctx.get('object')\"/>\n"
"        <t t-set=\"company\" t-value=\"object_ctx and object_ctx.company_id or user.company_id\"/>\n"
"        <t t-set=\"challenge_lines\" t-value=\"ctx.get('challenge_lines', [])\"/>\n"
"        <table cellspacing=\"0\" cellpadding=\"0\" width=\"600\" style=\"margin: 0 auto; width: 570px;\">\n"
"            <tr><td>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n"
"                    <tr>\n"
"                        <div>\n"
"                            <t t-if=\"object.visibility_mode == 'ranking'\">\n"
"                                <td style=\"padding:15px;\">\n"
"                                    <p style=\"font-size:20px;color:#666666;\" align=\"center\">Leaderboard</p>\n"
"                                </td>\n"
"                            </t>\n"
"                        </div>\n"
"                    </tr>\n"
"                </table>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" bgcolor=\"#fff\" style=\"background-color:#fff;\">\n"
"                    <tr><td style=\"padding: 15px;\">\n"
"                        <t t-if=\"object.visibility_mode == 'personal'\">\n"
"                            <span style=\"color:#666666;font-size:13px;\">Here is your current progress in the challenge <strong t-out=\"object.name or ''\"/>.</span>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:20px;\">\n"
"                                <tr>\n"
"                                    <td align=\"center\">\n"
"                                        <div>Personal Performance</div>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;color:#666666;\">\n"
"                                <thead>\n"
"                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                        <th align=\"left\" style=\"padding-bottom: 0px;width:40%;text-align:left;\">Goals</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"left\">Target</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Current</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Completeness</th>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                    </tr>\n"
"                                </thead>\n"
"                                <tbody t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                    <tr style=\"font-weight:bold;\">\n"
"                                        <td style=\"padding: 20px 0;\" align=\"left\">\n"
"                                            <t t-out=\"line['name'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                (<t t-out=\"line['full_suffix'] or ''\"/>)\n"
"                                            </t>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['current'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;font-size:25px;color:#9A6C8E;\" align=\"right\"><strong><t t-out=\"int(line['completeness']) or ''\"/>%</strong></td>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#e3e3e3;\"/>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>                   \n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"color:#A8A8A8;font-size:13px;\">\n"
"                                Challenge: <strong t-out=\"object.name or ''\"/>.\n"
"                            </span> \n"
"                            <t t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                <!-- Header + Button table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:35px;\">\n"
"                                    <tr>\n"
"                                        <td width=\"50%\">\n"
"                                            <div>Top Achievers for goal <strong t-out=\"line['name'] or ''\"/></div>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table>\n"
"                                <!-- Podium -->\n"
"                                <t t-if=\"len(line['goals']) == 2\">\n"
"                                    <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:10px;\">\n"
"                                        <tr><td style=\"padding:0 30px;\">\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"table-layout: fixed;\">\n"
"                                                <tr>\n"
"                                                    <t t-set=\"top_goals\" t-value=\"line['goals'][:3]\"/>\n"
"                                                    <t t-foreach=\"top_goals\" t-as=\"goal\">\n"
"                                                        <td align=\"center\" style=\"width:32%;\">\n"
"                                                            <t t-if=\"loop.index == 1\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:40px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"95\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"75\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#b898b0'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"50\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'2'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 2\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"''\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"85\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'1'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 3\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:60px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#c8afc1'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"35\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'3'\"/>\n"
"                                                            </t>\n"
"                                                            <div style=\"margin:0 3px 0 3px;height:220px;\">\n"
"                                                                <div t-attf-style=\"height:{{ heightA }}px;\">\n"
"                                                                    <t t-out=\"extra_div or ''\"/>\n"
"                                                                    <div style=\"height:55px;\">\n"
"                                                                        <img style=\"margin-bottom:5px;width:50px;height:50px;border-radius:50%;object-fit:cover;\" t-att-src=\"image_data_uri(object.env['res.users'].browse(goal['user_id']).partner_id.image_128)\" t-att-alt=\"goal['name']\"/>\n"
"                                                                    </div>\n"
"                                                                    <div align=\"center\" t-attf-style=\"color:{{ bgColor }};height:20px\">\n"
"                                                                        <t t-out=\"goal['name'] or ''\"/>\n"
"                                                                    </div>\n"
"                                                                </div>\n"
"                                                                <div t-attf-style=\"background-color:{{ bgColor }};height:{{ heightB }}px;\">\n"
"                                                                    <strong><span t-attf-style=\"color:#fff;font-size:{{ fontSize }}px;\" t-out=\"podiumPosition or ''\"/></strong>\n"
"                                                                </div>\n"
"                                                                <div style=\"height:30px;\">\n"
"                                                                    <t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/>\n"
"                                                                    <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                                        <t t-out=\"line['full_suffix'] or ''\"/>\n"
"                                                                    </t>\n"
"                                                                </div>\n"
"                                                            </div>\n"
"                                                        </td>\n"
"                                                    </t>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                            </td>\n"
"                                        </tr>\n"
"                                    </table>\n"
"                                </t>\n"
"                                <!-- data table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-bottom:5px\">\n"
"                                    <tr>\n"
"                                        <td>\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;margin-bottom:5px;color:#666666;\">\n"
"                                                <thead>\n"
"                                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                                        <th style=\"width:15%;text-align:center;\">Rank</th>\n"
"                                                        <th style=\"width:25%;text-align:left;\">Name</th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Performance \n"
"                                                            <t t-if=\"line['suffix']\">\n"
"                                                                (<t t-out=\"line['suffix'] or ''\"/>)\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"line['monetary']\">\n"
"                                                                (<t t-out=\"company.currency_id.symbol or ''\"/>)\n"
"                                                            </t>\n"
"                                                        </th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Completeness</th>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                                    </tr>\n"
"                                                </thead>\n"
"                                                <tbody t-foreach=\"line['goals']\" t-as=\"goal\">\n"
"                                                    <tr>\n"
"                                                        <t t-set=\"tdBgColor\" t-value=\"'#fff'\"/>\n"
"                                                        <t t-set=\"tdColor\" t-value=\"'gray'\"/>\n"
"                                                        <t t-set=\"mutedColor\" t-value=\"'#AAAAAA'\"/>\n"
"                                                        <t t-set=\"tdPercentageColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                        <td width=\"15%\" align=\"center\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:20px;\"><t t-out=\"goal['rank']+1 or ''\"/>\n"
"                                                        </td>\n"
"                                                        <td width=\"25%\" align=\"left\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:13px;\"><t t-out=\"goal['name'] or ''\"/></td>\n"
"                                                        <td width=\"30%\" align=\"right\" t-attf-style=\"background-color:{{ tdBgColor }};padding:5px 0;line-height:1;\"><t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/><br/><span t-attf-style=\"font-size:13px;color:{{ mutedColor }};\">on <t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/></span>\n"
"                                                        </td>\n"
"                                                        <td width=\"30%\" t-attf-style=\"color:{{ tdPercentageColor }};background-color:{{ tdBgColor }};padding-right:15px;font-size:22px;\" align=\"right\"><strong><t t-out=\"int(goal['completeness']) or ''\"/>%</strong></td>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#DADADA;\"/>\n"
"                                                    </tr>\n"
"                                                </tbody>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table> \n"
"                            </t>\n"
"                        </t>\n"
"                    </td></tr>\n"
"                </table>\n"
"            </td></tr>\n"
"        </table>\n"
"    </td>\n"
"</tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.badge_list_action
msgid ""
"A badge is a symbolic token granted to a user as a sign of reward.\n"
"                It can be deserved automatically when some conditions are met or manually by users.\n"
"                Some badges are harder than others to get with specific conditions."
msgstr ""
"O insignă este un token simbolic acordat unui utilizator ca semn al recompensei.\n"
"                Poate fi meritat automat atunci când unele condiții sunt îndeplinite sau manual de către utilizatori.\n"
"                Unele insigne sunt mai greu de obținut decât altele cu condiții specifice."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_definition_list_action
msgid ""
"A goal definition is a technical specification of a condition to reach.\n"
"                The dates, values to reach or users are defined in goal instance."
msgstr ""
"O definiție a obiectivului este o specificație tehnică a unei condiții de atins.\n"
"                Datele, valorile de atins sau utilizatorii sunt definite în instanța de obiectiv."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__condition
#: model:ir.model.fields,help:gamification.field_gamification_goal__definition_condition
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__condition
msgid ""
"A goal is considered as completed when the current value is compared to the "
"value to reach"
msgstr ""
"Un obiectiv este considerat ca fiind terminat când valoarea curentă este "
"comparată cu valoarea dorită"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_list_action
msgid ""
"A goal is defined by a user and a goal definition.\n"
"                Goals can be created automatically by using challenges."
msgstr ""
"Un obiectiv este definit de un utilizator și o definiție a obiectivului.\n"
"Obiectivele pot fi create automat folosind provocări."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.gamification_karma_ranks_action
msgid ""
"A rank correspond to a fixed karma level. The more you have karma, the more your rank is high.\n"
"                    This is used to quickly know which user is new or old or highly or not active."
msgstr ""
"Un rang corespunde unui nivel de karma fix. Cu cât ai mai multă karma, cu atât rangul tău este mai mare.\n"
"Aceasta este utilizată pentru a cunoaște rapid ce utilizator este nou sau vechi sau foarte activ sau nu este activ."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__users
msgid "A selected list of users"
msgstr "O listă selectată de utilizatori"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__action_id
msgid "Action"
msgstr "Acțiune"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_needaction
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_needaction
msgid "Action Needed"
msgstr "Intervenție necesară"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__active
msgid "Active"
msgstr "Activ"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Advanced Options"
msgstr "Opțiuni Avansate"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth
msgid "Allowance to Grant"
msgstr "Permisie de acordare"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__user_domain
msgid "Alternative to a list of users"
msgstr "Alternativa la o listă de utilizatori"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Apare în"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.gamification_badge_view_search
msgid "Archived"
msgstr "Arhivat"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Assign Challenge to"
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.challenge_list_action
msgid ""
"Assign a list of goals to chosen users to evaluate them.\n"
"                The challenge can use a period (weekly, monthly...) for automatic creation of goals.\n"
"                The goals are created for the specified users or member of the group."
msgstr ""
"Alocați o listă de obiective utilizatorilor aleși pentru a le evalua.\n"
"                Provocarea poate folosi o perioadă (săptămânală, lunară ...) pentru crearea automată a obiectivelor.\n"
"                Obiectivele sunt create pentru utilizatorii sau membrii grupului specificați."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_attachment_count
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth_user_ids
msgid "Authorized Users"
msgstr "Utilizatori autorizați"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__python
msgid "Automatic: execute a specific Python code"
msgstr "Automat: execută un cod Python"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__count
msgid "Automatic: number of records"
msgstr "Automat: număr de înregistrări"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__sum
msgid "Automatic: sum on a field"
msgstr "Automat: suma unui câmp"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_bachelor
msgid "Bachelor"
msgstr "Licență"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__badge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__badge_id
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Badge"
msgstr "Insignă"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Badge Description"
msgstr "Descriere insignă"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__level
msgid "Badge Level"
msgstr "Nivel Ecuson"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_list_view
msgid "Badge List"
msgstr "Listă insigne"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__badge_name
msgid "Badge Name"
msgstr "Nume insignă"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.badge_list_action
#: model:ir.model.fields,field_description:gamification.field_res_users__badge_ids
#: model:ir.ui.menu,name:gamification.gamification_badge_menu
msgid "Badges"
msgstr "Insigne"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"Badges are granted when a challenge is finished. This is either at the end "
"of a running period (eg: end of the month for a monthly challenge), at the "
"end date of a challenge (if no periodicity is set) or when the challenge is "
"manually closed."
msgstr ""
"Insigne se acordă atunci când o provocare este terminată. Acesta este fie la"
" sfârșitul unei perioade de funcționare (de ex: sfârșitul lunii pentru o "
"provocare lunară), la data de încheiere a unui provocări (dacă nu este "
"setată o periodicitate) sau când provocarea este închisă manual."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_mode
msgid "Batch Mode"
msgstr "Mod în masă"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_idea
msgid "Brilliant"
msgstr "Genial"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__bronze
msgid "Bronze"
msgstr "Bronz"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__bronze_badge
msgid "Bronze badges count"
msgstr "Număr Insigne Bronz"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Can not grant"
msgstr "Nu se poate acorda"

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid "Can not modify the configuration of a started goal"
msgstr "Nu se poate modifica configurația unui obiectiv început"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Cancel"
msgstr "Anulează"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__canceled
msgid "Canceled"
msgstr "Anulat(ă)"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Category"
msgstr "Categorie"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__challenge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__challenge_id
msgid "Challenge"
msgstr "Provocare"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__line_id
msgid "Challenge Line"
msgstr "Linie provocare"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_line_list_view
msgid "Challenge Lines"
msgstr "Linii provocare"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__name
msgid "Challenge Name"
msgstr "Nume provocare"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__challenge_id
msgid "Challenge originating"
msgstr "Provocare inițiată"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__challenge_id
msgid ""
"Challenge that generated the goal, assign challenge to users to generate "
"goals with a value in this field."
msgstr ""
"Provocare care a generat obiectivul, atribuiți utilizatorilor provocarea de "
"a genera obiective cu o valoare în acest domeniu."

#. module: gamification
#: model:mail.template,name:gamification.simple_report_template
msgid "Challenge: Simple Challenge Report Progress"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.challenge_list_action
#: model:ir.ui.menu,name:gamification.gamification_challenge_menu
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Challenges"
msgstr "Provocări"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_max
msgid "Check to set a monthly limit per person of sending this badge"
msgstr ""
"Bifați pentru a seta o limită lunară pe persoană pentru trimiterea acestei "
"insigne"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Clickable Goals"
msgstr "Obiective clicabile"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__closed
msgid "Closed goal"
msgstr "Obiectiv atins"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__comment
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__comment
msgid "Comment"
msgstr "Comentariu"

#. module: gamification
#: model:gamification.challenge,name:gamification.challenge_base_discover
msgid "Complete your Profile"
msgstr "Completați profilul dvs."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__completeness
msgid "Completeness"
msgstr "Realizare"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__computation_mode
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__computation_mode
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Computation Mode"
msgstr "Mod calcul"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__condition
msgid "Condition"
msgstr "Condiție"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__consolidated
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "Consolidated"
msgstr "Consolidat"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_new_simplified_res_users
msgid "Create User"
msgstr "Creați utilizator"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.badge_list_action
msgid "Create a new badge"
msgstr "Creați o nouă insignă"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.challenge_list_action
msgid "Create a new challenge"
msgstr "Creați o nouă provocare"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_list_action
msgid "Create a new goal"
msgstr "Creați un nou obiectiv"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_definition_list_action
msgid "Create a new goal definition"
msgstr "Creați o nouă definiție a obiectivului"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.gamification_karma_ranks_action
msgid "Create a new rank"
msgstr "Creați un nou rang"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.action_new_simplified_res_users
msgid ""
"Create and manage users that will connect to the system. Users can be "
"deactivated should there be a period of time during which they will/should "
"not connect to the system. You can assign them groups in order to give them "
"specific access to the applications they need to use in the system."
msgstr ""
"Creați și gestionați utilizatori care se vor conecta la sistem. Utilizatorii"
" pot fi dezactivați în caz că există o perioada de timp în care ei nu se "
"conectează la sistem. Le puteți atribui grupuri pentru a le da acces "
"specific la aplicațiile de care ei au nevoie în sistem."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__create_date
msgid "Created on"
msgstr "Creat în"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__current
msgid "Current"
msgstr "Curent(ă)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__current
msgid "Current Value"
msgstr "Valoare curentă"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__daily
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__daily
msgid "Daily"
msgstr "Zilnic"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Data"
msgstr "Data"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__field_date_id
msgid "Date Field"
msgstr "Câmp de dată"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__computation_mode
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__computation_mode
msgid ""
"Define how the goals will be computed. The result of the operation will be "
"stored in the field 'Current'."
msgstr ""
"Definiți cum vor fi calculate obiectivele. Rezultatul operației va fi stocat"
" în câmpul 'Current'."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Definiți vizibilitate provocare prin meniuri"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_condition
msgid "Definition Condition"
msgstr "Condiția definiției"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_description
msgid "Definition Description"
msgstr "Descrierea definiției"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Depending on the Display mode, reports will be individual or shared."
msgstr ""
"În funcție de modul de afișare, rapoartele vor fi individuale sau comune."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"Describe the challenge: what is does, who it targets, why it matters..."
msgstr "Descrieți provocarea: ce este, cine vizează, de ce contează ..."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Describe what they did and why it matters (will be public)"
msgstr "Descrieți ce au făcut și de ce contează (va fi public)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__description
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__description
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__description
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Description"
msgstr "Descriere"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__visibility_mode
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_display
msgid "Display Mode"
msgstr "Mod afișare"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__display_mode
msgid "Displayed as"
msgstr "Afișat ca"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_distinctive_field
msgid "Distinctive field for batch user"
msgstr "Câmp distinctiv pentru lot utilizator"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_doctor
msgid "Doctor"
msgstr "Doctor"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__domain
msgid ""
"Domain for filtering records. General rule, not user depending, e.g. "
"[('state', '=', 'done')]. The expression can contain reference to 'user' "
"which is a browse record of the current user if not in batch mode."
msgstr ""
"Domeniu pentru filtrarea înregistrărilor. Regulă generală nu depinde de "
"utilizator, ex: [('state', '=', 'done')]. Expresia poate conține referința "
"la 'user', care este o înregistrare  a utilizatorului curent, dacă nu este "
"utilizat modul în masă."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__done
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Done"
msgstr "Efectuat"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__draft
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__draft
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Draft"
msgstr "Ciornă"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_newbie
msgid "Earn your first points and join the adventure !"
msgstr "Câștigă primele tale puncte și alătură-te aventurii!"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__end_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__end_date
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "End Date"
msgstr "Dată sfârșit"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_mode
msgid "Evaluate the expression in batch instead of once for each user"
msgstr "Evaluare expresie în masă nu pentru fiecare utilizator"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_user_expression
msgid "Evaluated expression for batch mode"
msgstr "Expresie evaluată pentru modul lotului"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__everyone
msgid "Everyone"
msgstr "Toți"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__display_mode__boolean
msgid "Exclusive (done or not-done)"
msgstr "Exclusiv (realizat sau nerealizat)"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__failed
msgid "Failed"
msgstr "Eșuat"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__field_id
msgid "Field to Sum"
msgstr "Câmp de însumat"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__domain
msgid "Filter Domain"
msgstr "Domeniu filtru"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_follower_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_follower_ids
msgid "Followers"
msgstr "Persoane interesate"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_partner_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_first_id
msgid "For 1st user"
msgstr "Pentru primul utilizator"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_second_id
msgid "For 2nd user"
msgstr "Pentru al doilea utilizator"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_third_id
msgid "For 3rd user"
msgstr "Pentru al treilea utilizator"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_id
msgid "For Every Succeeding User"
msgstr "Pentru fiecare utilizator care are succes"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Formatting Options"
msgstr "Opțiuni de formatare"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__level
msgid "Forum Badge Level"
msgstr "Nivel Ecuson Forum"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "From"
msgstr "De la"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__full_suffix
msgid "Full Suffix"
msgstr "Sufix întreg"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge
msgid "Gamification Badge"
msgstr "Insignă Competiție"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Provocare Competiție"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal
msgid "Gamification Goal"
msgstr "Obiectivul Competiției"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal_definition
msgid "Gamification Goal Definition"
msgstr "Definiție Obiectiv Competiție"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal_wizard
msgid "Gamification Goal Wizard"
msgstr "Expert Obiectiv Competiție"

#. module: gamification
#: model:ir.ui.menu,name:gamification.gamification_menu
msgid "Gamification Tools"
msgstr "Unelte competiție"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge_user
msgid "Gamification User Badge"
msgstr "Ecuson Utilizator Comăpetiție"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge_user_wizard
msgid "Gamification User Badge Wizard"
msgstr "Expert Ecuson Utilizator Competiție"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_challenge_line
msgid "Gamification generic goal for challenge"
msgstr "Obiectiv generic pentru provocare"

#. module: gamification
#: model:ir.actions.server,name:gamification.ir_cron_check_challenge_ir_actions_server
#: model:ir.cron,cron_name:gamification.ir_cron_check_challenge
#: model:ir.cron,name:gamification.ir_cron_check_challenge
msgid "Gamification: Goal Challenge Check"
msgstr "Competiție: Verificare provocare obiectiv"

#. module: gamification
#: model:ir.actions.server,name:gamification.ir_cron_consolidate_last_month_ir_actions_server
#: model:ir.cron,cron_name:gamification.ir_cron_consolidate_last_month
#: model:ir.cron,name:gamification.ir_cron_consolidate_last_month
msgid "Gamification: Karma tracking consolidation"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__goal_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Goal"
msgstr "Obiectiv"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__name
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Goal Definition"
msgstr "Definiție obiectiv"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goal_definition_list_action
#: model:ir.ui.menu,name:gamification.gamification_definition_menu
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_list_view
msgid "Goal Definitions"
msgstr "Definiție obiectiv"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__description
msgid "Goal Description"
msgstr "Descriere obiectiv"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Goal Failed"
msgstr "Obiectiv ratat"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_list_view
msgid "Goal List"
msgstr "Listă obiective"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__condition
msgid "Goal Performance"
msgstr "Performanță obiectiv"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Goal Reached"
msgstr "Obiectiv atins"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.challenge_list_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Goal definitions"
msgstr "Definiții obiectiv"

#. module: gamification
#: model:mail.template,name:gamification.email_template_goal_reminder
msgid "Goal: Reminder for Goal Update"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goal_list_action
#: model:ir.ui.menu,name:gamification.gamification_goal_menu
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Goals"
msgstr "Obiective"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__gold
msgid "Gold"
msgstr "Aur"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__gold_badge
msgid "Gold badges count"
msgstr "Numărul de insigne de aur"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_good_job
msgid "Good Job"
msgstr "Bună treabă"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Grant"
msgstr "Acordă"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_grant_wizard
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Grant Badge"
msgstr "Insignă acordată"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Grant Badge To"
msgstr "Insignă acordată lui"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Grant this Badge"
msgstr "Acordă această insignă"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_user_kanban_view
msgid "Granted by"
msgstr "Generat de"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Granting"
msgstr "Acordarea"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Group By"
msgstr "Grupează după"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__report_message_group_id
msgid "Group that will receive a copy of the report in addition to the user"
msgstr "Grup care va primi o copie a raportului în plus față de utilizator"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "HR Challenges"
msgstr "Provocări HR"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__has_message
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_hidden
msgid "Hidden"
msgstr "Ascuns"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "How is the goal computed?"
msgstr "Cum este calculat obiectivul?"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__challenge_category__hr
msgid "Human Resources / Engagement"
msgstr "Resurse umane / Angajament"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__id
msgid "ID"
msgstr "ID"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__res_id_field
msgid "ID Field of user"
msgstr "Câmp pt. ID utilizator"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__remaining_sending
msgid "If a maximum is set"
msgstr "Dacă maximul este un set"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_needaction
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_unread
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_needaction
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_unread
msgid "If checked, new messages require your attention."
msgstr "Dacă este selectat, mesajele noi necesită atenția dumneavoastră."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_has_error
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, unele mesaje au o eroare de livrare."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user__challenge_id
msgid "If this badge was rewarded through a challenge"
msgstr "Dacă această insignă a fost răsplata unei provocare"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_1920
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_1920
msgid "Image"
msgstr "Imagine"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_1024
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_1024
msgid "Image 1024"
msgstr "Imagine 1024"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_128
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_128
msgid "Image 128"
msgstr "Imagine 128"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_256
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_256
msgid "Image 256"
msgstr "Imagine 256"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_512
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_512
msgid "Image 512"
msgstr "Imagine 512"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__inprogress
msgid "In Progress"
msgstr "În desfășurare"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid ""
"In batch mode, the domain is evaluated globally. If enabled, do not use "
"keyword 'user' in above filter domain."
msgstr ""
"În mod în masă, domeniul este evaluat global. Dacă este activat nu utilizați"
" cuvântul cheie 'user' în domeniul filtrului."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_distinctive_field
msgid ""
"In batch mode, this indicates which field distinguishes one user from the "
"other, e.g. user_id, partner_id..."
msgstr ""
"În modul lotului, aceasta indică câmpul care distinge un utilizator de "
"celălalt, de ex. user_id, partner_id ..."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__last_update
msgid ""
"In case of manual goal, reminders are sent if the goal as not been updated "
"for a while (defined in challenge). Ignored in case of non-manual goal or "
"goal not linked to a challenge."
msgstr ""
"În cazul unui obiectiv manual, mementourile sunt trimise dacă obiectivul nu "
"a fost actualizat pentru o vreme (definit în provocare). Ignorat în cazul "
"unui obiectiv non-manual sau obiectiv care nu este legat de o provocare."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__inprogress
msgid "In progress"
msgstr "În desfășurare"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__visibility_mode__personal
msgid "Individual Goals"
msgstr "Obiective individuale"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__model_inherited_ids
msgid "Inherited models"
msgstr "Modele moștenite"

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin3
#: model:gamification.goal.definition,name:gamification.definition_base_invite
msgid "Invite new Users"
msgstr "Invită utilizatori noi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_is_follower
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__karma
msgid "Karma"
msgstr "Reputație"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__karma_tracking_ids
msgid "Karma Changes"
msgstr "Schimbări karma"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__last_report_date
msgid "Last Report Date"
msgstr "Data ultimului raport"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__last_update
msgid "Last Update"
msgstr "Ultima actualizare"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__visibility_mode__ranking
msgid "Leader Board (Group Ranking)"
msgstr "Clasament (Grup rang)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_max_number
msgid "Limitation Number"
msgstr "Limitare la numărul"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Line List"
msgstr "Linie listă"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__line_ids
msgid "Lines"
msgstr "Linii"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__line_ids
msgid "List of goals that will be set"
msgstr "Lista obiectivelor ce vor fi setate"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__user_ids
msgid "List of users participating to the challenge"
msgstr "Lista utilizatorilor care participă la provocare"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_main_attachment_id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_main_attachment_id
msgid "Main Attachment"
msgstr "Atașament principal"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_master
msgid "Master"
msgstr "Master"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Member"
msgstr "Membru"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_has_error
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_has_error
msgid "Message Delivery error"
msgstr "Eroare livrare mesaj"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_karma_rank__karma_min
msgid "Minimum karma needed to reach this rank"
msgstr "Karma minimă necesară pentru a atinge acest rang"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__model_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Model"
msgstr "Model"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_monetary
msgid "Monetary"
msgstr "Monetar"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__monetary
msgid "Monetary Value"
msgstr "Valoare monetar"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__monthly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__monthly
msgid "Monthly"
msgstr "Lunar"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_max
msgid "Monthly Limited Sending"
msgstr "Trimitere limită lunară"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_this_month
msgid "Monthly total"
msgstr "Total lunar"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__description_motivational
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Motivational"
msgstr "Motivațional"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_karma_rank__description_motivational
msgid "Motivational phrase to reach this rank"
msgstr "Frază motivațională pentru a ajunge la acest rang"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "My Goals"
msgstr "Obiectivele mele"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my_monthly_sending
msgid "My Monthly Sending Total"
msgstr "Totalul meu trimis lunar"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my_this_month
msgid "My Monthly Total"
msgstr "Totalul meu lunar"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my
msgid "My Total"
msgstr "Totalul meu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__name
msgid "Name"
msgstr "Nume"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__never
msgid "Never"
msgstr "Niciodată"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__remind_update_delay
msgid "Never reminded if no value or zero is specified."
msgstr ""
"Nu vă reamintiți niciodată dacă nu este specificată nicio valoare sau zero."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__new_value
msgid "New Karma Value"
msgstr "Valoare Nouă Reputație"

#. module: gamification
#: model:mail.template,subject:gamification.email_template_badge_received
msgid "New badge {{ object.badge_id.name }} granted"
msgstr ""

#. module: gamification
#: model:mail.template,subject:gamification.mail_template_data_new_rank_reached
msgid "New rank: {{ object.rank_id.name }}"
msgstr ""

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_newbie
msgid "Newbie"
msgstr "Începător"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__next_rank_id
msgid "Next Rank"
msgstr "Următorul Rang"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__next_report_date
msgid "Next Report Date"
msgstr "Data următorului raport"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goals_from_challenge_act
msgid "No goal found"
msgstr "Niciun obiectiv găsit"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "No monthly sending limit"
msgstr "Fără trimitere limită lunară"

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_problem_solver
msgid "No one can solve challenges like you do."
msgstr "Nimeni nu poate rezolva provocările ca tine."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__nobody
msgid "No one, assigned through challenges"
msgstr "Nimănui, alocată prin provocări"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "Nobody reached the required conditions to receive special badges."
msgstr ""
"Nimeni nu a atins condițiile necesare pentru a primi ecusoane speciale."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__once
msgid "Non recurring"
msgstr "Nerecursiv"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__remind_update_delay
msgid "Non-updated manual goals will be reminded after"
msgstr "Obiectivele neactualizate manual vă vor fi reamintite după"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Notification Messages"
msgstr "Mesaje de notificare"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_needaction_counter
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_has_error_counter
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_has_error_counter
msgid "Number of errors"
msgstr "Numărul de erori"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_needaction_counter
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Număr de mesaje ce necesită intervenție"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_has_error_counter
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_unread_counter
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_unread_counter
msgid "Number of unread messages"
msgstr "Număr de mesaje necitite"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__granted_users_count
msgid "Number of users"
msgstr "Numar utilizatori"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__old_value
msgid "Old Karma Value"
msgstr "Valoare Veche Reputație"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__onchange
msgid "On change"
msgstr "La modificare"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth_badge_ids
msgid "Only the people having these badges can give this badge"
msgstr "Numai persoanele care au aceste ecusoane pot da acest ecuson"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth_user_ids
msgid "Only these people can give this badge"
msgstr "Numai acești oameni pot acorda această insignă"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Optimisation"
msgstr "Optimizare"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Owner"
msgstr "Proprietar"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__owner_ids
msgid "Owners"
msgstr "Proprietarii"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__having
msgid "People having some badges"
msgstr "Persoane având o anumită insignă"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Period"
msgstr "Perioadă"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__period
msgid ""
"Period of automatic goal assigment. If none is selected, should be launched "
"manually."
msgstr ""
"Perioada de stabilire automată a obiectivelor. Dacă nu este selectat "
"niciunul, ar trebui să fie lansat manual."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__period
msgid "Periodicity"
msgstr "Periodicitate"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_problem_solver
msgid "Problem Solver"
msgstr "Problemă rezolvată"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__display_mode__progress
msgid "Progressive (using numerical values)"
msgstr "Progresiv (utilizând valoare numerică)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__compute_code
msgid "Python Code"
msgstr "Cod Python"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__compute_code
msgid ""
"Python code to be executed for each user. 'result' should contains the new "
"current value. Evaluated user can be access through object.user_id."
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__rank_id
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Rank"
msgstr "Rang"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__name
msgid "Rank Name"
msgstr "Nume Rang"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_karma_rank
msgid "Rank based on karma"
msgstr "Rang bazat pe reputație"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.gamification_karma_ranks_action
#: model:ir.ui.menu,name:gamification.gamification_karma_ranks_menu
msgid "Ranks"
msgstr "Ranguri"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_ranks_view_tree
msgid "Ranks List"
msgstr "Listă Ranguri"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_bachelor
msgid "Reach the next rank and gain a very magic wand !"
msgstr "Ajungeți la următorul rang și câștigați o baghetă magică!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_master
msgid "Reach the next rank and gain a very nice hat !"
msgstr "Ajungeți la următorul rang și câștigați o pălărie foarte drăguță!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_student
msgid "Reach the next rank and gain a very nice mug !"
msgstr "Ajungeți la următorul rang și câștigați o cană foarte drăguță!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_doctor
msgid "Reach the next rank and gain a very nice unicorn !"
msgstr "Ajunge la următorul rang și câștigă un unicorn foarte drăguț!"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__reached
msgid "Reached"
msgstr "Atins"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reached when current value is"
msgstr "Atins atunci când valoarea curentă este"

#. module: gamification
#: model:mail.template,name:gamification.email_template_badge_received
msgid "Received Badge"
msgstr ""

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__manually
msgid "Recorded manually"
msgstr "Înregistrat manual"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reference"
msgstr "Referință"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Refresh Challenge"
msgstr "Actualizează Provocarea"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goals_from_challenge_act
msgid "Related Goals"
msgstr "Obiective asociate"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user_wizard__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Numele utilizatorului asociat resursei pentru a-i gestiona accesul."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__remaining_sending
msgid "Remaining Sending Allowed"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__remind_update_delay
msgid "Remind delay"
msgstr "Zile rămase"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Reminders for Manual Goals"
msgstr "Memento-uri pentru obiective actualizate manual"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_message_frequency
msgid "Report Frequency"
msgstr "Frecvență raport"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_template_id
msgid "Report Template"
msgstr "Șablon raport"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth_badge_ids
msgid "Required Badges"
msgstr "Insigne obligatorii"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__karma_min
msgid "Required Karma"
msgstr "Reputație Necesară"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reset Completion"
msgstr "Resetare completitudine"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__manager_id
msgid "Responsible"
msgstr "Responsabil"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "Retrieving progress for personal challenge without user information"
msgstr ""
"Recuperarea progresului pentru provocare personală fără informații despre "
"utilizator"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Reward"
msgstr "Recompensă"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_failure
msgid "Reward Bests if not Succeeded?"
msgstr "Recompensați cele mai bune dacă nu ați reușit?"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_realtime
msgid "Reward as soon as every goal is reached"
msgstr "Recompensă de îndată ce se ajunge la fiecare obiectiv"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__challenge_ids
msgid "Reward of Challenges"
msgstr "Recompensa provocărilor"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__goal_definition_ids
msgid "Rewarded by"
msgstr "Recompensat de"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Rewards for challenges"
msgstr "Recompense pentru provocări"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Running"
msgstr "În execuție"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Running Challenges"
msgstr "Provocări în desfășurare "

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Schedule"
msgstr "Programare"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_badge_view_search
msgid "Search Badge"
msgstr "Căutare Ecuson"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Search Challenges"
msgstr "Caută provocări"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Search Goal Definitions"
msgstr "Caută definiții obiective"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Search Goals"
msgstr "Caută obiective"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_ranks_view_search
msgid "Search Ranks"
msgstr "Căutare Ranguri"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "Search Trackings"
msgstr "Căutare urmăriri"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid ""
"Security rules to define who is allowed to manually grant badges. Not "
"enforced for administrator."
msgstr ""
"Reguli de securitate pentru a defini cui îi sunt permise să acordare manual "
"insigne. Nu sunt aplicate pentru administrator."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Send Report"
msgstr "Trimite raport"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_message_group_id
msgid "Send a copy to"
msgstr "Trimite o copie la"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__sender_id
msgid "Sender"
msgstr "Expeditor"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__sequence
msgid "Sequence"
msgstr "Secvență"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__sequence
msgid "Sequence number for ordering"
msgstr "Număr secvențial pentru ordonare"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Set the current value you have reached for this goal"
msgstr "Setați valoarea curentă pe care ați atins-o pentru acest obiectiv"

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin1
#: model:gamification.goal.definition,name:gamification.definition_base_company_data
msgid "Set your Company Data"
msgstr "Setați datele companiei dvs."

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin2
#: model:gamification.goal.definition,name:gamification.definition_base_company_logo
msgid "Set your Company Logo"
msgstr "Setează Logo companiei dvs."

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_discover1
#: model:gamification.goal.definition,name:gamification.definition_base_timezone
msgid "Set your Timezone"
msgstr "Setare fus orar"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__challenge_category__other
msgid "Settings / Gamification Tools"
msgstr "Setări / Instrumente de Competiție"

#. module: gamification
#: model:gamification.challenge,name:gamification.challenge_base_configure
msgid "Setup your Company"
msgstr "Setați compania dvs."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__silver
msgid "Silver"
msgstr "Argint"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__silver_badge
msgid "Silver badges count"
msgstr "Număr ecusoare de arging"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Start Challenge"
msgstr "Start provocare"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__start_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__start_date
msgid "Start Date"
msgstr "Dată început"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Start goal"
msgstr "Start obiectiv"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__state
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__state
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "State"
msgstr "Stare"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Statistics"
msgstr "Statistica"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_student
msgid "Student"
msgstr "Student"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Subscriptions"
msgstr "Abonamente"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_full_suffix
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_suffix
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__suffix
msgid "Suffix"
msgstr "Sufix"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__invited_user_ids
msgid "Suggest to users"
msgstr "Sugerează utilizatorilor"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.challenge_line_list_view
msgid "Target"
msgstr "Țintă"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__target_goal
msgid "Target Value to Reach"
msgstr "Valoare țintă de atins"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "Target: less than"
msgstr "Ținta: mai mică de"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__action_id
msgid "The action that will be called to update the goal value."
msgstr "Acțiunea care va fi chemată pentru actualizarea valorii obiectivului."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "The challenge %s is finished."
msgstr "Provocarea este %s finalizată."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_full_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal__definition_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__full_suffix
msgid "The currency and suffix field"
msgstr "Câmpul monedă și sufix"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__field_date_id
msgid "The date to use for the time period evaluated"
msgstr "Data de utilizat pentru perioada de timp evaluată"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__end_date
msgid ""
"The day a new challenge will be automatically closed. If no periodicity is "
"set, will use this date as the goal end date."
msgstr ""
"În ziua în care o nouă provocare va fi închisă automat. Dacă nu este "
"stabilită nicio periodicitate, va folosi această dată ca dată de încheiere a"
" obiectivului."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__start_date
msgid ""
"The day a new challenge will be automatically started. If no periodicity is "
"set, will use this date as the goal start date."
msgstr ""
"În ziua în care va începe automat o nouă provocare. Dacă nu este stabilită "
"nicio periodicitate, va folosi această dată ca dată de începere a "
"obiectivului."

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid ""
"The domain for the definition %s seems incorrect, please check it.\n"
"\n"
"%s"
msgstr ""
"Domeniul pentru definiție  %s pare incorect, vă rugăm să-l verificați.\n"
"%s"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__field_id
msgid "The field containing the value to evaluate"
msgstr "Câmpul conținând valoarea de evaluat"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__res_id_field
msgid ""
"The field name on the user profile (res.users) containing the value for "
"res_id for action."
msgstr ""
"Numele câmpului din profilul utilizatorului (res.users) care conține "
"valoarea res_id pentru acțiune."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__condition__higher
msgid "The higher the better"
msgstr "Mai mare e mai bine"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__owner_ids
msgid "The list of instances of this badge granted to users"
msgstr "Lista instanțelor acestei ecusoane acordate utilizatorilor"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__model_inherited_ids
msgid "The list of models that extends the current model."
msgstr "Lista modelelor care extinde modelul actual."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__unique_owner_ids
msgid "The list of unique users having received this badge."
msgstr "Lista utilizatorilor unici care au primit această insignă."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__condition__lower
msgid "The lower the better"
msgstr "Mai mic e mai bine"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_max_number
msgid ""
"The maximum number of time this badge can be sent per month per person."
msgstr ""
"Numărul maxim de timp în care această insignă poate fi trimisă pe lună de "
"persoană."

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid ""
"The model configuration for the definition %(name)s seems incorrect, please check it.\n"
"\n"
"%(error)s not found"
msgstr ""
"Configurația modelului pentru definiție%(name)s pare incorect, vă rugăm să îl verificați.\n"
"\n"
"%(error)s nu a fost găsite"

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid ""
"The model configuration for the definition %(name)s seems incorrect, please check it.\n"
"\n"
"%(field_name)s not stored"
msgstr ""
"Configurația modelului pentru definiție %(name)s pare incorect, vă rugăm să o verificați.\n"
"\n"
"%(field_name)s nu este stocat"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__model_id
msgid "The model object for the field to evaluate"
msgstr "Obiectul model pentru câmpul de evaluat"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__remind_update_delay
msgid ""
"The number of days after which the user assigned to a manual goal will be "
"reminded. Never reminded if no value is specified."
msgstr ""
"Numărul de zile după care utilizatorul atribuit unui obiectiv manual va fi "
"reamintit. Nu vă reamintiți niciodată dacă nu este specificată nicio valoare"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my_this_month
msgid ""
"The number of time the current user has received this badge this month."
msgstr ""
"Numărul de timp în care utilizatorul actual a primit această insignă luna "
"aceasta."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my
msgid "The number of time the current user has received this badge."
msgstr "Numărul de timp în care utilizatorul actual a primit această insignă."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my_monthly_sending
msgid "The number of time the current user has sent this badge this month."
msgstr ""
"Numărul de timp în care utilizatorul actual a trimis această insignă luna "
"aceasta."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__granted_users_count
msgid "The number of time this badge has been received by unique users."
msgstr ""
"Numărul de timp în care această insignă a fost primită de utilizatori unici."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_this_month
msgid "The number of time this badge has been received this month."
msgstr "Numărul de timp în care această insignă a fost primită luna aceasta."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__granted_count
msgid "The number of time this badge has been received."
msgstr "Numărul de timp în care a primit această insignă."

#. module: gamification
#: model:ir.model.constraint,message:gamification.constraint_gamification_karma_rank_karma_min_check
msgid "The required karma has to be above 0."
msgstr "Reputația necesară trebuie să fie peste 0."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_monetary
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__monetary
msgid "The target and current value are defined in the company currency."
msgstr "Ținta și valoarea curentă sunt definite în moneda companiei."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__suffix
msgid "The unit of the target and current values"
msgstr "Unitatea valorilor țintă și curente"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__manager_id
msgid "The user responsible for the challenge."
msgstr "Utilizatorul responsabil pentru provocare."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user__sender_id
msgid "The user who has send the badge"
msgstr "Utilizatorul care a trimis ecusonul"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__goal_definition_ids
msgid ""
"The users that have succeeded theses goals will receive automatically the "
"badge."
msgstr ""
"Utilizatorii care au reușit aceste obiective vor primi automat ecusonul."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_user_expression
msgid ""
"The value to compare with the distinctive field. The expression can contain "
"reference to 'user' which is a browse record of the current user, e.g. "
"user.id, user.partner_id.id..."
msgstr ""
"Valoarea de comparat cu câmpul distinctiv. Expresia poate conține referință "
"la „utilizator”, care este o înregistrare de navigare a utilizatorului "
"curent, de ex. user.id, user.partner_id.id ..."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goals_from_challenge_act
msgid ""
"There is no goal associated to this challenge matching your search.\n"
"            Make sure that your challenge is active and assigned to at least one user."
msgstr ""
"Nu există niciun obiectiv asociat acestei provocări care să corespundă căutării dvs.\n"
"            Asigurați-vă că provocarea dvs. este activă și atribuită cel puțin unui utilizator."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__closed
msgid "These goals will not be recomputed."
msgstr "Aceste obiective no pot fi recalculate."

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "This badge can not be sent by users."
msgstr "Această insignă nu poate fi trimisă de utilizatori."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "To"
msgstr "Către"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__target_goal
msgid "To Reach"
msgstr "De atins"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__to_update
msgid "To update"
msgstr "De actualizat"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__granted_count
msgid "Total"
msgstr "Total"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_form
msgid "Tracking"
msgstr "Trasabilitate"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__tracking_date
msgid "Tracking Date"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.gamification_karma_tracking_action
#: model:ir.ui.menu,name:gamification.gamification_karma_tracking_menu
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_tree
msgid "Trackings"
msgstr ""

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__unique_owner_ids
msgid "Unique Owners"
msgstr "Proprietari unici"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_suffix
msgid "Unit"
msgstr "buc"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_unread
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_unread
msgid "Unread Messages"
msgstr "Mesaje necitite"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_unread_counter
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contor mesaje necitite"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Update"
msgstr "Actualizare"

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid "Update %s"
msgstr "Actualizare %s"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__user_id
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "User"
msgstr "Operator"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__user_domain
msgid "User domain"
msgstr "Domeniu utilizator"

#. module: gamification
#: model:mail.template,name:gamification.mail_template_data_new_rank_reached
msgid "User: New rank reached"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_current_rank_users
#: model:ir.model,name:gamification.model_res_users
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__user_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__user_ids
msgid "Users"
msgstr "Utilizatori"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_karma_rank__user_ids
msgid "Users having this rank"
msgstr "Utilizatorii care au acest rang"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__weekly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__weekly
msgid "Weekly"
msgstr "Săptămânal"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth
msgid "Who can grant this badge"
msgstr "Cine poate acorda această insignă"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Who would you like to reward?"
msgstr "Pe cine dorești să recompensezi?"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__reward_realtime
msgid ""
"With this option enabled, a user can receive a badge only once. The top 3 "
"badges are still rewarded only at the end of the challenge."
msgstr ""
"Cu această opțiune activată, un utilizator poate primi o insignă o singură "
"dată. Primele 3 ecusoane sunt încă răsplătite doar la sfârșitul provocării."

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_idea
msgid "With your brilliant ideas, you are an inspiration to others."
msgstr "Cu ideile tale strălucitoare, ești o inspirație pentru ceilalți."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__yearly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__yearly
msgid "Yearly"
msgstr "Anual"

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "You are not in the user allowed list."
msgstr "Nu vă aflați în lista de utilizatori autorizați."

#. module: gamification
#: code:addons/gamification/wizard/grant_badge.py:0
#, python-format
msgid "You can not grant a badge to yourself."
msgstr "Nu vă puteți acorda o insignă."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "You can not reset a challenge with unfinished goals."
msgstr "Nu puteți reseta o provocare cu obiective neterminate."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "You can still grant"
msgstr "Puteți acorda în continuare"

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_good_job
msgid "You did great at your job."
msgstr "Te-ai descurcat foarte bine la locul de muncă."

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "You do not have the required badges."
msgstr "Nu aveți insignele necesare."

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "You have already sent this badge too many time this month."
msgstr "Ați trimis deja această insignă de prea multe ori luna aceasta."

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_hidden
msgid "You have found the hidden badge"
msgstr "Ați găsit insigna ascunsă"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_doctor
msgid "You have reached the last rank. Congratulations!"
msgstr "Ai atins ultimul rang. Felicitări!"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_newbie
msgid "You just began the adventure! Welcome!"
msgstr "Tocmai ai început aventura! Bine ati venit!"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_master
msgid "You know what you are talking about. People learn from you."
msgstr "You know what you are talking about. People learn from you."

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_bachelor
msgid "You love learning things. Curiosity is a good way to progress."
msgstr ""
"Îți place să înveți lucruri. Curiozitatea este o modalitate bună de a "
"progresa."

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_student
msgid "You're a young padawan now. May the force be with you!"
msgstr "Ești un tânăr padawan acum. Fie ca forța să fie cu tine!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "badges this month"
msgstr "insignele acestei luni"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "days"
msgstr "zile"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid ""
"e.g. A Master Chief knows quite everything on the forum! You cannot beat "
"him!"
msgstr ""
"de exemplu. Un master Chief știe destul de tot pe forum! Nu-l poți bate!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. Get started"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "e.g. Master Chief"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "e.g. Monthly Sales Objectives"
msgstr "ex: Obiective vânzări lunare"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "e.g. Problem Solver"
msgstr "ex. Rezolvator de probleme"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "e.g. Reach this rank to gain a free mug !"
msgstr "de exemplu. Atingeți acest rang pentru a obține o cană gratuită!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. Register to the platform"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. days"
msgstr "ex. zile"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. user.partner_id.id"
msgstr "ex. user.partner_id.id"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "granted,"
msgstr "acordat,"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "refresh"
msgstr "actualizează"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "than the target."
msgstr "decât ținta."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_user_kanban_view
msgid "the"
msgstr " "

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "this month"
msgstr "luna aceasta"
