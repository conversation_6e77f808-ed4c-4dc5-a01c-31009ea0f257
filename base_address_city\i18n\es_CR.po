# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_address_city
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:52+0000\n"
"PO-Revision-Date: 2017-09-20 09:52+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Costa Rica) (https://www.transifex.com/odoo/teams/41243/es_CR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_CR\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_address_city
#: model:ir.model.fields,help:base_address_city.field_res_country_enforce_cities
#: model:ir.model.fields,help:base_address_city.field_res_partner_country_enforce_cities
#: model:ir.model.fields,help:base_address_city.field_res_users_country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""

#. module: base_address_city
#: model:ir.actions.act_window,name:base_address_city.action_res_city_tree
#: model_terms:ir.ui.view,arch_db:base_address_city.view_res_country_city_extended_form
msgid "Cities"
msgstr ""

#. module: base_address_city
#: model:ir.model,name:base_address_city.model_res_city
#: model_terms:ir.ui.view,arch_db:base_address_city.view_city_filter
#: model_terms:ir.ui.view,arch_db:base_address_city.view_city_tree
msgid "City"
msgstr ""

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_partner_city_id
#: model:ir.model.fields,field_description:base_address_city.field_res_users_city_id
msgid "Company"
msgstr "Compañía"

#. module: base_address_city
#: model:ir.model,name:base_address_city.model_res_partner
msgid "Contact"
msgstr ""

#. module: base_address_city
#: model:ir.model,name:base_address_city.model_res_country
#: model:ir.model.fields,field_description:base_address_city.field_res_city_country_id
msgid "Country"
msgstr ""

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city_create_uid
msgid "Created by"
msgstr ""

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city_create_date
msgid "Created on"
msgstr "Creado en"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city_display_name
msgid "Display Name"
msgstr ""

#. module: base_address_city
#: model_terms:ir.actions.act_window,help:base_address_city.action_res_city_tree
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"                your partner records. Note that an option can be set on each country separately\n"
"                to enforce any address of it to have a city in this list."
msgstr ""

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_country_enforce_cities
#: model:ir.model.fields,field_description:base_address_city.field_res_partner_country_enforce_cities
#: model:ir.model.fields,field_description:base_address_city.field_res_users_country_enforce_cities
msgid "Enforce Cities"
msgstr ""

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city_id
msgid "ID"
msgstr "ID"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city___last_update
msgid "Last Modified on"
msgstr ""

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city_write_uid
msgid "Last Updated by"
msgstr ""

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city_write_date
msgid "Last Updated on"
msgstr ""

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city_name
msgid "Name"
msgstr ""

#. module: base_address_city
#: model_terms:ir.ui.view,arch_db:base_address_city.view_city_filter
msgid "Search City"
msgstr ""

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city_state_id
msgid "State"
msgstr ""

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city_zipcode
msgid "Zip"
msgstr ""
