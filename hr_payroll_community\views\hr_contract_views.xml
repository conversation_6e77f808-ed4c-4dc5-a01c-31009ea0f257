<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Root Menus -->

    <menuitem id="menu_hr_payroll_community_root" name="Payroll" sequence="45"
              web_icon="hr_payroll_community,static/description/icon.png"/>
    <menuitem id="menu_hr_payroll_community_configuration" name="Configuration" parent="menu_hr_payroll_community_root"
              sequence="100" groups="hr_payroll_community.group_hr_payroll_community_manager"/>

    <!-- Contract View -->
    <record id="hr_contract_form_inherit" model="ir.ui.view">
        <field name="name">hr.contract.view.form.inherit</field>
        <field name="model">hr.contract</field>
        <field name="inherit_id" ref="hr_contract.hr_contract_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='type_id']" position="after">
                <field name="struct_id" required="1"/>
            </xpath>
            <xpath expr="//field[@name='type_id']" position="before">
                <!--                <field name="company_id" groups="base.group_multi_company"/>-->
                <field name="currency_id" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='resource_calendar_id']" position="after">
                <field name="schedule_pay"/>
            </xpath>
        </field>
    </record>

    <record id="hr_contract_advantage_template_view_form" model="ir.ui.view">
        <field name="name">hr.contract.advantage.template.form</field>
        <field name="model">hr.contract.advantage.template</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Advantage Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="default_value"/>
                            <field name="lower_bound"/>
                            <field name="upper_bound"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hr_contract_form_additional_allowance" model="ir.ui.view">
        <field name="name">hr.contract.view.additional.allowance</field>
        <field name="model">hr.contract</field>
        <field name="inherit_id" ref="hr_contract.hr_contract_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='salary']" position="after">
                <group string="Monthly Advantages in Cash" name='allowances'>
                    <label for="hra"/>
                    <div class="o_row">
                        <field name="hra" nolabel="1"/>
                        <span>/ month</span>
                    </div>
                    <label for="da"/>
                    <div class="o_row">
                        <field name="da" nolabel="1"/>
                        <span>/ month</span>
                    </div>
                    <label for="travel_allowance"/>
                    <div class="o_row">

                        <field name="travel_allowance" nolabel="1"/>
                        <span>/ month</span>
                    </div>
                    <label for="meal_allowance"/>
                    <div class="o_row">

                        <field name="meal_allowance" nolabel="1"/>
                        <span>/ month</span>
                    </div>
                    <label for="medical_allowance"/>
                    <div class="o_row">

                        <field name="medical_allowance" nolabel="1"/>
                        <span>/ month</span>
                    </div>
                    <!--<label for="personal_computer_allowance"/>
                    <div class="o_row">

                        <field name="personal_computer_allowance" nolabel="1"/>
                        <span>/ month</span>
                    </div>-->
                    <label for="other_allowance"/>
                    <div class="o_row">

                        <field name="other_allowance" nolabel="1"/>
                        <span>/ month</span>
                    </div>
                </group>
            </xpath>
        </field>
    </record>


    <record id="hr_contract_advantage_template_view_tree" model="ir.ui.view">
        <field name="name">hr.contract.advantage.template.tree</field>
        <field name="model">hr.contract.advantage.template</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="code"/>
                <field name="lower_bound"/>
                <field name="upper_bound"/>
                <field name="default_value"/>
            </tree>
        </field>
    </record>

    <record id="hr_contract_advantage_template_action" model="ir.actions.act_window">
        <field name="name">Contract Advantage Templates</field>
        <field name="res_model">hr.contract.advantage.template</field>
    </record>

    <menuitem
            id="hr_contract_advantage_template_menu_action"
            action="hr_contract_advantage_template_action"
            parent="menu_hr_payroll_community_configuration"
            sequence="50"/>
</odoo>