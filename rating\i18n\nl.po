# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rating
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_count
msgid "# Ratings"
msgstr "# Beoordelingen"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__access_token
msgid "Access token to set the rating of the value"
msgstr "Toegangtoken om de beoordeling in te stellen op de waarde"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__message_id
msgid ""
"Associated message when posting a review. Mainly used in website addons."
msgstr ""
"Geassocieerd bericht bij het plaatsen van een recensie. Voornamelijk "
"gebruikt in website addons."

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__partner_id
msgid "Author of the rating"
msgstr "Auteur van de beoordeling"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__feedback
msgid "Comment"
msgstr "Opmerking"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Customer"
msgstr "Klant"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Date"
msgstr "Datum"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ko
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Dissatisfied"
msgstr "Ontevreden"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Document"
msgstr "Document"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model
msgid "Document Model"
msgstr "Documentmodel"

#. module: rating
#: model:ir.model,name:rating.model_mail_thread
msgid "Email Thread"
msgstr "E-mail discussie"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__consumed
msgid "Enabled if the rating has been filled."
msgstr "Ingeschakeld indien de beoordeling is ingevuld."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Feel free to write a feedback on your experience:"
msgstr "Voel je vrij om feedback te geven over je ervaring:"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__consumed
msgid "Filled Rating"
msgstr "Ingevulde beoordeling"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Go back to the Homepage"
msgstr "Ga terug naar de home pagina"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Group By"
msgstr "Groeperen op"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""
"Verbergen voor openbare/portaal gebruikers, onafhankelijk van de "
"configuratie van het subtype."

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__id
msgid "ID"
msgstr "ID"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_id
msgid "Identifier of the rated object"
msgstr "Identifier van het beoordeelde object"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image
msgid "Image"
msgstr "Afbeelding"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 30 days"
msgstr "Laatste 30 dagen"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 7 days"
msgstr "Laatste 7 dagen"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: rating
#: model:ir.model,name:rating.model_mail_message
#: model:ir.model.fields,field_description:rating.field_rating_rating__message_id
msgid "Message"
msgstr "Bericht"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_model_id
msgid "Model of the followed resource"
msgstr "Model van de gevolgde bron"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "My Ratings"
msgstr "Mijn beoordelingen"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_name
msgid "Name"
msgstr "Naam"

#. module: rating
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__none
msgid "No Rating yet"
msgstr "Nog geen beoordeling"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_view
msgid "No rating yet"
msgstr "Nog geen beoordeling"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ok
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Okay"
msgstr "Oké"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__rated_partner_id
msgid "Owner of the rated resource"
msgstr "Eigenaar van de beoordeelde resource"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_id
msgid "Parent Document"
msgstr "Bovenliggend document"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model
msgid "Parent Document Model"
msgstr "Bovenliggend documentmodel"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_name
msgid "Parent Document Name"
msgstr "Bovenliggend documentnaam"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Parent Holder"
msgstr "Bovenliggend houder"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_ref
msgid "Parent Ref"
msgstr "Klant ref"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model_id
msgid "Parent Related Document Model"
msgstr "Bovenliggend gerelateerde documentmodel"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Percentage tevreden beoordelingen"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rated Operator"
msgstr "Ingestelde operator"

#. module: rating
#: model:ir.model,name:rating.model_rating_rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rating"
msgstr "Beoordeling"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg
msgid "Rating Average"
msgstr "Gemiddelde beoordeling"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Beoordeling laatste feedback"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_image
msgid "Rating Last Image"
msgstr "Beoordeling laatste afbeelding"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_value
msgid "Rating Last Value"
msgstr "Beoordeling laatste waarde"

#. module: rating
#: model:ir.model,name:rating.model_rating_mixin
msgid "Rating Mixin"
msgstr "Beoordeling Mixin"

#. module: rating
#: model:ir.model,name:rating.model_rating_parent_mixin
msgid "Rating Parent Mixin"
msgstr "Beoordeling bovenliggende mixin"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Beoordelingstevredenheid"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_value
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_value
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating
msgid "Rating Value"
msgstr "Beoordelingswaarde"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
msgid "Rating Value (/5)"
msgstr "Beoordelingswaarde (/5)"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_count
msgid "Rating count"
msgstr "Aantal beoordelingen"

#. module: rating
#: model:ir.model.constraint,message:rating.constraint_rating_rating_rating_range
msgid "Rating should be between 0 and 5"
msgstr "De beoordeling moet tussen 0 en 5 liggen."

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__rating
msgid "Rating value: 0=Unhappy, 5=Happy"
msgstr "Beoordeling waarde: 0=Ontevreden, 5=Blij"

#. module: rating
#: model:ir.actions.act_window,name:rating.rating_rating_view
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_ids
#: model:ir.ui.menu,name:rating.rating_rating_menu_technical
#: model_terms:ir.ui.view,arch_db:rating.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_tree
msgid "Ratings"
msgstr "Beoordelingen"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__rating_last_feedback
#: model:ir.model.fields,help:rating.field_rating_rating__feedback
msgid "Reason of the rating"
msgstr "Reden van de beoordeling"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model_id
msgid "Related Document Model"
msgstr "Gerelateerde documentmodel"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_ids
msgid "Related ratings"
msgstr "Gekoppelde beoordelingen"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Resource"
msgstr "Resource"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__resource_ref
msgid "Resource Ref"
msgstr "Bron ref"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_name
msgid "Resource name"
msgstr "Naam bron"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__top
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Satisfied"
msgstr "Tevreden"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__access_token
msgid "Security Token"
msgstr "Veiligheidstoken"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Send Feedback"
msgstr "Verzend feedback"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_date
msgid "Submitted on"
msgstr "Verstuurd op"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Thank you for rating our services!"
msgstr "Bedankt voor het beoordelen van onze diensten!"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Thank you, we appreciate your feedback!"
msgstr "Bedankt, we waarderen je feedback!"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_name
msgid "The name of the rated resource."
msgstr "De naam van de beoordeelde resource."

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_view
msgid "There is no rating for this object at the moment."
msgstr "Er is geen beoordeling voor dit object op de moment."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Today"
msgstr "Vandaag"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__is_internal
msgid "Visible Internally Only"
msgstr "Alleen intern zichtbaar"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "by"
msgstr "in"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "for"
msgstr "voor"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "on"
msgstr "op"
