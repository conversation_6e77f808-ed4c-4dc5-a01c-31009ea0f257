<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="ir.cron" id="cron_post_process_payment_tx">
        <field name="name">payment: post-process transactions</field>
        <field name="model_id" ref="payment.model_payment_transaction" />
        <field name="state">code</field>
        <field name="code">model._cron_finalize_post_processing()</field>
        <field name="user_id" ref="base.user_root" />
        <field name="interval_number">10</field>
        <field name="interval_type">minutes</field>
        <field name="numbercall">-1</field>
    </record>

</odoo>
