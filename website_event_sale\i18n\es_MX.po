# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_sale
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity cannot be applied to this "
"event tickets product."
msgstr ""
"No se puede aplicar un artículo de la lista de precios con una cantidad "
"mínima positiva a este tipo de boletos."

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity will not be applied to the "
"event tickets products."
msgstr ""
"No se aplicará un artículo de la lista de precios con una cantidad mínima "
"positiva a este tipo de boletos."

#. module: website_event_sale
#: model:ir.model.fields,field_description:website_event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "Boletos del evento"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "Free"
msgstr "Gratis"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "From"
msgstr "Desde"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr "Regla de la lista de precios"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_product
msgid "Product"
msgstr "Producto"

#. module: website_event_sale
#: code:addons/website_event_sale/controllers/main.py:0
#, python-format
msgid "Registration"
msgstr "Registro"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order
msgid "Sales Order"
msgstr "Orden de venta"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea de la orden de venta"

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "Sorry, The %(ticket)s tickets for the %(event)s event are sold out."
msgstr ""
"Lo sentimos, los boletos %(ticket)s para el evento %(event)s están agotados."

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid ""
"Sorry, only %(remaining_seats)d seats are still available for the %(ticket)s"
" ticket for the %(event)s event."
msgstr ""
"Lo sentimos, solo quedan %(remaining_seats)d asientos disponibles para el "
"boleto%(ticket)s del evento %(event)s."

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "The ticket doesn't match with this product."
msgstr "El tipo de boleto no coincide con este producto."

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid "Warning"
msgstr "Alerta"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_website
msgid "Website"
msgstr "Sitio web"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "to"
msgstr "a"
