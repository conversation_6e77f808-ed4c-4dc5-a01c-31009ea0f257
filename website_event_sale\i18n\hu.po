# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_sale
# 
# Translators:
# <PERSON>, 2021
# krnk<PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity cannot be applied to this "
"event tickets product."
msgstr ""

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity will not be applied to the "
"event tickets products."
msgstr ""

#. module: website_event_sale
#: model:ir.model.fields,field_description:website_event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "Jegyek a rendezvényre"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "Free"
msgstr "Szabad"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "From"
msgstr "Forrás"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr "Árlista szabály"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_product
msgid "Product"
msgstr "Termék"

#. module: website_event_sale
#: code:addons/website_event_sale/controllers/main.py:0
#, python-format
msgid "Registration"
msgstr "Regisztráció"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order
msgid "Sales Order"
msgstr "Értékesítési rendelés"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Értékesítési rendelés sor"

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "Sorry, The %(ticket)s tickets for the %(event)s event are sold out."
msgstr ""
"Sajnáljuk, A %(ticket)s belépők a  %(event)s eseményhez már értékesítésre "
"kerültek."

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid ""
"Sorry, only %(remaining_seats)d seats are still available for the %(ticket)s"
" ticket for the %(event)s event."
msgstr ""
"Sajnáljuk, csak %(remaining_seats)d ülőhely maradt a %(ticket)s belépőkhöz "
"az %(event)s eseményre."

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "The ticket doesn't match with this product."
msgstr "A cédula nem egyezik ezzel a termékkel."

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid "Warning"
msgstr "Figyelmeztetés"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_website
msgid "Website"
msgstr "Honlap"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "to"
msgstr "eddig"
