# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* link_tracker
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__absolute_url
msgid "Absolute URL"
msgstr "URL مطلق "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__label
msgid "Button label"
msgstr "عنوان الزر "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__campaign_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Campaign"
msgstr "الحملة"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_click_action_statistics
msgid "Click Statistics"
msgstr "اضغط على الإحصاءات "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_click_ids
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_kanban
msgid "Clicks"
msgstr "نقرات"

#. module: link_tracker
#: model:ir.model.constraint,message:link_tracker.constraint_link_tracker_code_code
msgid "Code must be unique."
msgstr "يجب أن يكون الكود فريدًا."

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_code_ids
msgid "Codes"
msgstr "أكواد"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__country_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
msgid "Country"
msgstr "الدولة"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action_campaign
msgid "Create a link tracker"
msgstr "إنشاء متتبع الروابط "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: link_tracker
#: code:addons/link_tracker/models/link_tracker.py:0
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid "Creating a Link Tracker without URL is not possible"
msgstr "لا يمكن إنشاء متتبع روابط دون رابط URL "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Group By"
msgstr "التجميع حسب "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url_host
msgid "Host of the short URL"
msgstr "مضيف رابط URL المختصر "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__id
msgid "ID"
msgstr "المُعرف"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__ip
msgid "Internet Protocol"
msgstr "بروتوكول الإنترنت"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker____last_update
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click____last_update
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__link_id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__link_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
msgid "Link"
msgstr "الرابط"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_form
msgid "Link Click"
msgstr "الضغط على الرابط "

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_graph
msgid "Link Clicks"
msgstr "الضغطات على الرابط "

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_action
#: model:ir.model,name:link_tracker.model_link_tracker
#: model:ir.ui.menu,name:link_tracker.link_tracker_menu_main
msgid "Link Tracker"
msgstr "متتبع الرابط "

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_click
msgid "Link Tracker Click"
msgstr "نقرة متتبع الرابط"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_code
msgid "Link Tracker Code"
msgstr "كود متتبع الرابط"

#. module: link_tracker
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid ""
"Link Tracker values (URL, campaign, medium and source) must be unique (%s, "
"%s, %s, %s)."
msgstr ""
"يجب أن تكون قيم متتبع الروابط (URL، الحملة، الوسط والمصدر) فريدة (%s، %s، "
"%s، %s). "

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_graph
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_tree
msgid "Links"
msgstr "الروابط"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_tree
msgid "Links Clicks"
msgstr "ضغطات الروابط "

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "مجموعة مخصصات تكوين البريد "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__medium_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Medium"
msgstr "وسط"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_click_action_statistics
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__count
msgid "Number of Clicks"
msgstr "عدد النقرات"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_utm_campaign__click_count
msgid "Number of clicks generated by the campaign"
msgstr "عدد النقرات التي أنشأتها هذه الحملة "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__title
msgid "Page Title"
msgstr "عنوان الصفحة"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__redirected_url
msgid "Redirected URL"
msgstr "الرابط المُعاد توجيهه"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__code
msgid "Short URL Code"
msgstr "كود رابط URL المختصر "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__code
msgid "Short URL code"
msgstr "كود رابط URL المختصر "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__source_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Source"
msgstr "المصدر"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_action_campaign
msgid "Statistics of Clicks"
msgstr "إحصائيات النقرات"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__url
msgid "Target URL"
msgstr "رابط URL المستهدف "

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__campaign_id
#: model:ir.model.fields,help:link_tracker.field_link_tracker_click__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"يساعدك هذا الاسم علي تتبع جهود حملتك المختلفة، مثلًا: fall_drive "
"،christmas_special"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"هذه هي طريقة التسليم، مثلًا: بطاقة بريدية، أو البريد الإلكتروني أو لافتة "
"إعلانية"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"هذا هو مصدر الرابط، مثلًا: محرك بحث، أو نطاق آخر، أو اسم في قائمة البريد "
"الإلكتروني"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Title and URL"
msgstr "العنوان ورابط URL "

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url
msgid "Tracked URL"
msgstr "رابط URL المتتبع "

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action_campaign
msgid ""
"Trackers are used to collect count stat about click on links and generate "
"short URLs."
msgstr ""
"تستخدم المتتبعات لجمع أعداد الإحصاءات حول النقرات وإنشاء روابط URL قصيرة. "

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "URL"
msgstr "رابط URL "

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "UTM"
msgstr "UTM"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_utm_campaign
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__campaign_id
msgid "UTM Campaign"
msgstr "حملة UTM"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_tree
msgid "Visit Page"
msgstr "زيارة الصفحة"

#. module: link_tracker
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid "Visit Webpage"
msgstr "زيارة صفحة الويب"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "Website Link"
msgstr "رابط الموقع"
