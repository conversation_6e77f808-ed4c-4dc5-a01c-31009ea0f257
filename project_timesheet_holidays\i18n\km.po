# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * project_timesheet_holidays
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_account_analytic_line
msgid "Analytic Line"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave__timesheet_ids
msgid "Analytic Lines"
msgstr ""

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:38
#, python-format
msgid ""
"Both the internal project and task are required to generate timesheet for "
"the leaves."
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_company
msgid "Companies"
msgstr "ក្រុមហ៊ុន"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_company__leave_timesheet_project_id
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__leave_timesheet_project_id
msgid "Default project value for timesheet generated from leave type."
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid "Generate Timesheet"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid ""
"If checked, when validating a leave, timesheet will be generated in the "
"Vacation Project of the company."
msgstr ""

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:30
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_project_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_project_id
#, python-format
msgid "Internal Project"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_task_id
msgid "Internal Task for timesheet"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave
msgid "Leave"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__holiday_id
msgid "Leave Request"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid "Leave Task"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave_type
msgid "Leave Type"
msgstr ""

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:40
#, python-format
msgid "Leaves"
msgstr ""

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Project"
msgstr "គំរោង"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Task"
msgstr "កិច្ចការ"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
msgid ""
"The project will contain the timesheet generated when a leave is validated."
msgstr ""

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/account_analytic.py:16
#, python-format
msgid ""
"You cannot delete timesheet lines attached to a leaves. Please cancel the "
"leaves instead."
msgstr ""
