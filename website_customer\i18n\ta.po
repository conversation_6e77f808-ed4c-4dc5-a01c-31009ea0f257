# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_customer
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON> <bag<PERSON><PERSON><PERSON>@gmail.com>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-02-05 10:06+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <bag<PERSON><PERSON><EMAIL>>\n"
"Language-Team: Tamil (http://www.transifex.com/odoo/odoo-9/language/ta/)\n"
"Language: ta\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr "<span class=\"fa fa-1x fa-tags\"/> முழுவதும்"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_active
msgid "Active"
msgstr ""

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:68
#, python-format
msgid "All Countries"
msgstr "அனைத்து நாடுகள்"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag_classname
msgid "Bootstrap class to customize the color"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_name
msgid "Category Name"
msgstr "வகை பெயர்"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_classname
msgid "Class"
msgstr "வகுப்பு"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Click to create a new partner tag."
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_create_uid
msgid "Created by"
msgstr "உருவாக்கியவர்"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_create_date
msgid "Created on"
msgstr "உருவாக்கப்பட்ட தேதி"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_display_name
msgid "Display Name"
msgstr "காட்சி பெயர்"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_id
msgid "ID"
msgstr "ID"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "Implemented By"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag___last_update
msgid "Last Modified on"
msgstr "கடைசியாக திருத்திய"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_write_uid
msgid "Last Updated by"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_write_date
msgid "Last Updated on"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage the partner tags to better classify them on your website.\n"
"                    You can add the filter by tag on your website in the "
"\"Customize\" menu."
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No result found"
msgstr "பலன் இல்லை"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
#: model_terms:ir.ui.view,arch_db:website_customer.footer_custom
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our References"
msgstr "எங்கள் குறிப்புகள்"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Partner"
msgstr "கூட்டாளி"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr "கூட்டாளி மேற்கோள்"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ... "
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag_partner_ids
msgid "Partners"
msgstr "கூட்டாளிகள்"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
msgid "References"
msgstr "குறிப்புகள்"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "References by Country"
msgstr "நாடு மூலம் குறிப்புகள்"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "References by Tag"
msgstr "மேற்கோள் மூலம் குறிப்புகள்"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "தேடு"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Trusted by millions worldwide"
msgstr ""

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr "இணையத்தளம் குறிச்சொற்களை"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_website_tag_ids
msgid "Website tags"
msgstr "இணையத்தளம் குறிச்சொற்களை"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr "உலக வரைபடம்"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "reference(s))"
msgstr "குறிப்பு(கள்))"

#~ msgid "Partner Tags"
#~ msgstr "கூட்டாளி மேற்கோள்கள்"

#~ msgid "Tags"
#~ msgstr "மேற்கோள்கள்"

#~ msgid "Visible in Website"
#~ msgstr "இணையத்தளம் தெரிபவை"

#~ msgid "Website URL"
#~ msgstr "வலைத்தள URL"
