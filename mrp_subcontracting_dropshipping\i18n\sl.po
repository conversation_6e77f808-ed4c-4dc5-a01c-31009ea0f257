# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting_dropshipping
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Nejc G <<EMAIL>>, 2022\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_res_company
msgid "Companies"
msgstr "Podjetja"

#. module: mrp_subcontracting_dropshipping
#: code:addons/mrp_subcontracting_dropshipping/models/stock_warehouse.py:0
#: model:stock.location.route,name:mrp_subcontracting_dropshipping.route_subcontracting_dropshipping
#, python-format
msgid "Dropship Subcontractor on Order"
msgstr ""

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_to_resupply
msgid "Dropship Subcontractors"
msgstr ""

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,help:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_to_resupply
msgid "Dropship subcontractors with components"
msgstr ""

#. module: mrp_subcontracting_dropshipping
#: code:addons/mrp_subcontracting_dropshipping/models/purchase.py:0
#, python-format
msgid ""
"It appears some components in this RFQ are not meant for subcontracting. "
"Please create a separate order for these."
msgstr ""

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_purchase_order
msgid "Purchase Order"
msgstr "Nabavni nalog"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_rule
msgid "Stock Rule"
msgstr "Pravilo zaloge"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_pull_id
msgid "Subcontracting-Dropshipping MTS Rule"
msgstr ""

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_picking
msgid "Transfer"
msgstr "Prenos"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_warehouse
msgid "Warehouse"
msgstr "Skladišče"
