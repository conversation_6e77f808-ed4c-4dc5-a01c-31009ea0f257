# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * event_sale
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Faroese (https://www.transifex.com/odoo/teams/41243/fo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fo\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Apply"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
msgid "Attendee"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_availability
msgid "Available Seat"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_available
msgid "Available Seats"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before confirming"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line_event_id
msgid ""
"Choose an event and it will automatically create a registration for this "
"event."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line_event_ticket_id
msgid ""
"Choose an event ticket and it will automatically create a registration for "
"this event ticket."
msgstr ""

#. module: event_sale
#: model:product.product,name:event_sale.event_2_product
#: model:product.template,name:event_sale.event_2_product_product_template
msgid "Conference on Business Applications"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_create_uid
msgid "Created by"
msgstr "Byrjað av"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_create_date
msgid "Created on"
msgstr "Byrjað tann"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_event_ticket_seats_max
msgid ""
"Define the number of available tickets. If you have too much registrations "
"you will not be able to sell tickets anymore. Set 0 to ignore this rule set "
"as unlimited."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_product_product_event_ok
#: model:ir.model.fields,help:event_sale.field_product_template_event_ok
#: model:ir.model.fields,help:event_sale.field_sale_order_line_event_ok
msgid ""
"Determine if a product needs to create automatically an event registration "
"at the confirmation of a sales order line."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_display_name
msgid "Display Name"
msgstr "Vís navn"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_editor_id
msgid "Editor"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_email
msgid "Email"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line_event_id
msgid "Event"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_type
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_event_type_id
msgid "Event Category"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_form_view
msgid "Event Name"
msgstr ""

#. module: event_sale
#: model:product.product,name:event_sale.product_product_event
#: model:product.template,name:event_sale.product_product_event_product_template
msgid "Event Registration"
msgstr ""

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_event_event_ticket_ids
#: model:ir.model.fields,field_description:event_sale.field_event_registration_event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line_event_ticket_id
msgid "Event Ticket"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product_event_ticket_ids
msgid "Event Tickets"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_form_view
msgid "Event's Ticket"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_product_template_form
msgid "Events"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:226
#, python-format
msgid "Free"
msgstr ""

#. module: event_sale
#: model:product.product,name:event_sale.event_1_product
#: model:product.template,name:event_sale.event_1_product_product_template
msgid "Functional Webinar"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_id
msgid "ID"
msgstr "ID"

#. module: event_sale
#: model:ir.model,name:event_sale.model_account_invoice
msgid "Invoice"
msgstr "Faktura"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_is_expired
msgid "Is Expired"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product_event_ok
#: model:ir.model.fields,field_description:event_sale.field_product_template_event_ok
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line_event_ok
msgid "Is an Event Ticket"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket___last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor___last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line___last_update
msgid "Last Modified on"
msgstr "Seinast rættað tann"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_write_uid
msgid "Last Updated by"
msgstr "Seinast dagført av"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_write_date
msgid "Last Updated on"
msgstr "Seinast dagført tann"

#. module: event_sale
#: selection:event.event.ticket,seats_availability:0
msgid "Limited"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_max
msgid "Maximum Available Seats"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:222
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_name
#, python-format
msgid "Name"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:151
#, python-format
msgid "No more available seats for the ticket"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:178
#, python-format
msgid "No more available seats for this ticket"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:223
#, python-format
msgid "None"
msgstr ""

#. module: event_sale
#: model:product.product,name:event_sale.event_0_product
#: model:product.template,name:event_sale.event_0_product_product_template
msgid "Open Days in Los Angeles"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_ticket_form
msgid "Origin"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_registration_id
msgid "Original Registration"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:231
#, python-format
msgid "Paid"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:232
#, python-format
msgid "Payment"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_phone
msgid "Phone"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_price
msgid "Price"
msgstr "Prísur"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_price_reduce
msgid "Price Reduce"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_product
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_product_id
msgid "Product"
msgstr "Vøra"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
msgid "Product Template"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
msgid "Quotation"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:19
#: code:addons/event_sale/models/event.py:32
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
#, python-format
msgid "Registration"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:33
#: code:addons/event_sale/models/event.py:50
#, python-format
msgid "Registration for %s"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_registration_ids
msgid "Registrations"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_event_registration_ids
msgid "Registrations to Edit"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_reserved
msgid "Reserved Seats"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_deadline
msgid "Sales End"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_sale_order_id
msgid "Sales Order"
msgstr "Søluordri"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
#: model:ir.model.fields,field_description:event_sale.field_event_registration_sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_sale_order_line_id
msgid "Sales Order Line"
msgstr "Søluordra-linja"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_used
msgid "Seats Used"
msgstr ""

#. module: event_sale
#: model:event.type,name:event_sale.event_type_data_sale
msgid "Sell Online"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Skip"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration_sale_order_id
msgid "Source Sales Order"
msgstr ""

#. module: event_sale
#: model:event.event.ticket,name:event_sale.event_0_ticket_1
#: model:event.event.ticket,name:event_sale.event_1_ticket_1
#: model:event.event.ticket,name:event_sale.event_2_ticket_1
#: model:event.event.ticket,name:event_sale.event_3_ticket_1
msgid "Standard"
msgstr ""

#. module: event_sale
#: model:product.product,name:event_sale.event_3_product
#: model:product.template,name:event_sale.event_3_product_product_template
msgid "Technical Training"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:229
#, python-format
msgid "The registration must be paid"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:223
#, python-format
msgid "Ticket"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_ticket_search
msgid "Ticket Type"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:156
#, python-format
msgid "Ticket should belong to either event category or event but not both"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_type_use_ticketing
msgid "Ticketing"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_type_event_ticket_ids
#: model_terms:ir.ui.view,arch_db:event_sale.event_type_view_form_inherit_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Tickets"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:228
#, python-format
msgid "To pay"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_unconfirmed
msgid "Unconfirmed Seat Reservations"
msgstr ""

#. module: event_sale
#: selection:event.event.ticket,seats_availability:0
msgid "Unlimited"
msgstr ""

#. module: event_sale
#: model:event.event.ticket,name:event_sale.event_0_ticket_2
#: model:event.event.ticket,name:event_sale.event_2_ticket_2
#: model:event.event.ticket,name:event_sale.event_3_ticket_2
msgid "VIP"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please give details about the registrations"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "registration.editor"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "registration.editor.line"
msgstr ""
