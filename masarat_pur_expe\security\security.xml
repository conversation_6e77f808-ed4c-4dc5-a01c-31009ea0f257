<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <record model="ir.module.category" id="mudule_masarat_expense_categ">
        <field name="name">Masarat Expense Groups</field>
        <field name="sequence">120</field>
    </record>

    <record id="group_expense_masarat_type1" model="res.groups">
        <field name="name">شراء أجهزة و معدات(أصول)</field>
        <field name="category_id" ref="mudule_masarat_expense_categ"/>
    </record>

    <record id="group_expense_masarat_type2" model="res.groups">
        <field name="name">مشتريات متنوعة</field>
        <field name="category_id" ref="mudule_masarat_expense_categ"/>
    </record>



</odoo>