# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_cache
# 
# Translators:
# <PERSON>, 2021
# <PERSON>HT<PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__cache_ids
msgid "Cache"
msgstr "Cache"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__compute_user_id
msgid "Cache compute user"
msgstr "Utilisateur calculant le cache"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__config_id
msgid "Config"
msgstr "Configuration"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__create_date
msgid "Created on"
msgstr "Créé le"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__id
msgid "ID"
msgstr "ID"

#. module: pos_cache
#: model_terms:ir.ui.view,arch_db:pos_cache.pos_config_view_form_inherit_pos_cache
msgid "Invalidate cache"
msgstr "Cache non valide"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__limit_products_per_request
msgid "Limit Products Per Request"
msgstr "Limiter les Articles par Requête "

#. module: pos_cache
#. openerp-web
#: code:addons/pos_cache/static/src/js/pos_cache.js:0
#, python-format
msgid "Loading"
msgstr "En cours de chargement..."

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__oldest_cache_time
msgid "Oldest cache time"
msgstr "Heure de cache le plus ancien"

#. module: pos_cache
#: model:ir.actions.server,name:pos_cache.refresh_pos_cache_cron_ir_actions_server
#: model:ir.cron,cron_name:pos_cache.refresh_pos_cache_cron
#: model:ir.cron,name:pos_cache.refresh_pos_cache_cron
msgid "PoS: refresh cache"
msgstr "PdV : Rafraichir le cache"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_cache
msgid "Point of Sale Cache"
msgstr "Cache du point de vente"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Paramétrage du point de vente"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__product_domain
msgid "Product Domain"
msgstr "Domaine de l'Article"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__product_fields
msgid "Product Fields"
msgstr "Champs de l'article"
