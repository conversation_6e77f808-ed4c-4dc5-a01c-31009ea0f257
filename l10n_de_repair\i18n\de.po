# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_de_repair
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-06-30 07:08+0000\n"
"PO-Revision-Date: 2021-07-15 11:47+0200\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0\n"
"Last-Translator: \n"
"Language: de\n"

#. module: l10n_de_repair
#: model:ir.model.fields,field_description:l10n_de_repair.field_repair_order__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: l10n_de_repair
#: model:ir.model.fields,field_description:l10n_de_repair.field_repair_order__id
msgid "ID"
msgstr "ID"

#. module: l10n_de_repair
#: model:ir.model.fields,field_description:l10n_de_repair.field_repair_order__l10n_de_document_title
msgid "L10N De Document Title"
msgstr "L10N De Document Title"

#. module: l10n_de_repair
#: model:ir.model.fields,field_description:l10n_de_repair.field_repair_order__l10n_de_template_data
msgid "L10N De Template Data"
msgstr "L10N De Template Data"

#. module: l10n_de_repair
#: model:ir.model.fields,field_description:l10n_de_repair.field_repair_order____last_update
msgid "Last Modified on"
msgstr "Zuletzt bearbeitet am"

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#, python-format
msgid "Lot/Serial Number"
msgstr "Los-/Seriennummer"

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#, python-format
msgid "Printing Date"
msgstr "Druckdatum"

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#, python-format
msgid "Product to Repair"
msgstr "Produkt zur Reparatur"

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#: model:ir.model,name:l10n_de_repair.model_repair_order
#, python-format
msgid "Repair Order"
msgstr "Reparaturauftrag"

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#, python-format
msgid "Repair Quotation"
msgstr "Reparaturangebot"

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#, python-format
msgid "Warranty"
msgstr "Garantie"
