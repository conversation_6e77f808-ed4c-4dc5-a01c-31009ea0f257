<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_id" model="res.partner">
        <field name="name">ID Company</field>
        <field name="vat"></field>
        <field name="street">AE</field>
        <field name="city">Moncongloe</field>
        <field name="country_id" ref="base.id"/>
        <field name="state_id" ref="base.state_id_yo"/>
        <field name="zip">90241</field>
        <field name="phone">+62 ***********</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.idexample.com</field>
    </record>

    <record id="demo_company_id" model="res.company">
        <field name="name">ID Company</field>
        <field name="partner_id" ref="partner_demo_company_id"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_id')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_id.demo_company_id'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_id.l10n_id_chart')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_id.demo_company_id')"/>
    </function>
</odoo>
