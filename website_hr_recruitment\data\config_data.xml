<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="action_open_website" model="ir.actions.act_url">
            <field name="name">Website Recruitment Form</field>
            <field name="target">self</field>
            <field name="url">/jobs</field>
        </record>
        <record id="base.open_menu" model="ir.actions.todo">
            <field name="action_id" ref="action_open_website"/>
            <field name="state">open</field>
        </record>
    </data>
    <data>
        <record id="hr_recruitment.model_hr_applicant" model="ir.model">
            <field name="website_form_key">apply_job</field>
            <field name="website_form_default_field_id" ref="hr_recruitment.field_hr_applicant__description" />
            <field name="website_form_access">True</field>
            <field name="website_form_label">Apply for a Job</field>
        </record>
        <function model="ir.model.fields" name="formbuilder_whitelist">
            <value>hr.applicant</value>
            <value eval="[
                'description',
                'email_from',
                'partner_name',
                'partner_phone',
                'job_id',
                'department_id',
            ]"/>
        </function>
    </data>
</odoo>
