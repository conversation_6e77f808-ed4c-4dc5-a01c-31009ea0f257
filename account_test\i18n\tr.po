# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_test
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2017
# <PERSON>, 2017
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2016-08-05 12:55+0000\n"
"Last-Translator: <PERSON>, 2017\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid ""
"<br/>\n"
"                        <strong>Description:</strong>"
msgstr ""
"<br/>\n"
"                        <strong>Açıklama:</strong>"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid "<strong>Name:</strong>"
msgstr "<strong>Adı</strong>"

#. module: account_test
#: model:ir.model,name:account_test.model_report_account_test_report_accounttest
msgid "Account Test Report"
msgstr ""

#. module: account_test
#: model:ir.model,name:account_test.model_accounting_assert_test
msgid "Accounting Assert Test"
msgstr ""

#. module: account_test
#: model:ir.actions.act_window,name:account_test.action_accounting_assert
#: model:ir.actions.report,name:account_test.account_assert_test_report
#: model:ir.ui.menu,name:account_test.menu_action_license
msgid "Accounting Tests"
msgstr "Muhasebe Testleri"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid "Accouting tests on"
msgstr "Bunun için Muhasebe testi"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__active
msgid "Active"
msgstr "Aktif"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_03
msgid "Check if movement lines are balanced and have the same date and period"
msgstr ""
"Hareket kalemlerinin denk olduğunu ve aynı tarih ve dönemde olduğunu "
"denetleyin"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_07
msgid ""
"Check on bank statement that the Closing Balance = Starting Balance + sum of"
" statement lines"
msgstr ""
"Hesap özetinde Kapanış Bakiyesi = Açılış Bakiyesi + hesap özeti kalemleri "
"toplamı olduğunu denetleyin"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_06
msgid "Check that paid/reconciled invoices are not in 'Open' state"
msgstr ""
"Ödenmiş/uzlaştırılmış faturaların 'Açık' durumda olmadığını denetleyin"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_05_2
msgid ""
"Check that reconciled account moves, that define Payable and Receivable "
"accounts, are belonging to reconciled invoices"
msgstr ""
"Borç ve Alacak hesaplarını tanımlayan uzlaştırılmış hareket kalemlerinin "
"uzlaştırılmış faturalara ait olduğunu denetleyin"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_05
msgid ""
"Check that reconciled invoice for Sales/Purchases has reconciled entries for"
" Payable and Receivable Accounts"
msgstr ""
"Uzlaştırılmış Satış/Satınalma faturasının Alacak ve Borç Hesaplarında "
"uzlaştırılmış girişler olup olmadığını denetleyin"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_01
msgid "Check the balance: Debit sum = Credit sum"
msgstr "Bakiyeyi denetle: Borç Toplamı = Alacak toplamı"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Code Help"
msgstr "Kod Yardımı"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid ""
"Code should always set a variable named `result` with the result of your test, that can be a list or\n"
"a dictionary. If `result` is an empty list, it means that the test was succesful. Otherwise it will\n"
"try to translate and print what is inside `result`.\n"
"\n"
"If the result of your test is a dictionary, you can set a variable named `column_order` to choose in\n"
"what order you want to print `result`'s content.\n"
"\n"
"Should you need them, you can also use the following variables into your code:\n"
"    * cr: cursor to the database\n"
"    * uid: ID of the current user\n"
"\n"
"In any ways, the code must be legal python statements with correct indentation (if needed).\n"
"\n"
"Example: \n"
"    sql = '''SELECT id, name, ref, date\n"
"             FROM account_move_line \n"
"             WHERE account_id IN (SELECT id FROM account_account WHERE type = 'view')\n"
"          '''\n"
"    cr.execute(sql)\n"
"    result = cr.dictfetchall()"
msgstr ""
"Kod her zaman, testinizin sonucunda ‘sonuç’ olarak adlandırılan bir değişken oluşturmalıdır, bu bir\n"
"liste ya da bir sözlük olabilir. Eğer ‘sonuç’ boş bir listeyse, bu testin başarılı olduğu anlamındadır. Aksi\n"
"takdirde ‘sonuç’ içinde ne varsa çevirisini yapın ve yazdırın.\n"
"\n"
"Testinizin sonucu bir sözlük ise, ‘sonuç’ içeriğini hangi sırada yazdırmayı seçmeniz için ‘sütun_sırası’\n"
"adıyla bir değişken oluşturmalısınız.\n"
"\n"
"Gerekirse kodunuza aşağıdaki değişkenleri de ekleyebilirsiniz:\n"
"    * cr: veritabanı imleçi\n"
"    * uid: geçerli kullanıcı ID\n"
"\n"
"Kod her durumda doğru satırbaşı girintisi ile (gerekirse) geçerli piton komutları olmalı.\n"
"\n"
"Example: \n"
"    sql = '''SELECT id, name, ref, date\n"
"             FROM account_move_line \n"
"             WHERE account_id IN (SELECT id FROM account_account WHERE type = 'view')\n"
"          '''\n"
"    cr.execute(sql)\n"
"    result = cr.dictfetchall()"

#. module: account_test
#: model_terms:ir.actions.act_window,help:account_test.action_accounting_assert
msgid "Create a new accounting test"
msgstr ""

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__create_date
msgid "Created on"
msgstr "Oluşturulma zamanı"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Description"
msgstr "Tanım"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__display_name
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Expression"
msgstr "İfade"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__id
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest__id
msgid "ID"
msgstr "ID"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test____last_update
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest____last_update
msgid "Last Modified on"
msgstr "Son Güncelleme"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Python Code"
msgstr "Python Kodu"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__code_exec
msgid "Python code"
msgstr "Python kodu"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__sequence
msgid "Sequence"
msgstr "Sıra"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_01
msgid "Test 1: General balance"
msgstr "Test 1: Genel bilanço"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_03
msgid "Test 3: Movement lines"
msgstr "Test 3: Hareket kalemleri"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_05
msgid ""
"Test 5.1 : Payable and Receivable accountant lines of reconciled invoices"
msgstr "Test 5.1 : Uzlaştırılmış faturaların Borç ve Alacak hesap kalemleri"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_05_2
msgid "Test 5.2 : Reconcilied invoices and Payable/Receivable accounts"
msgstr "Test 5.2 : Uzlaştırılmış faturalar be Borç/Alacak hesapları"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_06
msgid "Test 6 : Invoices status"
msgstr "Test 6 : Fatura durumu"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_07
msgid "Test 7 : Closing balance on bank statements"
msgstr "Test 7 : Banka hesap özetlerindeki kapanış bakiyesi"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__desc
msgid "Test Description"
msgstr "Test Açıklaması"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__name
msgid "Test Name"
msgstr "Test Adı"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_tree
msgid "Tests"
msgstr "Testler"

#. module: account_test
#: code:addons/account_test/report/report_account_test.py:53
#, python-format
msgid "The test was passed successfully"
msgstr "Testler başarıyla geçildi"
