<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Menu Items -->
    <menuitem
        id="menu_coupon_type_config"
        action="coupon.coupon_program_action_coupon_program"
        parent="sale.product_menu_catalog"
        name="Coupon Programs"
        groups="sales_team.group_sale_manager"
        sequence="5"
    />

    <menuitem
        id="menu_promotion_type_config"
        action="coupon.coupon_program_action_promo_program"
        parent="sale.product_menu_catalog"
        name="Promotion Programs"
        groups="sales_team.group_sale_manager"
        sequence="4"
    />

    <!-- Form Views -->
    <record id="sale_coupon_program_view_coupon_program_form" model="ir.ui.view">
        <field name="name">coupon.program.form</field>
        <field name="model">coupon.program</field>
        <field name="inherit_id" ref="coupon.coupon_program_view_coupon_program_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='coupon_count']/.." position="before">
                <button class="oe_stat_button" type="object" icon="fa-usd" name="action_view_sales_orders">
                    <field name="order_count" string="Sales" widget="statinfo"/>
                </button>
            </xpath>
        </field>
    </record>

    <record id="sale_coupon_program_view_promo_program_form" model="ir.ui.view">
        <field name="name">coupon.program.form</field>
        <field name="model">coupon.program</field>
        <field name="inherit_id" ref="coupon.coupon_program_view_promo_program_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='coupon_count']/.." position="before">
                <button class="oe_stat_button" type="object" icon="fa-usd" name="action_view_sales_orders">
                    <field name="order_count" string="Sales" widget="statinfo"/>
                </button>
            </xpath>
        </field>
    </record>

    <!-- Kanban Views -->
    <record id="sale_coupon_view_coupon_program_kanban" model="ir.ui.view">
        <field name="name">coupon.program.kanban</field>
        <field name="model">coupon.program</field>
        <field name="inherit_id" ref="coupon.view_coupon_program_kanban" />
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('coupon-count-label')]" position="after">
                <div class="col-4 text-center">
                    <strong>Sales</strong>
                </div>
            </xpath>
            <xpath expr="//div[hasclass('coupon-count-value')]" position="after">
                <div class="col-4 text-center">
                    <field name="order_count"/>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
