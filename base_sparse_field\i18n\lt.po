# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_sparse_field
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>l<PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Anatolij, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> V. <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_base
msgid "Base"
msgstr "Bazė"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__boolean
msgid "Boolean"
msgstr "Boolean"

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Changing the storing system for field \"%s\" is not allowed."
msgstr "Keisti saugojimo sistemos laukui \"%s\" negalima."

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__char
msgid "Char"
msgstr "Simbolis"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__data
msgid "Data"
msgstr "Duomenys"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_ir_model_fields__ttype
msgid "Field Type"
msgstr "Lauko tipas"

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_ir_model_fields
msgid "Fields"
msgstr "Laukai"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__float
msgid "Float"
msgstr "Pozicija"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__id
msgid "ID"
msgstr "ID"

#. module: base_sparse_field
#: model:ir.model.fields,help:base_sparse_field.field_ir_model_fields__serialization_field_id
msgid ""
"If set, this field will be stored in the sparse structure of the "
"serialization field, instead of having its own database column. This cannot "
"be changed after creation."
msgstr ""
"Jei nustatyta, šis laukas bus laikomas retoje serijavimo lauko struktūroje, "
"vietoje to, kad turėtų savo duomenų bazės stulpelį. "

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__integer
msgid "Integer"
msgstr "Sveikasis skaičius"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__sparse_fields_test__selection__one
msgid "One"
msgstr "Vienas"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__partner
msgid "Partner"
msgstr "Partneris"

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Renaming sparse field \"%s\" is not allowed"
msgstr "Reto lauko pervadinimas \"%s\" neleidžiamas"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__selection
msgid "Selection"
msgstr "Pasirinkimas"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_ir_model_fields__serialization_field_id
msgid "Serialization Field"
msgstr "Serijavimo laukas"

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Serialization field %r not found for sparse field %s!"
msgstr ""

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_sparse_fields_test
msgid "Sparse fields Test"
msgstr "Retų laukų testas"

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__sparse_fields_test__selection__two
msgid "Two"
msgstr "Du"

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__ir_model_fields__ttype__serialized
msgid "serialized"
msgstr "serijuotas"
