# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_mass_mailing
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid ""
"<strong>Newsletter Popup!</strong> The newsletter popup is active on this "
"page."
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:0
#, python-format
msgid "Add a Newsletter Subscribe Button"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:0
#, python-format
msgid "Add a Newsletter Subscribe Popup"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block
msgid "Always First."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_block
msgid "Be the first to find out all the latest news, products and trends."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid "Change Newsletter"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_mail_block_footer_social_left
msgid "Contact"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_website_mass_mailing_popup__create_uid
msgid "Created by"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_website_mass_mailing_popup__create_date
msgid "Created on"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.js:0
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_website_mass_mailing_popup__display_name
msgid "Display Name"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid "Edit Popup"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.social_links
msgid "Facebook"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.social_links
msgid "Google Plus"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_website_mass_mailing_popup__id
msgid "ID"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.social_links
msgid "Instagram"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_website_mass_mailing_popup____last_update
msgid "Last Modified on"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_website_mass_mailing_popup__write_uid
msgid "Last Updated by"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_website_mass_mailing_popup__write_date
msgid "Last Updated on"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.social_links
msgid "LinkedIn"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model,name:website_mass_mailing.model_mailing_list
#: model:ir.model.fields,field_description:website_mass_mailing.field_website_mass_mailing_popup__mailing_list_id
msgid "Mailing List"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model,name:website_mass_mailing.model_website_mass_mailing_popup
msgid "Mailing list popup"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:0
#, python-format
msgid "Newsletter"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "Subscribe"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.js:0
#, python-format
msgid "Success"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "Thanks"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_mailing_list__toast_content
msgid "Toast Content"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.social_links
msgid "Twitter"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_website_mass_mailing_popup__website_id
msgid "Website"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_website_mass_mailing_popup__popup_content
msgid "Website Popup Content"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_mailing_list__website_popup_ids
msgid "Website Popups"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.mailing_list_view_form
msgid "Website popups"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "your email..."
msgstr ""
