# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_mrp
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Sarah Park, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.purchase_order_form_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">제조</span>"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.mrp_production_form_view_purchase
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">구매완료</span>"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_purchase_order__mrp_production_count
msgid "Count of MO Source"
msgstr "원본 제조요청서 수"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_production__purchase_order_count
msgid "Count of generated PO"
msgstr "발행된 구매발주서 수"

#. module: purchase_mrp
#: code:addons/purchase_mrp/models/purchase.py:0
#, python-format
msgid "Manufacturing Source of %s"
msgstr "%s의 제조 원본"

#. module: purchase_mrp
#: code:addons/purchase_mrp/models/stock_move.py:0
#, python-format
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr "Odoo는 앵글로 색슨 항목을 생성할 수 없습니다. %s의 총 평가는 0입니다."

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_production
msgid "Production Order"
msgstr "생산 주문"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order
msgid "Purchase Order"
msgstr "발주서"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "발주서 내역"

#. module: purchase_mrp
#: code:addons/purchase_mrp/models/mrp_production.py:0
#, python-format
msgid "Purchase Order generated from %s"
msgstr "%s에서 구매발주서 생성"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_stock_move
msgid "Stock Move"
msgstr "재고 이동"
