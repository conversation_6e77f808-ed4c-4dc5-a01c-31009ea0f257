<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="purchase_order_view_form_inherit" model="ir.ui.view">
        <field name="name">purchase.order.form.inherit</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header/button[@name='action_rfq_send']" position="after">
                <button name="action_view_picking" 
                    string="Receive Products" class="oe_highlight" type="object" 
                    attrs="{'invisible': ['|', '|' , ('is_shipped', '=', True), ('state','not in', ('purchase','done')), ('incoming_picking_count', '=', 0)]}" 
                    data-hotkey="y" groups="stock.group_stock_user"/>
            </xpath>
            <xpath expr="//header/button[@name='confirm_reminder_mail']" position="attributes">
                <attribute name="attrs">{'invisible': ['|', '|', '|', ('state', 'not in', ('purchase', 'done')), ('mail_reminder_confirmed', '=', True), ('date_planned', '=', False), ('effective_date', '!=', False)]}</attribute>
            </xpath>
            <xpath expr="//div[hasclass('oe_button_box')]" position="inside">
                <button type="object"
                    name="action_view_picking"
                    class="oe_stat_button"
                    icon="fa-truck" attrs="{'invisible':[('incoming_picking_count','=', 0)]}" groups="stock.group_stock_user">
                    <field name="incoming_picking_count" widget="statinfo" string="Receipt" help="Incoming Shipments"/>
                </button>
            </xpath>
            <xpath expr="//field[@name='currency_id']" position="after">
                <field name="is_shipped" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/tree//field[@name='date_planned']" position="after">
                <field name="move_dest_ids" invisible="1"/>
            </xpath>
            <xpath expr="//page/field[@name='order_line']/tree/field[@name='product_qty']" position="after">
                <field name="forecasted_issue" invisible="1"/>
                <button type="object" name="action_product_forecast_report" icon="fa-area-chart" attrs="{'invisible': ['|', ('forecasted_issue', '=', False), ('product_type', '!=', 'product')]}" class="text-danger"/>
                <button type="object" name="action_product_forecast_report" icon="fa-area-chart" attrs="{'invisible': ['|', ('forecasted_issue', '=', True), ('product_type', '!=', 'product')]}"/>
            </xpath>
            <xpath expr="//div[@name='date_planned_div']" position="inside">
                <button name="%(action_purchase_vendor_delay_report)d" class="oe_link" type="action" context="{'search_default_partner_id': partner_id}" attrs="{'invisible': ['|', ('state', 'in', ['purchase', 'done']), ('partner_id', '=', False)]}">
                    <span attrs="{'invisible': [('on_time_rate', '&lt;', 0)]}"><field name="on_time_rate" widget="integer" class="oe_inline"/>% On-Time Delivery</span>
                    <span attrs="{'invisible': [('on_time_rate', '&gt;=', 0)]}">No On-time Delivery Data</span>
                </button>
            </xpath>
            <xpath expr="//div[@name='reminder']" position="attributes">
                <attribute name="attrs">{'invisible': [('effective_date', '!=', False)]}</attribute>
            </xpath>
            <xpath expr="//div[@name='reminder']" position="after">
                <field name="effective_date" attrs="{'invisible': [('effective_date', '=', False)]}"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/form//field[@name='invoice_lines']" position="after">
                <field name="move_ids"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/form//field[@name='account_analytic_id']" position="before">
                <field name="propagate_cancel" groups="base.group_no_one"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/tree//field[@name='qty_received']" position="attributes">
                <attribute name="attrs">{'column_invisible': [('parent.state', 'not in', ('purchase', 'done'))], 'readonly': [('product_type', 'in', ('consu', 'product'))]}</attribute>
            </xpath>
            <xpath expr="//page[@name='purchase_delivery_invoice']/group/group" position="inside">
                <field name="default_location_dest_id_usage" invisible="1"/>
                <field name="incoterm_id"/>
            </xpath>
            <xpath expr="//div[@name='reminder']" position="after">
                <field name="picking_type_id" domain="[('code','=','incoming'), '|', ('warehouse_id', '=', False), ('warehouse_id.company_id', '=', company_id)]" options="{'no_create': True}" groups="stock.group_stock_multi_locations"/>
                <field name="dest_address_id" groups="stock.group_stock_multi_locations" attrs="{'invisible': [('default_location_dest_id_usage', '!=', 'customer')], 'required': [('default_location_dest_id_usage', '=', 'customer')]}"/>
            </xpath>
        </field>
    </record>

    <record id="purchase_order_line_view_form_inherit" model="ir.ui.view">
        <field name="name">purchase.order.line.form.inherit</field>
        <field name="model">purchase.order.line</field>
        <field name="inherit_id" ref="purchase.purchase_order_line_form2"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='invoice_lines']" position="after">
                <separator string="Stock Moves"/>
                <field name="move_ids"/>
            </xpath>
        </field>
    </record>

</odoo>
