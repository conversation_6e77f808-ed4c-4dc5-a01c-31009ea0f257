# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON>awa<PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON>cile Collart <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>n Jamwutthipreecha, 2022
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-10 06:07+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#, python-format
msgid " Add Images"
msgstr "เพิ่มรูปภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "\" alert with a"
msgstr "เตือนด้วย"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL from\" can not be empty."
msgstr "\"URL จาก\" ต้องไม่เว้นว่าง"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" can not be empty."
msgstr "ไม่สามารถเว้น \"URL ถึง\" ได้"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" cannot contain parameter %s which is not used in \"URL from\"."
msgstr "\"URL ถึง\" ต้องไม่มีพารามิเตอร์ %s ซึ่งไม่ได้ใช้ใน \"URL จาก\""

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" is invalid: %s"
msgstr "\"URL ถึง\" ไม่ถูกต้อง: %s"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must contain parameter %s used in \"URL from\"."
msgstr "\"URL ถึง\" ต้องมีพารามิเตอร์ %s ซึ่งใช้ใน \"URL จาก\""

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must start with a leading slash."
msgstr "\"URL ถึง\" จำเป็นต้องเริ่มต้นด้วยเครื่องหมายทับ (/)"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__page_count
msgid "# Visited Pages"
msgstr "# หน้าที่มีผู้เข้าชม"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__visit_count
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "# Visits"
msgstr "#เยี่ยมชม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$10.50"
msgstr "$10.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$12.00"
msgstr "$12.00"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$15.50"
msgstr "$15.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$7.50"
msgstr "$7.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$9.00"
msgstr "$9.00"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "%s (id:%s)"
msgstr "%s (id:%s)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;/body&amp;gt;"
msgstr "&amp;lt;/body&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;head&amp;gt;"
msgstr "&amp;lt;head&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&gt;"
msgstr "&gt;"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "' did not match any pages."
msgstr "ไม่ตรงกับหน้าใด ๆ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "' did not match anything."
msgstr "ไม่ตรงกับสิ่งใด ๆ "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid ""
"' did not match anything.\n"
"                        Results are displayed for '"
msgstr ""
"ไม่ตรงกับสิ่งใด ๆ \n"
"                        ผลลัพธ์จะแสดงสำหรับ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "' to link to an anchor."
msgstr "เพื่อเชื่อมโยงไปยัง anchor"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"' to search a page.\n"
"                    '"
msgstr ""
"เพื่อค้นหาหน้า\n"
"                    "

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' วันที่ไม่ถูกต้อง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' เป็นวันที่และเวลาที่ไม่ถูกต้อง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "'. Showing results for '"
msgstr "กำลังแสดงผลลัพธ์สำหรับ "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "(could be used in"
msgstr "(สามารถนำมาใช้ใน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid ""
"(see e.g. Opinion\n"
"            04/2012 on Cookie Consent Exemption by the EU Art.29 WP)."
msgstr ""
"(ดูตัวอย่างเช่น ตัวเลือก\n"
"            04/2012 เกี่ยวกับการยกเว้นความยินยอมในการใช้คุกกี้โดย EU Art.29 WP)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "+ Field"
msgstr "+ ฟิลด์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_2
msgid "+1 (650) 555-0111"
msgstr "+1 (650) 555-0111"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid ", .s_searchbar_input"
msgstr ", .s_searchbar_input"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid ", .s_website_form"
msgstr ", .s_website_form"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr "ผู้เขียน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
".\n"
"                                                The website will still work if you reject or discard those cookies."
msgstr ""
"  \n"
"                                                เว็บไซต์จะยังคงใช้งานได้ถึงแม้คุณปฏิเสธหรือยกเลิกคุกกี้เหล่านั้น"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid ""
".\n"
"            Changing its name will break these calls."
msgstr ""
" \n"
"            เมื่อเปลี่ยนชื่อการโทรต่าง ๆ จะถูกระงับ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "...and switch the timeline contents to fit your needs."
msgstr "...และเปลี่ยนเนื้อหาไทม์ไลน์ให้เหมาะกับความต้องการของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "/contactus"
msgstr "/ติดต่อเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1 km"
msgstr "1 กม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/2 - 1/2"
msgstr "1/2 - 1/2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/3 - 2/3"
msgstr "1/3 - 2/3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/4 - 3/4"
msgstr "1/4 - 3/4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "10 m"
msgstr "10 ม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 km"
msgstr "100 กม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 m"
msgstr "100 ม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "100%"
msgstr "100%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1000 km"
msgstr "1,000 กม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "12"
msgstr "12"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "15 km"
msgstr "15 กม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "2 <span class=\"sr-only\">(current)</span>"
msgstr "2 <span class=\"sr-only\">(หมุนเวียน)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2 km"
msgstr "2 กม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2.5 m"
msgstr "2.5 ม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "20 m"
msgstr "20 ม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 km"
msgstr "200 กม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 m"
msgstr "200 ม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2000 km"
msgstr "2000 กม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr "รองรับระบบ toll-free ตลอด 24x7"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "25%"
msgstr "25%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
msgid ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "30 km"
msgstr "30 กม."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__301
#, python-format
msgid "301 Moved permanently"
msgstr "301 ย้ายอย่างถาวร"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__302
#, python-format
msgid "302 Moved temporarily"
msgstr "302 ย้ายชั่วคราว"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__308
msgid "308 Redirect / Rewrite"
msgstr "308 เปลี่ยนทิศทาง / เขียนใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "4 km"
msgstr "4 กม."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "4 steps"
msgstr "4 ขั้นตอน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 km"
msgstr "400 กม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 m"
msgstr "400 ม."

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__404
msgid "404 Not Found"
msgstr "404 ไม่พบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "5 m"
msgstr "5 ม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 km"
msgstr "50 กม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 m"
msgstr "50 ม."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "50%"
msgstr "50%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "50,000+ companies run Odoo to grow their businesses."
msgstr "บริษัทมากกว่า 50,000 แห่งเรียกใช้ Odoo เพื่อขยายธุรกิจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "75%"
msgstr "75%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "8 km"
msgstr "8 กม."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_progress_bar/options.js:0
#, python-format
msgid "80% Development"
msgstr "80% การพัฒนา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr "<b>50,000+ บริษัท</b> ที่เรียกใช้ Odoo เพื่อขยายธุรกิจของพวกเขา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Add</b> the selected image."
msgstr "<b>เพิ่ม</b> รูปภาพที่ถูกเลือก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click Edit</b> to start designing your homepage."
msgstr "<b> คลิกแก้ไข </b> เพื่อเริ่มออกแบบหน้าแรกของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a snippet</b> to access its options menu."
msgstr "<b>คลิกที่ Snippet</b>เพื่อเข้าถึงเมนูตัวเลือก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a text</b> to start editing it."
msgstr "<b>คลิกที่ข้อความ</b>เพื่อเริ่มแก้ไขมัน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this column to access its options."
msgstr "<b>คลิก</b>ที่คอลัมน์นี้เพื่อเข้าถึงตัวเลือกต่าง ๆ "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this header to configure it."
msgstr "<b>คลิก</b>บนส่วนหัวนี้เพื่อกำหนดค่าต่าง ๆ "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this option to change the %s of the block."
msgstr "<b>คลิก</b> บนตัวเลือกเพื่อเปลี่ยน %s ของบล็อก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"color of this block."
msgstr ""
"<b>ปรับแต่ง</b> บล็อกใด ๆ ผ่านเมนูนี้ ทดลองเปลี่ยนสีพื้นหลังของบล็อกนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"image of this block."
msgstr ""
"<b>ปรับแต่ง</b> บล็อกใด ๆ ผ่านเมนูนี้ ลองเปลี่ยนภาพพื้นหลังของบล็อกนี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "<b>Designed</b> <br/>for Companies"
msgstr "<b>ออกแบบ</b> <br/>เพื่อบริษัทเดียว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<b>Designed</b> for companies"
msgstr "<b>ออกแบบ</b> เพื่อหลายบริษัท"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an icon</b> to change it with one of your choice."
msgstr "<b>ดับเบิลคลิกที่ไอคอน</b>เพื่อเปลี่ยนด้วยตัวเลือกของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an image</b> to change it with one of your choice."
msgstr "<b>ดับเบิลคลิกที่ไอคอน</b>เพื่อเปลี่ยนด้วยตัวเลือกของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"
msgstr ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a %s."
msgstr "<b>เลือก</b>  %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a Color Palette."
msgstr "<b>เลือก</b>จานสี"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the %s padding"
msgstr "<b>สไลด์</b> ที่ปุ่มนี้เพื่อเปลี่ยนการเติม%sPadding"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the column size."
msgstr "<b>สไลด์</b> ที่ปุ่มนี้เพื่อเปลี่ยนขนาดคอลัมน์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid ""
"<br/><br/>\n"
"                    Example of rule:<br/>"
msgstr ""
"<br/><br/>\n"
"                    ตัวอย่างกฎ:<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"background-color: rgb(255, 255, 255);\">Good writing is "
"simple, but not simplistic.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 255, "
"255);\">การเขียนที่ดีนั้นเรียบง่ายแต่ไม่ง่าย</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<font style=\"font-size: 14px;\">Created in 2021, the company is young and "
"dynamic. Discover the composition of the team and their skills.</font>"
msgstr ""
"<font style=\"font-size: 14px;\">ก่อตั้งขึ้นในปี 2021 "
"บริษัทยังใหม่และมีพลวัต ค้นพบองค์ประกอบของทีมและทักษะของพวกเขา</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, 255);\">Edit "
"this title</font>"
msgstr ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, "
"255);\">แก้ไขชื่อนี้</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "<font style=\"font-size: 62px; font-weight: bold;\">Catchy Headline</font>"
msgstr ""
"<font style=\"font-size: 62px; font-weight: bold;\">หัวเรื่องที่จับใจ</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "<font style=\"font-size: 62px;\">A punchy Headline</font>"
msgstr "<font style=\"font-size: 62px;\">หัวเรื่องที่กระแทกใจ</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "<font style=\"font-size: 62px;\">Sell Online. Easily.</font>"
msgstr "<font style=\"font-size: 62px;\">ขายออนไลน์ได้อย่างง่ายดาย</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "<font style=\"font-size: 62px;\">Slide Title</font>"
msgstr "<font style=\"font-size: 62px;\">ชื่อสไลด์</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "<font style=\"font-size: 62px;\">Win $20</font>"
msgstr "<font style=\"font-size: 62px;\">ชนะรับ $20</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "<font style=\"font-size: 62px;\">Your Site Title</font>"
msgstr "<font style=\"font-size: 62px;\">ชื่อเว็บไซต์ของคุณ</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 days ago</small>"
msgstr "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 วันก่อน</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"mr-2\"/><span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"mr-2\"/><span><EMAIL></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid ""
"<i class=\"fa fa-1x fa-fw fa-map-marker mr-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-map-marker mr-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Create a Google Project and Get a Key"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            สร้าง Google โปรเจกต์และรับคีย์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Enable billing on your Google Project"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            เปิดใช้งานการเรียกเก็บเงินใน Google Project ของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Client ID"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            วิธีรับไอดีลูกค้าของฉัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Measurement ID"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            วิธีรับ Measurement ID ของฉัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-eye ml-2 text-muted\" title=\"Go to View\"/>"
msgstr "<i class=\"fa fa-eye ml-2 text-muted\" title=\"ไปที่ดู\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-files-o ml-2 text-muted\" title=\"Show Arch Diff\"/>"
msgstr "<i class=\"fa fa-files-o ml-2 text-muted\" title=\"แสดง Arch Diff\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr "<i class=\"fa fa-fw fa-circle\"/> วงกลม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr "<i class=\"fa fa-fw fa-heart\"/> หัวใจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh mr-1\"/> Replace Icon"
msgstr "<i class=\"fa fa-fw fa-refresh mr-1\"/> แทนที่ไอคอน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr "<i class=\"fa fa-fw fa-square\"/>สี่เหลี่ยม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr "<i class=\"fa fa-fw fa-star\"/> ดาว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr "<i class=\"fa fa-fw fa-thumbs-up\"/> ยกนิ้ว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Offline</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>ออฟไลน์</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Connected</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>เชื่อมต่อ</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"<i class=\"fa fa-info-circle\"/> Edit the content below this line to adapt "
"the default <strong>Page not found</strong> page."
msgstr ""
"<i class=\"fa fa-info-circle\"/> "
"แก้ไขเนื้อหาด้านล่างบรรทัดนี้เพื่อปรับค่าเริ่มต้น<strong>ไม่พบเพจ</strong>ของเพจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">A password is required to access this page.</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">จำเป็นต้องมีรหัสเพื่อเข้าถึงเพจนี้</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Wrong password</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">รหัสผิด</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-map-marker fa-fw mr-2\"/><span "
"class=\"o_force_ltr\">Chaussée de Namur 40</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw mr-2\"/><span "
"class=\"o_force_ltr\">Chaussée de Namur 40</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-phone fa-fw mr-2\"/><span class=\"o_force_ltr\">+ 32 81 81 "
"37 00</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw mr-2\"/><span class=\"o_force_ltr\">+ 32 81 81 "
"37 00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_add_language
msgid ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>Add a language...</span>"
msgstr ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>เพิ่มภาษา...</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<i class=\"fa fa-th-large mr-2\"/> WEBSITE"
msgstr "เว็บไซต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw mr-2\"/>\n"
"                            <b>Events</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw mr-2\"/>\n"
"                            <b>อีเวนต์</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw mr-2\"/>\n"
"                            <b>About us</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw mr-2\"/>\n"
"                            <b>เกี่ยวกับเรา</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw mr-2\"/>\n"
"                            <b>Partners</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw mr-2\"/>\n"
"                            <b>พาร์ทเนอร์</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw mr-2\"/>\n"
"                            <b>Services</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw mr-2\"/>\n"
"                            <b>บริการ</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw mr-2\"/>\n"
"                            <b>Help center</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw mr-2\"/>\n"
"                            <b>ศูนย์ช่วยเหลือ</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw mr-2\"/>\n"
"                            <b>Guides</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw mr-2\"/>\n"
"                            <b>การแนะนำ</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw mr-2\"/>\n"
"                            <b>Our blog</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw mr-2\"/>\n"
"                            <b>บล็อกของเรา</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw mr-2\"/>\n"
"                            <b>Customers</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw mr-2\"/>\n"
"                            <b>ลูกค้า</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw mr-2\"/>\n"
"                            <b>Products</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw mr-2\"/>\n"
"                            <b>สินค้า</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments mr-2\"/> Contact us"
msgstr "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments mr-2\"/> ติดต่อเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-cube mr-2\"/> Free returns"
msgstr "<i class=\"s_mega_menu_thumbnails_icon fa fa-cube mr-2\"/> คืนสินค้าฟรี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket mr-2\"/> Pickup"
" in store"
msgstr ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket "
"mr-2\"/>รับของที่ร้าน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-truck mr-2\"/> Express delivery"
msgstr "<i class=\"s_mega_menu_thumbnails_icon fa fa-truck mr-2\"/>การจัดส่งด่วน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page SEO optimized?\" class=\"fa fa-search\"/>"
msgstr "<i title=\"หน้า SEO ปรับให้เหมาะสมหรือไม่?\" class=\"fa fa-search\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid ""
"<i title=\"Is the page included in the main menu?\" class=\"fa fa-thumb-"
"tack\"/>"
msgstr "<i title=\"หน้านี้รวมอยู่ในเมนูหลักหรือไม่?\" class=\"fa fa-thumb-tack\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page indexed by search engines?\" class=\"fa fa-globe\"/>"
msgstr ""
"<i title=\"หน้านั้นจัดทำดัชนีโดยเครื่องมือค้นหาหรือไม่?\" class=\"fa fa-"
"globe\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page published?\" class=\"fa fa-eye\"/>"
msgstr "<i title=\"เพจเผยแพร่หรือยัง?\" class=\"fa fa-eye\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr "<i>ติดตั้งทันที พึงพอใจหรือขอคืนเงิน</i>"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "<p>Attached files : </p>"
msgstr "<p>แนบไฟล์ </p>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid "<small class=\"s_share_title d-none\"><b>Follow us</b></small>"
msgstr "<small class=\"s_share_title d-none\"><b>ติดตามเรา</b></small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
msgid "<small class=\"s_share_title text-muted d-none\"><b>Follow us</b></small>"
msgstr "<small class=\"s_share_title text-muted d-none\"><b>ติดตามเรา</b></small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<small class=\"text-muted\">\n"
"                                            <i class=\"fa fa-info\"/>: type some of the first chars after 'google' is enough, we'll guess the rest.\n"
"                                        </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                                            <i class=\"fa fa-info\"/>: พิมพ์ตัวอักษรเพียงตัวเดียวหลังจาก 'google' ก็พอแล้วเราจะคาดเดาส่วนที่เหลือ\n"
"                                        </small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Form field help "
"text</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-"
"muted\">ข้อความช่วยเหลือช่องแบบฟอร์ม</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">We'll never share "
"your email with anyone else.</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-"
"muted\">เราจะไม่เปิดเผยอีเมลของคุณกับคนอื่น</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<small>/ month</small>"
msgstr "<small>/ เดือน</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "<small>TABS</small>"
msgstr "<small>แท็บ</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid "<small>We help you grow your business</small>"
msgstr "<small>เราช่วยคุณขยายธุรกิจ</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2015</b></span>"
msgstr "<span class=\"bg-white\"><b>2015</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2018</b></span>"
msgstr "<span class=\"bg-white\"><b>2018</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2019</b></span>"
msgstr "<span class=\"bg-white\"><b>2019</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"sr-only\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"sr-only\">ต่อไป</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"sr-only\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"sr-only\">ก่อนหน้า</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">Discover our new products</font></b>\n"
"                        </span>"
msgstr ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">ค้นพบสินค้าใหม่ของเรา</font></b>\n"
"                        </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Your message has been sent <b>successfully</b></span>"
msgstr ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>ข้อความของคุณถูกส่ง<b>เรียบร้อยแล้ว</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Previous</span>"
msgstr ""
"<span class=\"fa fa-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">ก่อนหน้า</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Next</span>"
msgstr ""
"<span class=\"fa fa-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">ต่อไป</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" "
"title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะเว็บไซต์\" "
"groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-pencil mr-2\"/>Edit"
msgstr "<span class=\"fa fa-pencil mr-2\"/>แก้ไข"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-plus mr-2\"/>New"
msgstr "<span class=\"fa fa-plus mr-2\"/>ใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" title=\"Sort\"/>"
msgstr "<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" title=\"Sort\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "<span class=\"fa-2x\">×</span>"
msgstr "<span class=\"fa-2x\">×</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid "<span class=\"list-inline-item\">|</span>"
msgstr "<span class=\"list-inline-item\">|</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr "<span class=\"mx-2\">/</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid ""
"<span class=\"o_add_language list-inline-item\" "
"groups=\"website.group_website_publisher\">|</span>"
msgstr ""
"<span class=\"o_add_language list-inline-item\" "
"groups=\"website.group_website_publisher\">|</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_copyright_company_name
msgid ""
"<span class=\"o_footer_copyright_name mr-2\">Copyright &amp;copy; Company "
"name</span>"
msgstr ""
"<span class=\"o_footer_copyright_name mr-2\">Copyright &amp;copy; Company "
"name</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Robots.txt</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Robots.txt</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะเว็บไซต์\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Sitemap</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">แผนที่เว็บไซต์ </span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะเว็บไซต์\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_badge
msgid ""
"<span class=\"s_badge badge badge-secondary o_animable\" data-name=\"Badge\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>Category\n"
"    </span>"
msgstr ""
"<span class=\"s_badge badge badge-secondary o_animable\" data-name=\"ป้าย\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>หมวดหมู่\n"
"    </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO ของ "
"MyCompany</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO ของ "
"MyCompany</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO ของ "
"MyCompany</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">12</span><br/>"
msgstr "<span class=\"s_number display-4\">12</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">37</span><br/>"
msgstr "<span class=\"s_number display-4\">37</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">45</span><br/>"
msgstr "<span class=\"s_number display-4\">45</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">8</span><br/>"
msgstr "<span class=\"s_number display-4\">8</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "<span class=\"s_progress_bar_text\">80% Development</span>"
msgstr "<span class=\"s_progress_bar_text\">80% การพัฒนา</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Email To</span>"
msgstr "<span class=\"s_website_form_label_content\">อีเมลถึง</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">หมายเลขโทรศัพท์</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">หัวข้อ</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">บริษัทของคุณ</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">อีเมลของคุณ</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">ชื่อของคุณ</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Your Question</span>"
msgstr "<span class=\"s_website_form_label_content\">คำถามของคุณ</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"sr-only\">Toggle Dropdown</span>"
msgstr "<span class=\"sr-only\">Toggle Dropdown</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"<span title=\"Mobile preview\" role=\"img\" aria-label=\"Mobile preview\" "
"class=\"fa fa-mobile\"/>"
msgstr ""
"<span title=\"ตัวอย่างบนมือถือ\" role=\"img\" aria-label=\"Mobile preview\" "
"class=\"fa fa-mobile\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_publisher
msgid ""
"<span/>\n"
"                    <span class=\"css_publish\">Unpublished</span>\n"
"                    <span class=\"css_unpublish\">Published</span>"
msgstr ""
"<span/>\n"
"                    <span class=\"css_publish\">ไม่ได้เผยแพร่</span>\n"
"                    <span class=\"css_unpublish\">เผยแพร่</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<span>Theme</span>"
msgstr "<span>ธีม</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"A CDN helps you serve your website’s content with high availability and high"
" performance to any visitor wherever they are located."
msgstr ""
"CDN "
"ช่วยให้คุณแสดงเนื้อหาเว็บไซต์ของคุณได้อย่างทรงประสิทธิภาพและยังออนไลน์เกือบตลอดเวลา"
" แก่ผู้เข้าชมทุกที่ที่พวกเขาอยู่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart
msgid "A Chart Title"
msgstr "ชื่อแผนภูมิ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"A Google Map error occurred. Make sure to read the key configuration popup "
"carefully."
msgstr ""
"เกิดข้อผิดพลาดของ Google Map "
"ตรวจสอบให้แน่ใจว่าได้อ่านป๊อปอัปการกำหนดค่าคีย์อย่างระมัดระวัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr "คำบรรยายส่วน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid ""
"A card is a flexible and extensible content container. It includes options "
"for headers and footers, a wide variety of content, contextual background "
"colors, and powerful display options."
msgstr ""
"การ์ดเป็นที่ใส่เนื้อหาที่มีความยืดหยุ่นและสามารถขยายได้ "
"ประกอบด้วยตัวเลือกสำหรับส่วนหัวและส่วนท้าย ใส่เนื้อหาได้หลากหลาย "
"เปลี่ยนสีพื้นหลังตามบริบท และยังมีตัวเลือกการแสดงผลที่มีประสิทธิภาพ"

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_published
#: model:ir.model.fields,help:website.field_ir_cron__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""
"การดำเนินการโค้ดเซิร์ฟเวอร์สามารถทำได้ได้จากเว็บไซต์ โดยใช้คอนโทรลเลอร์เฉพาะ"
" ซึ่งที่อยู่คือ <base>/website/action/<website_path> ตั้งค่าฟิลด์นี้เป็น "
"\"เป็นจริง\" เพื่ออนุญาตให้ผู้ใช้สามารถเรียกใช้การดำเนินการนี้ "
"หากตั้งค่าเป็น \"เป็นเท็จ\" การดำเนินการจะไม่สามารถดำเนินการผ่านเว็บไซต์ได้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "A color block"
msgstr "บล็อกสี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr "ชื่อเรื่องที่โดดเด่น"

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__field_names
msgid "A list of comma-separated field names"
msgstr "รายชื่อฟิลด์ที่คั่นด้วยเครื่องหมายจุลภาค"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_stores_locator
msgid "A map and a listing of your stores"
msgstr "แผนที่และรายชื่อร้านค้าของคุณ"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visit_count
msgid ""
"A new visit is considered if last connection was more than 8 hours ago."
msgstr ""
"การเข้าชมครั้งใหม่จะได้รับการพิจารณาหากการเชื่อมต่อครั้งล่าสุดนานกว่า 8 "
"ชั่วโมงที่แล้ว"

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_partner_uniq
msgid "A partner is linked to only one visitor."
msgstr "พาร์ทเนอร์เชื่อมโยงกับผู้เยี่ยมชมเพียงคนเดียว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "A short description of this great feature."
msgstr "คำอธิบายสั้น ๆ ของฟีเจอร์ที่ยอดเยี่ยมนี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great <br/>feature, in clear words."
msgstr "คำอธิบายเล็ก ๆ ของ<br/>ฟีเจอร์ที่ชัดเจน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"A timeline is a graphical representation on which important events are "
"marked."
msgstr "ไทม์ไลน์คือการแสดงกราฟิกที่มีการทำเครื่องหมายเหตุการณ์สำคัญต่าง ๆ ไว้"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__is_connected
msgid ""
"A visitor is considered as connected if his last page view was within the "
"last 5 minutes."
msgstr ""
"ผู้เข้าชมจะถือว่าเชื่อมต่อหากการดูหน้าเว็บครั้งสุดท้ายของเขาเกิดขึ้นภายใน 5 "
"นาทีที่ผ่านมา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "API Key"
msgstr "API Key"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_about_us
msgid "About Us"
msgstr "เกี่ยวกับเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "About us"
msgstr "เกี่ยวกับเรา"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Access Error"
msgstr "การเข้าถึงผิดพลาด"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__access_token
msgid "Access Token"
msgstr "เข้าถึงโทเคน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid "Access to this page"
msgstr "เข้าถึงหน้านี้"

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_access_token_unique
msgid "Access token should be unique."
msgstr "การเข้าถึงโทเคนควรแตกต่างกัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Accessories"
msgstr "เครื่องประดับเสริม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr "บัญชีและการบริหารการขาย"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__redirect_type
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Action"
msgstr "การดำเนินการ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Activate anyway"
msgstr "เปิดใช้งานต่อไป"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__active
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__active
#: model:ir.model.fields,field_description:website.field_website_page__active
#: model:ir.model.fields,field_description:website.field_website_rewrite__active
#: model:ir.model.fields,field_description:website.field_website_visitor__active
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"ปรับสามคอลัมน์นี้ให้เหมาะกับความต้องการในการออกแบบของคุณ หากต้องการทำซ้ำ ลบ "
"หรือย้ายคอลัมน์ ให้เลือกคอลัมน์และใช้ไอคอนด้านบนเพื่อดำเนินการ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#, python-format
msgid "Add"
msgstr "เพิ่ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Add Features"
msgstr "เพิ่มฟีเจอร์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Item"
msgstr "เพิ่มรายการ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Add Media"
msgstr "เพิ่มสื่อ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Mega Menu Item"
msgstr "เพิ่มรายการเมนูใหญ่"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Menu Item"
msgstr "เพิ่มรายการเมนู"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
msgid "Add Product"
msgstr "เพิ่มสินค้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Row"
msgstr "เพิ่มแถว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Serie"
msgstr "เพิ่มซีรีส์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Slide"
msgstr "เพิ่มสไลด์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Add Tab"
msgstr "เพิ่มแถบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Add Year"
msgstr "เพิ่มปี"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Add a Google Font"
msgstr "เพิ่ม Google Font"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "Add a caption to enhance the meaning of this image."
msgstr "เพิ่มคำบรรยายเพื่อเพิ่มความหมายของภาพนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_product_catalog/options.js:0
#, python-format
msgid "Add a description here"
msgstr "เพิ่มคำบรรยายตรงนี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Add a great slogan."
msgstr "เพิ่มสโลแกนที่ยอดเยี่ยม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Add a menu description."
msgstr "เพิ่มคำบรรยายเมนู"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Add a menu item"
msgstr "เพิ่มรายการเมนู"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field after this one"
msgstr "เพิ่มฟิลด์ใหม่หลังจากอันนี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field at the end"
msgstr "เพิ่มฟิลด์ใหม่ในตอนท้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add features"
msgstr "เพิ่มฟีเจอร์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add links to social media on your website"
msgstr "เพิ่มลิงค์ไปยังสื่อสังคมออนไลน์บนเว็บไซต์ของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Add new %s"
msgstr "เพิ่ม %s ใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Add to cart"
msgstr "เพิ่มในรถเข็น"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Add to menu"
msgstr "เพิ่มในเมนู"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Adding a font requires a reload of the page. This will save all your "
"changes."
msgstr ""
"การเพิ่มฟอนต์ใหม่จำเป็นต้องโหลดหน้าซ้ำ "
"การดำเนินการนี้จะบันทึกการเปลี่ยนแปลงทั้งหมดของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Address"
msgstr "ที่อยู่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Advertising &amp; Marketing"
msgstr "การโฆษณาและการตลาด"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__after
msgid "After"
msgstr "หลังจาก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Alert"
msgstr "เตือน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Bottom"
msgstr "จัดตำแหน่งด้านล่าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Middle"
msgstr "จัดตำแหน่งตรงกลาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Top"
msgstr "จัดตำแหน่งด้านบน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Alignment"
msgstr "การจัดตำแหน่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Aline "
"เป็นหนึ่งในบุคคลต้นแบบสำคัญของผู้คนที่สามารถพูดได้ว่าเธอรักในสิ่งที่เธอทำอย่างแท้จริง"
" เธอให้คำปรึกษานักพัฒนาภายในองค์กรมากกว่า 100 "
"รายและดูแลชุมชนนักพัฒนาหลายพันคน"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__
msgid "All"
msgstr "ทั้งหมด"

#. module: website
#: model:ir.model,name:website.model_website_route
msgid "All Website Route"
msgstr "เส้นทางเว็บไซต์ทั้งหมด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "All Websites"
msgstr "เว็บไซต์ทั้งหมด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "All informations you need"
msgstr "ข้อมูลทั้งหมดที่คุณต้องการ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "All pages"
msgstr "เพจทั้งหมด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "All results"
msgstr "ผลลัพธ์ทั้งหมด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr "ไอคอนทั้งหมดนี้ไม่มีค่าใช้จ่ายสำหรับการใช้งานเชิงพาณิชย์"

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__track
#: model:ir.model.fields,help:website.field_website_page__track
msgid "Allow to specify for one page of the website to be trackable or not"
msgstr "อนุญาตให้ระบุหน้าเว็บไซต์ว่าสามารถติดตามได้หรือไม่"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_access
msgid "Allowed to use in forms"
msgstr "อนุญาตให้ใช้ในฟอร์ม "

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Already installed"
msgstr "ติดตั้งแล้ว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Image Text"
msgstr "คำอธิบายรูปภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text"
msgstr "คำอธิบาย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image"
msgstr "คำอธิบายรูปภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr "คำอธิบายรูปภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"แม้ว่าเว็บไซต์นี้อาจเชื่อมโยงกับเว็บไซต์อื่น ๆ แต่ไม่ได้หมายความว่าเรายอมรับ"
" เกี่ยวข้อง สนับสนุน รับรอง หรือมีความสัมพันธ์ใด ๆ "
"กับเว็บไซต์ที่มีการเชื่อมโยงโดยตรงหรือโดยอ้อม "
"เว้นแต่จะระบุไว้โดยเฉพาะในที่นี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always Underlined"
msgstr "ขีดเส้นใต้เสมอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Always Visible"
msgstr "มองเห็นได้เสมอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always visible"
msgstr "มองเห็นได้เสมอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Amazing pages"
msgstr "เพจที่น่าตื่นตาตื่นใจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map
msgid "An address must be specified for a map to be embedded"
msgstr "ต้องระบุที่อยู่เพื่อฝังแผนที่"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "An error has occured, the form has not been sent."
msgstr "เกิดข้อผิดพลาดและแบบฟอร์มยังไม่ถูกจัดส่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "An error occurred while rendering the template"
msgstr "เกิดข้อผิดพลาดขณะแสดงเทมเพลต"

#. module: website
#: model:ir.actions.client,name:website.backend_dashboard
#: model:ir.ui.menu,name:website.menu_website_google_analytics
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics"
msgstr "การวิเคราะห์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics cookies and privacy information."
msgstr "คุกกี้การวิเคราะห์และข้อมูลความเป็นส่วนตัว"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Anchor copied to clipboard<br>Link: %s"
msgstr "คัดลอก Anchor ไปยังคลิปบอร์ดแล้ว<br>ลิงก์: %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Anchor name"
msgstr "ชื่อ Anchor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr "และคำบรรยายที่ยอดเยี่ยม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animate"
msgstr "เคลื่อนไหว"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Animate text"
msgstr "ข้อความเคลื่อนไหว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Animated"
msgstr "เคลื่อนไหว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Delay"
msgstr "แอนิเมชันดีเลย์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Duration"
msgstr "ระยะเวลาของการเคลื่อนไหว "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Launch"
msgstr "ดำเนินการทำภาพเคลื่อนไหว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "Another color block"
msgstr "บล็อกสีอื่น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Another feature"
msgstr "ฟีเจอร์อื่น"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__append
msgid "Append"
msgstr "ผนวก"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_add_features
#: model:ir.ui.menu,name:website.menu_website_add_features
msgid "Apps"
msgstr "แอป"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Apps url"
msgstr "แอป URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch
msgid "Arch"
msgstr "Arch"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_db
msgid "Arch Blob"
msgstr "Arch Blob"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_fs
msgid "Arch Filename"
msgstr "Arch Filename"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch_fs
msgid "Arch Fs"
msgstr "Arch Fs"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__specific_user_account
msgid "Are newly created user accounts website specific"
msgstr "เป็นบัญชีผู้ใช้ที่สร้างขึ้นใหม่โดยเฉพาะสำไหรับเว็บไซต์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Are you sure you want to delete this page ?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการลบหน้านี้?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Arrows"
msgstr "ลูกศร"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "As promised, we will offer 4 free tickets to our next summit."
msgstr "ตามที่สัญญาไว้ เราจะมอบทิกเก็ตฟรี 4 ใบสำหรับการประชุมสุดยอดครั้งต่อไป"

#. module: website
#: model:ir.model,name:website.model_ir_asset
msgid "Asset"
msgstr "Asset"

#. module: website
#: model:ir.model,name:website.model_web_editor_assets
msgid "Assets Utils"
msgstr "Assets ยูทิลิตี้"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__copy_ids
msgid "Assets using a copy of me"
msgstr "Assets โดยใช้สำเนาของฉัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "At The End"
msgstr "ในตอนจบ"

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "Attachment"
msgstr "การแนบ"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__copy_ids
msgid "Attachment using a copy of me"
msgstr "แนบไฟล์โดยใช้สำเนาของฉัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Authenticate users, protect user data and allow the website to deliver the services users expects,\n"
"                                                such as maintaining the content of their cart, or allowing file uploads."
msgstr ""
"ตรวจสอบผู้ใช้ ปกป้องข้อมูลผู้ใช้ และอนุญาตให้เว็บไซต์ให้บริการตามที่ผู้ใช้คาดหวัง\n"
"                                                เช่น การรักษาเนื้อหาในรถเข็น หรือการอนุญาตให้อัปโหลดไฟล์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Author"
msgstr "ผู้เขียน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Auto"
msgstr "อัตโนมัติ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Automatically opens the pop-up if the user stays on a page longer than the "
"specified time."
msgstr "เปิดป๊อปอัปโดยอัตโนมัติหากผู้ใช้อยู่ในหน้านานกว่าเวลาที่กำหนด"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__auto_redirect_lang
msgid "Autoredirect Language"
msgstr "เปลี่ยนเส้นทางภาษาอัตโนมัติ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Autosizing"
msgstr "ปรับขนาดอัตโนมัติ"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_published
#: model:ir.model.fields,field_description:website.field_ir_cron__website_published
msgid "Available on the Website"
msgstr "มีอยู่ในเว็บไซต์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"
msgstr "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "BTS Base Colors"
msgstr "สี BTS Base "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Backdrop"
msgstr "ฉากหลัง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Background"
msgstr "พื้นหลัง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#, python-format
msgid "Background Shape"
msgstr "รูปร่างพื้นหลัง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Badge"
msgstr "ป้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Bags"
msgstr "กระเป๋า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Horizontal"
msgstr "บาร์แนวนอน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Vertical"
msgstr "บาร์แนวตั้ง"

#. module: website
#: model:ir.model,name:website.model_base
msgid "Base"
msgstr "Base"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_base
msgid "Base View Architecture"
msgstr "สถาปัตยกรรมของ Base View"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__primary
msgid "Base view"
msgstr "Base view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr "การขายขั้นพื้นฐานและการตลาดสำหรับผู้ใช้สูงสุด 2 คน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Beautiful snippets"
msgstr "ชุดข้อความที่สวยงาม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Beef Carpaccio"
msgstr "คาร์ปาชโช่เนื้อวัว"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__before
msgid "Before"
msgstr "ก่อนหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr "ผู้เริ่มต้น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Below Each Other"
msgstr "ใต้กันและกัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Big"
msgstr "ใหญ่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big Icons Subtitles"
msgstr "คำบรรยายไอคอนขนาดใหญ่"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr "แบล็คลิสฟิลด์นี้สำหรับแบบฟอร์มเว็บ"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr "แบล็คลิสต์ในแบบฟอร์มเว็บ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Blazers"
msgstr "เสื้อเบลเซอร์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Block"
msgstr "บล็อก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Blockquote"
msgstr "บล็อกคำพูด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Blog"
msgstr "บล็อก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Blog Post"
msgstr "บล็อกโพสต์"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_news
msgid "Blogging and posting relevant content"
msgstr "บล็อกและโพสต์เนื้อหาที่เกี่ยวข้อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Books"
msgstr "หนังสือ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Bootstrap-based templates"
msgstr "Bootstrap-based templates"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
#, python-format
msgid "Border"
msgstr "กรอบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Bottom"
msgstr "ขอบล่าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Border Color"
msgstr "สีเส้นขอบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Radius"
msgstr "รูปร่างขอบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Width"
msgstr "ความกว้างเส้นขอบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bordered"
msgstr "เพิ่มขอบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Bottom"
msgstr "ล่าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Bottom to Top"
msgstr "จากล่างขึ้นบน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In"
msgstr "เด้งเข้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Down"
msgstr "เด้งเข้า-ลง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Left"
msgstr "เด้งเข้า-ซ้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Right"
msgstr "เด้งเข้า-ขวา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Boxed"
msgstr "กล่อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Boxes"
msgstr "กล่อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Breadcrumb"
msgstr "Breadcrumb"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Build my website"
msgstr "สร้างเว็บไซต์ของฉัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Building blocks system"
msgstr "ระบบกล่องข้อความ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Building your %s"
msgstr "สร้าง %sของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Building your website..."
msgstr "การสร้างเว็บไซต์ของคุณ..."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__bundle
msgid "Bundle"
msgstr "กลุ่ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Button"
msgstr "ปุ่ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Button Position"
msgstr "ตำแหน่งปุ่ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Buttons"
msgstr "ปุ่ม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "By clicking on this banner, you give us permission to collect data."
msgstr "การคลิกที่แบนเนอร์นี้แสดงว่าคุณอนุญาตให้เราเก็บรวบรวมข้อมูล"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_url
#: model:ir.model.fields,field_description:website.field_website__cdn_url
msgid "CDN Base URL"
msgstr "CDN Base URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,field_description:website.field_website__cdn_filters
msgid "CDN Filters"
msgstr "ตัวกรอง CDN "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "CTA"
msgstr "CTA"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__cache_key_expr
msgid "Cache Key Expr"
msgstr "Cache Key Expr"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__cache_time
msgid "Cache Time"
msgstr "Cache Time"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call to Action"
msgstr "การกระตุ้นให้เกิดความสนใจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Call us"
msgstr "โทรหาเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call-to-action"
msgstr "การกระตุ้นให้เกิดความสนใจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Camera"
msgstr "กล้อง"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__can_publish
#: model:ir.model.fields,field_description:website.field_res_users__can_publish
#: model:ir.model.fields,field_description:website.field_website_page__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__can_publish
msgid "Can Publish"
msgstr "สามารถเผยแพร่"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/menu/new_content.js:0
#: code:addons/website/static/src/js/utils.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Cancel"
msgstr "ยกเลิก"

#. module: website
#: code:addons/website/models/res_lang.py:0
#, python-format
msgid "Cannot deactivate a language that is currently used on a website."
msgstr "ไม่สามารถปิดการใช้งานภาษาที่ใช้บนเว็บไซต์ได้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Cannot load google map."
msgstr "ไม่สามารถโหลด google map ได้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Card"
msgstr "การ์ด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Body"
msgstr "การ์ดส่วนเนื้อหา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Footer"
msgstr "การ์ดส่วนท้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Header"
msgstr "การ์ดส่วนหัว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Card Style"
msgstr "สไตล์การ์ด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cards"
msgstr "การ์ด"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_career
msgid "Career"
msgstr "งาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Case Studies"
msgstr "กรณีศึกษา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Category"
msgstr "หมวดหมู่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Category of Cookie"
msgstr "หมวดหมู่ของคุกกี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Center"
msgstr "กลาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered"
msgstr "ตรงกลาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered Logo"
msgstr "โลโก้ตรงกลาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Change Icons"
msgstr "เปลี่ยนไอคอน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Change theme in a few clicks, and browse through Odoo's catalog of\n"
"                            ready-to-use themes available in our app store."
msgstr ""
"เปลี่ยนธีมได้ในไม่กี่คลิก และเรียกดูแคตตาล็อกธีมพร้อมใช้งานของ Odoo\n"
"                            ที่มีไว้ให้เลือกใช้ในร้านค้าแอปของเรา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing the color palette will reset all your color customizations, are you"
" sure you want to proceed?"
msgstr ""
"การเปลี่ยนจานสีจะตั้งค่าการปรับแต่งสีทั้งหมดของคุณ "
"คุณแน่ใจหรือไม่ว่าต้องการดำเนินการต่อ?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing theme requires to leave the editor. This will save all your "
"changes, are you sure you want to proceed? Be careful that changing the "
"theme will reset all your color customizations."
msgstr ""
"การเปลี่ยนธีมจำเป็นต้องออกจากหมวดการแก้ไข "
"การดำเนินการนี้จะบันทึกการเปลี่ยนแปลงทั้งหมดของคุณ "
"คุณแน่ใจหรือไม่ว่าต้องการดำเนินการต่อ? "
"โปรดระวังว่าการเปลี่ยนธีมจะเปลี่ยนการตั้งค่าและการปรับแต่งสีทั้งหมดของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#, python-format
msgid "Chart"
msgstr "แผนภูมิ"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_live_chat
msgid "Chat with visitors to improve traction"
msgstr "พูดคุยกับผู้เข้าชมเพื่อปรับปรุงการดึงลูกค้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "Check out now and get $20 off your first order."
msgstr "ชำระเงินตอนนี้และรับส่วนลด $20 สำหรับการสั่งซื้อครั้งแรกของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Check your configuration."
msgstr "ตรวจสอบการกำหนดค่าของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Check your connection and try again"
msgstr "ตรวจสอบการเชื่อมต่อแล้วลองอีกครั้ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Checkbox"
msgstr "กล่องกาเครื่องหมาย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Cheese Onion Rings"
msgstr "หัวหอมอบชีส"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Chefs Fresh Soup of the Day"
msgstr "ซุปสดใหม่ประจำวันของเชฟ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__field_parent
msgid "Child Field"
msgstr "ฟิลด์ลูก"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__child_id
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Child Menus"
msgstr "เมนูเด็ก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Children"
msgstr "เด็ก ๆ "

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Choose"
msgstr "เลือก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph about it.<br/> It "
"does not have to be long, but it should reinforce your image."
msgstr ""
"เลือกภาพที่มีชีวิตชีวาและเขียนย่อหน้าที่สร้างแรงบันดาลใจเกี่ยวกับภาพนั้น<br/>ไม่ต้องยาว"
" แต่ควรเสริมสร้างภาพลักษณ์ที่ดีของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Choose an anchor name"
msgstr "เลือกชื่อ anchor"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Choose another theme"
msgstr "เลือกธีมอื่น ๆ "

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Choose your favorite"
msgstr "เลือก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Circle"
msgstr "วงกลม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Classic"
msgstr "คลาสสิก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Clean"
msgstr "เรียบง่าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Clever Slogan"
msgstr "สโลแกนที่ชาญฉลาด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Click and change content directly from the front-end: no complex back\n"
"                            end to deal with."
msgstr ""
"คลิกและเปลี่ยนเนื้อหาโดยตรงจากส่วนหน้า ไม่จำเป็นต้องเข้าถึงส่วนหลัง\n"
"                           ที่ซับซ้อน."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Click here to go back to block tab."
msgstr "คลิกที่นี่เพื่อกลับไปที่บล็อกแท็บ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Click on"
msgstr "คลิกที่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Click on the icon to adapt it <br/>to your purpose."
msgstr "คลิกที่ไอคอนเพื่อปรับเปลี่ยน <br/>ตามจุดประสงค์ของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Click to choose more images"
msgstr "คลิกเพื่อเลือกภาพเพิ่มเติม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Click to select"
msgstr "คลิกเพื่อเลือก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client ID"
msgstr "ไอดีลูกค้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client Secret"
msgstr "Client Secret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Clone this page"
msgstr "ทำสำเนาหน้านี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.s_popup
#: model_terms:ir.ui.view,arch_db:website.show_website_info
#, python-format
msgid "Close"
msgstr "ปิด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Close Button Color"
msgstr "สีปุ่มปิด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Clothes"
msgstr "เสื้อผ้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
msgid "Code"
msgstr "โค้ด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Code Injection"
msgstr "Code Injection"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Collapse Icon"
msgstr "การยุบไอคอน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Color"
msgstr "สี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""
"บล็อกสีเป็นวิธีที่ง่ายและมีประสิทธิภาพในการ "
"<b>นำเสนอและเน้นเนื้อหาของคุณ</b>เลือกรูปภาพหรือสีสำหรับพื้นหลัง "
"คุณยังสามารถปรับขนาดและสร้างบล็อกซ้ำเพื่อสร้างเค้าโครงของคุณเองได้ "
"ทั้งยังสามารถเพิ่มรูปภาพหรือไอคอนเพื่อปรับแต่งบล็อก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Color filter"
msgstr "ตัวกรองสี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Colors"
msgstr "สี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Columns"
msgstr "คอลัมน์"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__website_config_preselection
msgid ""
"Comma-separated list of website type/purpose for which this feature should "
"be pre-selected"
msgstr ""
"รายการประเภทเว็บไซต์/วัตถุประสงค์ที่คั่นด้วยเครื่องหมายจุลภาคที่ควรเลือกฟีเจอร์นี้ล่วงหน้า"

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "หลายบริษัท"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website__company_id
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Company"
msgstr "บริษัทเดียว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr "CRM ที่สมบูรณ์แบบสำหรับทีมทุกขนาด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Components"
msgstr "ชิ้นส่วนต่าง ๆ "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers"
msgstr "คอมพิวเตอร์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers &amp; Devices"
msgstr "คอมพิวเตอร์และอุปกรณ์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Conditionally"
msgstr "แบบมีเงื่อนไข"

#. module: website
#: model:ir.model,name:website.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__configurator_done
msgid "Configurator Done"
msgstr "การกำหนดค่าเสร็จสิ้น"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_social_network
msgid "Configure Social Network"
msgstr "กำหนดค่าสื่อสังคมออนไลน์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Connect Google Analytics"
msgstr "เชื่อมต่อ Google Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Connect with us"
msgstr "เชื่อมต่อกับเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Connected"
msgstr "เชื่อมต่อ"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_search_console
msgid "Console Google Search"
msgstr "Console Google Search"

#. module: website
#: model:ir.model,name:website.model_res_partner
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_id
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Contact"
msgstr "ติดต่อ"

#. module: website
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.header_call_to_action
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_banner
#, python-format
msgid "Contact Us"
msgstr "ติดต่อเรา"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Contact Visitor"
msgstr "ติดต่อผู้เยี่ยมชม"

#. module: website
#: model:website.menu,name:website.menu_contactus
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Contact us"
msgstr "ติดต่อเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                    We'll do our best to get back to you as soon as possible."
msgstr ""
"ติดต่อเราเกี่ยวกับสิ่งที่เกี่ยวข้องกับบริษัทหรือบริการของเรา<br/>\n"
"                                    เราจะพยายามติดต่อกลับโดยเร็วที่สุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Contact us anytime"
msgstr "ติดต่อเราได้ตลอดเวลา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Contact us for any issue or question"
msgstr "ติดต่อเราสำหรับปัญหาหรือคำถามใด ๆ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Contacts"
msgstr "การติดต่อ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Contains"
msgstr "ประกอบด้วย"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_robots__content
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Content"
msgstr "เนื้อหา"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_activated
#: model:ir.model.fields,field_description:website.field_website__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr "Content Delivery Network (CDN)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Content Width"
msgstr "ความกว้างของเนื้อหา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Content to translate"
msgstr "เนื้อหาที่จะแปล"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Continue"
msgstr "ต่อไป"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"
msgstr "อ่านต่อไป<i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "Cookie Policy"
msgstr "นโยบายคุกกี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "Cookie bars may significantly impair the experience"
msgstr "แถบคุกกี้อาจทำให้ประสบการณ์ลดลงอย่างมาก"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,field_description:website.field_website__cookies_bar
msgid "Cookies Bar"
msgstr "แถบคุกกี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Cookies are small bits of text sent by our servers to your computer or device when you access our services.\n"
"                            They are stored in your browser and later sent back to our servers so that we can provide contextual content.\n"
"                            Without cookies, using the web would be a much more frustrating experience.\n"
"                            We use them to support your activities on our website. For example, your session (so you don't have to login again) or your shopping cart.\n"
"                            <br/>\n"
"                            Cookies are also used to help us understand your preferences based on previous or current activity on our website (the pages you have\n"
"                            visited), your language and country, which enables us to provide you with improved services.\n"
"                            We also use cookies to help us compile aggregate data about site traffic and site interaction so that we can offer\n"
"                            better site experiences and tools in the future."
msgstr ""
"คุกกี้คือข้อความขนาดเล็กที่เซิร์ฟเวอร์ของเราส่งไปยังคอมพิวเตอร์หรืออุปกรณ์ของคุณเมื่อคุณเข้าถึงบริการของเรา\n"
"                            สิ่งเหล่านี้จะถูกเก็บไว้ในเบราว์เซอร์ของคุณ และส่งกลับไปยังเซิร์ฟเวอร์ของเราในภายหลัง เพื่อให้เราสามารถจัดหาเนื้อหาตามบริบทได้\n"
"                            หากไม่มีคุกกี้ การใช้เว็บจะเป็นประสบการณ์ที่น่าผิดหวังมากขึ้น\n"
"                            เราใช้คุกกี้เพื่อสนับสนุนกิจกรรมของคุณบนเว็บไซต์ของเรา ตัวอย่างเช่น เซสชั่นของคุณเพื่อที่คุณจะได้ไม่ต้องเข้าสู่ระบบอีกครั้ง หรือรถเข็นสินค้าของคุณ\n"
"                            <br/>\n"
"                            คุกกี้ยังใช้เพื่อช่วยให้เราเข้าใจการตั้งค่าของคุณตามกิจกรรมก่อนหน้าหรือปัจจุบันบนเว็บไซต์ของเรา (หน้าที่คุณมีการ\n"
"                            เยี่ยมชม) ภาษาและประเทศของคุณ ซึ่งช่วยให้เราสามารถให้บริการที่ดีขึ้นแก่คุณได้\n"
"                            เรายังใช้คุกกี้เพื่อช่วยเรารวบรวมข้อมูลโดยรวมเกี่ยวกับการเข้าชมเว็บไซต์และการโต้ตอบต่าง ๆ เพื่อให้เราสามารถนำเสนอ\n"
"                            ประสบการณ์เว็บไซต์และเครื่องมือที่ดียิ่งขึ้นในอนาคต"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Copyright"
msgstr "ลิขสิทธิ์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Countdown ends in"
msgstr "นับถอยหลังสิ้นสุดใน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Countdown is over - Firework"
msgstr "นับถอยหลังสิ้นสุด - ดอกไม้ไฟ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Country"
msgstr "ประเทศ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_flag
msgid "Country Flag"
msgstr "ธงประเทศ"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,field_description:website.field_website__country_group_ids
msgid "Country Groups"
msgstr "กลุ่มประเทศ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Course"
msgstr "คอร์ส"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.record_cover
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Cover"
msgstr "หน้าปก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Cover Photo"
msgstr "ภาพหน้าปก"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cover_properties_mixin__cover_properties
msgid "Cover Properties"
msgstr "คุณสมบัติปก"

#. module: website
#: model:ir.model,name:website.model_website_cover_properties_mixin
msgid "Cover Properties Website Mixin"
msgstr "คุณสมบัติปกเว็บไซต์ Mixin"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/utils.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Create"
msgstr "สร้าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr "สร้างเพจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "Create a"
msgstr "สร้าง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Create a Google Project and Get a Key"
msgstr "สร้างโปรเจกต์ใน Google และรับคีย์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Create a New Website"
msgstr "สร้างเว็บไซต์ใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Create a link to target this section"
msgstr "สร้างลิงก์เพื่อกำหนดเป้าหมายของส่วนนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Create new"
msgstr "สร้างใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Create your page from scratch by dragging and dropping pre-made,\n"
"                            fully customizable building blocks."
msgstr ""
"สร้างเพจของคุณตั้งแต่เริ่มต้นด้วยการลากและวางกล่องข้อความสำเร็จรูป\n"
"                         ที่สามารถปรับแต่งได้"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website__create_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_uid
#: model:ir.model.fields,field_description:website.field_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_uid
#: model:ir.model.fields,field_description:website.field_website_robots__create_uid
#: model:ir.model.fields,field_description:website.field_website_route__create_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Created in 2021, the company is young and dynamic. Discover the composition "
"of the team and their skills."
msgstr ""
"ก่อตั้งขึ้นในปี 2021 บริษัทยังใหม่และมีพลังในการขับเคลื่อนสูง "
"ค้นพบองค์ประกอบของทีมและทักษะของพวกเขา"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website__create_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_date
#: model:ir.model.fields,field_description:website.field_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_date
#: model:ir.model.fields,field_description:website.field_website_robots__create_date
#: model:ir.model.fields,field_description:website.field_website_route__create_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#, python-format
msgid "Custom"
msgstr "ปรับแต่ง"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_head
msgid "Custom <head> code"
msgstr "ปรับแต่ง <head> โค้ด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Custom Code"
msgstr "โค้ดที่กำหนดเอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Custom Key"
msgstr "คีย์ที่กำหนดเอง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Custom Url"
msgstr "URL ที่กำหนดเอง"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_footer
msgid "Custom end of <body> code"
msgstr "กำหนดส่วนท้ายของ <body> โค้ด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom end of body code"
msgstr "โค้ดส่วนท้ายที่กำหนดเอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Custom field"
msgstr "ฟิลด์ที่กำหนดเอง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom head code"
msgstr "โค้ดส่วนหัวที่กำหนดเอง"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__auth_signup_uninvited
#: model:ir.model.fields,field_description:website.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "บัญชีของลูกค้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Customers"
msgstr "ลูกค้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Customization tool"
msgstr "เครื่องมือปรับแต่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize"
msgstr "ปรับแต่ง"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__customize_show
msgid "Customize Show"
msgstr "ปรับแต่งการแสดง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M"
msgstr "D - H - M"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M - S"
msgstr "D - H - M - S"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr "ลากกล่องข้อความมาที่นี่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
msgid "Danger"
msgstr "อันตราย"

#. module: website
#: model:ir.ui.menu,name:website.menu_dashboard
msgid "Dashboard"
msgstr "แดชบอร์ด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dashed"
msgstr "เส้นประ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Data"
msgstr "Data"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Border"
msgstr "กรอบข้อความ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Color"
msgstr "สีข้อความ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Border"
msgstr "กรอบชุดข้อมูล"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Color"
msgstr "สีชุดข้อมูล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Date"
msgstr "วันที่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Date &amp; Time"
msgstr "วันที่และเวลา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Days"
msgstr "วัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Decimal Number"
msgstr "เลขทศนิยม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Default"
msgstr "ค่าเริ่มต้น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "สิทธิการเข้าถึงเริ่มต้น"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__default_lang_id
msgid "Default Language"
msgstr "ค่าเริ่มต้นภาษา"

#. module: website
#: model:website.menu,name:website.main_menu
msgid "Default Main Menu"
msgstr "ค่าเริ่มต้นเมนูหลัก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Default Reversed"
msgstr "ย้อนค่าเริ่มต้น"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,field_description:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Social Share Image"
msgstr "ค่าเริ่มต้นรูปภาพที่แชร์ทางสื่อสังคม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Default Value"
msgstr "ตั้งค่าเริ่มต้น"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_id
msgid "Default language"
msgstr "ค่าเริ่มต้นภาษา"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_code
msgid "Default language code"
msgstr "ค่าเริ่มต้นโค้ดภาษา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Delay"
msgstr "ล่าช้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Delete Blocks"
msgstr "ลบบล็อก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Delete Menu Item"
msgstr "ลบรายการเมนู"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Delete Page"
msgstr "ลบเพจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"ลบรูปภาพด้านบนหรือแทนที่ด้วยรูปภาพที่แสดงข้อความของคุณ "
"คลิกที่ภาพเพื่อเปลี่ยนสไตล์ <em>Rounded corner</em> "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Delete this font"
msgstr "ลบฟอนต์นี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Delete this page"
msgstr "ลบเพจนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Deleting a font requires a reload of the page. This will save all your "
"changes and reload the page, are you sure you want to proceed?"
msgstr ""
"การลบฟอนต์ต้องโหลดหน้าซ้ำ "
"การดำเนินการนี้จะบันทึกการเปลี่ยนแปลงทั้งหมดของคุณและโหลดหน้านี้ซ้ำ "
"คุณแน่ใจหรือไม่ว่าต้องการดำเนินการต่อ ? "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Deliveries"
msgstr "การจัดส่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Departments"
msgstr "แผนก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Dependencies"
msgstr "การพึ่งพา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Describe your field here."
msgstr "อธิบายฟิลด์ของคุณที่นี่"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__description
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#, python-format
msgid "Description"
msgstr "รายละเอียด"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_our_services
msgid "Description of your services offer"
msgstr "คำอธิบายข้อเสนอการบริการของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "Descriptions"
msgstr "รายละเอียด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Descriptive"
msgstr "คำบรรยาย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Design"
msgstr "ออกแบบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Design features"
msgstr "ออกแบบฟีเจอร์"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_pricing
msgid "Designed to drive conversion"
msgstr "ออกแบบมาเพื่อขับเคลื่อนการแปลง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Desktop"
msgstr "เดสก์ทอป"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Desktop computers"
msgstr "คอมพิวเตอร์ตั้งโต๊ะ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Detail"
msgstr "รายละเอียด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Details"
msgstr "รายละเอียด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Detect"
msgstr "ตรวจจับ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Direction"
msgstr "ทิศทาง"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__directive
msgid "Directive"
msgstr "คำสั่ง"

#. module: website
#: model:ir.actions.server,name:website.website_disable_unused_snippets_assets_ir_actions_server
#: model:ir.cron,cron_name:website.website_disable_unused_snippets_assets
#: model:ir.cron,name:website.website_disable_unused_snippets_assets
msgid "Disable unused snippets assets"
msgstr "ปิดใช้งานตัวอย่างข้อมูลที่ไม่ได้ใช้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Disabled"
msgstr "Disabled"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Disappearing"
msgstr "หายไป"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disappears"
msgstr "หายไป"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Discard"
msgstr "ละทิ้ง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Discard & Edit in backend"
msgstr "ละทิ้งและแก้ไขในส่วนหลัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Discover"
msgstr "ค้นพบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Discover all the features"
msgstr "ค้นพบฟีเจอร์ทั้งหมด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Discover more"
msgstr "ค้นพบเพิ่มเติม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Discover our culture and our values"
msgstr "ค้นพบวัฒนธรรมและค่านิยมของเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our legal notice"
msgstr "ค้นพบประกาศทางกฎหมายของเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our realisations"
msgstr "ค้นพบความตระหนักของเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "Discover our team"
msgstr "ค้นพบทีมของเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Discrete"
msgstr "แยกส่วน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Discussion Group"
msgstr "กลุ่มสนทนา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Disk"
msgstr "ดิสก์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Display"
msgstr "แสดง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Display Inline"
msgstr "แสดงแบบอินไลน์"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website__display_name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__display_name
#: model:ir.model.fields,field_description:website.field_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website_rewrite__display_name
#: model:ir.model.fields,field_description:website.field_website_robots__display_name
#: model:ir.model.fields,field_description:website.field_website_route__display_name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__display_name
#: model:ir.model.fields,field_description:website.field_website_track__display_name
#: model:ir.model.fields,field_description:website.field_website_visitor__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,help:website.field_website__cookies_bar
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display a customizable cookies bar on your website."
msgstr "แสดงแถบคุกกี้ที่ปรับแต่งได้บนเว็บไซต์ของคุณ"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the badges"
msgstr "แสดงป้าย"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the biography"
msgstr "แสดงชีวประวัติ"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the website description"
msgstr "แสดงคำอธิบายเว็บไซต์"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_logo
#: model:ir.model.fields,help:website.field_website__logo
msgid "Display this logo on the website."
msgstr "แสดงโลโก้นี้บนเว็บไซต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display this website when users visit this domain"
msgstr "แสดงเว็บไซต์นี้เมื่อผู้ใช้เยี่ยมชมโดเมนนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Do not activate"
msgstr "อย่าเปิดใช้งาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Do you need specific information? Our specialists will help you with "
"pleasure."
msgstr ""
"คุณต้องการข้อมูลเฉพาะหรือไม่ ? ผู้เชี่ยวชาญของเรายินดีให้ความช่วยเหลือ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Do you want to edit the company data ?"
msgstr "คุณต้องการแก้ไขข้อมูลบริษัทหรือไม่ ? "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Do you want to install the \"%s\" App?"
msgstr "คุณต้องการติดตั้งแอป \"%s\" หรือไม่ ? "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Documentation"
msgstr "เอกสารประกอบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Doesn't contain"
msgstr "ไม่มี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Domain"
msgstr "โดเมน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Don't forget to update all links referring to this page."
msgstr "อย่าลืมอัปเดตลิงก์ทั้งหมดที่อ้างอิงถึงหน้านี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/theme_preview_kanban.js:0
#, python-format
msgid "Don't worry, you can switch later."
msgstr "ไม่ต้องกังวล คุณสามารถเปลี่ยนได้ในภายหลัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation"
msgstr "การโดเนท"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation Button"
msgstr "ปุ่มโดเนท"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Dots"
msgstr "จุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dotted"
msgstr "เส้นจุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Double"
msgstr "สองเส้น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr "ดับเบิลคลิกที่ไอคอนเพื่อแทนที่ด้วยไอคอนที่คุณเลือก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Doughnut"
msgstr "โดนัท"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"Drag the <b>%s</b> building block and drop it at the bottom of the page."
msgstr "ลาก<b>%s</b> กล่องข้อความสำเร็จรูปและวางลงที่ด้านล่างของเพจ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Drag to the right to get a submenu"
msgstr "ลากไปทางขวาเพื่อปรับเมนูย่อย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Dresses"
msgstr "ชุดกระโปรง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Dropdown"
msgstr "ดรอปดาว์น"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "เมนูดรอปดาว์น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Due Date"
msgstr "วันครบกำหนด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate"
msgstr "ทำซ้ำ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Duplicate Page"
msgstr "ทำซ้ำหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Duplicate blocks <br/>to add more steps."
msgstr "ทำซ้ำบล็อก <br/> เพื่อเพิ่มขั้นตอน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr "ทำซ้ำบล็อกและคอลัมน์เพื่อเพิ่มฟีเจอร์อื่น ๆ "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Content"
msgstr "เนื้อหาแบบเคลื่อนไหว"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_domain
#: model:ir.model.fields,help:website.field_website__domain
msgid "E.g. https://www.mydomain.com"
msgstr "เช่น https://www.mydomain.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Easily design your own Odoo templates thanks to clean HTML\n"
"                            structure and bootstrap CSS."
msgstr ""
"ออกแบบเทมเพลต Odoo ของคุณเองอย่างง่ายดายด้วยโครงสร้าง HTML \n"
"                            ที่ชัดเจนและ Bootstrap CSS"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit"
msgstr "แก้ไข"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/widgets/link_popover_widget.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Edit Menu"
msgstr "แก้ไขเมนู"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Edit Menu Item"
msgstr "แก้ไขรายการเมนู"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Edit Message"
msgstr "แก้ไขข้อความ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Edit Styles"
msgstr "แก้ไขสไตล์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit Top Menu"
msgstr "แก้ไขเมนูส่วนบน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Edit code in backend"
msgstr "แก้ไขโค้ดในส่วนหลัง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit embedded code"
msgstr "แก้ไขโค้ดที่ถูกฝัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit in backend"
msgstr "แก้ไขในส่วนหลัง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Edit my Analytics Client ID"
msgstr "แก้ไข Analytics Client ID ของฉัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Edit robots.txt"
msgstr "แก้ไข robots.txt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Edit video"
msgstr "แก้ไขวิดีโอ"

#. module: website
#: model:res.groups,name:website.group_website_designer
msgid "Editor and Designer"
msgstr "แก้ไขและออกแบบ"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Either action_server_id or filter_id must be provided."
msgstr "ต้องระบุ action_server_id หรือ filter_id"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Electronics"
msgstr "อิเล็กทรอนิกส์"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__email
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Email"
msgstr "อีเมล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Email address"
msgstr "ที่อยู่อีเมล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr "การสนับสนุนทางอีเมล"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Empty field name in %r"
msgstr "ชื่อฟิลด์ว่างใน %r"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable billing on your Google Project"
msgstr "เปิดใช้งานการเรียกเก็บเงินใน Google Project ของคุณ"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_access
msgid "Enable the form builder feature for this model."
msgstr "เปิดใช้งานฟีเจอร์ตัวสร้างฟอร์มสำหรับรุ่นนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable the right google map APIs in your google account"
msgstr "เปิดใช้งาน Google Map API ที่ถูกต้องในบัญชี Google ของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Enter an API Key"
msgstr "ใส่ API Key"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added before the </body> of every page of your site."
msgstr "ป้อนโค้ดที่จะเพิ่มก่อน </body> ของทุกหน้าในเว็บไซต์ของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Enter code that will be added into every page of your site"
msgstr "ป้อนโค้ดที่จะเพิ่มลงในทุกหน้าเว็บไซต์ของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added into the <head> of every page of your site."
msgstr "ป้อนโค้ดที่จะเพิ่มลงใน <head> ในทุกหน้าเว็บไซต์ของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Enter email"
msgstr "กรอกอีเมล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Equal Widths"
msgstr "ความกว้างเท่ากัน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Error"
msgstr "ผิดพลาด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Essential oils"
msgstr "น้ำมันหอมระเหย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Event"
msgstr "อีเวนต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Event heading"
msgstr "หัวข้ออีเวนต์"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_event
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Events"
msgstr "อีเวนต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Every Time"
msgstr "ทุกเวลา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Everything"
msgstr "ทุกอย่าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Examples"
msgstr "ตัวอย่าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr "ผู้เชี่ยวชาญ"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_privacy_policy
msgid "Explain how you protect privacy"
msgstr "อธิบายว่าคุณสามารถปกป้องความเป็นส่วนตัวได้อย่างไร"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert
msgid ""
"Explain the benefits you offer. <br/>Don't write about products or services "
"here, write about solutions."
msgstr ""
"อธิบายประโยชน์ที่คุณนำเสนอ <br/> "
"อย่าเขียนเกี่ยวกับผลิตภัณฑ์หรือบริการที่นี่เขียนเกี่ยวกับโซลูชัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Explore"
msgstr "สำรวจ"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__cache_key_expr
msgid ""
"Expression (tuple) to evaluate the cached key. \n"
"E.g.: \"(request.params.get(\"currency\"), )\""
msgstr ""
"Expression (tuple) เพื่อประเมินคีย์ที่แคชไว้\n"
"เช่น: \"(request.params.get(\"currency\"), )\""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__extension
msgid "Extension View"
msgstr "Extension View"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,field_description:website.field_ir_cron__xml_id
#: model:ir.model.fields,field_description:website.field_website_page__xml_id
msgid "External ID"
msgstr "ไอดีจากภายนอก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Extra Large"
msgstr "ใหญ่พิเศษ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Extra link"
msgstr "ลิงก์เสริม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Large"
msgstr "ใหญ่มาก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Small"
msgstr "เล็กมาก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "F.A.Q."
msgstr "F.A.Q."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Facebook"
msgstr "Facebook"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_facebook
#: model:ir.model.fields,field_description:website.field_website__social_facebook
msgid "Facebook Account"
msgstr "บัญชีเฟสบุ๊ค"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade"
msgstr "จาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In"
msgstr "จางเข้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Down"
msgstr "จางเข้า-ลง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Left"
msgstr "จางเข้า-ซ้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Right"
msgstr "จางเข้า-ขวา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Up"
msgstr "จางเข้า-บน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade Out"
msgstr "จางออก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Failed to install \"%s\""
msgstr "การติดตั้งล้มเหลว \"%s\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Farm Friendly Chicken Supreme"
msgstr "ไก่สุพรีมจากฟาร์ม"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__favicon
msgid "Favicon"
msgstr "Favicon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr "ฟีเจอร์หนึ่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr "ฟีเจอร์สาม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "Feature Title"
msgstr "ชื่อฟีเจอร์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr "ฟีเจอร์สอง"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__feature_url
msgid "Feature Url"
msgstr "ฟีเจอร์ URL"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Features"
msgstr "ฟีเจอร์ต่าง ๆ "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Fetched elements"
msgstr "องค์ประกอบที่ดึงมา"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__field_names
msgid "Field Names"
msgstr "ชื่อฟิลด์"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_default_field_id
msgid "Field for custom form data"
msgstr "ฟิลด์หรับข้อมูลแบบฟอร์มที่กำหนดเอง"

#. module: website
#: model:ir.model,name:website.model_ir_model_fields
msgid "Fields"
msgstr "ฟิลด์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "File Upload"
msgstr "อัปโหลดไฟล์"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_fs
msgid ""
"File from where the view originates.\n"
"                                                          Useful to (hard) reset broken views or to read arch from file in dev-xml mode."
msgstr ""
"ไฟล์จากแหล่งที่มาของมุมมอง\n"
"                                                          ซึ่งมีประโยชน์ในการรีเซ็ตมุมมองที่เสียหายแบบ Hard หรือเพื่ออ่านไฟล์ arch ในโหมด dev-xml"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Filet Mignon 8oz"
msgstr "ฟิเลต์มิญอง 8 ออนซ์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fill"
msgstr "Fill"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Fill and justify"
msgstr "เติมและปรับให้เหมาะสม"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__filter_id
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Filter"
msgstr "ตัวกรอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Filter Intensity"
msgstr "ความเข้มของตัวกรอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Find a store near you"
msgstr "ค้นหาร้านค้าใกล้คุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find all information about our deliveries, express deliveries and all you "
"need to know to return a product."
msgstr ""
"ค้นหาข้อมูลทั้งหมดเกี่ยวกับการจัดส่งของเรา การจัดส่งด่วน "
"และทุกสิ่งที่คุณจำเป็นต้องรู้เมื่อต้องการส่งคืนสินค้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find out how we were able helping them and set in place solutions adapted to"
" their needs."
msgstr ""
"ค้นหาว่าเราสามารถช่วยเหลือพวกเขาได้อย่างไรและสามารถสร้างโซลูชันให้เข้ากับความต้องการของพวกเขาได้อย่างไร"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Find the perfect solution for you"
msgstr "ค้นหาโซลูชันที่สมบูรณ์แบบสำหรับคุณ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__create_date
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "First Connection"
msgstr "การเชื่อมต่อครั้งแรก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr "ฟีเจอร์แรก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "First Menu"
msgstr "เมนูแรก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "First Time Only"
msgstr "ครั้งแรกเท่านั้น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "First feature"
msgstr "ฟีเจอร์แรก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "First list of Features"
msgstr "รายการฟีเจอร์แรก"

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,help:website.field_website_page__first_page_id
msgid "First page linked to this view"
msgstr "หน้าแรกที่เชื่อมโยงกับมุมมองนี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit content"
msgstr "เนื้อหาที่เหมาะสม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit text"
msgstr "ข้อความที่เหมาะสม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Fixed"
msgstr "คงที่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag"
msgstr "ธง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag and Text"
msgstr "ป้ายและข้อความ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flash"
msgstr "แฟลช"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Flat"
msgstr "เรียบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-X"
msgstr "พลิก-เข้า-X"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-Y"
msgstr "พลิก-เข้า-Y"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Float"
msgstr "ลอย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#: model_terms:ir.ui.view,arch_db:website.template_header_boxed_oe_structure_header_boxed_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_full_oe_structure_header_hamburger_full_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_1
msgid "Follow us"
msgstr "ติดตามเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Follow your website traffic in Odoo."
msgstr "ติดตามการเข้าชมเว็บไซต์ของคุณใน Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font"
msgstr "ฟอนต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font Size"
msgstr "ขนาดฟอนต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font family"
msgstr "ตระกูลฟอนต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font size"
msgstr "ขนาดฟอนต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__footer_visible
msgid "Footer Visible"
msgstr "มองเห็นส่วนท้าย"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "For session cookies, authentification and analytics,"
msgstr "สำหรับคุกกี้เซสชัน การรับรองความถูกต้องและการวิเคราะห์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Force your user to create an account per website"
msgstr "บังคับให้ผู้ใช้ของคุณสร้างบัญชีต่อเว็บไซต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Form"
msgstr "ฟอร์ม"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr ""
"ป้ายการดำเนินการแบบฟอร์ม เช่น crm.lead อาจเป็น 'ส่งอีเมล' และ project.issue "
"อาจเป็น 'สร้างประเด็น'"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_forum
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Forum"
msgstr "ฟอรั่ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                to keep his hands full by participating in the development of the software,\n"
"                                marketing, and customer experience strategies."
msgstr ""
"ผู้ก่อตั้งและหัวหน้าผู้มีวิสัยทัศน์ Tony คือพลังขับเคลื่อนเบื้องหลังบริษัท เขารัก\n"
"                                ที่จะมีส่วนร่วมอย่างเต็มที่ในการพัฒนาซอฟต์แวร์\n"
"                                กลยุทธ์ทางการตลาดและประสบการณ์ลูกค้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Framed"
msgstr "กรอบ"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "ลงชื่อใช้งานฟรี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Friends' Faces"
msgstr "หน้าเพื่อน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"From seminars to team building activities, we offer a wide choice of events "
"to organize."
msgstr "ตั้งแต่งานสัมมนาไปจนถึงกิจกรรมการสร้างทีม เรามีกิจกรรมให้เลือกมากมาย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full"
msgstr "เต็ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full Screen"
msgstr "เต็มหน้าจอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Full Width"
msgstr "เต็มความกว้าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full screen"
msgstr "เต็มหน้าจอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full-Width"
msgstr "เต็มความกว้าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Furniture"
msgstr "เฟอร์นิเจอร์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "G-XXXXXXXXXX"
msgstr "G-XXXXXXXXXX"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "GPS &amp; navigation"
msgstr "GPS และการนำทาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Gaming"
msgstr "เกมมิ่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Get Delivered"
msgstr "รับการจัดส่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr "เข้าถึงโมดูลทั้งหมด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr "เข้าถึงโมดูลและฟีเจอร์ทั้งหมด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Get in touch"
msgstr "ติดต่อกันอยู่เสมอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "GitHub"
msgstr "GitHub"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_github
#: model:ir.model.fields,field_description:website.field_website__social_github
msgid "GitHub Account"
msgstr "บัญชี GitHub"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_forum
msgid "Give visitors the information they need"
msgstr "ให้ข้อมูลแก่ผู้เยี่ยมชมที่ต้องการ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Glasses"
msgstr "แว่นตา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Go To Page"
msgstr "ไปยังหน้า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Go to"
msgstr "ไปที่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Go to Page Manager"
msgstr "ไปที่ตัวจัดการหน้า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Go to Website"
msgstr "ไปที่เว็บไซต์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Go to the Theme tab"
msgstr "ไปที่แถบธีม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Good copy starts with understanding how your product or service helps your "
"customers. Simple words communicate better than big words and pompous "
"language."
msgstr ""
"สำเนาที่ดีเริ่มต้นด้วยการทำความเข้าใจว่าผลิตภัณฑ์หรือบริการของคุณว่าสามารถช่วยลูกค้าได้อย่างไร"
" คำง่าย ๆ สามารถสื่อสารได้ดีกว่าคำที่ใหญ่โตและภาษาที่เข้าใจยาก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Good job! It's time to <b>Save</b> your work."
msgstr "ยอดเยี่ยมมาก! ถึงเวลา <b>บันทึก</b> งานของคุณแล้ว"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics
msgid "Google Analytics"
msgstr "Google Analytics"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics_dashboard
msgid "Google Analytics Dashboard"
msgstr "แดชบอร์ดของ Google Analytics "

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_analytics_key
#: model:ir.model.fields,field_description:website.field_website__google_analytics_key
msgid "Google Analytics Key"
msgstr "Google Analytics Key"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Google Analytics initialization failed. Maybe this domain is not whitelisted"
" in your Google Analytics project for this client ID."
msgstr ""
"การเริ่มต้น Google Analytics ล้มเหลว "
"บางทีโดเมนนี้อาจไม่อยู่ในรายการที่อนุญาตหรือน่าเชื่อถือในโปรเจกต์ Google "
"Analytics ของไอดีลูกค้านี้"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_id
#: model:ir.model.fields,field_description:website.field_website__google_management_client_id
msgid "Google Client ID"
msgstr "ไอดีลูกค้า Google "

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_secret
#: model:ir.model.fields,field_description:website.field_website__google_management_client_secret
msgid "Google Client Secret"
msgstr "Google Client Secret"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Google Font address"
msgstr "ที่อยู่ฟอนต์ของ Google"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Google Map"
msgstr "Google Map"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Google Map API Key"
msgstr "Google Map API Key"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_maps
msgid "Google Maps"
msgstr "Google Maps"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_maps_api_key
#: model:ir.model.fields,field_description:website.field_website__google_maps_api_key
msgid "Google Maps API Key"
msgstr "Google Maps API Key"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,field_description:website.field_website__google_search_console
msgid "Google Search Console"
msgstr "Google Search Console"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Google deprecated both its \"Universal Analytics\" and \"Google Sign-In\" "
"API. It means that only accounts and keys created before 2020 will be able "
"to integrate their Analytics dashboard in Odoo (or any other website). This "
"will be possible only up to mid 2023. After that, those services won't work "
"anymore, at all."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,help:website.field_website__google_search_console
msgid "Google key, or Enable to access first reply"
msgstr " Google คีย์ หรือเปิดใช้งานเพื่อเข้าถึงการตอบกลับครั้งแรก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Gray #{grayCode}"
msgstr "Gray #{grayCode}"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grays"
msgstr "สีเทา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Great Value"
msgstr "ทรงคุณค่า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"เรื่องราวดี ๆ  <b>สำหรับทุกคน</b> "
"แม้ว่าจะถูกเขียนขึ้น<b>เพื่อคนเพียงคนเดียว</b>หากคุณพยายามเขียนโดยคำนึงถึงผู้ชมทั่วในวงกว้าง"
" เรื่องราวของคุณจะฟังดูไร้สาระและขาดอารมณ์ จะไม่มีใครสนใจ "
"เขียนเพื่อคนหนึ่งคน หากมันงดงามสำหรับหนึ่งคน ก็งดงามสำหรับคนอื่นเช่นกัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"เรื่องราวที่ยิ่งใหญ่มักมี "
"<b>ลักษณะเฉพาะ</b>พิจารณาการเล่าเรื่องราวที่มีลักษณะเฉพาะ "
"การเขียนเรื่องราวที่มีเอกลักษณ์นี้มีไว้สำหรับผู้มีโอกาสเป็นลูกค้าและจะช่วยในการสร้างความสัมพันธ์"
" สิ่งนี้แสดงให้เห็นเอกลักษณ์ต่าง ๆ เช่นการเลือกคำหรือวลี "
"และจงเขียนจากมุมมองของคุณเอง ไม่ใช่จากประสบการณ์ของคนอื่น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Grid"
msgstr "ตาราง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Group By"
msgstr "จัดกลุ่มโดย"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__groups_id
msgid "Groups"
msgstr "กลุ่ม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H1"
msgstr "H1"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H2"
msgstr "H2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "H4 Card title"
msgstr "H4 Card title"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "H5 Card subtitle"
msgstr "H5 Card subtitle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HTML/CSS/JS Editor"
msgstr "ตัวแก้ไข HTML/CSS/JS"

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half Screen"
msgstr "ครึ่งจอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half screen"
msgstr "ครึ่งจอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Full"
msgstr "ปุ่มแฮมเบอร์เกอร์ เต็ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Type"
msgstr "ประเภทแฮมเบอร์เกอร์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger menu"
msgstr "เมนูแฮมเบอร์เกอร์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Happy Odoo Anniversary!"
msgstr "สุขสันต์วันครบรอบ Odoo!"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__has_social_default_image
msgid "Has Social Default Image"
msgstr "มีภาพเริ่มต้นทางสังคม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Header"
msgstr "ส่วนหัว"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_color
msgid "Header Color"
msgstr "สีส่วนหัว"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_overlay
msgid "Header Overlay"
msgstr "ส่วนหัวแบบ overlay"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Header Position"
msgstr "ตำแหน่งส่วนหัว"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_visible
msgid "Header Visible"
msgstr "มองเห็นส่วนหัว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 1"
msgstr "หัวเรื่อง 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 2"
msgstr "หัวเรื่อง 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 3"
msgstr "หัวเรื่อง 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 4"
msgstr "หัวเรื่อง 4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 5"
msgstr "หัวเรื่อง 5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 6"
msgstr "หัวเรื่อง 6"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Headline"
msgstr "หัวเรื่อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height"
msgstr "ความสูง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height (Scrolled)"
msgstr "ความสูง (เลื่อน)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Help center"
msgstr "ศูนย์ช่วยเหลือ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr "นี่คือภาพที่ใช้เพื่อช่วยให้คุณแปลได้อย่างมีประสิทธิภาพ:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Here is an overview of the cookies that may be stored on your device when "
"you visit our website:"
msgstr ""
"นี่คือภาพรวมของคุกกี้ที่อาจจัดเก็บไว้ในอุปกรณ์ของคุณเมื่อคุณเยี่ยมชมเว็บไซต์ของเรา:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hidden"
msgstr "ซ่อนแล้ว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Hidden for"
msgstr "ซ่อนแล้วเพื่อ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Hidden on mobile"
msgstr "ซ่อนบนมือถือ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Hide"
msgstr "ซ่อน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Hide For"
msgstr "ซ่อนเพื่อ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Hide this page from search results"
msgstr "ซ่อนหน้านี้จากผลการค้นหา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "High"
msgstr "สูง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: How to use Google Map on your website (Contact Us page and as a "
"snippet)"
msgstr ""
"Hint: วิธีใช้ Google Map บนเว็บไซต์ของคุณ (เพจติดต่อเราและเป็น Snippet)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: Type '/' to search an existing page and '#' to link to an anchor."
msgstr ""
"Hint: พิมพ์ '/' เพื่อค้นหาหน้าที่มีอยู่และ '#' เพื่อเชื่อมต่อไปยัง Anchor"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: model:website.menu,name:website.menu_home
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Home"
msgstr "โฮมเพจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Home <span class=\"sr-only\">(current)</span>"
msgstr "หน้าแรก <span class=\"sr-only\">(ปัจจุบัน)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Home audio"
msgstr "เครื่องเสียงบ้าน"

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model:ir.model.fields,field_description:website.field_website__homepage_id
#: model:ir.model.fields,field_description:website.field_website_page__is_homepage
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Homepage"
msgstr "Homepage"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Hoodies"
msgstr "เสื้อฮู้ด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Horizontal"
msgstr "แนวนอน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Hours"
msgstr "ชั่วโมง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "How can we help?"
msgstr "เราจะช่วยได้อย่างไร?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Client ID"
msgstr "วิธีการรับไอดีลูกค้าของฉัน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Measurement ID"
msgstr "วิธีการรับ Measurement ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Hybrid"
msgstr "ไฮบริด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "I agree"
msgstr "ฉันเห็นด้วย"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "I want"
msgstr "ฉันต้องการ"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__id
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__id
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__id
#: model:ir.model.fields,field_description:website.field_theme_website_menu__id
#: model:ir.model.fields,field_description:website.field_theme_website_page__id
#: model:ir.model.fields,field_description:website.field_website__id
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__id
#: model:ir.model.fields,field_description:website.field_website_menu__id
#: model:ir.model.fields,field_description:website.field_website_page__id
#: model:ir.model.fields,field_description:website.field_website_rewrite__id
#: model:ir.model.fields,field_description:website.field_website_robots__id
#: model:ir.model.fields,field_description:website.field_website_route__id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__id
#: model:ir.model.fields,field_description:website.field_website_track__id
#: model:ir.model.fields,field_description:website.field_website_visitor__id
msgid "ID"
msgstr "ID"

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,help:website.field_ir_cron__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "ID ของการดำเนินการหากกำหนดไว้ในไฟล์ XML"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__xml_id
msgid "ID of the view defined in xml file"
msgstr "ID ของมุมมองที่กำหนดในไฟล์ xml"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__iap_page_code
msgid "Iap Page Code"
msgstr "โค้ดเพจ Iap "

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__icon
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Icon"
msgstr "ไอคอน"

#. module: website
#: model:ir.model.fields,help:website.field_website__specific_user_account
msgid "If True, new accounts will be associated to the current website"
msgstr "หากเป็นจริง บัญชีใหม่จะเชื่อมโยงกับเว็บไซต์ปัจจุบัน"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_sequence
msgid "If set, a website menu will be created for the feature."
msgstr "หากตั้งค่าไว้ เมนูเว็บไซต์จะถูกสร้างขึ้นสำหรับฟีเจอร์"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_company
msgid ""
"If set, add the menu as a second level menu, as a child of \"Company\" menu."
msgstr ""
"หากตั้งค่าไว้ สามารถเพิ่มเมนูเป็นเมนูระดับที่สองหรือเมนูลูกของเมนู "
"\"บริษัท\""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,help:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "If set, replaces the website logo as the default social share image."
msgstr ""
"หากตั้งค่าไว้ "
"สามารถแทนที่โลโก้ของเว็บไซต์เป็นรูปภาพเริ่มต้นสำหรับแชร์บนโซเชียล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset the template to its <strong>factory settings</strong>."
msgstr ""
"หากข้อผิดพลาดนี้เกิดจากการที่คุณเปลี่ยนเทมเพลต "
"คุณสามารถตั้งค่าเทมเพลตใหม่เป็น <strong>การตั้งค่าโรงงาน</strong>"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__groups_id
msgid ""
"If this field is empty, the view applies to all users. Otherwise, the view "
"applies to the users of those groups only."
msgstr ""
"ถ้าฟิลด์นี้ว่างเปล่ามุมมองจะนำไปใช้กับผู้ใช้ทั้งหมด "
"มิฉะนั้นมุมมองจะใช้กับผู้ใช้ของกลุ่มเหล่านั้นเท่านั้น"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__active
msgid ""
"If this view is inherited,\n"
"* if True, the view always extends its parent\n"
"* if False, the view currently does not extend its parent but can be enabled\n"
"         "
msgstr ""
"หากมุมมองนี้ได้รับการสืบทอด\n"
"* ถ้าเป็นจริง มุมมองจะขยายพาเรนต์เสมอ\n"
"* หากเป็นเท็จ มุมมองจะไม่ขยายพาเรนต์ แต่สามารถเปิดใช้งานได้\n"
"         "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"หากคุณละทิ้งการแก้ไขปัจจุบัน การเปลี่ยนแปลงที่ไม่ได้บันทึกทั้งหมดจะสูญหายไป "
"คุณสามารถยกเลิกเพื่อกลับสู่โหมดแก้ไข"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_image
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
#, python-format
msgid "Image"
msgstr "รูปภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Image Cover"
msgstr "รูปภาพปก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Image Menu"
msgstr "เมนูภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Image Size"
msgstr "ขนาดภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Image Text Image"
msgstr "รูปภาพ ข้อความ รูปภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Images"
msgstr "รูปภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Images Spacing"
msgstr "ระยะห่างของรูปภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Images Subtitles"
msgstr "คำบรรยายรูปภาพ "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "In main menu"
msgstr "ในเมนูหลัก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "In the meantime we invite you to visit our"
msgstr "ในระหว่างนี้เราขอเชิญคุณเยี่ยมชม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid ""
"In this mode, you can only translate texts. To change the structure of the page, you must edit the master page.\n"
"        Each modification on the master page is automatically applied to all translated versions."
msgstr ""
"ในโหมดนี้คุณสามารถแปลได้เฉพาะข้อความเท่านั้น ในการเปลี่ยนโครงสร้างของเพจคุณต้องแก้ไขจากเพจต้นแบบ\n"
"         การแก้ไขแต่ละรายการในหน้าต้นแบบจะถูกนำไปใช้กับเวอร์ชันที่แปลทั้งหมดโดยอัตโนมัติ"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__include
msgid "Include"
msgstr "รวมถึง"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Incorrect Client ID / Key"
msgstr "ไอดีลูกค้า / คีย์ไม่ถูกต้อง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.index_management
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Indexed"
msgstr "ทำดัชนี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Indicators"
msgstr "ตัวชี้วัด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Info"
msgstr "ข้อมูล"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_about_us
msgid "Info and stats about your company"
msgstr "ข้อมูลและสถิติเกี่ยวที่กับบริษัทของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr "ข้อมูลเกี่ยวกับ"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__inherit_id
msgid "Inherit"
msgstr "สืบทอด"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_id
msgid "Inherited View"
msgstr "สืบทอดมุมมอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inline"
msgstr "อินไลน์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Inner"
msgstr "ด้านใน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Inner content"
msgstr "เนื้อหาด้านใน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Aligned"
msgstr "ใส่ข้อมูลตรงกัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Type"
msgstr "ประเภทการนำเข้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inputs"
msgstr "การนำเข้า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a badge snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a blockquote snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a card snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a chart snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a progress bar snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a rating snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a share snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a text Highlight snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert an alert snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert an horizontal separator sippet."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Insert text styles like headers, bold, italic, lists, and fonts with\n"
"                            a simple WYSIWYG editor. Flexible and easy to use."
msgstr ""
"แทรกรูปแบบข้อความ เช่น ส่วนหัว ตัวหนา ตัวเอียง รายการ และฟอนต์ด้วย\n"
"                            โปรแกรมแก้ไข WYSIWYG ที่ยืดหยุ่นและใช้งานง่าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Inset"
msgstr "แทรก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Instagram"
msgstr "Instagram"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_instagram
#: model:ir.model.fields,field_description:website.field_website__social_instagram
msgid "Instagram Account"
msgstr "บัญชี Instagram"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Install"
msgstr "ติดตั้ง"

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "ติดตั้งภาษา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Install new language"
msgstr "ติดตั้งภาษาใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr "แอปพลิเคชันที่ติดตั้งแล้ว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Localizations / Account Charts"
msgstr "ติดตั้ง การปรับให้เข้ากับภูมิภาค / แผนภูมิบัญชี"

#. module: website
#: model:ir.model.fields,help:website.field_website__theme_id
msgid "Installed theme"
msgstr "ธีมที่ติดตั้งแล้ว"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Installing \"%s\""
msgstr "กำลังติดตั้ง \"%s\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Interaction History"
msgstr "ประวัติการโต้ตอบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Intuitive system"
msgstr "ระบบที่ใช้งานง่าย"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invalid API Key. The following error was returned by Google:"
msgstr "คีย์ API ไม่ถูกต้อง Google ส่งคืนข้อผิดพลาดต่อไปนี้:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"ด้วยประสบการณ์ระดับนานาชาติของ Iris "
"ช่วยให้เราเข้าใจตัวเลขและปรับปรุงตัวเลขเหล่านี้ได้อย่างง่ายดาย "
"เธอมุ่งมั่นที่จะขับเคลื่อนความสำเร็จและมอบความเฉียบแหลมในอาชีพของเธอเพื่อนำพาบริษัทให้ก้าวไปอีกขั้น"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__website_indexed
msgid "Is Indexed"
msgstr "จัดทำดัชนี"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__is_installed_on_current_website
msgid "Is Installed On Current Website"
msgstr "ติดตั้งบนเว็บไซต์ปัจจุบัน"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_mega_menu
msgid "Is Mega Menu"
msgstr "คือ Mega Menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__is_published
#: model:ir.model.fields,field_description:website.field_res_users__is_published
#: model:ir.model.fields,field_description:website.field_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__is_published
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Is Published"
msgstr "เผยแพร่แล้ว"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_visible
#: model:ir.model.fields,field_description:website.field_website_page__is_visible
msgid "Is Visible"
msgstr "มองเห็นได้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after"
msgstr "คือหลังจาก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after or equal to"
msgstr "มาหลังหรือเท่ากับ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before"
msgstr "มาก่อน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before or equal to"
msgstr "มาก่อนหรือเท่ากับ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is between (included)"
msgstr "อยู่ระหว่าง (รวม)"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__is_connected
msgid "Is connected ?"
msgstr "เชื่อมต่อ ?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is equal to"
msgstr "เท่ากับ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than"
msgstr "มากกว่า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than or equal to"
msgstr "มากกว่าหรือเท่ากับ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than"
msgstr "น้อยกว่า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than or equal to"
msgstr "น้อยกว่าหรือเท่ากับ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not between (excluded)"
msgstr "ไม่อยู่ระหว่าง (ไม่รวม)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not equal to"
msgstr "ไม่เท่ากับ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Is not set"
msgstr "ไม่ได้ตั้งค่า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Is set"
msgstr "ตั้งค่า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"It appears you are in debug=assets mode, all theme customization options "
"require a page reload in this mode."
msgstr ""
"ดูเหมือนว่าคุณอยู่ในโหมด debug=asset "
"ตัวเลือกการปรับแต่งธีมทั้งหมดต้องการการโหลดหน้าซ้ำในโหมดนี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid ""
"It appears your website is still using the old color system of\n"
"            Odoo 13.0 in some places. We made sure it is still working but\n"
"            we recommend you to try to use the new color system, which is\n"
"            still customizable."
msgstr ""
"ดูเหมือนว่าเว็บไซต์ของคุณยังคงใช้ระบบสีแบบเก่าของ\n"
"            Odoo 13.0 ในบางที่ เรามั่นใจว่ามันยังใช้งานได้แต่\n"
"            เราขอแนะนำให้คุณลองใช้ระบบสีใหม่ ซึ่งก็คือ\n"
"           และยังคงปรับแต่งได้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "It looks like your file is being called by"
msgstr "ดูเหมือนว่าไฟล์ของคุณจะถูกเรียกโดย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Item"
msgstr "รายการ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 1"
msgstr "รายการ 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 2"
msgstr "รายการ  2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options
msgid "Items per row"
msgstr "รายการต่อแถว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Items per slide"
msgstr "รายการต่อสไลด์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jacket"
msgstr "เสื้อแจ็กเกต"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jeans"
msgstr "ยีนส์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Job Offer"
msgstr "ข้อเสนองาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Join us and make your company a better place."
msgstr "เข้าร่วมกับเราและทำให้บริษัทของคุณเป็นสถานที่ที่ดีกว่า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keep empty to use default value"
msgstr "เว้นว่างไว้เพื่อใช้ค่าเริ่มต้น"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__key
#: model:ir.model.fields,field_description:website.field_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__key
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__key
#: model:ir.model.fields,field_description:website.field_website_page__key
msgid "Key"
msgstr "คีย์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Keyboards"
msgstr "คีย์บอร์ด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keyword"
msgstr "คำสำคัญ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keywords"
msgstr "คำสำคัญ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Label"
msgstr "ป้าย"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_label
msgid "Label for form action"
msgstr "ป้ายสำหรับการดำเนินการฟอร์ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Labels Width"
msgstr "ความกว้างป้าย"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__lang_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Language"
msgstr "ภาษา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Language Selector"
msgstr "ตัวเลือกภาษา"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__lang_id
msgid "Language from the website when visitor has been created"
msgstr "ภาษาจากเว็บไซต์เมื่อสร้างผู้เยี่ยมชมแล้ว"

#. module: website
#: model:ir.model,name:website.model_res_lang
#: model:ir.model.fields,field_description:website.field_res_config_settings__language_ids
#: model:ir.model.fields,field_description:website.field_website__language_ids
msgid "Languages"
msgstr "ภาษา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Languages available on your website"
msgstr "ภาษาที่แสดงในเว็บไซต์ของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Laptops"
msgstr "แล็ปท็อป"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Large"
msgstr "ใหญ่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last 7 Days"
msgstr "7 วันล่าสุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Last Action"
msgstr "การกระทำล่าสุด"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_connection_datetime
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last Connection"
msgstr "การเชื่อมต่อล่าสุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Last Feature"
msgstr "ฟีเจอร์ล่าสุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Last Menu"
msgstr "เมนูสุดท้าย"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website____last_update
#: model:ir.model.fields,field_description:website.field_website_configurator_feature____last_update
#: model:ir.model.fields,field_description:website.field_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website_rewrite____last_update
#: model:ir.model.fields,field_description:website.field_website_robots____last_update
#: model:ir.model.fields,field_description:website.field_website_route____last_update
#: model:ir.model.fields,field_description:website.field_website_snippet_filter____last_update
#: model:ir.model.fields,field_description:website.field_website_track____last_update
#: model:ir.model.fields,field_description:website.field_website_visitor____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Month"
msgstr "เดือนที่แล้ว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Last Page"
msgstr "หน้าล่าสุด"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website__write_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_uid
#: model:ir.model.fields,field_description:website.field_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_uid
#: model:ir.model.fields,field_description:website.field_website_robots__write_uid
#: model:ir.model.fields,field_description:website.field_website_route__write_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__write_uid
msgid "Last Updated by"
msgstr "อัพเดทครั้งสุดท้ายโดย"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website__write_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_date
#: model:ir.model.fields,field_description:website.field_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_date
#: model:ir.model.fields,field_description:website.field_website_robots__write_date
#: model:ir.model.fields,field_description:website.field_website_route__write_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_date
#: model:ir.model.fields,field_description:website.field_website_visitor__write_date
msgid "Last Updated on"
msgstr "อัพเดทครั้งสุดท้ายเมื่อ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_visited_page_id
msgid "Last Visited Page"
msgstr "หน้าที่เข้าชมล่าสุด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Week"
msgstr "สัปดาห์ที่แล้ว"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Year"
msgstr "ปีที่แล้ว"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__time_since_last_action
msgid "Last action"
msgstr "การดำเนินการล่าสุด"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Last modified pages"
msgstr "หน้าที่แก้ไขล่าสุด"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__last_connection_datetime
msgid "Last page view date"
msgstr "วันที่เปิดดูเพจล่าสุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Latests news and case studies"
msgstr "ข่าวและกรณีศึกษาล่าสุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Layout"
msgstr "เลย์เอาต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background"
msgstr "ออกแบบพื้นหลัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background Color"
msgstr "ออกแบบสีพื้นหลัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Learn more"
msgstr "เรียนรู้เพิ่มเติม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr "ซ้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Left Menu"
msgstr "เมนูด้านซ้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Legal"
msgstr "กฎหมาย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Legal Notice"
msgstr "ประกาศทางกฎหมาย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Legend"
msgstr "แบบแผนภูมิ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Let your customers follow <br/>and understand your process."
msgstr "ให้ลูกค้าของคุณได้ติดตาม <br/>และเข้าใจใขั้นตอนต่าง ๆ "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "ให้ลูกค้าของคุณเข้าสู่ระบบเพื่อดูเอกสารของพวกเขา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Let's do it"
msgstr "ลุยกันเลย"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Let's go!"
msgstr "ไปกันเลย!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Library"
msgstr "Library"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Light"
msgstr "อ่อน"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__limit
msgid "Limit"
msgstr "จำกัด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr "การปรับแต่งที่จำกัด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Line"
msgstr "เส้น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Line Color"
msgstr "สีเส้น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Link"
msgstr "ลิงก์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Link Anchor"
msgstr "ลิงก์ Anchor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Link Style"
msgstr "สไตล์ลิงก์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Link button"
msgstr "ปุ่มลิงค์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Link text"
msgstr "ลิงก์ข้อความ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_linkedin
#: model:ir.model.fields,field_description:website.field_website__social_linkedin
msgid "LinkedIn Account"
msgstr "บัญชี LinkedIn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Linkedin"
msgstr "Linkedin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links"
msgstr "ลิงก์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links Style"
msgstr "สไตล์ลิงก์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Links to other Websites"
msgstr "ลิงก์ไปยังเว็บไซต์อื่น ๆ "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Little Icons"
msgstr "ไอคอนเล็ก"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_live_chat
msgid "Live Chat"
msgstr "ไลฟ์แชท"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Live Preview"
msgstr "ดูตัวอย่างแบบไลฟ์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Livechat Widget"
msgstr "วิดเจ็ตไลฟ์แชท"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/xml/website.background.video.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Loading..."
msgstr "กำลังโหลด..."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Logo"
msgstr "โลโก้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logo Type"
msgstr "ประเภทโลโก้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Logo of MyCompany"
msgstr "โลโก้ของ MyCompany"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logos"
msgstr "โลโก้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Long Text"
msgstr "ข้อความยาว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Low"
msgstr "ต่ำ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Magazine"
msgstr "นิตยสาร"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Main Course"
msgstr "จานหลัก"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__menu_id
msgid "Main Menu"
msgstr "เมนูหลัก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Main actions"
msgstr "การดำเนินการหลัก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure billing is enabled"
msgstr "ตรวจสอบให้แน่ใจว่าเปิดใช้งานการเรียกเก็บเงินแล้ว"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Make sure to wait if errors keep being shown: sometimes enabling an API "
"allows to use it immediately but Google keeps triggering errors for a while"
msgstr ""
"ตรวจสอบให้แน่ใจว่าได้รอ หากมีข้อผิดพลาดแสดงอยู่: บางครั้งการเปิดใช้งาน API "
"ทำให้สามารถใช้งานได้ทันที แต่ Google จะคอยกระตุ้นข้อผิดพลาดอยู่พักหนึ่ง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure your settings are properly configured:"
msgstr "ตรวจสอบให้แน่ใจว่าการตั้งค่าของคุณได้รับการกำหนดค่าอย่างถูกต้อง:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Pages"
msgstr "จัดการหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "Manage Your Pages"
msgstr "จัดการหน้าของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Your Website Pages"
msgstr "จัดการหน้าเว็บไซต์ของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Manage this page"
msgstr "จัดการหน้านี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps JavaScript API"
msgstr "แผนที่ JavaScript API"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps Static API"
msgstr "แผนที่ API แบบคงที่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Mark Text"
msgstr "ทำเครื่องหมายข้อความ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Marked Fields"
msgstr "ทำเครื่องหมายฟิลด์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Marker style"
msgstr "สไตล์เครื่องหมาย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Marketplace"
msgstr "ตลาดกลาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Masonry"
msgstr "Masonry"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Measurement ID"
msgstr "Measurement ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Media"
msgstr "มีเดีย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Media heading"
msgstr "หัวเรื่องสื่อ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr "ตัวกลาง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Mega Menu"
msgstr "Mega Menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_classes
msgid "Mega Menu Classes"
msgstr "คลาส Mega Menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_content
msgid "Mega Menu Content"
msgstr "เนื้อหา Mega Menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Men"
msgstr "ผู้ชาย"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website_menu__name
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Menu"
msgstr "เมนู"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_company
msgid "Menu Company"
msgstr "เมนูบริษัท"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Menu Item %s"
msgstr "รายการเมนู %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Menu Label"
msgstr "ป้ายเมนู"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_sequence
msgid "Menu Sequence"
msgstr "ลำดับเมนู"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__copy_ids
msgid "Menu using a copy of me"
msgstr "เมนูที่ใช้สำเนาของฉัน"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.ui.menu,name:website.menu_website_menu_list
#, python-format
msgid "Menus"
msgstr "เมนู"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Messages"
msgstr "ข้อความ"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Metadata"
msgstr "ข้อมูลอภิพันธุ์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Mich ชอบความท้าทาย "
"และด้วยประสบการณ์หลายปีในตำแหน่งผู้อำนวยการฝ่ายการค้าในอุตสาหกรรมซอฟต์แวร์ "
"เขาได้ช่วยให้บริษัทไปถึงจุดที่เป็นอยู่ในปัจจุบัน Mich "
"เป็นหนึ่งในแรงขับเคลื่อนที่ดีที่สุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Middle"
msgstr "ตรงกลาง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Min-Height"
msgstr "ความสูงขั้นต่ำ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Minimalist"
msgstr "มินิมอลลิส"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Minutes"
msgstr "นาที"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#: model:ir.model.fields,field_description:website.field_website_visitor__mobile
#, python-format
msgid "Mobile"
msgstr "มือถือ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile Alignment"
msgstr "การจัดตำแหน่งมือถือ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile menu"
msgstr "เมนูมือถือ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/mobile_view.js:0
#, python-format
msgid "Mobile preview"
msgstr "ตัวอย่างบนมือถือ"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__mode
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Mode"
msgstr "โหมด"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model
msgid "Model"
msgstr "โมเดล"

#. module: website
#: model:ir.model,name:website.model_ir_model_data
#: model:ir.model.fields,field_description:website.field_website_page__model_data_id
msgid "Model Data"
msgstr "ข้อมูลโมเดล"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__model_name
msgid "Model name"
msgstr "ชื่อโมเดล"

#. module: website
#: model:ir.model,name:website.model_ir_model
msgid "Models"
msgstr "โมเดล"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_updated
msgid "Modified Architecture"
msgstr "ดัดแปลงสถาปัตยกรรม"

#. module: website
#: model:ir.model,name:website.model_ir_module_module
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__module_id
msgid "Module"
msgstr "โมดูล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Monitor Google Search results data"
msgstr "ตรวจสอบข้อมูลผลการค้นหาของ Google"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid ""
"Monitor your visitors while they are browsing your website with the Odoo "
"Social app. Engage with them in just a click using a live chat request or a "
"push notification. If they have completed one of your forms, you can send "
"them an SMS, or call them right away while they are browsing your website."
msgstr ""
"ตรวจสอบผู้เยี่ยมชมของคุณในขณะที่พวกเขากำลังเรียกดูเว็บไซต์ของคุณด้วยแอป Odoo"
" Social "
"มีส่วนร่วมกับพวกเขาด้วยการคลิกเพียงครั้งเดียวโดยส่งคำขอไลฟ์แชทหรือการแจ้งเตือนแบบพุช"
" หากพวกเขากรอกแบบฟอร์มของคุณเรียบร้อยแล้ว คุณสามารถส่ง SMS "
"หรือโทรหาพวกเขาได้ทันทีในขณะที่พวกเขากำลังเรียกดูเว็บไซต์ของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Monitors"
msgstr "จอภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "More Details"
msgstr "รายละเอียดเพิ่มเติม"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid ""
"More than 90 shapes exist and their colors are picked to match your Theme."
msgstr "มีรูปร่างมากกว่า 90 แบบสีต่าง ๆ จะถูกเลือกให้เข้ากับธีมของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "More than one group has been set on the view."
msgstr "มีการตั้งค่ากลุ่มมากกว่าหนึ่งกลุ่มในมุมมอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Mosaic"
msgstr "Mosaic"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Most searched topics related to your keyword, ordered by importance"
msgstr ""
"หัวข้อที่ค้นหามากที่สุดที่เกี่ยวข้องกับคำสำคัญของคุณ "
"ซึ่งเรียงลำดับตามความสำคัญ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Mouse"
msgstr "เม้าส์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Backward"
msgstr "เคลื่อนไปข้างหลัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Forward"
msgstr "เคลื่อนไปข้างหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to first"
msgstr "เลื่อนขึ้นเป็นลำดับแรกสุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to last"
msgstr "ย้ายไปลำดับสุดดท้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to next"
msgstr "ถัดไป"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to previous"
msgstr "ก่อนหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Multi Menus"
msgstr "หลายเมนู"

#. module: website
#: model:ir.model,name:website.model_website_multi_mixin
msgid "Multi Website Mixin"
msgstr "หลายเมนู Mixin"

#. module: website
#: model:ir.model,name:website.model_website_published_multi_mixin
msgid "Multi Website Published Mixin"
msgstr "หลายเมนูเผยแพร่ Mixin"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__group_multi_website
#: model:res.groups,name:website.group_multi_website
msgid "Multi-website"
msgstr "หลายเมนู"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Multimedia"
msgstr "มัลติมีเดีย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Multiple Checkboxes"
msgstr "กล่องกาเครื่องหมายหลายกล่อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Multiple tree exists for this view"
msgstr "มีต้นไม้หลายต้นสำหรับมุมมองนี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "My Company"
msgstr "My Company"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_brand_name
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "My Website"
msgstr "เว็บไซต์ของฉัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "MyCompany"
msgstr "MyCompany"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__name
#: model:ir.model.fields,field_description:website.field_website_rewrite__name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__name
#: model:ir.model.fields,field_description:website.field_website_visitor__name
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Name"
msgstr "ชื่อ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (A-Z)"
msgstr "ชื่อ (A-Z)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (Z-A)"
msgstr "ชื่อ (Z-A)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Name and favicon of your website"
msgstr "ชื่อและ Favicon ของเว็บไซต์คุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Name, id or key"
msgstr "ชื่อไอดีหรือคีย์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Narrow"
msgstr "แคบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Navbar"
msgstr "Navbar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Need to pick up your order at one of our stores? Discover the nearest to "
"you."
msgstr "ต้องการรับสินค้าที่ร้านของเรา? ค้นพบร้านที่ใกล้คุณที่สุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Network Advertising Initiative opt-out page"
msgstr "หน้าที่เลือกไม่เข้าร่วม Network Advertising Initiative"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Networks"
msgstr "เครือข่าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "New"
msgstr "ใหม่"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"New Google Analytics accounts and keys are now using Google Analytics 4 "
"which, for now, can't be integrated/embed in external websites."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "New Page"
msgstr "เพจใหม่"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__new_window
#: model:ir.model.fields,field_description:website.field_website_menu__new_window
msgid "New Window"
msgstr "หน้าต่างใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "New collection"
msgstr "คอลเลกชันใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "New customer"
msgstr "ลูกค้าใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New page"
msgstr "เพจใหม่"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_news
msgid "News"
msgstr "ข่าวสาร"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter"
msgstr "จดหมายข่าว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter Popup"
msgstr "จดหมายข่าวแบบป๊อปอัพ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Next"
msgstr "ต่อไป"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No Animation"
msgstr "ไม่มีแอนิเมชัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "No Slide Effect"
msgstr "ไม่มีเอฟเฟกต์สไลด์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_widget.xml:0
#, python-format
msgid "No Url"
msgstr "ไม่มี URL"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid "No Visitors yet!"
msgstr "ยังไม่มีผู้เยี่ยมชม!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr "ไม่มีการปรับแต่ง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "No matching record !"
msgstr "ไม่มีบันทึกที่ตรงกัน !"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_page_action
msgid "No page views yet for this visitor"
msgstr "ยังไม่มีการดูหน้าเว็บสำหรับผู้เยี่ยมชมรายนี้"

#. module: website
#: model_terms:ir.actions.act_window,help:website.visitor_partner_action
msgid "No partner linked for this visitor"
msgstr "ไม่มีพาร์ทเนอร์ที่เชื่อมโยงสำหรับผู้เยี่ยมชมรายนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "No result found, broaden your search."
msgstr "ไม่พบผลลัพธ์ เพิ่มการค้นหาให้กว้างขึ้น"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "No results found for '"
msgstr "ไม่พบผลลัพธ์สำหรับ "

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "No results found. Please try another search."
msgstr "ไม่พบผลลัพธ์โปรดลองค้นหาอีกครั้ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr "ไม่สนับสนุน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "None"
msgstr "ไม่มี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Normal"
msgstr "ปกติ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not SEO optimized"
msgstr "ไม่ได้ปรับ SEO ให้เหมาะสม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not in main menu"
msgstr "ไม่อยู่ในเมนูหลัก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not indexed"
msgstr "ไม่ได้จัดทำดัชนี"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid ""
"Not only can you search for royalty-free illustrations, their colors are "
"also converted so that they always fit your Theme."
msgstr ""
"ไม่เพียงแต่คุณสามารถค้นหาภาพประกอบที่ปลอดค่าลิขสิทธิ์ได้เท่านั้น "
"แต่ยังสามารถเปลี่ยนสีเพื่อให้เข้ากับธีมของคุณได้เสมอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not published"
msgstr "ไม่เผยแพร่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not tracked"
msgstr "ไม่ได้ติดตาม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Note that some third-party services may install additional cookies on your "
"browser in order to identify you."
msgstr ""
"โปรดทราบว่าบริการบุคคลที่สามบางบริการอาจจำเป็นต้องติดตั้งคุกกี้เพิ่มเติมบนเบราว์เซอร์ของคุณเพื่อระบุตัวตนของคุณก่อน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Note: To hide this page, uncheck it from the top Customize menu."
msgstr ""
"หมายเหตุ: หากต้องการซ่อนหน้านี้ ให้ยกเลิกการเลือกจากเมนูการปรับแต่งด้านบน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Nothing"
msgstr "ไม่มี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Number"
msgstr "ตัวเลข"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_language_count
msgid "Number of languages"
msgstr "จำนวนภาษา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "OR"
msgstr "หรือ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Odoo Logo"
msgstr "โลโก้ Odoo "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Odoo Menu"
msgstr "เมนู Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr "เวอร์ชัน Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Off-Canvas"
msgstr "Off-Canvas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office audio"
msgstr "เครื่องเสียงสำนักงาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office screens"
msgstr "หน้าจอสำนักงาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Offline"
msgstr "ออฟไลน์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Ok"
msgstr "ตกลง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Ok, never show me this again"
msgstr "ตกลงไม่ต้องแสดงให้ฉันเห็นอีก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Click"
msgstr "เมื่อคลิก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "On Exit"
msgstr "เมื่อออก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Hover"
msgstr "วางเมาส์เหนือ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "On Success"
msgstr "เมื่อสำเร็จ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "On Website"
msgstr "บนเว็บไซต์"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "ตามคำเชิญ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Once the selection of available websites by domain is done, you can filter "
"by country group."
msgstr ""
"เมื่อเลือกเว็บไซต์ที่มีอยู่ตามโดเมนเสร็จแล้วคุณสามารถกรองตามกลุ่มประเทศได้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Once the user closes the popup, it won't be shown again for that period of "
"time."
msgstr "เมื่อผู้ใช้ปิดป๊อปอัพ ป๊อปอัพจะไม่แสดงอีกในช่วงเวลานั้น"

#. module: website
#: code:addons/website/models/website_configurator_feature.py:0
#, python-format
msgid ""
"One and only one of the two fields 'page_view_id' and 'module_id' should be "
"set"
msgstr "ควรตั้งค่า One and only one ในสองฟิลด์ 'page_view_id' และ 'module_id'"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Online"
msgstr "ออนไลน์"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__mode
msgid ""
"Only applies if this view inherits from an other one (inherit_id is not False/Null).\n"
"\n"
"* if extension (default), if this view is requested the closest primary view\n"
"is looked up (via inherit_id), then all views inheriting from it with this\n"
"view's model are applied\n"
"* if primary, the closest primary view is fully resolved (even if it uses a\n"
"different model than this one), then this view's inheritance specs\n"
"(<xpath/>) are applied, and the result is used as if it were this view's\n"
"actual arch.\n"
msgstr ""
"ใช้เฉพาะเมื่อมุมมองนี้สืบทอดมาจากอีกมุมมองหนึ่ง (inherit_id ไม่ใช่ False/Null)\n"
"\n"
"* ถ้าขยาย (ค่าเริ่มต้น) และถ้ามุมมองนี้ร้องขอมุมมองหลักที่ใกล้เคียงที่สุดนั้น\n"
"ถูกค้นหา (ผ่าน inherit_id) หากเป็นเช่นนั้นการดูทั้งหมดที่สืบทอดมาจาก\n"
"มุมมองโมเดลนี้จะถูกนำมาใช้\n"
"* หากเป็นมุมมองหลัก มุมมองหลักที่ใกล้เคียงที่สุดจะได้รับการแก้ไขอย่างสมบูรณ์ (แม้ว่าจะใช้โมเดลที่\n"
"แตกต่างจากรุ่นนี้) แล้วสเปคที่ถูกสืบทอดจากมุมมองนี้\n"
"(<xpath/>)ถูกนำไปใช้ และใช้ผลลัพธ์ราวกับว่าเป็นมุมมอง\n"
"Arch ที่แท้จริง\n"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr "โอเพนซอร์ส ERP"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Optimize SEO"
msgstr "เพิ่มประสิทธิภาพ SEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Optimize SEO of this page"
msgstr "เพิ่มประสิทธิภาพ SEO สำหรับหน้านี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 1"
msgstr "ตัวเลือก 1"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 2"
msgstr "ตัวเลือก 2"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 3"
msgstr "ตัวเลือก 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Optional"
msgstr "เลือกได้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Order by"
msgstr "สั่งโดย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr "สั่งเลย"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Other Information:"
msgstr "ข้อมูลอื่น ๆ:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Our Company"
msgstr "บริษัทของเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Our References"
msgstr "การอ้างอิงของเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Our seminars and trainings for you"
msgstr "การสัมมนาและการฝึกอบรมของเราสำหรับคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Our team"
msgstr "ทีมของเรา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Our team will message you back as soon as possible."
msgstr "ทีมงานของเราจะส่งข้อความกลับหาคุณโดยเร็วที่สุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Outline"
msgstr "เค้าโครง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Outset"
msgstr "เริ่มแรก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Outstanding images"
msgstr "ภาพที่โดดเด่น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Over The Content"
msgstr "เหนือเนื้อหา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Paddings"
msgstr "Paddings"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model:ir.model,name:website.model_website_page
#: model:ir.model.fields,field_description:website.field_ir_ui_view__page_ids
#: model:ir.model.fields,field_description:website.field_theme_website_menu__page_id
#: model:ir.model.fields,field_description:website.field_website_page__page_ids
#: model:ir.model.fields,field_description:website.field_website_track__page_id
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#, python-format
msgid "Page"
msgstr "หน้า"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> contains a link to this page"
msgstr "เพจ <b>%s </b> มีลิงก์ไปยังหน้านี้"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> is calling this file"
msgstr "เพจ<b>%s</b> เรียกไฟล์นี้"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__website_indexed
msgid "Page Indexed"
msgstr "จัดทำดัชนีหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Layout"
msgstr "เค้าโครงหน้า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Page Name"
msgstr "ชื่อเพจ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Page Properties"
msgstr "คุณสมบัติของเพจ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Page Title"
msgstr "หัวข้อเพจ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__url
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#, python-format
msgid "Page URL"
msgstr "URL ของเพจ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__page_view_id
msgid "Page View"
msgstr "ยอดเข้าชมเพจ"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_view_action
#: model:ir.model.fields,field_description:website.field_website_visitor__visitor_page_count
#: model:ir.ui.menu,name:website.menu_visitor_view_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Page Views"
msgstr "ยอดเข้าชมเพจ"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_page_action
msgid "Page Views History"
msgstr "ประวัติการดูหน้าเพจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Visibility"
msgstr "การมองเห็นเพจ"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__iap_page_code
msgid ""
"Page code used to tell IAP website_service for which page a snippet list "
"should be generated"
msgstr "เพจโค้ดที่ใช้บอก IAP website_service ว่าเพจไหนควรสร้างรายการ Snippet"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__copy_ids
msgid "Page using a copy of me"
msgstr "หน้าที่ใช้สำเนาของฉัน"

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model:ir.ui.menu,name:website.menu_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Pages"
msgstr "หน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pagination"
msgstr "หมายเลขหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Pants"
msgstr "กางเกง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"Paragraph text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."
msgstr ""
"ข้อความย่อหน้า. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"Paragraph with <strong>bold</strong>, <span class=\"text-"
"muted\">muted</span> and <em>italic</em> texts"
msgstr ""
"ย่อหน้าที่มีข้อความ <strong>ตัวหนา</strong>  <span class=\"text-"
"muted\">ถูกระงับ</span> และ <em>เอียง</em> "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Paragraph."
msgstr "ย่อหน้า"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__parent_id
msgid "Parent"
msgstr "แม่"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_id
msgid "Parent Menu"
msgstr "เมนูแม่"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_path
msgid "Parent Path"
msgstr "เส้นทางแม่"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__partner_id
msgid "Partner of the last logged in user."
msgstr "พาร์ทเนอร์ของผู้ใช้ที่เข้าสู่ระบบล่าสุด"

#. module: website
#: model:ir.model.fields,help:website.field_website__partner_id
msgid "Partner-related data of the user"
msgstr "ข้อมูลที่เกี่ยวข้องกับพาร์ทเนอร์ของผู้ใช้"

#. module: website
#: model:ir.actions.act_window,name:website.visitor_partner_action
msgid "Partners"
msgstr "พาร์ทเนอร์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Password"
msgstr "ใส่รหัส"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__path
msgid "Path"
msgstr "เส้นทาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pattern"
msgstr "แบบแผน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Pay"
msgstr "จ่าย"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Phone Number"
msgstr "หมายเลขโทรศัพท์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Phones"
msgstr "โทรศัพท์"

#. module: website
#: model:ir.actions.act_window,name:website.theme_install_kanban_action
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Pick a Theme"
msgstr "เลือกธีม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Pie"
msgstr "พาย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pill"
msgstr "ทรงยา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pills"
msgstr "ทรงยา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Placeholder"
msgstr "ตัวอย่างข้อความ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Places API"
msgstr "API สถานที่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Plain"
msgstr "ธรรมดา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Please confirm"
msgstr "โปรดยืนยัน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Please fill in the form correctly."
msgstr "กรุณากรอกแบบฟอร์มให้ถูกต้อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Points of sale"
msgstr "การขายหน้าร้าน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Popup"
msgstr "ป็อปอัพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Portfolio"
msgstr "Portfolio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Position"
msgstr "ตำแหน่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Post heading"
msgstr "หัวเรื่อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Postcard"
msgstr "โปสการ์ด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Preferences"
msgstr "การตั้งค่า"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__prepend
msgid "Prepend"
msgstr "นำหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Preset"
msgstr "พรีเซ็ต"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Press"
msgstr "กด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/view_hierarchy.js:0
#, python-format
msgid "Press %s for next %s"
msgstr "กด%s เพื่อต่อ %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Preview"
msgstr "ตัวอย่าง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Previous"
msgstr "ก่อนหน้า"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_prev
msgid "Previous View Architecture"
msgstr "ดูสถาปัตยกรรมก่อนหน้า"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_pricing
#: model_terms:ir.ui.view,arch_db:website.pricing
msgid "Pricing"
msgstr "ราคา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary"
msgstr "หลัก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary Style"
msgstr "สไตล์หลัก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Printers"
msgstr "เครื่องพิมพ์"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__priority
msgid "Priority"
msgstr "ระดับความสำคัญ"

#. module: website
#: code:addons/website/models/website.py:0
#: model:website.configurator.feature,name:website.feature_page_privacy_policy
#: model_terms:ir.ui.view,arch_db:website.privacy_policy
#, python-format
msgid "Privacy Policy"
msgstr "นโยบายความเป็นส่วนตัว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Product"
msgstr "สินค้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Products"
msgstr "สินค้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr "มืออาชีพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Professional themes"
msgstr "ธีมที่ดูเป็นมืออาชีพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Profile"
msgstr "โปรไฟล์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Progress Bar"
msgstr "แถบความคืบหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Color"
msgstr "สีแถบความคืบหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Style"
msgstr "สไตล์แถบความคืบหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Weight"
msgstr "ความหนาแถบความคืบหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Projectors"
msgstr "เครื่องฉายภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote"
msgstr "โปรโมต"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote page on the web"
msgstr "โปรโมทเพจบนเว็บ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Promotions"
msgstr "โปรโมชั่น"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Public"
msgstr "สาธารณะ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__partner_id
msgid "Public Partner"
msgstr "พาร์ทเนอร์สาธารณะ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__user_id
msgid "Public User"
msgstr "ผู้ใช้สาธารณะ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Publish"
msgstr "เผยแพร่"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_career
msgid "Publish job offers and let people apply"
msgstr "เผยแพร่ข้อเสนองานและให้ผู้คนมาสมัคร"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_event
msgid "Publish on-site and online events"
msgstr "เผยแพร่กิจกรรมในสถานที่และออนไลน์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
#, python-format
msgid "Published"
msgstr "เผยแพร่แล้ว"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__date_publish
#, python-format
msgid "Publishing Date"
msgstr "วันที่เผยแพร่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pulse"
msgstr "Pulse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Purpose"
msgstr "เสนอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr "ให้ความสำคัญกับที่สิ่งที่คุณต้องพูด!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating
msgid "Quality"
msgstr "คุณภาพ"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "Qweb"
msgstr "QWeb"

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "ฟิลด์ติดต่อ Qweb"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Radar"
msgstr "เรดาร์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Radio Buttons"
msgstr "Radio Buttons"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Rating"
msgstr "การให้คะแนน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Re-order"
msgstr "สั่งซื้อใหม่"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Ready to build the"
msgstr "พร้อมที่จะสร้าง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Recipient Email"
msgstr "อีเมลผู้รับ"

#. module: website
#: model:ir.model,name:website.model_ir_rule
msgid "Record Rule"
msgstr "กฎการบันทึก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Redirect"
msgstr "เปลี่ยนเส้นทาง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Redirect Old URL"
msgstr "เปลี่ยนเส้นทาง URL เก่า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Redirect to URL in a new tab"
msgstr "เปลี่ยนเส้นทางไปยัง URL ในแท็บใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Redirection Type"
msgstr "ประเภทการเปลี่ยนเส้นทาง"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_rewrite
msgid "Redirects"
msgstr "การเปลี่ยนเส้นทาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Refresh route's list"
msgstr "รีเฟรชรายการเส้นทาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Regular"
msgstr "ปกติ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Related Menu Items"
msgstr "รายการเมนูที่เกี่ยวข้อง"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__menu_ids
msgid "Related Menus"
msgstr "เมนูที่เกี่ยวข้อง"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__page_id
msgid "Related Page"
msgstr "เพจที่เกี่ยวข้อง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Related keywords"
msgstr "คำสำคัญที่เกี่ยวข้อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Remember information about the preferred look or behavior of the website, "
"such as your preferred language or region."
msgstr ""
"จดจำข้อมูลเกี่ยวกับรูปลักษณ์หรือพฤติกรรมที่ต้องการของเว็บไซต์ เช่น "
"ภาษาหรือภูมิภาคที่คุณต้องการ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__remove
#, python-format
msgid "Remove"
msgstr "ลบออก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Row"
msgstr "ลบแถว"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Serie"
msgstr "ลบซีรีส์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove Slide"
msgstr "ลบสไลด์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Remove Tab"
msgstr "ลบแท็บ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Remove all"
msgstr "ลบทั้งหมด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Remove theme"
msgstr "ลบธีม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Rename Page To:"
msgstr "เปลี่ยนชื่อหน้าเป็น:"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__replace
msgid "Replace"
msgstr "แทนที่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code
msgid "Replace this with your own HTML code"
msgstr "แทนที่ด้วยโค้ด HTML ของคุณเอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Required"
msgstr "ต้องการ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset templates"
msgstr "ตั้งค่าเทมเพลตใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset to initial version (hard reset)."
msgstr "รีเซ็ตเป็นเวอร์ชันเริ่มต้น (ฮาร์ดรีเซ็ต)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Resources"
msgstr "ทรัพยากร"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Respecting your privacy is our priority."
msgstr "การเคารพความเป็นส่วนตัวของคุณคือสิ่งที่เราให้ความสำคัญที่สุด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Restore previous version (soft reset)."
msgstr "กู้คืนเวอร์ชันก่อนหน้า (ซอฟต์รีเซ็ต)"

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_id
#: model:ir.model.fields,help:website.field_res_users__website_id
#: model:ir.model.fields,help:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_page__website_id
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_id
msgid "Restrict publishing to this website."
msgstr "จำกัดการเผยแพร่ในเว็บไซต์นี้"

#. module: website
#: model:res.groups,name:website.group_website_publisher
msgid "Restricted Editor"
msgstr "ผู้แก้ไขที่ถูกจำกัด"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__restricted_group
msgid "Restricted Group"
msgstr "กลุ่มที่ถูกจำกัด"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_rewrite_list
msgid "Rewrite"
msgstr "เขียนใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr "ขวา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Right Menu"
msgstr "เมนูด้านขวา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Ripple Effect"
msgstr "เอฟเฟกต์ระลอกคลื่น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Road"
msgstr "ถนน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "RoadMap"
msgstr "แผนที่นำทาง"

#. module: website
#: code:addons/website/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:website.field_website__robots_txt
#, python-format
msgid "Robots.txt"
msgstr "Robots.txt"

#. module: website
#: model:ir.model,name:website.model_website_robots
msgid "Robots.txt Editor"
msgstr "ตัวแก้ไข Robots.txt "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Robots.txt: This file tells to search engine crawlers which pages or files "
"they can or can't request from your site.<br/>"
msgstr ""
"Robots.txt: "
"ไฟล์นี้จะบอกโปรแกรมค้นหาและรวบรวมข้อมูลว่าหน้าหรือไฟล์ใดที่พวกเขาสามารถหรือไม่สามารถขอจากเว็บไซต์ของคุณได้<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In"
msgstr "หมุนเข้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In-Down-Left"
msgstr "หมุนเข้า-ลง-ซ้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In-Down-Right"
msgstr "หมุนเข้า-ลง-ขวา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
msgid "Round Corners"
msgstr "มุมโค้งมน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded"
msgstr "โค้งมน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Rounded Miniatures"
msgstr "เพชรโค้งมน"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__route_id
#: model:ir.model.fields,field_description:website.field_website_route__path
msgid "Route"
msgstr "เส้นทาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "SEO"
msgstr "SEO"

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr "SEO ข้อมูลอภิพันธุ์"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__is_seo_optimized
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "SEO optimized"
msgstr "ปรับ SEO ให้เหมาะสม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Same as desktop"
msgstr "เช่นเดียวกับเดสก์ท็อป"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Sample %s"
msgstr "ตัวอย่าง %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Sample Icons"
msgstr "ไอคอนตัวอย่าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Satellite"
msgstr "ดาวเทียม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#, python-format
msgid "Save"
msgstr "บันทึก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & Reload"
msgstr "บันทึกและโหลดซ้ำ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & copy"
msgstr "บันทึกและคัดลอก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Save the block to use it elsewhere"
msgstr "บันทึกบล็อกเพื่อใช้ที่อื่น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Score"
msgstr "คะแนน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Screens"
msgstr "หน้าจอ"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__image_ids
msgid "Screenshots"
msgstr "ถ่ายภาพหน้าจอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll"
msgstr "เลื่อน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Effect"
msgstr "เอฟเฟกต์การเลื่อน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_footer_scrolltop
msgid "Scroll To Top"
msgstr "เลื่อนไปด้านบน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Top Button"
msgstr "ปุ่มเลื่อนด้านบน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll down button"
msgstr "ปุ่มเลื่อนลง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Scroll down to next section"
msgstr "เลื่อนลงไปที่ส่วนถัดไป"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
#: model_terms:ir.ui.view,arch_db:website.website_search_box
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Search"
msgstr "ค้นหา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr "เมนูค้นหา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Search Redirect"
msgstr "เปลี่ยนเส้นทางการค้นหา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Search Results"
msgstr "ผลการค้นหา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Search Visitor"
msgstr "ค้นหาผู้เยี่ยมชม"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid ""
"Search in the media dialogue when you need photos to illustrate your "
"website. Odoo's integration with Unsplash, featuring millions of royalty "
"free and high quality photos, makes it possible for you to get the perfect "
"picture, in just a few clicks."
msgstr ""
"ค้นหาในสื่อการสนทนาเมื่อคุณต้องการรูปภาพเพื่อแสดงเว็บไซต์ของคุณ "
"การผสานการทำงานระหว่าง Odoo กับ Unsplash "
"ซึ่งประกอบด้วยภาพถ่ายคุณภาพสูงปลอดค่าลิขสิทธิ์นับล้านรายการ "
"ซึ่งช่วยให้คุณได้ภาพที่สมบูรณ์แบบด้วยการคลิกเพียงไม่กี่ครั้ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "Search on our website"
msgstr "ค้นหาบนเว็บไซต์ของเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Search within"
msgstr "ค้นหาใน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr "ค้นหา..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr "ฟีเจอร์ที่สอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Second Menu"
msgstr "เมนูที่สอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Second feature"
msgstr "ฟีเจอร์ที่สอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Second list of Features"
msgstr "รายการฟีเจอร์ที่สอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary"
msgstr "รอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary Style"
msgstr "สไตล์รอง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Seconds"
msgstr "ที่สอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Section Subtitle"
msgstr "คำบรรยายส่วน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Select a Menu"
msgstr "เลือกเมนู"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select a website to load its settings."
msgstr "เลือกเว็บไซต์เพื่อโหลดการตั้งค่า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Select an image for social share"
msgstr "เลือกรูปภาพสำหรับแชร์บนโซเชียล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Select and delete blocks <br/>to remove some steps."
msgstr "เลือกและลบบล็อก <br/>เพื่อลดขั้นตอนต่าง ๆ "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr "เลือกและลบบล็อกเพื่อลบฟีเจอร์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Select one font on"
msgstr "เลือกหนึ่งฟอนต์บน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select the Website to Configure"
msgstr "เลือกเว็บไซต์เพื่อกำหนดค่า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Selection"
msgstr "เลือก"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_shop
msgid "Sell more with an eCommerce"
msgstr "ขายได้มากขึ้นด้วยอีคอมเมิร์ซ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Send Email"
msgstr "ส่งอีเมล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Send us a message"
msgstr "ส่งข้อความหาเรา"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__seo_name
#: model:ir.model.fields,field_description:website.field_website_page__seo_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__seo_name
msgid "Seo name"
msgstr "ชื่อ Seo "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Separate email addresses with a comma."
msgstr "แยกที่อยู่อีเมลด้วยเครื่องหมายจุลภาค (,)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Separated link"
msgstr "แยกลิงก์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Separator"
msgstr "ตัวแบ่ง"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__sequence
#: model:ir.model.fields,field_description:website.field_theme_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website__sequence
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__sequence
#: model:ir.model.fields,field_description:website.field_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website_page__priority
#: model:ir.model.fields,field_description:website.field_website_rewrite__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Serve font from Google servers"
msgstr "ให้บริการฟอนต์จากเซิร์ฟเวอร์ของ Google"

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__action_server_id
msgid "Server Action"
msgstr "การดำเนินการเซิร์ฟเวอร์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Service"
msgstr "บริการ"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_our_services
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.our_services
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Services"
msgstr "บริการ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Session &amp; Security"
msgstr "เซสชั่นและความปลอดภัย"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model:ir.ui.menu,name:website.menu_website_website_settings
msgid "Settings"
msgstr "ตั้งค่า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings on this page will apply to this website"
msgstr "การตั้งค่าในหน้านี้จะใช้กับเว็บไซต์นี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Shadow"
msgstr "เงา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Shadows"
msgstr "เงา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shake"
msgstr "เขย่า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#, python-format
msgid "Share"
msgstr "แชร์"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_elearning
msgid "Share knowledge publicly or for a fee"
msgstr "แบ่งปันความรู้สู่สาธารณะหรือเก็บค่าธรรมเนียม"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_success_stories
msgid "Share your best case studies"
msgstr "แชร์กรณีศึกษาที่ดีที่สุดของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Shoes"
msgstr "ร้านค้า"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_shop
msgid "Shop"
msgstr "ร้านค้า"

#. module: website
#: model:ir.model.fields,help:website.field_website__auto_redirect_lang
msgid "Should users be redirected to their browser's language"
msgstr "ควรเปลี่ยนเส้นทางผู้ใช้ไปยังภาษาของเบราว์เซอร์"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__customize_show
msgid "Show As Optional Inherit"
msgstr "แสดงตัวเลือกในการสืบทอด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Header"
msgstr "แสดงหัวเรื่อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show Message"
msgstr "แสดงข้อความ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and hide countdown"
msgstr "แสดงข้อความและซ่อนการนับถอยหลัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and keep countdown"
msgstr "แสดงข้อความและนับถอยหลัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Sign In"
msgstr "แสดงการเข้าสู่ระบบ"

#. module: website
#: model:ir.actions.act_window,name:website.action_show_viewhierarchy
msgid "Show View Hierarchy"
msgstr "แสดงมุมมองของลำดับชั้น"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Show in Top Menu"
msgstr "แสดงในเมนูด้านบน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Show inactive views"
msgstr "แสดงมุมมองที่ไม่ได้ใช้งาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Show on"
msgstr "แสดงบน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show reCaptcha Policy"
msgstr "แสดงนโยบาย reCaptcha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sidebar"
msgstr "แถบด้านข้าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Sign in"
msgstr "ลงชื่อเข้าใช้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__connected
#, python-format
msgid "Signed In"
msgstr "ลงชื่อเข้าใช้แล้ว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Sitemap.xml: Help search engine crawlers to find out what pages are present "
"and which have recently changed, and to crawl your site accordingly. This "
"file is automatically generated by Odoo."
msgstr ""
"Sitemap.xml: "
"ช่วยโปรแกรมค้นหารวบรวมข้อมูลเพื่อค้นหาว่ามีหน้าใดแสดงอยู่บ้างและหน้าใดที่มีการเปลี่ยนแปลงล่าสุด"
" และรวบรวมข้อมูลเว็บไซต์ของคุณตามนั้น ไฟล์นี้จะถูกสร้างขึ้นโดยอัตโนมัติโดย "
"Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Size"
msgstr "ขนาด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Skip and start from scratch"
msgstr "ข้ามแล้วเริ่มใหม่ตั้งแต่ต้น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide"
msgstr "สไลด์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Down"
msgstr "เลื่อนลง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide Hover"
msgstr "สไลด์ Hover"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Left"
msgstr "เลื่อนซ้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Right"
msgstr "เลื่อนขวา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Up"
msgstr "เลื่อนบน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideout Effect"
msgstr "เอฟเฟกต์สไลด์ออก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Slider Speed"
msgstr "ความเร็วสไลด์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Slideshow"
msgstr "การนำเสนอภาพนิ่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slogan"
msgstr "สโลแกน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Small"
msgstr "เล็ก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Small Header"
msgstr "ส่วนหัวขนาดเล็ก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"Small text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."
msgstr ""
"ข้อความขนาดเล็ก. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Smartphones"
msgstr "สมาร์ทโฟน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Social Media"
msgstr "สื่อสังคมออนไลน์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Social Preview"
msgstr "ดูตัวอย่างในโซเชียล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Solid"
msgstr "Solid"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Some Users"
msgstr "ผู้ใช้บางคน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Something else here"
msgstr "อย่างอื่นที่นี่"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Something went wrong."
msgstr "มีอะไรบางอย่างผิดปกติ"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Name"
msgstr "เรียงตามชื่อ"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Url"
msgstr "เรียงตาม URL"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Sound"
msgstr "เสียง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""
"วิทยากรจากทั่วทุกมุมโลกจะเข้าร่วมกับผู้เชี่ยวชาญของเราในการบรรยายที่สร้างแรงบันดาลใจในหัวข้อต่าง"
" ๆ ติดตามแนวทางการจัดการธุรกิจและเทคโนโลยีล่าสุด"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__specific_user_account
#: model:ir.model.fields,field_description:website.field_website__specific_user_account
msgid "Specific User Account"
msgstr "บัญชีผู้ใช้เฉพาะ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Specify a search term."
msgstr "ระบุคำค้นหา"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_default_field_id
msgid ""
"Specify the field which will contain meta and custom form fields datas."
msgstr "ระบุฟิลด์ที่จะมีข้อมูลฟิลด์แบบเมตาและฟอร์มที่กำหนดเอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Speed"
msgstr "ความเร็ว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Spring collection has arrived !"
msgstr "คอลเลคชั่นฤดูใบไม้ผลิมาแล้ว !"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Square"
msgstr "สี่เหลี่ยม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Squared Miniatures"
msgstr "สี่เหลี่ยมจัตุรัส"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Stacked"
msgstr "ซ้อนกัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Standard"
msgstr "มาตรฐาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Start Button"
msgstr "ปุ่มเริ่ม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Start Now"
msgstr "เริ่มเลย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Start now"
msgstr "เริ่มเลย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Start with the customer – find out what they want and give it to them."
msgstr "เริ่มต้นพร้อมกับลูกค้า ค้นหาสิ่งที่พวกเขาต้องการและมอบให้พวกเขา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Start your journey"
msgstr "เริ่มต้นการเดินทางของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Starter"
msgstr "จานเริ่ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Status Colors"
msgstr "สถานะสี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Stay informed of our latest news and discover what will happen in the next "
"weeks."
msgstr "ติดตามข่าวสารล่าสุดของเราและค้นพบสิ่งที่จะเกิดขึ้นในสัปดาห์หน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Sticky"
msgstr "ติด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Storage"
msgstr "อุปกรณ์จัดเก็บ"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_stores_locator
msgid "Stores Locator"
msgstr "ที่ตั้งร้านค้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Storytelling is powerful.<br/> It draws readers in and engages them."
msgstr "การเล่าเรื่องนั้นทรงพลัง<br/>มันดึงดูดผู้อ่านให้มีส่วนร่วม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Stretch to Equal Height"
msgstr "ยืดให้สูงเท่ากัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Striped"
msgstr "Striped"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Structure"
msgstr "โครงสร้าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Style"
msgstr "สไตล์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Styling"
msgstr "สไตล์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sub Menus"
msgstr " เมนูย่อย"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Subject"
msgstr "หัวเรื่อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.s_website_form
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Submit"
msgstr "ส่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Submit sitemap to Google"
msgstr "ส่งแผนที่เว็บไซต์ไปที่ Google"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Success"
msgstr "สำเร็จ"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_success_stories
msgid "Success Stories"
msgstr "เรื่องราวความสำเร็จ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Suggestions"
msgstr "ข้อเสนอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Surrounded"
msgstr "ล้อมรอบ"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "กิจกรรมน่าสงสัยที่ตรวจพบโดย Google reCaptcha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Switch Theme"
msgstr "เปลี่ยนธีม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "T-shirts"
msgstr "เสื้อยืด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "TIP: Once loaded, follow the"
msgstr "เคล็ดลับ: เมื่อโหลดแล้ว ให้ปฏิบัติตาม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"TIP: Once loaded, follow the\n"
"                    <span class=\"o_tooltip o_tooltip_visible bottom o_animated position-relative\"/>\n"
"                    <br/>pointer to build the perfect page in 7 steps."
msgstr ""
"เคล็ดลับ: เมื่อโหลดแล้ว ให้ปฏิบัติตาม\n"
"                    <span class=\"o_tooltip o_tooltip_visible bottom o_animated position-relative\"/>\n"
"                    <br/>คำแนะนำเพื่อสร้างเพจที่สมบูรณ์แบบใน 7 ขั้นตอน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "TRANSLATE"
msgstr "แปล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Tablets"
msgstr "แท็บเล็ต"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs"
msgstr "แท็บ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs color"
msgstr "แถบสี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Tada"
msgstr "ทาด้า"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__target
msgid "Target"
msgstr "เป้าหมาย"

#. module: website
#: model:ir.model.fields,help:website.field_ir_asset__key
msgid ""
"Technical field used to resolve multiple assets in a multi-website "
"environment."
msgstr ""
"ฟิลด์เทคนิคที่ใช้ในการแก้ไข Asset หลายรายการในสภาพแวดล้อมแบบหลายเว็บไซต์"

#. module: website
#: model:ir.model.fields,help:website.field_ir_attachment__key
msgid ""
"Technical field used to resolve multiple attachments in a multi-website "
"environment."
msgstr "ฟิลด์เทคนิคที่ใช้ในการแก้ไขไฟล์แนบหลายรายการในสภาพแวดล้อมหลายเว็บไซต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr "ชื่อทางเทคนิค:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Telephone"
msgstr "โทรศัพท์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Televisions"
msgstr "โทรทัศน์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the <br/>customer for this feature."
msgstr "บอกคุณค่าของฟีเจอร์นี้ <br/>แก่ลูกค้า"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Template"
msgstr "เทมเพลต"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> contains a link to this page"
msgstr "เทมเพลต <b>%s (id:%s)</b> มีลิงค์ไปยังหน้านี้"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> is calling this file"
msgstr "เทมเพลต <b>%s (id:%s)</b> เรียกไฟล์นี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Template fallback"
msgstr "Template fallback"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "Templates"
msgstr "เทมเพลต"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Terms of Services"
msgstr "ข้อกำหนดการให้บริการ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Terms of service"
msgstr "ข้อกำหนดการให้บริการ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Terrain"
msgstr "ภูมิประเทศ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid "Test your robots.txt with Google Search Console"
msgstr "ทดสอบ robots.txt ของคุณด้วย Google Search Console"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
msgid "Text"
msgstr "ข้อความ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Alignment"
msgstr "การจัดตำแหน่งข้อความ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Color"
msgstr "สีข้อความ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
#, python-format
msgid "Text Highlight"
msgstr "เน้นข้อความ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Text Image Text"
msgstr "ข้อความ รูปภาพ ข้อความ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Inline"
msgstr "ข้อความอินไลน์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Text Position"
msgstr "ตำแหน่งข้อความ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Text muted. Lorem <b>ipsum dolor sit amet</b>, consectetur."
msgstr "ข้อความที่ถูกระงับ. Lorem <b>ipsum dolor sit amet</b>, consectetur."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Thank You For Your Feedback"
msgstr "ขอบคุณสำหรับความคิดเห็นของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "Thank You!"
msgstr "ขอบคุณ!"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "The Google Analytics Client ID or Key you entered seems incorrect."
msgstr "ไอดีลูกค้า Google Analytics หรือคีย์ที่คุณป้อนดูเหมือนไม่ถูกต้อง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "The chosen name already exists"
msgstr "ชื่อที่เลือกมีอยู่แล้ว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "The company this website belongs to"
msgstr "บริษัทที่เว็บไซต์นี้เป็นของ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid ""
"The current text selection cannot be animated. Try clearing the format and "
"try again."
msgstr "ข้อความที่เลือกไม่สามารถเคลื่อนไหวได้ ลองล้างรูปแบบแล้วลองอีกครั้ง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by search engines based on page content "
"unless you specify one."
msgstr ""
"คำอธิบายจะถูกสร้างขึ้นโดยเครื่องมือค้นหาตามเนื้อหาของหน้านั้น ๆ "
"เว้นแต่คุณจะระบุไว้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by social media based on page content "
"unless you specify one."
msgstr ""
"คำอธิบายจะถูกสร้างขึ้นโดยสื่อออนไลน์ตามเนื้อหาของหน้า เว้นแต่ว่าคุณจะระบุไว้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#, python-format
msgid "The form has been sent successfully."
msgstr "ส่งแบบฟอร์มเรียบร้อยแล้ว"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "The form's specified model does not exist"
msgstr "ไม่มีโมเดลที่ระบุในแบบฟอร์ม"

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_url
#: model:ir.model.fields,help:website.field_res_users__website_url
#: model:ir.model.fields,help:website.field_website_page__website_url
#: model:ir.model.fields,help:website.field_website_published_mixin__website_url
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_url
msgid "The full URL to access the document through the website."
msgstr "URL แบบเต็มเพื่อเข้าถึงเอกสารผ่านเว็บไซต์"

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_url
#: model:ir.model.fields,help:website.field_ir_cron__website_url
msgid "The full URL to access the server action through the website."
msgstr "URL แบบเต็มเพื่อเข้าถึงการดำเนินการของเซิร์ฟเวอร์ผ่านเว็บไซต์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "The installation of an App is already in progress."
msgstr "การติดตั้งแอปอยู่ระหว่างดำเนินการ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The language of the keyword and related keywords."
msgstr "ภาษาของคำสำคัญและคำสำคัญที่เกี่ยวข้อง"

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__limit
msgid "The limit is the maximum number of records retrieved"
msgstr "ขีดจำกัดคือจำนวนบันทึกสูงสุดที่ดึงมาได้"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "The limit must be between 1 and 16."
msgstr "ขีดจำกัดต้องอยู่ระหว่าง 1 ถึง 16"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "The message will be visible once the countdown ends"
msgstr "ข้อความจะปรากฏเมื่อสิ้นสุดการนับถอยหลัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "The selected templates will be reset to their factory settings."
msgstr "เทมเพลตที่เลือกจะถูกรีเซ็ตเป็นการตั้งค่าจากโรงงาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "The team"
msgstr "ทีมของเรา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The title will take a default value unless you specify one."
msgstr "ชื่อจะใช้ค่าเริ่มต้นเว้นแต่คุณจะระบุ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"The website will not work properly if you reject or discard those cookies."
msgstr "เว็บไซต์จะไม่ทำงานอย่างเหมาะสมหากคุณปฏิเสธหรือทิ้งคุกกี้เหล่านั้น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "The website will still work if you reject or discard those cookies."
msgstr "เว็บไซต์จะยังคงใช้งานได้หากคุณปฏิเสธหรือยกเลิกคุกกี้เหล่านั้น"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model:ir.model.fields,field_description:website.field_website__theme_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
#, python-format
msgid "Theme"
msgstr "ธีม"

#. module: website
#: model:ir.model,name:website.model_theme_ir_asset
msgid "Theme Asset"
msgstr "ธีม Asset"

#. module: website
#: model:ir.model,name:website.model_theme_ir_attachment
msgid "Theme Attachments"
msgstr "การแนบธีม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
#, python-format
msgid "Theme Colors"
msgstr "สีธีม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Theme Options"
msgstr "ตัวเลือกธีม"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_menu__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_page__theme_template_id
msgid "Theme Template"
msgstr "เทมเพลตธีม"

#. module: website
#: model:ir.model,name:website.model_theme_ir_ui_view
msgid "Theme UI View"
msgstr "มุมมองธีมแบบ UI "

#. module: website
#: model:ir.model,name:website.model_theme_utils
msgid "Theme Utils"
msgstr "ยูทิลิตี้ธีม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "There are currently no pages for this website."
msgstr "ขณะนี้ไม่มีเพจสำหรับเว็บไซต์นี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "There are currently no pages for your website."
msgstr "ขณะนี้ไม่มีเพจสำหรับเว็บไซต์ของคุณ"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "There are no contact and/or no email linked to this visitor."
msgstr "ไม่มีการติดต่อและ/หรืออีเมลที่เชื่อมโยงกับผู้เยี่ยมชมรายนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "There is no data currently available."
msgstr "ยังไม่มีข้อมูลในขณะนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "There is no field available for this option."
msgstr "ไม่มีช่องสำหรับตัวเลือกนี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"There is no website available for this company. You could create a new one."
msgstr "ไม่มีเว็บไซต์สำหรับบริษัทนี้ คุณสามารถสร้างใหม่ได้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"ข้อกำหนดในการให้บริการเหล่านี้ (\"ข้อกำหนด\"และ\"ข้อตกลง\") "
"เป็นข้อตกลงระหว่างเว็บไซต์ (\"ผู้ให้บริการเว็บไซต์\" \"เรา\" หรือ "
"\"ของเรา\") และคุณ (\"ผู้ใช้\"  \"คุณ\" หรือ \"ของคุณ \") "
"ข้อตกลงนี้กำหนดข้อกำหนดและเงื่อนไขทั่วไปของการใช้งานเว็บไซต์นี้และผลิตภัณฑ์หรือบริการใด"
" ๆ ของคุณ (เรียกรวมกันว่า \"เว็บไซต์\" หรือ \"บริการ\")"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "They trust us since years"
msgstr "พวกเขาไว้วางใจเรามานานหลายปี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thick"
msgstr "หนา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thin"
msgstr "บาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr "ฟีเจอร์ที่สาม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Third Menu"
msgstr "เมนูที่สาม"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__favicon
#: model:ir.model.fields,help:website.field_website__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr "ฟิลด์นี้เก็บภาพที่ใช้แสดง favicon บนเว็บไซต์"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_base
msgid "This field is the same as `arch` field without translations"
msgstr "ฟิลด์นี้เหมือนกับฟิลด์ `arch` ที่ยังไม่มีการแปล"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr "ฟิลด์นี้ใช้เพื่อตั้งค่า/รับตำแหน่งสำหรับผู้ใช้"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch
msgid ""
"This field should be used when accessing view arch. It will use translation.\n"
"                               Note that it will read `arch_db` or `arch_fs` if in dev-xml mode."
msgstr ""
"ฟิลด์นี้ควรใช้เมื่อเข้าถึงมุมมอง Arch และจะใช้ในการการแปล\n"
"                               โปรดทราบว่าระบบจะอ่าน \"arch_db\" หรือ \"arch_fs\" หากอยู่ในโหมด dev-xml"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_db
msgid "This field stores the view arch."
msgstr "ฟิลด์นี้เก็บมุมมองแบบ Arch ไว้"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_prev
msgid ""
"This field will save the current `arch_db` before writing on it.\n"
"                                                                         Useful to (soft) reset a broken view."
msgstr ""
"ฟิลด์นี้จะบันทึก \"arch_db\" ปัจจุบันก่อนที่จะเขียนลงไป\n"
"                                                                         มีประโยชน์ในการรีเซ็ตมุมมองที่เสียหายแบบซอฟต์ "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"This font already exists, you can only add it as a local font to replace the"
" server version."
msgstr ""
"ฟอนต์นี้มีอยู่แล้ว "
"คุณสามารถเพิ่มเป็นฟอนต์ในเครื่องเพื่อแทนที่เวอร์ชันเซิร์ฟเวอร์เท่านั้น"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "This font is hosted and served to your visitors by Google servers"
msgstr "ฟอนต์นี้โฮสต์และให้บริการแก่ผู้เยี่ยมชมของคุณโดยเซิร์ฟเวอร์ของ Google"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "This is a \""
msgstr "นี้คือ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr ""
"นี่คือ Hero unit และองค์ประกอบ Jumbotron-style "
"ที่เรียบง่ายเพื่อทำให้เนื้อหาหรือข้อมูลนั้นโดดเด่นดึงดูดความสนใจมากยิ่งขึ้น"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "This message has been posted on your website!"
msgstr "ข้อความนี้ถูกโพสต์บนเว็บไซต์ของคุณ!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "This page"
msgstr "เพจนี้"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "This operator is not supported"
msgstr "ไม่รองรับผู้ปฏิบัติการนี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exist, but you can create it as you are editor of this "
"site."
msgstr ""
"ไม่มีหน้านี้ แต่คุณสามารถสร้างได้เนื่องจากคุณเป็นผู้มีสิทธิแก้ไขของไซต์นี้"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "This page is in the menu <b>%s</b>"
msgstr "หน้านี้อยู่ในเมนู <b>%s</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "This page will be published on {{ date_formatted }}"
msgstr "หน้านี้จะถูกเผยแพร่เมื่อ {{ date_formatted }}"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "This translation is not editable."
msgstr "การแปลนี้ไม่สามารถแก้ไขได้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"This value will be escaped to be compliant with all major browsers and used "
"in url. Keep it empty to use the default name of the record."
msgstr ""
"ค่านี้จะถูกหลีกเลี่ยงเพื่อให้สอดคล้องกับเบราว์เซอร์หลักทั้งหมดและใช้ใน URL "
"เว้นว่างไว้เพื่อใช้ชื่อเริ่มต้นของการบันทึก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "This view arch has been modified"
msgstr "มุมมองนี้ได้รับการแก้ไขแล้ว"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Those accounts should now check their Analytics dashboard in the Google "
"platform directly."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr "Thumbnails"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__time_since_last_action
msgid "Time since last page view. E.g.: 2 minutes ago"
msgstr "เวลานับตั้งแต่การดูเพจครั้งล่าสุด เช่น 2 นาทีที่แล้ว"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__cache_time
msgid "Time to cache the page. (0 = no cache)"
msgstr "ถึงเวลาแคชหน้า. (0 = no cache)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Time's up! You can now visit"
msgstr "หมดเวลา! เข้าชมได้แล้ว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Timeline"
msgstr "ไทม์ไลน์"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__timezone
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Timezone"
msgstr "เขตเวลา"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_4
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid "Tip: Add shapes to energize your Website"
msgstr "Tip: เพิ่มรูปทรงเพื่อเพิ่มพลังให้เว็บไซต์ของคุณ"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_0
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid "Tip: Engage with visitors to convert them into leads"
msgstr "Tip: สร้างการมีส่วนร่วมกับผู้เยี่ยมชมเพื่อแปลงเป็นลูกค้าเป้าหมาย"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_2
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid "Tip: Search Engine Optimization (SEO)"
msgstr "Tip: เพิ่มประสิทธิภาพการค้นหา (SEO)"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_3
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid "Tip: Use illustrations to spice up your website"
msgstr "Tip: ใช้ภาพประกอบเพื่อเพิ่มสีสันให้เว็บไซต์ของคุณ"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_1
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid "Tip: Use royalty-free photos"
msgstr "Tip: ใช้ภาพถ่ายปลอดค่าลิขสิทธิ์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Title"
msgstr "ชื่อเรื่อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Title Position"
msgstr "ตำแหน่งชื่อเรื่อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"หากต้องการเพิ่มคอลัมน์ที่สี่ "
"ให้ลดขนาดของคอลัมน์ทั้งสามโดยใช้ไอคอนด้านขวาของแต่ละบล็อก จากนั้น "
"ทำซ้ำคอลัมน์ใดคอลัมน์หนึ่งเพื่อสร้างคอลัมน์ใหม่ในรูปแบบสำเนา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "To be successful your content needs to be useful to your readers."
msgstr ""
"การจะประสบความสำเร็จได้นั้น เนื้อหาของคุณต้องเป็นประโยชน์ต่อผู้อ่านของคุณ"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid ""
"To get more visitors, you should target keywords that are often searched in "
"Google. With the built-in SEO tool, once you define a few keywords, Odoo "
"will recommend you the best keywords to target. Then adapt your title and "
"description accordingly to boost your traffic."
msgstr ""
"เพื่อให้มีผู้เข้าชมมากขึ้น คุณควรกำหนดเป้าหมายของคำสำคัญที่มักถูกค้นหาใน "
"Google ด้วยเครื่องมือ SEO ที่มีในตัว เมื่อคุณกำหนดคำสำคัญเพียงสองสามคำแล้ว "
"Odoo จะแนะนำคำสำคัญที่ดีที่สุดอื่น ๆ ให้คุณเพื่อกำหนดเป้าหมาย "
"จากนั้นปรับชื่อและคำอธิบายของคุณให้เหมาะสมเพื่อเพิ่มจำนวนการเข้าชม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"หากต้องการส่งคำเชิญในโหมด B2B "
"ให้เปิดผู้ติดต่อหรือเลือกหลายรายการในมุมมองรายการแล้วคลิกตัวเลือก "
"'จัดการการเข้าถึงพอร์ทัล' ในเมนูดรอปดาวน์ *การดำเนินการ*"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle"
msgstr "สลับ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle navigation"
msgstr "ระบบนำทางแบบสลับ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Tooltip"
msgstr "ทูลทิป"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Top"
msgstr "บน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Top Menu"
msgstr "เมนูด้านบน"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Top Menu for Website %s"
msgstr "เมนูด้านบนสำหรับเว็บไซต์ %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Top to Bottom"
msgstr "บนลงล่าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Tops"
msgstr "เสื้อ"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__page_count
msgid "Total number of tracked page visited"
msgstr "จำนวนหน้าที่ติดตามทั้งหมดที่เยี่ยมชม"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visitor_page_count
msgid "Total number of visits on tracked pages"
msgstr "จำนวนการเข้าชมเพจที่ติดตามทั้งหมด"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__track
#: model:ir.model.fields,field_description:website.field_website_page__track
msgid "Track"
msgstr "ติดตาม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/track_page.xml:0
#, python-format
msgid "Track Visitor"
msgstr "ติดตามผู้เยี่ยมชม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track visits in Google Analytics"
msgstr "ติดตามการเยี่ยมชมใน Google Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Tracked"
msgstr "ติดตามแล้ว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Transition"
msgstr "การเปลี่ยนแปลง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate Attribute"
msgstr "คุณสมบัติการแปล"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate Selection Option"
msgstr "ตัวเลือกการแปล"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate header in the text. Menu is generated automatically."
msgstr "แปลส่วนหัวในข้อความ เมนูถูกสร้างขึ้นโดยอัตโนมัติ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Translated content"
msgstr "เนื้อหาที่แปลแล้ว"

#. module: website
#: model:ir.model,name:website.model_ir_translation
msgid "Translation"
msgstr "การแปล"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translation Info"
msgstr "ข้อมูลการแปล"

#. module: website
#: model:ir.model.fields,help:website.field_website__configurator_done
msgid "True if configurator has been completed or ignored"
msgstr "เป็นจริง หากตัวกำหนดค่านั้นเสร็จสิ้นหรือละเว้น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Tuna and Salmon Burger"
msgstr "เบอร์เกอร์ทูน่าและแซลมอน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr "เปลี่ยนทุกฟีเจอร์ให้เป็นประโยชน์สำหรับผู้อ่านของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Twitter"
msgstr "Twitter"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_twitter
#: model:ir.model.fields,field_description:website.field_website__social_twitter
msgid "Twitter Account"
msgstr "บัญชี Twitter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Twitter Scroller"
msgstr "Twitter Scroller"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__type
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Type"
msgstr "ประเภท"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Type '"
msgstr "ประเภท"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr ""
"พิมพ์'<i class=\"confirm_word\">ใช้</i>' ในกล่องด้านล่างหากคุณต้องการยืนยัน."

#. module: website
#: model:ir.model.fields,help:website.field_website_rewrite__redirect_type
msgid ""
"Type of redirect/Rewrite:\n"
"\n"
"        301 Moved permanently: The browser will keep in cache the new url.\n"
"        302 Moved temporarily: The browser will not keep in cache the new url and ask again the next time the new url.\n"
"        404 Not Found: If you want remove a specific page/controller (e.g. Ecommerce is installed, but you don't want /shop on a specific website)\n"
"        308 Redirect / Rewrite: If you want rename a controller with a new url. (Eg: /shop -> /garden - Both url will be accessible but /shop will automatically be redirected to /garden)\n"
"    "
msgstr ""
"ประเภทของการเปลี่ยนเส้นทาง / เขียนใหม่:\n"
"\n"
"         301 ย้ายอย่างถาวร: เบราว์เซอร์จะเก็บ URL ใหม่ไว้ในแคช\n"
"         302 ย้ายชั่วคราว: เบราว์เซอร์จะไม่เก็บไว้ในแคชและ url ใหม่และถามอีกครั้งในครั้งต่อไปที่ URL ใหม่\n"
"         404 ไม่พบ: หากคุณต้องการลบเพจ / ตัวควบคุมเฉพาะ (เช่นติดตั้งอีคอมเมิร์ซ แต่คุณไม่ต้องการ / ซื้อสินค้าบนเว็บไซต์ใดเว็บไซต์หนึ่ง)\n"
"         308 เปลี่ยนเส้นทาง / เขียนใหม่: หากคุณต้องการเปลี่ยนชื่อแผงควบคุมด้วย url ใหม่ (เช่น: / shop -> / garden - ทั้งสอง url จะเข้าถึงได้ แต่ / shop จะถูกเปลี่ยนเส้นทางไปที่ / garden โดยอัตโนมัติ)\n"
" "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "URL"
msgstr "URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_from
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "URL from"
msgstr "URL จาก"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,help:website.field_website__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr "URL ที่ตรงกับตัวกรองเหล่านั้นจะถูกเขียนใหม่โดยใช้ CDN Base URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_to
msgid "URL to"
msgstr "URL ถึง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Unalterable unique identifier"
msgstr "ตัวระบุเฉพาะที่ไม่สามารถเปลี่ยนแปลงได้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Underline On Hover"
msgstr "ขีดเส้นใต้บน Hover"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Understand how visitors engage with our website, via Google Analytics.\n"
"                                                Learn more about"
msgstr ""
"ทำความเข้าใจว่าผู้เยี่ยมชมสามารถมีส่วนร่วมกับเว็บไซต์ของเราอย่างไรผ่าน Google Analytics\n"
"                                               เรียนรู้เพิ่มเติม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.index_management
msgid "Unindexed"
msgstr "ไม่ได้จัดทำดัชนี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr "ความสามารถและการสนับสนุนจาก CRM ที่ไม่จำกัด"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr "ปรับแต่งได้ไม่จำกัด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#, python-format
msgid "Unpublish"
msgstr "ไม่ได้เผยแพร่"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Unpublished"
msgstr "ไม่ได้เผยแพร่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Unregistered"
msgstr "ไม่ได้ลงทะเบียน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Update theme"
msgstr "อัปเดตธีม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Upload"
msgstr "อัพโหลด"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Uploaded file is too large."
msgstr "ไฟล์ที่อัปโหลดมีขนาดใหญ่เกินไป"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__url
#: model:ir.model.fields,field_description:website.field_theme_website_menu__url
#: model:ir.model.fields,field_description:website.field_theme_website_page__url
#: model:ir.model.fields,field_description:website.field_website_menu__url
#: model:ir.model.fields,field_description:website.field_website_track__url
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Url"
msgstr "Url"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__country_flag
msgid "Url of static flag image"
msgstr "URL ของภาพธงคงที่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Urls & Pages"
msgstr "Urls & Pages"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use Google Map on your website ("
msgstr "ใช้ Google Map บนเว็บไซต์ของคุณ ("

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Use Google Map on your website (Contact Us page, snippets, etc)."
msgstr ""
"ใช้ Google Map บนเว็บไซต์ของคุณ เช่น ในหน้าติดต่อเรา Snippet และ อื่น ๆ "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use a CDN to optimize the availability of your website's content"
msgstr "ใช้ CDN เพื่อปรับความพร้อมใช้งานของเนื้อหาเว็บไซต์ของคุณให้เหมาะสม"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_default_share_image
msgid "Use a image by default for sharing"
msgstr "ใช้รูปภาพเป็นค่าเริ่มต้นสำหรับการแชร์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Use as Homepage"
msgstr "ใช้เป็นโฮมเพจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Use of Cookies"
msgstr "การใช้คุกกี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr ""
"ใช้องค์ประกอบนี้เพื่อสร้างรายการองค์ประกอบของฟีเจอร์ที่คุณต้องการทำให้โดดเด่นขึ้น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""
"ใช้ snippet นี้เพื่อสร้างส่วนประกอบประเภทต่าง ๆ "
"ที่จัดอยู่ในด้านซ้ายหรือขวาของรูปภาพควบคู่ไปกับเนื้อหาที่เป็นข้อความ "
"ทำซ้ำองค์ประกอบเพื่อสร้างรายการที่ตรงกับความต้องการของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Use this snippet to presents your content in a slideshow-like format. Don't "
"write about products or services here, write about solutions."
msgstr ""
"ใช้ snippet เพื่อนำเสนอเนื้อหาของคุณในรูปแบบที่เหมือนงานนำเสนอภาพนิ่ง "
"อย่าเขียนเกี่ยวกับผลิตภัณฑ์หรือบริการที่นี่ เขียนเกี่ยวกับโซลูชัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Use this theme"
msgstr "ใช้ธีมนี้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"Use this timeline as a part of your resume, to show your visitors what "
"you've done in the past."
msgstr ""
"ใช้ไทม์ไลน์นี้เป็นส่วนหนึ่งของประวัติย่อของคุณ "
"เพื่อแสดงให้ผู้เยี่ยมชมเห็นว่าคุณทำอะไรไปบ้างในอดีต"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_key
msgid "Used in FormBuilder Registry"
msgstr "ใช้ใน FormBuilder Registry"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page content"
msgstr "ใช้ในเนื้อหาของเพจ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page description"
msgstr "ใช้ในคำอธิบายของเพจ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page first level heading"
msgstr "ใช้ในหัวเรื่องระดับแรกของหน้า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page second level heading"
msgstr "ใช้ในหัวเรื่องระดับสองของหน้า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page title"
msgstr "ใช้ในชื่อหน้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to collect information about your interactions with the website, the pages you've seen,\n"
"                                                and any specific marketing campaign that brought you to the website."
msgstr ""
"ใช้เพื่อรวบรวมข้อมูลเกี่ยวกับการโต้ตอบของคุณกับเว็บไซต์ หน้าที่คุณเข้าชม\n"
"                                                และแคมเปญการตลาดเฉพาะใด ๆ ที่นำคุณมาที่เว็บไซต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to make advertising more engaging to users and more valuable to publishers and advertisers,\n"
"                                                such as providing more relevant ads when you visit other websites that display ads or to improve reporting on ad campaign performance."
msgstr ""
"ใช้เพื่อทำให้โฆษณามีส่วนร่วมกับผู้ใช้มากขึ้นและมีคุณค่ามากขึ้นสำหรับผู้เผยแพร่และนักโฆษณา\n"
"                                                เช่น การแสดงโฆษณาที่เกี่ยวข้องมากขึ้นเมื่อคุณเยี่ยมชมเว็บไซต์อื่น ๆ ที่แสดงโฆษณา หรือเพื่อปรับปรุงการรายงานเกี่ยวกับประสิทธิภาพของแคมเปญโฆษณา"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,help:website.field_website__country_group_ids
msgid "Used when multiple websites have the same domain."
msgstr "ใช้เมื่อหลายเว็บไซต์มีโดเมนเดียวกัน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Useful Links"
msgstr "ลิงก์ที่มีประโยชน์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Useful options"
msgstr "ตัวเลือกที่มีประโยชน์"

#. module: website
#: model:ir.model.fields,help:website.field_website_menu__group_ids
msgid "User need to be at least in one of these groups to see the menu"
msgstr "ผู้ใช้ต้องอยู่ในกลุ่มเหล่านี้อย่างน้อยหนึ่งกลุ่มจึงจะเห็นเมนู"

#. module: website
#: model:ir.model,name:website.model_res_users
msgid "Users"
msgstr "ผู้ใช้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Utilities &amp; Typography"
msgstr "ประโยชน์ใช้สอยและการพิมพ์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Value"
msgstr "ค่า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vert. Alignment"
msgstr "Vert. Alignment"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical"
msgstr "แนวตั้ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical Alignment"
msgstr "การจัดตำแหน่งแนวตั้ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Video"
msgstr "วิดีโอ"

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
#: model:ir.model.fields,field_description:website.field_theme_website_page__view_id
#: model:ir.model.fields,field_description:website.field_website_page__view_id
msgid "View"
msgstr "มุมมอง"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch
msgid "View Architecture"
msgstr "ดูสถาปัตยกรรม"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__name
msgid "View Name"
msgstr "ชื่อมุมมอง"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__type
msgid "View Type"
msgstr "ประเภทมุมมอง"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__mode
msgid "View inheritance mode"
msgstr "ดูโหมดการสืบทอด"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__copy_ids
msgid "Views using a copy of me"
msgstr "มุมมองโดยใช้สำเนาของฉัน"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_children_ids
msgid "Views which inherit from this one"
msgstr "มุมมองที่สืบทอดมาจากมุมมองนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility
#: model:ir.model.fields,field_description:website.field_website_page__visibility
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Visibility"
msgstr "การมองเห็น"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Visibility Password"
msgstr "การมองเห็นรหัสผ่าน"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password_display
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password_display
msgid "Visibility Password Display"
msgstr "แสดงการมองเห็นรหัสผ่าน"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__group_ids
msgid "Visible Groups"
msgstr "กลุ่มที่่มองเห็น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Visible for"
msgstr "มองเห็นได้สำหรับ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Everyone"
msgstr "มองเห็นได้สำหรับทุกคน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged In"
msgstr "มองเห็นได้สำหรับการเข้าสู่ระบบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged Out"
msgstr "มองเห็นได้เมื่อออกจากระบบ"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_published
#: model:ir.model.fields,field_description:website.field_res_users__website_published
#: model:ir.model.fields,field_description:website.field_website_page__website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_published
msgid "Visible on current website"
msgstr "มองเห็นได้บนเว็บไซต์ปัจจุบัน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Visible on mobile"
msgstr "มองเห็นได้บนมือถือ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Visible only if"
msgstr "มองเห็นได้ก็ต่อเมื่อ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visit_datetime
msgid "Visit Date"
msgstr "วันที่เข้าชม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Visit our Facebook page to know if you are one of the lucky winners."
msgstr "เยี่ยมชมหน้า Facebook ของเราเพื่อดูว่าคุณเป็นหนึ่งในผู้โชคดีหรือไม่"

#. module: website
#: model:ir.model,name:website.model_website_track
#: model:ir.model.fields,field_description:website.field_website_visitor__page_ids
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visited Pages"
msgstr "หน้าที่เข้าชม"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__website_track_ids
msgid "Visited Pages History"
msgstr "ประวัติเพจที่เข้าชม"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visitor_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visitor"
msgstr "ผู้เยี่ยมชม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_graph
msgid "Visitor Page Views"
msgstr "การเข้าชมเพจของผู้เยี่ยมชม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_tree
msgid "Visitor Page Views History"
msgstr "ประวัติเพจที่เข้าชมของผู้เยี่ยมชม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_graph
msgid "Visitor Views"
msgstr "จำนวนผู้เข้าชม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_tree
msgid "Visitor Views History"
msgstr "ประวัติการเข้าชมของผู้เยี่ยมชม"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitors_action
#: model:ir.model.fields,field_description:website.field_res_partner__visitor_ids
#: model:ir.model.fields,field_description:website.field_res_users__visitor_ids
#: model:ir.ui.menu,name:website.menu_visitor_sub_menu
#: model:ir.ui.menu,name:website.website_visitor_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_graph
msgid "Visitors"
msgstr "ผู้เยี่ยมชม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#, python-format
msgid "Visits"
msgstr "เยี่ยมชม"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_view_action
msgid ""
"Wait for visitors to come to your website to see the pages they viewed."
msgstr "รอให้ผู้เยี่ยมชมมาที่เว็บไซต์ของคุณเพื่อดูหน้าที่พวกเขาดู"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid ""
"Wait for visitors to come to your website to see their history and engage "
"with them."
msgstr "รอให้ผู้เข้าชมมาที่เว็บไซต์ของคุณเพื่อดูประวัติและมีส่วนร่วมกับพวกเขา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Warning"
msgstr "คำเตือน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Watches"
msgstr "นาฬิกา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products. We build great products to solve your business problems.\n"
"                            <br/><br/>Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""
"เราเป็นทีมที่มีความกระตือรือร้นและมีเป้าหมายเพื่อปรับปรุงชีวิตของทุกคนผ่านสินค้าที่จะสั่นสะเทือนวงการ เราสร้างผลิตภัณฑ์ที่ยอดเยี่ยมเพื่อแก้ปัญหาธุรกิจของคุณ\n"
"                            <br/><br/> สินค้าของเราได้รับการออกแบบสำหรับบริษัทขนาดเล็กถึงขนาดกลางที่ต้องการเพิ่มประสิทธิภาพการทำงาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems. Our products are designed for small to medium size companies "
"willing to optimize their performance."
msgstr ""
"เราเป็นทีมที่มีความกระตือรือร้นและมีเป้าหมายเพื่อปรับปรุงชีวิตของทุกคนผ่านสินค้าที่จะสั่นสะเทือนวงการ"
" เราสร้างผลิตภัณฑ์ที่ยอดเยี่ยมเพื่อแก้ปัญหาธุรกิจของคุณ "
"สินค้าของเราได้รับการออกแบบสำหรับบริษัทขนาดเล็กถึงขนาดกลางที่ต้องการเพิ่มประสิทธิภาพการทำงาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid ""
"We are a team of passionate people whose goal is to improve everyone's "
"life.<br/>Our services are designed for small to medium size companies."
msgstr ""
"เราเป็นทีมที่มีความกระตือรือร้นซึ่งมีเป้าหมายที่จะปรับปรุงชีวิตของทุกคน<br/>บริการของเราออกแบบมาสำหรับบริษัทขนาดเล็กถึงขนาดกลาง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "We are almost done!"
msgstr "เราใกล้จะเสร็จแล้ว!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "We are in good company."
msgstr "เราอยู่ในบริษัทที่ดี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We do not currently support Do Not Track signals, as there is no industry "
"standard for compliance."
msgstr ""
"ขณะนี้เราไม่รองรับสัญญาณ \"ห้ามติดตาม\" "
"เนื่องจากไม่มีมาตรฐานอุตสาหกรรมสำหรับการร่วมมือนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "We found these ones:"
msgstr "เราพบสิ่งเหล่านี้:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_slogan_oe_structure_header_slogan_1
msgid "We help <b>you</b> grow your business"
msgstr "เราช่วย <b>คุณ</b> พัฒนาธุรกิจของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We may not be able to provide the best service to you if you reject those "
"cookies, but the website will work."
msgstr ""
"เราอาจไม่สามารถมอบบริการที่ดีที่สุดแก่คุณได้หากคุณปฏิเสธคุกกี้เหล่านั้น "
"แต่เว็บไซต์จะยังใช้งานได้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "We offer tailor-made products according to your needs and your budget."
msgstr "เรานำเสนอสินค้าที่ออกแบบตามความต้องการและงบประมาณของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "We use cookies to provide you a better user experience."
msgstr "เราใช้คุกกี้เพื่อมอบประสบการณ์การใช้งานที่ดียิ่งขึ้นแก่คุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid ""
"We use them to store info about your habits on our website. It will helps us"
" to provide you the very best experience and customize what you see."
msgstr ""
"เราใช้มันเพื่อเก็บข้อมูลเกี่ยวกับนิสัยของคุณบนเว็บไซต์ของเรา "
"ซึ่งจะช่วยให้เรามอบประสบการณ์ที่ดีที่สุดแก่คุณและปรับแต่งสิ่งที่คุณเห็น"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "We will get back to you shortly."
msgstr "เราจะติดต่อกลับหาคุณในไม่ช้า"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "We'll set you up and running in"
msgstr "เราจะตั้งค่าและดำเนินการใน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Web Visitors"
msgstr "ผู้เยี่ยมชมเว็บ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_asset__website_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_id
#: model:ir.model.fields,field_description:website.field_res_company__website_id
#: model:ir.model.fields,field_description:website.field_res_partner__website_id
#: model:ir.model.fields,field_description:website.field_res_users__website_id
#: model:ir.model.fields,field_description:website.field_website_menu__website_id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_page__website_id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_rewrite__website_id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_id
#: model:ir.model.fields,field_description:website.field_website_visitor__website_id
#: model:ir.ui.menu,name:website.menu_website_configuration
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
#, python-format
msgid "Website"
msgstr "เว็บไซต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_company_id
msgid "Website Company"
msgstr "เว็บไซต์ของบริษัท"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__website_config_preselection
msgid "Website Config Preselection"
msgstr "การเลือกการกำหนดค่าเว็บไซต์ล่วงหน้า"

#. module: website
#: model:ir.actions.act_url,name:website.start_configurator_act_url
msgid "Website Configurator"
msgstr "ตัวกำหนดค่าเว็บไซต์"

#. module: website
#: model:ir.model,name:website.model_website_configurator_feature
msgid "Website Configurator Feature"
msgstr "ฟีเจอร์ตัวกำหนดค่าเว็บไซต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_domain
#: model:ir.model.fields,field_description:website.field_website__domain
msgid "Website Domain"
msgstr "เว็บไซต์โดเมน"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__favicon
msgid "Website Favicon"
msgstr "เว็บไซต์ Favicon"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_key
msgid "Website Form Key"
msgstr "คีย์เว็บไซต์ฟอร์ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.ir_model_view
msgid "Website Forms"
msgstr "เว็บไซต์ฟอร์ม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_logo
#: model:ir.model.fields,field_description:website.field_website__logo
#, python-format
msgid "Website Logo"
msgstr "โลโก้เว็บไซต์"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr "เมนูเว็บไซต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Website Menus Settings"
msgstr "ตั้งค่าเมนูเว็บไซต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_name
#: model:ir.model.fields,field_description:website.field_website__name
msgid "Website Name"
msgstr "ชื่อเว็บไซต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,field_description:website.field_website_page__first_page_id
msgid "Website Page"
msgstr "หน้าของเว็บไซต์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Website Page Settings"
msgstr "ตั้งค่าหน้าของเว็บไซต์"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Website Pages"
msgstr "หน้าของเว็บไซต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_path
#: model:ir.model.fields,field_description:website.field_ir_cron__website_path
msgid "Website Path"
msgstr "เส้นทางเว็บไซต์"

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "Website Published Mixin"
msgstr "เผยแพร่เว็บไซต์ Mixin"

#. module: website
#: model:ir.model,name:website.model_website_searchable_mixin
msgid "Website Searchable Mixin"
msgstr "เว็บไซต์ที่ค้นหาได้ใน Mixin"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form
#, python-format
msgid "Website Settings"
msgstr "ตั้งค่าเว็บไซต์"

#. module: website
#: model:ir.model,name:website.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "ตัวกรอง Snippet ของเว็บไซต์"

#. module: website
#: model:ir.model,name:website.model_theme_website_menu
msgid "Website Theme Menu"
msgstr "เมนูธีมของเว็บไซต์"

#. module: website
#: model:ir.model,name:website.model_theme_website_page
msgid "Website Theme Page"
msgstr "เพจเว็บไซต์ธีม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Title"
msgstr "ชื่อเว็บไซต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_url
#: model:ir.model.fields,field_description:website.field_res_partner__website_url
#: model:ir.model.fields,field_description:website.field_res_users__website_url
#: model:ir.model.fields,field_description:website.field_website_page__website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_url
msgid "Website URL"
msgstr " เว็บไซต์ URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_url
#: model:ir.model.fields,field_description:website.field_ir_cron__website_url
msgid "Website Url"
msgstr "เว็บไซต์ URL"

#. module: website
#: model:ir.model,name:website.model_website_visitor
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Website Visitor"
msgstr "ผู้เยี่ยมชมเว็บไซต์"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Website Visitor #%s"
msgstr "ผู้เยี่ยมชมเว็บไซต์ #%s"

#. module: website
#: model:ir.actions.server,name:website.website_visitor_cron_ir_actions_server
#: model:ir.cron,cron_name:website.website_visitor_cron
#: model:ir.cron,name:website.website_visitor_cron
msgid "Website Visitor : Archive old visitors"
msgstr "ผู้เยี่ยมชมเว็บไซต์ : เก็บถาวรผู้เยี่ยมชมเก่า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"เว็บไซต์อาจใช้คุกกี้เพื่อปรับแต่งและอำนวยความสะดวกในการนำทางสูงสุดของผู้ใช้โดยเว็บไซต์นี้"
" "
"ผู้ใช้อาจกำหนดค่าเบราว์เซอร์ของตนเพื่อแจ้งและปฏิเสธการติดตั้งคุกกี้ที่เราส่งมา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr "เมนูเว็บไซต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_description
msgid "Website meta description"
msgstr "คำอธิบายเนื้อหาเว็บไซต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_keywords
msgid "Website meta keywords"
msgstr " คำสำคัญในคำอธิบายเนื้อหาเว็บไซต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_title
msgid "Website meta title"
msgstr "ชื่อข้อมูลเว็บไซต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_og_img
msgid "Website opengraph image"
msgstr "รูป Opengraph บนเว็บไซต์"

#. module: website
#: model:ir.model,name:website.model_website_rewrite
msgid "Website rewrite"
msgstr "เขียนเว็บไซต์ใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Website rewrite Settings"
msgstr "ตั้งค่าการเขียนเว็บไซต์ใหม่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.action_website_rewrite_tree
msgid "Website rewrites"
msgstr "เขียนเว็บไซต์ใหม่"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_google_analytics
msgid "Website: Analytics"
msgstr "เว็บไซต์: การวิเคราะห์"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_dashboard
msgid "Website: Dashboard"
msgstr "เว็บไซต์: แดชบอร์ด"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_list
#: model:ir.ui.menu,name:website.menu_website_websites_list
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr "เว็บไซต์"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install__website_ids
msgid "Websites to translate"
msgstr "เว็บไซต์ที่จะแปล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Websites-shared page"
msgstr "หน้าที่แชร์ของเว็บไซต์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Welcome to your"
msgstr "ยินดีต้อนรับสู่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "What you see is what you get"
msgstr "สิ่งที่คุณเห็นคือสิ่งที่คุณได้รับ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Width"
msgstr "ความกว้าง"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__password
msgid "With Password"
msgstr "ด้วยรหัสผ่าน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Women"
msgstr "ผู้หญิง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid ""
"Would you like to save before being redirected? Unsaved changes will be "
"discarded."
msgstr ""
"คุณต้องการบันทึกก่อนที่จะเปลี่ยนเส้นทางหรือไม่? "
"การเปลี่ยนแปลงที่ยังไม่ได้บันทึกจะถูกละทิ้ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr ""
"เขียนโควทคำพูดจากลูกค้าของคุณที่นี่ "
"โควทเป็นวิธีที่ยอดเยี่ยมในการสร้างความมั่นใจในสินค้าหรือบริการของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Write one or two paragraphs describing your product or services."
msgstr "เขียนหนึ่งหรือสองย่อหน้าเพื่ออธิบายผลิตภัณฑ์หรือบริการ "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"เขียนหนึ่งหรือสองย่อหน้าเพื่ออธิบายผลิตภัณฑ์หรือบริการ "
"เพื่อให้คุณประสบความสำเร็จเนื้อหาจะต้องเป็นประโยชน์ต่อผู้อ่านของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"เขียนหนึ่งหรือสองย่อหน้าเพื่ออธิบายผลิตภัณฑ์ บริการ "
"หรือคุณลักษณะเฉพาะของคุณ<br/>เพื่อให้คุณประสบความสำเร็จเนื้อหาจะต้องเป็นประโยชน์ต่อผู้อ่านของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know, <br/>not what you want to show."
msgstr "เขียนสิ่งที่ลูกค้าต้องการทราบ<br/>ไม่ใช่สิ่งที่คุณต้องการแสดง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Year"
msgstr "ปี"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "You are about to enter the translation mode."
msgstr "คุณกำลังจะเข้าสู่โหมดการแปล"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"You can choose to have your computer warn you each time a cookie is being sent, or you can choose to turn off all cookies.\n"
"                            Each browser is a little different, so look at your browser's Help menu to learn the correct way to modify your cookies."
msgstr ""
"คุณสามารถเลือกให้คอมพิวเตอร์เตือนคุณทุกครั้งที่มีการส่งคุกกี้ หรือคุณสามารถเลือกที่จะปิดคุกกี้ทั้งหมดได้\n"
"                            แต่ละเบราว์เซอร์จะแตกต่างกันเล็กน้อย ดังนั้นให้ดูที่เมนูวิธีใช้ของเบราว์เซอร์เพื่อเรียนรู้วิธีแก้ไขคุกกี้ที่ถูกต้อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr "คุณสามารถแก้ไขสีและพื้นหลังเพื่อเน้นฟีเจอร์ต่าง ๆ "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "You can edit, duplicate..."
msgstr "คุณสามารถแก้ไข ทำซ้ำ..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"You can have 2 websites with same domain AND a condition on country group to"
" select wich website use."
msgstr ""
"คุณสามารถมีเว็บไซต์ 2 "
"เว็บที่มีโดเมนเดียวกันและต้องตามเงื่อนไขในกลุ่มประเทศที่เลือกเพื่อเลือกเว็บไซต์ที่ใช้"

#. module: website
#: code:addons/website/models/res_users.py:0
#: model:ir.model.constraint,message:website.constraint_res_users_login_key
#, python-format
msgid "You can not have two users with the same login!"
msgstr "คุณไม่สามารถมีผู้ใช้สองคนสำหรับการเข้าสู่ระบบเดียวกันได้!"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "You can only use template prefixed by dynamic_filter_template_ "
msgstr "คุณสามารถใช้ได้เฉพาะเทมเพลตที่นำหน้าด้วย dynamic_filter_template_ "

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate a model field."
msgstr "คุณไม่สามารถทำซ้ำฟิลด์ของโมเดลได้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate the submit button of the form."
msgstr "คุณไม่สามารถทำซ้ำปุ่มส่งของแบบฟอร์มได้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove a field that is required by the model itself."
msgstr "คุณไม่สามารถลบฟิลด์ที่โมเดลต้องการได้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove the submit button of the form"
msgstr "คุณไม่สามารถลบปุ่มส่งของแบบฟอร์มได้"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid ""
"You cannot delete default website %s. Try to change its settings instead"
msgstr "คุณไม่สามารถลบเว็บไซต์เริ่มต้น %s ได้ ลองเปลี่ยนการตั้งค่าแทน"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "You do not have sufficient rights to perform that action."
msgstr "คุณไม่มีสิทธิ์เพียงพอที่จะดำเนินการดังกล่าว"

#. module: website
#: code:addons/website/models/mixins.py:0
#, python-format
msgid "You do not have the rights to publish/unpublish"
msgstr "คุณไม่มีสิทธิ์เผยแพร่/ยกเลิกการเผยแพร่"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You do not seem to have access to this Analytics Account."
msgstr "ดูเหมือนว่าคุณจะไม่มีสิทธิเข้าถึงบัญชี Analytics นี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"You have hidden this page from search results. It won't be indexed by search"
" engines."
msgstr ""
"คุณได้ซ่อนหน้านี้จากผลการค้นหา มันจะไม่ถูกสจัดทำดัชนีโดยเครื่องมือค้นหา"

#. module: website
#: code:addons/website/models/res_config_settings.py:0
#, python-format
msgid "You haven't defined your domain"
msgstr "คุณยังไม่ได้กำหนดโดเมนของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "You may opt-out of a third-party's use of cookies by visiting the"
msgstr "คุณสามารถเลือกไม่ใช้คุกกี้ของบุคคลที่สามได้โดยไปที่"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "You must keep at least one website."
msgstr "คุณต้องเก็บอย่างน้อยหนึ่งเว็บไซต์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You need to log in to your Google Account before:"
msgstr "คุณต้องลงชื่อเข้าใช้บัญชี Google ของคุณก่อน:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"คุณควรตรวจสอบข้อความทางกฎหมายและเงื่อนไขอื่น ๆ ในการใช้งานเว็บไซต์ใด ๆ "
"ที่คุณเข้าถึงผ่านลิงก์จากเว็บไซต์นี้อย่างรอบคอบ การเชื่อมโยงไปยังเพจนอกอื่น "
"ๆ หรือเว็บไซต์อื่น ๆ ถือเป็นความเสี่ยงของคุณเอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "You will get results from blog posts, products, etc"
msgstr "คุณจะได้ผลลัพธ์จากการบล็อกโพสต์ สินค้า และอื่น ๆ "

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "You'll be able to create your pages later on."
msgstr "คุณจะสามารถสร้างเพจของคุณได้ในภายหลัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "YouTube"
msgstr "YouTube"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Client ID:"
msgstr "ไอดีลูกค้าของคุณ:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Company"
msgstr "บริษัทของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid ""
"Your Dynamic Snippet will be displayed here... This message is displayed "
"because you did not provided both a filter and a template to use.<br/>"
msgstr ""
"Dynamic Snippet ของคุณจะแสดงที่นี่... "
"ข้อความนี้แสดงขึ้นเนื่องจากคุณไม่ได้ให้ทั้งตัวกรองและเทมเพลตที่จะใช้<br/>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Email"
msgstr "อีเมลของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Measurement ID:"
msgstr "Measurement ID ของคุณ:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Name"
msgstr "ชื่อของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Question"
msgstr "คำถามของคุณ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too long."
msgstr "คำอธิบายของคุณดูยาวเกินไป"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too short."
msgstr "คำอธิบายของคุณดูสั้นเกินไป"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Your experience may be degraded if you discard those cookies, but the "
"website will still work."
msgstr ""
"ประสบการณ์ของคุณอาจลดลงหากคุณละทิ้งคุกกี้เหล่านั้น "
"แต่เว็บไซต์จะยังคงทำงานอยู่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "Your search '"
msgstr "การค้นหาของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "Your title"
msgstr "หัวข้อของคุณ"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_youtube
#: model:ir.model.fields,field_description:website.field_website__social_youtube
msgid "Youtube Account"
msgstr "บัญชี Youtube"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Zoom"
msgstr "ขยาย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In"
msgstr "ขยายเข้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Down"
msgstr "ขยายเข้า-ลง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Left"
msgstr "ขยายเข้า-ซ้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Right"
msgstr "ขยายเข้า-ขวา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom Out"
msgstr "ขยายออก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"
msgstr ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"
msgstr ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "a blog"
msgstr "บล็อก"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "a business website"
msgstr "เว็บไซต์ธุรกิจ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "a new image"
msgstr "รูปภาพใหม่"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "a pre-made Palette"
msgstr "จานสีสำเร็จรูป"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an elearning platform"
msgstr "แพลตฟอร์มอีเลิร์นนิง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an event website"
msgstr "เว็บไซต์อีเวนต์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an online store"
msgstr "ร้านค้าออนไลน์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "and"
msgstr "และ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "and copy paste the address of the font page here."
msgstr "แล้วคัดลอกวางที่อยู่ของหน้าแรกที่นี่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "big"
msgstr "ใหญ่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "breadcrumb"
msgstr "breadcrumb"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-outline-primary"
msgstr "btn-outline-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-outline-secondary"
msgstr "btn-outline-secondary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-primary"
msgstr "btn-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-secondary"
msgstr "btn-secondary"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "business"
msgstr "ธุรกิจ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "celebration, launch"
msgstr "งานเฉลิมฉลอง, เปิดตัว"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "chart, table, diagram, pie"
msgstr "แผนภูมิแท่ง, ตาราง, แผนภูมิภาพ, แผนภูมิวงกลม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "cite"
msgstr "อ้างอิง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "columns, description"
msgstr "คอลัมน์, คำอธิบาย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "common answers, common questions"
msgstr "คำตอบทั่วไป, คำถามทั่วไป"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "content"
msgstr "เนื้อหา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "customers, clients"
msgstr "ลูกค้า, ผู้รับบริการ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "days"
msgstr "วัน"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "develop the brand"
msgstr "พัฒนาแบรนด์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "e.g. /my-awesome-page"
msgstr "เช่น  /my-awesome-page"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "e.g. About Us"
msgstr "เช่น เกี่ยวกับเรา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "e.g. De Brouckere, Brussels, Belgium"
msgstr "เช่น De Brouckere, Brussels, Belgium"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_dashboard
msgid "eCommerce Dashboard"
msgstr "แดชบอร์ดอีคอมเมิร์ซ"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_elearning
msgid "eLearning"
msgstr "eLearning"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "email"
msgstr "อีเม"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "esc"
msgstr "esc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "evolution, growth"
msgstr "วิวัฒนาการ การเติบโต"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "fonts.google.com"
msgstr "fonts.google.com"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "for my"
msgstr "ของ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "found(s)"
msgstr "พบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "free website"
msgstr "เว็บไซต์ฟรี"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "from Logo"
msgstr "จากโลโก้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "frontend_lang (Odoo)"
msgstr "frontend_lang (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "gallery, carousel"
msgstr "แกลลอรี่, carousel"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "get leads"
msgstr "รับลูกค้าเป้าหมาย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "google1234567890123456.html"
msgstr "google1234567890123456.html"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "heading, h1"
msgstr "heading, h1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "hero, jumbotron"
msgstr "hero, jumbotron"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "https://fonts.google.com/specimen/Roboto"
msgstr "https://fonts.google.com/specimen/Roboto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "https://www.odoo.com"
msgstr "https://www.odoo.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "iPhone"
msgstr "ไอโฟน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"
msgstr ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "image, media, illustration"
msgstr "รูปภาพ, มีเดีย, ภาพประกอบ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "in the top right corner to start designing."
msgstr "ที่มุมขวาบนเพื่อเริ่มออกแบบ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "inform customers"
msgstr "แจ้งลูกค้า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr "ตัวอย่างของ Odoo"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "link"
msgstr "ลิงก์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "masonry, grid"
msgstr "masonry, ตาราง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "menu, pricing"
msgstr "เมนู, ราคา"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "no value"
msgstr "ไม่มีค่า"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "o-color-"
msgstr "o-color-"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid ""
"of\n"
"            your visitors. We recommend you avoid them unless you have\n"
"            verified with a legal advisor that you absolutely need cookie\n"
"            consent in your country."
msgstr ""
"ของ\n"
"           ผู้เยี่ยมชมของคุณ เราแนะนำให้คุณหลีกเลี่ยงเว้นแต่ว่าคุณได้\n"
"           ตรวจสอบกับที่ปรึกษาด้านกฎหมายว่าคุณจำเป็นต้องมีการยินยอมคุกกี้\n"
"            ในประเทศของคุณ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "or Edit Master"
msgstr "หรือ ตัวแก้ไขหลัก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "organization, structure"
msgstr "องกรค์, โครงสร้าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "page, snippets, ...)"
msgstr "เพจ, snippets, ...)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "perfect website?"
msgstr "เว็บไซต์ที่ยอดเยี่ยม?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_form_preview
msgid "phone"
msgstr "โทรศัพท์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "placeholder"
msgstr "ตัวอย่างข้อความ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "pointer to build the perfect page in 7 steps."
msgstr "คำแนะนำเพื่อสร้างเพจที่สมบูรณ์แบบใน 7 ขั้นตอน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "pricing"
msgstr "ราคา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "promotion, characteristic, quality"
msgstr "โปรโมชั่น ลักษณะ คุณภาพ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "results"
msgstr "ผลลัพธ์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "rows"
msgstr "แถว"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "schedule appointments"
msgstr "กำหนดการนัดหมาย"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "sell more"
msgstr "ขายเพิ่ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "separator, divider"
msgstr "ตัวคั่น, ตัวแบ่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "session_id (Odoo)<br/>"
msgstr "session_id (Odoo)<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "statistics, stats, KPI"
msgstr "สถิติ, สแตท, KPI"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "testimonials"
msgstr "คำรับรอง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "text link"
msgstr "ลิงก์ข้อความ"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__theme_ir_ui_view
msgid "theme.ir.ui.view"
msgstr "theme.ir.ui.view"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "this page"
msgstr "เพจนี้"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "to exit full screen"
msgstr "เพื่อออกจากโหมดเต็มหน้าจอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "true"
msgstr "จริง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "valuation, rank"
msgstr "การประเมิน, อันดับ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_id
#, python-format
msgid "website"
msgstr "เว็บไซต์"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "with the main objective to"
msgstr "โดยมีวัตถุประสงค์หลักเพื่อ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "yes"
msgstr "ใช่"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "you do not need to ask for the consent"
msgstr "คุณไม่จำเป็นต้องขอความยินยอม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Active"
msgstr "⌙ ทำงาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Background"
msgstr "⌙ พื้นหลัง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Blur"
msgstr "⌙ เบลอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Buttons"
msgstr "⌙ ปุ่ม"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Color"
msgstr "⌙ สี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Colors"
msgstr "⌙ สี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Country"
msgstr "⌙ ประเทศ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "⌙ Delay"
msgstr "⌙ ดีเลย์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "⌙ Desktop"
msgstr "⌙ เดสก์ทอป"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Display"
msgstr "⌙ แสดง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Headings"
msgstr "⌙ หัวเรื่อง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Height"
msgstr "⌙ ความสูง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Hue"
msgstr "⌙ สี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Inactive"
msgstr "⌙ ไม่ทำงาน"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Intensity"
msgstr "⌙ ความเข้มของสี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Label"
msgstr "⌙ ป้าย"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Languages"
msgstr "⌙ ภาษา"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Large"
msgstr "⌙ ใหญ่"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "⌙ Mobile"
msgstr "⌙ มือถือ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Off-Canvas Logo"
msgstr "⌙ Off-Canvas โลโก้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Offset (X, Y)"
msgstr "⌙ Offset (X, Y)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "⌙ Page Anchor"
msgstr "⌙ เพจ Anchor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Parallax"
msgstr "⌙ Parallax"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Position"
msgstr "⌙ ตำแหน่วง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Saturation"
msgstr "⌙ ความอิ่มตัวสี"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "⌙ Separator"
msgstr "⌙ ตัวแบ่ง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Small"
msgstr "⌙ เล็ก"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Spacing"
msgstr "⌙  ระยะห่าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Spread"
msgstr "⌙  ระยะห่าง"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "⌙ Style"
msgstr "⌙ สไตล์"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Campaign"
msgstr "⌙ UTM แคมเปญ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Medium"
msgstr "⌙ UTM Medium"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Source"
msgstr "⌙ UTM Source"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Users"
msgstr "⌙ ผู้ใช้"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Video"
msgstr "⌙ วีดีโอ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Width"
msgstr "⌙ความกว้าง"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "└ Height"
msgstr ""
