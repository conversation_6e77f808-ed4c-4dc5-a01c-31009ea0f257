# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_gift_card
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Sarah Park, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Sarah Park, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "<strong>Gift Card Code</strong>"
msgstr "<strong>기프트 카드 코드</strong>"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Amount of the gift card:"
msgstr ""

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "Barcode"
msgstr "바코드"

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_barcode_rule
msgid "Barcode Rule"
msgstr "바코드 규칙"

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_order_line__generated_gift_card_ids
msgid "Bought Gift Card"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_gift_card__buy_pos_order_line_id
msgid "Buy Pos Order Line"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Cancel"
msgstr "취소"

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "Card expires"
msgstr "만료된 카드입니다"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Check a gift card"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Confirm"
msgstr "승인"

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_pos_order_line__gift_card_id
msgid "Deducted from this Gift Card"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_pos_config__gift_card_settings
msgid "Defines the way you want to set your gift cards."
msgstr "기프트 카드를 어떻게 설정할지 정의합니다."

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Discard"
msgstr "작성 취소"

#. module: pos_gift_card
#: model:ir.model.fields.selection,name:pos_gift_card.selection__pos_config__gift_card_settings__create_set
msgid "Generate a new barcode and set a price"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Generate barcode"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardButton.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: model:ir.actions.report,name:pos_gift_card.gift_card_report_pdf
#: model:ir.model,name:pos_gift_card.model_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_config__use_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_order_line__gift_card_id
#: model:ir.model.fields.selection,name:pos_gift_card.selection__barcode_rule__type__gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.pos_gift_card_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_gift_card.res_config_view_form_inherit_pos_coupon
#, python-format
msgid "Gift Card"
msgstr "기프트 카드"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Gift Card Barcode:"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_order__gift_card_count
msgid "Gift Card Count"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Gift Card Error"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_config__gift_card_product_id
#: model_terms:ir.ui.view,arch_db:pos_gift_card.pos_gift_card_config_view_form
msgid "Gift Card Product"
msgstr ""

#. module: pos_gift_card
#: model:ir.ui.menu,name:pos_gift_card.pos_gift_card_menu
msgid "Gift Cards"
msgstr "기프트 카드"

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_config__gift_card_settings
msgid "Gift Cards settings"
msgstr "기프트 카드 설정"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Gift card balance is too low."
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Gift card is not valid."
msgstr ""

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.pos_gift_card_config_view_form
msgid "Gift card settings"
msgstr ""

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "Here is your gift card!"
msgstr "고객님의 기프트 카드를 확인하세요!"

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_pos_config
msgid "Point of Sale Configuration"
msgstr "점포판매시스템 환경 설정"

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "점포판매시스템 주문 명세"

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_pos_order
msgid "Point of Sale Orders"
msgstr "점포판매시스템 주문"

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_gift_card__buy_pos_order_line_id
msgid "Pos Order line where this gift card has been bought."
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_gift_card__redeem_pos_order_line_ids
msgid "Pos Redeems"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Remaining amount of the gift card:"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields.selection,name:pos_gift_card.selection__pos_config__gift_card_settings__scan_set
msgid "Scan an existing barcode and set a price"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields.selection,name:pos_gift_card.selection__pos_config__gift_card_settings__scan_use
msgid "Scan an existing barcode with an existing price"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Scan and set price on gift card"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Scan gift card"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/GiftCardPopup.js:0
#, python-format
msgid "This gift card has already been sold"
msgstr "이미 판매 완료된 기프트 카드입니다."

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/models.js:0
#, python-format
msgid "This gift card is already applied"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_pos_config__gift_card_product_id
msgid "This product is used as reference on customer receipts."
msgstr "이 상품은 고객 영수증에 대한 참조로 사용됩니다."

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_barcode_rule__type
msgid "Type"
msgstr "유형"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Use a gift card"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/GiftCardPopup.js:0
#, python-format
msgid "You cannot sell a gift card that has already been sold"
msgstr ""
