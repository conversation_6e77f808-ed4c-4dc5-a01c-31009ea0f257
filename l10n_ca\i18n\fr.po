# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ca
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-05 15:45+0000\n"
"PO-Revision-Date: 2022-04-08 12:41+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_ab_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_ab_en
msgid "Alberta (AB)"
msgstr "Alberta (AB)"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512102_en
#: model:account.account.template,name:l10n_ca.chart512102_en
msgid "Bonus"
msgstr "Boni"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2542_en
#: model:account.account.template,name:l10n_ca.chart2542_en
msgid "Bonus Accrued"
msgstr "Boni courus"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2183_en
#: model:account.account.template,name:l10n_ca.chart2183_en
msgid "Bonus to pay"
msgstr "Boni à payer"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_bc_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_bc_en
msgid "British Columbia (BC)"
msgstr "Colombie-Britannique (BC)"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2141_en
#: model:account.account.template,name:l10n_ca.chart2141_en
msgid "CANADA REVENUE AGENCY"
msgstr "Agence du Revenu du Canada"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214131_en
#: model:account.account.template,name:l10n_ca.chart214131_en
msgid "CPP - Employees Contribution"
msgstr "Régime de pensions du Canada - Contribution des employés"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214132_en
#: model:account.account.template,name:l10n_ca.chart214132_en
msgid "CPP - Employer Contribution"
msgstr "Régime de pensions du Canada - Contribution de l'employeur"

#. module: l10n_ca
#: model:account.chart.template,name:l10n_ca.ca_en_chart_template_en
msgid "Canada - Chart of Accounts"
msgstr "Canada - Charte de comptes"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512301_en
#: model:account.account.template,name:l10n_ca.chart512301_en
msgid "Canada Pension Plan"
msgstr "Régime de pensions du Canada"

#. module: l10n_ca
#: model:ir.model,name:l10n_ca.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: l10n_ca
#: model:ir.model,name:l10n_ca.model_base_document_layout
msgid "Company Document Layout"
msgstr "Mise en page des documents de votre société"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart254102_en
#: model:account.account,name:l10n_ca.4_chart512202_en
#: model:account.account.template,name:l10n_ca.chart254102_en
#: model:account.account.template,name:l10n_ca.chart512202_en
msgid "Compensatory Days Accrued"
msgstr "Jours de compensation courus"

#. module: l10n_ca
#: model:ir.model,name:l10n_ca.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart1151_en
#: model:account.account.template,name:l10n_ca.chart1151_en
msgid "Customers Account"
msgstr "Comptes clients"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart11511_en
#: model:account.account.template,name:l10n_ca.chart11511_en
msgid "Customers Account (PoS)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214111_en
#: model:account.account.template,name:l10n_ca.chart214111_en
msgid "EI - Employees Contribution"
msgstr "Assurance Emploi - Contribution des employés"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214112_en
#: model:account.account.template,name:l10n_ca.chart214112_en
msgid "EI - Employer Contribution"
msgstr "Assurance Emploi - Contribution de l'employeur"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart218601_en
#: model:account.account.template,name:l10n_ca.chart218601_en
msgid "Employee Benefits Provision - Employees Contribution"
msgstr "Avantages sociaux - Contribution des employés"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart218602_en
#: model:account.account.template,name:l10n_ca.chart218602_en
msgid "Employee Benefits Provision - Employer Contribution"
msgstr "Avantages sociaux - Contribution de l'employeur"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512304_en
#: model:account.account.template,name:l10n_ca.chart512304_en
msgid "Employee benefits expense"
msgstr "Avantages sociaux"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512302_en
#: model:account.account.template,name:l10n_ca.chart512302_en
msgid "Employment Insurance"
msgstr "Assurance Emploi"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21412_en
#: model:account.account.template,name:l10n_ca.chart21412_en
msgid "Federal Income Tax"
msgstr "Impôt fédéral"

#. module: l10n_ca
#: model:ir.model.fields,field_description:l10n_ca.field_base_document_layout__account_fiscal_country_id
msgid "Fiscal Country"
msgstr "Pays d'imposition"

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_gstpst_bc_purc_en
#: model:account.tax,description:l10n_ca.4_gstpst_bc_sale_en
#: model:account.tax,description:l10n_ca.4_gstpst_mb_purc_en
#: model:account.tax,description:l10n_ca.4_gstpst_mb_sale_en
#: model:account.tax,description:l10n_ca.4_gstpst_sk_purc_en
#: model:account.tax,description:l10n_ca.4_gstpst_sk_sale_en
#: model:account.tax.template,description:l10n_ca.gstpst_bc_purc_en
#: model:account.tax.template,description:l10n_ca.gstpst_bc_sale_en
#: model:account.tax.template,description:l10n_ca.gstpst_mb_purc_en
#: model:account.tax.template,description:l10n_ca.gstpst_mb_sale_en
#: model:account.tax.template,description:l10n_ca.gstpst_sk_purc_en
#: model:account.tax.template,description:l10n_ca.gstpst_sk_sale_en
msgid "GST + PST"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_bc_purc_en
#: model:account.tax.template,name:l10n_ca.gstpst_bc_purc_en
msgid "GST + PST for purchases (BC)"
msgstr "TPS + TVP sur les achats (BC)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_mb_purc_en
#: model:account.tax.template,name:l10n_ca.gstpst_mb_purc_en
msgid "GST + PST for purchases (MB)"
msgstr "TPS + TVP sur les achats (MB)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_sk_purc_en
#: model:account.tax.template,name:l10n_ca.gstpst_sk_purc_en
msgid "GST + PST for purchases (SK)"
msgstr "TPS + TVP sur les achats (SK)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_bc_sale_en
#: model:account.tax.template,name:l10n_ca.gstpst_bc_sale_en
msgid "GST + PST for sales (BC)"
msgstr "TPS + TVP sur les ventes (BC)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_mb_sale_en
#: model:account.tax.template,name:l10n_ca.gstpst_mb_sale_en
msgid "GST + PST for sales (MB)"
msgstr "TPS + TVP sur les ventes (MB)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_sk_sale_en
#: model:account.tax.template,name:l10n_ca.gstpst_sk_sale_en
msgid "GST + PST for sales (SK)"
msgstr "TPS + TVP sur les ventes (SK)"

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_gstqst_purc_en
#: model:account.tax,description:l10n_ca.4_gstqst_sale_en
#: model:account.tax.template,description:l10n_ca.gstqst_purc_en
#: model:account.tax.template,description:l10n_ca.gstqst_sale_en
msgid "GST + QST"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstqst_purc_en
#: model:account.tax.template,name:l10n_ca.gstqst_purc_en
msgid "GST + QST for purchases"
msgstr "TPS + TVQ sur les achats"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstqst_sale_en
#: model:account.tax.template,name:l10n_ca.gstqst_sale_en
msgid "GST + QST for sales"
msgstr "TPS + TVQ sur les ventes"

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_gst_purc_en
#: model:account.tax,description:l10n_ca.4_gst_sale_en
#: model:account.tax,description:l10n_ca.4_gstpst_purc_bc_gst_en
#: model:account.tax,description:l10n_ca.4_gstpst_purc_mb_gst_en
#: model:account.tax,description:l10n_ca.4_gstpst_purc_sk_gst_en
#: model:account.tax,description:l10n_ca.4_gstpst_sale_bc_gst_en
#: model:account.tax,description:l10n_ca.4_gstpst_sale_mb_gst_en
#: model:account.tax,description:l10n_ca.4_gstpst_sale_sk_gst_en
#: model:account.tax,description:l10n_ca.4_gstqst_purc_gst_en
#: model:account.tax,description:l10n_ca.4_gstqst_sale_gst_en
#: model:account.tax.group,name:l10n_ca.tax_group_gst_5
#: model:account.tax.template,description:l10n_ca.gst_purc_en
#: model:account.tax.template,description:l10n_ca.gst_sale_en
#: model:account.tax.template,description:l10n_ca.gstpst_purc_bc_gst_en
#: model:account.tax.template,description:l10n_ca.gstpst_purc_mb_gst_en
#: model:account.tax.template,description:l10n_ca.gstpst_purc_sk_gst_en
#: model:account.tax.template,description:l10n_ca.gstpst_sale_bc_gst_en
#: model:account.tax.template,description:l10n_ca.gstpst_sale_mb_gst_en
#: model:account.tax.template,description:l10n_ca.gstpst_sale_sk_gst_en
#: model:account.tax.template,description:l10n_ca.gstqst_purc_gst_en
#: model:account.tax.template,description:l10n_ca.gstqst_sale_gst_en
msgid "GST 5%"
msgstr ""

#. module: l10n_ca
#: model:account.tax.group,name:l10n_ca.tax_group_gst_7
msgid "GST 7%"
msgstr ""

#. module: l10n_ca
#: model:account.tax.group,name:l10n_ca.tax_group_gst_8
msgid "GST 8%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gst_purc_en
#: model:account.tax.template,name:l10n_ca.gst_purc_en
msgid "GST for purchases - 5%"
msgstr "TPS sur les achats - 5%"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_purc_bc_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_purc_bc_gst_en
msgid "GST for purchases - 5% (BC)"
msgstr "TPS sur les achats - 5% (BC)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_purc_mb_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_purc_mb_gst_en
msgid "GST for purchases - 5% (MB)"
msgstr "TPS sur les achats - 5% (MB)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstqst_purc_gst_en
#: model:account.tax.template,name:l10n_ca.gstqst_purc_gst_en
msgid "GST for purchases - 5% (QC)"
msgstr "TPS sur les achats - 5% (QC)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_purc_sk_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_purc_sk_gst_en
msgid "GST for purchases - 5% (SK)"
msgstr "TPS sur les achats - 5% (SK)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gst_sale_en
#: model:account.tax.template,name:l10n_ca.gst_sale_en
msgid "GST for sales - 5%"
msgstr "TPS sur les ventes - 5%"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_sale_bc_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_sale_bc_gst_en
msgid "GST for sales - 5% (BC)"
msgstr "TPS sur les ventes - 5% (BC)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_sale_mb_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_sale_mb_gst_en
msgid "GST for sales - 5% (MB)"
msgstr "TPS sur les ventes - 5% (MB)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstqst_sale_gst_en
#: model:account.tax.template,name:l10n_ca.gstqst_sale_gst_en
msgid "GST for sales - 5% (QC)"
msgstr "TPS sur les ventes - 5% (QC)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_sale_sk_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_sale_sk_gst_en
msgid "GST for sales - 5% (SK)"
msgstr "TPS sur les ventes - 5% (SK)"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart1181_en
#: model:account.account.template,name:l10n_ca.chart1181_en
msgid "GST receivable"
msgstr "TPS recevable"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2131_en
#: model:account.account.template,name:l10n_ca.chart2131_en
msgid "GST to pay"
msgstr "TPS payable"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512303_en
#: model:account.account.template,name:l10n_ca.chart512303_en
msgid "Group Pension Plan"
msgstr "Régime de pension collective"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart218501_en
#: model:account.account.template,name:l10n_ca.chart218501_en
msgid "Group Pension Plan to pay - Employees Contribution"
msgstr "Régime de pension collective à payer - Contribution des employés"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart218502_en
#: model:account.account.template,name:l10n_ca.chart218502_en
msgid "Group Pension Plan to pay - Employer Contribution"
msgstr "Régime de pension collective à payer - Contribution de l'employeur"

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_hst13_purc_en
#: model:account.tax,description:l10n_ca.4_hst13_sale_en
#: model:account.tax.group,name:l10n_ca.tax_group_hst_13
#: model:account.tax.template,description:l10n_ca.hst13_purc_en
#: model:account.tax.template,description:l10n_ca.hst13_sale_en
msgid "HST 13%"
msgstr ""

#. module: l10n_ca
#: model:account.tax.group,name:l10n_ca.tax_group_hst_14
msgid "HST 14%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_hst15_purc_en
#: model:account.tax,description:l10n_ca.4_hst15_sale_en
#: model:account.tax.group,name:l10n_ca.tax_group_hst_15
#: model:account.tax.template,description:l10n_ca.hst15_purc_en
#: model:account.tax.template,description:l10n_ca.hst15_sale_en
msgid "HST 15%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_hst13_purc_en
#: model:account.tax.template,name:l10n_ca.hst13_purc_en
msgid "HST for purchases - 13%"
msgstr "TVH sur les achats - 13%"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_hst15_purc_en
#: model:account.tax.template,name:l10n_ca.hst15_purc_en
msgid "HST for purchases - 15%"
msgstr "TVH sur les achats - 15%"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_hst13_sale_en
#: model:account.tax.template,name:l10n_ca.hst13_sale_en
msgid "HST for sales - 13%"
msgstr "TVH sur les ventes - 13%"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_hst15_sale_en
#: model:account.tax.template,name:l10n_ca.hst15_sale_en
msgid "HST for sales - 15%"
msgstr "TVH sur les ventes - 15%"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart11831_en
#: model:account.account.template,name:l10n_ca.chart11831_en
msgid "HST receivable - 13%"
msgstr "TVH à recevoir - 13%"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart11832_en
#: model:account.account.template,name:l10n_ca.chart11832_en
msgid "HST receivable - 14%"
msgstr "TVH à recevoir - 14%"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart11833_en
#: model:account.account.template,name:l10n_ca.chart11833_en
msgid "HST receivable - 15%"
msgstr "TVH à recevoir - 15%"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21331_en
#: model:account.account.template,name:l10n_ca.chart21331_en
msgid "HST to pay - 13%"
msgstr "TVH à payer - 13%"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21332_en
#: model:account.account.template,name:l10n_ca.chart21332_en
msgid "HST to pay - 14%"
msgstr "TVH à payer - 14%"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21333_en
#: model:account.account.template,name:l10n_ca.chart21333_en
msgid "HST to pay - 15%"
msgstr "TVH à payer - 15%"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart412_en
#: model:account.account.template,name:l10n_ca.chart412_en
msgid "Harmonized Provinces Sales"
msgstr "Ventes dans des provinces harmonisées"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512314_en
#: model:account.account.template,name:l10n_ca.chart512314_en
msgid "Health Service Fund"
msgstr "Fond des services de santé"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21421_en
#: model:account.account.template,name:l10n_ca.chart21421_en
msgid "Health Services Fund to pay"
msgstr "Fond des services de santé à payer"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart5111_en
#: model:account.account.template,name:l10n_ca.chart5111_en
msgid "Inside Purchases"
msgstr "Achats"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart411_en
#: model:account.account.template,name:l10n_ca.chart411_en
msgid "Inside Sales"
msgstr "Ventes"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_intl_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_intl_en
msgid "International (INTL)"
msgstr "International (INTL)"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart5114_en
#: model:account.account.template,name:l10n_ca.chart5114_en
msgid "International Purchases"
msgstr "Import"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart414_en
#: model:account.account.template,name:l10n_ca.chart414_en
msgid "International Sales"
msgstr "Export"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512312_en
#: model:account.account.template,name:l10n_ca.chart512312_en
msgid "Labour Health and Safety"
msgstr "Commission de la santé et de la sécurité du travail"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21424_en
#: model:account.account.template,name:l10n_ca.chart21424_en
msgid "Labour Health and Safety to pay"
msgstr "Commission de la santé et de la sécurité du travail à payer"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512313_en
#: model:account.account.template,name:l10n_ca.chart512313_en
msgid "Labour Standards"
msgstr "Commission des normes du travail"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21425_en
#: model:account.account.template,name:l10n_ca.chart21425_en
msgid "Labour Standards to pay"
msgstr "Commission des normes du travail à payer"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_ca_en_chart_template_en_liquidity_transfer
#: model:account.account.template,name:l10n_ca.ca_en_chart_template_en_liquidity_transfer
msgid "Liquidity Transfer"
msgstr "Transfert de liquidités"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_mb_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_mb_en
msgid "Manitoba (MB)"
msgstr "Manitoba (MB)"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart55_en
#: model:account.account.template,name:l10n_ca.chart55_en
msgid "NON-OPERATING EXPENSES"
msgstr "Dépenses hors-exploitation"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart42_en
#: model:account.account.template,name:l10n_ca.chart42_en
msgid "NON-OPERATING INCOMES"
msgstr "Revenus hors-exploitation"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_nb_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_nb_en
msgid "New Brunswick (NB)"
msgstr "Nouveau Brunswick (NB)"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_nl_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_nl_en
msgid "Newfoundland and Labrador (NL)"
msgstr "Terre-Neuve et Labrador"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart413_en
#: model:account.account.template,name:l10n_ca.chart413_en
msgid "Non-Harmonized Provinces Sales"
msgstr "Ventes dans des provinces non-harmonisées"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_nt_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_nt_en
msgid "Northwest Territories (NT)"
msgstr "Territoires du Nord-Ouest"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_ns_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_ns_en
msgid "Nova Scotia (NS)"
msgstr "Nouvelle-Écosse (NS)"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_nu_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_nu_en
msgid "Nunavut (NU)"
msgstr "Nunavut (NU)"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_on_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_on_en
msgid "Ontario (ON)"
msgstr "Ontario (ON)"

#. module: l10n_ca
#: model:ir.model.fields,field_description:l10n_ca.field_base_document_layout__l10n_ca_pst
#: model:ir.model.fields,field_description:l10n_ca.field_res_company__l10n_ca_pst
#: model:ir.model.fields,field_description:l10n_ca.field_res_partner__l10n_ca_pst
#: model:ir.model.fields,field_description:l10n_ca.field_res_users__l10n_ca_pst
msgid "PST"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_pst_sk_purc_en
#: model:account.tax,description:l10n_ca.4_pst_sk_sale_en
#: model:account.tax.group,name:l10n_ca.tax_group_pst_5
#: model:account.tax.template,description:l10n_ca.pst_sk_purc_en
#: model:account.tax.template,description:l10n_ca.pst_sk_sale_en
msgid "PST 5%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_pst_bc_purc_en
#: model:account.tax,description:l10n_ca.4_pst_bc_sale_en
#: model:account.tax.template,description:l10n_ca.pst_bc_purc_en
#: model:account.tax.template,description:l10n_ca.pst_bc_sale_en
msgid "PST 7%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_pst_mb_purc_en
#: model:account.tax,description:l10n_ca.4_pst_mb_sale_en
#: model:account.tax.group,name:l10n_ca.tax_group_pst_8
#: model:account.tax.template,description:l10n_ca.pst_mb_purc_en
#: model:account.tax.template,description:l10n_ca.pst_mb_sale_en
msgid "PST 8%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_sk_purc_en
#: model:account.tax.template,name:l10n_ca.pst_sk_purc_en
msgid "PST for purchases - 5% (SK)"
msgstr "TVP sur les achats - 5% (SK)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_bc_purc_en
#: model:account.tax.template,name:l10n_ca.pst_bc_purc_en
msgid "PST for purchases - 7% (BC)"
msgstr "TVP sur les achats - 7% (BC)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_mb_purc_en
#: model:account.tax.template,name:l10n_ca.pst_mb_purc_en
msgid "PST for purchases - 8% (MB)"
msgstr "TVP sur les achats - 8% (MB)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_sk_sale_en
#: model:account.tax.template,name:l10n_ca.pst_sk_sale_en
msgid "PST for sales - 5% (SK)"
msgstr "TVP sur les ventes - 5% (SK)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_bc_sale_en
#: model:account.tax.template,name:l10n_ca.pst_bc_sale_en
msgid "PST for sales - 7% (BC)"
msgstr "TVP sur les ventes - 7% (BC)"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_mb_sale_en
#: model:account.tax.template,name:l10n_ca.pst_mb_sale_en
msgid "PST for sales - 8% (MB)"
msgstr "TVP sur les ventes - 8% (MB)"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart1182_en
#: model:account.account.template,name:l10n_ca.chart1182_en
msgid "PST/QST receivable"
msgstr "TVP/TVQ à recevoir"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2132_en
#: model:account.account.template,name:l10n_ca.chart2132_en
msgid "PST/QST to pay"
msgstr "TVP/TVQ à payer"

#. module: l10n_ca
#: model_terms:ir.ui.view,arch_db:l10n_ca.l10n_ca_external_layout_bold
#: model_terms:ir.ui.view,arch_db:l10n_ca.l10n_ca_external_layout_boxed
#: model_terms:ir.ui.view,arch_db:l10n_ca.l10n_ca_external_layout_standard
#: model_terms:ir.ui.view,arch_db:l10n_ca.l10n_ca_external_layout_striped
#: model_terms:ir.ui.view,arch_db:l10n_ca.l10n_ca_report_invoice_document_inherit
msgid "PST:"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214231_en
#: model:account.account.template,name:l10n_ca.chart214231_en
msgid "Parental Insurance Plan - Employee Contribution"
msgstr "Régime d'assurance parentale - Contribution des employés"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214232_en
#: model:account.account.template,name:l10n_ca.chart214232_en
msgid "Parental Insurance Plan - Employer Contribution"
msgstr "Régime d'assurance parentale - Contribution de l'employeur"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_pe_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_pe_en
msgid "Prince Edward Islands (PE)"
msgstr "Îles du Prince-Édouard (PE)"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21426_en
#: model:account.account.template,name:l10n_ca.chart21426_en
msgid "Provincial Income Tax"
msgstr "Impôt provincial"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512311_en
#: model:account.account.template,name:l10n_ca.chart512311_en
msgid "Provincial Parental Insurance Plan"
msgstr "Régime provincial d'assurance parentale"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512310_en
#: model:account.account.template,name:l10n_ca.chart512310_en
msgid "Provincial Pension Plan"
msgstr "Régime provincial de pension"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214221_en
#: model:account.account.template,name:l10n_ca.chart214221_en
msgid "Provincial Pension Plan - Employees Contribution"
msgstr "Régime provincial de pension - Contribution des employés"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214222_en
#: model:account.account.template,name:l10n_ca.chart214222_en
msgid "Provincial Pension Plan - Employer Contribution"
msgstr "Régime provincial de pension - Contribution de l'employeur"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2521_en
#: model:account.account.template,name:l10n_ca.chart2521_en
msgid "Provision for pension plans"
msgstr "Provision pour les régimes de pension"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart5112_en
#: model:account.account.template,name:l10n_ca.chart5112_en
msgid "Purchases in harmonized provinces"
msgstr "Achats dans des provinces harmonisées"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart5113_en
#: model:account.account.template,name:l10n_ca.chart5113_en
msgid "Purchases in non-harmonized provinces"
msgstr "Achats dans des provinces non-harmonisées"

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_qst_purc_en
#: model:account.tax,description:l10n_ca.4_qst_sale_en
#: model:account.tax.group,name:l10n_ca.tax_group_qst_9975
#: model:account.tax.template,description:l10n_ca.qst_purc_en
#: model:account.tax.template,description:l10n_ca.qst_sale_en
msgid "QST 9.975%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_qst_purc_en
#: model:account.tax.template,name:l10n_ca.qst_purc_en
msgid "QST for purchases - 9.975%"
msgstr "TVQ sur les achats - 9,975%"

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_qst_sale_en
#: model:account.tax.template,name:l10n_ca.qst_sale_en
msgid "QST for sales - 9.975%"
msgstr "TVQ sur les ventes - 9,975%"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_qc_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_qc_en
msgid "Quebec (QC)"
msgstr "Québec (QC)"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512101_en
#: model:account.account.template,name:l10n_ca.chart512101_en
msgid "Regular Salaries"
msgstr "Salaires"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512103_en
#: model:account.account.template,name:l10n_ca.chart512103_en
msgid "Retroactive Pay"
msgstr "Paie rétroactive"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2184_en
#: model:account.account.template,name:l10n_ca.chart2184_en
msgid "Retroactive Payment to pay"
msgstr "Paie rétroactive à payer"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2181_en
#: model:account.account.template,name:l10n_ca.chart2181_en
msgid "Salaries to pay"
msgstr "Salaires à payer"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_sk_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_sk_en
msgid "Saskatchewan (SK)"
msgstr "Saskatchewan (SK)"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart254103_en
#: model:account.account,name:l10n_ca.4_chart512203_en
#: model:account.account.template,name:l10n_ca.chart254103_en
#: model:account.account.template,name:l10n_ca.chart512203_en
msgid "Sick Leaves Accrued"
msgstr "Congés de maladie courus"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart1145_en
#: model:account.account.template,name:l10n_ca.chart1145_en
msgid "Stock Delivered But Not Billed"
msgstr "Marchandises expédiées non-facturées"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart1141_en
#: model:account.account.template,name:l10n_ca.chart1141_en
msgid "Stock In Hand"
msgstr "Stock disponible"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2171_en
#: model:account.account.template,name:l10n_ca.chart2171_en
msgid "Stock Received But Not Billed"
msgstr "Marchandises reçues non-facturées"

#. module: l10n_ca
#: model:account.tax.group,name:l10n_ca.tax_group_fix
msgid "Taxes"
msgstr "Taxes"

#. module: l10n_ca
#: model:ir.model.fields,help:l10n_ca.field_base_document_layout__account_fiscal_country_id
msgid "The country to use the tax reports from for this company"
msgstr "Le pays à utiliser pour les rapports de taxes de cette société"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart254101_en
#: model:account.account,name:l10n_ca.4_chart512201_en
#: model:account.account.template,name:l10n_ca.chart254101_en
#: model:account.account.template,name:l10n_ca.chart512201_en
msgid "Vacations Accrued"
msgstr "Vacances courues"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2111_en
#: model:account.account.template,name:l10n_ca.chart2111_en
msgid "Vendors Account"
msgstr "Comptes fournisseurs"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_yt_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_yt_en
msgid "Yukon (YT)"
msgstr "Yukon (YT)"
