# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_timesheet
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    تسجيل الجداول الزمنية\n"
"                </p><p>\n"
"                    يمكنك تسجيل وتتبع ساعات العمل حسب المشروع كل يوم.\n"
"                    سيتم تحويل الوقت المستغرق في إتمام مشروع إلى تكلفة ويمكن إعادة فوترته\n"
"                    للعملاء عند الحاجة.\n"
"                </p>\n"
"            "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice___candidate_orders
msgid " Candidate Orders"
msgstr "طلبات المرشح "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__billable_percentage
msgid ""
"% of timesheets that are billable compared to the total number of timesheets"
" linked to the AA of the project, rounded to the unit."
msgstr ""
"نسبة الجداول الزمنية القابلة للفوترة مقارنة بإجمالي عدد الجداول الزمنية "
"المرتبطة بـ AA المشروع، مقربة إلى الوحدة. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "%(amount)s %(label)s will be added to the new Sales Order."
msgstr "سةف تتم إضافة %(amount)s %(label)s إلى أمر البيع الجديد. "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid ", for a revenue of"
msgstr "، لإيرادات تصل إلى "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid ", leading to a"
msgstr "، مما يؤدي إلى "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for Invoice:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">الجداول الزمنية للفاتورة:</em> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid ""
"<em class=\"font-weight-normal text-muted\">Timesheets for sales order "
"item:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">الجداول الزمنية لعنصر أمر "
"المبيعات:</em> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid ""
"<em class=\"font-weight-normal text-muted\">Timesheets for sales order:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">الجداول الزمنية لأمر البيع:</em>"
" "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"سهم \"/>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Time Billing</span>"
msgstr "<span class=\"o_form_label\">فوترة الوقت</span> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                        Billable Time\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                        الوقت القابل للفوترة\n"
"                        </span> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">مسجل</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoiced:</strong>"
msgstr "<strong>مفوتر:</strong> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoices:</strong>"
msgstr "<strong>الفواتير:</strong> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Sales Order:</strong>"
msgstr "<strong>أمر المبيعات:</strong> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>To invoice:</strong>"
msgstr "<strong>بانتظار الفوترة:</strong> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Profitability</u>"
msgstr "<u>الربحية</u> "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Sold</u>"
msgstr "<u>المبيعات</u> "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"حسب تهيئة المنتج، يمكن حساب الكمية التي تم توصيلها تلقائياً بإحدى الطرق التالية:\n"
"  - يدوياً: تُحسب الكمية يدوياً في البند\n"
"  - تحليلياً من النفقات: تكون الكمية هي مجموع الكمية من النفقات المُرحلة\n"
"  - الجداول الزمنية: تكون الكمية هي مجموع الساعات المستغرقة لإجراء المهام المرتبطة ببند المبيعات\n"
"  - حركات المخزون: تأتي الكمية من عمليات الانتقاء المؤكدة\n"

#. module: sale_timesheet
#: model:project.project,name:sale_timesheet.project_support
msgid "After-Sales Services"
msgstr "خدمات ما بعد المبيعات "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_profitability_report__other_revenues
msgid ""
"All revenues that are not from timesheets and that are linked to the "
"analytic account of the project."
msgstr ""
"كافة الإيرادات التي لم يكن مصدرها الجداول الزمنية والمرتبطة بالحساب التحليلي"
" للمشروع. "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__amount_untaxed_invoiced
msgid "Amount Invoiced"
msgstr "المبلغ المفوتر "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_amount_untaxed_invoiced
msgid "Amount Re-invoiced"
msgstr "المبلغ تمت إعادة فوترته "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__amount_untaxed_to_invoice
msgid "Amount to Invoice"
msgstr "المبلغ بانتظار الفوترة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_amount_untaxed_to_invoice
msgid "Amount to Re-invoice"
msgstr "المبلغ بانتظار الفوترة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr "المبلغ بانتظار الفوترة "

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_create_sale_order_line_unique_employee_per_wizard
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_sale_line_employee_map_uniqueness_employee
msgid ""
"An employee cannot be selected more than once in the mapping. Please remove "
"duplicate(s) and try again."
msgstr ""
"لا يمكن تحديد الموظف أكثر من مرة في التخطيط. يرجى إزالة النسخة (النسخ) ثم "
"حاول مجدداً. "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__analytic_account_id
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "البند التحليلي"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "البنود التحليلية"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "At least one line should be filled."
msgstr "يجب ملء بند واحد على الأقل."

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__delivered_manual
msgid "Based on Milestones"
msgstr "بناءً على مؤشرات التقدم "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__delivered_timesheet
msgid "Based on Timesheets"
msgstr "بناءً على الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__allow_billable
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__allow_billable
msgid "Billable"
msgstr "قابل للفوترة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billable_percentage
msgid "Billable Percentage"
msgstr "النسبة القابلة للفوترة "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Billable Time"
msgstr "الوقت القابل للفوترة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billable Type"
msgstr "النوع القابل للفوترة "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed at a Fixed Price"
msgstr "مفوترة بسعر ثابت "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_fixed
msgid "Billed at a Fixed price"
msgstr "مفوترة بسعر ثابت "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_time
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Timesheets"
msgstr "مفوتر في الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr "الفوترة "

#. module: sale_timesheet
#: model:project.task,legend_blocked:sale_timesheet.project_task_1
#: model:project.task,legend_blocked:sale_timesheet.project_task_2
#: model:project.task,legend_blocked:sale_timesheet.project_task_3
#: model:project.task,legend_blocked:sale_timesheet.project_task_internal
msgid "Blocked"
msgstr "محجوب"

#. module: sale_timesheet
#: model:project.task,legend_done:sale_timesheet.project_task_4
msgid "Buzz or set as done"
msgstr "تنبيه أو تعين كمهمة منتهية"

#. module: sale_timesheet
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Type"
msgstr "حسب نوع الفوترة "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Cancel"
msgstr "إلغاء "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr "اختر أمر المبيعات الذي ترغب في فوترته "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__commercial_partner_id
msgid "Commercial Entity"
msgstr "الكيان التجاري"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__commercial_partner_id
msgid "Commercial Partner"
msgstr "الشريك التجاري "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__company_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__company_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Company"
msgstr "الشركة "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Configure your services"
msgstr "قم بتهيئة خدماتك "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost
msgid "Cost"
msgstr "التكلفة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost_currency_id
msgid "Cost Currency"
msgstr "عملة التكلفة "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Costs"
msgstr "التكاليف"

#. module: sale_timesheet
#: model:ir.filters,name:sale_timesheet.ir_filter_project_profitability_report_costs_and_revenues
msgid "Costs and Revenues"
msgstr "التكاليف والإيرادات"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Invoice"
msgstr "إنشاء فاتورة"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr "إنشاء فاتورة من المشروع"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order_line
msgid "Create SO Line from project"
msgstr "إنشاء بند أمر بيع من المشروع"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order
msgid "Create SO from project"
msgstr "إنشاء أمر بيع من المشروع"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/models/project.py:0
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
#, python-format
msgid "Create Sales Order"
msgstr "إنشاء أمر مبيعات"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr "إنشاء أمر مبيعات من المشروع"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Create a Sales Order"
msgstr "إنشاء أمر مبيعات"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__currency_id
msgid "Currency"
msgstr "العملة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__partner_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Customer"
msgstr "العميل"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_order_timesheet
#: model:product.template,name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Customer Care (Prepaid Hours)"
msgstr "خدمة العملاء (الساعات المدفوعة مسبقاً) "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "Customer Ratings"
msgstr "تقييمات العميل"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__partner_id
msgid "Customer of the sales order"
msgstr "عميل أمر المبيعات"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__line_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Date"
msgstr "التاريخ"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Ordered,"
msgstr "الأيام المطلوبة، "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Remaining)"
msgstr "أيام متبقية) "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Default Sales Order Item"
msgstr "عنصر أمر البيع الافتراضي "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Default Service"
msgstr "الخدمة الافتراضية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__display_create_order
msgid "Display Create Order"
msgstr "عرض إنشاء الطلب "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#, python-format
msgid "Effective"
msgstr "الفعلي"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__employee_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr "الموظف"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__employee_rate
msgid "Employee rate"
msgstr "معدل الموظف "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__employee_id
msgid "Employee that has timesheets on the project."
msgstr "الموظف الذي له جداول زمنية في المشروع. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_employee_ids
msgid ""
"Employee/Sale Order Item Mapping:\n"
" Defines to which sales order item an employee's timesheet entry will be linked.By extension, it defines the rate at which an employee's time on the project is billed."
msgstr ""
"تخطيط عنصر أمر البيع/الموظف:\n"
" يحدد أي عناصر أمر البيع سيتم ربط قيد الجداول الزمنية للموظف بها. إضافة إلى ذلك، فإنه يحدد المعدل الذي تتم فوترة وقت الموظف في المشروع على أساسه. "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Fixed price services"
msgstr "الخدمات ثابتة السعر "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Group By"
msgstr "التجميع حسب "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__has_displayed_warning_upsell
msgid "Has Displayed Warning Upsell"
msgstr "يحتوي على عرض تحذير الارتقاء بالصفقة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__has_multi_sol
msgid "Has Multi Sol"
msgstr "يحتوي على عدة بنود أمر بيع "

#. module: sale_timesheet
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_timesheet_1
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_timesheet_2
#: model:product.product,uom_name:sale_timesheet.product_service_order_timesheet
#: model:product.product,uom_name:sale_timesheet.time_product
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_timesheet_1_product_template
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_timesheet_2_product_template
#: model:product.template,uom_name:sale_timesheet.product_service_order_timesheet_product_template
#: model:product.template,uom_name:sale_timesheet.time_product_product_template
msgid "Hours"
msgstr "الساعات"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
msgid "ID"
msgstr "المُعرف"

#. module: sale_timesheet
#: model:project.task,legend_normal:sale_timesheet.project_task_1
#: model:project.task,legend_normal:sale_timesheet.project_task_2
#: model:project.task,legend_normal:sale_timesheet.project_task_3
#: model:project.task,legend_normal:sale_timesheet.project_task_4
#: model:project.task,legend_normal:sale_timesheet.project_task_internal
msgid "In Progress"
msgstr "قيد التنفيذ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__info_invoice
msgid "Info Invoice"
msgstr "فاتورة العلومات "

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
#, python-format
msgid "Invoice"
msgstr "فاتورة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Invoice Policy"
msgstr "سياسة الفاتورة "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity) on projects or tasks you'll"
" create later on."
msgstr ""
"قم بالفوترة بناءً على الجداول الزمنية (الكميات التي قد تم توصيلها) في "
"المشاريع أو المهام التي ستقوم بإنشائها لاحقاً. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create a project for "
"the order with a task for each sales order line to track the time spent."
msgstr ""
"قم بالفوترة بناءً على الجداول الزمنية (الكمية التي قد تم إيصالها)، ثم أنشئ "
"مشروعاً للأمر مع مهمة لكل بند أمر بيع لتتبع الوقت المقضي. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create a task in an "
"existing project to track the time spent."
msgstr ""
"قم بالفوترة بناءً على الجداول الزمنية (الكمية التي قد تم إيصالها)، ثم أنشئ "
"مهمة في مشروع موجود بالفعل لتتبع الوقت المقضي. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create an empty "
"project for the order to track the time spent."
msgstr ""
"قم بالفوترة بناءً على الجداول الزمنية (الكمية التي قد تم إيصالها)، ثم أنشئ "
"مشروعاً فارغاً للأمر لتتبع الوقت المقضي. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr "الفاتورة المنشأة من الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__allow_billable
#: model:ir.model.fields,help:sale_timesheet.field_project_task__allow_billable
msgid "Invoice your time and material from tasks."
msgstr "قم بفوترة وقتك والمواد من المهام. "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoice your time and material to customers"
msgstr "قم بفوترة وقتك وموادك إلى عملائك "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Invoices"
msgstr "الفواتير"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr "الفوترة"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Invoicing Policy"
msgstr "سياسة الفوترة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__invoicing_timesheet_enabled
msgid "Invoicing Timesheet Enabled"
msgstr "تم تمكين فوترة الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__is_cost_changed
msgid "Is Cost Manually Changed"
msgstr "تم تغيير التكلفة يدوياً "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr "خريطة المشروع فارغة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__is_so_line_edited
msgid "Is Sales Order Item Manually Edited"
msgstr "تم تحرير عنصر أمر البيع يدوياً "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_timesheet_2
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_2_product_template
msgid "Junior Architect (Invoice on Timesheets)"
msgstr "مهندس معماري مبتدئ (الفوترة على الجداول الزمنية) "

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_manual
#: model:product.template,name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Kitchen Assembly (Milestones)"
msgstr "تركيب المطبخ (مؤشرات التقدم) "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__line_ids
msgid "Lines"
msgstr "البنود"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"تعيين الكمية يدوياً في الطلب: يتم احتساب الفاتورة حسب الكمية المُدخلة يدوياً، دون إنشاء حساب تحليلي.\n"
"الجداول الزمنية على العقود: تُحتسب الفاتورة حسب الساعات المتتبعة في الجداول الزمنية ذات الصلة.\n"
"إنشاء مهمة وتتبع الساعات: إنشاء مهمة عند تصديق أمر البيع وتتبع ساعات العمل. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__margin
#, python-format
msgid "Margin"
msgstr "الهامش"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "طريقة تحديث الكمية التي قد تم توصيلها "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Milestone services"
msgstr "خدمات مؤشرات الأداء "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "My Projects"
msgstr "مشاريعي "

#. module: sale_timesheet
#: model:project.task,legend_blocked:sale_timesheet.project_task_4
msgid "Need functional or technical help"
msgstr "تحتاج إلى مساعدة فنية أو تقنية "

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid "No activities found"
msgstr "لم يتم العثور على أي أنشطة "

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non Billable"
msgstr "غير قابلة للفوترة "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable
msgid "Non Billable Tasks"
msgstr "المهام غير القابلة للفوترة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_count
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_count
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_count
msgid "Number of timesheets"
msgstr "عدد الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"Only timesheets not yet invoiced (and validated, if applicable) from this "
"period will be invoiced. If the period is not indicated, all timesheets not "
"yet invoiced (and validated, if applicable) will be invoiced without "
"distinction."
msgstr ""
"وحدها الجداول الزمنية التي لم تتم فوترتها بعد (وتصديقها، إذا انطبق الأمر) من"
" هذه الفترة ستتم فوترتها. إذا لم تتم الإشارة إلى الفترة، ستتم فوترة كافة "
"الجداول الزمنية التي لم تتم فوترتها بعد (وتصديقها، إذا انطبق الأمر) دون "
"تمييز. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Operation not supported"
msgstr "العملية غير مدعومة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__order_id
msgid "Order Reference"
msgstr "مرجع الطلب "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Ordered,"
msgstr "تم طلبه "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_cost
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_costs
msgid "Other Costs"
msgstr "التكاليف الأخرى "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__other_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_revenues
msgid "Other Revenues"
msgstr "الإيرادات الأخرى "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_upsell_threshold
msgid ""
"Percentage of time delivered compared to the prepaid amount that must be "
"reached for the upselling opportunity activity to be triggered."
msgstr ""
"نسبة الوقت المقضي مقارنة بالكمية المدفوعة مسبقاً التي يجب الوصول إليها حتى "
"يتم تشغيل نشاط فرصة الارتقاء بالصفقة. "

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#, python-format
msgid "Planned"
msgstr "المخطط له "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__ordered_timesheet
msgid "Prepaid/Fixed Price"
msgstr "مدفوع مسبقاً/سعر ثابت "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__pricing_type
msgid "Pricing"
msgstr "التسعير"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_product
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__product_id
msgid "Product"
msgstr "المنتج"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product Template"
msgstr "قالب المنتج"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__product_id
msgid ""
"Product of the sales order item. Must be a service invoiced based on "
"timesheets on tasks."
msgstr ""
"منتج عنصر أمر المبيعات. يجب أن يكون خدمة مفوترة حسب الجداول الزمنية المسجلة "
"في المهام. "

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#, python-format
msgid "Profitability"
msgstr "الربحية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_graph
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Profitability Analysis"
msgstr "تحليل الربحية "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__project_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Project"
msgstr "المشروع"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__company_id
msgid "Project Company"
msgstr "شركة المشروع "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__currency_id
msgid "Project Currency"
msgstr "عملة المشروع"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__user_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Project Manager"
msgstr "مدير المشروع"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_profitability_report
msgid "Project Profitability Report"
msgstr "تقرير ربحية المشروع"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "بند مبيعات المشروع، تخطيط الموظف "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr "قالب المشروع"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_update
msgid "Project Update"
msgstr "تحديث المشروع "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__project_id
msgid "Project for which we are creating a sales order"
msgstr "المشروع الذي ننشئ له أمر مبيعات"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__fixed_rate
msgid "Project rate"
msgstr "معدل المشروع "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr "المشروع لجعله قابلاً للفوترة "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Rating"
msgstr "التقييم"

#. module: sale_timesheet
#: model:project.task,legend_done:sale_timesheet.project_task_1
#: model:project.task,legend_done:sale_timesheet.project_task_2
#: model:project.task,legend_done:sale_timesheet.project_task_3
#: model:project.task,legend_done:sale_timesheet.project_task_internal
msgid "Ready"
msgstr "جاهز"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#, python-format
msgid "Remaining"
msgstr "المتبقي"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
msgid "Remaining Days on SO"
msgstr "الأيام المتبقية في أمر البيع  "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "Remaining Days on SO:"
msgstr "الأيام المتبقية في أمر البيع: "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_available
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours_available
msgid "Remaining Hours Available"
msgstr "الساعات المتبيقية المتاحة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
msgid "Remaining Hours on SO"
msgstr "الساعات المتبقية في أمر البيع "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "Remaining Hours on SO:"
msgstr "الساعات المتبقية في أمر البيع: "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Remaining)"
msgstr "متبقية) "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Revenues"
msgstr "الإيرادات "

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid ""
"Review your timesheets by billing type and make sure your time is billable."
msgstr ""
"قم بمراجعة جداولك الزمنية حسب نوع الفوترة وتأكد من أن وقتك قابل للفوترة. "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__sale_order_id
msgid "Sale Order"
msgstr "أمر البيع"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__so_analytic_account_id
msgid "Sale Order Analytic Account"
msgstr "الحساب التحليلي لأمر البيع "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
msgid "Sale Order Item"
msgstr "عنصر أمر البيع"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__sale_line_id
msgid "Sale Order Line"
msgstr "بند أمر البيع"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__sale_line_employee_ids
msgid "Sale line/Employee map"
msgstr "بند البيع/خريطة الموظف"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "فاتورة الدفعة المقدمة للمبيعات"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
#, python-format
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__order_confirmation_date
msgid "Sales Order Confirmation Date"
msgstr "تاريخ تأكيد أمر المبيعات"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
#, python-format
msgid "Sales Order Item"
msgstr "عنصر أمر المبيعات"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity."
msgstr ""
"تحدد بنود أمر البيع مؤشرات تقدم المشروع التي يجب فوترتها عن طريق تعيين "
"الكمية التي قد تم إيصالها. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity. Create a project for the order with a task for each "
"sales order line to track the time spent."
msgstr ""
"تحدد بنود أمر البيع مؤشرات تقدم مشروع ما لفوترته عن طريق تعيين الكميات التي "
"قد تم توصيلها. قم بإنشاء مشروع للأمر مع مهمة لكل بند أمر بيع لتتبع الوقت "
"المقضي. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity. Create a task in an existing project to track the time "
"spent."
msgstr ""
"تحدد بنود أمر البيع مؤشرات تقدم مشروع ما لفوترته عن طريق تعيين الكميات التي "
"قد تم توصيلها. قم بإنشاء مهمة في مشروع موجود بالفعل لتتبع الوقت المقضي. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity. Create an empty project for the order to track the time"
" spent."
msgstr ""
"تحدد بنود أمر البيع مؤشرات تقدم مشروع ما لفوترته عن طريق تعيين الكميات التي "
"قد تم توصيلها. قم بإنشاء مشروع فارغ للأمر لتتبع الوقت المقضي. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "أمر البيع الذي ترتبط به هذه المهمة. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "البحث في الفاتورة"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "البحث في أمر البيع "

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order Item"
msgstr "البحث في عنصر أمر البيع "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_task__timesheet_product_id
msgid ""
"Select a Service product with which you would like to bill your time spent "
"on tasks."
msgstr "قم بتحديد منتج خدمة ترغب في استخدامه لفوترة الوقت المقضي على المهام. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,help:sale_timesheet.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr ""
"قم بتحديد مشروع قابل للفوترة يمكن إنشاء المهام فيه. يجب تعيين هذه الإعدادات "
"لكل شركة. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,help:sale_timesheet.field_product_template__project_template_id
msgid ""
"Select a billable project to be the skeleton of the new created project when"
" selling the current product. Its stages and tasks will be duplicated."
msgstr ""
"قم بتحديد مشروع قابل للفوترة ليكون هيكل المشروع الجديد الذي تم إنشاؤه عند "
"بيع المنتج الحالي. سوف يتم استنساخ مراحله ومهامه. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr "قم بتحديد مشروع غير قابل للفوترة يمكن إنشاء المهام عليه. "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr "قم ببيع الخدمات وفوترة الوقت المقضي "

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_timesheet_1
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_1_product_template
msgid "Senior Architect (Invoice on Timesheets)"
msgstr "مهندس معماري كبير (الفوترة على الجداول الزمنية) "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__product_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Service"
msgstr "الخدمة"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr "سياسة فوترة الخدمة"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__service_revenues
msgid "Service Revenues"
msgstr "إيرادات الخدمة "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold_ratio
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold_ratio
msgid "Service Upsell Threshold Ratio"
msgstr "نسبة الحد الأدنى للارتقاء بالصفقة للخدمة "

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.time_product
#: model:product.template,name:sale_timesheet.time_product_product_template
msgid "Service on Timesheet"
msgstr "الخدمة في الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_default_services
msgid "Services"
msgstr "الخدمات"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#, python-format
msgid "Sold"
msgstr "مبيعات"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
msgid "Start Date"
msgstr "تاريخ البدء "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Amount Invoiced"
msgstr "إجمالي المبلغ المفوتر "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Amount to Invoice"
msgstr "إجمالي المبلغ بانتظار الفوترة "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Margin"
msgstr "إجمالي الهامش "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Timesheet Cost"
msgstr "إجمالي تكلفة الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__task_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Task"
msgstr "المهمة"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "تكرار المهمة "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__task_rate
msgid "Task rate"
msgstr "سعر المهمة "

#. module: sale_timesheet
#: model:project.project,label_tasks:sale_timesheet.project_support
msgid "Tasks"
msgstr "المهام"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"The %s product is required by the Timesheets app and cannot be archived nor "
"deleted."
msgstr "يتطلب تطبيق الجداول الزمنية وجود المنتج %s ولا يمكن فوترته أو حذفه. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid ""
"The Sales Order cannot be created because you did not enter some employees that entered timesheets on this project. Please list all the relevant employees before creating the Sales Order.\n"
"Missing employee(s): %s"
msgstr ""
"تعذّر إنشاء أمر المبيعات لأنك لم تُدرج بعض الموظفين الذين شاركوا بالجداول الزمنية في هذا المشروع. يرجى إدراج كافة الموظفين المرتبطين قبل إنشاء أمر المبيعات.\n"
"الموظف(ين) المفقودين: %s "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__so_analytic_account_id
msgid "The analytic account related to a sales order."
msgstr "الحساب التحليلي المتعلق بأمر مبيعات."

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "The cost of the project is now at"
msgstr "تكلفة المشروع الآن "

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "The project has already a sale order."
msgstr "لدى المشروع أمر بيع بالفعل "

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "The project is already linked to a sales order item."
msgstr "المشروع مرتبط بالفعل بعنصر أمر مبيعات."

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid ""
"The sales order cannot be created because some timesheets of this project "
"are already linked to another sales order."
msgstr ""
"لا يمكن إنشاء أمر المبيعات لأن بعض الجداول الزمنية لهذا المشروع مرتبطة "
"بالفعل بأمر مبيعات آخر. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
#, python-format
msgid "The selected Sales Order should contain something to invoice."
msgstr "ينبغي أن يحتوي أمر المبيعات المحدد على بنود لفوترتها."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,help:sale_timesheet.field_project_task__pricing_type
msgid ""
"The task rate is perfect if you would like to bill different services to "
"different customers at different rates. The fixed rate is perfect if you "
"bill a service at a fixed rate per hour or day worked regardless of the "
"employee who performed it. The employee rate is preferable if your employees"
" deliver the same service at a different rate. For instance, junior and "
"senior consultants would deliver the same service (= consultancy), but at a "
"different rate because of their level of seniority."
msgstr ""
"خاصية سعر المهمة مثالية عندما ترغب في فوترة عدة خدمات لعدة عملاء بأسعار "
"مختلفة. خاصية السعر الثابت مثالية إذا كنت تقوم بفوترة الخدمة بسعر ثابت حسب "
"الساعات أو الأيام المقضية بغض النظر عن الموظف الذي قام بالخدمة. يفضل استخدام"
" سعر الموظف إذا كان موظفوك يؤدون نفس الخدمة بأسعار مختلفة. على سبيل المثال، "
"الاستشاريين المبتدئين وذوي الخبرة يقدمون ذات الخدمة (=استشارة) بأسعار مختلفة"
" بسبب مدى أقدميتهم. "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_sale_line_employee_map__cost
msgid ""
"This cost overrides the employee's default timesheet cost in employee's HR "
"Settings"
msgstr ""
"تتخطى هذه التكلفة تكلفة الجداول الزمنية الافتراضية للموظف في إعدادات الموارد"
" البشرية للموظف "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_encode_uom_id
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_encode_uom_id
#: model:ir.model.fields,help:sale_timesheet.field_account_payment__timesheet_encode_uom_id
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""
"سيقوم هذا بتعيين وحدة القياس المستخدمة لترميز الجداول الزمنية. سيوفر هذا أدوات\n"
"        للمساعدة في عملية الترميز. لكن ستظل التقارير مُعبر عنها بالساعات (القيمة الافتراضية). "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold
msgid "Threshold"
msgstr "الحد الأدنى "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Time-based services"
msgstr "الخدمات المبنية على الوقت "

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_pivot_revenue
msgid "Timesheet"
msgstr "الجدول الزمني "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__timesheet_cost
msgid "Timesheet Cost"
msgstr "تكلفة الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheet Costs"
msgstr "تكاليف الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__timesheet_unit_amount
msgid "Timesheet Duration"
msgstr "مدة الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "وحدة ترميز الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__timesheet_product_id
msgid "Timesheet Product"
msgstr "منتج الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__timesheet_revenues
msgid "Timesheet Revenues"
msgstr "إيرادات الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_total_duration
msgid "Timesheet Total Duration"
msgstr "إجمالي مدة الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_count
msgid "Timesheet activities"
msgstr "أنشطة الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_ids
msgid "Timesheet activities associated to this sale"
msgstr "أنشطة الجداول الزمنية المرتبطة بعملية البيع هذه "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account_move.py:0
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_ids
#: model:ir.model.fields.selection,name:sale_timesheet.selection__sale_order_line__qty_delivered_method__timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
#, python-format
msgid "Timesheets"
msgstr "الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid "Timesheets Period"
msgstr "فترة الجداول الزمنية "

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
msgid "Timesheets by Billing Type"
msgstr "الجداول الزمنية حسب نوع الفوترة "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Timesheets of %s"
msgstr "الجداول الزمنية لـ %s "

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_type__timesheet
msgid "Timesheets on project (one fare per SO/Project)"
msgstr "الجداول الزمنية للمشروع (أجرة واحدة لكل أمر بيع/مشروع) "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr "الجداول الزمنية التي يتم اعتبارها عند فوترة وقتك "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Timesheets taken when invoicing time spent"
msgstr "الجداول الزمنية التي يتم اعتبارها عند فوترة الوقت المقضي "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Total"
msgstr "الإجمالي"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#, python-format
msgid "Total Sold"
msgstr "إجمالي المبيعات "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid ""
"Total amount to invoice on the sales order, including all items (services, "
"storables, expenses, ...)"
msgstr ""
"إجمالي المبلغ المراد فوترته في أمر المبيعات، شامل كافة العناصر (الخدمات، "
"والمنتجات المخزنة، والنفقات،...) "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_payment__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_total_duration
msgid ""
"Total recorded duration, expressed in the encoding UoM, and rounded to the "
"unit"
msgstr ""
"إجمالي المدة المسجلة، معبر عنها بوحدة قياس الترميز، ومقربة إلى الوحدة "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Total:"
msgstr "الإجمالي:"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr "تتبع الخدمة"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr "تتبع ساعات عملك في كل مشروع كل يوم وقم بفوترة هذا الوقت لعملائك. "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__price_unit
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "سعر الوحدة"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__price_unit
msgid "Unit price of the sales order item."
msgstr "سعر وحدة عنصر أمر المبيعات."

#. module: sale_timesheet
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_manual
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Units"
msgstr "الوحدات"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Value does not exist in the pricing type"
msgstr "القيمة غير موجودة في نوع التسعير "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_order_portal_content_inherit
msgid "View Timesheets"
msgstr "عرض الجداول الزمنية "

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Warn the salesperson for an upsell when work done exceeds"
msgstr "قم بتحذير مندوب المبيعات للارتقاء بالصفقة عندما يتخطى العمل المنجز "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__warning_employee_rate
msgid "Warning Employee Rate"
msgstr "تحذير سعر الموظف "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__wizard_id
msgid "Wizard"
msgstr "المعالج"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "You can only apply this action from a project."
msgstr "لا يمكن تطبيق هذا الإجراء إلا من مشروع."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"You cannot link a billable project to a sales order item that comes from an "
"expense or a vendor bill."
msgstr ""
"لا يمكنك ربط مشروع قابل للفوترة بعنصر أمر بيع  يأتي من نفقة أو فاتورة مورّد."
" "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"You cannot link a billable project to a sales order item that is not a "
"service."
msgstr "لا يمكنك ربط مشروع قابل للفوترة بعنصر أمر بيع  ليس خدمة. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account.py:0
#, python-format
msgid "You cannot modify timesheets that are already invoiced."
msgstr "لا يمكنك تعديل جداول زمنية قد تمت فوترتها بالفعل. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account.py:0
#, python-format
msgid "You cannot remove a timesheet that has already been invoiced."
msgstr "لا يمكنك إزالة جداول زمنية قد تمت فوترتها بالفعل. "

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:0
#, python-format
msgid "day"
msgstr "يوم"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:0
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "days"
msgstr "أيام "

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "hours"
msgstr "ساعات"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "margin ("
msgstr "هامش ("

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "of hours sold. ("
msgstr "ساعات مباعة. ("
