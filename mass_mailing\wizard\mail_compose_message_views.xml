<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <!-- Add mass mail campaign to the mail.compose.message form view -->
        <record model="ir.ui.view" id="email_compose_form_mass_mailing">
            <field name="name">mail.compose.message.form.mass_mailing</field>
            <field name="model">mail.compose.message</field>
            <field name="inherit_id" ref="mail.email_compose_message_wizard_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='notify']" position="after">
                    <field name="campaign_id" groups="mass_mailing.group_mass_mailing_campaign"
                        attrs="{'invisible': [('composition_mode', '!=', 'mass_mail')]}"/>
                    <field name="mass_mailing_name"
                        attrs="{'invisible': [('composition_mode', '!=', 'mass_mail')]}"/>
                </xpath>
            </field>
        </record>

</odoo>
