<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_do" model="res.partner">
        <field name="name">DO Company</field>
        <field name="vat">*********</field>
        <field name="street">A</field>
        <field name="city">Andrés</field>
        <field name="country_id" ref="base.do"/>
        <field name="state_id" ref="base.state_DO_32"/>
        <field name="zip">15700</field>
        <field name="phone">******-234-5678</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.doexample.com</field>
    </record>

    <record id="demo_company_do" model="res.company">
        <field name="name">DO Company</field>
        <field name="partner_id" ref="partner_demo_company_do"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_do')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_do.demo_company_do'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_do.do_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_do.demo_company_do')"/>
    </function>
</odoo>
