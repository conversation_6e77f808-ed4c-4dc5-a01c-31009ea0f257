Section "Device"
    Identifier  "fbdev0"
    Driver      "fbdev"
    Option      "fbdev" "/dev/fb0"
EndSection

Section "Device"
    Identifier  "fbdev1"
    Driver      "fbdev"
    Option      "fbdev" "/dev/fb1"
EndSection

# Dummy device to use Xlib in KeyboardUSBDriver when no monitor is connected
Section "Device"
    Identifier  "dummy"
    Driver      "dummy"
EndSection

Section "Monitor"
    Identifier  "Monitor0"
EndSection

Section "Monitor"
    Identifier  "Monitor1"
EndSection

Section "Monitor"
    Identifier  "DummyMonitor"
EndSection

Section "Screen"
    Identifier  "Screen0"
    Monitor     "Monitor0"
    Device      "fbdev0"
    Subsection  "Display"
    EndSubSection
EndSection

Section "Screen"
    Identifier  "Screen1"
    Monitor     "Monitor1"
    Device      "fbdev1"
    Subsection  "Display"
    EndSubSection
EndSection

Section "Screen"
    Identifier  "DummyScreen"
    Monitor     "DummyMonitor"
    Device      "dummy"
    Subsection  "Display"
    EndSubSection
EndSection

Section "ServerLayout"
    Identifier  "Multihead"
    Screen 0    "Screen0"
    Screen 1    "Screen1" rightof "Screen0"
    Screen 2    "DummyScreen" rightof "Screen1"
EndSection
