# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_management
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:27+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_management
#: model:sale.order.template.line,name:sale_management.sale_order_template_line_1
msgid "4 Person Desk"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
msgid "<span>Options</span>"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__active
msgid "Active"
msgstr "Σε Ισχύ"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a note"
msgstr "Προσθήκη σημείωσης"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a product"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a section"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Add one"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Add to cart"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Add to order lines"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_digest_digest__kpi_all_sale_total
msgid "All Sales"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_search
msgid "Archived"
msgstr "Αρχειοθετημένα"

#. module: sale_management
#: model:ir.ui.menu,name:sale_management.menu_product_attribute_action
msgid "Attributes"
msgstr "Ιδιότητες"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__product_uom_category_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_category_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__product_uom_category_id
msgid "Category"
msgstr "Κατηγορία"

#. module: sale_management
#: model:ir.model,name:sale_management.model_res_company
msgid "Companies"
msgstr "Εταιρίες"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__company_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__company_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__company_id
msgid "Company"
msgstr "Εταιρία"

#. module: sale_management
#: model:ir.model,name:sale_management.model_res_config_settings
msgid "Config Settings"
msgstr "Ρυθμίσεις διαμόρφωσης"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Confirmation"
msgstr "Επιβεβαίωση"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__mail_template_id
msgid "Confirmation Mail"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__product_uom_category_id
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__product_uom_category_id
#: model:ir.model.fields,help:sale_management.field_sale_order_template_option__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Μετατροπή ανάμεσα σε Μονάδες Μέτρησης μπορεί να πραγματοποιηθεί μόνο αν "
"ανήκουν στην ίδια κατηγορία. Η μετατροπή θα γίνει βάσει των αναλογιών."

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Create standardized offers with default products"
msgstr ""

#. module: sale_management
#: model_terms:ir.actions.act_window,help:sale_management.sale_order_template_action
msgid "Create your quotation template"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_company__sale_order_template_id
msgid "Default Sale Template"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__company_so_template_id
msgid "Default Template"
msgstr "Προεπιλεγμένο Πρότυπο"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__name
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Description"
msgstr "Περιγραφή"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid ""
"Design your quotation templates using building blocks<br/>\n"
"                            <em attrs=\"{'invisible': [('module_sale_quotation_builder','=',False)]}\">Warning: this option will install the Website app.</em>"
msgstr ""

#. module: sale_management
#: model:ir.model,name:sale_management.model_digest_digest
msgid "Digest"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Disc.%"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__discount
msgid "Discount (%)"
msgstr "Έκπτωση (%)"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__display_type
msgid "Display Type"
msgstr "Τύπος Οθόνης"

#. module: sale_management
#: code:addons/sale_management/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Documentation"
msgstr "Τεκμηρίωση"

#. module: sale_management
#: model:ir.model.constraint,message:sale_management.constraint_sale_order_template_line_non_accountable_fields_null
msgid ""
"Forbidden product, unit price, quantity, and UoM on non-accountable sale "
"quote line"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__sequence
msgid "Gives the sequence order when displaying a list of optional products."
msgstr ""

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__sequence
msgid "Gives the sequence order when displaying a list of sale quote lines."
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__id
msgid "ID"
msgstr "Κωδικός"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__active
msgid ""
"If unchecked, it will allow you to hide the quotation template without "
"removing it."
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_digest_digest__kpi_all_sale_total_value
msgid "Kpi All Sale Total Value"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__line_id
msgid "Line"
msgstr "Γραμμή"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__sale_order_template_line_ids
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Lines"
msgstr "Γραμμές"

#. module: sale_management
#: model:ir.model.constraint,message:sale_management.constraint_sale_order_template_line_accountable_product_id_required
msgid "Missing required product and UoM on accountable sale quote line."
msgstr ""

#. module: sale_management
#: model:ir.model.fields.selection,name:sale_management.selection__sale_order_template_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Note"
msgstr "Σημείωση"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__number_of_days
msgid "Number of days for the validity date computation of the quotation"
msgstr ""

#. module: sale_management
#: model:sale.order.template.option,name:sale_management.sale_order_template_option_1
msgid "Office Chair"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__require_payment
msgid "Online Payment"
msgstr "Ηλεκτρονική Πληρωμή"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__require_signature
msgid "Online Signature"
msgstr "Ηλεκτρονική Υπογραφή"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__sale_order_template_option_ids
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Optional Products"
msgstr "Προαιρετικά Είδη"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order__sale_order_option_ids
#: model:ir.model.fields,field_description:sale_management.field_sale_order_line__sale_order_option_ids
msgid "Optional Products Lines"
msgstr "Προαιρετικές Γραμμές Είδους"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Options"
msgstr "Επιλογές"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__is_present
msgid "Present on Quotation"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__product_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__product_id
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Product"
msgstr "Είδος"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__quantity
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_qty
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__quantity
msgid "Quantity"
msgstr "Ποσότητα"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Quantity:"
msgstr "Ποσότητα:"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__module_sale_quotation_builder
msgid "Quotation Builder"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__number_of_days
msgid "Quotation Duration"
msgstr "Διάρκεια Προσφοράς"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template
#: model:ir.model.fields,field_description:sale_management.field_sale_order__sale_order_template_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__name
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_tree
msgid "Quotation Template"
msgstr "Πρότυπο Προσφοράς"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Quotation Template Lines"
msgstr "Γραμμές Προτύπου Προσφοράς"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__sale_order_template_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__sale_order_template_id
msgid "Quotation Template Reference"
msgstr "Αναφορά Προτύπου Προσφοράς"

#. module: sale_management
#: model:ir.actions.act_window,name:sale_management.sale_order_template_action
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__group_sale_order_template
#: model:ir.ui.menu,name:sale_management.sale_order_template_menu
#: model:res.groups,name:sale_management.group_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Quotation Templates"
msgstr "Πρότυπα Προσφοράς"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Quotation expires after"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Remove"
msgstr "Αφαίρεση"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Remove one"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__require_signature
msgid ""
"Request a online signature to the customer in order to confirm orders "
"automatically."
msgstr ""

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__require_payment
msgid ""
"Request an online payment to the customer in order to confirm orders "
"automatically."
msgstr ""

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_option
msgid "Sale Options"
msgstr "Επιλογές Πώλησης"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.digest_digest_view_form
msgid "Sales"
msgstr "Πωλήσεις"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order
msgid "Sales Order"
msgstr "Παραγγελία"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_line
msgid "Sales Order Line"
msgstr "Γραμμή Παραγγελίας"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__order_id
msgid "Sales Order Reference"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Sales Quotation Template Lines"
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_search
msgid "Search Quotation Template"
msgstr "Αναζήτηση Προτύπου Προσφοράς"

#. module: sale_management
#: model:ir.model.fields.selection,name:sale_management.selection__sale_order_template_line__display_type__line_section
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Section"
msgstr "Τομέας"

#. module: sale_management
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale_management_1
msgid ""
"Selling the same product in different sizes or colors? Try the product grid "
"and populate your orders with multiple quantities of each variant. This "
"feature also exists in the Purchase application."
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__sequence
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__sequence
msgid "Sequence"
msgstr "Ακολουθία"

#. module: sale_management
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale1_management_0
msgid ""
"Struggling with a complex product catalog? Try out the Product Configurator "
"to help sales configure a product with different options: colors, size, "
"capacity, etc. Make sale orders encoding easier and error-proof."
msgstr ""

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__display_type
msgid "Technical field for UX purpose."
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__note
msgid "Terms and conditions"
msgstr "Όροι και προϋποθέσεις"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid ""
"The Administrator can set default Terms & Conditions in Sales Settings. "
"Terms set here will show up instead if you select this quotation template."
msgstr ""

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__mail_template_id
msgid ""
"This e-mail template will be sent on confirmation. Leave empty to send "
"nothing."
msgstr ""

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__is_present
msgid ""
"This field will be checked if the option line's product is already present "
"in the quotation."
msgstr ""

#. module: sale_management
#: model:digest.tip,name:sale_management.digest_tip_sale1_management_0
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale1_management_0
msgid "Tip: Odoo supports configurable products"
msgstr ""

#. module: sale_management
#: model:digest.tip,name:sale_management.digest_tip_sale_management_1
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale_management_1
msgid "Tip: Sell or buy products in bulk with matrixes"
msgstr ""

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__price_unit
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Unit Price"
msgstr "Τιμή Μονάδας"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Unit Price:"
msgstr "Τιμή Μονάδας:"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_id
msgid "Unit of Measure"
msgstr "Μονάδα Μέτρησης"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__uom_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__uom_id
msgid "Unit of Measure "
msgstr "Μονάδα Μέτρησης "

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "UoM"
msgstr "ΜΜ"

#. module: sale_management
#: model_terms:ir.actions.act_window,help:sale_management.sale_order_template_action
msgid ""
"Use templates to create polished, professional quotes in minutes.\n"
"                Send these quotes by email and let your customers sign online.\n"
"                Use cross-selling and discounts to push and boost your sales."
msgstr ""

#. module: sale_management
#: code:addons/sale_management/models/sale_order.py:0
#, python-format
msgid "You cannot add options to a confirmed order."
msgstr "Δεν μπορείτε να προσθέσετε επιλογές σε μια επιβεβαιωμένη εντολή."

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid ""
"You cannot change the type of a sale quote line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""

#. module: sale_management
#: code:addons/sale_management/models/sale_order.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid "Your template cannot contain products from multiple companies."
msgstr ""

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid ""
"Your template contains products from company %(product_company)s whereas your template belongs to company %(template_company)s. \n"
" Please change the company of your template or remove the products from other companies."
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "days"
msgstr "ημέρες"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "e.g. Standard Consultancy Package"
msgstr ""
