# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_automation
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__help
msgid "Action Description"
msgstr "Opis akcije"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__name
msgid "Action Name"
msgstr "Naziv akcije"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__state
msgid "Action To Do"
msgstr "Akcija za uraditi"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__type
msgid "Action Type"
msgstr "Tip akcije"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__active
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__active
msgid "Active"
msgstr "Aktivan"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_type_id
msgid "Activity"
msgstr "Aktivnost"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_type
msgid "Activity User Type"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__channel_ids
msgid "Add Channels"
msgstr "Dodaj kanale"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__partner_ids
msgid "Add Followers"
msgstr "Dodaj pratioce"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_domain
msgid "Apply on"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__is_assigned_to_admin
msgid "Assigned to admin user"
msgstr ""

#. module: base_automation
#: selection:ir.actions.server,usage:0
#: model:ir.model,name:base_automation.model_base_automation
msgid "Automated Action"
msgstr ""

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
msgid "Automated Actions"
msgstr "Automatizirane radnje"

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation_line_test
msgid "Automated Rule Line Test"
msgstr ""

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation_lead_test
msgid "Automated Rule Test"
msgstr ""

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation"
msgstr "Automatizacija"

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
#: model:ir.cron,cron_name:base_automation.ir_cron_data_base_automation_check
#: model:ir.cron,name:base_automation.ir_cron_data_base_automation_check
msgid "Base Action Rule: check and execute"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_recursive
#: model:ir.actions.server,name:base_automation.test_rule_recursive_ir_actions_server
msgid "Base Automation: test recursive rule"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_on_create
#: model:ir.actions.server,name:base_automation.test_rule_on_create_ir_actions_server
msgid "Base Automation: test rule on create"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_on_recompute
#: model:ir.actions.server,name:base_automation.test_rule_on_recompute_ir_actions_server
msgid "Base Automation: test rule on recompute"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_on_line
#: model:ir.actions.server,name:base_automation.test_rule_on_line_ir_actions_server
msgid "Base Automation: test rule on secondary model"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_on_write
#: model:ir.actions.server,name:base_automation.test_rule_on_write_ir_actions_server
msgid "Base Automation: test rule on write"
msgstr ""

#. module: base_automation
#: model:base.automation,name:base_automation.test_rule_on_write_check_context
#: model:ir.actions.server,name:base_automation.test_rule_on_write_check_context_ir_actions_server
msgid "Base Automation: test rule on write check context"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "Based on Form Modification"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "Based on Timed Condition"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_pre_domain
msgid "Before Update Domain"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_model_id
msgid "Binding Model"
msgstr "Vezni model"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_type
msgid "Binding Type"
msgstr "Tip vezivanja"

#. module: base_automation
#: selection:base.automation.lead.test,state:0
msgid "Cancelled"
msgstr "Otkazan"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation_lead_test__customer
msgid ""
"Check this box if this contact is a customer. It can be selected in sales "
"orders."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__child_ids
msgid "Child Actions"
msgstr "Podređena akcija"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__child_ids
msgid ""
"Child server actions that will be executed. Note that the last return "
"returned action value will be used as global return value."
msgstr ""
"Podređena serverska akcija koja će biti izvršena. Zapamtite da će zadnja "
"vraćena vrijednost akcije biti korišćena kao globalna vraćena vrijednost."

#. module: base_automation
#: selection:base.automation.lead.test,state:0
msgid "Closed"
msgstr "Zatvoreno"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__on_change_fields
msgid "Comma-separated list of field names that triggers the onchange."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_id
msgid "Create/Write Target Model"
msgstr "Kreiraj/Piši ciljani model"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_uid
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__create_uid
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_date
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__create_date
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: base_automation
#: selection:base.automation,trg_date_range_type:0
msgid "Days"
msgstr "Dani"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__deadline
msgid "Deadline"
msgstr "Rok izvršenja"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_range
msgid ""
"Delay after the trigger date.\n"
"                                    You can put a negative number if you need a delay before the\n"
"                                    trigger date, like sending a reminder 15 minutes before a meeting."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range
msgid "Delay after trigger date"
msgstr "Odgodi nakon datuma okidanja"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range_type
msgid "Delay type"
msgstr "Vrsta odgode"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__display_name
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__display_name
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range
msgid "Due Date In"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range_type
msgid "Due type"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__template_id
msgid "Email Template"
msgstr "Predložak email-a"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__xml_id
msgid "External ID"
msgstr "Externi ID"

#. module: base_automation
#: selection:base.automation,trg_date_range_type:0
msgid "Hours"
msgstr "Sati"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__id
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__id
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test__id
msgid "ID"
msgstr "ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the action "
"rule."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record."
msgstr ""
"Ako je prisutan, ovaj uslov mora biti ispunjen prije ažuriranja zapisa."

#. module: base_automation
#: selection:base.automation.lead.test,state:0
msgid "In Progress"
msgstr "U Toku"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__customer
msgid "Is a Customer"
msgstr "Kupac"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__date_action_last
msgid "Last Action"
msgstr "Posljednja akcija"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation____last_update
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test____last_update
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__last_run
msgid "Last Run"
msgstr "Posljednje pokretanje"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_uid
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__write_uid
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_date
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__write_date
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test__lead_id
msgid "Lead"
msgstr "Potencijal"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__line_ids
msgid "Line"
msgstr "Stavka"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__link_field_id
msgid "Link using field"
msgstr "Polje koje koristi vezu"

#. module: base_automation
#: selection:base.automation,trg_date_range_type:0
msgid "Minutes"
msgstr "Minute"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_id
msgid "Model"
msgstr "Model"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_name
msgid "Model Description"
msgstr "Opis modela"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_name
msgid "Model Name"
msgstr "Naziv modela"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__crud_model_id
msgid ""
"Model for record creation / update. Set this field only to specify a "
"different model than the base model."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__model_id
msgid "Model on which the server action runs."
msgstr "Model na kojem se izvršava serverska akcija."

#. module: base_automation
#: selection:base.automation,trg_date_range_type:0
msgid "Months"
msgstr "Mjeseci"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test__name
msgid "Name"
msgstr "Naziv:"

#. module: base_automation
#: selection:base.automation.lead.test,state:0
msgid "New"
msgstr "Novi"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_note
msgid "Note"
msgstr "Zabilješka"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__on_change_fields
msgid "On Change Fields Trigger"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "On Creation"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "On Creation & Update"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "On Deletion"
msgstr ""

#. module: base_automation
#: selection:base.automation,trigger:0
msgid "On Update"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__help
msgid ""
"Optional help text for the users with a description of the target view, such"
" as its usage and purpose."
msgstr ""
"Opcionalni tekst pomoći za korisnike sa opisom ciljnog pogleda, kao što je "
"njegova upotreba i svrha."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__partner_id
msgid "Partner"
msgstr "Partner"

#. module: base_automation
#: selection:base.automation.lead.test,state:0
msgid "Pending"
msgstr "Na čekanju"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__priority
msgid "Priority"
msgstr "Prioritet"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__link_field_id
msgid ""
"Provide the field used to link the newly created record on the record on "
"used by the server action."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__code
msgid "Python Code"
msgstr "Python Kod"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Remove Action"
msgstr ""

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Remove the contextual action related to this server action"
msgstr "Ukloni kontekstnu akciju povezanu sa ovom serverskom akcijom"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_id
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__user_id
msgid "Responsible"
msgstr "Odgovoran"

#. module: base_automation
#: selection:ir.actions.server,usage:0
msgid "Scheduled Action"
msgstr "Zakazane akcije"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: base_automation
#: selection:ir.actions.server,usage:0
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "Server Action"
msgstr "Serverska akcija"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__action_server_id
msgid "Server Actions"
msgstr "Serverske akcije"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__binding_model_id
msgid ""
"Setting a value makes this action available in the sidebar for the given "
"model."
msgstr ""
"Postavljanjem vrijednosti omogućava ovoj akciji da bude dostupna u meniju "
"ovog modela."

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Setup a new automated automation"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__state
msgid "Status"
msgstr "Status"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_lead_test__name
msgid "Subject"
msgstr "Tema"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_summary
msgid "Summary"
msgstr "Sažetak"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr ""

#. module: base_automation
#: code:addons/base_automation/models/base_automation.py:87
#, python-format
msgid ""
"The \"%(trigger_value)s\" %(trigger_label)s can only be used with the "
"\"%(state_value)s\" action type"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger
msgid "Trigger Condition"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_id
msgid "Trigger Date"
msgstr "Datum okidanja"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create or Copy a new Record': create a new record with new values, or copy an existing record in your database\n"
"- 'Write on a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Add Followers': add followers to a record (available in Discuss)\n"
"- 'Send Email': automatically send an email (available in email_template)"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__usage
msgid "Usage"
msgstr "Upotreba"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_calendar_id
msgid "Use Calendar"
msgstr ""

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Use automated actions to automatically trigger actions for\n"
"                various screens. Example: a lead created by a specific user may\n"
"                be automatically set to a specific Sales Team, or an\n"
"                opportunity which still has status pending after 14 days might\n"
"                trigger an automatic reminder email."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation_line_test__user_id
msgid "User"
msgstr "Korisnik"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_field_name
msgid "User field name"
msgstr ""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__fields_lines
msgid "Value Mapping"
msgstr "Mapirana vrijednost"

#. module: base_automation
#: code:addons/base_automation/models/base_automation.py:86
#, python-format
msgid "Warning"
msgstr "Upozorenje"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possible to use a "
"calendar to compute the date based on working days."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__sequence
msgid ""
"When dealing with multiple actions, the execution order is based on the "
"sequence. Low number means high priority."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                                  If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr "Kada nije označeno, pravilo je skriveno i neće biti izvršeno."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__code
msgid ""
"Write Python code that the action will execute. Some variables are available"
" for use; help about python expression is given in the help tab."
msgstr ""
