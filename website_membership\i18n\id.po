# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_membership
# 
# Translators:
# <PERSON> <amunif<PERSON><PERSON>@gmail.com>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <wa<PERSON><PERSON>@gmail.com>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Abe <PERSON>, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"
msgstr ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"Link eksternal\"/>"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "All"
msgstr "Semua"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Semua Negara"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Associations"
msgstr "Asosiasi"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "Close"
msgstr "Tutup"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Find a business partner"
msgstr "Menemukan mitra bisnis"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:0
#, python-format
msgid "Free Members"
msgstr "Free Members"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_country
msgid "Location"
msgstr "Lokasi"

#. module: website_membership
#: code:addons/website_membership/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_membership.index
#, python-format
msgid "Members"
msgstr "Anggota"

#. module: website_membership
#: model:ir.model,name:website_membership.model_membership_membership_line
msgid "Membership Line"
msgstr "Garis Keanggotaan"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "No result found."
msgstr "Tidak ada hasil yang ditemukan."

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Our Members Directory"
msgstr "Direktori Anggota kami"

#. module: website_membership
#: model:ir.model,name:website_membership.model_website
msgid "Website"
msgstr "Website"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "World Map"
msgstr "Peta Dunia"
