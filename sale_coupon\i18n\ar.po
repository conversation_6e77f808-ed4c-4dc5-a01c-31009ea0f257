# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_coupon
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <must<PERSON><PERSON>@cubexco.com>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_view_coupon_program_kanban
msgid "<strong>Sales</strong>"
msgstr "<strong>المبيعات</strong>"

#. module: sale_coupon
#: model:mail.template,body_html:sale_coupon.mail_template_sale_coupon
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object.partner_id.name\">\n"
"            Congratulations <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/>\n"
"        </t>\n"
"\n"
"        Here is your reward from <t t-out=\"object.program_id.company_id.name or ''\">YourCompany</t>.<br/>\n"
"\n"
"        <t t-if=\"object.program_id.reward_type == 'discount'\">\n"
"            <t t-if=\"object.program_id.discount_type == 'fixed_amount'\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-out=\"'%s' % format_amount(object.program_id.discount_fixed_amount, object.program_id.currency_id) or ''\">$ 10.00</span><br/>\n"
"                <strong style=\"font-size: 24px;\">off on your next order</strong><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\"><t t-out=\"object.program_id.discount_percentage or ''\">10</t> %</span>\n"
"            </t>\n"
"            <t t-if=\"object.program_id.discount_apply_on == 'specific_products'\">\n"
"                <br/>\n"
"                <t t-if=\"len(object.program_id.discount_specific_product_ids) != 1\">\n"
"                    <t t-set=\"display_specific_products\" t-value=\"True\"/>\n"
"                    <strong style=\"font-size: 24px;\">\n"
"                        on some products*\n"
"                    </strong>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <strong style=\"font-size: 24px;\" t-out=\"'on %s' % object.program_id.discount_specific_product_ids.name or ''\">Customizable Desk</strong>\n"
"                </t>\n"
"            </t>\n"
"            <t t-elif=\"object.program_id.discount_apply_on == 'cheapest_product'\">\n"
"                <br/><strong style=\"font-size: 24px;\">\n"
"                    off on the cheapest product\n"
"                </strong>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <br/><strong style=\"font-size: 24px;\">\n"
"                    off on your next order\n"
"                </strong>\n"
"            </t>\n"
"            <br/>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'product'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\" t-out=\"'get %s free %s' % (object.program_id.reward_product_quantity, object.program_id.reward_product_id.name) or ''\">get 1 free Corner Desk Right Sit</span><br/>\n"
"            <strong style=\"font-size: 24px;\">on your next order</strong><br/>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'free_shipping'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\">\n"
"                get free shipping\n"
"            </span><br/>\n"
"            <strong style=\"font-size: 24px;\">on your next order</strong><br/>\n"
"        </t>\n"
"    </td></tr>\n"
"    <tr style=\"margin-top: 16px\"><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Use this promo code\n"
"        <t t-if=\"object.expiration_date\">\n"
"            before <t t-out=\"object.expiration_date or ''\">2021-06-16</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">15637502648479132902</strong>\n"
"        </p>\n"
"        <t t-if=\"object.program_id.rule_min_quantity not in [0, 1]\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                Minimum purchase of <t t-out=\"object.program_id.rule_min_quantity or ''\">10</t> products\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"object.program_id.rule_minimum_amount != 0.00\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                Valid for purchase above <t t-out=\"object.program_id.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(object.program_id.rule_minimum_amount) or ''\">10.00</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"display_specific_products\">\n"
"            <span>\n"
"                *Valid for following products: <t t-out=\"', '.join(object.program_id.discount_specific_product_ids.mapped('name')) or ''\">Customizable Desk</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <br/>\n"
"        Thank you,\n"
"        <t t-if=\"object.order_id.user_id.signature\">\n"
"            <br/>\n"
"            <t t-out=\"object.order_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object.partner_id.name\">\n"
"            تهانينا <t t-out=\"object.partner_id.name or ''\">براندن فريمان</t>،<br/>\n"
"        </t>\n"
"\n"
"        إليك مكافأتك من <t t-out=\"object.program_id.company_id.name or ''\">شركتك</t>.<br/>\n"
"\n"
"        <t t-if=\"object.program_id.reward_type == 'discount'\">\n"
"            <t t-if=\"object.program_id.discount_type == 'fixed_amount'\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-out=\"'%s' % format_amount(object.program_id.discount_fixed_amount, object.program_id.currency_id) or ''\">$ 10.00</span><br/>\n"
"                <strong style=\"font-size: 24px;\">خصم على طلبك التالي</strong><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\"><t t-out=\"object.program_id.discount_percentage or ''\">10</t> %</span>\n"
"            </t>\n"
"            <t t-if=\"object.program_id.discount_apply_on == 'specific_products'\">\n"
"                <br/>\n"
"                <t t-if=\"len(object.program_id.discount_specific_product_ids) != 1\">\n"
"                    <t t-set=\"display_specific_products\" t-value=\"True\"/>\n"
"                    <strong style=\"font-size: 24px;\">\n"
"                        على بعض المنتجات*\n"
"                    </strong>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <strong style=\"font-size: 24px;\" t-out=\"'on %s' % object.program_id.discount_specific_product_ids.name or ''\">مكتب قابل للتعديل</strong>\n"
"                </t>\n"
"            </t>\n"
"            <t t-elif=\"object.program_id.discount_apply_on == 'cheapest_product'\">\n"
"                <br/><strong style=\"font-size: 24px;\">\n"
"                    خصم على أرخص منتج\n"
"                </strong>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <br/><strong style=\"font-size: 24px;\">\n"
"                    خصم على طلبك التالي\n"
"                </strong>\n"
"            </t>\n"
"            <br/>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'product'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\" t-out=\"'get %s free %s' % (object.program_id.reward_product_quantity, object.program_id.reward_product_id.name) or ''\">احصل على مكتب زاوية 1 مجاني</span><br/>\n"
"            <strong style=\"font-size: 24px;\">في طلبك التالي</strong><br/>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'free_shipping'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\">\n"
"                شحن مجاني\n"
"            </span><br/>\n"
"            <strong style=\"font-size: 24px;\">في طلبك التالي</strong><br/>\n"
"        </t>\n"
"    </td></tr>\n"
"    <tr style=\"margin-top: 16px\"><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        استخدم هذا الرمز الترويجي\n"
"        <t t-if=\"object.expiration_date\">\n"
"            قبل <t t-out=\"object.expiration_date or ''\">2021-06-16</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">15637502648479132902</strong>\n"
"        </p>\n"
"        <t t-if=\"object.program_id.rule_min_quantity not in [0, 1]\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                الحد الأدنى للشراء هو <t t-out=\"object.program_id.rule_min_quantity or ''\">10</t> منتجات\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"object.program_id.rule_minimum_amount != 0.00\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                صالح للمشتريات التي تفوق قيمتها <t t-out=\"object.program_id.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(object.program_id.rule_minimum_amount) or ''\">10.00</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"display_specific_products\">\n"
"            <span>\n"
"                *صالح للمنتجات التالية: <t t-out=\"', '.join(object.program_id.discount_specific_product_ids.mapped('name')) or ''\">مكتب قابل للتعديل</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <br/>\n"
"        شكراً لك،\n"
"        <t t-if=\"object.order_id.user_id.signature\">\n"
"            <br/>\n"
"            <t t-out=\"object.order_id.user_id.signature or ''\">--<br/>ميتشل آدمن</t>\n"
"        </t>\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon.py:0
#, python-format
msgid "A Coupon is already applied for the same reward"
msgstr "تم بالفعل تطبيق كوبون لنفس المكافأة"

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon.py:0
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid ""
"A minimum of %(amount)s %(currency)s should be purchased to get the reward"
msgstr "يجب شراء %(amount)s %(currency)s كحد أدنى للحصول على المكافأة  "

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__applied_coupon_ids
msgid "Applied Coupons"
msgstr "الكوبونات المُطبقة"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__no_code_promo_program_ids
msgid "Applied Immediate Promo Programs"
msgstr "البرامج الترويجية المطبقة فورًا"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__code_promo_program_id
msgid "Applied Promo Program"
msgstr "البرنامج الترويجي المطبق"

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_order__promo_code
msgid "Applied program code"
msgstr "الكود الترويجي المطبق"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_apply_code_view_form
msgid "Apply"
msgstr "تطبيق"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_apply_code_view_form
msgid "Apply coupon"
msgstr "تطبيق الكوبون"

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon.py:0
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid "At least one of the required conditions is not met to get the reward!"
msgstr "لم يتم استيفاء بعض شروط الحصول على المكافأة!"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_apply_code_view_form
msgid "Cancel"
msgstr "إلغاء "

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__coupon_code
msgid "Code"
msgstr "الكود"

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_coupon_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_order_view_form
msgid "Coupon"
msgstr "كوبون"

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_coupon_program
msgid "Coupon Program"
msgstr "برنامج الكوبونات"

#. module: sale_coupon
#: model:ir.ui.menu,name:sale_coupon.menu_coupon_type_config
#: model_terms:ir.ui.view,arch_db:sale_coupon.res_config_settings_view_form
msgid "Coupon Programs"
msgstr "برامج الكوبونات"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_order.py:0
#, python-format
msgid "Discount: %(program)s - On product with following taxes: %(taxes)s"
msgstr "الخصم: %(program)s - على المنتجات ذات الضرائب التالية: %(taxes)s "

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_order.py:0
#: code:addons/sale_coupon/models/sale_order.py:0
#, python-format
msgid "Discount: %s"
msgstr "الخصم: %s "

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: sale_coupon
#: model:ir.actions.act_window,name:sale_coupon.sale_coupon_apply_code_action
msgid "Enter Promotion or Coupon Code"
msgstr "أدخل رمز العرض أو رمز الكوبون "

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_order.py:0
#, python-format
msgid "Free Product"
msgstr "منتج مجاني"

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon.py:0
#, python-format
msgid "Global discounts are not cumulable."
msgstr "الخصومات الشاملة ليست تراكمية."

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid "Global discounts are not cumulative."
msgstr "الخصومات الشاملة ليست تراكمية."

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__id
msgid "ID"
msgstr "المُعرف"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order_line__is_reward_line
msgid "Is a program reward line"
msgstr "بند مكافأة برنامج"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__generated_coupon_ids
msgid "Offered Coupons"
msgstr "الكوبونات المعروضة"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_coupon_program__order_count
msgid "Order Count"
msgstr "عدد الطلبات "

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_coupon_coupon__order_id
msgid "Order Reference"
msgstr "مرجع الطلب "

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid "Promo code %s has been expired."
msgstr "انتهت صلاحية الكود الترويجي %s. "

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid "Promo code is expired"
msgstr "انتهت صلاحية الكود الترويجي "

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid "Promo code is invalid"
msgstr "الكود الترويجي غير صالح "

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__promo_code
msgid "Promotion Code"
msgstr "الكود الترويجي"

#. module: sale_coupon
#: model:ir.ui.menu,name:sale_coupon.menu_promotion_type_config
#: model_terms:ir.ui.view,arch_db:sale_coupon.res_config_settings_view_form
msgid "Promotion Programs"
msgstr "البرامج الترويجية"

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid "Promotionals codes are not cumulative."
msgstr "الأكواد الترويجية ليست تراكمية."

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_order_view_form
msgid "Promotions"
msgstr "العروض "

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__reward_amount
msgid "Reward Amount"
msgstr "مبلغ المكافأة "

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_coupon_program_form
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_promo_program_form
msgid "Sales"
msgstr "المبيعات"

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_sale_coupon_apply_code
msgid "Sales Coupon Apply Code"
msgstr "تطبيق كود كوبون المبيعات"

#. module: sale_coupon
#: model:ir.actions.act_window,name:sale_coupon.sale_order_action
#: model:ir.model,name:sale_coupon.model_sale_order
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid "Sales Orders"
msgstr "أوامر البيع"

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon.py:0
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid "The customer doesn't have access to this reward."
msgstr "لا يملك العميل صلاحية الوصول لهذه المكافأة."

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid "The promo code is already applied on this order"
msgstr "تم تطبيق الكود الترويجي على هذا الطلب بالفعل "

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid "The promotional offer is already applied on this order"
msgstr "تم تطبيق العرض الترويجي على هذا الطلب بالفعل "

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon.py:0
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid ""
"The reward products should be in the sales order lines to apply the "
"discount."
msgstr "يجب أن تكون منتجات المكافأة في بنود أمر المبيعات لتطبيق الخصم. "

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_coupon_coupon__order_id
msgid "The sales order from which coupon is generated"
msgstr "أمر المبيعات الذي تم إنشاء الكوبون منه"

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_coupon_coupon__sales_order_id
msgid "The sales order on which the coupon is applied"
msgstr "أمر المبيعات الذي تم تطبيق الكوبون عليه"

#. module: sale_coupon
#: code:addons/sale_coupon/wizard/sale_coupon_apply_code.py:0
#, python-format
msgid "This coupon is invalid (%s)."
msgstr "هذا الكوبون غير صالح (%s). "

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid "This coupon is not yet usable. It will be starting from %s"
msgstr "هذا الكوبون غير قابل للاستخدام بعد. ستتمكن من استخدامه بدءاً من %s "

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_coupon_coupon__sales_order_id
msgid "Used in"
msgstr "مُستخدَم في "

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_order_view_form
msgid ""
"When clicked, the content of the order will be checked to detect (and apply)"
" possible promotion programs."
msgstr ""
"عندما تقوم بالضغط عليه، سوف يتم التحقق من محتوى طلبك لرصد (وتطبيق) البرامج "
"الترويجية الممكنة. "

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon.py:0
#, python-format
msgid ""
"You don't have the required product quantities on your sales order. All the "
"products should be recorded on the sales order. (Example: You need to have 3"
" T-shirts on your sales order if the promotion is 'Buy 2, Get 1 Free')."
msgstr ""
"لا تملك كميات المنتج المطلوبة في أمر مبيعاتك. يجب أن تكون كافة المنتجات "
"مسجلة في أمر المبيعات. (مثال: يجب أن يكون هناك 3 قمصان في أمر المبيعات إذا "
"كان العرض هو 'اشترِ قميصين، واحصل على الثالث مجاناً'). "

#. module: sale_coupon
#: code:addons/sale_coupon/models/coupon_program.py:0
#, python-format
msgid ""
"You don't have the required product quantities on your sales order. If the "
"reward is same product quantity, please make sure that all the products are "
"recorded on the sales order (Example: You need to have 3 T-shirts on your "
"sales order if the promotion is 'Buy 2, Get 1 Free'."
msgstr ""
"لا تملك كميات المنتج المطلوبة في أمر مبيعاتك. إذا كانت المكافأة هي نفس كمية "
"المنتج، يرجى التأكد من أن كافة المنتجات مسجلة في أمر المبيعات. (مثال: يجب أن"
" يكون هناك 3 قمصان في أمر المبيعات إذا كان العرض هو 'اشترِ قميصين، واحصل على"
" الثالث مجاناً'). "

#. module: sale_coupon
#: model:mail.template,report_name:sale_coupon.mail_template_sale_coupon
msgid "Your Coupon Code"
msgstr "كود الكوبون الخاص بك"

#. module: sale_coupon
#: model:mail.template,subject:sale_coupon.mail_template_sale_coupon
msgid "Your reward coupon from {{ object.program_id.company_id.name }} "
msgstr "كوبون المكافأة الخاص بك من {{ object.program_id.company_id.name }} "

#. module: sale_coupon
#: model:mail.template,name:sale_coupon.mail_template_sale_coupon
msgid "[Sales] Coupon: Send by Email"
msgstr "كوبون [المبيعات]: الإرسال عبر البريد الإلكتروني "

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_order.py:0
#, python-format
msgid "limited to "
msgstr "يقتصر على "
