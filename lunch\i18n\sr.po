# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * lunch
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <bojan.borov<PERSON><EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-09-21 13:18+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "<span class=\"o_stat_text\">Balance</span>"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.report_lunch_order
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: lunch
#: model:mail.template,body_html:lunch.lunch_order_mail_supplier
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Lunch Order</span><br/>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${user.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"    <div>\n"
"        % set lines = ctx['lines']\n"
"        % set order = ctx['order']\n"
"        % set supplier = user.env['res.partner'].browse(order['supplier_id'])\n"
"        % set currency = user.env['res.currency'].browse(order['currency_id'])\n"
"        <p>\n"
"        Dear ${order['supplier_name']},\n"
"        </p><p>\n"
"        Here is, today orders for ${order['company_name']}:\n"
"        </p>\n"
"\n"
"      <ul>\n"
"        % for line in lines:\n"
"            <li>\n"
"                <strong>\n"
"                    ${line['product']}\n"
"                    (x${line['quantity']})\n"
"                 </strong>\n"
"                % if line['note']:\n"
"                    <em>(${line['note']})</em>\n"
"                % endif\n"
"                <div style=\"display: inline-block; float:right\">\n"
"                    ${format_amount(line['price'], currency)}\n"
"                </div>\n"
"            </li>\n"
"        % endfor\n"
"        </ul>\n"
"\n"
"        <p>Amounting in <strong>${format_amount(order['amount_total'], currency)}</strong>.</p>\n"
"        <p>Do not hesitate to contact us if you have any question.</p>\n"
"    </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    ${user.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    ${user.company_id.phone}\n"
"                    % if user.company_id.phone and (user.company_id.email or user.company_id.website)\n"
"                    |\n"
"                    % endif\n"
"                    % if user.company_id.email\n"
"                    <a href=\"'mailto:%s' % ${user.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${user.company_id.email}</a>\n"
"                    % endif\n"
"                    % if user.company_id.email and user.company_id.website\n"
"                    |\n"
"                    % endif\n"
"                    % if user.company_id.website\n"
"                    <a href=\"'%s' % ${user.company_id.website}\" style=\"text-decoration:none; color: #454748;\">\n"
"                    ${user.company_id.website}\n"
"                    </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_control_accounts
msgid ""
"A cashmove can either be an expense or a payment.<br>\n"
"                An expense is automatically created at the order receipt.<br>\n"
"                A payment represents the employee reimbursement to the company."
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action
msgid "A product is defined by its name, category, price and vendor."
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__active
#: model:ir.model.fields,field_description:lunch.field_lunch_product__active
msgid "Active"
msgstr "Активно"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch.xml:15
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: lunch
#: code:addons/lunch/models/lunch.py:425
#, python-format
msgid "Alert"
msgstr ""

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_alert_action
#: model:ir.model.fields,field_description:lunch.field_lunch_order__alerts
#: model:ir.ui.menu,name:lunch.lunch_alert_menu
msgid "Alerts"
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_alert_action
msgid ""
"Alerts are used to warn employee from possible issues concerning the lunch orders.\n"
"                To create a lunch alert you have to define its recurrence, the time interval during which the alert should be executed and the message to display."
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__amount
msgid "Amount"
msgstr "Iznos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__end_hour
msgid "And"
msgstr "И"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Archived"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__available
msgid "Available"
msgstr "Dostupno"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__balance_visible
msgid "Balance Visible"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__start_hour
msgid "Between"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_alert_kanban
msgid "Between:"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search_2
msgid "By Employee"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
msgid "By Order"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "By User"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
msgid "By Vendor"
msgstr ""

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_cashmove__description
msgid "Can be an order or a payment"
msgstr ""

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_cashmove__amount
msgid ""
"Can be positive (payment) or negative (order or payment if user wants to get"
" his money back)"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_order_line_lucky
msgid "Cancel"
msgstr "Otkaži"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
#: selection:lunch.order,state:0 selection:lunch.order.line,state:0
msgid "Cancelled"
msgstr "Otkazano"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__cashmove
msgid "Cash Move"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__cash_move_balance
msgid "Cash Move Balance"
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_line_action_control_suppliers
msgid ""
"Click on the <span class=\"fa fa-phone text-success\" title=\"Order button\"></span> to announce that the order is ordered.<br>\n"
"                Click on the <span class=\"fa fa-check text-success\" title=\"Receive button\"></span> to announce that the order is received.<br>\n"
"                Click on the <span class=\"fa fa-times-circle text-danger\" title=\"Cancel button\"></span> red X to announce that the order isn't available."
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_line_action_by_supplier
msgid ""
"Click on the <span class=\"fa fa-phone text-success\" title=\"Order button\"></span> to announce that the order is ordered.<br>\n"
"                Click on the <span class=\"fa fa-check text-success\" title=\"Receive button\"></span> to announce that the order is received.<br>\n"
"                Click on the <span class=\"fa fa-times-circle text-danger\" title=\"Cancel button\"></span> to announce that the order isn't available."
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__company_id
msgid "Company"
msgstr "Kompanija"

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_config
msgid "Configuration"
msgstr "Konfiguracija"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_action_control_accounts
#: model:ir.ui.menu,name:lunch.lunch_cashmove_menu_control_accounts
msgid "Control Accounts"
msgstr ""

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_line_action_control_suppliers
msgid "Control Vendors"
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_alert_action
msgid "Create a new lunch alert"
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_category_action
msgid "Create a new lunch category"
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_control_accounts
msgid "Create a new payment"
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action
msgid "Create a new product for lunch"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line_lucky__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__create_uid
msgid "Created by"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line_lucky__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__create_date
msgid "Created on"
msgstr "Kreiran"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__date
#: model:ir.model.fields,field_description:lunch.field_lunch_order__date
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__date
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
msgid "Date"
msgstr "Datum"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__specific_day
msgid "Day"
msgstr "Dan"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__description
#: model:ir.model.fields,field_description:lunch.field_lunch_product__description
#: model_terms:ir.ui.view,arch_db:lunch.report_lunch_order
msgid "Description"
msgstr "Opis"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__display
msgid "Display"
msgstr "Prikaz"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line_lucky__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_product__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__display_name
msgid "Display Name"
msgstr ""

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch.xml:7
#, python-format
msgid "Don't forget the alerts displayed in the reddish area"
msgstr ""

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_cashmove_menu_payment
msgid "Employee Payments"
msgstr ""

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_order_line_lucky__is_max_budget
msgid "Enable this option to set a maximal budget for your lucky order."
msgstr ""

#. module: lunch
#: selection:lunch.alert,alert_type:0
msgid "Every Day"
msgstr ""

#. module: lunch
#: selection:lunch.alert,alert_type:0
msgid "Every Week"
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_alert_action
msgid ""
"Example:<br>\n"
"                Recurency: Everyday,<br>\n"
"                Time interval: from 00h00 am to 11h59 pm,<br>\n"
"                Message: \"You must order before 10h30 am\"."
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Feeling Lucky"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__friday
msgid "Friday"
msgstr "Petak"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Group By"
msgstr "Grupiši po"

#. module: lunch
#: model:ir.module.category,description:lunch.module_lunch_category
msgid ""
"Helps you handle your lunch needs, if you are a manager you will be able to "
"create new products, cashmoves and to confirm or cancel orders."
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_category_action
msgid "Here you can access all categories for the lunch products."
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_line_action_by_supplier
msgid "Here you can see today's orders grouped by vendors."
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_account
msgid ""
"Here you can see your cash moves.<br>A cash move can either be an expense or a payment.\n"
"                An expense is automatically created when an order is received while a payment is a reimbursement to the company encoded by the manager."
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_order_line_lucky
msgid "I'm feeling lucky"
msgstr ""

#. module: lunch
#: model:ir.actions.act_window,name:lunch.action_lunch_order_line_lucky
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_order_line_lucky
msgid "I'm feeling lucky today !"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line_lucky__is_max_budget
msgid "I'm not feeling rich"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__id
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__id
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line_lucky__id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__id
msgid "ID"
msgstr "ID"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_alert__partner_id
msgid "If specified, the selected vendor can be ordered only on selected days"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__state
msgid "Is an order or a payment"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_order____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line_lucky____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_product____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category____last_update
msgid "Last Modified on"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line_lucky__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__write_uid
msgid "Last Updated by"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line_lucky__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__write_date
msgid "Last Updated on"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "List"
msgstr ""

#. module: lunch
#: model:ir.module.category,name:lunch.module_lunch_category
#: model:ir.ui.menu,name:lunch.menu_lunch
msgid "Lunch"
msgstr ""

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_alert
msgid "Lunch Alert"
msgstr ""

#. module: lunch
#: code:addons/lunch/models/lunch.py:393
#: model:ir.model,name:lunch.model_lunch_cashmove
#, python-format
msgid "Lunch Cashmove"
msgstr ""

#. module: lunch
#: code:addons/lunch/models/lunch.py:67
#: model:ir.actions.report,name:lunch.action_report_lunch_order
#: model:ir.model,name:lunch.model_lunch_order
#: model_terms:ir.ui.view,arch_db:lunch.report_lunch_order
#, python-format
msgid "Lunch Order"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Lunch Order Form"
msgstr ""

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_order_line
msgid "Lunch Order Line"
msgstr ""

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_order_line_lucky
msgid "Lunch Order Line Lucky"
msgstr ""

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_product
msgid "Lunch Product"
msgstr ""

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_product_category
msgid "Lunch Product Category"
msgstr ""

#. module: lunch
#: model:ir.actions.server,name:lunch.action_server_lunch_archive_product
msgid "Lunch: Archive/Restore products"
msgstr ""

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_line_action_cancel
msgid "Lunch: Cancel meals"
msgstr ""

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_line_action_order
msgid "Lunch: Order meals"
msgstr ""

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_line_action_confirm
msgid "Lunch: Receive meals"
msgstr ""

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_admin
#: model:res.groups,name:lunch.group_lunch_manager
msgid "Manager"
msgstr "Menadžer"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line_lucky__max_budget
msgid "Max Budget"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__message
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "Message"
msgstr "Poruka"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__monday
msgid "Monday"
msgstr "Ponedeljak"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "My Account grouped"
msgstr ""

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_title
msgid "My Lunch"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
msgid "My Orders"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.report_lunch_order
msgid "Name/Date"
msgstr ""

#. module: lunch
#: selection:lunch.order,state:0 selection:lunch.order.line,state:0
msgid "New"
msgstr "Novi"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action_form
#: model:ir.ui.menu,name:lunch.lunch_order_menu_form
msgid "New Order"
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_account
msgid "No cash move yet"
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_line_action_control_suppliers
msgid "No lunch order yet"
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_line_action
msgid "No previous order found"
msgstr ""

#. module: lunch
#: code:addons/lunch/wizard/lucky_order.py:40
#, python-format
msgid "No product is matching your request. Now you will starve to death."
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
msgid "Not Received"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__note
msgid "Note"
msgstr "Napomena"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_line_action_by_supplier
msgid "Nothing to order today"
msgstr ""

#. module: lunch
#: code:addons/lunch/models/lunch.py:318
#, python-format
msgid "Only your lunch manager cancels the orders."
msgstr ""

#. module: lunch
#: code:addons/lunch/models/lunch.py:287
#, python-format
msgid "Only your lunch manager processes the orders."
msgstr ""

#. module: lunch
#: code:addons/lunch/models/lunch.py:307
#, python-format
msgid "Only your lunch manager sets the orders as received."
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__order_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__order_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.report_lunch_order
#: selection:lunch.cashmove,state:0
msgid "Order"
msgstr "Nalog"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
msgid "Order Date"
msgstr "Datum Zahteva"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_tree
msgid "Order lines Tree"
msgstr ""

#. module: lunch
#: selection:lunch.order.line,state:0
msgid "Ordered"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Orders Form"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Orders Tree"
msgstr ""

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_line_action_by_supplier
#: model:ir.ui.menu,name:lunch.lunch_order_line_menu_control_suppliers
msgid "Orders by Vendor"
msgstr ""

#. module: lunch
#: model:mail.template,subject:lunch.lunch_order_mail_supplier
msgid "Orders for ${ctx['order']['company_name']}"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
#: selection:lunch.cashmove,state:0
msgid "Payment"
msgstr "Isplata"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_payment
msgid ""
"Payments are used to register liquidity movements. You can process those "
"payments by your own means or by using installed facilities."
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__previous_order_ids
msgid "Previous Order"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__previous_order_widget
msgid "Previous Order Widget"
msgstr ""

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_order_menu_tree
msgid "Previous Orders"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__price
#: model:ir.model.fields,field_description:lunch.field_lunch_product__price
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_tree
msgid "Price"
msgstr "Cena"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_product_kanban
msgid "Price:"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__product_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line_lucky__product_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__name
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
msgid "Product"
msgstr "Proizvod"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_category_action
#: model:ir.ui.menu,name:lunch.lunch_product_category_menu
msgid "Product Categories"
msgstr "Kategorije proizvoda"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__category_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__category_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__name
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Product Category"
msgstr "Kategorija proizvoda"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_form
msgid "Product Category:"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__name
msgid "Product Name"
msgstr "Ime Proizvoda"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Product Search"
msgstr ""

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_action
#: model:ir.model.fields,field_description:lunch.field_lunch_order__order_line_ids
#: model:ir.ui.menu,name:lunch.lunch_product_menu
msgid "Products"
msgstr "Proizvod"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_form
msgid "Products Form"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_tree
msgid "Products Tree"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid ""
"Products from this vendor can <strong>not</strong> be ordered at that "
"moment."
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "Products from this vendor can be ordered at that moment."
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_tree
msgid "Receive"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
#: selection:lunch.order,state:0 selection:lunch.order.line,state:0
msgid "Received"
msgstr "Primljeno"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__alert_type
msgid "Recurrence"
msgstr ""

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_action_payment
msgid "Register Cash Moves"
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_payment
msgid "Register a payment"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__saturday
msgid "Saturday"
msgstr "Subota"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "Schedule"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
msgid "Search"
msgstr "Pronađi"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch.xml:5
#, python-format
msgid "Select a product and put your order comments on the note."
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Select your order"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_order_line_lucky
msgid "Select your vendor"
msgstr ""

#. module: lunch
#: selection:lunch.alert,alert_type:0
msgid "Specific Day"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__state
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__state
msgid "Status"
msgstr "Status"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_line_action_control_suppliers
msgid "Summary of all lunch orders, grouped by vendor and by date."
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__sunday
msgid "Sunday"
msgstr "Nedelja"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_order__company_id
msgid "The company this user is currently working for."
msgstr "Preduzeće ovog korisnika"

#. module: lunch
#: code:addons/lunch/models/lunch.py:130
#, python-format
msgid "The date of your order is in the past."
msgstr ""

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_line_action
msgid ""
"There is no previous order recorded. Click on \"My Lunch\" and then create a"
" new lunch order."
msgstr ""

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch.xml:4
#, python-format
msgid "This is the first time you order a meal"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__thursday
msgid "Thursday"
msgstr "Četvrtak"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_order_line_menu_by_supplier
msgid "Today's Orders"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__total
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Total"
msgstr "Ukupno"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__tuesday
msgid "Tuesday"
msgstr "Utorak"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.report_lunch_order
msgid "Unit Price"
msgstr "Jedinična cijena"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__user_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__user_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__user_id
#: model:res.groups,name:lunch.group_lunch_user
msgid "User"
msgstr "Korisnik"

#. module: lunch
#: code:addons/lunch/models/lunch.py:234
#, python-format
msgid ""
"Validate order for one company at a time to send emails (mixed orders from "
"%s and %s)"
msgstr ""

#. module: lunch
#: code:addons/lunch/models/lunch.py:239
#, python-format
msgid ""
"Validate order for one currency at a time to send emails (mixed orders from "
"%s and %s)"
msgstr ""

#. module: lunch
#: code:addons/lunch/models/lunch.py:229
#, python-format
msgid ""
"Validate order for one supplier at a time to send emails (mixed orders from "
"%s and %s)"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__partner_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line__supplier
#: model:ir.model.fields,field_description:lunch.field_lunch_order_line_lucky__supplier_ids
#: model:ir.model.fields,field_description:lunch.field_lunch_product__supplier
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Vendor"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_line_view_search
msgid "Vendor Orders by Date"
msgstr ""

#. module: lunch
#: code:addons/lunch/models/lunch.py:192
#, python-format
msgid "Vendor(s) '%s' is not available today"
msgstr ""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__wednesday
msgid "Wednesday"
msgstr "Sreda"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "Write the message you want to display during the defined period..."
msgstr ""

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_action_account
msgid "Your Account"
msgstr ""

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_cashmove_menu_form
msgid "Your Lunch Account"
msgstr ""

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_line_action
msgid "Your Orders"
msgstr ""

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch.xml:6
#, python-format
msgid "Your favorite meals will be created based on your last orders."
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_form
msgid "cashmove form"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree_2
msgid "cashmove tree"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search_2
msgid "lunch cashmove"
msgstr ""

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "lunch employee payment"
msgstr ""
