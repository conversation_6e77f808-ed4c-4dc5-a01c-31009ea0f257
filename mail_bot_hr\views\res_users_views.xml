<?xml version="1.0" ?>
<odoo>
    <data>
        <record id="res_users_view_form_simple_modif" model="ir.ui.view">
            <field name="name">res.users.preferences.form.simplified.inherit</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="hr.res_users_view_form_simple_modif" />
            <field name="priority">15</field>
            <field name="arch" type="xml">
                <widget name="notification_alert" position="replace" />
            </field>
        </record>

        <record id="res_users_view_form_preferences" model="ir.ui.view">
            <field name="name">res.users.preferences.form.inherit</field>
            <field name="model">res.users</field>
            <field name="inherit_id"
                   ref="mail_bot.res_users_view_form_preferences" />
            <field name="arch" type="xml">
                <field name="odoobot_state" position="before">
                    <field name="can_edit" invisible="1"/>
                </field>
                <xpath expr="//field[@name='odoobot_state']" position="attributes">
                    <attribute name="attrs">{'readonly': [('can_edit', '=', False)]}</attribute>
                </xpath>
            </field>
        </record>

        <record id="res_users_view_form_profile" model="ir.ui.view">
            <field name="name">res.users.profile.form.inherit</field>
            <field name="model">res.users</field>
            <field name="inherit_id"
                   ref="hr.res_users_view_form_profile" />
            <field name="arch" type="xml">
                <sheet position="before">
                    <widget name="notification_alert" class="mb-0" />
                </sheet>
            </field>
        </record>
    </data>
</odoo>
