# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_lu
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-06-19 13:53+0000\n"
"PO-Revision-Date: 2019-08-30 08:50+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VP-IC-EX
msgid " EX-IC-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_V-ART-43_60b
msgid "0-E-Art.43&60b"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_V-ART-44_56q
msgid "0-E-Art.44&56q"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-PA-0
msgid "0-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-PA-0
msgid "0-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-ECP-0
msgid "0-EC(P)-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-EC-0
msgid "0-EC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-EC-0
msgid "0-EC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-EC-0
msgid "0-EC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-EC-0
msgid "0-EC-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VP-EC-0
msgid "0-EC-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-EC-Tab
msgid "0-EC-ST-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-IC-0
msgid "0-IC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-IC-0
msgid "0-IC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-IC-0
msgid "0-IC-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VP-IC-0
msgid "0-IC-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-IC-Tab
msgid "0-IC-ST-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VB-TR-0
msgid "0-ICT-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-PA-0
msgid "0-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-PA-0
msgid "0-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-PA-0
msgid "0-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-PA-0
msgid "0-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_SANS
msgid "0-P-Tax-Free"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-PA-0
msgid "0-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VP-PA-0
msgid "0-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_SANS_sale
msgid "0-S-Tax-Free"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-PA-Tab
msgid "0-ST-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_overall_turnover
msgid "012 - Overall turnover"
msgstr "012 - Gesamtumsatz"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_2_export
msgid "014"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_2_export
msgid "014 - Exports"
msgstr "014 - Ausfuhren"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_3_other_exemptions_art_43
msgid "015"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_3_other_exemptions_art_43
msgid "015 - Other exemptions"
msgstr "015 - Andere Befreiungen"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_4_other_exemptions_art_44_et_56quater
msgid "016"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_4_other_exemptions_art_44_et_56quater
msgid "016 - Other exemptions"
msgstr "016 - Andere Befreiungen"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_5_manufactured_tobacco_vat_collected
msgid "017"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_5_manufactured_tobacco_vat_collected
msgid ""
"017 - Manufactured tobacco whose VAT was collected at the source or at the "
"exit of the tax..."
msgstr ""
"017 - Tabakwaren, deren Mehrwertsteuer an der Quelle oder am Ausgang des "
"Steuerlagers gemeinsam mit den Verbrauchsteuern erhoben wurde"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_a_subsequent_to_intra_community
msgid "018"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_6_a_subsequent_to_intra_community
msgid ""
"018 - Supply, subsequent to intra-Community acquisitions of goods, in the "
"context of triangular transactions, when the customer identified,..."
msgstr ""
"018 - An innergemeinschaftliche Erwerbe anschließende Lieferungen im Rahmen von "
"Dreiecksgeschäften..."

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_d_supplies_other_referred
msgid "019"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_6_d_supplies_other_referred
msgid "019 - Supplies other than referred to in 018 and 423 or 424"
msgstr "019 - Andere im Ausland getätigte (steuerpflichtige) Umsätze"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_exemptions_deductible_amounts
msgid "021 - Exemptions and deductible amounts"
msgstr "021 - Steuerbefreiungen und abzugsfähige Beträge"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1c_taxable_turnover
msgid "022 - Taxable turnover"
msgstr "022 - Steuerpflichtiger Umsatz"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_3
msgid "031"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_3
msgid "031 - base 3%"
msgstr "031 - Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_0
msgid "033"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_0
msgid "033 - base 0%"
msgstr "033 - Besteuerungsgrundlage 0%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_breakdown_taxable_turnover_base
msgid "037 - Breakdown of taxable turnover – base"
msgstr "037 - Steuerpflichtiger Umsatz: Aufteilung - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_3
msgid "040"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_3
msgid "040 - tax 3%"
msgstr "040 - MwSt. 3%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_breakdown_taxable_turnover_tax
msgid "046 - Breakdown of taxable turnover – tax"
msgstr "046 - Steuerpflichtiger Umsatz: Aufteilung - MwSt."

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_3
msgid "049"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_3
msgid "049 - base 3%"
msgstr "049 - Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_intra_community_acqui_of_goods_base
msgid "051 - Intra-Community acquisitions of goods – base"
msgstr "051 - Innergemeinschaftliche Erwerbe von Gegenständen - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_3
msgid "054"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_tax_3
msgid "054 - tax 3%"
msgstr "054 - MwSt. 3%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_intra_community_acquisitions_goods_tax
msgid "056 - Intra-Community acquisitions of goods – tax"
msgstr "056 - Innergemeinschaftliche Erwerbe von Gegenständen - MwSt."

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_3
msgid "059"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_3
msgid "059 - for business purposes: base 3%"
msgstr "059 - für Zwecke des Unternehmens: Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_3
msgid "063"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_3
msgid "063 - for non-business purposes: base 3%"
msgstr "063 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_importation_of_goods_base
msgid "065 - Importation of goods – base"
msgstr "065 - Einfuhren von Gegenständen - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_3
msgid "068"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_3
msgid "068 - for business purposes: tax 3%"
msgstr "068 - für Zwecke des Unternehmens: MwSt. 3%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_3
msgid "073"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_3
msgid "073 - for non-business purposes: tax 3%"
msgstr "073 - für unternehmensfremde Zwecke: MwSt. 3%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2h_total_tax_due
msgid "076 - Total tax due"
msgstr "076 - Gesamtbetrag der Steuer"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_4_due_respect_application_goods
msgid "090"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_4_due_respect_application_goods
msgid "090 - Due in respect of the application of goods for business purposes"
msgstr ""
"090 - Erklärte Mehrwertsteuer für die Zuordnung von Gegenständen zu Zwecken "
"des Unternehmens"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_6_paid_joint_several_guarantee
msgid "092"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_6_paid_joint_several_guarantee
msgid "092 - Paid as joint and several guarantee"
msgstr "092 - Als solidarisch haftender Bürge bezahlte MwSt."

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_total_input_tax
msgid "093 - Total input tax"
msgstr "093 - Gesamtbetrag Vorsteuer"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3b1_rel_trans
msgid ""
"094 - relating to transactions which are exempt pursuant to articles 44 and "
"56quater"
msgstr ""
"094 - Nicht abziehbare Vorsteuer betreffend die gemäß Art. 44 und Art. 56quater "
"steuerfreien Umsätze"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3b2_ded_prop
msgid ""
"095 - where the deductible proportion determined in accordance to article 50 "
"is applied"
msgstr ""
"095 - Nicht abziehbare Vorsteuer in Anwendung der in Art. 50 vorgesehenen "
"Prorata-Regel"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3b2_input_tax_margin
msgid "096"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3b2_input_tax_margin
msgid ""
"096 - Non recoverable input tax in accordance with Art. 56ter-1(7) and "
"56ter-2(7) (when applying the margin scheme)"
msgstr "096 - Nicht abziehbare Vorsteuer in Anwendung von Art. 56ter-1/7 und 56ter-2/7 "

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3b_total_input_tax_nd
msgid "097 - Total input tax non-deductible"
msgstr "097 - Gesamtbetrag der nicht abziehbaren Vorsteuer"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3c_total_input_tax_deductible
msgid "102 - Total input tax deductible"
msgstr "102 - Gesamtbetrag der abziehbaren Vorsteuer"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_4a_total_tax_due
msgid "103 - Total tax due"
msgstr "103 - Gesamtbetrag der Steuer"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_4a_total_input_tax_deductible
msgid "104 - Total input tax deductible"
msgstr "104 - Gesamtbetrag der abziehbaren Vorsteuer"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_4c_exceeding_amount
msgid "105 - Exceeding amount"
msgstr "105 - Überschuss"

#. module: l10n_lu
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-ECP-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-ECP-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-ECP-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_VB-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_VP-PA-14
msgid "14%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-PA-14
msgid "14-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-PA-14
msgid "14-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-ECP-14
msgid "14-EC(P)-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-ECP-14
msgid "14-EC(P)-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-ECP-14
msgid "14-EC(P)-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-EC-14
msgid "14-EC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-EC-14
msgid "14-EC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-EC-14
msgid "14-EC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-EC-14
msgid "14-EC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-EC-14
msgid "14-EC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-EC-14
msgid "14-EC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-IC-14
msgid "14-IC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-IC-14
msgid "14-IC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-IC-14
msgid "14-IC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-IC-14
msgid "14-IC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-IC-14
msgid "14-IC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-IC-14
msgid "14-IC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-PA-14
msgid "14-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-PA-14
msgid "14-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-PA-14
msgid "14-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-PA-14
msgid "14-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VB-PA-14
msgid "14-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VP-PA-14
msgid "14-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2c_acquisitions_triangular_transactions_base
msgid "152"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2c_acquisitions_triangular_transactions_base
msgid "152 - Acquisitions, in the context of triangular transactions – base"
msgstr "152 - Im Rahmen von Dreiecksgeschäften getätigte Erwerbe - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-ECP-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-ECP-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-ECP-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_VB-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_VP-PA-17
msgid "17%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-PA-17
msgid "17-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-PA-17
msgid "17-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-ECP-17
msgid "17-EC(P)-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-ECP-17
msgid "17-EC(P)-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-ECP-17
msgid "17-EC(P)-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-EC-17
msgid "17-EC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-EC-17
msgid "17-EC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-EC-17
msgid "17-EC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-EC-17
msgid "17-EC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-EC-17
msgid "17-EC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-EC-17
msgid "17-EC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-IC-17
msgid "17-IC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-IC-17
msgid "17-IC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-IC-17
msgid "17-IC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-IC-17
msgid "17-IC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-IC-17
msgid "17-IC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-IC-17
msgid "17-IC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-PA-17
msgid "17-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-PA-17
msgid "17-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-PA-17
msgid "17-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-PA-17
msgid "17-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VB-PA-17
msgid "17-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VP-PA-17
msgid "17-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_exempt
msgid "194"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_exempt
msgid "194 - base exempt"
msgstr "194 - Besteuerungsgrundlage steuerbefreit"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_exempt
msgid "195"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_exempt
msgid "195 - for business purposes: base exempt"
msgstr "195 - für Zwecke des Unternehmens: Besteuerungsgrundlage steuerbefreit"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_exempt
msgid "196"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_exempt
msgid "196 - for non-business purposes: base exempt"
msgstr "196 - für unternehmensfremde Zwecke: Besteuerungsgrundlage steuerbefreit"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_c_supplies_scope_special_arrangement
msgid "226"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_6_c_supplies_scope_special_arrangement
msgid ""
"226 - Supplies carried out within the scope of the special arrangement of "
"art. 56sexies"
msgstr "226 - Im Rahmen der Sonderregelung von Artikel 56sexies getätigte Umsätze"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2g_special_arrangement
msgid "227"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2g_special_arrangement
msgid "227 - Special arrangement for tax suspension: adjustment"
msgstr "227 - Sonderregelung zur Steueraussetzung: Berichtigung"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_7_adjusted_tax_special_arrangement
msgid "228"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_7_adjusted_tax_special_arrangement
msgid "228 - Adjusted tax - special arrangement for tax suspension"
msgstr "228 - Berichtigte Steuer - Sonderregelung zur Steueraussetzung"

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-PA-3
msgid "3-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-PA-3
msgid "3-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-ECP-3
msgid "3-EC(P)-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-ECP-3
msgid "3-EC(P)-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-ECP-3
msgid "3-EC(P)-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-EC-3
msgid "3-EC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-EC-3
msgid "3-EC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-EC-3
msgid "3-EC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-EC-3
msgid "3-EC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-EC-3
msgid "3-EC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-EC-3
msgid "3-EC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-IC-3
msgid "3-IC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-IC-3
msgid "3-IC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-IC-3
msgid "3-IC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-IC-3
msgid "3-IC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-IC-3
msgid "3-IC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-IC-3
msgid "3-IC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-PA-3
msgid "3-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-PA-3
msgid "3-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-PA-3
msgid "3-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-PA-3
msgid "3-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-PA-3
msgid "3-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VP-PA-3
msgid "3-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_importation_of_goods_tax
msgid "407 - Importation of goods – tax"
msgstr "407 - Einfuhren von Gegenständen - MwSt."

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_supply_of_service_for_customer
msgid ""
"409 - Supply of services for which the customer is liable for the payment of "
"VAT – base"
msgstr ""
"409 - Vom Empfänger als Steuerschuldner zu erklärende Dienstleistungen "
" - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_supply_of_service_for_customer_liable_for_payment_tax
msgid ""
"410 - Supply of services for which the customer is liable for the payment of "
"VAT – tax"
msgstr ""
"410 - Vom Empfänger als Steuerschuldner zu erklärende Dienstleistungen "
" - MwSt."

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_7_inland_supplies_for_customer
msgid "419"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_7_inland_supplies_for_customer
msgid ""
"419 - Inland supplies for which the customer is liable for the payment of VAT"
msgstr ""
"419 - Umsätze im Inland, für die der Empfänger Steuerschuldner ist"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_b1_non_exempt_customer_vat
msgid "423"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_6_b1_non_exempt_customer_vat
msgid ""
"423 - not exempt in the MS where the customer is liable for payment of VAT"
msgstr ""
"423 - Dienstleistungen, die im Mitgliedstaat des Empfängers, der dort für "
"Zwecke der MwSt. erfasst und Steuerschuldner ist, nicht steuerbefreit sind"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_b2_exempt_ms_customer
msgid "424"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_6_b2_exempt_ms_customer
msgid "424 - exempt in the MS where the customer is identified"
msgstr ""
"424 - Dienstleistungen, die im Mitgliedstaat des Empfängers, "
"der dort für Zwecke der MwSt. erfasst ist, steuerbefreit sind"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_3
msgid "431"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_3
msgid "431 - not exempt within the territory: base 3%"
msgstr "431 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_3
msgid "432"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_3
msgid "432 - not exempt within the territory: tax 3%"
msgstr "432 - nicht steuerbefreit im Inland: MwSt. 3%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_b_exempt
msgid "435"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_b_exempt
msgid "435 - exempt within the territory: exempt"
msgstr "435 - steuerbefreit im Inland: steuerbefreit"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_base
msgid "436 - base"
msgstr "436 - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_3
msgid "441"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_3
msgid "441 - not established or residing within the Community: base 3%"
msgstr ""
"441 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: Besteuerungsgrundlage 3%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_3
msgid "442"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_3
msgid "442 - not established or residing within the Community: tax 3%"
msgstr ""
"442 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: MwSt. 3%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_exempt
msgid "445"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_exempt
msgid "445 - not established or residing within the Community: exempt"
msgstr ""
"445 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: steuerbefreit"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_total_sale
msgid "454 - Total Sales / Receipts"
msgstr "454 - Gesamtbetrag der Entgelte"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1a_app_goods_non_bus
msgid "455"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_app_goods_non_bus
msgid ""
"455 - Application of goods for non-business use and for business purposes"
msgstr ""
"455 - Entnahmen von Gegenständen für Zwecke des Unternehmens und für "
"unternehmensfremde Zwecke"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1a_non_bus_gs
msgid "456"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_non_bus_gs
msgid "456 - Non-business use of goods and supply of services free of charge"
msgstr "456 - Erbringung von Dienstleistungen für unternehmensfremde Zwecke"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_1_intra_community_goods_pi_vat
msgid "457"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_1_intra_community_goods_pi_vat
msgid ""
"457 - Intra-Community supply of goods to persons identified for VAT purposes "
"in another Member State (MS)"
msgstr ""
"457 - Innergemeinschaftliche Lieferungen an Personen, die eine Id.-Nummer in "
"einem anderen Mitgliedstaat besitzen"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_1_invoiced_by_other_taxable_person
msgid "458"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_1_invoiced_by_other_taxable_person
msgid "458 - Invoiced by other taxable persons for goods or services supplied"
msgstr "458 - Von anderen Steuerpflichtigen für Warenlieferungen und "
"Dienstleistungen in Rechnung gestellte Mehrwertsteuer"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_2_due_respect_intra_comm_goods
msgid "459"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_2_due_respect_intra_comm_goods
msgid "459 - Due in respect of intra-Community acquisitions of goods"
msgstr ""
"459 - Erklärte oder bezahlte Mehrwertsteuer für innergemeinschaftliche "
"Erwerbe von Gegenständen"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_3_due_paid_respect_importation_goods
msgid "460"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_3_due_paid_respect_importation_goods
msgid "460 - Due or paid in respect of importation of goods"
msgstr "460 - Erklärte oder bezahlte Mehrwertsteuer für eingeführte Waren"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_5_due_under_reverse_charge
msgid "461"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_5_due_under_reverse_charge
msgid "461 - Due under the reverse charge (see points II.E and F)"
msgstr "461 - Als Schuldner erklärte Mehrwertsteuer (Siehe Punkte II.E und F)"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax
msgid "462 - tax"
msgstr "462 - MwSt."

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base
msgid "463 - base"
msgstr "463 - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax
msgid "464 - tax"
msgstr "464 - MwSt."

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1a_telecom_service
msgid "471"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_telecom_service
msgid ""
"471 - Telecommunications services, radio and television broadcasting "
"services..."
msgstr "471 - Telekommunikationsdienstleistungen, Rundfunk- und Fernsehdienstleistungen..."

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_other_sales
msgid "472 - Other sales / receipts"
msgstr "472 - Andere Umsätze / Erträge"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_17
msgid "701"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_17
msgid "701 - base 17%"
msgstr "701 - Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_17
msgid "702"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_17
msgid "702 - tax 17%"
msgstr "702 - MwSt. 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_14
msgid "703"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_14
msgid "703 - base 14%"
msgstr "703 - Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_14
msgid "704"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_14
msgid "704 - tax 14%"
msgstr "704 - MwSt. 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_8
msgid "705"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_8
msgid "705 - base 8%"
msgstr "705 - Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_8
msgid "706"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_8
msgid "706 - tax 8%"
msgstr "706 - MwSt. 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_17
msgid "711"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_17
msgid "711 - base 17%"
msgstr "711 - Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_17
msgid "712"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_tax_17
msgid "712 - tax 17%"
msgstr "712 - MwSt. 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_14
msgid "713"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_14
msgid "713 - base 14%"
msgstr "713 - Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_14
msgid "714"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_tax_14
msgid "714 - tax 14%"
msgstr "714 - MwSt. 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_8
msgid "715"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_8
msgid "715 - base 8%"
msgstr "715 - Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_8
msgid "716"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_tax_8
msgid "716 - tax 8%"
msgstr "716 - MwSt. 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_manufactured_tobacco
msgid "719"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_manufactured_tobacco
msgid ""
"719 - of manufactured tobacco (VAT is collected at the exit of the tax "
"warehouse with excise duties)"
msgstr ""
"719 - von Tabakwaren, deren Mehrwertsteuer am Ausgang des Steuerlagers "
"gemeinsam mit den Verbrauchsteuern erhoben wird"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_17
msgid "721"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_17
msgid "721 - for business purposes: base 17%"
msgstr "721 - für Zwecke des Unternehmens: Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_17
msgid "722"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_17
msgid "722 - for business purposes: tax 17%"
msgstr "722 - für Zwecke des Unternehmens: MwSt. 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_14
msgid "723"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_14
msgid "723 - for business purposes: base 14%"
msgstr "723 - für Zwecke des Unternehmens: Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_14
msgid "724"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_14
msgid "724 - for business purposes: tax 14%"
msgstr "724 - für Zwecke des Unternehmens: MwSt. 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_8
msgid "725"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_8
msgid "725 - for business purposes: base 8%"
msgstr "725 - für Zwecke des Unternehmens: Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_8
msgid "726"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_8
msgid "726 - for business purposes: tax 8%"
msgstr "726 - für Zwecke des Unternehmens: MwSt. 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_manufactured_tobacco
msgid "729"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_manufactured_tobacco
msgid ""
"729 - of manufactured tobacco (VAT is collected at the exit of the tax "
"warehouse with excise duties)"
msgstr ""
"729 - von Tabakwaren, deren Mehrwertsteuer am Ausgang des Steuerlagers "
"gemeinsam mit den Verbrauchsteuern erhoben wird"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_17
msgid "731"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_17
msgid "731 - for non-business purposes: base 17%"
msgstr "731 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_17
msgid "732"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_17
msgid "732 - for non-business purposes: tax 17%"
msgstr "732 - für unternehmensfremde Zwecke: MwSt. 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_14
msgid "733"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_14
msgid "733 - for non-business purposes: base 14%"
msgstr "733 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_14
msgid "734"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_14
msgid "734 - for non-business purposes: tax 14%"
msgstr "734 - für unternehmensfremde Zwecke: MwSt. 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_8
msgid "735"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_8
msgid "735 - for non-business purposes: base 8%"
msgstr "735 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_8
msgid "736"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_8
msgid "736 - for non-business purposes: tax 8%"
msgstr "736 - für unternehmensfremde Zwecke: MwSt. 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_17
msgid "741"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_17
msgid "741 - not exempt within the territory: base 17%"
msgstr "741 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_17
msgid "742"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_17
msgid "742 - not exempt within the territory: tax 17%"
msgstr "742 - nicht steuerbefreit im Inland: MwSt. 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_14
msgid "743"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_14
msgid "743 - not exempt within the territory: base 14%"
msgstr "743 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_14
msgid "744"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_14
msgid "744 - not exempt within the territory: tax 14%"
msgstr "744 - nicht steuerbefreit im Inland: MwSt. 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_8
msgid "745"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_8
msgid "745 - not exempt within the territory: base 8%"
msgstr "745 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_8
msgid "746"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_8
msgid "746 - not exempt within the territory: tax 8%"
msgstr "746 - nicht steuerbefreit im Inland: MwSt. 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_17
msgid "751"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_17
msgid "751 - not established or residing within the Community: base 17%"
msgstr ""
"751 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_17
msgid "752"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_17
msgid "752 - not established or residing within the Community: tax 17%"
msgstr ""
"752 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: MwSt. 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_14
msgid "753"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_14
msgid "753 - not established or residing within the Community: base 14%"
msgstr ""
"753 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: Besteuerungsgrundlage 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_14
msgid "754"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_14
msgid "754 - not established or residing within the Community: tax 14%"
msgstr ""
"754 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: MwSt. 14%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_8
msgid "755"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_8
msgid "755 - not established or residing within the Community: base 8%"
msgstr ""
"755 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_8
msgid "756"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_8
msgid "756 - not established or residing within the Community: tax 8%"
msgstr "756 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: MwSt. 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_3_base_17
msgid "761"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_3_base_17
msgid "761 - suppliers established within the territory: base 17%"
msgstr ""
"761 - erbracht an den Erklärenden von im Inland ansässigen Steuerpflichtigen: "
"Besteuerungsgrundlage 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_3_tax_17
msgid "762"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_3_tax_17
msgid "762 - suppliers established within the territory: tax 17%"
msgstr "762 - erbracht an den Erklärenden von im Inland ansässigen "
"Steuerpflichtigen: MwSt. 17%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2f_supply_goods_base_8
msgid "763"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base_8
msgid "763 - base 8%"
msgstr "763 - Besteuerungsgrundlage 8%"

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2f_supply_goods_tax_8
msgid "764"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax_8
msgid "764 - tax 8%"
msgstr "764 - MwSt. 8%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_3_base
msgid "765 - base"
msgstr "765 - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_3_tax
msgid "766 - tax"
msgstr "766 - MwSt."

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base
msgid ""
"767 - Supply of goods for which the purchaser is liable for the payment of "
"VAT - base"
msgstr ""
"767 -Vom Erwerber als Steuerschuldner zu erklärende Lieferungen - Besteuerungsgrundlage"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax
msgid ""
"768 - Supply of goods for which the purchaser is liable for the payment of "
"VAT - tax"
msgstr ""
"768 -Vom Erwerber als Steuerschuldner zu erklärende Lieferungen - MwSt."

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-PA-8
msgid "8-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-PA-8
msgid "8-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-ECP-8
msgid "8-EC(P)-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-ECP-8
msgid "8-EC(P)-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-ECP-8
msgid "8-EC(P)-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-EC-8
msgid "8-EC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-EC-8
msgid "8-EC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-EC-8
msgid "8-EC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-EC-8
msgid "8-EC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-EC-8
msgid "8-EC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-EC-8
msgid "8-EC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-IC-8
msgid "8-IC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-IC-8
msgid "8-IC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-IC-8
msgid "8-IC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-IC-8
msgid "8-IC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-IC-8
msgid "8-IC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-IC-8
msgid "8-IC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-PA-8
msgid "8-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-PA-8
msgid "8-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-PA-8
msgid "8-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-PA-8
msgid "8-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VB-PA-8
msgid "8-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VP-PA-8
msgid "8-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_16
msgid "921 - for business purposes: base 16%"
msgstr "921 - für Zwecke des Unternehmens: Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_13
msgid "923 - for business purposes: base 13%"
msgstr "923 - für Zwecke des Unternehmens: Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_7
msgid "925 - for business purposes: base 7%"
msgstr "925 - für Zwecke des Unternehmens: Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_16
msgid "931 - for non-business purposes: base 16%"
msgstr "931 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_13
msgid "933 - for non-business purposes: base 13%"
msgstr "933 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_7
msgid "935 - for non-business purposes: base 7%"
msgstr "935 - für unternehmensfremde Zwecke: Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_16
msgid "941 - not exempt within the territory: base 16%"
msgstr "941 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_13
msgid "943 - not exempt within the territory: base 13%"
msgstr "943 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_7
msgid "945 - not exempt within the territory: base 7%"
msgstr "945 - nicht steuerbefreit im Inland: Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_16
msgid "951 - not established or residing within the Community: base 16%"
msgstr ""
"951 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_13
msgid "953 - not established or residing within the Community: base 13%"
msgstr ""
"953 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_7
msgid "955 - not established or residing within the Community: base 7%"
msgstr ""
"955 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_3_base_16
msgid "961 - suppliers established within the territory: base 16%"
msgstr ""
"961 - erbracht an den Erklärenden von im Inland ansässigen Steuerpflichtigen: "
"Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base_7
msgid "963 - base 7%"
msgstr "963 - Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax_7
msgid "964 - tax 7%"
msgstr "964 - MwSt. 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_16
msgid "901 - base 16%"
msgstr "901 - Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_13
msgid "903 - base 13%"
msgstr "903 - Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_7
msgid "905 - base 7%"
msgstr "905 - Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_16
msgid "902 - tax 16%"
msgstr "902 - MwSt. 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_13
msgid "904 - tax 13%"
msgstr "904 - MwSt. 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_7
msgid "906 - tax 7%"
msgstr "906 - MwSt. 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_16
msgid "911 - base 16%"
msgstr "911 - Besteuerungsgrundlage 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_13
msgid "913 - base 13%"
msgstr "913 - Besteuerungsgrundlage 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_7
msgid "915 - base 7%"
msgstr "915 - Besteuerungsgrundlage 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_tax_16
msgid "912 - tax 16%"
msgstr "912 - MwSt. 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_tax_13
msgid "914 - tax 13%"
msgstr "914 - MwSt. 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_tax_7
msgid "916 - tax 7%"
msgstr "916 - MwSt. 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_16
msgid "922 - for business purposes: tax 16%"
msgstr "922 - für Zwecke des Unternehmens: MwSt. 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_13
msgid "924 - for business purposes: tax 13%"
msgstr "924 - für Zwecke des Unternehmens: MwSt. 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_7
msgid "926 - for business purposes: tax 7%"
msgstr "926 - für Zwecke des Unternehmens: MwSt. 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_16
msgid "932 - for non-business purposes: tax 16%"
msgstr "932 - für unternehmensfremde Zwecke: MwSt. 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_13
msgid "934 - for non-business purposes: tax 13%"
msgstr "934 - für unternehmensfremde Zwecke: MwSt. 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_7
msgid "936 - for non-business purposes: tax 7%"
msgstr "936 - für unternehmensfremde Zwecke: MwSt. 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_16
msgid "942 - not exempt within the territory: tax 16%"
msgstr "942 - nicht steuerbefreit im Inland: MwSt. 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_13
msgid "944 - not exempt within the territory: tax 13%"
msgstr "944 - nicht steuerbefreit im Inland: MwSt. 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_7
msgid "946 - not exempt within the territory: tax 7%"
msgstr "946 - nicht steuerbefreit im Inland: MwSt. 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_16
msgid "952 - not established or residing within the Community: tax 16%"
msgstr ""
"952 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: MwSt. 16%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_13
msgid "954 - not established or residing within the Community: tax 13%"
msgstr ""
"954 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: MwSt. 13%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_7
msgid "956 - not established or residing within the Community: tax 7%"
msgstr ""
"956 - erbracht an den Erklärenden von Steuerpflichtigen, die nicht "
"in der Gemeinschaft ansässig sind: MwSt. 7%"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_3_tax_16
msgid "962 - suppliers established within the territory: tax 16%"
msgstr "962 - erbracht an den Erklärenden von im Inland ansässigen "
"Steuerpflichtigen: MwSt. 16%"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_46128
#: model:account.group.template,name:l10n_lu.account_group_46128
msgid "ACD - Other amounts payable"
msgstr "ACD - Sonstige Verbindlichkeiten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_42148
#: model:account.group.template,name:l10n_lu.account_group_42148
msgid "ACD - Other amounts receivable"
msgstr "ACD - Sonstige Verbindlichkeiten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_46148
#: model:account.group.template,name:l10n_lu.account_group_46148
msgid "AED - Other debts"
msgstr ""
"Steuerverwaltung - Indirekte Steuern (AED) - Sonstige Verbindlichkeiten"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_635
msgid "AVA and FVA on receivables from current assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65112
#: model:account.group.template,name:l10n_lu.account_group_65112
msgid "AVA on amounts owed by affiliated undertakings"
msgstr "ZWb von Forderungen gegen verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6352
#: model:account.group.template,name:l10n_lu.account_group_6352
msgid ""
"AVA on amounts owed by affiliated undertakings and undertakings with which "
"the undertaking is linked by virtue of participating interests"
msgstr ""
"ZWb von Forderungen gegen verbundene Unternehmen und Unternehmen, mit denen "
"ein Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65114
#: model:account.group.template,name:l10n_lu.account_group_65114
msgid ""
"AVA on amounts owed by undertakings with which the undertaking is linked by "
"virtue of participating interests"
msgstr ""
"ZWb von Forderungen gegen Unternehmen, mit denen ein Beteiligungsverhältnis "
"besteht"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_63313
#: model:account.group.template,name:l10n_lu.account_group_63313
msgid "AVA on buildings"
msgstr "ZWb von Bauten / Gebäuden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6322
#: model:account.group.template,name:l10n_lu.account_group_6322
msgid ""
"AVA on concessions, patents, licences, trademarks and similar rights and "
"assets"
msgstr ""
"ZWb von Konzessionen, Patenten, Lizenzen, Warenzeichen und vergleichbaren "
"Rechten und Werten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6321
#: model:account.group.template,name:l10n_lu.account_group_6321
msgid "AVA on development costs"
msgstr "ZWb von Entwicklungskosten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6324
#: model:account.group.template,name:l10n_lu.account_group_6324
msgid "AVA on down payments and intangible fixed assets under development"
msgstr ""
"ZWb von geleisteten Anzahlungen und immateriellen Vermögensgegenständen in "
"Entwicklung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6334
#: model:account.group.template,name:l10n_lu.account_group_6334
msgid "AVA on down payments and tangible fixed assets under development"
msgstr "ZWb von geleisteten Anzahlungen und Anlagen im Bau"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6345
#: model:account.group.template,name:l10n_lu.account_group_6345
msgid "AVA on down payments on inventories"
msgstr "ZWb von geleisteten Anzahlungen auf Vorräte"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6313
#: model:account.group.template,name:l10n_lu.account_group_6313
msgid ""
"AVA on expenses for capital increases and various operations (mergers, "
"demergers, changes of legal form)"
msgstr ""
"ZWb von Kosten für Kapitalerhöhung und anderen Vorgängen (Verschmelzungen, "
"Spaltungen und Umwandlungen)"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6511
msgid "AVA on financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_63314
#: model:account.group.template,name:l10n_lu.account_group_63314
msgid "AVA on fixtures and fittings-out of buildings"
msgstr "ZWb von Einrichtungen von Bauten/Gebäuden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_63312
#: model:account.group.template,name:l10n_lu.account_group_63312
msgid "AVA on fixtures and fittings-out of land"
msgstr "ZWb von Erschließungen von Grundstücken"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_631
msgid "AVA on formation expenses and similar expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6323
#: model:account.group.template,name:l10n_lu.account_group_6323
msgid "AVA on goodwill acquired for consideration"
msgstr ""
"ZWb vom Geschäfts- oder Firmenwert, soweit er entgeltlich erworben wurde"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_632
msgid "AVA on intangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_634
msgid "AVA on inventories"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6343
#: model:account.group.template,name:l10n_lu.account_group_6343
msgid "AVA on inventories of goods"
msgstr "ZWb von Erzeugnissen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6344
#: model:account.group.template,name:l10n_lu.account_group_6344
msgid "AVA on inventories of merchandise and other goods for resale"
msgstr "ZWb von Waren und zum Verkauf bestimmten Gütern/Vermögensgegenständen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6341
#: model:account.group.template,name:l10n_lu.account_group_6341
msgid "AVA on inventories of raw materials and consumables"
msgstr "ZWb von Roh-, Hilfs- und Betriebsstoffen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6342
#: model:account.group.template,name:l10n_lu.account_group_6342
msgid "AVA on inventories of work and contracts in progress"
msgstr "ZWb von unfertigen Erzeugnissen und in Arbeit befindlichen Aufträgen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_63311
#: model:account.group.template,name:l10n_lu.account_group_63311
msgid "AVA on land"
msgstr "ZWb von Grundstücken  "

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6331
msgid ""
"AVA on land, fittings-out and buildings and FVA on investment properties"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6314
#: model:account.group.template,name:l10n_lu.account_group_6314
msgid "AVA on loan-issuance expenses"
msgstr "ZWb von Emissionskosten von Anleihen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65116
#: model:account.group.template,name:l10n_lu.account_group_65116
msgid "AVA on loans, deposits and claims held as fixed assets"
msgstr ""
"ZWb von Ausleihungen, geleisteten Hinterlegungen und Forderungen des "
"Anlagevermögens"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6333
#: model:account.group.template,name:l10n_lu.account_group_6333
msgid ""
"AVA on other fixtures and fittings, tools and equipment (including rolling "
"stock)"
msgstr ""
"ZWb von anderen Anlagen, Betriebs- und Geschäftsausstattung und Fuhrpark"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6353
#: model:account.group.template,name:l10n_lu.account_group_6353
msgid "AVA on other receivables"
msgstr "ZWb von sonstigen Forderungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6318
#: model:account.group.template,name:l10n_lu.account_group_6318
msgid "AVA on other similar expenses"
msgstr "ZWb von sonstigen vergleichbaren Kosten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65318
#: model:account.group.template,name:l10n_lu.account_group_65318
msgid "AVA on other transferable securities"
msgstr "ZWb von sonstigen Wertpapieren"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65312
#: model:account.group.template,name:l10n_lu.account_group_65312
msgid "AVA on own shares or own corporate units"
msgstr "ZWb von eigenen Aktien oder Anteilen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65113
#: model:account.group.template,name:l10n_lu.account_group_65113
msgid "AVA on participating interests"
msgstr "ZWb von Anteilen  "

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6332
#: model:account.group.template,name:l10n_lu.account_group_6332
msgid "AVA on plant and machinery"
msgstr "ZWb von technischen Anlagen und Maschinen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65115
#: model:account.group.template,name:l10n_lu.account_group_65115
msgid "AVA on securities held as fixed assets"
msgstr "ZWb von Wertpapieren des Anlagevermögens"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6311
#: model:account.group.template,name:l10n_lu.account_group_6311
msgid "AVA on set-up and start-up costs"
msgstr "ZWb von Gründungs- und Errichtungskosten (Rechts- und Beratungskosten)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65111
#: model:account.account.template,name:l10n_lu.lu_2011_account_65311
#: model:account.group.template,name:l10n_lu.account_group_65111
#: model:account.group.template,name:l10n_lu.account_group_65311
msgid "AVA on shares in affiliated undertakings"
msgstr "ZWb von Anteilen an verbundenen Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65313
#: model:account.group.template,name:l10n_lu.account_group_65313
msgid ""
"AVA on shares in undertakings with which the undertaking is linked by virtue "
"of participating interests"
msgstr ""
"ZWb von Anteilen an Unternehmen, mit denen ein Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_633
msgid ""
"AVA on tangible fixed assets and fair value adjustments (FVA) on investment "
"properties"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6351
#: model:account.group.template,name:l10n_lu.account_group_6351
msgid "AVA on trade receivables"
msgstr "ZWb von Forderungen aus Lieferungen und Leistungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6531
msgid "AVA on transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106142
msgid "Accident insurance"
msgstr "Unfallversicherung"

#. module: l10n_lu
#: model:ir.model,name:l10n_lu.model_account_chart_template
msgid "Account Chart Template"
msgstr "Kontenplanvorlage"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_106
msgid "Account of the owner or the co-owners"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61342
#: model:account.group.template,name:l10n_lu.account_group_61342
msgid "Accounting, tax consulting, auditing and similar fees"
msgstr "Buchführungs-, Steuerberatungs- und Prüfungskosten und ähnliche"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_16121
msgid "Acquired against payment (except Goodwill)"
msgstr "Entgeltlich erworben (Firmenwert nicht inbegriffen)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_771
#: model:account.group.template,name:l10n_lu.account_group_771
msgid "Adjustments of corporate income tax (CIT)"
msgstr "Erstattung der Körperschaftssteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_773
#: model:account.group.template,name:l10n_lu.account_group_773
msgid "Adjustments of foreign income taxes"
msgstr "Erstattung der ausländischen Ertragssteuern"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_783
#: model:account.group.template,name:l10n_lu.account_group_783
msgid "Adjustments of foreign taxes"
msgstr "Erstattung der ausländischen Steuern"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_77
msgid "Adjustments of income taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_772
#: model:account.group.template,name:l10n_lu.account_group_772
msgid "Adjustments of municipal business tax (MBT)"
msgstr "Erstattung der Gewerbesteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_781
#: model:account.group.template,name:l10n_lu.account_group_781
msgid "Adjustments of net wealth tax (NWT)"
msgstr "Erstattung der Vermögenssteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_788
#: model:account.group.template,name:l10n_lu.account_group_788
msgid "Adjustments of other taxes"
msgstr "Erstattung sonstiger Steuern"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_78
msgid "Adjustments of other taxes not included in the previous caption"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_782
#: model:account.group.template,name:l10n_lu.account_group_782
msgid "Adjustments of subscription tax"
msgstr "Erstattung der Abgeltungssteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42111
#: model:account.group.template,name:l10n_lu.account_group_42111
msgid "Advances and down payments"
msgstr "Vorschüsse und geleistete Anzahlungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_659
msgid "Allocations to financial provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6591
#: model:account.group.template,name:l10n_lu.account_group_6591
msgid "Allocations to financial provisions - affiliated undertakings"
msgstr "Zuführungen zu finanziellen Rückstellungen - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6592
#: model:account.group.template,name:l10n_lu.account_group_6592
msgid "Allocations to financial provisions - other"
msgstr "Zuführungen zu finanziellen Rückstellungen - sonstige"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6492
#: model:account.group.template,name:l10n_lu.account_group_6492
msgid "Allocations to operating provisions"
msgstr "Zuführungen zu betrieblichen Rückstellungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_649
msgid "Allocations to provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_679
#: model:account.group.template,name:l10n_lu.account_group_679
msgid "Allocations to provisions for deferred taxes"
msgstr "Zuführungen zu Rückstellungen für Steuern"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6491
#: model:account.group.template,name:l10n_lu.account_group_6491
msgid "Allocations to tax provisions"
msgstr "Zuführungen zu Steuerrückstellungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_647
#: model:account.group.template,name:l10n_lu.account_group_647
msgid "Allocations to tax-exempt capital gains"
msgstr "Zuführungen zu Sonderposten mit Rücklageanteil"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_653
msgid ""
"Allocations to value adjustment (AVA) and fair-value adjustments (FVA) on "
"transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_63
msgid ""
"Allocations to value adjustments (AVA) and fair value adjustments (FVA) on "
"formation expenses, intangible, tangible and current assets (except "
"transferable securities)"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_651
msgid ""
"Allocations to value adjustments (AVA) and fair-value adjustments (FVA) of "
"financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_232
#: model:account.account.template,name:l10n_lu.lu_2011_account_6452
#: model:account.account.template,name:l10n_lu.lu_2011_account_75112
#: model:account.account.template,name:l10n_lu.lu_2020_account_65212
#: model:account.account.template,name:l10n_lu.lu_2020_account_75212
#: model:account.group.template,name:l10n_lu.account_group_232
#: model:account.group.template,name:l10n_lu.account_group_411
#: model:account.group.template,name:l10n_lu.account_group_6452
#: model:account.group.template,name:l10n_lu.account_group_65212
#: model:account.group.template,name:l10n_lu.account_group_75212
#: model:account.group.template,name:l10n_lu.account_group_75222
msgid "Amounts owed by affiliated undertakings"
msgstr "Forderungen gegen verbundene Unternehmen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_41
msgid ""
"Amounts owed by affiliated undertakings and by undertakings with which the "
"undertaking is linked by virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4112
msgid ""
"Amounts owed by affiliated undertakings receivable after more than one year"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4111
msgid "Amounts owed by affiliated undertakings receivable within one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4212
#: model:account.account.template,name:l10n_lu.lu_2020_account_4222
#: model:account.group.template,name:l10n_lu.account_group_4212
#: model:account.group.template,name:l10n_lu.account_group_4222
msgid ""
"Amounts owed by partners and shareholders (others than from affiliated "
"undertakings)"
msgstr ""
"Forderungen gegen Gesellschafter und Aktionäre (andere als von verbundenen "
"Unternehmen)"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4217
msgid "Amounts owed by the Social Security and other social bodies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75114
msgid ""
"Amounts owed by undertakings with which the company is linked by virtue of "
"participating interests"
msgstr ""
"Forderungen gegen Unternehmen, mit denen ein Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_234
#: model:account.account.template,name:l10n_lu.lu_2011_account_6453
#: model:account.account.template,name:l10n_lu.lu_2020_account_65214
#: model:account.account.template,name:l10n_lu.lu_2020_account_75214
#: model:account.group.template,name:l10n_lu.account_group_234
#: model:account.group.template,name:l10n_lu.account_group_412
#: model:account.group.template,name:l10n_lu.account_group_6453
#: model:account.group.template,name:l10n_lu.account_group_65214
#: model:account.group.template,name:l10n_lu.account_group_75214
#: model:account.group.template,name:l10n_lu.account_group_75224
msgid ""
"Amounts owed by undertakings with which the undertaking is linked by virtue "
"of participating interests"
msgstr ""
"Forderungen gegen Unternehmen, mit denen ein Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4121
msgid ""
"Amounts owed by undertakings with which the undertaking is linked by virtue "
"of participating interests receivable within one year"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_194
msgid "Amounts owed to credit institutions"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_451
msgid "Amounts payable to affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4512
msgid "Amounts payable to affiliated undertakings after more than one year"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_45
msgid ""
"Amounts payable to affiliated undertakings and to undertakings with which "
"the undertaking is linked by virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4511
msgid "Amounts payable to affiliated undertakings within one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4713
#: model:account.account.template,name:l10n_lu.lu_2011_account_4723
#: model:account.group.template,name:l10n_lu.account_group_4713
#: model:account.group.template,name:l10n_lu.account_group_4723
msgid "Amounts payable to directors, managers, statutory auditors and similar"
msgstr ""
"Verbindlichkeiten gegenüber Verwaltern, Geschäftsführern, Kommissaren und "
"ähnlichen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4712
#: model:account.account.template,name:l10n_lu.lu_2020_account_4722
#: model:account.group.template,name:l10n_lu.account_group_4712
#: model:account.group.template,name:l10n_lu.account_group_4722
msgid ""
"Amounts payable to partners and shareholders (others than from affiliated "
"undertakings)"
msgstr ""
"Verbindlichkeiten gegenüber Gesellschaftern und Aktionären (andere als "
"gegnüber verbundenen Unternehmen)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4714
#: model:account.account.template,name:l10n_lu.lu_2020_account_4724
#: model:account.group.template,name:l10n_lu.account_group_4714
#: model:account.group.template,name:l10n_lu.account_group_4724
msgid "Amounts payable to staff"
msgstr "Verbindlichkeiten gegenüber dem Personal"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_452
msgid ""
"Amounts payable to undertakings with which the undertaking is linked by "
"virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4522
msgid ""
"Amounts payable to undertakings with which the undertaking is linked by "
"virtue of participating interests payable after more than one year"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4521
msgid ""
"Amounts payable to undertakings with which the undertaking is linked by "
"virtue of participating interests within one year"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4122
msgid "Amounts receivable after more than one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60813
#: model:account.group.template,name:l10n_lu.account_group_60813
msgid "Architects' and engineers' fees"
msgstr "Honorare für Architekten und Ingenieure"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6431
#: model:account.group.template,name:l10n_lu.account_group_6431
msgid "Attendance fees"
msgstr "Sitzungsgeld"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_643
msgid "Attendance fees, director's fees and similar remuneration"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_743
#: model:account.group.template,name:l10n_lu.account_group_743
msgid "Attendance fees, director's fees and similar remunerations"
msgstr "Sitzungsgeld, Tantiemen und vergleichbare Erträge"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61333
#: model:account.group.template,name:l10n_lu.account_group_61333
msgid ""
"Bank account charges and bank commissions (included custody fees on "
"securities)"
msgstr ""
"Kontogebühren und Bankkommissionen (Verwahrungsrechte von Wertpapieren "
"inbegriffen)"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7552
msgid "Bank and similar interest"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6552
msgid "Banking and similar interest"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6133
msgid "Banking and similar services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65521
#: model:account.group.template,name:l10n_lu.account_group_65521
msgid "Banking interest on current accounts"
msgstr "Kontozinsen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65522
#: model:account.group.template,name:l10n_lu.account_group_65522
msgid "Banking interest on financing operations"
msgstr "Bankzinsen auf Finanzoperationen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_5131
#: model:account.group.template,name:l10n_lu.account_group_5131
msgid "Banks and CCP : available balance"
msgstr "Kreditinstitute und CCP : Guthaben"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_5132
#: model:account.group.template,name:l10n_lu.account_group_5132
msgid "Banks and CCP : overdraft"
msgstr "Kreditinstitute und CCP : Überziehungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_513
msgid "Banks and postal cheques accounts (CCP)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6467
#: model:account.group.template,name:l10n_lu.account_group_6467
msgid "Bar licence tax"
msgstr "Getränkesteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_62111
#: model:account.group.template,name:l10n_lu.account_group_62111
msgid "Base wages"
msgstr "Grundlöhne"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_62115
#: model:account.account.template,name:l10n_lu.lu_2011_account_746
#: model:account.group.template,name:l10n_lu.account_group_62115
#: model:account.group.template,name:l10n_lu.account_group_746
msgid "Benefits in kind"
msgstr "Sachleistungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_442
msgid "Bills of exchange payable"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4422
#: model:account.group.template,name:l10n_lu.account_group_4422
msgid "Bills of exchange payable after more than one year"
msgstr ""
"Durch Handelswechsel entstandene Verbindlichkeiten (Schuldwechsel) mit einer "
"Restlaufzeit von mehr als einem Jahr"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4421
#: model:account.group.template,name:l10n_lu.account_group_4421
msgid "Bills of exchange payable within one year"
msgstr ""
"Durch Handelswechsel entstandene Verbindlichkeiten (Schuldwechsel) mit einer "
"Restlaufzeit von bis zu einem Jahr"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652221
#: model:account.account.template,name:l10n_lu.lu_2020_account_752221
#: model:account.group.template,name:l10n_lu.account_group_652221
#: model:account.group.template,name:l10n_lu.account_group_752221
msgid "Book value of yielded amounts owed by affiliated undertakings"
msgstr "Buchwert der verkauften Forderungen gegen verbundene Unternehmen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_752241
msgid ""
"Book value of yielded amounts owed by undertakings with which the "
"undertaking is linked by virtue of participating  interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652241
#: model:account.account.template,name:l10n_lu.lu_2020_account_752241
#: model:account.group.template,name:l10n_lu.account_group_652241
msgid ""
"Book value of yielded amounts owed by undertakings with which the "
"undertaking is linked by virtue of participating interests"
msgstr ""
"Buchwert der verkauften Forderungen gegen Unternehmen, mit denen ein "
"Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_64411
#: model:account.account.template,name:l10n_lu.lu_2020_account_74411
#: model:account.group.template,name:l10n_lu.account_group_64411
#: model:account.group.template,name:l10n_lu.account_group_74411
msgid "Book value of yielded intangible fixed assets"
msgstr "Buchwert der verkauften immateriellen Anlagewerte"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652261
#: model:account.account.template,name:l10n_lu.lu_2020_account_752261
#: model:account.group.template,name:l10n_lu.account_group_652261
#: model:account.group.template,name:l10n_lu.account_group_752261
msgid "Book value of yielded loans, deposits and claims held as fixed assets"
msgstr ""
"Buchwert der verkauften Ausleihungen, geleistete Hinterlegungen und "
"Forderungen des Umlaufvermögens"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652231
#: model:account.account.template,name:l10n_lu.lu_2020_account_752231
#: model:account.group.template,name:l10n_lu.account_group_652231
#: model:account.group.template,name:l10n_lu.account_group_752231
msgid "Book value of yielded participating interests"
msgstr "Buchwert der verkauften Anteile"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652251
#: model:account.account.template,name:l10n_lu.lu_2020_account_752251
#: model:account.group.template,name:l10n_lu.account_group_652251
#: model:account.group.template,name:l10n_lu.account_group_752251
msgid "Book value of yielded securities held as fixed assets"
msgstr "Buchwert der verkauften Wertpapiere des Anlagevermögens"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652211
#: model:account.account.template,name:l10n_lu.lu_2020_account_752211
#: model:account.group.template,name:l10n_lu.account_group_652211
#: model:account.group.template,name:l10n_lu.account_group_752211
msgid "Book value of yielded shares in affiliated undertakings"
msgstr "Buchwert der verkauften Beteiligungen an verbundenen Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_64421
#: model:account.account.template,name:l10n_lu.lu_2020_account_74421
#: model:account.group.template,name:l10n_lu.account_group_64421
#: model:account.group.template,name:l10n_lu.account_group_74421
msgid "Book value of yielded tangible fixed assets"
msgstr "Buchwert der verkauften materiellen Anlagewerte"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61112
#: model:account.account.template,name:l10n_lu.lu_2011_account_61221
#: model:account.account.template,name:l10n_lu.lu_2011_account_61411
#: model:account.group.template,name:l10n_lu.account_group_2213
#: model:account.group.template,name:l10n_lu.account_group_61112
#: model:account.group.template,name:l10n_lu.account_group_61221
#: model:account.group.template,name:l10n_lu.account_group_61411
msgid "Buildings"
msgstr "Bauten/Gebäude"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60763
#: model:account.group.template,name:l10n_lu.account_group_60763
msgid "Buildings for resale"
msgstr "Zum Verkauf bestimmte Bauten/Gebäude"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_22131
msgid "Buildings in Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22132
#: model:account.group.template,name:l10n_lu.account_group_22132
msgid "Buildings in foreign countries"
msgstr "Bauten / Gebäude im Ausland"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_314
#: model:account.group.template,name:l10n_lu.account_group_314
msgid "Buildings under construction"
msgstr "Im Bau befindliche Bauten / Gebäude"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61523
#: model:account.group.template,name:l10n_lu.account_group_61523
msgid "Business assignments"
msgstr "Dienstreisen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6144
#: model:account.group.template,name:l10n_lu.account_group_6144
msgid "Business risk insurance"
msgstr "Versicherung für betriebliche Risiken"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10629
msgid "Business share in private expenses"
msgstr "Gewerblicher Anteil an privaten Ausgaben"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6
msgid "CHARGES ACCOUNTS"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461212
#: model:account.group.template,name:l10n_lu.account_group_461212
msgid "CIT - Tax payable"
msgstr "Körperschaftssteuer - Zu zahlende Steuerschuld"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6711
#: model:account.group.template,name:l10n_lu.account_group_6711
msgid "CIT - current financial year"
msgstr "KSt - laufendes Geschäftsjahr"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6712
#: model:account.group.template,name:l10n_lu.account_group_6712
msgid "CIT - previous financial years"
msgstr "KSt - vorhergehende Geschäftsjahre"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_115
#: model:account.group.template,name:l10n_lu.account_group_115
msgid "Capital contribution without issue of shares"
msgstr "Kapitaleinlagen ohne Ausgabe von Anteilen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7473
#: model:account.group.template,name:l10n_lu.account_group_16
#: model:account.group.template,name:l10n_lu.account_group_7473
msgid "Capital investment subsidies"
msgstr "Investitionszuschüsse"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_104
#: model:account.group.template,name:l10n_lu.account_group_104
msgid "Capital of individual companies, corporate partnerships and similar"
msgstr ""
"Kapital von Einzelkaufleuten, Personenhandelsgesellschaften und ähnlichen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_72
msgid "Capitalised production"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106166
msgid "Car"
msgstr "Fahrzeug"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_51
msgid "Cash at bank, in postal cheques accounts, cheques and in hand"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_516
#: model:account.group.template,name:l10n_lu.account_group_516
msgid "Cash in hand"
msgstr "Kassenbestand"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10611
msgid "Cash withdrawals (daily life)"
msgstr "Barentnahmen (Lebenshaltungskosten)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6222
#: model:account.group.template,name:l10n_lu.account_group_6222
msgid "Casual workers"
msgstr "Gelegenheitsarbeitskräfte"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61515
#: model:account.group.template,name:l10n_lu.account_group_61515
msgid "Catalogues, printed materials and publications"
msgstr "Kataloge, Drucksachen und Veröffentlichungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7121
#: model:account.group.template,name:l10n_lu.account_group_7121
msgid "Change in inventories of finished goods"
msgstr "Bestandsveränderung von fertigen Erzeugnissen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_712
msgid "Change in inventories of goods"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_71
msgid "Change in inventories of goods and of work in progress"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7123
#: model:account.group.template,name:l10n_lu.account_group_7123
msgid "Change in inventories of residual goods"
msgstr "Bestandsveränderung von Restprodukten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7122
#: model:account.group.template,name:l10n_lu.account_group_7122
msgid "Change in inventories of semi-finished goods"
msgstr "Bestandsveränderung von Zwischenprodukten"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_711
msgid "Change in inventories of work and contracts in progress"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7111
#: model:account.group.template,name:l10n_lu.account_group_7111
msgid "Change in inventories of work in progress"
msgstr "Bestandsveränderung von unfertigen Erzeugnissen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7114
#: model:account.group.template,name:l10n_lu.account_group_7114
msgid "Change in inventories: buildings under construction"
msgstr "Bestandsveränderung von im Bau befindlichen Bauten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7112
#: model:account.group.template,name:l10n_lu.account_group_7112
msgid "Change in inventories: contracts in progress - goods"
msgstr "Bestandsveränderung von in Arbeit befindlichen Aufträgen - Erzeugnisse"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7113
#: model:account.group.template,name:l10n_lu.account_group_7113
msgid "Change in inventories: contracts in progress - services"
msgstr ""
"Bestandsveränderung von in Arbeit befindlichen Aufträgen - Dienstleistungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_607
msgid "Changes in inventory"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6073
#: model:account.group.template,name:l10n_lu.account_group_6073
msgid "Changes in inventory of consumable materials and supplies"
msgstr "Bestandsveränderungen an Hilfs- und Betriebsstoffen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6076
msgid "Changes in inventory of merchandise and other goods for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6074
#: model:account.group.template,name:l10n_lu.account_group_6074
msgid "Changes in inventory of packaging"
msgstr "Bestandsveränderungen an Verpackungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6071
#: model:account.group.template,name:l10n_lu.account_group_6071
msgid "Changes in inventory of raw materials"
msgstr "Bestandsveränderungen an Rohstoffen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_62412
#: model:account.group.template,name:l10n_lu.account_group_62412
msgid "Changes to provisions for complementary pensions"
msgstr "Zuführung zu Rückstellungen für Zusatzrenten"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_652
msgid "Charges and loss of disposal of financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_61334
msgid "Charges for electronic means of paiment"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61334
msgid "Charges for electronic means of payment"
msgstr "Gebühren auf elektronische Bezahlungsmittel"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6521
msgid "Charges of financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106152
msgid "Child benefit office"
msgstr "Familienzulagen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6165
#: model:account.group.template,name:l10n_lu.account_group_6165
msgid "Collective staff transportation"
msgstr "Beförderungen von Personal"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6131
#: model:account.account.template,name:l10n_lu.lu_2020_account_705
#: model:account.group.template,name:l10n_lu.account_group_6131
#: model:account.group.template,name:l10n_lu.account_group_705
msgid "Commissions and brokerage fees"
msgstr "Provisionen und Maklergebühren"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7453
#: model:account.group.template,name:l10n_lu.account_group_7453
msgid "Compensatory allowances"
msgstr "Ausgleichszahlungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6241
msgid "Complementary pensions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_62415
#: model:account.group.template,name:l10n_lu.account_group_62415
msgid "Complementary pensions paid by the employer"
msgstr "Ausgezahlte betriebliche Zusatzrente"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2235
#: model:account.group.template,name:l10n_lu.account_group_2235
msgid "Computer equipment"
msgstr "IT-Ausstattung "

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_21211
#: model:account.account.template,name:l10n_lu.lu_2011_account_21221
#: model:account.account.template,name:l10n_lu.lu_2011_account_6411
#: model:account.account.template,name:l10n_lu.lu_2011_account_72121
#: model:account.account.template,name:l10n_lu.lu_2011_account_7411
#: model:account.account.template,name:l10n_lu.lu_2020_account_70311
#: model:account.group.template,name:l10n_lu.account_group_21211
#: model:account.group.template,name:l10n_lu.account_group_21221
#: model:account.group.template,name:l10n_lu.account_group_6411
#: model:account.group.template,name:l10n_lu.account_group_70311
#: model:account.group.template,name:l10n_lu.account_group_72121
#: model:account.group.template,name:l10n_lu.account_group_7411
msgid "Concessions"
msgstr "Konzessionen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_1612
#: model:account.group.template,name:l10n_lu.account_group_212
#: model:account.group.template,name:l10n_lu.account_group_7212
msgid ""
"Concessions, patents, licences, trademarks and similar rights and assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_312
#: model:account.group.template,name:l10n_lu.account_group_312
msgid "Contracts in progress - goods"
msgstr "In Arbeit befindlichen Aufträge - Waren"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_313
#: model:account.group.template,name:l10n_lu.account_group_313
msgid "Contracts in progress - services"
msgstr "In Arbeit befindlichen Aufträge - Dienstleistungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_113
#: model:account.group.template,name:l10n_lu.account_group_113
msgid "Contribution premium"
msgstr "Agio bei Einlagen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6187
#: model:account.group.template,name:l10n_lu.account_group_6187
msgid "Contributions to professional associations"
msgstr "Beiträge für Berufsverbände"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_192
msgid "Convertible debenture loans"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_212151
#: model:account.account.template,name:l10n_lu.lu_2011_account_212251
#: model:account.account.template,name:l10n_lu.lu_2011_account_64151
#: model:account.account.template,name:l10n_lu.lu_2011_account_721251
#: model:account.account.template,name:l10n_lu.lu_2011_account_74151
#: model:account.account.template,name:l10n_lu.lu_2020_account_703151
#: model:account.group.template,name:l10n_lu.account_group_212151
#: model:account.group.template,name:l10n_lu.account_group_212251
#: model:account.group.template,name:l10n_lu.account_group_64151
#: model:account.group.template,name:l10n_lu.account_group_703151
#: model:account.group.template,name:l10n_lu.account_group_721251
#: model:account.group.template,name:l10n_lu.account_group_74151
msgid "Copyrights and reproduction rights"
msgstr "Urheber- und Reproduktionsrechte"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_42141
#: model:account.group.template,name:l10n_lu.account_group_42141
msgid "Corporate income tax"
msgstr "Körperschaftssteuer"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_46121
#: model:account.group.template,name:l10n_lu.account_group_671
msgid "Corporate income tax (CIT)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461211
#: model:account.group.template,name:l10n_lu.account_group_461211
msgid "Corporate income tax - Tax accrual"
msgstr "Körperschaftssteuer - Ermittelte Steuerschuld"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6182
#: model:account.group.template,name:l10n_lu.account_group_6182
msgid "Costs of training, symposiums, seminars, conferences"
msgstr "Kosten von Weiterbildungen, Kolloquien, Seminaren, Konferenzen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_16122
msgid "Created by the undertaking itself"
msgstr "Vom Unternehmen selbst erstellt "

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_67321
#: model:account.group.template,name:l10n_lu.account_group_67321
msgid "Current financial year"
msgstr "Laufendes Geschäftsjahr"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4011
#: model:account.account.template,name:l10n_lu.lu_2011_account_4021
#: model:account.group.template,name:l10n_lu.account_group_4011
#: model:account.group.template,name:l10n_lu.account_group_4021
msgid "Customers"
msgstr "Kunden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_40111
msgid "Customers (PoS)"
msgstr "Kunden (POS)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4012
#: model:account.group.template,name:l10n_lu.account_group_4012
msgid "Customers - Receivable bills of exchange"
msgstr "Kunden - Einzutreibende Wechsel (Besitzwechsel)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4014
#: model:account.group.template,name:l10n_lu.account_group_4014
msgid "Customers - Unbilled sales"
msgstr "Noch auszustellende Rechnungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6145
#: model:account.group.template,name:l10n_lu.account_group_6145
msgid "Customers credit insurance"
msgstr "Forderungsausfallversicherung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4015
#: model:account.group.template,name:l10n_lu.account_group_4015
msgid "Customers with a credit balance"
msgstr "Kunden - Kreditorische Debitoren"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4025
#: model:account.group.template,name:l10n_lu.account_group_4025
msgid "Customers with creditor balance"
msgstr "Kreditorische Debitoren"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4215
#: model:account.account.template,name:l10n_lu.lu_2020_account_4613
#: model:account.group.template,name:l10n_lu.account_group_4215
#: model:account.group.template,name:l10n_lu.account_group_4613
msgid "Customs and Excise Authority (ADA)"
msgstr "Zoll- und Verbrauchssteuerverwaltung (ADA)"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4
msgid "DEBTORS AND CREDITORS"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106154
msgid "Death and other health insurance funds"
msgstr "Sterbekasse oder sonstige Krankenzusatzversicherungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_19
msgid "Debenture loans and amounts owed to credit institutions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5083
#: model:account.group.template,name:l10n_lu.account_group_5083
msgid "Debenture loans and other notes issued and repurchased by the company"
msgstr "Eigene Anleihen und sonstige eigene Schuldtitel"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_23521
#: model:account.group.template,name:l10n_lu.account_group_23521
msgid "Debentures"
msgstr "Anleihen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_481
#: model:account.group.template,name:l10n_lu.account_group_481
msgid "Deferred charges (on one or more financial years)"
msgstr "Abgegrenzte Aufwendungen (über ein oder mehrere Geschäfstjahre)"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_48
msgid "Deferred charges and income"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_482
#: model:account.group.template,name:l10n_lu.account_group_482
msgid "Deferred income (on one or more financial years)"
msgstr "Abgegrenzte Erträge (über ein oder mehrere Geschäfstjahre)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_183
#: model:account.group.template,name:l10n_lu.account_group_183
msgid "Deferred tax provisions"
msgstr "Rückstellungen für latente Steuern"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_2362
#: model:account.group.template,name:l10n_lu.account_group_2362
msgid "Deposits and guarantees paid"
msgstr "Geleistete Hinterlegungen und Kautionen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106192
msgid "Deposits on private financial accounts"
msgstr "Anlagen auf finanziellen Privatkonten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_42187
#: model:account.account.template,name:l10n_lu.lu_2020_account_42287
#: model:account.account.template,name:l10n_lu.lu_2020_account_4717
#: model:account.account.template,name:l10n_lu.lu_2020_account_4727
#: model:account.group.template,name:l10n_lu.account_group_42187
#: model:account.group.template,name:l10n_lu.account_group_42287
#: model:account.group.template,name:l10n_lu.account_group_4717
#: model:account.group.template,name:l10n_lu.account_group_4727
msgid "Derivative financial instruments"
msgstr "Derivative Finanzinstrumente"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221111
#: model:account.group.template,name:l10n_lu.account_group_221111
msgid "Developed land"
msgstr "Bebaute Grundstücke"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_211
#: model:account.account.template,name:l10n_lu.lu_2011_account_7211
#: model:account.account.template,name:l10n_lu.lu_2020_account_1611
#: model:account.group.template,name:l10n_lu.account_group_1611
#: model:account.group.template,name:l10n_lu.account_group_211
#: model:account.group.template,name:l10n_lu.account_group_7211
msgid "Development costs"
msgstr "Entwicklungskosten"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4214
#: model:account.group.template,name:l10n_lu.account_group_4612
msgid "Direct Tax Authority (ACD)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6432
#: model:account.group.template,name:l10n_lu.account_group_6432
msgid "Director's fees"
msgstr "Tantiemen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6555
msgid "Discounts and charges on bills of exchange"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65551
#: model:account.group.template,name:l10n_lu.account_group_65551
msgid "Discounts and charges on bills of exchange - affiliated undertakings"
msgstr "Diskonte und Kosten von Handelswechsel - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65552
#: model:account.group.template,name:l10n_lu.account_group_65552
msgid "Discounts and charges on bills of exchange - other"
msgstr "Diskonte und Kosten von Handelswechsel - sonstige"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7555
msgid "Discounts on bills of exchange"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75551
#: model:account.group.template,name:l10n_lu.account_group_75551
msgid "Discounts on bills of exchange - affiliated undertakings"
msgstr "Diskonte auf Handelswechsel - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75552
#: model:account.group.template,name:l10n_lu.account_group_75552
msgid "Discounts on bills of exchange - other"
msgstr "Diskonte auf Handelswechsel - sonstige"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7556
msgid "Discounts received"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75561
#: model:account.group.template,name:l10n_lu.account_group_75561
msgid "Discounts received - affiliated undertakings"
msgstr "Erhaltene Diskonten - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75562
#: model:account.group.template,name:l10n_lu.account_group_75562
msgid "Discounts received - other"
msgstr "Erhaltene Diskonten - sonstige"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_752262
#: model:account.group.template,name:l10n_lu.account_group_752262
msgid "Disposal proceed of loans, deposits and claims held as fixed assets"
msgstr ""
"Veräußerungswert der Ausleihungen, der geleistete Hinterlegungen und der "
"Forderungen des Anlagevermögens"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652222
#: model:account.account.template,name:l10n_lu.lu_2020_account_752222
#: model:account.group.template,name:l10n_lu.account_group_652222
#: model:account.group.template,name:l10n_lu.account_group_752222
msgid "Disposal proceeds of amounts owed by affiliated undertakings"
msgstr "Veräußerungswert der Forderungen gegen verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652242
#: model:account.account.template,name:l10n_lu.lu_2020_account_752242
#: model:account.group.template,name:l10n_lu.account_group_652242
#: model:account.group.template,name:l10n_lu.account_group_752242
msgid ""
"Disposal proceeds of amounts owed by undertakings with which the undertaking "
"is linked by virtue of participating interests"
msgstr ""
"Veräußerungswert der Forderungen gegen Unternehmen, mit denen ein "
"Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_64412
#: model:account.account.template,name:l10n_lu.lu_2020_account_74412
#: model:account.group.template,name:l10n_lu.account_group_64412
#: model:account.group.template,name:l10n_lu.account_group_74412
msgid "Disposal proceeds of intangible fixed assets"
msgstr "Verkaufserlös von immateriellen Anlagewerten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652262
#: model:account.group.template,name:l10n_lu.account_group_652262
msgid "Disposal proceeds of loans, deposits and claims held as fixed assets"
msgstr ""
"Veräußerungswert der Ausleihungen, geleistete Hinterlegungen und Forderungen "
"des Umlaufvermögens"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652232
#: model:account.account.template,name:l10n_lu.lu_2020_account_752232
#: model:account.group.template,name:l10n_lu.account_group_652232
#: model:account.group.template,name:l10n_lu.account_group_752232
msgid "Disposal proceeds of participating interests"
msgstr "Veräußerungswert der Anteile"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652252
#: model:account.account.template,name:l10n_lu.lu_2020_account_752252
#: model:account.group.template,name:l10n_lu.account_group_652252
#: model:account.group.template,name:l10n_lu.account_group_752252
msgid "Disposal proceeds of securities held as fixed assets"
msgstr "Veräußerungswert der Wertpapiere des Anlagevermögens"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652212
#: model:account.account.template,name:l10n_lu.lu_2020_account_752212
#: model:account.group.template,name:l10n_lu.account_group_652212
#: model:account.group.template,name:l10n_lu.account_group_752212
msgid "Disposal proceeds of shares in affiliated undertakings"
msgstr "Veräußerungswert der Anteile an verbundenen Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_64422
#: model:account.account.template,name:l10n_lu.lu_2020_account_74422
#: model:account.group.template,name:l10n_lu.account_group_64422
#: model:account.group.template,name:l10n_lu.account_group_74422
msgid "Disposal proceeds of tangible fixed assets"
msgstr "Verkaufserlös von materiellen Anlagewerten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6181
#: model:account.group.template,name:l10n_lu.account_group_6181
msgid "Documentation"
msgstr "Dokumentation"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61516
#: model:account.group.template,name:l10n_lu.account_group_61516
msgid "Donations"
msgstr "Spenden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4013
#: model:account.group.template,name:l10n_lu.account_group_4013
msgid "Doubtful or disputed customers"
msgstr "Zweifelhafte oder strittige Kunden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_214
#: model:account.group.template,name:l10n_lu.account_group_214
msgid "Down payments and intangible fixed assets under development"
msgstr ""
"Geleistete Anzahlungen und immaterielle Vermögensgegenstände in Entwicklung"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_224
msgid "Down payments and tangible fixed assets under development"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_37
#: model:account.group.template,name:l10n_lu.account_group_37
msgid "Down payments on account on inventories"
msgstr "Geleistete Anzahlungen auf Vorräte"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_432
#: model:account.group.template,name:l10n_lu.account_group_432
msgid "Down payments received after more than one year"
msgstr "Erhaltene Anzahlungen mit einer Restlaufzeit von mehr als einem Jahr"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_315
msgid ""
"Down payments received on inventories of work and on contracts in progress"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4311
#: model:account.group.template,name:l10n_lu.account_group_4321
msgid "Down payments received on orders"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_43
msgid ""
"Down payments received on orders as far as they are not deducted distinctly "
"from inventories"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_431
#: model:account.group.template,name:l10n_lu.account_group_431
msgid "Down payments received within one year"
msgstr "Erhaltene Anzahlungen mit einer Restlaufzeit von bis zu einem Jahr"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1922
#: model:account.account.template,name:l10n_lu.lu_2020_account_1932
#: model:account.account.template,name:l10n_lu.lu_2020_account_1942
msgid "Due and payable after more than one year"
msgstr "Mit einer Restlaufzeit von mehr als einem Jahr"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1921
#: model:account.account.template,name:l10n_lu.lu_2020_account_1931
#: model:account.account.template,name:l10n_lu.lu_2020_account_1941
msgid "Due and payable within one year"
msgstr "Mit einer Restlaufzeit von bis zu einem Jahr"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6463
#: model:account.group.template,name:l10n_lu.account_group_6463
msgid "Duties on imported merchandise"
msgstr "Zölle und Einfuhrabgaben"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_1
msgid "EQUITY, PROVISIONS AND FINANCIAL LIABILITIES ACCOUNTS"
msgstr ""

#. module: l10n_lu
#: model:account.fiscal.position,name:l10n_lu.1_account_fiscal_position_template_private_LU_IC
#: model:account.fiscal.position.template,name:l10n_lu.account_fiscal_position_template_private_LU_IC
msgid "EU private"
msgstr "EU privat"

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-ECP-0
msgid "EX-EC(P)-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-ECP-0
msgid "EX-EC(P)-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-EC-0
msgid "EX-EC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-EC-0
msgid "EX-EC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-EC-0
msgid "EX-EC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-IC-0
msgid "EX-IC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-IC-0
msgid "EX-IC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-IC-0
msgid "EX-IC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-IC-0
msgid "EX-IC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60315
#: model:account.account.template,name:l10n_lu.lu_2020_account_61845
#: model:account.group.template,name:l10n_lu.account_group_60315
#: model:account.group.template,name:l10n_lu.account_group_61845
msgid "Electricity"
msgstr "Strom"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_105
#: model:account.group.template,name:l10n_lu.account_group_105
msgid "Endowment of branches"
msgstr "Dotationskapital von Niederlassungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6464
#: model:account.group.template,name:l10n_lu.account_group_6464
msgid "Excise duties on production and tax on consumption"
msgstr "Verbrauchsabgaben aus der Produktion und Verbrauchssteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_203
#: model:account.group.template,name:l10n_lu.account_group_203
msgid ""
"Expenses for increases in capital and for various operations (merger, "
"demerger, change of legal form)"
msgstr ""
"Kosten für Kapitalerhöhung und für verschiedene umwandlugsrechtliche "
"Vorgänge (Verschmelzungen, Spaltungen, Formwechsel)"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_617
msgid "External staff of the company"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6172
#: model:account.group.template,name:l10n_lu.account_group_6172
msgid "External staff on secondment"
msgstr "Ausgeliehenes Personal"

#. module: l10n_lu
#: model:account.fiscal.position,name:l10n_lu.1_account_fiscal_position_template_LU_EC
#: model:account.fiscal.position.template,name:l10n_lu.account_fiscal_position_template_LU_EC
msgid "Extra-Community Taxable Person"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_5
msgid "FINANCIAL ACCOUNTS"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_2
msgid "FORMATION EXPENSES AND FIXED ASSETS ACCOUNTS"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6512
#: model:account.account.template,name:l10n_lu.lu_2011_account_7512
#: model:account.group.template,name:l10n_lu.account_group_6512
#: model:account.group.template,name:l10n_lu.account_group_7512
msgid "FVA on financial fixed assets"
msgstr "AFV der Finanzanlagen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_63315
#: model:account.account.template,name:l10n_lu.lu_2020_account_73315
#: model:account.group.template,name:l10n_lu.account_group_63315
#: model:account.group.template,name:l10n_lu.account_group_73315
msgid "FVA on investment properties"
msgstr "AFV von Anlageimmobilien"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6354
#: model:account.account.template,name:l10n_lu.lu_2020_account_7354
#: model:account.group.template,name:l10n_lu.account_group_6354
#: model:account.group.template,name:l10n_lu.account_group_7354
msgid "FVA on receivables from current assets"
msgstr "AFV von Forderungen des Umlaufvermögens"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6532
#: model:account.group.template,name:l10n_lu.account_group_6532
#: model:account.group.template,name:l10n_lu.account_group_7532
msgid "FVA on transferable securities"
msgstr "AFV der Wertpapiere"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61336
#: model:account.group.template,name:l10n_lu.account_group_61336
msgid "Factoring services"
msgstr "Factoringgebühren"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7532
msgid "Fair value adjustments on transferable securities"
msgstr "AFV der Wertpapiere"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61513
#: model:account.group.template,name:l10n_lu.account_group_61513
msgid "Fairs and exhibitions"
msgstr "Messen und Ausstellungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_641
#: model:account.group.template,name:l10n_lu.account_group_7031
msgid ""
"Fees and royalties for concessions, patents, licences, trademarks and "
"similar rights and assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_741
msgid ""
"Fees and royalties for concessions, patents, licences, trademarks and "
"similar rights and assets from ancillary activities"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_65
msgid "Financial charges"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_23
msgid "Financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75
msgid "Financial income"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6115
msgid "Financial leasing on movable property"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6114
#: model:account.group.template,name:l10n_lu.account_group_6114
msgid "Financial leasing on real property"
msgstr "Immobilienfinanzierungsleasing"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_1882
#: model:account.group.template,name:l10n_lu.account_group_1882
msgid "Financial provisions"
msgstr "Finanzielle Rückstellungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6481
#: model:account.group.template,name:l10n_lu.account_group_6481
msgid "Fines, sanctions and penalties"
msgstr "Steuer- und strafrechtliche Bußgelder"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106143
msgid "Fire insurance"
msgstr "Brandschutzversicherung"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_2214
msgid "Fixtures and fitting-outs of buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_22141
#: model:account.group.template,name:l10n_lu.account_group_22141
msgid "Fixtures and fitting-outs of buildings in Luxembourg"
msgstr "Einrichtung von Bauten / Gebäuden in Luxemburg"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_22142
#: model:account.group.template,name:l10n_lu.account_group_22142
msgid "Fixtures and fitting-outs of buildings in foreign countries"
msgstr "Einrichtung von Bauten / Gebäuden im Ausland"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22121
#: model:account.group.template,name:l10n_lu.account_group_22121
msgid "Fixtures and fitting-outs of land in Luxembourg"
msgstr "Erschließung von Grundstücken in Luxembourg"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22122
#: model:account.group.template,name:l10n_lu.account_group_22122
msgid "Fixtures and fitting-outs of land in foreign countries"
msgstr "Erschließung von Grundstücken im Ausland"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_2212
msgid "Fixtures and fittings-out of land"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4622
#: model:account.group.template,name:l10n_lu.account_group_4622
msgid "Foreign Social Security offices"
msgstr "Ausländische Sozialversicherungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421811
#: model:account.account.template,name:l10n_lu.lu_2020_account_46151
#: model:account.group.template,name:l10n_lu.account_group_421811
#: model:account.group.template,name:l10n_lu.account_group_46151
msgid "Foreign VAT"
msgstr "Ausländische MwSt"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_756
msgid "Foreign currency exchange gains"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7561
#: model:account.group.template,name:l10n_lu.account_group_7561
msgid "Foreign currency exchange gains - affiliated undertakings"
msgstr "Wechselkursgewinne - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7562
#: model:account.group.template,name:l10n_lu.account_group_7562
msgid "Foreign currency exchange gains - other"
msgstr "Wechselkursgewinne - sonstige"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_656
msgid "Foreign currency exchange losses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6561
#: model:account.group.template,name:l10n_lu.account_group_6561
msgid "Foreign currency exchange losses - affiliated undertakings"
msgstr "Wechselkursverluste - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6562
#: model:account.group.template,name:l10n_lu.account_group_6562
msgid "Foreign currency exchange losses - other"
msgstr "Wechselkursverluste - sonstige"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_673
msgid "Foreign income taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42172
#: model:account.group.template,name:l10n_lu.account_group_42172
msgid "Foreign social security offices"
msgstr "Ausländische Sozialversicherungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4615
msgid "Foreign tax authorities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_683
#: model:account.group.template,name:l10n_lu.account_group_42181
#: model:account.group.template,name:l10n_lu.account_group_683
msgid "Foreign taxes"
msgstr "Ausländische Steuern"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_20
msgid "Formation expenses and similar expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65411
#: model:account.account.template,name:l10n_lu.lu_2020_account_755231
#: model:account.group.template,name:l10n_lu.account_group_65411
msgid "From affiliated undertakings"
msgstr "Forderungen gegen verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_755232
msgid "From other"
msgstr "Von sonstigen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65413
msgid "From other receivables from current assets"
msgstr "Sonstige Forderungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65412
#: model:account.group.template,name:l10n_lu.account_group_65412
msgid ""
"From undertakings with which the undertaking is linked by virtue of "
"participating interests"
msgstr ""
"Forderungen gegen Unternehmen, mit denen ein Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6031
msgid "Fuels, gas, water and electricity"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6184
msgid ""
"Fuels, gas, water and electricity (not included in the production of goods "
"and services)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106145
msgid "Full coverage insurance"
msgstr "Mehrgefahrenversicherung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2234
#: model:account.group.template,name:l10n_lu.account_group_2234
msgid "Furniture"
msgstr "Mobiliar"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_754
msgid ""
"Gain on disposal and other income from current receivables and transferable "
"securities of current assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7522
msgid "Gain on disposal of financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_744
msgid "Gain on disposal of intangible and tangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7441
msgid "Gain on disposal of intangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7541
msgid "Gain on disposal of receivables from current assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7542
msgid "Gain on disposal of transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60313
#: model:account.account.template,name:l10n_lu.lu_2020_account_61843
#: model:account.group.template,name:l10n_lu.account_group_60313
#: model:account.group.template,name:l10n_lu.account_group_61843
msgid "Gas"
msgstr "Gas"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6121
#: model:account.group.template,name:l10n_lu.account_group_6121
msgid ""
"General subcontracting (not included in the production of goods and services)"
msgstr ""
"Allgemeine Zulieferung (nicht für Werklieferungen und -leistungen und "
"sonstige Arbeiten)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106194
msgid "Gifts and allowance to children"
msgstr "Spenden und Zuwendungen an Kinder"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61514
#: model:account.group.template,name:l10n_lu.account_group_61514
msgid "Gifts to customers"
msgstr "Geschenke für Kunden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_213
#: model:account.account.template,name:l10n_lu.lu_2020_account_1613
#: model:account.group.template,name:l10n_lu.account_group_1613
#: model:account.group.template,name:l10n_lu.account_group_213
msgid "Goodwill acquired for consideration"
msgstr "Geschäfts- oder Firmenwert, soweit er entgeltlich erworben wurde"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6556
msgid "Granted discounts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65561
#: model:account.group.template,name:l10n_lu.account_group_65561
msgid "Granted discounts - affiliated undertakings"
msgstr "Gewährte Diskonten - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65562
#: model:account.group.template,name:l10n_lu.account_group_65562
msgid "Granted discounts - other"
msgstr "Gewährte Diskonten - sonstige"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_212152
msgid "Greenhous gas and similar emission quotas"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_212152
msgid "Greenhouse gas and similar emission quotas"
msgstr "Kontigente für Treibhausgasemissionen und ähnliche"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6211
msgid "Gross wages"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106153
msgid "Health insurance funds"
msgstr "Krankenversicherungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106163
msgid "Heating, gas, electricity"
msgstr "Heizung, Gas, Strom"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1_assessment_taxable_turnover
msgid "I. ASSESSMENT OF TAXABLE TURNOVER"
msgstr "I. BERECHNUNG DES STEUERPFLICHTIGEN UMSATZES"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2_assesment_of_tax_due
msgid "II. ASSESSMENT OF TAX DUE (output tax)"
msgstr "II. BERECHNUNG DER STEUER"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3_assessment_deducible_tax
msgid "III. ASSESSMENT OF DEDUCTIBLE TAX (input tax)"
msgstr "III. BERECHNUNG DER ABZIEHBAREN VORSTEUER"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7
msgid "INCOME ACCOUNTS"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_3
msgid "INVENTORIES ACCOUNTS"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6132
#: model:account.group.template,name:l10n_lu.account_group_6132
msgid "IT services"
msgstr "IT - Instandhaltung"

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_4_tax_tobe_paid_or_reclaimed
msgid "IV. TAX TO BE PAID OR TO BE RECLAIMED"
msgstr "IV. BERECHNUNG DES ÜBERSCHUSSES"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_62114
#: model:account.group.template,name:l10n_lu.account_group_62114
msgid "Incentives, bonuses and commissions"
msgstr "Gratifikationen, Prämien und Provisionen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_752
msgid "Income and gain on disposal of financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7521
msgid "Income from financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7442
msgid "Income of yielded tangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106281
msgid "Income tax"
msgstr "Einkommenssteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106181
msgid "Income tax paid"
msgstr "Gezahlte Einkommenssteuer"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_67
msgid "Income taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_642
#: model:account.group.template,name:l10n_lu.account_group_642
msgid "Indemnities, damages and interest"
msgstr "Entschädigungen, Schadensersatz und Zinsen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4216
msgid "Indirect Tax Authority (AED)"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4614
msgid "Indirect tax authorities (AED)"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_42162
#: model:account.group.template,name:l10n_lu.account_group_46142
msgid "Indirect taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6183
#: model:account.group.template,name:l10n_lu.account_group_6183
msgid "Industrial and non-industrial waste treatment"
msgstr "Abfallbeseitigung von Industrieabfällen und sonstigen Abfällen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10621
msgid "Inheritance or donation"
msgstr "Erbschaft oder Schenkung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106195
msgid "Inheritance taxes and mutation tax due to death"
msgstr "Erbschaftssteuern und Wechselsteuer durch Sterbefall"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_62414
#: model:account.group.template,name:l10n_lu.account_group_62414
msgid "Insolvency insurance premiums"
msgstr "Beiträge zur Insolvenzversicherung"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6141
msgid "Insurance for assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7481
#: model:account.group.template,name:l10n_lu.account_group_7481
msgid "Insurance indemnities"
msgstr "Versicherungsentschädigungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6142
#: model:account.group.template,name:l10n_lu.account_group_6142
msgid "Insurance on rented assets"
msgstr "Versicherung für gemietete Vermögensgegenstände"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_614
msgid "Insurance premiums"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_21
#: model:account.group.template,name:l10n_lu.account_group_721
msgid "Intangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_655
msgid "Interest and discounts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75541
#: model:account.group.template,name:l10n_lu.account_group_75541
msgid "Interest on amounts owed by affiliated undertakings"
msgstr "Zinsen auf Forderungen gegen verbundene Unternehmen "

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7554
msgid ""
"Interest on amounts owed by affiliated undertakings and undertakings with "
"which the undertaking is linked by virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75542
#: model:account.group.template,name:l10n_lu.account_group_75542
msgid ""
"Interest on amounts owed by undertakings with which the undertaking is "
"linked by virtue of participating interests"
msgstr ""
"Zinsen auf Forderungen gegen Unternehmen, mit denen ein "
"Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75521
#: model:account.group.template,name:l10n_lu.account_group_75521
msgid "Interest on bank accounts"
msgstr "Kontozinsen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6551
msgid "Interest on debenture loans"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65511
#: model:account.group.template,name:l10n_lu.account_group_65511
msgid "Interest on debenture loans - affiliated undertakings"
msgstr "Zinsen auf Anleihen - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65512
#: model:account.group.template,name:l10n_lu.account_group_65512
msgid "Interest on debenture loans - other"
msgstr "Zinsen auf Anleihen - sonstige"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_65523
#: model:account.group.template,name:l10n_lu.account_group_75523
msgid "Interest on financial leases"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_655231
#: model:account.group.template,name:l10n_lu.account_group_655231
msgid "Interest on financial leases - affiliated undertakings"
msgstr "Zinsen auf Finanzierungsleasings - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_655232
#: model:account.group.template,name:l10n_lu.account_group_655232
msgid "Interest on financial leases - other"
msgstr "Zinsen auf Finanzierungsleasings - sonstige"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7558
msgid "Interest on other amounts receivable"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75581
#: model:account.group.template,name:l10n_lu.account_group_75581
msgid "Interest on other amounts receivable - affiliated undertakings"
msgstr "Zinsen auf sonstigen Forderungen - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75582
#: model:account.group.template,name:l10n_lu.account_group_75582
msgid "Interest on other amounts receivable - other"
msgstr "Zinsen auf sonstigen Forderungen - sonstige"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6553
#: model:account.group.template,name:l10n_lu.account_group_6553
msgid "Interest on trade payables"
msgstr "Zinsen auf Verbindlichkeiten aus Lieferungen und Leistungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7553
#: model:account.group.template,name:l10n_lu.account_group_7553
msgid "Interest on trade receivables"
msgstr "Zinsen auf Handelsforderungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6558
msgid "Interest payable on other loans and debts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65581
#: model:account.group.template,name:l10n_lu.account_group_65581
msgid "Interest payable on other loans and debts - affiliated undertakings"
msgstr ""
"Zinsen auf sonstige Ausleihungen und Verbindlichkeiten - verbundene "
"Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65582
#: model:account.group.template,name:l10n_lu.account_group_65582
msgid "Interest payable on other loans and debts - other"
msgstr "Zinsen auf sonstige Ausleihungen und Verbindlichkeiten - sonstige"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65541
#: model:account.group.template,name:l10n_lu.account_group_65541
msgid "Interest payable to affiliated undertakings"
msgstr "Zinsen auf Verbindlichkeiten gegenüber verbundenen Unternehmen "

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6554
msgid ""
"Interest payable to affiliated undertakings and undertakings with which the "
"undertaking is linked by virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65542
#: model:account.group.template,name:l10n_lu.account_group_65542
msgid ""
"Interest payable to undertakings with which the undertaking is linked by "
"virtue of participating interests"
msgstr ""
"Zinsen auf Verbindlichkeiten gegenüber Unternehmen, mit denen ein "
"Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7452
#: model:account.group.template,name:l10n_lu.account_group_7452
msgid "Interest subsidies"
msgstr "Zinszuschüsse"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_15
#: model:account.group.template,name:l10n_lu.account_group_15
msgid "Interim dividends"
msgstr "Vorabdividenden"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_517
msgid "Internal transfers"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_5172
#: model:account.group.template,name:l10n_lu.account_group_5172
msgid "Internal transfers : credit balance"
msgstr "Interne Transferkonten : Guthaben"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_5171
#: model:account.group.template,name:l10n_lu.account_group_5171
msgid "Internal transfers : debit balance"
msgstr "Interne Transferkonten : Sollsaldo"

#. module: l10n_lu
#: model:account.fiscal.position,name:l10n_lu.1_account_fiscal_position_template_LU_IC
#: model:account.fiscal.position.template,name:l10n_lu.account_fiscal_position_template_LU_IC
msgid "Intra-Community Taxable Person"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_363
msgid "Inventories of buildings for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_3631
#: model:account.group.template,name:l10n_lu.account_group_3631
msgid "Inventories of buildings for resale in Luxembourg"
msgstr "Vorräte an, zum Verkauf bestimmten, Bauten / Gebäuden in Luxemburg"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_3632
#: model:account.group.template,name:l10n_lu.account_group_3632
msgid "Inventories of buildings for resale in foreign countries"
msgstr "Vorräte an, zum Verkauf bestimmten, Bauten / Gebäuden im Ausland"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_303
#: model:account.group.template,name:l10n_lu.account_group_303
msgid "Inventories of consumable materials and supplies"
msgstr "Vorräte an Hilfs-und Betriebsstoffen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_321
#: model:account.group.template,name:l10n_lu.account_group_321
msgid "Inventories of finished goods"
msgstr "Vorräte an fertigen Erzeugnissen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_32
msgid "Inventories of goods"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_362
msgid "Inventories of land for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_3621
#: model:account.group.template,name:l10n_lu.account_group_3621
msgid "Inventories of land for resale in Luxembourg"
msgstr "Vorräte an, zum Verkauf bestimmten, Grundstücken in Luxemburg"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_3622
#: model:account.group.template,name:l10n_lu.account_group_3622
msgid "Inventories of land for resale in foreign countries"
msgstr "Vorräte an, zum Verkauf bestimmten, Grundstücken im Ausland"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_361
#: model:account.group.template,name:l10n_lu.account_group_361
msgid "Inventories of merchandise"
msgstr "Vorräte an Waren"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_36
msgid "Inventories of merchandises and other goods for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_304
#: model:account.group.template,name:l10n_lu.account_group_304
msgid "Inventories of packaging"
msgstr "Vorräte an Verpackungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_301
#: model:account.group.template,name:l10n_lu.account_group_301
msgid "Inventories of raw materials"
msgstr "Vorräte an Rohstoffen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_30
msgid "Inventories of raw materials and consumables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_323
#: model:account.group.template,name:l10n_lu.account_group_323
msgid ""
"Inventories of residual goods (waste, rejected and recuperable material)"
msgstr "Vorräte an Restprodukten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_322
#: model:account.group.template,name:l10n_lu.account_group_322
msgid "Inventories of semi-finished goods"
msgstr "Vorräte an Zwischenprodukten"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_31
msgid "Inventories of work and contracts in progress"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4312
#: model:account.group.template,name:l10n_lu.account_group_4322
msgid ""
"Inventories of work and contracts in progress less down payments received"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_311
#: model:account.group.template,name:l10n_lu.account_group_311
msgid "Inventories of work in progress"
msgstr "Vorräte an unfertigen Erzeugnissen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_2215
msgid "Investment properties"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_22151
#: model:account.group.template,name:l10n_lu.account_group_22151
msgid "Investment properties in Luxembourg"
msgstr "Anlageimmobilien in Luxembourg"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_22152
#: model:account.group.template,name:l10n_lu.account_group_22152
msgid "Investment properties in foreign countries"
msgstr "Anlageimmobilien im Ausland"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42131
#: model:account.account.template,name:l10n_lu.lu_2011_account_42231
#: model:account.group.template,name:l10n_lu.account_group_42131
#: model:account.group.template,name:l10n_lu.account_group_42231
msgid "Investment subsidies"
msgstr "Investitionszuschüsse"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_42171
#: model:account.group.template,name:l10n_lu.account_group_4621
msgid "Joint Social Security Centre (CCSS)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61111
#: model:account.group.template,name:l10n_lu.account_group_2211
#: model:account.group.template,name:l10n_lu.account_group_61111
msgid "Land"
msgstr "Grundstücke"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60762
#: model:account.group.template,name:l10n_lu.account_group_60762
msgid "Land for resale"
msgstr "Zum Verkauf bestimme Grundstücke"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_22111
msgid "Land in Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22112
#: model:account.group.template,name:l10n_lu.account_group_22112
msgid "Land in foreign countries"
msgstr "Grundstücke im Ausland"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_2241
msgid "Land, fitting-outs and buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22411
#: model:account.group.template,name:l10n_lu.account_group_22411
msgid "Land, fitting-outs and buildings in Luxembourg"
msgstr "Grundstücke, Erschließungen, Einrichtungen und Bauten in Luxemburg"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22412
#: model:account.group.template,name:l10n_lu.account_group_22412
msgid "Land, fitting-outs and buildings in foreign countries"
msgstr "Grundstücke, Erschließungen, Einrichtungen und Bauten im Ausland"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7221
#: model:account.group.template,name:l10n_lu.account_group_7221
msgid "Land, fittings and buildings"
msgstr "Grundstücke, Erschließungen, Einrichtungen und Bauten"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_221
msgid "Land, fixtures and fitting-outs and buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_47162
#: model:account.account.template,name:l10n_lu.lu_2020_account_47262
#: model:account.group.template,name:l10n_lu.account_group_47162
#: model:account.group.template,name:l10n_lu.account_group_47262
msgid "Lease debts"
msgstr "Verbindlichkeiten aus Finanzierungsleasing"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_131
#: model:account.group.template,name:l10n_lu.account_group_131
msgid "Legal reserve"
msgstr "Gesetzliche Rücklage"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61341
#: model:account.group.template,name:l10n_lu.account_group_61341
msgid "Legal, litigation and similar fees"
msgstr "Rechts- und Prozesskosten und ähnliche"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_47163
#: model:account.account.template,name:l10n_lu.lu_2020_account_47263
#: model:account.group.template,name:l10n_lu.account_group_47163
#: model:account.group.template,name:l10n_lu.account_group_47263
msgid "Life annuities"
msgstr "Leibrenten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106141
msgid "Life insurance"
msgstr "Lebensversicherung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_486
#: model:account.group.template,name:l10n_lu.account_group_486
msgid "Linking accounts (branches) - Assets"
msgstr "Verbindungskonten (Niederlassungen) - Aktiva"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_487
#: model:account.group.template,name:l10n_lu.account_group_487
msgid "Linking accounts (branches) - Liabilities"
msgstr "Verbindungskonten (Niederlassungen) - Passiva"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60312
#: model:account.group.template,name:l10n_lu.account_group_60312
msgid "Liquid fuels"
msgstr "Flüssige Brennstoffe"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_61842
#: model:account.group.template,name:l10n_lu.account_group_61842
msgid "Liquid fuels (oil, motor fuel, etc.)"
msgstr "Flüssige Brennsoffe"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_chart_1_liquidity_transfer
msgid "Liquidity Transfer"
msgstr "Liquiditätsübertragung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5084
#: model:account.group.template,name:l10n_lu.account_group_5084
msgid "Listed debenture loans"
msgstr "Anleihen - Notierte Wertpapiere"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_235111
#: model:account.group.template,name:l10n_lu.account_group_235111
msgid "Listed shares"
msgstr "Notierte Aktien"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2236
#: model:account.group.template,name:l10n_lu.account_group_2236
msgid "Livestock"
msgstr "Viehbestand"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_204
#: model:account.group.template,name:l10n_lu.account_group_204
msgid "Loan issuances expenses"
msgstr "Emissionskosten von Anleihen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_2361
#: model:account.group.template,name:l10n_lu.account_group_2361
msgid "Loans"
msgstr "Ausleihungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_41112
#: model:account.account.template,name:l10n_lu.lu_2011_account_41122
#: model:account.account.template,name:l10n_lu.lu_2011_account_41212
#: model:account.account.template,name:l10n_lu.lu_2011_account_41222
#: model:account.account.template,name:l10n_lu.lu_2011_account_45112
#: model:account.account.template,name:l10n_lu.lu_2011_account_45122
#: model:account.account.template,name:l10n_lu.lu_2011_account_45212
#: model:account.account.template,name:l10n_lu.lu_2011_account_45222
#: model:account.group.template,name:l10n_lu.account_group_41112
#: model:account.group.template,name:l10n_lu.account_group_41122
#: model:account.group.template,name:l10n_lu.account_group_41212
#: model:account.group.template,name:l10n_lu.account_group_41222
#: model:account.group.template,name:l10n_lu.account_group_45112
#: model:account.group.template,name:l10n_lu.account_group_45122
#: model:account.group.template,name:l10n_lu.account_group_45212
#: model:account.group.template,name:l10n_lu.account_group_45222
msgid "Loans and advances"
msgstr "Ausleihungen und geleistete Anzahlungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4716
#: model:account.group.template,name:l10n_lu.account_group_4726
msgid "Loans and similar debts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61332
#: model:account.group.template,name:l10n_lu.account_group_61332
msgid "Loans' issuance expenses"
msgstr "Emissionskosten von Anleihen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75116
#: model:account.account.template,name:l10n_lu.lu_2020_account_65216
#: model:account.account.template,name:l10n_lu.lu_2020_account_75216
#: model:account.group.template,name:l10n_lu.account_group_236
#: model:account.group.template,name:l10n_lu.account_group_65216
#: model:account.group.template,name:l10n_lu.account_group_75216
#: model:account.group.template,name:l10n_lu.account_group_75226
msgid "Loans, deposits and claims held as fixed assets"
msgstr ""
"Ausleihungen, geleistete Hinterlegungen und Forderungen (Anlagevermögen)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2363
#: model:account.group.template,name:l10n_lu.account_group_2363
msgid "Long-term receivables"
msgstr "Forderungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_65222
msgid "Loss on disposal of amounts owed by affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_65224
msgid ""
"Loss on disposal of amounts owed by undertakings with which the undertaking "
"is linked by virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6522
msgid "Loss on disposal of financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_644
msgid "Loss on disposal of intangible and tangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6441
msgid "Loss on disposal of intangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_65226
msgid "Loss on disposal of loans, deposits and claims held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_65223
msgid "Loss on disposal of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_654
msgid ""
"Loss on disposal of receivables and transferable securities from current "
"assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6541
msgid "Loss on disposal of receivables from current assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_65225
msgid "Loss on disposal of securities held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_65221
msgid "Loss on disposal of shares in affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6442
msgid "Loss on disposal of tangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6542
msgid "Loss on disposal of transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_645
msgid "Losses on bad debts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6037
#: model:account.group.template,name:l10n_lu.account_group_6037
msgid "Lubricants"
msgstr "Schmiermittel"

#. module: l10n_lu
#: model:ir.ui.menu,name:l10n_lu.account_reports_lu_statements_menu
msgid "Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.fiscal.position,name:l10n_lu.1_account_fiscal_position_template_LU_LU
#: model:account.fiscal.position.template,name:l10n_lu.account_fiscal_position_template_LU_LU
msgid "Luxembourgish Taxable Person"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461221
#: model:account.group.template,name:l10n_lu.account_group_461221
msgid "MBT - Tax accrual"
msgstr "Gewerbesteuer - Ermittelte Steuerschuld"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461222
#: model:account.group.template,name:l10n_lu.account_group_461222
msgid "MBT - Tax payable"
msgstr "Gewerbesteuer - Zu zahlende Steuerschuld"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6721
#: model:account.group.template,name:l10n_lu.account_group_6721
msgid "MBT - current financial year"
msgstr "GewSt - laufendes Geschäftsjahr"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6722
#: model:account.group.template,name:l10n_lu.account_group_6722
msgid "MBT - previous financial years"
msgstr "GewSt - vorhergehende Geschäftsjahre"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2222
#: model:account.group.template,name:l10n_lu.account_group_2222
msgid "Machinery"
msgstr "Maschinen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6032
#: model:account.account.template,name:l10n_lu.lu_2020_account_61854
#: model:account.group.template,name:l10n_lu.account_group_6032
#: model:account.group.template,name:l10n_lu.account_group_61854
msgid "Maintenance supplies"
msgstr "Pflegemittel"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_615211
msgid "Management (if appropriate owner and partner)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_615211
msgid "Management (respectively owner and partner)"
msgstr "Geschäftsleitung (Einzelunternehmer bzw. Gesellschafter)"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6151
msgid "Marketing and advertising costs"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_615
msgid "Marketing and communication costs"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60761
#: model:account.group.template,name:l10n_lu.account_group_60761
msgid "Merchandise"
msgstr "Waren"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_112
#: model:account.group.template,name:l10n_lu.account_group_112
msgid "Merger premium"
msgstr "Agio bei Verschmelzungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_618
msgid "Miscellaneous external charges"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6488
#: model:account.group.template,name:l10n_lu.account_group_6488
msgid "Miscellaneous operating charges"
msgstr "Sonstige betriebliche Aufwendungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7488
#: model:account.group.template,name:l10n_lu.account_group_7488
msgid "Miscellaneous operating income"
msgstr "Andere betriebliche Erträge"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4218
#: model:account.group.template,name:l10n_lu.account_group_4228
msgid "Miscellaneous receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221313
#: model:account.group.template,name:l10n_lu.account_group_221313
msgid "Mixed-use buildings"
msgstr "Mischnutzungsbauten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6036
#: model:account.group.template,name:l10n_lu.account_group_6036
msgid "Motor fuels"
msgstr "Treibstoffe"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2232
#: model:account.group.template,name:l10n_lu.account_group_2232
msgid "Motor vehicles"
msgstr "Transportfahrzeuge"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6466
#: model:account.group.template,name:l10n_lu.account_group_6466
msgid "Motor-vehicle taxes"
msgstr "Kfz-Steuern"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4611
#: model:account.group.template,name:l10n_lu.account_group_4611
msgid "Municipal authorities"
msgstr "Gemeindeverwaltungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_42142
#: model:account.group.template,name:l10n_lu.account_group_42142
#: model:account.group.template,name:l10n_lu.account_group_672
msgid "Municipal business tax"
msgstr "Gewerbesteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106284
#: model:account.group.template,name:l10n_lu.account_group_46122
msgid "Municipal business tax (MBT)"
msgstr "Gewerbesteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106183
msgid "Municipal business tax - payment in arrears"
msgstr "Gewerbesteuer - gezahlte Rückstände"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461231
#: model:account.group.template,name:l10n_lu.account_group_461231
msgid "NWT - Tax accrual"
msgstr "Vermögenssteuer - Ermittelte Steuerschuld"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461232
#: model:account.group.template,name:l10n_lu.account_group_461232
msgid "NWT - Tax payable"
msgstr "Vermögenssteuer - Zu zahlende Steuerschuld"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6811
#: model:account.group.template,name:l10n_lu.account_group_6811
msgid "NWT - current financial year"
msgstr "VermSt - laufendes Geschäftsjahr"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6812
#: model:account.group.template,name:l10n_lu.account_group_6812
msgid "NWT - previous financial years"
msgstr "VermSt - vorhergehende Geschäftsjahre"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_70
msgid "Net turnover"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_42143
#: model:account.group.template,name:l10n_lu.account_group_42143
msgid "Net wealth tax"
msgstr "Vermögenssteuer"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_46123
#: model:account.group.template,name:l10n_lu.account_group_681
msgid "Net wealth tax (NWT)"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_193
msgid "Non-convertible debenture loans"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6462
#: model:account.group.template,name:l10n_lu.account_group_6462
msgid "Non-refundable VAT"
msgstr "Nicht erstattungsfähige Mehrwertsteuer (MwSt)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221312
#: model:account.group.template,name:l10n_lu.account_group_221312
msgid "Non-residential buildings"
msgstr "Zweckbauten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7099
#: model:account.group.template,name:l10n_lu.account_group_7099
msgid "Not allocated rebates, discounts and refunds"
msgstr "Nicht zugeordnete RPR"

#. module: l10n_lu
#: model:account.fiscal.position,name:l10n_lu.1_account_fiscal_position_template_LU_NO
#: model:account.fiscal.position.template,name:l10n_lu.account_fiscal_position_template_LU_NO
msgid "Not liable to VAT"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6135
#: model:account.group.template,name:l10n_lu.account_group_6135
msgid "Notarial and similar fees"
msgstr "Notarielle Beurkundungskosten und ähnliche"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6035
#: model:account.group.template,name:l10n_lu.account_group_6035
msgid "Office and administrative supplies"
msgstr "Büroausstattung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_61851
#: model:account.group.template,name:l10n_lu.account_group_61851
msgid "Office supplies"
msgstr "Büromaterial"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75411
msgid "On affiliated undertakings"
msgstr "Forderungen gegen verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75413
msgid "On other current receivables"
msgstr "Sonstige Forderungen des Umlaufvermögens"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75412
msgid ""
"On undertakings with which the undertaking is linked by virtue of "
"participating interests"
msgstr ""
"Forderungen gegen Unternehmen, mit denen ein Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_1881
#: model:account.group.template,name:l10n_lu.account_group_1881
msgid "Operating provisions"
msgstr "Betriebliche Rückstellungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42132
#: model:account.account.template,name:l10n_lu.lu_2011_account_42232
#: model:account.group.template,name:l10n_lu.account_group_42132
#: model:account.group.template,name:l10n_lu.account_group_42232
msgid "Operating subsidies"
msgstr "Betriebszuschüsse"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61418
#: model:account.account.template,name:l10n_lu.lu_2011_account_6228
#: model:account.account.template,name:l10n_lu.lu_2020_account_61128
#: model:account.account.template,name:l10n_lu.lu_2020_account_61158
#: model:account.account.template,name:l10n_lu.lu_2020_account_61228
#: model:account.account.template,name:l10n_lu.lu_2020_account_61858
#: model:account.group.template,name:l10n_lu.account_group_61128
#: model:account.group.template,name:l10n_lu.account_group_61158
#: model:account.group.template,name:l10n_lu.account_group_61228
#: model:account.group.template,name:l10n_lu.account_group_61418
#: model:account.group.template,name:l10n_lu.account_group_61858
#: model:account.group.template,name:l10n_lu.account_group_6228
msgid "Other"
msgstr "Sonstige"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106178
msgid "Other acquisitions"
msgstr "Sonstiger Erwerb"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61338
#: model:account.group.template,name:l10n_lu.account_group_61338
msgid ""
"Other banking and similar services (except interest and similar expenses)"
msgstr "Sonstige Bankdienstleistungen (außer Zinsen und vergleichbare Kosten)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6218
#: model:account.group.template,name:l10n_lu.account_group_6218
msgid "Other benefits"
msgstr "Sonstige Zulagen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221318
#: model:account.group.template,name:l10n_lu.account_group_221318
msgid "Other buildings"
msgstr "Sonstige Bauten / Gebäude"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_168
#: model:account.group.template,name:l10n_lu.account_group_168
msgid "Other capital investment subsidies"
msgstr "Sonstige Kapitalsubventionen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_518
#: model:account.group.template,name:l10n_lu.account_group_518
msgid "Other cash amounts"
msgstr "Sonstige Guthaben"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_708
#: model:account.group.template,name:l10n_lu.account_group_708
msgid "Other components of turnover"
msgstr "Sonstige Umsatzerlöse"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6038
#: model:account.group.template,name:l10n_lu.account_group_6038
msgid "Other consumable supplies"
msgstr "Sonstige Betriebsstoffe"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106158
msgid "Other contributions"
msgstr "Sonstige Beiträge"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_47
msgid "Other debts"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_472
msgid "Other debts payable after more than one year"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_471
msgid "Other debts payable within one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106248
msgid "Other disposals"
msgstr "Sonstige Übertragungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6468
#: model:account.group.template,name:l10n_lu.account_group_6468
msgid "Other duties and taxes"
msgstr "Sonstige Gebühren und Steuern"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_61
msgid "Other external charges"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_658
msgid "Other financial charges"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6581
#: model:account.group.template,name:l10n_lu.account_group_6581
msgid "Other financial charges - affiliated undertakings"
msgstr "Sonstige finanzielle Aufwendungen - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6582
#: model:account.group.template,name:l10n_lu.account_group_6582
msgid "Other financial charges - other"
msgstr "Sonstige finanzielle Aufwendungen - sonstige"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_758
msgid "Other financial income"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7581
#: model:account.group.template,name:l10n_lu.account_group_7581
msgid "Other financial income - affiliated undertakings"
msgstr "Sonstige finanzielle Erträge - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7582
#: model:account.group.template,name:l10n_lu.account_group_7582
msgid "Other financial income - other"
msgstr "Sonstige finanzielle Erträge - sonstige"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2238
#: model:account.group.template,name:l10n_lu.account_group_2238
msgid "Other fixtures"
msgstr "Sonstige Anlagen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7223
#: model:account.account.template,name:l10n_lu.lu_2011_account_7333
#: model:account.group.template,name:l10n_lu.account_group_7223
#: model:account.group.template,name:l10n_lu.account_group_7333
msgid ""
"Other fixtures and fittings, tools and equipment (included motor vehicles)"
msgstr ""
"Sonstige Anlagen, Betriebs- und Geschäftsausstattung (Fuhrpark inbegriffen)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2243
#: model:account.group.template,name:l10n_lu.account_group_223
#: model:account.group.template,name:l10n_lu.account_group_2243
msgid ""
"Other fixtures and fittings, tools and equipment (including rolling stock)"
msgstr ""
"Sonstige Anlagen, Betriebs- und Geschäftsausstattung (Fuhrpark inbegriffen)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6738
#: model:account.group.template,name:l10n_lu.account_group_6738
msgid "Other foreign income taxes"
msgstr "Sonstige ausländische Steuern auf Einkommen und Erträge"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421818
#: model:account.account.template,name:l10n_lu.lu_2020_account_46158
#: model:account.group.template,name:l10n_lu.account_group_421818
#: model:account.group.template,name:l10n_lu.account_group_46158
msgid "Other foreign taxes"
msgstr "Sonstige ausländische Steuern"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106168
msgid "Other in kind withdrawals"
msgstr "Sonstige Sachentnahmen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7548
msgid "Other income from transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421628
#: model:account.account.template,name:l10n_lu.lu_2011_account_461428
#: model:account.group.template,name:l10n_lu.account_group_421628
#: model:account.group.template,name:l10n_lu.account_group_461428
msgid "Other indirect taxes"
msgstr "Sonstige indirekte Steuern"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6148
#: model:account.group.template,name:l10n_lu.account_group_6148
msgid "Other insurances"
msgstr "Sonstige Versicherungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_755
msgid "Other interest income from current assets and discounts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221118
#: model:account.group.template,name:l10n_lu.account_group_221118
msgid "Other land"
msgstr "Sonstige Grundstücke"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_47161
#: model:account.account.template,name:l10n_lu.lu_2020_account_47261
#: model:account.group.template,name:l10n_lu.account_group_47161
#: model:account.group.template,name:l10n_lu.account_group_47261
msgid "Other loans"
msgstr "Sonstige Ausleihungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4718
#: model:account.account.template,name:l10n_lu.lu_2011_account_4728
#: model:account.group.template,name:l10n_lu.account_group_4718
#: model:account.group.template,name:l10n_lu.account_group_4728
msgid "Other miscellaneous debts"
msgstr "Andere sonstige Verbindlichkeiten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6188
#: model:account.group.template,name:l10n_lu.account_group_6188
msgid "Other miscellaneous external charges"
msgstr "Andere sonstige externe Aufwendungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_648
msgid "Other miscellaneous operating charges"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_748
msgid "Other miscellaneous operating income"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42188
#: model:account.account.template,name:l10n_lu.lu_2011_account_42288
#: model:account.group.template,name:l10n_lu.account_group_42188
#: model:account.group.template,name:l10n_lu.account_group_42288
msgid "Other miscellaneous receivables"
msgstr "Andere sonstige Forderungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5088
#: model:account.group.template,name:l10n_lu.account_group_5088
msgid "Other miscellaneous transferable securities"
msgstr "Andere sonstige Wertpapiere"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_64
msgid "Other operating charges"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_74
msgid "Other operating income"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_45118
#: model:account.account.template,name:l10n_lu.lu_2011_account_45128
#: model:account.account.template,name:l10n_lu.lu_2011_account_45218
#: model:account.account.template,name:l10n_lu.lu_2011_account_45228
#: model:account.group.template,name:l10n_lu.account_group_45118
#: model:account.group.template,name:l10n_lu.account_group_45128
#: model:account.group.template,name:l10n_lu.account_group_45218
#: model:account.group.template,name:l10n_lu.account_group_45228
msgid "Other payables"
msgstr "Sonstige Verbindlichkeiten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106148
msgid "Other private insurance premiums"
msgstr "Sonstige Beiträge für private Versicherungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61348
#: model:account.group.template,name:l10n_lu.account_group_61348
msgid "Other professional fees"
msgstr "Sonstige Honorare"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_188
msgid "Other provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6088
#: model:account.group.template,name:l10n_lu.account_group_6088
msgid "Other purchases included in the production of goods and services"
msgstr "Sonstige bezogene Gutachten und Dienstleistungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61518
#: model:account.group.template,name:l10n_lu.account_group_61518
msgid "Other purchases of advertising services"
msgstr "Sonstige Werbung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6082
#: model:account.group.template,name:l10n_lu.account_group_6082
msgid ""
"Other purchases of material included in the production of goods and services"
msgstr ""
"Sonstige Einkäufe von Material, Ausstattungen, Ersatzteilen und Arbeiten für "
"Werklieferungen und -leistungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_41118
#: model:account.account.template,name:l10n_lu.lu_2011_account_41128
#: model:account.account.template,name:l10n_lu.lu_2011_account_41218
#: model:account.account.template,name:l10n_lu.lu_2011_account_41228
#: model:account.account.template,name:l10n_lu.lu_2011_account_42168
#: model:account.account.template,name:l10n_lu.lu_2020_account_6454
#: model:account.group.template,name:l10n_lu.account_group_41118
#: model:account.group.template,name:l10n_lu.account_group_41128
#: model:account.group.template,name:l10n_lu.account_group_41218
#: model:account.group.template,name:l10n_lu.account_group_41228
#: model:account.group.template,name:l10n_lu.account_group_42
#: model:account.group.template,name:l10n_lu.account_group_42168
#: model:account.group.template,name:l10n_lu.account_group_6454
msgid "Other receivables"
msgstr "Sonstige Forderungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_422
msgid "Other receivables after one year"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_421
msgid "Other receivables within one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_64658
#: model:account.group.template,name:l10n_lu.account_group_64658
msgid "Other registration fees, stamp duties and mortgage duties"
msgstr "Sonstige Eintragungs- und Stempelgebühren und Hypothekensteuern"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6138
#: model:account.group.template,name:l10n_lu.account_group_6138
msgid "Other remuneration of intermediaries and professional fees"
msgstr "Sonstige Vermittlervergütungen und Honorare"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_1381
#: model:account.group.template,name:l10n_lu.account_group_1381
msgid "Other reserves available for distribution"
msgstr "Sonstige freie Rücklagen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_1382
msgid "Other reserves not available for distribution"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_138
msgid "Other reserves, including fair-value reserve"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_128
#: model:account.group.template,name:l10n_lu.account_group_128
msgid "Other revaluation reserves"
msgstr "Sonstige Neubewertungsrücklagen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2358
#: model:account.group.template,name:l10n_lu.account_group_2358
msgid "Other securities held as fixed assets"
msgstr "Sonstige Wertpapiere des Anlagevermögens"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_23528
#: model:account.group.template,name:l10n_lu.account_group_23528
msgid "Other securities held as fixed assets (creditor's right)"
msgstr "Sonstige Wertpapiere des Anlagevermögens (Forderungsrecht)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_23518
#: model:account.group.template,name:l10n_lu.account_group_23518
msgid "Other securities held as fixed assets (equity right)"
msgstr "Sonstige Wertpapiere des Anlagevermögens (Eigentumsrecht)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_47164
#: model:account.account.template,name:l10n_lu.lu_2020_account_47264
#: model:account.group.template,name:l10n_lu.account_group_47168
#: model:account.group.template,name:l10n_lu.account_group_47268
msgid "Other similar debts"
msgstr "Sonstige vergleichbare Verbindlichkeiten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_208
#: model:account.group.template,name:l10n_lu.account_group_208
msgid "Other similar expenses"
msgstr "Sonstige ähnliche Kosten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6438
#: model:account.group.template,name:l10n_lu.account_group_6438
msgid "Other similar remuneration"
msgstr "Sonstige ähnliche Vergütungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_64158
#: model:account.account.template,name:l10n_lu.lu_2011_account_721258
#: model:account.account.template,name:l10n_lu.lu_2011_account_74158
#: model:account.account.template,name:l10n_lu.lu_2020_account_703158
#: model:account.group.template,name:l10n_lu.account_group_64158
#: model:account.group.template,name:l10n_lu.account_group_703158
#: model:account.group.template,name:l10n_lu.account_group_721258
#: model:account.group.template,name:l10n_lu.account_group_74158
msgid "Other similar rights and assets"
msgstr "Sonstige vergleichbare Rechte und Werte"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_212158
#: model:account.group.template,name:l10n_lu.account_group_212158
msgid "Other similar rights and assets acquired for consideration"
msgstr "Sonstige entgeltlich erworbene Rechte und Werte"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_212258
#: model:account.group.template,name:l10n_lu.account_group_212258
msgid "Other similar rights and assets created by the undertaking itself"
msgstr ""
"Sonstige vergleichbare vom Unternehmen selbst erstellte Rechte und Werte"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42178
#: model:account.account.template,name:l10n_lu.lu_2011_account_4628
#: model:account.group.template,name:l10n_lu.account_group_42178
#: model:account.group.template,name:l10n_lu.account_group_4628
msgid "Other social bodies"
msgstr "Sonstige Einrichtungen der sozialen Sicherheit"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6232
#: model:account.group.template,name:l10n_lu.account_group_6232
msgid "Other social security costs (including illness, accidents, a.s.o.)"
msgstr ""
"Sonstige soziale Aufwendungen (einschließlich für Krankheits- und "
"Arbeitsunfallversicherung)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106198
msgid "Other special private withdrawals"
msgstr "Sonstige besondere Privatentnahmen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_624
msgid "Other staff expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6248
#: model:account.group.template,name:l10n_lu.account_group_6248
msgid "Other staff expenses not mentioned above"
msgstr "Sonstige nicht oben aufgeführte Personalaufwendungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_622
msgid "Other staff remuneration"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42138
#: model:account.account.template,name:l10n_lu.lu_2011_account_42238
#: model:account.group.template,name:l10n_lu.account_group_42138
#: model:account.group.template,name:l10n_lu.account_group_42238
msgid "Other subsidies"
msgstr "Sonstige Zuschüsse"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7458
#: model:account.group.template,name:l10n_lu.account_group_7458
msgid "Other subsidies for operating activities"
msgstr "Sonstige Zuschüsse für die laufende Geschäftstätigkeit"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_621128
#: model:account.group.template,name:l10n_lu.account_group_621128
msgid "Other supplements"
msgstr "Sonstige Zuschläge"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106288
msgid "Other tax refunds"
msgstr "Sonstige Steuerrückzahlungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106188
#: model:account.account.template,name:l10n_lu.lu_2011_account_688
#: model:account.group.template,name:l10n_lu.account_group_688
msgid "Other taxes"
msgstr "Sonstige Steuern"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_68
msgid "Other taxes not included in the previous caption"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75488
#: model:account.account.template,name:l10n_lu.lu_2020_account_65428
#: model:account.account.template,name:l10n_lu.lu_2020_account_75318
#: model:account.account.template,name:l10n_lu.lu_2020_account_75428
#: model:account.group.template,name:l10n_lu.account_group_508
#: model:account.group.template,name:l10n_lu.account_group_65428
#: model:account.group.template,name:l10n_lu.account_group_75428
#: model:account.group.template,name:l10n_lu.account_group_75488
msgid "Other transferable securities"
msgstr "Sonstige Wertpapiere"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6168
#: model:account.group.template,name:l10n_lu.account_group_6168
msgid "Other transportation"
msgstr "Sonstige Transporte"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60814
#: model:account.group.template,name:l10n_lu.account_group_60814
msgid "Outsourcing included in the production of goods and services"
msgstr "Zulieferungen für Werklieferungen und -leistungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_621123
#: model:account.group.template,name:l10n_lu.account_group_621123
msgid "Overtime"
msgstr "Überstunden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75482
#: model:account.account.template,name:l10n_lu.lu_2020_account_65422
#: model:account.account.template,name:l10n_lu.lu_2020_account_75312
#: model:account.account.template,name:l10n_lu.lu_2020_account_75422
#: model:account.group.template,name:l10n_lu.account_group_65422
#: model:account.group.template,name:l10n_lu.account_group_75422
#: model:account.group.template,name:l10n_lu.account_group_75482
msgid "Own shares or corporate units"
msgstr "Eigene Aktien oder Anteile"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_502
#: model:account.group.template,name:l10n_lu.account_group_502
msgid "Own shares or own corporate units"
msgstr "Eigene Aktien oder eigene Anteile"

#. module: l10n_lu
#: model:account.chart.template,name:l10n_lu.lu_2011_chart_1
msgid "PCMN Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_233
#: model:account.account.template,name:l10n_lu.lu_2011_account_75113
#: model:account.account.template,name:l10n_lu.lu_2020_account_65213
#: model:account.account.template,name:l10n_lu.lu_2020_account_75213
#: model:account.group.template,name:l10n_lu.account_group_233
#: model:account.group.template,name:l10n_lu.account_group_65213
#: model:account.group.template,name:l10n_lu.account_group_75213
#: model:account.group.template,name:l10n_lu.account_group_75223
msgid "Participating interests"
msgstr "Anteile"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_21212
#: model:account.account.template,name:l10n_lu.lu_2011_account_21222
#: model:account.account.template,name:l10n_lu.lu_2011_account_6412
#: model:account.account.template,name:l10n_lu.lu_2011_account_72122
#: model:account.account.template,name:l10n_lu.lu_2011_account_7412
#: model:account.account.template,name:l10n_lu.lu_2020_account_70312
#: model:account.group.template,name:l10n_lu.account_group_21212
#: model:account.group.template,name:l10n_lu.account_group_21222
#: model:account.group.template,name:l10n_lu.account_group_6412
#: model:account.group.template,name:l10n_lu.account_group_70312
#: model:account.group.template,name:l10n_lu.account_group_72122
#: model:account.group.template,name:l10n_lu.account_group_7412
msgid "Patents"
msgstr "Patente"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10622
msgid "Personal holdings"
msgstr "Private Guthaben"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2221
#: model:account.group.template,name:l10n_lu.account_group_2221
msgid "Plant"
msgstr "Technische Anlagen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2242
#: model:account.account.template,name:l10n_lu.lu_2011_account_7222
#: model:account.group.template,name:l10n_lu.account_group_222
#: model:account.group.template,name:l10n_lu.account_group_2242
#: model:account.group.template,name:l10n_lu.account_group_7222
msgid "Plant and machinery"
msgstr "Technische Anlagen und Maschinen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61531
#: model:account.group.template,name:l10n_lu.account_group_61531
msgid "Postal charges"
msgstr "Postgebühren"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6153
msgid "Postal charges and telecommunication costs"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_62411
#: model:account.group.template,name:l10n_lu.account_group_62411
msgid "Premiums for external pensions funds"
msgstr "Beiträge für externe Rentenfonds"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_114
#: model:account.group.template,name:l10n_lu.account_group_114
msgid "Premiums on conversion of bonds into shares"
msgstr "Agio bei der Umwandlung von Anleihen in Aktien"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61511
#: model:account.group.template,name:l10n_lu.account_group_61511
msgid "Press advertising"
msgstr "Annoncen und Inserate"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_67322
#: model:account.group.template,name:l10n_lu.account_group_67322
msgid "Previous financial years"
msgstr "Vorhergehende Geschäftsjahre"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106174
#: model:account.account.template,name:l10n_lu.lu_2011_account_106244
msgid "Private buildings"
msgstr "Private Gebäude"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106172
#: model:account.account.template,name:l10n_lu.lu_2011_account_106242
msgid "Private car"
msgstr "Privates Fahrzeug"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106171
#: model:account.account.template,name:l10n_lu.lu_2011_account_106241
msgid "Private furniture"
msgstr "Privates Mobiliar"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106173
msgid "Private held securities"
msgstr "Private Wertpapiere"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10623
msgid "Private loans"
msgstr "Private Ausleihungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10613
msgid "Private share of medical services expenses"
msgstr "Privater Anteil an den Krankheitskosten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106243
msgid "Private shares / bonds"
msgstr "Private Wertpapiere / Aktien"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7451
#: model:account.group.template,name:l10n_lu.account_group_7451
msgid "Product subsidies"
msgstr "Produktzuschüsse"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6134
msgid "Professional fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221112
#: model:account.group.template,name:l10n_lu.account_group_221112
msgid "Property rights and similar"
msgstr "Immobilien- / Eigentumsrechte auf Sachanlagen und ähnliche"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_18
msgid "Provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_181
#: model:account.group.template,name:l10n_lu.account_group_181
msgid "Provisions for pensions and similar obligations"
msgstr "Rückstellungen für Pensionen und ähnliche Verpflichtungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_182
#: model:account.group.template,name:l10n_lu.account_group_182
msgid "Provisions for taxation"
msgstr "Steuerrückstellungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_621122
#: model:account.group.template,name:l10n_lu.account_group_621122
msgid "Public holidays"
msgstr "Feiertagsarbeit"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6083
msgid "Purchase of greenhous gas and similar emission quotas"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6083
msgid "Purchase of greenhouse gas and similar emission quotas"
msgstr "Einkäufe von Kontigenten für Treibhausgasemissionen und ähnliche"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_45111
#: model:account.account.template,name:l10n_lu.lu_2011_account_45121
#: model:account.account.template,name:l10n_lu.lu_2011_account_45211
#: model:account.account.template,name:l10n_lu.lu_2011_account_45221
#: model:account.group.template,name:l10n_lu.account_group_45111
#: model:account.group.template,name:l10n_lu.account_group_45121
#: model:account.group.template,name:l10n_lu.account_group_45211
#: model:account.group.template,name:l10n_lu.account_group_45221
msgid "Purchases and services"
msgstr "Käufe und Dienstleistungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6063
#: model:account.group.template,name:l10n_lu.account_group_6063
msgid "Purchases of buildings for resale"
msgstr "Einkäufe von zum Verkauf bestimmten Bauten/Gebäuden"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_603
msgid "Purchases of consumable materials and supplies"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_608
msgid "Purchases of items included in the production of goods and services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6062
#: model:account.group.template,name:l10n_lu.account_group_6062
msgid "Purchases of land for resale"
msgstr "Einkäufe von zum Verkauf bestimmten Grundstücken"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6061
#: model:account.group.template,name:l10n_lu.account_group_6061
msgid "Purchases of merchandise"
msgstr "Einkäufe von Waren"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_606
msgid "Purchases of merchandise and other goods for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_604
#: model:account.group.template,name:l10n_lu.account_group_604
msgid "Purchases of packaging"
msgstr "Einkäufe von Verpackungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_601
#: model:account.group.template,name:l10n_lu.account_group_601
msgid "Purchases of raw materials"
msgstr "Einkäufe von Rohstoffen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7095
#: model:account.group.template,name:l10n_lu.account_group_7095
msgid "RDR on commissions and brokerage fees"
msgstr "RPR auf Kommissionen und Courtagen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7098
#: model:account.group.template,name:l10n_lu.account_group_7098
msgid "RDR on other components of turnover"
msgstr "RPR auf sonstige Umsatzerlöse"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6098
#: model:account.group.template,name:l10n_lu.account_group_6098
msgid "RDR on purchases included in the production of goods and services"
msgstr ""
"Erhaltene Rabatte, Preisnachlässe und Rückvergütungen auf Einkäufe für "
"Werklieferungen und -leistungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6093
#: model:account.group.template,name:l10n_lu.account_group_6093
msgid "RDR on purchases of consumable materials and supplies"
msgstr ""
"Erhaltene Rabatte, Preisnachlässe und Rückvergütungen auf Hilfs- und "
"Betriebsstoffe"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6096
#: model:account.group.template,name:l10n_lu.account_group_6096
msgid "RDR on purchases of merchandise and other goods for resale"
msgstr ""
"Erhaltene Rabatte, Preisnachlässe und Rückvergütungen auf Waren und sonstige "
"zum Verkauf bestimmte Güter/Vermögensgegenstände"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6094
#: model:account.group.template,name:l10n_lu.account_group_6094
msgid "RDR on purchases of packaging"
msgstr "Erhaltene Rabatte, Preisnachlässe und Rückvergütungen auf Verpackungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6091
#: model:account.group.template,name:l10n_lu.account_group_6091
msgid "RDR on purchases of raw materials"
msgstr "Erhaltene Rabatte, Preisnachlässe und Rückvergütungen auf Rohstoffe"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7092
#: model:account.group.template,name:l10n_lu.account_group_7092
msgid "RDR on sales of goods"
msgstr "RPR auf Verkäufe von Erzeugnissen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7096
#: model:account.group.template,name:l10n_lu.account_group_7096
msgid "RDR on sales of merchandise and other goods for resale"
msgstr ""
"RPR auf Verkäufe von Waren und zum Verkauf bestimmten Gütern/"
"Vermögensgegenständen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7094
#: model:account.group.template,name:l10n_lu.account_group_7094
msgid "RDR on sales of packages"
msgstr "RPR auf Verkäufe von Verpackungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7093
#: model:account.group.template,name:l10n_lu.account_group_7093
msgid "RDR on sales of services"
msgstr "RPR auf Verkäufe von Dienstleistungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_735
msgid "RVA and FVA on receivables from current assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75112
msgid "RVA on amounts owed by affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7352
#: model:account.group.template,name:l10n_lu.account_group_7352
msgid ""
"RVA on amounts owed by affiliated undertakings and undertakings with which "
"the undertaking is linked by virtue of participating interests"
msgstr ""
"Forderungen gegen verbundene Unternehmen und gegen Unternehmen, mit denen "
"ein Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75114
msgid ""
"RVA on amounts owed by undertakings with which the company is linked by "
"virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_73313
#: model:account.group.template,name:l10n_lu.account_group_73313
msgid "RVA on buildings"
msgstr "Wertaufholungen von Bauten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7322
#: model:account.group.template,name:l10n_lu.account_group_7322
msgid ""
"RVA on concessions, patents, licences, trademarks and similar rights and "
"assets"
msgstr ""
"Wertaufholungen von Konzessionen, Patenten, Lizenzen, Warenzeichen und "
"ähnlichen Rechten und Werten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7321
#: model:account.group.template,name:l10n_lu.account_group_7321
msgid "RVA on development costs"
msgstr "Wertaufholungen von Entwicklungskosten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7324
#: model:account.group.template,name:l10n_lu.account_group_7324
msgid "RVA on down payments and intangible fixed assets under development"
msgstr ""
"Wertaufholungen von geleisteten Anzahlungen und immateriellen "
"Vermögensgegenständen in Entwicklung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7334
#: model:account.group.template,name:l10n_lu.account_group_7334
msgid "RVA on down payments and tangible fixed assets under development"
msgstr "Wertaufholungen von geleisteten Anzahlungen und Anlagen im Bau"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7345
#: model:account.group.template,name:l10n_lu.account_group_7345
msgid "RVA on down payments on inventories"
msgstr "Geleistete Anzahlungen auf Vorräte"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7511
msgid "RVA on financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_73314
#: model:account.group.template,name:l10n_lu.account_group_73314
msgid "RVA on fixtures and fittings-out of buildings"
msgstr "Wertaufholungen von Einrichtungen von Bauten/Gebäuden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_73312
#: model:account.group.template,name:l10n_lu.account_group_73312
msgid "RVA on fixtures and fittings-out of land"
msgstr "Wertaufholungen von Erschließungen von Grundstücken"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_732
msgid "RVA on intangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_734
msgid "RVA on inventories"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7343
#: model:account.group.template,name:l10n_lu.account_group_7343
msgid "RVA on inventories of goods"
msgstr "Erzeugnisse"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7344
#: model:account.group.template,name:l10n_lu.account_group_7344
msgid "RVA on inventories of merchandise and other goods for resale"
msgstr "Waren und sonstige zum Verkauf bestimmte Güter/Vermögensgegenstände "

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7341
#: model:account.group.template,name:l10n_lu.account_group_7341
msgid "RVA on inventories of raw materials and consumables"
msgstr "Wertaufholungen von Roh-, Hilfs- und Betriebsstoffen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7342
#: model:account.group.template,name:l10n_lu.account_group_7342
msgid "RVA on inventories of work and contracts in progress"
msgstr ""
"Wertaufholungen von unfertigen Erzeugnisse und sich in Arbeit befindlichen "
"Aufträgen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_73311
#: model:account.group.template,name:l10n_lu.account_group_73311
msgid "RVA on land"
msgstr ""
"Wertaufholungen von Grundstücken, Erschließungen, Einrichtungen und Bauten"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7331
msgid ""
"RVA on land, fixtures and fittings-out and buildings and FVA on investment "
"properties"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75116
msgid "RVA on loans, deposits and claims held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7353
#: model:account.group.template,name:l10n_lu.account_group_7353
msgid "RVA on other receivables"
msgstr "Sonstige Forderungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75318
msgid "RVA on other transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75312
msgid "RVA on own shares or corporate units"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75113
msgid "RVA on participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7332
#: model:account.group.template,name:l10n_lu.account_group_7332
msgid "RVA on plant and machinery"
msgstr "Wertaufholungen von technischen Anlagen und Maschinen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75115
msgid "RVA on securities held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75111
#: model:account.group.template,name:l10n_lu.account_group_75311
msgid "RVA on shares in affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75313
msgid ""
"RVA on shares in undertakings with which the undertaking is linked by virtue "
"of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_733
msgid ""
"RVA on tangible fixed assets and fair value adjustments (FVA) on investment "
"properties"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7351
#: model:account.group.template,name:l10n_lu.account_group_7351
msgid "RVA on trade receivables"
msgstr "Forderungen aus Lieferungen und Leistungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7531
msgid "RVA on transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6461
#: model:account.group.template,name:l10n_lu.account_group_6461
msgid "Real property tax"
msgstr "Grundsteuer"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_709
msgid ""
"Rebates, discounts and refunds (RDR) granted and not immediately deducted "
"from sales"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_609
msgid ""
"Rebates, discounts and refunds (RDR) received and not directly deducted from "
"purchases"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_619
#: model:account.group.template,name:l10n_lu.account_group_619
msgid "Rebates, discounts and refunds received on other external charges"
msgstr ""
"Erhaltene Rabatte, Preisnachlässe und Rückvergütungen auf sonstige externe "
"Aufwendungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10627
msgid "Received child benefit"
msgstr "Erhaltene Familienzulagen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4711
#: model:account.account.template,name:l10n_lu.lu_2020_account_4721
#: model:account.group.template,name:l10n_lu.account_group_4711
#: model:account.group.template,name:l10n_lu.account_group_4721
msgid "Received deposits and guarantees"
msgstr "Erhaltene Hinterlegungen und Kautionen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10625
msgid "Received rents"
msgstr "Mieteinnahmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10626
msgid "Received wages or pensions"
msgstr "Eingenommene Löhne oder Renten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61524
#: model:account.group.template,name:l10n_lu.account_group_61524
msgid "Receptions and entertainment costs"
msgstr "Bewirtungs- und Repräsentationskosten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106193
msgid "Refund of private debts"
msgstr "Rückzahlungen privater Schulden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6219
#: model:account.group.template,name:l10n_lu.account_group_6219
msgid "Refunds on wages paid"
msgstr "Erstattungen von gezahlten Löhnen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421621
#: model:account.account.template,name:l10n_lu.lu_2011_account_461421
#: model:account.group.template,name:l10n_lu.account_group_421621
#: model:account.group.template,name:l10n_lu.account_group_461421
msgid "Registration duties"
msgstr "Registriergebühren"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_64651
#: model:account.group.template,name:l10n_lu.account_group_64651
msgid "Registration fees"
msgstr "Eintragungsgebühren"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6465
msgid "Registration fees, stamp duties and mortgage duties"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61522
#: model:account.group.template,name:l10n_lu.account_group_61522
msgid "Relocation expenses"
msgstr "Umzugskosten des Unternehmens"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_613
msgid "Remuneration of intermediaries and professional fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106162
msgid "Rent"
msgstr "Miete"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_7032
msgid "Rental income"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_742
msgid "Rental income from ancillary activities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_70322
#: model:account.group.template,name:l10n_lu.account_group_70322
msgid "Rental income from movable property"
msgstr "Mieterträge aus beweglichem Vermögen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_70321
#: model:account.group.template,name:l10n_lu.account_group_70321
msgid "Rental income from real property"
msgstr "Mieterträge aus Immobilien"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7422
#: model:account.group.template,name:l10n_lu.account_group_7422
msgid "Rental income on movable property"
msgstr "Mieterträge aus beweglichem Vermögen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7421
#: model:account.group.template,name:l10n_lu.account_group_7421
msgid "Rental income on real property"
msgstr "Mieterträge aus Immobilien"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6112
msgid "Rents and operational leasing on movable property"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6111
msgid "Rents and operationnal leasing for real property"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_611
msgid "Rents and service charges"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106191
msgid "Repairs to private buildings"
msgstr "Reparaturen an privaten Gebäuden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60812
#: model:account.group.template,name:l10n_lu.account_group_60812
msgid "Research and development"
msgstr "Forschung und Entwicklung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_13821
#: model:account.group.template,name:l10n_lu.account_group_13821
msgid "Reserve for net wealth tax (NWT)"
msgstr "Vermögenssteuerrücklage"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_13
msgid "Reserves"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_132
#: model:account.group.template,name:l10n_lu.account_group_132
msgid "Reserves for own shares or own corporate units"
msgstr "Rücklagen für eigene Aktien oder eigene Anteile"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_13822
#: model:account.group.template,name:l10n_lu.account_group_13822
msgid "Reserves in application of fair value"
msgstr "Rücklagen durch Anwendung des beizulegenden Zeitwerts (Fair Value)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_122
#: model:account.group.template,name:l10n_lu.account_group_122
msgid "Reserves in application of the equity method"
msgstr "Rücklagen durch Anwendung der Kapitalanteilsmethode "

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_13828
#: model:account.group.template,name:l10n_lu.account_group_13828
msgid "Reserves not available for distribution not mentioned above"
msgstr "Gebundene Rücklagen welche nicht oben aufgelistet wurden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_133
#: model:account.group.template,name:l10n_lu.account_group_133
msgid "Reserves provided for by the articles of association"
msgstr "Satzungsmäßige Rücklagen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221311
#: model:account.group.template,name:l10n_lu.account_group_221311
msgid "Residential buildings"
msgstr "Wohngebäude"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_142
#: model:account.group.template,name:l10n_lu.account_group_142
msgid "Result for the financial year"
msgstr "Ergebnis des Geschäftsjahres"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_14
msgid "Result for the financial year and results brought forward"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_141
msgid "Results brought forward"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1412
#: model:account.group.template,name:l10n_lu.account_group_1412
msgid "Results brought forward (assigned)"
msgstr "Ergebnisvorträge (zugewiesen)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1411
#: model:account.group.template,name:l10n_lu.account_group_1411
msgid "Results brought forward in the process of assignment"
msgstr "Ergebnisvorträge in Zuweisung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2237
#: model:account.group.template,name:l10n_lu.account_group_2237
msgid "Returnable packaging"
msgstr "Wiederverwendbare Verpackungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_12
msgid "Revaluation reserves"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_759
msgid "Reversals of financial provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7591
#: model:account.group.template,name:l10n_lu.account_group_7591
msgid "Reversals of financial provisions - affiliated undertakings"
msgstr ""
"Wertaufholungen von finanziellen Rückstellungen - verbundene Unternehmen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7592
#: model:account.group.template,name:l10n_lu.account_group_7592
msgid "Reversals of financial provisions - other"
msgstr "Wertaufholungen von finanziellen Rückstellungen - sonstige"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7492
#: model:account.group.template,name:l10n_lu.account_group_7492
msgid "Reversals of operating provisions"
msgstr "Wertaufholungen von betrieblichen Rückstellungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_749
msgid "Reversals of provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_779
#: model:account.group.template,name:l10n_lu.account_group_779
msgid "Reversals of provisions for deferred taxes"
msgstr "Auflösungen von Rückstellungen für Ertragssteuern"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7491
#: model:account.group.template,name:l10n_lu.account_group_7491
msgid "Reversals of provisions for taxes"
msgstr "Wertaufholungen von Steuerrückstellungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_747
msgid ""
"Reversals of temporarily not taxable capital gains and of investment "
"subsidies"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_751
msgid ""
"Reversals of value adjustments (RVA) and fair-value adjustments (FVA) on "
"financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_753
msgid ""
"Reversals of value adjustments (RVA) and fair-value adjustments (FVA) on "
"transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_73
msgid ""
"Reversals of value adjustments (RVA) on intangible, tangible and current "
"assets (except transferable securities)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61123
#: model:account.account.template,name:l10n_lu.lu_2011_account_61153
#: model:account.account.template,name:l10n_lu.lu_2011_account_61223
#: model:account.account.template,name:l10n_lu.lu_2011_account_61412
#: model:account.group.template,name:l10n_lu.account_group_61123
#: model:account.group.template,name:l10n_lu.account_group_61153
#: model:account.group.template,name:l10n_lu.account_group_61223
#: model:account.group.template,name:l10n_lu.account_group_61412
msgid "Rolling stock"
msgstr "Fuhrpark"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_703001
msgid "Sale of Services"
msgstr "Verkäufe von Dienstleistungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7063
#: model:account.group.template,name:l10n_lu.account_group_7063
msgid "Sales of buildings for resale"
msgstr "Verkäufe von zum Verkauf bestimmten Bauten/Gebäuden "

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7021
#: model:account.group.template,name:l10n_lu.account_group_7021
msgid "Sales of finished goods"
msgstr "Verkäufe von fertigen Erzeugnissen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_702
msgid "Sales of goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7062
#: model:account.group.template,name:l10n_lu.account_group_7062
msgid "Sales of land resale"
msgstr "Verkäufe von zum Verkauf bestimmten Grundstücken"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7061
#: model:account.group.template,name:l10n_lu.account_group_7061
msgid "Sales of merchandise"
msgstr "Verkäufe von Waren"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_706
msgid "Sales of merchandise and other goods for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_704
#: model:account.group.template,name:l10n_lu.account_group_704
msgid "Sales of packaging"
msgstr "Verkäufe von Verpackungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7023
#: model:account.group.template,name:l10n_lu.account_group_7023
msgid "Sales of residual products"
msgstr "Verkäufe von Restprodukten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7022
#: model:account.group.template,name:l10n_lu.account_group_7022
msgid "Sales of semi-finished goods"
msgstr "Verkäufe von Zwischenprodukten"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_703
msgid "Sales of services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7039
#: model:account.group.template,name:l10n_lu.account_group_7039
msgid "Sales of services in the course of completion"
msgstr "Nicht ausgeführte Dienstleistungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7033
#: model:account.group.template,name:l10n_lu.account_group_7033
msgid "Sales of services not mentioned above"
msgstr "Dienstleistungen welche nicht oben erwähnt wurden"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7029
#: model:account.group.template,name:l10n_lu.account_group_7029
msgid "Sales of work in progress"
msgstr "Verkäufe von unfertigen Erzeugnissen "

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61512
#: model:account.group.template,name:l10n_lu.account_group_61512
msgid "Samples"
msgstr "Muster"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75115
#: model:account.account.template,name:l10n_lu.lu_2020_account_65215
#: model:account.account.template,name:l10n_lu.lu_2020_account_75215
#: model:account.group.template,name:l10n_lu.account_group_235
#: model:account.group.template,name:l10n_lu.account_group_65215
#: model:account.group.template,name:l10n_lu.account_group_75215
#: model:account.group.template,name:l10n_lu.account_group_75225
msgid "Securities held as fixed assets"
msgstr "Wertpapiere des Anlagevermögens"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_2352
msgid "Securities held as fixed assets (creditor's right)"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_2351
msgid "Securities held as fixed assets (equity right)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6113
#: model:account.group.template,name:l10n_lu.account_group_6113
msgid "Service charges and co-ownership expenses"
msgstr "Mietnebenkosten und Miteigentumsgemeinschaftskosten"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6081
msgid "Services included in the production of goods and services"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6122
msgid "Servicing, repairs and maintenance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_201
#: model:account.group.template,name:l10n_lu.account_group_201
msgid "Set-up and start-up costs"
msgstr "Gründungs- und Einrichtungskosten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_62116
#: model:account.group.template,name:l10n_lu.account_group_62116
msgid "Severance pay"
msgstr "Abfindungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_657
#: model:account.group.template,name:l10n_lu.account_group_657
msgid ""
"Share in the losses of undertakings accounted for under the equity method"
msgstr ""
"Verlustanteile in den gemeinsamen Unternehmen (andere als "
"Kapitalgesellschaften)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_757
#: model:account.group.template,name:l10n_lu.account_group_757
msgid "Share of profit from undertakings accounted for under the equity method"
msgstr "Gewinnanteil aus Unternehmungen (andere als Kapitalgesellschaften)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_111
#: model:account.group.template,name:l10n_lu.account_group_111
msgid "Share premium"
msgstr "Ausgabeagio"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_11
msgid "Share premium and similar premiums"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5081
#: model:account.group.template,name:l10n_lu.account_group_5081
msgid "Shares - listed securities"
msgstr "Aktien - Notierte Wertpapiere"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5082
#: model:account.group.template,name:l10n_lu.account_group_5082
msgid "Shares - unlisted securities"
msgstr "Aktien - Nicht notierte Wertpapiere"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_231
#: model:account.account.template,name:l10n_lu.lu_2011_account_501
#: model:account.account.template,name:l10n_lu.lu_2011_account_75111
#: model:account.account.template,name:l10n_lu.lu_2011_account_75481
#: model:account.account.template,name:l10n_lu.lu_2020_account_65211
#: model:account.account.template,name:l10n_lu.lu_2020_account_65421
#: model:account.account.template,name:l10n_lu.lu_2020_account_75211
#: model:account.account.template,name:l10n_lu.lu_2020_account_75311
#: model:account.account.template,name:l10n_lu.lu_2020_account_75421
#: model:account.group.template,name:l10n_lu.account_group_231
#: model:account.group.template,name:l10n_lu.account_group_501
#: model:account.group.template,name:l10n_lu.account_group_65211
#: model:account.group.template,name:l10n_lu.account_group_65421
#: model:account.group.template,name:l10n_lu.account_group_75211
#: model:account.group.template,name:l10n_lu.account_group_75221
#: model:account.group.template,name:l10n_lu.account_group_75421
#: model:account.group.template,name:l10n_lu.account_group_75481
msgid "Shares in affiliated undertakings"
msgstr "Anteile an verbundenen Unternehmen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_65423
msgid ""
"Shares in in undertakings with which the undertaking is linked by virtue of "
"participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_503
#: model:account.account.template,name:l10n_lu.lu_2011_account_75483
#: model:account.account.template,name:l10n_lu.lu_2020_account_65423
#: model:account.account.template,name:l10n_lu.lu_2020_account_75313
#: model:account.account.template,name:l10n_lu.lu_2020_account_75423
#: model:account.group.template,name:l10n_lu.account_group_503
#: model:account.group.template,name:l10n_lu.account_group_75423
#: model:account.group.template,name:l10n_lu.account_group_75483
msgid ""
"Shares in undertakings with which the undertaking is linked by virtue of "
"participating interests"
msgstr "Anteile an Unternehmen, mit denen ein Beteiligungsverhältnis besteht"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_2353
#: model:account.group.template,name:l10n_lu.account_group_2353
msgid "Shares of collective investment funds"
msgstr "OPC Anteile"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_23511
msgid "Shares or corporate units"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_21215
#: model:account.group.template,name:l10n_lu.account_group_21225
#: model:account.group.template,name:l10n_lu.account_group_6415
#: model:account.group.template,name:l10n_lu.account_group_70315
#: model:account.group.template,name:l10n_lu.account_group_72125
#: model:account.group.template,name:l10n_lu.account_group_7415
msgid "Similar rights and assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_61852
#: model:account.group.template,name:l10n_lu.account_group_61852
msgid "Small equipment"
msgstr "Kleines Werkzeug"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106151
msgid "Social Security"
msgstr "Sozialversicherungen (Pflegeversicherung)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42171
#: model:account.account.template,name:l10n_lu.lu_2011_account_4621
msgid "Social Security office (CCSS)"
msgstr "Sozialversicherungszentrum (CCSS)"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_623
msgid "Social security costs (employer's share)"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_462
msgid "Social security debts and other social securities offices"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6231
#: model:account.group.template,name:l10n_lu.account_group_6231
msgid "Social security on pensions"
msgstr "Soziale Aufwendungen für Renten "

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_21213
#: model:account.account.template,name:l10n_lu.lu_2011_account_21223
#: model:account.account.template,name:l10n_lu.lu_2011_account_6413
#: model:account.account.template,name:l10n_lu.lu_2011_account_72123
#: model:account.account.template,name:l10n_lu.lu_2011_account_7413
#: model:account.account.template,name:l10n_lu.lu_2020_account_70313
#: model:account.group.template,name:l10n_lu.account_group_21213
#: model:account.group.template,name:l10n_lu.account_group_21223
#: model:account.group.template,name:l10n_lu.account_group_6413
#: model:account.group.template,name:l10n_lu.account_group_70313
#: model:account.group.template,name:l10n_lu.account_group_72123
#: model:account.group.template,name:l10n_lu.account_group_7413
msgid "Software licences"
msgstr "Software- und Softwarepaketlizenzen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60311
#: model:account.account.template,name:l10n_lu.lu_2020_account_61841
#: model:account.group.template,name:l10n_lu.account_group_60311
#: model:account.group.template,name:l10n_lu.account_group_61841
msgid "Solid fuels"
msgstr "Feste Brennstoffe"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61517
#: model:account.group.template,name:l10n_lu.account_group_61517
msgid "Sponsorship"
msgstr "Sponsoring"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_615212
#: model:account.group.template,name:l10n_lu.account_group_615212
msgid "Staff"
msgstr "Personal"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4211
msgid "Staff - Advances and down payments"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4221
#: model:account.group.template,name:l10n_lu.account_group_4221
msgid "Staff - advances and down payments"
msgstr "Personal - Vorschüsse und geleistete Anzahlungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_62
msgid "Staff expenses"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_621
msgid "Staff remuneration"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_483
msgid "State - Greenhous gas and similar emission quotas received"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4715
#: model:account.group.template,name:l10n_lu.account_group_4715
msgid ""
"State - Greenhous gas and similar emission quotas to be returned or acquired"
msgstr ""
"Staat - zurückzugebende oder zu erwerbende Kontigente für "
"Treibhausgasemissionen und ähnliche"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_483
msgid "State - Greenhouse gas and similar emission quotas received"
msgstr "Staat - zugeteilte Kontigente für Treibhausgasemissionen und ähnliche"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4213
#: model:account.group.template,name:l10n_lu.account_group_4223
msgid "State - Subsidies to be received"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6221
#: model:account.group.template,name:l10n_lu.account_group_6221
msgid "Students"
msgstr "Studentische Aushilfskräfte"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_612
msgid "Subcontracting, servicing, repairs and maintenance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_101
#: model:account.group.template,name:l10n_lu.account_group_101
msgid "Subscribed capital"
msgstr "Gezeichnetes Kapital "

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_103
#: model:account.group.template,name:l10n_lu.account_group_103
msgid "Subscribed capital called but unpaid"
msgstr "Gezeichnetes eingefordertes und nicht eingezahltes Kapital"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_102
#: model:account.group.template,name:l10n_lu.account_group_102
msgid "Subscribed capital not called"
msgstr "Gezeichnetes nicht eingefordertes Kapital "

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_10
msgid "Subscribed capital or branches' assigned capital and owner's account"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421622
#: model:account.account.template,name:l10n_lu.lu_2011_account_461422
#: model:account.account.template,name:l10n_lu.lu_2011_account_682
#: model:account.group.template,name:l10n_lu.account_group_421622
#: model:account.group.template,name:l10n_lu.account_group_461422
#: model:account.group.template,name:l10n_lu.account_group_682
msgid "Subscription tax"
msgstr "Abgeltungssteuer"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_745
msgid "Subsidies for operating activities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7454
#: model:account.group.template,name:l10n_lu.account_group_7454
msgid "Subsidies in favour of employment development"
msgstr "Zuschüsse zur Beschäftigungsförderung"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_161
msgid "Subsidies on intangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1621
#: model:account.group.template,name:l10n_lu.account_group_1621
msgid "Subsidies on land, fitting-outs and buildings"
msgstr "Investitionszuschüsse auf Grundstücke, Erschließungen und Bauten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1623
#: model:account.group.template,name:l10n_lu.account_group_1623
msgid ""
"Subsidies on other fixtures, fittings, tools and equipment (including "
"rolling stock)"
msgstr ""
"Investitionszuschüsse auf sonstige Anlagen, Betriebs- und "
"Geschäftsausstattung (Fuhrpark inbegriffen)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1622
#: model:account.group.template,name:l10n_lu.account_group_1622
msgid "Subsidies on plant and machinery"
msgstr "Investitionszuschüsse auf Ttechnische Anlagen und Maschinen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_162
msgid "Subsidies on tangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_621121
#: model:account.group.template,name:l10n_lu.account_group_621121
msgid "Sunday"
msgstr "Sonntagsarbeit"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_44111
#: model:account.account.template,name:l10n_lu.lu_2011_account_44121
#: model:account.group.template,name:l10n_lu.account_group_44111
#: model:account.group.template,name:l10n_lu.account_group_44121
msgid "Suppliers"
msgstr "Lieferanten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_44112
#: model:account.group.template,name:l10n_lu.account_group_44112
msgid "Suppliers - invoices not yet received"
msgstr "Lieferanten - Noch nicht erhaltene Rechnungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_44113
#: model:account.account.template,name:l10n_lu.lu_2020_account_44123
#: model:account.group.template,name:l10n_lu.account_group_44113
#: model:account.group.template,name:l10n_lu.account_group_44123
msgid "Suppliers with a debit balance"
msgstr "Lieferanten - Debitorische Kreditoren"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6185
msgid "Supplies and small equipment"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6186
#: model:account.group.template,name:l10n_lu.account_group_6186
msgid "Surveillance and security charges"
msgstr "Wach- und Sicherheitsdienste"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_62117
#: model:account.group.template,name:l10n_lu.account_group_62117
msgid "Survivor's pay"
msgstr "Hinterbliebenenzuschuss"

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_0
msgid "TVA 0%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_10
msgid "TVA 10%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_12
msgid "TVA 12%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_14
msgid "TVA 14%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_15
msgid "TVA 15%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_17
msgid "TVA 17%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_3
msgid "TVA 3%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_6
msgid "TVA 6%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_8
msgid "TVA 8%"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60811
#: model:account.group.template,name:l10n_lu.account_group_60811
msgid "Tailoring"
msgstr "Lohnverarbeitung"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_22
#: model:account.group.template,name:l10n_lu.account_group_722
msgid "Tangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_46
msgid "Tax and social security debts"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_461
msgid "Tax debts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6733
#: model:account.group.template,name:l10n_lu.account_group_6733
msgid "Taxes levied on non-resident undertakings"
msgstr ""
"Steuern, die durch die nicht gebietsansässige Unternehmen getragen wurden"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6732
msgid "Taxes levied on permanent establishments"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_646
msgid "Taxes, duties and similar expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61532
#: model:account.group.template,name:l10n_lu.account_group_61532
msgid "Telecommunication costs"
msgstr "Sonstige Telekommunikationskosten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106165
msgid "Telephone"
msgstr "Telefon"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_13823
msgid "Temporarily not taxable capital gains"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7471
#: model:account.group.template,name:l10n_lu.account_group_7471
msgid "Temporarily not taxable capital gains not reinvested"
msgstr "Sonderposten mit Rücklageanteil, nicht wiederangelegt"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7472
#: model:account.account.template,name:l10n_lu.lu_2020_account_138232
#: model:account.group.template,name:l10n_lu.account_group_138232
#: model:account.group.template,name:l10n_lu.account_group_7472
msgid "Temporarily not taxable capital gains reinvested"
msgstr "Sonderposten mit Rücklageanteil, wiederangelegt"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_138231
#: model:account.group.template,name:l10n_lu.account_group_138231
msgid "Temporarily not taxable capital gains to reinvest"
msgstr "Sonderposten mit Rücklageanteil, zur Wiederanlage bestimmt"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_123
#: model:account.group.template,name:l10n_lu.account_group_123
msgid "Temporarily not taxable currency translation adjustments"
msgstr "Rücklagen aus der Währungsumrechnung (unversteuert)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6171
#: model:account.group.template,name:l10n_lu.account_group_6171
msgid "Temporary staff"
msgstr "Aushilfskräfte"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106144
#: model:account.account.template,name:l10n_lu.lu_2011_account_6146
#: model:account.group.template,name:l10n_lu.account_group_6146
msgid "Third-party insurance"
msgstr "Haftpflichtversicherung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2233
#: model:account.group.template,name:l10n_lu.account_group_2233
msgid "Tools"
msgstr "Werkzeuge"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_441
msgid "Trade payables"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4412
msgid "Trade payables after more than one year"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_44
msgid "Trade payables and bills of exchange"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_4411
msgid "Trade payables within one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_41111
#: model:account.account.template,name:l10n_lu.lu_2011_account_41121
#: model:account.account.template,name:l10n_lu.lu_2011_account_41211
#: model:account.account.template,name:l10n_lu.lu_2011_account_41221
#: model:account.account.template,name:l10n_lu.lu_2011_account_6451
#: model:account.group.template,name:l10n_lu.account_group_41111
#: model:account.group.template,name:l10n_lu.account_group_41121
#: model:account.group.template,name:l10n_lu.account_group_41211
#: model:account.group.template,name:l10n_lu.account_group_41221
#: model:account.group.template,name:l10n_lu.account_group_6451
msgid "Trade receivables"
msgstr "Verkäufe und Dienstleistungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_40
msgid "Trade receivables (Receivables from sales and rendering of services)"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_402
msgid "Trade receivables due and payable after more than one year"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_401
msgid "Trade receivables due and payable within one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6414
#: model:account.group.template,name:l10n_lu.account_group_6414
msgid "Trademarks and franchise"
msgstr "Warenzeichen und Franchising (Verkaufskonzession/Alleinverkaufsrecht)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_21214
#: model:account.account.template,name:l10n_lu.lu_2011_account_21224
#: model:account.account.template,name:l10n_lu.lu_2011_account_72124
#: model:account.account.template,name:l10n_lu.lu_2011_account_7414
#: model:account.account.template,name:l10n_lu.lu_2020_account_70314
#: model:account.group.template,name:l10n_lu.account_group_21214
#: model:account.group.template,name:l10n_lu.account_group_21224
#: model:account.group.template,name:l10n_lu.account_group_70314
#: model:account.group.template,name:l10n_lu.account_group_72124
#: model:account.group.template,name:l10n_lu.account_group_7414
msgid "Trademarks and franchises"
msgstr "Warenzeichen und Verkaufskonzession / Alleinverkaufsrecht"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_50
msgid "Transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_484
#: model:account.group.template,name:l10n_lu.account_group_484
msgid "Transitory or suspense accounts - Assets"
msgstr "Transitorische Konten - Aktiva"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_485
#: model:account.group.template,name:l10n_lu.account_group_485
msgid "Transitory or suspense accounts - Liabilities"
msgstr "Transitorische Konten - Passiva"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6143
#: model:account.group.template,name:l10n_lu.account_group_6143
msgid "Transport insurance"
msgstr "Transportversicherung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2231
#: model:account.group.template,name:l10n_lu.account_group_2231
msgid "Transportation and handling equipment"
msgstr "Transport- und Wartungsmittel"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_616
msgid "Transportation of goods and collective staff transportation"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6161
#: model:account.group.template,name:l10n_lu.account_group_6161
msgid "Transportation of purchased goods"
msgstr "Transporte von Einkäufen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6162
#: model:account.group.template,name:l10n_lu.account_group_6162
msgid "Transportation of sold goods"
msgstr "Transporte von Verkäufen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_6152
msgid "Travel and entertainment expenses"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_61521
msgid "Travel expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6099
#: model:account.group.template,name:l10n_lu.account_group_6099
msgid "Unallocated RDR"
msgstr "Nicht zugeordnete Rabatte, Preisnachlässe und Rückvergütungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5085
#: model:account.group.template,name:l10n_lu.account_group_5085
msgid "Unlisted debenture loans"
msgstr "Anleihen - Nicht notierte Wertpapiere"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_235112
#: model:account.group.template,name:l10n_lu.account_group_235112
msgid "Unlisted shares"
msgstr "Nicht notierte Aktien"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_60
msgid "Use of merchandise, raw and consumable materials"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461418
#: model:account.group.template,name:l10n_lu.account_group_461418
msgid "VAT - Other payables"
msgstr "MwSt - Sonstige Verbindlichkeiten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421618
#: model:account.group.template,name:l10n_lu.account_group_421618
msgid "VAT - Other receivables"
msgstr "MwSt - Sonstige Forderungen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421613
#: model:account.group.template,name:l10n_lu.account_group_421613
msgid "VAT down payments made"
msgstr "Geleistete Anzahlungen auf MwSt"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461413
#: model:account.group.template,name:l10n_lu.account_group_461413
msgid "VAT down payments received"
msgstr "Erhaltene Anzahlungen auf MwSt"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_421611
#: model:account.group.template,name:l10n_lu.account_group_421611
msgid "VAT paid and recoverable"
msgstr "Vorsteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461412
#: model:account.group.template,name:l10n_lu.account_group_461412
msgid "VAT payable"
msgstr "Zu zahlende MwSt"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421612
#: model:account.group.template,name:l10n_lu.account_group_421612
msgid "VAT receivable"
msgstr "Zu erhaltende MwSt"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_461411
#: model:account.group.template,name:l10n_lu.account_group_461411
msgid "VAT received"
msgstr "Umsatzsteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4019
#: model:account.account.template,name:l10n_lu.lu_2011_account_4029
#: model:account.account.template,name:l10n_lu.lu_2011_account_41119
#: model:account.account.template,name:l10n_lu.lu_2011_account_41129
#: model:account.account.template,name:l10n_lu.lu_2011_account_41219
#: model:account.account.template,name:l10n_lu.lu_2011_account_41229
#: model:account.account.template,name:l10n_lu.lu_2011_account_42119
#: model:account.account.template,name:l10n_lu.lu_2011_account_42189
#: model:account.account.template,name:l10n_lu.lu_2011_account_42289
#: model:account.group.template,name:l10n_lu.account_group_4019
#: model:account.group.template,name:l10n_lu.account_group_4029
#: model:account.group.template,name:l10n_lu.account_group_41119
#: model:account.group.template,name:l10n_lu.account_group_41129
#: model:account.group.template,name:l10n_lu.account_group_41219
#: model:account.group.template,name:l10n_lu.account_group_41229
#: model:account.group.template,name:l10n_lu.account_group_42119
#: model:account.group.template,name:l10n_lu.account_group_42189
#: model:account.group.template,name:l10n_lu.account_group_42289
msgid "Value adjustments"
msgstr "Wertberichtigungen"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_42161
#: model:account.group.template,name:l10n_lu.account_group_46141
msgid "Value-added tax (VAT)"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_62112
msgid "Wage supplements"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106161
msgid "Wages"
msgstr "Löhne "

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106164
msgid "Water"
msgstr "Wasser"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60314
#: model:account.group.template,name:l10n_lu.account_group_60314
msgid "Water and sewage"
msgstr "Wasser und Abwasser"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_61844
#: model:account.group.template,name:l10n_lu.account_group_61844
msgid "Water and waste water"
msgstr "Wasserversorgung und Abwasserbeseitigung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10612
msgid "Withdrawals of merchandise, finished products and services (at cost)"
msgstr ""
"Sachentnahme von Waren, von fertigen Erzeugnissen und Dienstleistungen (zu "
"Einstandspreisen)"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_62413
#: model:account.group.template,name:l10n_lu.account_group_62413
msgid "Withholding tax on complementary pensions"
msgstr "Einbehaltene Steuer auf Zusatzrenten"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_46126
#: model:account.account.template,name:l10n_lu.lu_2020_account_42146
#: model:account.group.template,name:l10n_lu.account_group_42146
#: model:account.group.template,name:l10n_lu.account_group_46126
msgid "Withholding tax on director's fees"
msgstr "Einbehaltene Steuer auf Tantiemen"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_46125
#: model:account.account.template,name:l10n_lu.lu_2020_account_42145
#: model:account.group.template,name:l10n_lu.account_group_42145
#: model:account.group.template,name:l10n_lu.account_group_46125
msgid "Withholding tax on financial investment income"
msgstr "Einbehaltene Kapitalertragsteuer"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_46124
#: model:account.account.template,name:l10n_lu.lu_2020_account_42144
#: model:account.group.template,name:l10n_lu.account_group_42144
#: model:account.group.template,name:l10n_lu.account_group_46124
msgid "Withholding tax on wages and salaries"
msgstr "Einbehaltene Steuer auf Gehälter und Löhne"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6731
#: model:account.group.template,name:l10n_lu.account_group_6731
msgid "Withholding taxes"
msgstr "Quellensteuern"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6034
#: model:account.account.template,name:l10n_lu.lu_2020_account_61853
#: model:account.group.template,name:l10n_lu.account_group_6034
#: model:account.group.template,name:l10n_lu.account_group_61853
msgid "Work clothes"
msgstr "Berufsbekleidung"

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6033
#: model:account.group.template,name:l10n_lu.account_group_6033
msgid "Workshop, factory and store supplies and small equipment"
msgstr "Werkstatt-, Fabrik- und Ladenausstattung"

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_16121
msgid "acquired against payment (except Goodwill)"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_2121
msgid "acquired for consideration (except Goodwill)"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_16122
#: model:account.group.template,name:l10n_lu.account_group_2122
msgid "created by the undertaking itself"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_1922
#: model:account.group.template,name:l10n_lu.account_group_1932
#: model:account.group.template,name:l10n_lu.account_group_1942
msgid "due and payable after more than one year"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_1921
#: model:account.group.template,name:l10n_lu.account_group_1931
#: model:account.group.template,name:l10n_lu.account_group_1941
msgid "due and payable within one year"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_755231
msgid "from affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_755232
msgid "from other"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_65413
msgid "from other receivables from current assets"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75411
msgid "on affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75413
msgid "on other current receivables"
msgstr ""

#. module: l10n_lu
#: model:account.group.template,name:l10n_lu.account_group_75412
msgid ""
"on undertakings with which the undertaking is linked by virtue of "
"participating interests"
msgstr ""
