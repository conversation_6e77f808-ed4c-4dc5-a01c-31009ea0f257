<?xml version="1.0" encoding="UTF-8"?>
<templates>

    <!--
        @param {mail.systray.ActivityMenu} widget
        @param {Object[]} widget.activities
    -->
    <t t-name="mail.systray.ActivityMenu.Previews">
        <t t-set="activities" t-value="widget._activities"/>
        <t t-if="_.isEmpty(activities)">
            <div class="dropdown-item-text text-center o_no_activity d-flex justify-content-center">
                <span>Congratulations, you're done with your activities.</span>
            </div>
        </t>
        <t t-foreach="activities" t-as="activity">
            <div class="o_mail_preview o_systray_activity" t-att-data-res_model="activity.model" t-att-data-model_name="activity.name" t-att-data-domain="activity.domain" data-filter='my'>
                <div t-if="activity.icon" class="o_mail_preview_image o_mail_preview_app">
                    <img t-att-src="activity.icon" alt="Activity"/>
                </div>
                <div class="o_preview_info">
                    <div class="o_preview_title">
                        <span class="o_preview_name">
                            <t t-esc="activity.name"/>
                        </span>
                        <div t-if="activity.actions" class="o_mail_activity_action_buttons">
                            <t t-foreach="activity.actions" t-as="action">
                                <button type="button"
                                    t-att-title="action.name"
                                    t-att-class="'o_mail_activity_action btn btn-link fa ' + action.icon"
                                    t-att-data-action_xmlid="action.action_xmlid"
                                    t-att-data-res_model="activity.model"
                                    t-att-data-model_name="activity.name"
                                    t-att-data-domain="activity.domain">
                                </button>
                            </t>
                        </div>
                    </div>
                    <div t-if="activity and activity.type == 'activity'">
                        <button t-if="activity.overdue_count" type="button" class="btn btn-link o_activity_filter_button mr16" t-att-data-res_model="activity.model" t-att-data-model_name="activity.name" data-filter='overdue'><t t-esc="activity.overdue_count"/> Late </button>
                        <span t-if="!activity.overdue_count" class="o_no_activity mr16">0 Late </span>
                        <button t-if="activity.today_count" type="button" class="btn btn-link o_activity_filter_button mr16" t-att-data-res_model="activity.model" t-att-data-model_name="activity.name" data-filter='today'> <t t-esc="activity.today_count"/> Today </button>
                        <span t-if="!activity.today_count" class="o_no_activity mr16">0 Today </span>
                        <button t-if="activity.planned_count" type="button" class="btn btn-link o_activity_filter_button float-right" t-att-data-res_model="activity.model" t-att-data-model_name="activity.name" data-filter='upcoming_all'> <t t-esc="activity.planned_count"/> Future </button>
                        <span t-if="!activity.planned_count" class="o_no_activity float-right">0 Future</span>
                    </div>
                </div>
            </div>
        </t>
    </t>

    <t t-name="mail.systray.ActivityMenu">
        <div class="o_mail_systray_item dropdown">
            <a class="dropdown-toggle o-no-caret o-dropdown--narrow" data-toggle="dropdown" data-display="static" aria-expanded="false" title="Activities" href="#" role="button">
                <i class="fa fa-clock-o" role="img" aria-label="Activities"/> <span class="o_notification_counter badge badge-pill"/>
            </a>
            <div class="o_mail_systray_dropdown dropdown-menu dropdown-menu-right" role="menu">
                <div class="o_mail_systray_dropdown_items"/>
            </div>
        </div>
    </t>
</templates>
