<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="hr.employee_mit" model="hr.employee">
            <field name="address_home_id" ref="base.res_partner_address_3"/>
        </record>

        <record id="hr_expense_account_journal" model="account.journal">
            <field name="name">Expense</field>
            <field name="code">EXP</field>
            <field name="type">purchase</field>
            <!-- avoid being selected as default journal -->
            <field name="sequence">99</field>
            <field name="alias_name">purchase_expense</field>
        </record>

        <record id="res_partner_address_fp" model="res.partner">
            <field name="name">Pieter Parter's Farm</field>
            <field name="parent_id" ref="base.partner_root"/>
            <field name="street">Chaussée de Namur, 40</field>
            <field name="zip">1367</field>
            <field name="city">Grand-Rosière-Hottomont</field>
            <field name="country_id" ref="base.be"/>
            <field name="type">private</field>
        </record>

        <record id="hr.employee_fme" model="hr.employee">
            <field name="address_home_id" ref="res_partner_address_fp"/>
        </record>

        <record id="hr.employee_al" model="hr.employee">
            <field name="address_home_id" ref="res_partner_address_fp"/>
        </record>

        <record id="food_expense_product" model="product.product">
            <field name="name">Food &amp; beverages</field>
            <field name="list_price">10</field>
            <field name="standard_price">0</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FOOD</field>
            <field name="can_be_expensed" eval="True"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/food.png"/>
        </record>
        <record id="trans_expense_product" model="product.product">
            <field name="name">Flights, train, bus, taxi, parking</field>
            <field name="standard_price">0</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">TRANS</field>
            <field name="can_be_expensed" eval="True"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/transport.png"/>
        </record>
        <record id="mileage_expense_product" model="product.product">
            <field name="name">Mileage</field>
            <field name="standard_price">0.37</field>
            <field name="uom_id" ref="uom.product_uom_km"/>
            <field name="uom_po_id" ref="uom.product_uom_km"/>
            <field name="default_code">MIL</field>
            <field name="can_be_expensed" eval="True"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/mileage.png"/>
        </record>
        <record id="accomodation_expense_product" model="product.product">
            <field name="name">Accomodation</field>
            <field name="standard_price">0</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">ACC</field>
            <field name="can_be_expensed" eval="True"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/accomodation.png"/>
        </record>
        <record id="allowance_expense_product" model="product.product">
            <field name="name">Daily Allowance</field>
            <field name="standard_price">100</field>
            <field name="type">service</field>
            <field name="uom_id" ref="uom.product_uom_day"/>
            <field name="uom_po_id" ref="uom.product_uom_day"/>
            <field name="default_code">DAL</field>
            <field name="can_be_expensed" eval="True"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/allowance.png"/>
        </record>
        <record id="other_expense_product" model="product.product">
            <field name="standard_price">0</field>
            <field name="name">Other expenses</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">OTHER</field>
            <field name="can_be_expensed" eval="True"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="image_1920" type="base64" file="hr_expense/static/img/other.png"/>
        </record>

        <!-- ++++++++++++++ Expense sheet for Admin ++++++++++++++-->

        <record id="screen_expense" model="hr.expense">
            <field name="name">Screen</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_account_id" ref="analytic.analytic_our_super_product"/>
            <field name="product_id" ref="other_expense_product"/>
            <field eval="289.0" name="unit_amount"/>
            <field name="product_uom_id" ref="uom.product_uom_unit"/>
            <field eval="1.0" name="quantity"/>
            <field name="date" eval="time.strftime('%Y')+'-04-03'"/>
        </record>

        <record id="laptop_expense" model="hr.expense">
            <field name="name">Laptop</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_account_id" ref="analytic.analytic_our_super_product"/>
            <field name="product_id" ref="other_expense_product"/>
            <field eval="889.0" name="unit_amount"/>
            <field name="product_uom_id" ref="uom.product_uom_unit"/>
            <field eval="1.0" name="quantity"/>
            <field name="date" eval="time.strftime('%Y')+'-04-03'"/>
        </record>

        <record id="travel_admin_by_car_expense" model="hr.expense">
            <field name="name">Travel by car</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="product_id" ref="mileage_expense_product"/>
            <field eval="0.52" name="unit_amount"/>
            <field name="product_uom_id" ref="uom.product_uom_km"/>
            <field eval="320.0" name="quantity"/>
        </record>

        <record id="breakfast_admin_expense" model="hr.expense">
            <field name="name">BreakFast</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="product_id" ref="food_expense_product"/>
            <field eval="20" name="unit_amount"/>
            <field eval="1.0" name="quantity"/>
        </record>

        <record id="travel_ny_sheet" model="hr.expense.sheet">
            <field name="name">Commercial Travel at New York</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="state">approve</field>
        </record>

        <record id="travel_by_air_expense" model="hr.expense">
            <field name="name">Travel by Air</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_account_id" ref="analytic.analytic_our_super_product"/>
            <field name="product_id" ref="trans_expense_product"/>
            <field eval="700.0" name="unit_amount"/>
            <field name="product_uom_id" ref="uom.product_uom_unit"/>
            <field eval="1.0" name="quantity"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-12'"/>
            <field name="sheet_id" ref="travel_ny_sheet"/>
        </record>

        <record id="hotel_bill_expense" model="hr.expense">
            <field name="name">Hotel Expenses</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_account_id" ref="analytic.analytic_nebula"/>
            <field name="product_id" ref="accomodation_expense_product"/>
            <field eval="400.0" name="unit_amount"/>
            <field name="product_uom_id" ref="uom.product_uom_unit"/>
            <field eval="5.0" name="quantity"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-17'"/>
            <field name="sheet_id" ref="travel_ny_sheet"/>
        </record>

        <record id="lunch_customer_bill_expense" model="hr.expense">
            <field name="name">Lunch with Customer</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_account_id" ref="analytic.analytic_nebula"/>
            <field name="product_id" ref="food_expense_product"/>
            <field eval="152.8" name="unit_amount"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-13'"/>
            <field eval="1.0" name="quantity"/>
            <field name="sheet_id" ref="travel_ny_sheet"/>
        </record>

        <record id="lunch_bill_expense" model="hr.expense">
            <field name="name">Lunch</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="analytic_account_id" ref="analytic.analytic_nebula"/>
            <field name="product_id" ref="food_expense_product"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-15'"/>
            <field eval="56.8" name="unit_amount"/>
            <field eval="1.0" name="quantity"/>
            <field name="sheet_id" ref="travel_ny_sheet"/>
        </record>

        <!-- ++++++++++++++ Expense sheet for Demo ++++++++++++++-->
        <record id="customer_meeting_sheet" model="hr.expense.sheet">
            <field name="name">Customer meeting</field>
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="state">submit</field>
        </record>

        <record id="travel_demo_by_car_expense" model="hr.expense">
            <field name="name">Travel by Car</field>
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="analytic_account_id" ref="analytic.analytic_our_super_product"/>
            <field name="product_id" ref="mileage_expense_product"/>
            <field eval="0.52" name="unit_amount"/>
            <field name="product_uom_id" ref="uom.product_uom_km"/>
            <field name="date" eval="time.strftime('%Y')+'-01-15'"/>
            <field eval="152.0" name="quantity"/>
            <field name="sheet_id" ref="customer_meeting_sheet"/>
        </record>

        <record id="lunch_demo_customer_bill_expense" model="hr.expense">
            <field name="name">Lunch with Customer</field>
            <field name="employee_id" ref="hr.employee_qdp"/>
            <field name="analytic_account_id" ref="analytic.analytic_nebula"/>
            <field name="product_id" ref="other_expense_product"/>
            <field name="date" eval="time.strftime('%Y')+'-01-15'"/>
            <field eval="152.8" name="unit_amount"/>
            <field eval="1.0" name="quantity"/>
            <field name="sheet_id" ref="customer_meeting_sheet"/>
        </record>

        <!-- ++++++++++++++ Expense sheet for Keith Byrd ++++++++++++++-->
        <record id="team_building_sheet" model="hr.expense.sheet">
            <field name="name">Team Building</field>
            <field name="employee_id" ref="hr.employee_fme"/>
            <field name="state">submit</field>
        </record>

        <record id="pizzas_bill_expense" model="hr.expense">
            <field name="name">Pizzas</field>
            <field name="employee_id" ref="hr.employee_fme"/>
            <field name="analytic_account_id" ref="analytic.analytic_nebula"/>
            <field name="product_id" ref="food_expense_product"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-05'"/>
            <field eval="12.5" name="unit_amount"/>
            <field eval="12.0" name="quantity"/>
            <field name="sheet_id" ref="team_building_sheet"/>
        </record>

        <record id="drinks_bill_expense" model="hr.expense">
            <field name="name">Drinks</field>
            <field name="employee_id" ref="hr.employee_fme"/>
            <field name="analytic_account_id" ref="analytic.analytic_nebula"/>
            <field name="product_id" ref="food_expense_product"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-05'"/>
            <field eval="2.5" name="unit_amount"/>
            <field eval="17.0" name="quantity"/>
            <field name="sheet_id" ref="team_building_sheet"/>
        </record>

        <record id="paintball_bill_expense" model="hr.expense">
            <field name="name">Paintball</field>
            <field name="employee_id" ref="hr.employee_fme"/>
            <field name="analytic_account_id" ref="analytic.analytic_nebula"/>
            <field name="product_id" ref="other_expense_product"/>
            <field name="date" eval="time.strftime('%Y-%m')+'-05'"/>
            <field eval="25" name="unit_amount"/>
            <field eval="12.0" name="quantity"/>
            <field name="sheet_id" ref="team_building_sheet"/>
        </record>

        <!-- ++++++++++++++ Expense sheet for Ronnie Hart ++++++++++++++-->
        <record id="office_furniture_sheet" model="hr.expense.sheet">
            <field name="name">Office furniture</field>
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="state">submit</field>
        </record>

        <record id="chair_bill_expense" model="hr.expense">
            <field name="name">Chairs</field>
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="analytic_account_id" ref="analytic.analytic_nebula"/>
            <field name="product_id" ref="other_expense_product"/>
            <field name="date" eval="time.strftime('%Y')+'-06-02'"/>
            <field eval="55.75" name="unit_amount"/>
            <field eval="6.0" name="quantity"/>
            <field name="sheet_id" ref="office_furniture_sheet"/>
        </record>

        <record id="lamp_bill_expense" model="hr.expense">
            <field name="name">Lamp</field>
            <field name="employee_id" ref="hr.employee_al"/>
            <field name="analytic_account_id" ref="analytic.analytic_nebula"/>
            <field name="product_id" ref="other_expense_product"/>
            <field name="date" eval="time.strftime('%Y')+'-06-02'"/>
            <field eval="28.99" name="unit_amount"/>
            <field eval="1.0" name="quantity"/>
            <field name="sheet_id" ref="office_furniture_sheet"/>
        </record>

        <!-- ++++++++++++++ Expense for Randall Lewis ++++++++++++++-->
        <record id="afterwork_bill_expense" model="hr.expense">
            <field name="name">Car tyres</field>
            <field name="employee_id" ref="hr.employee_stw"/>
            <field name="analytic_account_id" ref="analytic.analytic_nebula"/>
            <field name="product_id" ref="other_expense_product"/>
            <field name="date" eval="time.strftime('%Y')+'-03-15'"/>
            <field eval="112.58" name="unit_amount"/>
            <field eval="4.0" name="quantity"/>
        </record>

    </data>
</odoo>
