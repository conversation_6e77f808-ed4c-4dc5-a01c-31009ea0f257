# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_livechat
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Layna Nascimento, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__session_count
msgid "# Sessions"
msgstr "# Sessões"

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "%s has left the conversation."
msgstr "%s deixou a conversa."

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid ""
"%s has started a conversation with %s. \n"
"                        The chat request has been canceled."
msgstr ""
"%s iniciou uma conversa com %s. \n"
"                        A solicitação de conversa foi cancelada."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<small>%</small>"
msgstr "<small>%</small>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Live Chat</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Live Chat</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Valores definidos aqui são específicos por site.\" groups=\"website.group_multi_website\"/>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<span>Livechat Channel</span>"
msgstr "<span>Canal de Chat</span>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "Available"
msgstr "Disponível"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Bad"
msgstr "Ruim"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__can_publish
msgid "Can Publish"
msgstr "Pode Publicar"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Channel"
msgstr "Canal"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_tree
msgid "Chat"
msgstr "Chat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Chats"
msgstr "Chats"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_res_config_settings
msgid "Config Settings"
msgstr "Definições de Configuração"

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_description
msgid "Description of the channel displayed on the website page"
msgstr "Descrição do canal exibido na página do site"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_mail_channel
msgid "Discussion Channel"
msgstr "Canal de Discussão"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Great"
msgstr "Ótimo"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_ir_http
msgid "HTTP Routing"
msgstr "Roteamento HTTP"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Happy face"
msgstr "Carinha Feliz"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "History"
msgstr "Histórico"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "In Conversation"
msgstr "em conversa"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__is_published
msgid "Is Published"
msgstr "Está publicado"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Lang"
msgstr "Idioma"

#. module: website_livechat
#: code:addons/website_livechat/models/website.py:0
#, python-format
msgid "Live Support"
msgstr "Atendimento ao Vivo"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Live chat channel of your website"
msgstr "Canal de site do seu site"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_im_livechat_channel
msgid "Livechat Channel"
msgstr "Canal de Chat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "Livechat Support Channels"
msgstr "Canais de Suporte ao Chat"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/js/website_livechat.editor.js:0
#, python-format
msgid "Name"
msgstr "Nome"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Neutral face"
msgstr "Carinha Neutra"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/js/website_livechat.editor.js:0
#, python-format
msgid "New Channel"
msgstr "Novo Canal"

#. module: website_livechat
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid "No Livechat Channel allows you to send a chat request for website %s."
msgstr ""
"Nenhum canal de chat permite que você envie uma solicitação de chat para o "
"site %s."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Not rated yet"
msgstr "Ainda não avaliado"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Okay"
msgstr "Certo"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Online"
msgstr "Online"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Operator Avatar"
msgstr "Avatar do operador"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_name
msgid "Operator Name"
msgstr "Nome do Operador"

#. module: website_livechat
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid ""
"Recipients are not available. Please refresh the page to get latest visitors"
" status."
msgstr ""
"Os destinatários não estão disponíveis. Por favor, atualize a página para "
"obter a situação dos últimos visitantes."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Sad face"
msgstr "Carinha Triste"

#. module: website_livechat
#: model:ir.actions.server,name:website_livechat.website_livechat_send_chat_request_action_server
msgid "Send Chat Requests"
msgstr "Enviar Solicitações de Chat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
msgid "Send chat request"
msgstr "Enviar solicitação de chat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Speaking With"
msgstr "Conversando Com"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_id
msgid "Speaking with"
msgstr "Conversando com"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Statistics"
msgstr "Estatísticas"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The"
msgstr "O"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The Team"
msgstr "A Equipe"

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_url
msgid "The full URL to access the document through the website."
msgstr "A URL completa para acessar o documento através do site."

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "The visitor"
msgstr "O visitante"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "There are no public livechat channels to show."
msgstr "Não há canais de chat públicos para exibir."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "There are no ratings for this channel for now."
msgstr "Não há avaliações para este canal por enquanto."

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_published
msgid "Visible on current website"
msgstr "Visível neste site"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_mail_channel__livechat_visitor_id
msgid "Visitor"
msgstr "Visitante"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Visitor is online"
msgstr "O visitante está online"

#. module: website_livechat
#: model:ir.actions.act_window,name:website_livechat.website_visitor_livechat_session_action
msgid "Visitor's Sessions"
msgstr "Sessões do Visitante"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__mail_channel_ids
msgid "Visitor's livechat channels"
msgstr "Canais de chat do visitante"

#. module: website_livechat
#: model:ir.ui.menu,name:website_livechat.website_livechat_visitor_menu
msgid "Visitors"
msgstr "Visitantes"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#: model:ir.model,name:website_livechat.model_website
#, python-format
msgid "Website"
msgstr "Website"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_res_config_settings__channel_id
msgid "Website Live Channel"
msgstr "Canal ao Vivo do Site"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website__channel_id
msgid "Website Live Chat Channel"
msgstr "Canal de Chat no Site"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_url
msgid "Website URL"
msgstr "URL do site"

#. module: website_livechat
#: code:addons/website_livechat/tests/test_livechat_basic_flow.py:0
#: model:ir.model,name:website_livechat.model_website_visitor
#, python-format
msgid "Website Visitor"
msgstr "Visitante do site"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_description
msgid "Website description"
msgstr "Descrição do site"

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "an operator"
msgstr "um operador"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "last feedbacks"
msgstr "últimas opiniões"
