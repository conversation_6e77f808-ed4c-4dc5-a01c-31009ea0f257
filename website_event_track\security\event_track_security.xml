<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="event_track_public" model="ir.rule">
        <field name="name">Event Tracks: public/portal: published</field>
        <field name="model_id" ref="website_event_track.model_event_track"/>
        <field name="domain_force">[('website_published', '=', True)]</field>
        <field name="groups" eval="[(4, ref('base.group_public')), (4, ref('base.group_portal'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="ir_rule_event_track_tag_public" model="ir.rule">
        <field name="name">Event Track Tag: public/portal: color = published</field>
        <field name="model_id" ref="website_event_track.model_event_track_tag"/>
        <field name="domain_force">['&amp;', ('color', '!=', False), ('color', '!=', 0)]</field>
        <field name="groups" eval="[(4, ref('base.group_public')), (4, ref('base.group_portal'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

</odoo>
