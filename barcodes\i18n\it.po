# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* barcodes
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: barcodes
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid " '*' is not a valid Regex Barcode Pattern. Did you mean '.*' ?"
msgstr ""
"«*» non è uno modello di espressione regolare valido per un codice a barre. "
"Si intendeva «.*» ?"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"<i>Barcodes Nomenclatures</i> define how barcodes are recognized and categorized.\n"
"                                When a barcode is scanned it is associated to the <i>first</i> rule with a matching\n"
"                                pattern. The pattern syntax is that of regular expression, and a barcode is matched\n"
"                                if the regular expression matches a prefix of the barcode."
msgstr ""
"Le <i>nomenclature codice a barre</i> definiscono il modo in cui i codici a barre vengono riconosciuti e classificati.\n"
"                                Quando un codice a barre viene acquisito viene associato alla <i>prima</i> regola che corrisponde\n"
"                                a un modello. La sintassi del modello è quella di un'espressione regolare. Il codice a barre trova riscontro\n"
"                                se l'espressione regolare corrisponde a un prefisso del codice a barre."

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid ""
"A barcode nomenclature defines how the point of sale identify and interprets"
" barcodes"
msgstr ""
"Una nomenclatura codice a barre definisce come il punto vendita identifica e"
" interpreta i codici"

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid "Add a new barcode nomenclature"
msgstr "Aggiunge una nuova nomenclatura codice a barre"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__alias
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__alias
msgid "Alias"
msgstr "Alias"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__always
msgid "Always"
msgstr "Sempre"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__name
msgid "An internal identification for this barcode nomenclature rule"
msgstr ""
"Un identificativo interno per questa regola di nomenclatura codice a barre"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__name
msgid "An internal identification of the barcode nomenclature"
msgstr "Un identificativo interno della nomenclatura codice a barre"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__any
msgid "Any"
msgstr "Qualsiasi"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcodes_barcode_events_mixin
msgid "Barcode Event Mixin"
msgstr "Mixin evento codice a barre"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_nomenclature
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Barcode Nomenclature"
msgstr "Nomenclatura codice a barre"

#. module: barcodes
#: model:ir.actions.act_window,name:barcodes.action_barcode_nomenclature_form
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_tree
msgid "Barcode Nomenclatures"
msgstr "Nomenclature codice a barre"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__pattern
msgid "Barcode Pattern"
msgstr "Modello codice"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_rule
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_rule_form
msgid "Barcode Rule"
msgstr "Regola codice a barre"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Barcode Scanned"
msgstr "Codice a barre scansionato"

#. module: barcodes
#: model:ir.model,name:barcodes.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:0
#, python-format
msgid "Discard"
msgstr "Abbandona"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__display_name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean13
msgid "EAN-13"
msgstr "EAN-13"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__ean2upc
msgid "EAN-13 to UPC-A"
msgstr "Da EAN-13 a UPC-A"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean8
msgid "EAN-8"
msgstr "EAN-8"

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:0
#, python-format
msgid "Enable edit mode to modify this document"
msgstr "Per cambiare questo documento abilitare la modalità di modifica"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__encoding
msgid "Encoding"
msgstr "Codifica"

#. module: barcodes
#: model:ir.model,name:barcodes.model_ir_http
msgid "HTTP Routing"
msgstr "Instradamento HTTP"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__id
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__id
msgid "ID"
msgstr "ID"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature____last_update
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__none
msgid "Never"
msgstr "Mai"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_res_company__nomenclature_id
msgid "Nomenclature"
msgstr "Nomenclatura"

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:0
#, python-format
msgid "Pager unavailable"
msgstr "Cercapersone non disponibile"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"Patterns can also define how numerical values, such as weight or price, can be\n"
"                                encoded into the barcode. They are indicated by <code>{NNN}</code> where the N's\n"
"                                define where the number's digits are encoded. Floats are also supported with the \n"
"                                decimals indicated with D's, such as <code>{NNNDD}</code>. In these cases, \n"
"                                the barcode field on the associated records <i>must</i> show these digits as \n"
"                                zeroes."
msgstr ""
"I modelli possono anche definire il modo in cui i valori numerici, come peso o prezzo, possono essere\n"
"                                codificati nel codice a barre. I valori sono indicati da <code>{NNN}</code>, dove i caratteri N\n"
"                                definiscono dove sono codificate le cifre del numero. Sono supportati anche i numeri a virgola mobile, con i \n"
"                                decimali indicati dai caratteri D, come <code>{NNNDD}</code>. In questi casi, \n"
"                                il campo codice a barre nei record associati <i>deve</i> mostrare queste cifre come \n"
"                                zeri."

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__name
msgid "Rule Name"
msgstr "Nome regola"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__rule_ids
msgid "Rules"
msgstr "Regole"

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:0
#, python-format
msgid "Scan a barcode to set the quantity"
msgstr "Scansiona un codice a barre per impostare la quantità"

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:0
#, python-format
msgid "Select"
msgstr "Seleziona"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:0
#, python-format
msgid "Set quantity"
msgstr "Imposta quantità"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Tables"
msgstr "Tabelle"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__pattern
msgid "The barcode matching pattern"
msgstr "Modello di corrispondenza del codice a barre"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__rule_ids
msgid "The list of barcode rules"
msgstr "Elenco regole del codice a barre"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__alias
msgid "The matched pattern will alias to this barcode"
msgstr "Il modello corrispondente sarà l'alias di questo codice a barre"

#. module: barcodes
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: a rule can only "
"contain one pair of braces."
msgstr ""
"È presente un errore di sintassi nel modello del codice a barre %(pattern)s:"
" una regola può contenere una sola coppia di parentesi graffe."

#. module: barcodes
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: braces can only "
"contain N's followed by D's."
msgstr ""
"È presente un errore di sintassi nel modello del codice a barre %(pattern)s:"
" le parentesi graffe possono contenere solo N seguite da D."

#. module: barcodes
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: empty braces."
msgstr ""
"È presente un errore di sintassi nel modello del codice a barre %(pattern)s:"
" le parentesi graffe sono vuote."

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__encoding
msgid ""
"This rule will apply only if the barcode is encoded with the specified "
"encoding"
msgstr ""
"Questa regola viene applicata solo se il codice rispetta la codifica "
"specificata"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__type
msgid "Type"
msgstr "Tipologia"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid ""
"UPC Codes can be converted to EAN by prefixing them with a zero. This "
"setting determines if a UPC/EAN barcode should be automatically converted in"
" one way or another when trying to match a rule with the other encoding."
msgstr ""
"I codici UPC possono essere convertiti a EAN anteponendo uno zero. Questa "
"impostazione determina se un codice a barre UPC/EAN debba essere convertito "
"automaticamente in uno o nell'altro modo quando si cerca di abbinare una "
"regola con l'altra codifica."

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__upca
msgid "UPC-A"
msgstr "UPC-A"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__upc2ean
msgid "UPC-A to EAN-13"
msgstr "Da UPC-A a EAN-13"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid "UPC/EAN Conversion"
msgstr "Conversione UPC/EAN"

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:0
#, python-format
msgid "Undefined barcode command"
msgstr "Comando codice a barre non definito"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__product
msgid "Unit Product"
msgstr "Prodotto unitario"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__sequence
msgid ""
"Used to order rules such that rules with a smaller sequence match first"
msgstr ""
"Utilizzato per ordinare le regole, come confrontare quelle con sequenza più "
"bassa per prime"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Valore dell'ultimo codice a barre scansionato."
