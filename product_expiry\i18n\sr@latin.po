# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_expiry
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
msgid ""
"<span class=\"label label-danger\" attrs=\"{'invisible': "
"[('product_expiry_alert', '=', False)]}\">Expiration Alert</span>"
msgstr ""

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.report_delivery_document_inherit_product_expiry
msgid "<span class=\"pull-right\">Expiry Date</span>"
msgstr ""

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "<span>days</span>"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot_alert_date
msgid "Alert Date"
msgstr "Datum alarma"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot_use_date
msgid "Best before Date"
msgstr "Najbolje pre Datuma"

#. module: product_expiry
#: model:product.product,name:product_expiry.product_product_pain
#: model:product.template,name:product_expiry.product_product_pain_product_template
msgid "Bread"
msgstr "Hleb"

#. module: product_expiry
#: model:product.product,name:product_expiry.product_product_lait
#: model:product.template,name:product_expiry.product_product_lait_product_template
msgid "Cow milk"
msgstr "Kravlje Mleko"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot_alert_date
msgid ""
"Date to determine the expired lots and serial numbers using the filter "
"\"Expiration Alerts\"."
msgstr ""

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "Dates"
msgstr "Datumi"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot_life_date
msgid "End of Life Date"
msgstr "Istek roka trajanja"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.search_product_lot_filter_inherit_product_expiry
msgid "Expiration alerts"
msgstr ""

#. module: product_expiry
#: model:product.product,name:product_expiry.product_product_jambon
#: model:product.template,name:product_expiry.product_product_jambon_product_template
msgid "French cheese Camembert"
msgstr ""

#. module: product_expiry
#: model:product.product,name:product_expiry.product_product_from
#: model:product.template,name:product_expiry.product_product_from_product_template
msgid "Ham"
msgstr "Šunka"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_production_lot
msgid "Lot/Serial"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product_alert_time
#: model:ir.model.fields,help:product_expiry.field_product_template_alert_time
msgid ""
"Number of days before an alert should be raised on the lot/serial number."
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product_life_time
#: model:ir.model.fields,help:product_expiry.field_product_template_life_time
msgid ""
"Number of days before the goods may become dangerous and must not be "
"consumed. It will be computed on the lot/serial number."
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product_removal_time
#: model:ir.model.fields,help:product_expiry.field_product_template_removal_time
msgid ""
"Number of days before the goods should be removed from the stock. It will be"
" computed on the lot/serial number."
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product_use_time
#: model:ir.model.fields,help:product_expiry.field_product_template_use_time
msgid ""
"Number of days before the goods starts deteriorating, without being "
"dangerous yet. It will be computed on the lot/serial number."
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product_alert_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template_alert_time
msgid "Product Alert Time"
msgstr "Vreme Upozorenja Proizvoda"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot_product_expiry_alert
msgid "Product Expiry Alert"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product_life_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template_life_time
msgid "Product Life Time"
msgstr "Rok TRajanja Proizvoda"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product_removal_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template_removal_time
msgid "Product Removal Time"
msgstr "Vreme Uklanjanja Proizvoda"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_template
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product_use_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template_use_time
msgid "Product Use Time"
msgstr "Upotrebno Vreme Proizvoda"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_quant
msgid "Quants"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot_removal_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant_removal_date
msgid "Removal Date"
msgstr "Datum uklanjanja"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot_product_expiry_alert
msgid "The Alert Date has been reached."
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot_life_date
msgid ""
"This is the date on which the goods with this Serial Number may become "
"dangerous and must not be consumed."
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot_removal_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant_removal_date
msgid ""
"This is the date on which the goods with this Serial Number should be "
"removed from the stock."
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot_use_date
msgid ""
"This is the date on which the goods with this Serial Number start "
"deteriorating, without being dangerous yet."
msgstr ""
