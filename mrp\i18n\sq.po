# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * mrp
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-16 08:08+0000\n"
"PO-Revision-Date: 2017-11-16 08:08+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Albanian (https://www.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:43
#, python-format
msgid " Manufacture"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product_bom_count
#: model:ir.model.fields,field_description:mrp.field_product_template_bom_count
msgid "# Bill of Material"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product_used_in_bom_count
msgid "# BoM Where Used"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workorder_done_count
msgid "# Done Work Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product_mo_count
#: model:ir.model.fields,field_description:mrp.field_product_template_mo_count
msgid "# Manufacturing Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_workorder_ready_count
msgid "# Read Work Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workorder_count
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_workorder_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_workorder_count
msgid "# Work Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_template_used_in_bom_count
msgid "# of BoM Where is Used"
msgstr ""

#. module: mrp
#: model:product.product,description:mrp.product_product_computer_desk_leg
#: model:product.template,description:mrp.product_product_computer_desk_leg_product_template
msgid "18″ x 2½″ Square Leg"
msgstr ""

#. module: mrp
#: model:product.product,description:mrp.product_product_laptop_charger
#: model:product.template,description:mrp.product_product_laptop_charger_product_template
msgid "19v,5.5 x 2.5 mm Pin Type Adapter (Power Cord Included)"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:293
#, python-format
msgid ""
"<p class=\"oe_view_nocontent_create\">\n"
"                        Click to upload files to your product.\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"<span class=\"label label-danger\" attrs=\"{'invisible': "
"['|',('availability', 'in', ('assigned', 'none')), ('state', 'not in', "
"('confirmed','progress'))]}\">Raw materials not available!</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Lost</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">OEE</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Performance</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "<span class=\"o_stat_text\">Scraps</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_form_view
msgid "<span class=\"o_stat_text\">Time<br/> Analysis</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Work Center Load</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Work Orders</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Actions</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>New</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>PLAN ORDERS</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Reporting</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>View</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>WORK ORDERS</span>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid ""
"<strong attrs=\"{'invisible': [('date_planned_finished', '=', False)]}\" class=\"mr8\">to</strong>\n"
"                                    <strong class=\"oe_edit_only mr8\" attrs=\"{'invisible': [('date_planned_finished', '!=', False)]}\">to</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "<strong class=\"mr8\">to</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Effectiveness Category: </strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Is a Blocking Reason? </strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Name</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>No. Of Minutes</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Printing date:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Product:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Product</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Products to Consume</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Reason: </strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Scheduled Date:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source Document:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Work Orders</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>WorkCenter</strong>"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:472
#, python-format
msgid "A Manufacturing Order is already done or cancelled!"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action_planning
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action_waiting
msgid ""
"A manufacturing order, based on a bill of materials, will\n"
"                consume raw materials and produce finished products."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_access_token
msgid "Access Token"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_active
#: model:ir.model.fields,field_description:mrp.field_mrp_document_active
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_active
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_active
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_search
msgid "Active"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_active_move_line_ids
msgid "Active Move Line"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Add a description..."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Add by-products to bills of materials. This can be used to get several "
"finished products as well. Without this option you only do: A + B = C. With "
"the option: A + B = C + D."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "All"
msgstr ""

#. module: mrp
#: selection:mrp.bom,ready_to_produce:0
msgid "All components available"
msgstr ""

#. module: mrp
#: sql_constraint:mrp.bom.line:0
msgid ""
"All product quantities must be greater or equal to 0.\n"
"Lines with 0 quantities can be used as optional lines. \n"
"You should install the mrp_byproduct module if you want to manage extra products on BoMs !"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_post_visible
msgid "Allowed to Post Inventory"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_unreserve_visible
msgid "Allowed to Unreserve Inventory"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Approve"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Archived"
msgstr ""

#. module: mrp
#: model:product.product,name:mrp.product_assembly
#: model:product.template,name:mrp.product_assembly_product_template
msgid "Assembly Service Cost"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_name
msgid "Attachment Name"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_local_url
#: model:ir.model.fields,field_description:mrp.field_mrp_document_website_url
msgid "Attachment URL"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:285
#, python-format
msgid "Attachments"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_tree
msgid "Author"
msgstr ""

#. module: mrp
#: selection:mrp.workcenter.productivity.loss,loss_type:0
msgid "Availability"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Availability Losses"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
#: selection:mrp.production,availability:0
msgid "Available"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_product_product_produce_delay
#: model:ir.model.fields,help:mrp.field_product_template_produce_delay
msgid ""
"Average delay in days to produce this product. In the case of multi-level "
"BOM, the manufacturing lead times of the components will be added."
msgstr ""

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_bom_price
msgid "BOM Cost"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_structure_report
msgid "BOM Name"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_attribute_value_ids
msgid "BOM Product Variants needed form apply this line."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_structure_report
msgid "BOM Ref"
msgstr ""

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_bom_structure
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_structure_report
msgid "BOM Structure"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_child_line_ids
msgid "BOM lines of the referred bom"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_time_mode_batch
msgid "Based on"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid ""
"Based on a Bill of Material, a Manufacturing Order will consume raw materials\n"
"                and produce finished products."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
msgid "Bill Of Material"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom
#: model:ir.model.fields,field_description:mrp.field_mrp_message_bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Bill of Material"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
msgid "Bill of Material line"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_open_bom
#: model:ir.actions.act_window,name:mrp.template_open_bom
#: model:ir.model.fields,field_description:mrp.field_product_product_bom_ids
#: model:ir.model.fields,field_description:mrp.field_product_template_bom_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Bill of Materials"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_bom_id
msgid ""
"Bill of Materials allow you to define the list of required raw materials to "
"make a finished product."
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_bom_form_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom_form_action
msgid "Bills of Materials"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Bills of Materials allow you to define the list of required raw\n"
"                materials used to make a finished product; through a manufacturing\n"
"                order or a pack of products."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter_wo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block Workcenter"
msgstr ""

#. module: mrp
#: selection:mrp.workcenter,working_state:0
msgid "Blocked"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_blocked_time
msgid "Blocked Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_blocked_time
msgid "Blocked hour(s) over the last month"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_template_search_view_procurment
msgid "BoM Components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_bom_line_id
msgid "BoM Line"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_bom_line_ids
msgid "BoM Lines"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_type
msgid "BoM Type"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
msgid "BoM details"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:67
#, python-format
msgid "BoM line product %s should not be same as BoM product."
msgstr ""

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_bolt
#: model:product.template,name:mrp.product_product_computer_desk_bolt_product_template
msgid "Bolt"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings_module_mrp_byproduct
msgid "By-Products"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:36
#, python-format
msgid "Can't find any generic Manufacture route."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_produce_wizard
msgid "Cancel"
msgstr "Anullo"

#. module: mrp
#: selection:mrp.production,state:0 selection:mrp.workorder,state:0
msgid "Cancelled"
msgstr "E Anulluar"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:316
#, python-format
msgid "Cannot delete a manufacturing order not in cancel state"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_capacity
msgid "Capacity"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Change Product Qty"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_change_production_qty
msgid "Change Quantity To Produce"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_change_production_qty
msgid "Change Quantity of Products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_check_to_done
msgid "Check Produced Qty"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Check availability"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_checksum
msgid "Checksum/SHA1"
msgstr ""

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:9
#, python-format
msgid "Clear"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid "Click here"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "Click here to create a Manufacturing Order."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid "Click here to create a Unbuild Order."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid "Click here to create a new Routing."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid "Click here to create a new Work Center"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_message_action_main
msgid "Click here to create a new Work Order Message."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
msgid "Click to add a work center."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid "Click to create a bill of material."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action_planning
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action_waiting
msgid "Click to start a new manufacturing order."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
msgid "Click to start a new work order."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_code
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view_kanban
msgid "Code"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_color
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_color
msgid "Color"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_res_company
msgid "Companies"
msgstr "Kompanitë"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_document_company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_company_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Company"
msgstr "Kompani"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_component_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Components"
msgstr ""

#. module: mrp
#: selection:mrp.routing.workcenter,time_mode:0
msgid "Compute based on real time"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_configuration
msgid "Configuration"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: selection:mrp.production,state:0
msgid "Confirmed"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "Consumed"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_consume_line_ids
msgid "Consumed Disassembly Lines"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_unbuild_id
msgid "Consumed Disassembly Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_consumed_less_than_planned
msgid "Consumed Less Than Planned"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Consumed Materials"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_operation_id
msgid "Consumed in Operation"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Continue Production"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost_report
msgid "Cost Structure"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Create Workorders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_created_production_id
msgid "Created Production Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_message_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_create_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild_create_uid
msgid "Created by"
msgstr "Krijuar nga"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_message_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_create_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild_create_date
msgid "Created on"
msgstr "Krijuar me"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Current Production"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Current Qty"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_qty_producing
msgid "Currently Produced Quantity"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_dashboard
msgid "Dashboard"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_db_datas
msgid "Database Data"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Date"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_date_planned_finished
msgid "Deadline End"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_date_planned_start
msgid "Deadline Start"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Default Duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings_use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
msgid "Default Unit of Measure"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_description
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_note
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_description
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description"
msgstr "Përshkrimi"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_note
msgid "Description of the Work Center."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description of the work center..."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_location_dest_id
msgid "Destination Location"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_unbuild_id
msgid "Disassembly Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_form_embedded_bom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_form_embedded_product
msgid "Discard"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_document_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_message_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_display_name
#: model:ir.model.fields,field_description:mrp.field_report_mrp_mrp_bom_cost_report_display_name
#: model:ir.model.fields,field_description:mrp.field_report_mrp_mrp_bom_structure_report_display_name
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild_display_name
msgid "Display Name"
msgstr "Emri i paraqitur"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_qty_done
#: model:ir.model.fields,field_description:mrp.field_stock_move_is_done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: selection:mrp.production,state:0 selection:mrp.unbuild,state:0
msgid "Done"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_line_done_wo
msgid "Done for Work Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: selection:mrp.unbuild,state:0
msgid "Draft"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_time_cycle
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_duration
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Duration"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Duration (minutes)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_time_mode
msgid "Duration Computation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_duration_percent
msgid "Duration Deviation (%)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_duration_unit
msgid "Duration Per Unit"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Effective Date"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_date_finished
msgid "Effective End Date"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_date_start
msgid "Effective Start Date"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_loss_type
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type
msgid "Effectiveness Category"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Efficiency"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_date_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_date_end
msgid "End Date"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_duration_expected
msgid "Expected Duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder_duration_expected
msgid "Expected duration (in minutes)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_datas
msgid "File Content"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_datas_fname
msgid "File Name"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_file_size
msgid "File Size"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Filters"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Finish Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
#: selection:mrp.workorder,state:0
msgid "Finished"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_line_lot_produced_id
msgid "Finished Lot"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_finished_lots_exist
msgid "Finished Lots Exist"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_finished_move_line_ids
#: model_terms:ir.ui.view,arch_db:mrp.view_finisehd_move_line
msgid "Finished Product"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_move_finished_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Finished Products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_location_dest_id
msgid "Finished Products Location"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Fully Productive"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Future Activitie"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Future Activities"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "General Information"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document_priority
msgid "Gives the sequence order when displaying a list of MRP documents."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_sequence
msgid "Gives the sequence order when displaying a list of bills of material."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter_sequence
msgid ""
"Gives the sequence order when displaying a list of routing Work Centers."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_sequence
msgid "Gives the sequence order when displaying a list of work centers."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_sequence
msgid "Gives the sequence order when displaying."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
msgid "Group By"
msgstr "Grupo Nga"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Group By..."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Group by..."
msgstr ""

#. module: mrp
#: model:product.product,name:mrp.product_product_19
#: model:product.template,name:mrp.product_product_19_product_template
msgid "HDD on Demand"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_has_attachments
msgid "Has Attachments"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_is_produced
msgid "Has Been Produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_has_moves
msgid "Has Moves"
msgstr ""

#. module: mrp
#: selection:mrp.document,priority:0
msgid "High"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_id
#: model:ir.model.fields,field_description:mrp.field_mrp_document_id
#: model:ir.model.fields,field_description:mrp.field_mrp_message_id
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_id
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_id
#: model:ir.model.fields,field_description:mrp.field_report_mrp_mrp_bom_cost_report_id
#: model:ir.model.fields,field_description:mrp.field_report_mrp_mrp_bom_structure_report_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild_id
msgid "ID"
msgstr "ID"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_product_id
msgid ""
"If a product variant is defined the BOM is available only for this product."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_propagate
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_active
msgid ""
"If the active field is set to False, it will allow you to hide the bills of "
"material without removing it."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_active
msgid ""
"If the active field is set to False, it will allow you to hide the routing "
"without removing it."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"If the manufacturing order is unlocked you can add to the initial demand."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"If the product is a finished product: When processing a sales\n"
"                                    order for this product, the delivery order will contain the raw\n"
"                                    materials, instead of the finished product."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"If the product is a semi-finished product: When processing a\n"
"                                    manufacturing order that contains that product as component,\n"
"                                    the raw materials of that product will be added to the\n"
"                                    manufacturing order of the final product."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
#: selection:mrp.production,state:0 selection:mrp.workcenter,working_state:0
#: selection:mrp.workorder,state:0
msgid "In Progress"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_index_content
msgid "Indexed Content"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move_line_lot_produced_qty
msgid "Informative, not used in matching"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:187
#, python-format
msgid "Insufficient Quantity"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_form_embedded_bom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_form_embedded_product
msgid "Inventory Message"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_moves
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Inventory Moves"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder_move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_is_locked
msgid "Is Locked"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_manual
msgid "Is a Blocking Reason"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "Is a Blocking Reason?"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_public
msgid "Is public document"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_is_user_working
msgid "Is the Current User Working"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:162
#, python-format
msgid "It has been unblocked already. "
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_location_id
msgid ""
"Keep empty if you produce at the location where you find the raw "
"materials.Set a location if you produce at a fixed location. This can be a "
"partner location if you subcontract the manufacturing operations."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter selection:mrp.bom,type:0
msgid "Kit"
msgstr ""

#. module: mrp
#: model:product.product,name:mrp.product_product_laptop_charger
#: model:product.template,name:mrp.product_product_laptop_charger_product_template
#: model:stock.inventory.line,product_name:mrp.stock_inventory_line_product_laptop_charger
msgid "Laptop Charger"
msgstr ""

#. module: mrp
#: model:stock.inventory.line,product_name:mrp.stock_inventory_product_27_lot0
#: model:stock.inventory.line,product_name:mrp.stock_inventory_product_27_lot1
msgid "Laptop Customized"
msgstr ""

#. module: mrp
#: model:product.product,name:mrp.product_product_laptop_keypad
#: model:product.template,name:mrp.product_product_laptop_keypad_product_template
#: model:stock.inventory.line,product_name:mrp.stock_inventory_line_product_laptop_keypad
msgid "Laptop Keypad"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_document___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_message___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_routing___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder___last_update
#: model:ir.model.fields,field_description:mrp.field_report_mrp_mrp_bom_cost_report___last_update
#: model:ir.model.fields,field_description:mrp.field_report_mrp_mrp_bom_structure_report___last_update
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild___last_update
msgid "Last Modified on"
msgstr "Modifikimi i fundit në"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_message_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_write_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild_write_uid
msgid "Last Updated by"
msgstr "Modifikuar per here te fundit nga"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_message_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_write_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild_write_date
msgid "Last Updated on"
msgstr "Modifikuar per here te fundit me"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
msgid "Late"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Activities"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:432
#, python-format
msgid ""
"Lines need to be deleted, but can not as you still have some quantities to "
"consume in them. "
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_location_id
msgid "Location"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_location_src_id
msgid "Location where the system will look for components."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_location_dest_id
msgid "Location where the system will stock the finished products."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lock"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_id_8239
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Loss Reason"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_lot_id
msgid "Lot"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_production_lot
msgid "Lot/Serial"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_final_lot_id
msgid "Lot/Serial Number"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_finisehd_move_line
msgid "Lot/Serial number"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_active_move_line_ids
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_lots
msgid "Lots"
msgstr ""

#. module: mrp
#: selection:mrp.document,priority:0
msgid "Low"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings_module_mrp_maintenance
msgid "Maintenance"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_routings
msgid "Manage Work Order Operations"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_manager
msgid "Manager"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_time_cycle_manual
msgid "Manual Duration"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:34
#: model:stock.location.route,name:mrp.route_warehouse0_manufacture
#, python-format
msgid "Manufacture"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_manufacture_pull_id
msgid "Manufacture Rule"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_manufacture_to_resupply
msgid "Manufacture in this Warehouse"
msgstr ""

#. module: mrp
#: selection:mrp.bom,type:0
msgid "Manufacture this product"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:64
#: model:ir.ui.menu,name:mrp.menu_mrp_root
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model:stock.picking.type,name:mrp.picking_type_manufacturing
#, python-format
msgid "Manufacturing"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product_produce_delay
#: model:ir.model.fields,field_description:mrp.field_product_template_produce_delay
#: model:ir.model.fields,field_description:mrp.field_res_company_manufacturing_lead
#: model:ir.model.fields,field_description:mrp.field_res_config_settings_manufacturing_lead
msgid "Manufacturing Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_manu_type_id
msgid "Manufacturing Operation Type"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_production_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap_production_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Manufacturing Order"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production_workcenter
#: model:ir.actions.act_window,name:mrp.action_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_production_action
#: model:ir.actions.act_window,name:mrp.mrp_production_action_picking_deshboard
#: model:ir.actions.act_window,name:mrp.mrp_production_action_planning
#: model:ir.actions.act_window,name:mrp.mrp_production_report
#: model:ir.ui.menu,name:mrp.menu_mrp_production_action
#: model:ir.ui.menu,name:mrp.menu_mrp_production_report
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_production_calendar
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_production_pivot
msgid "Manufacturing Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid ""
"Manufacturing Orders can be generated automatically based on customer\n"
"                requirements or reordering rules."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are currently in production."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are in confirmed state."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_ready_to_produce
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manufacturing Readiness"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Manufacturing Reference"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed\n"
"                of workers and/or machines, they are used for costing, scheduling, capacity planning, etc.\n"
"                The Work Centers are defined on the Routing's operations."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.\n"
"                The Work Centers are defined on the Routing's operations."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action_planning
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action_waiting
msgid ""
"Manufacturing orders are usually proposed automatically based\n"
"                on customer requirements or automated rules like the minimum\n"
"                stock rule."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Mark as Done"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_bom
msgid "Master Data"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings_module_mrp_mps
msgid "Master Production Schedule"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_availability
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_production_availability
msgid "Materials Availability"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_message_message
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_tree
msgid "Message"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_mimetype
msgid "Mime Type"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Miscellaneous"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_move_id
msgid "Move"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_line_done_move
msgid "Move Done"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Move forward deadline start dates by"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_move_raw_ids
msgid "Moves"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_move_line_ids
msgid "Moves to Track"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Mrp Workcenter"
msgstr ""

#. module: mrp
#: model:ir.actions.server,name:mrp.production_order_server_action
msgid "Mrp: Plan Production Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "My Activities"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_search
msgid "My Messages"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_message_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_name
msgid "Name"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:46
#: code:addons/mrp/models/mrp_production.py:302
#: code:addons/mrp/models/mrp_production.py:306
#: code:addons/mrp/models/mrp_routing.py:18
#: code:addons/mrp/models/mrp_routing.py:34
#: code:addons/mrp/models/mrp_routing.py:35
#: code:addons/mrp/models/mrp_unbuild.py:21
#: code:addons/mrp/models/mrp_unbuild.py:81
#: code:addons/mrp/models/mrp_unbuild.py:82
#, python-format
msgid "New"
msgstr ""

#. module: mrp
#: model:product.product,description:mrp.product_product_laptop_keypad
#: model:product.template,description:mrp.product_product_laptop_keypad_product_template
msgid "New Original Laptop Keyboard Part No 9GT99"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_batch
msgid "Next Operation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_next_work_order_id
msgid "Next Work Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "No Routing"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_delta_report
msgid ""
"No data to display. You will get here statistics about the\n"
"              work orders duration related to this routing."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_loss_action
msgid "No productivity loss defined."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_blocked
msgid "No productivity loss for this equipment."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid ""
"No workorder currently in progress. Click to mark work center as blocked."
msgstr ""

#. module: mrp
#: selection:mrp.production,availability:0
msgid "None"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: selection:mrp.document,priority:0 selection:mrp.production,priority:0
#: selection:mrp.workcenter,working_state:0
msgid "Normal"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_scrap_workorder_id
msgid "Not to restrict or prefer quants, but informative."
msgstr ""

#. module: mrp
#: selection:mrp.production,priority:0
msgid "Not urgent"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_form_view
msgid "Notes"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type_count_mo_late
msgid "Number of Manufacturing Orders Late"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type_count_mo_waiting
msgid "Number of Manufacturing Orders Waiting"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type_count_mo_todo
msgid "Number of Manufacturing Orders to Process"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity
#: model:ir.model.fields,help:mrp.field_mrp_workorder_capacity
msgid "Number of pieces that can be produced in parallel."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "OEE"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_oee_target
msgid "OEE Target"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_oee_target
msgid "OEE Target in percentage"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Odoo uses these BoMs to automatically propose manufacturing\n"
"                orders according to procurement needs."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_oee
msgid "Oee"
msgstr ""

#. module: mrp
#: model:product.product,description:mrp.product_product_19
#: model:product.template,description:mrp.product_product_19_product_template
msgid "On demand hard-disk having capacity based on requirement."
msgstr ""

#. module: mrp
#: selection:mrp.routing.workcenter,batch:0
msgid "Once a minimum number of products is processed"
msgstr ""

#. module: mrp
#: selection:mrp.routing.workcenter,batch:0
msgid "Once all products are processed"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_operation_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Operation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_operation_id
msgid "Operation To Consume"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_picking_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_picking_type_id
msgid "Operation Type"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_operation_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_manufacturing
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_calendar
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Operations"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_order_finished_lot_ids
msgid "Order Finished Lot"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_order_ids
msgid "Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_qty_production
msgid "Original Production Quantity"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_oee
#: model:ir.ui.menu,name:mrp.menu_mrp_workcenter_productivity_report
msgid "Overall Equipment Effectiveness"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_oee
msgid "Overall Equipment Effectiveness, based on the last month"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_oee
msgid "Overall Equipment Effectiveness: no working or blocked time."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move_line
msgid "Packing Operation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_bom_id
msgid "Parent BoM"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_routing_id
msgid "Parent Routing"
msgstr ""

#. module: mrp
#: selection:mrp.production,availability:0
msgid "Partially Available"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Pause"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
#: selection:mrp.workorder,state:0
msgid "Pending"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_performance
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: selection:mrp.workcenter.productivity.loss,loss_type:0
msgid "Performance"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Performance Losses"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_performance
msgid "Performance over the last month"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Plan Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Plan manufacturing or purchase orders based on forecasts"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: selection:mrp.production,state:0
msgid "Planned"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Planned Date"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.mrp_planning_menu_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Planning"
msgstr ""

#. module: mrp
#: code:addons/mrp/wizard/mrp_product_produce.py:147
#, python-format
msgid "Please enter a lot or serial number for %s !"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:288
#, python-format
msgid ""
"Please set the quantity you are currently producing. It should be different "
"from zero."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Post Inventory"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_priority
#: model:ir.model.fields,field_description:mrp.field_mrp_production_priority
msgid "Priority"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Process operations at specific work centers based on the routing\n"
"                                        and carry out quality checks."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_produce_line_ids
msgid "Processed Disassembly Lines"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_procurement_group_id
msgid "Procurement Group"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_rule
msgid "Procurement Rule"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_product_produce
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_produce_wizard
msgid "Produce"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Produce residual products (A + B -&gt; C + D)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "Produced"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_product_product
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_message_product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Product"
msgstr "Produkti"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings_module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_unbuild_moves
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Product Moves"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_product_produce_id
msgid "Product Produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_product_qty
msgid "Product Quantity"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_product_template
#: model:ir.model.fields,field_description:mrp.field_mrp_message_product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_tmpl_id
msgid "Product Template"
msgstr "Shëmbull i Produktit"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_uom_id
msgid "Product Unit of Measure"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_product_id
msgid "Product Variant"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_produce_line_ids
msgid "Product to Track"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_message_kanban
msgid "Product:"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_production_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
msgid "Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_production_date
msgid "Production Date"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_document
msgid "Production Document"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_production_location_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_location_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_search_view
msgid "Production Location"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_message
msgid "Production Message"
msgstr ""

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_production_order
#: model:ir.model.fields,field_description:mrp.field_stock_move_line_production_id
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Production Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Production Order # :"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_production_id
msgid "Production Order for finished products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_raw_material_production_id
msgid "Production Order for raw materials"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Production Workcenter"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
msgid "Production started late"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_production_gantt
msgid "Productions"
msgstr ""

#. module: mrp
#: selection:mrp.workcenter.productivity.loss,loss_type:0
msgid "Productive"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productive_time
msgid "Productive Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_productive_time
msgid "Productive hour(s) over the last month"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_loss_action
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_blocked
#: model:ir.ui.menu,name:mrp.menu_mrp_workcenter_productivity_loss
msgid "Productivity Losses"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_template_action
#: model:ir.ui.menu,name:mrp.menu_mrp_product_form
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_propagate
msgid "Propagate cancel and split"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_location_path
msgid "Pushed Flow"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings_module_quality_mrp
#: selection:mrp.workcenter.productivity.loss,loss_type:0
msgid "Quality"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Quality Losses"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_qty_produced
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost_report
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_structure_report
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_finisehd_move_line
msgid "Quantity"
msgstr "Sasia"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_line_lot_produced_qty
msgid "Quantity Finished Product"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_qty_produced
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Quantity Produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_qty_remaining
msgid "Quantity To Be Produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_qty
msgid "Quantity To Produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_batch_size
msgid "Quantity to Process"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Raw Material"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_move_raw_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost_report
msgid "Raw Materials"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_location_src_id
msgid "Raw Materials Location"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
#: selection:mrp.workorder,state:0
msgid "Ready"
msgstr ""

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp.js:147
#, python-format
msgid "Ready to produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_duration
msgid "Real Duration"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_name
msgid "Reason"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_product_produce
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_produce_wizard
msgid "Record Production"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_product_produce_line
msgid "Record Production Line"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:159
#, python-format
msgid ""
"Recursion error!  A product with a Bill of Material should not have itself "
"in its BoM or child BoMs!"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_code
#: model:ir.model.fields,field_description:mrp.field_mrp_production_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_code
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_name
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost_report
msgid "Reference"
msgstr ""

#. module: mrp
#: sql_constraint:mrp.production:0
msgid "Reference must be unique per Company!"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_origin
msgid ""
"Reference of the document that generated this production order request."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_ir_attachment_id
msgid "Related attachment"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings_module_mrp_repair
msgid "Repair"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Repair products and invoice the repair orders"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_reporting
msgid "Reporting"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_serial
msgid "Requires Serial"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_lots
msgid "Reserved"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_res_field
msgid "Resource Field"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_res_id
msgid "Resource ID"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_view_resource_calendar_leaves_search_mrp
msgid "Resource Leaves"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_res_model
msgid "Resource Model"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_res_name
msgid "Resource Name"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_pm_resources_config
msgid "Resources"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_user_id
msgid "Responsible"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_routing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_routing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_message_routing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_routing_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Routing"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_routing_line_ids
msgid "Routing Lines"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_name
msgid "Routing Name"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
msgid "Routing Work Centers"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_routing_action
#: model:ir.model,name:mrp.model_mrp_routing
#: model:ir.ui.menu,name:mrp.menu_mrp_routing_action
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
msgid "Routings"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid ""
"Routings define the successive operations that need to be\n"
"                done to realize a Manufacturing Order. Each operation from\n"
"                a Routing is done at a specific Work Center and has a specific duration."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_form_embedded_bom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_form_embedded_product
msgid "Save"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Schedule manufacturing orders earlier to avoid delays"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Schedule the maintenance of your equipment"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_date_planned_finished
msgid "Scheduled Date Finished"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_date_planned_start
msgid "Scheduled Date Start"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
msgid "Scheduled Date by Month"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
msgid "Scheduled Month"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:590
#: code:addons/mrp/models/mrp_workorder.py:481
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_scrap_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
#, python-format
msgid "Scrap"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_scrap_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_scrap_count
msgid "Scrap Move"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_scrap
msgid "Scrap Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_scrap_ids
msgid "Scraps"
msgstr ""

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_screw
#: model:product.template,name:mrp.product_product_computer_desk_screw_product_template
msgid "Screw"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_search
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Search"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Search Bill Of Material"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
msgid "Search Bill Of Material Components"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Search Production"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
msgid "Search Work Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Search for mrp workcenter"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Security Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_res_company_manufacturing_lead
#: model:ir.model.fields,help:mrp.field_res_config_settings_manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr ""

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:7
#: code:addons/mrp/static/src/xml/mrp.xml:8
#, python-format
msgid "Select"
msgstr ""

#. module: mrp
#: model:product.product,name:mrp.product_product_build_kit
#: model:product.template,name:mrp.product_product_build_kit_product_template
msgid "Self Build Kit"
msgstr ""

#. module: mrp
#: model:product.product,description:mrp.product_product_build_kit
#: model:product.template,description:mrp.product_product_build_kit_product_template
msgid "Self Build kit."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_sequence
msgid "Sequence"
msgstr "Sekuencë"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter_batch
msgid ""
"Set 'no' to schedule the next work order after the previous one. Set 'yes' "
"to produce after the quantity set in 'Quantity To Process' has been "
"produced."
msgstr ""

#. module: mrp
#: selection:mrp.routing.workcenter,time_mode:0
msgid "Set duration manually"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_configuration
#: model:ir.ui.menu,name:mrp.menu_mrp_config
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Settings"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:89
#, python-format
msgid "Should have a lot for the finished product"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_show_final_lots
msgid "Show Final Lots"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: mrp
#: model:product.product,description:mrp.product_product_computer_desk_head
#: model:product.template,description:mrp.product_product_computer_desk_head_product_template
msgid "Solid wood is a durable natural material."
msgstr ""

#. module: mrp
#: model:product.product,description:mrp.product_product_computer_desk
#: model:product.template,description:mrp.product_product_computer_desk_product_template
msgid "Solid wood table."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_origin
msgid "Source"
msgstr ""

#. module: mrp
#: model:product.product,description:mrp.product_product_computer_desk_screw
#: model:product.template,description:mrp.product_product_computer_desk_screw_product_template
msgid "Stainless steel screw"
msgstr ""

#. module: mrp
#: model:product.product,description:mrp.product_product_computer_desk_bolt
#: model:product.template,description:mrp.product_product_computer_desk_bolt_product_template
msgid "Stainless steel screw full (dia - 5mm, Length - 10mm)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_date_start
msgid "Start Date"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Start Working"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_production_state
msgid "State"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_working_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_working_state
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
msgid "Status"
msgstr "Statusi"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_move_dest_ids
msgid "Stock Movements of Produced Goods"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_store_fname
msgid "Stored Filename"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_child_bom_id
msgid "Sub BoM"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "TPM Big Loss"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss
msgid "TPM Big Losses"
msgstr ""

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk
#: model:product.template,name:mrp.product_product_computer_desk_product_template
msgid "Table"
msgstr ""

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_head
#: model:product.template,name:mrp.product_product_computer_desk_head_product_template
msgid "Table Head"
msgstr ""

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_leg
#: model:product.template,name:mrp.product_product_computer_desk_leg_product_template
msgid "Table Leg"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move_is_done
#: model:ir.model.fields,help:mrp.field_stock_move_line_done_move
msgid "Technical Field to order moves"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_check_to_done
msgid "Technical Field to see if we can show 'Mark as Done' button"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move_line_done_wo
msgid ""
"Technical Field which is False when temporarily filled in in work order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder_is_user_working
msgid "Technical field indicating whether the current user is working. "
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_post_visible
msgid "Technical field to check when we can post"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_consumed_less_than_planned
msgid ""
"Technical field used to see if we have to display a warning or not when "
"confirming an order."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder_production_availability
msgid "Technical: used in views and domains only."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder_working_state
msgid "Technical: used in views only"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder_product_id
#: model:ir.model.fields,help:mrp.field_mrp_workorder_product_tracking
#: model:ir.model.fields,help:mrp.field_mrp_workorder_product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_workorder_production_state
msgid "Technical: used in views only."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_production_lot_use_next_on_work_order_id
msgid "Technical: used to figure out default serial number on work orders"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:76 code:addons/mrp/models/mrp_bom.py:254
#, python-format
msgid ""
"The Product Unit of Measure you chose has a different category than in the "
"product form."
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:156
#, python-format
msgid "The capacity must be strictly positive."
msgstr ""

#. module: mrp
#: selection:mrp.bom,ready_to_produce:0
msgid "The components of 1st operation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document_res_model
msgid "The database object this attachment will be attached to."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_routing_id
msgid ""
"The list of operations (list of work centers) to produce the finished "
"product. The routing is mainly used to compute work center costs during "
"operations and to plan future loads on work centers based on production "
"planning."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_routing_id
msgid ""
"The list of operations to produce the finished product. The routing is "
"mainly used to compute work center costs during operations and to plan "
"future loads on work centers based on production planning."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder_qty_produced
msgid "The number of products already handled by this work order"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking_type
msgid "The operation type determines the picking view"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_operation_id
msgid ""
"The operation where the components are consumed, or the finished products "
"created."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_routing_id
msgid ""
"The operations for producing this BoM.  When a routing is specified, the "
"production orders will  be executed through work orders, otherwise "
"everything is processed in the production order itself. "
msgstr ""

#. module: mrp
#: code:addons/mrp/wizard/mrp_product_produce.py:94
#, python-format
msgid "The production order for '%s' has no quantity specified"
msgstr ""

#. module: mrp
#: sql_constraint:mrp.production:0
msgid "The quantity to produce must be positive!"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document_res_id
msgid "The record id this is attached to."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter_routing_id
msgid ""
"The routing contains all the Work Centers used and for how long. This will "
"create work orders afterwardswhich alters the execution of the manufacturing"
" order. "
msgstr ""

#. module: mrp
#: code:addons/mrp/models/procurement.py:20
#, python-format
msgid ""
"There is no Bill of Material found for the product %s. Please define a Bill "
"of Material for this product."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "This Month"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "This Year"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_time_efficiency
msgid ""
"This field is used to calculate the the expected duration of a work order at"
" this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_production_location_id
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_time_ids
msgid "Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_time_efficiency
msgid "Time Efficiency"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_time_ids
msgid "Time Logs"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Time Tracking"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:402
#, python-format
msgid "Time Tracking: "
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_time_stop
msgid "Time after prod."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_time_start
msgid "Time before prod."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_time_stop
msgid "Time in minutes for the cleaning."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_time_start
msgid "Time in minutes for the setup."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter_time_cycle_manual
msgid ""
"Time in minutes. Is the time used in manual mode, or the first time supposed"
" in real time when there are not any work orders yet."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_qty_to_consume
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "To Consume"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Do"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "To Launch"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "To Produce"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
msgid ""
"To manufacture or assemble products, and use raw materials and\n"
"            finished products you must also handle manufacturing operations.\n"
"            Manufacturing operations are often called Work Orders. The various\n"
"            operations will have different impacts on the costs of\n"
"            manufacturing and planning depending on the available workload."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Today"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today Activities"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost_report
msgid "Total Cost"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost_report
msgid "Total Cost of Components"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_workorder_late_count
msgid "Total Late Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Qty"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_workorder_pending_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_workorder_progress_count
msgid "Total Running Orders"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_traceability
msgid "Traceability: Lots"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_product_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_has_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_product_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_tracking
#: model:ir.model.fields,field_description:mrp.field_stock_move_needs_lots
msgid "Tracking"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_type
msgid "Type"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Unblock"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild_unbuild_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild Order"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:77
#, python-format
msgid "Unbuild Order product quantity has to be strictly positive."
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_unbuild
#: model:ir.ui.menu,name:mrp.menu_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid ""
"Unbuild Orders are used to break down a product you manufactured or "
"purchased into its components, based on its Bill of Material."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost_report
msgid "Unit Cost"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_unit_factor
msgid "Unit Factor"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild_product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_product_uom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Unit of Measure"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_product_uom_id
msgid ""
"Unit of Measure (Unit of Measure) is the unit of measurement for the "
"inventory control"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unlock"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unreserve"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Update"
msgstr ""

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:7
#, python-format
msgid "Upload your file"
msgstr ""

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:6
#, python-format
msgid "Uploading..."
msgstr ""

#. module: mrp
#: selection:mrp.production,priority:0
msgid "Urgent"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document_url
msgid "Url"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_production_lot_use_next_on_work_order_id
msgid "Use Next On Work Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Use the Produce button or process the work orders to create some finished "
"products."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Used In"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_user_id
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model:res.groups,name:mrp.group_mrp_user
msgid "User"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Using a MPS report to schedule your reordering and manufacturing operations "
"is useful if you have long lead time and if you produce based on sales "
"forecasts."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_message_valid_until
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_tree
msgid "Validity Date"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_message_view_form_embedded_product
msgid "Variant"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_attribute_value_ids
msgid "Variants"
msgstr ""

#. module: mrp
#: selection:mrp.document,priority:0
msgid "Very High"
msgstr ""

#. module: mrp
#: selection:mrp.production,priority:0
msgid "Very Urgent"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: selection:mrp.production,availability:0
msgid "Waiting"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Waiting Availability"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_production_action_waiting
msgid "Waiting Availability MO"
msgstr ""

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp.js:147
#, python-format
msgid "Waiting Materials"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse
msgid "Warehouse"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:76 code:addons/mrp/models/mrp_bom.py:254
#, python-format
msgid "Warning"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_picking_type_id
msgid ""
"When a procurement has a ‘produce’ route with a operation type set, it will "
"try to create a Manufacturing Order for that product using a BoM of the same"
" operation type. That allows to define procurement rules which trigger "
"different manufacturing orders with different BoMs."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse_manufacture_to_resupply
msgid ""
"When products are manufactured, they can be manufactured in this warehouse."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp.field_mrp_message_workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
msgid "Work Center"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_workcenter_load
msgid "Work Center Load"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Work Center Loads"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Work Center Name"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_form_view
msgid "Work Center Operations"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
msgid "Work Center load"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_action
#: model:ir.ui.menu,name:mrp.menu_view_resource_search_mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Centers"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_kanban_action
msgid "Work Centers Dashboard"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
msgid ""
"Work Centers allow you to create and manage manufacturing\n"
"                units. They consist of workers and/or machines, which are\n"
"                considered as units for task assignation as well as capacity\n"
"                and planning forecast."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
msgid "Work Instruction"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_workorder_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_name
#: model:ir.model.fields,field_description:mrp.field_stock_move_line_workorder_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap_workorder_id
msgid "Work Order"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_message_action_main
#: model:ir.ui.menu,name:mrp.mrp_message_menu
msgid "Work Order Messages"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_message_action_main
msgid ""
"Work Order Messages are used to warn users about any change in the product,\n"
"                process, etc. These messages will appear on the involved Work Orders."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Work Order Operations allow you to create and manage the manufacturing "
"operations that should be followed within your work centers in order to "
"produce a product. They are attached to bills of materials that will define "
"the required raw materials."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_workorder_id
msgid "Work Order To Consume"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_routing_time
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production_specific
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_workcenter
#: model:ir.actions.act_window,name:mrp.action_work_orders
#: model:ir.actions.act_window,name:mrp.mrp_workorder_todo
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workorder_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_workorder_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_work_order_report
#: model:ir.ui.menu,name:mrp.menu_mrp_workorder_todo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_tree_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workcenter_form_view_filter
msgid "Work Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings_group_mrp_routings
msgid "Work Orders & Quality"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workorder_delta_report
msgid "Work Orders Deviation"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workorder_report
#: model:ir.actions.act_window,name:mrp.mrp_workorder_workcenter_report
msgid "Work Orders Performance"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production
msgid "Work Orders Planning"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
msgid ""
"Work Orders are operations to be processed at a Work Center to realize a\n"
"            Manufacturing Order. Work Orders are trigerred by Manufacturing Orders,\n"
"            they are based on the Routing defined on these ones"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid ""
"Work Orders are operations to be processed at a Work Center to realize a\n"
"            Manufacturing Order. Work Orders are trigerred by Manufacturing Orders,\n"
"            they are based on the Routing defined on these ones."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
msgid ""
"Work Orders are operations to be processed at a Work Center to realize a\n"
"           Manufacturing Order. Work Orders are trigerred by Manufacturing Orders,\n"
"           they are based on the Routing defined on these ones."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Work Sheet"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:567
#, python-format
msgid "Work order %s is still running"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Work orders in progress. Click to block work center."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Workcenter"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Workcenter Productivity"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Workcenter Productivity Loss"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Workcenter blocked, click to unblock."
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_view_resource_calendar_search_mrp
msgid "Working Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_production_messages
msgid "Workorder Message"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder_worksheet
msgid "Worksheet"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:521
#, python-format
msgid ""
"You can not cancel production order, a work order is still in progress."
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:240
#, python-format
msgid "You can not change the finished work order."
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:553
#, python-format
msgid "You can not consume without telling for which lot you consumed it"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:91
#, python-format
msgid ""
"You can not delete a Bill of Material with running manufacturing orders.\n"
"Please close or cancel it first."
msgstr ""

#. module: mrp
#: code:addons/mrp/models/stock_move.py:134
#, python-format
msgid ""
"You cannot cancel a manufacturing order if you have already consumed "
"material.             If you want to cancel this MO, please change the "
"consumed quantities to 0."
msgstr ""

#. module: mrp
#: code:addons/mrp/wizard/mrp_product_produce.py:204
#, python-format
msgid ""
"You cannot consume the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr ""

#. module: mrp
#: code:addons/mrp/wizard/change_production_qty.py:44
#, python-format
msgid "You have already processed %d. Please input a quantity higher than %d "
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"You have consumed less material than what was planned. Are you sure you want"
" to close this MO?"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:392
#, python-format
msgid ""
"You need to define at least one productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:388
#, python-format
msgid ""
"You need to define at least one productivity loss in the category "
"'Productivity'. Create one from the Manufacturing app, menu: Configuration /"
" Productivity Losses."
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:446
#, python-format
msgid ""
"You need to define at least one unactive productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""

#. module: mrp
#: code:addons/mrp/wizard/mrp_product_produce.py:125
#, python-format
msgid "You need to provide a lot for the finished product"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:314
#, python-format
msgid "You should provide a lot/serial number for a component"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:291
#, python-format
msgid "You should provide a lot/serial number for the final product"
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:95
#, python-format
msgid ""
"You should specify a manufacturing order in order to find the correct "
"tracked products."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "days"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "hour(s)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "last"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "minute(s)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workcenter_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "minutes"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_line
msgid "mrp.bom.line"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "quantity has been updated."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_mrp_bom_cost_report
msgid "report.mrp.mrp_bom_cost_report"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_mrp_bom_structure_report
msgid "report.mrp.mrp_bom_structure_report"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_scrap
msgid "stock.scrap"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_traceability_report
msgid "stock.traceability.report"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warn_insufficient_qty_unbuild
msgid "stock.warn.insufficient.qty.unbuild"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid "to create a new Work Center."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "work orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_worksheet
msgid "worksheet"
msgstr ""
