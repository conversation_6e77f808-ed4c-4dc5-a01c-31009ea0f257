# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * social_media
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: social_media
#: model:ir.model,name:social_media.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_facebook
msgid "Facebook Account"
msgstr "Facebook nalog"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_github
msgid "GitHub Account"
msgstr "GitHub nalog"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_googleplus
msgid "Google+ Account"
msgstr "Google+ nalog"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_instagram
msgid "Instagram Account"
msgstr ""

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_linkedin
msgid "LinkedIn Account"
msgstr "LinkedIn nalog"

#. module: social_media
#: model_terms:ir.ui.view,arch_db:social_media.view_company_form_inherit_social_media
msgid "Social Media"
msgstr "Socijalni Mediji"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_twitter
msgid "Twitter Account"
msgstr "Twitter nalog"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_youtube
msgid "Youtube Account"
msgstr "Youtube nalog"
