# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_alipay
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_acquirer__alipay_payment_method
msgid ""
"* Cross-border: For the overseas seller \n"
"* Express Checkout: For the Chinese Seller"
msgstr ""
"* عبر الحدود: للبائع الخارجي \n"
"* الدفع والخروج السريع: للبائع من الصين "

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_payment_method
msgid "Account"
msgstr "الحساب "

#. module: payment_alipay
#: model:account.payment.method,name:payment_alipay.payment_method_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_acquirer__provider__alipay
msgid "Alipay"
msgstr "Alipay"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_seller_email
msgid "Alipay Seller Email"
msgstr "البريد الإلكتروني لبائع Alipay "

#. module: payment_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_acquirer__alipay_payment_method__standard_checkout
msgid "Cross-border"
msgstr "عبر الحدود "

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment_transaction.py:0
#, python-format
msgid "Expected signature %(sc) but received %(sign)s."
msgstr "التوقيع المتوقع %(sc) ولكن تم استلام %(sign)s. "

#. module: payment_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_acquirer__alipay_payment_method__express_checkout
msgid "Express Checkout (only for Chinese merchants)"
msgstr "الدفع والخروج السريع (فقط للتجار الصينيين) "

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_md5_signature_key
msgid "MD5 Signature Key"
msgstr "مفتاح توقيع MD5 "

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__alipay_merchant_partner_id
msgid "Merchant Partner ID"
msgstr "معرف شريك التاجر "

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "لم يتم العثور على معاملة تطابق المرجع %s. "

#. module: payment_alipay
#: model:ir.model,name:payment_alipay.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "بوابة الدفع "

#. module: payment_alipay
#: model:ir.model,name:payment_alipay.model_account_payment_method
msgid "Payment Methods"
msgstr "طرق الدفع "

#. module: payment_alipay
#: model:ir.model,name:payment_alipay.model_payment_transaction
msgid "Payment Transaction"
msgstr "معاملة الدفع "

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_acquirer__provider
msgid "Provider"
msgstr "المزود"

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference %(r)s or txn_id %(t)s."
msgstr "تم استلام البيانات دون مرجع %(r)s أو txn_id %(t)s. "

#. module: payment_alipay
#: code:addons/payment_alipay/controllers/main.py:0
#, python-format
msgid ""
"Received notification data not acknowledged by Alipay:\n"
"%s"
msgstr ""
"بيانات الإشعارات المستلمة غير معترف بها من قِبَل Alipay: \n"
"%s"

#. module: payment_alipay
#: code:addons/payment_alipay/controllers/main.py:0
#, python-format
msgid ""
"Received notification data with unknown reference:\n"
"%s"
msgstr ""
"تم استلام بيانات الإشعارات مع مرجع مجهول: \n"
"%s "

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "مقدم خدمة الدفع لاستخدامه مع بوابة الدفع هذه "

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment_transaction.py:0
#, python-format
msgid "The amount does not match the total + fees."
msgstr "لا يطابق المبلغ الرسوم + الإجمالي. "

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment_transaction.py:0
#, python-format
msgid ""
"The currency returned by Alipay %(rc)s does not match the transaction "
"currency %(tc)s."
msgstr "لا تطابق العملة المرجعة بواسطة Alipay %(rc)s عملة المعاملة %(tc)s. "

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_acquirer__alipay_seller_email
msgid "The public Alipay partner email"
msgstr "عنوان البريد الإلكتروني لشريك Alipay العالم "

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_acquirer__alipay_merchant_partner_id
msgid "The public partner ID solely used to identify the account with Alipay"
msgstr "معرف الشريك العام الذي يُستخدم فقط لتعريف الحساب مع Alipay "

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment_transaction.py:0
#, python-format
msgid "The seller email does not match the configured Alipay account."
msgstr "لا يطابق عنوان البريد الإلكتروني للبائع حساب Alipay المهيأ. "

#. module: payment_alipay
#: code:addons/payment_alipay/models/payment_transaction.py:0
#, python-format
msgid "received invalid transaction status: %s"
msgstr "حالة المعاملة غير الصالحة المستلمة: %s "
