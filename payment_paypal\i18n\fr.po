# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_paypal
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-31 17:26+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"<br/><br/>\n"
"                Thanks,<br/>\n"
"                <b>The Odoo Team</b>"
msgstr ""
"<br/><br/>\n"
"                Merci,<br/>\n"
"                <b>L' équipe d'Odoo</b>"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_acquirer.py:0
#, python-format
msgid "Add your PayPal account to Odoo"
msgstr "Ajoutez votre compte Paypal à Odoo"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_email_account
msgid "Email"
msgstr "Courriel"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"Hello,\n"
"                <br/><br/>\n"
"                You have received a payment through PayPal.<br/>\n"
"                Kindly follow the instructions given by PayPal to create your account.<br/>\n"
"                Then, help us complete your Paypal credentials in Odoo.<br/><br/>"
msgstr ""
"Bonjour,\n"
"                <br/><br/>\n"
"                 Vous avez reçu un paiement via PayPal.<br/>\n"
"                 Veuillez suivre les instructions données par PayPal pour créer votre compte.<br/>\n"
"                 Ensuite, aidez-nous à compléter vos identifiants Paypal dans Odoo.<br/><br/>"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_acquirer_form
msgid "How to configure your paypal account?"
msgstr "Comment configurer votre compte Payal ?"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_seller_account
msgid "Merchant Account ID"
msgstr "Identifiant du compte marchand"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "Missing value for txn_id (%(txn_id)s) or txn_type (%(txn_type)s)."
msgstr ""
"Valeur manquante pour for txn_id (%(txn_id)s) ou txn_type (%(txn_type)s)."

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Aucune transaction trouvée correspondant à la référence %s."

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid "Notification data were not acknowledged."
msgstr "Données de notification n'ont pas été reconnues."

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "Jeton d'Identité PDT"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_transaction__paypal_type
msgid "PayPal Transaction Type"
msgstr "Type de transaction PayPal"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Intermédiaire de Paiement"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_account_payment_method
msgid "Payment Methods"
msgstr "Méthodes de paiements"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transaction"

#. module: payment_paypal
#: model:account.payment.method,name:payment_paypal.payment_method_paypal
#: model:ir.model.fields.selection,name:payment_paypal.selection__payment_acquirer__provider__paypal
msgid "Paypal"
msgstr "Paypal"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Paypal Instant Payment Notification"
msgstr "Notification Instantanée de Paiement Paypal"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__provider
msgid "Provider"
msgstr "Fournisseur"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "Réception de données avec statut de paiement invalide : %s"

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"Received unrecognized authentication check response code: received %s, "
"expected VERIFIED or INVALID."
msgstr ""
"Code de réponse de contrôle d'authentification non reconnu : reçu %s, "
"attendu VÉRIFIÉ ou NON VALIDE."

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid "Set Paypal credentials"
msgstr "Définir les identifiants Paypal"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""
"Le fournisseur de services de paiement à utiliser avec cet intermédiaire"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_email_account
msgid ""
"The public business email solely used to identify the account with PayPal"
msgstr ""
"L'email professionnel public uniquement utilisé pour identifier le compte "
"avec PayPal"

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"The status of transaction with reference %(ref)s was not synchronized "
"because the 'Payment data transfer' option is not enabled on the PayPal "
"dashboard."
msgstr ""
"Le statut de la transaction portant la référence %(ref)s n'a pas été "
"synchronisé parce que l'option 'Transfert des données de paiement' n'est pas"
" activée dans le tableau de bord de PayPal."

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"The status of transaction with reference %(ref)s was not synchronized "
"because the PDT Identify Token is not configured on the acquirer "
"%(acq_link)s."
msgstr ""
"Le statut de la transaction portant la référence %(ref)s n'a pas été "
"synchronisé parce que le Jeton d'identité PDT n'est pas configuré pour "
"l'acquéreur %(acq_link)s."

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_transaction__paypal_type
msgid "This has no use in Odoo except for debugging."
msgstr "Cela n'a aucune utilité dans Odoo, sauf pour le débogage."

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Use IPN"
msgstr "Utiliser la Notification Instantanée de Paiement"
