<?xml version="1.0"?>
<odoo>
    <record id="sale_order_form_quote" model="ir.ui.view">
        <field name="name">sale.order.form.payment</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">

            <xpath expr="//page/field[@name='order_line']/.." position="after">
                <page string="Optional Products" name="optional_products" attrs="{'invisible': [('state', 'not in', ['draft', 'sent'])]}">
                    <field name="sale_order_option_ids" mode="tree,form,kanban">
                        <form string="Optional Products">
                            <group>
                                <field name="product_id" domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"/>
                                <field name="name"/>
                                <field name="quantity"/>
                                <field name="product_uom_category_id" invisible="1"/>
                                <field name="uom_id" groups="uom.group_uom"/>
                                <field name="price_unit"/>
                                <field name="discount" groups="product.group_discount_per_so_line"/>
                                <field name="is_present" />
                            </group>
                        </form>
                        <kanban class="o_kanban_mobile">
                            <field name="product_id"/>
                            <field name="quantity"/>
                            <field name="uom_id" groups="uom.group_uom"/>
                            <field name="price_unit"/>
                            <field name="is_present" />
                            <templates>
                                <t t-name="kanban-box">
                                    <div class="oe_kanban_card oe_kanban_global_click">
                                        <div class="row">
                                            <div class="col-10">
                                                <strong>
                                                    <span>
                                                        <t t-esc="record.product_id.value"/>
                                                    </span>
                                                </strong>
                                            </div>
                                            <div class="col-2">
                                                <button name="button_add_to_order" class="btn btn-link oe_link fa fa-shopping-cart" title="Add to order lines" type="object" attrs="{'invisible': [('is_present', '=', True)]}"/>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12 text-muted">
                                                <span>
                                                    Quantity:
                                                    <t t-esc="record.quantity.value"/>
                                                    <t t-esc="record.uom_id.value"/>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12 text-muted">
                                                <span>
                                                    Unit Price:
                                                    <t t-esc="record.price_unit.value"/>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </templates>
                        </kanban>
                        <tree string="Sales Quotation Template Lines" editable="bottom" decoration-success="is_present == True">
                            <control>
                                <create name="add_product_control" string="Add a product"/>
                            </control>
                            <field name="sequence" widget="handle"/>
                            <field name="product_id" domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"/>
                            <field name="name" optional="show"/>
                            <field name="quantity"/>
                            <field name="uom_id" string="UoM" groups="uom.group_uom" optional="show"/>
                            <field name="product_uom_category_id" invisible="1"/>
                            <field name="price_unit"/>
                            <field name="discount" string="Disc.%" groups="product.group_discount_per_so_line" optional="show"/>
                            <field name="is_present" invisible="1" />
                            <button name="button_add_to_order" class="oe_link" icon="fa-shopping-cart" title="Add to order lines" type="object" attrs="{'invisible': [('is_present', '=', True)]}"/>
                        </tree>
                    </field>
                </page>
            </xpath>

            <xpath expr="//field[@name='partner_shipping_id']" position="after">
                <field name="sale_order_template_id" context="{'company_id': company_id}"
                    options="{'no_create': True, 'no_open': True}"
                    groups="sale_management.group_sale_order_template"
                />
            </xpath>

        </field>
    </record>

    <record id="sale.action_quotations" model="ir.actions.act_window">
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new quotation, the first step of a new sale!
            </p>
            <p>
                Once the quotation is confirmed by the customer, it becomes a sales order.<br/> You will be able to create an invoice and collect the payment.
            </p>
        </field>
    </record>

    <menuitem id="menu_product_attribute_action"
        action="product.attribute_action"
        parent="sale.prod_config_main"  groups="product.group_product_variant" sequence="1"/>
</odoo>
