# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting_purchase
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_subcontracting_purchase
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_purchase.purchase_order_form_mrp_subcontracting_purchase
msgid "<span class=\"o_stat_text\">Resupply</span>"
msgstr "<span class=\"o_stat_text\">补给</span>"

#. module: mrp_subcontracting_purchase
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_purchase.stock_picking_form_mrp_subcontracting
msgid "<span class=\"o_stat_text\">Source PO</span>"
msgstr "<span class=\"o_stat_text\">来源于PO</span>"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,field_description:mrp_subcontracting_purchase.field_purchase_order__subcontracting_resupply_picking_count
msgid "Count of Subcontracting Resupply"
msgstr "外包补给的数量"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,help:mrp_subcontracting_purchase.field_purchase_order__subcontracting_resupply_picking_count
msgid "Count of Subcontracting Resupply for component"
msgstr "外包补给组件的数量"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,field_description:mrp_subcontracting_purchase.field_stock_picking__subcontracting_source_purchase_count
msgid "Number of subcontracting PO Source"
msgstr "外包PO数量 来源"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,help:mrp_subcontracting_purchase.field_stock_picking__subcontracting_source_purchase_count
msgid "Number of subcontracting Purchase Order Source"
msgstr "外包采购订单的来源数量 "

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "采购订单"

#. module: mrp_subcontracting_purchase
#: code:addons/mrp_subcontracting_purchase/models/stock_picking.py:0
#, python-format
msgid "Source PO of %s"
msgstr "%s的来源PO"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_stock_picking
msgid "Transfer"
msgstr "调拨"
