<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="TicketButton" owl="1">
        <div class="ticket-button" t-att-class="{ highlight: props.isTicketScreenShown }" t-on-click="onClick">
            <div class="with-badge" t-att-badge="count">
                <i class="fa fa-ticket" aria-hidden="true"></i>
            </div>
            <div t-if="!env.isMobile">Orders</div>
        </div>
    </t>

</templates>
