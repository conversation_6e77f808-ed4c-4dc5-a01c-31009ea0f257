<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="model_form_view" model="ir.ui.view">
        <field name="model">ir.model</field>
        <field name="inherit_id" ref="base.view_model_form"/>
        <field name="arch" type="xml">
            <field name="transient" position="after">
                <field name="is_mail_thread" attrs="{'readonly': [('state','!=', 'manual')]}" groups="base.group_no_one"/>
                <field name="is_mail_activity" attrs="{'readonly': [('state','!=', 'manual')]}" groups="base.group_no_one"/>
                <field name="is_mail_blacklist" attrs="{'readonly': [('state','!=', 'manual')]}" groups="base.group_no_one"/>
            </field>
            <xpath expr="//field[@name='field_id']//field[@name='copied']" position="after">
                <field name="tracking" attrs="{'readonly': [('state','!=', 'manual')]}"/>
            </xpath>
        </field>
    </record>

    <record id="model_search_view" model="ir.ui.view">
        <field name="model">ir.model</field>
        <field name="inherit_id" ref="base.view_model_search"/>
        <field name="arch" type="xml">
            <field name="model" position="after">
                <filter string="Mail Thread" name="is_mail_thread" domain="[('is_mail_thread', '=', True)]"/>
                <filter string="Mail Activity" name="is_mail_activity" domain="[('is_mail_activity', '=', True)]"/>
                <filter string="Mail Blacklist" name="is_mail_blacklist" domain="[('is_mail_blacklist', '=', True)]"/>
            </field>
        </field>
    </record>

    <record id="field_form_view" model="ir.ui.view">
        <field name="model">ir.model.fields</field>
        <field name="inherit_id" ref="base.view_model_fields_form"/>
        <field name="arch" type="xml">
            <field name="copied" position="after">
                <field name="tracking" attrs="{'invisible': [('ttype', '=', 'binary')], 'readonly': [('state','!=', 'manual')]}"/>
            </field>
        </field>
    </record>

</odoo>
