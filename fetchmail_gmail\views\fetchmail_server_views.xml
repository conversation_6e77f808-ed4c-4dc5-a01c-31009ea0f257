<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="fetchmail_server_view_form" model="ir.ui.view">
        <field name="name">fetchmail.server.view.form.inherit.gmail</field>
        <field name="model">fetchmail.server</field>
        <field name="priority">100</field>
        <field name="inherit_id" ref="fetchmail.view_email_server_form"/>
        <field name="arch" type="xml">
            <field name="server" position="before">
                <field name="use_google_gmail_service" string="Gmail" attrs="{'readonly': [('state', '=', 'done')]}"/>
            </field>
            <field name="user" position="after">
                <field name="google_gmail_uri" invisible="1"/>
                <field name="google_gmail_refresh_token" invisible="1"/>
                <div></div>
                <div attrs="{'invisible': [('use_google_gmail_service', '=', False)]}">
                    <span attrs="{'invisible': ['|', ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '=', False)]}"
                        class="badge badge-success">
                        Gmail Token Valid
                    </span>
                    <button type="object"
                        name="open_google_gmail_uri" class="btn-link px-0"
                        attrs="{'invisible': ['|', '|', ('google_gmail_uri', '=', False), ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '!=', False)]}">
                        <i class="fa fa-arrow-right"/>
                        Connect your Gmail account
                    </button>
                    <button type="object"
                        name="open_google_gmail_uri" class="btn-link px-0"
                        attrs="{'invisible': ['|', '|', ('google_gmail_uri', '=', False), ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '=', False)]}">
                        <i class="fa fa-cog"/>
                        Edit Settings
                    </button>
                    <div class="alert alert-warning" role="alert"
                        attrs="{'invisible': ['|', ('use_google_gmail_service', '=', False), ('google_gmail_uri', '!=', False)]}">
                        Setup your Gmail API credentials in the general settings to link a Gmail account.
                    </div>
                </div>
            </field>
            <field name="password" position="attributes">
                <attribute name="attrs">{'required' : [('server_type', '!=', 'local'), ('use_google_gmail_service', '=', False), ('password', '!=', False)], 'invisible' : [('use_google_gmail_service', '=', True)]}</attribute>
            </field>
        </field>
    </record>
</odoo>
