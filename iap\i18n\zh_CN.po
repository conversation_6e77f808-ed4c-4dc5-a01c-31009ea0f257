# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr "账号信息"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr "账户令牌"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Buy credits"
msgstr "购买信用"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "公司"

#. module: iap
#: model:ir.model,name:iap.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
msgid "Created by"
msgstr "创建人"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
msgid "Created on"
msgstr "创建时间"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Documentation"
msgstr "文档"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr "IAP"

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr "IAP 账户"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr "IAP 账户"

#. module: iap
#: model:ir.model,name:iap.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr "IAP 丰富线索 API"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
msgid "ID"
msgstr "ID"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Insufficient Balance"
msgstr "余额不足"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Insufficient credit to perform this service."
msgstr "额度不足，无法执行此服务。"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr "Odoo IAP"

#. module: iap
#: model:ir.actions.server,name:iap.open_iap_account
msgid "Open IAP Account"
msgstr "Open IAP 账户"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
msgid "Service Name"
msgstr "服务名称"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Start a Trial at Odoo"
msgstr "在odoo中开始试用"

#. module: iap
#: code:addons/iap/tools/iap_tools.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app. The url it tried to contact was %s"
msgstr "此服务请求的url返回错误。请联系应用程序的作者。它试图联系的网址是 %s"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
#, python-format
msgid "View My Services"
msgstr "查看我的服务"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr "查看您的IAP服务并自己充值"
