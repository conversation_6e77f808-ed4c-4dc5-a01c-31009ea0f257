<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <record id="hr_contract_form_view_inherit" model="ir.ui.view">
        <field name="name">hr.contract.view.form.inherit</field>
        <field name="model">hr.contract</field>
        <field name="inherit_id" ref="hr_contract.hr_contract_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='employee_id']" position="after">
                <field name="type_id"/>
            </xpath>
            <xpath expr="//field[@name='contract_type_id']" position="replace">
                <field name="contract_type_id" invisible="1"/>
            </xpath>
        </field>
    </record>

<!--    CONTRACT TYPE     -->
    <record id="hr_contract_type_view_form" model="ir.ui.view">
            <field name="name">hr.contract.type.form</field>
            <field name="model">hr.contract.type</field>
            <field name="arch" type="xml">
                <form string="Contract Type">
                    <group col="4">
                        <field colspan="4" name="name"/>
                    </group>
                </form>
            </field>
        </record>

        <record id="hr_contract_type_view_tree" model="ir.ui.view">
            <field name="name">hr.contract.type.tree</field>
            <field name="model">hr.contract.type</field>
            <field name="arch" type="xml">
                <tree string="Contract Type">
                    <field name="sequence" widget="handle"/>
                    <field colspan="4" name="name"/>
                </tree>
            </field>
        </record>

        <record id="hr_contract_type_view_search" model="ir.ui.view">
            <field name="name">hr.contract.type.search</field>
            <field name="model">hr.contract.type</field>
            <field name="arch" type="xml">
                <search string="Search Contract Type">
                    <field name="name" string="Contract Type"/>
                    <field name="sequence" string="Sequence"/>
               </search>
            </field>
        </record>

        <record id="action_hr_contract_type" model="ir.actions.act_window">
            <field name="name">Contract Types</field>
            <field name="res_model">hr.contract.type</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="hr_contract_type_view_search"/>
        </record>

        <menuitem
            id="hr_menu_contract_type"
            action="action_hr_contract_type"
            parent="hr.menu_human_resources_configuration"
            sequence="3"
            groups="base.group_no_one"/>
</odoo>
