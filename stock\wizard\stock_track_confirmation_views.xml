<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_stock_track_confirmation" model="ir.ui.view">
        <field name="name">stock.track.confirmation.view.form</field>
        <field name="model">stock.track.confirmation</field>
        <field name="arch" type="xml">
            <form string="Lots or serial numbers were not provided for tracked products">
                <field name="product_ids" invisible="1"/>
                <field name="quant_ids" invisible="1"/>
                <p>Some products of the inventory adjustment are tracked. Are you sure you don't want to specify a serial or lot number for them?</p>
                <strong>Tracked product(s):</strong>
                <field name="tracking_line_ids" readonly="1">
                    <tree>
                        <field name="product_display_name"/>
                        <field name="tracking"/>
                    </tree>
                </field>
                <footer>
                    <button name="action_confirm" string="Confirm" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Discard" special="cancel" data-hotkey="z" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
