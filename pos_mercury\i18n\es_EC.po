# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_mercury
#
# Translators:
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-02-12 08:51+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:75
#, python-format
msgid "&nbsp;&nbsp;APPROVAL CODE:"
msgstr "&nbsp;&nbsp;CODIGO DE APROBACIÓN:"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:186
#, python-format
msgid "128 bit CryptoAPI failed"
msgstr "128 bit CryptoAPI falló"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"<i>Mercury Configurations</i> define what Mercury account will be used when\n"
"                                processing credit card transactions in the "
"Point Of Sale. Setting up a Mercury\n"
"                                configuration will enable you to allow "
"payments with various credit cards\n"
"                                (eg. Visa, MasterCard, Discovery, American "
"Express, ...). After setting up this\n"
"                                configuration you should associate it with a "
"Point Of Sale payment method."
msgstr ""
"<i>Configuraciones de Mercury </i> define que cuenta Mercury se utilizará al "
"procesar las transacciones de tarjetas de crédito en el punto de venta. La "
"creación de una configuración Mercury permitirá que los pagos con diferentes "
"tarjetas de crédito (por ejemplo. Visa, MasterCard, Discovery, American "
"Express, etc.). Después de la creación de esta configuración se debe "
"asociarlo con un Punto de Venta como método de pago."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:46
#, python-format
msgid "ABOVE AMOUNT PURSUANT"
msgstr "CONFORME CANTIDAD ARRIBA"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:60
#, python-format
msgid "APPROVAL CODE:"
msgstr "Código de aprobación"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:190
#, python-format
msgid "All Connections Failed"
msgstr "Todas las conexiones fallaron"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_return
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_transaction
msgid "Allow"
msgstr "Permitir"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Detalle de estado de cuenta"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:45
#, python-format
msgid "CARDHOLDER WILL PAY CARD ISSUER"
msgstr "TITULAR DE LA TARJETA PAGARÁ LA TARJETA DE EMISOR"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_card_brand
msgid "Card Brand"
msgstr "Marca de Tarjeta"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_card_number
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_prefixed_card_number
msgid "Card Number"
msgstr "Número de Tarjeta"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_card_owner_name
msgid "Card Owner Name"
msgstr "Propietario de la Tarjeta de Crédito"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_tree
msgid "Card Reader"
msgstr "Lector de tarjetas"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:196
#, python-format
msgid "Clear Text Request Not Supported"
msgstr "Reqerimiento en texto plano no es soportado"

#. module: pos_mercury
#: model_terms:ir.actions.act_window,help:pos_mercury.action_configuration_form
msgid "Click to configure your card reader."
msgstr "Haga clic para configurar el lector de tarjetas."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:164
#, python-format
msgid "Connect Canceled"
msgstr "Conexión cancelada"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:161
#, python-format
msgid "Connection Lost"
msgstr "Conexión pérdida"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:185
#, python-format
msgid "Control failed to find branded serial (password lookup failed)"
msgstr ""
"El control falló en encontrar la serie marcada (búsqueda de password falló)"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:202
#, python-format
msgid "Could Not Encrypt Response- Call Provider"
msgstr "No se pudo cifrar respuesta - Llamar Proveedor"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_create_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_create_uid
msgid "Created by"
msgstr "Creado por:"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_create_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_create_date
msgid "Created on"
msgstr "Creado"

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury.py:17
#, python-format
msgid "Credit Card"
msgstr "Tarjeta de crédito"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:178
#, python-format
msgid "Disconnecting Socket"
msgstr "Socket desconectado"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_display_name
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:180
#, python-format
msgid "Duplicate Serial Number Detected"
msgstr "Número de serie duplicado detectado"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:7
#, python-format
msgid "Electronic Payment"
msgstr "Pagos electrónicos"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:173
#, python-format
msgid "Empty Command String"
msgstr "Vaciar cadena de comandos"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:198
#, python-format
msgid "Error Occurred While Decrypting Request"
msgstr "Error al descifrar una Solicitud"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:188
#, python-format
msgid "Failed to start Event Thread."
msgstr "Error al iniciar el hilo del Evento."

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"For quickly handling orders: just swiping a credit card when on the payment "
"screen\n"
"                                (without having pressed anything else) will "
"charge the full amount of the order to\n"
"                                the card."
msgstr ""
"Para el manejo de pedidos de forma rápida: basta con deslizar una tarjeta de "
"crédito en la pantalla de pago (sin tener nada presionado),  para que se "
"cargue el importe total de la orden de la tarjeta."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:170
#, python-format
msgid "General Failure"
msgstr "Falla General"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:155
#, python-format
msgid "Global API Not Initialized"
msgstr "API Global No Inicializada"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:192
#, python-format
msgid "Global Response Length Error (Too Short)"
msgstr "Error de respuesta global de longitud  (demasiado corta)"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:194
#, python-format
msgid "Global String Error"
msgstr "Error de texto global"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:262
#, python-format
msgid "Go to payment screen to use cards"
msgstr "r a la pantalla de pago para usar tarjetas"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:393
#, python-format
msgid "Handling transaction..."
msgstr "Manejo de transacciones ..."

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_id
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_id
msgid "ID"
msgstr "ID"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration_merchant_id
msgid "ID of the merchant to authenticate him on the payment provider server"
msgstr ""
"Identificación del comerciante que lo autentique en el servidor de proveedor "
"de pago"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"If you don't already have a Mercury account, contact Mercury at +1 (800) "
"846-4472\n"
"                                to create one."
msgstr ""
"Si aún no dispone de una cuenta Mercury, póngase en contacto al +1 (800) "
"846-4472 para crear una."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:184
#, python-format
msgid "In Process with server"
msgstr "En proceso con el servidor"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:165
#, python-format
msgid "Initialize Failed"
msgstr "Inicialización fallida "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:172
#, python-format
msgid "Insufficient Fields"
msgstr "Campos insuficientes"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:183
#, python-format
msgid "Internal Server Error – Call Provider"
msgstr "Error interno del servidor - Llamar al Proveedor "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:206
#, python-format
msgid "Invalid Account Number"
msgstr "Número de cuenta no válido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:210
#, python-format
msgid "Invalid Authorization Amount"
msgstr "Monto de autorización inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:208
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:209
#, python-format
msgid "Invalid Authorization Code"
msgstr "Código de autorización inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:225
#, python-format
msgid "Invalid Batch Item Count"
msgstr "Lote de conteo de elementos no válido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:224
#, python-format
msgid "Invalid Batch Number"
msgstr "Número de lotes inválidos"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:230
#, python-format
msgid "Invalid Card Type"
msgstr "Tipo de tarjeta inválida"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:211
#, python-format
msgid "Invalid Cash Back Amount"
msgstr "Monto inválido de devolución de dinero"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:199
#, python-format
msgid "Invalid Check Digit"
msgstr "Digito verificador inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:219
#, python-format
msgid "Invalid Check Type"
msgstr "Tipo de Cheque inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:171
#, python-format
msgid "Invalid Command Format"
msgstr "Formato de comando inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:218
#, python-format
msgid "Invalid Date of Birth"
msgstr "Fecha de nacimiento inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:216
#, python-format
msgid "Invalid Derived Key Data"
msgstr "Clave de datos derivada inválida"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:227
#, python-format
msgid "Invalid Driver’s License"
msgstr "Licencia de Conductor inválida"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:207
#, python-format
msgid "Invalid Expiration Date"
msgstr "Fecha de expiración inválida"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:212
#, python-format
msgid "Invalid Gratuity Amount"
msgstr "Cantidad gratuita inválida"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:226
#, python-format
msgid "Invalid MICR Input Type"
msgstr "Tipo de Entrada MICR inválida"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:214
#, python-format
msgid "Invalid Magnetic Stripe Data"
msgstr "Datos de banda magnética no válidos"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:205
#, python-format
msgid "Invalid Memo"
msgstr "Memo inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:222
#, python-format
msgid "Invalid Merchant ID"
msgstr "ID de Comerciante inválida"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:204
#, python-format
msgid "Invalid Operator ID"
msgstr "ID Operador inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:215
#, python-format
msgid "Invalid PIN Block Data"
msgstr "Bloquear PIN con datos no válidos "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:229
#, python-format
msgid "Invalid Pass Data"
msgstr "Datos de acceso inválidos"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:213
#, python-format
msgid "Invalid Purchase Amount"
msgstr "Monto de compra inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:220
#, python-format
msgid "Invalid Routing Number"
msgstr "Número de ruta inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:228
#, python-format
msgid "Invalid Sequence Number"
msgstr "Número de secuencia inválida"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:217
#, python-format
msgid "Invalid State Code"
msgstr "Estado de código inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:223
#, python-format
msgid "Invalid TStream Type"
msgstr "Tipo TStream Inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:221
#, python-format
msgid "Invalid TranCode"
msgstr "TranCode Inválido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:203
#, python-format
msgid "Invalid Transaction Type"
msgstr "Tipo de transacción inválida"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_invoice_no
msgid "Invoice number from Mercury Pay"
msgstr "Número de factura desde Pago Mercury"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_account_journal
msgid "Journal"
msgstr "Diario"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration___last_update
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction___last_update
msgid "Last Modified on"
msgstr "Fecha de modificación"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_write_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_write_uid
msgid "Last Updated by"
msgstr "Ultima Actualización por"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_write_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_write_date
msgid "Last Updated on"
msgstr "Actualizado en"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_transaction
msgid "MagneSafe"
msgstr "MagneSafe"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_merchant_id
msgid "Merchant ID"
msgstr "ID Proveedor"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:200
#, python-format
msgid "Merchant ID Missing"
msgstr "Falta de identificación del comerciante"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_merchant_pwd
msgid "Merchant Password"
msgstr "Contraseña del Proveedor"

#. module: pos_mercury
#: model:ir.actions.act_window,name:pos_mercury.action_configuration_form
#: model:ir.ui.menu,name:pos_mercury.menu_pos_pos_mercury_config
msgid "Mercury Configurations"
msgstr "Configuraciones Mercury "

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_journal_pos_mercury_config_id
msgid "Mercury configuration"
msgstr "Configuración Mercury"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_invoice_no
msgid "Mercury invoice number"
msgstr "Número de factura Mercury"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_record_no
msgid "Mercury record number"
msgstr "Número de registro Mercury"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_ref_no
msgid "Mercury reference number"
msgstr "Número de referencia Mercury"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_name
msgid "Name"
msgstr "Nombre"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration_name
msgid "Name of this Mercury configuration"
msgstr "Nombre de está configuración Mercury"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:334
#, python-format
msgid "No response from Mercury (Mercury down?)"
msgstr "Sin respuesta desde Mercury (¿Mercury fuera de línea?)"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:336
#, python-format
msgid "No response from server (connected to network?)"
msgstr "No hay respuesta del servidor (¿está conectado a la red?)"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:414
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:562
#, python-format
msgid "Odoo error while processing transaction."
msgstr "Odoo error al procesar la transacción."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:639
#, python-format
msgid "One credit card swipe already pending."
msgstr "Una transacción de tarjeta de crédito está pendiente."

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_common
msgid "OneTime"
msgstr "Una vez"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:460
#, python-format
msgid "Partially approved"
msgstr "Parcialmente aprobado"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:181
#, python-format
msgid "Password Failed (Client / Server)"
msgstr "Contraseña incorrecta (Cliente / Servidor)"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:176
#, python-format
msgid "Password Failed – Disconnecting"
msgstr "Contraseña fallida - Desconectando"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:174
#, python-format
msgid "Password Verified"
msgstr "Contraseña verificada"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:182
#, python-format
msgid "Password failed (Challenge / Response)"
msgstr "Contraseña fallida (Impugnar/Respuesta)"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration_merchant_pwd
msgid ""
"Password of the merchant to authenticate him on the payment provider server"
msgstr ""
"Contraseña de comerciante que lo autentique en el servidor de proveedor de "
"pago"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_record_no
msgid "Payment record number from Mercury Pay"
msgstr "El número de registro del pago de Mercury Pay"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_ref_no
msgid "Payment reference number from Mercury Pay"
msgstr "El número de referencia del pago de Mercury Pay"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:407
#, python-format
msgid "Please setup your Mercury merchant account."
msgstr "Por favor configure su cuenta de comerciante Mercury"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_order
msgid "Point of Sale"
msgstr "Punto de Venta"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:175
#, python-format
msgid "Queue Full"
msgstr "Fila llena"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_transaction
msgid "RecordNumberRequested"
msgstr "Número de registro solicitado"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:179
#, python-format
msgid "Refused ‘Max Connections’"
msgstr "Denegado 'Máxima Conexiones'"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:540
#, python-format
msgid "Reversal failed, sending VoidSale..."
msgstr "Reversión falló, enviando VoidSale..."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:576
#, python-format
msgid "Reversal succeeded"
msgstr "Su reversión tuvo éxito"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:543
#, python-format
msgid "Sending reversal..."
msgstr "Enviando reversion..."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:191
#, python-format
msgid "Server Login Failed"
msgstr "Fallo al ingresar el usuario en el servidor"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:160
#, python-format
msgid "Socket Connection Failed"
msgstr "Conexión de Socket falló"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:159
#, python-format
msgid "Socket Creation Failed"
msgstr "Creación de socket fallado"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:157
#, python-format
msgid "Socket Error sending request"
msgstr "Error de Socket al enviar la solicitud"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:158
#, python-format
msgid "Socket already open or in use"
msgstr "Socket ya abierto o en uso"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_transaction
msgid "Swiped"
msgstr "Dibujar"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:177
#, python-format
msgid "System Going Offline"
msgstr "Sistema esta fuera de línea"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:162
#, python-format
msgid "TCP/IP Failed to Initialize"
msgstr "TCP/IP falló al inicializarse "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:47
#, python-format
msgid "TO CARDHOLDER AGREEMENT"
msgstr "ACUERDO A LOS TITULARES DE TARJETA"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:201
#, python-format
msgid "TStream Type Missing"
msgstr "Tipo desconocido TStream "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_card_brand
msgid "The brand of the payment card (e.g. Visa, AMEX, ...)"
msgstr "La marca de la tarjeta de crédito (ejemplo: Visa, AMEX, ...)"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_prefixed_card_number
msgid "The card number used for the payment."
msgstr "El número de tarjeta usado para el pago."

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_journal_pos_mercury_config_id
msgid "The configuration of Mercury used for this journal"
msgstr "La configuración de Mercury usado por este diario"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_card_number
msgid "The last 4 numbers of the card used to pay"
msgstr "Los últimos 4 números de la tarjeta usada para pagar"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_card_owner_name
msgid "The name of the card owner"
msgstr "El nombre del propietario"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:358
#, python-format
msgid ""
"This can be caused by a badly executed swipe or by not having your keyboard "
"layout set to US QWERTY (not US International)."
msgstr ""
"Esto puede ser causado por no contar con la distribución del teclado "
"configurado para Estados Unidos QWERTY (No US International)."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:187
#, python-format
msgid "Threaded Auth Started Expect Response"
msgstr "Hilo de autorización esperando respuesta"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:163
#, python-format
msgid "Time Out waiting for server response"
msgstr "Tiempo de espera de respuesta del servidor agotado"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:169
#, python-format
msgid "Timeout error"
msgstr "Error de tiempo de espera"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:156
#, python-format
msgid "Timeout on Response"
msgstr "Error de tiempo en respuesta"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:152
#, python-format
msgid "Transaction approved"
msgstr "Transacción aprobada"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:193
#, python-format
msgid "Unable to Parse Response from Global (Indistinguishable)"
msgstr "No se puede analizar la respuesta desde Global (Indistinguible)"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:166
#, python-format
msgid "Unknown Error"
msgstr "Error desconocido"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:197
#, python-format
msgid "Unrecognized Request Format"
msgstr "Requerimiento de formato desconocido"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"Using the Mercury integration in the Point Of Sale is easy: just press the\n"
"                                associated payment method. After that the "
"amount can be adjusted (eg. for cashback)\n"
"                                just like on any other payment line. "
"Whenever the payment line is set up, a card\n"
"                                can be swiped through the card reader device."
msgstr ""
"Mediante la integración de Mercury en el punto de venta es fácil: sólo tiene "
"que pulsar el método de pago asociado. Después de que el monto puede ser "
"ajustado (por ejemplo. Para la devolución de dinero) al igual que en "
"cualquier otra línea de pago. Siempre que la línea de pago está configurada, "
"una tarjeta se puede pasar a través del dispositivo lector de tarjetas."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:584
#, python-format
msgid "VoidSale succeeded"
msgstr "VoidSale exitosa"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:17
#, python-format
msgid "WAITING FOR SWIPE"
msgstr "Esperando por trazos"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"We currently support the MagTek Dynamag card reader device. It can be "
"connected\n"
"                                directly to the Point Of Sale device or it "
"can be connected to the POSBox."
msgstr ""
"Actualmente apoyamos el dispositivo lector de tarjetas MagTek Dynamag. Se "
"puede conectar directamente al punto de venta dispositivo o puede ser "
"conectado a la POSBox."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:195
#, python-format
msgid "Weak Encryption Request Not Supported"
msgstr "Solicitud de cifrado débil no compatible"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:189
#, python-format
msgid "XML Parse Error"
msgstr "Error de análisis XML "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:50
#, python-format
msgid "X______________________________"
msgstr "X______________________________"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_barcode_rule
msgid "barcode.rule"
msgstr "Reglas de Código de Barras"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_configuration
msgid "pos_mercury.configuration"
msgstr "pos_mercury.configuration"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_mercury_transaction
msgid "pos_mercury.mercury_transaction"
msgstr "pos_mercury.mercury_transaction"
