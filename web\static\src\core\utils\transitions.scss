/**
 * This file includes all the t-transition mixins.
 */

/*
  Fade in and out with t-transition
  Usage:
    scss: @include owl-fade(0.5s, 'o_notification')
    xml: <tag t-transition="o_notification_fade" />
 */
@mixin owl-fade($time, $name) {
  .#{$name}_fade-enter-active,
  .#{$name}_fade-active {
    transition: all $time;
  }

  .#{$name}_fade-enter {
    opacity: 0;
  }

  .#{$name}_fade-leave-to {
    opacity: 0;
  }
}
