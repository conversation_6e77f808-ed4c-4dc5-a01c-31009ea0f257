# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_lead_website
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid ""
"1 credit is consumed per visitor matching the website traffic conditions and"
" whose company can be identified.<br/>"
msgstr ""
"1 crédit est consommé par visiteur correspondant aux conditions de trafic du"
" site web et dont l'entreprise peut être identifiée."

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Leads </span>"
msgstr "<span class=\"o_stat_text\"> Pistes commerciales </span>"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Opportunities </span>"
msgstr "<span class=\"o_stat_text\"> Opportunités </span>"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__active
msgid "Active"
msgstr "Active"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Archived"
msgstr "Archivé"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_reveal_rule
msgid "CRM Lead Generation Rules"
msgstr "Règles de génération des pistes commerciales du CRM"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_reveal_view
msgid "CRM Reveal View"
msgstr "Vue de CRM Révélation"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__lead_for
msgid "Choose whether to track companies only or companies and their contacts"
msgstr ""
"Choisissez de suivre uniquement les entreprises ou les entreprises et leurs "
"contacts"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_for__companies
msgid "Companies"
msgstr "Sociétés"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_for__people
msgid "Companies and their Contacts"
msgstr "Sociétés et leurs contacts"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__company_size_min
msgid "Company Size"
msgstr "Taille de la société"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__company_size_max
msgid "Company Size Max"
msgstr "Taille maximum de la société"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Contact Filter"
msgstr "Filtre des contacts"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__country_ids
msgid "Countries"
msgstr "Pays"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__create_date
msgid "Create Date"
msgstr "Date de création"

#. module: crm_iap_lead_website
#: model_terms:ir.actions.act_window,help:crm_iap_lead_website.crm_reveal_rule_action
msgid "Create a conversion rule"
msgstr "Créer une règle de conversion"

#. module: crm_iap_lead_website
#: model_terms:ir.actions.act_window,help:crm_iap_lead_website.crm_reveal_rule_action
msgid ""
"Create rules to generate B2B leads/opportunities from your website visitors."
msgstr ""
"Créer des règles pour générer des pistes/opportunités commerciales B2B à "
"partir des visiteurs de votre site web"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__create_date
msgid "Created on"
msgstr "Créé le"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_for
msgid "Data Tracking"
msgstr "Suivi des données"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__display_name
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Enter Valid Regex."
msgstr "Entrez un regex valide."

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__contact_filter_type
msgid "Filter On"
msgstr "Filtrer sur"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__filter_on_size
msgid "Filter companies based on their size."
msgstr "Filtrer les sociétés en fonction de leur taille"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__filter_on_size
msgid "Filter on Size"
msgstr "Filtrer en fonction de la taille"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "From"
msgstr "De"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_ids
msgid "Generated Lead / Opportunity"
msgstr "Piste / opportunité commerciale générée"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_ir_http
msgid "HTTP Routing"
msgstr "Routage HTTP"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__2
msgid "High"
msgstr "Élevé"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_iap_credits
msgid "IAP Credits"
msgstr "Crédits IAP"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__id
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__id
msgid "ID"
msgstr "ID"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_ip
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_ip
msgid "IP Address"
msgstr "Adresse IP"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__industry_tag_ids
msgid "Industries"
msgstr "Industries"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule____last_update
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__write_date
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_type__lead
msgid "Lead"
msgstr "Piste"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Lead Data"
msgstr "Données sur la piste commerciale"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_lead_opportunity_form
msgid "Lead Generation Information"
msgstr "Information concernant la génération de piste commerciale"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_rule_id
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_rule_id
msgid "Lead Generation Rule"
msgstr "Règle de génération de piste commerciale"

#. module: crm_iap_lead_website
#: model:ir.actions.act_window,name:crm_iap_lead_website.crm_reveal_view_action
#: model:ir.ui.menu,name:crm_iap_lead_website.crm_reveal_view_menu_action
msgid "Lead Generation Views"
msgstr "Vues de génération de piste commerciale"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid ""
"Lead Generation requires a GeoIP resolver which could not be found on your "
"system. Please consult https://pypi.org/project/GeoIP/."
msgstr ""
"La génération de piste commerciale nécessite un GeoIP resolver introuvable "
"sur votre système. Veuillez consulter https://pypi.org/project/GeoIP/."

#. module: crm_iap_lead_website
#: model:ir.actions.server,name:crm_iap_lead_website.ir_cron_crm_reveal_lead_ir_actions_server
#: model:ir.cron,cron_name:crm_iap_lead_website.ir_cron_crm_reveal_lead
#: model:ir.cron,name:crm_iap_lead_website.ir_cron_crm_reveal_lead
msgid "Lead Generation: Leads/Opportunities Generation"
msgstr "Génération de Pistes: Génération de Pistes/Opportunités"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Piste/opportunité"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__industry_tag_ids
msgid "Leave empty to always match. Odoo will not create lead if no match"
msgstr ""
"Laissez vide pour toujours correspondre. Odoo ne créera pas de piste s'il "
"n'y a pas de correspondance"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__0
msgid "Low"
msgstr "Faible"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid ""
"Make sure you know if you have to be GDPR compliant for storing personal "
"data."
msgstr ""
"Assurez-vous de savoir si vous devez être conforme au RGPD pour stocker des "
"données personnelles."

#. module: crm_iap_lead_website
#: model:ir.model.constraint,message:crm_iap_lead_website.constraint_crm_reveal_rule_limit_extra_contacts
msgid "Maximum 5 contacts are allowed!"
msgstr "5 contacts sont autorisés au maximum"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__1
msgid "Medium"
msgstr "Moyen"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Missing Library"
msgstr "Librairie manquante"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_view__reveal_state__not_found
msgid "Not Found"
msgstr "Introuvable"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__extra_contacts
msgid "Number of Contacts"
msgstr "Nombre de contacts"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_count
msgid "Number of Generated Leads"
msgstr "Nombre de pistes commerciales générées"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__opportunity_count
msgid "Number of Generated Opportunity"
msgstr "Nombre d'opportunités générées"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__country_ids
msgid ""
"Only visitors of following countries will be converted into "
"leads/opportunities (using GeoIP)."
msgstr ""
"Seuls les visiteurs des pays suivants seront convertis en pistes / "
"opportunités commerciales (en utilisant GeoIP)."

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__state_ids
msgid ""
"Only visitors of following states will be converted into "
"leads/opportunities."
msgstr ""
"Seuls les visiteurs des états suivants seront convertis en pistes / "
"opportunités commerciales"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_type__opportunity
msgid "Opportunity"
msgstr "Opportunité"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Opportunity Data"
msgstr "Données sur l'opportunité commerciale"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Opportunity Generation Conditions"
msgstr "Conditions de génération d'opportunité"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Opportunity created by Odoo Lead Generation"
msgstr "Opportunité créée par le Générateur de Pistes d'Odoo"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__other_role_ids
msgid "Other Roles"
msgstr "Autres rôles"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__preferred_role_id
msgid "Preferred Role"
msgstr "Rôle préféré"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__priority
msgid "Priority"
msgstr "Priorité"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__regex_url
msgid ""
"Regex to track website pages. Leave empty to track the entire website, or / "
"to target the homepage. Example: /page* to track all the pages which begin "
"with /page"
msgstr ""
"Regex pour tracker des pages du site internet. Laissez vide pour tracker "
"l'entierté du site internet, ou / pour cibler la page d'accueil. Exemple: "
"/page* pour tracker toutes les pages qui commencent par /page."

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__website_id
msgid "Restrict Lead generation to this website."
msgstr "Restreindre la génération de piste commerciale à ce site web."

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__contact_filter_type__role
msgid "Role"
msgstr "Rôle"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Rule"
msgstr "Règle"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__name
msgid "Rule Name"
msgstr "Nom de la règle"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__team_id
msgid "Sales Team"
msgstr "Équipe commerciale"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__user_id
msgid "Salesperson"
msgstr "Vendeur"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Search CRM Reveal Rule"
msgstr "Chercher une règle de CRM Révélation"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__seniority_id
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__contact_filter_type__seniority
msgid "Seniority"
msgstr "séniorité"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_state
msgid "State"
msgstr "État"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__state_ids
msgid "States"
msgstr "États"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__suffix
msgid "Suffix"
msgstr "Suffixe"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__tag_ids
msgid "Tags"
msgstr "Étiquettes"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__extra_contacts
msgid ""
"This is the number of contacts to track if their role/seniority match your "
"criteria. Their details will show up in the history thread of generated "
"leads/opportunities. One credit is consumed per tracked contact."
msgstr ""
"C'est le nombre de contacts à suivre si leur rôle / ancienneté correspond à "
"vos critères. Leurs détails apparaîtront dans l'historique des prospects / "
"opportunités générés. Un crédit est consommé par contact suivi."

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__suffix
msgid ""
"This will be appended in name of generated lead so you can identify "
"lead/opportunity is generated with this rule"
msgstr ""
"Ceci sera ajouté au nom du prospect généré afin que vous puissiez identifier"
" le prospect / opportunité générée avec cette règle"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_view__reveal_state__to_process
msgid "To Process"
msgstr "A traiter"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_type
msgid "Type"
msgstr "Type"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__regex_url
msgid "URL Expression"
msgstr "Expression URL"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Up to"
msgstr "jusqu'à"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__sequence
msgid ""
"Used to order the rules with same URL and countries. Rules with a lower "
"sequence number will be processed first."
msgstr ""
"Utilisé pour régir les règles avec les mêmes URL et pays. Les règles avec un"
" numéro de séquence inférieur seront traitées en premier."

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__3
msgid "Very High"
msgstr "Très haut"

#. module: crm_iap_lead_website
#: model:ir.actions.act_window,name:crm_iap_lead_website.crm_reveal_rule_action
#: model:ir.ui.menu,name:crm_iap_lead_website.crm_reveal_rule_menu_action
msgid "Visits to Leads Rules"
msgstr "Règles de Visites à Pistes"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__website_id
msgid "Website"
msgstr "Site Web"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Website Traffic Conditions"
msgstr "Conditions du Traffic du Site Internet"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "additional credit(s) are consumed if the company matches this rule."
msgstr ""
"les crédits additionnels sont consommés si la société est conforme à cette "
"condition."

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "e.g. /page"
msgstr "par exemple /page"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "e.g. US Visitors"
msgstr "par exemple visiteurs américains"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "employees"
msgstr "employés"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "to"
msgstr "au"
