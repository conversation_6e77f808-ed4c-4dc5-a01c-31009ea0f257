# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed_slides_count
msgid "# Completed Slides"
msgstr "عدد الشرائح المكتملة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__count_views
msgid "# Views"
msgstr "عدد المشاهدات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__public_views
msgid "# of Public Views"
msgstr "عدد المشاهدات العامة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_views
msgid "# of Website Views"
msgstr "عدد مشاهدات الموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completion
msgid "% Completed Slides"
msgstr "نسبة الشرائح المكتملة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "'. Showing results for '"
msgstr "'. يتم عرض النتائج لـ '"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_slides_list.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "(empty)"
msgstr "(فارغ)"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ". This way, they will be secured."
msgstr ". وبهذه الطريقة، سيكونون في أمان. "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_5
msgid "3 Main Methodologies"
msgstr "3 منهجيات رئيسية "

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "<b>%s</b> is requesting access to this course."
msgstr "<b>%s</b> يطلب إذن الوصول إلى هذه الدورة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<b>(empty)</b>"
msgstr "<b>(فارغ)</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<b>Order by</b>"
msgstr "<b>ترتيب حسب</b>"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"<b>Save & Publish</b> your lesson to make it available to your attendees."
msgstr "<b>قم بحفظ ونشر</b> دروسك لجعلها متاحة لحاضريك. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "<b>Save</b> your question."
msgstr "<b>احفظ</b> سؤالك."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<b>Uncategorized</b>"
msgstr "<b>غير مصنف</b>"

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        You have been invited to join a new course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        مرحباً<br/><br/>\n"
"        لقد تمت دعوتك لحضور دورة جديدة: <t t-out=\"object.channel_id.name or ''\">مبادئ العناية بالحدائق</t>.\n"
"    </p>\n"
"</div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_completed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p><br/>\n"
"                        <p><b>Congratulations!</b></p>\n"
"                        <p>You've completed the course <b t-out=\"object.channel_id.name or ''\">Basics of Gardening</b></p>\n"
"                        <p>Check out the other available courses.</p><br/>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Explore courses\n"
"                            </a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"object.channel_id.user_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">مرحباً <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>،</p><br/>\n"
"                        <p><b>تهانينا!</b></p>\n"
"                        <p>لقد أكملت دورة <b t-out=\"object.channel_id.name or ''\">مبادئ العناية بالحدائق</b></p>\n"
"                        <p>ألقِ نظرة على الدورات الأخرى المتاحة.</p><br/>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                استكشف الدورات\n"
"                            </a>\n"
"                        </div>\n"
"                        استمتع بهذا المحتوى الحصري!\n"
"                        <t t-if=\"object.channel_id.user_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <t t-out=\"object.slide_type or ''\">document</t> <strong t-out=\"object.name or ''\">Trees</strong> with you!\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View <strong t-out=\"object.name or ''\">Trees</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        مرحباً <br/><br/>\n"
"                        <t t-out=\"user.name or ''\">قام Mitchell Admin</t> بمشاركة <t t-out=\"object.slide_type or ''\">تفرعات المستند</t> <strong t-out=\"object.name or ''\">معك!</strong>\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">عرض <strong t-out=\"object.name or ''\">التفرعات</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_published
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        There is something new in the course <strong t-out=\"object.channel_id.name or ''\">Trees, Wood and Gardens</strong> you are following:<br/><br/>\n"
"                        <center><strong t-out=\"object.name or ''\">Trees</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View content</a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        مرحباً<br/><br/>\n"
"                        لقد تمت إضافة محتوى جديد في دورة <strong t-out=\"object.channel_id.name or ''\">الأشجار، الخشب، والحدائق</strong> التي تتبعها:<br/><br/>\n"
"                        <center><strong t-out=\"object.name or ''\">التفرعات</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">عرض المحتوى</a>\n"
"                        </div>\n"
"                        استمتع بهذا المحتوى الحصري!\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>ميتشل آدمن</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"fa fa-arrow-right mr-1\"/>All Courses"
msgstr "<i class=\"fa fa-arrow-right mr-1\"/>جميع الدورات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-bar-chart\"/> Statistics"
msgstr "<i class=\"fa fa-bar-chart\"/> الإحصائيات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Lessons</span>"
msgstr ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ml-1\">الدروس</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-chevron-left mr-2\"/> <span class=\"d-none d-sm-inline-"
"block\">Prev</span>"
msgstr ""
"<i class=\"fa fa-chevron-left mr-2\"/> <span class=\"d-none d-sm-inline-"
"block\">السابق</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-circle-o-notch fa-spin mr-2\"/><b>Loading...</b>"
msgstr "<i class=\"fa fa-circle-o-notch fa-spin mr-2\"/><b>جاري التحميل...</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-clock-o mr-2\" aria-label=\"Create date\"/>"
msgstr "<i class=\"fa fa-clock-o mr-2\" aria-label=\"Create date\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-clock-o mr-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"
msgstr ""
"<i class=\"fa fa-clock-o mr-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"المدة \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-cloud-upload mr-1\"/>Upload new content"
msgstr "<i class=\"fa fa-cloud-upload mr-1\"/>تحميل محتوى جديد"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-code\"/> Embed"
msgstr "<i class=\"fa fa-code\"/> تضمين"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-comments\"/> Comments ("
msgstr "<i class=\"fa fa-comments\"/> التعليقات ("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-desktop mr-2\"/>\n"
"                <span class=\"d-none d-sm-inline-block\">Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-desktop mr-2\"/>\n"
"                <span class=\"d-none d-sm-inline-block\">ملء الشاشة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-envelope\"/> Email"
msgstr "<i class=\"fa fa-envelope\"/> البريد الإلكتروني"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<i class=\"fa fa-envelope\"/> Send Email"
msgstr "<i class=\"fa fa-envelope\"/> إرسال بريد إلكتروني "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-eraser mr-1\"/>Clear filters"
msgstr "<i class=\"fa fa-eraser mr-1\"/>إزالة عوامل التصفية "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "<i class=\"fa fa-eraser\"/> Clear filters"
msgstr "<i class=\"fa fa-eraser\"/> إزالة عوامل التصفية "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> This document is private."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"انتباه \"/> هذا المستند خاص."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-eye mr-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye mr-2\" aria-label=\"Views\" role=\"img\" title=\"المشاهدات \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-code-o mr-2\" aria-label=\"Webpage\" role=\"img\" "
"title=\"Webpage\"/>"
msgstr ""
"<i class=\"fa fa-file-code-o mr-2\" aria-label=\"Webpage\" role=\"img\" "
"title=\"صفحة الويب \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-image-o mr-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"
msgstr ""
"<i class=\"fa fa-file-image-o mr-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"مخطط معلومات بياني \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-pdf-o mr-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"
msgstr ""
"<i class=\"fa fa-file-pdf-o mr-2\" aria-label=\"Document\" role=\"img\" "
"title=\"مستند \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-video-o mr-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"
msgstr ""
"<i class=\"fa fa-file-video-o mr-2\" aria-label=\"Video\" role=\"img\" "
"title=\"مقطع فيديو \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-flag mr-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"
msgstr "<i class=\"fa fa-flag mr-2\" aria-label=\"Quiz\" role=\"img\" title=\"اختبار \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<i class=\"fa fa-flag text-warning\"/> Quiz"
msgstr "<i class=\"fa fa-flag text-warning\"/>اختبار قصير "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen_sidebar_category
msgid "<i class=\"fa fa-flag-checkered text-warning mr-2\"/>Quiz"
msgstr "<i class=\"fa fa-flag-checkered text-warning mr-2\"/>اختبار قصير "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-folder mr-2\" aria-label=\"Open folder\"/>"
msgstr "<i class=\"fa fa-folder mr-2\" aria-label=\"Open folder\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-folder-o mr-1\"/><span>Add Section</span>"
msgstr "<i class=\"fa fa-folder-o mr-1\"/><span>إضافة قسم</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-folder-o mr-1\"/>Add a section"
msgstr "<i class=\"fa fa-folder-o mr-1\"/>إضافة قسم "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "<i class=\"fa fa-graduation-cap mr-1\"/>All courses"
msgstr "<i class=\"fa fa-graduation-cap mr-1\"/>جميع الدورات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-home\"/> About"
msgstr "<i class=\"fa fa-home\"/> عن المحتوى"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-home\"/> Course"
msgstr "<i class=\"fa fa-home\"/>الدورة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block ml-1\">Back "
"to course</span>"
msgstr ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block "
"ml-1\">العودة إلى الدورة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-info-circle\"/>\n"
"                    The social sharing module will be unlocked when a moderator will allow your publication."
msgstr ""
"<i class=\"fa fa-info-circle\"/>\n"
"                    سوف يتم إلغاء قفل تطبيق المشاركة الاجتماعية عندما يسمح المنسق بنشر عملك. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<i class=\"fa fa-plus mr-1\"/> <span class=\"d-none d-md-inline-block\">Add "
"Content</span>"
msgstr ""
"<i class=\"fa fa-plus mr-1\"/><span class=\"d-none d-md-inline-block\">إضافة"
" محتوى</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-plus mr-1\"/><span>Add Content</span>"
msgstr "<i class=\"fa fa-plus mr-1\"/><span>إضافة محتوى</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>Add Question</span>"
msgstr ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>إضافة سؤال</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>Add Quiz</span>"
msgstr ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>إضافة اختبار قصير</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-question mr-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"
msgstr ""
"<i class=\"fa fa-question mr-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"عدد الأسئلة \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-share-alt\"/> Share"
msgstr "<i class=\"fa fa-share-alt\"/> مشاركة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-share-alt\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt\"/><span class=\"d-none d-md-inline-block "
"ml-1\">مشاركة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "<i class=\"fa fa-share-square fa-fw\"/> Share"
msgstr "<i class=\"fa fa-share-square fa-fw\"/>مشاركة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-share-square\"/> Share"
msgstr "<i class=\"fa fa-share-square\"/> مشاركة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Exit Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ml-1\">الخروج من وضع ملء الشاشة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star text-black-25\" aria-label=\"A star\"/>"
msgstr "<i class=\"fa fa-star text-black-25\" aria-label=\"A star\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star\" aria-label=\"A star\" role=\"img\"/>"
msgstr "<i class=\"fa fa-star\" aria-label=\"A star\" role=\"img\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star-half-o\" aria-label=\"Half a star\" role=\"img\"/>"
msgstr "<i class=\"fa fa-star-half-o\" aria-label=\"Half a star\" role=\"img\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid ""
"<i class=\"fa fa-tag mr-2 text-muted\"/>\n"
"                      My Courses"
msgstr ""
"<i class=\"fa fa-tag mr-2 text-muted\"/>\n"
"                      دوراتي "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-thumbs-down fa-1x\" role=\"img\" aria-label=\"Dislikes\" "
"title=\"Dislikes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down fa-1x\" role=\"img\" aria-label=\"Dislikes\" "
"title=\"عدم الإعجاب \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-thumbs-up fa-1x\" role=\"img\" aria-label=\"Likes\" title=\"Likes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up fa-1x\" role=\"img\" aria-label=\"Likes\" "
"title=\"الإعجابات \"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid ""
"<small class=\"text-success\">\n"
"                                Request already sent\n"
"                            </small>"
msgstr ""
"<small class=\"text-success\">\n"
"                                تم إرسال الطلب بالفعل\n"
"                            </small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid ""
"<small><span class=\"badge badge-pill badge-success pull-right my-1 py-1 "
"px-2 font-weight-normal\"><i class=\"fa fa-check\"/> "
"Completed</span></small>"
msgstr ""
"<small><span class=\"badge badge-pill badge-success pull-right my-1 py-1 "
"px-2 font-weight-normal\"><i class=\"fa fa-check\"/> مكتمل</span></small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<span class=\"badge badge-danger\">Unpublished</span>"
msgstr "<span class=\"badge badge-danger\">غير منشور</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge badge-info badge-arrow-right font-weight-normal px-2 "
"py-1 m-1\">New</span>"
msgstr ""
"<span class=\"badge badge-info badge-arrow-right font-weight-normal px-2 "
"py-1 m-1\">جديد</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<span class=\"badge badge-info\">Preview</span>"
msgstr "<span class=\"badge badge-info\">معاينة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge badge-light badge-hide border font-weight-normal px-2 "
"py-1 m-1\">Add Quiz</span>"
msgstr ""
"<span class=\"badge badge-light badge-hide border font-weight-normal px-2 "
"py-1 m-1\">إضافة اختبار قصير</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid ""
"<span class=\"badge badge-pill badge-success pull-right py-1 px-2\"><i "
"class=\"fa fa-check\"/> Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success pull-right py-1 px-2\"><i "
"class=\"fa fa-check\"/>مكتملة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid ""
"<span class=\"badge badge-pill badge-success py-1 px-2 mx-auto\" "
"style=\"font-size: 1em\"><i class=\"fa fa-check\"/> Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success py-1 px-2 mx-auto\" "
"style=\"font-size: 1em\"><i class=\"fa fa-check\"/> مكتملة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<span class=\"badge badge-pill badge-success py-1 px-2\" style=\"font-size: "
"1em\"><i class=\"fa fa-check\"/> Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success py-1 px-2\" style=\"font-size: "
"1em\"><i class=\"fa fa-check\"/> مكتملة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-check\"/> "
"Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-"
"check\"/>مكتملة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\"><span>Preview</span></span>"
msgstr ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\"><span>معاينة</span></span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\">Preview</span>"
msgstr ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\">معاينة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge font-weight-bold px-2 py-1 m-1 badge-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"
msgstr ""
"<span class=\"badge font-weight-bold px-2 py-1 m-1 badge-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"d-none d-sm-inline-block\">Next</span> <i class=\"fa fa-"
"chevron-right ml-2\"/>"
msgstr ""
"<span class=\"d-none d-sm-inline-block\">التالي</span><i class=\"fa fa-"
"chevron-right ml-2\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-arrow-right\"/>\n"
"                                    Create a Google Project and Get a Key"
msgstr ""
"<span class=\"fa fa-arrow-right\"/>\n"
"                                     إنشاء مشروع Google والحصول على مفتاح "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
msgid "<span class=\"fa fa-clipboard\"> Copy Text</span>"
msgstr "<span class=\"fa fa-clipboard\">نسخ النص</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "<span class=\"font-weight-bold text-muted mr-2\">Current rank:</span>"
msgstr "<span class=\"font-weight-bold text-muted mr-2\">المرتبة الحالية:</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "<span class=\"font-weight-normal\">Last update:</span>"
msgstr "<span class=\"font-weight-normal\">آخر تحديث:</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid ""
"<span class=\"form-text text-muted d-block w-100\">Send presentation through"
" email</span>"
msgstr ""
"<span class=\"form-text text-muted d-block w-100\">إرسال العرض التقديمي عن "
"طريق البريد الإلكتروني</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Slides</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">الشرائح</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"القيم التي تعين هنا تحدد حسب الموقع.\" groups=\"website.group_multi_website\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_partner_view_form
msgid "<span class=\"o_stat_text\">Courses</span>"
msgstr "<span class=\"o_stat_text\">الدورات</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card_information_arrow
msgid "<span class=\"o_wslides_arrow\">New Content</span>"
msgstr "<span class=\"o_wslides_arrow\">محتوى جديد</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training
msgid "<span class=\"p-2\">Course content</span>"
msgstr "<span class=\"p-2\">محتوى الدورة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "<span class=\"text-500 mx-2\">•</span>"
msgstr "<span class=\"text-500 mx-2\">•</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted font-weight-bold mr-3\">Rating</span>"
msgstr "<span class=\"text-muted font-weight-bold mr-3\">التقييم</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Attendees</span>"
msgstr "<span class=\"text-muted\">الحاضرين</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Common tasks for a computer scientist</span>"
msgstr "<span class=\"text-muted\">المهام الاعتيادية لعالِم الحاسوب</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Contents</span>"
msgstr "<span class=\"text-muted\">المحتويات</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Parts of computer science</span>"
msgstr "<span class=\"text-muted\">أجزاء من علوم الحاسوب</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"first\" class=\"mr-1 mr-sm-2\" title=\"First slide\" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"Previous slide\" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"Next slide\" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"
msgstr ""
"<span id=\"first\" class=\"mr-1 mr-sm-2\" title=\"First slide\" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"Previous slide\" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"Next slide\" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"fullscreen\" class=\"ml-1 ml-sm-2\" title=\"View fullscreen\" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"fullscreen\" class=\"ml-1 ml-sm-2\" title=\"View fullscreen\" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"zoomout\" class=\"d-none d-sm-inline ml-2 mr-2\" title=\"Zoom out\" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-none d-sm-inline\" title=\"Zoom in\" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"zoomout\" class=\"d-none d-sm-inline ml-2 mr-2\" title=\"Zoom out\" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-none d-sm-inline\" title=\"Zoom in\" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span name=\"done_members_count_label\" class=\"text-muted\">Finished</span>"
msgstr "<span name=\"done_members_count_label\" class=\"text-muted\">منتهية</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "<span name=\"members_done_count_label\" class=\"o_stat_text\">Finished</span>"
msgstr "<span name=\"members_done_count_label\" class=\"o_stat_text\">منتهية</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span> hours</span>"
msgstr "<span> الساعات</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<span>Add Tag</span>"
msgstr "<span>إضافة علامة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Answering Questions</span>"
msgstr "<span>إجابة الأسئلة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking Question</span>"
msgstr "<span>طرح سؤال</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking the right question</span>"
msgstr "<span>طرح السؤال المناسب</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Logic</span>"
msgstr "<span>منطق</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Mathematics</span>"
msgstr "<span>الرياضيات</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "<span>Preview</span>"
msgstr "<span>معاينة</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Science</span>"
msgstr "<span>العلوم</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>XP</span>"
msgstr "<span>XP</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_fullscreen_player.js:0
#: code:addons/website_slides/static/src/js/slides_share.js:0
#, python-format
msgid "<strong>Thank you!</strong> Mail has been sent."
msgstr "<strong>شكراً لك!</strong> لقد تم إرسال البريد الإلكتروني. "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_1
msgid "A Mighty Forest from Ages"
msgstr "غابة هائلة من قديم الزمان "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_0
msgid "A fruit"
msgstr "فاكهة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"A good course has a structure. Pick a name for your first section and click "
"<b>Save</b> to create it."
msgstr ""
"يجب أن تحتوي الدورة الجيدة على هيكل. اختر اسماً للجزء الأول من دورتك ثم اضغط"
" على <b>حفظ</b> لإنشائه. "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_4
msgid "A little chat with Harry Potted"
msgstr "دردشة قصيرة مع Harry Potted "

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_2_gard2
msgid ""
"A lot of nice documentation: trees, wood, gardens. A gold mine for "
"references."
msgstr ""
"العديد من الوثائق الرائعة: الأشجار، الأخشاب، الحدائق. إنه منجم ذهب للمراجع. "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_0
msgid "A shovel"
msgstr "مجرفة "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_exclusion_html_content_and_url
msgid ""
"A slide is either filled with a document url or HTML content. Not both."
msgstr ""
"عادة ما تكون الشريحة بها رابط URL للمستند أو محتوى HTML، ولكن ليس الاثنين "
"معاً. "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_1
msgid "A spoon"
msgstr "ملعقة "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_5
msgid "A summary of know-how: how and what."
msgstr "ملخص المعرفة: كيف وماذا. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_0
msgid ""
"A summary of know-how: how and what. All the basics for this course about "
"gardening."
msgstr ""
"ملخص المعرفة: كيف وماذا. كافة المبادئ لهذه الدورة عن العناية بالحدائق. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_0
msgid ""
"A summary of know-how: what are the main trees categories and how to "
"differentiate them."
msgstr "ملخص المعرفة: فئات الأشجار الرئيسية وكيفية التفريق بينها. "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_2
msgid "A table"
msgstr "طاولة "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_tag_slide_tag_unique
msgid "A tag must be unique!"
msgstr "يجب أن تكون علامة التصنيف فريدةً من نوعها! "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_1
msgid "A vegetable"
msgstr "خضار "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "API Key"
msgstr "مفتاح الواجهة البرمجية للتطبيق (API)"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Access Granted"
msgstr "تم السماح بالوصول "

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_groups
msgid "Access Groups"
msgstr "مجموعات الوصول"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Access Refused"
msgstr "تم رفض الوصول "

#. module: website_slides
#: model:mail.activity.type,name:website_slides.mail_activity_data_access_request
msgid "Access Request"
msgstr "أذونات الوصول "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_requested_access
msgid "Access Requested"
msgstr "تم طلب إذن بالوصول "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Access Rights"
msgstr "صلاحيات الوصول"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction
msgid "Action Needed"
msgstr "بحاجة إلى اتخاذ إجراء "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Actions"
msgstr "الإجراءات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__active
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__active
msgid "Active"
msgstr "نشط"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#, python-format
msgid "Add"
msgstr "إضافة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_comment
msgid "Add Comment"
msgstr "إضافة تعليق"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Content"
msgstr "إضافة محتوى"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_review
msgid "Add Review"
msgstr "إضافة مراجعة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Section"
msgstr "إضافة قسم"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Add Tag"
msgstr "إضافة علامة تصنيف "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
msgid "Add a new lesson"
msgstr "إضافة درس جديد "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#, python-format
msgid "Add a section"
msgstr "إضافة قسم"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#, python-format
msgid "Add a tag"
msgstr "إضافة علامة تصنيف "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Add an answer below this one"
msgstr "إضافة إجابة تحتها "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Add comment on this answer"
msgstr "إضافة تعليق على هذه الإجابة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Add existing contacts..."
msgstr "إضافة جهات اتصال موجودة..."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid ""
"Add quizzes at the end of your lessons to evaluate what your students "
"understood."
msgstr "قم بإضافة اختبارات قصيرة في نهاية دروسك لتقيّم مدى فهم طلابك. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_ids
msgid "Additional Resource for this slide"
msgstr "مورد إضافي لهذه الشريحة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Additional Resources"
msgstr "موارد إضافية "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_resource
msgid "Additional resource for a particular slide"
msgstr "مورد إضافي لشريحة معيّنة "

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_advanced
msgid "Advanced"
msgstr "متقدم"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "All Courses"
msgstr "كافة الدورات"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "All completed classes and earned karma will be lost."
msgstr "سوف تفقد كافة المواد التي أنهيتها ونقاط كارما المجمعة. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__partner_ids
msgid "All members of the channel."
msgstr "كافة أعضاء القناة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "All questions must be answered !"
msgstr "يجب الإجابة على كافة الأسئلة! "

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_5_furn2
msgid "All you need to know about furniture creation."
msgstr "كل ما تحتاج لمعرفته عن صناعة الأثاث. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow Download"
msgstr "السماح بالتحميل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_preview
msgid "Allow Preview"
msgstr "السماح بالمعاينة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Allow Rating"
msgstr "السماح بالتقييم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__allow_comment
msgid "Allow rating on Course"
msgstr "السماح بالتقييم في الدورة"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Allow students to review your course"
msgstr "السماح للطلاب بمراجعة دورتك "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow the user to download the content of the slide."
msgstr "السماح للمستخدم بتحميل محتوى الشريحة. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_allow_comment
msgid "Allows comment"
msgstr "السماح بالتعليق"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Already Requested"
msgstr "تم الطلب بالفعل "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Already installing \"%s\"."
msgstr "يتم تثبيت \"%s\" بالفعل. "

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Already member"
msgstr "عضو بالفعل "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Amazing!"
msgstr "رائع! "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_2
msgid "And also bananas"
msgstr "والموز أيضاً "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__text_value
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answer_ids
msgid "Answer"
msgstr "إجابة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "يظهر في"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_visibility
msgid ""
"Applied directly as ACLs. Allow to hide channels and their content for non "
"members."
msgstr ""
"تم التطبيق مباشرة كقوائم التحكم في الوصول (ACLs). السماح بإخفاء القنوات "
"ومحتواها لغير الأعضاء. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "Archive"
msgstr "الأرشيف "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "Archive Slide"
msgstr "أرشفة الشريحة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Archived"
msgstr "مؤرشف"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Are you sure you want to archive this slide ?"
msgstr "هل أنت متأكد من أنك ترغب في أرشفة هذا المحتوى؟"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Are you sure you want to delete this category ?"
msgstr "هل أنت متأكد من أنك ترغب في حذف هذه الفئة؟ "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Are you sure you want to delete this question :"
msgstr "هل أنت متأكد من أنك ترغب في حذف هذا السؤال: "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Attachment"
msgstr "مرفق"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_attachment_count
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__attachment_ids
msgid "Attachments"
msgstr "مرفقات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_avg
msgid "Attempts Avg"
msgstr "متوسط المحاولات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_count
msgid "Attempts Count"
msgstr "عدد المحاولات "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_attendees
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Attendees"
msgstr "الحاضرين "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_done_count
msgid "Attendees Done Count"
msgstr "عدد الحاضرين الذين انتهوا "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_count
msgid "Attendees count"
msgstr "عدد الحاضرين "

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Attendees of %s"
msgstr "حاضري %s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_group_ids
msgid "Auto Enroll Groups"
msgstr "مجموعات التسجيل التلقائي "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Badges"
msgstr "الشارات"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_basic
msgid "Basic"
msgstr "أساسي"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_5_furn2
msgid "Basics of Furniture Creation"
msgstr "مبادئ صناعة الأثاث "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_0_gard_0
msgid "Basics of Gardening"
msgstr "مبادئ العناية بالحدائق "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "Be notified when a new content is added."
msgstr "كن على علم عندما تتم إضافة محتوى جديد. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_comment
msgid "Can Comment"
msgstr "يمكنه التعليق"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__can_edit_body
msgid "Can Edit Body"
msgstr "يمكن تحرير النص"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_publish
msgid "Can Publish"
msgstr "بإمكانه النشر "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_review
msgid "Can Review"
msgstr "يمكن المراجعة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_upload
msgid "Can Upload"
msgstr "يمكن الرفع "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_vote
msgid "Can Vote"
msgstr "يمكن التصويت "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
#, python-format
msgid "Cancel"
msgstr "إلغاء"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_carpenter
msgid "Carpenter"
msgstr "نجّار "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Catchy Headline"
msgstr "عنوان جذاب"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_category_ids
msgid "Categories"
msgstr "الفئات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Category"
msgstr "فئة"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_1
msgid "Certification"
msgstr "شهادة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Certifications"
msgstr "الشهادات"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_certification
#: model:gamification.challenge.line,name:website_slides.badge_data_certification_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_certification_goal
msgid "Certified Knowledge"
msgstr "معرفة مُعتَمدة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Change video privacy settings"
msgstr "تغيير إعدادات خصوصية الفيديو "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Channel"
msgstr "القناة"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_partner
msgid "Channel / Partners (Members)"
msgstr "القناة / الشركاء (الأعضاء) "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_invite
msgid "Channel Invitation Wizard"
msgstr "معالج دعوة القناة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Channel Member"
msgstr "عضو القناة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_type
msgid "Channel type"
msgstr "نوع القناة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__completed
msgid "Channel validated, even if slides / lessons are added once done."
msgstr "تم تصديق القناة، حتى وإن تمت إضافة الشرائح / الدروس عند انتهائها. "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag_group
msgid "Channel/Course Groups"
msgstr "مجموعات القناة / الدورة "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag
msgid "Channel/Course Tag"
msgstr "علامة تصنيف القناة / الدورة "

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_invite
msgid "Channel: Invite by email"
msgstr "القناة: الدعوة عبر البريد الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__channel_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channels"
msgstr "القنوات"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_cheatsheet
msgid "CheatSheet"
msgstr "ورقة المعلومات المرجعية "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check Profile"
msgstr "تحقق من الملف التعريفي "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check answers"
msgstr "تحقق من الإجابات "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check your answers"
msgstr "تحقق من إجاباتك"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Choose a <b>File</b> on your computer."
msgstr "اختر <b>ملفاً</b> في حاسوبك. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Choose a PDF or an Image"
msgstr "اختر ملف PDF أو صورة"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Choose a layout"
msgstr "اختر مخططاً "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_3_furn0
msgid "Choose your wood !"
msgstr "اختر نوع الخشب الذي تفضله! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Clear filters"
msgstr "إزالة عوامل التصفية "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Click here to send a verification email allowing you to participate at the "
"eLearning."
msgstr ""
"انقر هنا لإرسال بريد تحقق إلكتروني والذي يتيح لك المشاركة في التعلّم "
"الإلكتروني. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to start the course"
msgstr "انقر هنا لتبدأ الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Click on \"New\" in the top-right corner to write your first course."
msgstr "انقر على \"جديد\" في الزاوية العلوية إلى اليسار لكتابة دورتك الأولى. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Click on the <b>Create</b> button to create your first course."
msgstr "انقر على زر <b>إنشاء</b> لإنشاء دورتك الأولى. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Click on your <b>Course</b> to go back to the table of content."
msgstr "انقر على <b>دورتك</b> للعودة إلى جدول المحتويات. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
#, python-format
msgid "Close"
msgstr "إغلاق"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Color"
msgstr "اللون"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__color
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__color
msgid "Color Index"
msgstr "مؤشر اللون "

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_colorful
msgid "Colorful"
msgstr "ملون "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__comment
msgid "Comment"
msgstr "تعليق"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Comments"
msgstr "التعليقات"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering                               questions. In this course, you'll "
"study those topics with activities about mathematics, science and logic."
msgstr ""
"من المهام المعتادة لعالِم الحاسوب هي سؤال الأسئلة المناسبة والإجابة على "
"الأسئلة. سوف تتعلم في هذه الدورة عن تلك المواضيع  عن طريق الأنشطة المتعلقة "
"بالرياضيات والعلوم والمنطق. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions. In this course, you'll study those topics with "
"activities about mathematics, science and logic."
msgstr ""
"من المهام المعتادة لعالِم الحاسوب هي سؤال الأسئلة المناسبة والإجابة على "
"الأسئلة. سوف تتعلم في هذه الدورة عن تلك المواضيع عن طريق الأنشطة المتعلقة "
"بالرياضيات والعلوم والمنطق. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Communication"
msgstr "التواصل "

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_karma
#: model:gamification.challenge.line,name:website_slides.badge_data_karma_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_karma_goal
msgid "Community hero"
msgstr "بطل المجتمع "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_company_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_company_count
msgid "Company Course Count"
msgstr "عدد دورات الشركة "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_0
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_0
msgid "Comparing Hardness of Wood Species"
msgstr "مقارنة صلابة أنواع الخشب "

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_course_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_course
msgid "Complete a course"
msgstr "إكمال دورة "

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_profile_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_profile
msgid "Complete your profile"
msgstr "أكمل ملفك التعريفي "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Completed"
msgstr "مكتملة "

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: model:mail.template,name:website_slides.mail_template_channel_completed
#, python-format
msgid "Completed Course"
msgstr "الدورة المكتملة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_completed_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_completed_ids
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Completed Courses"
msgstr "الدورات المكتملة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completion
msgid "Completion"
msgstr "الإكمال "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed_template_id
msgid "Completion Email"
msgstr "بريد إلكتروني بالإكمال "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Completion Time"
msgstr "وقت الإكمال "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Compose Email"
msgstr "إنشاء رسالة بريد إلكتروني جديدة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Computer Science for kids"
msgstr "علوم الحاسوب للأطفال "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_enroll
msgid "Condition to enroll: everyone, on invite, on payment (sale bridge)."
msgstr "شروط التسجيل: الجميع، عند الدعوة، عند الدفع (sale bridge). "

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_configuration
msgid "Configuration"
msgstr "التهيئة "

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_completed
msgid "Congratulation! You completed {{ object.channel_id.name }}"
msgstr "تهانينا! لقد أكملت {{ object.channel_id.name }}"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations! Your first lesson is available. Let's see the options "
"available here. The tag \"<b>New</b>\" indicates that this lesson was "
"created less than 7 days ago."
msgstr ""
"تهانينا! بات أول درس لك متاحاً الآن. فلنلقِ نظرة على الخيارات المتاحة هنا.  "
"تشير العلامة \"<b>جديد</b>\" إلى أنه قد تم إنشاء هذا الدرس قبل أقل من 7 أيام"
" مضت. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Congratulations, you have reached the last rank!"
msgstr "تهانينا، لقد وصلت إلى المرتبة الأخيرة! "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations, you've created your first course.<br/>Click on the title of"
" this content to see it in fullscreen mode."
msgstr ""
"تهانينا، لقد قمت بإنشاء دورتك الأولى.<br/>اضغط على عنوان هذا المحتوى لتتمكن "
"من رؤيته في وضع ملء الشاشة. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations, your course has been created, but there isn't any content "
"yet. First, let's add a <b>Section</b> to give your course a structure."
msgstr ""
"تهانينا، لقد تم إنشاء دورتك، ولكن لا يوجد أي محتوى بعد. أولاً، فلنقم بإضافة "
"<b>جزء</b> لإضفاء هيكل لدورتك. "

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_partner
msgid "Contact"
msgstr "جهة اتصال "

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_6_furn3
#, python-format
msgid "Contact Responsible"
msgstr "التواصل مع الشخص المسؤول "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Contact all the members of a course via mass mailing"
msgstr "تواصل مع كافة أعضاء الدورة عبر البريد الجماعي "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Contact the responsible to enroll."
msgstr "تواصل مع الشخص المسؤول عن التسجيل. "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Contact us"
msgstr "تواصل معنا"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__datas
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Content"
msgstr "المحتوى"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Content Preview"
msgstr "معاينة المحتوى"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_question
msgid "Content Quiz Question"
msgstr "سؤال الاختبار القصير الخاص بالمحتوى "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_tag
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_content_tags
msgid "Content Tags"
msgstr "علامات تصنيف المحتوى "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Title"
msgstr "عنوان المحتوى "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_slide_action
#: model:ir.actions.act_window,name:website_slides.slide_slide_action_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_content
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_contents
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Contents"
msgstr "المحتويات"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Continue"
msgstr "متابعة"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Copy Link"
msgstr "انسخ الرابط"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_1
msgid "Correct !"
msgstr "صحيح! "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_0
msgid "Correct ! A shovel is the perfect tool to dig a hole."
msgstr "صحيح! المجرفة هي الأداة المناسبة لحفر حفرة. "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_0
msgid "Correct ! A strawberry is a fruit because it's the product of a tree."
msgstr "صحيح! الفراولة هي فاكهة لأنها تأتي من شجرة. "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_0
msgid "Correct ! Congratulations you have time to loose"
msgstr "هذا صحيح! تهانينا، لديك وقت لإمضائه "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Correct ! You did it !"
msgstr "صحيح! لقد فعلتها! "

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Could not fetch data from url. Document or access right not available:\n"
"%s"
msgstr ""
"تعذّر الحصول على البيانات من الرابط. قد يكون المستند أو صلاحية الوصول غير متاحين:\n"
"%s"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Course"
msgstr "الدورة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_count
msgid "Course Count"
msgstr "عدد الدورات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Group Name"
msgstr "اسم مجموعة الدورة "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_group_action
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_course_groups
msgid "Course Groups"
msgstr "مجموعات الدورات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Course Name"
msgstr "اسم الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_form
msgid "Course Tag"
msgstr "علامة تصنيف الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Tag Group"
msgstr "مجموعة علامة تصنيف الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Course Tag Groups"
msgstr "مجموعات علامات تصنيف الدورات "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_tree
msgid "Course Tags"
msgstr "علامات تصنيف الدورات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Course Title"
msgstr "عنوان الدورة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_finish
msgid "Course finished"
msgstr "انتهت الدورة "

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Course not published yet"
msgstr "لم يتم نشر الدورة بعد "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_rank
msgid "Course ranked"
msgstr "تم تصنيف الدورة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_type
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_type
msgid "Course type"
msgstr "نوع الدورة "

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Course: %s"
msgstr "الدورة: %s"

#. module: website_slides
#: code:addons/website_slides/models/website.py:0
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_courses
#: model:website.menu,name:website_slides.website_menu_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#, python-format
msgid "Courses"
msgstr "الدورات"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Create"
msgstr "إنشاء"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Create a community and let the members help each others"
msgstr "أنشئ مجتمعاً ودع الأعضاء يساعدون بعضهم البعض "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid "Create a course"
msgstr "إنشاء دورة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Create new %s '%s'"
msgstr "إنشاء %s جديد(ة) '%s'"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Create new Tag '%s'"
msgstr "أنشئ علامة تصنيف جديدة '%s'"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "Create new content for your eLearning"
msgstr "أنشئ محتوى جديد للتعلّم الإلكتروني لديك "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
msgid "Creation Date"
msgstr "تاريخ الإنشاء"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__html_content
msgid "Custom HTML content for slides of type 'Web Page'."
msgstr "محتوى HTML مخصص للشرائح من نوع 'صفحة ويب'. "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_6_furn3
msgid "DIY Furniture"
msgstr "أثاث يمكنك صنعه بنفسك "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "التاريخ (من الأحدث إلى الأقدم) "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "التاريخ (من الأقدم إلى الأحدث) "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__null_value
msgid "Default Value"
msgstr "القيمة الافتراضية"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "تحديد إمكانية ظهور التحدي عبر القوائم"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#, python-format
msgid "Delete"
msgstr "حذف"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
#, python-format
msgid "Delete Category"
msgstr "حذف الفئة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "Delete Question"
msgstr "حذف السؤال "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__promote_strategy
msgid ""
"Depending the promote strategy, a slide will appear on the top of the course's page :\n"
" * Latest Published : the slide created last.\n"
" * Most Voted : the slide which has to most votes.\n"
" * Most Viewed ; the slide which has been viewed the most.\n"
" * Specific : You choose the slide to appear.\n"
" * None : No slides will be shown.\n"
msgstr ""
"سوف تظهر شريحة في أعلى صفحة الدورة، بناء على استراتيجية الترويج:\n"
" * الأحدث نشراً: آخر شريحة تم إنشاؤها.\n"
" * الأكثر تصويتاً: الشريحة الحاصلة على أكبر عدد من الأصوات.\n"
" * الأكثر عرضاً: أكثر شريحة تم عرضها.\n"
" * محدد: اختر الشريحة التي ترغب في ظهورها.\n"
" * لا شيء: لن يتم عرض أي شريحة.\n"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__description
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Description"
msgstr "الوصف"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_html
msgid "Detailed Description"
msgstr "الوصف المفصّل "

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_1
msgid "Did you read the whole article ?"
msgstr "هل قرأت المقال بأكمله؟ "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Discard"
msgstr "إهمال "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Discover more"
msgstr "اكتشف المزيد "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__dislikes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Dislikes"
msgstr "عدم الإعجاب"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Display"
msgstr "عرض"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_question__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__sequence
msgid "Display order"
msgstr "ترتيب العرض"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_0
msgid "Do you make beams out of lemon trees ?"
msgstr "هل يمكنك صنع عوارض خشبية من أشجار الليمون؟ "

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_1
msgid "Do you make lemons out of beams ?"
msgstr "هل يمكنك صنع الليمون من العوارض الخشبية؟ "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "Do you really want to leave the course?"
msgstr "هل تريد حقًا مغادرة الدورة؟"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_0
msgid "Do you think Harry Potted has a good name ?"
msgstr "هل لدى Harry Potted اسم جيد برأيك؟ "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Do you want to install the \"%s\" app?"
msgstr "هل تريد تثبيت تطبيق \"%s\"؟"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_5_3_question_0
msgid "Do you want to reply correctly ?"
msgstr "هل ترغب في الرد بشكل صحيح؟ "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Do you want to request access to this course ?"
msgstr "هل ترغب في طلب إذن للوصول لهذه الدورة؟ "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__document
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Document"
msgstr "المستند"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_id
msgid "Document ID"
msgstr "معرف المستند"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__url
msgid "Document URL"
msgstr "رابط المستند"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__documentation
msgid "Documentation"
msgstr "الوثائق "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Documentation Layout"
msgstr "مخطط الوثائق "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_document
#: model:slide.slide,name:website_slides.slide_category_demo_4_0
msgid "Documents"
msgstr "المستندات"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_2
msgid "Dog Friendly"
msgstr "صديق للكلاب "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Don't have an account ?"
msgstr "ليس لديك حساب؟"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Done"
msgstr "انتهيت "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Done !"
msgstr "انتهيت! "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__done_count
msgid "Done Count"
msgstr "عدد الأسئلة المنجزة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download"
msgstr "تحميل"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download Content"
msgstr "تحميل المحتوى "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_10
msgid "Drawing 1"
msgstr "الرسمة 1 "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_11
msgid "Drawing 2"
msgstr "الرسمة 2 "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Dropdown menu"
msgstr "القائمة المنسدلة"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_time
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__completion_time
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#, python-format
msgid "Duration"
msgstr "المدة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Edit"
msgstr "تحرير"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Edit in backend"
msgstr "تحرير في الواجهة الخلفية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_email
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__publish_template_id
msgid "Email attendees once a new content is published"
msgstr "أرسل رسائل بريد إلكتروني للحاضرين فور نشر محتوى جديد "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__completed_template_id
msgid "Email attendees once they've finished the course"
msgstr "أرسل رسائل بريد إلكتروني للحاضرين فور إتمامهم للدورة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_template_id
msgid "Email template used when sharing a slide"
msgstr "قالب البريد الإلكتروني المستخدَم عند مشاركة شريحة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code
msgid "Embed Code"
msgstr "تضمين كود"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embedcount_ids
msgid "Embed Count"
msgstr "عدد التضمينات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Embed in your website"
msgstr "التضمين في موقعك الإلكتروني "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_embed
msgid "Embedded Slides View Counter"
msgstr "عداد مشاهدات الشرائح المضمنة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "End course"
msgstr "إنهاء الدورة "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_2
msgid "Energy Efficiency Facts"
msgstr "حقائق عن كفاءة الطاقة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Enjoy this exclusive content !"
msgstr "استمتع بهذا المحتوى الحصري! "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_msg
msgid "Enroll Message"
msgstr "رسالة التسجيل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_enroll
msgid "Enroll Policy"
msgstr "سياسة التسجيل "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Enrolled On"
msgstr "سجّل في "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Enter at least two possible <b>Answers</b>."
msgstr "أدخِل <b>إجابتين</b>محتملتين على الأقل. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Enter your <b>Question</b>. Be clear and concise."
msgstr "أدخِل <b>سؤالك</b> بكل وضوح واختصار. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Enter your answer"
msgstr "أدخِل إجابتك "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Enter your question"
msgstr "أدخِل سؤالك "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Estimated slide completion time"
msgstr "الوقت المقدر لإكمال المحتوى"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Evaluate and certify your students."
msgstr "قم بتقييم واعتماد طلابك. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Evaluate your students and certify them"
msgstr "قم بتقييم واعتماد طلابك "

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_exercises
msgid "Exercises"
msgstr "التمارين "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "External Links"
msgstr "الروابط الخارجية "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_link
msgid "External URL for a particular slide"
msgstr "رابط URL خارجي لشريحة معينة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__link_ids
msgid "External URL for this slide"
msgstr "رابط URL خارجي لهذه الشريحة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "External sources"
msgstr "مصادر خارجية "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Failed to install \"%s\"."
msgstr "تعذر تثبيت \"%s\"."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__model_object_field
msgid "Field"
msgstr "حقل"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/controllers/main.py:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "File is too big. File size cannot exceed 25MB"
msgstr "الملف كبير للغاية. يجب ألا يتجاوز حجم الملف 25 ميجابايت"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Filter &amp; order"
msgstr "التصفية والترتيب "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr "تعبير العنصر النائب النهائي، ليتم نسخه ولصقه في حقل القالب المرغوب."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Finish Course"
msgstr "إنهاء الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "First attempt"
msgstr "المحاولة الأولى "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"First, create your lesson, then edit it with the website builder. You'll be "
"able to drop building blocks on your page and edit them."
msgstr ""
"أنشئ درسك أولاً ثم قم بتحريره باستخدام أداة بناء المواقع الإلكترونية. سوف "
"يكون بمقدورك سحب وإفلات الكتل البرمجية الإنشائية في صفحتك وتحريرها. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "First, let's add a <b>Presentation</b>. It can be a .pdf or an image."
msgstr ""
"فلنقم بإضافة <b>عرض تقديمي</b> أولاً. يمكن أن يكون بصيغة .pdf أو صورة. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "First, upload your videos on YouTube and mark them as"
msgstr "قم برفع مقاطع الفيديو الخاصة بك إلى اليوتيوب أولاً، ثم حددها كـ"

#. module: website_slides
#: code:addons/website_slides/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
#, python-format
msgid "Followed Courses"
msgstr "الدورات المتابعة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_follower_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_follower_ids
msgid "Followers"
msgstr "المتابعين "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_partner_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة Font awesome مثال fa-tasks "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_0
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Foreword"
msgstr "المقدمة "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_0
msgid "Foreword for this documentation: how to use it, main attention points"
msgstr "مقدمة هذه الوثيقة: كيفية الاستخدام والنقاط الأساسية الأكثر أهمية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_forum
msgid "Forum"
msgstr "المنتدى"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Fourth and more attempt"
msgstr "المحاولة الرابعة فما فوق "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_2
msgid "From a piece of wood to a fully functional furniture, step by step."
msgstr "من مجرد قطعة خشب إلى أثاث عملي بالكامل، خطوة بخطوة. "

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_furniture
msgid "Furniture Designer"
msgstr "مصمم أثاث "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_4_furn1
msgid "Furniture Technical Specifications"
msgstr "المواصفات التقنية للأثاث "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_12
msgid "GLork"
msgstr "GLork"

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "تحدي التلعيب "

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_gardener
msgid "Gardener"
msgstr "بستاني "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_0
msgid "Gardening: The Know-How"
msgstr "العناية بالحدائق: الدليل المعرفي "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Generate revenues thanks to your courses"
msgstr "احصل على الإيرادات بفضل دوراتك "

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_certification_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_certification
msgid "Get a certification"
msgstr "احصل على شهادة "

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_register
#: model:gamification.challenge.line,name:website_slides.badge_data_register_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_register_goal
msgid "Get started"
msgstr "ابدأ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Give your course a helpful <b>Description</b>."
msgstr "اعطِ دورتك <b>وصفاً</b> مفيداً. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Give your course an engaging <b>Title</b>."
msgstr "امنح دورتك <b>عنواناً</b> مثيراً للاهتمام. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__website_slide_google_app_key
#: model:ir.model.fields,field_description:website_slides.field_website__website_slide_google_app_key
msgid "Google Doc Key"
msgstr "مفتاح مستندات Google "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Google Drive API Key"
msgstr "مفتاح API لـ Google Drive  "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/components/activity/activity.xml:0
#, python-format
msgid "Grant Access"
msgstr "منح إذن الوصول "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_graph
msgid "Graph of Contents"
msgstr "المخطط البياني للمحتويات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_id
msgid "Group"
msgstr "المجموعة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Group By"
msgstr "التجميع حسب "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__name
msgid "Group Name"
msgstr "اسم المجموعة"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__upload_group_ids
msgid "Group of users allowed to publish contents on a documentation course."
msgstr "مجموعة المستخدمين المسموح لهم بنشر المحتوى في دورة توثيقية. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_sequence
msgid "Group sequence"
msgstr "تسلسل جماعي "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__html_content
msgid "HTML Content"
msgstr "محتوى HTML "

#. module: website_slides
#: model:ir.model,name:website_slides.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_1
msgid "Hand on !"
msgstr "بنفسك! "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_message
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__has_message
msgid "Has Message"
msgstr "توجد رسالة "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_6
msgid "Here is How to get the Sweetest Strawberries you ever tasted!"
msgstr "إليك طريقة الحصوع على ألذ فراولة تتذوقها في حياتك! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Home"
msgstr "الصفحة الرئيسية "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_1
msgid "Home Gardening"
msgstr "العناية بالحديقة المنزلية "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_2
msgid "How To Build a HIGH QUALITY Dining Table with LIMITED TOOLS"
msgstr "كيفية بناء طاولة عشاء ذات جودة عالية باستخدام أدوات محدودة "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_3
msgid "How to Grow and Harvest The Best Strawberries | Basics"
msgstr "كيفية زراعة وحصد أفضل فراولة على الإطلاق | المبادئ "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_6
msgid ""
"How to Grow and Harvest The Best Strawberries | Gardening Tips and Tricks"
msgstr ""
"كيفية زراحة وحصد أفضل فراولة على الإطلاق | نصائح مفيدة للعناية بالحدائق "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to create a Lesson as a Web Page?"
msgstr "كيف تنشئ درساً كصفحة ويب؟ "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_1
msgid "How to find quality wood"
msgstr "كيفية إيجاد خشب ذو جودة عالية "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_3
msgid "How to plant a potted tree"
msgstr "كيفية زراعة شجرة في أصيص "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to upload your PowerPoint Presentations or Word Documents?"
msgstr "كيف تقوم برفع عرضك التقديمي على PowerPoint أو مستند Word؟ "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to upload your videos ?"
msgstr "كيف ترفع مقاطع الفيديو؟ "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_2
msgid "How to wall decorating by tree planting in hanging plastic bottles."
msgstr ""
"كيفية تزيين الجدران عن طريق زراعة النباتات في القوارير البلاستيكية المعلقة. "

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_howto
msgid "HowTo"
msgstr "كيفية عمل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__id
#: model:ir.model.fields,field_description:website_slides.field_slide_question__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__id
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__id
msgid "ID"
msgstr "المُعرف"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى استثناء النشاط"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__allow_comment
#: model:ir.model.fields,help:website_slides.field_slide_slide__channel_allow_comment
msgid ""
"If checked it allows members to either:\n"
" * like content and post comments on documentation course;\n"
" * post comment and review on training course;"
msgstr ""
"إذا تم تحديده، سوف يكون بوسع الأعضاء تنفيذ أحد الخيارين:\n"
" * الإعجاب بالمحتوى ونشر التعليقات في المحتوى التوثيقي؛\n"
" * كتابة التعليقات والتقييمات في الدورة التدريبية؛"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_4_furn1
msgid ""
"If you are looking for technical specifications, have a look at this "
"documentation."
msgstr "إذا كنت تبحث عن مواصفات تقنية، ألقِ نظرة على هذه الوثيقة. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"If you want to be sure that attendees have understood and memorized the "
"content, you can add a Quiz on the lesson. Click on <b>Add Quiz</b>."
msgstr ""
"إذا كنت تريد التأكد من أن الحاضرين قد تمكنوا من فهم وحفظ المحتوى، يمكنك "
"إضافة اختبار قصير للدرس. اضغط على <b>إضافة اختبار قصير</b>. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1920
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1920
msgid "Image"
msgstr "صورة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1024
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1024
msgid "Image 1024"
msgstr "صورة 1024"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_128
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_128
msgid "Image 128"
msgstr "صورة 128"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_256
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_256
msgid "Image 256"
msgstr "صورة 256"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_512
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_512
msgid "Image 512"
msgstr "صورة 512"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_0
msgid "Incorrect !"
msgstr "غير صحيح! "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_1
msgid "Incorrect ! A strawberry is not a vegetable."
msgstr "غير صحيح! الفراولة ليست من الخضار. "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_2
msgid "Incorrect ! A table is a piece of furniture."
msgstr "غير صحيح! الطاولة هي قطعة أثاث. "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_1
msgid "Incorrect ! Good luck digging a hole with a spoon..."
msgstr "غير صحيح! حظاً موفقاً في حفر حفرة بملعقة... "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_2
msgid "Incorrect ! Seriously ?"
msgstr "غير صحيح! بجدية؟ "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_1
msgid "Incorrect ! You better think twice..."
msgstr "غير صحيح! فكّر ملياً... "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_1
msgid "Incorrect ! You really should read it."
msgstr "غير صحيح! ربما عليك قراءة السؤال مجدداً. "

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_2
msgid "Incorrect ! of course not ..."
msgstr "غير صحيح! بالطبع لا... "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__infographic
msgid "Infographic"
msgstr "مخطط معلومات بياني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_infographic
msgid "Infographics"
msgstr "مخططات المعلومات البيانية "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Install"
msgstr "تثبيت"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Installing \"%s\"."
msgstr "جاري التثبيت \"%s\"."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_0
msgid "Interesting Facts"
msgstr "حقائق مثيرة للاهتمام "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_1
msgid "Interesting Tree Facts"
msgstr "حقائق مثيرة للاهتمام عن الأشجار "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_1
msgid "Interesting information about home gardening. Keep it close !"
msgstr ""
"معلومات مثيرة للاهتمام عن العناية بالحدائق المنزلية. أبقها قريبة منك! "

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_intermediate
msgid "Intermediate"
msgstr "متوسط "

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Internal server error, please try again later or contact administrator.\n"
"Here is the error message: %s"
msgstr ""
"خطأ داخلي في الخادم، الرجاء المحاولة مرة أخرى لاحقًا أو التواصل مع المدير.\n"
"رسالة الخطأ: %s"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Invalid file type. Please select pdf or image file"
msgstr "نوع الملف غير صالح. الرجاء اختيار ملف بصيغة pdf أو صورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Invite"
msgstr "دعوة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed
msgid "Is Completed"
msgstr "مكتملة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "محرر "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_is_follower
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member
msgid "Is Member"
msgstr "عضو "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_new_slide
msgid "Is New Slide"
msgstr "شريحة جديدة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_published
msgid "Is Published"
msgstr "تم نشره "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_category
msgid "Is a category"
msgstr "فئة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__is_correct
msgid "Is correct answer"
msgstr "الإجابة الصحيحة "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_3
msgid ""
"Jim and Todd plant a potted tree for a customer of Knecht's Nurseries and "
"Landscaping. Narrated by Leif Knecht, owner."
msgstr ""
"يزرع Jim وTodd شجرة في أصيص لعميل في مشتل وخدمة تنسيق الحدائق Knecht's. بقلم"
" Leif Knecht، المالك. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "Join & Submit"
msgstr "الانضمام والإرسال "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
#, python-format
msgid "Join Course"
msgstr "الانضمام للدورة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Join the Course"
msgstr "الانضمام للدورة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Join the course to take the quiz and verify your answers!"
msgstr ""
"قم بالانضمام للدورة حتى تتمكن من القيام بالاختبار القصير وتأكيد إجاباتك! "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_2
msgid "Just some basics Energy Efficiency Facts."
msgstr "بعض الحقائق الأساسية عن كفاءة الطاقة. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_1
msgid "Just some basics Interesting Tree Facts."
msgstr "بعض الحقائق الأساسية المثيرة للاهتمام عن الأشجار. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_0
msgid "Just some basics Tree Infographic."
msgstr "بعض مخططات المعلومات البيانية الأساسية عن الأشجار. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Karma"
msgstr "نقاط الكارما"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_comment
msgid "Karma needed to add a comment on a slide of this course"
msgstr ""
"نقاط الكارما المطلوبة حتى تتمكن من كتابة تعليق على إحدى شرائح هذه الدورة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_review
msgid "Karma needed to add a review on the course"
msgstr "نقاط الكارما المطلوبة حتى تتمكن من كتابة تقييم لهذه الدورة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_vote
msgid "Karma needed to like/dislike a slide of this course."
msgstr ""
"نقاط الكارما المطلوبة حتى تتمكن من إبداء الإعجاب / عدم الإعجاب لشريحة في هذه"
" الدورة. "

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_profile
#: model:gamification.challenge.line,name:website_slides.badge_data_profile_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_profile_goal
msgid "Know yourself"
msgstr "اعرف نفسك "

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_3_furn0
msgid ""
"Knowing which kind of wood to use depending on your application is important. In this course you\n"
"will learn the basics of wood characteristics."
msgstr ""
"من المهم معرفة أي أنواع الخشب عليك استخدامه بناء على طرق تطبيقك. سوف تتعلم في هذه الدورة\n"
"مبادئ خصائص الخشب. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_3
msgid ""
"Knowing wood characteristics is a requirement in order to know which kind of"
" wood to use in a given situation."
msgstr ""
"من الضروري معرفة خصائص الخشب حتى تتمكن من معرفة أي الأنواع عليك استخدامه في "
"المواقف المختلفة. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__lang
msgid "Language"
msgstr "اللغة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Action On"
msgstr "آخر إجراء في "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_embed____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_question____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_tag____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_last_update
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Last Update"
msgstr "آخر تحديث"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__latest
msgid "Latest Published"
msgstr "أحدث المنشورات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_latest_achievements
msgid "Latest achievements"
msgstr "أحدث الإنجازات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "Leaderboard"
msgstr "لوحة الصدارة "

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_1_gard1
msgid ""
"Learn how to take care of your favorite trees. Learn when to plant, how to "
"manage potted trees, ..."
msgstr ""
"تعلم كيفية الاعتناء بأشجارك المفضلة. اعرف متى عليك أن تزرع وكيفية التعامل مع"
" الأشجار المزروعة في أصيص، ..."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_0_gard_0
msgid "Learn the basics of gardening !"
msgstr "تعلم مبادئ العناية بالحدائق! "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_1
msgid "Learn to identify quality wood in order to create solid furnitures."
msgstr "تعلم كيفية التعرف على الخشب عالي الجودة حتى تتمكن من بناء أثاث متين. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Leave the course"
msgstr "مغادرة الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Lesson"
msgstr "الدروس "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Lesson Nav"
msgstr "التنقل بين الدروس "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_slide_vote
msgid "Lesson voted"
msgstr "الدرس الذي تم التصويت له "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Lessons"
msgstr "الدروس "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz_finish.js:0
#, python-format
msgid "Level up!"
msgstr "الارتقاء للمستوى التالي! "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__likes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Likes"
msgstr "الإعجابات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__link
msgid "Link"
msgstr "الرابط"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__template_id
msgid "Mail Template"
msgstr "قالب الرسالة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_mass_mailing_slides
msgid "Mailing"
msgstr "المراسلات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_0
msgid "Main Trees Categories"
msgstr "فئات الأشجار الأساسية "

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_manager
msgid "Manager"
msgstr "المدير"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Mark the correct answer by checking the <b>correct</b> mark."
msgstr "قم بتحديد الإجابة الصحيحة عن طريق تحديد العلامة <b>الصحيحة</b>.  "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Member"
msgstr "العضو"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_ids
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Members"
msgstr "الأعضاء"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_ids
msgid "Members Information"
msgstr "معلومات الأعضاء "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__members
msgid "Members Only"
msgstr "للأعضاء فقط "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Members Views"
msgstr "مشاهدات الأعضاء "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_group_ids
msgid ""
"Members of those groups are automatically added as members of the channel."
msgstr "يتم إضافة أعضاء هذه المجموعات تلقائياً كأعضاء للقناة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Menu Entry"
msgstr "قيد القائمة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_msg
msgid "Message explaining the enroll process"
msgstr "رسالة توضح عملية التسجيل "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_1
msgid "Methods"
msgstr "الطرق "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_2
msgid "Mighty Carrots"
msgstr "جزر رائع "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_1
msgid ""
"Mighty forest just don't appear in a few weeks. Learn how time made our "
"forests mighty and mysterious."
msgstr ""
"لا تظهر الغابات الرائعة خلال أسابيع قليلة. تعلّم كيف تمكنا من جعل غاباتنا "
"رائعة وساحرة مع الوقت. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__mime_type
msgid "Mime-type"
msgstr "نوع المنجم "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Minutes"
msgstr "الدقائق"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Missing \"Tag Group\" for creating a new \"Tag\"."
msgstr "لم يتم العثور على \"مجموعة علامة التصنيف\" لإنشاء \"علامة تصنيف\" جديدة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Mobile sub-nav"
msgstr "التنقل الفرعي في الهاتف المحمول "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "More info"
msgstr "المزيد من المعلومات"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_viewed
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Most Viewed"
msgstr "الأكثر عرضًا"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_voted
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Most Voted"
msgstr "الأكثر تصويتًا"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Most popular courses"
msgstr "الدورات الأكثر شعبية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "نهاية الوقت المعين للنشاط"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "My Courses"
msgstr "دوراتي "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "My courses"
msgstr "دوراتي "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__name
msgid "Name"
msgstr "الاسم"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Nav"
msgstr "التنقل "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "New"
msgstr "جديد"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "New Certification"
msgstr "شهادة جديدة"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__publish_template_id
msgid "New Content Email"
msgstr "بريد إلكتروني بمحتوى جديد "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "New Course"
msgstr "دورة جديدة "

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_published
msgid "New {{ object.slide_type }} published on {{ object.channel_id.name }}"
msgstr " {{ object.slide_type }} جديد، منشور في {{ object.channel_id.name }}"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Newest"
msgstr "الأحدث"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Newest courses"
msgstr "أحدث الدورات "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Next"
msgstr "التالي"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Next rank:"
msgstr "التصنيف التالي: "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_1
#, python-format
msgid "No"
msgstr "لا"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "No Course created yet."
msgstr "لم يتم إنشاء دورة بعد "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No completed courses yet!"
msgstr "لم تكمل أي دورة بعد! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "No content was found using your search"
msgstr "لم يتم العثور على أي محتوى باستخدام بحثك "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search"
msgstr "لم يتم العثور على أي دورة تطابق بحثك "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search."
msgstr "لم يتم العثور على أي دورة تطابق بحثك "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No followed courses yet!"
msgstr "لا تتبع أي دورات بعد! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "No leaderboard currently :("
msgstr "لا توجد لوحة صدارة في الوقت الحالي :( "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "No presentation available."
msgstr "ليس هناك عرض تقديمي متاح. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No results found for '"
msgstr "لم يتم العثور على نتائج لـ’"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__none
msgid "None"
msgstr "لا شيء"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Not enough karma to comment"
msgstr "لا تملك نقاط كارما كافية لكتابة تعليق "

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Not enough karma to review"
msgstr "لا تملك نقاط كارما كافية لكتابة تقييم "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Notifications"
msgstr "إشعارات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_document
msgid "Number of Documents"
msgstr "عدد المستندات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_infographic
msgid "Number of Infographics"
msgstr "عدد مخططات المعلومات البيانية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_presentation
msgid "Number of Presentations"
msgstr "عدد العروض التقديمية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_quiz
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_quiz
msgid "Number of Quizs"
msgstr "عدد الاختبارات القصيرة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_video
msgid "Number of Videos"
msgstr "عدد مقاطع الفيديو"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_webpage
msgid "Number of Webpages"
msgstr "عدد صفحات الويب "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__comments_count
msgid "Number of comments"
msgstr "عدد التعليقات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل التي بها خطأ في التسليم"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل غير المقروءة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__questions_count
msgid "Numbers of Questions"
msgstr "عدد الأسئلة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Odoo"
msgstr "أودو"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Odoo • Image and Text"
msgstr "أودو • صورة ونص"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_officer
msgid "Officer"
msgstr "موظف"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__invite
msgid "On Invitation"
msgstr "عن طريق الدعوة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Once you're done, don't forget to <b>Publish</b> your course."
msgstr "بمجرد انتهائك، لا تنس <b>نشر</b> دورتك. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Only JPG, PNG, PDF, files types are supported"
msgstr "صيغ الملفات المدعومة هي JPG، PNG، وPDF فقط "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"لغة الترجمة الاختيارية (كود ISO) لاختيارها عند إرسال بريد إلكتروني. إذا لم "
"يتم تعيينها، سوف تُستخدم النسخة باللغة الإنجليزية. عادة ما يكون ذلك تمثيلاً "
"للعنصر النائب المسؤول عن التزويد باللغة المناسبة، مثال: {{ "
"object.partner_id.lang }}. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__null_value
msgid "Optional value to use if the target field is empty"
msgstr "قيمة اختيارية لاستخدامها إذا كان الحقل المطلوب خالياً "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Options"
msgstr "الخيارات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__partner_id
msgid "Partner"
msgstr "الشريك"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_has_new_content
msgid "Partner Has New Content"
msgstr "لدى الشريك محتوى جديد "

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"People already took this quiz. To keep course progression it should not be "
"deleted."
msgstr ""
"خضع الأفراد لهذا الاختبار القصير بالفعل. لتحافظ على تقدم دورتك، يجب ألّا "
"تقوم بحذفه. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__copyvalue
msgid "Placeholder Expression"
msgstr "تعبير العنصر النائب"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Please"
msgstr "رجاءً "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid "Please <a href=\"/web/login?redirect=%s\">login</a> to join this course"
msgstr ""
"الرجاء <a href=\"/web/login?redirect=%s\"> تسجيل الدخول</a> للانضمام إلى هذه"
" الدورة"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Please <a href=\"/web/login?redirect=%s\">login</a> to vote this lesson"
msgstr ""
"الرجاء <a href=\"/web/login?redirect=%s\">تسجيل الدخول</a> للتصويت لهذه "
"الدورة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid ""
"Please <a href=\"/web/signup?redirect=%s\">create an account</a> to join "
"this course"
msgstr ""
"الرجاء <a href=\"/web/signup?redirect=%s\">إنشاء حساب</a> للانضمام إلى هذه "
"الدورة"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid ""
"Please <a href=\"/web/signup?redirect=%s\">create an account</a> to vote "
"this lesson"
msgstr ""
"الرجاء <a href=\"/web/signup?redirect=%s\">إنشاء حساب</a> للتصويت لهذا الدرس"
" "

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Please enter valid Youtube or Google Doc URL"
msgstr "الرجاء إدخال رابط URL صالح لليوتيوب أو  لمستندات Google "

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Please enter valid youtube or google doc url"
msgstr "الرجاء إدخال رابط URL صالح لليوتيوب أو لمستندات Google "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz_question_form.js:0
#, python-format
msgid "Please fill in the question"
msgstr "يرجى الإجابة على السؤال "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "يرجى ملء هذا الحقل"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid "Please select at least one recipient."
msgstr "الرجاء اختيار مستلم واحد على الأقل. "

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_course
#: model:gamification.challenge.line,name:website_slides.badge_data_course_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_course_goal
msgid "Power User"
msgstr "مستخدم متمرس "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Powered by"
msgstr "مشغل بواسطة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__slide_id
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__presentation
#: model:slide.slide,name:website_slides.slide_slide_demo_4_12
#, python-format
msgid "Presentation"
msgstr "عرض تقديمي "

#. module: website_slides
#: model:mail.message.subtype,description:website_slides.mt_channel_slide_published
#: model:mail.message.subtype,name:website_slides.mt_channel_slide_published
msgid "Presentation Published"
msgstr "تم نشر العرض التقديمي "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_presentation
msgid "Presentations"
msgstr "العروض التقديمية "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Preview"
msgstr "معاينة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Private Course"
msgstr "دورة خاصة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Progress"
msgstr "مدى التقدم "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promote_strategy
msgid "Promoted Content"
msgstr "المحتوى المروَّج له "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promoted_slide_id
msgid "Promoted Slide"
msgstr "الشريحة المروَّج لها "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__public
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__public
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Public"
msgstr "عام"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Public Views"
msgstr "المشاهدات العامة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "تاريخ النشر "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__date_published
msgid "Publish Date"
msgstr "تاريخ النشر"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Published"
msgstr "منشور"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Published Date"
msgstr "تاريخ النشر"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Publishing is restricted to the responsible of training courses or members "
"of the publisher group for documentation courses"
msgstr ""
"تقتصر إمكانية النشر على الشخص المسؤول عن الدورات التدريبية أو أعضاء مجموعة "
"الناشر للدورات التوثيقية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__question_id
msgid "Question"
msgstr "السؤال"

#. module: website_slides
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer"
msgstr "يجب أن يكون للسؤال \"%s\" إجابة صحيحة واحدة فقط "

#. module: website_slides
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer and at least 1 incorrect answer"
msgstr ""
"يجب أن يكون للسؤال \"%s\" إجابة صحيحة واحدة فقط وإجابة خاطئة واحدة على الأقل"
" "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__question
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Question Name"
msgstr "اسم السؤال"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__question_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Questions"
msgstr "الأسئلة"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__quiz
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Quiz"
msgstr "اختبار قصير "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Quiz Demo Data"
msgstr "بيانات الاختبار القصير التجريبية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__quiz_attempts_count
msgid "Quiz attempts count"
msgstr "عدد محاولات الاختبار القصير "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_question_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_quizzes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree_report
msgid "Quizzes"
msgstr "الاختبارات القصيرة "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_ids
msgid "Rating"
msgstr "التقييم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_graph_slide_channel
msgid "Rating Average"
msgstr "متوسط التقييم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_stars
msgid "Rating Average (Stars)"
msgstr "متوسط التقييم (النجوم) "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "آخر ملاحظات التقييم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_image
msgid "Rating Last Image"
msgstr "آخر صورة للتقييم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_value
msgid "Rating Last Value"
msgstr "آخر قيمة للتقييم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_count
msgid "Rating count"
msgstr "عدد التقييمات"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Rating of %s"
msgstr "تقييم %s"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_karma_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_karma
msgid "Reach 2000 XP"
msgstr "الوصول إلى 2000 XP"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Reach new heights"
msgstr "الوصول إلى قمم جديدة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__rating_last_feedback
msgid "Reason of the rating"
msgstr "سبب التقييم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__partner_ids
msgid "Recipients"
msgstr "المستلمين"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/components/activity/activity.xml:0
#, python-format
msgid "Refuse Access"
msgstr "رفض الوصول "

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_register_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_register
msgid "Register to the platform"
msgstr "التسجيل في المنصة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Related"
msgstr "ذو صلة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Remove"
msgstr "إزالة"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Remove the answer comment"
msgstr "إزالة تعليق الإجابة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Remove this answer"
msgstr "إزالة هذه الإجابة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__render_model
msgid "Rendering Model"
msgstr "نموذج التكوين "

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report
msgid "Reporting"
msgstr "إعداد التقارير"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Request Access."
msgstr "طلب إذن الوصول "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Request sent !"
msgstr "تم إرسال الطلب! "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Reset"
msgstr "إعادة تعيين"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__data
msgid "Resource"
msgstr "المورد"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Resources"
msgstr "الموارد"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__user_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_user_id
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Responsible"
msgstr "المسؤول"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Responsible already contacted."
msgstr "تم التواصل مع الشخص المسؤول بالفعل. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_id
msgid "Restrict publishing to this website."
msgstr "تحديد إمكانية النشر على هذا الموقع. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Retry"
msgstr "إعادة المحاولة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.private_profile
msgid "Return to the course."
msgstr "العودة إلى الدورة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Review"
msgstr "مراجعة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Review Course"
msgstr "مراجعة الدورة "

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_reviews
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_reviews
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Reviews"
msgstr "التقييمات "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/portal_chatter.js:0
#, python-format
msgid "Reviews (%d)"
msgstr "التقييمات (%d)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_fourth_attempt_reward
msgid "Reward: every attempt after the third try"
msgstr "مكافأة: كل محاولة بعد المحاولة الثالثة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_first_attempt_reward
msgid "Reward: first attempt"
msgstr "مكافأة: المحاولة الأولى "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_second_attempt_reward
msgid "Reward: second attempt"
msgstr "مكافأة: المحاولة الثانية "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_third_attempt_reward
msgid "Reward: third attempt"
msgstr "مكافأة: المحاولة الثالثة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Rewards"
msgstr "المكافآت"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_seo_optimized
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_seo_optimized
msgid "SEO optimized"
msgstr "تم تحسين محركات البحث"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Save"
msgstr "حفظ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Save & Publish"
msgstr "حفظ ونشر "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Save your presentations or documents as PDF files and upload them."
msgstr "احفظ عروضك التقديمية أو مستنداتك كملفات بصيغة PDF وقم برفعها. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search"
msgstr "بحث"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Search Contents"
msgstr " البحث عن المحتوى "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Search courses"
msgstr "البحث عن الدورات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search in content"
msgstr "البحث في المحتوى "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Second attempt"
msgstr "المحاولة الثانية "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__category_id
#, python-format
msgid "Section"
msgstr "القسم"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Section Subtitle"
msgstr "عنوان فرعي للقسم"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Section name"
msgstr "اسم القسم"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__access_token
msgid "Security Token"
msgstr "رمز الحماية"

#. module: website_slides
#: code:addons/website_slides/models/res_users.py:0
#, python-format
msgid "See our eLearning"
msgstr "ألقِ نظرة على التعلم الإلكتروني "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Select <b>Course</b> to create it and manage it."
msgstr "اختر <b>دورةً</b> لإنشائها وإدارتها. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Select page to start with"
msgstr "اختر صفحة لتبدأ بها"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"اختيار الحقل المستهدف من نموذج المستند المتعلق به.\n"
"إذا كان حقل علاقة، فسيكون بمقدورك اختيار الحقل المستهدف من امتداد العل"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Select the correct answer below :"
msgstr "اختر الإجابة الصحيحة أدناه: "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_sale_slides
msgid "Sell on eCommerce"
msgstr "البيع في موقع التجارة الإلكترونية "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Send"
msgstr "إرسال"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Send Email"
msgstr "إرسال بريد إلكتروني"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__seo_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__seo_name
msgid "Seo name"
msgstr "اسم محسنات محرك البحث "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_question__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__sequence
msgid "Sequence"
msgstr "التسلسل "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Set Done"
msgstr "التعيين كمنتهي "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.website_slides_action_settings
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_settings
msgid "Settings"
msgstr "الإعدادات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "Share"
msgstr "مشاركة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Share Channel"
msgstr "مشاركة القناة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
#, python-format
msgid "Share Link"
msgstr "مشاركة الرابط"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_template_id
msgid "Share Template"
msgstr "مشاركة القالب "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Share by mail"
msgstr "المشاركة عبر البريد الإلكتروني "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Facebook"
msgstr "المشاركة على فيسبوك"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on LinkedIn"
msgstr "المشاركة على LinkedIn"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_body
#, python-format
msgid "Share on Social Networks"
msgstr "المشاركة على مواقع التواصل الاجتماعي"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Twitter"
msgstr "المشاركة على تويتر"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_short
msgid "Short Description"
msgstr "وصف قصير "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_4
msgid "Show your newly mastered knowledge !"
msgstr "أظهِر للجميع معارفك الجديدة! "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign Up !"
msgstr "سجل الآن!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Sign in"
msgstr "تسجيل الدخول"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign in and join the course to verify your answers!"
msgstr "قم بتسجيل الدخول والانضمام إلى الدورة لتأكيد إجاباتك! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Size"
msgstr "الحجم"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Skill up and have an impact! Your business career starts here.<br/>Time to "
"start a course."
msgstr ""
"ارتق بمهاراتك وأحدِث فرقاً! رحلة الأعمال الخاصة بك تبدأ هنا.<br/>حان الوقت "
"لبدء الدورة. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__slide_id
msgid "Slide"
msgstr "الشريحة"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr "الشريحة / m2m المزين للشريك "

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_published
msgid "Slide Published"
msgstr "الشريحة المنشورة "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_answer
msgid "Slide Question's Answer"
msgstr "إجابة سؤال الشريحة "

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_shared
msgid "Slide Shared"
msgstr "الشريحة التي تمت مشاركتها "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_tag
msgid "Slide Tag"
msgstr "علامة تصنيف الشريحة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_partner_ids
msgid "Slide User Data"
msgstr "بيانات مستخدم الشريحة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_id
msgid "Slide channel"
msgstr "قناة الشريحة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#, python-format
msgid "Slide image"
msgstr "صورة الشريحة"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Slide with questions must be marked as done when submitting all good answers"
" "
msgstr ""
"يجب تحديد الشرائح التي بها أسئلة كمنتهية، عند إرسال كافة الإجابات الجيدة "

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_content_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_ids
msgid "Slides"
msgstr "الشرائح"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_ids
msgid "Slides and categories"
msgstr "الشرائح والفئات "

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_6_furn3
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_6_furn3
msgid "So much amazing certification."
msgstr "العديد من الشهادات الرائعة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Sort by"
msgstr "فرز حسب "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__specific
msgid "Specific"
msgstr "محدد "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Start Course"
msgstr "ابدأ الدورة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Start Course Channel"
msgstr "ابدأ قناة الدورة "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Start with the customer – find out what they want and give it to them."
msgstr "ابدأ بالعميل - اكتشف ماذا يريد وامنحه إياه."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Start your online course today!"
msgstr "ابدأ دورتك عبر الإنترنت اليوم! "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Statistics"
msgstr "الإحصائيات"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الحالة على أساس الأنشطة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__sub_model_object_field
msgid "Sub-field"
msgstr "حقل-فرعي"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__sub_object
msgid "Sub-model"
msgstr "النموذج الفرعي"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__subject
msgid "Subject"
msgstr "الموضوع "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Subject..."
msgstr "الموضوع..."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Subscribe"
msgstr "اشتراك"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information"
msgstr "معلومات المشترك "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information for the current logged in user"
msgstr "معلومات المشترك للمستخدم المسجل دخوله حالياً "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__partner_ids
msgid "Subscribers"
msgstr "المشتركين "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_partner_ids
msgid "Subscribers information"
msgstr "معلومات المشتركين "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Succeed and gain karma"
msgstr "انجح واكتسب نقاط كارما "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/xml/website_slides_channel_tag.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_form
#, python-format
msgid "Tag"
msgstr "علامة التصنيف "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel_tag.xml:0
#, python-format
msgid "Tag Group"
msgstr "مجموعة علامة التصنيف "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#, python-format
msgid "Tag Group (required for new tags)"
msgstr "مجموعة علامة التصنيف (ضروري لعلامات التصنيف الجديدة) "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Tag Name"
msgstr "اسم علامة التصنيف "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag__color
msgid ""
"Tag color used in both backend and website. No color means no display in "
"kanban or front-end, to distinguish internal tags from public categorization"
" tags"
msgstr ""
"تُستخدَم ألوان علامات التصنيف في الواجهة الخلفية وفي الموقع الإلكتروني. لو "
"لم يكن هناك لون، هذا يعني أنه لن يكون هناك عرض في كانبان أو الواجهة "
"الأمامية، للتمييز بين علامات التصنيف الداخلية وعلامات تصنيف الفئة العامة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__tag_ids
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_data_other
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
#, python-format
msgid "Tags"
msgstr "علامات التصنيف "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Tags..."
msgstr "علامات التصنيف... "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_1_gard1
msgid "Taking care of Trees"
msgstr "العناية بالأشجار "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_4_1
msgid "Technical Drawings"
msgstr "الرسومات التقنية "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_10
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_11
msgid "Technical drawing"
msgstr "رسم تقني "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_2
msgid "Test Yourself"
msgstr "اختبر نفسك "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_4
msgid "Test your knowledge"
msgstr "اختبر معرفتك "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_3
msgid "Test your knowledge !"
msgstr "اختبر معرفتك! "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Test your students with small Quizzes"
msgstr "اختبر طلابك عن طريق الاختبارات القصيرة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"The <b>Duration</b> of the lesson is based on the number of pages of your "
"document. You can change this number if your attendees will need more time "
"to assimilate the content."
msgstr ""
"تعتمد <b>مدة</b> الدرس على عدد صفحات مستندك. بوسعك تغيير الرقم إذا كان "
"حاضريك بحاجة إلى المزيد من الوقت لاستيعاب المحتوى. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"The <b>Title</b> of your lesson is autocompleted but you can change it if "
"you want.</br>A <b>Preview</b> of your file is available on the right side "
"of the screen."
msgstr ""
" يتم إكمال<b>عنوان</b> درسك تلقائياً ولكن بإمكانك تغييره إذا أردت.</br>تتوفر"
" <b>معاينة</b> لملفك على يسار الشاشة. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__is_preview
msgid ""
"The course is accessible by anyone : the users don't need to join the "
"channel to access the content of the course."
msgstr ""
"بإمكان أي شخص الوصول إلى هذه الدورة: لن يحتاج المشتركون للانضمام إلى القناة "
"للوصول إلى محتوى الدورة. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description_short
msgid "The description that is displayed on the course card"
msgstr "الوصف المعروض على بطاقة الدورة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description
msgid ""
"The description that is displayed on top of the course page, just below the "
"title"
msgstr "الوصف المعروض أعلى صفحة الدورة، تحت العنوان مباشرة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_type
msgid ""
"The document type will be set automatically based on the document URL and "
"properties (e.g. height and width for presentation and document)."
msgstr ""
"سوف يتم تحديد نوع المستند تلقائيًا حسب رابط وخصائص المستند (مثل طول وعرض "
"العرض التقديمي والمستند)."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__completion_time
msgid "The estimated completion time for this slide"
msgstr "وقت المقدر لإكمال هذه الشريحة "

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external sign up in configuration."
msgstr ""
"ليس للمستلمين التاليين حسابات مستخدمين: %s. عليك إنشاء حسابات مستخدمين لهم "
"أو السماح بالتسجيل الخارجي في التهيئة. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_url
msgid "The full URL to access the document through the website."
msgstr "الرابط الكامل للوصول للمستند من خلال الموقع."

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_theory
msgid "Theory"
msgstr "نظري "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "There are no quizzes"
msgstr "لا توجد اختبارات قصيرة "

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel_report
msgid "There are no ratings for these courses at the moment"
msgstr "لا توجد تقييمات لهذه الدورة في الوقت الحالي "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "There was an error validating this quiz."
msgstr "حدث خطأ أثناء التحقق من صحة هذا الاختبار القصير. "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__url
msgid "Third Party Website URL"
msgstr "رابط موقع طرف ثالث "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Third attempt"
msgstr "المحاولة الثالثة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_answer__comment
msgid "This comment will be displayed to the user if he selects this answer"
msgstr "سوف يتم عرض هذا التعليق للمستخدم إذا اختار هذه الإجابة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "This course is private."
msgstr "هذه الدورة خاصة."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "This is the correct answer"
msgstr "هذه هي الإجابة الصحيحة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "This is the correct answer, congratulations"
msgstr "هذه هي الإجابة الصحيحة، تهانينا "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "This quiz is already done. Retaking it is not possible."
msgstr "لقد خضعت لهذا الاختبار بالفعل. لا يمكنك إعادة المحاولة مجدداً. "

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This video already exists in this channel on the following slide: %s"
msgstr "هذا الفيديو موجود بالفعل في هذه القناة في الشريحة التالية: %s "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__name
#, python-format
msgid "Title"
msgstr "العنوان"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Toggle navigation"
msgstr "توجيه التبديل"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_tools
msgid "Tools"
msgstr "الأدوات"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_0
msgid "Tools and Methods"
msgstr "الأدوات والطرق "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_0
msgid "Tools you will need to complete this course."
msgstr "الأدوات التي سوف تحتاج إليها لإكمال هذه الدورة. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Total"
msgstr "المجموع "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_slides
msgid "Total Slides"
msgstr "مجموع الشرائح "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Total Views"
msgstr "مجموع المشاهدات "

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__training
msgid "Training"
msgstr "التدريب"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Training Layout"
msgstr "مخطط التدريب "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_0
msgid "Tree Infographic"
msgstr "مخطط معلومات بياني متفرع "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_2
msgid "Tree planting in hanging bottles on wall"
msgstr "زراعة الأشجار في قوارير معلقة على الحائط "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_0
msgid "Trees"
msgstr "الأشجار "

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_2_gard2
msgid "Trees, Wood and Gardens"
msgstr "الأشجار، الخشب، والحدائق "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_type
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Type"
msgstr "النوع"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط الاستثنائي المسجل."

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "تعذّر نشر الرسالة، الرجاء تهيئة البريد الإلكتروني الخاص بالمرسل. "

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#, python-format
msgid "Uncategorized"
msgstr "غير مصنف"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_0
msgid "Unforgettable Tools"
msgstr "أدوات لا تُنسى "

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Unknown document"
msgstr "مستند غير معروف"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Unknown error"
msgstr "خطأ غير معروف"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Unknown error, try again."
msgstr "خطأ غير معروف، حاول مجدداً. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Unpublished"
msgstr "غير منشور"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عداد الرسائل غير المقروءة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Update"
msgstr "تحديث"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__upload_group_ids
msgid "Upload Groups"
msgstr "رفع المجموعات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "Upload Presentation"
msgstr "رفع العرض التقديمي "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Upload a document"
msgstr "رفع مستند"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_id
msgid "Uploaded by"
msgstr "تم الرفع بواسطة "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Uploading document ..."
msgstr "جارٍ رفع المستند ..."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__tag_ids
msgid "Used to categorize and filter displayed channels/courses"
msgstr "مُستخدَم لتصنيف وتصفية القنوات/الدورات المعروضة "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__color
msgid "Used to decorate kanban view"
msgstr "مُستخدَم لتزيين عرض كانبان "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_vote
msgid "User vote"
msgstr "تصويت المستخدم "

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_users
msgid "Users"
msgstr "المستخدمين "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__video
#, python-format
msgid "Video"
msgstr "فيديو"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_video
msgid "Videos"
msgstr "مقاطع الفيديو"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "View"
msgstr "عرض "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "View all"
msgstr "عرض الكل"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "View course"
msgstr "عرض الدورات "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Views"
msgstr "المشاهدات "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_card
msgid "Views •"
msgstr "المشاهدات •"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_visibility
msgid "Visibility"
msgstr "الظهور"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_published
msgid "Visible on current website"
msgstr "ظاهرة في الموقع الحالي"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Visits"
msgstr "الزيارات"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_vote
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__vote
msgid "Vote"
msgstr "التصويت"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_votes
msgid "Votes"
msgstr "التصويتات"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Votes and comments are disabled for this course"
msgstr "تم تعطيل التصويت والتعليقات لهذه الدورة"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Waiting for validation"
msgstr "بانتظار التصديق "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_1
msgid "Watching the master(s) at work"
msgstr "مشاهدة المحترف(ين) في العمل "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_4
msgid ""
"We had a little chat with Harry Potted, sure he had interesting things to "
"say !"
msgstr ""
"لقد تحدثنا قليلاً مع Harry Potted، وقد أخبرنا بالعديد من الأشياء المثيرة "
"للاهتمام! "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__webpage
#, python-format
msgid "Web Page"
msgstr "صفحة ويب"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_webpage
msgid "Webpages"
msgstr "صفحات الويب "

#. module: website_slides
#: model:ir.model,name:website_slides.model_website
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_id
msgid "Website"
msgstr "الموقع الإلكتروني"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__gamification_challenge__challenge_category__slides
msgid "Website / Slides"
msgstr "الموقع الإلكتروني / الشرائح "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_url
msgid "Website URL"
msgstr "رابط الموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_description
msgid "Website meta description"
msgstr "الوصف الدلالي في الموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_keywords
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_keywords
msgid "Website meta keywords"
msgstr "الكلمات الدلالية في الموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_title
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_title
msgid "Website meta title"
msgstr "العنوان الدلالي في الموقع الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_og_img
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_og_img
msgid "Website opengraph image"
msgstr "صورة الرسم البياني المفتوح للموقع الإلكتروني "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Welcome on your course's home page. It's still empty for now. Click on "
"\"<b>New</b>\" to write your first course."
msgstr ""
"مرحباً بك في الصفحة الرئيسية لدورتك. إنها لا تزال فارغة في الوقت الحالي. "
"اضغط على \"<b>جديد</b>\" لكتابة دورتك الأولى. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "What does"
msgstr "ماذا يفعل "

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_0
msgid "What is a strawberry ?"
msgstr "ما هي الفراولة؟ "

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_1
msgid "What is the best tool to dig a hole for your plants ?"
msgstr "ما هي أفضل أداة يمكن استخدامها لحفر حفرة لنباتاتك؟ "

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_2
msgid "What was the question again ?"
msgstr "ما السؤال مرة أخرى؟ "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"عند اختيار حقل العلاقة كحقل أول، يصبح بمقدورك اختيار الحقل المراد مع نموذج "
"المستند المستلِم (نموذج-فرعي). "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"عند اختيار حقل العلاقة كحقل أول، يظهر هذا الحقل نموذج المستند الذي ستنتقل "
"إليه العلاقة. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_1
msgid ""
"Which wood type is best for my solid wood furniture? That's the question we "
"help you answer in this video !"
msgstr ""
"أي أنواع الخشب يُعد الأفضل للأثاث الخشبي المتين؟ هذا هو السؤال الذي سوف نجيب"
" عليه في هذا الفيديو! "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"With Quizzes you can keep your students focused and motivated by answering "
"some questions and gaining some karma points"
msgstr ""
"بإمكانك إبقاء طلابك مهتمين ومتأهبين عن طريق الإجابة على بعض الأسئلة واكتساب "
"نقاط الكارما، باستخدام الاختبارات القصيرة "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_1
msgid "Wood"
msgstr "الخشب "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_1
msgid "Wood Bending With Steam Box"
msgstr "ثني الخشب باستخدام صندوق البخار "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_3
msgid "Wood Characteristics"
msgstr "خصائص الخشب "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_1
msgid "Wood Types"
msgstr "أنواع الخشب "

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_3_0
msgid "Working with Wood"
msgstr "العمل باستخدام الخشب "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product or services. <br>To be "
"successful your content needs to be useful to your readers."
msgstr ""
"اكتب فقرة أو فقرتين تصف فيهما منتجك أو خدماتك. <br>حتى تكون ناجحاً، يجب أن "
"يكون محتواك مفيداً لقرائك. "

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"اكتب فقرة أو فقرتين تصف فيهما منتجك أو خدماتك أو خاصية معينة.<br> حتى تكون "
"ناجحاً، يجب أن يكون محتواك مفيداً لقرائك. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "XP"
msgstr "XP"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_0
#, python-format
msgid "Yes"
msgstr "نعم"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid ""
"You are not allowed to add members to this course. Please contact the course"
" responsible or an administrator."
msgstr ""
"لا يُسمح لك بإضافة الأعضاء إلى هذه الدورة. يرجى التواصل مع مسؤول الدورة أو "
"مع أحد المدراء. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"You can add <b>comments</b> on answers. This will be visible with the "
"results if the user select this answer."
msgstr ""
"بإمكانك إضافة <b>تعليقات</b> على الإجابات. سوف يكون مرئياً مع النتائج إذا "
"اختار المُستخدِم هذه الإجابة. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "You can not upload password protected file."
msgstr "لا يمكنك رفع ملف محمي بكلمة مرور."

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You cannot add tags to this course."
msgstr "لا يمكنك إضافة علامات تصنيف لهذه الدورة. "

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as completed if you are not among its members."
msgstr "لا يمكنك تحديد شريحة كمكتملة إذا لم تكن أحد الأعضاء. "

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as viewed if you are not among its members."
msgstr "لا يمكنك تحديد شريحة كشريحة تم عرضها إذا لم تكن أحد الأعضاء. "

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"You cannot mark a slide quiz as completed if you are not among its members."
msgstr "لا يمكنك تحديد الاختبار القصير لشريحة كمكتمل إذا لم تكن أحد الأعضاء. "

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You cannot upload on this channel."
msgstr "لا يمكنك الرفع على هذه القناة. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have access to this lesson"
msgstr "ليس لديك حق الوصول إلى هذا الدرس"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have enough karma to vote"
msgstr "لا تملك نقاط كارما كافية للتصويت "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "You gained"
msgstr "لقد اكتسبت "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid "You have already joined this channel"
msgstr "لقد انضممت إلى هذه القناة بالفعل "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You have already voted for this lesson"
msgstr "لقد قمت بالتصويت لهذا الدرس بالفعل "

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_invite
msgid "You have been invited to join {{ object.channel_id.name }}"
msgstr "لقد تمت دعوتك للانضمام إلى {{ object.channel_id.name }}"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "You have to sign in before"
msgstr "عليك تسجيل الدخول أولاً "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "You may now participate in our eLearning."
msgstr "يمكنك الآن المشاركة في التعلم الإلكتروني. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "You must be logged to submit the quiz."
msgstr "عليك تسجيل الدخول أولاً حتى تتمكن من تسليم الاختبار القصير. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You must be member of this course to vote"
msgstr "يجب أن تكون عضوا في هذه الدورة حتى تتمكن من التصويت "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_2
msgid "You won't believe those facts about carrots."
msgstr "لن تصدق هذه الحقائق عن الجزر. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "You're enrolled"
msgstr "لقد قمت بالتسجيل "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Your"
msgstr " "

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Your Google API key is invalid, please update it in your settings.\n"
"Settings > Website > Features > API Key"
msgstr ""
"مفتاح Google API الخاص بك غير صالح. الرجاء تحديثه من الإعدادات.\n"
"الإعدادات > الموقع الإلكتروني > الخصائص > مفتاح API "

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_level
msgid "Your Level"
msgstr "مستواك "

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_role
msgid "Your Role"
msgstr "دورك "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Your first section is created, now it's time to add lessons to your course. "
"Click on <b>Add Content</b> to upload a document, create a web page or link "
"a video."
msgstr ""
"تم إنشاء أول جزئية، حان الوقت الآن لإضافة دروس لدوراتك. اضغط على <b>إضافة "
"محتوى</b> لرفع مستند أو إنشاء صفحة ويب أو ربط مقطع فيديو. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Youtube Link"
msgstr "رابط يوتيوب"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Youtube Video URL"
msgstr "رابط فيديو يوتيوب"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__document_id
msgid "Youtube or Google Document ID"
msgstr "معرف يوتيوب أو مستندات Google "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__url
msgid "Youtube or Google Document URL"
msgstr "رابط يوتيوب أو مستندات Google "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.achievement_card
msgid "achieved"
msgstr "مؤرشف "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "breadcrumb"
msgstr "التعقب "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "by email."
msgstr "عن طريق البريد الإلكتروني. "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "e.g. Computer Science for kids"
msgstr "مثال: علوم الحاسوب للأطفال "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. How to grow your business with Odoo?"
msgstr "مثال: كيف تنمي أعمالك مع أودو؟ "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid ""
"e.g. In this video, we'll give you the keys on how Odoo can help you to grow"
" your business. At the end, we'll propose you a quiz to test your knowledge."
msgstr ""
"مثال: في هذا الفيديو، سوف تحصل على المعلومات الأساسية عن كيف يمكن لأودو "
"مساعدتك في تنمية أعمالك. في نهاية الفيديو، سوف نطرح اختباراً قصيراً لنختبر "
"به معرفتك. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "e.g. Introduction"
msgstr "مثال: المقدمة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "e.g. Your Level"
msgstr "مثال: مستواك "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. https://www.odoo.com"
msgstr "مثال: https://www.odoo.com "

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_root
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "eLearning"
msgstr "التعلم الإلكتروني "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_ids
msgid "eLearning Courses"
msgstr "دورات التعلم الإلكتروني "

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_overview
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "eLearning Overview"
msgstr "نظرة عامة على التعلم الإلكتروني "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "login"
msgstr "تسجيل الدخول"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"means? The YouTube \"unlisted\" means it is a video which can be viewed only"
" by the users with the link to it. Your video will never come up in the "
"search results nor on your channel."
msgstr ""
"؟ يوتيوب \"غير مدرج\" تعني أنه مقطع فيديو يمكن عرضه فقط من قِبَل المستخدمين "
"الذين يملكون الرابط. لن يظهر مقطع الفيديو ضمن نتائج البحث أو في قناتك. "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "or Leave the course"
msgstr "أو قم بمغادرة الدورة "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "reviews"
msgstr "التقييمات "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#, python-format
msgid "sign in"
msgstr "تسجيل الدخول"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid "steps"
msgstr "الخطوات"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "to contact responsible."
msgstr "للتواصل مع الشخص المسؤول."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
msgid "to download resources"
msgstr "لتحميل الموارد "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "to enroll."
msgstr "للتسجيل."

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "to share this"
msgstr "لمشاركة هذا"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "unlisted"
msgstr "غير مدرج "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "xp"
msgstr "xp"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_shared
msgid "{{ user.name }} shared a {{ object.slide_type }} with you!"
msgstr "{{ user.name }} شارك {{ object.slide_type }} معك!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "└<span class=\"ml-1\">Uncategorized</span>"
msgstr "└<span class=\"ml-1\">غير مصنف</span>"
