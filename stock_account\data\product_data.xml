<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record forcecreate="True" id="property_stock_account_output_categ_id" model="ir.property">
            <field name="name">property_stock_account_output_categ_id</field>
            <field name="fields_id" search="[('model','=','product.category'),('name','=','property_stock_account_output_categ_id')]"/>
            <field eval="False" name="value"/>
            <field name="company_id" ref="base.main_company"/>
        </record>
        <record forcecreate="True" id="property_stock_account_input_categ_id" model="ir.property">
            <field name="name">property_stock_account_input_categ_id</field>
            <field name="fields_id" search="[('model','=','product.category'),('name','=','property_stock_account_input_categ_id')]"/>
            <field eval="False" name="value"/>
            <field name="company_id" ref="base.main_company"/>
        </record>
    </data>
</odoo>
