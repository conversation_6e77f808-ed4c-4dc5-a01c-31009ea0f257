# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_blog
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-11-12 16:15+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Dutch (Belgium) (http://www.transifex.com/odoo/odoo-9/"
"language/nl_BE/)\n"
"Language: nl_BE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:117
#, python-format
msgid " Click on this button to send your blog post online."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Finally, the leading edge is being brought to the masses.\n"
"                    It will now be the turn of the big players to catch up "
"to\n"
"                    the superior technologies of the SME.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Odoo now competes on many fronts, with no real\n"
"                competition out there to knock them off the top spot.\n"
"                With the launch of their integrated CMS and Ecommerce\n"
"                systems,it only elevates their position as one of the "
"leading\n"
"                lights in the open source revolution. It will be at least 5\n"
"                years before another ERP or CMS provider will be able to\n"
"                compete at this level due to the technology currently\n"
"                employed by most industry providers.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Odoo's latest launch will allow a business to go from\n"
"                zero to trading online quicker than ever before,” Stuart\n"
"                Mackintosh, MD of Open Source specialist and Odoo\n"
"                integration partner, OpusVL, explains. “The investment\n"
"                required to have a fully automated business system is\n"
"                dramatically reduced, enabling the small and medium\n"
"                enterprise to compete at a level of functionality and\n"
"                performance previously reserved for the big IT investors.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"This is another clever and highly disruptive move by\n"
"                Odoo,which will force other technology providers to\n"
"                take another look at the value they are providing to ensure\n"
"                that their 'solutions' can still compete.\""
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "&amp;times;"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A mix of push &amp; pull: Today, people\n"
"                    are victims of what others decide to push to them.\n"
"                    Odoo differentiates:"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_about_us
msgid "About us"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:24
#, python-format
msgid "Add Content"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Adding to industry leading technology"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_history
msgid "Archives"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"As it comes, there is a default website based on Bootstrap\n"
"                3, the latest industry standard for rapid development of\n"
"                multi-device websites backed by Twitter, so can be directly\n"
"                integrated with many web tools and works across all devices\n"
"                by default."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"At Odoo, we build tools to bring productivity to\n"
"                enterprises. As emails and information flows are one of\n"
"                the biggest wastes of time in companies, we have to fix\n"
"                this."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_author_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post_create_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Auteur"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_author_avatar
msgid "Avatar"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_blog_id
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#: model:website.menu,name:website_blog.menu_news
msgid "Blog"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_name
msgid "Blog Name"
msgstr ""

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:283
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog Post"
msgstr ""

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:287
#, python-format
msgid "Blog Post <b>%s</b> seems to have a link to this page !"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:37
#, python-format
msgid "Blog Post Created"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr ""

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_post
#: model:ir.ui.menu,name:website_blog.menu_page
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
msgid "Blog Posts"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr ""

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr ""

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
#: model:ir.ui.menu,name:website_blog.menu_blog_tag
msgid "Blog Tags"
msgstr ""

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
msgid "Blogs"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blue"
msgstr ""

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "Building your company's website and selling your products online easy."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:58
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
#, python-format
msgid "Change Cover"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:54
#, python-format
msgid "Change and customize your blog post cover."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:108
#, python-format
msgid "Check Mobile Preview"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:68
#, python-format
msgid "Choose an image"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:69
#, python-format
msgid "Choose an image from the library."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Click on \"Content\" on the top menu to write your first blog post."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:94
#, python-format
msgid "Click on '<em>Save</em>' button to record changes on the page."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:77
#, python-format
msgid "Click on '<em>Save</em>' to set the picture as cover."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:102
#, python-format
msgid ""
"Click on the mobile icon to preview how your blog post will be displayed on "
"a mobile device."
msgstr ""

#. module: website_blog
#: model_terms:ir.actions.act_window,help:website_blog.action_blog_post
msgid "Click to create a new blog post."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:124
#, python-format
msgid "Close Tutorial"
msgstr "Sluit tutorial"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Color"
msgstr "Kleur"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_about_us
msgid "Contact us"
msgstr "Contacteer ons"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:84
#: model:ir.model.fields,field_description:website_blog.field_blog_post_content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Content"
msgstr "Inhoud"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:39
#, python-format
msgid "Continue"
msgstr "Doorgaan"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:60
#, python-format
msgid "Cover"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_cover_properties
msgid "Cover Properties"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:14
#, python-format
msgid "Create a blog post"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_create_uid
msgid "Created by"
msgstr "Gemaakt door"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post_create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_create_date
msgid "Created on"
msgstr "Gemaakt op"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:53
#, python-format
msgid "Customize Cover"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_mail_compose_message_path
#: model:ir.model.fields,field_description:website_blog.field_mail_message_path
#: model:ir.model.fields,field_description:website_blog.field_survey_mail_compose_message_path
msgid "Discussion Path"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post_display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Duplicate"
msgstr "Dupliceer"

#. module: website_blog
#: model:ir.model,name:website_blog.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: website_blog
#: model:ir.model,name:website_blog.model_survey_mail_compose_message
msgid "Email composition wizard for Survey"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Emails are broken."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Emails make me waste my time. But I need them.\n"
"                Given the importance that emails have in our lives,\n"
"                it's incredible it's still one of the only software\n"
"                areas that did not evolve in the past 20 years!"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Focus on the Content: Everything is\n"
"                    stripped to emphasize on the real message. No more\n"
"                    welcome introductions, greetings, signatures and legal\n"
"                    notes.We standardize the layout of each message.\n"
"                    (signatures are on the profile of a contact, not in\n"
"                    every message)"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Folders and mailing lists are great tools but too\n"
"                    complex in traditional email clients. In Odoo, a\n"
"                    group of contacts that share a discussion can be\n"
"                    created with one click. Every group should have it's\n"
"                    own email address."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Follow us"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Full Screen"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Get Things Done: your inbox is a\n"
"                    todo list. You should be able to process (not only\n"
"                    read) the inbox and easily mark messages for future\n"
"                    actions. Every inbox should be empty after having\n"
"                    been processed; no more overload of information."
msgstr ""

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Get in touch with us"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Green"
msgstr "Groen"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Groeperen op"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Here are the ideas behind the Odoo communication tools:"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "High"
msgstr "Hoog"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"However, unlike other web content management systems, it\n"
"                fully integrates into the back-end database. This means\n"
"                that when you edit a product description, image or price,\n"
"                it updates the product database in real time, providing a\n"
"                true self-service window into the business."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post_id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "Ideas behind the Odoo communication tools."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "In"
msgstr ""

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
msgid "Integrating your CMS and E-Commerce"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Keep control of what you want to receive or don't want\n"
"                    to receive. People should never receive spam. You\n"
"                    should follow/unfollow any kind of information in one\n"
"                    click."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog___last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post___last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post_write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag___last_update
msgid "Last Modified on"
msgstr "Laatst Aangepast op"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "Latest Posts"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:18
#, python-format
msgid "Let's go through the first steps to write beautiful blog posts."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Like many modern website editors, with Odoo you can edit\n"
"                content in-line, enabling you to see exactly what you are\n"
"                changing and ensure your changes suit the context."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Low"
msgstr "Laag"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Medium"
msgstr "Gemiddeld"

#. module: website_blog
#: model:ir.model,name:website_blog.model_mail_message
msgid "Message"
msgstr "Bericht"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Messages \"for action\": they\n"
"                            require your immediate attention and you need\n"
"                            to process them all. This accounts for 10%\n"
"                            of your daily emails. Use the \"To: me\" menu\n"
"                            for these."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Messages \"for information\":\n"
"                            you can pull them when you need some specific\n"
"                            information; they are not required to be read\n"
"                            every day.You receive only what you decided\n"
"                            to follow.This accounts for 90% of your daily\n"
"                            emails.Use the \"Inbox\" menu for these."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:101
#, python-format
msgid "Mobile Preview"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_name
msgid "Name"
msgstr "Naam:"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Narrow"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:17
#: code:addons/website_blog/static/src/js/website.tour.blog.js:31
#: code:addons/website_blog/static/src/js/website_blog.editor.js:21
#: model_terms:ir.ui.view,arch_db:website_blog.content_new_blogpost
#, python-format
msgid "New Blog Post"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "New Features Launched"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.header_footer_custom
msgid "News"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "No Cover"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No blog post yet."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "No keywords defined!"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_visits
msgid "No of Views"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "None"
msgstr "Geen"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:9
#, python-format
msgid "Not Published"
msgstr "Niet gepubliceerd"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Odoo claims to be 'the Open Source software that makes\n"
"                building your company's website and selling your products\n"
"                online easy'. So how true is this statement?"
msgstr ""

#. module: website_blog
#: model:blog.post,website_meta_keywords:website_blog.blog_post_1
msgid "Odoo, email"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Opacity"
msgstr ""

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Our Blog"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_blogs
msgid "Our Blogs"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Participate on our social stream."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:28
#, python-format
msgid "Please"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:31
#, python-format
msgid "Post"
msgstr "Boeken"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:44
#, python-format
msgid "Post Headline"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_post_ids
msgid "Posts"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Productivity is key: our smart user\n"
"                    interface does not require you to click on every mail\n"
"                    to read a thread. Reading a full thread, replying,\n"
"                    attaching documents is super fast."
msgstr ""

#. module: website_blog
#: code:addons/website_blog/controllers/main.py:268
#, python-format
msgid "Public user cannot post comments on blog post."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:10
#, python-format
msgid "Published"
msgstr "Gepubliceerd"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:116
#, python-format
msgid "Publishing status"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Purple"
msgstr "Paars"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_ranking
msgid "Ranking"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Read Next <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Reading my inbox is the most unproductive task I do\n"
"                on a daily basis. I have to spend one full hour a\n"
"                day to process my emails. All the junk flows in the\n"
"                same inbox; spams, information that doesn't matter,\n"
"                quoted answers of quoted answers, etc. At the end\n"
"                of the hour, only 10 emails actually requested an\n"
"                answer from me. With a good tool, I could have done\n"
"                my job in 10 minutes!"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Red"
msgstr "Rood"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:76
#, python-format
msgid "Save"
msgstr "Opslaan"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:93
#, python-format
msgid "Save your modifications once you are done"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:109
#, python-format
msgid "Scroll to check rendering and then close the mobile preview."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:61
#, python-format
msgid "Select this menu item to change blog cover."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:32
#, python-format
msgid "Select this menu item to create a new blog post."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:29
#, python-format
msgid "Sign in"
msgstr "Aanmelden"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Size"
msgstr "Grootte"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:19
#, python-format
msgid "Skip"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:19
#, python-format
msgid "Start Tutorial"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/models/website_blog.py:91
#: code:addons/website_blog/static/src/js/website.tour.blog.js:90
#, python-format
msgid "Start writing here..."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:85
#, python-format
msgid "Start writing your story here."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_subtitle
msgid "Sub Title"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Subtitle"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr ""

#. module: website_blog
#: sql_constraint:blog.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_tag_ids
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_tags
msgid "Tags"
msgstr "Labels"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Technical"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "The Communication Mechanism of Odoo"
msgstr ""

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
#: model:blog.post,website_meta_description:website_blog.blog_post_1
msgid "The Future of Emails"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:38
#, python-format
msgid "This is your new blog post. Let's edit it."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid ""
"This page is great to improve your <strong>Search Engine Optimization</"
"strong>;\n"
"                   You can review titles, keywords and descriptions of all "
"blogs at once."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"This provides a single source of data for your company and\n"
"                removes the need to create offline synchronisation between\n"
"                website and product database."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:123
#, python-format
msgid ""
"This tutorial is over. To discover more features and improve the content of "
"this page, go to the upper left customize menu. You can also add some cool "
"content with your text in the edit mode with the upper right button."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_name
msgid "Title"
msgstr "Titel"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"To add to an already comprehensive set of Odoo\n"
"                    features, a website content management system (CMS\n"
"                    or WMS) has been developed and a beta release is\n"
"                    available from today, 31st January 2014."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"To disrupt emails, you need more than just another user\n"
"                interface. We need to rethink the whole communication flow."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Untitled Post"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:25
#, python-format
msgid ""
"Use this button to create a new blog post like any other document (page, "
"menu, products, event, ...)."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_mail_compose_message_path
#: model:ir.model.fields,help:website_blog.field_mail_message_path
#: model:ir.model.fields,help:website_blog.field_survey_mail_compose_message_path
msgid ""
"Used to display messages in a paragraph-based chatter using a unique path;"
msgstr ""

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:262
#, python-format
msgid "View Blog Post"
msgstr ""

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
msgid "Website"
msgstr ""

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_website_message_ids
msgid "Website Messages"
msgstr "Websiteberichten"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post_website_message_ids
msgid "Website communication history"
msgstr "Websitecommunicatiehistoriek"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:26
#, python-format
msgid "Write a comment..."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_about_us
msgid ""
"Write a small text here for when <b>new visitors</b> find your website\n"
"            through your <b>blog entries</b>, referenced in Google."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:46
#, python-format
msgid "Write a title, the subtitle is optional."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Yellow"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid ""
"You should <strong>add a banner on the top</strong> as it is a frequent "
"landing page for new visitors.\n"
"                   <span class=\"text-muted\">This box will not be visible "
"to your visitors.</span>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:14
#, python-format
msgid "by"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "comment"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "comments"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "not published"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "pull-right"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:29
#, python-format
msgid "to comment."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "view"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "views"
msgstr ""

#~ msgid "Action Needed"
#~ msgstr "Vereist actie"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Datum laatste bericht voor dit record."

#~ msgid "Followers"
#~ msgstr "Volgers"

#~ msgid "Followers (Channels)"
#~ msgstr "Volgers (Kanalen)"

#~ msgid "Followers (Partners)"
#~ msgstr "Volgers (Partners)"

#~ msgid "If checked new messages require your attention."
#~ msgstr ""
#~ "Als dit is ingeschakeld, zijn er nieuwe berichten die uw aandacht vragen."

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Indien aangevinkt vragen nieuwe berichten uw aandacht."

#~ msgid "Is Follower"
#~ msgstr "Is Volger"

#~ msgid "Last Message Date"
#~ msgstr "Datum laatste bericht"

#~ msgid "Messages"
#~ msgstr "Berichten"

#~ msgid "Messages and communication history"
#~ msgstr "Berichten en communicatiehistoriek"

#~ msgid "Number of Actions"
#~ msgstr "Aantal acties"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Aantal berichten die actie vereisen"

#~ msgid "Number of unread messages"
#~ msgstr "Aantal ongelezen berichten"

#~ msgid "Unread Messages"
#~ msgstr "Ongelezen berichten"

#~ msgid "Unread Messages Counter"
#~ msgstr "Teller ongelezen berichten"

#~ msgid "on"
#~ msgstr "aan"
