<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="survey_action_server_clean_test_answers" model="ir.actions.server">
            <field name="name">Survey: Clean test answers</field>
            <field name="type">ir.actions.server</field>
            <field name="model_id" ref="model_survey_survey" />
            <field name="binding_model_id" ref="model_survey_survey" />
            <field name="state">code</field>
            <field name="code">
if records:
    env['survey.user_input'].search([('survey_id', 'in', records.ids), ('test_entry', '=', 'True')]).unlink()
            </field>
        </record>
</data>
</odoo>