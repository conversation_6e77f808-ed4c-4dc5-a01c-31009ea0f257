# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import date, datetime
from dateutil.relativedelta import relativedelta
from odoo.exceptions import ValidationError


class HrAnnualContractAssisment(models.Model):
    _name = "hr.contract.assessment.monthly"
    _rec_name = 'assessment_name'

    _sql_constraints = [('assessment_unique', 'UNIQUE(date_from,date_to,employee_id)', 'Assessment already exists!')]

    #### Date period
    date_from = fields.Date("Date From", default=lambda self: fields.Date.to_string(date.today().replace(day=1)),required=True)
    date_to = fields.Date("Date To", default=lambda self: fields.Date.to_string((datetime.now() + relativedelta(months=+1, day=1, days=-1)).date()), required=True)
    ######################

    current_user_id = fields.Many2one('res.users', 'معد التقييم', default=lambda self: self.env.user, readonly=True)## the one how cuducted the evaluation

    employee_id = fields.Many2one('hr.employee', string='اسم الموظف', domain="[('parent_id.user_id', '=', current_user_id)]", required=True)
    department_id = fields.Many2one('hr.department', compute='_compute_employee_contract',string='الادارة'
                                    , store=True)
    #contract_id = fields.Many2one('hr.contract')
    job_id = fields.Many2one('hr.job', compute='_compute_employee_contract',string='الوظيفة', store=True)
    assessment_name = fields.Char(compute='_compute_assessment_name', store=True)

    #annual_id = fields.Many2one('hr.contract.byyear.assessment', string='سنة التقييم')
    final_result = fields.Float(string="نتيجة القسم", compute='_compute_final_result', default = 0)

    time_attendance_ids = fields.One2many('hr.assessment.annual.items', 'contract_assessment_time_attendance_id')
    time_attendance_wight = fields.Integer(string="وزن القسم (نسبة مئوية)", default=15,required=True)
    time_attendance_result = fields.Float(string="نتيجة القسم", compute='_compute_time_attendance_result',default=0)

    performance_ids = fields.One2many('hr.assessment.annual.items', 'contract_assessment_performance_id')
    performance_wight = fields.Integer(string="وزن القسم (نسبة مئوية)", default=70,required=True)
    performance_result = fields.Float(string="نتيجة القسم", compute='_compute_performance_result',default=0)

    behaviors_ids = fields.One2many('hr.assessment.annual.items', 'contract_assessment_behaviors_id')
    behaviors_wight = fields.Integer(string="وزن القسم (نسبة مئوية)", default=15,required=True)
    behaviors_result = fields.Float(string="نتيجة القسم", compute='_compute_behaviors_result',default=0)


    @api.depends('time_attendance_ids')
    def _compute_time_attendance_result(self):
        for elem in self:
            elem.time_attendance_result = 0
            if elem.time_attendance_ids:
                total_item_value = 0
                total_item_scores = 0
                for e in elem.time_attendance_ids:
                    total_item_value+=e.item_value
                    total_item_scores+=e.item_score
                ## constrains
                if total_item_scores > total_item_value:
                    raise ValidationError('There is an Error With Your Assessments Scores !')
                ####
                elem.time_attendance_result = (float(total_item_scores)/float(total_item_value))*(elem.time_attendance_wight)

    @api.depends('performance_ids')
    def _compute_performance_result(self):
        for elem in self:
            elem.performance_result = 0
            if elem.performance_ids:
                total_item_value = 0
                total_item_scores = 0
                for e in elem.performance_ids:
                    total_item_value += e.item_value
                    total_item_scores += e.item_score
                ## constrains
                if total_item_scores > total_item_value:
                    raise ValidationError('There is an Error With Your Assessments Scores !')
                ####
                elem.performance_result = (float(total_item_scores) / float(total_item_value)) * (elem.performance_wight)

    @api.depends('behaviors_ids')
    def _compute_behaviors_result(self):
        for elem in self:
            elem.behaviors_result = 0
            if elem.behaviors_ids:
                total_item_value = 0
                total_item_scores = 0
                for e in elem.behaviors_ids:
                    total_item_value += e.item_value
                    total_item_scores += e.item_score
                ## constrains
                if total_item_scores > total_item_value:
                    raise ValidationError('There is an Error With Your Assessments Scores !')
                ####
                elem.behaviors_result = (float(total_item_scores) / float(total_item_value)) * (
                            elem.behaviors_wight)

    @api.depends('time_attendance_result','performance_result','behaviors_result')
    def _compute_final_result(self):
        for elem in self:
            elem.final_result = 0
            if elem.time_attendance_result and elem.performance_result and elem.behaviors_result:
                elem.final_result = elem.time_attendance_result +elem.performance_result+elem.behaviors_result

    @api.depends('employee_id')
    def _compute_assessment_name(self):
        for elem in self:
            elem.assessment_name = str(elem.employee_id.name)+'-Monthly-Assessment-'+str(elem.date_from)[:7]

    @api.depends('employee_id')
    def _compute_employee_contract(self):
        for contract in self.filtered('employee_id'):
            contract.job_id = contract.employee_id.job_id
            contract.department_id = contract.employee_id.department_id
#
    @api.model
    def default_get(self, fields):
        res = super(HrAnnualContractAssisment, self).default_get(fields)
        #employ_id = self._context.get('active_id')
        #res['contract_id'] = self._context.get('active_id')
        #res['employee_id'] = self.env['hr.contract'].search([('id', '=', employ_id)]).employee_id.id

        #### time_attendance #####################
        llist = self.env['hr.assessment.annual.element'].search([('item_type','=','time_attendance')])
        default_list = []
        i=1
        for ele in llist:
            default_list.append((0,0, {
                'seq_number': i,
                'item_name': ele.item_name,
                'item_value': ele.item_value,
                'item_score': 0,
            }))
            i+=1

        res['time_attendance_ids'] = default_list
        ##############################################
        #### performance #############################
        llist = self.env['hr.assessment.annual.element'].search([('item_type', '=', 'performance')])
        default_list = []
        i = 1
        for ele in llist:
            default_list.append((0, 0, {
                'seq_number': i,
                'item_name': ele.item_name,
                'item_value': ele.item_value,
                'item_score': 0,
            }))
            i += 1

        res['performance_ids'] = default_list
        ##############################################
        #### behaviors #############################
        llist = self.env['hr.assessment.annual.element'].search([('item_type', '=', 'behaviors')])
        default_list = []
        i = 1
        for ele in llist:
            default_list.append((0, 0, {
                'seq_number': i,
                'item_name': ele.item_name,
                'item_value': ele.item_value,
                'item_score': 0,
            }))
            i += 1
        res['behaviors_ids'] = default_list
        return res


    def unlink(self):
        for elem in self:
            if not elem.is_hr_group:
                raise ValidationError('ليس لديك صلاحية حذف التقييم'+' '+str(elem.assessment_name))
            if elem.current_user_id.id != elem.env.user.id:
                raise ValidationError('ليس لديك صلاحية حذف التقييم'+' '+str(elem.assessment_name))
            # if elem.state != 'draft':
            #     raise ValidationError('لا يمكن حذف التقييم الا في حالة كان مسودة'+' '+str(elem.assessment_name))
        return super(HrAnnualContractAssisment, self).unlink()

    @api.model
    def create(self, vals):
        default_list = self.env['hr.assessment.annual.element'].search([('item_type','=','time_attendance')])
        i=0
        for elem in default_list:
            vals['time_attendance_ids'][i][2].setdefault('seq_number',i+1)
            vals['time_attendance_ids'][i][2]['item_value']=elem.item_value
            vals['time_attendance_ids'][i][2]['item_name']=elem.item_name
            i+=1

        default_list = self.env['hr.assessment.annual.element'].search([('item_type', '=', 'performance')])
        i = 0
        for elem in default_list:
            vals['performance_ids'][i][2].setdefault('seq_number', i + 1)
            vals['performance_ids'][i][2]['item_value'] = elem.item_value
            vals['performance_ids'][i][2]['item_name'] = elem.item_name
            i += 1

        default_list = self.env['hr.assessment.annual.element'].search([('item_type', '=', 'behaviors')])
        i = 0
        for elem in default_list:
            vals['behaviors_ids'][i][2].setdefault('seq_number', i + 1)
            vals['behaviors_ids'][i][2]['item_value'] = elem.item_value
            vals['behaviors_ids'][i][2]['item_name'] = elem.item_name
            i += 1
        return super(HrAnnualContractAssisment, self).create(vals)


class HrAnnualContractAssismentItems(models.Model):
    _name = 'hr.assessment.annual.items'
    #contract_assessment_id = fields.Many2one('hr.contract.assessment.annual')

    contract_assessment_time_attendance_id = fields.Many2one('hr.contract.assessment.monthly')
    contract_assessment_performance_id = fields.Many2one('hr.contract.assessment.monthly')
    contract_assessment_behaviors_id = fields.Many2one('hr.contract.assessment.monthly')

    seq_number = fields.Integer(string='الرقم')
    item_name = fields.Char(string='اسم العنصر')
    item_value = fields.Float(string='قيمة العنصر')
    item_score = fields.Float(string='النتيجة')

    @api.constrains('item_score')
    def check_item_score(self):
        for elem in self:
            if (elem.item_score > 5) or (elem.item_score < 0.0):
                raise ValidationError('نتيجة العنصر يجب أن تكون بين 0 و 5')


class EvaluationElement(models.Model):
    # عناصر تقييم الأداء السنوي
    _name = 'hr.assessment.annual.element'
    #contract_assessment_id = fields.Many2one('hr.contract.assessment.monthly')

    item_name = fields.Char(string='أسم العنصر', required=True)
    item_value = fields.Integer(string='وزن العنصر', default=5, required=True)
    item_type = fields.Selection(([
        ('time_attendance','Time and Attendance'),
        ('performance','Performance'),
        ('behaviors','Behaviors')]),string='نوع العنصر', required=True)


class HrPayslipX(models.Model):
    _inherit = 'hr.payslip'

    monthly_assessment_result = fields.Float(string="Monthly Assessment", compute='_get_monthly_assessment',store=True, default = 0)

    @api.depends('employee_id', 'date_to', 'date_from')
    def _get_monthly_assessment(self):
        for payslip in self:
            fmt = '%Y-%m-%d'
            payslip.monthly_assessment_result = 0
            pre_date = str((datetime.strptime(str(payslip.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01'
            date_from = (datetime.strptime(pre_date, fmt))
            date_to = (datetime.strptime(pre_date, fmt) + relativedelta(months=1, day=1, days=-1))
            assessment = self.env['hr.contract.assessment.monthly'].search([('employee_id','=',payslip.employee_id.id),('date_from','=',date_from),('date_to','=',date_to)], limit=1)
            payslip.monthly_assessment_result= assessment.final_result
