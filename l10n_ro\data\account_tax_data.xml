<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="tvac_00" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">4</field>
        <field name="description">TVA colectat 0% Bunuri</field>
        <field name="name">TVA colectat 0% Bunuri</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record id="tvac_05" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">3</field>
        <field name="name">TVA colectat 5% Bunuri</field>
        <field name="description">TVA colectat 5% Bunuri</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd11')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd11')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd11')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd11')],
            }),
        ]"/>
    </record>

    <record id="tvac_09" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">2</field>
        <field name="name">TVA colectat 9% Bunuri</field>
        <field name="description">TVA colectat 9% Bunuri</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_9"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd10')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd10')],
            }),
        ]"/>
    </record>

    <record id="tvac_19" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">1</field>
        <field name="name">TVA colectat 19% Bunuri</field>
        <field name="description">TVA colectat 19% Bunuri</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd9')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd9')],
            }),
        ]"/>
    </record>

    <record id="tvac_00_s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">4</field>
        <field name="description">TVA colectat 0% Servicii</field>
        <field name="name">TVA colectat 0% Servicii</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record id="tvac_05_s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">3</field>
        <field name="name">TVA colectat 5% Servicii</field>
        <field name="description">TVA colectat 5% Servicii</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd11')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd11')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd11')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd11')],
            }),
        ]"/>
    </record>

    <record id="tvac_09_s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">2</field>
        <field name="name">TVA colectat 9% Servicii</field>
        <field name="description">TVA colectat 9% Servicii</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_9"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd10')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd10')],
            }),
        ]"/>
    </record>

    <record id="tvac_19_s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">1</field>
        <field name="name">TVA colectat 19% Servicii</field>
        <field name="description">TVA colectat 19% Servicii</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd9')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd9')],
            }),
        ]"/>
    </record>

    <record id="tvad_00" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">43</field>
        <field name="name">TVA deductibil 0% Bunuri</field>
        <field name="description">TVA deductibil 0% Bunuri</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record id="tvad_05" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">42</field>
        <field name="name">TVA deductibil 5% Bunuri</field>
        <field name="description">TVA deductibil 5% Bunuri</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd261')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd261')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd261')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd261')],
            }),
        ]"/>
    </record>

    <record id="tvad_09" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">41</field>
        <field name="name">TVA deductibil 9% Bunuri</field>
        <field name="description">TVA deductibil 9% Bunuri</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd251')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd251')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd251')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd251')],
            }),
        ]"/>
    </record>

    <record id="tvad_19" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">40</field>
        <field name="name">TVA deductibil 19% Bunuri</field>
        <field name="description">TVA deductibil 19% Bunuri</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd241')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd241')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd241')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd241')],
            }),
        ]"/>
    </record>

    <record id="tvad_00_s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">43</field>
        <field name="name">TVA deductibil 0% Servicii</field>
        <field name="description">TVA deductibil 0% Servicii</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record id="tvad_05_s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">42</field>
        <field name="name">TVA deductibil 5% Servicii</field>
        <field name="description">TVA deductibil 5% Servicii</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd261')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd261')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd261')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd261')],
            }),
        ]"/>
    </record>

    <record id="tvad_09_s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">41</field>
        <field name="name">TVA deductibil 9% Servicii</field>
        <field name="description">TVA deductibil 9% Servicii</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd251')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd251')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd251')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd251')],
            }),
        ]"/>
    </record>

    <record id="tvad_19_s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">40</field>
        <field name="name">TVA deductibil 19% Servicii</field>
        <field name="description">TVA deductibil 19% Servicii</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd241')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd241')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd241')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd241')],
            }),
        ]"/>
    </record>

    <record id="tvaic_00" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">13</field>
        <field name="name">TVA la Incasare - colectat 0%</field>
        <field name="description">TVA 0%</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record id="tvaic_05" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">12</field>
        <field name="name">TVA la Incasare - colectat 5%</field>
        <field name="description">TVA colectat 5%</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44281"/>
        <field name="tax_group_id" ref="tax_group_tva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd11')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd11')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd11')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd11')],
            }),
        ]"/>
    </record>

    <record id="tvaic_09" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">11</field>
        <field name="name">TVA la Incasare - colectat 9%</field>
        <field name="description">TVA colectat 9%</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44281"/>
        <field name="tax_group_id" ref="tax_group_tva_9"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd10')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd10')],
            }),
        ]"/>
    </record>

    <record id="tvaic_19" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">10</field>
        <field name="name">TVA la Incasare - colectat 19%</field>
        <field name="description">TVA colectat 19%</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44281"/>
        <field name="tax_group_id" ref="tax_group_tva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd9')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd9')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd9')],
            }),
        ]"/>
    </record>

    <record id="tvaid_00" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">53</field>
        <field name="name">TVA la Incasare - deductibil 0%</field>
        <field name="description">TVA deductibil 0%</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record id="tvaid_05" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">52</field>
        <field name="name">TVA la Incasare - deductibil 5%</field>
        <field name="description">TVA deductibil 5%</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44282"/>
        <field name="tax_group_id" ref="tax_group_tva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd261')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd261')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd261')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd261')],
            }),
        ]"/>
    </record>

    <record id="tvaid_09" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">51</field>
        <field name="name">TVA la Incasare - deductibil 9%</field>
        <field name="description">TVA deductibil 9%</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44282"/>
        <field name="tax_group_id" ref="tax_group_tva_9"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd251')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd251')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd251')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd251')],
            }),
        ]"/>
    </record>

    <record id="tvaid_19" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">50</field>
        <field name="name">TVA la Incasare - deductibil 19%</field>
        <field name="description">TVA deductibil 19%</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_exigibility">on_payment</field>
        <field name="cash_basis_transition_account_id" ref="pcg_44282"/>
        <field name="tax_group_id" ref="tax_group_tva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd241')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd241')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd241')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd241')],
            }),
        ]"/>
    </record>

    <record id="tvati" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">20</field>
        <field name="name">TVA Taxare Inversa</field>
        <field name="description">TVA Taxare Inversa</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_ti"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd13')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd13')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="tvatip00" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">63</field>
        <field name="name">TVA Taxare Inversa 0%</field>
        <field name="description">TVA Taxare Inversa 0%</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_ti"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record id="tvatip05" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">62</field>
        <field name="name">TVA Taxare Inversa 5%</field>
        <field name="description">TVA Taxare Inversa 5%</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_ti"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd123'),
                                         ref('account_tax_report_ro_baza_rd273')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd123')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd273')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd123'),
                                          ref('account_tax_report_ro_baza_rd273')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd123')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd273')],
            }),
        ]"/>
    </record>

    <record id="tvatip09" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">61</field>
        <field name="name">TVA Taxare Inversa 9%</field>
        <field name="description">TVA Taxare Inversa 9%</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_ti"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd122'),
                                         ref('account_tax_report_ro_baza_rd272')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd122')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd272')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd122'),
                                          ref('account_tax_report_ro_baza_rd272')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd122')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd272')],
            }),
        ]"/>
    </record>

    <record id="tvatip19" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">60</field>
        <field name="name">TVA Taxare Inversa 19%</field>
        <field name="description">TVA Taxare Inversa 19%</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_ti"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd121'),
                                         ref('account_tax_report_ro_baza_rd271')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd121')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd271')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd121'),
                                          ref('account_tax_report_ro_baza_rd271')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd121')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd271')],
            }),
        ]"/>
    </record>

    <record id="tvatiscded" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">31</field>
        <field name="name">TVA Taxare Scutita Livrari cu drept de deducere</field>
        <field name="description">Scutit-L1</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_scutit"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="tvatiscnoded" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">32</field>
        <field name="name">TVA Taxare Scutita Livrari fara drept de deducere</field>
        <field name="description">Scutit-L2</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_scutit"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd15')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd15')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="tvatiscdeda" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">61</field>
        <field name="name">TVA Taxare Scutita Achizitii</field>
        <field name="description">Scutit-A</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_scutit"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="tvatiscdeda_intr" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">61</field>
        <field name="name">TVA Taxare Scutita Achizitii Intracomunitare</field>
        <field name="description">Scutit-AI</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_scutit"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd30'),
                                         ref('account_tax_report_ro_baza_rd301')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd30'),
                                          ref('account_tax_report_ro_baza_rd301')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="tvatine" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">33</field>
        <field name="name">TVA Taxare Neimpozabila Livrari</field>
        <field name="description">Neimpozabil-L</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_neimp"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd15')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd15')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="tvatinea" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">62</field>
        <field name="name">TVA Taxare Neimpozabila Achizitii</field>
        <field name="description">Neimpozabil-A</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_neimp"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="tvati_intrab" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">34</field>
        <field name="name">TVA Intracomunitara Livrari Bunuri</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd1')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd1')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="tvati_intras" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">34</field>
        <field name="name">TVA Intracomunitara Livrari Servicii</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd3')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="tvati_extra" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">35</field>
        <field name="name">TVA Export Bunuri</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_scutit"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="tvati_extras" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">36</field>
        <field name="name">TVA Export Servicii</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="tax_group_tva_scutit"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd14')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="tvati_intrap0b" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">66</field>
        <field name="name">TVA Intracomunitara Achizitii 0% - Bunuri</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_0_eu"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record id="tvati_intrap5b" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">65</field>
        <field name="name">TVA Intracomunitara Achizitii 5% - Bunuri</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_5_eu"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd5'),
                                         ref('account_tax_report_ro_baza_rd51'),
                                         ref('account_tax_report_ro_baza_rd20'),
                                         ref('account_tax_report_ro_baza_rd201')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd5'),
                                          ref('account_tax_report_ro_tva_rd51')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd20'),
                                         ref('account_tax_report_ro_tva_rd201')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd5'),
                                          ref('account_tax_report_ro_baza_rd51'),
                                          ref('account_tax_report_ro_baza_rd20'),
                                          ref('account_tax_report_ro_baza_rd201')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd5'),
                                         ref('account_tax_report_ro_tva_rd51')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd20'),
                                          ref('account_tax_report_ro_tva_rd201')],
            }),
        ]"/>
    </record>

    <record id="tvati_intrap9b" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">64</field>
        <field name="name">TVA Intracomunitara Achizitii 9% - Bunuri</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_9_eu"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd5'),
                                         ref('account_tax_report_ro_baza_rd51'),
                                         ref('account_tax_report_ro_baza_rd20'),
                                         ref('account_tax_report_ro_baza_rd201')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd5'),
                                          ref('account_tax_report_ro_tva_rd51')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd20'),
                                         ref('account_tax_report_ro_tva_rd201')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd5'),
                                          ref('account_tax_report_ro_baza_rd51'),
                                          ref('account_tax_report_ro_baza_rd20'),
                                          ref('account_tax_report_ro_baza_rd201')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd5'),
                                         ref('account_tax_report_ro_tva_rd51')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd20'),
                                          ref('account_tax_report_ro_tva_rd201')],
            }),
        ]"/>
    </record>

    <record id="tvati_intrap19b" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">63</field>
        <field name="name">TVA Intracomunitara Achizitii 19% - Bunuri</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_19_eu"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd5'),
                                         ref('account_tax_report_ro_baza_rd51'),
                                         ref('account_tax_report_ro_baza_rd20'),
                                         ref('account_tax_report_ro_baza_rd201')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd5'),
                                          ref('account_tax_report_ro_tva_rd51')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd20'),
                                         ref('account_tax_report_ro_tva_rd201')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd5'),
                                          ref('account_tax_report_ro_baza_rd51'),
                                          ref('account_tax_report_ro_baza_rd20'),
                                          ref('account_tax_report_ro_baza_rd201')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd5'),
                                         ref('account_tax_report_ro_tva_rd51')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd20'),
                                          ref('account_tax_report_ro_tva_rd201')],
            }),
        ]"/>
    </record>

    <record id="tvati_intrap0s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">70</field>
        <field name="name">TVA Intracomunitara Achizitii 0% - Servicii</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_0_eu"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd30')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record id="tvati_intrap5s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">69</field>
        <field name="name">TVA Intracomunitara Achizitii 5% - Servicii</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_5_eu"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd7'),
                                         ref('account_tax_report_ro_baza_rd71'),
                                         ref('account_tax_report_ro_baza_rd22'),
                                         ref('account_tax_report_ro_baza_rd221')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd7'),
                                          ref('account_tax_report_ro_tva_rd71')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd22'),
                                         ref('account_tax_report_ro_tva_rd221')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd7'),
                                          ref('account_tax_report_ro_baza_rd71'),
                                          ref('account_tax_report_ro_baza_rd22'),
                                          ref('account_tax_report_ro_baza_rd221')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd7'),
                                         ref('account_tax_report_ro_tva_rd71')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd22'),
                                          ref('account_tax_report_ro_tva_rd221')],
            }),
        ]"/>
    </record>

    <record id="tvati_intrap9s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">68</field>
        <field name="name">TVA Intracomunitara Achizitii 9% - Servicii</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_9_eu"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd7'),
                                         ref('account_tax_report_ro_baza_rd71'),
                                         ref('account_tax_report_ro_baza_rd22'),
                                         ref('account_tax_report_ro_baza_rd221')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd7'),
                                          ref('account_tax_report_ro_tva_rd71')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd22'),
                                         ref('account_tax_report_ro_tva_rd221')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd7'),
                                          ref('account_tax_report_ro_baza_rd71'),
                                          ref('account_tax_report_ro_baza_rd22'),
                                          ref('account_tax_report_ro_baza_rd221')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd7'),
                                         ref('account_tax_report_ro_tva_rd71')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd22'),
                                          ref('account_tax_report_ro_tva_rd221')],
            }),
        ]"/>
    </record>

    <record id="tvati_intrap19s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">67</field>
        <field name="name">TVA Intracomunitara Achizitii 19% - Servicii</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_19_eu"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd7'),
                                         ref('account_tax_report_ro_baza_rd71'),
                                         ref('account_tax_report_ro_baza_rd22'),
                                         ref('account_tax_report_ro_baza_rd221')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd7'),
                                          ref('account_tax_report_ro_tva_rd71')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd22'),
                                         ref('account_tax_report_ro_tva_rd221')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd7'),
                                          ref('account_tax_report_ro_baza_rd71'),
                                          ref('account_tax_report_ro_baza_rd22'),
                                          ref('account_tax_report_ro_baza_rd221')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd7'),
                                         ref('account_tax_report_ro_tva_rd71')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4427'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd22'),
                                          ref('account_tax_report_ro_tva_rd221')],
            }),
        ]"/>
    </record>

    <!-- Import -->
    <record id="tvati_extrap0b" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">66</field>
        <field name="name">TVA Import Bunuri</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_scutit"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd7'),
                                         ref('account_tax_report_ro_baza_rd71'),
                                         ref('account_tax_report_ro_baza_rd22'),
                                         ref('account_tax_report_ro_baza_rd221')]
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd7'),
                                          ref('account_tax_report_ro_baza_rd71'),
                                          ref('account_tax_report_ro_baza_rd22'),
                                          ref('account_tax_report_ro_baza_rd221')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record id="tvati_extrap0s" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">70</field>
        <field name="name">TVA Import Servicii</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_scutit"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd7'),
                                         ref('account_tax_report_ro_baza_rd71'),
                                         ref('account_tax_report_ro_baza_rd22'),
                                         ref('account_tax_report_ro_baza_rd221')]
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd7'),
                                          ref('account_tax_report_ro_baza_rd71'),
                                          ref('account_tax_report_ro_baza_rd22'),
                                          ref('account_tax_report_ro_baza_rd221')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [],
            }),
        ]"/>
    </record>

    <record id="tva_ned_5_50" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">72</field>
        <field name="name">TVA 5% Nedeductibil 50%</field>
        <field name="description">TVA 5% Nedeductibil 50%</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_ned"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd262')],
            }),
            (0,0, {
                'factor_percent': -50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd262')],
            }),
            (0,0, {
                'factor_percent': 50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_6352'),
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd262')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd262')],
            }),
            (0,0, {
                'factor_percent': -50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd262')],
            }),
            (0,0, {
                'factor_percent': 50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_6352'),
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd262')],
            }),
        ]"/>
    </record>

    <record id="tva_ned_9_50" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">71</field>
        <field name="name">TVA 9% Nedeductibil 50%</field>
        <field name="description">TVA 9% Nedeductibil 50%</field>
        <field name="amount">9</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_ned"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd252')],
            }),
            (0,0, {
                'factor_percent': -50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd252')],
            }),
            (0,0, {
                'factor_percent': 50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_6352'),
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd252')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd252')],
            }),
            (0,0, {
                'factor_percent': -50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd252')],
            }),
            (0,0, {
                'factor_percent': 50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_6352'),
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd252')],
            }),
        ]"/>
    </record>

    <record id="tva_ned_19_50" model="account.tax.template">
        <field name="chart_template_id" ref="ro_chart_template"/>
        <field name="sequence">70</field>
        <field name="name">TVA 19% Nedeductibil 50%</field>
        <field name="description">TVA 19% Nedeductibil 50%</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="tax_group_tva_ned"/>
        <field name="invoice_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_ro_baza_rd242')],
            }),
            (0,0, {
                'factor_percent': -50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd242')],
            }),
            (0,0, {
                'factor_percent': 50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_6352'),
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd242')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5,0,0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_ro_baza_rd242')],
            }),
            (0,0, {
                'factor_percent': -50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'plus_report_line_ids': [ref('account_tax_report_ro_tva_rd242')],
            }),
            (0,0, {
                'factor_percent': 50,
                'repartition_type': 'tax',
                'account_id': ref('pcg_6352'),
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('pcg_4426'),
                'minus_report_line_ids': [ref('account_tax_report_ro_tva_rd242')],
            }),
        ]"/>
    </record>

</odoo>
