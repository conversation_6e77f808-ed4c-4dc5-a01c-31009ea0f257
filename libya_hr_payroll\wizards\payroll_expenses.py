# -*- coding: utf-8 -*-

from odoo import fields, models
from dateutil.relativedelta import relativedelta
from datetime import date, datetime
# import datetime


class CreateWizard(models.TransientModel):
    _name = 'payroll.expenses'
    _description = 'Payroll Expenses'

    currency_id = fields.Many2one('res.currency', default=lambda self: self.env.user.company_id.currency_id)
    date_from = fields.Date("Date From",default=lambda self: fields.Date.to_string(date.today().replace(day=1)),required=True)
    date_to = fields.Date("Date To",default=lambda self: fields.Date.to_string((datetime.now() + relativedelta(months=+1, day=1, days=-1)).date()),required=True)
    bank_id = fields.Many2one('res.bank',"Bank Name",required=True)
    branch_id = fields.Many2one('bank.branch',"Branch Name",domain="[('bank_id','=',bank_id)]",required=True)
    employee_id = fields.Many2one('hr.employee',"Employee")


    def generate_payroll_expenses(self):
        data = {'model': self._name,
                'ids': self.ids,
                'date_from': self.date_from,
                'date_to': self.date_to,
                'currency_id':self.currency_id.id,
                'bank_id':self.bank_id.id,
                'bank_name':self.bank_id.name,
                'branch_id':self.branch_id.id,
                'branch_name':self.branch_id.name,
                'employee_id':self.employee_id.id}
        return self.env.ref('libya_hr_payroll.report_payroll_libya_expenses_all_pdf').report_action(self,data=data)


class PayrollExpensesReport_x1(models.AbstractModel):
    _name = 'report.libya_hr_payroll.report_libya_expenses_all'
    _description = 'Payroll Expenses'

    def _get_report_values(self, docids, data=None):
        if data['employee_id']:
            exist_payslip = self.env['hr.payslip'].search(
                [('date_from', '>=', data['date_from']), ('date_to', '<=', data['date_to']),
                 ('employee_id.bank_account_id.bank_id', '=', data['bank_id']),
                 ('employee_id.bank_account_id.bank_branch', '=', data['branch_id']),('employee_id','=',data['employee_id'])],limit=1)
        else:
            exist_payslip = self.env['hr.payslip'].search([('date_from','>=',data['date_from']),('date_to','<=',data['date_to']),('employee_id.bank_account_id.bank_id', '=', data['bank_id']),('employee_id.bank_account_id.bank_branch', '=',data['branch_id'])])
        date_month = datetime.strptime(data['date_from'],'%Y-%m-%d')

        payslip_ids = []
        amount = 0
        for payslip in exist_payslip:
            payslip_ids.append({
                'employee_name': payslip.employee_id.name,
                'employee_number': payslip.employee_id.national_number,
                'employee_location': '/',
                # 'employee_location': payslip.employee_id.work_location.name,
                'employee_bank_acc': payslip.employee_id.bank_account_id.acc_number,
                'net_salary': payslip.line_ids.filtered(lambda line: line.code == 'NETSALARY').total,

            })
            amount += payslip.line_ids.filtered(lambda line: line.code == 'NETSALARY').total

        currency = self.env['res.currency'].search([('id','=',data['currency_id'])])

        value = currency.amount_to_text(amount)
        return {
            'payslip_ids': payslip_ids,
            'bank_name': data['bank_name'],
            'branch_name': data['branch_name'],
            'date_month': date_month.month,
            'date_year': date_month.year,
            'amount': value
        }