<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_acquirer_form" model="ir.ui.view">
        <field name="name">Authorize.Net Acquirer Form</field>
        <field name="model">payment.acquirer</field>
        <field name="inherit_id" ref="payment.payment_acquirer_form"/>
        <field name="arch" type="xml">
            <xpath expr='//group[@name="acquirer"]' position='inside'>
                <group attrs="{'invisible': [('provider', '!=', 'authorize')]}">
                    <field name="authorize_login" attrs="{'required':[('provider', '=', 'authorize'), ('state', '!=', 'disabled')]}"/>
                    <field name="authorize_transaction_key" password="True" attrs="{'required':[ ('provider', '=', 'authorize'), ('state', '!=', 'disabled')]}"/>
                    <field name="authorize_signature_key" password="True" attrs="{'required':[ ('provider', '=', 'authorize'), ('state', '!=', 'disabled')]}"/>
                    <label for="authorize_client_key"/>
                    <div>
                        <field name="authorize_client_key" password="True"/>
                        <button class="oe_link" icon="fa-refresh" type="object"
                                name="action_update_merchant_details"
                                string="Generate Client Key"/>
                    </div>
                    <a colspan="2" href="https://www.odoo.com/documentation/15.0/applications/general/payment_acquirers/authorize.html" target="_blank">
                        How to get paid with Authorize.Net
                    </a>
                </group>
            </xpath>
            <field name="display_as" position="before">
                <field name="authorize_payment_method_type"
                       attrs="{'invisible': [('provider', '!=', 'authorize')], 'required':[('provider', '=', 'authorize'), ('state', '!=', 'disabled')]}"/>
            </field>
            <xpath expr="//field[@name='country_ids']" position="after">
                <label for="authorize_currency_id" string="Currency" attrs="{'invisible': [('provider', '!=', 'authorize')]}"/>
                <div attrs="{'invisible': [('provider', '!=', 'authorize')]}">
                    <field name="authorize_currency_id"/>
                    <button class="oe_link" icon="fa-refresh" type="object"
                            name="action_update_merchant_details"
                            string="Set Account Currency"/>
                </div>
            </xpath>
        </field>
    </record>

    <record id="payment_token_form" model="ir.ui.view">
        <field name='name'>Authorize.Net Token Form</field>
        <field name='model'>payment.token</field>
        <field name="inherit_id" ref="payment.payment_token_form"/>
        <field name="arch" type="xml">
            <xpath expr='//field[@name="acquirer_ref"]' position='after'>
                <field name="provider" invisible="1"/>
                <field name="authorize_profile" attrs="{'invisible':[('provider', '!=', 'authorize')]}"/>
                <field name="authorize_payment_method_type" attrs="{'invisible': [('provider', '!=', 'authorize')]}"/>
            </xpath>
        </field>
    </record>

</odoo>
