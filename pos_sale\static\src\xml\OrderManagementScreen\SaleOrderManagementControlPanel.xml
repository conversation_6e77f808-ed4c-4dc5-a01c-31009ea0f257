<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="SaleOrderManagementControlPanel" owl="1">
        <div class="control-panel">
            <div class="item button back" t-on-click="trigger('close-screen')">
                <i class="fa fa-angle-double-left"></i>
                <span t-if="!env.isMobile"> Back</span>
            </div>
            <div class="item search-box">
                <span class="icon">
                    <i class="fa fa-search" />
                </span>
                <input type="text" t-model="orderManagementContext.searchString" t-on-keydown="onInputKeydown" placeholder="E.g. customer: Steward, date: 2020-05-09" />
                <span class="clear" t-on-click="trigger('clear-search')">
                    <i class="fa fa-remove" />
                </span>
            </div>
            <div t-if="showPageControls" class="item">
                <div class="page-controls">
                    <div class="previous" t-on-click="trigger('prev-page')">
                        <i class="fa fa-fw fa-caret-left" role="img" aria-label="Previous Order List" title="Previous Order List"></i>
                    </div>
                    <div class="next" t-on-click="trigger('next-page')">
                        <i class="fa fa-fw fa-caret-right" role="img" aria-label="Next Order List" title="Next Order List"></i>
                    </div>
                </div>
                <div class="page">
                    <span><t t-esc="pageNumber" /></span>
                </div>
            </div>
            <div t-else="" class="item"></div>
        </div>
    </t>

</templates>
