# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2021
# <PERSON>, 2021
# R<PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Harcogourmet, 2022
# <PERSON><PERSON>, 2022
# Jonatan Gk, 2022
# ma<PERSON><PERSON>, 2022
# Cristian<PERSON>ruz<PERSON>, 2022
# jabiri7, 2022
# <PERSON><PERSON><PERSON> F<PERSON> <<EMAIL>>, 2022
# martioodo hola, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid " days"
msgstr "dies"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid " hours"
msgstr "hores"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important &gt;&lt;/td&gt;"
msgstr "!important &gt;&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important /&gt;"
msgstr "!important /&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important/&gt;"
msgstr "!important/&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 10px\" &gt;"
msgstr "!important; font-size: 10px\" &gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 8px; min-width: 18px\"&gt;"
msgstr "!important; font-size: 8px; min-width: 18px\"&gt;"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s are only valid between %(start)s and %(end)s"
msgstr "%(leave_type)s només són vàlids entre %(start)s i %(end)s"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s are only valid starting from %(date)s"
msgstr "%(leave_type)snomés són vàlids començant des de%(date)s"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s are only valid until %(date)s"
msgstr "%(leave_type)s només són vàlids fins %(date)s"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s: %(duration).2f days (%(start)s)"
msgstr "%(leave_type)s: %(duration).2f dies (%(start)s)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s on %(leave_type)s: %(duration).2f days (%(start)s)"
msgstr "%(person)s en %(leave_type)s: %(duration).2f dies (%(start)s)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s on %(leave_type)s: %(duration).2f hours on %(date)s"
msgstr "%(person)s a %(leave_type)s: %(duration).2f hores %(date)s"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid "%g remaining out of %g"
msgstr "%g remaining out of %g"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (còpia)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%s (from %s to %s)"
msgstr "%s (de %s a %s)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%s (from %s to No Limit)"
msgstr "%s (de %s a Sense límit)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s : %.2f days"
msgstr "%s : %.2f dies"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s : %.2f hours"
msgstr "%s : %.2f hores"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s on Time Off : %.2f day(s)"
msgstr "%s en temps desactivat : %.2f die(s)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s on Time Off : %.2f hour(s)"
msgstr "%s en temps desactivat : %.2f hore(s)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s: Time Off"
msgstr "%s: Temps Desactivat"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&gt;"
msgstr "&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/td&gt;"
msgstr "&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/th&gt;"
msgstr "&lt;/th&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"
msgstr ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;td style=background-color:"
msgstr "&lt;td style=background-color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;th class=\"text-center\" colspan="
msgstr "&lt;th class=\"text-center\" colspan="

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "(based on worked time)"
msgstr "(basat en el temps de treball)"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__10
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__10
msgid "10:00 AM"
msgstr "10:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__22
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__22
msgid "10:00 PM"
msgstr "10:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__10_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__10_5
msgid "10:30 AM"
msgstr "10:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__22_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__22_5
msgid "10:30 PM"
msgstr "10:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__11
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__11
msgid "11:00 AM"
msgstr "11:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__23
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__23
msgid "11:00 PM"
msgstr "11:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__11_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__11_5
msgid "11:30 AM"
msgstr "11:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__23_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__23_5
msgid "11:30 PM"
msgstr "11:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__0
msgid "12:00 AM"
msgstr "12:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__12
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__12
msgid "12:00 PM"
msgstr "12:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__0_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__0_5
msgid "12:30 AM"
msgstr "12:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__12_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__12_5
msgid "12:30 PM"
msgstr "12:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__1
msgid "1:00 AM"
msgstr "1:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__13
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__13
msgid "1:00 PM"
msgstr "1:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__1_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__1_5
msgid "1:30 AM"
msgstr "1:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__13_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__13_5
msgid "1:30 PM"
msgstr "1:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__2
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__2
msgid "2:00 AM"
msgstr "2:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__14
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__14
msgid "2:00 PM"
msgstr "2:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__2_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__2_5
msgid "2:30 AM"
msgstr "2:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__14_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__14_5
msgid "2:30 PM"
msgstr "2:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__3
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__3
msgid "3:00 AM"
msgstr "3:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__15
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__15
msgid "3:00 PM"
msgstr "3:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__3_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__3_5
msgid "3:30 AM"
msgstr "3:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__15_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__15_5
msgid "3:30 PM"
msgstr "3:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__4
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__4
msgid "4:00 AM"
msgstr "4:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__16
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__16
msgid "4:00 PM"
msgstr "4:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__4_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__4_5
msgid "4:30 AM"
msgstr "4:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__16_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__16_5
msgid "4:30 PM"
msgstr "4:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__5
msgid "5:00 AM"
msgstr "5:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__17
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__17
msgid "5:00 PM"
msgstr "5:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__5_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__5_5
msgid "5:30 AM"
msgstr "5:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__17_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__17_5
msgid "5:30 PM"
msgstr "5:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__6
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__6
msgid "6:00 AM"
msgstr "6:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__18
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__18
msgid "6:00 PM"
msgstr "6:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__6_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__6_5
msgid "6:30 AM"
msgstr "6:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__18_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__18_5
msgid "6:30 PM"
msgstr "6:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__7
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__7
msgid "7:00 AM"
msgstr "7:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__19
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__19
msgid "7:00 PM"
msgstr "7:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__7_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__7_5
msgid "7:30 AM"
msgstr "7:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__19_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__19_5
msgid "7:30 PM"
msgstr "7:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__8
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__8
msgid "8:00 AM"
msgstr "8:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__20
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__20
msgid "8:00 PM"
msgstr "8:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__8_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__8_5
msgid "8:30 AM"
msgstr "8:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__20_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__20_5
msgid "8:30 PM"
msgstr "8:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__9
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__9
msgid "9:00 AM"
msgstr "9:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__21
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__21
msgid "9:00 PM"
msgstr "9:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__9_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__9_5
msgid "9:30 AM"
msgstr "9:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__21_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__21_5
msgid "9:30 PM"
msgstr "9:30 PM"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-check\"/> Validate"
msgstr "<i class=\"fa fa-check\"/> Validar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" attrs=\"{'invisible': ['|', ('allocation_type', '=', "
"'accrual'), ('state', 'not in', ('draft', 'confirm'))]}\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" attrs=\"{'invisible': ['|', ('allocation_type', '=', "
"'accrual'), ('state', 'not in', ('draft', 'confirm'))]}\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" attrs=\"{'invisible': [('allocation_type', '=', "
"'accrual')]}\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" attrs=\"{'invisible': [('allocation_type', '=', "
"'accrual')]}\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-thumbs-up\"/> Approve"
msgstr "<i class=\"fa fa-thumbs-up\"/> Aprova"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-times\"/> Refuse"
msgstr "<i class=\"fa fa-times\"/> Rebutjar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span attrs=\"{'invisible': [('holiday_type', '!=', 'employee')]}\">\n"
"                        The employee has a different timezone than yours! Here dates and times are displayed in the employee's timezone\n"
"                    </span>\n"
"                    <span attrs=\"{'invisible': [('holiday_type', '!=', 'department')]}\">\n"
"                        The department's company has a different timezone than yours! Here dates and times are displayed in the company's timezone\n"
"                    </span>\n"
"                    <span attrs=\"{'invisible': [('holiday_type', '!=', 'company')]}\">\n"
"                        The company has a different timezone than yours! Here dates and times are displayed in the company's timezone\n"
"                    </span>\n"
"                    ("
msgstr ""
"<span attrs=\"{'invisible': [('holiday_type', '!=', 'employee')]}\">\n"
"                        L'empleat té una zona horària diferent de la teva! Aquí les dates i les hores es mostren a la zona horària de l'empleat\n"
"                    </span>\n"
"                    <span attrs=\"{'invisible': [('holiday_type', '!=', 'department')]}\">\n"
"                        La companyia del departament té una zona horària diferent de la teva! Aquí les dates i les hores es mostren a la zona horària de l'empresa\n"
"                    </span>\n"
"                    <span attrs=\"{'invisible': [('holiday_type', '!=', 'company')]}\">\n"
"                        L'empresa té una zona horària diferent de la teva! Aquí les dates i les hores es mostren a la zona horària de l'empresa\n"
"                    </span>\n"
"                    ("

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_kanban_view_employees_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_kanban_view_public_employees_kanban
msgid ""
"<span class=\"fa fa-plane text-success\" role=\"img\" aria-label=\"Present but on leave\" title=\"Present but on leave\" name=\"presence_absent_active\">\n"
"                    </span>"
msgstr ""
"<span class=\"fa fa-plane text-success\" role=\"img\" aria-label=\"Present but on leave\" title=\"Present but on leave\" name=\"presence_absent_active\">\n"
"                    </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_kanban_view_employees_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_kanban_view_public_employees_kanban
msgid ""
"<span class=\"fa fa-plane text-warning\" role=\"img\" aria-label=\"To "
"define\" title=\"On Leave\"/>"
msgstr ""
"<span class=\"fa fa-plane text-warning\" role=\"img\" aria-label=\"To "
"define\" title=\"On Leave\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<span class=\"ml8\" attrs=\"{'invisible': [('type_request_unit', '=', 'hour')]}\">Days</span>\n"
"                                <span class=\"ml8\" attrs=\"{'invisible': [('type_request_unit', '!=', 'hour')]}\">Hours</span>"
msgstr ""
"<span class=\"ml8\" attrs=\"{'invisible': [('type_request_unit', '=', 'hour')]}\">Dies</span>\n"
"                                <span class=\"ml8\" attrs=\"{'invisible': [('type_request_unit', '!=', 'hour')]}\">Hores</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Time Off\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Absències\n"
"                                </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Off Till\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Tall\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Time Off\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                           Temps desactivat\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Accruals</span>"
msgstr "<span class=\"o_stat_text\">Controls</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Allocations</span>"
msgstr "<span class=\"o_stat_text\">Assignacions</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Time Off</span>"
msgstr "<span class=\"o_stat_text\">Temps Desactivat</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span class=\"oe_inline\" attrs=\"{'invisible': ['|', ('request_unit_half', '=', True), ('request_unit_hours', '=', True)]}\">\n"
"                                    From\n"
"                                </span>"
msgstr ""
"<span class=\"oe_inline\" attrs=\"{'invisible': ['|', ('request_unit_half', '=', True), ('request_unit_hours', '=', True)]}\">\n"
"                                    De\n"
"                                </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span class=\"oe_inline\" attrs=\"{'invisible': ['|', ('request_unit_half', '=', True), ('request_unit_hours', '=', True)]}\">\n"
"                                    To\n"
"                                </span>"
msgstr ""
"<span class=\"oe_inline\" attrs=\"{'invisible': ['|', ('request_unit_half', '=', True), ('request_unit_hours', '=', True)]}\">\n"
"                                    A\n"
"                                </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\">from </span>"
msgstr "<span class=\"text-muted\">de</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\">to </span>"
msgstr "<span class=\"text-muted\">fins </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "<span>Days</span>"
msgstr "<span>Dies</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "<strong>Departments and Employees</strong>"
msgstr "<strong>Departaments i empleats</strong>"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""
"Una gran manera de seguir el seguiment dels PTO dels empleats, els dies "
"malalts i l'estat d'aprovació."

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid ""
"A great way to keep track on your time off requests, sick days, and approval"
" status."
msgstr ""
"Una gran manera de seguir el temps lliure de sol·licituds, dies malalts i "
"estat d'aprovació."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "A time off cannot be duplicated."
msgstr "No es pot duplicar el temps lliure."

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "AVAILABLE"
msgstr "DISPONIBLE"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__show_leaves
msgid "Able to see Remaining Time Off"
msgstr "Capaç de veure el temps restant "

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid "Absence"
msgstr "Absència"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__absence_of_today
msgid "Absence by Today"
msgstr "Absències per avui"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid ""
"Absent Employee(s), Whose time off requests are either confirmed or "
"validated on today"
msgstr ""
"Empleat(s) absents, la sol·licitud de les absències està validada o "
"confirmada avui"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_employee_action_from_department
msgid "Absent Employees"
msgstr "Empleats absents"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__is_absent
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "Absent Today"
msgstr "Absent avui"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__accrual
msgid "Accrual Allocation"
msgstr "Planificació d'assignacions"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Accrual Allocations"
msgstr "Planificació d'assignacions"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Accrual Level"
msgstr "Nivell d'ingrés"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_plan
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_plan_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__accrual_plan_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Accrual Plan"
msgstr "Pla d'ingrés"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_level
msgid "Accrual Plan Level"
msgstr "Nivell del pla de l'arxiu"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#, python-format
msgid "Accrual Plan's Employees"
msgstr "Empleats del Pla d'Accrual"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_accrual_plans
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_accrual_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_tree
msgid "Accrual Plans"
msgstr "Plans d'Accrual"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_allocation_cron_accrual_ir_actions_server
#: model:ir.cron,cron_name:hr_holidays.hr_leave_allocation_cron_accrual
#: model:ir.cron,name:hr_holidays.hr_leave_allocation_cron_accrual
msgid "Accrual Time Off: Updates the number of time off"
msgstr "Temps d'espera: Actualitza el nombre de temps d'espera"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accruals_ids
msgid "Accruals"
msgstr "Accruals"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accrual_count
msgid "Accruals count"
msgstr "Comptador d'accessoris"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "Action Needed"
msgstr "Cal fer alguna acció"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__active_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__active
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__active_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__active_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__active
msgid "Active"
msgstr "Actiu"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Allocations"
msgstr "Assignacions actives"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__active_employee
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Employee"
msgstr "Treballador actiu"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Active Time Off"
msgstr "Absències actives"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Types"
msgstr "Tipus actius"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_ids
msgid "Activities"
msgstr "Activitats"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoració de l'activitat d'excepció"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.mail_activity_type_action_config_hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipus d'activitats"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Add a new level"
msgstr "Afegeix un nivell nou"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Add a reason..."
msgstr "Afegir un motiu..."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value_type
msgid "Added Value Type"
msgstr "Tipus de valor afegit"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Adds"
msgstr "Afegeix"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_manager
msgid "Administrator"
msgstr "Administrador"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__end_of_accrual
msgid "After this accrual's period"
msgstr "Després d'aquest període d'acumulació"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__pm
msgid "Afternoon"
msgstr "Tarda"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_all
msgid "All Allocations"
msgstr "Totes les assignacions"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_dashboard
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_approve_department
msgid "All Time Off"
msgstr "Tot el Temps desactivat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__duration_display
msgid "Allocated (Days/Hours)"
msgstr "Assignat (Dies/Hores)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_allocation_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__allocation_ids
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__allocation
#: model:mail.message.subtype,name:hr_holidays.mt_leave_allocation
msgid "Allocation"
msgstr "Assignació"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_approval
msgid "Allocation Approval"
msgstr "Aprovació d'assignacions"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__private_name
msgid "Allocation Description"
msgstr "Descripció de l'assignació"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_display
msgid "Allocation Display"
msgstr "Visualització de l'assignació"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_type
msgid "Allocation Mode"
msgstr "Mode d'assignació"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_notif_subtype_id
msgid "Allocation Notification Subtype"
msgstr "Subtipus de notificació d'assignació"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: model:mail.message.subtype,description:hr_holidays.mt_leave_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#, python-format
msgid "Allocation Request"
msgstr "Petició d'assignació"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Allocation Requests"
msgstr "Peticions d'assignació"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_second_approval
msgid "Allocation Second Approve"
msgstr "Segona aprovació per l'assignació"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__allocation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Allocation Type"
msgstr "Tipus d'assignació"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_used_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_used_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_used_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_used_display
msgid "Allocation Used Display"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Allocation of %(allocation_name)s : %(duration).2f %(duration_type)s to "
"%(person)s"
msgstr ""
"Assignació de %(allocationname)s : %(duration).2f %(durationtype)s a "
"%(person)s"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__week_day
msgid "Allocation on"
msgstr "Assignació activada"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Allocation request must be confirmed in order to approve it."
msgstr "La sol·licitud d'assignació s'ha de confirmar per aprovar-la."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Allocation request must be confirmed or validated in order to refuse it."
msgstr ""
"La sol·licitud d'assignació s'ha de confirmar o validar per rebutjar-la."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Allocation request must be in Draft state (\"To Submit\") in order to "
"confirm it."
msgstr ""
"La sol·licitud d'assignació ha d'estar en l'estat d'Esbós («Enviar») per tal"
" de confirmar-ho."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Allocation request state must be \"Refused\" or \"To Approve\" in order to "
"be reset to Draft."
msgstr ""
"L'estat de la sol·licitud d'assignació ha de ser \"Rebutjat\" o \"Aprovar\" "
"per tal de ser reiniciat a Esborrany."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__allocation_to_approve_count
msgid "Allocation to Approve"
msgstr "Assignacions per aprovar"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_approve_department
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_approve_allocations
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_departure_wizard_view_form
msgid "Allocations"
msgstr "Assignacions"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Allow To Join Supporting Document"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__holiday_type
msgid ""
"Allow to create requests in batchs:\n"
"- By Employee: for a specific employee\n"
"- By Company: all employees of the specified company\n"
"- By Department: all employees of the specified department\n"
"- By Employee Tag: all employees of the specific employee group category"
msgstr ""
"Permet crear peticions en massa:\n"
"- Per empleat: per un empleat en concret\n"
"- Per empresa: per tots els empleats de l'empresa seleccionada\n"
"- Per departament: per tots els empleats del departament seleccionat\n"
"- Per etiqueta d'empleat: per tots els empleats que tinguin l'etiqueta seleccionada"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Analyze from"
msgstr "Analitzar des de"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_evaluation_report_graph
msgid "Appraisal Analysis"
msgstr "Anàlisis d'avaluacions"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_validation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Approval"
msgstr "Aprovació"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_approvals
msgid "Approvals"
msgstr "Aprovacions"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Approve"
msgstr "Aprova"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.ir_actions_server_approve_allocations
msgid "Approve Allocations"
msgstr "Aprova les assignacions"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__approved
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Approved"
msgstr "Aprovat"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Approved Allocations"
msgstr "Assignacions aprovades"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Approved Requests"
msgstr "Peticions aprovades"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Approved Time Off"
msgstr "Temps d'inactivitat aprovat"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__officer
msgid "Approved by Time Off Officer"
msgstr "Aprovat per l'Oficial Time Off"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__apr
msgid "April"
msgstr "abril"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_departure_wizard__archive_allocation
msgid "Archive Employee Allocations"
msgstr "Arxiva les assignacions dels empleats"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Archived"
msgstr "Arxivat"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Archived Time Off"
msgstr "Temps arxivat desactivat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__action_with_unused_accruals
msgid "At the end of the calendar year, unused accruals will be"
msgstr "Al final de l'any natural, els ingressos no utilitzats seran"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "At the end of the year, unused accruals will be"
msgstr "A la fi d'any, els ingressos no utilitzats seran"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids
msgid "Attach File"
msgstr "Adjunta un fitxer"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_attachment_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__attachment_ids
msgid "Attachments"
msgstr "Adjunts"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__aug
msgid "August"
msgstr "agost"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Available"
msgstr "Disponible"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/hr_holidays/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Away"
msgstr "Absent"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__is_based_on_worked_time
msgid "Based on worked time"
msgstr "Basat en el temps treballat"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee_base
msgid "Basic Employee"
msgstr "Empleat bàsic"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__black
msgid "Black"
msgstr "Negre"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__blue
msgid "Blue"
msgstr "Blau"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__both
msgid "Both Approved and Confirmed"
msgstr "Aprovats i confirmats"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__brown
msgid "Brown"
msgstr "Marró"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__company
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__company
msgid "By Company"
msgstr "Per empresa"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__department
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__department
msgid "By Department"
msgstr "Per departament"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__employee
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__employee
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__holiday_type__employee
msgid "By Employee"
msgstr "Per empleat"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__category
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__category
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__holiday_type__category
msgid "By Employee Tag"
msgstr "Per etiqueta de l'empleat"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__manager
msgid "By Employee's Approver"
msgstr "Per l'aprenent del treballador"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__both
msgid "By Employee's Approver and Time Off Officer"
msgstr "Per l'aprovador de l'empleat i pel responsable d'absències"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__holiday_type
msgid ""
"By Employee: Allocation/Request for individual Employee, By Employee Tag: "
"Allocation/Request for group of employees in category"
msgstr ""
"Per empleat: peticions/assignacions per a cada empleat individualment.\n"
"Per etiqueta d'empleat: peticions/assignacions per grup de categoria d'empleats."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__hr
msgid "By Time Off Officer"
msgstr "Per temps fora de l'oficina"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_approve
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__can_approve
msgid "Can Approve"
msgstr "Pot aprovar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_reset
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__can_reset
msgid "Can reset"
msgstr "Pot restablir"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Cancel"
msgstr "Cancel·lar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_departure_wizard__cancel_leaves
msgid "Cancel Future Leaves"
msgstr "Cancel·la les fulles futures"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_departure_wizard__cancel_leaves
msgid "Cancel all time off after this date."
msgstr "Cancel·la tot el temps de descans després d'aquesta data."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__cancel
msgid "Cancelled"
msgstr "Cancel·lat"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__category_id
msgid "Category of Employee"
msgstr "Categoria de l'empleat"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Choose a cap for this accrual. 0 means no cap."
msgstr "Trieu una tapa per a aquest ingrés. 0 vol dir sense límit."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__responsible_id
msgid ""
"Choose the Time Off Officer who will be notified to approve allocation or "
"Time Off request"
msgstr ""
"Trieu l'Oficial de Temps d'espera que se li notificarà per aprovar "
"l'assignació o la sol·licitud de Temps d'espera"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Click on the 'Edit' button to add new rules."
msgstr "Feu clic al botó 'Editar' per a afegir regles noves."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__color
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Color"
msgstr "Color"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__color_name
msgid "Color in Report"
msgstr "Color a l'informe"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__company_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Company"
msgstr "Empresa"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__mode_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__mode_company_id
msgid "Company Mode"
msgstr "Mode d'empresa"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_comp
msgid "Compensatory Days"
msgstr "Dies compensatoris"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_configuration
msgid "Configuration"
msgstr "Configuració"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Confirm"
msgstr "Confirmar"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__confirmed
msgid "Confirmed"
msgstr "Confirmada"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Could not find an allocation of type %(leave_type)s for the requested time "
"period."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__icon_id
msgid "Cover Image"
msgstr "Imatge de portada"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
msgid "Create a new time off allocation"
msgstr "Crear una nova assignació d'absències"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid "Create a new time off allocation request"
msgstr "Creeu una nova sol·licitud d'assignació de temps lliure"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_date
msgid "Created on"
msgstr "Creat el"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__current_leave_state
msgid "Current Time Off Status"
msgstr "Estat actual de l'hora desactivada"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_id
msgid "Current Time Off Type"
msgstr "Tipus de temps excedit actual"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Current Year"
msgstr "Any actual"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_hours
msgid "Custom Hours"
msgstr "Hores personalitzades"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "DAYS"
msgstr "DIES"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__daily
msgid "Daily"
msgstr "Diàriament"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_new_request
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_new_request
msgid "Dashboard"
msgstr "Tauler"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from_period
msgid "Date Period Start"
msgstr "Data inicial del període"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__lastcall
msgid "Date of the last accrual allocation"
msgstr "Data de l'última assignació de la meritació"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__nextcall
msgid "Date of the next accrual allocation"
msgstr "Data de la propera assignació planificada"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Dates"
msgstr "Dates"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__day
msgid "Day"
msgstr "Dia"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__days
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
#, python-format
msgid "Days"
msgstr "Dies"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__group_days_allocation
msgid "Days Allocated"
msgstr "Dies assignats"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_custom
msgid "Days-long custom hours"
msgstr "Dies-llargs hores personalitzades"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__dec
msgid "December"
msgstr "Desembre"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
#, python-format
msgid "Delete"
msgstr "Eliminar"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__department_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Department"
msgstr "Departament"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Department search"
msgstr "Cerca de departament"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Assistent de sortida"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__name
msgid "Description"
msgstr "Descripció"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name_validity
msgid "Description with validity"
msgstr "Descripció amb validesa"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Display Option"
msgstr "Mostra l'opció"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_calendar_meeting
msgid "Display Time Off in Calendar"
msgstr "Mostra el temps d'apagat al calendari"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Dropdown menu"
msgstr "Menú desplegable"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__duration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Duration"
msgstr "Durada"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days
msgid "Duration (Days)"
msgstr "Durada (Dies)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days_display
msgid "Duration (days)"
msgstr "Durada (dies)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid "Duration (hours)"
msgstr "Durada (hores)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days_display
msgid "Duration in days"
msgstr "Durada en dies"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days
msgid "Duration in days. Reference field to use when necessary."
msgstr ""
"Durada en dies. Camp de referència per fer servir quan sigui necessari."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours_display
msgid "Duration in hours"
msgstr "Durada en hores"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
msgid "Edit Allocation"
msgstr "Editar assignació"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Edit Time Off"
msgstr "Editar absència"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__employee_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Employee"
msgstr "Empleat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__employee_requests
msgid "Employee Requests"
msgstr "Sol·licituds de treballador"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__category_id
msgid "Employee Tag"
msgstr "Etiqueta de l'empleat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__emp
msgid "Employee(s)"
msgstr "Empleat(s)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__employees_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_ids
msgid "Employees"
msgstr "Empleats"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_to
msgid "End Date"
msgstr "Data de finalització"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__yes
msgid "Extra Days Requests Allowed"
msgstr "Sol·licituds de dies addicionals permeses"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__feb
msgid "February"
msgstr "febrer"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__duration_display
msgid ""
"Field allowing to see the allocation duration in days or hours depending on "
"the type_request_unit"
msgstr ""
"Camp que permet veure la durada de l'assignació en dies o hores segons el "
"tyipe_request_unit"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__duration_display
msgid ""
"Field allowing to see the leave request duration in days or hours depending "
"on the leave_type_request_unit"
msgstr ""
"Camp que permet veure la durada de la sol·licitud de permís en dies o hores,"
" depenent del leave_type_request_unit"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid ""
"Filters only on allocations that belong to an time off type that is 'active'"
" (active field is True)"
msgstr ""
"Filtra només en assignacions que pertanyen a un tipus de temps d'espera que "
"és «actiu» (el camp actiu és cert)"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid ""
"Filters only on requests that belong to an time off type that is 'active' "
"(active field is True)"
msgstr ""
"Filtra només en les sol·licituds que pertanyen a un tipus de temps d'espera "
"que sigui «actiu» (el camp actiu és cert)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__first_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__approver_id
msgid "First Approval"
msgstr "Primera aprovació"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day
msgid "First Day"
msgstr "Primer dia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day_display
msgid "First Day Display"
msgstr "Visualització del primer dia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month
msgid "First Month"
msgstr "Primer mes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day
msgid "First Month Day"
msgstr "Primer mes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day_display
msgid "First Month Day Display"
msgstr "Visualització del primer mes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_follower_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_partner_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: hr_holidays
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""
"El contingut del formulari no és present, aquest informe no es pot imprimir."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__frequency
msgid "Frequency"
msgstr "Freqüència"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__fri
msgid "Friday"
msgstr "Divendres"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__start_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "From"
msgstr "Des de"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_from
msgid "From Date"
msgstr "Des de la data"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "From:"
msgstr "Des de:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Future Activities"
msgstr "Activitats futures"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Group By"
msgstr "Agrupar per"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__group_days_leave
msgid "Group Time Off"
msgstr "Temps d'inactivitat del grup"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "HOURS"
msgstr "HORES"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_approval
msgid "HR Approval"
msgstr "Aprovació de l'HR"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__report_note
msgid "HR Comments"
msgstr "Comentaris de RRHH"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_summary_employee
msgid "HR Time Off Summary Report By Employee"
msgstr "Creeu una nova sol·licitud d'assignació de temps lliure"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_half
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__half_day
msgid "Half Day"
msgstr "Mig dia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_message
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "Has Valid Allocation"
msgstr "Té una assignació vàlida"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_hatched
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_hatched
msgid "Hatched"
msgstr "Ombrejat"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__multi_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__multi_employee
msgid "Holds whether this allocation concerns more than 1 employee"
msgstr "Manté si aquesta assignació afecta a més d'un empleat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__holiday_status
msgid "Holiday Status"
msgstr "Estat de les vacances"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_report_hr_holidays_report_holidayssummary
msgid "Holidays Summary Report"
msgstr "Informe resum de vacances"

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_home_working
#: model:mail.message.subtype,name:hr_holidays.mt_leave_home_working
msgid "Home Working"
msgstr "Treball a casa"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_from
msgid "Hour from"
msgstr "Hora inici"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_to
msgid "Hour to"
msgstr "Hora fins"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__hours
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__hour
#, python-format
msgid "Hours"
msgstr "Hores"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__hr_icon_display
msgid "Hr Icon Display"
msgstr "Visualització d'icones de recursos humans"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__id
msgid "ID"
msgstr "ID"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days_display
msgid ""
"If Accrual Allocation: Number of days allocated in addition to the ones you "
"will get via the accrual' system."
msgstr ""
"Si l'assignació de la meritació: Nombre de dies assignats a més dels que "
"obtindreu a través del sistema de meritació."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid ""
"If Accrual Allocation: Number of hours allocated in addition to the ones you"
" will get via the accrual' system."
msgstr ""
"Si l'assignació de l'ingrés és: Nombre d'hores assignades a més de les que "
"obtindreu a través del sistema d'acumulació."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_unread
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__active_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__active_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_employee_type_report__active_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_report__active_employee
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Si el camp actiu es desmarca, permet ocultar el registre del recurs sense "
"eliminar-ho."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the time off "
"type without removing it."
msgstr ""
"Si el camp actiu està establert a fals, us permetrà ocultar el tipus de "
"temps de desactivació sense eliminar-lo."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__parent_id
msgid "If this field is empty, this level is the first one."
msgstr "Si aquest camp està buit, aquest nivell és el primer."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_duration_check
msgid ""
"If you want to change the number of days you should use the 'period' mode"
msgstr ""
"Si vols canviar el nombre de dies hauries de fer servir el mode 'període'"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__immediately
msgid "Immediately"
msgstr "Immediatament"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid ""
"Immediately: When the date corresponds to the new level, your accrual is automatically computed, granted and you switch to new level\n"
"                After this accrual's period: When the accrual is complete (a week, a month), and granted, you switch to next level if allocation date corresponds"
msgstr ""
"Immediatament: Quan la data correspon al nou nivell, el vostre ingrés es calcula automàticament, es concedeix i canvieu a un nivell nou\n"
"                Després del període d'aquest ingrés: Quan es completi l'ingrés (una setmana, un mes) i es concedeixi, canviareu al nivell següent si la data d'assignació correspon"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "In"
msgstr "Dins"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_is_follower
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_is_follower
msgid "Is Follower"
msgstr "És seguidor"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__is_officer
msgid "Is Officer"
msgstr "és responsable de les vacances"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__unpaid
msgid "Is Unpaid"
msgstr "És impagat"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__ivory
msgid "Ivory"
msgstr "Ivori"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jan
msgid "January"
msgstr "Gener"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__job_id
msgid "Job"
msgstr "Feina"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Job Position"
msgstr "Lloc de treball"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jul
msgid "July"
msgstr "Juliol"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jun
msgid "June"
msgstr "Juny"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid "Keep track of your PTOs."
msgstr "Vigila els teus PTO."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__time_type
msgid "Kind of Leave"
msgstr "Tipus d'absència"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Late Activities"
msgstr "Activitats endarrerides"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lavender
msgid "Lavender"
msgstr "Lavanda"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar_leaves__holiday_id
msgid "Leave Request"
msgstr "Petició d'absència"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_status_id
msgid "Leave Type"
msgstr "Tipus d'absència"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_validation_type
msgid "Leave Validation"
msgstr "Deixa la validació"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__leaves_taken
msgid "Leaves Taken"
msgstr "Fulles agafades"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__left
msgid "Left"
msgstr "Dreta"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Legend"
msgstr "Llegenda"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__level
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_ids
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Level"
msgstr "Nivell"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid "Level Transition"
msgstr "Transició del nivell"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__level
msgid "Level computed through the sequence."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_count
msgid "Levels"
msgstr "Nivells"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightblue
msgid "Light Blue"
msgstr "Blau clar"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightcoral
msgid "Light Coral"
msgstr "Corall clar"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightcyan
msgid "Light Cyan"
msgstr "Cian clar"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightgreen
msgid "Light Green"
msgstr "Verd clar"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightpink
msgid "Light Pink"
msgstr "Rosa clar"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightsalmon
msgid "Light Salmon"
msgstr "Salmó clar"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightyellow
msgid "Light Yellow"
msgstr "Groc clar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Limit of"
msgstr "Límit de"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Limit to"
msgstr "Limita a"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__linked_request_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__linked_request_ids
msgid "Linked Requests"
msgstr "Peticions associades"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__lost
msgid "Lost"
msgstr "Perdut"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__magenta
msgid "Magenta"
msgstr "Magenta"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjunt principal"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__manager_id
msgid "Manager"
msgstr "Director"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_manager_approval
msgid "Manager Approval"
msgstr "Gestor d'aprovació"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__mar
msgid "March"
msgstr "Març"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Mark as Draft"
msgstr "Marca com a esborrany"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__max_leaves
msgid "Max Leaves"
msgstr "Fulles màximes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Max Time Off:"
msgstr "Temps màxim desactivat:"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_leaves
msgid "Maximum Allowed"
msgstr "Màxim permès"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__remaining_leaves
msgid "Maximum Time Off Allowed - Time Off Already Taken"
msgstr "Temps màxim desactivat permès - Temps desactivat ja pres"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid ""
"Maximum Time Off Allowed - Time Off Already Taken - Time Off Waiting "
"Approval"
msgstr ""
"Temps màxim desactivat permès - Temps desactivat ja pres - Temps d'espera "
"d'aprovació"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__may
msgid "May"
msgstr "Maig"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid "Meet the time off dashboard."
msgstr "Coneix el tauler de temps lliure."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__meeting_id
msgid "Meeting"
msgstr "Reunió"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Subtipus del missatge"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Mode"
msgstr "Mode"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__mon
msgid "Monday"
msgstr "Dilluns"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Month"
msgstr "Mes"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__monthly
msgid "Monthly"
msgstr "Mensualment"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__am
msgid "Morning"
msgstr "Matí"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__multi_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__multi_employee
msgid "Multi Employee"
msgstr "Multiempleat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_my
#: model:ir.ui.menu,name:hr_holidays.menu_open_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Allocations"
msgstr "Les meves assignacions"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Department"
msgstr "El meu departament"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "My Requests"
msgstr "Les meves peticions"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Team"
msgstr "El meu equip"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_my
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_my_leaves
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "My Time Off"
msgstr "Les meves absències"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_leaves_tree_inherit
msgid "Name"
msgstr "Nom"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Need Second Approval"
msgstr "Necessita la segona aprovació"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__draft
msgid "New"
msgstr "Nou"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "New %(leave_type)s Request created by %(user)s"
msgstr "Nova petició %(leavetype)s creada per %(user)s"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#, python-format
msgid "New Allocation"
msgstr "Assignació nova"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#, python-format
msgid "New Allocation Request"
msgstr "Nova sol·licitud d'assignació"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"New Allocation Request created by %(user)s: %(count)s Days of "
"%(allocation_type)s"
msgstr ""
"Nova sol·licitud d'assignació creada per %(user)s: %(count)s Dies de "
"%(allocation_type)s"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#, python-format
msgid "New Time Off"
msgstr "Nova petició d'absència"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#, python-format
msgid "New time off"
msgstr "Nova petició d'absència"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_summary
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__no
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No Limit"
msgstr "Sense límit"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__no_validation
msgid "No Validation"
msgstr "Sense validació"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.action_hr_available_holidays_report
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_department
msgid "No data yet!"
msgstr "Encara no hi han dades!"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No limit"
msgstr "Sense límit"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "No rules added"
msgstr "No s'han afegir regles"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__no
msgid "No validation needed"
msgstr "No cal validació"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#, python-format
msgid "None"
msgstr "Cap"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__no
msgid "Not Allowed"
msgstr "No permès"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__nov
msgid "November"
msgstr "Novembre"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours_text
msgid "Number Of Hours Text"
msgstr "Text del nombre d'hores"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Number of Days"
msgstr "Nombre de dies"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leaves_count
msgid "Number of Time Off"
msgstr "Nombre d'absències"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days_display
msgid ""
"Number of days of the time off request according to your working schedule. "
"Used for interface."
msgstr ""
"Nombre de dies del temps d'espera sol·licitat segons la vostra planificació "
"de treball. Utilitzat per a la interfície."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days
msgid ""
"Number of days of the time off request. Used in the calculation. To manually"
" correct the duration, use this field."
msgstr ""
"Nombre de dies de la sol·licitud de desconnexió. S'utilitza en el càlcul. "
"Per a corregir manualment la durada, utilitzeu aquest camp."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_hours_display
msgid ""
"Number of hours of the time off request according to your working schedule. "
"Used for interface."
msgstr ""
"Nombre d'hores del temps d'espera sol·licitat segons la vostra planificació "
"de treball. Utilitzat per a la interfície."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_unread_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de missatges no llegits"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__oct
msgid "October"
msgstr "Octubre"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Off Till"
msgstr "Fora de l'abast"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Off Today"
msgstr "Fora avui"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_absent
msgid "On leave"
msgstr "En sortir"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/hr_holidays/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Online"
msgstr "En línia"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can approve/refuse its own requests."
msgstr ""
"Només un gestor de temps fora pot aprovar/refusar les seves pròpies "
"peticions."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset a refused leave."
msgstr ""
"Només un gestor de temps desconnectat pot restablir un permís rebutjat."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset a started leave."
msgstr ""
"Només un gestor de temps desconnectat pot restablir un permís iniciat."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset other people leaves."
msgstr ""
"Només un gestor de temps desconnectat pot reinicialitzar altres persones."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Only a Time off Approver can apply the second approval on allocation "
"requests."
msgstr ""
"Només un aprovador d'absències pot aplicar la segona validació a les "
"peticions d'assignació."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Only a time off Manager can approve its own requests."
msgstr ""
"Només un gestor de temps lliure pot aprovar les seves pròpies peticions."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Only a time off Manager can reset other people allocation."
msgstr ""
"Només un gestor de temps lliure pot restablir l'assignació d'altres "
"persones."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Only a time off Officer/Responsible or Manager can approve or refuse time "
"off requests."
msgstr ""
"Només els administradors i responsables d'absències poden aprovar o refusar "
"les sol·licituds d'absència. "

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__is_based_on_worked_time
msgid ""
"Only accrue for the time worked by the employee. This is the time when the "
"employee did not take time off."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__other
msgid "Other"
msgstr "Altres"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/hr_holidays/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Out of office"
msgstr "Fora de l'oficina"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/models/partner/partner.js:0
#, python-format
msgid "Out of office until %s"
msgstr "Fora de l'oficina fins a %s"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_dashboard
msgid "Overview"
msgstr "Vista general"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_cl
msgid "Paid Time Off"
msgstr "Absències pagades"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__parent_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__parent_id
msgid "Parent"
msgstr "Pare"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.hr_holiday_status_dv
msgid "Parental Leaves"
msgstr "Absències de paternitat"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Period"
msgstr "Període"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__planned
msgid "Planned"
msgstr "Planificat"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_present
msgid "Present but on leave"
msgstr "Present però amb permís"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__parent_id
msgid "Previous Level"
msgstr "Nivell anterior"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Print"
msgstr "Imprimir"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_public_holiday
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_public_time_off_menu_configuration
msgid "Public Holidays"
msgstr "Festius públics"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
msgid "Public Time Off"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value
msgid "Rate"
msgstr "Taxa"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__notes
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__notes
msgid "Reasons"
msgstr "Raons"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__red
msgid "Red"
msgstr "Vermell"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Refuse"
msgstr "Rebutja"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__refuse
#, python-format
msgid "Refused"
msgstr "Rebutjada"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__regular
msgid "Regular Allocation"
msgstr "Assignació regular"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Usuari relacionat amb el recurs per gestionar-ne el seu accés."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Remaining Days"
msgstr "Dies restants"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__remaining_leaves
msgid "Remaining Paid Time Off"
msgstr "Absències pagades restants"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__remaining_leaves
msgid "Remaining Time Off"
msgstr "Temps restant desactivat"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Remaining leaves"
msgstr "Absències restants"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_departure_wizard__archive_allocation
msgid "Remove employee from existing accrual plans."
msgstr "Elimina l'empleat dels plans de contractació existents."

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_report
msgid "Reporting"
msgstr "Informes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Allocation"
msgstr "Petició d'assignació"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_to
msgid "Request End Date"
msgstr "Data final petició"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from
msgid "Request Start Date"
msgstr "Data inicial petició"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Time off"
msgstr "Absències sol·licitades"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_type
msgid "Request Type"
msgstr "Tipus de petició"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__duration_display
msgid "Requested (Days/Hours)"
msgstr "Sol·licitat (Dies/Hores)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__requires_allocation
msgid "Requires allocation"
msgstr "Requereix assignació"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.resource_calendar_global_leaves_action_from_calendar
msgid "Resource Time Off"
msgstr "Temps de recurs desactivat"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Detall del temps lliure dels recursos"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__responsible_id
msgid "Responsible Time Off Officer"
msgstr "Agent responsable del temps d'espera"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_user_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Rules"
msgstr "Regles"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Run until"
msgstr "Executar fins"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sat
msgid "Saturday"
msgstr "Dissabte"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Search Time Off"
msgstr "Cercar absències"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Search Time Off Type"
msgstr "Tipus de temps per a la cerca"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Search allocations"
msgstr "Cerca assignacions"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__second_approver_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate1
msgid "Second Approval"
msgstr "Segona aprovació"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day
msgid "Second Day"
msgstr "Segon dia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day_display
msgid "Second Day Display"
msgstr "Visualització del segon dia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month
msgid "Second Month"
msgstr "Segon mes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day
msgid "Second Month Day"
msgstr "Segon mes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day_display
msgid "Second Month Day Display"
msgstr "Visualització del segon mes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__holiday_type
msgid "Select Time Off Type"
msgstr "Seleccioneu el tipus de temps d'apagada"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_res_users__leave_manager_id
msgid ""
"Select the user responsible for approving \"Time Off\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""
"Seleccioneu l'usuari responsable d'aprovar «Temps d'apagat» d'aquest empleat.\n"
"Si està buit, l'aprovació la fa un Administrador o Aprovador (determinada a la configuració/usuaris)."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__sep
msgid "September"
msgstr "Setembre"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "Sequence is generated automatically by start time delta."
msgstr "La seqüència es genera automàticament per delta del temps d'inici."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__set
msgid "Set by Time Off Officer"
msgstr "Establert per responsable d'absències"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostra tots els registres en que la data de següent acció és abans d'avui"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_sl
#: model:mail.message.subtype,description:hr_holidays.mt_leave_sick
#: model:mail.message.subtype,name:hr_holidays.mt_leave_sick
msgid "Sick Time Off"
msgstr "Absències per malaltia"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
msgid ""
"Specify if this accrual plan can only be used with this Time Off Type.\n"
"                Leave empty if this accrual plan can be used with any Time Off Type."
msgstr ""
"Especifiqueu si aquest pla d'acumulació només es pot utilitzar amb aquest tipus de temps de desconnexió.\n"
"                Deixeu-ho en blanc si aquest pla d'acumulació es pot utilitzar amb qualsevol tipus de temps de descans."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_from
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Start Date"
msgstr "Data inicial"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__start_count
msgid "Start after"
msgstr "Comença després de"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Starts"
msgstr "Inicia"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Starts immediately after allocation start date"
msgstr "Comença immediatament després de la data d'inici de l'assignació"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__state
msgid "State"
msgstr "Estat/Província"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__state
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Status"
msgstr "Estat"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Sobrepassat: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_striked
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_striked
msgid "Striked"
msgstr "Ratllat"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Such grouping is not allowed."
msgstr "Aquest grup no està permès."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Sum"
msgstr "Suma"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__virtual_leaves_taken
msgid "Sum of validated and non validated time off requests."
msgstr "Suma de sol·licituds de baixades validades i no validades."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sun
msgid "Sunday"
msgstr "Diumenge"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids_count
msgid "Supported Attachment Ids Count"
msgstr "Comptador d'identificadors d'adjunts admesos"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_support_document
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__support_document
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Supporting Document"
msgstr "Document de suport"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Supporting Documents"
msgstr "Documents compatibles"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "TAKEN"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__type_request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__request_unit
msgid "Take Time Off in"
msgstr "Temps d'espera desactivat"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__taken
#, python-format
msgid "Taken"
msgstr "Agafat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__taken_leave_ids
msgid "Taken Leave"
msgstr "Surt agafat"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_count
msgid ""
"The accrual starts after a defined period from the allocation start date. "
"This field defines the number of days, months or years after which accrual "
"is used."
msgstr ""
"L'acumulació comença després d'un període definit a partir de la data "
"d'inici de l'assignació. Aquest camp defineix el nombre de dies, mesos o "
"anys després dels quals s'utilitza l'acumulació."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__color
msgid ""
"The color selected here will be used in every screen with the time off type."
msgstr ""
"El color seleccionat aquí s'utilitzarà en totes les pantalles amb el tipus "
"de temps desactivat."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_check_dates
msgid "The dates you've set up aren't correct. Please check them."
msgstr "Les dates que has configurat no són correctes. Si us plau, revisa'ls."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_allocation_duration_check
msgid "The duration must be greater than 0."
msgstr "La durada ha de ser superior a 0."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_allocation_type_value
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_type_value
msgid ""
"The employee, department, company or employee category of this request is "
"missing. Please make sure that your user login is linked to an employee."
msgstr ""
"Falta l'empleat, departament o categoria d'empleat per aquesta petició. Si "
"us plau, assegura't que el teu usuari està enllaçat a un empleat."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The following employees are not supposed to work during that period:\n"
" %s"
msgstr ""
"Se suposa que els següents empleats no han de treballar durant aquest període\n"
" %s"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__added_value
msgid ""
"The number of hours/days that will be incremented in the specified Time Off "
"Type for every period"
msgstr ""
"El nombre d'hores/dies que s'incrementaran en el tipus de temps especificat"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The number of remaining time off is not sufficient for this time off type.\n"
"Please also check the time off waiting for validation."
msgstr ""
"El nombre de temps restant no és suficient per a aquest tipus de temps desactivat.\n"
"Comproveu també el temps d'espera per a la validació."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"\n"
"The employees that lack allocation days are:\n"
"%s"
msgstr ""
"Els empleats que manquen de dies d'assignació són:\n"
"%s"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check2
msgid "The start date must be anterior to the end date."
msgstr "La data d'inici ha de ser anterior a la data de fi."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""
"L'estat s'estableix com a \"Per enviar\", quan es crea una sol·licitud de temps lliure.\n"
"L'estat és \"Aprovar\", quan l'usuari confirma la sol·licitud de temps lliure.\n"
"L'estat es \"Refused\", quan el gestor rebutja la sol·licitud de temps lliure.\n"
"L'estat és \"aprovat\", quan el gestor aprova la sol·licitud de temps lliure."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__state
msgid ""
"The status is set to 'To Submit', when an allocation request is created.\n"
"The status is 'To Approve', when an allocation request is confirmed by user.\n"
"The status is 'Refused', when an allocation request is refused by manager.\n"
"The status is 'Approved', when an allocation request is approved by manager."
msgstr ""
"L'estat s'estableix a 'Enviar', quan es crea una sol·licitud d'assignació.\n"
"L'estat és 'Aprovar', quan una sol·licitud d'assignació és confirmada per l'usuari.\n"
"L'estat és «Rebutjat», quan el gestor rebutja una sol·licitud d'assignació.\n"
"L'estat és \"Aprovat\", quan una sol·licitud d'assignació és aprovada pel gestor."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "The time off has been automatically approved"
msgstr "El temps d'espera s'ha aprovat automàticament"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__sequence
msgid ""
"The type with the smallest sequence is the default value in time off request"
msgstr ""
"El tipus amb la seqüència menor es el tipus per defecte en la petició "
"d'absències"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"This allocation have already ran once, any modification won't be effective "
"to the days allocated to the employee. If you need to change the "
"configuration of the allocation, cancel and create a new one."
msgstr ""
"Aquesta assignació ja s'ha executat una vegada, qualsevol modificació no "
"serà efectiva en els dies assignats a l'empleat. Si cal canviar la "
"configuració de l'assignació, cancel·la i crea una de nova."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__first_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off"
msgstr ""
"Aquesta àrea s'omple automàticament per l'usuari que valida l'absència"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__second_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off with"
" second level (If time off type need second validation)"
msgstr ""
"Aquest espai es emplenat automàticament per l'usuari que valida l'absència "
"en segon nivell (si el tipus d'absència necessita segona validació)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__approver_id
msgid ""
"This area is automatically filled by the user who validates the allocation"
msgstr ""
"Aquest espai es emplenat automàticament per l'usuari que valida l'absència"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__color_name
msgid ""
"This color will be used in the time off summary located in Reporting > Time "
"off by Department."
msgstr ""
"Aquest color serà utilitzat al resum d'absències en Llistat > Absències per "
"Departament."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_type
msgid "This field defines the unit of time after which the accrual starts."
msgstr ""
"Aquest camp defineix la unitat de temps després del qual s'inicia "
"l'acumulació."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "This indicates if it is still possible to use this type of leave"
msgstr "Això indica si encara és possible fer servir aquest tipus d'absència"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "This modification is not allowed in the current state."
msgstr "Aquesta modificació no està permesa en l'estat actual."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__leaves_taken
msgid ""
"This value is given by the sum of all time off requests with a negative "
"value."
msgstr ""
"Aquest valor ve donat per la suma de totes les peticions d'absència amb "
"valor negatiu."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_leaves
msgid ""
"This value is given by the sum of all time off requests with a positive "
"value."
msgstr ""
"Aquest valor ve donat per la suma de totes les peticions d'absència amb "
"valor positiu."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__thu
msgid "Thursday"
msgstr "Dijous"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_holiday_allocation_id
#: model:ir.model,name:hr_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_manager_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__request
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__leave
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_root
#: model:ir.ui.menu,name:hr_holidays.menu_open_department_leave_approve
#: model:mail.message.subtype,name:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_departure_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
msgid "Time Off"
msgstr "Absències"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#, python-format
msgid "Time Off : %s"
msgstr "Absències : %s"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "Assignació d'absències"

#. module: hr_holidays
#: code:addons/hr_holidays/report/hr_leave_employee_type_report.py:0
#: code:addons/hr_holidays/report/hr_leave_report.py:0
#: model:ir.actions.act_window,name:hr_holidays.action_hr_available_holidays_report
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_department
#, python-format
msgid "Time Off Analysis"
msgstr "Anàlisi de temps excedit"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_approval
msgid "Time Off Approval"
msgstr "Temps de desaprovació"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_tree_inherit_leave
msgid "Time Off Approver"
msgstr "Aprovador d'absències"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr "Calendari d'absències"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_employee.py:0
#, python-format
msgid "Time Off Dashboard"
msgstr "Temps fora del tauler"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__private_name
msgid "Time Off Description"
msgstr "Descripció de l'hora de desconnexió"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_notif_subtype_id
msgid "Time Off Notification Subtype"
msgstr "Subtipus de notificació per apagar el temps"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_user
msgid "Time Off Officer"
msgstr "Responsable d'absències"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid ""
"Time Off Officers allocate time off days to employees (e.g. paid time off).<br>\n"
"                Employees request allocations to Time Off Officers (e.g. recuperation days)."
msgstr ""
"El temps d'espera dels oficials assigna temps d'espera als empleats (p. ex. temps de pagament).<br>\n"
"                Els empleats sol·liciten assignacions per a l'Oficial Time Off (p. ex. dies de recuperació)."

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my_request
#: model:mail.message.subtype,description:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_employee_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#, python-format
msgid "Time Off Request"
msgstr "Petició d'absència"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Time Off Requests"
msgstr "Peticions d'absència"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_responsible
msgid "Time Off Responsible"
msgstr "Temps d'apagat responsable"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_second_approval
msgid "Time Off Second Approve"
msgstr "Temps d'apagada de la segona aproximació"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_summary_employee
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary2
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_pivot
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Time Off Summary"
msgstr "Resum d'absències"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_employee_type_report
#: model:ir.model,name:hr_holidays.model_hr_leave_report
msgid "Time Off Summary / Report"
msgstr "Resum / informe d'absències"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Time Off Taken:"
msgstr "Temps d'espera ocupat:"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off Type"
msgstr "Tipus de temps desactivat"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_holiday_status
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_status_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Time Off Types"
msgstr "Tipus d'absències"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off of Your Team Member"
msgstr "Temps d'espera del membre de l'equip"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__leave_to_approve_count
msgid "Time Off to Approve"
msgstr "Absències per a aprovar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Time Off."
msgstr "Absències."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leaves_taken
msgid "Time off Already Taken"
msgstr "Temps d'apagat ja ocupat"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.act_hr_employee_holiday_request
msgid "Time off Analysis"
msgstr "Anàlisi de temps exhaurit"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_holidays_by_employee_and_type_report
msgid "Time off Analysis by Employee and Time Off Type"
msgstr "Temps de desconnexió per treballador i tipus de desactivació"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Time off Taken/Total Allocated"
msgstr "Temps de descans/Total assignat"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time off of people you are manager of"
msgstr "Absències del personal que gestioneu"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request must be confirmed (\"To Approve\") in order to approve it."
msgstr ""
"Per aprovar-la cal confirmar el temps d'espera de la sol·licitud "
"(\"Aprovar\")."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Time off request must be confirmed in order to approve it."
msgstr "La petició d'absència ha de confirmar-se per a poder ser aprovada"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Time off request must be confirmed or validated in order to refuse it."
msgstr ""
"Per rebutjar-ho cal confirmar o validar el temps d'espera de la sol·licitud."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request must be in Draft state (\"To Submit\") in order to confirm "
"it."
msgstr ""
"El temps de desconnexió ha de ser en estat d'Esbós («Enviar») per tal de "
"confirmar-ho."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request state must be \"Refused\" or \"To Approve\" in order to be "
"reset to draft."
msgstr ""
"L'estat de la sol·licitud ha de ser \"Rebutjat\" o \"Aprovat\" per tal de "
"ser reiniciat a esborrany."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__tz
msgid "Timezone"
msgstr "Zona horària"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__stop_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "To"
msgstr "Fins a"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__confirm
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#, python-format
msgid "To Approve"
msgstr "Per aprovar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_date_to
msgid "To Date"
msgstr "Fins la data"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__draft
msgid "To Submit"
msgstr "Per publicar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "To:"
msgstr "Per:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Today Activities"
msgstr "Activitats d'avui"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocations_count
msgid "Total number of allocations"
msgstr "Nombre total d'assignacions"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_count
msgid "Total number of days allocated."
msgstr "Nombre total de dies assignats."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_used_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_used_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_used_count
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_used_count
msgid "Total number of days off used"
msgstr "Nombre total de dies lliures usats"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__remaining_leaves
msgid ""
"Total number of paid time off allocated to this employee, change this value "
"to create allocation/time off request. Total based on all the time off types"
" without overriding limit."
msgstr ""
"Nombre total de temps de pagament assignat a aquest empleat, canvieu aquest "
"valor per crear una sol·licitud d'assignació/temps de baixa. Total basat en "
"tots els tipus de temps inactiu sense límit absolut."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__postponed
msgid "Transferred to the next year"
msgstr "Transferit a l'any següent"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__tue
msgid "Tuesday"
msgstr "Dimarts"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__bimonthly
msgid "Twice a month"
msgstr "Dues vegades al mes"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__biyearly
msgid "Twice a year"
msgstr "Dues vegades a l'any"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Type"
msgstr "Tipus"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz
msgid "Tz"
msgstr "Tz"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz_mismatch
msgid "Tz Mismatch"
msgstr "Incoherència Tz"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_unpaid
msgid "Unpaid"
msgstr "Impagat"

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_unpaid
#: model:mail.message.subtype,name:hr_holidays.mt_leave_unpaid
msgid "Unpaid Time Off"
msgstr "Absències no pagades"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_unread
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_unread
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Unread Messages"
msgstr "Missatges pendents de llegir"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_unread_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Comptador de missatges no llegits"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__user_id
msgid "User"
msgstr "Usuari"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#, python-format
msgid "User is away"
msgstr "L'usuari està absent"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#, python-format
msgid "User is online"
msgstr "L' usuari és en línia"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#, python-format
msgid "User is out of office"
msgstr "L'usuari està fora de l'oficina"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_users
msgid "Users"
msgstr "Usuaris"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Validate"
msgstr "Validar"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Validated"
msgstr "Validat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__validation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__validation_type
msgid "Validation Type"
msgstr "Tipus de validació"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Validity Period"
msgstr "Període de validesa"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Start"
msgstr "Inici de validesa"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Stop"
msgstr "Aturada de la validesa"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__violet
msgid "Violet"
msgstr "Violeta"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid "Virtual Remaining Time Off"
msgstr "Temps restant virtual desactivat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__virtual_leaves_taken
msgid "Virtual Time Off Already Taken"
msgstr "El temps virtual ja està ocupat"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__confirm
msgid "Waiting Approval"
msgstr "Esperant aprovació"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate1
msgid "Waiting Second Approval"
msgstr "Esperant segona arovació"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Waiting for Approval"
msgstr "S'està esperant l'aprovació"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__wed
msgid "Wednesday"
msgstr "Dimecres"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__weekly
msgid "Weekly"
msgstr "Setmanalment"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__wheat
msgid "Wheat"
msgstr "Groc palla"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__time_type
msgid ""
"Whether this should be computed as a holiday or as work time (eg: formation)"
msgstr ""
"Indica si s'ha de comptar com vacances o temps de treball (exemple: "
"formació)"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__yearly
msgid "Yearly"
msgstr "Anualment"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day
msgid "Yearly Day"
msgstr "Dia anual"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day_display
msgid "Yearly Day Display"
msgstr "Mostra el dia anual"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_month
msgid "Yearly Month"
msgstr "Mes anual"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__yes
msgid "Yes"
msgstr "Sí"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You can not have 2 time off that overlaps on the same day."
msgstr "No es pot tenir 2 temps lliure que es sobreposi el mateix dia."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You can not set 2 time off that overlaps on the same day for the same "
"employee."
msgstr ""

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_start_count_check
msgid "You can not start an accrual in the past."
msgstr "En el passat no es pot començar un ingrés."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You cannot delete a time off which is in %s state"
msgstr "No podeu esborrar una absència en estat %s "

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You cannot delete a time off which is in the past"
msgstr "No podeu suprimir un temps d'espera que és en el passat"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "You cannot delete an allocation request which is in %s state."
msgstr ""
"No podeu suprimir una sol·licitud d'assignació que estigui en estat %s."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You cannot delete an allocation request which has some validated leaves."
msgstr ""
"No podeu suprimir una sol·licitud d'assignació que tingui algunes fulles "
"validades."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You cannot first approve a time off for %s, because you are not his time off"
" manager"
msgstr ""
"No podeu aprovar primer un temps d'espera per a %s, perquè no sou el seu "
"gestor de temps d'espera"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You don't have the rights to apply second approval on a time off request"
msgstr ""
"No teniu drets per aplicar la segona aprovació en una sol·licitud de "
"desconnexió temporal"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You have several allocations for those type and period.\n"
"Please split your request to fit in their number of days."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You must be %s\\'s Manager to approve this leave"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You must be either %s's manager or Time off Manager to approve this leave"
msgstr ""
"Heu de ser el gestor de %s o el gestor de temps per aprovar aquest permís"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You must be either %s's manager or time off manager to approve this time off"
msgstr ""

#. module: hr_holidays
#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_added_value_greater_than_zero
msgid "You must give a rate greater than 0 in accrual plan levels."
msgstr ""
"Heu d'indicar una taxa superior a 0 en els nivells dels plans d'acumulació."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You must have manager rights to modify/validate a time off that already "
"begun"
msgstr ""
"Heu de tenir drets de gestor per modificar/validar un temps de baixa que ja "
"s'ha començat"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Your %(leave_type)s planned on %(date)s has been accepted"
msgstr "S'ha acceptat el vostre %(leavetype)s planificat el %(date)s"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Your %(leave_type)s planned on %(date)s has been refused"
msgstr "S'ha rebutjat el vostre %(leavetype)s planificat el %(date)s"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "after allocation date"
msgstr "després de la data d'assignació"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "after allocation start date"
msgstr "després de la data d'inici de l'assignació"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "and on the"
msgstr "i al"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_available_holidays_report_tree
msgid "by Employee"
msgstr "per empleat"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_report_employee_time_off
msgid "by Employee and Time Off Type"
msgstr "per empleat i tipus d'absència"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_summary_all
msgid "by Type"
msgstr "per tipus"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "day of the month"
msgstr "dia del mes"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__day
#, python-format
msgid "day(s)"
msgstr "Dia/es"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
#, python-format
msgid "days"
msgstr "dies"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "days of the months"
msgstr "dies dels mesos"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "e.g. Time Off type (From validity start to validity end / no limit)"
msgstr ""
"P. ex. Tipus de temps d'apagat (Des de l'inici de la validesa fins al final "
"de la validesa / sense límit)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "hours"
msgstr "hores"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#, python-format
msgid "in"
msgstr "en"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
#, python-format
msgid "last day"
msgstr "últim dia"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "lost"
msgstr "perdut"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__month
msgid "month(s)"
msgstr "mesos"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "of"
msgstr "de"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "of the"
msgstr "de"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "of the month"
msgstr "del mes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on"
msgstr "en"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on the"
msgstr "a"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "postponed"
msgstr "posposat"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "refused"
msgstr "rebutjat"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "sequence"
msgstr "seqüència"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "to"
msgstr "fins"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "validate"
msgstr "validar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "validated"
msgstr "validat"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__year
msgid "year(s)"
msgstr "anys"
