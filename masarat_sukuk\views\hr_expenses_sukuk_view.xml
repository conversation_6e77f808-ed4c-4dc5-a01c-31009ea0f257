<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="hr_expense_view_form_xx1" model="ir.ui.view">
        <field name="name">hr.expense.form.extend</field>
        <field name="model">hr.expense</field>
        <field name="inherit_id" ref="hr_expense.hr_expense_view_form"/>
        <field name="arch" type="xml">
            <field name="account_id" position="replace">
                <field name="account_id" options="{'no_create': True}" domain="[('internal_type', '=', 'other'), ('company_id', '=', company_id)]" groups="account.group_account_readonly" attrs="{'readonly': [('is_editable', '=', False)],'invisible':[('state','not in',('approved','done'))]}" context="{'default_company_id': company_id}"/>
                <field name="payment_method" attrs="{'required':[('state','in',('approved','done'))],'invisible':[('state','not in',('approved','done'))]}"/>
                <field name="suke_id" invisible="1"/>
            </field>
            <field name="currency_id" position="replace">
                <field name="currency_id" groups="base.group_multi_currency" attrs="{'invisible':[('state','not in',('approved','done'))]}"/>
            </field>
        </field>
    </record>
</odoo>
