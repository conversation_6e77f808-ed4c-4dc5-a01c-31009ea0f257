# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* analytic
# 
# Translators:
# <PERSON><PERSON><PERSON> <david.machak<PERSON>@gmail.com>, 2021
# <PERSON><PERSON> <skhmal<PERSON><PERSON>@uglt.org>, 2021
# <PERSON>, 2021
# Temur, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Georgian (https://app.transifex.com/odoo/teams/41243/ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction
msgid "Action Needed"
msgstr "საჭიროა ქმედება"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__active
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__active
msgid "Active"
msgstr "აქტიური"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.action_account_analytic_account_form
#: model_terms:ir.actions.act_window,help:analytic.action_analytic_account_form
msgid "Add a new analytic account"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_tag_action
msgid "Add a new tag"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Amount"
msgstr "სულ"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_account
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__account_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__account_id
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Analytic Account"
msgstr "ანალიტიკური ანგარიში"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_distribution
msgid "Analytic Account Distribution"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_group_action
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_group_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_group_tree_view
msgid "Analytic Account Groups"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_res_config_settings__group_analytic_accounting
#: model:res.groups,name:analytic.group_analytic_accounting
msgid "Analytic Accounting"
msgstr "ანალიტიკური ბუღალტერია"

#. module: analytic
#: model:res.groups,name:analytic.group_analytic_tags
msgid "Analytic Accounting Tags"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_account_analytic_account_form
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__analytic_distribution_ids
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Analytic Accounts"
msgstr ""

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_group
msgid "Analytic Categories"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__active_analytic_distribution
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
msgid "Analytic Distribution"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_graph
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_pivot
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Analytic Entries"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Analytic Entry"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action_entries
msgid "Analytic Items"
msgstr ""

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_line
msgid "Analytic Line"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__line_ids
msgid "Analytic Lines"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__name
msgid "Analytic Tag"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_tag_action
#: model:ir.model,name:analytic.model_account_analytic_tag
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_tree_view
msgid "Analytic Tags"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_view_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Archived"
msgstr "დაარქივებული"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Associated Partner"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_attachment_count
msgid "Attachment Count"
msgstr "დანართების რაოდენობა"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__balance
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Balance"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_kanban
msgid "Balance:"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__category
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Category"
msgstr "კატეგორია"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_account_form
msgid "Chart of Analytic Accounts"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__children_ids
msgid "Childrens"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_group_action
msgid "Click to add a new analytic account group."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__color
msgid "Color Index"
msgstr "ფერის ინდექსი"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__company_id
msgid "Company"
msgstr "კომპანია"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__complete_name
msgid "Complete Name"
msgstr "სრული სახელი"

#. module: analytic
#: model:ir.model,name:analytic.model_res_config_settings
msgid "Config Settings"
msgstr "კონფიგურაციის პარამეტრები"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"საზომი ერთეულების გარდაქმნა მოხდება, მხოლოდ ერთი კატეგორიის ერთეულებს შორის."
" გარდაქმნა მოხდება ერთეულებს შორის არსებული თანაფარდობების შესაბამისად."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Costs will be created automatically when you register supplier\n"
"                invoices, expenses or timesheets."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__create_uid
msgid "Created by"
msgstr "შემქმნელი"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__create_date
msgid "Created on"
msgstr "შექმნის თარიღი"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__credit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Credit"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__currency_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__currency_id
msgid "Currency"
msgstr "ვალუტა"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__partner_id
msgid "Customer"
msgstr "კლიენტი"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__date
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Date"
msgstr "თარიღ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__debit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Debit"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__description
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__name
msgid "Description"
msgstr "აღწერა"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__display_name
msgid "Display Name"
msgstr "სახელი"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_follower_ids
msgid "Followers"
msgstr "მიმდევრები"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action
msgid "Gross Margin"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__group_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__group_id
msgid "Group"
msgstr "ჯგუფი"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Group By..."
msgstr "დაჯგუფება"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__has_message
msgid "Has Message"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__id
msgid "ID"
msgstr "იდენტიფიკატორი/ID"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__active
msgid ""
"If the active field is set to False, it will allow you to hide the account "
"without removing it."
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"In Odoo, sales orders and projects are implemented using\n"
"                analytic accounts. You can track costs and revenues to analyse\n"
"                your margins easily."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag____last_update
msgid "Last Modified on"
msgstr "ბოლოს განახლებულია"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__write_uid
msgid "Last Updated by"
msgstr "ბოლოს განაახლა"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__write_date
msgid "Last Updated on"
msgstr "ბოლოს განახლდა"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_ids
msgid "Messages"
msgstr "მესიჯები"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__name
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Name"
msgstr "სახელი"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid "No activity yet"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
msgid "No activity yet on this account"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_line__category__other
msgid "Other"
msgstr "სხვა"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__parent_id
msgid "Parent"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__parent_path
msgid "Parent Path"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__tag_id
msgid "Parent tag"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "პარტნიორი"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__percentage
msgid "Percentage"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__unit_amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Quantity"
msgstr "რაოდენობა"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__code
msgid "Reference"
msgstr "წყარო"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Revenues will be created automatically when you create customer\n"
"                invoices. Customer invoices can be created based on sales orders\n"
"                (fixed price invoices), on timesheets (based on the work done) or\n"
"                on expenses (e.g. reinvoicing of travel costs)."
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Search Analytic Lines"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_view_search
msgid "Search Analytic Tags"
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_tag__active
msgid "Set active to false to hide the Analytic Tag without removing it."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__tag_ids
msgid "Tags"
msgstr "ტეგები"

#. module: analytic
#: model:ir.model.constraint,message:analytic.constraint_account_analytic_distribution_check_percentage
msgid ""
"The percentage of an analytic distribution should be between 0 and 100."
msgstr ""

#. module: analytic
#: code:addons/analytic/models/analytic_account.py:0
#, python-format
msgid ""
"The selected account belongs to another company than the one you're trying "
"to create an analytic item for"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_group_action
msgid "This allows you to classify your analytic accounts."
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Total"
msgstr "ჯამი"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_id
msgid "Unit of Measure"
msgstr "საზომი ერთეული"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_unread
msgid "Unread Messages"
msgstr "წაუკითხავი შეტყობინებები"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_category_id
msgid "UoM Category"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__user_id
msgid "User"
msgstr "მომხმარებელი"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "e.g. Project XYZ"
msgstr ""
