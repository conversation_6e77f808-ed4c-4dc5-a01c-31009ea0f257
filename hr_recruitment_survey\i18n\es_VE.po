# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_recruitment_survey
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Spanish (Venezuela) (https://www.transifex.com/odoo/teams/41243/es_VE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_VE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.recruitment_1_3_1
msgid "0-15"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.recruitment_1_3_2
msgid "16-20"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.recruitment_1_3_3
msgid "21-30"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.recruitment_1_3_4
msgid "31-40"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.recruitment_1_3_5
msgid "41-50"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.recruitment_1_3_6
msgid "51-60"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.recruitment_1_3_7
msgid "61-70"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.recruitment_1_3_8
msgid "71+"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.crm_case_form_view_job_inherit
msgid ""
"<span class=\"o_stat_text\">Print</span>\n"
"                        <span class=\"o_stat_text\">Interview</span>"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.crm_case_form_view_job_inherit
msgid ""
"<span class=\"o_stat_text\">Start</span>\n"
"                        <span class=\"o_stat_text\">Interview</span>"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.recruitment_2_4
msgid "Activities"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.crm_case_form_view_job_inherit
msgid "Answer related job question"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_applicant
msgid "Applicant"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.page,title:hr_recruitment_survey.recruitment_1
msgid "Basic information"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_applicant_survey_id
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_job_survey_id
msgid ""
"Choose an interview form for this job position and you will be able to "
"print/answer this interview from all applicants who apply for this job"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_4
msgid "Desk space"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_job_survey_inherit
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Display Interview Form"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_11
msgid "Dress code"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.recruitment_2_2
msgid "Education"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.page,title:hr_recruitment_survey.recruitment_2
msgid "Education and Activities"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.recruitment_2_3
msgid "Experience"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.recruitment_1_2_2
msgid "Female"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_8
msgid "Freebies such as tea, coffee and stationery"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.recruitment_1_1
msgid "From which university will you graduate?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_2
msgid "Getting on with colleagues"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_7
msgid "Good management"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_1
msgid "Good pay"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_13
msgid "Good social life"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,comments_message:hr_recruitment_survey.recruitment_1_1
#: model:survey.question,comments_message:hr_recruitment_survey.recruitment_1_2
#: model:survey.question,comments_message:hr_recruitment_survey.recruitment_1_3
#: model:survey.question,comments_message:hr_recruitment_survey.recruitment_2_1
#: model:survey.question,comments_message:hr_recruitment_survey.recruitment_2_2
#: model:survey.question,comments_message:hr_recruitment_survey.recruitment_2_3
#: model:survey.question,comments_message:hr_recruitment_survey.recruitment_2_4
#: model:survey.question,comments_message:hr_recruitment_survey.recruitment_3_1
msgid "If other, please specify:"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.page,title:hr_recruitment_survey.recruitment_3
msgid "Importance"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rcol_3_1_3
msgid "Important"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_job_survey_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Interview Form"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.res_config_settings_view_form
msgid "Interview Forms"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_job
msgid "Job Position"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.recruitment_2_1
msgid "Knowledge"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.recruitment_1_2_1
msgid "Male"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rcol_3_1_5
msgid "Most important"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "No Interview Form"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_10
msgid "No out of hours working"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rcol_3_1_1
msgid "Not important"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_3
msgid "Office environment"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_6
msgid "Office location"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_9
msgid "Perks such as free parking, gym passes"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.crm_case_form_view_job_inherit
msgid "Print interview report"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.recruitment_3_1
msgid "Rate the Importance"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.survey,title:hr_recruitment_survey.recruitment_form
msgid "Recruitment Form"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_12
msgid "Regular meetings"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant_response_id
msgid "Response"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rcol_3_1_2
msgid "Somewhat important"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rrow_2_1_5
msgid "State of the art technology"
msgstr ""

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant_survey_id
msgid "Survey"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,validation_error_msg:hr_recruitment_survey.recruitment_1_1
#: model:survey.question,validation_error_msg:hr_recruitment_survey.recruitment_1_2
#: model:survey.question,validation_error_msg:hr_recruitment_survey.recruitment_1_3
#: model:survey.question,validation_error_msg:hr_recruitment_survey.recruitment_2_1
#: model:survey.question,validation_error_msg:hr_recruitment_survey.recruitment_2_2
#: model:survey.question,validation_error_msg:hr_recruitment_survey.recruitment_2_3
#: model:survey.question,validation_error_msg:hr_recruitment_survey.recruitment_2_4
#: model:survey.question,validation_error_msg:hr_recruitment_survey.recruitment_3_1
msgid "The answer you entered has an invalid format."
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.survey,description:hr_recruitment_survey.recruitment_form
msgid ""
"This form is intended to help the responsible of a recruitment interview."
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,constr_error_msg:hr_recruitment_survey.recruitment_1_1
#: model:survey.question,constr_error_msg:hr_recruitment_survey.recruitment_1_2
#: model:survey.question,constr_error_msg:hr_recruitment_survey.recruitment_1_3
#: model:survey.question,constr_error_msg:hr_recruitment_survey.recruitment_2_1
#: model:survey.question,constr_error_msg:hr_recruitment_survey.recruitment_2_2
#: model:survey.question,constr_error_msg:hr_recruitment_survey.recruitment_2_3
#: model:survey.question,constr_error_msg:hr_recruitment_survey.recruitment_2_4
#: model:survey.question,constr_error_msg:hr_recruitment_survey.recruitment_3_1
msgid "This question requires an answer."
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.rcol_3_1_4
msgid "Very important"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.recruitment_1_3
msgid "What age group do you belong to?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.recruitment_1_2
msgid "What is your gender?"
msgstr ""
