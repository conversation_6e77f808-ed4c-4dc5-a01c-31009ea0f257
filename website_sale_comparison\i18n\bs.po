# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale_comparison
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_8
msgid "134.7 x 200 x 7.2 mm"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_7
msgid "308 g"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.recommended_product
msgid "<i class=\"fa fa-exchange\"/> Compare"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<i class=\"fa fa-shopping-cart\"/>&amp;nbsp;Add to Cart"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.recommended_product
msgid "<span class=\"h3\">Suggested alternatives: </span>"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>Price:</strong>"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_1
msgid "Apple"
msgstr ""

#. module: website_sale_comparison
#: model:ir.actions.act_window,name:website_sale_comparison.product_attribute_category_action
#: model:ir.ui.menu,name:website_sale_comparison.menu_attribute_category_action
msgid "Attribute Categories"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_1
msgid "Brand"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute__category_id
msgid "Category"
msgstr "Kategorija"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__name
msgid "Category Name"
msgstr "Naziv kategorije"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:8
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:20
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
#, python-format
msgid "Compare"
msgstr ""

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/js/website_sale_comparison.js:51
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#, python-format
msgid "Compare Products"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_8
msgid "Dimensions"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__id
msgid "ID"
msgstr "ID"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:7
#, python-format
msgid "Product"
msgstr "Proizvod"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute
msgid "Product Attribute"
msgstr "Atribut proizvoda"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute_category
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attribute_category_tree_view
msgid "Product Attribute Category"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_template
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Product image"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Remove"
msgstr "Ukloni"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: website_sale_comparison
#: model:ir.model.fields,help:website_sale_comparison.field_product_attribute__category_id
msgid ""
"Set a category to regroup similar attributes under the same section in the "
"Comparison page of eCommerce"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Shop Comparator"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "Specifications for"
msgstr ""

#. module: website_sale_comparison
#: code:addons/website_sale_comparison/controllers/main.py:23
#: code:addons/website_sale_comparison/models/website_sale_comparison.py:30
#, python-format
msgid "Uncategorized"
msgstr "Nekategorisano"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:15
#, python-format
msgid "Warning"
msgstr "Upozorenje"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_7
msgid "Weight"
msgstr "Težina"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:15
#, python-format
msgid "You can compare max 4 products."
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "or"
msgstr "ili"
