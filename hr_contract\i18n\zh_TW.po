# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-02 15:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_count
msgid "# Contracts"
msgstr "# 契約"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_resource_calendar__contracts_count
msgid "# Contracts using it"
msgstr "套用合約數目"

#. module: hr_contract
#: code:addons/hr_contract/report/hr_contract_history.py:0
#, python-format
msgid "%s's Contracts History"
msgstr "%s的契約歷程"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('calendar_mismatch', '=', False), ('state', '!=', 'open')]}\" class=\"fa fa-exclamation-triangle text-danger o_calendar_warning pl-3\">\n"
"                                    </span>\n"
"                                    <span class=\"o_calendar_warning_tooltip text-danger\">\n"
"                                        Calendar Mismatch : The employee's calendar does not match this contract's calendar. This could lead to unexpected behaviors.\n"
"                                    </span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('calendar_mismatch', '=', False), ('state', '!=', 'open')]}\" class=\"fa fa-exclamation-triangle text-danger o_calendar_warning pl-3\">\n"
"                                    </span><span class=\"o_calendar_warning_tooltip text-danger\">日曆不匹配：員工的日曆與此契約的日曆不匹配。這可能會導致意外行為。</span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span attrs=\"{'invisible': [('calendar_mismatch', '=', False)]}\" class=\"fa fa-exclamation-triangle text-danger o_calendar_warning pl-3\">\n"
"                            </span>\n"
"                            <span class=\"o_calendar_warning_tooltip text-danger\">\n"
"                                Calendar Mismatch : The employee's calendar does not match its current contract calendar. This could lead to unexpected behaviors.\n"
"                            </span>"
msgstr ""
"<span attrs=\"{'invisible': [('calendar_mismatch', '=', False)]}\" class=\"fa fa-exclamation-triangle text-danger o_calendar_warning pl-3\">\n"
"                            </span>\n"
"                            <span class=\"o_calendar_warning_tooltip text-danger\">日曆不匹配 ：員工的日曆與其當前契約日曆不匹配。這可能會導致意外的行為。</span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span class=\"o_stat_text text-danger\" attrs=\"{'invisible' : [('contract_warning', '=', False)]}\" title=\"In Contract Since\">\n"
"                                    In Contract Since\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text text-danger\" attrs=\"{'invisible' : [('contract_warning', '=', False)]}\" title=\"In Contract Since\">\n"
"                                    在契約中\n"
"                                </span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span class=\"o_stat_text text-danger\">\n"
"                                    Contracts\n"
"                                </span>"
msgstr "<span class=\"o_stat_text text-danger\">契約</span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span class=\"o_stat_text text-success\" attrs=\"{'invisible' : "
"[('contract_warning', '=', True)]}\" title=\"In Contract Since\"> In "
"Contract Since</span>"
msgstr ""
"<span class=\"o_stat_text text-success\" attrs=\"{'invisible' : "
"[('contract_warning', '=', True)]}\" title=\"In Contract Since\">在契約中</span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/ 月</span>"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction
msgid "Action Needed"
msgstr "需採取行動"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__active
msgid "Active"
msgstr "啟用"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__active_employee
msgid "Active Employee"
msgstr "有效員工"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Active Employees"
msgstr "有效員工"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_ids
msgid "Activities"
msgstr "活動"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常圖示"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_state
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: hr_contract
#: model:res.groups,name:hr_contract.group_hr_contract_manager
msgid "Administrator"
msgstr "管理員"

#. module: hr_contract
#: code:addons/hr_contract/models/hr_contract.py:0
#, python-format
msgid ""
"An employee can only have one contract at the same time. (Excluding Draft and Cancelled contracts).\n"
"\n"
"Employee: %(employee_name)s"
msgstr ""
"一名員工只能同時擁有一份契約。 （不包括草稿和取消的契約）。\n"
"\n"
"員工：%(employee_name)s"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Archived"
msgstr "歸檔"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_attachment_count
msgid "Attachment Count"
msgstr "附件數"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_res_users__bank_account_id
msgid "Bank Account Number"
msgstr "銀行帳戶號碼"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__calendar_mismatch
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__calendar_mismatch
msgid "Calendar Mismatch"
msgstr "日曆不匹配"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__cancel
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__cancel
msgid "Cancelled"
msgstr "已取消"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__company_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__company_id
msgid "Company"
msgstr "公司"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__vehicle
#: model:ir.model.fields,field_description:hr_contract.field_res_users__vehicle
msgid "Company Vehicle"
msgstr "公司汽車"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__company_country_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__company_country_id
msgid "Company country"
msgstr "公司所屬的國家"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_departure_wizard_view_form
msgid "Contract"
msgstr "契約"

#. module: hr_contract
#: code:addons/hr_contract/models/hr_contract.py:0
#, python-format
msgid ""
"Contract %(contract)s: start date (%(start)s) must be earlier than contract "
"end date (%(end)s)."
msgstr "契約 %(contract)s：開始日期 (%(start)s) 必須早於契約結束日期 (%(end)s)。"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contracts_count
msgid "Contract Count"
msgstr "契約統計"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Details"
msgstr "契約詳細資訊"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_departure_wizard_view_form
msgid "Contract End Date"
msgstr "契約結束日期"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Contract History"
msgstr "契約歷程"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Contract Name"
msgstr "契約名稱"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Reference"
msgstr "契約編號"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Start Date"
msgstr "契約開始日期"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract_type
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contract_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_type_id
msgid "Contract Type"
msgstr "契約類型"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contract_wage
msgid "Contract Wage"
msgstr "契約薪資"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_warning
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_employee_view_search
msgid "Contract Warning"
msgstr "契約通知"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_pending
#: model:mail.message.subtype,description:hr_contract.mt_department_contract_pending
msgid "Contract about to expire"
msgstr "契約即將到期"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_close
msgid "Contract expired"
msgstr "契約到期"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract_history
msgid "Contract history"
msgstr "契約歷程"

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_department_contract_pending
msgid "Contract to Renew"
msgstr "需續簽的契約"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.action_hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_view_form_action
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_ids
#: model:ir.ui.menu,name:hr_contract.hr_menu_contract_history
#: model:ir.ui.menu,name:hr_contract.menu_human_resources_configuration_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_list
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_activity
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract.resource_calendar_view_form
msgid "Contracts"
msgstr "契約"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_to_review_view_list_action
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Contracts to Review"
msgstr "待審查的契約"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__under_contract_state
msgid "Contractual Status"
msgstr "契約狀態"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__country_id
msgid "Country"
msgstr "國家"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__country_code
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__country_code
msgid "Country Code"
msgstr "國家/地區代碼"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Create"
msgstr "建立"

#. module: hr_contract
#: model_terms:ir.actions.act_window,help:hr_contract.action_hr_contract
msgid "Create a new contract"
msgstr "建立一個新契約"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__create_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__create_uid
msgid "Created by"
msgstr "創立者"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__create_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__create_date
msgid "Created on"
msgstr "建立於"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__currency_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__currency_id
msgid "Currency"
msgstr "幣別"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Current Contract"
msgstr "目前契約"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Current Contracts"
msgstr "當前契約"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_employee__contract_id
msgid "Current contract of the employee"
msgstr "員工當前契約"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Currently Under Contract"
msgstr "目前契約中"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__default_resource_calendar_id
msgid "Default Working Hours"
msgstr "預設工作時間"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Delete"
msgstr "刪除"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__department_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__department_id
msgid "Department"
msgstr "部門"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "離職作業"

#. module: hr_contract
#: code:addons/hr_contract/wizard/hr_departure_wizard.py:0
#, python-format
msgid ""
"Departure date can't be earlier than the start date of current contract."
msgstr "離職日期不能早於當前契約的開始日期。"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Dropdown menu"
msgstr "下拉選單"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Edit Contract"
msgstr "編輯契約"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Employed"
msgstr "受僱"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__employee_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Employee"
msgstr "員工"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_ids
msgid "Employee Contracts"
msgstr "工作契約"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Employee Information"
msgstr "員工資訊"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_res_users__bank_account_id
msgid "Employee bank salary account"
msgstr "員工銀行薪資帳戶"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__wage
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__wage
msgid "Employee's monthly gross wage."
msgstr "員工月薪資總額。"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_view_list_action
msgid "Employees"
msgstr "員工"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_end
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_end
msgid "End Date"
msgstr "結束日期"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__date_end
msgid "End date of the contract (if it's a fixed-term contract)."
msgstr "契約終止日期（如果是固定期限契約）。"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__trial_date_end
msgid "End date of the trial period (if there is one)."
msgstr "試用期結束日期（如果有的話）。"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__trial_date_end
msgid "End of Trial Period"
msgstr "試用期結束"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__close
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__close
#: model:mail.message.subtype,name:hr_contract.mt_contract_close
msgid "Expired"
msgstr "到期"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__first_contract_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__first_contract_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_public__first_contract_date
msgid "First Contract Date"
msgstr "首次契約日期"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示，例如，fa-task"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Future Activities"
msgstr "未來活動"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__done
msgid "Green"
msgstr "綠色"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__normal
msgid "Grey"
msgstr "灰色"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Group By"
msgstr "分組依據"

#. module: hr_contract
#: model:ir.actions.server,name:hr_contract.ir_cron_data_contract_update_state_ir_actions_server
#: model:ir.cron,cron_name:hr_contract.ir_cron_data_contract_update_state
#: model:ir.cron,name:hr_contract.ir_cron_data_contract_update_state
msgid "HR Contract: update state"
msgstr "工作契約：更新狀態"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__hr_responsible_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__hr_responsible_id
msgid "HR Responsible"
msgstr "人力資源負責人"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_hired
msgid "Hire Date"
msgstr "聘用日期"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__id
msgid "ID"
msgstr "ID"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "用於指示異常活動的圖示。"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_unread
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__is_under_contract
msgid "Is Currently Under Contract"
msgstr "是目前契約中"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__job_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__job_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Job Position"
msgstr "工作職缺"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__kanban_state
msgid "Kanban State"
msgstr "看板狀態"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract____last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history____last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type____last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__write_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__write_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Late Activities"
msgstr "逾期活動"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_ids
msgid "Messages"
msgstr "訊息"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Monthly Wage"
msgstr "每月薪資"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止時間"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__name
msgid "Name"
msgstr "名稱"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__draft
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__draft
msgid "New"
msgstr "新建"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活動截止日期"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_type_id
msgid "Next Activity Type"
msgstr "下一活動類型"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "No Contracts"
msgstr "無契約"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Not Running"
msgstr "未執行"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__under_contract_state__blocked
msgid "Not Under Contract"
msgstr "未簽約"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__notes
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Notes"
msgstr "備註"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要處理的消息數量"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_unread_counter
msgid "Number of unread messages"
msgstr "未讀訊息的數量"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__hr_responsible_id
msgid "Person responsible for validating the employee's contracts."
msgstr "負責驗證員工契約的人員。"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee_public
msgid "Public Employee"
msgstr "公開員工"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__blocked
msgid "Red"
msgstr "紅色"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_list
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Reference Working Time"
msgstr "參考工作時間"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_employee_public_view_form
msgid "Related User"
msgstr "相關使用者"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_resource_calendar
msgid "Resource Working Time"
msgstr "資源工作時間"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_user_id
msgid "Responsible User"
msgstr "責任使用者"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__open
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__open
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Running"
msgstr "執行中"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Running Contracts"
msgstr "執行中契約"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Salary Information"
msgstr "薪資資訊"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__structure_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__structure_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Salary Structure Type"
msgstr "薪酬結構類型"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Search Contract"
msgstr "搜尋契約"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Search Reference Contracts"
msgstr "搜索參考契約"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_departure_wizard__set_date_end
msgid "Set Contract End Date"
msgstr "設置契約結束日期"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_departure_wizard__set_date_end
msgid "Set the end date on the current contract."
msgstr "設置目前契約的結束日期。"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Show all records which have a next action date before today"
msgstr "顯示下一個操作日期在今天之前的所有記錄"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_start
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_start
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Start Date"
msgstr "開始日期"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__date_start
msgid "Start date of the contract."
msgstr "契約的開始日期。"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__state
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__state
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_employee_public_view_form
msgid "Status"
msgstr "狀態"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_state
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"根據活動的狀態 \n"
" 逾期：已經超過截止日期 \n"
" 現今：活動日期是當天 \n"
" 計劃：未來活動。"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__state
msgid "Status of the contract"
msgstr "契約狀態"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__country_code
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO 國家代碼使用兩個字元。\n"
" 您可以使用此欄位進行快速搜尋。"

#. module: hr_contract
#: code:addons/hr_contract/models/hr_contract.py:0
#, python-format
msgid "The contract of %s is about to expire."
msgstr "%s 的契約即將到期。"

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_contract_pending
msgid "To Renew"
msgstr "待續約"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Today Activities"
msgstr "今天的活動"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記錄的異常活動的類型。"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__under_contract_state__done
msgid "Under Contract"
msgstr "契約中"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_unread
msgid "Unread Messages"
msgstr "未讀消息"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未讀消息計數器"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_res_users
msgid "Users"
msgstr "使用者"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__visa_expire
msgid "Visa Expiration Date"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__visa_no
msgid "Visa No"
msgstr "簽證編號"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__wage
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__wage
msgid "Wage"
msgstr "薪資"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__website_message_ids
msgid "Website Messages"
msgstr "網站信息"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__permit_no
msgid "Work Permit No"
msgstr "工作許可編號"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__resource_calendar_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Working Schedule"
msgstr "工作安排"
