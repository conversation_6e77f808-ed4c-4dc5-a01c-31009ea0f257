# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_fi
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-19 16:10+0000\n"
"PO-Revision-Date: 2020-05-19 16:10+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr "Maksuviitteen tyyppi"

#. module: l10n_fi
#: code:addons/l10n_fi/models/account_partial_reconcile.py:0
#: code:addons/l10n_fi/models/account_partial_reconcile.py:0
#, python-format
msgid "Currency exchange rate difference"
msgstr "Valuuttakurssivaihtoero"

#. module: l10n_fi
#: model:ir.ui.menu,name:l10n_fi.account_reports_fi_statements_menu
msgid "Finland"
msgstr "Suomi"

#. module: l10n_fi
#: model:ir.model.fields.selection,name:l10n_fi.selection__account_journal__invoice_reference_model__fi_rf
msgid "Finnish Creditor Reference (RF)"
msgstr "Kansainvälinen viitenumero (RF)"

#. module: l10n_fi
#: model:ir.model.fields.selection,name:l10n_fi.selection__account_journal__invoice_reference_model__fi
msgid "Finnish Standard Reference"
msgstr "Suomalainen viitenumero"

#. module: l10n_fi
#: code:addons/addons/l10n_fi/models/account_move.py:0
#: code:addons/l10n_fi/models/account_move.py:0
#, python-format
msgid "Invoice number must contain numeric characters"
msgstr "Laskun numeron on sisällettävä numeerisia merkkejä"

#. module: l10n_fi
#: model:ir.model,name:l10n_fi.model_account_journal
msgid "Journal"
msgstr "Päiväkirja"

#. module: l10n_fi
#: model:ir.model,name:l10n_fi.model_account_move
msgid "Journal Entries"
msgstr "Päiväkirjaviennit"

#. module: l10n_fi
#: model:account.tax.group,name:l10n_fi.tax_group_0
msgid "VAT 0%"
msgstr "ALV 0%"

#. module: l10n_fi
#: model:account.tax.group,name:l10n_fi.tax_group_10
msgid "VAT 10%"
msgstr "ALV 10%"

#. module: l10n_fi
#: model:account.tax.group,name:l10n_fi.tax_group_14
msgid "VAT 14%"
msgstr "ALV 14%"

#. module: l10n_fi
#: model:account.tax.group,name:l10n_fi.tax_group_24
msgid "VAT 24%"
msgstr "ALV 24%"

#. module: l10n_fi
#: model:ir.model.fields,help:l10n_fi.field_account_journal__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr "Voit valita erilaisen maksuviitetyypin. Oletus on Odoon laskuviite."
