<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                'xml_id': 'stock.chi_picking_type_in',
                'record': obj().env.ref('stock.stock_warehouse_shop0').in_type_id,
                'noupdate': True,
            }, {
                'xml_id': 'stock.chi_picking_type_out',
                'record': obj().env.ref('stock.stock_warehouse_shop0').out_type_id,
                'noupdate': True,
            }, {
                'xml_id': 'stock.stock_location_shop0',
                'record': obj().env.ref('stock.stock_warehouse_shop0').lot_stock_id,
                'noupdate': True,
            }]"/>
        </function>

        <record id="location_refrigerator_small" model="stock.location">
            <field name="name">Small Refrigerator</field>
            <field name="usage">internal</field>
            <field name="location_id" ref="stock.stock_location_14"/>
            <field name="barcode">WH-SHELF-REF</field>
        </record>

        <record id="product_cable_management_box" model="product.product">
            <field name="default_code">FURN_5555</field>
            <field name="name">Cable Management Box</field>
            <field name="detailed_type">product</field>
            <field name="weight">0.01</field>
            <field name="categ_id" ref="product.product_category_5"/>
            <field name="lst_price">100.0</field>
            <field name="standard_price">70.0</field>
            <field name="weight">1.0</field>
            <field name="tracking">lot</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="image_1920" type="base64" file="stock/static/img/cable_management.png"/>
        </record>

        <record id="lot_product_cable_management" model="stock.production.lot">
            <field name="name">LOT-000001</field>
            <field name="product_id" ref="stock.product_cable_management_box"/>
            <field name="company_id" ref="base.main_company"/>
        </record>

        <record id="lot_product_product_cable_management_box_0" model="stock.production.lot">
            <field name="name">CM-BOX-00001</field>
            <field name="product_id" ref="stock.product_cable_management_box"/>
            <field name="company_id" ref="base.main_company"/>
        </record>

        <record id="lot_product_product_cable_management_box_1" model="stock.production.lot">
            <field name="name">CM-BOX-00002</field>
            <field name="product_id" ref="stock.product_cable_management_box"/>
            <field name="company_id" ref="base.main_company"/>
        </record>

        <record id="stock_inventory_icecream_lot_0" model="stock.quant">
            <field name="product_id" ref="stock.product_cable_management_box"/>
            <field name="inventory_quantity">50.0</field>
            <field name="location_id" ref="stock.stock_location_14"/>
            <field name="lot_id" ref="lot_product_product_cable_management_box_0"/>
        </record>

        <record id="stock_inventory_icecream_lot_1" model="stock.quant">
            <field name="product_id" ref="stock.product_cable_management_box"/>
            <field name="inventory_quantity">40.0</field>
            <field name="location_id" ref="stock.stock_location_14"/>
            <field name="lot_id" ref="lot_product_product_cable_management_box_1"/>
        </record>

        <function model="stock.quant" name="action_apply_inventory">
            <function eval="[[('id', 'in', (ref('stock_inventory_icecream_lot_0'),
                                            ref('stock_inventory_icecream_lot_1')
                                            ))]]" model="stock.quant" name="search"/>
        </function>

        <!-- Create STOCK_MOVE for OUT -->
        <record id="outgoing_shipment_main_warehouse" model="stock.picking">
            <field name="picking_type_id" ref="stock.picking_type_out"/>
            <field name="origin">outgoing shipment</field>
            <field name="user_id"></field>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="scheduled_date" eval="DateTime.today()"/>
            <field name="location_id" ref="stock.stock_location_stock"/>
            <field name="location_dest_id" ref="stock.stock_location_customers"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_27').name,
                'product_id': ref('product.product_product_27'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 15.0,
                'picking_type_id': ref('stock.picking_type_out'),
                'location_id': ref('stock.stock_location_stock'),
                'location_dest_id': ref('stock.stock_location_customers'),
            })]"/>
        </record>

        <record id="outgoing_shipment_main_warehouse1" model="stock.picking">
            <field name="picking_type_id" ref="stock.picking_type_out"/>
            <field name="origin">outgoing shipment</field>
            <field name="user_id"></field>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="scheduled_date" eval="DateTime.today() - timedelta(days=15)"/>
            <field name="location_id" ref="stock.stock_location_stock"/>
            <field name="location_dest_id" ref="stock.stock_location_customers"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_6').name,
                'product_id': ref('product.product_product_6'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 180.0,
                'picking_type_id': ref('stock.picking_type_out'),
                'location_id': ref('stock.stock_location_stock'),
                'location_dest_id': ref('stock.stock_location_customers'),
            })]"/>
        </record>

        <record id="outgoing_shipment_main_warehouse2" model="stock.picking">
            <field name="picking_type_id" ref="stock.picking_type_out"/>
            <field name="origin">outgoing shipment</field>
            <field name="user_id"></field>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="scheduled_date" eval="DateTime.today() - timedelta(days=5)"/>
            <field name="location_id" ref="stock.stock_location_stock"/>
            <field name="location_dest_id" ref="stock.stock_location_customers"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_delivery_02').name,
                'product_id': ref('product.product_delivery_02'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 45.0,
                'picking_type_id': ref('stock.picking_type_out'),
                'location_id': ref('stock.stock_location_stock'),
                'location_dest_id': ref('stock.stock_location_customers'),
            })]"/>
        </record>

        <record id="outgoing_shipment_main_warehouse3" model="stock.picking">
            <field name="picking_type_id" ref="stock.picking_type_out"/>
            <field name="user_id"></field>
            <field name="origin">your company warehouse</field>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="scheduled_date" eval="DateTime.today()"/>
            <field name="location_id" ref="stock.stock_location_stock"/>
            <field name="location_dest_id" ref="stock.stock_location_customers"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_5').name,
                'product_id': ref('product.product_product_5'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 75.0,
                'picking_type_id': ref('stock.picking_type_out'),
                'location_id': ref('stock.stock_location_stock'),
                'location_dest_id': ref('stock.stock_location_customers'),
            })]"/>
        </record>

        <record id="outgoing_shipment_main_warehouse4" model="stock.picking">
            <field name="picking_type_id" ref="stock.picking_type_out"/>
            <field name="origin">outgoing shipment</field>
            <field name="user_id"></field>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="scheduled_date" eval="DateTime.today() - timedelta(days=7)"/>
            <field name="location_id" ref="stock.stock_location_stock"/>
            <field name="location_dest_id" ref="stock.stock_location_customers"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_3').name,
                'product_id': ref('product.product_product_3'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 16.0,
                'picking_type_id': ref('stock.picking_type_out'),
                'location_id': ref('stock.stock_location_stock'),
                'location_dest_id': ref('stock.stock_location_customers'),
            })]"/>
        </record>

        <record id="outgoing_shipment_main_warehouse5" model="stock.picking">
            <field name="picking_type_id" ref="stock.picking_type_out"/>
            <field name="origin">outgoing shipment</field>
            <field name="user_id"></field>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="scheduled_date" eval="DateTime.today() - timedelta(days=12)"/>
            <field name="location_id" ref="stock.stock_location_stock"/>
            <field name="location_dest_id" ref="stock.stock_location_customers"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_3').name,
                'product_id': ref('product.product_product_3'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 40.0,
                'picking_type_id': ref('stock.picking_type_out'),
                'location_id': ref('stock.stock_location_stock'),
                'location_dest_id': ref('stock.stock_location_customers'),
            })]"/>
        </record>

        <record id="outgoing_shipment_main_warehouse6" model="stock.picking">
            <field name="picking_type_id" ref="stock.picking_type_out"/>
            <field name="origin">outgoing shipment</field>
            <field name="user_id"></field>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="scheduled_date" eval="DateTime.today() - timedelta(days=20)"/>
            <field name="location_id" ref="stock.stock_location_stock"/>
            <field name="location_dest_id" ref="stock.stock_location_customers"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_6').name,
                'product_id': ref('product.product_product_6'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 50.0,
                'picking_type_id': ref('stock.picking_type_out'),
                'location_id': ref('stock.stock_location_stock'),
                'location_dest_id': ref('stock.stock_location_customers'),
            })]"/>
        </record>

        <!-- Create STOCK_PICKING for IN -->
        <record id="incomming_shipment" model="stock.picking">
            <field name="user_id"></field>
            <field name="picking_type_id" ref="stock.picking_type_in"/>
            <field name="location_id" ref="stock.stock_location_suppliers"/>
            <field name="location_dest_id" ref="stock.stock_location_stock"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('stock.product_cable_management_box').name,
                'product_id': ref('stock.product_cable_management_box'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 50.0,
                'picking_type_id': ref('stock.picking_type_in'),
                'location_id': ref('stock.stock_location_suppliers'),
                'location_dest_id': ref('stock.stock_location_stock'),
            })]"/>
        </record>

        <record id="incomming_shipment1" model="stock.picking">
            <field name="user_id"></field>
            <field name="picking_type_id" ref="stock.picking_type_in"/>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="scheduled_date" eval="DateTime.today() - timedelta(days=5)"/>
            <field name="location_id" ref="stock.stock_location_suppliers"/>
            <field name="location_dest_id" ref="stock.stock_location_stock"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_delivery_01').name,
                'product_id': ref('product.product_delivery_01'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 35.0,
                'picking_type_id': ref('stock.picking_type_in'),
                'location_id': ref('stock.stock_location_suppliers'),
                'location_dest_id': ref('stock.stock_location_stock'),
            })]"/>
        </record>

        <record id="incomming_shipment2" model="stock.picking">
            <field name="user_id"></field>
            <field name="picking_type_id" ref="stock.picking_type_in"/>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="location_id" ref="stock.stock_location_suppliers"/>
            <field name="location_dest_id" ref="stock.stock_location_stock"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_22').name,
                'product_id': ref('product.product_product_22'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 125.0,
                'picking_type_id': ref('stock.picking_type_in'),
                'location_id': ref('stock.stock_location_suppliers'),
                'location_dest_id': ref('stock.stock_location_stock'),
            })]"/>
        </record>

        <record id="incomming_shipment3" model="stock.picking">
            <field name="user_id"></field>
            <field name="picking_type_id" ref="stock.picking_type_in"/>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="location_id" ref="stock.stock_location_suppliers"/>
            <field name="location_dest_id" ref="stock.stock_location_stock"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_10').name,
                'product_id': ref('product.product_product_10'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 120.0,
                'picking_type_id': ref('stock.picking_type_in'),
                'location_id': ref('stock.stock_location_suppliers'),
                'location_dest_id': ref('stock.stock_location_stock'),
            })]"/>
        </record>

        <record id="incomming_shipment4" model="stock.picking">
            <field name="user_id"></field>
            <field name="picking_type_id" ref="stock.picking_type_in"/>
            <field name="partner_id" ref="base.res_partner_1"/>
            <field name="location_id" ref="stock.stock_location_suppliers"/>
            <field name="location_dest_id" ref="stock.stock_location_stock"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_delivery_02').name,
                'product_id': ref('product.product_delivery_02'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 50.0,
                'picking_type_id': ref('stock.picking_type_in'),
                'location_id': ref('stock.stock_location_suppliers'),
                'location_dest_id': ref('stock.stock_location_stock'),
            })]"/>
        </record>

        <!-- Create STOCK_PICKING_IN for Chicago Warehouse-->
        <record id="incomming_chicago_warehouse" model="stock.picking">
            <field name="picking_type_id" ref="stock.chi_picking_type_in"/>
            <field name="origin">incoming_chicago_warehouse</field>
            <field name="user_id"></field>
            <field name="scheduled_date" eval="DateTime.today()"/>
            <field name="location_id" ref="stock.stock_location_suppliers"/>
            <field name="location_dest_id" ref="stock.stock_location_shop0"/>
            <field name="company_id" ref="stock.res_company_1"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_9').name,
                'product_id': ref('product.product_product_9'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 25.0,
                'picking_type_id': ref('stock.chi_picking_type_in'),
                'location_id': ref('stock.stock_location_suppliers'),
                'location_dest_id': ref('stock.stock_location_shop0'),
                'company_id': ref('stock.res_company_1'),
            })]"/>
        </record>

        <record id="incomming_chicago_warehouse1" model="stock.picking">
            <field name="user_id"></field>
            <field name="picking_type_id" ref="stock.chi_picking_type_in"/>
            <field name="scheduled_date" eval="DateTime.today() - timedelta(days=5)"/>
            <field name="location_id" ref="stock.stock_location_suppliers"/>
            <field name="location_dest_id" ref="stock.stock_location_shop0"/>
            <field name="company_id" ref="stock.res_company_1"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_delivery_01').name,
                'product_id': ref('product.product_delivery_01'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 180.0,
                'picking_type_id': ref('stock.chi_picking_type_in'),
                'location_id': ref('stock.stock_location_suppliers'),
                'location_dest_id': ref('stock.stock_location_shop0'),
                'company_id': ref('stock.res_company_1'),
            })]"/>
        </record>

        <record id="incomming_chicago_warehouse2" model="stock.picking">
            <field name="user_id"></field>
            <field name="picking_type_id" ref="stock.chi_picking_type_in"/>
            <field name="location_id" ref="stock.stock_location_suppliers"/>
            <field name="location_dest_id" ref="stock.stock_location_shop0"/>
            <field name="company_id" ref="stock.res_company_1"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_delivery_01').name,
                'product_id': ref('product.product_delivery_01'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 45.0,
                'picking_type_id': ref('stock.chi_picking_type_in'),
                'location_id': ref('stock.stock_location_suppliers'),
                'location_dest_id': ref('stock.stock_location_shop0'),
                'company_id': ref('stock.res_company_1'),
            })]"/>
        </record>

        <record id="incomming_chicago_warehouse3" model="stock.picking">
            <field name="user_id"></field>
            <field name="picking_type_id" ref="stock.chi_picking_type_in"/>
            <field name="origin">chicago_warehouse</field>
            <field name="scheduled_date" eval="DateTime.today() - timedelta(days=2)"/>
            <field name="location_id" ref="stock.stock_location_suppliers"/>
            <field name="location_dest_id" ref="stock.stock_location_shop0"/>
            <field name="company_id" ref="stock.res_company_1"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_22').name,
                'product_id': ref('product.product_product_22'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 75.0,
                'picking_type_id': ref('stock.chi_picking_type_in'),
                'location_id': ref('stock.stock_location_suppliers'),
                'location_dest_id': ref('stock.stock_location_shop0'),
                'company_id': ref('stock.res_company_1'),
            })]"/>
        </record>

        <!-- Create STOCK_PICKING_OUT for Chicago Warehouse -->
        <record id="outgoing_chicago_warehouse" model="stock.picking">
            <field name="picking_type_id" ref="stock.chi_picking_type_out"/>
            <field name="user_id"></field>
            <field name="origin">outgoing_chicago_warehouse</field>
            <field name="scheduled_date" eval="DateTime.today()"/>
            <field name="location_id" ref="stock.stock_location_shop0"/>
            <field name="location_dest_id" ref="stock.stock_location_customers"/>
            <field name="company_id" ref="stock.res_company_1"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_27').name,
                'product_id': ref('product.product_product_27'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 15.0,
                'picking_type_id': ref('stock.chi_picking_type_out'),
                'location_id': ref('stock.stock_location_shop0'),
                'location_dest_id': ref('stock.stock_location_customers'),
                'company_id': ref('stock.res_company_1'),
            })]"/>
        </record>

        <record id="outgoing_chicago_warehouse1" model="stock.picking">
            <field name="user_id"></field>
            <field name="picking_type_id" ref="stock.chi_picking_type_out"/>
            <field name="origin">outgoing_shipment_chicago_warehouse</field>
            <field name="scheduled_date" eval="DateTime.today() - timedelta(days=10)"/>
            <field name="location_id" ref="stock.stock_location_shop0"/>
            <field name="location_dest_id" ref="stock.stock_location_customers"/>
            <field name="company_id" ref="stock.res_company_1"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_6').name,
                'product_id': ref('product.product_product_6'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 180.0,
                'picking_type_id': ref('stock.chi_picking_type_out'),
                'location_id': ref('stock.stock_location_shop0'),
                'location_dest_id': ref('stock.stock_location_customers'),
                'company_id': ref('stock.res_company_1'),
            })]"/>
        </record>

        <record id="outgoing_chicago_warehouse2" model="stock.picking">
            <field name="user_id"></field>
            <field name="picking_type_id" ref="stock.chi_picking_type_out"/>
            <field name="origin">chicago_warehouse</field>
            <field name="scheduled_date" eval="DateTime.today()"/>
            <field name="location_id" ref="stock.stock_location_shop0"/>
            <field name="location_dest_id" ref="stock.stock_location_customers"/>
            <field name="company_id" ref="stock.res_company_1"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_delivery_02').name,
                'product_id': ref('product.product_delivery_02'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 45.0,
                'picking_type_id': ref('stock.chi_picking_type_out'),
                'location_id': ref('stock.stock_location_shop0'),
                'location_dest_id': ref('stock.stock_location_customers'),
                'company_id': ref('stock.res_company_1'),
            })]"/>
        </record>

        <record id="outgoing_chicago_warehouse3" model="stock.picking">
            <field name="user_id"></field>
            <field name="picking_type_id" ref="stock.chi_picking_type_out"/>
            <field name="origin">outgoing chicago warehouse</field>
            <field name="scheduled_date" eval="DateTime.today()"/>
            <field name="location_id" ref="stock.stock_location_shop0"/>
            <field name="location_dest_id" ref="stock.stock_location_customers"/>
            <field name="company_id" ref="stock.res_company_1"/>
            <field name="move_lines" model="stock.move" eval="[(0, 0, {
                'name': obj().env.ref('product.product_product_5').name,
                'product_id': ref('product.product_product_5'),
                'product_uom': ref('uom.product_uom_unit'),
                'product_uom_qty': 75.0,
                'picking_type_id': ref('stock.chi_picking_type_out'),
                'location_id': ref('stock.stock_location_shop0'),
                'location_dest_id': ref('stock.stock_location_customers'),
                'company_id': ref('stock.res_company_1'),
            })]"/>
        </record>

        <function model="stock.picking" name="action_confirm">
            <value model="stock.picking" eval="[
                obj().env.ref('stock.outgoing_shipment_main_warehouse').id,
                obj().env.ref('stock.outgoing_shipment_main_warehouse1').id,
                obj().env.ref('stock.outgoing_shipment_main_warehouse4').id,
                obj().env.ref('stock.outgoing_shipment_main_warehouse5').id,
                obj().env.ref('stock.outgoing_shipment_main_warehouse6').id,
                obj().env.ref('stock.incomming_shipment1').id,
                obj().env.ref('stock.incomming_shipment2').id,
                obj().env.ref('stock.incomming_shipment3').id,
                obj().env.ref('stock.outgoing_chicago_warehouse1').id,
                obj().env.ref('stock.outgoing_chicago_warehouse2').id,
                obj().env.ref('stock.outgoing_chicago_warehouse3').id,
                obj().env.ref('stock.incomming_chicago_warehouse1').id,
                obj().env.ref('stock.incomming_chicago_warehouse2').id,
                obj().env.ref('stock.incomming_chicago_warehouse3').id]"/>
        </function>

        <function model="stock.picking" name="action_assign">
            <value model="stock.picking" eval="[
                obj().env.ref('stock.outgoing_shipment_main_warehouse1').id,
                obj().env.ref('stock.outgoing_shipment_main_warehouse4').id,
                obj().env.ref('stock.outgoing_shipment_main_warehouse5').id,
                obj().env.ref('stock.outgoing_shipment_main_warehouse6').id,
                obj().env.ref('stock.outgoing_chicago_warehouse1').id,
            ]"/>
        </function>

        <!-- Adds move lines qty. done -->
        <function model="stock.move.line" name="write">
            <value model="stock.move.line" search="[('picking_id', '=', ref('stock.outgoing_shipment_main_warehouse1'))]"/>
            <value eval="{'qty_done': 100}"/>
        </function>
        <function model="stock.move.line" name="write">
            <value model="stock.move.line" search="[('picking_id', '=', ref('stock.outgoing_shipment_main_warehouse4'))]"/>
            <value eval="{'qty_done': 16}"/>
        </function>
        <function model="stock.move.line" name="write">
            <value model="stock.move.line" search="[('picking_id', '=', ref('stock.outgoing_shipment_main_warehouse5'))]"/>
            <value eval="{'qty_done': 32}"/>
        </function>
        <function model="stock.move.line" name="write">
            <value model="stock.move.line" search="[('picking_id', '=', ref('stock.outgoing_shipment_main_warehouse6'))]"/>
            <value eval="{'qty_done': 50}"/>
        </function>
        <function model="stock.move.line" name="write">
            <value model="stock.move.line" search="[('picking_id', '=', ref('stock.incomming_chicago_warehouse1'))]"/>
            <value eval="{'qty_done': 100}"/>
        </function>
        <function model="stock.move.line" name="write">
            <value model="stock.move.line" search="[('picking_id', '=', ref('stock.outgoing_chicago_warehouse1'))]"/>
            <value eval="{'qty_done': 100}"/>
        </function>

        <function model="stock.picking" name="_action_done">
            <value model="stock.picking" eval="[
                obj().env.ref('stock.outgoing_shipment_main_warehouse1').id,
                obj().env.ref('stock.outgoing_shipment_main_warehouse5').id,
                obj().env.ref('stock.outgoing_shipment_main_warehouse6').id,
                obj().env.ref('stock.incomming_chicago_warehouse1').id,
                obj().env.ref('stock.outgoing_chicago_warehouse1').id,
                obj().env.ref('stock.outgoing_chicago_warehouse2').id]"/>
        </function>

        <record id="stock.outgoing_chicago_warehouse1" model="stock.picking">
            <field name="date_done" eval="DateTime.today() - timedelta(days=5)"/>
        </record>

        <record id="stock.outgoing_shipment_main_warehouse5" model="stock.picking">
            <field name="date_done" eval="DateTime.today() - timedelta(days=17)"/>
        </record>

        <record id="stock.outgoing_shipment_main_warehouse6" model="stock.picking">
            <field name="date_done" eval="DateTime.today() - timedelta(days=7)"/>
        </record>

        <function model="stock.move" name="write">
            <value model="stock.move" search="[('picking_id', '=', ref('stock.outgoing_shipment_main_warehouse4'))]"/>
            <value eval="{'date': DateTime.today() + timedelta(days=3)}"/>
        </function>

        <function model="stock.move" name="write">
            <value model="stock.move" search="[('picking_id', '=', ref('stock.outgoing_shipment_main_warehouse5'))]"/>
            <value eval="{'date': DateTime.today() - timedelta(days=18)}"/>
        </function>

        <function model="stock.move" name="write">
            <value model="stock.move" search="[('picking_id', '=', ref('stock.outgoing_shipment_main_warehouse6'))]"/>
            <value eval="{'date': DateTime.today() - timedelta(days=7)}"/>
        </function>

    </data>
</odoo>
