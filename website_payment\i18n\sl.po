# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_payment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid ""
".\n"
"                        <br/>\n"
"                        We appreciate your support for our organization as such.\n"
"                        <br/>\n"
"                        Regards."
msgstr ""
".\n"
"                       <br/>\n"
"                        Cenimo vašo podporo naši organizaciji kot taki.\n"
"                       <br/>\n"
"                        S spoštovanjem."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Comment:</b>"
msgstr "<b>Komentar:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donation Date:</b>"
msgstr "<b>Datum donacije:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Email:</b>"
msgstr "<b>Elektronska pošta donatorja:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Name:</b>"
msgstr "<b>Ime donatorja:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment ID:</b>"
msgstr "<b>ID plačila:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment Method:</b>"
msgstr "<b>Način plačila:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "<option value=\"\">Country...</option>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Primerne možnosti plačila ni bilo mogoče najti.</strong><br/>\n"
"                                    Če menite, da gre za napako, se obrnite na skrbnika spletnega mesta."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>Opozorilo</strong> Valuta manjka ali je napačna."

#. module: website_payment
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "A donation has been made on your website"
msgstr "Na vašem spletnem mestu je bila opravljena donacija"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "A year of cultural awakening."
msgstr "Leto kulturnega prebujanja."

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add a description here"
msgstr "Tukaj dodajte opis"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add new pre-filled option"
msgstr "Dodajte novo vnaprej izpolnjeno možnost"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#, python-format
msgid "Amount"
msgstr "Znesek"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Amount ("
msgstr "Znesek ("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Amount("
msgstr "Znesek("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Caring for a baby for 1 month."
msgstr "Skrb za dojenčka za 1 mesec."

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#, python-format
msgid "Choose Your Amount"
msgstr "Izberite svoj znesek"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Country"
msgstr "Država"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Country\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"Država\n"
"                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Country is required."
msgstr "Država je zahtevana."

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#: model_terms:ir.ui.view,arch_db:website_payment.donation_input
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
#, python-format
msgid "Custom Amount"
msgstr "Znesek po meri"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Dear"
msgstr "Spoštovani"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Default Amount"
msgstr "Privzeti znesek"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Display Options"
msgstr "Možnosti prikaza"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Donate Now"
msgstr "Darujte zdaj"

#. module: website_payment
#: model:mail.template,name:website_payment.mail_template_donation
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Donation"
msgstr "Donacija"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donation amount must be at least %.2f."
msgstr "Znesek donacije mora biti vsaj %.2f."

#. module: website_payment
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Donation confirmation"
msgstr "Potrditev donacije"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Donation notification"
msgstr "Obvestilo o donaciji"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Email"
msgstr "E-pošta"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Email\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"E-pošta\n"
"                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Email is invalid"
msgstr "E-pošta je neveljavna"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Email is required."
msgstr "E-pošta je zahtevana."

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Field '%s' is mandatory"
msgstr "Polje '%s' je obvezno"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Input"
msgstr "Vnos"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_payment__is_donation
msgid "Is Donation"
msgstr "Je donacija"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_transaction__is_donation
msgid "Is donation"
msgstr "Je donacija"

#. module: website_payment
#: model:ir.model.fields,help:website_payment.field_account_payment__is_donation
#: model:ir.model.fields,help:website_payment.field_payment_transaction__is_donation
msgid "Is the payment a donation"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Make a Donation"
msgstr "Opravite donacijo"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Name"
msgstr "Naziv"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Name\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"Ime\n"
"                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Name is required."
msgstr "Ime je zahtevano."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "None"
msgstr "Ni prenosa"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in elementary school."
msgstr "Eno leto v osnovni šoli."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in high school."
msgstr "Eno leto v srednji šoli."

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Ponudnik plačilne storitve"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Payment Details"
msgstr "Podrobnosti plačila"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "Plačilna transakcija"

#. module: website_payment
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Payment received from donation with following details:"
msgstr "Prejeto plačilo iz donacije z naslednjimi podatki:"

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_payment
msgid "Payments"
msgstr "Plačila"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "Please select or enter an amount"
msgstr "Izberite ali vnesite znesek"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Pre-filled Options"
msgstr "Predhodno izpolnjene možnosti"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Recipient Email"
msgstr "Prejemnik e-pošta"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Napaka strežnika"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Slider"
msgstr "Drsnik"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Small or large, your contribution is essential."
msgstr "Vaš prispevek je bistvenega pomena, pa naj bo majhen ali velik."

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Some information is missing to process your payment."
msgstr "Za obdelavo plačila manjkajo nekateri podatki."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Thank you for your donation of"
msgstr "Zahvaljujemo se vam za vašo donacijo"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "The minimum donation amount is %s%s%s"
msgstr "Najmanjši znesek donacije je %s%s%s"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "There is nothing to pay."
msgstr "Ničesar ni treba plačati."

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Validation Error"
msgstr "Napaka pri potrjevanju"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "We could not obtain payment fees."
msgstr "Plačilnih provizij nismo mogli pridobiti."

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_acquirer__website_id
msgid "Website"
msgstr "Spletna stran"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Write us a comment"
msgstr "Napišite nam komentar"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Your comment"
msgstr "Vaš komentar"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "made on"
msgstr "narejeno na"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Descriptions"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Maximum"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Minimum"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Step"
msgstr ""
