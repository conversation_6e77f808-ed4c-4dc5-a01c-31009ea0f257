<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <!-- Delivery Carriers -->
        <record id="view_delivery_carrier_tree" model="ir.ui.view">
            <field name="name">delivery.carrier.tree</field>
            <field name="model">delivery.carrier</field>
            <field name="arch" type="xml">
                <tree string="Carrier">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="delivery_type"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <record id="view_delivery_carrier_search" model="ir.ui.view">
            <field name="name">delivery.carrier.search</field>
            <field name="model">delivery.carrier</field>
            <field name="arch" type="xml">
                <search string="Delivery Carrier">
                    <field name="name" string="Carrier" />
                    <field name="delivery_type"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <group expand="1" string="Group By">
                        <filter string="Provider" name="provider" context="{'group_by':'delivery_type', 'residual_visible':True}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="view_delivery_carrier_form" model="ir.ui.view">
            <field name="name">delivery.carrier.form</field>
            <field name="model">delivery.carrier</field>
            <field name="arch" type="xml">
                <form string="Carrier">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="toggle_prod_environment"
                                    attrs="{'invisible': ['|', '|', ('prod_environment', '=', False), ('delivery_type', '=', 'fixed'), ('delivery_type', '=', 'base_on_rule')]}"
                                    class="oe_stat_button"
                                    type="object" icon="fa-play">
                                <div class="o_stat_info o_field_widget">
                                    <span class="text-success">Production</span>
                                    <span class="o_stat_text">Environment</span>
                                </div>
                            </button>
                            <!-- transfer referenced here due to view inheritance issue in current master (post-saas-16) -->
                            <button name="toggle_prod_environment"
                                    attrs="{'invisible': ['|', '|', ('prod_environment', '=', True), ('delivery_type', '=', 'fixed'), ('delivery_type', '=', 'base_on_rule')]}"
                                    class="oe_stat_button"
                                    type="object" icon="fa-stop">
                                <div class="o_stat_info o_field_widget">
                                    <span class="o_warning_text">Test</span>
                                    <span class="o_stat_text">Environment</span>
                                </div>
                            </button>
                            <button name="toggle_debug"
                                    attrs="{'invisible': ['|', '|', ('delivery_type', '=', 'fixed'), ('delivery_type', '=', 'base_on_rule'), ('debug_logging', '=', True)]}"
                                    class="oe_stat_button"
                                    type="object" icon="fa-code">
                                <div class="o_stat_info o_field_widget">
                                    <span class="text-danger">No debug</span>
                                </div>
                            </button>
                            <button name="toggle_debug"
                                    attrs="{'invisible': ['|', '|', ('delivery_type', '=', 'fixed'), ('delivery_type', '=', 'base_on_rule'), ('debug_logging', '=', False)]}"
                                    class="oe_stat_button"
                                    type="object" icon="fa-code">
                                <div class="o_stat_info o_field_widget">
                                    <span class="text-success">Debug requests</span>
                                </div>
                            </button>
                        </div>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                        <div class="oe_title" name="title">
                            <label for="name" string="Shipping Method"/>
                            <h1>
                                <field name="name" placeholder="e.g. UPS Express"/>
                            </h1>
                        </div>
                        <group>
                            <group name="provider_details">
                                <field name="active" invisible="1"/>
                                <field name="prod_environment" invisible="1"/>
                                <field name="debug_logging" invisible="1"/>
                                <label for="delivery_type"/>
                                <div>
                                    <field name="delivery_type" />
                                    <button string="Install more Providers" name="install_more_provider" type="object" class="oe_link oe_edit_only"/>
                                </div>
                                <field name="integration_level" widget="radio" attrs="{'invisible': ['|', ('delivery_type', '=', 'fixed'), ('delivery_type', '=', 'base_on_rule')]}"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                            <group name="delivery_details">
                                <field name="product_id" context="{'default_detailed_type': 'service', 'default_sale_ok': False, 'default_purchase_ok': False, 'default_invoice_policy': 'order'}" />
                                <field name="invoice_policy" widget="radio" attrs="{'invisible': ['|', ('delivery_type', 'in', ('fixed', 'base_on_rule')), ('integration_level', '=', 'rate')]}"/>
                                <label for="margin" string="Margin on Rate"/>
                                <div>
                                    <field name="margin" class="oe_inline"/>%
                                </div>
                                <field name="free_over"/>
                                <field name="amount" attrs="{'required':[('free_over','!=', False)], 'invisible':[('free_over','=', False)]}"/>
                            </group>
                        </group>
                        <notebook>
                            <page name="pricing" string="Pricing" attrs="{'invisible': [('delivery_type', 'not in', ['fixed', 'base_on_rule'])]}">
                                <group attrs="{'invisible':[('delivery_type', '!=', 'fixed')]}">
                                    <group name="fixed_price">
                                        <field name="fixed_price"/>
                                    </group>
                                </group>
                                <group name="general" attrs="{'invisible':[('delivery_type', '!=', 'base_on_rule')]}">
                                    <field name="price_rule_ids" nolabel="1"/>
                                </group>
                            </page>
                            <page string="Destination Availability" name="destination">
                                <group>
                                    <p>
                                        Filling this form allows you to filter delivery carriers according to the delivery address of your customer.
                                    </p>
                                </group>
                                <group>
                                    <group name="country_details">
                                        <field name="country_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}"/>
                                        <field name="state_ids" widget="many2many_tags"/>
                                    </group>
                                    <group></group>
                                    <group name="zip_from">
                                        <field name="zip_from"/>
                                    </group>
                                    <group name="zip_to">
                                        <field name="zip_to"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="action_delivery_carrier_form" model="ir.actions.act_window">
            <field name="name">Shipping Methods</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">delivery.carrier</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'search_default_group_by_provider': True}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Define a new delivery method
              </p><p>
                Each carrier (e.g. UPS) can have several delivery methods (e.g.
                UPS Express, UPS Standard) with a set of pricing rules attached
                to each method.
              </p><p>
                These methods allow to automatically compute the delivery price
                according to your settings; on the sales order (based on the
                quotation) or the invoice (based on the delivery orders).
              </p>
            </field>
        </record>

        <menuitem action="action_delivery_carrier_form" id="menu_action_delivery_carrier_form" parent="stock.menu_delivery" sequence="1"/>
        <menuitem action="action_delivery_carrier_form" id="sale_menu_action_delivery_carrier_form" parent="sale.menu_sales_config" sequence="4"/>

        <record id="view_delivery_price_rule_form" model="ir.ui.view">
            <field name="name">delivery.price.rule.form</field>
            <field name="model">delivery.price.rule</field>
            <field name="arch" type="xml">
                <form string="Price Rules">
                    <group>
                        <field name="name" invisible="1"/>
                    </group>
                    <group>
                        <label for="variable" string="Condition"/>
                        <div class="o_row">
                            <field name="variable"/>
                            <field name="operator"/>
                            <field name="max_value"/>
                        </div>
                        <label for="list_base_price" string="Delivery Cost"/>
                        <div>
                            <field name="list_base_price" class="oe_inline"/>
                            +
                            <field name="list_price" class="oe_inline"/>
                            *
                            <field name="variable_factor" class="oe_inline"/>
                        </div>
                    </group>
                </form>
            </field>
        </record>
        <record id="view_delivery_price_rule_tree" model="ir.ui.view">
            <field name="name">delivery.price.rule.tree</field>
            <field name="model">delivery.price.rule</field>
            <field name="arch" type="xml">
                <tree string="Price Rules">
                    <field name="sequence" widget="handle" />
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <record id="view_picking_withcarrier_out_form" model="ir.ui.view">
            <field name="name">delivery.stock.picking_withcarrier.form.view</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
              <data>
                <xpath expr="//group[@name='other_infos']" position="before">
                    <group name='carrier_data' string="Shipping Information">
                        <field name="is_return_picking" invisible="1"/>
                        <field name="carrier_id" attrs="{'readonly': [('state', 'in', ('done', 'cancel'))]}" options="{'no_create': True, 'no_open': True}"/>
                        <field name="delivery_type" attrs="{'invisible':True}"/>
                        <label for="carrier_tracking_ref"/>
                        <div name="tracking">
                            <field name="carrier_tracking_ref" class="oe_inline text-break" attrs="{'readonly': [('state', 'in', ('done', 'cancel'))]}"/>
                            <button type='object' class="fa fa-arrow-right oe_link" name="cancel_shipment" string="Cancel" attrs="{'invisible':['|','|','|',('carrier_tracking_ref','=',False),('delivery_type','in', ['fixed', 'base_on_rule']),('delivery_type','=',False),('state','not in',('done'))]}"/>
                        </div>
                        <label for="weight" string="Weight"/>
                        <div>
                            <field name="weight" class="oe_inline"/>
                            <field name="weight_uom_name" nolabel="1" class="oe_inline" style="margin-left:5px"/>
                        </div>
                        <label for="shipping_weight" string="Weight for shipping"/>
                        <div>
                            <field name="shipping_weight" class="oe_inline"/>
                            <field name="weight_uom_name" nolabel="1" class="oe_inline" style="margin-left:5px"/>
                        </div>
                    </group>
                </xpath>
                <div name="button_box" position="inside">
                    <button type="object" name="open_website_url" class="oe_stat_button" icon='fa-truck' string="Tracking"
                         attrs="{'invisible': ['|','|',('carrier_tracking_ref','=',False),('carrier_id', '=', False),('delivery_type','=','grid')]}" />
                </div>
                <xpath expr="/form/header/button[last()]" position="after">
                    <button name="send_to_shipper" string="Send to Shipper" type="object" attrs="{'invisible':['|','|','|','|',('carrier_tracking_ref','!=',False),('delivery_type','in', ['fixed', 'base_on_rule']),('delivery_type','=',False),('state','not in',('done')),('picking_type_code', '=', 'incoming')]}" data-hotkey="shift+v"/>
                </xpath>
                <xpath expr="/form/header/button[last()]" position="after">
                    <button name="print_return_label" string="Print Return Label" type="object" attrs="{'invisible':['|', '|', ('is_return_picking', '=', False),('state', '=', 'done'),('picking_type_code', '!=', 'incoming')]}" data-hotkey="shift+o"/>
                </xpath>
              </data>
              <xpath expr="//field[@name='partner_id']" position="attributes">
                  <attribute name="attrs">{'required': ['&amp;', ('delivery_type', '!=', False), ('delivery_type', 'not in', ['fixed', 'base_on_rule'])]}</attribute>
              </xpath>
            </field>
        </record>

        <record id="view_picking_withweight_internal_move_form" model="ir.ui.view">
            <field name="name">stock.picking_withweight.internal.move.form.view</field>
            <field name="model">stock.move</field>
            <field name="inherit_id" ref="stock.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='location_dest_id']" position="after">
                    <field name="weight"/>
                </xpath>
            </field>
        </record>

        <record id="view_move_line_tree_detailed_delivery" model="ir.ui.view">
            <field name="name">stock.move.line.tree.detailed</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_move_line_tree_detailed"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='picking_partner_id']" position="after">
                    <field name="destination_country_code" optional="hide"/>
                    <field name="carrier_id" optional="hide"/>
                </xpath>
            </field>
        </record>

        <record id="view_quant_package_weight_form" model="ir.ui.view">
            <field name="name">stock.quant.package.weight.form</field>
            <field name="model">stock.quant.package</field>
            <field name="inherit_id" ref="stock.view_quant_package_form"/>
            <field name="arch" type="xml">
                <field name="company_id" position="before">
                    <label for="shipping_weight"/>
                    <div class="o_row" name="Shipping Weight">
                        <field name="shipping_weight"/>
                        <span><field name="weight_uom_name"/></span>
                        <span class="text-muted">(computed: <field name="weight" nolabel="1"/></span><span class="text-muted"><field name="weight_uom_name" nolabel="1"/>)</span>
                    </div>
                </field>
            </field>
        </record>

        <record id="view_order_form_with_carrier" model="ir.ui.view">
            <field name="name">delivery.sale.order.form.view.with_carrier</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <data>
                    <xpath expr="//field[@name='partner_id']" position='after'>
                        <field name="delivery_set" invisible="1"/>
                        <field name="is_all_service" invisible="1"/>
                        <field name="recompute_delivery_price" invisible="1"/>
                    </xpath>
                    <xpath expr="//group[@name='note_group']" position="before">
                        <div class="oe_right">
                            <button
                                string="Add shipping"
                                name="action_open_delivery_wizard"
                                type="object"
                                attrs="{'invisible': ['|', '|',('is_all_service', '=', True), ('order_line', '=', []), ('delivery_set', '=', True)]}"
                            />
                            <button
                                string="Update shipping cost"
                                name="action_open_delivery_wizard"
                                context="{'carrier_recompute':True}"
                                type="object"
                                class="text-warning btn-secondary"
                                attrs="{'invisible': ['|', '|',('is_all_service', '=', True), ('recompute_delivery_price', '=', False), ('delivery_set', '=', False)]}"
                            />
                            <button
                                string="Update shipping cost"
                                name="action_open_delivery_wizard"
                                context="{'carrier_recompute':True}"
                                type="object"
                                attrs="{'invisible': ['|', '|',('is_all_service', '=', True), ('recompute_delivery_price', '=', True), ('delivery_set', '=', False)]}"
                            />
                        </div>
                    </xpath>
                    <xpath expr="//field[@name='order_line']/form/group/group/field[@name='price_unit']" position="before">
                        <field name="recompute_delivery_price" invisible="1"/>
                        <field name="is_delivery" invisible="1"/>
                    </xpath>
                    <xpath expr="//field[@name='order_line']/tree/field[@name='price_unit']" position="before">
                        <field name="recompute_delivery_price" invisible="1"/>
                        <field name="is_delivery" invisible="1"/>
                    </xpath>
                    <xpath expr="//field[@name='order_line']/tree" position="attributes">
                        <attribute name="decoration-warning">recompute_delivery_price and is_delivery</attribute>
                    </xpath>
                </data>
            </field>
        </record>

        <record id="delivery_tracking_url_warning_form" model="ir.ui.view">
            <field name="name">delivery.carrier.warning.url.form</field>
            <field name="model">stock.picking</field>
            <field name="arch" type="xml">
                <form string="Trackers URL">
                    <group>
                        <div class="alert alert-info" role="status">
                            <p>You have multiple tracker links, they are available in the chatter.</p>
                        </div>
                    </group>
                    <footer>
                        <button string="OK" special="cancel" data-hotkey="z" class="oe_highlight"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="act_delivery_trackers_url" model="ir.actions.act_window">
            <field name="name">Display tracking links</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">stock.picking</field>
            <field name="view_id" ref="delivery.delivery_tracking_url_warning_form"/>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

        <record id="vpicktree_view_tree" model="ir.ui.view">
            <field name="name">stock.picking.delivery.tree.inherit.delivery</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.vpicktree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='backorder_id']" position="after">
                    <field name="carrier_tracking_ref" optional="hide"/>
                    <field name="carrier_id" optional="hide"/>
                    <field name="destination_country_code" optional="hide" string="Destination"/>
                    <field name="weight" optional="hide"/>
                    <field name="shipping_weight" optional="hide"/>
                </xpath>
            </field>
        </record>

</odoo>
