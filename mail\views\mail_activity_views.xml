<?xml version="1.0"?>
<odoo>
    <record id="mail_activity_type_view_form" model="ir.ui.view">
        <field name="name">mail.activity.type.view.form</field>
        <field name="model">mail.activity.type</field>
        <field name="arch" type="xml">
            <form string="Activities">
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="e.g. Schedule a meeting"/></h1>
                    </div>
                    <group>
                        <group name="activity_details" string="Activity Settings">
                            <field name="active" invisible="1"/>
                            <field name="category"/>
                            <field name="default_user_id" options="{'no_create': True, 'no_edit': True}" domain="[('share', '=', False)]"/>
                            <field name="res_model" groups="base.group_no_one"/>
                            <field name="res_model_change" invisible="1"/>
                            <field name="initial_res_model" invisible="1"/>
                            <field name="summary"/>
                            <field name="icon" groups="base.group_no_one"/>
                            <field name="decoration_type" groups="base.group_no_one"/>
                        </group>
                        <group name="activity_planning" string="Next Activity">
                            <field name="chaining_type" attrs="{'invisible': [('category', '=', 'upload_file')]}"/>
                            <field name="triggered_next_type_id" options="{'no_open': True}" context="{'default_res_model': res_model}"
                                attrs="{'required': ['&amp;', ('chaining_type', '=', 'trigger'), ('category', '!=', 'upload_file')],
                                'invisible': ['&amp;', ('chaining_type', '=', 'suggest'), ('category', '!=', 'upload_file')]}"/>
                            <field name="suggested_next_type_ids" widget="many2many_tags" context="{'default_res_model': res_model}"
                                attrs="{'invisible': ['|', ('chaining_type', '=', 'trigger'), ('category', '=', 'upload_file')]}"/>
                            <field name="mail_template_ids" widget="many2many_tags"
                                domain="[('model_id.model', '=', res_model)]"
                                attrs="{'invisible': [('res_model', '=', False)]}"
                                context="{'default_model': res_model}"/>
                            <label for="delay_count"/>
                            <div>
                                <div class="o_row">
                                    <field class="col-1 pl-0 pr-0" name="delay_count"/>
                                    <field class="col-3 pl-1 pr-1" name="delay_unit"/>
                                    <field name="delay_from"/>
                                </div>
                            </div>
                        </group>
                    </group>
                    <label for="default_note" class="fw-bold"/>
                    <field nolabel="1" name="default_note" placeholder="e.g. &quot;Go over the offer and discuss details&quot;" class="oe-bordered-editor"/>
                    <p class="alert alert-info" role="alert" attrs="{'invisible': [('res_model_change', '=', False)]}">Modifying the model can have an impact on existing activities using this activity type, be careful.</p>
                </sheet>
            </form>
        </field>
    </record>

    <record id="mail_activity_type_view_search" model="ir.ui.view">
        <field name="name">mail.activity.type.search</field>
        <field name="model">mail.activity.type</field>
        <field name="arch" type="xml">
            <search string="Activities">
                <field name="name"/>
                <filter string="Archived" domain="[('active', '=', False)]" name="archived"/>
            </search>
        </field>
    </record>

    <record id="mail_activity_type_view_tree" model="ir.ui.view">
        <field name="name">mail.activity.type.view.tree</field>
        <field name="model">mail.activity.type</field>
        <field name="arch" type="xml">
            <tree string="Activities" sample="1">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="summary"/>
                <field name="delay_label" string="Planned in" class="text-right"/>
                <field name="delay_from" string="Type"/>
                <field name="res_model" groups="base.group_no_one"/>
                <field name="icon" groups="base.group_no_one"/>
            </tree>
        </field>
    </record>

    <record id="mail_activity_type_action" model="ir.actions.act_window">
        <field name="name">Activity Types</field>
        <field name="res_model">mail.activity.type</field>
        <field name="view_mode">tree,form</field>
    </record>


    <record id="mail_activity_action" model="ir.actions.act_window">
        <field name="name">Activities</field>
        <field name="res_model">mail.activity</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="mail_activity_view_form_popup" model="ir.ui.view">
        <field name="name">mail.activity.view.form.popup</field>
        <field name="model">mail.activity</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <form string="Log an Activity" create="false">
                <sheet string="Activity">
                    <group invisible="1">
                        <field name="activity_category" invisible="1" />
                        <field name="res_model" invisible="1"/>
                        <field name="res_model_id" invisible="1"/>
                        <field name="res_id" invisible="1" widget="integer"/>
                        <field name="chaining_type" invisible="1"/>
                        <field name="previous_activity_type_id"/>
                        <field name="has_recommended_activities"/>
                    </group>
                    <group attrs="{'invisible': [('has_recommended_activities','=',False)]}">
                        <div class="o_row">
                            <strong>Recommended Activities</strong>
                            <field name="recommended_activity_type_id" widget="selection_badge"
                                domain="[('previous_type_ids', '=', previous_activity_type_id)]"
                                nolabel="1"/>
                        </div>
                    </group>
                    <group>
                        <group>
                            <field name="activity_type_id" required="1" options="{'no_create': True, 'no_open': True}"/>
                            <field name="summary" placeholder="e.g. Discuss proposal"/>
                        </group>
                        <group>
                            <field name="date_deadline"/>
                            <field name="user_id"/>
                        </group>
                    </group>
                    <field name="note" class="oe-bordered-editor" placeholder="Log a note..."/>
                    <footer>
                        <field name="id" invisible="1"/>
                        <button id="mail_activity_schedule" string="Schedule" name="action_close_dialog" type="object" class="btn-primary"
                            attrs="{'invisible': [('id', '!=', False)]}" data-hotkey="q"/>
                        <button id="mail_activity_save" string="Save" name="action_close_dialog" type="object" class="btn-primary"
                            attrs="{'invisible': [('id', '=', False)]}" data-hotkey="q"/>
                        <button attrs="{'invisible': [('chaining_type', '=', 'trigger')]}" string="Mark as Done" name="action_done"
                            type="object" class="btn-secondary" data-hotkey="w"
                            context="{'mail_activity_quick_update': True}"/>
                        <button attrs="{'invisible': [('chaining_type', '=', 'trigger')]}" string="Done &amp; Schedule Next" name="action_done_schedule_next"
                            type="object" class="btn-secondary" data-hotkey="x"
                            context="{'mail_activity_quick_update': True}"/>
                        <button attrs="{'invisible': [('chaining_type', '=', 'suggest')]}" string="Done &amp; Launch Next" name="action_done_schedule_next"
                            type="object" class="btn-secondary" data-hotkey="x"
                            context="{'mail_activity_quick_update': True}"/>
                        <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="z" />
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

    <record id="mail_activity_view_form" model="ir.ui.view">
        <field name="name">mail.activity.view.form</field>
        <field name="model">mail.activity</field>
        <field name="priority">21</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="mail.mail_activity_view_form_popup"/>
        <field name="arch" type="xml">
            <field name="activity_type_id" position="before">
                <field name="res_name" readonly="1" string="Document"/>
            </field>
            <footer position="replace"/>
        </field>
    </record>

    <record id="mail_activity_view_search" model="ir.ui.view">
        <field name="name">mail.activity.view.search</field>
        <field name="model">mail.activity</field>
        <field name="arch" type="xml">
            <search string="Activity">
                <field name="res_model"/>
                <field name="summary"/>
                <field name="activity_type_id"/>
                <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))
                        ]"/>
                <group expand="0" string="Group By">
                    <filter string="Deadline" name="date_deadline" context="{'group_by': 'date_deadline'}"/>
                    <filter string="Created By" name="createdby" context="{'group_by': 'create_uid'}"/>
                    <filter string="Activity Type" name="activittype" context="{'group_by': 'activity_type_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="mail_activity_view_tree" model="ir.ui.view">
        <field name="name">mail.activity.view.tree</field>
        <field name="model">mail.activity</field>
        <field name="arch" type="xml">
            <tree string="Next Activities"
                    decoration-danger="date_deadline &lt; current_date"
                    decoration-success="date_deadline == current_date"
                    default_order="date_deadline" create="false">
                <field name="res_name"/>
                <field name="activity_type_id"/>
                <field name="summary"/>
                <field name="date_deadline"/>
            </tree>
        </field>
    </record>

    <record id="mail_activity_action_view_tree" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="act_window_id" ref="mail.mail_activity_action"/>
    </record>
    <record id="mail_activity_action_view_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="mail.mail_activity_view_form"/>
        <field name="act_window_id" ref="mail.mail_activity_action"/>
    </record>

    <record id="mail_activity_view_calendar" model="ir.ui.view">
        <field name="name">mail.activity.view.calendar</field>
        <field name="model">mail.activity</field>
        <field name="priority" eval="2"/>
        <field name="arch" type="xml">
            <calendar string="Activity" date_start="date_deadline" color="activity_type_id">
                <field name="user_id" avatar_field="avatar_128"/>
                <field name="res_name"/>
                <field name="date_deadline"/>
                <field name="summary"/>
                <field name="activity_type_id" filters="1" invisible="1"/>
            </calendar>
        </field>
    </record>

</odoo>
