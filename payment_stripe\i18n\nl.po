# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-27 15:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Connect Stripe"
msgstr "Connect met Stripe"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "Kan geen verbinding maken met de API."

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Generate your webhook"
msgstr "Genereer je webhook"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Get your Secret and Publishable keys"
msgstr "Krijg je geheime en publiceerbare sleutels"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid ""
"If a webhook is enabled on your Stripe account, this signing secret must be "
"set to authenticate the messages sent from Stripe to Odoo."
msgstr ""
"Als een webhook is ingeschakeld op je Stripe-account, moet dit signing "
"secret  worden ingesteld om de berichten die van Stripe naar Odoo worden "
"verzonden, te verifiëren."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Geen transactie gevonden die overeenkomt met referentie %s."

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Betaalprovider"

#. module: payment_stripe
#: model:ir.actions.act_window,name:payment_stripe.action_payment_acquirer_onboarding
msgid "Payment Acquirers"
msgstr "Betaalproviders"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_account_payment_method
msgid "Payment Methods"
msgstr "Betaalwijzes"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr "Betalingstoken"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr "Betalingstransactie"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__provider
msgid "Provider"
msgstr "Provider"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "Publishable Key"
msgstr "Publiceerbare sleutel"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid intent status: %s"
msgstr "Gegevens ontvangen met ongeldige intentiestatus: %s"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing intent status."
msgstr "Gegevens ontvangen met ontbrekende intentiestatus."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr "Gegevens ontvangen met ontbrekende verkopersreferentie"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_secret_key
msgid "Secret Key"
msgstr "Geheime sleutel"

#. module: payment_stripe
#: model:account.payment.method,name:payment_stripe.payment_method_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_acquirer__provider__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__stripe_payment_intent
msgid "Stripe Payment Intent ID"
msgstr "Stripe betalingsinteresse ID"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Stripe Payment Method ID"
msgstr "Stripe-betaalmethode-ID"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy error: %(error)s"
msgstr "Stripe Proxy-fout: %(error)s"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy: An error occurred when communicating with the proxy."
msgstr ""
"Stripe Proxy: er is een fout opgetreden bij de communicatie met de proxy."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy: Could not establish the connection."
msgstr "Stripe Proxy: kon de verbinding niet tot stand brengen."

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""
"De betalingsdienstaanbieder die bij deze profider moet worden gebruikt"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid ""
"The communication with the API failed.\n"
"Stripe gave us the following info about the problem:\n"
"'%s'"
msgstr ""
"De communicatie met de API is mislukt.\n"
"Stripe gaf ons de volgende informatie over het probleem:\n"
"'%s'"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "The key solely used to identify the account with Stripe"
msgstr ""
"De sleutel die uitsluitend wordt gebruikt om het account bij Stripe te "
"identificeren"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "De transactie is niet gekoppeld aan een token."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_token.py:0
#, python-format
msgid "Unable to convert payment token to new API."
msgstr "Kan betalingstoken niet converteren naar nieuwe API."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid "Webhook Signing Secret"
msgstr "Webhook Signing Secret"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "You Stripe Webhook was successfully set up!"
msgstr "Je Stripe Webhook is succesvol ingesteld!"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot create a Stripe Webhook if your Stripe Secret Key is not set."
msgstr ""
"Je kunt geen Stripe Webhook maken als je Stripe Secret Key niet is "
"ingesteld."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot set the acquirer state to Enabled until your onboarding to Stripe"
" is completed."
msgstr ""
"Je kan de status van de provider niet op geactiveerd instellen zolang je "
"integratie met Stripe niet voltooid is."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot set the acquirer to Test Mode while it is linked with your Stripe"
" account."
msgstr ""
"Je kunt de provider niet in de testmodus zetten terwijl deze is gekoppeld "
"aan je Stripe-account."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Your Stripe Webhook is already set up."
msgstr "Je Stripe Webhook is al ingesteld."
