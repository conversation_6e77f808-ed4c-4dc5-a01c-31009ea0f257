# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* gamification
# 
# Translators:
# <PERSON><PERSON><PERSON> <jak<PERSON>@lepremier.cz>, 2021
# <PERSON> <jan.<PERSON><PERSON><PERSON>@centrum.cz>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# karol<PERSON>a schustero<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:55+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__rank_users_count
msgid "# Users"
msgstr "# Uživatelé"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "%s has joined the challenge"
msgstr "%s se připojil k výzvě"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "%s has refused the challenge"
msgstr "%s odmítl výzvu"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "<br/> %(rank)d. %(user_name)s - %(reward_name)s"
msgstr "<br/> %(rank)d. %(user_name)s - %(reward_name)s"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid ""
"<br/>Nobody has succeeded to reach every goal, no badge is rewarded for this"
" challenge."
msgstr ""
"<br/>Nikomu se nepodařilo dosáhnout jakéhokoliv cíle, za tuto výzvu není "
"odměněn žádný odznak."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid ""
"<br/>Reward (badge %(badge_name)s) for every succeeding user was sent to "
"%(users)s."
msgstr ""
"<br/>Odměna (odznak %(badge_name)s) za každého následujícího uživatele bylo "
"odesláno na %(users)s."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid ""
"<br/>Special rewards were sent to the top competing users. The ranking for "
"this challenge is :"
msgstr ""
"<br/>Speciální odměny byly zaslány špičkovým soutěžícím uživatelům. "
"Hodnocení této výzvy je:"

#. module: gamification
#: model:mail.template,body_html:gamification.mail_template_data_new_rank_reached
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"<table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"    <tbody>\n"
"        <tr>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>\n"
"                    Congratulations\n"
"                    <span t-out=\"object.name or ''\">Joel Willis</span>!\n"
"                </p>\n"
"                <p>\n"
"                    You just reached a new rank : <strong t-out=\"object.rank_id.name or ''\">Newbie</strong>\n"
"                </p>\n"
"                <t t-if=\"object.next_rank_id.name\">\n"
"                    <p>Continue your work to become a <strong t-out=\"object.next_rank_id.name or ''\">Student</strong> !</p>\n"
"                </t>\n"
"                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                    <t t-set=\"gamification_redirection_data\" t-value=\"object.get_gamification_redirection_data()\"/>\n"
"                    <t t-foreach=\"gamification_redirection_data\" t-as=\"data\">\n"
"                        <t t-set=\"url\" t-value=\"data['url']\"/>\n"
"                        <t t-set=\"label\" t-value=\"data['label']\"/>\n"
"                        <a t-att-href=\"url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-out=\"label or ''\">LABEL</a>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p style=\"text-align: center;\">\n"
"                    <img t-attf-src=\"/web/image/gamification.karma.rank/{{ object.rank_id.id }}/image_128\"/>\n"
"                </p>\n"
"            </td>\n"
"        </tr>\n"
"        <tr t-if=\"user.signature\">\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"            </td>\n"
"        </tr>\n"
"    </tbody>\n"
" </table>\n"
"</div>"
msgstr ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"<table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"    <tbody>\n"
"        <tr>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>\n"
"                    Blahopřejeme\n"
"                    <span t-out=\"object.name or ''\">Joele Willisi</span>!\n"
"                </p>\n"
"                <p>\n"
"                    Právě jste dosáhli úrovně : <strong t-out=\"object.rank_id.name or ''\">Nováček</strong>\n"
"                </p>\n"
"                <t t-if=\"object.next_rank_id.name\">\n"
"                    <p>Pokračujte v práci k dosažení <strong t-out=\"object.next_rank_id.name or ''\">Studenta</strong> !</p>\n"
"                </t>\n"
"                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                    <t t-set=\"gamification_redirection_data\" t-value=\"object.get_gamification_redirection_data()\"/>\n"
"                    <t t-foreach=\"gamification_redirection_data\" t-as=\"data\">\n"
"                        <t t-set=\"url\" t-value=\"data['url']\"/>\n"
"                        <t t-set=\"label\" t-value=\"data['label']\"/>\n"
"                        <a t-att-href=\"url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-out=\"label or ''\">ŠTÍTEK</a>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p style=\"text-align: center;\">\n"
"                    <img t-attf-src=\"/web/image/gamification.karma.rank/{{ object.rank_id.id }}/image_128\"/>\n"
"                </p>\n"
"            </td>\n"
"        </tr>\n"
"        <tr t-if=\"user.signature\">\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"            </td>\n"
"        </tr>\n"
"    </tbody>\n"
" </table>\n"
"</div>"

#. module: gamification
#: model:mail.template,body_html:gamification.email_template_goal_reminder
msgid ""
"<div>\n"
"    <strong>Reminder</strong><br/>\n"
"    You have not updated your progress for the goal <t t-out=\"object.definition_id.name or ''\"/> (currently reached at <t t-out=\"object.completeness or ''\"/>%) for at least <t t-out=\"object.remind_update_delay or ''\"/> days. Do not forget to do it.\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.challenge_id.manager_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.challenge_id.manager_id.signature or ''\"/>\n"
"    </t>\n"
"</div>"
msgstr ""
"<div>\n"
"    <strong>Připomínka</strong><br/>\n"
"    Neaktualizovali jste svůj postup pro cíl <t t-out=\"object.definition_id.name or ''\"/> (aktuálně dosaženo <t t-out=\"object.completeness or ''\"/>%) po dobu nejméně <t t-out=\"object.remind_update_delay or ''\"/> dní. Nezapomeňte to udělat.\n"
"    <br/><br/>\n"
"    Děkujeme,\n"
"    <t t-if=\"object.challenge_id.manager_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.challenge_id.manager_id.signature or ''\"/>\n"
"    </t>\n"
"</div>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"fa fa-clock-o fa-3x\" title=\"Goal in Progress\" "
"aria-label=\"Goal in Progress\"/>"
msgstr ""
"<i role=\"img\" class=\"fa fa-clock-o fa-3x\" title=\"Probíhá cíl\" aria-"
"label=\"Probíhá cíl\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"o_green fa fa-check fa-3x\" title=\"Goal Reached\" "
"aria-label=\"Goal Reached\"/>"
msgstr ""
"<i role=\"img\" class=\"o_green fa fa-check fa-3x\" title=\"Dosažený cíl\" "
"aria-label=\"Dosažený cíl\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"o_red fa fa-times fa-3x\" title=\"Goal Failed\" "
"aria-label=\"Goal Failed\"/>"
msgstr ""
"<i role=\"img\" class=\"o_red fa fa-times fa-3x\" title=\"Cíl selhal\" aria-"
"label=\"Cíl selhal\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"<span class=\"o_stat_text\">Related</span>\n"
"                                <span class=\"o_stat_text\">Goals</span>"
msgstr ""
"<span class=\"o_stat_text\">Související</span>\n"
"                                <span class=\"o_stat_text\">Cíle</span>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "<span class=\"o_stat_text\">Users</span>"
msgstr "<span class=\"o_stat_text\">Uživatelé</span>"

#. module: gamification
#: model:mail.template,body_html:gamification.email_template_badge_received
msgid ""
"<table border=\"0\" cellpadding=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" width=\"590\" cellpadding=\"0\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Badge</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.badge_id.name or ''\"/>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Congratulations <t t-out=\"object.user_id.name or ''\"/> !<br/>\n"
"                        You just received badge <strong t-out=\"object.badge_id.name or ''\"/> !<br/>\n"
"                        <table t-if=\"not is_html_empty(object.badge_id.description)\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" style=\"width: 560px; margin-top: 5px;\">\n"
"                            <tbody><tr>\n"
"                                <td valign=\"center\">\n"
"                                    <img t-attf-src=\"/web/image/gamification.badge/{{ object.badge_id.id }}/image_128/80x80\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                                </td>\n"
"                                <td valign=\"center\">\n"
"                                    <cite t-out=\"object.badge_id.description or ''\"/>\n"
"                                </td>\n"
"                            </tr></tbody>\n"
"                        </table>\n"
"                        <br/>\n"
"                        <t t-if=\"object.sender_id\">\n"
"                            This badge was granted by <strong t-out=\"object.sender_id.name or ''\"/>.\n"
"                        </t>\n"
"                        <br/>\n"
"                        <t t-if=\"object.comment\" t-out=\"object.comment or ''\"/>\n"
"                        <br/><br/>\n"
"                        Thank you,\n"
"                        <t t-if=\"object.sender_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.sender_id.signature or ''\"/>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; font-size: 12px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=gamification\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" width=\"590\" cellpadding=\"0\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- ZÁHLAVÍ -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Váš odznak</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.badge_id.name or ''\"/>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- OBSAH -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Congratulations <t t-out=\"object.user_id.name or ''\"/> !<br/>\n"
"                        You just received badge <strong t-out=\"object.badge_id.name or ''\"/> !<br/>\n"
"                        <table t-if=\"not is_html_empty(object.badge_id.description)\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" style=\"width: 560px; margin-top: 5px;\">\n"
"                            <tbody><tr>\n"
"                                <td valign=\"center\">\n"
"                                    <img t-attf-src=\"/web/image/gamification.badge/{{ object.badge_id.id }}/image_128/80x80\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                                </td>\n"
"                                <td valign=\"center\">\n"
"                                    <cite t-out=\"object.badge_id.description or ''\"/>\n"
"                                </td>\n"
"                            </tr></tbody>\n"
"                        </table>\n"
"                        <br/>\n"
"                        <t t-if=\"object.sender_id\">\n"
"                            Tento odznak byl přidělen uživatelem <strong t-out=\"object.sender_id.name or ''\"/>.\n"
"                        </t>\n"
"                        <br/>\n"
"                        <t t-if=\"object.comment\" t-out=\"object.comment or ''\"/>\n"
"                        <br/><br/>\n"
"                        Děkujeme,\n"
"                        <t t-if=\"object.sender_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.sender_id.signature or ''\"/>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!--ZÁPATÍ -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; font-size: 12px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">VašeSpolečnost</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.priklad.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- PROVOZ -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Provozované společností <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=gamification\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"

#. module: gamification
#: model:mail.template,body_html:gamification.simple_report_template
msgid ""
"<table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"background-color: #EEE; border-collapse: collapse;\">\n"
"<tr>\n"
"    <td valign=\"top\" align=\"center\">\n"
"        <t t-set=\"object_ctx\" t-value=\"ctx.get('object')\"/>\n"
"        <t t-set=\"company\" t-value=\"object_ctx and object_ctx.company_id or user.company_id\"/>\n"
"        <t t-set=\"challenge_lines\" t-value=\"ctx.get('challenge_lines', [])\"/>\n"
"        <table cellspacing=\"0\" cellpadding=\"0\" width=\"600\" style=\"margin: 0 auto; width: 570px;\">\n"
"            <tr><td>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n"
"                    <tr>\n"
"                        <div>\n"
"                            <t t-if=\"object.visibility_mode == 'ranking'\">\n"
"                                <td style=\"padding:15px;\">\n"
"                                    <p style=\"font-size:20px;color:#666666;\" align=\"center\">Leaderboard</p>\n"
"                                </td>\n"
"                            </t>\n"
"                        </div>\n"
"                    </tr>\n"
"                </table>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" bgcolor=\"#fff\" style=\"background-color:#fff;\">\n"
"                    <tr><td style=\"padding: 15px;\">\n"
"                        <t t-if=\"object.visibility_mode == 'personal'\">\n"
"                            <span style=\"color:#666666;font-size:13px;\">Here is your current progress in the challenge <strong t-out=\"object.name or ''\"/>.</span>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:20px;\">\n"
"                                <tr>\n"
"                                    <td align=\"center\">\n"
"                                        <div>Personal Performance</div>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;color:#666666;\">\n"
"                                <thead>\n"
"                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                        <th align=\"left\" style=\"padding-bottom: 0px;width:40%;text-align:left;\">Goals</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"left\">Target</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Current</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Completeness</th>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                    </tr>\n"
"                                </thead>\n"
"                                <tbody t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                    <tr style=\"font-weight:bold;\">\n"
"                                        <td style=\"padding: 20px 0;\" align=\"left\">\n"
"                                            <t t-out=\"line['name'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                (<t t-out=\"line['full_suffix'] or ''\"/>)\n"
"                                            </t>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['current'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;font-size:25px;color:#9A6C8E;\" align=\"right\"><strong><t t-out=\"int(line['completeness']) or ''\"/>%</strong></td>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#e3e3e3;\"/>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>                   \n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"color:#A8A8A8;font-size:13px;\">\n"
"                                Challenge: <strong t-out=\"object.name or ''\"/>.\n"
"                            </span> \n"
"                            <t t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                <!-- Header + Button table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:35px;\">\n"
"                                    <tr>\n"
"                                        <td width=\"50%\">\n"
"                                            <div>Top Achievers for goal <strong t-out=\"line['name'] or ''\"/></div>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table>\n"
"                                <!-- Podium -->\n"
"                                <t t-if=\"len(line['goals']) == 2\">\n"
"                                    <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:10px;\">\n"
"                                        <tr><td style=\"padding:0 30px;\">\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"table-layout: fixed;\">\n"
"                                                <tr>\n"
"                                                    <t t-set=\"top_goals\" t-value=\"line['goals'][:3]\"/>\n"
"                                                    <t t-foreach=\"top_goals\" t-as=\"goal\">\n"
"                                                        <td align=\"center\" style=\"width:32%;\">\n"
"                                                            <t t-if=\"loop.index == 1\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:40px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"95\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"75\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#b898b0'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"50\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'2'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 2\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"''\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"85\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'1'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 3\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:60px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#c8afc1'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"35\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'3'\"/>\n"
"                                                            </t>\n"
"                                                            <div style=\"margin:0 3px 0 3px;height:220px;\">\n"
"                                                                <div t-attf-style=\"height:{{ heightA }}px;\">\n"
"                                                                    <t t-out=\"extra_div or ''\"/>\n"
"                                                                    <div style=\"height:55px;\">\n"
"                                                                        <img style=\"margin-bottom:5px;width:50px;height:50px;border-radius:50%;object-fit:cover;\" t-att-src=\"image_data_uri(object.env['res.users'].browse(goal['user_id']).partner_id.image_128)\" t-att-alt=\"goal['name']\"/>\n"
"                                                                    </div>\n"
"                                                                    <div align=\"center\" t-attf-style=\"color:{{ bgColor }};height:20px\">\n"
"                                                                        <t t-out=\"goal['name'] or ''\"/>\n"
"                                                                    </div>\n"
"                                                                </div>\n"
"                                                                <div t-attf-style=\"background-color:{{ bgColor }};height:{{ heightB }}px;\">\n"
"                                                                    <strong><span t-attf-style=\"color:#fff;font-size:{{ fontSize }}px;\" t-out=\"podiumPosition or ''\"/></strong>\n"
"                                                                </div>\n"
"                                                                <div style=\"height:30px;\">\n"
"                                                                    <t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/>\n"
"                                                                    <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                                        <t t-out=\"line['full_suffix'] or ''\"/>\n"
"                                                                    </t>\n"
"                                                                </div>\n"
"                                                            </div>\n"
"                                                        </td>\n"
"                                                    </t>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                            </td>\n"
"                                        </tr>\n"
"                                    </table>\n"
"                                </t>\n"
"                                <!-- data table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-bottom:5px\">\n"
"                                    <tr>\n"
"                                        <td>\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;margin-bottom:5px;color:#666666;\">\n"
"                                                <thead>\n"
"                                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                                        <th style=\"width:15%;text-align:center;\">Rank</th>\n"
"                                                        <th style=\"width:25%;text-align:left;\">Name</th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Performance \n"
"                                                            <t t-if=\"line['suffix']\">\n"
"                                                                (<t t-out=\"line['suffix'] or ''\"/>)\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"line['monetary']\">\n"
"                                                                (<t t-out=\"company.currency_id.symbol or ''\"/>)\n"
"                                                            </t>\n"
"                                                        </th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Completeness</th>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                                    </tr>\n"
"                                                </thead>\n"
"                                                <tbody t-foreach=\"line['goals']\" t-as=\"goal\">\n"
"                                                    <tr>\n"
"                                                        <t t-set=\"tdBgColor\" t-value=\"'#fff'\"/>\n"
"                                                        <t t-set=\"tdColor\" t-value=\"'gray'\"/>\n"
"                                                        <t t-set=\"mutedColor\" t-value=\"'#AAAAAA'\"/>\n"
"                                                        <t t-set=\"tdPercentageColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                        <td width=\"15%\" align=\"center\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:20px;\"><t t-out=\"goal['rank']+1 or ''\"/>\n"
"                                                        </td>\n"
"                                                        <td width=\"25%\" align=\"left\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:13px;\"><t t-out=\"goal['name'] or ''\"/></td>\n"
"                                                        <td width=\"30%\" align=\"right\" t-attf-style=\"background-color:{{ tdBgColor }};padding:5px 0;line-height:1;\"><t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/><br/><span t-attf-style=\"font-size:13px;color:{{ mutedColor }};\">on <t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/></span>\n"
"                                                        </td>\n"
"                                                        <td width=\"30%\" t-attf-style=\"color:{{ tdPercentageColor }};background-color:{{ tdBgColor }};padding-right:15px;font-size:22px;\" align=\"right\"><strong><t t-out=\"int(goal['completeness']) or ''\"/>%</strong></td>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#DADADA;\"/>\n"
"                                                    </tr>\n"
"                                                </tbody>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table> \n"
"                            </t>\n"
"                        </t>\n"
"                    </td></tr>\n"
"                </table>\n"
"            </td></tr>\n"
"        </table>\n"
"    </td>\n"
"</tr>\n"
"</table>\n"
"            "
msgstr ""
"<table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"background-color: #EEE; border-collapse: collapse;\">\n"
"<tr>\n"
"    <td valign=\"top\" align=\"center\">\n"
"        <t t-set=\"object_ctx\" t-value=\"ctx.get('object')\"/>\n"
"        <t t-set=\"company\" t-value=\"object_ctx and object_ctx.company_id or user.company_id\"/>\n"
"        <t t-set=\"challenge_lines\" t-value=\"ctx.get('challenge_lines', [])\"/>\n"
"        <table cellspacing=\"0\" cellpadding=\"0\" width=\"600\" style=\"margin: 0 auto; width: 570px;\">\n"
"            <tr><td>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n"
"                    <tr>\n"
"                        <div>\n"
"                            <t t-if=\"object.visibility_mode == 'ranking'\">\n"
"                                <td style=\"padding:15px;\">\n"
"                                    <p style=\"font-size:20px;color:#666666;\" align=\"center\">Výsledková tabulka</p>\n"
"                                </td>\n"
"                            </t>\n"
"                        </div>\n"
"                    </tr>\n"
"                </table>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" bgcolor=\"#fff\" style=\"background-color:#fff;\">\n"
"                    <tr><td style=\"padding: 15px;\">\n"
"                        <t t-if=\"object.visibility_mode == 'personal'\">\n"
"                            <span style=\"color:#666666;font-size:13px;\">Zde je váš aktuální postup výzvy <strong t-out=\"object.name or ''\"/>.</span>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:20px;\">\n"
"                                <tr>\n"
"                                    <td align=\"center\">\n"
"                                        <div>Osobní výkon</div>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;color:#666666;\">\n"
"                                <thead>\n"
"                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                        <th align=\"left\" style=\"padding-bottom: 0px;width:40%;text-align:left;\">Cíle</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"left\">Cílová</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Aktuální</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Úplnost</th>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                    </tr>\n"
"                                </thead>\n"
"                                <tbody t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                    <tr style=\"font-weight:bold;\">\n"
"                                        <td style=\"padding: 20px 0;\" align=\"left\">\n"
"                                            <t t-out=\"line['name'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                (<t t-out=\"line['full_suffix'] or ''\"/>)\n"
"                                            </t>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['current'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;font-size:25px;color:#9A6C8E;\" align=\"right\"><strong><t t-out=\"int(line['completeness']) or ''\"/>%</strong></td>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#e3e3e3;\"/>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>                   \n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"color:#A8A8A8;font-size:13px;\">\n"
"                                Výzva: <strong t-out=\"object.name or ''\"/>.\n"
"                            </span> \n"
"                            <t t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                <!-- Záhlaví + Tabulka tlačítek -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:35px;\">\n"
"                                    <tr>\n"
"                                        <td width=\"50%\">\n"
"                                            <div>Největší úspěchy v cíli <strong t-out=\"line['name'] or ''\"/></div>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table>\n"
"                                <!-- Pódium -->\n"
"                                <t t-if=\"len(line['goals']) == 2\">\n"
"                                    <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:10px;\">\n"
"                                        <tr><td style=\"padding:0 30px;\">\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"table-layout: fixed;\">\n"
"                                                <tr>\n"
"                                                    <t t-set=\"top_goals\" t-value=\"line['goals'][:3]\"/>\n"
"                                                    <t t-foreach=\"top_goals\" t-as=\"goal\">\n"
"                                                        <td align=\"center\" style=\"width:32%;\">\n"
"                                                            <t t-if=\"loop.index == 1\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:40px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"95\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"75\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#b898b0'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"50\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'2'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 2\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"''\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"85\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'1'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 3\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:60px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#c8afc1'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"35\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'3'\"/>\n"
"                                                            </t>\n"
"                                                            <div style=\"margin:0 3px 0 3px;height:220px;\">\n"
"                                                                <div t-attf-style=\"height:{{ heightA }}px;\">\n"
"                                                                    <t t-out=\"extra_div or ''\"/>\n"
"                                                                    <div style=\"height:55px;\">\n"
"                                                                        <img style=\"margin-bottom:5px;width:50px;height:50px;border-radius:50%;object-fit:cover;\" t-att-src=\"image_data_uri(object.env['res.users'].browse(goal['user_id']).partner_id.image_128)\" t-att-alt=\"goal['name']\"/>\n"
"                                                                    </div>\n"
"                                                                    <div align=\"center\" t-attf-style=\"color:{{ bgColor }};height:20px\">\n"
"                                                                        <t t-out=\"goal['name'] or ''\"/>\n"
"                                                                    </div>\n"
"                                                                </div>\n"
"                                                                <div t-attf-style=\"background-color:{{ bgColor }};height:{{ heightB }}px;\">\n"
"                                                                    <strong><span t-attf-style=\"color:#fff;font-size:{{ fontSize }}px;\" t-out=\"podiumPosition or ''\"/></strong>\n"
"                                                                </div>\n"
"                                                                <div style=\"height:30px;\">\n"
"                                                                    <t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/>\n"
"                                                                    <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                                        <t t-out=\"line['full_suffix'] or ''\"/>\n"
"                                                                    </t>\n"
"                                                                </div>\n"
"                                                            </div>\n"
"                                                        </td>\n"
"                                                    </t>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                            </td>\n"
"                                        </tr>\n"
"                                    </table>\n"
"                                </t>\n"
"                                <!-- datová tabulka -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-bottom:5px\">\n"
"                                    <tr>\n"
"                                        <td>\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;margin-bottom:5px;color:#666666;\">\n"
"                                                <thead>\n"
"                                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                                        <th style=\"width:15%;text-align:center;\">Hodnost</th>\n"
"                                                        <th style=\"width:25%;text-align:left;\">Jméno</th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Výkon \n"
"                                                            <t t-if=\"line['suffix']\">\n"
"                                                                (<t t-out=\"line['suffix'] or ''\"/>)\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"line['monetary']\">\n"
"                                                                (<t t-out=\"company.currency_id.symbol or ''\"/>)\n"
"                                                            </t>\n"
"                                                        </th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Úplnost</th>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                                    </tr>\n"
"                                                </thead>\n"
"                                                <tbody t-foreach=\"line['goals']\" t-as=\"goal\">\n"
"                                                    <tr>\n"
"                                                        <t t-set=\"tdBgColor\" t-value=\"'#fff'\"/>\n"
"                                                        <t t-set=\"tdColor\" t-value=\"'gray'\"/>\n"
"                                                        <t t-set=\"mutedColor\" t-value=\"'#AAAAAA'\"/>\n"
"                                                        <t t-set=\"tdPercentageColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                        <td width=\"15%\" align=\"center\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:20px;\"><t t-out=\"goal['rank']+1 or ''\"/>\n"
"                                                        </td>\n"
"                                                        <td width=\"25%\" align=\"left\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:13px;\"><t t-out=\"goal['name'] or ''\"/></td>\n"
"                                                        <td width=\"30%\" align=\"right\" t-attf-style=\"background-color:{{ tdBgColor }};padding:5px 0;line-height:1;\"><t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/><br/><span t-attf-style=\"font-size:13px;color:{{ mutedColor }};\">na <t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/></span>\n"
"                                                        </td>\n"
"                                                        <td width=\"30%\" t-attf-style=\"color:{{ tdPercentageColor }};background-color:{{ tdBgColor }};padding-right:15px;font-size:22px;\" align=\"right\"><strong><t t-out=\"int(goal['completeness']) or ''\"/>%</strong></td>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#DADADA;\"/>\n"
"                                                    </tr>\n"
"                                                </tbody>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table> \n"
"                            </t>\n"
"                        </t>\n"
"                    </td></tr>\n"
"                </table>\n"
"            </td></tr>\n"
"        </table>\n"
"    </td>\n"
"</tr>\n"
"</table>\n"
"            "

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.badge_list_action
msgid ""
"A badge is a symbolic token granted to a user as a sign of reward.\n"
"                It can be deserved automatically when some conditions are met or manually by users.\n"
"                Some badges are harder than others to get with specific conditions."
msgstr ""
"Odznak je symbol udělený uživateli jako znak odměny.\n"
"To může být zaslouženo automaticky, když jsou splněny některé podmínky, nebo ručně uživateli.\n"
"Některé odznaky je těžší získat než jiné v závislosti na specifickýxch podmákách."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_definition_list_action
msgid ""
"A goal definition is a technical specification of a condition to reach.\n"
"                The dates, values to reach or users are defined in goal instance."
msgstr ""
"Definice cíle je technická specifikace podmínky, které má být dosaženo.\n"
"                Data, hodnoty k zásahu nebo uživatelé jsou definováni v instanci cíle."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__condition
#: model:ir.model.fields,help:gamification.field_gamification_goal__definition_condition
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__condition
msgid ""
"A goal is considered as completed when the current value is compared to the "
"value to reach"
msgstr ""
"Cíl je považován za dokončený, když je aktuální hodnota porovnána s "
"hodnotou, která má být dosažena"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_list_action
msgid ""
"A goal is defined by a user and a goal definition.\n"
"                Goals can be created automatically by using challenges."
msgstr ""
"Cíl je definován uživatelem a definicí cíle.\n"
"Cíle lze vytvářet automaticky pomocí výzev."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.gamification_karma_ranks_action
msgid ""
"A rank correspond to a fixed karma level. The more you have karma, the more your rank is high.\n"
"                    This is used to quickly know which user is new or old or highly or not active."
msgstr ""
"Hodnost odpovídá pevné úrovni karmy. Čím větší máte karmu, tím vyšší je vaše hodnost.\n"
"Slouží k rychlému zjištění, který uživatel je nový nebo starý, vysoce aktivní, nebo neaktivní."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__users
msgid "A selected list of users"
msgstr "Vybraný seznam uživatelů"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__action_id
msgid "Action"
msgstr "Akce"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_needaction
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_needaction
msgid "Action Needed"
msgstr "Vyžaduje akci"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__active
msgid "Active"
msgstr "Aktivní"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Advanced Options"
msgstr "Rozšířené volby"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth
msgid "Allowance to Grant"
msgstr "Příspěvek na grant"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__user_domain
msgid "Alternative to a list of users"
msgstr "Alternativa k seznamu uživatelů"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Objeví se v"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.gamification_badge_view_search
msgid "Archived"
msgstr "Archivováno"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Assign Challenge to"
msgstr "Přiřadit výzvu k"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.challenge_list_action
msgid ""
"Assign a list of goals to chosen users to evaluate them.\n"
"                The challenge can use a period (weekly, monthly...) for automatic creation of goals.\n"
"                The goals are created for the specified users or member of the group."
msgstr ""
"Vybraným uživatelům přiřaďte seznam cílů k jejich vyhodnocení.\n"
"Výzva může použít období (týdenní, měsíční ...) pro automatické vytváření cílů.\n"
"Cíle jsou vytvářeny pro určené uživatele nebo člena skupiny."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_attachment_count
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_attachment_count
msgid "Attachment Count"
msgstr "Počet příloh"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth_user_ids
msgid "Authorized Users"
msgstr "Oprávnění uživatelé"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__python
msgid "Automatic: execute a specific Python code"
msgstr "Automaticky: provede specifický kód Pythonu"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__count
msgid "Automatic: number of records"
msgstr "Automaticky: počet záznamů"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__sum
msgid "Automatic: sum on a field"
msgstr "Automaticky: součet na poli"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_bachelor
msgid "Bachelor"
msgstr "Bakalář"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__badge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__badge_id
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Badge"
msgstr "Odznak"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Badge Description"
msgstr "Popis odznaku"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__level
msgid "Badge Level"
msgstr "Úroveň odznaku"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_list_view
msgid "Badge List"
msgstr "Seznam odznaků"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__badge_name
msgid "Badge Name"
msgstr "Název odznaku"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.badge_list_action
#: model:ir.model.fields,field_description:gamification.field_res_users__badge_ids
#: model:ir.ui.menu,name:gamification.gamification_badge_menu
msgid "Badges"
msgstr "Odznaky"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"Badges are granted when a challenge is finished. This is either at the end "
"of a running period (eg: end of the month for a monthly challenge), at the "
"end date of a challenge (if no periodicity is set) or when the challenge is "
"manually closed."
msgstr ""
"Po dokončení výzvy se udělují odznaky. To je buď na konci běžícího období "
"(např .: konec měsíce pro měsíční výzvu), na konci data výzvy (pokud není "
"stanovena periodicita), nebo když je výzva ručně ukončena."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_mode
msgid "Batch Mode"
msgstr "Dávkový režim"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_idea
msgid "Brilliant"
msgstr "Skvělý"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__bronze
msgid "Bronze"
msgstr "Bronz"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__bronze_badge
msgid "Bronze badges count"
msgstr "Počet bronzových odznaků"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Can not grant"
msgstr "Nelze udělit"

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid "Can not modify the configuration of a started goal"
msgstr "Nelze změnit konfiguraci zahájeného cíle"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Cancel"
msgstr "Zrušit"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__canceled
msgid "Canceled"
msgstr "Zrušeno"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Category"
msgstr "Kategorie"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__challenge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__challenge_id
msgid "Challenge"
msgstr "Výzva"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__line_id
msgid "Challenge Line"
msgstr "Řádek výzvy"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_line_list_view
msgid "Challenge Lines"
msgstr "Řádky výzvy"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__name
msgid "Challenge Name"
msgstr "Název výzvy"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__challenge_id
msgid "Challenge originating"
msgstr "Vyvolání výzvy"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__challenge_id
msgid ""
"Challenge that generated the goal, assign challenge to users to generate "
"goals with a value in this field."
msgstr ""
"Výzva, která vytvořila cíl, přiřazuje výzvu uživatelům k tvorbě cíle s "
"hodnotou v této oblasti."

#. module: gamification
#: model:mail.template,name:gamification.simple_report_template
msgid "Challenge: Simple Challenge Report Progress"
msgstr "Výzva: Jednoduchá zpráva o průběhu výzvy"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.challenge_list_action
#: model:ir.ui.menu,name:gamification.gamification_challenge_menu
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Challenges"
msgstr "Výzvy"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_max
msgid "Check to set a monthly limit per person of sending this badge"
msgstr "Zaškrtnutím nastavte měsíční limit na osobu odesílající tento odznak"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Clickable Goals"
msgstr "Klikací cíle"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__closed
msgid "Closed goal"
msgstr "Uzavřený cíl"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__comment
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__comment
msgid "Comment"
msgstr "Komentář"

#. module: gamification
#: model:gamification.challenge,name:gamification.challenge_base_discover
msgid "Complete your Profile"
msgstr "Dokončete svůj profil"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__completeness
msgid "Completeness"
msgstr "Úplnost"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__computation_mode
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__computation_mode
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Computation Mode"
msgstr "Režim výpočtu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__condition
msgid "Condition"
msgstr "Stav"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__consolidated
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "Consolidated"
msgstr "Konsolidováno"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_new_simplified_res_users
msgid "Create User"
msgstr "Vytvořit uživatele"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.badge_list_action
msgid "Create a new badge"
msgstr "Vytvořit nový odznak"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.challenge_list_action
msgid "Create a new challenge"
msgstr "Vytvořit novou výzvu"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_list_action
msgid "Create a new goal"
msgstr "Vytvořit nový cíl"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_definition_list_action
msgid "Create a new goal definition"
msgstr "Vytvořit novou definici cíle"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.gamification_karma_ranks_action
msgid "Create a new rank"
msgstr "Vytvořit nové hodnocení"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.action_new_simplified_res_users
msgid ""
"Create and manage users that will connect to the system. Users can be "
"deactivated should there be a period of time during which they will/should "
"not connect to the system. You can assign them groups in order to give them "
"specific access to the applications they need to use in the system."
msgstr ""
"Vytvořit a spravovat uživatele, kteří se připojí k systému. Uživatelé mohou "
"být deaktivováni, pokud by existoval určitý čas, během kterého by se chtěli "
"/ neměli připojovat k systému. Můžete jim přiřadit skupiny, abyste jim "
"poskytli konkrétní přístup k aplikacím, které potřebují k použití v systému."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__current
msgid "Current"
msgstr "Aktuální"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__current
msgid "Current Value"
msgstr "Aktuální hodnota"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__daily
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__daily
msgid "Daily"
msgstr "Denně"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Data"
msgstr "Data"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__field_date_id
msgid "Date Field"
msgstr "Datové pole"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__computation_mode
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__computation_mode
msgid ""
"Define how the goals will be computed. The result of the operation will be "
"stored in the field 'Current'."
msgstr ""
"Definujte, jak budou cíle vypočítávány. Výsledek operace bude uložen v poli "
"'Aktuální'."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Definujte viditelnost výzvy prostřednictvím nabídek"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_condition
msgid "Definition Condition"
msgstr "Popis podmínky"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_description
msgid "Definition Description"
msgstr "popis definice"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Depending on the Display mode, reports will be individual or shared."
msgstr ""
"V závislosti na režimu zobrazení budou přehledy individuální nebo sdílené."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"Describe the challenge: what is does, who it targets, why it matters..."
msgstr "Popište výzvu: co se děje, na koho se zaměřuje, na čem záleží ..."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Describe what they did and why it matters (will be public)"
msgstr "Popište, co udělali a proč je to důležité (bude veřejné)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__description
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__description
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__description
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Description"
msgstr "Popis"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__visibility_mode
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_display
msgid "Display Mode"
msgstr "Režim zobrazení"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__display_mode
msgid "Displayed as"
msgstr "Zobrazeno jako"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_distinctive_field
msgid "Distinctive field for batch user"
msgstr "Rozlišovací pole pro dávkové uživatele"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_doctor
msgid "Doctor"
msgstr "Doktor"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__domain
msgid ""
"Domain for filtering records. General rule, not user depending, e.g. "
"[('state', '=', 'done')]. The expression can contain reference to 'user' "
"which is a browse record of the current user if not in batch mode."
msgstr ""
"Doména pro filtrování záznamů. Obecné pravidlo, nezávisí na uživateli, např."
" [('state', '=', 'done')]. Výraz může obsahovat odkaz na 'user', což je "
"záznam procházení aktuálního uživatele, pokud není v dávkovém režimu."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__done
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Done"
msgstr "Hotovo"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__draft
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__draft
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Draft"
msgstr "Návrh"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_newbie
msgid "Earn your first points and join the adventure !"
msgstr "Získejte první body a připojte se k dobrodružství!"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__end_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__end_date
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "End Date"
msgstr "Datum ukončení"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_mode
msgid "Evaluate the expression in batch instead of once for each user"
msgstr "Vyhodnotit výraz v dávce namísto jednou pro každého uživatele"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_user_expression
msgid "Evaluated expression for batch mode"
msgstr "Vyhodnocený výraz pro dávkový režim"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__everyone
msgid "Everyone"
msgstr "Každý"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__display_mode__boolean
msgid "Exclusive (done or not-done)"
msgstr "Exkluzivní (hotové nebo nedokončené)"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__failed
msgid "Failed"
msgstr "Selhalo"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__field_id
msgid "Field to Sum"
msgstr "Pole na součet"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__domain
msgid "Filter Domain"
msgstr "Filtrovat doménu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_follower_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_follower_ids
msgid "Followers"
msgstr "Sledující"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_partner_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledující (partneři)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_first_id
msgid "For 1st user"
msgstr "Pro 1. uživatele"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_second_id
msgid "For 2nd user"
msgstr "Pro 2. uživatele"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_third_id
msgid "For 3rd user"
msgstr "Pro třetí uživatele"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_id
msgid "For Every Succeeding User"
msgstr "Pro každého úspěšného uživatele"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Formatting Options"
msgstr "Možnosti formátování"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__level
msgid "Forum Badge Level"
msgstr "Úroveň odznaku fóra"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "From"
msgstr "Od"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__full_suffix
msgid "Full Suffix"
msgstr "Plná přípona"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge
msgid "Gamification Badge"
msgstr "Odznak gamifikace"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Gamifikační výzva"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal
msgid "Gamification Goal"
msgstr "Gamifikační cíl"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal_definition
msgid "Gamification Goal Definition"
msgstr "Definice cíle gamifikace"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal_wizard
msgid "Gamification Goal Wizard"
msgstr "Průvodce cílem gamifikace"

#. module: gamification
#: model:ir.ui.menu,name:gamification.gamification_menu
msgid "Gamification Tools"
msgstr "Nástroje Gamifikace"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge_user
msgid "Gamification User Badge"
msgstr "Odznak uživatele gamifikace"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge_user_wizard
msgid "Gamification User Badge Wizard"
msgstr "Průvodce odznakem uživatele Gamifikace"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_challenge_line
msgid "Gamification generic goal for challenge"
msgstr "Obecný cíl gamifikace pro výzvu"

#. module: gamification
#: model:ir.actions.server,name:gamification.ir_cron_check_challenge_ir_actions_server
#: model:ir.cron,cron_name:gamification.ir_cron_check_challenge
#: model:ir.cron,name:gamification.ir_cron_check_challenge
msgid "Gamification: Goal Challenge Check"
msgstr "Gamifikace: kontrola cíle"

#. module: gamification
#: model:ir.actions.server,name:gamification.ir_cron_consolidate_last_month_ir_actions_server
#: model:ir.cron,cron_name:gamification.ir_cron_consolidate_last_month
#: model:ir.cron,name:gamification.ir_cron_consolidate_last_month
msgid "Gamification: Karma tracking consolidation"
msgstr "Gamifikace: Konsolidace sledování karmy"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__goal_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Goal"
msgstr "Cíl"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__name
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Goal Definition"
msgstr "Určení cíle"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goal_definition_list_action
#: model:ir.ui.menu,name:gamification.gamification_definition_menu
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_list_view
msgid "Goal Definitions"
msgstr "Definice cílů"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__description
msgid "Goal Description"
msgstr "Popis cíle"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Goal Failed"
msgstr "Cíl se nezdařil"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_list_view
msgid "Goal List"
msgstr "Seznam cílů"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__condition
msgid "Goal Performance"
msgstr "Výkonnost cíle"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Goal Reached"
msgstr "Cíl dosažen"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.challenge_list_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Goal definitions"
msgstr "Definice cílů"

#. module: gamification
#: model:mail.template,name:gamification.email_template_goal_reminder
msgid "Goal: Reminder for Goal Update"
msgstr "Cíl: Připomenutí aktualizace cíle"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goal_list_action
#: model:ir.ui.menu,name:gamification.gamification_goal_menu
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Goals"
msgstr "Cíle"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__gold
msgid "Gold"
msgstr "Zlato"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__gold_badge
msgid "Gold badges count"
msgstr "Počet zlatých odznaků"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_good_job
msgid "Good Job"
msgstr "Dobrá práce"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Grant"
msgstr "Povolení"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_grant_wizard
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Grant Badge"
msgstr "Udělit odznak"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Grant Badge To"
msgstr "Udělit odznak komu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Grant this Badge"
msgstr "Udělit tento odznak"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_user_kanban_view
msgid "Granted by"
msgstr "Udělen od"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Granting"
msgstr "Udělování"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Group By"
msgstr "Seskupit podle"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__report_message_group_id
msgid "Group that will receive a copy of the report in addition to the user"
msgstr "Skupina, která kromě uživatele obdrží kopii zprávy"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "HR Challenges"
msgstr "Výzvy personálního oddělení"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__has_message
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__has_message
msgid "Has Message"
msgstr "Má zprávu"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_hidden
msgid "Hidden"
msgstr "Skrytý"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "How is the goal computed?"
msgstr "Jak jsou cíle vypočítávány?"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__challenge_category__hr
msgid "Human Resources / Engagement"
msgstr "Lidské zdroje / angažovanost"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__id
msgid "ID"
msgstr "ID"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__res_id_field
msgid "ID Field of user"
msgstr "ID Pole uživatele"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__remaining_sending
msgid "If a maximum is set"
msgstr "Pokud je nastaveno maximum"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_needaction
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_unread
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_needaction
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_unread
msgid "If checked, new messages require your attention."
msgstr "Pokud je zaškrtnuto, nové zprávy vyžadují vaši pozornost."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_has_error
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Pokud je zaškrtnuto, některé zprávy mají chybu při doručení."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user__challenge_id
msgid "If this badge was rewarded through a challenge"
msgstr "Pokud byl tento odznak odměněn výzvou"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_1920
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_1920
msgid "Image"
msgstr "Obrázek"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_1024
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_1024
msgid "Image 1024"
msgstr "Obrázek 1024"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_128
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_128
msgid "Image 128"
msgstr "Obrázek 128"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_256
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_256
msgid "Image 256"
msgstr "Obrázek 256"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_512
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_512
msgid "Image 512"
msgstr "Obrázek 512"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__inprogress
msgid "In Progress"
msgstr "Probíhá"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid ""
"In batch mode, the domain is evaluated globally. If enabled, do not use "
"keyword 'user' in above filter domain."
msgstr ""
"V dávkovém režimu je doména vyhodnocena globálně. Pokud je tato možnost "
"povolena, nepoužívejte klíčové slovo „uživatel“ ve výše uvedené doméně "
"filtru."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_distinctive_field
msgid ""
"In batch mode, this indicates which field distinguishes one user from the "
"other, e.g. user_id, partner_id..."
msgstr ""
"V dávkovém režimu to označuje, které pole odlišuje jednoho uživatele od "
"druhého, např. user_id, partner_id..."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__last_update
msgid ""
"In case of manual goal, reminders are sent if the goal as not been updated "
"for a while (defined in challenge). Ignored in case of non-manual goal or "
"goal not linked to a challenge."
msgstr ""
"V případě ručně zadaného cíle jsou připomenutí odeslány, pokud cíl není "
"aktualizován na chvíli (definován jako výzva). Ignorovány v případě "
"neúmyslného cíle nebo cíle nesouvisejícího s výzvou."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__inprogress
msgid "In progress"
msgstr "Probíhá"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__visibility_mode__personal
msgid "Individual Goals"
msgstr "Individuální cíle"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__model_inherited_ids
msgid "Inherited models"
msgstr "Zděděné modely"

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin3
#: model:gamification.goal.definition,name:gamification.definition_base_invite
msgid "Invite new Users"
msgstr "Pozvat nové uživatele"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_is_follower
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_is_follower
msgid "Is Follower"
msgstr "Je sledující"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__karma
msgid "Karma"
msgstr "Karma"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__karma_tracking_ids
msgid "Karma Changes"
msgstr "Změny karmy"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__last_report_date
msgid "Last Report Date"
msgstr "Datum poslední zprávy"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__last_update
msgid "Last Update"
msgstr "Poslední aktualizace"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__visibility_mode__ranking
msgid "Leader Board (Group Ranking)"
msgstr "Žebříček (hodnocení skupin)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_max_number
msgid "Limitation Number"
msgstr "Číslo omezení"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Line List"
msgstr "Řádek seznamu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__line_ids
msgid "Lines"
msgstr "Řádky"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__line_ids
msgid "List of goals that will be set"
msgstr "Seznam cílů, které budou nastaveny"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__user_ids
msgid "List of users participating to the challenge"
msgstr "Seznam uživatelů účastnících se výzvy"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_main_attachment_id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hlavní příloha"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_master
msgid "Master"
msgstr "Mistr"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Member"
msgstr "Člen"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_has_error
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_has_error
msgid "Message Delivery error"
msgstr "Chyba při doručování zpráv"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_ids
msgid "Messages"
msgstr "Zprávy"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_karma_rank__karma_min
msgid "Minimum karma needed to reach this rank"
msgstr "K dosažení této hodnosti je nutná minimální karma"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__model_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Model"
msgstr "Model"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_monetary
msgid "Monetary"
msgstr "Měnový"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__monetary
msgid "Monetary Value"
msgstr "Peněžní hodnota"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__monthly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__monthly
msgid "Monthly"
msgstr "Měsíčně"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_max
msgid "Monthly Limited Sending"
msgstr "Měsíční omezené odesílání"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_this_month
msgid "Monthly total"
msgstr "Měsíční součet"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__description_motivational
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Motivational"
msgstr "Motivační"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_karma_rank__description_motivational
msgid "Motivational phrase to reach this rank"
msgstr "Motivační fráze k dosažení této hodnosti"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "My Goals"
msgstr "Moje cíle"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my_monthly_sending
msgid "My Monthly Sending Total"
msgstr "Můj měsíční součet odesílání"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my_this_month
msgid "My Monthly Total"
msgstr "Můj měsíční součet"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my
msgid "My Total"
msgstr "Můj součet"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__name
msgid "Name"
msgstr "Jméno"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__never
msgid "Never"
msgstr "Nikdy"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__remind_update_delay
msgid "Never reminded if no value or zero is specified."
msgstr "Nikdy nepřipomenut, pokud není zadána žádná hodnota nebo nula."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__new_value
msgid "New Karma Value"
msgstr "Nová hodnota karmy"

#. module: gamification
#: model:mail.template,subject:gamification.email_template_badge_received
msgid "New badge {{ object.badge_id.name }} granted"
msgstr "Byl udělen nový odznak {{ object.badge_id.name }}"

#. module: gamification
#: model:mail.template,subject:gamification.mail_template_data_new_rank_reached
msgid "New rank: {{ object.rank_id.name }}"
msgstr "Nová úroveň: {{ object.rank_id.name }}"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_newbie
msgid "Newbie"
msgstr "Nováček"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__next_rank_id
msgid "Next Rank"
msgstr "Další hodnost"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__next_report_date
msgid "Next Report Date"
msgstr "Datum příští zprávy"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goals_from_challenge_act
msgid "No goal found"
msgstr "Cíl nenalezen"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "No monthly sending limit"
msgstr "Žádný měsíční limit odesílání"

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_problem_solver
msgid "No one can solve challenges like you do."
msgstr "Nikdo nemůže vyřešit výzvy, jako vy."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__nobody
msgid "No one, assigned through challenges"
msgstr "Nikdo, přidělen prostřednictvím výzev"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "Nobody reached the required conditions to receive special badges."
msgstr "Nikdo nedosáhl požadovaných podmínek k získání zvláštních odznaků."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__once
msgid "Non recurring"
msgstr "Neopakující se"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__remind_update_delay
msgid "Non-updated manual goals will be reminded after"
msgstr "Po aktualizaci nebudou upřesněny manuální cíle"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Notification Messages"
msgstr "Oznámení"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_needaction_counter
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcí"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_has_error_counter
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chyb"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_needaction_counter
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Počet zpráv, které vyžadují akci"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_has_error_counter
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet zpráv s chybou při doručení"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_unread_counter
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_unread_counter
msgid "Number of unread messages"
msgstr "Počet nepřečtených zpráv"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__granted_users_count
msgid "Number of users"
msgstr "počet uživatelů"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__old_value
msgid "Old Karma Value"
msgstr "Stará hodnota karmy"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__onchange
msgid "On change"
msgstr "Při změně"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth_badge_ids
msgid "Only the people having these badges can give this badge"
msgstr "Tento odznak mohou dát jen lidé, kteří mají tyto odznaky"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth_user_ids
msgid "Only these people can give this badge"
msgstr "Pouze tito lidé mohou dát tento odznak"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Optimisation"
msgstr "Optimalizace"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Owner"
msgstr "Majitel"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__owner_ids
msgid "Owners"
msgstr "Majitelé"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__having
msgid "People having some badges"
msgstr "Lidé mající nějaké odznaky"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Period"
msgstr "Období"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__period
msgid ""
"Period of automatic goal assigment. If none is selected, should be launched "
"manually."
msgstr ""
"Perioda automatického přiřazování cílů. Pokud není vybráno žádné, mělo by se"
" spustit ručně."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__period
msgid "Periodicity"
msgstr "Periodicita"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_problem_solver
msgid "Problem Solver"
msgstr "Řešitel problémů"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__display_mode__progress
msgid "Progressive (using numerical values)"
msgstr "Progresivní (pomocí číselných hodnot)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__compute_code
msgid "Python Code"
msgstr "Python kód"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__compute_code
msgid ""
"Python code to be executed for each user. 'result' should contains the new "
"current value. Evaluated user can be access through object.user_id."
msgstr ""
"Kód Pythonu, který se má provést pro každého uživatele. 'result' by měl "
"obsahovat novou aktuální hodnotu. Vyhodnocenému uživateli je umožněn přístup"
" přes object.user_id."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__rank_id
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Rank"
msgstr "Hodnost"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__name
msgid "Rank Name"
msgstr "Název úrovně"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_karma_rank
msgid "Rank based on karma"
msgstr "Úroveň založená na karmě"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.gamification_karma_ranks_action
#: model:ir.ui.menu,name:gamification.gamification_karma_ranks_menu
msgid "Ranks"
msgstr "Úrovně"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_ranks_view_tree
msgid "Ranks List"
msgstr "Seznam úrovní"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_bachelor
msgid "Reach the next rank and gain a very magic wand !"
msgstr "Dosáhněte další pozice a získejte velmi kouzelnou hůlku!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_master
msgid "Reach the next rank and gain a very nice hat !"
msgstr "Dosáhněte další pozice a získejte velmi hezkou čepici!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_student
msgid "Reach the next rank and gain a very nice mug !"
msgstr "Dosáhněte další pozice a získejte velmi hezký hrnek!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_doctor
msgid "Reach the next rank and gain a very nice unicorn !"
msgstr "Dosáhněte další pozice a získejte velmi hezkého jednorožce!"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__reached
msgid "Reached"
msgstr "Dosáhl"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reached when current value is"
msgstr "Dosaženo, když aktuální hodnota je"

#. module: gamification
#: model:mail.template,name:gamification.email_template_badge_received
msgid "Received Badge"
msgstr "Přijatý odznak"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__manually
msgid "Recorded manually"
msgstr "Zaznamenáno ručně"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reference"
msgstr "Reference"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Refresh Challenge"
msgstr "Obnovení výzvy"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goals_from_challenge_act
msgid "Related Goals"
msgstr "Související cíle"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user_wizard__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Vztažené uživatelské jméno pro zdroj ke spravování jeho přístupu."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__remaining_sending
msgid "Remaining Sending Allowed"
msgstr "Zbývající povolená odeslání"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__remind_update_delay
msgid "Remind delay"
msgstr "Připomenout zpoždění"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Reminders for Manual Goals"
msgstr "Připomínky pro manuální cíle"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_message_frequency
msgid "Report Frequency"
msgstr "Frekvence přehledů"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_template_id
msgid "Report Template"
msgstr "Šablona výkazu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth_badge_ids
msgid "Required Badges"
msgstr "Požadované odznaky"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__karma_min
msgid "Required Karma"
msgstr "Vyžadovaná Karma"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reset Completion"
msgstr "Obnovit kompletizaci"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__manager_id
msgid "Responsible"
msgstr "Odpovědný"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "Retrieving progress for personal challenge without user information"
msgstr "Získání pokroku pro osobní výzvu bez informací o uživateli"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Reward"
msgstr "Odměna"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_failure
msgid "Reward Bests if not Succeeded?"
msgstr "Odměňování nejlepších, pokud neuspěli?"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_realtime
msgid "Reward as soon as every goal is reached"
msgstr "Odměnit, jakmile je dosaženo každého cíle"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__challenge_ids
msgid "Reward of Challenges"
msgstr "Odměna výzev"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__goal_definition_ids
msgid "Rewarded by"
msgstr "Odměněn od"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Rewards for challenges"
msgstr "Odměny za výzvy"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Running"
msgstr "Spuštěno"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Running Challenges"
msgstr "Běžící výzvy"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Schedule"
msgstr "Naplánovat"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_badge_view_search
msgid "Search Badge"
msgstr "Vyhledávací znak"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Search Challenges"
msgstr "Hledat výzvy"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Search Goal Definitions"
msgstr "Definice cílů vyhledávání"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Search Goals"
msgstr "Hledat cíle"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_ranks_view_search
msgid "Search Ranks"
msgstr "Hledat hodnost"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "Search Trackings"
msgstr "Hledat stopy"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid ""
"Security rules to define who is allowed to manually grant badges. Not "
"enforced for administrator."
msgstr ""
"Bezpečnostní pravidla pro určení, kdo smí ručně udělovat odznaky. Není "
"vynuceno pro administrátora."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Send Report"
msgstr "Odeslat Zprávu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_message_group_id
msgid "Send a copy to"
msgstr "Zaslat kopii na"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__sender_id
msgid "Sender"
msgstr "Odesílatel"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__sequence
msgid "Sequence"
msgstr "Číselná řada"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__sequence
msgid "Sequence number for ordering"
msgstr "Pořadové číslo pro objednání"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Set the current value you have reached for this goal"
msgstr "Nastavte aktuální hodnotu, kterou jste pro tento cíl dosáhli"

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin1
#: model:gamification.goal.definition,name:gamification.definition_base_company_data
msgid "Set your Company Data"
msgstr "Nastavení údajů o společnosti"

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin2
#: model:gamification.goal.definition,name:gamification.definition_base_company_logo
msgid "Set your Company Logo"
msgstr "Nastavit logo společnosti"

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_discover1
#: model:gamification.goal.definition,name:gamification.definition_base_timezone
msgid "Set your Timezone"
msgstr "Nastavte si časovou zónu"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__challenge_category__other
msgid "Settings / Gamification Tools"
msgstr "Nástroje nastavení / Gamifikace"

#. module: gamification
#: model:gamification.challenge,name:gamification.challenge_base_configure
msgid "Setup your Company"
msgstr "Nastavení společnosti"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__silver
msgid "Silver"
msgstr "Stříbrný"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__silver_badge
msgid "Silver badges count"
msgstr "počet stříbrných odznaků"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Start Challenge"
msgstr "Začít výzvu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__start_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__start_date
msgid "Start Date"
msgstr "Počáteční datum"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Start goal"
msgstr "Začít cíl"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__state
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__state
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "State"
msgstr "Stav"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Statistics"
msgstr "Statistiky"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_student
msgid "Student"
msgstr "Student"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Subscriptions"
msgstr "Předplatné"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_full_suffix
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_suffix
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__suffix
msgid "Suffix"
msgstr "Přípona"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__invited_user_ids
msgid "Suggest to users"
msgstr "Navrhnout uživatelům"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.challenge_line_list_view
msgid "Target"
msgstr "Cíl"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__target_goal
msgid "Target Value to Reach"
msgstr "Cílová hodnota k dosažení"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "Target: less than"
msgstr "Cíl: méně než"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__action_id
msgid "The action that will be called to update the goal value."
msgstr "Akce, která bude volána k aktualizaci hodnoty cíle."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "The challenge %s is finished."
msgstr "Výzva %s je hotová."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_full_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal__definition_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__full_suffix
msgid "The currency and suffix field"
msgstr "Pole měny a přípony"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__field_date_id
msgid "The date to use for the time period evaluated"
msgstr "Datum použití pro hodnocené časové období"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__end_date
msgid ""
"The day a new challenge will be automatically closed. If no periodicity is "
"set, will use this date as the goal end date."
msgstr ""
"Den, kdy bude nová výzva automaticky uzavřena. Pokud není nastavena "
"periodicita, použije toto datum jako datum ukončení cíle."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__start_date
msgid ""
"The day a new challenge will be automatically started. If no periodicity is "
"set, will use this date as the goal start date."
msgstr ""
"Den, kdy se automaticky spustí nová výzva. Pokud není nastavena žádná "
"periodicita, použije se toto datum jako datum zahájení cíle."

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid ""
"The domain for the definition %s seems incorrect, please check it.\n"
"\n"
"%s"
msgstr ""
"Doména pro definici %s se zdá být nesprávná, zkontrolujte ji prosím.\n"
"\n"
"%s"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__field_id
msgid "The field containing the value to evaluate"
msgstr "Pole obsahující hodnotu k vyhodnocení"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__res_id_field
msgid ""
"The field name on the user profile (res.users) containing the value for "
"res_id for action."
msgstr ""
"Název pole v profilu uživatele (res.users) obsahující hodnotu res_id pro "
"akci."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__condition__higher
msgid "The higher the better"
msgstr "Čím vyšší, tím lepší"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__owner_ids
msgid "The list of instances of this badge granted to users"
msgstr "Seznam instancí tohoto odznaku udělených uživatelům"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__model_inherited_ids
msgid "The list of models that extends the current model."
msgstr "Seznam modelů, které rozšiřují aktuální model."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__unique_owner_ids
msgid "The list of unique users having received this badge."
msgstr "Seznam jedinečných uživatelů, kteří tento odznak obdrželi."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__condition__lower
msgid "The lower the better"
msgstr "Čím nižší, tím lépe"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_max_number
msgid ""
"The maximum number of time this badge can be sent per month per person."
msgstr ""
"Maximální počet opakování, kolikrát může být tento odznak odeslán za měsíc "
"jedné osobě."

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid ""
"The model configuration for the definition %(name)s seems incorrect, please check it.\n"
"\n"
"%(error)s not found"
msgstr ""
"Konfigurace modelu pro definici %(name)s se zdá nesprávná, zkontrolujte ji.\n"
"\n"
"%(error)s nenalezeno"

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid ""
"The model configuration for the definition %(name)s seems incorrect, please check it.\n"
"\n"
"%(field_name)s not stored"
msgstr ""
"Konfigurace modelu pro definici %(name)s se zdá nesprávná, zkontrolujte ji.\n"
"\n"
"%(field_name)s neuloženo"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__model_id
msgid "The model object for the field to evaluate"
msgstr "Objekt modelu pro pole, které se má vyhodnotit"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__remind_update_delay
msgid ""
"The number of days after which the user assigned to a manual goal will be "
"reminded. Never reminded if no value is specified."
msgstr ""
"Počet dnů, po kterých bude uživatel přiřazený k ručnímu cíli připomenut. "
"Nikdy není připomenuto, pokud není zadána žádná hodnota."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my_this_month
msgid ""
"The number of time the current user has received this badge this month."
msgstr "Počet, kolikrát tento uživatel tento měsíc obdržel tento odznak."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my
msgid "The number of time the current user has received this badge."
msgstr "Počet, kolikrát tento uživatel tento odznak obdržel."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my_monthly_sending
msgid "The number of time the current user has sent this badge this month."
msgstr "Čas, kdy tento uživatel tento měsíc tento odznak odeslal."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__granted_users_count
msgid "The number of time this badge has been received by unique users."
msgstr "Počet obdržených odznaků jednotlivými uživateli."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_this_month
msgid "The number of time this badge has been received this month."
msgstr "Počet obdržení tohoto odznaku v tomto měsíci."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__granted_count
msgid "The number of time this badge has been received."
msgstr "Počet obdržených odznaků."

#. module: gamification
#: model:ir.model.constraint,message:gamification.constraint_gamification_karma_rank_karma_min_check
msgid "The required karma has to be above 0."
msgstr "Požadované karma musí být vyšší než 0."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_monetary
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__monetary
msgid "The target and current value are defined in the company currency."
msgstr "Cílová a aktuální hodnota jsou definovány v měně společnosti."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__suffix
msgid "The unit of the target and current values"
msgstr "Jednotka cíle a aktuální hodnoty"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__manager_id
msgid "The user responsible for the challenge."
msgstr "Uživatel odpovědný za výzvu."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user__sender_id
msgid "The user who has send the badge"
msgstr "Uživatel, který odeslal odznak"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__goal_definition_ids
msgid ""
"The users that have succeeded theses goals will receive automatically the "
"badge."
msgstr "Uživatelé, kteří uspěli v těchto cílech, automaticky obdrží odznak."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_user_expression
msgid ""
"The value to compare with the distinctive field. The expression can contain "
"reference to 'user' which is a browse record of the current user, e.g. "
"user.id, user.partner_id.id..."
msgstr ""
"Hodnota k porovnání s rozlišovacím polem. Výraz může obsahovat odkaz na "
"„uživatele“, což je procházení záznamu aktuálního uživatele, např. user.id, "
"user.partner_id.id ..."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goals_from_challenge_act
msgid ""
"There is no goal associated to this challenge matching your search.\n"
"            Make sure that your challenge is active and assigned to at least one user."
msgstr ""
"S touto výzvou není spojen žádný cíl.\n"
"Ujistěte se, že je vaše výzva aktivní a přiřazena alespoň jednomu uživateli."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__closed
msgid "These goals will not be recomputed."
msgstr "Tyto cíle nebudou přičítány."

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "This badge can not be sent by users."
msgstr "Tento odznak nemohou uživatelé odeslat."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "To"
msgstr "Do"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__target_goal
msgid "To Reach"
msgstr "K dosažení"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__to_update
msgid "To update"
msgstr "K aktualizaci"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__granted_count
msgid "Total"
msgstr "Celkem"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr "Sledujte změny karmy"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_form
msgid "Tracking"
msgstr "Sledování"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__tracking_date
msgid "Tracking Date"
msgstr "Datum sledování"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.gamification_karma_tracking_action
#: model:ir.ui.menu,name:gamification.gamification_karma_tracking_menu
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_tree
msgid "Trackings"
msgstr "Sledování"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__unique_owner_ids
msgid "Unique Owners"
msgstr "Jedineční majitelé"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_suffix
msgid "Unit"
msgstr "Jednotka"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_unread
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_unread
msgid "Unread Messages"
msgstr "Nepřečtené zprávy"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_unread_counter
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Počítadlo nepřečtených zpráv"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Update"
msgstr "Aktualizace"

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid "Update %s"
msgstr "Aktualizace %s"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__user_id
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "User"
msgstr "Uživatel"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__user_domain
msgid "User domain"
msgstr "Doména uživatele"

#. module: gamification
#: model:mail.template,name:gamification.mail_template_data_new_rank_reached
msgid "User: New rank reached"
msgstr "Uživatel: Dosažena nová úroveň"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_current_rank_users
#: model:ir.model,name:gamification.model_res_users
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__user_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__user_ids
msgid "Users"
msgstr "Uživatelé"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_karma_rank__user_ids
msgid "Users having this rank"
msgstr "Uživatelé mající tuto úroveň"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__weekly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__weekly
msgid "Weekly"
msgstr "Týdně"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth
msgid "Who can grant this badge"
msgstr "Kdo může tento odznak udělit"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Who would you like to reward?"
msgstr "Koho byste chtěli odměnit?"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__reward_realtime
msgid ""
"With this option enabled, a user can receive a badge only once. The top 3 "
"badges are still rewarded only at the end of the challenge."
msgstr ""
"Pokud je tato možnost povolena, může uživatel obdržet odznak pouze jednou. "
"Nejlepší 3 odznaky jsou stále odměňovány až na konci výzvy."

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_idea
msgid "With your brilliant ideas, you are an inspiration to others."
msgstr "Se svými skvělými nápady jste inspirací pro ostatní."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__yearly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__yearly
msgid "Yearly"
msgstr "Ročně"

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "You are not in the user allowed list."
msgstr "Nejste v seznamu povolených uživatelů."

#. module: gamification
#: code:addons/gamification/wizard/grant_badge.py:0
#, python-format
msgid "You can not grant a badge to yourself."
msgstr "Nemůžete udělit odznak sobě."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "You can not reset a challenge with unfinished goals."
msgstr "Nemůžete resetovat výzvu s nedokončenými cíli."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "You can still grant"
msgstr "Stále můžete udělit"

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_good_job
msgid "You did great at your job."
msgstr "Ve své práci se vám dařilo."

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "You do not have the required badges."
msgstr "Nemáte požadované odznaky."

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "You have already sent this badge too many time this month."
msgstr "Tento odznak jste tento měsíc již příliš mnohokrát odeslali."

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_hidden
msgid "You have found the hidden badge"
msgstr "Našli jste skrytý odznak"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_doctor
msgid "You have reached the last rank. Congratulations!"
msgstr "Dosáhli jste nejvyšší úrovně. Gratulujeme!"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_newbie
msgid "You just began the adventure! Welcome!"
msgstr "Právě jste začali dobrodružství! Vítejte!"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_master
msgid "You know what you are talking about. People learn from you."
msgstr "Víte, o čem mluvíte. Lidé se od vás učí."

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_bachelor
msgid "You love learning things. Curiosity is a good way to progress."
msgstr "Rádi se učíte věci. Zvědavost je dobrý způsob, jak postupovat."

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_student
msgid "You're a young padawan now. May the force be with you!"
msgstr "Teď jste malý Padawan. Ať vás provází síla!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "badges this month"
msgstr "odznaky tohoto měsíce"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "days"
msgstr "dnů"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid ""
"e.g. A Master Chief knows quite everything on the forum! You cannot beat "
"him!"
msgstr "např. Hlavní šéf ví na fóru úplně všechno! Nemůžete ho porazit!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. Get started"
msgstr "např. Začít"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "e.g. Master Chief"
msgstr "např. Hlavní šéf"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "e.g. Monthly Sales Objectives"
msgstr "např. měsíční cíle prodeje"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "e.g. Problem Solver"
msgstr "např. Řešitel problémů"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "e.g. Reach this rank to gain a free mug !"
msgstr "např. Dosáhněte této úrovně a získejte hrnek zdarma!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. Register to the platform"
msgstr "např. registrovat se na platformě"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. days"
msgstr "např. dnů"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. user.partner_id.id"
msgstr "např. user.partner_id.id"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "granted,"
msgstr "udělené,"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "refresh"
msgstr "obnovit"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "than the target."
msgstr "než cíl."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_user_kanban_view
msgid "the"
msgstr " "

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "this month"
msgstr "tento měsíc"
