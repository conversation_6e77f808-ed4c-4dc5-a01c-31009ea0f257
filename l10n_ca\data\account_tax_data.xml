<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

    <!-- SALES TAXES -->

    <!-- British Columbia PST -->

    <record id="gstpst_sale_bc_gst_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST for sales - 5% (BC)</field>
        <field name="description">GST 5%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="sequence">1</field>
        <field name="include_base_amount" eval="False"/>
        <field name="tax_group_id" ref="tax_group_gst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2131_en'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2131_en'),
            }),
        ]"/>
    </record>

    <record id="pst_bc_sale_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">PST for sales - 7% (BC)</field>
        <field name="description">PST 7%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="sequence">2</field>
        <field name="tax_group_id" ref="tax_group_gst_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2132_en'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2132_en'),
            }),
        ]"/>
    </record>

    <record id="gstpst_bc_sale_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST + PST for sales (BC)</field>
        <field name="description">GST + PST</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">100</field>
        <field name="amount_type">group</field>
        <field name="children_tax_ids" eval="[(6,0,[ref('pst_bc_sale_en'), ref('gstpst_sale_bc_gst_en')])]"/>
        <field name="tax_group_id" ref="tax_group_fix"/>
    </record>

    <!-- Manitoba PST -->

    <record id="gstpst_sale_mb_gst_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST for sales - 5% (MB)</field>
        <field name="description">GST 5%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="sequence">1</field>
        <field name="include_base_amount" eval="False"/>
        <field name="tax_group_id" ref="tax_group_gst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2131_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2131_en'),
            }),
        ]"/>
    </record>

    <record id="pst_mb_sale_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">PST for sales - 8% (MB)</field>
        <field name="description">PST 8%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="sequence">2</field>
        <field name="tax_group_id" ref="tax_group_gst_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2132_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2132_en'),
            }),
        ]"/>
    </record>

    <record id="gstpst_mb_sale_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST + PST for sales (MB)</field>
        <field name="description">GST + PST</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">100</field>
        <field name="amount_type">group</field>
        <field name="children_tax_ids" eval="[(6,0,[ref('gstpst_sale_mb_gst_en'), ref('pst_mb_sale_en')])]"/>
        <field name="tax_group_id" ref="tax_group_fix"/>
    </record>

    <!-- Quebec PST -->

    <record id="gstqst_sale_gst_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST for sales - 5% (QC)</field>
        <field name="description">GST 5%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="sequence">1</field>
        <field name="tax_group_id" ref="tax_group_gst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2131_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2131_en'),
            }),
        ]"/>
    </record>

    <record id="qst_sale_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">QST for sales - 9.975%</field>
        <field name="description">QST 9.975%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">9.9750</field>
        <field name="amount_type">percent</field>
        <field name="sequence">2</field>
        <field name="tax_group_id" ref="tax_group_qst_9975"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2132_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2132_en'),
            }),
        ]"/>
    </record>

    <record id="gstqst_sale_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST + QST for sales</field>
        <field name="description">GST + QST</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">100</field>
        <field name="amount_type">group</field>
        <field name="children_tax_ids" eval="[(6,0,[ref('gstqst_sale_gst_en'), ref('qst_sale_en')])]"/>
        <field name="tax_group_id" ref="tax_group_fix"/>
    </record>

    <!-- Saskatchewan PST -->

    <record id="gstpst_sale_sk_gst_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST for sales - 5% (SK)</field>
        <field name="description">GST 5%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="sequence">1</field>
        <field name="include_base_amount" eval="False"/>
        <field name="tax_group_id" ref="tax_group_gst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2131_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2131_en'),
            }),
        ]"/>
    </record>

    <record id="pst_sk_sale_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">PST for sales - 5% (SK)</field>
        <field name="description">PST 5%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="sequence">2</field>
        <field name="tax_group_id" ref="tax_group_pst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2132_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2132_en'),
            }),
        ]"/>
    </record>

    <record id="gstpst_sk_sale_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST + PST for sales (SK)</field>
        <field name="description">GST + PST</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">100</field>
        <field name="amount_type">group</field>
        <field name="children_tax_ids" eval="[(6,0,[ref('gstpst_sale_sk_gst_en'), ref('pst_sk_sale_en')])]"/>
        <field name="tax_group_id" ref="tax_group_fix"/>
    </record>

    <!-- HST -->

    <record id="hst13_sale_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">HST for sales - 13%</field>
        <field name="description">HST 13%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_hst_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart21331_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart21331_en'),
            }),
        ]"/>
    </record>

    <record id="hst15_sale_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">HST for sales - 15%</field>
        <field name="description">HST 15%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">15</field>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_hst_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart21333_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart21333_en'),
            }),
        ]"/>
    </record>

    <!-- GST -->

    <record id="gst_sale_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST for sales - 5%</field>
        <field name="description">GST 5%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_gst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2131_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart2131_en'),
            }),
        ]"/>
    </record>

    <!-- PURCHASE TAXES -->

    <!-- British Columbia PST -->

    <record id="gstpst_purc_bc_gst_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST for purchases - 5% (BC)</field>
        <field name="description">GST 5%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="sequence">1</field>
        <field name="include_base_amount" eval="False"/>
        <field name="tax_group_id" ref="tax_group_gst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1181_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1181_en'),
            }),
        ]"/>
    </record>

    <record id="pst_bc_purc_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">PST for purchases - 7% (BC)</field>
        <field name="description">PST 7%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">7</field>
        <field name="amount_type">percent</field>
        <field name="sequence">2</field>
        <field name="tax_group_id" ref="tax_group_gst_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1182_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1182_en'),
            }),
        ]"/>
    </record>

    <record id="gstpst_bc_purc_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST + PST for purchases (BC)</field>
        <field name="description">GST + PST</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">100</field>
        <field name="amount_type">group</field>
        <field name="children_tax_ids" eval="[(6,0,[ref('gstpst_purc_bc_gst_en'), ref('pst_bc_purc_en')])]"/>
        <field name="tax_group_id" ref="tax_group_fix"/>
    </record>

    <!-- Manitoba PST -->

    <record id="gstpst_purc_mb_gst_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST for purchases - 5% (MB)</field>
        <field name="description">GST 5%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="sequence">1</field>
        <field name="include_base_amount" eval="False"/>
        <field name="tax_group_id" ref="tax_group_gst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1181_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1181_en'),
            }),
        ]"/>
    </record>

    <record id="pst_mb_purc_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">PST for purchases - 8% (MB)</field>
        <field name="description">PST 8%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="sequence">2</field>
        <field name="tax_group_id" ref="tax_group_pst_8"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1182_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1182_en'),
            }),
        ]"/>
    </record>

    <record id="gstpst_mb_purc_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST + PST for purchases (MB)</field>
        <field name="description">GST + PST</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">100</field>
        <field name="amount_type">group</field>
        <field name="children_tax_ids" eval="[(6,0,[ref('gstpst_purc_mb_gst_en'), ref('pst_mb_purc_en')])]"/>
        <field name="tax_group_id" ref="tax_group_fix"/>
    </record>

    <!-- Quebec PST -->

    <record id="gstqst_purc_gst_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST for purchases - 5% (QC)</field>
        <field name="description">GST 5%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="sequence">1</field>
        <field name="tax_group_id" ref="tax_group_gst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1181_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1181_en'),
            }),
        ]"/>
    </record>

    <record id="qst_purc_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">QST for purchases - 9.975%</field>
        <field name="description">QST 9.975%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">9.9750</field>
        <field name="amount_type">percent</field>
        <field name="sequence">2</field>
        <field name="tax_group_id" ref="tax_group_qst_9975"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1182_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1182_en'),
            }),
        ]"/>
    </record>

    <record id="gstqst_purc_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST + QST for purchases</field>
        <field name="description">GST + QST</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">100</field>
        <field name="amount_type">group</field>
        <field name="children_tax_ids" eval="[(6,0,[ref('gstqst_purc_gst_en'), ref('qst_purc_en')])]"/>
        <field name="tax_group_id" ref="tax_group_fix"/>
    </record>

    <!-- Saskatchewan PST -->

    <record id="gstpst_purc_sk_gst_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST for purchases - 5% (SK)</field>
        <field name="description">GST 5%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="sequence">1</field>
        <field name="include_base_amount" eval="False"/>
        <field name="tax_group_id" ref="tax_group_gst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1181_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1181_en'),
            }),
        ]"/>
    </record>

    <record id="pst_sk_purc_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">PST for purchases - 5% (SK)</field>
        <field name="description">PST 5%</field>
        <field name="type_tax_use">none</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="sequence">2</field>
        <field name="tax_group_id" ref="tax_group_pst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1182_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1182_en'),
            }),
        ]"/>
    </record>

    <record id="gstpst_sk_purc_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST + PST for purchases (SK)</field>
        <field name="description">GST + PST</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">1</field>
        <field name="amount_type">group</field>
        <field name="children_tax_ids" eval="[(6,0,[ref('gstpst_purc_sk_gst_en'), ref('pst_sk_purc_en')])]"/>
        <field name="tax_group_id" ref="tax_group_fix"/>
    </record>

    <!-- HST -->

    <record id="hst13_purc_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">HST for purchases - 13%</field>
        <field name="description">HST 13%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">13</field>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_hst_13"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart11831_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart11831_en'),
            }),
        ]"/>
    </record>

    <record id="hst15_purc_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">HST for purchases - 15%</field>
        <field name="description">HST 15%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">15</field>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_hst_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart11833_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart11833_en'),
            }),
        ]"/>
    </record>

    <!-- GST -->

    <record id="gst_purc_en" model="account.tax.template">
        <field name="chart_template_id" ref="ca_en_chart_template_en"/>
        <field name="name">GST for purchases - 5%</field>
        <field name="description">GST 5%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_gst_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1181_en'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('chart1181_en'),
            }),
        ]"/>
    </record>

    </data>
</odoo>
