.no-link-style {
  color: black;
  text-decoration: none;
}

.required-form-control {
  background-color: #CECDFF;
}

#filters li a,
#filters li.active a, 
#filters li.active a.active,
#filters li.active a:hover, 
#filters li.active a:focus, 
#filters li a:hover {
  padding: 0px 5px;
  border-radius: 0px;
  border: 0px;
  border-color: transparent;
  border-right: 1px solid #999;
  padding-bottom: 0;
  background-color: #FFFFFF;
}

#filters li.active a {
  color: #999;
}

#filters li:last-child a,
#filters li:last-child a:hover,
#filters li:last-child a:focus {
  border-right: 0px;
}

.nav-tabs-inline {
    font-size: 14px;
}

#o_website_links_recent_links {
  min-height:30em;
}

.o_website_links_code_error {
  display:none;
  color:red;
  font-weight:bold;
}

.truncate_text {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 500px;
}
