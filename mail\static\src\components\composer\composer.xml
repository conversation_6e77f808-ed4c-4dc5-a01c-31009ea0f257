<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.Composer" owl="1">
        <div class="o_Composer"
            t-att-class="{
                'o-focused': composerView and composerView.hasFocus,
                'o-has-current-partner-avatar': props.hasCurrentPartnerAvatar,
                'o-has-footer': hasFooter,
                'o-has-header': hasHeader,
                'o-is-compact': props.isCompact,
                'o-messaging-in-editing': composerView and composerView.messageViewInEditing,
            }"
            t-on-keydown="_onKeydown"
        >
            <t t-if="composerView">
                <t t-if="isDropZoneVisible.value and composerView.hasDropZone">
                    <DropZone
                        class="o_Composer_dropZone"
                        t-on-o-dropzone-files-dropped="_onDropZoneFilesDropped"
                        t-ref="dropzone"
                    />
                </t>
                <FileUploader
                    composerViewLocalId="composerView.localId"
                    threadLocalId="composerView.composer.activeThread.localId"
                    t-ref="fileUploader"
                />
                <t t-if="hasHeader">
                    <div class="o_Composer_coreHeader text-truncate">
                        <t t-if="composerView.threadView and composerView.threadView.replyingToMessageView">
                            Replying to <b t-esc="composerView.threadView.replyingToMessageView.message.authorName"/>
                            <i t-if="composerView.threadView.thread !== messaging.inbox" class="o_Composer_cancelReply fa fa-lg fa-times-circle rounded-circle p-0 ml-1" title="Stop replying" t-on-click="composerView.onClickStopReplying"/>
                        </t>
                        <t t-if="props.hasThreadName">
                            <span class="o_Composer_threadName">
                                on: <b><t t-esc="composerView.composer.activeThread.displayName"/></b>
                            </span>
                        </t>
                        <t t-if="props.hasFollowers and !composerView.composer.isLog">
                            <!-- Text for followers -->
                            <small class="o_Composer_followers">
                                <b class="text-muted">To: </b>
                                <em class="text-muted">Followers of </em>
                                <b>
                                    <t t-if="composerView.composer.activeThread.displayName">
                                        &#32;&quot;<t t-esc="composerView.composer.activeThread.displayName"/>&quot;
                                    </t>
                                    <t t-else="">
                                        this document
                                    </t>
                                </b>
                            </small>
                            <ComposerSuggestedRecipientList threadLocalId="composerView.composer.activeThread.localId"/>
                        </t>
                    </div>
                </t>
                <t t-if="props.hasCurrentPartnerAvatar">
                    <div class="o_Composer_sidebarMain">
                        <t t-if="!messaging.currentGuest or composerView.composer.activeThread.model !== 'mail.channel'">
                            <img class="o_Composer_currentPartnerAvatar rounded-circle" t-att-src="currentPartnerAvatar" alt="Avatar of user"/>
                        </t>
                        <t t-if="messaging.currentGuest and composerView.composer.activeThread.model === 'mail.channel'">
                            <img class="o_Composer_currentPartnerAvatar rounded-circle" t-attf-src="/mail/channel/{{ composerView.composer.activeThread.id }}/guest/{{ messaging.currentGuest.id }}/avatar_128?unique={{ messaging.currentGuest.name }}" alt="Avatar of guest"/>
                        </t>
                    </div>
                </t>
                <div
                    class="o_Composer_coreMain"
                    t-att-class="{
                        'o-composer-is-compact': props.isCompact,
                    }"
                >
                    <ComposerTextInput
                        class="o_Composer_textInput"
                        t-att-class="{
                            'o-composer-is-compact': props.isCompact,
                            'o_Composer_textInput-mobile': messaging.device.isMobile,
                            'o-has-current-partner-avatar': props.hasCurrentPartnerAvatar,
                        }"
                        composerViewLocalId="composerView.localId"
                        hasMentionSuggestionsBelowPosition="props.hasMentionSuggestionsBelowPosition"
                        isCompact="props.isCompact"
                        sendShortcuts="props.textInputSendShortcuts"
                        t-on-o-composer-text-input-send-shortcut="_onComposerTextInputSendShortcut"
                        t-on-paste="_onPasteTextInput"
                        t-key="composerView.localId"
                        t-ref="textInput"
                    />
                    <div class="o_Composer_buttons" t-att-class="{ 'o-composer-is-compact': props.isCompact, 'o-mobile': messaging.device.isMobile, 'o-messaging-in-editing': composerView and composerView.messageViewInEditing }">
                        <div class="o_Composer_toolButtons"
                            t-att-class="{
                                'o-composer-has-current-partner-avatar': props.hasCurrentPartnerAvatar,
                                'o-composer-is-compact': props.isCompact,
                            }">
                            <t t-if="props.isCompact">
                                <div class="o_Composer_toolButtonSeparator"/>
                            </t>
                            <div class="o_Composer_primaryToolButtons" t-att-class="{ 'o-composer-is-compact': props.isCompact }">
                                <Popover position="'top'" t-on-o-emoji-selection="_onEmojiSelection">
                                    <!-- TODO FIXME o-open and aria-expanded not possible to code due to https://github.com/odoo/owl/issues/693 -->
                                    <button class="o_Composer_button o_Composer_buttonEmojis o_Composer_toolButton btn btn-light"
                                        t-att-class="{
                                            'o-open': false and state.displayed,
                                            'o-mobile': messaging.device.isMobile,
                                        }"
                                        t-on-keydown="_onKeydownEmojiButton"
                                        aria-label="Emojis"
                                        t-att-aria-expanded="false and (state.displayed ? 'true' : 'false')"
                                    >
                                        <i class="o_Composer_EmojiIcon fa fa-smile-o"/>
                                    </button>
                                    <t t-set="opened">
                                        <EmojisPopover t-ref="emojisPopover"/>
                                    </t>
                                </Popover>
                                <button class="o_Composer_button o_Composer_buttonAttachment o_Composer_toolButton btn btn-light fa fa-paperclip" t-att-class="{ 'o-mobile': messaging.device.isMobile }" title="Add attachment" aria-label="Add attachment" type="button" t-on-click="_onClickAddAttachment"/>
                            </div>
                            <t t-if="props.isExpandable">
                                <div class="o_Composer_secondaryToolButtons">
                                    <button class="btn btn-light fa fa-expand o_Composer_button o_Composer_buttonFullComposer o_Composer_toolButton" t-att-class="{ 'o-mobile': messaging.device.isMobile }" title="Full composer" aria-label="Full composer" type="button" t-on-click="_onClickFullComposer"/>
                                </div>
                            </t>
                        </div>
                        <t t-if="props.isCompact">
                            <t t-call="mail.Composer.actionButtons"/>
                        </t>
                    </div>
                </div>
                <t t-if="hasFooter">
                    <div class="o_Composer_coreFooter" t-att-class="{ 'o-composer-is-compact': props.isCompact }">
                        <t t-if="props.hasThreadTyping">
                            <ThreadTextualTypingStatus class="o_Composer_threadTextualTypingStatus" threadLocalId="composerView.composer.activeThread.localId"/>
                        </t>
                        <AttachmentList
                            t-if="composerView.attachmentList"
                            class="o_Composer_attachmentList"
                            t-att-class="{ 'o-composer-is-compact': props.isCompact }"
                            attachmentListLocalId="composerView.attachmentList.localId"
                        />
                        <t t-if="!props.isCompact">
                            <t t-call="mail.Composer.actionButtons"/>
                        </t>
                        <t t-if="composerView.messageViewInEditing">
                            <span class="text-muted">escape to <a href="#" t-on-click="composerView.onClickCancelLink">cancel</a>, enter to <a href="#" t-on-click="composerView.onClickSaveLink">save</a></span>
                        </t>
                    </div>
                </t>
            </t>
        </div>
    </t>

    <t t-name="mail.Composer.actionButtons" owl="1">
        <div class="o_Composer_actionButtons" t-att-class="{ 'o-composer-is-compact': props.isCompact }">
            <t t-if="props.hasSendButton">
                <button class="o_Composer_actionButton o_Composer_button o_Composer_buttonSend btn btn-primary"
                    t-att-class="{
                        'o-last': messaging.device.isMobile or !props.hasDiscardButton,
                        'o-composer-is-compact': props.isCompact,
                        'o-has-current-partner-avatar': props.hasCurrentPartnerAvatar,
                    }"
                    t-att-disabled="!composerView.composer.canPostMessage ? 'disabled' : ''"
                    type="button"
                    t-on-click="_onClickSend"
                >
                    <t t-if="!messaging.device.isMobile"><t t-esc="composerView.sendButtonText"/></t>
                    <t t-else=""><i class="fa fa-paper-plane-o"/></t>
                </button>
            </t>
            <t t-if="!messaging.device.isMobile and props.hasDiscardButton">
                <button class="o_Composer_actionButton o-last o_Composer_button o_Composer_buttonDiscard btn btn-secondary" t-att-class="{ 'o-composer-is-compact': props.isCompact, 'o-has-current-partner-avatar': props.hasCurrentPartnerAvatar }" type="button" t-on-click="_onClickDiscard">
                    Discard
                </button>
            </t>
        </div>
    </t>

</templates>
