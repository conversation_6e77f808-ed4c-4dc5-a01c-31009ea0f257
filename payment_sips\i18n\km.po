# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_sips
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:164
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:162
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr ""

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr ""

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:75
#, python-format
msgid "Currency not supported by Wordline"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:56
#, python-format
msgid "Incorrect payment acquirer provider"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_version
msgid "Interface Version"
msgstr ""

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Merchant ID"
msgstr ""

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr ""

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_prod_url
msgid "Production url"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__provider
msgid "Provider"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_secret
msgid "Secret Key"
msgstr ""

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:160
#, python-format
msgid "Sips: received data for reference %s"
msgstr ""

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_test_url
msgid "Test url"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Used for production only"
msgstr ""

#. module: payment_sips
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr ""
