# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock_wishlist
# 
# Translators:
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: website_sale_stock_wishlist
#. openerp-web
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
#, python-format
msgid ""
"Add the item to your wishlist to be notified when the product is back in "
"stock."
msgstr ""

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "Be notified when back in stock"
msgstr ""

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.availability_email_body
msgid "Dear Customer,"
msgstr ""

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.availability_email_body
msgid "Order Now"
msgstr ""

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_template
msgid "Product Template"
msgstr "Šablóna produktu"

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_wishlist
msgid "Product Wishlist"
msgstr ""

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.availability_email_body
msgid "Regards,"
msgstr "S pozdravom,"

#. module: website_sale_stock_wishlist
#: model:ir.model.fields,field_description:website_sale_stock_wishlist.field_product_wishlist__stock_notification
msgid "Stock Notification"
msgstr ""

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "Temporarily out of stock"
msgstr "Dočasne nie je na sklade"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.availability_email_body
msgid "The following product is now available."
msgstr ""

#. module: website_sale_stock_wishlist
#: code:addons/website_sale_stock_wishlist/models/product_wishlist.py:0
#, python-format
msgid "The product '%(product_name)s' is now available"
msgstr ""

#. module: website_sale_stock_wishlist
#: code:addons/website_sale_stock_wishlist/models/product_wishlist.py:0
#, python-format
msgid "Wishlist"
msgstr ""

#. module: website_sale_stock_wishlist
#: model:ir.actions.server,name:website_sale_stock_wishlist.ir_cron_send_availability_email_ir_actions_server
#: model:ir.cron,cron_name:website_sale_stock_wishlist.ir_cron_send_availability_email
#: model:ir.cron,name:website_sale_stock_wishlist.ir_cron_send_availability_email
msgid "Wishlist: send email regarding products availability"
msgstr ""
