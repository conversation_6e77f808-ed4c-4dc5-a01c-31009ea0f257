<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.MessageList" owl="1">
        <div class="o_MessageList" t-att-class="{ 'o-empty': threadView and threadView.messages.length === 0 }" t-on-scroll="onScroll">
            <t t-if="threadView">
                <!-- No result messages -->
                <t t-if="threadView.threadCache.orderedNonEmptyMessages.length === 0">
                    <div class="o_MessageList_empty o_MessageList_item">
                        <t t-if="threadView.thread === messaging.inbox">
                            <div class="o_MessageList_emptyTitle">
                                Congratulations, your inbox is empty
                            </div>
                            New messages appear here.
                        </t>
                        <t t-elif="threadView.thread === messaging.starred">
                            <div class="o_MessageList_emptyTitle">
                                No starred messages
                            </div>
                            You can mark any message as 'starred', and it shows up in this mailbox.
                        </t>
                        <t t-elif="threadView.thread === messaging.history">
                            <div class="o_MessageList_emptyTitle o-neutral-face-icon">
                                No history messages
                            </div>
                            Messages marked as read will appear in the history.
                        </t>
                        <t t-else="">
                            There are no messages in this conversation.
                        </t>
                    </div>
                </t>
                <!-- LOADING (if order asc)-->
                <t t-if="threadView.threadCache.hasLoadingFailed">
                    <div class="o_ThreadView_alertLoadingFailed alert alert-info">
                        <div>
                            An error occurred while fetching messages.
                        </div>
                        <button class="o_ThreadView_alertLoadingFailedRetryButton btn btn-link" t-on-click="_onClickRetryLoadMoreMessages">
                            Click here to retry
                        </button>
                    </div>
                </t>
                <t t-if="!threadView.threadCache.hasLoadingFailed and threadView.order === 'asc'">
                    <t t-call="mail.MessageList.loadMore"/>
                </t>
                <div t-if="threadView.order === 'asc' and threadView.threadCache.orderedNonEmptyMessages.length !== 0 and !(threadView and threadView.thread and threadView.thread.model === 'mail.box')" class="flex-grow-1"/>
                <!-- MESSAGES -->
                <t t-set="current_day" t-value="0"/>
                <t t-foreach="threadView.messageViews" t-as="messageView" t-key="messageView.localId">
                    <t t-if="messageView.message === threadView.thread.messageAfterNewMessageSeparator">
                        <div class="o_MessageList_separator o_MessageList_separatorNewMessages o_MessageList_item" t-att-class="{ 'o-disable-animation': messaging.disableAnimation }" t-transition="fade">
                            <hr class="o_MessageList_separatorLine o_MessageList_separatorLineNewMessages"/><span class="o_MessageList_separatorLabel o_MessageList_separatorLabelNewMessages">New messages</span>
                        </div>
                    </t>
                    <t t-if="!messageView.message.isEmpty">
                        <t t-set="message_day" t-value="messageView.message.dateDay"/>
                        <t t-if="current_day !== message_day">
                            <div class="o_MessageList_separator o_MessageList_separatorDate o_MessageList_item pt-4">
                                <hr class="o_MessageList_separatorLine"/><span class="o_MessageList_separatorLabel o_MessageList_separatorLabelDate"><t t-esc="message_day"/></span><hr class="o_MessageList_separatorLine"/>
                                <t t-set="current_day" t-value="message_day"/>
                            </div>
                        </t>
                        <Message class="o_MessageList_item o_MessageList_message" messageViewLocalId="messageView.localId"/>
                    </t>
                </t>
                <!-- LOADING (if order desc)-->
                <t t-if="!threadView.threadCache.hasLoadingFailed and threadView.order === 'desc'">
                    <t t-call="mail.MessageList.loadMore"/>
                </t>
            </t>
        </div>
    </t>

    <t t-name="mail.MessageList.loadMore" owl="1">
        <t t-if="threadView.threadCache.isLoadingMore">
            <div class="o_MessageList_item o_MessageList_isLoadingMore">
                <i class="o_MessageList_isLoadingMoreIcon fa fa-spin fa-circle-o-notch"/>
                Loading...
            </div>
        </t>
        <t t-elif="!threadView.threadCache.isAllHistoryLoaded and !threadView.thread.isTemporary">
            <a class="o_MessageList_item o_MessageList_loadMore" href="#" t-on-click="_onClickLoadMore" t-ref="loadMore">
                Load more
            </a>
        </t>
    </t>

</templates>
