<?xml version="1.0"?>
<odoo>
    <data>
        <!-- Firebase Log -->
        <record id="view_form_firebase_log" model="ir.ui.view">
            <field name="name">Firebase Log</field>
            <field name="model">firebase.log</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group col="4">
                            <field name="is_processed"/>
                            <field name="date_processed"/>
                        </group>
                        <group string="Datos de Odoo" col="4">
                            <field name="model_name"/>
                            <field name="res_id"/>
                        </group>
                        <group string="Datos de Firebase" col="4">
                            <field name="path_name"/>
                            <field name="path_id"/>
                        </group>
                        <group string="Data">
                            <field nolabel="1" name="data"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="view_tree_firebase_log" model="ir.ui.view">
            <field name="name">Firebase Log</field>
            <field name="model">firebase.log</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="model_name"/>
                    <field name="res_id"/>
                    <field name="path_name"/>
                    <field name="path_id"/>
                    <field name="path_id"/>
                    <field name="path_id"/>
                </tree>
            </field>
        </record>

        <!-- ACTION -->
        <record id="action_firebase_log" model="ir.actions.act_window">
            <field name="name">Firebase log</field>
            <field name="res_model">firebase.log</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[]</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    <b>You have not any setting at moment</b>...
                </p>
            </field>
        </record>
    </data>
</odoo>