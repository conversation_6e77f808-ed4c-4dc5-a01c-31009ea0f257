<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="product_pos_category_form_view" model="ir.ui.view">
        <field name="name">pos.category.form</field>
        <field name="model">pos.category</field>
        <field name="arch" type="xml">
            <form string="Pos Product Categories">
                <sheet>
                    <field name="image_128" widget="image" class="oe_avatar"/>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1>
                            <field name="name" class="o_text_overflow" placeholder="e.g. Soft Drinks" required="True"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="parent_id" class="o_text_overflow"/>
                            <field name="sequence" groups="base.group_no_one" />
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="product_pos_category_tree_view" model="ir.ui.view">
        <field name="name">pos.category.tree</field>
        <field name="model">pos.category</field>
        <field name="field_parent" eval="False"/>
        <field name="arch" type="xml">
            <tree string="Product Product Categories">
                <field name="sequence" widget="handle"/>
                <field name="display_name" string="PoS Product Category"/>
            </tree>
        </field>
    </record>

    <record id="view_pos_category_kanban" model="ir.ui.view">
        <field name="name">pos.category.kanban</field>
        <field name="model">pos.category</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="name"/>
                <field name="id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div class="row">
                                <div class="col-4">
                                    <img height="100" width="100" t-att-src="kanban_image('pos.category', 'image_128', record.id.raw_value)" alt="Category"/>
                                </div>
                                <div class="col-8">
                                    <strong class="o_kanban_record_title"><field name="name"/></strong>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="product_pos_category_action" model="ir.actions.act_window">
        <field name="name">PoS Product Categories</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">pos.category</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="view_id" eval="False"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Define a new category
            </p><p>
                Categories are used to browse your products through the
                touchscreen interface.
            </p>
        </field>
    </record>

</odoo>
