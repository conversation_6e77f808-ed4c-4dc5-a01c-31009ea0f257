# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_timesheet
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>esselbosch, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "%s Spent"
msgstr "%s Impiegati"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "(%(sign)s%(hours)s:%(minutes)s remaining)"
msgstr "(restano %(sign)s%(hours)s:%(minutes)s)"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "(%s days remaining)"
msgstr "(%s giorni residui)"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid "<b class=\"tip_title\">Tip: Record your Timesheets faster</b>"
msgstr ""
"<b class=\"tip_title\">Suggerimento: Registra i tuoi fogli ore più "
"velocemente</b>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for employee:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">Fogli ore per dipendente:</em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for project:</em>"
msgstr "<em class=\"font-weight-normal text-muted\">Fogli ore per progetto:</em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for task:</em>"
msgstr "<em class=\"font-weight-normal text-muted\">Fogli ore per lavoro:</em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets on </em>"
msgstr "<em class=\"font-weight-normal text-muted\">Fogli ore del </em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Data\" title=\"Data\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o \" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o \" title=\"I valori impostati qui sono "
"specifici per azienda.\" groups=\"base.group_multi_company\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"I valori impostati qui sono "
"specifici per azienda.\" groups=\"base.group_multi_company\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">Registrate</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "<span class=\"o_stat_text\">Timesheets</span>"
msgstr "<span class=\"o_stat_text\">Fogli ore</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Hours Spent on Sub-tasks:</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Days Spent on Sub-tasks:</span>"
msgstr ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Ore impiegate per sottocompiti:</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Giorni impiegati per sottocompiti:</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Sub-tasks Hours Spent</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Sub-tasks Days Spent</span>"
msgstr ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Ore impiegate per sottolavori</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Giorni impiegati per sottolavori</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Days)</span>"
msgstr "<span style=\"margin-right: 15px;\">Totale (giorni)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Hours)</span>"
msgstr "<span style=\"margin-right: 15px;\">Totale (ore)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
msgid "<span> day(s)</span>"
msgstr "<span> giorno/i</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
msgid "<span> hour(s)</span>"
msgstr "<span> ora/e</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Date</span>"
msgstr "<span>Data</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Description</span>"
msgstr "<span>Descrizione</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Project</span>"
msgstr "<span>Progetto</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Responsible</span>"
msgstr "<span>Responsabile</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Task</span>"
msgstr "<span>Lavoro</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Days recorded:</strong>"
msgstr "<strong>Giorni registrati:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<strong>Duration: </strong>"
msgstr "<strong>Durata: </strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Hours recorded:</strong>"
msgstr "<strong>Ore registrate:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task_planned_hours_template
msgid "<strong>Planned Days:</strong>"
msgstr "<strong>Giorni programmati:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<strong>Progress:</strong>"
msgstr "<strong>Avanzamento:</strong>"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__analytic_account_active
msgid "Active Analytic Account"
msgstr "Conto analitico attivo"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_timesheet_manager
msgid "Administrator"
msgstr "Amministratore"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Tutti"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_all
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_all
msgid "All Timesheets"
msgstr "Tutti i fogli ore"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__allow_timesheets
msgid "Allow timesheets"
msgstr "Consenti fogli ore"

#. module: hr_timesheet
#: code:addons/hr_timesheet/__init__.py:0
#, python-format
msgid "Analysis"
msgstr "Analisi"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__analytic_account_id
msgid "Analytic Account"
msgstr "Conto analitico"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Analytic Entry"
msgstr "Registrazione analitica"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Riga analitica"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__analytic_account_id
msgid ""
"Analytic account to which this project is linked for financial management. "
"Use an analytic account to record cost and revenue on your project."
msgstr ""
"Conto analitico al quale è collegato il progetto per la gestione "
"finanziaria. Usare un conto analitico per registrare i costi e i ricavi nel "
"progetto."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Apple App Store"
msgstr "App Store di Apple"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "Application Settings"
msgstr "Impostazioni applicazione"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_ids
msgid "Associated Timesheets"
msgstr "Fogli ore associati"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_synchro
msgid "Awesome Timesheet"
msgstr "Foglio ore fantastico"

#. module: hr_timesheet
#: model:project.task.type,legend_blocked:hr_timesheet.internal_project_default_stage
msgid "Blocked"
msgstr "Bloccato"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_activity_analysis
msgid "By Employee"
msgstr "Per dipendente"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_project
msgid "By Project"
msgstr "Per progetto"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_task
msgid "By Task"
msgstr "Per lavoro"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "Collaboratori nel progetto condiviso"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.hr_timesheet_menu_configuration
msgid "Configuration"
msgstr "Configurazione"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_task_create_timesheet
msgid "Create Timesheet from task"
msgstr "Creazione foglio ore da lavoro"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Date"
msgstr "Data"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Days Spent"
msgstr "Giorni impiegati"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Days recorded on sub-tasks:"
msgstr "Giorni registrati sulle sotto-attività:"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__internal_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr ""
"Valore predefinito del progetto per il foglio ore generato dalla tipologia "
"di ferie."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Delete"
msgstr "Elimina"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__department_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Department"
msgstr "Ufficio"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Describe your activity..."
msgstr "Descrivi l'attività..."

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__description
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Description"
msgstr "Descrizione"

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
#, python-format
msgid "Discard"
msgstr "Abbandona"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__progress
msgid "Display progress of current task."
msgstr "Visualizza l'avanzamento del lavoro corrente."

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#, python-format
msgid "Download our App"
msgstr "Scarica l'applicazione"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Duration"
msgstr "Durata"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Duration (Days)"
msgstr "Durata (giorni)"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Duration (Hours)"
msgstr "Durata (ore)"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__hours_effective
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Effective Hours"
msgstr "Ore effettive"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__employee_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Employee"
msgstr "Dipendente"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid "Employee Reminder"
msgstr "Promemoria per i dipendenti"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__allow_timesheets
msgid "Enable timesheeting on the project."
msgstr "Abilita i fogli ore sul progetto."

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__encode_uom_in_days
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__encode_uom_in_days
msgid "Encode Uom In Days"
msgstr "Codificare UdM in giorni"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__timesheet_encode_uom_id
msgid "Encoding Unit"
msgstr "Unità di codifica"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__encoding_uom_id
msgid "Encoding Uom"
msgstr "UdM di codifica"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Generate timesheets upon time off validation"
msgstr "Generare fogli ore dopo la convalida del congedo"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Google Chrome Store"
msgstr "Google Chrome Store"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Google Play Store"
msgstr "Google Play Store"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_http
msgid "HTTP Routing"
msgstr "Instradamento HTTP"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__has_planned_hours_tasks
msgid "Has Planned Hours Tasks"
msgstr "Ha pianificato ore di lavoro"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Hours"
msgstr "ore"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__effective_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Hours Spent"
msgstr "Ore impiegate"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Hours recorded on sub-tasks:"
msgstr "Ore registrate sulle sotto-attività:"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__id
msgid "ID"
msgstr "ID"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__reminder_manager_allow
msgid "If checked, send an email to all manager"
msgstr "Se selezionato, inviare una email a tutti i responsabili"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid ""
"If checked, send an email to all users who have not recorded their timesheet"
msgstr ""
"Se selezionato, inviare una email a tutti gli utenti che non hanno "
"registrato il loro foglio ore"

#. module: hr_timesheet
#: model:project.task.type,legend_normal:hr_timesheet.internal_project_default_stage
msgid "In Progress"
msgstr "In corso"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Initially Planned Days"
msgstr "Giorni inizialmente pianificati"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Initially Planned Hours"
msgstr "Ore inizialmente pianificate"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#: model:project.task.type,name:hr_timesheet.internal_project_default_stage
#, python-format
msgid "Internal"
msgstr "Interno"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__internal_project_id
msgid "Internal Project"
msgstr "Progetto interno"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__is_encode_uom_days
msgid "Is Encode Uom Days"
msgstr "È una codifica UdM in giorni"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__is_internal_project
msgid "Is Internal Project"
msgstr "È progetto interno"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last month"
msgstr "Ultimo mese"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last week"
msgstr "Ultima settimana"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last year"
msgstr "Ultimo anno"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
msgid "Log time on tasks"
msgstr "Registra tempi sui lavori"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_manager_allow
msgid "Manager Reminder"
msgstr "Promemoria per i responsabili"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Meeting"
msgstr "Incontro"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_ui_menu
msgid "Menu"
msgstr "Menù"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "My Team's Projects"
msgstr "Progetti della mia squadra"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "My Team's Tasks"
msgstr "Compiti della mia squadra"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_mine
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_user
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "My Timesheets"
msgstr "I miei fogli ore"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "Più recenti"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid "No activities found"
msgstr "Nessuna attività trovata"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid "No activities found. Let's start a new one!"
msgstr "Nessuna attività trovata, avviane una nuova!"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "Nessuno"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__overtime
msgid "Overtime"
msgstr "Straordinari"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "Partner"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__hours_planned
msgid "Planned Hours"
msgstr "Ore pianificate"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unità di misura prodotto"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__progress
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__progress
#, python-format
msgid "Progress"
msgstr "Avanzamento"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_project
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__project_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
#, python-format
msgid "Project"
msgstr "Progetto"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid "Project Time Unit"
msgstr "Unità di tempo progetto"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_project
msgid "Project's Timesheets"
msgstr "Fogli ore del progetto"

#. module: hr_timesheet
#: model:project.task.type,legend_done:hr_timesheet.internal_project_default_stage
msgid "Ready"
msgstr "Pronta"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
msgid "Record a new activity"
msgstr "Registra una nuova attività"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid ""
"Record your timesheets in an instant by pressing Shift + the corresponding "
"hotkey to add 15min to your projects."
msgstr ""
"Registra i tuoi fogli ore in un istante premendo Shift + la scorciatoia "
"corrispondente per aggiungere 15min ai tuoi progetti."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "Recorded"
msgstr "Registrate"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Remaining Days"
msgstr "Giorni residui"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Remaining Days:"
msgstr "Giorni rimanenti:"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__remaining_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Remaining Hours"
msgstr "Ore residue"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Remaining Hours:"
msgstr "Ore rimanenti:"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__remaining_hours
msgid "Remaining Invoiced Time"
msgstr "Tempo rimanente fatturato"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports
msgid "Reporting"
msgstr "Rendicontazione"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Save"
msgstr "Salva"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Save time"
msgstr "Salvataggio tempi"

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/xml/qr_modal_template.xml:0
#, python-format
msgid "Scan this QR code to get the Awesome Timesheet app:"
msgstr ""
"Per avere la fantastica applicazione per il foglio ore leggi questo codice "
"QR:"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "Ricerca tutto"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr "Ricerca nella descrizione"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Employee"
msgstr "Ricerca nei dipendenti"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "Ricerca nei progetti"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Task"
msgstr "Ricerca nei lavori"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "See timesheet entries"
msgstr "Vedi registrazioni foglio ore"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Send a periodical email reminder to timesheets managers"
msgstr ""
"Invia un promemoria periodico via e-mail ai responsabili dei fogli ore"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Send a periodical email reminder to timesheets users"
msgstr "Inviare un promemoria periodico via e-mail agli utenti dei fogli ore"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.hr_timesheet_config_settings_action
msgid "Settings"
msgstr "Impostazioni"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Sub-tasks Hours Spent"
msgstr "Ore impiegate per sottolavori"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Planned Days"
msgstr "Giorni pianificati per sottolavori"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Planned Hours"
msgstr "Ore pianificate per sottolavori"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_task
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#, python-format
msgid "Task"
msgstr "Lavoro"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task_create_timesheet__task_id
msgid "Task for which we are creating a sales order"
msgstr "Lavoro per cui si sta creando l'ordine di vendita"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_task
msgid "Task's Timesheets"
msgstr "Fogli ore del lavoro"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
msgid "Task:"
msgstr "Lavoro:"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Analisi lavori"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "Tasks in Overtime"
msgstr "Lavori con straordinari"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "The Internal Project of a company should be in that company."
msgstr ""
"Il progetto interno di un'azienda deve appartenere all'azienda stessa."

#. module: hr_timesheet
#: model:ir.model.constraint,message:hr_timesheet.constraint_project_task_create_timesheet_time_positive
msgid "The timesheet's time must be positive"
msgstr "I tempi del foglio ore devono essere positivi"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "There are no timesheets."
msgstr "Nessun foglio ore presente."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"These projects have some timesheet entries referencing them. Before removing"
" these projects, you have to remove these timesheet entries."
msgstr ""
"Alcune registrazioni del foglio ore si riferiscono ai progetti. Prima di "
"rimuoverli, eliminare tali registrazioni."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"These tasks have some timesheet entries referencing them. Before removing "
"these tasks, you have to remove these timesheet entries."
msgstr ""
"Alcune registrazioni del foglio ore si riferiscono ai lavori. Prima di "
"rimuoverli, eliminare tali registrazioni."

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This Quarter"
msgstr "Questo trimestre"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This month"
msgstr "Questo mese"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This project has some timesheet entries referencing it. Before removing this"
" project, you have to remove these timesheet entries."
msgstr ""
"Alcune registrazioni del foglio ore si riferiscono al progetto. Prima di "
"rimuoverlo, eliminare tali registrazioni."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This task has some timesheet entries referencing it. Before removing this "
"task, you have to remove these timesheet entries."
msgstr ""
"Alcune registrazioni del foglio ore si riferiscono al lavoro. Prima di "
"rimuoverlo, eliminare tali registrazioni."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This task must be part of a project because there are some timesheets linked"
" to it."
msgstr ""
"Questo lavoro deve far parte di un progetto, è collegato ad alcuni fogli "
"ore."

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This week"
msgstr "Questa settimana"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right unit of measure in your employees."
msgstr ""
"Verrà impostata l'unità di misura usata nei progetti e nei lavori.\n"
"Se viene usato il foglio ore collegato ai progetti, è necessario impostare i dipendenti con l'unità di misura corretta."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__timesheet_encode_uom_id
#: model:ir.model.fields,help:hr_timesheet.field_res_company__timesheet_encode_uom_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""
"Verrà impostata l'unità di misura usata nei fogli ore, con la disponibilità di strumenti e oggetti\n"
"        utili alla gestione. Le rendicontazioni resteranno tutte espresse in ore (valore predefinito)."

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This year"
msgstr "Questo anno"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__time_spent
msgid "Time"
msgstr "Tempi"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Encoding"
msgstr "Codifica tempi"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Off"
msgstr "Ferie"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Time Spent (Days)"
msgstr "Tempo impiegato (giorni)"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Time Spent (Hours)"
msgstr "Tempo impiegato (ore)"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Time spent on the sub-tasks (and their own sub-tasks) of this task."
msgstr ""
"Tempo impiegato nei sottolavori (e loro ulteriori sottolavori) del lavoro."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__effective_hours
msgid "Time spent on this task, excluding its sub-tasks."
msgstr "Tempo impiegato per il lavoro, escludendo i sottolavori."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr "Tempo impiegato per il lavoro, includendo i sottolavori."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time unit used to record your timesheets"
msgstr "Unità di tempo usata per registrare i fogli ore"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet"
msgstr "Foglio ore"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheet Activities"
msgstr "Attività foglio ore"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__timesheet_cost
msgid "Timesheet Cost"
msgstr "Costo foglio ore"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet Costs"
msgstr "Costi foglio ore"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_count
msgid "Timesheet Count"
msgstr "Conteggio fogli lavoro"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "Unità di codifica foglio ore"

#. module: hr_timesheet
#: model:ir.actions.report,name:hr_timesheet.timesheet_report
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_project
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
msgid "Timesheet Entries"
msgstr "Registrazioni foglio ore"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Timesheet by Date"
msgstr "Foglio ore per data"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#: code:addons/hr_timesheet/models/project.py:0
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line_by_project
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_from_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allow_timesheets
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__timesheet_ids
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_time_tracking
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_root
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_layout
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_kanban_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#, python-format
msgid "Timesheets"
msgstr "Fogli ore"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Timesheets Control"
msgstr "Controllo Fogli ore"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_report
msgid "Timesheets by Employee"
msgstr "Fogli ore per dipendente"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_project
msgid "Timesheets by Project"
msgstr "Fogli ore per progetto"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_task
msgid "Timesheets by Task"
msgstr "Foglio ore per attività"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__allow_timesheets
msgid "Timesheets can be logged on this task."
msgstr "Consente di registrare i fogli ore per questo lavoro."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Timesheets must be created with an active employee."
msgstr "I fogli ore devono essere creati con un dipendente attivo."

#. module: hr_timesheet
#: model:digest.tip,name:hr_timesheet.digest_tip_hr_timesheet_0
msgid "Tip: Record your Timesheets faster"
msgstr "Suggerimento: Registra i tuoi fogli ore più velocemente"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Today"
msgstr "Oggi"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
msgid "Total"
msgstr "Totale"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Total Days"
msgstr "Giorni totali"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__total_hours_spent
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Total Hours"
msgstr "Ore totali"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__total_timesheet_time
msgid "Total Timesheet Time"
msgstr "Tempo totale foglio ore"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__total_timesheet_time
msgid ""
"Total number of time (in the proper UoM) recorded in the project, rounded to"
" the unit."
msgstr ""
"Numero totale di periodi (nell'UdM appropriata) registrati nel progetto, "
"arrotondato all'unità."

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__remaining_hours
msgid ""
"Total remaining time, can be re-estimated periodically by the assignee of "
"the task."
msgstr ""
"Tempo residuo totale, chi è stato assegnato al lavoro può ricalcolarlo "
"periodicamente."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "Total:"
msgstr "Totale:"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Track your time from anywhere, even offline, with our web/mobile apps"
msgstr ""
"Tiene traccia dei tempi ovunque, anche non in linea, con le nostre  "
"applicazioni web/mobile"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"Tieni traccia delle ore lavorative giornaliere per progetto e fatturale ai "
"clienti."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Training"
msgstr "Formazione"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__has_planned_hours_tasks
msgid "True if any of the project's task has a set planned hours"
msgstr ""
"Vero se uno qualsiasi dei compiti del progetto ha un set di ore pianificate"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__user_id
msgid "User"
msgstr "Utente"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_approver
msgid "User: all timesheets"
msgstr "Utente: Tutti i fogli ore"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_user
msgid "User: own timesheets only"
msgstr "Utente: solo i propri fogli ore"

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#, python-format
msgid "View App"
msgstr "Visualizza App"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_uom_uom__timesheet_widget
msgid "Widget"
msgstr "Widget"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_uom_uom__timesheet_widget
msgid ""
"Widget used in the webclient when this unit is the one used to encode "
"timesheets."
msgstr ""
"Widget utilizzato nel client web quando viene usata questa unità per gestire"
" i fogli ore."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot access timesheets that are not yours."
msgstr "Impossibile accedere a fogli ore non propri."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid ""
"You cannot add timesheets to a project linked to an inactive analytic "
"account."
msgstr ""
"Non puoi aggiungere fogli ore a un progetto collegato a un account analitico"
" inattivo."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid ""
"You cannot add timesheets to a project or a task linked to an inactive "
"analytic account."
msgstr ""
"Non puoi aggiungere fogli ore a un progetto o a un'attività collegata a un "
"account analitico inattivo."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"You cannot log timesheets on this project since it is linked to an inactive "
"analytic account. Please change this account, or reactivate the current one "
"to timesheet on the project."
msgstr ""
"Impossibile registrare fogli ore sul progetto, è collegato a un conto "
"analitico non attivo. Cambiare il conto oppure riattivare quello corrente a "
"foglio ore sul progetto."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot set an archived employee to the existing timesheets."
msgstr ""
"Impossibile assegnare un dipendente archiviato ai fogli ore esistenti."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "You cannot use timesheets without an analytic account."
msgstr ""
"Non è possibile utilizzare i fogli di presenza senza un account analitico."

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "days"
msgstr "giorni"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
msgid "for the"
msgstr "per"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "hours"
msgstr "ore"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "per hour"
msgstr "per ora"
