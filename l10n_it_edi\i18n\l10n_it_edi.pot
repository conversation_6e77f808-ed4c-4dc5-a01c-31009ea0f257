# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_it_edi
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-03-29 14:31+0000\n"
"PO-Revision-Date: 2022-03-29 14:31+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "%s has an amount of 0.0, you must indicate the kind of exoneration."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_invoice.py:0
#, python-format
msgid ""
"%s isn't in a right state. It must be in a 'Not yet send' or 'Invalid' "
"state."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "%s must have a VAT number"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "%s must have a city."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "%s must have a codice fiscale number"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "%s must have a country"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "%s must have a country."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "%s must have a post code of length 5."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "%s must have a post code."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "%s must have a street."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_invoice.py:0
#, python-format
msgid ""
"'Scissione dei pagamenti' is not compatible with exoneration of kind 'N6'"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/res_company.py:0
#, python-format
msgid ""
"All fields about the Economic and Administrative Index must be completed."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "Attachment from XML"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "Bank account not found, useful informations from XML file:"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_codice_fiscale
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_partner__l10n_it_codice_fiscale
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_users__l10n_it_codice_fiscale
msgid "Codice Fiscale"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.constraint,message:l10n_it_edi.constraint_res_partner_l10n_it_codice_fiscale
msgid "Codice fiscale must have between 11 and 16 characters."
msgstr ""

#. module: l10n_it_edi
#: model:ir.model,name:l10n_it_edi.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_address_send_fatturapa
msgid "Company PEC-mail"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.res_company_form_l10n_it
msgid "Company have a tax representative"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.res_company_form_l10n_it
msgid "Company listed on the register of companies"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_res_company__l10n_it_mail_pec_server_id
msgid "Configure your PEC-mail server to send electronic invoices."
msgstr ""

#. module: l10n_it_edi
#: model:ir.model,name:l10n_it_edi.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_l10n_it_ddt__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_l10n_it_ddt__create_date
msgid "Created on"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_bank_statement_line__l10n_it_ddt_id
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_move__l10n_it_ddt_id
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_payment__l10n_it_ddt_id
#: model:ir.ui.menu,name:l10n_it_edi.menu_action_ddt_account
msgid "DDT"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_l10n_it_ddt__date
msgid "Data DDT"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_bank_statement_line__l10n_it_stamp_duty
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_move__l10n_it_stamp_duty
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_payment__l10n_it_stamp_duty
msgid "Dati Bollo"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_move__l10n_it_send_state__failed_delivery
msgid ""
"Delivery impossible, ES certify that it has received the invoice and that "
"the file                         could not be delivered to the addressee"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_edi_format__display_name
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_tax__display_name
#: model:ir.model.fields,field_description:l10n_it_edi.field_fetchmail_server__display_name
#: model:ir.model.fields,field_description:l10n_it_edi.field_ir_mail_server__display_name
#: model:ir.model.fields,field_description:l10n_it_edi.field_l10n_it_ddt__display_name
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_partner__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/ir_mail_server.py:0
#, python-format
msgid "E-Invoice is delivery to the destinatory:<br/>%s"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_invoice.py:0
#, python-format
msgid "E-Invoice is generated on %s by %s"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.account_invoice_line_it_FatturaPA
msgid "EAN"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model,name:l10n_it_edi.model_account_edi_format
msgid "EDI format"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/ir_mail_server.py:0
#, python-format
msgid ""
"ES certify that it has received the invoice and that the file"
"                         could not be delivered to the addressee. <br/>%s"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.res_company_form_l10n_it
msgid "Economic and Administrative Index"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.account_invoice_form_l10n_it
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.res_company_form_l10n_it
msgid "Electronic Invoicing"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_bank_statement_line__l10n_it_einvoice_id
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_move__l10n_it_einvoice_id
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_payment__l10n_it_einvoice_id
msgid "Electronic invoice"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_res_company__l10n_it_address_recipient_fatturapa
msgid "Enter Government PEC-mail address. Ex: <EMAIL>"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_res_company__l10n_it_address_send_fatturapa
msgid "Enter your company PEC-mail address. Ex: <EMAIL>"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_invoice.py:0
#, python-format
msgid "Error when sending mail with E-Invoice: %s"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_invoice.py:0
#, python-format
msgid ""
"Error when sending mail with E-Invoice: Your company must have a mail PEC "
"server and must indicate the mail PEC that will send electronic invoice."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/ir_mail_server.py:0
#, python-format
msgid "Errors in the E-Invoice :<br/>%s"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_tax__l10n_it_kind_exoneration
msgid "Exoneration"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_account_tax__l10n_it_kind_exoneration
msgid "Exoneration type"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/ir_mail_server.py:0
#, python-format
msgid ""
"Expiration of the maximum term for communication of acceptance/refusal:"
"                 %s<br/>%s"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_bank_statement_line__l10n_it_send_state
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_move__l10n_it_send_state
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_payment__l10n_it_send_state
msgid "FatturaPA Send State"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_res_company__l10n_it_codice_fiscale
msgid "Fiscal code of your company"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_address_recipient_fatturapa
msgid "Government PEC-mail"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_tax__l10n_it_has_exoneration
msgid "Has exoneration of tax (Italy)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_edi_format__id
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_move__id
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_tax__id
#: model:ir.model.fields,field_description:l10n_it_edi.field_fetchmail_server__id
#: model:ir.model.fields,field_description:l10n_it_edi.field_ir_mail_server__id
#: model:ir.model.fields,field_description:l10n_it_edi.field_l10n_it_ddt__id
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__id
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_partner__id
msgid "ID"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.account_invoice_line_it_FatturaPA
msgid "INTERNAL"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_fetchmail_server__l10n_it_is_pec
msgid ""
"If PEC Server, only mail from '...@pec.fatturapa.it' will be processed."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_invoice.py:0
#, python-format
msgid ""
"If the tax has exoneration, you must enter a kind of exoneration, a law "
"reference and the amount of the tax must be 0.0."
msgstr ""

#. module: l10n_it_edi
#: model:ir.model,name:l10n_it_edi.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/res_partner.py:0
#, python-format
msgid ""
"Invalid Codice Fiscale '%s': should be like '****************' for physical "
"person and '***********' or 'IT***********' for businesses."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_invoice.py:0
#, python-format
msgid "Invalid configuration:"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_l10n_it_ddt__invoice_id
msgid "Invoice Reference"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"Invoices for PA are not managed by Odoo, you can download the document and "
"send it on your own."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_invoice.py:0
#, python-format
msgid "Italian invoice: %s"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model,name:l10n_it_edi.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_bank_statement_line__l10n_it_einvoice_name
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_move__l10n_it_einvoice_name
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_payment__l10n_it_einvoice_name
msgid "L10N It Einvoice Name"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_has_eco_index
msgid "L10N It Has Eco Index"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_has_tax_representative
msgid "L10N It Has Tax Representative"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_edi_format____last_update
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_move____last_update
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_tax____last_update
#: model:ir.model.fields,field_description:l10n_it_edi.field_fetchmail_server____last_update
#: model:ir.model.fields,field_description:l10n_it_edi.field_ir_mail_server____last_update
#: model:ir.model.fields,field_description:l10n_it_edi.field_l10n_it_ddt____last_update
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company____last_update
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_partner____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_l10n_it_ddt__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_l10n_it_ddt__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_fetchmail_server__l10n_it_last_uid
msgid "Last message UID"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_tax__l10n_it_law_reference
msgid "Law Reference"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_eco_index_liquidation_state
msgid "Liquidation state"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.account_invoice_it_FatturaPA_export
msgid "MP05"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model,name:l10n_it_edi.model_ir_mail_server
msgid "Mail Server"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_invoice.py:0
#, python-format
msgid "Mail sent on %s by %s"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_res_company__l10n_it_eco_index_share_capital
msgid ""
"Mandatory if the seller/provider is a company with share        capital "
"(SpA, SApA, Srl), this field must contain the amount        of share capital"
" actually paid up as resulting from the last        financial statement"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_res_partner__l10n_it_pa_index
#: model:ir.model.fields,help:l10n_it_edi.field_res_users__l10n_it_pa_index
msgid ""
"Must contain the 6-character (or 7) code, present in the PA              "
"Index in the information relative to the electronic invoicing service,"
"              associated with the office which, within the addressee "
"administration, deals              with receiving (and processing) the "
"invoice."
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_move__l10n_it_send_state__new
msgid "New"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_eco_index_sole_shareholder__no
msgid "Not a limited liability company"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_move__l10n_it_send_state__to_send
msgid "Not yet send"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_eco_index_number
msgid "Number in register of companies"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_l10n_it_ddt__name
msgid "Numero DDT"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/ir_mail_server.py:0
#, python-format
msgid "Original E-invoice XML file"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_move__l10n_it_send_state__other
msgid "Other"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/ir_mail_server.py:0
#, python-format
msgid "Outcome notice: %s<br/>%s"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_partner__l10n_it_pa_index
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_users__l10n_it_pa_index
msgid "PA index"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.constraint,message:l10n_it_edi.constraint_res_partner_l10n_it_pa_index
msgid "PA index must have between 6 and 7 characters."
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.account_invoice_it_FatturaPA_export
msgid "PDF"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_partner__l10n_it_pec_email
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_users__l10n_it_pec_email
msgid "PEC e-mail"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/ir_mail_server.py:0
#, python-format
msgid "PEC mail server must be of type IMAP."
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_fetchmail_server__l10n_it_is_pec
msgid "PEC server"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_eco_index_sole_shareholder__sm
msgid "Più soci"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/ir_mail_server.py:0
#, python-format
msgid "Please configure Government PEC-mail\tin company settings"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/ir_mail_server.py:0
#, python-format
msgid "Please configure Username for this Server PEC"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_res_company__l10n_it_tax_system
msgid "Please select the Tax system to which you are subjected."
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_eco_index_office
msgid "Province of the register-of-companies office"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.account_invoice_it_FatturaPA_export
msgid "SI"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_invoice.py:0
#, python-format
msgid "Sending file: %s"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_invoice.py:0
#, python-format
msgid "Sending file: %s to ES: %s"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_move__l10n_it_send_state__invalid
msgid "Sent, but invalid"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_move__l10n_it_send_state__sent
msgid "Sent, waiting for response"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_mail_pec_server_id
msgid "Server PEC"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_eco_index_share_capital
msgid "Share capital actually paid up"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_eco_index_sole_shareholder
msgid "Shareholder"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_eco_index_sole_shareholder__su
msgid "Socio unico"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.account_invoice_it_FatturaPA_export
msgid "TP01"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.account_invoice_it_FatturaPA_export
msgid "TP02"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model,name:l10n_it_edi.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_tax_system
msgid "Tax System"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_account_tax__l10n_it_has_exoneration
msgid "Tax has a tax exoneration."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"Tax not found with percentage: %s and exoneration %s for the article: %s"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "Tax not found with percentage: %s for the article: %s"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.res_company_form_l10n_it
msgid "Tax representative"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_res_company__l10n_it_tax_representative_partner_id
msgid "Tax representative partner"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "Tax representative partner %s of %s must have a tax number."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/ir_mail_server.py:0
#, python-format
msgid ""
"The E-invoice is not delivered to the addressee. The Exchange System is"
"                unable to deliver the file to the Public Administration. The"
" Exchange System will                contact the PA to report the problem "
"and request that they provide a solution.                 During the "
"following 15 days, the Exchange System will try to forward the FatturaPA"
"                file to the Administration in question again. More "
"information:<br/>%s"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"The buyer, %s, or his company must have either a VAT number either a tax "
"code (Codice Fiscale)."
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_eco_index_liquidation_state__ls
msgid "The company is in a state of liquidation"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_eco_index_liquidation_state__ln
msgid "The company is not in a state of liquidation"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"The maximum length for VAT number is 30. %s have a VAT number too long: %s."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "The seller must have a bank account."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "The seller's company must have a tax system."
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.res_company_form_l10n_it
msgid ""
"The seller/provider is a company listed on the register of companies and as\n"
"                            such must also indicate the registration data on all documents (art. 2250, Italian\n"
"                            Civil Code)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_res_company__l10n_it_has_eco_index
msgid ""
"The seller/provider is a company listed on the register of companies and as"
"        such must also indicate the registration data on all documents (art."
" 2250, Italian        Civil Code)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_res_company__l10n_it_has_tax_representative
msgid ""
"The seller/provider is a non-resident subject which        carries out "
"transactions in Italy with relevance for VAT        purposes and which takes"
" avail of a tax representative in        Italy"
msgstr ""

#. module: l10n_it_edi
#: model_terms:ir.ui.view,arch_db:l10n_it_edi.res_company_form_l10n_it
msgid ""
"The seller/provider is a non-resident subject which carries out transactions in Italy\n"
"                            with relevance for VAT purposes and which takes avail of a tax representative in Italy"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_res_company__l10n_it_eco_index_number
msgid ""
"This field must contain the number under which the        seller/provider is"
" listed on the register of companies."
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_move__l10n_it_send_state__delivered
msgid "This invoice is delivered"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_move__l10n_it_send_state__delivered_accepted
msgid "This invoice is delivered and accepted by destinatory"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_move__l10n_it_send_state__delivered_expired
msgid ""
"This invoice is delivered and expired (expiry of the maximum term for "
"communication of acceptance/refusal)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_move__l10n_it_send_state__delivered_refused
msgid "This invoice is delivered and refused by destinatory"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "Total amount from the XML File: %s"
msgstr ""

#. module: l10n_it_edi
#: model:ir.actions.act_window,name:l10n_it_edi.action_ddt_account
#: model:ir.model,name:l10n_it_edi.model_l10n_it_ddt
msgid "Transport Document"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_l10n_it_ddt__date
msgid "Transport document date"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,help:l10n_it_edi.field_l10n_it_ddt__name
msgid "Transport document number"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "Transport informations from XML file:"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields,field_description:l10n_it_edi.field_account_tax__l10n_it_vat_due_date
msgid "VAT due date"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "Vendor not found, useful informations from XML file:"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"You can't regenerate an E-Invoice when the first one is sent and there are "
"no errors"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/res_company.py:0
#, python-format
msgid "You must select a tax representative."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "You must select one and only one tax by line."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/res_company.py:0
#, python-format
msgid "Your tax representative partner must have a country."
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/res_company.py:0
#, python-format
msgid "Your tax representative partner must have a tax number."
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_vat_due_date__d
msgid "[D] IVA ad esigibilità differita"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_vat_due_date__i
msgid "[I] IVA ad esigibilità immediata"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n1
msgid "[N1] Escluse ex art. 15"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n2_1
msgid ""
"[N2.1] Non soggette ad IVA ai sensi degli artt. Da 7 a 7-septies del DPR "
"633/72"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n2_2
msgid "[N2.2] Non soggette – altri casi"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n2
msgid "[N2] Non soggette"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n3_1
msgid "[N3.1] Non imponibili – esportazioni"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n3_2
msgid "[N3.2] Non imponibili – cessioni intracomunitarie"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n3_3
msgid "[N3.3] Non imponibili – cessioni verso San Marino"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n3_4
msgid ""
"[N3.4] Non imponibili – operazioni assimilate alle cessioni all’esportazione"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n3_5
msgid "[N3.5] Non imponibili – a seguito di dichiarazioni d’intento"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n3_6
msgid ""
"[N3.6] Non imponibili – altre operazioni che non concorrono alla formazione "
"del plafond"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n3
msgid "[N3] Non imponibili"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n4
msgid "[N4] Esenti"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n5
msgid "[N5] Regime del margine / IVA non esposta in fattura"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n6_1
msgid ""
"[N6.1] Inversione contabile – cessione di rottami e altri materiali di "
"recupero"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n6_2
msgid "[N6.2] Inversione contabile – cessione di oro e argento puro"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n6_3
msgid "[N6.3] Inversione contabile – subappalto nel settore edile"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n6_4
msgid "[N6.4] Inversione contabile – cessione di fabbricati"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n6_5
msgid "[N6.5] Inversione contabile – cessione di telefoni cellulari"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n6_6
msgid "[N6.6] Inversione contabile – cessione di prodotti elettronici"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n6_7
msgid ""
"[N6.7] Inversione contabile – prestazioni comparto edile esettori connessi"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n6_8
msgid "[N6.8] Inversione contabile – operazioni settore energetico"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n6_9
msgid "[N6.9] Inversione contabile – altri casi"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n6
msgid ""
"[N6] Inversione contabile (per le operazioni in reverse charge ovvero nei "
"casi di autofatturazione per acquisti extra UE di servizi ovvero per "
"importazioni di beni nei soli casi previsti)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_kind_exoneration__n7
msgid ""
"[N7] IVA assolta in altro stato UE (vendite a distanza ex art. 40 c. 3 e 4 e"
" art. 41 c. 1 lett. b,  DL 331/93; prestazione di servizi di "
"telecomunicazioni, tele-radiodiffusione ed elettronici ex art. 7-sexies "
"lett. f, g, art. 74-sexies DPR 633/72)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf01
msgid "[RF01] Ordinario"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf02
msgid "[RF02] Contribuenti minimi (art.1, c.96-117, L. 244/07)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf04
msgid ""
"[RF04] Agricoltura e attività connesse e pesca (artt.34 e 34-bis, DPR "
"633/72)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf05
msgid "[RF05] Vendita sali e tabacchi (art.74, c.1, DPR. 633/72)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf06
msgid "[RF06] Commercio fiammiferi (art.74, c.1, DPR  633/72)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf07
msgid "[RF07] Editoria (art.74, c.1, DPR  633/72)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf08
msgid "[RF08] Gestione servizi telefonia pubblica (art.74, c.1, DPR 633/72)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf09
msgid ""
"[RF09] Rivendita documenti di trasporto pubblico e di sosta (art.74, c.1, "
"DPR  633/72)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf10
msgid ""
"[RF10] Intrattenimenti, giochi e altre attività di cui alla tariffa allegata"
" al DPR 640/72 (art.74, c.6, DPR 633/72)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf11
msgid "[RF11] Agenzie viaggi e turismo (art.74-ter, DPR 633/72)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf12
msgid "[RF12] Agriturismo (art.5, c.2, L. 413/91)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf13
msgid "[RF13] Vendite a domicilio (art.25-bis, c.6, DPR  600/73)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf14
msgid ""
"[RF14] Rivendita beni usati, oggetti d’arte, d’antiquariato o da collezione "
"(art.36, DL 41/95)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf15
msgid ""
"[RF15] Agenzie di vendite all’asta di oggetti d’arte, antiquariato o da "
"collezione (art.40-bis, DL 41/95)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf16
msgid "[RF16] IVA per cassa P.A. (art.6, c.5, DPR 633/72)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf17
msgid "[RF17] IVA per cassa (art. 32-bis, DL 83/2012)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf18
msgid "[RF18] Altro"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__res_company__l10n_it_tax_system__rf19
msgid "[RF19] Regime forfettario (art.1, c.54-89, L. 190/2014)"
msgstr ""

#. module: l10n_it_edi
#: model:ir.model.fields.selection,name:l10n_it_edi.selection__account_tax__l10n_it_vat_due_date__s
msgid "[S] Scissione dei pagamenti"
msgstr ""

#. module: l10n_it_edi
#: code:addons/l10n_it_edi/models/account_edi_format.py:0
#, python-format
msgid "from XML file:"
msgstr ""
