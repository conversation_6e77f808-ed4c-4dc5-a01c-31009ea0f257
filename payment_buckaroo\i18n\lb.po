# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_buckaroo
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields.selection,name:payment_buckaroo.selection__payment_acquirer__provider__buckaroo
msgid "Buckaroo"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "Buckaroo: invalid shasign, received %s, computed %s, for data %s"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid "Buckaroo: received data for reference %s"
msgstr ""

#. module: payment_buckaroo
#: code:addons/payment_buckaroo/models/payment.py:0
#, python-format
msgid ""
"Buckaroo: received data with missing reference (%s) or pay_id (%s) or "
"shasign (%s)"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__provider
msgid "Provider"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_secretkey
msgid "SecretKey"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_acquirer__brq_websitekey
msgid "WebsiteKey"
msgstr ""
