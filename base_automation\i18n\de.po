# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_automation
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>Nessel<PERSON>ch, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"\"\n"
"                (ID:"
msgstr ""
"“\n"
"                (ID:"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__help
msgid "Action Description"
msgstr "Aktionsbeschreibung"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__name
msgid "Action Name"
msgstr "Name der Aktion"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__state
msgid "Action To Do"
msgstr "Folgeaktion"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__type
msgid "Action Type"
msgstr "Aktionsart"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__active
msgid "Active"
msgstr "Aktiv"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_type_id
msgid "Activity"
msgstr "Aktivität"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_type
msgid "Activity User Type"
msgstr "Benutzertyp der Aktivität"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__partner_ids
msgid "Add Followers"
msgstr "Follower hinzufügen"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_domain
msgid "Apply on"
msgstr "Anwenden auf"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_search
msgid "Archived"
msgstr "Archiviert"

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__ir_actions_server__usage__base_automation
msgid "Automated Action"
msgstr "Automatisierte Aktion"

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
msgid "Automated Actions"
msgstr "Automatisierte Aktionen"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation"
msgstr "Automatisierung"

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
#: model:ir.cron,cron_name:base_automation.ir_cron_data_base_automation_check
#: model:ir.cron,name:base_automation.ir_cron_data_base_automation_check
msgid "Base Action Rule: check and execute"
msgstr "Basisaktionsregel: Überprüfen und Ausführen"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_change
msgid "Based on Form Modification"
msgstr "Auf Basis von Formularanpassungen"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time
msgid "Based on Timed Condition"
msgstr "Auf Basis zeitlicher Bedingung"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_pre_domain
msgid "Before Update Domain"
msgstr "Domain vor Aktualisierung"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_model_id
msgid "Binding Model"
msgstr "Bindungsmodell"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_type
msgid "Binding Type"
msgstr "Bindungstyp"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__binding_view_types
msgid "Binding View Types"
msgstr "Bindende Ansichtstypen"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__child_ids
msgid "Child Actions"
msgstr "Untergeordnete Aktion"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__child_ids
msgid ""
"Child server actions that will be executed. Note that the last return "
"returned action value will be used as global return value."
msgstr ""
"Untergeordnete Serveraktion, die ausgeführt werden soll. Bitte beachten Sie,"
" das der letzte Rückgabewert der ausgeführten Aktion als globaler "
"Rückgabewert verwendet wird."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__day
msgid "Days"
msgstr "Tage"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_range
msgid ""
"Delay after the trigger date.\n"
"                                    You can put a negative number if you need a delay before the\n"
"                                    trigger date, like sending a reminder 15 minutes before a meeting."
msgstr ""
"Verzögerung nach dem Auslösedatum.\n"
"Sie können eine negative Zahl eingeben, wenn Sie eine Verzögerung vor dem Triggerdatum, wie z.B. das Senden einer Erinnerung 15 Minuten vor einer Besprechung."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range
msgid "Delay after trigger date"
msgstr "Verzögerung nach Auslösedatum"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range_type
msgid "Delay type"
msgstr "Verzögerungstyp"

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "Disable Action"
msgstr "Aktion deaktivieren"

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"Disabling this automated action will enable you to continue your workflow\n"
"                but any data created after this could potentially be corrupted,\n"
"                as you are effectively disabling a customization that may set\n"
"                important and/or required fields."
msgstr ""
"Wenn Sie diese automatische Aktion deaktivieren, können Sie Ihren Arbeitsablauf fortsetzen.\n"
"Daten, die danach erstellt werden, könnten jedoch möglicherweise beschädigt werden,\n"
"da Sie eine Funktion deaktivieren, die möglicherweise wichtige\n"
"und/oder erforderliche Felder beinhaltet."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range
msgid "Due Date In"
msgstr "Fälligkeitsdatum in"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_date_deadline_range_type
msgid "Due type"
msgstr "Fälligkeitstyp"

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "Edit action"
msgstr "Aktion bearbeiten"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__template_id
msgid "Email Template"
msgstr "E-Mail-Vorlage"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__xml_id
msgid "External ID"
msgstr "Externe ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr "Felder, die den Onchange auslösen."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__groups_id
msgid "Groups"
msgstr "Gruppen"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__hour
msgid "Hours"
msgstr "Stunden"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__id
msgid "ID"
msgstr "ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "ID dieser Aktion, wenn in einer XML-Datei definiert"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the action "
"rule."
msgstr ""
"Falls vorhanden, muss diese Bedingung vor Ausführung der Aktionsregel "
"erfüllt sein."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record."
msgstr ""
"Falls vorhanden, muss diese Bedingung vor der Aktualisierung des Datensatzes"
" erfüllt werden."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__last_run
msgid "Last Run"
msgstr "Zuletzt ausgeführt"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__least_delay_msg
msgid "Least Delay Msg"
msgstr "Nachricht für geringste Verzögerung"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__link_field_id
msgid "Link Field"
msgstr "Link-Feld"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__minutes
msgid "Minutes"
msgstr "Minuten"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_id
msgid "Model"
msgstr "Modell"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_name
msgid "Model Name"
msgstr "Modellname"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__crud_model_id
msgid ""
"Model for record creation / update. Set this field only to specify a "
"different model than the base model."
msgstr ""
"Modell für die Datensatzerstellung/-aktualisierung. Setzen Sie dieses Feld "
"nur, wenn Sie ein anderes Modell als das Basismodell angeben möchten."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__model_id
msgid "Model on which the server action runs."
msgstr "Modell, auf dem die Serveraktion läuft."

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__month
msgid "Months"
msgstr "Monate"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_note
msgid "Note"
msgstr "Notiz"

#. module: base_automation
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Note that this action can be trigged up to %d minutes after its schedule."
msgstr ""
"Beachten Sie, dass diese Aktion bis zu %d Minuten nach ihrem Zeitplan "
"ausgelöst werden kann."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "Auslöser für OnChange-Felder"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create
msgid "On Creation"
msgstr "Beim Erstellen"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create_or_write
msgid "On Creation & Update"
msgstr "Beim Erstellen und Aktualisieren"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unlink
msgid "On Deletion"
msgstr "Beim Löschen"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_write
msgid "On Update"
msgstr "Beim Aktualisieren"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__help
msgid ""
"Optional help text for the users with a description of the target view, such"
" as its usage and purpose."
msgstr ""
"Optionaler Hilfetext für Benutzer mit der Beschreibung der Zielansicht, "
"deren Verwendung und Zweck."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__link_field_id
msgid ""
"Provide the field used to link the newly created record on the record used "
"by the server action."
msgstr ""
"Geben Sie das Feld an, das zur Verknüpfung des neu erstellten Datensatzes "
"mit dem von der Serveraktion verwendeten Datensatz verwendet wird."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__code
msgid "Python Code"
msgstr "Python-Code"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_id
msgid "Responsible"
msgstr "Verantwortlich"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__sequence
msgid "Sequence"
msgstr "Sequenz"

#. module: base_automation
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "Server Action"
msgstr "Serveraktion"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__action_server_id
msgid "Server Actions"
msgstr "Serveraktionen"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__binding_model_id
msgid ""
"Setting a value makes this action available in the sidebar for the given "
"model."
msgstr ""
"Durch das Setzen eines Wertes wird diese Aktion in der Seitenleiste für das "
"angegebene Modell verfügbar."

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Setup a new automated automation"
msgstr "Richten Sie eine neue Automatisierung ein"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_summary
msgid "Summary"
msgstr "Zusammenfassung"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_id
msgid "Target Model"
msgstr "Zielmodell"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__crud_model_name
msgid "Target Model Name"
msgstr "Zielmodellname"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr "Technische Bezeichnung des Benutzers im Datensatz"

#. module: base_automation
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"The \"%(trigger_value)s\" %(trigger_label)s can only be used with the "
"\"%(state_value)s\" action type"
msgstr ""
"„%(trigger_value)s“ %(trigger_label)s kann nur benutzt werden mit dem "
"Aktionstyp „%(state_value)s“."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trigger_field_ids
msgid ""
"The action will be triggered if and only if one of these fields is "
"updated.If empty, all fields are watched."
msgstr ""
"Diese Aufgabe wird ausgelöst, wenn eines dieser Felder aktualisiert wird. "
"Ohne Angabe werden alle Felder auf Änderungen beobachtet."

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"The error occurred during the execution of the automated action\n"
"                \""
msgstr ""
"Der Fehler ist während der Ausführung der automatisierten Aktion aufgetreten\n"
"\""

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger
msgid "Trigger"
msgstr "Auslöser"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_id
msgid "Trigger Date"
msgstr "Auslösedatum"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger_field_ids
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Trigger Fields"
msgstr "Auslöser-Felder"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""
"Art der Serveraktionen. Folgende Werte sind verfügbar:\n"
"- „Python-Code ausführen“: ein Block Python-Code, der ausgeführt wird\n"
"- „Erstellen“: einen neuen Datensatz mit neuen Werten erstellen\n"
"- „Einen Datensatz aktualisieren“: die Werte eines Datensatzes aktualisieren\n"
"- „Mehrere Aktionen durchführen“: eine Aktion definieren, die weitere Serveraktionen auslöst\n"
"- „E-Mail versenden“: eine Nachricht oder Notiz automatisch hinterlassen oder eine E-Mail automatisch versenden (Dialog)\n"
"- „Follower hinzufügen“: einem Datensatz Follower hinzufügen (Dialog)\n"
"- „Nächste Aktivität erstellen“: eine Aktivität erstellen (Dialog)"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__usage
msgid "Usage"
msgstr "Verwendung"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""
"Verwenden Sie einen „Bestimmten Benutzer“, um bei der nächsten Aktivität "
"immer denselben Benutzer zugewiesen  zu bekommen. Verwenden Sie  „Aus "
"Datensatz generierter Benutzer“, um den Feldnamen des Benutzers anzugeben, "
"der im Datensatz ausgewählt werden soll."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_calendar_id
msgid "Use Calendar"
msgstr "Kalender verwenden"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Use automated actions to automatically trigger actions for\n"
"                various screens. Example: a lead created by a specific user may\n"
"                be automatically set to a specific Sales Team, or an\n"
"                opportunity which still has status pending after 14 days might\n"
"                trigger an automatic reminder email."
msgstr ""
"Verwenden Sie automatische Aktionen, um automatisiert Aktionen für\n"
"unterschiedliche Modelle auszulösen. Zum Beispiel: Ein Lead, erstellt durch einen bestimmten Benutzer wird\n"
"automatisch einem bestimmten Verkaufsteam zugewiesen. \n"
"Oder: Eine Verkaufschance die nach 14 Tagen noch den Status „Ausstehend“ hat, \n"
"löst automatisch eine Erinnerungs-E-Mail aus."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__activity_user_field_name
msgid "User field name"
msgstr "Benutzer-Feldname"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__fields_lines
msgid "Value Mapping"
msgstr "Wertzuordnung"

#. module: base_automation
#: code:addons/base_automation/models/base_automation.py:0
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Warning"
msgstr "Warnung"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possible to use a "
"calendar to compute the date based on working days."
msgstr ""
"Zur Berechnung Ihrer täglichen zeitlichen Bedingungen, können Sie einen "
"Kalender für die Terminberechnung auf Basis von Arbeitstagen nutzen."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__sequence
msgid ""
"When dealing with multiple actions, the execution order is based on the "
"sequence. Low number means high priority."
msgstr ""
"Wichtig beim Umgang mit mehreren Aktionen: Die Reihenfolge der Ausführung "
"erfolgt nach aufsteigenden Nummern."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                                  If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""
"Wann die Bedingung ausgelöst werden sollte.\n"
" Falls vorhanden, wird der Zeitplaner die Überprüfung vornehmen. Falls leer, wird bei der Erstellung und Aktualisierung eine Überprüfung vorgenommen."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr ""
"Wenn diese Option deaktiviert wurde, wird die Regel ausgeblendet und auch "
"demnach nicht ausgeführt."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__code
msgid ""
"Write Python code that the action will execute. Some variables are available"
" for use; help about python expression is given in the help tab."
msgstr ""
"Schreiben Sie Python-Code, den die Aktion ausführen wird. Einige Variablen "
"stehen zur Verfügung. Hilfe zum Python-Ausdruck finden Sie auf der "
"Registerkarte „Hilfe“."

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"You can ask an administrator to disable or correct this automated action."
msgstr ""
"Sie können einen Administrator bitten die automatisierte Aktion zu "
"deaktivieren oder zu korrigieren."

#. module: base_automation
#. openerp-web
#: code:addons/base_automation/static/src/xml/base_automation_error_dialog.xml:0
#, python-format
msgid "You can disable this automated action or edit it to solve the issue."
msgstr ""
"Sie können diese automatisierte Aktion deaktivieren oder bearbeiten, um das "
"Problem zu beheben."

#. module: base_automation
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"You cannot send an email, add followers or create an activity for a deleted "
"record.  It simply does not work."
msgstr ""
"Sie können für einen gelöschten Datensatz keine E-Mail senden, Follower "
"hinzufügen oder eine Aktivität erstellen. Das funktioniert einfach nicht."
