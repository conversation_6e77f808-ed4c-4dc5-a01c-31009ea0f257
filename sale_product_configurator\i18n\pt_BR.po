# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_configurator
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Kevilyn Rosa, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "<i class=\"fa fa-shopping-cart add-optionnal-item\"/> Add to cart"
msgstr ""
"<i class=\"fa fa-shopping-cart add-optionnal-item\"/>Adicionar no carrinho"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Price</span>"
msgstr "<span class=\"label\">Preço</span>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Product</span>"
msgstr "<span class=\"label\">Produto</span>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Quantity</span>"
msgstr "<span class=\"label\">Quantidade</span>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<strong>Total:</strong>"
msgstr "<strong>Total:</strong>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Add"
msgstr "Adicionar"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_quantity_config
msgid "Add one"
msgstr "Adicionar um"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Valores do Atributo"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "Available Options:"
msgstr "Opções Disponíveis:"

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Back"
msgstr "Voltar"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: sale_product_configurator
#: model:product.template,name:sale_product_configurator.product_product_1_product_template
msgid "Chair floor protection"
msgstr "Proteção de piso para cadeiras"

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Configure"
msgstr "Configurar"

#. module: sale_product_configurator
#: model:ir.actions.act_window,name:sale_product_configurator.sale_product_configurator_action
msgid "Configure a product"
msgstr "Configurar um produto"

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__create_date
msgid "Created on"
msgstr "Criado em"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr "Valores Personalizados"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr "Valores Extras"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__id
msgid "ID"
msgstr "ID"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "Este é um produto configurável?"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: sale_product_configurator
#: model:product.template,description_sale:sale_product_configurator.product_product_1_product_template
msgid "Office chairs can harm your floor: protect it."
msgstr "Cadeiras de escritório podem prejudicar seu piso: proteja-o."

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "Option not available"
msgstr "Opção não disponível"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_template__optional_product_ids
msgid "Optional Products"
msgstr "Produtos Opcionais"

#. module: sale_product_configurator
#: model:ir.model.fields,help:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,help:sale_product_configurator.field_product_template__optional_product_ids
msgid ""
"Optional Products are suggested whenever the customer hits *Add to Cart* "
"(cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr ""
"Produtos opcionais são sugeridos sempre que o cliente clica *Adicionar ao "
"Carrinho* (estratégia de venda cruzada, por exemplo, para computadores: "
"garantia, software, etc.)."

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__pricelist_id
msgid "Pricelist"
msgstr "Lista de Preço"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_template_id
msgid "Product"
msgstr "Produto"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "Product Image"
msgstr "Imagem do Produto"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_product_template
msgid "Product Template"
msgstr "Modelo de Produto"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_order_view_form
msgid "Product Variant"
msgstr "Variação do Produto"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__quantity
msgid "Quantity"
msgstr "Quantidade"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_template_view_form
msgid "Recommend when 'Adding to Cart' or quotation"
msgstr "Recomenda ao adicionar produto ao carrinho ou cotação"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_quantity_config
msgid "Remove one"
msgstr "Remova um"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_sale_product_configurator
msgid "Sale Product Configurator"
msgstr "Configurador de Produtos de Venda"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_sale_order_line
msgid "Sales Order Line"
msgstr "Linha do pedido de vendas"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Save"
msgstr "Salvar"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
msgid "This combination does not exist."
msgstr "Essa combinação não existe."

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
msgid "This product has no valid combination."
msgstr "Este produto não tem uma combinação válida."

#. module: sale_product_configurator
#: model:product.template,uom_name:sale_product_configurator.product_product_1_product_template
msgid "Units"
msgstr "Unidades"
