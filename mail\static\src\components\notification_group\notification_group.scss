// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_NotificationGroup {
    @include o-mail-notification-list-item-layout();

    &:hover .o_NotificationGroup_markAsRead {
        // TODO also mixin this
        // task-2258605
        opacity: 1;
    }
}

.o_NotificationGroup_content {
    @include o-mail-notification-list-item-content-layout();
}

.o_NotificationGroup_core {
    @include o-mail-notification-list-item-core-layout();
}

.o_NotificationGroup_coreItem {
    @include o-mail-notification-list-item-core-item-layout();
}

.o_NotificationGroup_counter {
    @include o-mail-notification-list-item-counter-layout();
}

.o_NotificationGroup_date {
    @include o-mail-notification-list-item-date-layout();
}

.o_NotificationGroup_header {
    @include o-mail-notification-list-item-header-layout();
}

.o_NotificationGroup_image {
    @include o-mail-notification-list-item-image-layout();
}

.o_NotificationGroup_imageContainer {
    @include o-mail-notification-list-item-image-container-layout();
}

.o_NotificationGroup_inlineText {
    @include o-mail-notification-list-item-inline-text-layout();
}

.o_NotificationGroup_markAsRead {
    @include o-mail-notification-list-item-mark-as-read-layout();
}

.o_NotificationGroup_name {
    @include o-mail-notification-list-item-name-layout();
}

.o_NotificationGroup_sidebar {
    @include o-mail-notification-list-item-sidebar-layout();
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_NotificationGroup {
    @include o-mail-notification-list-item-style();
}

.o_NotificationGroup_core {
    @include o-mail-notification-list-item-core-style();
}

.o_NotificationGroup_counter {
    @include o-mail-notification-list-item-bold-style();
}

.o_NotificationGroup_date {
    @include o-mail-notification-list-item-date-style();
}

.o_NotificationGroup_image {
    @include o-mail-notification-list-item-image-style();
}

.o_NotificationGroup_markAsRead {
    @include o-mail-notification-list-item-mark-as-read-style();
}

.o_NotificationGroup_name {
    @include o-mail-notification-list-item-bold-style();
}
