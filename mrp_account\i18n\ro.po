# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: mrp_account
#: code:addons/mrp_account/models/mrp_production.py:0
#: model:ir.model,name:mrp_account.model_account_analytic_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_account_id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_account_id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_id
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
#, python-format
msgid "Analytic Account"
msgstr "Cont analitic"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Linie analitica"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_production__analytic_account_id
msgid ""
"Analytic account in which cost and revenue entries will take        place "
"for financial management of the manufacturing order."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_bom__analytic_account_id
msgid ""
"Analytic account in which cost and revenue entries will take place for "
"financial management of the manufacturing order."
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_bom
msgid "Bill of Material"
msgstr "Listă de materiale"

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
#, python-format
msgid "Bills of Materials"
msgstr "Liste de materiale"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr "Categorie"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr "Calcul preț din LdM"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__cost_already_recorded
msgid "Cost Recorded"
msgstr "Cost Înregistrat"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Cost"
msgstr "Cost suplimentar"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_production__extra_cost
msgid "Extra cost per produced unit"
msgstr "Cost suplimentar pe unitate produsă"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__costs_hour_account_id
msgid ""
"Fill this only if you want automatic analytic accounting entries on "
"production orders."
msgstr ""
"Completați doar dacă doriți înregistrări contabile analitice automate ale "
"comenzilor de producție."

#. module: mrp_account
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr "Comanda de Producție"

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
#, python-format
msgid "Manufacturing Orders"
msgstr "Comenzi de producție"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
msgid "Manufacturing Orders Count"
msgstr ""

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_id
msgid "Mo Analytic Account Line"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product"
msgstr "Produs"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product Template"
msgstr "Șablon produs"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
msgid "Production Order"
msgstr "Comanda de Producție"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr "Mișcare stoc"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_rule
msgid "Stock Rule"
msgstr "Regulă stoc"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter_productivity__cost_already_recorded
msgid ""
"Technical field automatically checked when a ongoing production posts "
"journal entries for its costs. This way, we can record one production's cost"
" multiple times and only consider new entries in the work centers time "
"lines."
msgstr ""

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "Valuation"
msgstr "Evaluare"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_id
msgid "Wc Analytic Account Line"
msgstr ""

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "Centru de lucru"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr "Comanda de Lucru"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr ""

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#, python-format
msgid "Work Orders"
msgstr "Comenzi de lucru"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "Jurnal productivitate centru de lucru"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenters"
msgstr ""

#. module: mrp_account
#: code:addons/mrp_account/models/stock_move.py:0
#, python-format
msgid "[Raw] %s"
msgstr ""

#. module: mrp_account
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
#, python-format
msgid "[WC] %s"
msgstr ""
