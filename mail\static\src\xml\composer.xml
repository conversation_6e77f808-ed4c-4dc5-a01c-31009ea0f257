<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <div t-name="mail.legacy.Composer.emojis" class="o_mail_emoji_container">
        <t t-foreach="emojis" t-as="emoji">
            <span t-att-data-emoji="emoji.sources[0]" class="o_mail_emoji" t-att-title="emoji.description" t-att-aria-label="emoji.description">
                <t t-out="emoji.unicode"/>
            </span>
        </t>
    </div>

    <t t-name="FieldMany2ManyTagsEmail" t-extend="FieldMany2ManyTag">
        <t t-jquery="[t-attf-class*=badge]" t-operation="replace">
            <div t-attf-class="badge badge-pill dropdown o_tag_color_0 #{el.email.indexOf('@') &lt; 0 ? 'o_tag_error' : ''}" t-att-data-color="color" t-att-data-index="el_index" t-att-data-id="el.id" t-attf-title="Tag color: #{colornames[color]}">
                <span class="o_badge_text" t-att-title="el.email"><t t-esc="el.display_name"/></span>
                <a t-if="!readonly" href="#" class="fa fa-times o_delete" title="Delete" aria-label="Delete"/>
            </div>
        </t>
    </t>
</templates>
