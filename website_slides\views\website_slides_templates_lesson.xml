<?xml version="1.0" ?>
<odoo><data>

<!-- Slide: main template: detailed view -->
<template id="slide_main" name="Slide Detailed View" track="1">
    <t t-set="body_classname" t-value="'o_wslides_body'"/>
    <t t-call="website.layout">
        <div id="wrap" class="wrap o_wslides_wrap">
            <div class="o_wslides_lesson_header o_wslides_gradient position-relative text-white pb-0 pt-2 pt-md-5">
                <t t-call="website_slides.course_nav">
                    <t t-set="channel" t-value="slide.channel_id"/>
                </t>
                <div class="container o_wslides_lesson_header_container mt-5 mt-md-3 mt-xl-4">
                    <div class="row align-items-end align-items-md-stretch">
                        <div t-attf-class="col-12 col-lg-9 d-flex flex-column #{'offset-lg-3' if slide.channel_id.channel_type == 'training' else ''}">
                            <h2 class="font-weight-medium w-100">
                                <a t-att-href="'/slides/%s' % (slug(slide.channel_id))" class="text-white text-decoration-none" t-field="slide.channel_id.name"/>
                                <t t-if="slide.channel_id.completed">
                                    <small><span class="badge badge-pill badge-success pull-right my-1 py-1 px-2 font-weight-normal"><i class="fa fa-check"/> Completed</span></small>
                                </t>
                            </h2>

                            <div t-if="slide.channel_id.channel_type == 'documentation'" class="mb-3 small">
                                <span class="font-weight-normal">Last update:</span>
                                <t t-esc="slide.date_published" t-options="{'widget': 'date'}"/>
                            </div>

                            <div t-else="" t-if="not slide.channel_id.completed" class="d-flex align-items-center pb-3">
                                <div class="progress w-50 bg-black-25" style="height: 10px;">
                                    <div class="progress-bar rounded-left" role="progressbar"
                                        t-att-aria-valuenow="slide.channel_id.completion" aria-valuemin="0" aria-valuemax="100"
                                        t-attf-style="width: #{slide.channel_id.completion}%;">
                                    </div>
                                </div>
                                <i t-att-class="'fa fa-trophy m-0 ml-2 p-0 %s' % ('text-warning' if slide.channel_id.completed else 'text-black-50')"></i>
                                <small class="ml-2 text-white-50"><t t-esc="slide.channel_id.completion"/> %</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="container o_wslides_lesson_main">
                <div class="row">
                    <div t-attf-class="o_wslides_lesson_aside col-lg-3 #{'order-2' if slide.channel_id.channel_type == 'documentation' else ''}">
                        <t t-if="slide.channel_id.channel_type == 'training'" t-call="website_slides.slide_aside_training"/>
                        <t t-if="slide.channel_id.channel_type == 'documentation'" t-call="website_slides.slide_aside_documentation"/>
                    </div>
                    <div t-attf-class="o_wslides_lesson_content col-lg-9 #{'order-1' if slide.channel_id.channel_type == 'documentation' else ''}">
                        <t t-call="website_slides.slide_content_detailed"/>
                    </div>
                </div>
            </div>
        </div>
    </t>
</template>

<!-- Slide: sidebar documentation mode -->
<template id="slide_aside_documentation" name="Slide: Sidebar in Documentation">
    <div class="o_wslides_lesson_aside_doc position-relative bg-white pb-1 my-3 border-bottom">
        <ul class="nav nav-tabs nav-fill" role="tablist">
            <li class="nav-item"><a aria-controls="related" href="#related" class="nav-link rounded-0 border-top-0 border-left-0 py-2 active" data-toggle="tab">Related</a></li>
            <li class="nav-item"><a aria-controls="most_viewed" href="#most_viewed" class="nav-link rounded-0 border-top-0 border-right-0 py-2" data-toggle="tab">Most Viewed</a></li>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" id="related" class="tab-pane active bg-100">
                <ul class="list-group list-group-flush">
                    <t t-set="related_slides_list" t-value="list(related_slides)"/>
                    <t t-if="not related_slides_list">
                        No presentation available.
                    </t>
                    <t t-else="" t-foreach="related_slides_list" t-as="aside_slide">
                        <t t-call="website_slides.slide_aside_card"/>
                    </t>
                </ul>
            </div>
            <div role="tabpanel" id="most_viewed" class="tab-pane bg-100">
                <ul class="list-group list-group-flush">
                    <t t-set="most_viewed_slides_list" t-value="list(most_viewed_slides)"/>
                    <t t-if="not list(most_viewed_slides_list)">
                        No presentation available.
                    </t>
                    <t t-else="" t-foreach="most_viewed_slides_list" t-as="aside_slide">
                        <t t-call="website_slides.slide_aside_card"/>
                    </t>
                </ul>
            </div>
        </div>
    </div>
</template>

<!-- Slide sub-template: display an item in a list of related slides (Related, Most Viewed, ...) -->
<template id="slide_aside_card" name="Related Slide">
    <a class="list-group-item list-group-item-action d-flex align-items-start px-2" t-att-href="'/slides/slide/%s' % (slug(aside_slide))">
        <t t-set="slide_image" t-value="website.image_url(aside_slide, 'image_1024')"/>

        <div t-if="aside_slide.image_1024" class="flex-shrink-0 mr-1 border" t-attf-style="width: 20%; padding-top: 20%; background-image: url(#{slide_image}); background-size: cover; background-position:center"/>
        <div t-else="" class="o_wslides_gradient flex-shrink-0 mr-1" t-attf-style="width: 20%; padding-top: 20%;"/>
        <div class="overflow-hidden d-flex flex-column justify-content-start">
            <h6 t-esc="aside_slide.name" class="o_wslides_desc_truncate_2 mb-1" style="line-height: 1.15"/>
            <small class="text-600">
                <t t-esc="aside_slide.total_views"/> Views &#8226; <timeago class="timeago" t-att-datetime="aside_slide.create_date"></timeago>
            </small>
        </div>
    </a>
</template>

<!-- Slide: sidebar training mode -->
<template id="slide_aside_training" name="Slide: Sidebar in Training">
    <div class="o_wslides_lesson_aside_list position-relative bg-white border-bottom mt-4">
        <div class="bg-100 text-600 h6 my-0 text-decoration-none border-bottom d-flex align-items-center justify-content-between">
            <span class="p-2">Course content</span>
            <a href="#collapse_slide_aside" data-toggle="collapse" class="d-lg-none p-2 text-decoration-none o_wslides_lesson_aside_collapse">
                <i class="fa fa-chevron-down d-lg-none"/>
            </a>
        </div>
        <ul id="collapse_slide_aside" class="list-unstyled my-0 pb-3 collapse d-lg-block">
            <t t-set="i" t-value="0"/>
            <t t-if="category.get('slides')" t-foreach="category_data" t-as="category">
                <t t-call="website_slides.slide_aside_training_category">
                    <t t-set="category_slide_ids" t-value="category['slides']"/>
                </t>
            </t>
        </ul>
    </div>
</template>

<template id="slide_aside_training_category" name="Category item for the slide detailed view list">
    <t t-if="category" t-set="category" t-value="category.get('category')"/>
    <li class="o_wslides_fs_sidebar_section mt-2">
        <a t-att-href="('#collapse-%s') % (category.id if category else 0)" data-toggle="collapse" role="button" aria-expanded="true"
            class="o_wslides_lesson_aside_list_link pl-2 text-600 text-uppercase text-decoration-none py-1 small d-block"
            t-att-aria-controls="('collapse-%s') % (category.id if category else 0)">
            <t t-if="category">
                <b t-field="category.name"/>
            </t>
            <t t-else="">
                <b>Uncategorized</b>
            </t>
        </a>
        <ul class="collapse show p-0 m-0 list-unstyled" t-att-id="('collapse-%s') % (category.id if category else 0)" >
            <t t-set="is_member" t-value="slide.channel_id.is_member"/>
            <t t-set="can_access_channel" t-value="is_member or slide.channel_id.can_publish"/>
            <t t-foreach="category_slide_ids" t-as="aside_slide">
                <t t-set="slide_completed" t-value="channel_progress[aside_slide.id].get('completed')"/>
                <t t-set="can_access" t-value="can_access_channel or aside_slide.is_preview"/>
                <li class="p-0 pb-1">
                    <a t-att-href="'/slides/slide/%s' % (slug(aside_slide)) if can_access else '#'"
                        t-att-class="'o_wslides_lesson_aside_list_link d-flex align-items-top px-2 pt-1 text-decoration-none %s%s' % (('bg-100 py-1 active' if aside_slide == slide else ''), 'text-muted' if not can_access else '')">
                        <div t-if="is_member" >
                            <i t-att-id="'o_wslides_lesson_aside_slide_check_%s' % (aside_slide.id)"
                                t-att-class="'mr-1 fa fa-fw %s' % ('text-success fa-check-circle' if channel_progress[aside_slide.id].get('completed') else 'text-600 fa-circle')">
                            </i>
                        </div>
                        <div class="o_wslides_lesson_link_name text-truncate">
                            <t t-call="website_slides.slide_icon">
                                <t t-set="slide" t-value="aside_slide"/>
                            </t>
                            <span t-esc="aside_slide.name" class="mr-2"/>
                        </div>
                        <div class="ml-auto" t-if="aside_slide.question_ids">
                            <span t-att-class="'badge badge-pill %s' % ('badge-success' if channel_progress[aside_slide.id].get('completed') else 'badge-light text-600')">
                                <t t-esc="channel_progress[aside_slide.id].get('quiz_karma_won') if channel_progress[aside_slide.id].get('completed') else channel_progress[aside_slide.id].get('quiz_karma_gain')"/> xp
                            </span>
                        </div>
                    </a>
                    <ul t-if="aside_slide.link_ids or aside_slide._has_additional_resources() or aside_slide.question_ids" class="list-group px-2 mb-1 list-unstyled">
                        <t t-foreach="aside_slide.link_ids" t-as="resource">
                            <li class="pl-4">
                                <a t-if="can_access_channel" t-att-href="resource.link" target="new" class="text-decoration-none small">
                                    <i class="fa fa-link mr-1"/><span t-field="resource.name"/>
                                </a>
                                <span t-else="" class="text-decoration-none text-muted small">
                                    <i class="fa fa-link mr-1"/><span t-field="resource.name"/>
                                </span>
                            </li>
                        </t>
                        <div class="o_wslides_js_course_join pl-4" t-if="aside_slide._has_additional_resources()">
                            <t t-if="can_access_channel">
                                <li t-foreach="aside_slide.slide_resource_ids" t-as="resource">
                                    <a t-attf-href="/web/content/slide.slide.resource/#{resource.id}/data?download=true" class="text-decoration-none small">
                                        <i class="fa fa-download mr-1"/><span t-field="resource.name"/>
                                    </a>
                                </li>
                            </t>
                            <li t-elif="aside_slide.channel_id.enroll == 'public'" class="text-decoration-none small">
                                <i class="fa fa-download mr-1"/>
                                <t t-call="website_slides.join_course_link"/>
                            </li>
                        </div>
                        <li t-if="aside_slide.question_ids and aside_slide.slide_type != 'quiz'" class="pl-4">
                            <a t-if="can_access" t-att-href="'/slides/slide/%s#lessonQuiz' % (slug(aside_slide))"
                                class="o_wslides_lesson_aside_list_link text-decoration-none small text-600">
                                <i class="fa fa-flag text-warning"/> Quiz
                            </a>
                            <span t-else="" class="o_wslides_lesson_aside_list_link text-decoration-none small text-600 text-muted">
                                <i class="fa fa-flag text-warning"/> Quiz
                            </span>
                        </li>
                    </ul>
                </li>
            </t>
        </ul>
    </li>
</template>

<!-- Slide: all its content, not fullscreen mode -->
<template id="slide_content_detailed" name="Slide: Detailed Content">
    <div class="row align-items-center my-3">
        <div class="col-12 col-md order-2 order-md-1 d-flex">
            <div class="d-flex align-items-center overflow-hidden">
                <h1 class="h4 my-0 text-truncate">
                    <t t-call="website_slides.slide_icon">
                        <t t-set="icon_class" t-valuef="mr-1"/>
                    </t>
                    <span t-field="slide.name"/>
                </h1>
                <span t-if="slide.question_ids"
                    t-att-class="'ml-2 badge %s' % ('badge-success' if channel_progress[slide.id].get('completed') else 'badge-info')">
                    <span t-if="channel_progress[slide.id].get('completed')">
                        <i class="fa fa-check-circle"/>
                        <t t-esc="channel_progress[slide.id].get('quiz_karma_won', 0)"/>
                    </span>
                    <span t-else="" t-esc="channel_progress[slide.id].get('quiz_karma_gain', 0)"/>
                    <span>XP</span>
                </span>
            </div>
        </div>
        <div class="col-12 col-md order-1 order-md-2 text-nowrap flex-grow-0 d-flex justify-content-end mb-3 mb-md-0">
            <div class="btn-group flex-grow-1 flex-sm-0" role="group" aria-label="Lesson Nav">
                <a t-att-class="'btn btn-light border %s' % ('disabled' if not previous_slide else '')"
                    role="button" t-att-aria-disabled="'disabled' if not previous_slide else None"
                    t-att-href="'/slides/slide/%s' % (slug(previous_slide)) if previous_slide else '#'">
                    <i class="fa fa-chevron-left mr-2"></i> <span class="d-none d-sm-inline-block">Prev</span>
                </a>
                <t t-set="allow_done_btn" t-value="slide.slide_type in ['infographic', 'presentation', 'document', 'webpage', 'video'] and not slide.question_ids and not channel_progress[slide.id].get('completed') and slide.channel_id.is_member"/>
                <a t-att-class="'btn btn-primary border text-white %s' % ('disabled' if not allow_done_btn else '')"
                    role="button" t-att-aria-disabled="'true' if not allow_done_btn else None"
                    t-att-href="'/slides/slide/%s/set_completed?%s' % (slide.id, 'next_slide_id=%s' % (next_slide.id) if next_slide else '') if allow_done_btn else '#'">
                    Set Done
                </a>
                <a t-att-class="'btn btn-light border %s' % ('disabled' if not next_slide else '')"
                    role="button" t-att-aria-disabled="'disabled' if not next_slide else None"
                    t-att-href="'/slides/slide/%s' % (slug(next_slide)) if next_slide else '#'">
                    <span class="d-none d-sm-inline-block">Next</span> <i class="fa fa-chevron-right ml-2"></i>
                </a>
            </div>
            <a class="btn btn-light border ml-2" role="button" t-att-href="'/slides/slide/%s?fullscreen=1' % (slug(slide))">
                <i class="fa fa-desktop mr-2"/>
                <span class="d-none d-sm-inline-block">Fullscreen</span>
            </a>
        </div>
    </div>
    <div t-if="slide.tag_ids" class="pb-2">
        <t t-foreach="slide.tag_ids" t-as="tag">
            <a t-att-href="'/slides/%s/tag/%s' % (slug(slide.channel_id), slug(tag))" class="badge badge-info py-1 px-2" t-esc="tag.name"/>
        </t>
    </div>
    <div class="o_wslides_lesson_content_type">
        <img t-if="slide.slide_type == 'infographic'"
            t-att-src="website.image_url(slide, 'image_1024')" class="img-fluid" style="width:100%" t-att-alt="slide.name"/>
        <div t-if="slide.slide_type in ('presentation', 'document')" class="embed-responsive embed-responsive-4by3 embed-responsive-item mb8" style="height: 600px;">
            <t t-out="slide.embed_code"/>
        </div>
        <div t-if="slide.slide_type == 'video' and slide.document_id" class="embed-responsive embed-responsive-16by9 embed-responsive-item mb8">
            <t t-out="slide.embed_code"/>
        </div>
        <div t-if="slide.slide_type == 'webpage'" class="bg-white p-3">
            <div t-field="slide.html_content"/>
        </div>
    </div>

    <div class="mb-5">
        <ul class="nav nav-tabs o_wslides_lesson_nav" role="tablist">
            <li class="nav-item">
                <a href="#about" aria-controls="about" class="nav-link active" role="tab" data-toggle="tab">
                    <i class="fa fa-home"></i> About
                </a>
            </li>
            <li t-if="slide.channel_id.allow_comment" class="nav-item">
                <a href="#discuss" aria-controls="discuss" class="nav-link" role="tab" data-toggle="tab">
                    <i class="fa fa-comments"></i> Comments (<span t-esc="slide.comments_count"/>)
                </a>
            </li>
            <li class="nav-item">
                <a href="#statistic" aria-controls="statistic" class="nav-link" role="tab" data-toggle="tab">
                    <i class="fa fa-bar-chart"></i> Statistics
                </a>
            </li>
            <li class="nav-item">
                <a href="#share" aria-controls="share" class="nav-link" role="tab" data-toggle="tab">
                    <i class="fa fa-share-alt"></i> Share
                </a>
            </li>
        </ul>
        <div class="tab-content mt-3">
            <div role="tabpanel" t-att-class="not comments and 'tab-pane fade in show active' or 'tab-pane fade'" id="about">
                <div t-field="slide.description"/>
            </div>
            <div role="tabpanel" t-att-class="comments and 'tab-pane fade in show active' or 'tab-pane fade'" id="discuss">
                <t t-call="portal.message_thread">
                    <t t-set="object" t-value="slide"/>
                    <t t-set="disable_composer" t-value="not (slide.channel_id.can_comment and slide.channel_id.allow_comment and slide.channel_id.channel_type == 'training')"/>
                    <t t-set="display_rating" t-value="False"/>
                </t>
            </div>
            <div role="tabpanel" class="tab-pane fade" id="statistic" t-att-slide-url="slide.website_url">
                <div class="row">
                    <div class="col-12 col-md">
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <th colspan="2" class="border-top-0">Views</th>
                                </tr>
                                <tr class="border-top-0">
                                    <th class="border-top-0"><span t-esc="slide.total_views"/></th>
                                    <td class="border-top-0 w-100">Total Views</td>
                                </tr>
                                <tr>
                                    <th><span t-esc="slide.slide_views"/></th>
                                    <td>Members Views</td>
                                </tr>
                                <tr>
                                    <th><span t-esc="slide.public_views"/></th>
                                    <td>Public Views</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div t-if="slide.channel_id.allow_comment" class="col-12 col-md">
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <th colspan="2" class="border-top-0">Actions</th>
                                </tr>
                                <tr class="border-top-0">
                                    <th class="border-top-0"><span t-esc="slide.likes"/></th>
                                    <td class="border-top-0 w-100">Likes</td>
                                </tr>
                                <tr>
                                    <th><span t-esc="slide.dislikes"/></th>
                                    <td>Dislikes</td>
                                </tr>
                                <tr>
                                    <th><span t-esc="len(slide.website_message_ids)"/></th>
                                    <td>Comments</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div role="tabpanel" class="tab-pane fade" t-if="slide.website_published" id="share">
                <h4 t-if="not slide.website_published"><i class="fa fa-info-circle"></i>
                    The social sharing module will be unlocked when a moderator will allow your publication.
                </h4>
                <t t-if="slide.website_published">
                    <div class="row">
                        <div class="col-12 col-lg-6">
                            <h5 class="mt16">Share on Social Networks</h5>
                            <t t-call="website_slides.slide_share_social">
                                <t t-set="record" t-value="slide"/>
                            </t>
                        </div>
                        <div class="col-12 col-lg-6">
                            <t t-call="website_slides.slide_share_link">
                                <t t-set="record" t-value="slide"/>
                                <t t-set="website_url" t-value="slide.website_url"/>
                            </t>
                        </div>
                    </div>
                    <div class="row">
                        <div t-attf-class="col-12 #{'col-lg-6' if slide.embed_code else ''}">
                            <t t-call="website_slides.slide_social_email">
                                <t t-set="slide" t-value="slide"/>
                            </t>
                        </div>
                        <div class="col-12 col-lg-6" t-if="slide.embed_code">
                            <t t-call="website_slides.slide_social_embed">
                                <t t-set="slide" t-value="slide"/>
                            </t>
                        </div>
                    </div>
                </t>
            </div>
        </div>
    </div>
    <div class="o_wslides_js_quiz_container" t-att-data-slide-id="slide.id">
        <div class="row" t-if="slide.slide_type != 'certification'">
            <t t-if="slide.question_ids">
                <t t-call="website_slides.lesson_content_quiz"/>
            </t>
            <div t-else="" class="o_wslides_js_lesson_quiz col" t-att-data-id="slide.id">
                <t t-if="slide.channel_id.can_upload" t-call="website_slides.lesson_content_quiz_add_buttons"/>
            </div>
        </div>
    </div>
    <div class="row mt-3 mb-3">
        <div class="col-12 col-md d-flex align-items-start mb-4 mb-md-0" t-if="len(slide.link_ids)">
            <span t-if="slide.link_ids" class="text-muted font-weight-bold mr-3">External sources</span>
            <div class="text-muted mr-auto border-left pl-3">
                <t t-foreach="slide.link_ids" t-as="link">
                    <a t-att-href="link.link" t-esc="link.name"/><br />
                </t>
            </div>
        </div>
        <div class="col-12 col-md d-flex align-items-start mb-4 mb-md-0 o_wslides_js_course_join" t-if="slide._has_additional_resources()">
            <span t-if="slide.channel_id.is_member or slide.channel_id.can_publish or slide.is_preview or slide.channel_id.enroll in ['private', 'payment']" class="text-muted font-weight-bold mr-3">
                Additional Resources
            </span>
            <div t-if="slide.channel_id.is_member or slide.channel_id.can_publish" class="text-muted mr-auto border-left pl-3">
                <t t-foreach="slide.slide_resource_ids" t-as="resource">
                    <a t-attf-href="/web/content/slide.slide.resource/#{resource.id}/data?download=true" t-esc="resource.name"/><br />
                </t>
            </div>
            <div t-elif="slide.channel_id.enroll == 'public'" class="text-muted mr-auto border-left pl-3">
                <t t-call="website_slides.join_course_link"/>
            </div>
        </div>
        <div t-if="slide.channel_id.allow_comment and slide.channel_id.channel_type == 'documentation'"
             class="col-12 col-md d-flex align-items-start justify-content-md-end mb-2 mb-md-0">
            <span class="text-muted font-weight-bold mr-3">Rating</span>
            <div class="text-muted border-left pl-3">
                <div class="o_wslides_js_slide_like mr-2">
                    <span t-att-class="('o_wslides_js_slide_like_up %s') % ('disabled' if not slide.channel_id.can_vote else '')" tabindex="0" data-toggle="popover" t-att-data-slide-id="slide.id">
                        <i class="fa fa-thumbs-up fa-1x" role="img" aria-label="Likes" title="Likes"></i>
                        <span t-esc="slide.likes"/>
                    </span>
                    <span t-att-class="('o_wslides_js_slide_like_down ml-3 %s') % ('disabled' if not slide.channel_id.can_vote else '')" tabindex="0" data-toggle="popover" t-att-data-slide-id="slide.id">
                        <i class="fa fa-thumbs-down fa-1x" role="img" aria-label="Dislikes" title="Dislikes"></i>
                        <span t-esc="slide.dislikes"/>
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<!-- Slide sub-tempalte: render a quiz serverside. Should be sync with JS qweb template "slide.slide.quiz" -->
<template id="lesson_content_quiz" name="Lesson: Quiz specific content">
    <t t-set="slide_completed" t-value="channel_progress[slide.id].get('completed')"/>
    <div class="o_wslides_js_lesson_quiz col" id="lessonQuiz"
        t-att-data-id="slide.id"
        t-att-data-name="slide.name"
        t-att-data-slide-type="slide.slide_type"
        t-att-data-is-member="slide.channel_id.is_member"
        t-att-data-completed="1 if slide_completed else 0"
        t-att-data-quiz-attempts-count="quiz_attempts_count"
        t-att-data-quiz-karma-max="quiz_karma_max"
        t-att-data-quiz-karma-gain="quiz_karma_gain"
        t-att-data-quiz-karma-won="quiz_karma_won"
        t-att-data-has-next="1 if next_slide else 0"
        t-att-data-next-slide-url="'/slides/slide/%s' % (slug(next_slide)) if next_slide else None"
        t-att-data-channel-id="slide.channel_id.id"
        t-att-data-channel-enroll="slide.channel_id.enroll"
        t-att-data-channel-requested-access="slide.channel_id.has_requested_access"
        t-att-data-channel-can-upload="slide.channel_id.can_upload"
        t-att-data-signup-allowed="signup_allowed"
        t-att-data-session-answers="session_answers">
        <t t-foreach="slide_questions" t-as="question">
            <t t-call="website_slides.lesson_content_quiz_question"/>
        </t>
        <t t-if="slide.channel_id.can_upload" t-call="website_slides.lesson_content_quiz_add_buttons"/>
        <div class="o_wslides_js_lesson_quiz_validation pt-3"/>
    </div>
</template>

<template id="lesson_content_quiz_question" name="Lesson: Quiz question template">
    <div t-att-class="'o_wslides_js_lesson_quiz_question mt-3 %s' % ('completed-disabled' if slide_completed else ('disabled' if not (slide.channel_id.is_member or slide.is_preview) else ''))"
         t-att-data-question-id="question['id']" t-att-data-title="question['question']" >
        <div class="row d-flex mb-2 mx-0">
            <div class="h4">
                <small class="text-muted">
                    <i class="o_wslides_js_quiz_icon o_wslides_js_quiz_sequence_handler fa fa-bars mr-1 text-muted" t-if="slide.channel_id.can_upload and not slide_completed" />
                    <t t-if="question_index != NoneType"><span class="o_wslides_quiz_question_sequence" t-esc="question_index+1"/>.</t>
                    <t t-else=""><span class="o_wslides_quiz_question_sequence" t-esc="question['sequence']"/>.</t>
                </small>
                <span t-esc="question['question']"/>
            </div>
            <div class="ml-auto o_wslides_js_quiz_edit_del" t-if="slide.channel_id.can_upload and not slide_completed" >
                <i class="o_wslides_js_quiz_icon o_wslides_js_quiz_edit_question fa fa-pencil-square-o p-1 text-muted"></i>
                <i class="o_wslides_js_quiz_icon o_wslides_js_quiz_delete_question fa fa-trash p-1 text-muted"></i>
            </div>
        </div>
        <div class="list-group">
            <t t-foreach="question['answer_ids']" t-as="answer">
                <a t-att-data-answer-id="answer['id']" href="#"
                    t-att-data-text="answer['text_value']" t-att-data-is-correct="answer['is_correct']" t-att-data-comment="answer['comment']"
                    t-att-class="'o_wslides_quiz_answer list-group-item list-group-item-action d-flex align-items-center %s' % ('list-group-item-success' if slide_completed and answer['is_correct'] else '')">
                    <label class="my-0 d-flex align-items-center justify-content-center mr-2">
                        <input type="radio"
                            t-att-name="question['id']"
                            t-att-value="answer['id']"
                            class="d-none"
                            t-att-disabled="True if not slide.channel_id.is_member or slide_completed else ''"/>
                        <i t-att-class="'fa fa-circle text-400 %s' % ('d-none' if slide_completed and answer['is_correct'] else '')"/>
                        <i class="fa fa-times-circle text-danger d-none"></i>
                        <i t-att-class="'fa fa-check-circle text-success %s' % ('d-none' if not (slide_completed and answer['is_correct']) else '')"></i>
                    </label>
                    <span t-esc="answer['text_value']"/>
                </a>
            </t>
            <div class="o_wslides_quiz_answer_info list-group-item list-group-item-info d-none">
                <i class="fa fa-info-circle"/>
                <span class="o_wslides_quiz_answer_comment"/>
            </div>
        </div>
    </div>
</template>

<template id="lesson_content_quiz_add_buttons" name="Lesson: Quiz Add Buttons template">
    <div class="o_wslides_js_lesson_quiz_new_question row mt-3">
        <a t-attf-class="o_wslides_js_quiz_add o_wslides_js_quiz_add_quiz btn btn-light border ml-3 #{'d-none ' if slide.question_ids else ''}" role="button">
            <i class="fa fa-plus mr-2"/>
            <span>Add Quiz</span>
        </a>
        <a t-attf-class="o_wslides_js_quiz_add o_wslides_js_quiz_add_question btn btn-light border ml-3 #{'' if slide.question_ids else 'd-none '}" role="button">
            <i class="fa fa-plus mr-2"/>
            <span>Add Question</span>
        </a>
    </div>
</template>

<!-- Slide sub-template: share: send by email -->
<template id='slide_social_email' name="Share by Email">
    <h5 class="mt-4">Share by mail</h5>
    <div t-if="not is_public_user" class="form-inline">
        <form class="form-group oe_slide_js_share_email" role="form">
            <div class="input-group">
                <input type="email" class="form-control" placeholder="<EMAIL>"/>
                <span class="input-group-append">
                    <button class="btn btn-primary" type="button"
                        data-loading-text="Sending..."
                        t-attf-data-slide-id="#{slide.id}"
                        style="border-top-right-radius: 4px;border-bottom-right-radius: 4px;">
                        <i class="fa fa-envelope"/> Send Email
                    </button>
                </span>
            </div>
            <span class="form-text text-muted d-block w-100">Send presentation through email</span>
        </form>
    </div>
    <div t-if="is_public_user" class="alert alert-info d-inline-block">
        <p class="mb-0">Please <a t-attf-href="/web?redirect=#{request.httprequest.url}" class="font-weight-bold"> login </a> to share this <t t-esc="slide.slide_type"/> by email.</p>
    </div>
</template>

<!-- Slide sub-template: share: embed in your website -->
<template id="slide_social_embed" name="Share on Your Website">
    <div class="oe_slide_js_embed_code_widget mt-4">
        <h5 class="mt0">Embed in your website</h5>
        <div class="form-group">
            <textarea class="form-control slide_embed_code" readonly="readonly" onClick="this.select();"><t t-esc="slide.embed_code"/></textarea>
        </div>
        <div class="form-group d-flex" t-if="slide.slide_type in ('presentation', 'document')">
            <div class="form-text p-0 col-xs-5 col-sm-5 col-md-5 col-lg-5">Select page to start with</div>
            <div class="input-group col-xs-3 col-sm-2 col-md-2 col-lg-3">
                <input type="number" value="1" class="form-control"/>
            </div>
        </div>
    </div>
</template>

</data></odoo>
