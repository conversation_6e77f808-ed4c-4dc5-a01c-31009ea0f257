# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_expiry
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
msgid ""
"<span class=\"badge badge-danger\" attrs=\"{'invisible': "
"[('product_expiry_alert', '=', False)]}\">Expiration Alert</span>"
msgstr ""
"<span class=\"badge badge-danger\" attrs=\"{'invisible': "
"[('product_expiry_alert', '=', False)]}\">Alerta de caducidad</span>"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "<span> days</span>"
msgstr "<span> días</span>"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__alert_date
msgid "Alert Date"
msgstr "Fecha de alerta"

#. module: product_expiry
#: model:mail.activity.type,name:product_expiry.mail_activity_type_alert_date_reached
msgid "Alert Date Reached"
msgstr "Fecha de alerta alcanzada"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__alert_time
msgid "Alert Time"
msgstr "Periodo de alerta"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__use_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__use_time
msgid "Best Before Time"
msgstr "Consumir preferentemente antes de"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__use_date
msgid "Best before Date"
msgstr "Consumir preferentemente antes de"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.report_lot_label_expiry
msgid "Best before:"
msgstr "Consumir preferentemente antes de"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Confirm"
msgstr "Confirmar"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_expiry_picking_confirmation
msgid "Confirm Expiry"
msgstr "Confirmar caducidad"

#. module: product_expiry
#: code:addons/product_expiry/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
#, python-format
msgid "Confirmation"
msgstr "Confirmación"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__create_date
msgid "Created on"
msgstr "Creado el"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__alert_date
msgid ""
"Date to determine the expired lots and serial numbers using the filter "
"\"Expiration Alerts\"."
msgstr ""
"Fecha que determina la caducidad de los lotes y los números de serie al "
"utilizar el filtro \"Alertas de caducidad\"."

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "Dates"
msgstr "Fechas"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__description
msgid "Description"
msgstr "Descripción"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Discard"
msgstr "Descartar"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_res_config_settings__group_expiry_date_on_delivery_slip
msgid "Display Expiration Dates on Delivery Slips"
msgstr "Mostrar fecha de caducidad en las notas de remisión"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.quant_search_view_inherit_product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.search_product_lot_filter_inherit_product_expiry
msgid "Expiration Alerts"
msgstr "Alertas de caducidad"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_product_template__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_move_line__expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__use_expiration_date
#: model_terms:ir.ui.view,arch_db:product_expiry.stock_report_delivery_document_inherit_product_expiry
msgid "Expiration Date"
msgstr "Fecha de caducidad"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__expiration_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__expiration_time
msgid "Expiration Time"
msgstr "Fecha de caducidad"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.res_config_settings_view_form_stock
msgid "Expiration dates will appear on the delivery slip"
msgstr "Se mostrarán las fechas de caducidad en la nota de remisión"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Expired Lot(s)"
msgstr "Lotes caducados"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__product_expiry_reminded
msgid "Expiry has been reminded"
msgstr "Se recordó la fecha de caducidad"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__id
msgid "ID"
msgstr "ID"

#. module: product_expiry
#: model:res.groups,name:product_expiry.group_expiry_date_on_delivery_slip
msgid "Include expiration dates on delivery slip"
msgstr "Incluir las fechas de caducidad en la nota de remisión"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__lot_ids
msgid "Lot"
msgstr "Lote"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Lote/Número de serie"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__expiration_time
#: model:ir.model.fields,help:product_expiry.field_product_template__expiration_time
msgid ""
"Number of days after the receipt of the products (from the vendor or in "
"stock after production) after which the goods may become dangerous and must "
"not be consumed. It will be computed on the lot/serial number."
msgstr ""
"Número de días después de la recepción de los productos (del proveedor o de "
"las existencias después de la producción) después de los cuales los bienes "
"pueden representar un riesgo y no se deben consumir. Se registrará sobre el "
"número de lote/serie."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,help:product_expiry.field_product_template__alert_time
msgid ""
"Number of days before the Expiration Date after which an alert should be "
"raised on the lot/serial number. It will be computed on the lot/serial "
"number."
msgstr ""
"Número de días antes de la fecha de caducidad después de los cuales se debe "
"emitir una alarma para el número de lote/serie. Se registrará sobre el "
"número de lote/serie."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,help:product_expiry.field_product_template__removal_time
msgid ""
"Number of days before the Expiration Date after which the goods should be "
"removed from the stock. It will be computed on the lot/serial number."
msgstr ""
"Número de días antes de la fecha de caducidad después de los cuales se deben"
" retirar los bienes de las existencias. Se registrará sobre el número de "
"lote/serie."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__use_time
#: model:ir.model.fields,help:product_expiry.field_product_template__use_time
msgid ""
"Number of days before the Expiration Date after which the goods starts "
"deteriorating, without being dangerous yet. It will be computed on the "
"lot/serial number."
msgstr ""
"Número de días antes de la fecha de caducidad después de los cuales los "
"bienes se empiezan a deteriorar sin llegar a representar un peligro. Se "
"registrará sobre el número de lote/serie."

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__picking_ids
msgid "Picking"
msgstr "Recolección"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Proceed except for expired one"
msgstr "Continuar excepto por el que está caducado."

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_procurement_group
msgid "Procurement Group"
msgstr "Grupo de aprovisionamiento"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_product
msgid "Product"
msgstr "Producto"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__product_expiry_alert
msgid "Product Expiry Alert"
msgstr "Alerta de caducidad del producto"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Movimientos de producto (Línea de movimiento de existencias)"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_quant
msgid "Quants"
msgstr "Quants"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__removal_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__removal_date
msgid "Removal Date"
msgstr "Fecha de remoción"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__removal_time
msgid "Removal Time"
msgstr "Tiempo de remoción"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__show_lots
msgid "Show Lots"
msgstr "Mostrar lotes"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_move
msgid "Stock Move"
msgstr "Movimiento de existencias"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__product_expiry_alert
msgid "The Expiration Date has been reached."
msgstr "Se llegó a la fecha de caducidad."

#. module: product_expiry
#: code:addons/product_expiry/models/production_lot.py:0
#, python-format
msgid "The alert date has been reached for this lot/serial number"
msgstr "Se llegó a la fecha de alerta para este número de lote/serie."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_move_line__expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__expiration_date
msgid ""
"This is the date on which the goods with this Serial Number may become "
"dangerous and must not be consumed."
msgstr ""
"Esta fecha indica que los bienes con este número de serie pueden representar"
" un riesgo y no se deben consumir."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__removal_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__removal_date
msgid ""
"This is the date on which the goods with this Serial Number should be "
"removed from the stock. This date will be used in FEFO removal strategy."
msgstr ""
"Esta fecha indica que los bienes con este número de serie deben retirarse de"
" las existencias. Se utilizará esta fecha para la estrategia de remoción en "
"la FEFO."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__use_date
msgid ""
"This is the date on which the goods with this Serial Number start "
"deteriorating, without being dangerous yet."
msgstr ""
"Esta fecha indica que los bienes con este número de serie empiezan a "
"deteriorarse, sin llegar a representar un peligro."

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_picking
msgid "Transfer"
msgstr "Traslado"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_move__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__use_expiration_date
msgid "Use Expiration Date"
msgstr "Usar fecha de caducidad"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.report_lot_label_expiry
msgid "Use by:"
msgstr "Consumir antes de"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_product_template__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_move__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__use_expiration_date
msgid ""
"When this box is ticked, you have the possibility to specify dates to manage"
" product expiration, on the product and on the corresponding lot/serial "
"numbers"
msgstr ""
"Cuando se selecciona esta casilla puede especificar fechas para administrar "
"la caducidad de un producto, ya sea en el producto o en los números de "
"lote/serie."

#. module: product_expiry
#: code:addons/product_expiry/wizard/confirm_expiry.py:0
#, python-format
msgid ""
"You are going to deliver some product expired lots.\n"
"Do you confirm you want to proceed ?"
msgstr ""
"Se van a entregar algunos productos con lotes caducados.\n"
"¿Confirma la acción?"

#. module: product_expiry
#: code:addons/product_expiry/wizard/confirm_expiry.py:0
#, python-format
msgid ""
"You are going to deliver the product %(product_name)s, %(lot_name)s which is expired.\n"
"Do you confirm you want to proceed ?"
msgstr ""
"Se va a entregar el producto %(product_name)s.%(lot_name)s que está caducado.\n"
"¿Confirma la acción?"
