# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Create Invoice"
msgstr "إنشاء فاتورة"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "الإنشاء عند الطلب "

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__visible_project
msgid "Display project"
msgstr "عرض المشروع "

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__project_id
msgid "Generated Project"
msgstr "المشروع المُنشأ "

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__task_id
msgid "Generated Task"
msgstr "المهمة المُنشأة "

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_to_invoice
msgid "Has SO to Invoice"
msgstr "يحتوي على أمر بيع لفوترته "

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice as soon as this service is sold, and create a task in an existing "
"project to track the time spent."
msgstr ""
"قم بالفوترة بمجرد أن يتم بيع الخدمة، ثم قم بإنشاء مهمة في مشروع موجود بالفعل"
" لتتبع الوقت المقضي. "

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold, and create a "
"project for the order with a task for each sales order line to track the "
"time spent."
msgstr ""
"قم بفوترة الكميات المطلوبة بمجرد أن يتم بيع الخدمة، ثم قم بإنشاء مشروع للأمر"
" مع مهمة لكل بند أمر بيع لتتبع الوقت المقضي. "

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold, and create an "
"empty project for the order to track the time spent."
msgstr ""
"قم بفوترة الكميات المطلوبة بمجرد أن يتم بيع الخدمة، ثم قم بإنشاء مشروع للأمر"
" لتتبع الوقت المقضي. "

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr "قم بفوترة الكميات المطلوبة بمجرد أن يتم بيع الخدمة. "

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__is_service
msgid "Is a Service"
msgstr "خدمة"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid "New"
msgstr "جديد"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "None"
msgstr "لا شيء"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "لا شيء "

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_count
msgid "Number of Projects"
msgstr "عدد المشاريع "

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__invoice_count
msgid "Number of invoices"
msgstr "عدد الفواتير "

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_project.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""
"في تأكيد أمر البيع، بإمكان هذا المنتج إنشاء مشروع و/أو مهمة.         من خلالها، بإمكانك تتبع الخدمات التي تبيعها.\n"
"         'في مشروع أمر البيع': سوف يستخدم المشروع المهيأ لأمر البيع إذا كان محدداً أو الاستعانة مجدداً بإنشاء         مشروع جديد بناءً على القالب المحدد. "

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product"
msgstr "المنتج"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product Template"
msgstr "قالب المنتج"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "المشروع"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "المشروع والمهمة "

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "قالب المشروع"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__project_id
msgid "Project generated by the sales order item"
msgstr "المشروع الناشئ عن عنصر أمر المبيعات"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "أمر البيع الخاص بالمشروع "

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_ids
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Projects"
msgstr "المشاريع "

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr "المشاريع المستخدمة في أمر المبيعات هذا. "

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
msgid "Sale Order"
msgstr "أمر البيع"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#: code:addons/sale_project/models/project.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order"
msgstr "أمر البيع"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
msgid "Sales Order Id"
msgstr "مُعرّف أمر البيع "

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order Item"
msgstr "عنصر أمر المبيعات"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added, in "
"order to be invoiced to your customer."
msgstr ""
"عنصر أمر البيع الذي سوف تتم إضافة الوقت المقضي في هذه المهمة إليه، حتى تتم "
"فوترته لعميلك. "

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "بند أمر المبيعات"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__is_service
msgid ""
"Sales Order item should generate a task and/or a project, depending on the "
"product settings."
msgstr "يجب أن ينتج عنصر أمر المبيعات مهمة و/أو مشروع، حسب إعدادات المنتج."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item to which the project is linked. Link the timesheet entry to"
" the sales order item defined on the project. Only applies on tasks without "
"sale order item defined, and if the employee is not in the 'Employee/Sales "
"Order Item Mapping' of the project."
msgstr ""
"عنصر أمر المبيعات الذي يرتبط به المشروع. قم بربط قيد الجداول الزمنية بعنصر "
"أمر المبيعات المحدد في المشروع. يُطبّق فقط على المهام التي لا تحتوي على عنصر"
" أمر البيع، وإذا لم يكن الموظف في 'تخطيط الموظف/عنصر أمر المبيعات' للمشروع. "

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,help:sale_project.field_project_task__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "أمر البيع الذي يرتبط به هذا المشروع. "

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "أمر البيع الذي ترتبط به هذه المهمة. "

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "البحث في الفاتورة"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "البحث في أمر البيع "

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order Item"
msgstr "البحث في عنصر أمر البيع "

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__project_id
#: model:ir.model.fields,help:sale_project.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr ""
"قم بتحديد مشروع قابل للفوترة يمكن إنشاء المهام فيه. يجب تعيين هذه الإعدادات "
"لكل شركة. "

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,help:sale_project.field_product_template__project_template_id
msgid ""
"Select a billable project to be the skeleton of the new created project when"
" selling the current product. Its stages and tasks will be duplicated."
msgstr ""
"قم بتحديد مشروع قابل للفوترة ليكون هيكل المشروع الجديد الذي تم إنشاؤه عند "
"بيع المنتج الحالي. سوف يتم استنساخ مراحله ومهامه. "

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr "قم بتحديد مشروع غير قابل للفوترة يمكن إنشاء المهام عليه. "

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "المهمة"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"Task Created (%s): <a href=# data-oe-model=project.task data-oe-id=%d>%s</a>"
msgstr ""
"تم إنشاء المهمة (%s): <a href=# data-oe-model=project.task data-oe-"
"id=%d>%s</a>"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "تكرار المهمة "

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__task_id
msgid "Task generated by the sales order item"
msgstr "المهمة الناشئة عن عنصر أمر المبيعات"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Tasks"
msgstr "المهام"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "تحليل المهام"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr "المهام المرتبطة بهذا البيع "

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr "يجب ألا يكون للمنتج %s مشروع عام بما أنه سوف يقوم بإنشاء مشروع. "

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""
"يجب ألا يكون للمنتج %s مشروع أو قالب مشروع بما أنه لن يقوم بإنشاء مشروع. "

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr ""
"يجب ألا يكون للمنتج %s قالب مشروع بما أنه سوف يقوم بإنشاء مهمة في مشروع عام."
" "

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "There is nothing to invoice in this project."
msgstr "لا يوجد ما يمكن فوترته في هذا المشروع. "

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"This task has been created from: <a href=# data-oe-model=sale.order data-oe-"
"id=%d>%s</a> (%s)"
msgstr ""
"تم إنشاء هذه المهمة من: <a href=# data-oe-model=sale.order data-oe-"
"id=%d>%s</a> (%s) "

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "بانتظار الفوترة"

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr ""
"لا يمكنك ربط عنصر الطلب %(order_id)s - %(product_id)s بهذه المهمة لأنها نفقة"
" تمت إعادة فوترتها. "

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You have to unlink the task from the sale order item in order to delete it."
msgstr "عليك إلغاء ربط المهمة من عنصر أمر البيع حتى تتمكن من حذفها. "

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "for sale order item:"
msgstr "لعنصر أمر البيع: "

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "for sale order:"
msgstr "لأمر البيع: "
