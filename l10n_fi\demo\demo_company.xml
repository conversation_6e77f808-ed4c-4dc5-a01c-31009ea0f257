<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_fi" model="res.partner">
        <field name="name">FI Company</field>
        <field name="vat">FI 20774740</field>
        <field name="street">Jomalaby väg</field>
        <field name="city">Björsby</field>
        <field name="country_id" ref="base.fi"/>
        <field name="state_id" ref="base.state_fi_01"/>
        <field name="zip">22150</field>
        <field name="phone">+358 9 123 456</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.fi.example.com</field>
    </record>

    <record id="demo_company_fi" model="res.company">
        <field name="name">FI Company</field>
        <field name="partner_id" ref="partner_demo_company_fi"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_fi')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_fi.demo_company_fi'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_fi.fi_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_fi.demo_company_fi')"/>
    </function>
</odoo>
