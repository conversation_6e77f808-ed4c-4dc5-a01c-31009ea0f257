# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * link_tracker
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Chile) (https://www.transifex.com/odoo/teams/41243/es_CL/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_CL\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_link_click_ids
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_form
msgid "Clicks"
msgstr ""

#. module: link_tracker
#: sql_constraint:link.tracker.code:0
msgid "Code must be unique."
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_link_code_ids
msgid "Codes"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click_country_id
msgid "Country"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click_click_date
msgid "Create Date"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click_create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code_create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click_create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code_create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_create_date
msgid "Created on"
msgstr "Creado en"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click_display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code_display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_favicon
msgid "Favicon"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_icon_src
msgid "Favicon Source"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_short_url_host
msgid "Host of the short URL"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click_id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code_id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_id
msgid "ID"
msgstr "ID (identificación)"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click_ip
msgid "Internet Protocol"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker___last_update
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click___last_update
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click_write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code_write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click_write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code_write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click_link_id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code_link_id
msgid "Link"
msgstr ""

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.action_link_tracker
#: model:ir.ui.menu,name:link_tracker.menu_url_shortener_main
msgid "Link Tracker"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_count
msgid "Number of Clicks"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_title
msgid "Page Title"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_redirected_url
msgid "Redirected URL"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code_code
msgid "Short URL Code"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code
msgid "Short URL code"
msgstr ""

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.action_view_click_statistics
msgid "Statistics of Clicks"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_url
msgid "Target URL"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_filter
msgid "Title and URL"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_filter
msgid "Tracked Link"
msgstr ""

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_short_url
msgid "Tracked URL"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_form
msgid "Visit Page"
msgstr ""

#. module: link_tracker
#: code:addons/link_tracker/models/link_tracker.py:141
#, python-format
msgid "Visit Webpage"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_form
msgid "Website Link"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_click_form
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_click_graph
msgid "Website Link Clicks"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_form_stats
msgid "Website Link Graph"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_graph
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_tree
msgid "Website Links"
msgstr ""

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.view_link_tracker_click_tree
msgid "Website Links Clicks"
msgstr ""

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker
msgid "link.tracker"
msgstr ""

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_click
msgid "link.tracker.click"
msgstr ""

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_code
msgid "link.tracker.code"
msgstr ""

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.action_link_tracker_stats
msgid "link.tracker.form.graph.action"
msgstr ""
