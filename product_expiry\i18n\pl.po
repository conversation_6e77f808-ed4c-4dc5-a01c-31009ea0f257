# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_expiry
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.war<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON> <judyta.kaz<PERSON><PERSON><PERSON>@openglobe.pl>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON>eusz <PERSON>rpi<PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
msgid ""
"<span class=\"badge badge-danger\" attrs=\"{'invisible': "
"[('product_expiry_alert', '=', False)]}\">Expiration Alert</span>"
msgstr ""
"<span class=\"badge badge-danger\" attrs=\"{'invisible': "
"[('product_expiry_alert', '=', False)]}\">Alert o wygaśnięciu</span>"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "<span> days</span>"
msgstr "<span> dni</span>"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__alert_date
msgid "Alert Date"
msgstr "Data ostrzegawcza"

#. module: product_expiry
#: model:mail.activity.type,name:product_expiry.mail_activity_type_alert_date_reached
msgid "Alert Date Reached"
msgstr "Osiągnięta data alarmu"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__alert_time
msgid "Alert Time"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__use_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__use_time
msgid "Best Before Time"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__use_date
msgid "Best before Date"
msgstr "Data najlepszego użycia"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.report_lot_label_expiry
msgid "Best before:"
msgstr "\"Najlepiej przed\""

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Confirm"
msgstr "Potwierdź"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_expiry_picking_confirmation
msgid "Confirm Expiry"
msgstr "Potwierdź wygaśnięcie"

#. module: product_expiry
#: code:addons/product_expiry/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
#, python-format
msgid "Confirmation"
msgstr "Potwierdzenie"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__alert_date
msgid ""
"Date to determine the expired lots and serial numbers using the filter "
"\"Expiration Alerts\"."
msgstr ""
"Data, aby określić wygasłe partie i numery seryjne za pomocą filtra „Alerty "
"o wygaśnięciu”."

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "Dates"
msgstr "Daty"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__description
msgid "Description"
msgstr "Opis"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Discard"
msgstr "Odrzuć"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_res_config_settings__group_expiry_date_on_delivery_slip
msgid "Display Expiration Dates on Delivery Slips"
msgstr "Wyświetlanie dat ważności na dowodach dostawy"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.quant_search_view_inherit_product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.search_product_lot_filter_inherit_product_expiry
msgid "Expiration Alerts"
msgstr "Alerty o wygaśnięciu"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_product_template__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_move_line__expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__use_expiration_date
#: model_terms:ir.ui.view,arch_db:product_expiry.stock_report_delivery_document_inherit_product_expiry
msgid "Expiration Date"
msgstr "Data ważności"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__expiration_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__expiration_time
msgid "Expiration Time"
msgstr ""

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.res_config_settings_view_form_stock
msgid "Expiration dates will appear on the delivery slip"
msgstr "Daty ważności pojawią się na dowodzie dostawy"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Expired Lot(s)"
msgstr "Partia(-e), których ważność wygasła"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__product_expiry_reminded
msgid "Expiry has been reminded"
msgstr "Przypomniano o wygaśnięciu"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__id
msgid "ID"
msgstr "ID"

#. module: product_expiry
#: model:res.groups,name:product_expiry.group_expiry_date_on_delivery_slip
msgid "Include expiration dates on delivery slip"
msgstr "Uwzględnienie daty ważności na dowodzie dostawy"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__lot_ids
msgid "Lot"
msgstr "Partia"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Partia/Numer seryjny"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__expiration_time
#: model:ir.model.fields,help:product_expiry.field_product_template__expiration_time
msgid ""
"Number of days after the receipt of the products (from the vendor or in "
"stock after production) after which the goods may become dangerous and must "
"not be consumed. It will be computed on the lot/serial number."
msgstr ""
"Liczba dni po otrzymaniu produktów (od sprzedawcy lub w magazynie po "
"produkcji), po których towary mogą stać się niebezpieczne i nie mogą być "
"spożywane. Będzie ona obliczana na podstawie numeru partii/serii."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,help:product_expiry.field_product_template__alert_time
msgid ""
"Number of days before the Expiration Date after which an alert should be "
"raised on the lot/serial number. It will be computed on the lot/serial "
"number."
msgstr ""
"Liczba dni przed datą wygaśnięcia, po której należy zgłosić alert dotyczący "
"partii/numeru seryjnego. Będzie ona obliczana na podstawie numeru "
"partii/serii."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,help:product_expiry.field_product_template__removal_time
msgid ""
"Number of days before the Expiration Date after which the goods should be "
"removed from the stock. It will be computed on the lot/serial number."
msgstr ""
"Liczba dni przed datą ważności, po której towary powinny zostać usunięte z "
"magazynu. Będzie ona obliczana na podstawie numeru partii/serii."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__use_time
#: model:ir.model.fields,help:product_expiry.field_product_template__use_time
msgid ""
"Number of days before the Expiration Date after which the goods starts "
"deteriorating, without being dangerous yet. It will be computed on the "
"lot/serial number."
msgstr ""
"Liczba dni przed datą wygaśnięcia, po której towary zaczynają się psuć, ale "
"nie są jeszcze niebezpieczne. Będzie ona obliczana na podstawie numeru "
"partii/serii."

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__picking_ids
msgid "Picking"
msgstr "Pobranie"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Proceed except for expired one"
msgstr "Kontynuuj z wyjątkiem wygasłego"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_procurement_group
msgid "Procurement Group"
msgstr "Grupa zapotrzebowań"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_product
msgid "Product"
msgstr "Produkt"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__product_expiry_alert
msgid "Product Expiry Alert"
msgstr "Alert o wygaśnięciu produktu"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Przesunięcia produktu (pozycja przesunięcia zasobów)"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_template
msgid "Product Template"
msgstr "Szablon produktu"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_quant
msgid "Quants"
msgstr "Kwanty"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__removal_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__removal_date
msgid "Removal Date"
msgstr "Data usunięcia"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__removal_time
msgid "Removal Time"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__show_lots
msgid "Show Lots"
msgstr "Pokaż partie"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_move
msgid "Stock Move"
msgstr "Przesunięcie"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__product_expiry_alert
msgid "The Expiration Date has been reached."
msgstr "Data wygaśnięcia została osiągnięta"

#. module: product_expiry
#: code:addons/product_expiry/models/production_lot.py:0
#, python-format
msgid "The alert date has been reached for this lot/serial number"
msgstr "Osiągnięto datę alarmu dla tej partii/numeru seryjnego"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_move_line__expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__expiration_date
msgid ""
"This is the date on which the goods with this Serial Number may become "
"dangerous and must not be consumed."
msgstr ""
"To jest data upływu ważności produktu, po której produkt jest niebezpieczny "
"i musi być zutylizowany."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__removal_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__removal_date
msgid ""
"This is the date on which the goods with this Serial Number should be "
"removed from the stock. This date will be used in FEFO removal strategy."
msgstr ""
"Jest to data, w której towary z tym numerem seryjnym powinny zostać usunięte"
" z magazynu. Data ta zostanie wykorzystana w strategii usuwania FEFO."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__use_date
msgid ""
"This is the date on which the goods with this Serial Number start "
"deteriorating, without being dangerous yet."
msgstr ""
"To jest data upływu przydatności produktu. Produkt po tej dacie nie jest "
"jeszcze niebezpieczny."

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_picking
msgid "Transfer"
msgstr "Przekaz"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_move__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__use_expiration_date
msgid "Use Expiration Date"
msgstr "Używaj daty ważności"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.report_lot_label_expiry
msgid "Use by:"
msgstr "Użycie przez:"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_product_template__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_move__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__use_expiration_date
msgid ""
"When this box is ticked, you have the possibility to specify dates to manage"
" product expiration, on the product and on the corresponding lot/serial "
"numbers"
msgstr ""
"Gdy to pole jest zaznaczone, istnieje możliwość określenia dat zarządzania "
"wygaśnięciem produktu, na produkcie i na odpowiednich numerach partii/serii."

#. module: product_expiry
#: code:addons/product_expiry/wizard/confirm_expiry.py:0
#, python-format
msgid ""
"You are going to deliver some product expired lots.\n"
"Do you confirm you want to proceed ?"
msgstr ""
"Zamierzasz dostarczyć przeterminowane partie produktów.\n"
"Czy potwierdzasz, że chcesz kontynuować?"

#. module: product_expiry
#: code:addons/product_expiry/wizard/confirm_expiry.py:0
#, python-format
msgid ""
"You are going to deliver the product %(product_name)s, %(lot_name)s which is expired.\n"
"Do you confirm you want to proceed ?"
msgstr ""
"Zamierzasz dostarczyć produkt %(product_name)s, %(lot_name)s którego termin ważności upłynął.\n"
"Czy potwierdzasz, że chcesz kontynuować?"
