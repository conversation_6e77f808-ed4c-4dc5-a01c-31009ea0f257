<?xml version="1.0"?>
<odoo>
    <data>

    <!-- Purchase Requisition Type -->

    <record model="ir.ui.view" id="view_purchase_requisition_type_tree">
        <field name="name">purchase.requisition.type.tree</field>
        <field name="model">purchase.requisition.type</field>
        <field name="arch" type="xml">
            <tree string="Purchase Agreement Types">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="exclusive"/>
            </tree>
        </field>
    </record>

    <record id="view_purchase_requisition_type_kanban" model="ir.ui.view">
        <field name="name">purchase.requisition.type.kanban</field>
        <field name="model">purchase.requisition.type</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="name"/>
                <field name="exclusive"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div class="o_kanban_record_top ">
                                <div class="o_kanban_record_headings mt4">
                                    <strong class="o_kanban_record_title"><field name="name"/></strong>
                                </div>
                                <field name="exclusive" widget="label_selection"/>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record model="ir.ui.view" id="view_purchase_requisition_type_form">
        <field name="name">purchase.requisition.type.form</field>
        <field name="model">purchase.requisition.type</field>
        <field name="arch" type="xml">
            <form string="Purchase Agreement Types">
            <sheet>
                <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                <group>
                    <group string="Agreement Type">
                        <field name="name"/>
                        <field name="exclusive" widget="radio"/>
                        <field name="active" invisible="1"/>
                    </group>
                    <group string="Data for new quotations">
                        <field name="line_copy" widget="radio"/>
                        <field name="quantity_copy" widget="radio"/>
                    </group>
                </group>
            </sheet>
            </form>
        </field>
    </record>
    <record model="ir.ui.view" id="view_purchase_requisition_type_search">
        <field name="name">purchase.requisition.type.search</field>
        <field name="model">purchase.requisition.type</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <separator/>
                <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>
    <record id="tender_type_action" model="ir.actions.act_window">
        <field name="name">Purchase Agreement Types</field>
        <field name="res_model">purchase.requisition.type</field>
        <field name="context">{}</field>
        <field name="view_mode">tree,kanban,form</field>
    </record>
    <menuitem
        id="menu_purchase_requisition_type"
        sequence="2"
        parent="purchase.menu_purchase_config"
        action="tender_type_action"/>

    <!-- Purchase Orders -->

    <record model="ir.actions.act_window" id="action_purchase_requisition_to_so">
        <field name="name">Request for Quotation</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">purchase.order</field>
        <field name="view_mode">form,tree</field>
        <field name="domain">[('requisition_id','=',active_id)]</field>
        <field name="context">{
            "default_requisition_id":active_id,
            }
        </field>
    </record>
    <record model="ir.actions.act_window" id="action_purchase_requisition_list">
        <field name="name">Request for Quotations</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">purchase.order</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('requisition_id','=',active_id)]</field>
        <field name="context">{
            "default_requisition_id":active_id,
            }
        </field>
    </record>

    <record model="ir.ui.view" id="view_purchase_requisition_form">
        <field name="name">purchase.requisition.form</field>
        <field name="model">purchase.requisition</field>
        <field name="arch" type="xml">
            <form string="Purchase Agreements">
            <header>
                <button name="%(action_purchase_requisition_to_so)d" type="action"
                    string="New Quotation"
                    context="{'default_currency_id': currency_id, 'default_user_id': user_id}"
                    attrs="{'invisible': [('state', '!=', 'open')]}"/>
                <button name="%(action_purchase_requisition_to_so)d" type="action"
                    string="New Quotation" class="btn-primary"
                    context="{'default_currency_id': currency_id, 'default_user_id': user_id}"
                    attrs="{'invisible': [('state', 'not in', ('in_progress', 'ongoing'))]}"/>
                <button name="action_in_progress" states="draft" string="Confirm" type="object" class="btn-primary"/>
                <button name="action_open" states="in_progress" string="Validate" type="object" class="btn-primary"/>
                <button name="action_done" states="open,ongoing" string="Close" type="object" class="btn-primary"/>
                <button name="action_draft" states="cancel" string="Reset to Draft" type="object"/>
                <button name="action_cancel" states="draft,in_progress,ongoing" string="Cancel" type="object"/>
                <field name="state" widget="statusbar" statusbar_visible="draft,in_progress,open,done" attrs="{'invisible': [('is_quantity_copy', '=', 'none')]}"/>
                <field name="state_blanket_order" widget="statusbar" statusbar_visible="draft,ongoing,done" attrs="{'invisible': [('is_quantity_copy', '!=', 'none')]}"/>
            </header>
            <sheet>
                <div class="oe_button_box" name="button_box">
                    <button name="%(action_purchase_requisition_list)d" type="action" class="oe_stat_button" icon="fa-list-alt"
                        attrs="{'invisible': [('state', '=', 'draft')]}" context="{'default_currency_id': currency_id}">
                        <field name="order_count" widget="statinfo" string="RFQs/Orders"/>
                    </button>
                </div>
                <div class="float-left">
                    <label for="name" class="oe_inline"/>
                    <h1>
                        <field name="name"/>
                    </h1>
                </div>
                <group>
                    <group>
                        <field name="is_quantity_copy" invisible='1'/>
                        <field name="user_id" attrs="{'readonly': [('state','not in',('draft','in_progress','open'))]}" domain="[('share', '=', False)]"/>
                        <field name="type_id" attrs="{'readonly': [('state','!=','draft')]}"/>
                        <field name="vendor_id" context="{'res_partner_search_mode': 'supplier'}" attrs="{'required': [('is_quantity_copy', '=', 'none')], 'readonly': [('state', 'in', ['ongoing','done'])]}"/>
                        <field name="currency_id" groups="base.group_multi_currency"/>
                    </group>
                    <group>
                        <field name="date_end" attrs="{'readonly': [('state','not in',('draft','in_progress','open','ongoing'))]}"/>
                        <field name="ordering_date" attrs="{'readonly': [('state','not in',('draft','in_progress','open','ongoing'))]}"/>
                        <field name="schedule_date" attrs="{'readonly': [('state','not in',('draft','in_progress','open','ongoing'))]}"/>
                        <field name="origin" placeholder="e.g. PO0025" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" attrs="{'readonly': [('state','not in',('draft'))]}"/>
                    </group>
                </group>
                <notebook>
                    <page string="Products" name="products">
                        <field name="line_ids">
                            <tree string="Products" editable="bottom">
                                <field name="product_id" context="{'default_purchase_requisition': 'tenders'}"
                                       domain="[('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"/>
                                <field name="product_description_variants" attrs="{'invisible': [('product_description_variants', '=', '')], 'readonly': [('parent.state', '!=', 'draft')]}"/>
                                <field name="product_qty"/>
                                <field name="qty_ordered" optional="show"/>
                                <field name="product_uom_category_id" invisible="1"/>
                                <field name="product_uom_id" string="UoM" groups="uom.group_uom" optional="show" attrs="{'required': [('product_id', '!=', False)]}"/>
                                <field name="schedule_date" optional="hide"/>
                                <field name="account_analytic_id" optional="hide" domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]" groups="analytic.group_analytic_accounting"/>
                                <field name="analytic_tag_ids" optional="hide" domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]" groups="analytic.group_analytic_tags" widget="many2many_tags"/>
                                <field name="price_unit"/>
                            </tree>
                            <form string="Products">
                                <group>
                                    <field name="product_id" context="{'default_purchase_requisition': 'tenders'}"
                                           domain="[('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]" />
                                    <field name="product_qty"/>
                                    <field name="qty_ordered"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="product_uom_id" />
                                    <field name="schedule_date"/>
                                    <field name="account_analytic_id"  domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]" groups="analytic.group_analytic_accounting"/>
                                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                                </group>
                            </form>
                        </field>
                        <separator string="Terms and Conditions"/>
                        <field name="description" class="oe-bordered-editor" attrs="{'readonly': [('state','not in',('draft','in_progress','open'))]}"/>
                    </page>
                </notebook>
            </sheet>
            <div class="oe_chatter">
              <field name="message_follower_ids"/>
              <field name="activity_ids"/>
              <field name="message_ids"/>
            </div>
            </form>
        </field>
    </record>
    <record model="ir.ui.view" id="view_purchase_requisition_tree">
        <field name="name">purchase.requisition.tree</field>
        <field name="model">purchase.requisition</field>
        <field name="arch" type="xml">
            <tree string="Purchase Agreements" sample="1">
                <field name="message_needaction" invisible="1"/>
                <field name="name" decoration-bf="1"/>
                <field name="user_id" optional="show" widget='many2one_avatar_user'/>
                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" optional="show"/>
                <field name="ordering_date" optional="show"/>
                <field name="schedule_date" optional="hide"/>
                <field name="date_end" optional="show" widget='remaining_days' decoration-danger="date_end and date_end&lt;current_date" attrs="{'invisible': [('state','in', ('done', 'cancel'))]}"/>
                <field name="origin" optional="show"/>
                <field name="state" optional="show" widget='badge' decoration-success="state == 'done'" decoration-info="state not in ('done', 'cancel')"/>
                <field name="activity_exception_decoration" widget="activity_exception"/>
            </tree>
      </field>
    </record>

    <record id="view_purchase_requisition_kanban" model="ir.ui.view">
        <field name="name">purchase.requisition.kanban</field>
        <field name="model">purchase.requisition</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile" sample="1">
                <field name="name"/>
                <field name="state"/>
                <field name="user_id"/>
                <field name="type_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings mt4">
                                    <strong class="o_kanban_record_title"><span><field name="name"/></span></strong>
                                </div>
                                <field name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'in_progress': 'default', 'open': 'success', 'done': 'success', 'close': 'danger'}}" readonly="1"/>
                            </div>
                            <div class="o_kanban_record_body">
                                <span class="text-muted"><field name="type_id"/></span>
                            </div>
                            <div class="o_kanban_record_bottom">
                                <div class="oe_kanban_bottom_left">
                                    <field name="vendor_id"/>
                                </div>
                                <div class="oe_kanban_bottom_right">
                                    <field name="user_id" widget="many2one_avatar_user"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="view_purchase_requisition_filter" model="ir.ui.view">
            <field name="name">purchase.requisition.list.select</field>
            <field name="model">purchase.requisition</field>
            <field name="arch" type="xml">
                <search string="Search Purchase Agreements">
                    <field name="name" string="Reference" filter_domain="['|', ('name', 'ilike', self), ('origin', 'ilike', self)]"/>
                    <field name="user_id"/>
                    <field name="product_id"/>
                    <filter string="My Agreements" name="my_agreements" domain="[('user_id', '=', uid)]"/>
                    <separator/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]" help="New Agreements"/>
                    <filter string="Confirmed" name="confirmed" domain="[('state', 'in', ('in_progress', 'open'))]" help="In negotiation"/>
                    <filter string="Done" name="done" domain="[('state', '=', 'done')]"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Purchase Representative" name="representative" domain="[]" context="{'group_by': 'user_id'}"/>
                        <filter string="Status" name="status" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Ordering Date" name="ordering_date" domain="[]" context="{'group_by': 'ordering_date'}"/>
                    </group>
                </search>
            </field>
        </record>


    <record model="ir.actions.act_window" id="action_purchase_requisition">
        <field name="name">Purchase Agreements</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">purchase.requisition</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="context">{}</field>
        <field name="search_view_id" ref="view_purchase_requisition_filter"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Start a new purchase agreement
          </p><p>
            Example of purchase agreements include call for tenders and blanket orders.
          </p><p>
            In a call for tenders, you can record the products you need to buy
            and generate the creation of RfQs to vendors. Once the tenders have
            been registered, you can review and compare them and you can
            validate some and cancel others.
          </p><p>
            For a blanket order, you can record an agreement for a specifc period
            (e.g. a year) and you order products within this agreement, benefiting
            from the negotiated prices.
          </p>
        </field>
    </record>

    <menuitem
        id="menu_purchase_requisition_pro_mgt"
        sequence="10"
        parent="purchase.menu_procurement_management"
        action="action_purchase_requisition"/>

    </data>
</odoo>
