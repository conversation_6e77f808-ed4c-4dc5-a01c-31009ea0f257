# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* note
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> Bataa, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# Batmunk<PERSON> Ganbat <<EMAIL>>, 2021
# tser<PERSON><PERSON><PERSON> tsogtoo <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# Torbat Jargalsaikhan, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Torbat Jargalsaikhan, 2023\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "<i class=\"fa fa-check\" role=\"img\" aria-label=\"Opened\" title=\"Opened\"/>"
msgstr "<i class=\"fa fa-check\" role=\"img\" aria-label=\"Opened\" title=\"Opened\"/>"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "<i class=\"fa fa-undo\" role=\"img\" aria-label=\"Closed\" title=\"Closed\"/>"
msgstr "<i class=\"fa fa-undo\" role=\"img\" aria-label=\"Closed\" title=\"Closed\"/>"

#. module: note
#: model:ir.model.fields,field_description:note.field_mail_activity_type__category
msgid "Action"
msgstr "Үйлдэл"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_needaction
msgid "Action Needed"
msgstr "Үйлдэл шаардсан"

#. module: note
#: model:ir.model.fields,help:note.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__open
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Active"
msgstr "Идэвхитэй"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_ids
msgid "Activities"
msgstr "Ажилбар"

#. module: note
#: model:ir.model,name:note.model_mail_activity
msgid "Activity"
msgstr "Ажилбар"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Ажилбарын тайлбар"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_state
msgid "Activity State"
msgstr "Ажилбарын төлөв"

#. module: note
#: model:ir.model,name:note.model_mail_activity_type
msgid "Activity Type"
msgstr "Ажилбарын төрөл"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ажилбарын төрлийн зураг"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid "Add a new personal note"
msgstr "Шинэ хувийн тэмдэглэл нэмэх"

#. module: note
#: model_terms:ir.actions.act_window,help:note.note_tag_action
msgid "Add a new tag"
msgstr "Шинэ пайз нэмэх"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Add a note"
msgstr "Тэмдэглэл нэмэх"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Add new note"
msgstr "Шинэ тэмдэглэл нэмэх"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Archive"
msgstr "Архив"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_attachment_count
msgid "Attachment Count"
msgstr "Хавсралтын тоо"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "By sticky note Category"
msgstr "Тэмдэглэл, наалт ангилалаар"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Channel"
msgstr "Суваг"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__color
#: model:ir.model.fields,field_description:note.field_note_tag__color
msgid "Color Index"
msgstr "Өнгөний индекс"

#. module: note
#: model:ir.ui.menu,name:note.menu_note_configuration
msgid "Configuration"
msgstr "Тохиргоо"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__create_uid
#: model:ir.model.fields,field_description:note.field_note_stage__create_uid
#: model:ir.model.fields,field_description:note.field_note_tag__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__create_date
#: model:ir.model.fields,field_description:note.field_note_stage__create_date
#: model:ir.model.fields,field_description:note.field_note_tag__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__date_done
msgid "Date done"
msgstr "Хийгдсэн огноо"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Delete"
msgstr "Устгах"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__display_name
#: model:ir.model.fields,field_description:note.field_note_stage__display_name
#: model:ir.model.fields,field_description:note.field_note_tag__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Dropdown menu"
msgstr "Эвхэгддэг цэс"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage__fold
msgid "Folded by Default"
msgstr "Анхныхаараа эвхэгдсэн байна"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Follower"
msgstr "Дагагч"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_follower_ids
msgid "Followers"
msgstr "Дагагчид"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_partner_ids
msgid "Followers (Partners)"
msgstr "Дагагчид (Харилцагчид)"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon ж.ш. fa-tasks"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Future Activities"
msgstr "Ирээдүйн үйл ажиллагаанууд"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Group By"
msgstr "Бүлэглэлт"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__has_message
msgid "Has Message"
msgstr "Мессежтэй"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__id
#: model:ir.model.fields,field_description:note.field_note_stage__id
#: model:ir.model.fields,field_description:note.field_note_tag__id
msgid "ID"
msgstr "ID"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_exception_icon
msgid "Icon"
msgstr "Дүрс"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ажилбар дээр сануулга гарсныг илэрхийлэх зураг."

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_needaction
#: model:ir.model.fields,help:note.field_note_note__message_unread
msgid "If checked, new messages require your attention."
msgstr "Хэрэв сонгогдсон бол, шинэ зурвасууд таны анхаарлыг шаардана."

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Үүнийг сонговол алдаа үүсэх үед зурвасууд ирнэ."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_is_follower
msgid "Is Follower"
msgstr "Дагагч эсэх"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note____last_update
#: model:ir.model.fields,field_description:note.field_note_stage____last_update
#: model:ir.model.fields,field_description:note.field_note_tag____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__write_uid
#: model:ir.model.fields,field_description:note.field_note_stage__write_uid
#: model:ir.model.fields,field_description:note.field_note_tag__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__write_date
#: model:ir.model.fields,field_description:note.field_note_stage__write_date
#: model:ir.model.fields,field_description:note.field_note_tag__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Late Activities"
msgstr "Хоцорсон ажилбар"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_main_attachment_id
msgid "Main Attachment"
msgstr "Үндсэн хавсралт"

#. module: note
#: model:note.stage,name:note.note_stage_01
msgid "Meeting Minutes"
msgstr "Хурлын тэмдэглэл"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_has_error
msgid "Message Delivery error"
msgstr "Алдаа үүссэн талаарх зурвас"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_ids
msgid "Messages"
msgstr "Зурвасууд"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Миний ажилбарын эцсийн огноо"

#. module: note
#: model:note.stage,name:note.note_stage_00
msgid "New"
msgstr "Шинэ"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Календар дээрх дараагийн Үйл ажиллагаа"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Дараагийн ажилбарын эцсийн огноо"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_summary
msgid "Next Activity Summary"
msgstr "Дараагийн ажилбарын гарчиг"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_type_id
msgid "Next Activity Type"
msgstr "Дараагийн ажилбарын төрөл"

#. module: note
#: model:ir.model,name:note.model_note_note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Note"
msgstr "Тэмдэглэл"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__memo
msgid "Note Content"
msgstr "Тэмдэглэлийн агуулга"

#. module: note
#: model:ir.model,name:note.model_note_stage
msgid "Note Stage"
msgstr "Тэмдэглэлийн төлөв"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__name
msgid "Note Summary"
msgstr "Тэмдэглэлийн гарчиг"

#. module: note
#: model:ir.model,name:note.model_note_tag
msgid "Note Tag"
msgstr "Тэмдэглэлийн пайз"

#. module: note
#: code:addons/note/models/res_users.py:0
#: model:ir.actions.act_window,name:note.action_note_note
#: model:ir.ui.menu,name:note.menu_note_notes
#: model:note.stage,name:note.note_stage_02
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#, python-format
msgid "Notes"
msgstr "Тэмдэглэлүүд"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"Notes are private, unless you share them by inviting follower on a note.\n"
"            (Useful for meeting minutes)."
msgstr ""
"Тэмдэглэл бол хувийн нууц, гэвч та хүсвэл тэдгээрт дагагчид нэмэх аргаар бусадтай хуваалцаж болно.\n"
"           (Хурлын тэмдэглэл хөтлөхөд ашиглаж болно)."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_needaction_counter
msgid "Number of Actions"
msgstr "Үйлдлийн тоо"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_has_error_counter
msgid "Number of errors"
msgstr "Алдааны тоо"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Үйлдэл шаардсан зурвасын тоо"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Алдааны мэдэгдэл бүхий зурвасын тоо"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_unread_counter
msgid "Number of unread messages"
msgstr "Уншаагүй зурвасын тоо"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__user_id
#: model:ir.model.fields,field_description:note.field_note_stage__user_id
msgid "Owner"
msgstr "Эзэмшигч"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage__user_id
msgid "Owner of the note stage"
msgstr "Тэмдэглэлийн төлөв эзэмшигч"

#. module: note
#: model:ir.model.fields,field_description:note.field_mail_activity__note_id
msgid "Related Note"
msgstr "Холбоотой тэмдэглэл"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Remember..."
msgstr "Санамж..."

#. module: note
#: model:ir.model.fields.selection,name:note.selection__mail_activity_type__category__reminder
#: model:mail.activity.type,name:note.mail_activity_data_reminder
msgid "Reminder"
msgstr "Сануулга"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_user_id
msgid "Responsible User"
msgstr "Эд хариуцагч"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "SAVE"
msgstr "ХАДГАЛАХ"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__sequence
#: model:ir.model.fields,field_description:note.field_note_stage__sequence
msgid "Sequence"
msgstr "Дугаарлалт"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Set date and time"
msgstr "Огноо цаг оноох"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Өнөөдрийг хүртэлх хугацаанд дараагийн ажилбарын огноо нь тохируулагдсан бүх "
"тэмдэглэлүүд"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__stage_id
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Stage"
msgstr "Үе шат"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage__name
msgid "Stage Name"
msgstr "Үе шатын нэр"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_form
msgid "Stage of Notes"
msgstr "Тэмдэглэлийн үе шат"

#. module: note
#: model:ir.actions.act_window,name:note.action_note_stage
#: model:ir.ui.menu,name:note.menu_notes_stage
#: model_terms:ir.ui.view,arch_db:note.view_note_note_tree
msgid "Stages"
msgstr "Үе шат"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_tree
msgid "Stages of Notes"
msgstr "Тэмдэглэлийн үе шат"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__stage_ids
msgid "Stages of Users"
msgstr "Хэрэлэгчдийн үе шат"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Ажилбаруудын төлөв байдал\n"
"Хоцорсон: Гүйцэтгэх огноо нь аль хэдий нь өнгөрсөн\n"
"Өнөөдөр: Өнөөдөр гүйцэтгэх ёстой\n"
"Төлөвлөгдсөн: Ирээдүйд гүйцэтгэх ажилбарууд"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_tag__name
msgid "Tag Name"
msgstr "Пайзын нэр"

#. module: note
#: model:ir.model.constraint,message:note.constraint_note_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Пайзын нэр аль хэдийн үүссэн байна !"

#. module: note
#: model:ir.actions.act_window,name:note.note_tag_action
#: model:ir.model.fields,field_description:note.field_note_note__tag_ids
#: model:ir.ui.menu,name:note.notes_tag_menu
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_form
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_tree
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Tags"
msgstr "Пайз"

#. module: note
#. openerp-web
#: code:addons/note/static/src/js/systray_activity_menu.js:0
#, python-format
msgid "Today"
msgstr "Өнөөдөр"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Today Activities"
msgstr "Өнөөдрийн ажилбар"

#. module: note
#: model:note.stage,name:note.note_stage_03
msgid "Todo"
msgstr "Todo"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Бичлэг дээрх асуудал бүхий ажилбарын төрөл"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_unread
msgid "Unread Messages"
msgstr "Уншаагүй зурвас"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Уншаагүй зурвасын тоолуур"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage__sequence
msgid "Used to order the note stages"
msgstr "Тэмдэглэлийн үеийг эрэмбэлэхэд хэрэглэгддэг"

#. module: note
#: model:ir.model,name:note.model_res_users
msgid "Users"
msgstr "Хэрэглэгчид"
