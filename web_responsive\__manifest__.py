# Copyright 2016-2017 LasLabs Inc.
# Copyright 2017-2018 Tecnativa - <PERSON><PERSON>
# Copyright 2018-2019 Tecnativa - <PERSON>
# Copyright 2021 IT<PERSON>ra - <PERSON>
# License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl.html).

{
    "name": "Web Responsive",
    "summary": "Responsive web client, community-supported",
    "version": "15.0.1.1.7",
    "category": "Website",
    "website": "https://github.com/OCA/web",
    "author": "LasLabs, Tecnativa, ITerra, " "Odoo Community Association (OCA)",
    "license": "LGPL-3",
    "installable": True,
    "depends": ["web", "mail"],
    "development_status": "Production/Stable",
    "maintainers": ["Yajo", "Tardo", "SplashS"],
    "excludes": ["web_enterprise"],
    "data": ["views/res_users.xml", "views/web.xml"],
    "assets": {
        "web.assets_frontend": [
            "/web_responsive/static/src/legacy/js/website_apps_menu.js",
            "/web_responsive/static/src/legacy/scss/website_apps_menu.scss",
        ],
        "web.assets_backend": [
            "/web_responsive/static/src/legacy/scss/web_responsive.scss",
            "/web_responsive/static/src/legacy/js/web_responsive.js",
            "/web_responsive/static/src/legacy/scss/kanban_view_mobile.scss",
            "/web_responsive/static/src/legacy/js/kanban_renderer_mobile.js",
            "/web_responsive/static/src/components/ui_context.esm.js",
            "/web_responsive/static/src/components/apps_menu/apps_menu.scss",
            "/web_responsive/static/src/components/apps_menu/apps_menu.esm.js",
            "/web_responsive/static/src/components/navbar/main_navbar.scss",
            "/web_responsive/static/src/components/control_panel/control_panel.scss",
            "/web_responsive/static/src/components/control_panel/control_panel.esm.js",
            "/web_responsive/static/src/components/search_panel/search_panel.scss",
            "/web_responsive/static/src/components/search_panel/search_panel.esm.js",
            "/web_responsive/static/src/components/attachment_viewer/attachment_viewer.scss",
            "/web_responsive/static/src/components/attachment_viewer/attachment_viewer.esm.js",
            "/web_responsive/static/src/components/hotkey/hotkey.scss",
        ],
        "web.assets_qweb": [
            "/web_responsive/static/src/legacy/xml/form_buttons.xml",
            "/web_responsive/static/src/components/apps_menu/apps_menu.xml",
            "/web_responsive/static/src/components/control_panel/control_panel.xml",
            "/web_responsive/static/src/components/navbar/main_navbar.xml",
            "/web_responsive/static/src/components/search_panel/search_panel.xml",
            "/web_responsive/static/src/components/attachment_viewer/attachment_viewer.xml",
            "/web_responsive/static/src/components/hotkey/hotkey.xml",
        ],
        "web.assets_tests": [
            "/web_responsive/static/tests/test_patch.js",
        ],
    },
    "sequence": 1,
}
