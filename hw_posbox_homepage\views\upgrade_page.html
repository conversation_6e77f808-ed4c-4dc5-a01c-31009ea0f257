{% extends "layout.html" %}
{% from "loading.html" import loading_block_ui %}
{% block head %}
    <script type="text/javascript" src="/web/static/lib/jquery/jquery.js"></script>
    <script>
        $(function() {
            var updating = false;
            $('#upgrade').click(function() {
                if (!updating) {
                    updating = true;
                    $('.loading-block').removeClass('o_hide');
                    $.ajax({
                        url:'/hw_proxy/perform_upgrade/'
                    }).done(function() {
                        $('.message-title').text('Upgrade successful');
                        var cpt = 25;
                        setInterval(function() {
                            --cpt;
                            if (cpt === 0) {location.reload();}
                            $('.message-status').text('Restarting the IoTBox.  Available in ' + cpt);
                        } , 1000);
                    }).fail(function() {
                        $('.error-message').text('Upgrade Failed');
                    });
                }
            });
            $('#flash').click(async function() {
                if (confirm('Are you sure you want to flash your IoT Box?\nThe box will be unavailable for ~30 min\nDo not turn off the box or close this page during the flash.\nThis page will reaload when your box is ready.')) {
                    $('.loading-block').removeClass('o_hide');
                    $('.message-title').text('IoTBox perform a self flashing it take a lot of time (~30min).');
                    $('.message-status').text('Prepare space for IoTBox.');
                    try {
                        await $.ajax({url: '/hw_proxy/perform_flashing_create_partition/'}).promise();
                        $('.message-status').text('Prepare new boot partition.');
                        await $.ajax({url: '/hw_proxy/perform_flashing_download_raspios/'}).promise();
                        $('.message-status').text('Download file for new boot partition.');
                        await $.ajax({url: '/hw_proxy/perform_flashing_copy_raspios/'}).promise();
                        $('.message-status').text('Prepare to restart and installation of the new version of the IoT Box.');
                        setTimeout(function() {
                        $('.message-status').text('The auto flash is almost finished - the page will be automatically reloaded');
                        setInterval(function() {
                            $.ajax({
                                url: '/hw_proxy/get_version',
                                timeout: 4000,
                            }).done(function(version) {
                                if (version == {{ flashToVersion }}) {
                                    window.location = '/';
                                }
                            });
                        } , 2000);
                    }, 240000);
                    }
                    catch(error) {
                        $('.loading-block').addClass('o_hide');
                        $('.error-message').text(error.responseText);
                    }
                }
            });
        });
    </script>
    <style>
        .commit-details {
            background: #f1f1f1;
            padding: 10px 10px 0 10px;
            border-radius: 5px;
        }
    </style>
{% endblock %}
{% block content %}
    <h2 class="text-center">IoT Box Software Upgrade</h2>
    <p>
        This tool will help you perform an upgrade of the IoTBox's software over the internet.
        However the preferred method to upgrade the IoTBox is to flash the sd-card with
        the <a href='https://nightly.odoo.com/master/iotbox/iotbox-latest.zip'>latest image</a>. The upgrade
        procedure is explained into to the
        <a href='https://www.odoo.com/documentation/15.0/applications/productivity/iot.html'>IoTBox manual</a>
    </p>
    <p>
        To upgrade the IoTBox, click on the upgrade button. The upgrade will take a few minutes. <b>Do not reboot</b> the IoTBox during the upgrade.
    </p>
    <div class="commit-details">
        <div style="padding-bottom: 5px; font-weight: bold;">
            Latest patch:
        </div>
        <pre style="margin: 0;padding: 15px 0; overflow: auto;">{{ commit|safe }}</pre>
    </div>
    <div class="text-center" style="margin: 15px auto;">
        {% if flashToVersion %}
            <a class="btn" href='#' id='flash'>Upgrade to {{ flashToVersion }}</a>
        {% else %}
            <a class="btn" href='#' id='upgrade'>Upgrade</a>
        {% endif %}
    </div>
    {{ loading_block_ui(loading_message) }}
{% endblock %}
