<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="sale.sale_order_1" model="sale.order">
            <field name="warehouse_id" ref="stock.warehouse0"/>
        </record>

        <record id="sale.sale_order_2" model="sale.order">
            <field name="warehouse_id" ref="stock.warehouse0"/>
        </record>

        <record id="sale.sale_order_3" model="sale.order">
            <field name="warehouse_id" ref="stock.warehouse0"/>
        </record>

        <record id="sale.sale_order_5" model="sale.order">
            <field name="warehouse_id" ref="stock.warehouse0"/>
        </record>

        <record id="sale.sale_order_6" model="sale.order">
            <field name="warehouse_id" ref="stock.warehouse0"/>
        </record>

        <record id="sale.sale_order_8" model="sale.order">
            <field name="warehouse_id" ref="stock.warehouse0"/>
        </record>

        <!-- Create some new sale orders to have more data for the forecast report -->
        <record id="sale_order_19" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now() + relativedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="warehouse_id" ref="stock.warehouse0"/>
        </record>

        <record id="sale_order_line_42" model="sale.order.line">
            <field name="order_id" ref="sale_order_19"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">5</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">295.00</field>
        </record>

        <record id="sale_order_line_43" model="sale.order.line">
            <field name="order_id" ref="sale_order_19"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_10').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_10"/>
            <field name="product_uom_qty">5</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">140.00</field>
        </record>

        <record id="sale_order_20" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now() + relativedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="warehouse_id" ref="stock.warehouse0"/>
        </record>

        <record id="sale_order_line_44" model="sale.order.line">
            <field name="order_id" ref="sale_order_20"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">5</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">295.00</field>
        </record>

        <record id="sale_order_21" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now() + relativedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="warehouse_id" ref="stock.warehouse0"/>
        </record>

        <record id="sale_order_line_45" model="sale.order.line">
            <field name="order_id" ref="sale_order_21"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">10</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">295.00</field>
        </record>

        <record id="sale_order_line_46" model="sale.order.line">
            <field name="order_id" ref="sale_order_21"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_10').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_10"/>
            <field name="product_uom_qty">10</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">140.00</field>
        </record>

        <record id="sale_order_22" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now() + relativedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="warehouse_id" ref="stock.warehouse0"/>
        </record>

        <record id="sale_order_line_47" model="sale.order.line">
            <field name="order_id" ref="sale_order_22"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_5').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_5"/>
            <field name="product_uom_qty">10</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">199.00</field>
        </record>


        <function model="sale.order" name="action_confirm" eval="[[
            ref('sale_order_19'),
            ref('sale_order_20'),
            ref('sale_order_21'),
            ref('sale_order_22'),
        ]]"/>

        <!-- Change date of those sale orders' delivery -->
        <function model="stock.picking" name="write">
            <value model="stock.picking" search="[('sale_id', '=', obj().env.ref('sale_stock.sale_order_19').id)]"/>
            <value eval="{'scheduled_date': (datetime.now() + relativedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')}"/>
        </function>
        <function model="stock.picking" name="write">
            <value model="stock.picking" search="[('sale_id', '=', obj().env.ref('sale_stock.sale_order_20').id)]"/>
            <value eval="{'scheduled_date': (datetime.now() + relativedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')}"/>
        </function>
        <function model="stock.picking" name="write">
            <value model="stock.picking" search="[('sale_id', '=', obj().env.ref('sale_stock.sale_order_21').id)]"/>
            <value eval="{'scheduled_date': (datetime.now() + relativedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')}"/>
        </function>


        <!-- Inventory for new Customizable Desks -->
        <record id="stock_inventory_7e" model="stock.quant">
            <field name="product_id" ref="sale.product_product_4e"/>
            <field name="inventory_quantity">65.0</field>
            <field name="location_id" model="stock.location" eval="obj().env.ref('stock.warehouse0').lot_stock_id.id"/>
        </record>
        <record id="stock_inventory_7f" model="stock.quant">
            <field name="product_id" ref="sale.product_product_4f"/>
            <field name="inventory_quantity">70.0</field>
            <field name="location_id" model="stock.location" eval="obj().env.ref('stock.warehouse0').lot_stock_id.id"/>
        </record>
        <function model="stock.quant" name="action_apply_inventory">
            <function eval="[[('id', 'in', (ref('stock_inventory_7e'),
                                            ref('stock_inventory_7f'),
                                            ))]]" model="stock.quant" name="search"/>
        </function>
    </data>
</odoo>
