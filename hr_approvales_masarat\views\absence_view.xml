<?xml version="1.0"?>
<odoo>

    <record id="absence_view_form" model="ir.ui.view">
        <field name="name">hr.masarat.absence.form</field>
        <field name="model">hr.masarat.absence</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar"/>
                    <button string="Manager Approval" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}" name="make_manager_approval" type="object" class="oe_highlight"/>
                    <button string="Manager Refuse" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}" name="make_manager_refused" type="object"/>
                    <button string="HR Approval" name="make_hr_approval" type="object" class="oe_highlight" groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
                    <button string="HR Refuse" name="make_hr_refused" type="object" groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
                    <button string="Cancel" attrs="{'invisible': [('state','=','draft')]}" name="make_cancel_approval" type="object"/>
                </header>
                <sheet>
                    <div>
                        <h2>
                            <field name="employee_id" placeholder="Employee" attrs="{'readonly': [('is_hr_group', '!=', 'yes')]}"/>
                            <field name="is_hr_group" invisible="1"/>
                        </h2>
                    </div>
                    <group>
                        <group string="Request Info">
                            <field name="is_manager" invisible="1"/>

                            <field name="manager_id" options='{"no_open": True}'/>
                            <field name="request_date"/>
                        </group>
                        <group string="Absence Timestamp Info">
                            <field name="absence_type" attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="absence_start_at" attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="absence_end_at" attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="total_absence_days"/>
                        </group>
                        <group>
                            <field name="Note" attrs="{'readonly': [('state','!=','draft')]}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>


    <record id="absence_view_tree" model="ir.ui.view">
        <field name="name">hr.masarat.absence.tree</field>
        <field name="model">hr.masarat.absence</field>
        <field name="arch" type="xml">
            <tree>
                <field name="employee_id"/>
                <field name="absence_start_at"/>
                <field name="total_absence_days"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="action_absence_view" model="ir.actions.act_window">
        <field name="name">طلبات تبرير الغياب</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.masarat.absence</field>
        <field name="view_mode">tree,form</field>
    </record>


    <menuitem
            id="menu_absence"
            name="طلبات تبرير غياب"
            parent="hr_masarat_approvals"
            action="action_absence_view"
            sequence="2"/>


</odoo>

