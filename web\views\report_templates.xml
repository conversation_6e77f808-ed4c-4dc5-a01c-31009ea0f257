<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Private templates used internally by reports -->

    <template id="report_layout" name="Report layout">&lt;!DOCTYPE html&gt;
        <html t-att-lang="lang and lang.replace('_', '-')"
              t-att-data-report-margin-top="data_report_margin_top"
              t-att-data-report-header-spacing="data_report_header_spacing"
              t-att-data-report-dpi="data_report_dpi"
              t-att-data-report-landscape="data_report_landscape"
              t-att-web-base-url="web_base_url">
            <head>
                <meta charset="utf-8"/>
                <meta name="viewport" content="initial-scale=1"/>
                <title><t t-esc="title or 'Odoo Report'"/></title>
                <t t-call-assets="web.report_assets_common" t-js="false"/>
                <!--The following is kind of a hack-->
                <!--in an iframe, we don't want the session to perform qweb_templates rpc-->
                <script type="text/javascript">
                    window.odoo = {};
                    window.odoo.__session_info__ = {is_report: true};
                </script>
                <t t-call-assets="web.assets_common" t-css="false"/>
                <t t-call-assets="web.report_assets_common" t-css="false"/>
            </head>
            <body t-att-class="'container' if not full_width else 'container-fluid'">
                <div id="wrapwrap">
                    <main>
                        <t t-out="0"/>
                    </main>
                </div>
            </body>
        </html>
    </template>

    <template id="report_preview_layout" name="Report layout">&lt;!DOCTYPE html&gt;
        <html t-att-lang="lang and lang.replace('_', '-')"
              t-att-data-report-margin-top="data_report_margin_top"
              t-att-data-report-header-spacing="data_report_header_spacing"
              t-att-data-report-dpi="data_report_dpi"
              t-att-data-report-landscape="data_report_landscape"
              t-att-web-base-url="web_base_url"
        >
            <head>
                <meta charset="utf-8"/>
                <meta name="viewport" content="initial-scale=1"/>
                <title><t t-esc="title or 'Odoo Report'"/></title>
                <t t-call-assets="web.report_assets_common" t-js="false"/>
                <t t-call-assets="web.assets_common" t-css="false"/>
                <style>
                    <t t-out="preview_css"/>

                    /**
                        Some css is overridden as it doesn't work properly in the preview.
                        Before generating 'real' reports, a lot of processing is applied. It is therefore quite
                        complicated to have an almost exact replica of the pdf report.
                        This changes down here are hacks to make the preview look as good as possible.
                    **/

                    /** The footers are not at the bottom of the page, so we push them down. **/
                    .o_background_footer  {
                        margin-top: 295px !important;
                        font-size: 0.8rem
                    }
                    .o_standard_footer {
                        margin-top: 200px !important;
                        font-size: 0.8rem
                    }
                    .o_boxed_footer {
                         margin-top: 270px !important;
                         font-size: 0.8rem
                    }
                    .o_clean_footer {
                         margin-top: 175px !important;
                         font-size: 0.8rem
                    }

                    /** Avoid image overflowing and creating a scrollbar **/
                    .o_background_header {
                        min-width: initial !important;
                    }

                    /** This rule is overridden in web/report.scss to hack around whtml2pdf
                        Simply set it to its default value so it works properly in preview **/
                    .col-auto{
                        -webkit-box-flex: 0 !important;
                        -webkit-flex: 0 0 auto !important;
                        flex: 0 0 auto !important;
                        width: auto !important;
                        max-width: 100% !important;
                    }

                </style>
            </head>
            <body t-att-class="'container' if not full_width else 'container-fluid'" style="overflow:hidden">
                <div id="wrapwrap">
                        <t t-out="0"/>
                </div>
            </body>
        </html>
    </template>

    <template id="html_container">
        <t t-set="body_classname" t-value="'container'"/>
        <t t-call="web.report_layout">
            <t t-out="0"/>
        </t>
    </template>

    <template id="html_preview_container">
        <t t-set="body_classname" t-value="'container'"/>
        <t t-call="web.report_preview_layout">
            <t t-out="0"/>
        </t>
    </template>


    <template id="report_invoice_wizard_preview">
        <t t-call="web.html_preview_container">
           <t t-call="web.external_layout">
                   <div class="pt-5">
                       <div class="address row">
                           <div name="address" class="col-5" style="margin-left: auto">
                               <address>
                                   <address class="mb-0" itemscope="itemscope"
                                            itemtype="http://schema.org/Organization">
                                       <div>
                                           <span itemprop="name">Deco Addict</span>
                                       </div>
                                       <div itemprop="address" itemscope="itemscope"
                                            itemtype="http://schema.org/PostalAddress">
                                           <div class="d-flex align-items-baseline">
                                               <span class="w-100 o_force_ltr" itemprop="streetAddress">77 Santa Barbara
                                                   Rd<br/>Pleasant Hill CA 94523<br/>United States</span>
                                           </div>
                                       </div>
                                   </address>
                               </address>
                           </div>
                       </div>
                   </div>
                   <div class="page">
                       <h2>
                           <span>Invoice</span>
                           <span>INV/2020/07/0003</span>
                       </h2>
                       <div id="informations" class="row mt32 mb32">
                           <div class="col-auto mw-100 mb-2" name="invoice_date">
                               <strong>Invoice Date:</strong>
                               <p class="m-0">07/08/2020</p>
                           </div>
                           <div class="col-auto mw-100 mb-2" name="due_date">
                               <strong>Due Date:</strong>
                               <p class="m-0">08/07/2020</p>
                           </div>
                       </div>
                       <table class="table table-sm o_main_table" name="invoice_line_table">
                           <thead>
                               <tr>
                                   <th name="th_description" class="text-left"><span>Description</span></th>
                                   <th name="th_quantity" class="text-right"><span>Quantity</span></th>
                                   <th name="th_priceunit" class="text-right d-md-table-cell"><span>Unit Price</span></th>
                                   <th name="th_taxes" class="text-left d-md-table-cell"><span>Taxes</span></th>
                                   <th name="th_subtotal" class="text-right">
                                       <span>Amount</span>
                                   </th>
                               </tr>
                           </thead>
                           <tbody class="invoice_tbody">
                               <tr>
                                   <td name="account_invoice_line_name"><span>[FURN_8999] Three-Seat Sofa<br/>
                                       Three Seater Sofa with Lounger in Steel Grey Colour</span></td>
                                   <td class="text-right">
                                       <span>5.000</span>
                                   </td>
                                   <td class="text-right d-md-table-cell">
                                       <span class="text-nowrap">1,500.00</span>
                                   </td>
                                   <td class="text-left d-md-table-cell">
                                       <span id="line_tax_ids">15.00%</span>
                                   </td>
                                   <td class="text-right o_price_total">
                                       <span class="text-nowrap">$ <span class="oe_currency_value">7,500.00</span></span>
                                   </td>
                               </tr>
                               <tr>
                                   <td name="account_invoice_line_name"><span>[FURN_8220] Four Person Desk<br/>
                                       Four person modern office workstation</span></td>
                                   <td class="text-right">
                                       <span>5.000</span>
                                   </td>
                                   <td class="text-right d-md-table-cell">
                                       <span class="text-nowrap">2,350.00</span>
                                   </td>
                                   <td class="text-left d-md-table-cell">
                                       <span id="line_tax_ids">15.00%</span>
                                   </td>
                                   <td class="text-right o_price_total">
                                       <span class="text-nowrap">$ <span class="oe_currency_value">11,750.00</span></span>
                                   </td>
                               </tr>
                           </tbody>
                       </table>
                       <div class="clearfix">
                           <div id="total" class="row">
                               <div class="col-7 ml-auto">
                                   <table class="table table-sm" style="page-break-inside: avoid; position:relative;">
                                       <tbody><tr class="border-black o_subtotal" style="">
                                           <td><strong>Subtotal</strong></td>
                                           <td class="text-right">
                                               <span>$ <span class="oe_currency_value">19,250.00</span></span>
                                           </td>
                                       </tr>
                                           <tr style="">
                                               <td><span class="text-nowrap">Tax 15%</span></td>
                                               <td class="text-right o_price_total">
                                                   <span class="text-nowrap">$ 2,887.50</span>
                                               </td>
                                           </tr>
                                           <tr class="border-black o_total">
                                               <td><strong>Total</strong></td>
                                               <td class="text-right">
                                                   <span class="text-nowrap">$ <span class="oe_currency_value">
                                                       22,137.50</span></span>
                                               </td>
                                           </tr>
                                       </tbody></table>
                               </div>
                           </div>
                       </div>
                       <p>
                           Please use the following communication for your payment : <b><span>
                           INV/2020/07/0003</span></b>
                       </p>
                       <p name="payment_term">
                           <span>Payment terms: 30 Days</span>
                       </p>
                   </div>
           </t>
        </t>
    </template>

    <template id="minimal_layout">
        &lt;!DOCTYPE html&gt;
        <html style="height: 0;">
            <head>
                <base t-att-href="base_url"/>
                <t t-call-assets="web.report_assets_pdf" t-js="false"/>
                <t t-call-assets="web.report_assets_common" t-js="false"/>
                <t t-call-assets="web.report_assets_pdf" t-css="false"/>
                <meta charset="utf-8"/>
                <script t-if="subst">
                    function subst() {
                        var vars = {};
                        var x = document.location.search.substring(1).split('&amp;');
                        for (var i in x) {
                            var z = x[i].split('=', 2);
                            vars[z[0]] = unescape(z[1]);
                        }
                        var x = ['sitepage', 'sitepages', 'section', 'subsection', 'subsubsection'];
                        var z = {'sitepage': 'page', 'sitepages': 'topage'};
                        for (var i in x) {
                            var y = document.getElementsByClassName(z[x[i]] || x[i])
                            for (var j=0; j&lt;y.length; ++j)
                                y[j].textContent = vars[x[i]];
                        }

                        var index = vars['webpage'].split('.', 4)[3];
                        var header = document.getElementById('minimal_layout_report_headers');
                        if(header){
                            var companyHeader = header.children[index];
                            header.textContent = '';
                            header.appendChild(companyHeader);
                        }
                        var footer = document.getElementById('minimal_layout_report_footers');
                        if(footer){
                            var companyFooter = footer.children[index];
                            footer.textContent = '';
                            footer.appendChild(companyFooter);
                        }
                    }
                </script>
            </head>
            <body class="container" t-att-data-report-id="report_xml_id" t-att-onload="subst and 'subst()'" style="overflow:hidden">
                <t t-out="body"/>
            </body>
        </html>
    </template>

    <!-- External layouts styles -->

    <template id="address_layout">
        <t t-set="colclass" t-value="('col-sm-5' if report_type == 'html' else 'col-5') + ' ml-auto'"/>
        <t t-if="address">
            <div class="address row">
                <t t-if="information_block">
                    <t t-set="colclass" t-value="'col-5 offset-1'"/>
                    <div name="information_block" class="col-6">
                        <t t-out="information_block"/>
                    </div>
                </t>
                <div name="address" t-att-class="colclass">
                    <t t-esc="address"/>
                </div>
            </div>
        </t>
    </template>

    <template id="external_layout_striped">
        <div t-attf-class="o_company_#{company.id}_layout header" t-att-style="report_header_style">
            <div class="o_background_header">
            <div class="float-right">
                <h3 class="mt0 text-right" t-field="company.report_header"/>
            </div>
            <img t-if="company.logo" t-att-src="image_data_uri(company.logo)" class="float-left" alt="Logo"/>
            <div class="float-left company_address">
                <ul class="list-unstyled" t-if="company.company_details or forced_vat">
                    <li t-if="company.company_details"> <t t-esc="company.company_details"/></li>
                    <li t-if="forced_vat">
                        <t t-esc="company.country_id.vat_label or 'Tax ID'"/>:
                        <span t-esc="forced_vat"/>
                    </li>
                </ul>
            </div>
            <div class="clearfix mb8"/>
            </div>
        </div>

        <div t-attf-class="o_company_#{company.id}_layout article o_report_layout_striped {{  'o_layout_background' if company.layout_background in ['Geometric', 'Custom']  else  '' }}" t-attf-style="background-image: url({{ 'data:image/png;base64,%s' % company.layout_background_image.decode('utf-8') if company.layout_background_image and company.layout_background == 'Custom' else '/base/static/img/bg_background_template.jpg' if company.layout_background == 'Geometric' else ''}});" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
            <t t-call="web.address_layout"/>
            <t t-out="0"/>
        </div>

        <div t-attf-class="o_company_#{company.id}_layout footer o_background_footer">
            <div class="text-center">
                <ul class="list-inline">
                    <div t-field="company.report_footer"/>
                </ul>
                <div t-if="report_type == 'pdf'" class="text-muted">
                    Page:
                    <span class="page"/>
                    of
                    <span class="topage"/>
                </div>
                <div t-if="report_type == 'pdf' and display_name_in_footer" class="text-muted">
                    <span t-field="o.name"/>
                </div>
            </div>
        </div>
    </template>

    <template id="external_layout_boxed">
        <div t-attf-class="header o_company_#{company.id}_layout" t-att-style="report_header_style">
            <div class="o_boxed_header">
            <div class="row mb8">
                <div class="col-6">
                    <img t-if="company.logo" t-att-src="image_data_uri(company.logo)" alt="Logo"/>
                </div>
                <div class="col-6 text-right mb4">
                    <h4 class="mt0" t-field="company.report_header"/>
                    <div name="company_address" class="float-right mb4">
                        <ul class="list-unstyled" t-if="company.company_details or forced_vat">
                            <li t-if="company.company_details"> <t t-esc="company.company_details"/></li>
                            <li t-if="forced_vat">
                                <t t-esc="company.country_id.vat_label or 'Tax ID'"/>:
                                <span t-esc="forced_vat"/>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            </div>
        </div>

        <div t-attf-class="article o_report_layout_boxed o_company_#{company.id}_layout {{  'o_layout_background' if company.layout_background in ['Geometric', 'Custom']  else  '' }}" t-attf-style="background-image: url({{ 'data:image/png;base64,%s' % company.layout_background_image.decode('utf-8') if company.layout_background_image and company.layout_background == 'Custom' else '/base/static/img/bg_background_template.jpg' if company.layout_background == 'Geometric' else ''}});" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
            <div class="pt-5">
                <!-- This div ensures that the address is not cropped by the header. -->
                <t t-call="web.address_layout"/>
            </div>
            <t t-out="0"/>
        </div>

        <div t-attf-class="footer o_boxed_footer o_company_#{company.id}_layout">
            <div class="text-center">
                <div t-field="company.report_footer"/>
                <div t-if="report_type == 'pdf'">
                    Page: <span class="page"/> / <span class="topage"/>
                </div>
                <div t-if="report_type == 'pdf' and display_name_in_footer" class="text-muted">
                    <span t-field="o.name"/>
                </div>
            </div>
        </div>
    </template>

    <template id="external_layout_bold">
        <div t-attf-class="header o_company_#{company.id}_layout" t-att-style="report_header_style">
            <div class="o_clean_header">
            <div class="row">
                <div class="col-6">
                    <img t-if="company.logo" t-att-src="image_data_uri(company.logo)" alt="Logo"/>
                </div>
                <div class="col-5 offset-1" name="company_address">
                    <ul class="list-unstyled">
                        <strong><li t-if="company.name"><span t-field="company.name"/></li></strong>
                        <li t-if="forced_vat or company.vat">
                            <t t-esc="company.country_id.vat_label or 'Tax ID'"/>:
                            <span t-if="forced_vat" t-esc="forced_vat"/>
                            <span t-else="" t-field="company.vat"/>
                        </li>
                        <li t-if="company.phone">Tel: <span class="o_force_ltr" t-field="company.phone"/></li>
                        <li t-if="company.email"><span t-field="company.email"/></li>
                        <li t-if="company.website"><span t-field="company.website"/></li>
                    </ul>
                </div>
            </div>
            </div>
        </div>

        <div t-attf-class="article o_report_layout_bold o_company_#{company.id}_layout {{  'o_layout_background' if company.layout_background in ['Geometric', 'Custom']  else  '' }}" t-attf-style="background-image: url({{ 'data:image/png;base64,%s' % company.layout_background_image.decode('utf-8') if company.layout_background_image and company.layout_background == 'Custom' else ('/base/static/img/bg_background_template.jpg' if company.layout_background == 'Geometric' else '') }});" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
            <t t-call="web.address_layout"/>
            <t t-out="0"/>
        </div>

        <div t-attf-class="footer o_clean_footer o_company_#{company.id}_layout">
            <div class="row">
                <div class="col-4">
                    <span t-field="company.report_footer"/>
                </div>
                <div class="col-4">
                    <span t-if="company.company_details" t-field="company.company_details"/>
                </div>
                <div class="col-3">
                    <h5 class="mt0 mb0" t-field="company.report_header"/>
                </div>
                <div class="col-1">
                    <ul t-if="report_type == 'pdf'" class="list-inline pagenumber float-right text-center">
                        <li class="list-inline-item"><strong><span class="page"/></strong></li>
                    </ul>
                </div>
            </div>
        </div>
    </template>

    <template id="external_layout_standard">
        <div t-attf-class="header o_company_#{company.id}_layout" t-att-style="report_header_style">
            <div class="row">
                <div class="col-3 mb4">
                    <img t-if="company.logo" t-att-src="image_data_uri(company.logo)" style="max-height: 45px;" alt="Logo"/>
                </div>
                <div class="col-9 text-right" style="margin-top:22px;" t-field="company.report_header" name="moto"/>
            </div>
            <div t-if="company.logo or company.report_header" class="row zero_min_height">
                <div class="col-12">
                    <div style="border-bottom: 1px solid black;"/>
                </div>
            </div>
            <div class="row">
                <div class="col-6" name="company_address">
                    <ul class="list-unstyled" t-if="company.company_details or forced_vat">
                        <li t-if="company.company_details"> <t t-esc="company.company_details"/></li>
                        <li t-if="forced_vat">
                            <t t-esc="company.country_id.vat_label or 'Tax ID'"/>:
                            <span t-esc="forced_vat"/>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div t-attf-class="article o_report_layout_standard o_company_#{company.id}_layout {{  'o_layout_background' if company.layout_background in ['Geometric', 'Custom']  else  '' }}" t-attf-style="background-image: url({{ 'data:image/png;base64,%s' % company.layout_background_image.decode('utf-8') if company.layout_background_image and company.layout_background == 'Custom' else '/base/static/img/bg_background_template.jpg' if company.layout_background == 'Geometric' else ''}});" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
            <div class="pt-5">
                <!-- This div ensures that the address is not cropped by the header. -->
                <t t-call="web.address_layout"/>
            </div>
            <t t-out="0"/>
        </div>

        <div t-attf-class="footer o_standard_footer o_company_#{company.id}_layout">
            <div class="text-center" style="border-top: 1px solid black;">
                <ul class="list-inline mb4">
                    <div t-field="company.report_footer"/>
                </ul>

                <div t-if="report_type == 'pdf'" class="text-muted">
                    Page: <span class="page"/> / <span class="topage"/>
                </div>
                <div t-if="report_type == 'pdf' and display_name_in_footer" class="text-muted">
                    <span t-field="o.name"/>
                </div>
            </div>
        </div>
    </template>

    <!-- Public api: layout to t-call from reports -->

    <template id="external_layout">
        <t t-if="not o" t-set="o" t-value="doc"/>

        <t t-if="not company">
            <!-- Multicompany -->
            <t t-if="company_id">
                <t t-set="company" t-value="company_id"/>
            </t>
            <t t-elif="o and 'company_id' in o and o.company_id.sudo()">
                <t t-set="company" t-value="o.company_id.sudo()"/>
            </t>
            <t t-else="else">
                <t t-set="company" t-value="res_company"/>
            </t>
        </t>

        <t t-if="company.external_report_layout_id" t-call="{{company.external_report_layout_id.sudo().key}}"><t t-out="0"/></t>
        <t t-else="else" t-call="web.external_layout_standard"><t t-out="0"/></t>

    </template>

    <template id="internal_layout">
        <t t-if="not o" t-set="o" t-value="doc"/>

        <t t-if="not company">
            <!-- Multicompany -->
            <t t-if="company_id">
                <t t-set="company" t-value="company_id"/>
            </t>
            <t t-elif="o and 'company_id' in o and o.company_id.sudo()">
                <t t-set="company" t-value="o.company_id.sudo()"/>
            </t>
            <t t-else="else">
                <t t-set="company" t-value="res_company"/>
            </t>
        </t>

        <div class="header">
            <div class="row">
                <div class="col-3">
                    <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M')"/>
                </div>
                <div class="col-2 offset-2 text-center">
                    <span t-esc="company.name"/>
                </div>
                <div class="col-2 offset-3 text-right">
                    <ul class="list-inline">
                        <li class="list-inline-item"><span class="page"/></li>
                        <li class="list-inline-item">/</li>
                        <li class="list-inline-item"><span class="topage"/></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="article" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
          <t t-out="0"/>
        </div>
    </template>

    <template id="basic_layout">
        <t t-call="web.html_container">
            <t t-if="not o" t-set="o" t-value="doc"/>
            <div class="article" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
                <t t-out="0"/>
            </div>
        </t>
    </template>

    <!-- Customize the company form view by adding paperformat field as well as demo reports -->
    <!-- Demo reports definition -->
    <template id="preview_internalreport">
        <t t-set="company" t-value="env.company"/>
        <t t-call="web.html_container">
            <t t-set="o" t-value="res_company"/>
            <t t-call="web.internal_layout">
                <div class="page">
                    <p>This is a sample of an internal report.</p>
                </div>
            </t>
        </t>
    </template>
    <template id="preview_externalreport">
        <t t-set="company" t-value="env.company"/>
        <t t-call="web.html_container">
            <t t-set="o" t-value="res_company"/>
            <t t-call="web.external_layout">
                <div class="page">
                    <p>This is a sample of an external report.</p>
                </div>
            </t>
        </t>
    </template>

    <template id="preview_layout_report">
        <t t-set="company" t-value="env.company"/>
        <t t-call="web.html_container">
            <t t-set="o" t-value="res_company"/>
            <t t-call="web.report_invoice_wizard_preview"/>
        </t>
    </template>

    <template id="styles_company_report">
        <t t-foreach="company_ids" t-as="company" t-translation="off">
            <t t-set="forced_vat" t-value="False"/>
            <t t-set="font" t-value="company.font or 'Lato'" />
            <t t-set="primary" t-value="company.primary_color or 'black'" />
            <t t-set="secondary" t-value="company.secondary_color or 'black'" />
            <t t-set="layout" t-value="company.external_report_layout_id.key or 'web.external_layout_standard'" />
                .o_company_<t t-esc='company.id'/>_layout {
                font-family: <t t-esc="font" />;
            <t t-if="layout == 'web.external_layout_striped'">
                &amp;.o_report_layout_striped {
                   strong {
                        color: <t t-esc='secondary'/>;
                    }
                    h2 {
                        color: <t t-esc='primary'/>;
                    }
                    thead tr th {
                        color: <t t-esc='secondary'/>;
                    }
                }
                .row > div > table,
                div#total table {
                    &amp;:first-child,
                    &amp;:last-child,
                    &amp;.o_subtotal,
                    &amp;.o_total {
                        strong {
                            color: <t t-esc='primary'/>;
                        }
                    }
                }
            </t>
            <t t-elif="layout == 'web.external_layout_boxed'">
                &amp;.o_report_layout_boxed {
                    #total strong {
                        color: <t t-esc='primary'/>;
                    }
                    #informations strong {
                        color: <t t-esc='secondary'/>;
                    }
                    h2 span {
                        color: <t t-esc='primary'/>;
                    }
                    table {
                        thead {
                            tr th {
                                color: <t t-esc='secondary'/>;
                            }
                        }
                        tbody tr td {
                            &amp;.o_line_section td {
                                    background-color: rgba(<t t-esc='primary'/>, 0.7);
                                }
                            &amp;.is-subtotal,
                                td.o_price_total {
                                    background-color: rgba(<t t-esc='secondary'/>, 0.1);
                                }
                        }
                    }
                }
                .row > div > table,
                div#total table {
                    tr {
                        &amp;.o_total td {
                            background-color: rgba(<t t-esc='primary'/>, 0.9);
                        }
                    }
                }
            </t>
            <t t-elif="layout == 'web.external_layout_bold'" >
                &amp;.o_clean_footer {
                    border-top: 3px solid <t t-esc='secondary'/>;
                    h4 {
                        color: <t t-esc='secondary'/>;
                    }
                    .pagenumber {
                        border: 3px solid <t t-esc='primary'/>;
                        background-color: <t t-esc='secondary'/>;
                    }
                }
                &amp;.o_report_layout_bold {
                    h1, h2, h3 {
                        color: <t t-esc='primary'/>;
                    }
                    strong {
                        color: <t t-esc='secondary'/>;
                    }
                    table {
                       thead {
                           color: <t t-esc='secondary'/>;
                           tr th {
                                border-top: 3px solid <t t-esc='secondary'/> !important;
                            }
                        }
                        tbody {
                            tr:last-child td {
                                border-bottom: 3px solid <t t-esc='secondary'/>;
                            }
                        }
                    }
                    #total {
                        strong {
                            color: <t t-esc='secondary'/>;
                        }
                    }
                }
            </t>
            <t t-elif="layout == 'web.external_layout_standard'">
                &amp;.o_report_layout_standard {
                    h2 {
                        color: <t t-esc='primary'/>;
                    }
                    #informations strong {
                        color: <t t-esc='secondary'/>;
                    }
                    #total strong{
                        color: <t t-esc='primary'/>;
                    }
                    table {
                        thead {
                            color: <t t-esc='secondary'/>;
                        }
                    }
                }
            </t>
            }
        </t>
    </template>

    <record id="action_report_internalpreview" model="ir.actions.report">
        <field name="name">Preview Internal Report</field>
        <field name="model">res.company</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">web.preview_internalreport</field>
        <field name="report_file">web.preview_internalreport</field>
        <field name="binding_model_id" ref="model_res_company"/>
        <field name="binding_type">report</field>
    </record>
    <record id="action_report_externalpreview" model="ir.actions.report">
        <field name="name">Preview External Report</field>
        <field name="model">res.company</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">web.preview_externalreport</field>
        <field name="report_file">web.preview_externalreport</field>
        <field name="binding_model_id" ref="model_res_company"/>
        <field name="binding_type">report</field>
    </record>
    <record id="action_report_layout_preview" model="ir.actions.report">
        <field name="name">Report Layout Preview</field>
        <field name="model">res.company</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">web.preview_layout_report</field>
        <field name="report_file">web.preview_layout_report</field>
        <field name="binding_model_id" ref="model_res_company"/>
        <field name="binding_type">report</field>
    </record>
</odoo>
