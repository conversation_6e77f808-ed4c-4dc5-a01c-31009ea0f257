<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_fr" model="res.partner">
        <field name="name">FR Company</field>
        <field name="vat">FR91746948785</field>
        <field name="street">Rue Abbé Huet</field>
        <field name="city">Rennes</field>
        <field name="country_id" ref="base.fr"/>
        
        <field name="zip">35043</field>
        <field name="phone">+33 6 12 34 56 78</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.frexample.com</field>
    </record>

    <record id="demo_company_fr" model="res.company">
        <field name="name">FR Company</field>
        <field name="partner_id" ref="partner_demo_company_fr"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_fr')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_fr.demo_company_fr'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_fr.l10n_fr_pcg_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_fr.demo_company_fr')"/>
    </function>
</odoo>
