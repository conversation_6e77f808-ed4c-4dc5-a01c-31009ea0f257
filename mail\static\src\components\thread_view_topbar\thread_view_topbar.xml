<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.ThreadViewTopbar" owl="1">
        <div class="o_ThreadViewTopbar d-flex flex-shrink-0 w-100 px-3">
            <t t-if="threadViewTopbar">
                <t t-if="threadViewTopbar.thread">
                    <ThreadIcon class="o_ThreadViewTopbar_threadIcon mr-2 align-self-center" threadLocalId="threadViewTopbar.thread.localId"/>
                </t>
                <div class="o_ThreadViewTopbar_title d-flex flex-grow-1 align-self-center align-items-center">
                    <t t-if="threadViewTopbar.thread">
                        <t t-if="!threadViewTopbar.isEditingThreadName">
                            <div class="o_ThreadViewTopbar_threadName o_ThreadViewTopbar_editableItem flex-shrink-0 px-1 text-truncate lead font-weight-bold" t-att-title="threadViewTopbar.thread.displayName" t-on-click="threadViewTopbar.onClickTopbarThreadName" t-on-mouseenter="threadViewTopbar.onMouseEnterTopbarThreadName" t-on-mouseleave="threadViewTopbar.onMouseLeaveTopbarThreadName" t-att-class="{ 'o-threadNameEditable': threadViewTopbar.isMouseOverThreadName and !messaging.isCurrentUserGuest }">
                                <t t-esc="threadViewTopbar.thread.displayName"/>
                            </div>
                        </t>
                        <t t-if="threadViewTopbar.isEditingThreadName">
                            <input class="o_ThreadViewTopbar_threadNameInput lead font-weight-bold" type="text" t-att-value="threadViewTopbar.pendingThreadName" t-on-input="threadViewTopbar.onInputThreadNameInput" t-on-keydown="threadViewTopbar.onKeyDownThreadNameInput" t-ref="threadNameInput"/>
                        </t>
                    </t>
                    <t t-if="!threadViewTopbar.thread">
                        <div class="o_ThreadViewTopbar_threadName flex-shrink-0 px-1 text-truncate lead font-weight-bold">
                            Discuss
                        </div>
                    </t>
                    <t t-if="threadViewTopbar.hasDescriptionArea">
                        <div class="o_ThreadViewTopbar_threadDescriptionSeparator flex-shrink-0 mx-2"/>
                        <t t-if="!threadViewTopbar.isEditingThreadDescription">
                            <t t-if="threadViewTopbar.thread.description">
                                <div class="o_ThreadViewTopbar_threadDescription o_ThreadViewTopbar_editableItem text-truncate px-1" t-att-title="threadViewTopbar.thread.description" t-on-click="threadViewTopbar.onClickTopbarThreadDescription" t-on-mouseenter="threadViewTopbar.onMouseEnterTopbarThreadDescription" t-on-mouseleave="threadViewTopbar.onMouseLeaveTopbarThreadDescription" t-att-class="{ 'o-threadDescriptionEditable': threadViewTopbar.isDescriptionHighlighted }">
                                    <t t-esc="threadViewTopbar.thread.description"/>
                                </div>
                            </t>
                            <t t-if="!threadViewTopbar.thread.description">
                                <div class="o_ThreadViewTopbar_threadAddDescriptionEmptyLabel text-truncate" t-on-click="threadViewTopbar.onClickTopbarThreadDescription">
                                    Add a description
                                </div>
                            </t>
                        </t>
                        <t t-if="threadViewTopbar.isEditingThreadDescription">
                            <input class="o_ThreadViewTopbar_threadDescriptionInput" type="text" t-att-value="threadViewTopbar.pendingThreadDescription" t-on-input="threadViewTopbar.onInputThreadDescriptionInput" t-on-keydown="threadViewTopbar.onKeyDownThreadDescriptionInput" t-ref="threadDescriptionInput"/>
                        </t>
                    </t>
                </div>
                <div class="o_ThreadViewTopbar_actions d-flex align-items-center ml-1">
                    <t t-if="threadViewTopbar.thread and threadViewTopbar.thread === messaging.inbox">
                        <button class="o_ThreadViewTopbar_markAllReadButton btn btn-secondary" t-att-disabled="threadViewTopbar.threadView.messages.length === 0" t-on-click="threadViewTopbar.onClickInboxMarkAllAsRead">Mark all read</button>
                    </t>
                    <t t-if="threadViewTopbar.thread and threadViewTopbar.thread === messaging.starred">
                        <button class="o_ThreadViewTopbar_unstarAllButton btn btn-secondary" t-att-disabled="threadViewTopbar.threadView.messages.length === 0" t-on-click="threadViewTopbar.onClickUnstarAll">Unstar all</button>
                    </t>
                    <t t-if="threadViewTopbar.thread and threadViewTopbar.thread.hasCallFeature and threadViewTopbar.thread.rtcSessions.length === 0">
                        <button class="o_ThreadViewTopbar_callButton o_ThreadViewTopbar_button o-active" t-att-disabled="threadViewTopbar.thread.hasPendingRtcRequest" title="Start a Call" t-on-click="_onClickPhone">
                            <i class="fa fa-lg fa-phone"/>
                        </button>
                        <button class="o_ThreadViewTopbar_callButton o_ThreadViewTopbar_button o-active" t-att-disabled="threadViewTopbar.thread.hasPendingRtcRequest" title="Start a Video Call" t-on-click="_onClickCamera">
                            <i class="fa fa-lg fa-video-camera"/>
                        </button>
                    </t>
                    <t t-if="threadViewTopbar.thread and threadViewTopbar.thread.hasInviteFeature">
                        <button class="o_ThreadViewTopbar_inviteButton o_ThreadViewTopbar_button" t-att-class="{ 'o-active': threadViewTopbar.invitePopoverView }" title="Add users" t-on-click="threadViewTopbar.onClickInviteButton" t-ref="inviteButton">
                            <i class="fa fa-lg fa-user-plus"/>
                        </button>
                        <t t-if="threadViewTopbar.invitePopoverView">
                            <PopoverView class="o_ThreadViewTopbar_invitePopoverView" popoverViewLocalId="threadViewTopbar.invitePopoverView.localId"/>
                        </t>
                    </t>
                    <!-- FIXME: guests should be able to see members but there currently is no route for it, so hide it for now -->
                    <t t-if="!messaging.isCurrentUserGuest and threadViewTopbar.thread and threadViewTopbar.thread.hasMemberListFeature and threadViewTopbar.threadView.hasMemberList and !threadViewTopbar.threadView.isMemberListOpened">
                        <button class="o_ThreadViewTopbar_showMemberListButton o_ThreadViewTopbar_button" title="Show Member List" t-on-click="threadViewTopbar.onClickShowMemberList">
                            <i class="fa fa-lg fa-users"/>
                        </button>
                    </t>
                    <t t-if="threadViewTopbar.thread and threadViewTopbar.thread.hasMemberListFeature and threadViewTopbar.threadView.hasMemberList and threadViewTopbar.threadView.isMemberListOpened">
                        <button class="o_ThreadViewTopbar_hideMemberListButton o_ThreadViewTopbar_button o-active" title="Hide Member List" t-on-click="threadViewTopbar.onClickHideMemberList">
                            <i class="fa fa-lg fa-users"/>
                        </button>
                    </t>
                </div>
                <!-- FIXME: handle display on mobile -->
                <t t-if="threadViewTopbar.threadView.threadViewer.discussPublicView and !messaging.device.isMobile">
                    <div class="o_ThreadViewTopbar_userInfo d-flex align-items-center">
                        <img class="o_ThreadViewTopbar_avatar ml-1 mr-1 rounded-circle" t-att-src="threadViewTopbar.avatarUrl" alt="Avatar"/>
                        <t t-if="!threadViewTopbar.isEditingGuestName">
                            <span class="o_ThreadViewTopbar_userName o_ThreadViewTopbar_editableItem px-1 font-weight-bold text-truncate" t-att-class="{ 'o-userNameEditable': threadViewTopbar.isMouseOverUserName and messaging.isCurrentUserGuest }" t-on-click="threadViewTopbar.onClickUserName" t-on-mouseenter="threadViewTopbar.onMouseEnterUserName" t-on-mouseleave="threadViewTopbar.onMouseLeaveUserName">
                                <t t-esc="messaging.isCurrentUserGuest ? messaging.currentGuest.name : messaging.currentUser.nameOrDisplayName"/>
                            </span>
                        </t>
                        <t t-if="threadViewTopbar.isEditingGuestName">
                            <input class="o_ThreadViewTopbar_guestNameInput" type="text" t-att-value="messaging.currentGuest.name" t-on-input="threadViewTopbar.onInputGuestNameInput" t-on-keydown="threadViewTopbar.onKeyDownGuestNameInput" t-ref="guestNameInput"/>
                        </t>
                    </div>
                </t>
            </t>
        </div>
    </t>
</templates>
