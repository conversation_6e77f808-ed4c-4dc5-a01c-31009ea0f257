<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- Add a shortcut to favorites / tracks after registration -->
<template id="registration_complete" inherit_id="website_event.registration_complete">
    <xpath expr="//div[hasclass('row')][last()]" position="after">
        <div t-if="event.website_track" class="row mt-5 mb256">
            <div class="col-12">
                <h3>Book your seats to the best talks</h3>
                <p>Get prepared and
                    <a t-att-href="'/event/%s/track' % (slug(event))">register to your favorites talks now.</a>
                </p>
            </div>
        </div>
    </xpath>
</template>

<template id="layout" inherit_id="website_event.layout">
    <xpath expr='//t[@t-call="website.layout"]' position="inside">
        <t t-set="pageName" t-value="'event'"/>
        <t t-set="head">
            <t t-out="head"/>
            <t t-call="website_event_track.pwa_manifest"/>
        </t>
    </xpath>
</template>

<template id="index" inherit_id="website_event.index">
    <xpath expr='//t[@t-call="website.layout"]' position="inside">
        <t t-set="pageName" t-value="'event'"/>
        <t t-set="head">
            <t t-out="head"/>
            <t t-call="website_event_track.pwa_manifest"/>
        </t>
    </xpath>
</template>

<template id="pwa_manifest">
    <link rel="manifest" href="/event/manifest.webmanifest" crossorigin="use-credentials"/>
    <link rel="apple-touch-icon" t-att-href="website.image_url(website, 'app_icon', size='192x192')"/>
    <meta name="theme-color" content="#875A7B"/>
</template>

<template id="pwa_offline" inherit_id="website_event.index" primary="True">
    <xpath expr='//div[@id="wrap"]' position="replace">
        <div id="wrap" class="o_wevent_index">
            <div class="container">
                <div class="row">
                    <div class="col-12 card-body">
                        <div class="h2 mb-3">You're offline!</div>
                        <div class="alert alert-info text-center">
                            <span class="fa-stack fa-4x">
                                <i class="fa fa-wifi fa-stack-1x"></i>
                                <i class="fa fa-ban fa-stack-2x text-muted"></i>
                            </span>
                            <p>This page hasn't been saved for offline reading yet.<br/>Please check your network connection.</p>
                            <p>
                                <a t-att-href="url_for('/event')" class="btn btn-primary btn-block">Home page</a>
                                <button onclick="history.back();" class="btn btn-secondary btn-block">Previous page</button>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script type="text/javascript">
            window.addEventListener('online', function(e) {
                console.log('Go back online');
                location.reload();
            });
        </script>
    </xpath>
</template>

</odoo>
