# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * l10n_multilang
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 14:33+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account
msgid "Account"
msgstr "Счёт"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_chart_template
msgid "Account Chart Template"
msgstr "Шаблон плана счетов"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account_tag
msgid "Account Tag"
msgstr "Тег счёта"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_res_country_state__name
msgid ""
"Administrative divisions of a country. E.g. Fed. State, Departement, Canton"
msgstr ""
"Административно-территориальное деление страны. Например, штат, регион, "
"область"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_analytic_account
#: model:ir.model.fields,field_description:l10n_multilang.field_account_analytic_account__name
msgid "Analytic Account"
msgstr "Аналитический счёт"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_res_country_state
msgid "Country state"
msgstr "Область"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_template__description
msgid "Display on Invoices"
msgstr "Показать на счет-фактурах"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_fiscal_position
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position__name
msgid "Fiscal Position"
msgstr "Система налогов"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position_template__name
msgid "Fiscal Position Template"
msgstr "Шаблон системы налогов"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_base_language_install
msgid "Install Language"
msgstr "Установить язык"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_journal
msgid "Journal"
msgstr "Журнал"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_journal__name
msgid "Journal Name"
msgstr "Название журнала"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax__description
msgid "Label on Invoices"
msgstr "Обозначение на Счет-фактурах"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_account_fiscal_position__note
msgid "Legal mentions that have to be printed on the invoices."
msgstr "Юридическая заметка, которая должна отображаться на счет-фактуре. "

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account_tag__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account_template__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_chart_template__name
msgid "Name"
msgstr "Название"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position__note
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position_template__note
msgid "Notes"
msgstr "Заметки"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_chart_template__spoken_languages
msgid "Spoken Languages"
msgstr "Разговорные языки"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_res_country_state__name
msgid "State Name"
msgstr "Название Региона"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_account_chart_template__spoken_languages
msgid ""
"State here the languages for which the translations of templates could be "
"loaded at the time of installation of this localization module and copied in"
" the final object when generating them from templates. You must provide the "
"language codes separated by ';'"
msgstr ""
"Укажите здесь языки, для которых можно загрузить переводы шаблонов во время "
"установки этого модуля локализации и скопировать их в финальный объект при "
"генерировании из шаблонов. Вы должны предоставить языковые коды, разделенные"
" \";\""

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_tax
msgid "Tax"
msgstr "Налог"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_template__name
msgid "Tax Name"
msgstr "Наименование Налога"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Шаблон для системы налогов"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account_template
msgid "Templates for Accounts"
msgstr "Шаблоны для счетов"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Шаблоны для налогов"
