# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_account
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr ""

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_rtoken
msgid "Microsoft Refresh Token"
msgstr ""

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_microsoft_service
msgid "Microsoft Service"
msgstr ""

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token_validity
msgid "Microsoft Token Validity"
msgstr ""

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token
msgid "Microsoft User token"
msgstr ""

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_res_users
msgid "Users"
msgstr "İstifadəçilər"
