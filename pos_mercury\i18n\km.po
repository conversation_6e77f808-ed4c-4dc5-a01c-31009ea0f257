# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_mercury
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:04+0000\n"
"PO-Revision-Date: 2018-10-02 10:04+0000\n"
"Last-Translator: AN Souphorn <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:75
#, python-format
msgid "&nbsp;&nbsp;APPROVAL CODE:"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.pos_config_view_form_inherit_pos_mercury
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Buy a card reader"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"<i>Mercury Configurations</i> define what Mercury account will be used when\n"
"                                processing credit card transactions in the Point Of Sale. Setting up a Mercury\n"
"                                configuration will enable you to allow payments with various credit cards\n"
"                                (eg. Visa, MasterCard, Discovery, American Express, ...). After setting up this\n"
"                                configuration you should associate it with a Point Of Sale payment method."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:46
#, python-format
msgid "ABOVE AMOUNT PURSUANT"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:60
#, python-format
msgid "APPROVAL CODE:"
msgstr ""

#. module: pos_mercury
#: selection:barcode.rule,type:0
msgid "Alias"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_ir_autovacuum
msgid "Automatic Vacuum"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_barcode_rule
msgid "Barcode Rule"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:45
#, python-format
msgid "CARDHOLDER WILL PAY CARD ISSUER"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line__mercury_card_brand
msgid "Card Brand"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line__mercury_card_number
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_order
msgid "Card Number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line__mercury_prefixed_card_number
msgid "Card Number Prefix"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line__mercury_card_owner_name
msgid "Card Owner Name"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_tree
msgid "Card Reader"
msgstr ""

#. module: pos_mercury
#: selection:barcode.rule,type:0
msgid "Cashier"
msgstr ""

#. module: pos_mercury
#: selection:barcode.rule,type:0
msgid "Client"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.actions.act_window,help:pos_mercury.action_configuration_form
msgid "Configure your card reader"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:360
#, python-format
msgid "Could not read card"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: pos_mercury
#: selection:barcode.rule,type:0
msgid "Credit Card"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:346
#, python-format
msgid ""
"Credit card refunds are not supported. Instead select your credit card "
"payment method, click 'Validate' and refund the original charge manually "
"through the Vantiv backend."
msgstr ""

#. module: pos_mercury
#: selection:barcode.rule,type:0
msgid "Discounted Product"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__display_name
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:651
#, python-format
msgid "Error"
msgstr "កំហុស"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"For quickly handling orders: just swiping a credit card when on the payment screen\n"
"                                (without having pressed anything else) will charge the full amount of the order to\n"
"                                the card."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:256
#, python-format
msgid "Go to payment screen to use cards"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:396
#, python-format
msgid "Handling transaction..."
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__id
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__id
msgid "ID"
msgstr "ID"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "ID of the merchant to authenticate him on the payment provider server"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"If you don't already have a Mercury account, contact Mercury at +1 (800) 846-4472\n"
"                                to create one."
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line__mercury_invoice_no
msgid "Invoice number from Mercury Pay"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_account_journal
msgid "Journal"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration____last_update
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: pos_mercury
#: selection:barcode.rule,type:0
msgid "Location"
msgstr ""

#. module: pos_mercury
#: selection:barcode.rule,type:0
msgid "Lot"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "Merchant ID"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid "Merchant Password"
msgstr ""

#. module: pos_mercury
#: model:ir.actions.act_window,name:pos_mercury.action_configuration_form
#: model:ir.ui.menu,name:pos_mercury.menu_pos_pos_mercury_config
msgid "Mercury Configurations"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_journal__pos_mercury_config_id
msgid "Mercury Credentials"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line__mercury_invoice_no
msgid "Mercury invoice number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line__mercury_record_no
msgid "Mercury record number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line__mercury_ref_no
msgid "Mercury reference number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__name
msgid "Name"
msgstr "ឈ្មោះ"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__name
msgid "Name of this Mercury configuration"
msgstr ""

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:34
#, python-format
msgid "No Mercury configuration associated with the journal."
msgstr ""

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:22
#, python-format
msgid "No opened point of sale session for user %s found."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:328
#, python-format
msgid "No response from Mercury (Mercury down?)"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:330
#, python-format
msgid "No response from server (connected to network?)"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:423
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:577
#, python-format
msgid "Odoo error while processing transaction."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:652
#, python-format
msgid "One credit card swipe already pending."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:7
#, python-format
msgid "Online Payment"
msgstr ""

#. module: pos_mercury
#: selection:barcode.rule,type:0
msgid "Package"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:470
#, python-format
msgid "Partially approved"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid ""
"Password of the merchant to authenticate him on the payment provider server"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line__mercury_record_no
msgid "Payment record number from Mercury Pay"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line__mercury_ref_no
msgid "Payment reference number from Mercury Pay"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:416
#, python-format
msgid "Please setup your Mercury merchant account."
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_configuration
msgid "Point of Sale Mercury Configuration"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_mercury_transaction
msgid "Point of Sale Mercury Transaction"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_order
msgid "Point of Sale Orders"
msgstr ""

#. module: pos_mercury
#: selection:barcode.rule,type:0
msgid "Priced Product"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:345
#, python-format
msgid "Refunds not supported"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:549
#, python-format
msgid "Reversal failed, sending VoidSale..."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:591
#, python-format
msgid "Reversal succeeded"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:552
#, python-format
msgid "Sending reversal..."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:47
#, python-format
msgid "TO CARDHOLDER AGREEMENT"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line__mercury_card_brand
msgid "The brand of the payment card (e.g. Visa, AMEX, ...)"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line__mercury_prefixed_card_number
msgid "The card number used for the payment."
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_journal__pos_mercury_config_id
msgid "The configuration of Mercury used for this journal"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line__mercury_card_number
msgid "The last 4 numbers of the card used to pay"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line__mercury_card_owner_name
msgid "The name of the card owner"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:361
#, python-format
msgid ""
"This can be caused by a badly executed swipe or by not having your keyboard "
"layout set to US QWERTY (not US International)."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:146
#, python-format
msgid "Transaction approved"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_barcode_rule__type
msgid "Type"
msgstr "ប្រភេទ"

#. module: pos_mercury
#: selection:barcode.rule,type:0
msgid "Unit Product"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"Using the Mercury integration in the Point Of Sale is easy: just press the\n"
"                                associated payment method. After that the amount can be adjusted (eg. for cashback)\n"
"                                just like on any other payment line. Whenever the payment line is set up, a card\n"
"                                can be swiped through the card reader device."
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.pos_config_view_form_inherit_pos_mercury
msgid "Vantiv Accounts"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:599
#, python-format
msgid "VoidSale succeeded"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:17
#, python-format
msgid "WAITING FOR SWIPE"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"We currently support the MagTek Dynamag card reader device. It can be connected\n"
"                                directly to the Point Of Sale device or it can be connected to the IoTBox."
msgstr ""

#. module: pos_mercury
#: selection:barcode.rule,type:0
msgid "Weighted Product"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:50
#, python-format
msgid "X______________________________"
msgstr ""
