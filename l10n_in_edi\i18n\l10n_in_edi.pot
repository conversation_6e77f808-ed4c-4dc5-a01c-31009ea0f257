# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_in_edi
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-06 14:12+0000\n"
"PO-Revision-Date: 2023-01-06 14:12+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"\n"
"- City required min 3 and max 100 characters"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"\n"
"- Email address should be valid and not more then 100 characters"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"\n"
"- Mobile number should be minimum 10 or maximum 12 digits"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"\n"
"- State required min 3 and max 50 characters"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"\n"
"- Street required min 3 and max 100 characters"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"\n"
"- Street2 should be min 3 and max 100 characters"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"\n"
"- Zip code required 6 digits"
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.res_config_settings_view_form_inherit_l10n_in_edi
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.l10n_in_einvoice_report_invoice_document_inherit
msgid "<strong>Ack. Date:</strong>"
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.l10n_in_einvoice_report_invoice_document_inherit
msgid "<strong>Ack. No:</strong>"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid "Buy Credits"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,field_description:l10n_in_edi.field_account_bank_statement_line__l10n_in_edi_cancel_reason
#: model:ir.model.fields,field_description:l10n_in_edi.field_account_move__l10n_in_edi_cancel_reason
#: model:ir.model.fields,field_description:l10n_in_edi.field_account_payment__l10n_in_edi_cancel_reason
msgid "Cancel reason"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,field_description:l10n_in_edi.field_account_bank_statement_line__l10n_in_edi_cancel_remarks
#: model:ir.model.fields,field_description:l10n_in_edi.field_account_move__l10n_in_edi_cancel_remarks
#: model:ir.model.fields,field_description:l10n_in_edi.field_account_payment__l10n_in_edi_cancel_remarks
msgid "Cancel remarks"
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.res_config_settings_view_form_inherit_l10n_in_edi
msgid "Check the"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model,name:l10n_in_edi.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model,name:l10n_in_edi.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields.selection,name:l10n_in_edi.selection__account_move__l10n_in_edi_cancel_reason__2
msgid "Data Entry Mistake"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields.selection,name:l10n_in_edi.selection__account_move__l10n_in_edi_cancel_reason__1
msgid "Duplicate"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,field_description:l10n_in_edi.field_res_company__l10n_in_edi_production_env
msgid "E-invoice (IN) Is production OSE environment"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,field_description:l10n_in_edi.field_res_company__l10n_in_edi_password
msgid "E-invoice (IN) Password"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,field_description:l10n_in_edi.field_res_company__l10n_in_edi_token
msgid "E-invoice (IN) Token"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,field_description:l10n_in_edi.field_res_company__l10n_in_edi_username
msgid "E-invoice (IN) Username"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,field_description:l10n_in_edi.field_res_company__l10n_in_edi_token_validity
msgid "E-invoice (IN) Valid Until"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,field_description:l10n_in_edi.field_account_bank_statement_line__l10n_in_edi_show_cancel
#: model:ir.model.fields,field_description:l10n_in_edi.field_account_move__l10n_in_edi_show_cancel
#: model:ir.model.fields,field_description:l10n_in_edi.field_account_payment__l10n_in_edi_show_cancel
msgid "E-invoice(IN) is sent?"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model,name:l10n_in_edi.model_account_edi_format
msgid "EDI format"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,help:l10n_in_edi.field_res_company__l10n_in_edi_production_env
#: model:ir.model.fields,help:l10n_in_edi.field_res_config_settings__l10n_in_edi_production_env
msgid "Enable the use of production credentials"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid "Ensure GST Number set on company setting and API are Verified."
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid "HSN code is not set in product %s"
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.external_layout_bold_inherit_l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.external_layout_boxed_inherit_l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.external_layout_standard_inherit_l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.external_layout_striped_inherit_l10n_in_edi
msgid "IRN:"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/res_config_settings.py:0
#, python-format
msgid ""
"Incorrect username or password, or the GST number on company does not match."
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,field_description:l10n_in_edi.field_res_config_settings__l10n_in_edi_production_env
msgid "Indian EDI Testing Environment"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,field_description:l10n_in_edi.field_res_config_settings__l10n_in_edi_password
msgid "Indian EDI password"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields,field_description:l10n_in_edi.field_res_config_settings__l10n_in_edi_username
msgid "Indian EDI username"
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.res_config_settings_view_form_inherit_l10n_in_edi
msgid "Indian Electronic Invoicing"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid "Invalid HSN Code (%s) in product %s"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid "Invoice number should not be more than 16 characters"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model,name:l10n_in_edi.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields.selection,name:l10n_in_edi.selection__account_move__l10n_in_edi_cancel_reason__3
msgid "Order Cancelled"
msgstr ""

#. module: l10n_in_edi
#: model:ir.model.fields.selection,name:l10n_in_edi.selection__account_move__l10n_in_edi_cancel_reason__4
msgid "Others"
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.res_config_settings_view_form_inherit_l10n_in_edi
msgid "Password"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid "Please buy more credits and retry: "
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.res_config_settings_view_form_inherit_l10n_in_edi
msgid "Production Environment"
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.res_config_settings_view_form_inherit_l10n_in_edi
msgid "Setup E-invoice"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"Somehow this invoice had been cancelled to government before.<br/>Normally, "
"this should not happen too often<br/>Just verify by logging into government "
"website <a href='https://einvoice1.gst.gov.in'>here<a>."
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"Somehow this invoice had been submited to government before.<br/>Normally, "
"this should not happen too often<br/>Just verify value of invoice by uploade"
" json to government website <a "
"href='https://einvoice1.gst.gov.in/Others/VSignedInvoice'>here<a>."
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_move.py:0
#, python-format
msgid ""
"To cancel E-invoice set cancel reason and remarks at Other info tab in invoices: \n"
"%s"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid ""
"Unable to connect to the online E-invoice service.The web service may be "
"temporary down. Please try again in a moment."
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.res_config_settings_view_form_inherit_l10n_in_edi
msgid "Username"
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.res_config_settings_view_form_inherit_l10n_in_edi
msgid "Verify Username and Password"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid "You have insufficient credits to send this document!"
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.res_config_settings_view_form_inherit_l10n_in_edi
msgid "documentation"
msgstr ""

#. module: l10n_in_edi
#: code:addons/l10n_in_edi/models/account_edi_format.py:0
#, python-format
msgid "product is required to get HSN code"
msgstr ""

#. module: l10n_in_edi
#: model_terms:ir.ui.view,arch_db:l10n_in_edi.res_config_settings_view_form_inherit_l10n_in_edi
msgid "to get credentials"
msgstr ""
