# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_comparison
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON>or <PERSON>, 2023\n"
"Language-Team: Bengali (https://app.transifex.com/odoo/teams/41243/bn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_8
msgid "134.7 x 200 x 7.2 mm"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_7
msgid "308 g"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/>"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.recommended_product
msgid "<i class=\"fa fa-exchange\"/> Compare"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<i class=\"fa fa-shopping-cart mr-2\"/>Add to Cart"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "<i class=\"fa fa-trash\" role=\"img\" aria-label=\"Remove\"/>"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.add_to_compare_button
msgid "<span class=\"fa fa-exchange mr-2\"/>Add to compare"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.recommended_product
msgid "<span class=\"h3\">Suggested alternatives: </span>"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>Price:</strong>"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>x</strong>"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_1
msgid "Apple"
msgstr ""

#. module: website_sale_comparison
#: model:ir.actions.act_window,name:website_sale_comparison.product_attribute_category_action
#: model:ir.ui.menu,name:website_sale_comparison.menu_attribute_category_action
msgid "Attribute Categories"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute__category_id
msgid "Category"
msgstr "শ্রেণী"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__name
msgid "Category Name"
msgstr "বিভাগের নাম"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.add_to_compare_button
#, python-format
msgid "Compare"
msgstr ""

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/js/website_sale_comparison.js:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#, python-format
msgid "Compare Products"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_uid
msgid "Created by"
msgstr "দ্বারা সৃষ্টি"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_date
msgid "Created on"
msgstr "তৈরি"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_8
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_2
msgid "Dimensions"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__display_name
msgid "Display Name"
msgstr "প্রদর্শন নাম"

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_duration
msgid "Duration"
msgstr ""

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_general_features
msgid "General Features"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__id
msgid "ID"
msgstr "আইডি "

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category____last_update
msgid "Last Modified on"
msgstr "সর্বশেষ সংশোধিত"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_uid
msgid "Last Updated by"
msgstr "সর্বশেষ আপডেট করেছেন"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_date
msgid "Last Updated on"
msgstr "সর্বশেষ আপডেট হয়েছে"

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: model:ir.model,name:website_sale_comparison.model_product_product
#, python-format
msgid "Product"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute
msgid "Product Attribute"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute_category
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attribute_category_tree_view
msgid "Product Attribute Category"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Product image"
msgstr ""

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__attribute_ids
msgid "Related Attributes"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Remove"
msgstr "সরিয়ে ফেলুন"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__sequence
msgid "Sequence"
msgstr "ক্রম"

#. module: website_sale_comparison
#: model:ir.model.fields,help:website_sale_comparison.field_product_attribute__category_id
msgid ""
"Set a category to regroup similar attributes under the same section in the "
"Comparison page of eCommerce"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Shop Comparator"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "Specifications"
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Uncategorized"
msgstr ""

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#, python-format
msgid "Warning"
msgstr "সতর্কীকরণ"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_7
msgid "Weight"
msgstr ""

#. module: website_sale_comparison
#. openerp-web
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#, python-format
msgid "You can compare max 4 products."
msgstr ""

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "or"
msgstr ""
