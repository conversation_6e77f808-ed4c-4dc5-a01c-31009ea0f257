# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> Thaidev <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2021\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed_slides_count
msgid "# Completed Slides"
msgstr "# สไลด์ที่เสร็จสมบูรณ์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__count_views
msgid "# Views"
msgstr "# ยอดเข้าชม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__public_views
msgid "# of Public Views"
msgstr "# การเข้าชมสาธารณะ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_views
msgid "# of Website Views"
msgstr "# ยอดเข้าชมเว็บไซต์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completion
msgid "% Completed Slides"
msgstr "% สไลด์ที่เสร็จสมบูรณ์"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "'. Showing results for '"
msgstr "กำลังแสดงผลลัพธ์สำหรับ "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_slides_list.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "(empty)"
msgstr "(ว่างเปล่า)"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ". This way, they will be secured."
msgstr "ด้วยวิธีนี้พวกเขาจะปลอดภัย"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_5
msgid "3 Main Methodologies"
msgstr "3 วิธีการหลัก"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "<b>%s</b> is requesting access to this course."
msgstr "<b>%s</b>กำลังร้องขอการเข้าถึงคอร์สนี้"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<b>(empty)</b>"
msgstr "<b>(เว้นว่าง)</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<b>Order by</b>"
msgstr "<b>เรียงลำดับโดย</b>"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"<b>Save & Publish</b> your lesson to make it available to your attendees."
msgstr ""
"<b>บันทึกและเผยแพร่</b> บทเรียนของคุณเพื่อให้ผู้เข้าร่วมสามารถใช้งานได้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "<b>Save</b> your question."
msgstr "<b>บันทึก</b> คำถามของคุณ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<b>Uncategorized</b>"
msgstr "<b>ไม่ได้จัดหมวดหมู่</b>"

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        You have been invited to join a new course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        สวัสดี<br/><br/>\n"
"        คุณได้รับเชิญให้เข้าร่วมหลักสูตรใหม่: <t t-out=\"object.channel_id.name or ''\">การจัดสวนพื้นฐาน</t>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_completed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p><br/>\n"
"                        <p><b>Congratulations!</b></p>\n"
"                        <p>You've completed the course <b t-out=\"object.channel_id.name or ''\">Basics of Gardening</b></p>\n"
"                        <p>Check out the other available courses.</p><br/>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Explore courses\n"
"                            </a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"object.channel_id.user_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">สวัสดี <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p><br/>\n"
"                        <p><b>ขอแสดงความยินดีด้วย!</b></p>\n"
"                        <p>คุณได้สำเร็จคอร์ส <b t-out=\"object.channel_id.name or ''\">การจัดสวนพื้นฐาน</b></p>\n"
"                        <p>ตรวจสอบคอร์สอื่น ๆ ที่มีอยู่</p><br/>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                สำรวจคอร์สต่าง ๆ \n"
"                            </a>\n"
"                        </div>\n"
"                       สนุกไปกับเนื้อหาสุดพิเศษ\n"
"                        <t t-if=\"object.channel_id.user_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\">--<br/>แอดมิน Mitchell </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <t t-out=\"object.slide_type or ''\">document</t> <strong t-out=\"object.name or ''\">Trees</strong> with you!\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View <strong t-out=\"object.name or ''\">Trees</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        สวัสดี<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">แอดมิน Mitchell</t> ได้แชร์ <t t-out=\"object.slide_type or ''\">เอกสาร</t> <strong t-out=\"object.name or ''\">ต้นไม้</strong>กับคุณ!\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">ดู <strong t-out=\"object.name or ''\">ต้นไม้</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>แอดมิน Mitchell</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_published
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        There is something new in the course <strong t-out=\"object.channel_id.name or ''\">Trees, Wood and Gardens</strong> you are following:<br/><br/>\n"
"                        <center><strong t-out=\"object.name or ''\">Trees</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View content</a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        สวัสดี<br/><br/>\n"
"                        มีอะไรใหม่ในคอร์สนี้<strong t-out=\"object.channel_id.name or ''\">ต้นไม้ ไม้ และสวน</strong> คุณกำลังติดตาม:<br/><br/>\n"
"                        <center><strong t-out=\"object.name or ''\">ต้นไม้</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">ดูเนื้อหา</a>\n"
"                        </div>\n"
"                        สนุกไปกับเนื้อหาสุดพิเศษ!\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\">--<br/>แอดมิน Mitchell</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"fa fa-arrow-right mr-1\"/>All Courses"
msgstr "<i class=\"fa fa-arrow-right mr-1\"/>คอร์สทั้งหมด"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-bar-chart\"/> Statistics"
msgstr "<i class=\"fa fa-bar-chart\"/> สถิติ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Lessons</span>"
msgstr ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ml-1\">บทเรียน</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-chevron-left mr-2\"/> <span class=\"d-none d-sm-inline-"
"block\">Prev</span>"
msgstr ""
"<i class=\"fa fa-chevron-left mr-2\"/> <span class=\"d-none d-sm-inline-"
"block\">ก่อน</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-circle-o-notch fa-spin mr-2\"/><b>Loading...</b>"
msgstr "<i class=\"fa fa-circle-o-notch fa-spin mr-2\"/><b>กำลังโหลด...</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-clock-o mr-2\" aria-label=\"Create date\"/>"
msgstr "<i class=\"fa fa-clock-o mr-2\" aria-label=\"วันที่สร้าง\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-clock-o mr-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"
msgstr ""
"<i class=\"fa fa-clock-o mr-2\" aria-label=\"ระยะเวลา\" role=\"img\" "
"title=\"Duration\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-cloud-upload mr-1\"/>Upload new content"
msgstr "<i class=\"fa fa-cloud-upload mr-1\"/>อัปโหลดเนื้อหาใหม่"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-code\"/> Embed"
msgstr "<i class=\"fa fa-code\"/> ฝัง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-comments\"/> Comments ("
msgstr "<i class=\"fa fa-comments\"/> ความคิดเห็น ("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-desktop mr-2\"/>\n"
"                <span class=\"d-none d-sm-inline-block\">Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-desktop mr-2\"/>\n"
"                <span class=\"d-none d-sm-inline-block\">เต็มจอ</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-envelope\"/> Email"
msgstr "<i class=\"fa fa-envelope\"/> อีเมล"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<i class=\"fa fa-envelope\"/> Send Email"
msgstr "<i class=\"fa fa-envelope\"/>ส่งอีเมล"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-eraser mr-1\"/>Clear filters"
msgstr "<i class=\"fa fa-eraser mr-1\"/>ล้างตัวกรอง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "<i class=\"fa fa-eraser\"/> Clear filters"
msgstr "<i class=\"fa fa-eraser\"/> ล้างตัวกรอง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> This document is private."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-label=\"ความสนใจ\""
" title=\"ความสนใจ\"/> เอกสารนี้เป็นส่วนตัว"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-eye mr-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye mr-2\" aria-label=\"ยอดเข้าชม\" role=\"img\" title=\"Views\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-code-o mr-2\" aria-label=\"Webpage\" role=\"img\" "
"title=\"Webpage\"/>"
msgstr ""
"<i class=\"fa fa-file-code-o mr-2\" aria-label=\"เว็บเพจ\" role=\"img\" "
"title=\"Webpage\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-image-o mr-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"
msgstr ""
"<i class=\"fa fa-file-image-o mr-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"อินโฟกราฟิกส์\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-pdf-o mr-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"
msgstr ""
"<i class=\"fa fa-file-pdf-o mr-2\" aria-label=\"เอกสาร\" role=\"img\" "
"title=\"เอกสาร\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-video-o mr-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"
msgstr ""
"<i class=\"fa fa-file-video-o mr-2\" aria-label=\"วิดีโอ\" role=\"img\" "
"title=\"Video\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-flag mr-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"
msgstr "<i class=\"fa fa-flag mr-2\" aria-label=\"ควิซ\" role=\"img\" title=\"Quiz\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<i class=\"fa fa-flag text-warning\"/> Quiz"
msgstr "<i class=\"fa fa-flag text-warning\"/> ควิซ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen_sidebar_category
msgid "<i class=\"fa fa-flag-checkered text-warning mr-2\"/>Quiz"
msgstr "<i class=\"fa fa-flag-checkered text-warning mr-2\"/>ควิซ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-folder mr-2\" aria-label=\"Open folder\"/>"
msgstr "<i class=\"fa fa-folder mr-2\" aria-label=\"เปิดโฟลเดอร์\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-folder-o mr-1\"/><span>Add Section</span>"
msgstr "<i class=\"fa fa-folder-o mr-1\"/><span>เพิ่มส่วน</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-folder-o mr-1\"/>Add a section"
msgstr "<i class=\"fa fa-folder-o mr-1\"/>เพิ่มส่วน"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "<i class=\"fa fa-graduation-cap mr-1\"/>All courses"
msgstr "<i class=\"fa fa-graduation-cap mr-1\"/>คอร์สทั้งหมด"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-home\"/> About"
msgstr "<i class=\"fa fa-home\"/> เกี่ยวกับ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-home\"/> Course"
msgstr "<i class=\"fa fa-home\"/> คอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block ml-1\">Back "
"to course</span>"
msgstr ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block "
"ml-1\">กลับไปที่คอร์ส</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-info-circle\"/>\n"
"                    The social sharing module will be unlocked when a moderator will allow your publication."
msgstr ""
"<i class=\"fa fa-info-circle\"/>\n"
"                    โมดูลการแชร์ทางสืาอสังคมจะถูกปลดล็อกเมื่อผู้ดูแลอนุญาตให้สิ่งพิมพ์ของคุณ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<i class=\"fa fa-plus mr-1\"/> <span class=\"d-none d-md-inline-block\">Add "
"Content</span>"
msgstr ""
"<i class=\"fa fa-plus mr-1\"/> <span class=\"d-none d-md-inline-"
"block\">เพิ่มเนื้อหา</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-plus mr-1\"/><span>Add Content</span>"
msgstr "<i class=\"fa fa-plus mr-1\"/><span>เพิ่มเนื้อหา</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>Add Question</span>"
msgstr ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>เพิ่มคำถาม</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>Add Quiz</span>"
msgstr ""
"<i class=\"fa fa-plus mr-2\"/>\n"
"            <span>เพิ่มควิซ</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-question mr-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"
msgstr ""
"<i class=\"fa fa-question mr-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"จำนวนคำถาม\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-share-alt\"/> Share"
msgstr "<i class=\"fa fa-share-alt\"/>แชร์"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-share-alt\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt\"/><span class=\"d-none d-md-inline-block "
"ml-1\">แชร์</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "<i class=\"fa fa-share-square fa-fw\"/> Share"
msgstr "<i class=\"fa fa-share-square fa-fw\"/> แชร์"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-share-square\"/> Share"
msgstr "<i class=\"fa fa-share-square\"/> แชร์"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ml-1\">Exit Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ml-1\">ออกจากโหมดเต็มจอ</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star text-black-25\" aria-label=\"A star\"/>"
msgstr "<i class=\"fa fa-star text-black-25\" aria-label=\"ดาว\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star\" aria-label=\"A star\" role=\"img\"/>"
msgstr "<i class=\"fa fa-star\" aria-label=\"ดาว\" role=\"img\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_kanban_slide_channel
msgid "<i class=\"fa fa-star-half-o\" aria-label=\"Half a star\" role=\"img\"/>"
msgstr "<i class=\"fa fa-star-half-o\" aria-label=\"ครึ่งดาว\" role=\"img\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid ""
"<i class=\"fa fa-tag mr-2 text-muted\"/>\n"
"                      My Courses"
msgstr ""
"<i class=\"fa fa-tag mr-2 text-muted\"/>\n"
"                      คอร์สของฉัน"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-thumbs-down fa-1x\" role=\"img\" aria-label=\"Dislikes\" "
"title=\"Dislikes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down fa-1x\" role=\"img\" aria-label=\"่ไม่ชอบ\" "
"title=\"ไม่ชอบ\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-thumbs-up fa-1x\" role=\"img\" aria-label=\"Likes\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-up fa-1x\" role=\"img\" aria-label=\"ชอบ\" title=\"Likes\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid ""
"<small class=\"text-success\">\n"
"                                Request already sent\n"
"                            </small>"
msgstr ""
"<small class=\"text-success\">\n"
"                                ส่งคำขอเรียบร้อยแล้ว\n"
"                            </small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid ""
"<small><span class=\"badge badge-pill badge-success pull-right my-1 py-1 "
"px-2 font-weight-normal\"><i class=\"fa fa-check\"/> "
"Completed</span></small>"
msgstr ""
"<small><span class=\"badge badge-pill badge-success pull-right my-1 py-1 "
"px-2 font-weight-normal\"><i class=\"fa fa-check\"/> "
"เสร็จสิ้น</span></small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<span class=\"badge badge-danger\">Unpublished</span>"
msgstr "<span class=\"badge badge-danger\">ไม่ได้เผยแพร่</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge badge-info badge-arrow-right font-weight-normal px-2 "
"py-1 m-1\">New</span>"
msgstr ""
"<span class=\"badge badge-info badge-arrow-right font-weight-normal px-2 "
"py-1 m-1\">ใหม่</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<span class=\"badge badge-info\">Preview</span>"
msgstr "<span class=\"badge badge-info\">ดูตัวอย่าง</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge badge-light badge-hide border font-weight-normal px-2 "
"py-1 m-1\">Add Quiz</span>"
msgstr ""
"<span class=\"badge badge-light badge-hide border font-weight-normal px-2 "
"py-1 m-1\">เพิ่มควิซ</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid ""
"<span class=\"badge badge-pill badge-success pull-right py-1 px-2\"><i "
"class=\"fa fa-check\"/> Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success pull-right py-1 px-2\"><i "
"class=\"fa fa-check\"/> เสร็จสิ้น</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid ""
"<span class=\"badge badge-pill badge-success py-1 px-2 mx-auto\" "
"style=\"font-size: 1em\"><i class=\"fa fa-check\"/> Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success py-1 px-2 mx-auto\" "
"style=\"font-size: 1em\"><i class=\"fa fa-check\"/>  เสร็จสิ้น</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<span class=\"badge badge-pill badge-success py-1 px-2\" style=\"font-size: "
"1em\"><i class=\"fa fa-check\"/> Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success py-1 px-2\" style=\"font-size: "
"1em\"><i class=\"fa fa-check\"/>  เสร็จสิ้น</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-check\"/> "
"Completed</span>"
msgstr ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-check\"/>  "
"เสร็จสิ้น</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\"><span>Preview</span></span>"
msgstr ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\"><span>ดูตัวอย่าง</span></span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\">Preview</span>"
msgstr ""
"<span class=\"badge badge-success font-weight-normal px-2 py-1 "
"m-1\">ดูตัวอย่าง</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge font-weight-bold px-2 py-1 m-1 badge-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"
msgstr ""
"<span class=\"badge font-weight-bold px-2 py-1 m-1 badge-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"d-none d-sm-inline-block\">Next</span> <i class=\"fa fa-"
"chevron-right ml-2\"/>"
msgstr ""
"<span class=\"d-none d-sm-inline-block\">ต่อไป</span> <i class=\"fa fa-"
"chevron-right ml-2\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-arrow-right\"/>\n"
"                                    Create a Google Project and Get a Key"
msgstr ""
"<span class=\"fa fa-arrow-right\"/>\n"
"                                    สร้างโครงการ Google และรับคีย์"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
msgid "<span class=\"fa fa-clipboard\"> Copy Text</span>"
msgstr "<span class=\"fa fa-clipboard\">คัดลอกข้อความ</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "<span class=\"font-weight-bold text-muted mr-2\">Current rank:</span>"
msgstr "<span class=\"font-weight-bold text-muted mr-2\">อันดับปัจจุบัน:</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "<span class=\"font-weight-normal\">Last update:</span>"
msgstr "<span class=\"font-weight-normal\">อัปเดตล่าสุด:</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid ""
"<span class=\"form-text text-muted d-block w-100\">Send presentation through"
" email</span>"
msgstr ""
"<span class=\"form-text text-muted d-block "
"w-100\">ส่งงานนำเสนอทางอีเมล</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Slides</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">สไลด์</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะเว็บไซต์\" groups=\"website.group_multi_website\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_partner_view_form
msgid "<span class=\"o_stat_text\">Courses</span>"
msgstr "<span class=\"o_stat_text\">คอร์ส</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card_information_arrow
msgid "<span class=\"o_wslides_arrow\">New Content</span>"
msgstr "<span class=\"o_wslides_arrow\">เนื้อหาใหม่</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training
msgid "<span class=\"p-2\">Course content</span>"
msgstr "<span class=\"p-2\">เนื้อหาคอร์ส</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "<span class=\"text-500 mx-2\">•</span>"
msgstr "<span class=\"text-500 mx-2\">•</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted font-weight-bold mr-3\">Rating</span>"
msgstr "<span class=\"text-muted font-weight-bold mr-3\">การให้คะแนน</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Attendees</span>"
msgstr "<span class=\"text-muted\">ผู้เข้าร่วม</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Common tasks for a computer scientist</span>"
msgstr ""
"<span class=\"text-muted\">งานทั่วไปสำหรับนักวิทยาศาสตร์คอมพิวเตอร์</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Contents</span>"
msgstr "<span class=\"text-muted\">เนื้อหา</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Parts of computer science</span>"
msgstr "<span class=\"text-muted\">ส่วนของวิทยาการคอมพิวเตอร์</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"first\" class=\"mr-1 mr-sm-2\" title=\"First slide\" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"Previous slide\" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"Next slide\" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"
msgstr ""
"<span id=\"first\" class=\"mr-1 mr-sm-2\" title=\"สไลด์แรก\" aria-label=\"First slide\" role=\"ปุ่ม\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"สไลด์ก่อนหน้า\" aria-label=\"Previous slide\" role=\"ปุ่ม\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"สไลด์ต่อไป\" aria-label=\"สไลด์ต่อไป\" role=\"ปุ่ม\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"สไลด์ล่าสุด\" role=\"ปุ่ม\"><i class=\"fa fa-step-forward\"/></span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"fullscreen\" class=\"ml-1 ml-sm-2\" title=\"View fullscreen\" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"fullscreen\" class=\"ml-1 ml-sm-2\" title=\"ดูเต็มจอ\" aria-label=\"เต็มจอ\" role=\"ปุ่ม\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"zoomout\" class=\"d-none d-sm-inline ml-2 mr-2\" title=\"Zoom out\" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-none d-sm-inline\" title=\"Zoom in\" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"zoomout\" class=\"d-none d-sm-inline ml-2 mr-2\" title=\"ขยายออก\" aria-label=\"Zoom out\" role=\"ปุ่ม\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-none d-sm-inline\" title=\"ขยายเข้า\" aria-label=\"Zoom in\" role=\"ปุ่ม\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span name=\"done_members_count_label\" class=\"text-muted\">Finished</span>"
msgstr "<span name=\"done_members_count_label\" class=\"text-muted\">เสร็จสิ้น</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "<span name=\"members_done_count_label\" class=\"o_stat_text\">Finished</span>"
msgstr ""
"<span name=\"members_done_count_label\" "
"class=\"o_stat_text\">เสร็จสิ้น</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span> hours</span>"
msgstr "<span>ชั่วโมง</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<span>Add Tag</span>"
msgstr "<span>เพิ่มแท็ก</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Answering Questions</span>"
msgstr "<span>ตอบคำถาม</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking Question</span>"
msgstr "<span>ตอบคำถาม</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking the right question</span>"
msgstr "<span>ตอบคำถามที่ถูกต้อง</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Logic</span>"
msgstr "<span>ครรกวิทยา</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Mathematics</span>"
msgstr "<span>เลข</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "<span>Preview</span>"
msgstr "<span>ดูตัวอย่าง</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Science</span>"
msgstr "<span>วิทยาศาสตร์</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>XP</span>"
msgstr "<span>XP</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_fullscreen_player.js:0
#: code:addons/website_slides/static/src/js/slides_share.js:0
#, python-format
msgid "<strong>Thank you!</strong> Mail has been sent."
msgstr "<strong>ขอบคุณ!</strong> เมลถูกส่งแล้ว"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_1
msgid "A Mighty Forest from Ages"
msgstr "ป่าอันยิ่งใหญ่ตั้งแต่ยุคโบราณ"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_0
msgid "A fruit"
msgstr "ผลไม้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"A good course has a structure. Pick a name for your first section and click "
"<b>Save</b> to create it."
msgstr ""
"คอร์สที่ดีต้องมีโครงสร้าง เลือกชื่อสำหรับส่วนแรกของคุณแล้วคลิก<b>บันทึก</b> "
"เพื่อสร้าง"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_4
msgid "A little chat with Harry Potted"
msgstr "คุยกับ Harry Potted นิดหน่อย"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_2_gard2
msgid ""
"A lot of nice documentation: trees, wood, gardens. A gold mine for "
"references."
msgstr "เอกสารดี ๆ มากมาย: ต้นไม้ ไม้ สวน ขุมสมบัติสำหรับการอ้างอิง"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_0
msgid "A shovel"
msgstr "พลั่ว"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_exclusion_html_content_and_url
msgid ""
"A slide is either filled with a document url or HTML content. Not both."
msgstr "สไลด์เต็มไปด้วย URL เอกสาร หรือ เนื้อหา HTML ซึ่งไม่ใช่ทั้งสองอย่าง"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_1
msgid "A spoon"
msgstr "ช้อน"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_5
msgid "A summary of know-how: how and what."
msgstr "สรุปความรู้: อะไรและอย่างไร"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_0
msgid ""
"A summary of know-how: how and what. All the basics for this course about "
"gardening."
msgstr ""
"สรุปความรู้: อย่างไรและอะไร พื้นฐานทั้งหมดสำหรับหลักสูตรนี้เกี่ยวกับการทำสวน"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_0
msgid ""
"A summary of know-how: what are the main trees categories and how to "
"differentiate them."
msgstr "สรุปความรู้: หมวดหมู่ต้นไม้หลักคืออะไรและจะแยกความแตกต่างได้อย่างไร"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_2
msgid "A table"
msgstr "โต๊ะ"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_tag_slide_tag_unique
msgid "A tag must be unique!"
msgstr "แท็กต้องไม่ซ้ำกัน!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_1
msgid "A vegetable"
msgstr "ผัก"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "API Key"
msgstr "API Key"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Access Granted"
msgstr "ได้รับการเข้าถึง"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_groups
msgid "Access Groups"
msgstr "กลุ่มการเข้าถึง"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Access Refused"
msgstr "การเข้าถึงถูกปฏิเสธ"

#. module: website_slides
#: model:mail.activity.type,name:website_slides.mail_activity_data_access_request
msgid "Access Request"
msgstr "การร้องขอการเข้าถึง"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_requested_access
msgid "Access Requested"
msgstr "ร้องขอการเข้าถึงแล้ว"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Access Rights"
msgstr "สิทธิ์การเข้าใช้งาน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction
msgid "Action Needed"
msgstr "ต้องดำเนินการ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Actions"
msgstr "การดำเนินการ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__active
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#, python-format
msgid "Add"
msgstr "เพิ่ม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_comment
msgid "Add Comment"
msgstr "เพิ่มความคิดเห็น"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Content"
msgstr "เพิ่มเนื้อหา"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_review
msgid "Add Review"
msgstr "เพิ่มรีวิว"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Section"
msgstr "เพิ่มส่วน"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Add Tag"
msgstr "เพิ่มแท็ก"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
msgid "Add a new lesson"
msgstr "เพิ่มบทเรียนใหม่"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#, python-format
msgid "Add a section"
msgstr "เพิ่มส่วน"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#, python-format
msgid "Add a tag"
msgstr "เพิ่มแท็ก"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Add an answer below this one"
msgstr "เพิ่มคำตอบใต้ข้อนี้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Add comment on this answer"
msgstr "เพิ่มความคิดเห็นในคำตอบนี้"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Add existing contacts..."
msgstr "เพิ่มรายชื่อติดต่อที่มีอยู่..."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid ""
"Add quizzes at the end of your lessons to evaluate what your students "
"understood."
msgstr "เพิ่มแบบทดสอบในตอนท้ายของบทเรียนเพื่อประเมินสิ่งที่นักเรียนเข้าใจ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_ids
msgid "Additional Resource for this slide"
msgstr "แหล่งข้อมูลเพิ่มเติมสำหรับสไลด์นี้"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Additional Resources"
msgstr "แหล่งข้อมูลเพิ่มเติม"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_resource
msgid "Additional resource for a particular slide"
msgstr "แหล่งข้อมูลเพิ่มเติมสำหรับสไลด์เฉพาะ"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_advanced
msgid "Advanced"
msgstr "ระดับสูง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "All Courses"
msgstr "คอร์สทั้งหมด"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "All completed classes and earned karma will be lost."
msgstr "คลาสที่สำเร็จและกรรมที่ได้รับทั้งหมดจะหายไป"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__partner_ids
msgid "All members of the channel."
msgstr "สมาชิกทั้งหมดของช่อง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "All questions must be answered !"
msgstr "ต้องตอบทุกคำถาม !"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_5_furn2
msgid "All you need to know about furniture creation."
msgstr "ทั้งหมดที่คุณต้องรู้เกี่ยวกับการสร้างเฟอร์นิเจอร์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow Download"
msgstr "อนุญาตให้ดาวน์โหลด"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_preview
msgid "Allow Preview"
msgstr "อนุญาตให้ดูตัวอย่าง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Allow Rating"
msgstr "อนุญาตให้ให้คะแนน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__allow_comment
msgid "Allow rating on Course"
msgstr "อนุญาตให้ให้คะแนนในหลักสูตร"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Allow students to review your course"
msgstr "อนุญาติให้นักเรียนทบทวนหลักสูตรของคุณ"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow the user to download the content of the slide."
msgstr "อนุญาตให้ผู้ใช้ดาวน์โหลดเนื้อหาของสไลด์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_allow_comment
msgid "Allows comment"
msgstr "อนุญาตให้แสดงความคิดเห็น"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Already Requested"
msgstr "ได้ร้องขอแล้ว"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Already installing \"%s\"."
msgstr "ติดตั้งแล้ว \"%s\""

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Already member"
msgstr "เป็นสมาชิกแล้ว"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Amazing!"
msgstr "น่าทึ่ง!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_2
msgid "And also bananas"
msgstr "แล้วก็กล้วย"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__text_value
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answer_ids
msgid "Answer"
msgstr "ตอบ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "ปรากฎใน"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_visibility
msgid ""
"Applied directly as ACLs. Allow to hide channels and their content for non "
"members."
msgstr ""
"นำไปใช้โดยตรงเป็น ACL อนุญาตให้ซ่อนช่องและเนื้อหาสำหรับผู้ที่ไม่ใช่สมาชิก"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "Archive"
msgstr "เก็บถาวร"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "Archive Slide"
msgstr "เก็บสไลด์ถาวร"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Are you sure you want to archive this slide ?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการเก็บถาวรสไลด์นี้?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Are you sure you want to delete this category ?"
msgstr "คุณแน่ใจหรือว่าต้องการลบหมวดหมู่นี้ ?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Are you sure you want to delete this question :"
msgstr "คุณแน่ใจหรือว่าต้องการลบหมวดหมู่นี้ :"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Attachment"
msgstr "แนบ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_attachment_count
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_attachment_count
msgid "Attachment Count"
msgstr "จํานวนสิ่งที่แนบมา"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__attachment_ids
msgid "Attachments"
msgstr "เอกสารแนบ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_avg
msgid "Attempts Avg"
msgstr "ความพยายามโดยเฉลี่่ย"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_count
msgid "Attempts Count"
msgstr "จำนวนครั้งที่พยยายาม"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_attendees
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Attendees"
msgstr "ผู้เข้าร่วม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_done_count
msgid "Attendees Done Count"
msgstr "จำนวนผู้เข้าร่วมเสร็จสิ้น"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_count
msgid "Attendees count"
msgstr "จำนวนผู้เข้าร่วม"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Attendees of %s"
msgstr "ผู้เข้าร่วมของ %s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_group_ids
msgid "Auto Enroll Groups"
msgstr "กลุ่มลงทะเบียนอัตโนมัติ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Badges"
msgstr "ป้ายรางวัล"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_basic
msgid "Basic"
msgstr "พื้นฐาน"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_5_furn2
msgid "Basics of Furniture Creation"
msgstr "พื้นฐานของการสร้างเฟอร์นิเจอร์"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_0_gard_0
msgid "Basics of Gardening"
msgstr "พื้นฐานของการจัดสวน"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "Be notified when a new content is added."
msgstr "ได้รับแจ้งเมื่อมีการเพิ่มเนื้อหาใหม่"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_comment
msgid "Can Comment"
msgstr "สามารถแสดงความคิดเห็น"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__can_edit_body
msgid "Can Edit Body"
msgstr "แก้ไขเนื้อหาหลักได้"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_publish
msgid "Can Publish"
msgstr "สามารถเผยแพร่"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_review
msgid "Can Review"
msgstr "สามารถรีวิว"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_upload
msgid "Can Upload"
msgstr "สามารอัปโหลด"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_vote
msgid "Can Vote"
msgstr "สามารถโหวต"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
#, python-format
msgid "Cancel"
msgstr "ยกเลิก"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_carpenter
msgid "Carpenter"
msgstr "ช่างไม้"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Catchy Headline"
msgstr "การพาดหัวที่น่าสนใจ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_category_ids
msgid "Categories"
msgstr "หมวดหมู่"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Category"
msgstr "หมวดหมู่"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_1
msgid "Certification"
msgstr "ใบรับรอง"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Certifications"
msgstr "ใบรับรอง"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_certification
#: model:gamification.challenge.line,name:website_slides.badge_data_certification_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_certification_goal
msgid "Certified Knowledge"
msgstr "ความรู้ที่ผ่านการรับรอง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Change video privacy settings"
msgstr "เปลี่ยนการตั้งค่าความเป็นส่วนตัวของวิดีโอ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Channel"
msgstr "ช่อง"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_partner
msgid "Channel / Partners (Members)"
msgstr "ช่องทาง/พาร์ทเนอร์ (สมาชิก)"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_invite
msgid "Channel Invitation Wizard"
msgstr "ตัวช่วยสร้างการเชิญช่อง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Channel Member"
msgstr "สมาชิกช่อง"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_type
msgid "Channel type"
msgstr "ชนิดช่อง"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__completed
msgid "Channel validated, even if slides / lessons are added once done."
msgstr ""
"ตรวจสอบช่องแล้ว แม้ว่าจะมีการเพิ่มสไลด์ / บทเรียนจะเพิ่มเมื่อเสร็จแล้ว"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag_group
msgid "Channel/Course Groups"
msgstr "ช่อง/กลุ่มคอร์ส"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag
msgid "Channel/Course Tag"
msgstr "ช่อง/แท็กคอร์ส"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_invite
msgid "Channel: Invite by email"
msgstr "ช่อง: เชิญทางอีเมล"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__channel_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channels"
msgstr "ช่อง"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_cheatsheet
msgid "CheatSheet"
msgstr "CheatSheet"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check Profile"
msgstr "ตรวจสอบโปรไฟล์"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check answers"
msgstr "ตรวจคำตอบ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check your answers"
msgstr "ตรวจสอบคำตอบของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Choose a <b>File</b> on your computer."
msgstr "เลือก <b>ไฟล์</b> บนคอมพิวเตอร์ของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Choose a PDF or an Image"
msgstr "เลือก PDF หรือรูปภาพ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Choose a layout"
msgstr "เลือกเลย์เอาท์"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_3_furn0
msgid "Choose your wood !"
msgstr "เลือกไม้ของคุณ !"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Clear filters"
msgstr "ล้างตัวกรอง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Click here to send a verification email allowing you to participate at the "
"eLearning."
msgstr "คลิกที่นี่เพื่อส่งอีเมลยืนยันเพื่อให้คุณเข้าร่วมการอบรมออนไลน์"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to start the course"
msgstr "คลิกที่นี่เพื่อเริ่มคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Click on \"New\" in the top-right corner to write your first course."
msgstr "คลิกที่ \"ใหม่\" ที่มุมบนขวาเพื่อเขียนคอร์สแรกของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Click on the <b>Create</b> button to create your first course."
msgstr "คลิกที่ปุ่ม <b>สร้าง</b> เพื่อสร้างคอร์สแรกของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Click on your <b>Course</b> to go back to the table of content."
msgstr "คลิกที่ <b>คอร์ส</b>เพื่อกลับไปที่สารบัญ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
#, python-format
msgid "Close"
msgstr "ปิด"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Color"
msgstr "สี"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__color
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__color
msgid "Color Index"
msgstr "ดัชนีสี"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_colorful
msgid "Colorful"
msgstr "มีสีสัน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__comment
msgid "Comment"
msgstr "ความคิดเห็น"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Comments"
msgstr "ความคิดเห็น"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering                               questions. In this course, you'll "
"study those topics with activities about mathematics, science and logic."
msgstr ""
"งานทั่วไปสำหรับนักวิทยาศาสตร์คอมพิวเตอร์คือการถามคำถามที่ถูกต้องและตอบคำถาม "
"ในหลักสูตรนี้ คุณจะได้ศึกษาหัวข้อเหล่านั้นพร้อมกิจกรรมเกี่ยวกับคณิตศาสตร์ "
"วิทยาศาสตร์ และตรรกะ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions. In this course, you'll study those topics with "
"activities about mathematics, science and logic."
msgstr ""
"งานทั่วไปสำหรับนักวิทยาศาสตร์คอมพิวเตอร์คือการถามคำถามที่ถูกต้องและตอบคำถาม "
"ในหลักสูตรนี้ คุณจะได้ศึกษาหัวข้อเหล่านั้นพร้อมกิจกรรมเกี่ยวกับคณิตศาสตร์ "
"วิทยาศาสตร์ และตรรกะ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Communication"
msgstr "การติดต่อสื่อสาร"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_karma
#: model:gamification.challenge.line,name:website_slides.badge_data_karma_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_karma_goal
msgid "Community hero"
msgstr "ฮีโร่ของชุมชน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_company_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_company_count
msgid "Company Course Count"
msgstr "จำนวนหลักสูตรของบริษัท"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_0
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_0
msgid "Comparing Hardness of Wood Species"
msgstr "เปรียบเทียบความแข็งของพันธุ์ไม้"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_course_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_course
msgid "Complete a course"
msgstr "จบคอร์ส"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_profile_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_profile
msgid "Complete your profile"
msgstr "กรอกข้อมูลโปรไฟล์ของคุณให้สมบูรณ์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Completed"
msgstr "เสร็จสิ้น"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: model:mail.template,name:website_slides.mail_template_channel_completed
#, python-format
msgid "Completed Course"
msgstr "จบคอร์ส"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_completed_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_completed_ids
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Completed Courses"
msgstr "จบคอร์ส"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completion
msgid "Completion"
msgstr "เสร็จสิ้น"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed_template_id
msgid "Completion Email"
msgstr "อีเมลเสร็จสิ้น"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Completion Time"
msgstr "เวลาเสร็จสิ้น"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Compose Email"
msgstr "เขียนอีเมล"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Computer Science for kids"
msgstr "วิทยาศาสตร์คอมพิวเตอร์สำหรับเด็ก"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_enroll
msgid "Condition to enroll: everyone, on invite, on payment (sale bridge)."
msgstr ""
"เงื่อนไขการสมัคร : สำหรับทุกท่าน เมื่อได้รับเชิญ เมื่อชำระเงิน (สะพานขาย)"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_configuration
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_completed
msgid "Congratulation! You completed {{ object.channel_id.name }}"
msgstr "ขอแสดงความยินดี! คุณทำเสร็จแล้ว {{ object.channel_id.name }}"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations! Your first lesson is available. Let's see the options "
"available here. The tag \"<b>New</b>\" indicates that this lesson was "
"created less than 7 days ago."
msgstr ""
"ยินดีด้วย! บทเรียนแรกของคุณพร้อมใช้งานแล้ว ลองดูตัวเลือกที่มีที่นี่ แท็ก "
"\"<b>ใหม่</b>\" ระบุว่าบทเรียนนี้สร้างขึ้นไม่ถึง 7 วันที่ผ่านมา"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Congratulations, you have reached the last rank!"
msgstr "ขอแสดงความยินดี คุณมาถึงอันดับสุดท้ายแล้ว!"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations, you've created your first course.<br/>Click on the title of"
" this content to see it in fullscreen mode."
msgstr ""
"ขอแสดงความยินดี คุณได้สร้างหลักสูตรแรกของคุณแล้ว "
"<br/>คลิกที่ชื่อของเนื้อหานี้เพื่อดูในโหมดเต็มหน้าจอ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations, your course has been created, but there isn't any content "
"yet. First, let's add a <b>Section</b> to give your course a structure."
msgstr ""
"ขอแสดงความยินดี หลักสูตรของคุณถูกสร้างขึ้นแล้ว แต่ยังไม่มีเนื้อหาใด ๆ "
"ขั้นแรก ให้เพิ่ม <b>ส่วน</b> เพื่อให้หลักสูตรของคุณมีโครงสร้าง"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_6_furn3
#, python-format
msgid "Contact Responsible"
msgstr "ติดต่อรับผิดชอบ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Contact all the members of a course via mass mailing"
msgstr "ติดต่อสมาชิกทุกคนในคอร์สผ่านเมล์กลุ่ม"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Contact the responsible to enroll."
msgstr "ติดต่อผู้รับผิดชอบในการลงทะเบียน"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Contact us"
msgstr "ติดต่อเรา"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__datas
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Content"
msgstr "เนื้อหา"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Content Preview"
msgstr "ดูตัวอย่างเนื้อหา"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_question
msgid "Content Quiz Question"
msgstr "คำถามแบบทดสอบเนื้อหา"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_tag
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_content_tags
msgid "Content Tags"
msgstr "แท็กเนื้อหา"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Title"
msgstr "ชื่อเนื้อหา"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_slide_action
#: model:ir.actions.act_window,name:website_slides.slide_slide_action_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_content
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_contents
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Contents"
msgstr "เนื้อหา"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Continue"
msgstr "ต่อไป"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Copy Link"
msgstr "คัดลอกลิงก์"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_1
msgid "Correct !"
msgstr "ถูกต้อง !"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_0
msgid "Correct ! A shovel is the perfect tool to dig a hole."
msgstr "ถูกต้อง ! พลั่วเป็นเครื่องมือที่สมบูรณ์แบบในการขุดหลุม"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_0
msgid "Correct ! A strawberry is a fruit because it's the product of a tree."
msgstr "ถูกต้อง ! สตรอเบอรี่เป็นผลไม้เพราะเป็นผลผลิตของต้นไม้"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_0
msgid "Correct ! Congratulations you have time to loose"
msgstr "ถูกต้อง ! ยินดีด้วยที่คุณมีเวลาพักผ่อน"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Correct ! You did it !"
msgstr "ถูกต้อง ! คุณทำได้ !"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Could not fetch data from url. Document or access right not available:\n"
"%s"
msgstr ""
"ไม่สามารถดึงข้อมูลจาก url เอกสารหรือสิทธิ์การเข้าถึงไม่สามารถใช้ได้:\n"
"%s"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Course"
msgstr "คอร์ส"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_count
msgid "Course Count"
msgstr "จำนวนคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Group Name"
msgstr "ชื่อกลุ่มคอร์ส"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_group_action
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_course_groups
msgid "Course Groups"
msgstr "กลุ่มคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Course Name"
msgstr "ชื่อคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_form
msgid "Course Tag"
msgstr "แท็กคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Tag Group"
msgstr "กลุ่มแท็กคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Course Tag Groups"
msgstr "กลุ่มแท็กคอร์ส"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_tree
msgid "Course Tags"
msgstr "แท็กคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Course Title"
msgstr "ชื่อคอร์ส"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_finish
msgid "Course finished"
msgstr "จบคอร์ส"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Course not published yet"
msgstr "คอร์สยังไม่เผยแพร่"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_rank
msgid "Course ranked"
msgstr "อันดับคอร์ส"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_type
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_type
msgid "Course type"
msgstr "ชนิดคอร์ส"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Course: %s"
msgstr "คอร์ส: %s"

#. module: website_slides
#: code:addons/website_slides/models/website.py:0
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_courses
#: model:website.menu,name:website_slides.website_menu_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#, python-format
msgid "Courses"
msgstr "คอร์ส"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Create"
msgstr "สร้าง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Create a community and let the members help each others"
msgstr "สร้างชุมชนและให้สมาชิกช่วยเหลือซึ่งกันและกัน"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid "Create a course"
msgstr "สร้างคอร์ส"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Create new %s '%s'"
msgstr "สร้างใหม่ %s '%s'"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Create new Tag '%s'"
msgstr "สร้างแท็กใหม่ '%s'"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "Create new content for your eLearning"
msgstr "สร้างเนื้อหาใหม่สำหรับการอบรมออนไลน์ของคุณ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
msgid "Creation Date"
msgstr "วันที่สร้าง"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__html_content
msgid "Custom HTML content for slides of type 'Web Page'."
msgstr "เนื้อหา HTML ที่กำหนดเองสำหรับสไลด์ประเภท 'หน้าเว็บ'"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_6_furn3
msgid "DIY Furniture"
msgstr "DIY เฟอร์นิเจอร์"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "วันที่ (ใหม่ไปเก่า)"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "วันที่ (เก่าไปใหม่)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__null_value
msgid "Default Value"
msgstr "ค่าเริ่มต้น"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "กำหนดการมองเห็นความท้าทายผ่านเมนู"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#, python-format
msgid "Delete"
msgstr "ลบ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
#, python-format
msgid "Delete Category"
msgstr "ลบหมวดหมู่"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "Delete Question"
msgstr "ลบคำถาม"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__promote_strategy
msgid ""
"Depending the promote strategy, a slide will appear on the top of the course's page :\n"
" * Latest Published : the slide created last.\n"
" * Most Voted : the slide which has to most votes.\n"
" * Most Viewed ; the slide which has been viewed the most.\n"
" * Specific : You choose the slide to appear.\n"
" * None : No slides will be shown.\n"
msgstr ""
"สไลด์จะปรากฏที่ด้านบนของหน้าหลักสูตร ทั้งนี้ขึ้นอยู่กับกลยุทธ์การเลื่อนตำแหน่ง :\n"
"* เผยแพร่ล่าสุด : สไลด์ที่สร้างล่าสุด\n"
" * โหวตมากที่สุด : สไลด์ที่มีการโหวตมากที่สุด\n"
"* มีผู้เข้าชมมากที่สุด ; สไลด์ที่มีคนดูมากที่สุด\n"
"* เฉพาะ: คุณเลือกสไลด์ที่จะแสดง\n"
"* ไม่มี : ไม่มีการแสดงสไลด์\n"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__description
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Description"
msgstr "รายละเอียด"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_html
msgid "Detailed Description"
msgstr "คำอธิบายโดยละเอียด"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_1
msgid "Did you read the whole article ?"
msgstr "คุณอ่านบทความทั้งหมดหรือไม่?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "Discard"
msgstr "ละทิ้ง"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Discover more"
msgstr "ค้นพบเพิ่มขึ้น"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__dislikes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Dislikes"
msgstr "ไม่ชอบ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Display"
msgstr "แสดง"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_question__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__sequence
msgid "Display order"
msgstr "แสดงลำดับ"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_0
msgid "Do you make beams out of lemon trees ?"
msgstr "ทำคานจากต้นมะนาว ?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_1
msgid "Do you make lemons out of beams ?"
msgstr "คุณทำมะนาวจากคาน ?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_unsubscribe.xml:0
#, python-format
msgid "Do you really want to leave the course?"
msgstr "คุณต้องการที่จะออกจากคอร์ส?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_0
msgid "Do you think Harry Potted has a good name ?"
msgstr "คุณคิดว่า Harry Potted มีชื่อที่ดีหรือไม่?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Do you want to install the \"%s\" app?"
msgstr "คุณต้องการติดตั้ง\"%s\" แอป?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_5_3_question_0
msgid "Do you want to reply correctly ?"
msgstr "คุณต้องการตอบกลับอย่างถูกต้องหรือไม่?"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Do you want to request access to this course ?"
msgstr "คุณต้องการร้องขอการเข้าถึงคอร์สนี้หรือไม่?"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__document
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Document"
msgstr "เอกสาร"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_id
msgid "Document ID"
msgstr "ไอดีเอกสาร"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__url
msgid "Document URL"
msgstr "URL เอกสาร"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__documentation
msgid "Documentation"
msgstr "เอกสารกำกับ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Documentation Layout"
msgstr "เอกสารกำกับเลย์เอาท์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_document
#: model:slide.slide,name:website_slides.slide_category_demo_4_0
msgid "Documents"
msgstr "เอกสาร"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_2
msgid "Dog Friendly"
msgstr "เป็นมิตรกับสุนัข"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Don't have an account ?"
msgstr "ยังไม่มีบัญชี ?"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Done !"
msgstr "เสร็จสิ้น !"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__done_count
msgid "Done Count"
msgstr "นับเสร็จ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download"
msgstr "ดาวน์โหลด"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download Content"
msgstr "ดาวน์โหลดเนื้อหา"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_10
msgid "Drawing 1"
msgstr "ภาพร่าง 1"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_11
msgid "Drawing 2"
msgstr "ภาพร่าง 2"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Dropdown menu"
msgstr "เมนูแบบดรอปดาว์น"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_time
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__completion_time
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#, python-format
msgid "Duration"
msgstr "ระยะเวลา"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Edit"
msgstr "แก้ไข"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Edit in backend"
msgstr "แก้ไขในส่วนหลัง"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_email
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Email"
msgstr "อีเมล"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__publish_template_id
msgid "Email attendees once a new content is published"
msgstr "ส่งอีเมลถึงผู้เข้าร่วมเมื่อมีการเผยแพร่เนื้อหาใหม่"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__completed_template_id
msgid "Email attendees once they've finished the course"
msgstr "ส่งอีเมลถึงผู้เข้าร่วมเมื่อจบคอร์ส"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_template_id
msgid "Email template used when sharing a slide"
msgstr "เทมเพลตอีเมลที่ใช้เมื่อแชร์สไลด์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code
msgid "Embed Code"
msgstr "ฝังโค้ด"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embedcount_ids
msgid "Embed Count"
msgstr "จำนวนการฝัง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Embed in your website"
msgstr "ฝังในเว็บไซต์ของคุณ"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_embed
msgid "Embedded Slides View Counter"
msgstr "ตัวนับการเข้าชมสไลด์แบบฝัง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "End course"
msgstr "จบคอร์ส"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_2
msgid "Energy Efficiency Facts"
msgstr "ข้อมูลประสิทธิภาพพลังงาน"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Enjoy this exclusive content !"
msgstr "เพลิดเพลินไปกับเนื้อหาพิเศษนี้ !"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_msg
msgid "Enroll Message"
msgstr "ข้อความการลงทะเบียน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_enroll
msgid "Enroll Policy"
msgstr "นโยบายการสมัคร"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Enrolled On"
msgstr "ลงทะเบียนเมื่อ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Enter at least two possible <b>Answers</b>."
msgstr "ใส่อย่างน้อยสอง <b>คำตอบ</b>ที่เป็นไปได้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Enter your <b>Question</b>. Be clear and concise."
msgstr "ใส่ <b>คำถาม</b> ของคุณ ให้ชัดเจนและกระชับ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Enter your answer"
msgstr "ใส่คำตอบของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Enter your question"
msgstr "ใส่คำตอบของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Estimated slide completion time"
msgstr "เวลาสิ้นสุดสไลด์โดยประมาณ"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Evaluate and certify your students."
msgstr "ประเมินและรับรองนักเรียนของคุณ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Evaluate your students and certify them"
msgstr "ประเมินนักเรียนของคุณและรับรองพวกเขา"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_exercises
msgid "Exercises"
msgstr "แบบทดสอบ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "External Links"
msgstr "ลิงก์ภายนอก"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_link
msgid "External URL for a particular slide"
msgstr "URL ภายนอกสำหรับสไลด์เฉพาะ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__link_ids
msgid "External URL for this slide"
msgstr "URL ภายนอกสำหรับสไลด์นี้"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "External sources"
msgstr "แหล่งข้อมูลภายนอก"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Failed to install \"%s\"."
msgstr "ติดตั้งไม่สำเร็จ \"%s\""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__model_object_field
msgid "Field"
msgstr "ฟิลด์"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/controllers/main.py:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "File is too big. File size cannot exceed 25MB"
msgstr "ไฟล์มีขนาดใหญ่เกินไป ขนาดไฟล์ต้องไม่เกิน 25MB"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Filter &amp; order"
msgstr "ตัวกรองและคำสั่ง"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr "ข้อความตัวอย่างนิพจน์สุดท้าย ที่จะคัดลอกและวางในช่องเทมเพลตที่ต้องการ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Finish Course"
msgstr "จบคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "First attempt"
msgstr "ความพยายามครั้งแรก"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"First, create your lesson, then edit it with the website builder. You'll be "
"able to drop building blocks on your page and edit them."
msgstr ""
"ขั้นแรก สร้างบทเรียนของคุณ แล้วแก้ไขด้วยเครื่องมือสร้างเว็บไซต์ "
"คุณจะสามารถวางบล็อกสำเร็จรูปบนเพจของคุณและแก้ไขได้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "First, let's add a <b>Presentation</b>. It can be a .pdf or an image."
msgstr "ขั้นแรก ให้เพิ่ม <b>งานนำเสนอ</b> สามารถเป็น PDF หรือ รูปภาพ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "First, upload your videos on YouTube and mark them as"
msgstr "ขั้นแรก อัปโหลดวิดีโอของคุณบน YouTube และทำเครื่องหมายเป็น"

#. module: website_slides
#: code:addons/website_slides/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
#, python-format
msgid "Followed Courses"
msgstr "คอร์สที่ติดตาม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_follower_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_partner_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบฟอนต์ที่ยอดเยี่ยม เช่น fa-tasks"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_0
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Foreword"
msgstr "คำนำ"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_0
msgid "Foreword for this documentation: how to use it, main attention points"
msgstr "คำนำสำหรับเอกสารนี้: วิธีใช้งาน จุดน่าสนใจหลัก"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_forum
msgid "Forum"
msgstr "ฟอรั่ม"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Fourth and more attempt"
msgstr "ความพยายามครั้งที่สี่และหลังจากนั้น"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_2
msgid "From a piece of wood to a fully functional furniture, step by step."
msgstr "ตั้งแต่ชิ้นไม้ไปจนถึงเฟอร์นิเจอร์ที่ใช้งานได้จริง ทีละขั้นตอน"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_furniture
msgid "Furniture Designer"
msgstr "นักออกแบบเฟอร์นิเจอร์"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_4_furn1
msgid "Furniture Technical Specifications"
msgstr "ข้อมูลจำเพาะทางเทคนิคของเฟอร์นิเจอร์"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_12
msgid "GLork"
msgstr "GLork"

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "ความท้าทายในรูปแบบเกม"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_gardener
msgid "Gardener"
msgstr "คนสวน"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_0
msgid "Gardening: The Know-How"
msgstr "การจัดสวน: องค์ความรู้"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Generate revenues thanks to your courses"
msgstr "สร้างรายได้ด้วยหลักสูตรของคุณ"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_certification_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_certification
msgid "Get a certification"
msgstr "รับใบรับรอง"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_register
#: model:gamification.challenge.line,name:website_slides.badge_data_register_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_register_goal
msgid "Get started"
msgstr "เริ่มเลย"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Give your course a helpful <b>Description</b>."
msgstr "ให้<b>รายละเอียด</b>ที่เป็นประโยชน์แก่คอร์สของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Give your course an engaging <b>Title</b>."
msgstr "ให้ <b>ชื่อเรื่อง</b>มีส่วนร่วมกับคอร์สของคุณ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__website_slide_google_app_key
#: model:ir.model.fields,field_description:website_slides.field_website__website_slide_google_app_key
msgid "Google Doc Key"
msgstr "Google Doc Key"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Google Drive API Key"
msgstr "Google Drive API Key"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/components/activity/activity.xml:0
#, python-format
msgid "Grant Access"
msgstr "ให้สิทธิ์การเข้าถึง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_graph
msgid "Graph of Contents"
msgstr "การฟของเนื้อหา"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_id
msgid "Group"
msgstr "กลุ่ม"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Group By"
msgstr "จัดกลุ่มตาม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__name
msgid "Group Name"
msgstr "ชื่อกลุ่ม"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__upload_group_ids
msgid "Group of users allowed to publish contents on a documentation course."
msgstr "กลุ่มผู้ใช้ที่ได้รับอนุญาตให้เผยแพร่เนื้อหาของเอกสารกำกับคอร์ส"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_sequence
msgid "Group sequence"
msgstr "ลำดับกลุ่ม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__html_content
msgid "HTML Content"
msgstr "เนื้อหา HTML"

#. module: website_slides
#: model:ir.model,name:website_slides.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_1
msgid "Hand on !"
msgstr "ส่งต่อ !"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_message
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_6
msgid "Here is How to get the Sweetest Strawberries you ever tasted!"
msgstr "นี่คือวิธีการที่จะได้สตรอเบอร์รี่ที่หอมหวานที่สุดที่คุณเคยลิ้มลอง!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Home"
msgstr "โฮม"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_1
msgid "Home Gardening"
msgstr "จัดสวนที่บ้าน"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_2
msgid "How To Build a HIGH QUALITY Dining Table with LIMITED TOOLS"
msgstr "วิธีการสร้างโต๊ะรับประทานอาหารคุณภาพสูงด้วยเครื่องมือที่จำกัด"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_3
msgid "How to Grow and Harvest The Best Strawberries | Basics"
msgstr "วิธีปลูกและเก็บเกี่ยวสตรอเบอร์รี่ที่ดีที่สุด | พื้นฐาน"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_6
msgid ""
"How to Grow and Harvest The Best Strawberries | Gardening Tips and Tricks"
msgstr ""
"วิธีการปลูกและเก็บเกี่ยวสตรอเบอร์รี่ที่ดีที่สุด | เคล็ดลับและเทคนิคการจัดสวน"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to create a Lesson as a Web Page?"
msgstr "จะสร้างบทเรียนเป็นเว็บเพจได้อย่างไร?"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_1
msgid "How to find quality wood"
msgstr "วิธีหาไม้ที่มีคุณภาพ"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_3
msgid "How to plant a potted tree"
msgstr "วิธีการปลูกต้นไม้ในกระถาง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to upload your PowerPoint Presentations or Word Documents?"
msgstr "จะอัปโหลดงานนำเสนอ PowerPoint หรือเอกสาร Word ได้อย่างไร"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to upload your videos ?"
msgstr "อัปโหลดวิดีโอของคุณได้อย่างไร ?"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_2
msgid "How to wall decorating by tree planting in hanging plastic bottles."
msgstr "วิธีตกแต่งผนังด้วยการปลูกต้นไม้ในขวดพลาสติกแบบแขวน"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_howto
msgid "HowTo"
msgstr "ได้อย่างไร"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__id
#: model:ir.model.fields,field_description:website_slides.field_slide_question__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__id
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__id
msgid "ID"
msgstr "ไอดี"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุกิจกรรมการยกเว้น"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__allow_comment
#: model:ir.model.fields,help:website_slides.field_slide_slide__channel_allow_comment
msgid ""
"If checked it allows members to either:\n"
" * like content and post comments on documentation course;\n"
" * post comment and review on training course;"
msgstr ""
"หากทำเครื่องหมายไว้ สมาชิกสามารถทำสิ่งต่อไปนี้\n"
"* กดชอบเนื้อหาและแสดงความคิดเห็นในเอกสารคอร์ส;\n"
"* โพสต์แสดงความคิดเห็นและทบทวนคอร์สฝึกอบรม;"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_4_furn1
msgid ""
"If you are looking for technical specifications, have a look at this "
"documentation."
msgstr "หากคุณกำลังมองหาข้อกำหนดทางเทคนิค โปรดดูเอกสารนี้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"If you want to be sure that attendees have understood and memorized the "
"content, you can add a Quiz on the lesson. Click on <b>Add Quiz</b>."
msgstr ""
"หากคุณต้องการให้แน่ใจว่าผู้เข้าร่วมเข้าใจและจดจำเนื้อหาแล้ว "
"คุณสามารถเพิ่มแบบทดสอบในบทเรียนได้ คลิกที่<b>เพิ่มควิซ</b>"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1920
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1920
msgid "Image"
msgstr "รูปภาพ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1024
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1024
msgid "Image 1024"
msgstr "รูปภาพ 1024"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_128
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_128
msgid "Image 128"
msgstr "รูปภาพ 128"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_256
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_256
msgid "Image 256"
msgstr "รูปภาพ 256"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_512
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_512
msgid "Image 512"
msgstr "รูปภาพ 512"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_0
msgid "Incorrect !"
msgstr "ไม่ถูกต้อง !"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_1
msgid "Incorrect ! A strawberry is not a vegetable."
msgstr "ไม่ถูกต้อง ! สตรอเบอร์รี่ไม่ใช่ผัก"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_2
msgid "Incorrect ! A table is a piece of furniture."
msgstr "ไม่ถูกต้อง ! โต๊ะเป็นเฟอร์นิเจอร์ชิ้นหนึ่ง"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_1
msgid "Incorrect ! Good luck digging a hole with a spoon..."
msgstr "ไม่ถูกต้อง ! ขอให้โชคดี ขุดหลุมด้วยช้อน..."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_2
msgid "Incorrect ! Seriously ?"
msgstr "ไม่ถูกต้อง ! จริงจังป่ะเนี้ย ?"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_1
msgid "Incorrect ! You better think twice..."
msgstr "ไม่ถูกต้อง ! คุณควรคิดใหม่อีกครั้ง..."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_1
msgid "Incorrect ! You really should read it."
msgstr "ไม่ถูกต้อง ! คุณควรอ่านมันจริง ๆ"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_2
msgid "Incorrect ! of course not ..."
msgstr "ไม่ถูกต้อง ! แน่นอนว่าไม่ ..."

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__infographic
msgid "Infographic"
msgstr "อินโฟกราฟิกส์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_infographic
msgid "Infographics"
msgstr "อินโฟกราฟิกส์"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Install"
msgstr "ติดตั้ง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Installing \"%s\"."
msgstr "กำลังติดตั้ง \"%s\"."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_0
msgid "Interesting Facts"
msgstr "ข้อเท็จจริงที่น่าสนใจ"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_1
msgid "Interesting Tree Facts"
msgstr "ข้อเท็จจริงของต้นไม้ที่น่าสนใจ"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_1
msgid "Interesting information about home gardening. Keep it close !"
msgstr "ข้อมูลที่น่าสนใจเกี่ยวกับการจัดสวนที่บ้าน เอาไว้ใกล้ตัว !"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_intermediate
msgid "Intermediate"
msgstr "ปานกลาง"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Internal server error, please try again later or contact administrator.\n"
"Here is the error message: %s"
msgstr ""
"เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์ โปรดลองอีกครั้งในภายหลังหรือติดต่อผู้ดูแลระบบ\n"
"นี้คือข้อความแสดงข้อผิดพลาด: %s"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Invalid file type. Please select pdf or image file"
msgstr "ประเภทไฟล์ไม่ถูกต้อง กรุณาเลือก pdf หรือไฟล์รูปภาพ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Invite"
msgstr "เชิญ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed
msgid "Is Completed"
msgstr "เสร็จแล้ว"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "เป็นผู้แก้ไข"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_is_follower
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member
msgid "Is Member"
msgstr "เป็นสมาชิก"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_new_slide
msgid "Is New Slide"
msgstr "เป็นสไลด์ใหม่"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_published
msgid "Is Published"
msgstr "เผยแพร่"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_category
msgid "Is a category"
msgstr "เป็นหมวดหมู่"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__is_correct
msgid "Is correct answer"
msgstr "เป็นคำตอบที่ถูกต้อง"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_3
msgid ""
"Jim and Todd plant a potted tree for a customer of Knecht's Nurseries and "
"Landscaping. Narrated by Leif Knecht, owner."
msgstr ""
"Jim และ Todd ปลูกต้นไม้ในกระถางให้กับลูกค้าของสถานรับเลี้ยงเด็กของ Knecht "
"และปรับภูมิทัศน์ให้อีกด้วย บรรยายโดย Leif Knecht เจ้าของ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "Join & Submit"
msgstr "เข้าร่วมและส่ง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
#, python-format
msgid "Join Course"
msgstr "เข้าร่วมคอร์ส"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Join the Course"
msgstr "เข้าร่วมคอร์ส"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Join the course to take the quiz and verify your answers!"
msgstr "เข้าร่วมคอร์สเพื่อทำแบบทดสอบและยืนยันคำตอบของคุณ!"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_2
msgid "Just some basics Energy Efficiency Facts."
msgstr "ข้อมูลพื้นฐานบางประการเกี่ยวกับประสิทธิภาพพลังงาน"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_1
msgid "Just some basics Interesting Tree Facts."
msgstr "เพียงข้อมูลพื้นฐานเกี่ยวกับต้นไม้ที่น่าสนใจ"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_0
msgid "Just some basics Tree Infographic."
msgstr "ข้อมูลพื้นฐานเกี่ยวกับต้นไม้ในรูปแบบอินโฟกราฟิกส์"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Karma"
msgstr "คะแนน Karma"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_comment
msgid "Karma needed to add a comment on a slide of this course"
msgstr "คะแนน Karma จำเป็นสำหรับเพิ่มความคิดเห็นบนสไลด์ของคอร์สนี้"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_review
msgid "Karma needed to add a review on the course"
msgstr "คะแนน Karma จำเป็นสำหรับเพิ่มรีวิวของคอร์สนี้"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_vote
msgid "Karma needed to like/dislike a slide of this course."
msgstr "คะแนน Karma จำเป็นสำหรับกดถูกใจหรือไม่ถูกใจของคอร์สนี้"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_profile
#: model:gamification.challenge.line,name:website_slides.badge_data_profile_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_profile_goal
msgid "Know yourself"
msgstr "เรียนรู้ตัวคุณเอง"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_3_furn0
msgid ""
"Knowing which kind of wood to use depending on your application is important. In this course you\n"
"will learn the basics of wood characteristics."
msgstr ""
"การรู้ว่าควรใช้ไม้ชนิดใดขึ้นอยู่กับการใช้งานของคุณเป็นสิ่งสำคัญ ในหลักสูตรนี้คุณ\n"
"จะได้เรียนรู้พื้นฐานของคุณสมบัติของไม้"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_3
msgid ""
"Knowing wood characteristics is a requirement in order to know which kind of"
" wood to use in a given situation."
msgstr ""
"การรู้ลักษณะของไม้เป็นข้อกำหนดเพื่อให้ทราบว่าควรใช้ไม้ชนิดใดในสถานการณ์ที่กำหนด"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__lang
msgid "Language"
msgstr "ภาษา"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Action On"
msgstr "การดำเนินการล่าสุดเมื่อ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_embed____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_question____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_tag____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_last_update
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Last Update"
msgstr "อัปเดตครั้งล่าสุด"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__latest
msgid "Latest Published"
msgstr "เผยแพร่ล่าสุด"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_latest_achievements
msgid "Latest achievements"
msgstr "ความสำเร็จล่าสุด"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "Leaderboard"
msgstr "กระดานผู้นำ"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_1_gard1
msgid ""
"Learn how to take care of your favorite trees. Learn when to plant, how to "
"manage potted trees, ..."
msgstr ""
"เรียนรู้วิธีดูแลต้นไม้ที่คุณชอบ เรียนรู้ช่วงเวลาที่ควรปลูก "
"วิธีจัดการต้นไม้ในกระถาง ..."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_0_gard_0
msgid "Learn the basics of gardening !"
msgstr "เรียนรู้พื้นฐานของการทำสวน !"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_1
msgid "Learn to identify quality wood in order to create solid furnitures."
msgstr "เรียนรู้ที่จะระบุไม้ที่มีคุณภาพเพื่อสร้างเฟอร์นิเจอร์ที่แข็งแรง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Leave the course"
msgstr "ออกจากคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Lesson"
msgstr "บทเรียน"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Lesson Nav"
msgstr "นำทางบทเรียน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_slide_vote
msgid "Lesson voted"
msgstr "บทเรียนที่ถูกโหวต"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Lessons"
msgstr "บทเรียน"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz_finish.js:0
#, python-format
msgid "Level up!"
msgstr "เลเวลอัพ!"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__likes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Likes"
msgstr "กดชอบ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__link
msgid "Link"
msgstr "ลิงก์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__template_id
msgid "Mail Template"
msgstr "เทมเพลตเมล"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_mass_mailing_slides
msgid "Mailing"
msgstr "ส่งเมล"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_0
msgid "Main Trees Categories"
msgstr "หมวดหมู่ต้นไม้หลัก"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_manager
msgid "Manager"
msgstr "ผู้จัดการ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Mark the correct answer by checking the <b>correct</b> mark."
msgstr "ทำเครื่องหมายคำตอบที่ถูกต้องโดยทำเครื่องหมาย <b>ถูกต้อง</b> "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Member"
msgstr "สมาชิก"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_ids
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Members"
msgstr "สมาชิก"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_ids
msgid "Members Information"
msgstr "ข้อมูลสมาชิก"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__members
msgid "Members Only"
msgstr "เฉพาะสมาชิก"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Members Views"
msgstr "ยอดเข้าชมของสมาชิก"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_group_ids
msgid ""
"Members of those groups are automatically added as members of the channel."
msgstr "สมาชิกของกลุ่มเหล่านั้นจะถูกเพิ่มโดยอัตโนมัติให้เป็นสมาชิกของช่อง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Menu Entry"
msgstr "รายการเมนู"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error
msgid "Message Delivery error"
msgstr "เกิดการผิดพลาดในการส่งข้อความ"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_msg
msgid "Message explaining the enroll process"
msgstr "ข้อความอธิบายขั้นตอนการสมัคร"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_1
msgid "Methods"
msgstr "วิธีการ"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_2
msgid "Mighty Carrots"
msgstr "แครอทอันยิ่งใหญ่"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_1
msgid ""
"Mighty forest just don't appear in a few weeks. Learn how time made our "
"forests mighty and mysterious."
msgstr ""
"ป่าอันยิ่งใหญ่จะไม่ได้เกิดขึ้นภายในไม่กี่สัปดาห์ "
"เรียนรู้กันว่าเวลาทำให้ป่าของเรายิ่งใหญ่และลึกลับได้อย่างไร"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__mime_type
msgid "Mime-type"
msgstr "Mime-type"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Minutes"
msgstr "นาที"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Missing \"Tag Group\" for creating a new \"Tag\"."
msgstr "ไม่มี \"กลุ่มแท็ก\" สำหรับการสร้าง \"แท็ก\" ใหม่"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Mobile sub-nav"
msgstr "การนำทางย่อยบนมือถือ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "More info"
msgstr "ข้อมูลเพิ่มเติม"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_viewed
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Most Viewed"
msgstr "ยอดเข้าชมมากที่สุด"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_voted
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Most Voted"
msgstr "ได้รับการโหวตมากที่สุด"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Most popular courses"
msgstr "คอร์สที่เป็นที่นิยมที่สุด"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "หมดเขตกิจกรรมของฉัน"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "My Courses"
msgstr "คอร์สของฉัน"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "My courses"
msgstr "คอร์สของฉัน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__name
msgid "Name"
msgstr "ชื่อ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Nav"
msgstr "Nav"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "New"
msgstr "ใหม่"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "New Certification"
msgstr "ใบรับรองใหม่"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__publish_template_id
msgid "New Content Email"
msgstr "เนื้อหาอีเมลใหม่"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:0
#, python-format
msgid "New Course"
msgstr "คอร์สใหม่"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_published
msgid "New {{ object.slide_type }} published on {{ object.channel_id.name }}"
msgstr "ใหม่ {{ object.slide_type }}เผยแพร่บน{{ object.channel_id.name }}"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Newest"
msgstr "ใหม่ล่าสุด"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Newest courses"
msgstr "คอร์สใหม่ล่าสุด"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Next"
msgstr "ต่อไป"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมสุดท้าย"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Next rank:"
msgstr "อันดับต่อไป:"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_1
#, python-format
msgid "No"
msgstr "ไม่"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "No Course created yet."
msgstr "ไม่มีคอร์สถูกสร้างใหม่"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No completed courses yet!"
msgstr "ยังเรียนคอร์สไม่สำเร็จ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "No content was found using your search"
msgstr "ไม่พบเนื้อหาจากการค้นหาของคุณ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search"
msgstr "ไม่พบคอร์สที่ตรงกับการค้นหาของคุณ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search."
msgstr "ไม่พบคอร์สที่ตรงกับการค้นหาของคุณ"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "No data yet!"
msgstr "ยังไม่มีข้อมูล!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No followed courses yet!"
msgstr "ยังไม่มีคอร์สที่ติดตาม!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "No leaderboard currently :("
msgstr "ขณะนี้ไม่มีกระดานผู้นำ :("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "No presentation available."
msgstr "ไม่มีงานนำเสนอ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No results found for '"
msgstr "ไม่พบผลลัพธ์สำหรับ '"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__none
msgid "None"
msgstr "ไม่มี"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Not enough karma to comment"
msgstr "คะแนน Karma ไม่พอให้ออกความเห็น"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Not enough karma to review"
msgstr "คะแนน Karma ไม่พอให้รีวิว"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Notifications"
msgstr "การแจ้งเตือน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_document
msgid "Number of Documents"
msgstr "จำนวนของของเอกสาร"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_infographic
msgid "Number of Infographics"
msgstr "จำนวนของอินโฟกราฟิกส์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_presentation
msgid "Number of Presentations"
msgstr "จำนวนของงานนำเสนอ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_quiz
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_quiz
msgid "Number of Quizs"
msgstr "จำนวนของควิซ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_video
msgid "Number of Videos"
msgstr "จำนวนของวิดีโอ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_webpage
msgid "Number of Webpages"
msgstr "จำนวนของเว็บเพจ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__comments_count
msgid "Number of comments"
msgstr "จำนวนของความคิดเห็น"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "จํานวนข้อความที่ต้องการการดําเนินการ"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread_counter
msgid "Number of unread messages"
msgstr "จํานวนข้อความที่ยังไม่ได้อ่าน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__questions_count
msgid "Numbers of Questions"
msgstr "จำนวนของคำถาม"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Odoo"
msgstr "Odoo"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Odoo • Image and Text"
msgstr "Odoo • ภาพและข้อความ"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_officer
msgid "Officer"
msgstr "เจ้าหน้าที่"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__invite
msgid "On Invitation"
msgstr "ตามคำเชิญ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Once you're done, don't forget to <b>Publish</b> your course."
msgstr "เมื่อเสร็จแล้ว อย่าลืม <b>เผยแพร่</b> คอร์สของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Only JPG, PNG, PDF, files types are supported"
msgstr "รองรับเฉพาะไฟล์ JPG PNG PDF"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"ตัวเลือกภาษาการแปล (รหัส ISO) เพื่อเลือกเมื่อส่งอีเมล หากไม่ได้ตั้งค่าไว้ "
"ระบบจะใช้เวอร์ชันภาษาอังกฤษ "
"โดยปกติควรเป็นข้อความนิพจน์ตัวอย่างที่ใช้ภาษาที่เหมาะสม เช่น {{ "
"object.partner_id.lang }}"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__null_value
msgid "Optional value to use if the target field is empty"
msgstr "ค่าทางเลือกที่จะใช้ถ้าช่องเป้าหมายว่างเปล่า"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Options"
msgstr "ตัวเลือก"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__partner_id
msgid "Partner"
msgstr "พาร์ทเนอร์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_has_new_content
msgid "Partner Has New Content"
msgstr "พาร์ทเนอร์มีเนื้อหาใหม่"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"People already took this quiz. To keep course progression it should not be "
"deleted."
msgstr ""
"ผู้คนได้ทำแบบทดสอบนี้ไปแล้ว เพื่อให้ความคืบหน้ายังคงอยู่ หลักสูตรไม่ควรถูกลบ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__copyvalue
msgid "Placeholder Expression"
msgstr "ข้อความนิพจน์ตัวอย่าง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Please"
msgstr "กรุณา"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid "Please <a href=\"/web/login?redirect=%s\">login</a> to join this course"
msgstr ""
"โปรด <a href=\"/web/login?redirect=%s\">ลงชื่อเข้าใช้</a> เพื่อเข้าร่วมคอร์ส"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Please <a href=\"/web/login?redirect=%s\">login</a> to vote this lesson"
msgstr ""
"โปรด <a href=\"/web/login?redirect=%s\">ลงชื่อเข้าใช้</a> เพื่อโหวตบทเรียน"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid ""
"Please <a href=\"/web/signup?redirect=%s\">create an account</a> to join "
"this course"
msgstr "โปรด <a href=\"/web/signup?redirect=%s\">สร้างบัญชี</a>เพื่อเข้าสู่คอร์ส"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid ""
"Please <a href=\"/web/signup?redirect=%s\">create an account</a> to vote "
"this lesson"
msgstr "โปรด <a href=\"/web/signup?redirect=%s\">สร้างบัญชี</a>เพื่อโหวตบทเรียน"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Please enter valid Youtube or Google Doc URL"
msgstr "โปรดป้อน URL ของ Youtube หรือ Google Doc ที่ถูกต้อง"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Please enter valid youtube or google doc url"
msgstr "โปรดป้อน youtube หรือ google doc url ที่ถูกต้อง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz_question_form.js:0
#, python-format
msgid "Please fill in the question"
msgstr "กรุณากรอกคำถาม"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "กรุณากรอกข้อมูลในช่องนี้"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid "Please select at least one recipient."
msgstr "โปรดเลือกผู้รับอย่างน้อยหนึ่งราย"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_course
#: model:gamification.challenge.line,name:website_slides.badge_data_course_challenge_line_0
#: model:gamification.goal.definition,name:website_slides.badge_data_course_goal
msgid "Power User"
msgstr "ผู้ใช้ที่ทรงพลัง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Powered by"
msgstr "สนับสนุนโดย"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__slide_id
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__presentation
#: model:slide.slide,name:website_slides.slide_slide_demo_4_12
#, python-format
msgid "Presentation"
msgstr "งานนำเสนอ"

#. module: website_slides
#: model:mail.message.subtype,description:website_slides.mt_channel_slide_published
#: model:mail.message.subtype,name:website_slides.mt_channel_slide_published
msgid "Presentation Published"
msgstr "เผยแพร่งานนำเสนอ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_presentation
msgid "Presentations"
msgstr "งานนำเสนอ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Preview"
msgstr "ดูตัวอย่าง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Private Course"
msgstr "คอร์สส่วนตัว"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Progress"
msgstr "ความคืบหน้า"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promote_strategy
msgid "Promoted Content"
msgstr "เนื้อหาที่ถูกโปรโมต"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promoted_slide_id
msgid "Promoted Slide"
msgstr "สไลด์ที่ถูกโปรโมต"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__public
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__public
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Public"
msgstr "สาธารณะ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Public Views"
msgstr "การชมสาธารณะ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "วันที่เผยแพร่"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__date_published
msgid "Publish Date"
msgstr "วันที่เผยแพร่"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Published"
msgstr "เผยแพร่"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Published Date"
msgstr "วันที่เผยแพร่"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Publishing is restricted to the responsible of training courses or members "
"of the publisher group for documentation courses"
msgstr ""
"การเผยแพร่ถูกจำกัดให้รับผิดชอบภายในหลักสูตรการฝึกอบรมหรือสมาชิกของกลุ่มผู้จัดพิมพ์สำหรับเอกสารกำกับหลักสูตร"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__question_id
msgid "Question"
msgstr "คำถาม"

#. module: website_slides
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer"
msgstr "คำถาม \"%s\" ต้องมี 1 คำตอบที่ถูกต้อง"

#. module: website_slides
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer and at least 1 incorrect answer"
msgstr "คำถาม \"%s\" ต้องมี 1 คำตอบที่ถูกต้อง และอย่างน้อย 1 คำตอบที่ผิด"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__question
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Question Name"
msgstr "ชื่อคำถาม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__question_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Questions"
msgstr "คำถาม"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__quiz
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Quiz"
msgstr "ควิซ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Quiz Demo Data"
msgstr "ข้อมูลการสาธิตแบบทดสอบ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__quiz_attempts_count
msgid "Quiz attempts count"
msgstr "จำนวนครั้งที่ร่วมทดสอบ"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_question_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_quizzes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree_report
msgid "Quizzes"
msgstr "ควิซ"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_ids
msgid "Rating"
msgstr "การให้คะแนน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_graph_slide_channel
msgid "Rating Average"
msgstr "คะแนนเฉลี่ย"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_stars
msgid "Rating Average (Stars)"
msgstr "คะแนนเฉลี่ย (ดาว)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "การให้คะแนน ความคิดเห็นล่าสุด"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_image
msgid "Rating Last Image"
msgstr "ให้คะแนนภาพสุดท้าย"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_value
msgid "Rating Last Value"
msgstr "ให้คะแนนค่าล่าสุด"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_count
msgid "Rating count"
msgstr "จำนวนการให้คะแนน"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Rating of %s"
msgstr "คะแนนของ %s"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_karma_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_karma
msgid "Reach 2000 XP"
msgstr "ถึง 2000 XP"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Reach new heights"
msgstr "บรรลุระดับที่สูงขึ้น"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__rating_last_feedback
msgid "Reason of the rating"
msgstr "เหตุผลของการให้คะแนน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__partner_ids
msgid "Recipients"
msgstr "ผู้รับ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/components/activity/activity.xml:0
#, python-format
msgid "Refuse Access"
msgstr "ปฏิเสธการเข้าถึง"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_register_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_register
msgid "Register to the platform"
msgstr "ลงทะเบียนเข้าสู่แพลตฟอร์ม"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Related"
msgstr "เกี่ยวข้อง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Remove"
msgstr "นำออก"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Remove the answer comment"
msgstr "ลบคำตอบความคิดเห็น"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Remove this answer"
msgstr "ลบคำตอบ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__render_model
msgid "Rendering Model"
msgstr "โมเดลการแสดงผล"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report
msgid "Reporting"
msgstr "การรายงาน"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Request Access."
msgstr "ร้องขอการเข้าถึง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Request sent !"
msgstr "คำร้องถูกส่ง !"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Reset"
msgstr "คืนค่าเดิม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__data
msgid "Resource"
msgstr "แหล่งข้อมูล"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Resources"
msgstr "แหล่งข้อมูล"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__user_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_user_id
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Responsible"
msgstr "รับผิดชอบ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Responsible already contacted."
msgstr "ผู้รับผิดชอบได้ติดต่อไปแล้ว"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_id
msgid "Restrict publishing to this website."
msgstr "จำกัดการเผยแพร่ในเว็บไซต์นี้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Retry"
msgstr "ลองใหม่"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.private_profile
msgid "Return to the course."
msgstr "กลับสู่คอร์ส"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Review"
msgstr "รีวิว"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Review Course"
msgstr "รีวิวคอร์ส"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_reviews
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_reviews
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Reviews"
msgstr "รีวิว"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/portal_chatter.js:0
#, python-format
msgid "Reviews (%d)"
msgstr "รีวิว (%d)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_fourth_attempt_reward
msgid "Reward: every attempt after the third try"
msgstr "รางวัล: ทุกความพยายามหลังจากพยายามครั้งที่สาม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_first_attempt_reward
msgid "Reward: first attempt"
msgstr "รางวัล: ความพยายามครั้งแรก"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_second_attempt_reward
msgid "Reward: second attempt"
msgstr "รางวัล: ความพยายามครั้งที่สอง"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_third_attempt_reward
msgid "Reward: third attempt"
msgstr "รางวัล: ความพยายามครั้งที่สาม"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Rewards"
msgstr "รางวัล"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_seo_optimized
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_seo_optimized
msgid "SEO optimized"
msgstr "ปรับ SEO ให้เหมาะสม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Save"
msgstr "บันทึก"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Save & Publish"
msgstr "บันทึก & เผยแพร่"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Save your presentations or documents as PDF files and upload them."
msgstr "บันทึกงานนำเสนอหรือเอกสารของคุณเป็นไฟล์ PDF แล้วอัปโหลด"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search"
msgstr "ค้นหา"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Search Contents"
msgstr "ค้นหาเนื้อหา"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Search courses"
msgstr "ค้นหาคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search in content"
msgstr "ค้นหาในเนื้อหา"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Second attempt"
msgstr "ความพยายามครั้งที่สอง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__category_id
#, python-format
msgid "Section"
msgstr "หัวข้อ"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Section Subtitle"
msgstr "คำบรรยายส่วน"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Section name"
msgstr "ชื่อส่วน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__access_token
msgid "Security Token"
msgstr "โทเคนความปลอดภัย"

#. module: website_slides
#: code:addons/website_slides/models/res_users.py:0
#, python-format
msgid "See our eLearning"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Select <b>Course</b> to create it and manage it."
msgstr "เลือก <b>คอร์ส</b> สำหรับสร้างและจัดการ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Select page to start with"
msgstr "เลือกหน้าที่จะเริ่มด้วย"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"เลือกฟิลด์เป้าหมายจากแบบจำลองเอกสารที่เกี่ยวข้อง\n"
"ถ้าเป็นฟิลด์ความสัมพันธ์ คุณจะสามารถเลือกฟิลด์เป้าหมายที่ปลายทางของความสัมพันธ์ได้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Select the correct answer below :"
msgstr "เลือกคำตอบที่ถูกต้องด้านล่าง :"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_sale_slides
msgid "Sell on eCommerce"
msgstr "ขายบนอีคอมเมิร์ซ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Send"
msgstr "ส่ง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Send Email"
msgstr "ส่งอีเมล"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__seo_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__seo_name
msgid "Seo name"
msgstr "ชื่อ Seo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_question__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Set Done"
msgstr "ตั้งค่าเสร็จ"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.website_slides_action_settings
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_settings
msgid "Settings"
msgstr "ตั้งค่า"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "Share"
msgstr "แชร์"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Share Channel"
msgstr "แชร์ช่อง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
#, python-format
msgid "Share Link"
msgstr "แชร์ลิงก์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_template_id
msgid "Share Template"
msgstr "แชร์เทมเพลต"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Share by mail"
msgstr "แชร์โดยเมล"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Facebook"
msgstr "แชร์บน Facebook"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on LinkedIn"
msgstr "แชร์บน LinkedIn"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_body
#, python-format
msgid "Share on Social Networks"
msgstr "แชร์บนสื่อสังคมออนไลน์"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Twitter"
msgstr "แชร์บน Twitter"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_short
msgid "Short Description"
msgstr "คำอธิบายสั้น ๆ "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_4
msgid "Show your newly mastered knowledge !"
msgstr "แสดงความรู้ที่เชี่ยวชาญใหม่ของคุณ !"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign Up !"
msgstr "ลงชื่อเข้าใช้ !"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Sign in"
msgstr "ลงชื่อเข้าใช้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign in and join the course to verify your answers!"
msgstr "ลงชื่อเข้าใช้และเข้าร่วมหลักสูตรเพื่อยืนยันคำตอบของคุณ!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Size"
msgstr "ไซส์"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Skill up and have an impact! Your business career starts here.<br/>Time to "
"start a course."
msgstr ""
"ทักษะขึ้นและมีความเปลี่ยนแปลง! "
"อาชีพธุรกิจของคุณเริ่มต้นที่นี้<br/>ถึงเวลาเริ่มคอร์สของคุณ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__slide_id
msgid "Slide"
msgstr "สไลด์"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr "สไลด์ / พาร์ทเนอร์ตกแต่ง m2m"

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_published
msgid "Slide Published"
msgstr "เผยแพร่สไลด์"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_answer
msgid "Slide Question's Answer"
msgstr "คำตอบของคำถามสไลด์"

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_shared
msgid "Slide Shared"
msgstr "แชร์สไลด์"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_tag
msgid "Slide Tag"
msgstr "แท็กสไลด์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_partner_ids
msgid "Slide User Data"
msgstr "ข้อมูลผู้ใช้สไลด์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_id
msgid "Slide channel"
msgstr "ช่องสไลด์"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#, python-format
msgid "Slide image"
msgstr "ภาพสไลด์"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Slide with questions must be marked as done when submitting all good answers"
" "
msgstr ""
"สไลด์ที่มีคำถามต้องทำเครื่องหมายว่าเสร็จสิ้นเมื่อส่งคำตอบที่ดีทั้งหมดแล้ว"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_content_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_ids
msgid "Slides"
msgstr "สไลด์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_ids
msgid "Slides and categories"
msgstr "สไลด์และหมวดหมู่"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_6_furn3
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_6_furn3
msgid "So much amazing certification."
msgstr "หลากหลายการรับรองน่าทึ่ง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Sort by"
msgstr "เรียงลำดับตาม"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__specific
msgid "Specific"
msgstr "เฉพาะ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Start Course"
msgstr "เริ่มคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Start Course Channel"
msgstr "เริ่มช่องหลักสูตร"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Start with the customer – find out what they want and give it to them."
msgstr "เริ่มที่ลูกค้า ค้นหาสิ่งที่พวกเขาต้องการและมอบให้พวกเขา"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Start your online course today!"
msgstr "เริ่มหลักสูตรออนไลน์ของคุณวันนี้!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Statistics"
msgstr "สถิติ"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"สถานะอิงตามกิจกรรม\n"
"เกินกำหนด: วันที่ครบกำหนดผ่านไปแล้ว\n"
"วันนี้: วันที่จัดกิจกรรมคือวันนี้\n"
"วางแผน: กิจกรรมในอนาคต"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__sub_model_object_field
msgid "Sub-field"
msgstr "ฟิลด์ย่อย"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__sub_object
msgid "Sub-model"
msgstr "โมเดลย่อย"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__subject
msgid "Subject"
msgstr "ชื่อเรื่อง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Subject..."
msgstr "ชื่อเรื่อง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "Subscribe"
msgstr "ติดตาม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information"
msgstr "ข้อมูลสมาชิกผู้ติดตาม"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information for the current logged in user"
msgstr "ข้อมูลผู้ติดตามสำหรับผู้ใช้ที่เข้าสู่ระบบในปัจจุบัน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__partner_ids
msgid "Subscribers"
msgstr "ผู้ติดตาม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_partner_ids
msgid "Subscribers information"
msgstr "ข้อมูลสมาชิกผู้ติดตาม"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Succeed and gain karma"
msgstr "สำเร็จและ ตะแนน karma เพิ่ม"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/xml/website_slides_channel_tag.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_form
#, python-format
msgid "Tag"
msgstr "แท็ก"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel_tag.xml:0
#, python-format
msgid "Tag Group"
msgstr "แท็กกลุ่ม"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#, python-format
msgid "Tag Group (required for new tags)"
msgstr "แท็กกลุ่ม (จำเป็นสำหรับแท็กใหม่)"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Tag Name"
msgstr "ชือแท็ก"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag__color
msgid ""
"Tag color used in both backend and website. No color means no display in "
"kanban or front-end, to distinguish internal tags from public categorization"
" tags"
msgstr ""
"แท็กสีใช้ทั้งในส่วนหลังและเว็บไซต์ "
"แท็กไม่มีสีหมายถึงไม่มีการแสดงในคัมบังหรือส่วนหน้า "
"เพื่อแยกแท็กภายในออกจากแท็กการจัดหมวดหมู่สาธารณะ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__tag_ids
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_data_other
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
#, python-format
msgid "Tags"
msgstr "แท็ก"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Tags..."
msgstr "แท็ก..."

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_1_gard1
msgid "Taking care of Trees"
msgstr "ดูแลต้นไม้"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_4_1
msgid "Technical Drawings"
msgstr "ภาพร่างทางเทคนิค"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_10
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_11
msgid "Technical drawing"
msgstr "ภาพร่างทางเทคนิค"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_2
msgid "Test Yourself"
msgstr "ทดสอบตัวเอง"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_4
msgid "Test your knowledge"
msgstr "ทดสอบความรู้ของคุณ"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_3
msgid "Test your knowledge !"
msgstr "ทดสอบความรู้ของคุณ !"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Test your students with small Quizzes"
msgstr "ทดสอบนักเรียนของคุณด้วยควิซเล็ก ๆ "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"The <b>Duration</b> of the lesson is based on the number of pages of your "
"document. You can change this number if your attendees will need more time "
"to assimilate the content."
msgstr ""
"<b>ระยะเวลา</b> ของบทเรียนขึ้นอยู่กับจำนวนหน้าในเอกสารของคุณ "
"คุณสามารถเปลี่ยนหมายเลขนี้ได้หากผู้เข้าร่วมของคุณจะต้องใช้เวลามากขึ้นในการทำความเข้าใจเนื้อหา"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"The <b>Title</b> of your lesson is autocompleted but you can change it if "
"you want.</br>A <b>Preview</b> of your file is available on the right side "
"of the screen."
msgstr ""
"<b>ขื่อ</b>ของบทเรียนของคุณเป็นแบบเติมข้อความอัตโนมัติ "
"แต่คุณสามารถเปลี่ยนได้หากต้องการ</br> "
"<b>ตัวอย่าง</b>ของไฟล์ของคุณจะอยู่ทางด้านขวาของหน้าจอ"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__is_preview
msgid ""
"The course is accessible by anyone : the users don't need to join the "
"channel to access the content of the course."
msgstr ""
"ทุกคนสามารถเข้าถึงคอร์สได้: "
"ผู้ใช้ไม่จำเป็นต้องเข้าร่วมช่องเพื่อเข้าถึงเนื้อหาของหลักสูตร"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description_short
msgid "The description that is displayed on the course card"
msgstr "คำอธิบายที่ปรากฏบนการ์ดคอร์ส"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description
msgid ""
"The description that is displayed on top of the course page, just below the "
"title"
msgstr "คำอธิบายที่แสดงอยู่ด้านบนของหน้าคอร์ส ข้างใต้ชื่อคอร์ส"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_type
msgid ""
"The document type will be set automatically based on the document URL and "
"properties (e.g. height and width for presentation and document)."
msgstr ""
"ประเภทเอกสารจะถูกตั้งค่าโดยอัตโนมัติตาม URL ของเอกสารและคุณสมบัติ (เช่น "
"ความสูงและความกว้างสำหรับงานนำเสนอและเอกสาร)"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__completion_time
msgid "The estimated completion time for this slide"
msgstr "เวลาเสร็จสิ้นโดยประมาณสำหรับสไลด์นี้"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external sign up in configuration."
msgstr ""
"ผู้รับต่อไปนี้ไม่มีบัญชีผู้ใช้: "
"%sคุณควรสร้างบัญชีผู้ใช้สำหรับพวกเขาหรืออนุญาตให้ลงชื่อเข้าใช้งานจากภายนอกในการกำหนดค่า"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_url
msgid "The full URL to access the document through the website."
msgstr "URL แบบเต็มเพื่อเข้าถึงเอกสารผ่านเว็บไซต์"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_theory
msgid "Theory"
msgstr "ทฤษฎี"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "There are no quizzes"
msgstr "ไม่มีแบบควิซ"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel_report
msgid "There are no ratings for these courses at the moment"
msgstr "ไม่มีการให้คะแนนสำหรับหลักสูตรเหล่านี้ในขณะนี้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "There was an error validating this quiz."
msgstr "เกิดข้อผิดพลาดในการตรวจสอบควิซนี้"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__url
msgid "Third Party Website URL"
msgstr "URL เว็บไซต์บุคคลที่สาม"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Third attempt"
msgstr "ความพยายามครั้งที่สาม"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_answer__comment
msgid "This comment will be displayed to the user if he selects this answer"
msgstr "ความคิดเห็นนี้จะแสดงให้ผู้ใช้เห็นหากเขาเลือกคำตอบนี้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "This course is private."
msgstr "คอร์สนี้เป็นคอร์สส่วนตัว"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "This is the correct answer"
msgstr "นี่คือคำตอบที่ถูกต้อง"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "This is the correct answer, congratulations"
msgstr "นี่คือคำตอบที่ถูกต้อง ยินดีด้วย"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "This quiz is already done. Retaking it is not possible."
msgstr "แบบทดสอบนี้ได้ทำไปแล้ว ไม่สามารถนำกลับมาใช้ใหม่ได้"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This video already exists in this channel on the following slide: %s"
msgstr "มีวิดีโอนี้อยู่แล้วในช่องนี้บนสไลด์ต่อไปนี้: %s"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_link__name
#, python-format
msgid "Title"
msgstr "ชื่อเรื่อง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Toggle navigation"
msgstr "ระบบนำทางแบบสลับ"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_tools
msgid "Tools"
msgstr "เครื่องมือ"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_0
msgid "Tools and Methods"
msgstr "เครื่องมือและวิธีการ"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_0
msgid "Tools you will need to complete this course."
msgstr "เครื่องมือที่คุณต้องใช้ในการเรียนคอร์สนี้"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Total"
msgstr "รวม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_slides
msgid "Total Slides"
msgstr "สไลด์ทั้งหมด"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Total Views"
msgstr "จำนวนยอดเข้าชมทั้งหมด"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__training
msgid "Training"
msgstr "อบรม"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_channel.xml:0
#, python-format
msgid "Training Layout"
msgstr "เลย์เอาท์อบรม"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_0
msgid "Tree Infographic"
msgstr "อินโฟกราฟิกต้นไม้"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_2
msgid "Tree planting in hanging bottles on wall"
msgstr "ปลูกต้นไม้ในขวดแขวนบนผนัง"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_0
msgid "Trees"
msgstr "ต้นไม้"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_2_gard2
msgid "Trees, Wood and Gardens"
msgstr "ต้นไม้ ไม้ และสวน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_type
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Type"
msgstr "ประเภท"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "พิมพ์กิจกรรมข้อยกเว้นบนบันทึก"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "ไม่สามารถโพสต์ข้อความ โปรดกำหนดค่าที่อยู่อีเมลของผู้ส่ง"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#, python-format
msgid "Uncategorized"
msgstr "ไม่ถูกจัดหมวด"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_0
msgid "Unforgettable Tools"
msgstr "เครื่องมือที่น่าจดจำ"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Unknown document"
msgstr "เอกสารที่ไม่รู้จัก"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Unknown error"
msgstr "ข้อผิดพลาดที่ไม่รู้จัก"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Unknown error, try again."
msgstr "ข้อผิดพลาดที่ไม่รู้จัก โปรดลองอีกครั้ง"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Unpublished"
msgstr "ยกเลิกการเผยแพร่"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread_counter
msgid "Unread Messages Counter"
msgstr "ตัวนับข้อความที่ยังไม่ได้อ่าน"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Update"
msgstr "อัปเดต"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__upload_group_ids
msgid "Upload Groups"
msgstr "อัปโหลดกลุ่ม"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "Upload Presentation"
msgstr "อัปโหลดการนำเสนอ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Upload a document"
msgstr "อัปโหลดเอกสาร"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_id
msgid "Uploaded by"
msgstr "อัปโหลดโดย"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Uploading document ..."
msgstr "กำลังอัปโหลดเอกสาร ..."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__tag_ids
msgid "Used to categorize and filter displayed channels/courses"
msgstr "ใช้เพื่อจัดหมวดหมู่และตัวกรองสำหรับช่อง/หลักสูตรที่แสดง"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__color
msgid "Used to decorate kanban view"
msgstr "ใช้สำหรับตกแต่งมุมมองคัมบัง"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_vote
msgid "User vote"
msgstr "โหวตของผู้ใช้"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_users
msgid "Users"
msgstr "ผู้ใช้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__video
#, python-format
msgid "Video"
msgstr "วิดีโอ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_video
msgid "Videos"
msgstr "วิดีโอ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "View"
msgstr "ดู"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "View all"
msgstr "ดูทั้งหมด"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "View course"
msgstr "ดูคอร์ส"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Views"
msgstr "ยอดเข้าชม"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_card
msgid "Views •"
msgstr "ยอดเข้าชม •"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_visibility
msgid "Visibility"
msgstr "การมองเห็น"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_published
msgid "Visible on current website"
msgstr "ปรากฏบนเว็บไซต์ปัจจุบัน"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Visits"
msgstr "เยี่ยมชม"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_vote
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__vote
msgid "Vote"
msgstr "โหวต"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_votes
msgid "Votes"
msgstr "โหวต"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Votes and comments are disabled for this course"
msgstr "การโหวตและความคิดเห็นถูกปิดการใช้งานสำหรับคอร์สนี้"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Waiting for validation"
msgstr "รอการตรวจสอบ"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_1
msgid "Watching the master(s) at work"
msgstr "ดูผู้เชี่ยวชาญเวลาทำงาน"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_4
msgid ""
"We had a little chat with Harry Potted, sure he had interesting things to "
"say !"
msgstr "เราได้คุยกับ Harry Potted นิดหน่อย มั่นใจว่าเขามีเรื่องน่าสนใจจะพูด !"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__webpage
#, python-format
msgid "Web Page"
msgstr "เว็บเพจ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_webpage
msgid "Webpages"
msgstr "เว็บเพจ"

#. module: website_slides
#: model:ir.model,name:website_slides.model_website
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_id
msgid "Website"
msgstr "เว็บไซต์"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__gamification_challenge__challenge_category__slides
msgid "Website / Slides"
msgstr "เว็บไซต์ / สไลด์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_url
msgid "Website URL"
msgstr "URL เว็บไซต์"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_description
msgid "Website meta description"
msgstr "คำอธิบายเนื้อหาเว็บไซต์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_keywords
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_keywords
msgid "Website meta keywords"
msgstr "คำสำคัญในคำอธิบายเนื้อหาเว็บไซต์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_title
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_title
msgid "Website meta title"
msgstr "ชื่อข้อมูลเว็บไซต์"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_og_img
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_og_img
msgid "Website opengraph image"
msgstr "รูป Opengraph บนเว็บไซต์"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Welcome on your course's home page. It's still empty for now. Click on "
"\"<b>New</b>\" to write your first course."
msgstr ""
"ยินดีต้อนรับสู่หน้าแรกของหลักสูตรของคุณ ตอนนี้ยังว่างอยู่ครับ คลิกที่ "
"\"<b>ใหม่</b>\" เพื่อเขียนคอร์สแรกของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "What does"
msgstr "อะไรคือ"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_0
msgid "What is a strawberry ?"
msgstr "สตรอเบอรี่คืออะไร ?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_1
msgid "What is the best tool to dig a hole for your plants ?"
msgstr "เครื่องมือใดที่ดีที่สุดในการขุดหลุมให้ต้นไม้ของคุณ ?"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_2
msgid "What was the question again ?"
msgstr "คำถามอะไรนะ ?"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"เมื่อฟิลด์ความสัมพันธ์ถูกเลือกเป็นฟิลด์แรก "
"ฟิลด์นี้จะให้คุณเลือกฟิลด์เป้าหมายภายในโมเดลเอกสารปลายทาง (โมเดลย่อย)"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"เมื่อฟิลด์ความสัมพันธ์ถูกเลือกเป็นฟิลด์แรก "
"ฟิลด์นี้จะแสดงโมเดลเอกสารที่ความสัมพันธ์ไปถึง"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_1
msgid ""
"Which wood type is best for my solid wood furniture? That's the question we "
"help you answer in this video !"
msgstr ""
"ไม้ชนิดใดที่เหมาะกับกสรทำเฟอร์นิเจอร์ไม้เนื้อแข็งของฉันมากที่สุด? "
"นั่นคือคำถามที่เราช่วยคุณตอบในวิดีโอนี้ !"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"With Quizzes you can keep your students focused and motivated by answering "
"some questions and gaining some karma points"
msgstr ""
"ด้วยแบบทดสอบ "
"คุณสามารถให้นักเรียนจดจ่อและมีแรงจูงใจโดยการตอบคำถามบางข้อและรับคะแนนกรรม"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_1
msgid "Wood"
msgstr "ไม้"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_1
msgid "Wood Bending With Steam Box"
msgstr "ดัดไม้ด้วยหม้อพักไอ"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_3
msgid "Wood Characteristics"
msgstr "ลักษณะไม้"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_1
msgid "Wood Types"
msgstr "ชนิดไม้"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_3_0
msgid "Working with Wood"
msgstr "ทำงานกับไม้"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product or services. <br>To be "
"successful your content needs to be useful to your readers."
msgstr ""
"เขียนหนึ่งหรือสองย่อหน้าเพื่ออธิบายผลิตภัณฑ์ บริการ "
"หรือคุณลักษณะเฉพาะของคุณ<br>การจะประสบความสำเร็จได้นั้น "
"เนื้อหาของคุณต้องเป็นประโยชน์ต่อผู้อ่านของคุณ"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"เขียนหนึ่งหรือสองย่อหน้าเพื่ออธิบายผลิตภัณฑ์ บริการ "
"หรือคุณลักษณะเฉพาะของคุณ<br>การจะประสบความสำเร็จได้นั้น "
"เนื้อหาของคุณต้องเป็นประโยชน์ต่อผู้อ่านของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "XP"
msgstr "XP"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_0
#, python-format
msgid "Yes"
msgstr "ใช่"

#. module: website_slides
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid ""
"You are not allowed to add members to this course. Please contact the course"
" responsible or an administrator."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"You can add <b>comments</b> on answers. This will be visible with the "
"results if the user select this answer."
msgstr ""
"คุณสามารถเพิ่ม<b>ความคิดเห็น</b> ในคำตอบ "
"ข้อความนี้จะปรากฏพร้อมกับผลลัพธ์หากผู้ใช้เลือกคำตอบนี้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "You can not upload password protected file."
msgstr "คุณไม่สามารถอัปโหลดไฟล์ที่มีการป้องกันด้วยรหัสผ่าน"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You cannot add tags to this course."
msgstr "คุณไม่สามารถเพิ่มแท็กในคอร์สนี้"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as completed if you are not among its members."
msgstr "คุณไม่สามารถทำเครื่องหมายสไลด์ว่าเสร็จสิ้นหากคุณไม่ได้เป็นสมาชิก"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as viewed if you are not among its members."
msgstr "คุณไม่สามารถทำเครื่องหมายว่าดูสไลด์แล้ว หากคุณไม่ได้เป็นสมาชิก"

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"You cannot mark a slide quiz as completed if you are not among its members."
msgstr ""
"คุณไม่สามารถทำเครื่องหมายแบบทดสอบสไลด์ว่าเสร็จสิ้น หากคุณไม่ได้เป็นสมาชิก"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You cannot upload on this channel."
msgstr "คุณไม่สามารถอัปโหลดในช่องนี้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have access to this lesson"
msgstr "คุณไม่สามารถเข้าถึงบทเรียนนี้"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have enough karma to vote"
msgstr "คุณไม่มีคะแนน Karma มากพอที่จะโหวต"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "You gained"
msgstr "คุณได้รับ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid "You have already joined this channel"
msgstr "คุณได้เข้าร่วมช่องนี้แล้ว"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You have already voted for this lesson"
msgstr "คุณได้โหวตให้บทเรียนนี้แล้ว"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_invite
msgid "You have been invited to join {{ object.channel_id.name }}"
msgstr "คุณได้รับเชิญให้เข้าร่วม {{ object.channel_id.name }}"

#. module: website_slides
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "You have to sign in before"
msgstr "คุณต้องลงชื่อเข้าใช้ก่อน"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "You may now participate in our eLearning."
msgstr "ตอนนี้คุณสามารถเข้าร่วมการอบรมออนไลน์ของเราได้แล้ว"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "You must be logged to submit the quiz."
msgstr "คุณต้องเข้าสู่ระบบเพื่อส่งควิซ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You must be member of this course to vote"
msgstr "คุณต้องเป็นสมาชิกของคอร์สนี้จึงจะลงคะแนนได้"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_2
msgid "You won't believe those facts about carrots."
msgstr "คุณจะไม่เชื่อข้อเท็จจริงเหล่านั้นเกี่ยวกับแครอท"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "You're enrolled"
msgstr "คุณลงทะเบียนแล้ว"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Your"
msgstr " "

#. module: website_slides
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Your Google API key is invalid, please update it in your settings.\n"
"Settings > Website > Features > API Key"
msgstr ""
"Google API Key ของคุณไม่ถูกต้อง โปรดอัปเดตในการตั้งค่าของคุณ\n"
"การตั้งค่า > เว็บไซต์ > ฟีเจอร์ > คีย์ API"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_level
msgid "Your Level"
msgstr "ระดับของคุณ"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_role
msgid "Your Role"
msgstr "บทบาทของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Your first section is created, now it's time to add lessons to your course. "
"Click on <b>Add Content</b> to upload a document, create a web page or link "
"a video."
msgstr ""
"สร้างส่วนแรกของคุณแล้ว ตอนนี้ได้เวลาเพิ่มบทเรียนในหลักสูตรของคุณ คลิกที่ "
"<b>เพิ่มเนื้อหา</b> เพื่ออัปโหลดเอกสาร สร้างหน้าเว็บ หรือลิงก์วิดีโอ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Youtube Link"
msgstr "ลิงก์ Youtube "

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Youtube Video URL"
msgstr "URL วิดีโอ Youtube"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__document_id
msgid "Youtube or Google Document ID"
msgstr "Youtube หรือ Google Document ID"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__url
msgid "Youtube or Google Document URL"
msgstr "Youtube หรือ Google Document URL"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.achievement_card
msgid "achieved"
msgstr "ได้รับแล้ว"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "breadcrumb"
msgstr "breadcrumb"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "by email."
msgstr "โดยอีเมล"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "e.g. Computer Science for kids"
msgstr "เช่น วิทยาการคอมพิวเตอร์สำหรับเด็ก"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. How to grow your business with Odoo?"
msgstr "เช่น จะทำให้ธุรกิจของคุณเติบโตด้วย Odoo ได้อย่างไร?"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid ""
"e.g. In this video, we'll give you the keys on how Odoo can help you to grow"
" your business. At the end, we'll propose you a quiz to test your knowledge."
msgstr ""
"เช่น ในวิดีโอนี้ เราจะให้กุญแจแก่คุณเกี่ยวกับวิธีที่ Odoo "
"สามารถช่วยให้คุณเติบโตทางธุรกิจได้ ในตอนท้าย "
"เราจะมีแบบทดสอบเพื่อทดสอบความรู้ของคุณอีกด้วย"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "e.g. Introduction"
msgstr "เช่น บทนำ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "e.g. Your Level"
msgstr "เช่น ระดับของคุณ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. https://www.odoo.com"
msgstr "เช่น https://www.odoo.com"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_root
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "eLearning"
msgstr "eLearning"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_ids
msgid "eLearning Courses"
msgstr "คอร์สอบรมออนไลน์"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_overview
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "eLearning Overview"
msgstr "ภาพรวมการอบรมออนไลน์"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "login"
msgstr "ล็อกอิน"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"means? The YouTube \"unlisted\" means it is a video which can be viewed only"
" by the users with the link to it. Your video will never come up in the "
"search results nor on your channel."
msgstr ""
"และมันหมายความว่า? \"ไม่อยู่ในรายการ\" ของ YouTube "
"หมายความว่าเป็นวิดีโอที่สามารถดูได้โดยผู้ใช้ที่มีลิงก์ไปยังวิดีโอเท่านั้น "
"วิดีโอของคุณจะไม่ปรากฏในผลการค้นหาหรือในช่องของคุณ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_course_unsubscribe.js:0
#, python-format
msgid "or Leave the course"
msgstr "หรือออกจากคอร์ส"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "reviews"
msgstr "รีวิว"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#, python-format
msgid "sign in"
msgstr "ลงชื่อเข้าใช้"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid "steps"
msgstr "ขั้นตอน"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "to contact responsible."
msgstr "เพื่อติดต่อผู้รับผิดชอบ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
msgid "to download resources"
msgstr "เพื่อดาวน์โหลดแหล่งข้อมูล"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "to enroll."
msgstr "เพื่อลงทะเบียน"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "to share this"
msgstr "เพื่อแชร์"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "unlisted"
msgstr "ไม่อยู่ในรายการ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "xp"
msgstr "xp"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_shared
msgid "{{ user.name }} shared a {{ object.slide_type }} with you!"
msgstr "{{ user.name }} shared a {{ object.slide_type }} กับคุณ!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "└<span class=\"ml-1\">Uncategorized</span>"
msgstr "└<span class=\"ml-1\">ไม่มีหมวดหมู่</span>"
