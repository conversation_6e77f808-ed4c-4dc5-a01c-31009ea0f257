# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * resource
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-08 07:49+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: <PERSON><PERSON><PERSON> (http://www.transifex.com/odoo/odoo-9/language/kab/)\n"
"Language: kab\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: resource
#: code:addons/resource/resource.py:686
#, python-format
msgid "%s (copy)"
msgstr "%s (Anɣel )"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_active
msgid "Active"
msgstr "Urmid"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_closing_days
msgid "Closing Days"
msgstr "Azemz n tagara"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_code
msgid "Code"
msgstr "Tangalt"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_company_id
#: model:ir.model.fields,field_description:resource.field_resource_resource_company_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Company"
msgstr "Takebbwanit"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_create_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource_create_uid
msgid "Created by"
msgstr "Yerna-t"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_create_date
#: model:ir.model.fields,field_description:resource.field_resource_resource_create_date
msgid "Created on"
msgstr "Yerna di"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_dayofweek
msgid "Day of Week"
msgstr "Ass di Dduṛt"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Sbadu aɣawas n umahil"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_calendar_form
msgid ""
"Define working hours and time table that could be scheduled to your project "
"members"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_display_name
#: model:ir.model.fields,field_description:resource.field_resource_resource_display_name
msgid "Display Name"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
msgid "Duration"
msgstr "Timirt"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_time_efficiency
msgid "Efficiency Factor"
msgstr "Ameskar n tmellit"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_date_to
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_date_to
msgid "End Date"
msgstr "Azemz n tagara"

#. module: resource
#: constraint:resource.calendar.leaves:0
msgid "Error! leave start-date must be lower then leave end-date."
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Friday"
msgstr "Sem"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Group By"
msgstr "Sdukel s"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
msgid "Hours"
msgstr "Asragen"

#. module: resource
#: selection:resource.resource,resource_type:0
msgid "Human"
msgstr "Amdan"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_id
#: model:ir.model.fields,field_description:resource.field_resource_resource_id
msgid "ID"
msgstr "Asulay"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves_resource_id
msgid ""
"If empty, this is a generic holiday for the company. If a resource is set, "
"the holiday/leave is only for this resource"
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource_active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Inactive"
msgstr "Arurmid"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar___last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance___last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves___last_update
#: model:ir.model.fields,field_description:resource.field_resource_resource___last_update
msgid "Last Modified on"
msgstr "Aleqqem aneggaru di"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_write_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource_write_uid
msgid "Last Updated by"
msgstr "Aleqqem aneggaru sɣuṛ"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_write_date
#: model:ir.model.fields,field_description:resource.field_resource_resource_write_date
msgid "Last Updated on"
msgstr "Aleqqem aneggaru di"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_leaves
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Leave Detail"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Leave Month"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leave_ids
msgid "Leaves"
msgstr ""

#. module: resource
#: selection:resource.resource,resource_type:0
msgid "Material"
msgstr "Allal"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Monday"
msgstr "Arim"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_name
#: model:ir.model.fields,field_description:resource.field_resource_resource_name
msgid "Name"
msgstr "Isem"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Reason"
msgstr "Motif"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource_user_id
msgid "Related user name for the resource to manage its access."
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_resource_id
#: model:ir.ui.menu,name:resource.menu_resource_config
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Resource"
msgstr "Taɣbalut"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar
msgid "Resource Calendar"
msgstr ""

#. module: resource
#: model:ir.model,name:resource.model_resource_resource
msgid "Resource Detail"
msgstr ""

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_leave_tree
#: model:ir.ui.menu,name:resource.menu_view_resource_calendar_leaves_search
msgid "Resource Leaves"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_resource_type
msgid "Resource Type"
msgstr "Tawsit n teɣbalut"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_calendar_id
msgid "Resource's Calendar"
msgstr ""

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_resource_tree
#: model:ir.ui.menu,name:resource.menu_resource_resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_tree
msgid "Resources"
msgstr "Tiɣbula"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_resources_leaves
msgid "Resources Leaves"
msgstr ""

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_resource_tree
msgid ""
"Resources allow you to create and manage resources that should be involved "
"in a specific project phase. You can also set their efficiency level and "
"workload based on their weekly working hours."
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Saturday"
msgstr "Sed"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Search Resource"
msgstr "Nnadi taɣbalut"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Search Working Period Leaves"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
msgid "Search Working Time"
msgstr "Nnadi Akud n Umahil"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_date_from
msgid "Start Date"
msgstr "Azemz n tazwara"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance_hour_from
msgid "Start and End time of working."
msgstr "Tazwara d tagara n umahil"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_date_from
msgid "Starting Date"
msgstr "Azemz n tazwara"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Starting Date of Leave by Month"
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Sunday"
msgstr "Acer"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource_time_efficiency
msgid ""
"This field depict the efficiency of the resource to complete tasks. e.g  "
"resource put alone on a phase of 5 days with 5 tasks assigned to him, will "
"show a load of 100% for this phase by default, but if we put a efficiency of "
"200%, then his load will only be 50%."
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Thursday"
msgstr "Amhad"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Tuesday"
msgstr "Aram"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Type"
msgstr "Tawsit"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_user_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "User"
msgstr "Aseqdac"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Wednesday"
msgstr "Ahad"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_attendance
msgid "Work Detail"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_hour_from
msgid "Work from"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_hour_to
msgid "Work to"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_manager
msgid "Workgroup Manager"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Working Period"
msgstr "Tawala n umahil"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_form
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_ids
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_resource_calendar_id
#: model:ir.ui.menu,name:resource.menu_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_tree
msgid "Working Time"
msgstr "Akud n umahil"
