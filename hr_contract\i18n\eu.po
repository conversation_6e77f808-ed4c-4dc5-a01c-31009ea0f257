# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_contract
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Basque (https://www.transifex.com/odoo/teams/41243/eu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: eu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "<span class=\"text-muted\">(If fixed-term contract)</span>"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_advantages
msgid "Advantages"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Advantages..."
msgstr ""

#. module: hr_contract
#: selection:hr.contract,state:0
msgid "Cancelled"
msgstr "Ezeztatua"

#. module: hr_contract
#: model_terms:ir.actions.act_window,help:hr_contract.action_hr_contract
msgid "Click here to create new contracts."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_company_id
msgid "Company"
msgstr "Enpresa"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_vehicle
msgid "Company Vehicle"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Reference"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Terms"
msgstr ""

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract_type
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_id_5898
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Contract Type"
msgstr ""

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.action_hr_contract_type
#: model:ir.ui.menu,name:hr_contract.hr_menu_contract_type
msgid "Contract Types"
msgstr ""

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_pending
#: model:mail.message.subtype,description:hr_contract.mt_department_contract_pending
msgid "Contract about to expire"
msgstr ""

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_close
msgid "Contract expired"
msgstr ""

#. module: hr_contract
#: code:addons/hr_contract/models/hr_contract.py:100
#, python-format
msgid "Contract start date must be less than contract end date."
msgstr ""

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_department_contract_pending
msgid "Contract to Renew"
msgstr ""

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.act_hr_employee_2_hr_contract
#: model:ir.actions.act_window,name:hr_contract.action_hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_contract_ids
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_contracts_count
#: model:ir.ui.menu,name:hr_contract.hr_menu_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "Contracts"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_create_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_create_uid
msgid "Created by"
msgstr "Nork sortua"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_create_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_create_date
msgid "Created on"
msgstr "Created on"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_currency_id
msgid "Currency"
msgstr "Moneta"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "Current Contract"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Current Employee"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Delete"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_department_id
msgid "Department"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_display_name
msgid "Display Name"
msgstr "Izena erakutsi"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Edit Contract"
msgstr ""

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Employee"
msgstr ""

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract_wage
msgid "Employee's monthly gross wage."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract_resource_calendar_id
msgid "Employee's working schedule."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_date_end
msgid "End Date"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract_date_end
msgid "End date of the contract (if it's a fixed-term contract)."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract_trial_date_end
msgid "End date of the trial period (if there is one)."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_trial_date_end
msgid "End of Trial Period"
msgstr ""

#. module: hr_contract
#: selection:hr.contract,state:0
#: model:mail.message.subtype,name:hr_contract.mt_contract_close
msgid "Expired"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract_type_sequence
msgid "Gives the sequence when displaying a list of Contract."
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Group By"
msgstr "Group By"

#. module: hr_contract
#: model:ir.actions.server,name:hr_contract.ir_cron_data_contract_update_state_ir_actions_server
#: model:ir.cron,cron_name:hr_contract.ir_cron_data_contract_update_state
#: model:ir.cron,name:hr_contract.ir_cron_data_contract_update_state
msgid "HR Contract: update state"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_vehicle_distance
msgid "Home-Work Dist."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_id
msgid "ID"
msgstr "ID"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Important Messages"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_employee_vehicle_distance
msgid "In kilometers"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_manager
msgid "Is a Manager"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Job"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_job_id
msgid "Job Position"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract___last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type___last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_write_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_write_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_write_date
msgid "Last Updated on"
msgstr "Last Updated on"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_employee_contract_id
msgid "Latest contract of the employee"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "Medical Exam"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_medic_exam
msgid "Medical Examination Date"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Monthly Advantages in Cash"
msgstr ""

#. module: hr_contract
#: selection:hr.contract,state:0
msgid "New"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_notes
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Notes"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_children
msgid "Number of Children"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Other Information"
msgstr "Other Information"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_place_of_birth
msgid "Place of Birth"
msgstr ""

#. module: hr_contract
#: selection:hr.contract,state:0
msgid "Running"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Salary Information"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Search Contract"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_search
msgid "Search Contract Type"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type_sequence
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_search
msgid "Sequence"
msgstr "Sekuentzia"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_date_start
msgid "Start Date"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract_date_start
msgid "Start date of the contract."
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "State"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_state
msgid "Status"
msgstr "Egoera"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract_state
msgid "Status of the contract"
msgstr ""

#. module: hr_contract
#: selection:hr.contract,state:0
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model:mail.message.subtype,name:hr_contract.mt_contract_pending
msgid "To Renew"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_visa_expire
msgid "Visa Expire Date"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_visa_no
msgid "Visa No"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_wage
msgid "Wage"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_permit_no
msgid "Work Permit No"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_resource_calendar_id
msgid "Working Schedule"
msgstr ""
