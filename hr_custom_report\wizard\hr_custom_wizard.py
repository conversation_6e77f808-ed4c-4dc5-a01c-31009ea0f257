# -*- coding: utf-8 -*-

from odoo import fields, models
from dateutil.relativedelta import relativedelta
from datetime import date, datetime


class HrReportWizard(models.TransientModel):
    _name = 'hr.custom.wizard'
    _description = 'HR Report'


    fieldx_ids = fields.Many2many('ir.model.fields', domain="[('model','=','hr.employee'),('ttype','in',('char','date', 'datetime', 'integer','float','selection','text','many2one'))]")

    def _get_report_pdf(self):
        report_fields = [x.name for x in self.fieldx_ids]
        report_fields_label = [x.field_description for x in self.fieldx_ids]
        all_hr_employee = self.env['hr.employee'].search([])
        vals_list = []
        for elem in all_hr_employee:
            vals =  {}
            for field in self.fieldx_ids:
                if field.ttype in ('char','date', 'datetime', 'integer','float','selection','text'):
                    vals[field.name] = str(elem[field.name])
                elif field.ttype in ('many2one'):
                    vals[field.name] = str(elem[field.name].name)
            vals_list.append(vals)

        data = {'report_fields_label':report_fields_label,'report_fields':report_fields, 'vals':vals_list}
        return data

    def generate_report(self):
        data = self._get_report_pdf()
        return self.env.ref('hr_custom_report.hr_custom_fields_report').with_context(landscape=True).report_action([],data=data)







