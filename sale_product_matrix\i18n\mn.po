# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_matrix
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <bask<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Minj P <<EMAIL>>, 2021\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_template__product_add_mode
msgid "Add product mode"
msgstr ""

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,help:sale_product_matrix.field_product_template__product_add_mode
msgid ""
"Configurator: choose attribute values to add the matching         product variant to the order.\n"
"Grid: add several variants at once from the grid of attribute values"
msgstr ""

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr "Хүснэгтэн барааны загвар"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_update
msgid "Grid Update"
msgstr "Хүснэгт шинэчлэх"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__report_grids
msgid ""
"If set, the matrix of the products configurable by matrix will be shown on "
"the report of the order."
msgstr ""

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid
msgid "Matrix local storage"
msgstr ""

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__matrix
msgid "Order Grid Entry"
msgstr ""

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__report_grids
msgid "Print Variant Grids"
msgstr "Хувилбарын хүснэгтийг хэвлэх"

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__configurator
msgid "Product Configurator"
msgstr "Барааны тохируулга"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_product_template
msgid "Product Template"
msgstr "Барааны загвар"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_sale_order
msgid "Sales Order"
msgstr "Борлуулалтын захиалга"

#. module: sale_product_matrix
#: model_terms:ir.ui.view,arch_db:sale_product_matrix.product_template_grid_view_form
msgid "Sales Variant Selection"
msgstr ""

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid_product_tmpl_id
msgid "Technical field for product_matrix functionalities."
msgstr "Product_matrix функцийн техникийн талбар"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid
msgid ""
"Technical local storage of grid. \n"
"If grid_update, will be loaded on the SO. \n"
"If not, represents the matrix to open."
msgstr ""

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid_update
msgid "Whether the grid field contains a new matrix to apply or not."
msgstr ""

#. module: sale_product_matrix
#: code:addons/sale_product_matrix/models/sale_order.py:0
#, python-format
msgid ""
"You cannot change the quantity of a product present in multiple sale lines."
msgstr ""
