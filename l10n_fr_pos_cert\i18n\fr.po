# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_fr_pos_cert
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-27 14:45+0000\n"
"PO-Revision-Date: 2021-10-27 14:45+0000\n"
"Last-Translator: Rémi CAZENAVE <<EMAIL>>, 2021\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/res_company.py:0
#, python-format
msgid "(Receipt ref.: %s)"
msgstr "(Réf. reçu: %s)"

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid "According to French law, you cannot delet a point of sale order."
msgstr "Selon la loi Française, vous ne pouvez pas supprimer une commande de caisse."

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"According to the French law, you cannot modify a %s. Forbidden fields: %s."
msgstr ""
"Selon la loi française, vous ne pouvez pas modifier un %s. Champs interdits : %s."

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"According to the French law, you cannot modify a point of sale order line. "
"Forbidden fields: %s."
msgstr ""
"Selon la loi française, vous ne pouvez pas modifier une ligne de commande de caisse. "
"Champs interdits : %s."

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"According to the French law, you cannot modify a point of sale order. "
"Forbidden fields: %s."
msgstr ""
"Selon la loi française, vous ne pouvez pas modifier une commande de caisse. "
"Champs interdits : %s."

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.form_view_account_sale_closing
msgid "Account Closing"
msgstr "Clotûre de Compte"

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"An error occured when computing the inalterability. Impossible to get the "
"unique previous posted point of sale order."
msgstr ""
"Une erreur s'est produite lors de la vérification de l'inaltérabilité. "
"Impossible de récupérer la dernière commande de caisse unique et comptabilisée."

#. module: l10n_fr_pos_cert
#: model:ir.model.fields.selection,name:l10n_fr_pos_cert.selection__account_sale_closing__frequency__annually
msgid "Annual"
msgstr "Annuelle"

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_closing.py:0
#, python-format
msgid "Annual Closing"
msgstr "Clôture Annuelle"

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_account_bank_statement
msgid "Bank Statement"
msgstr "Relevé bancaire"

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Ligne de relevé bancaire"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__date_closing_stop
msgid "Closing Date"
msgstr "Date de clôture"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__frequency
msgid "Closing Type"
msgstr "Type de clôture"

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__company_id
msgid "Company"
msgstr "Société"

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "Contrôle des données du point de vente"
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/res_company.py:0
#, python-format
msgid "Corrupted data on point of sale order with id %s."
msgstr "Données corrompues sur la commande de caisse avec l'id %s."

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__create_date
msgid "Created on"
msgstr "Créé le"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__cumulative_total
msgid "Cumulative Grand Total"
msgstr "Grant Total Cumulé"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__currency_id
msgid "Currency"
msgstr "Devise"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields.selection,name:l10n_fr_pos_cert.selection__account_sale_closing__frequency__daily
msgid "Daily"
msgstr "Journalière"

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_closing.py:0
#, python-format
msgid "Daily Closing"
msgstr "Clôture Journalière"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__date_closing_start
msgid "Date from which the total interval is computed"
msgstr "Date à partir de laquelle le total de l'intervalle est calculé"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__date_closing_stop
msgid "Date to which the values are computed"
msgstr "Date jusqu'à laquelle les valeurs sont calculées"

#. module: l10n_fr_pos_cert
#. openerp-web
#: code:addons/l10n_fr_pos_cert/static/src/js/pos.js:0
#, python-format
msgid "Deleting of orders is not allowed."
msgstr "La suppression de commandes n'est pas autorisée."

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement_line__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_config__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_session__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_report_l10n_fr_pos_cert_report_pos_hash_integrity__display_name
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_res_company__display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "First Entry"
msgstr "Première Entrée"

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "First Hash"
msgstr "Premier Hachage"

#. module: l10n_fr_pos_cert
#. openerp-web
#: code:addons/l10n_fr_pos_cert/static/src/js/pos.js:0
#, python-format
msgid "Fiscal Data Module error"
msgstr "Erreur du Module de Données Fiscales"

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Position fiscale"

#. module: l10n_fr_pos_cert
#: model:ir.ui.menu,name:l10n_fr_pos_cert.pos_fr_statements_menu
msgid "French Statements"
msgstr "Relevés Français"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__name
msgid "Frequency and unique sequence number"
msgstr "Fréquence et numéro de séquence unique"

#. module: l10n_fr_pos_cert
#: model:ir.actions.server,name:l10n_fr_pos_cert.account_sale_closing_annually_ir_actions_server
#: model:ir.cron,cron_name:l10n_fr_pos_cert.account_sale_closing_annually
#: model:ir.cron,name:l10n_fr_pos_cert.account_sale_closing_annually
msgid "Generate Annual Sales Closing"
msgstr "Générer la clôture annuelle des ventes"

#. module: l10n_fr_pos_cert
#: model:ir.actions.server,name:l10n_fr_pos_cert.account_sale_closing_daily_ir_actions_server
#: model:ir.cron,cron_name:l10n_fr_pos_cert.account_sale_closing_daily
#: model:ir.cron,name:l10n_fr_pos_cert.account_sale_closing_daily
msgid "Generate Daily Sales Closing"
msgstr "Générer la clôture journalière des ventes"

#. module: l10n_fr_pos_cert
#: model:ir.actions.server,name:l10n_fr_pos_cert.account_sale_closing_monthly_ir_actions_server
#: model:ir.cron,cron_name:l10n_fr_pos_cert.account_sale_closing_monthly
#: model:ir.cron,name:l10n_fr_pos_cert.account_sale_closing_monthly
msgid "Generate Monthly Sales Closing"
msgstr "Générer la clôture mensuelle des ventes"

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_report_l10n_fr_pos_cert_report_pos_hash_integrity
msgid "Get french pos hash integrity result as PDF."
msgstr "Obtenir le résultat de la vérication d'intégrité de la caisse française en PDF."

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.pos_order_form_inherit
msgid "Hash"
msgstr "Hachage"

#. module: l10n_fr_pos_cert
#: model:ir.actions.report,name:l10n_fr_pos_cert.action_report_pos_hash_integrity
msgid "Hash integrity result PDF"
msgstr "Résultat d'intégrité en PDF"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement_line__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_config__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order_line__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_session__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_report_l10n_fr_pos_cert_report_pos_hash_integrity__id
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_res_company__id
msgid "ID"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order__l10n_fr_hash
msgid "Inalteralbility Hash"
msgstr "Hachage d'inaltérabilité"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order__l10n_fr_secure_sequence_number
msgid "Inalteralbility No Gap Sequence #"
msgstr "Numéro de séquence sans écart pour inaltérabilité"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_res_company__l10n_fr_pos_cert_sequence_id
msgid "L10N Fr Pos Cert Sequence"
msgstr "Séquence pour la caisse certifiée française"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order__l10n_fr_string_to_hash
msgid "L10N Fr String To Hash"
msgstr "Texte à hacher"

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid ""
"La chaîne de hachage est conforme: il n’est pas possible d’altérer les données\n"
"                                            sans casser la chaîne de hachage pour les pièces ultérieures."
msgstr ""

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "Last Entry"
msgstr "Dernière Entrée"

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "Last Hash"
msgstr "Dernier Hachage"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_bank_statement_line____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_fiscal_position____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_config____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_order_line____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_pos_session____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_report_l10n_fr_pos_cert_report_pos_hash_integrity____last_update
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_res_company____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__last_order_hash
msgid "Last Order entry's inalteralbility hash"
msgstr "Hachage d'inaltérabilité sur la dernière commande"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__last_order_id
msgid "Last Pos Order"
msgstr "Dernière Commande de caisse"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__last_order_id
msgid "Last Pos order included in the grand total"
msgstr "Dernière Commande de caisse incluse dans le grand total"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: l10n_fr_pos_cert
#. openerp-web
#: code:addons/l10n_fr_pos_cert/static/src/js/pos.js:0
#, python-format
msgid "Missing Country"
msgstr "Pays Manquant"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields.selection,name:l10n_fr_pos_cert.selection__account_sale_closing__frequency__monthly
msgid "Monthly"
msgstr "Mensuelle"

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_closing.py:0
#, python-format
msgid "Monthly Closing"
msgstr "Clôture Mensuelle"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__name
msgid "Name"
msgstr "Nom"

#. module: l10n_fr_pos_cert
#: model:ir.actions.server,name:l10n_fr_pos_cert.action_check_pos_hash_integrity
#: model:ir.ui.menu,name:l10n_fr_pos_cert.menu_check_move_integrity_reporting
msgid "POS Inalterability Check"
msgstr "Vérification d'inaltérabilité de la caisse"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__total_interval
msgid "Period Total"
msgstr "Total sur la Période"

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Paramétrage du point de vente"

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Lignes des commandes du point de vente"

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_pos_order
msgid "Point of Sale Orders"
msgstr "Commandes du point de vente"

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_pos_session
msgid "Point of Sale Session"
msgstr "Session du point de vente"

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid "Résultat du test d'intégrité -"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model,name:l10n_fr_pos_cert.model_account_sale_closing
msgid "Sale Closing"
msgstr "Clôture des Ventes"

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_closing.py:0
#: code:addons/l10n_fr_pos_cert/models/account_closing.py:0
#, python-format
msgid ""
"Sale Closings are not meant to be written or deleted under any "
"circumstances."
msgstr "Les clôtures de ventes ne peuvent ni être modifiées ni supprimées."

#. module: l10n_fr_pos_cert
#: model:ir.actions.act_window,name:l10n_fr_pos_cert.action_list_view_account_sale_closing
#: model:ir.ui.menu,name:l10n_fr_pos_cert.menu_account_closing
#: model:ir.ui.menu,name:l10n_fr_pos_cert.menu_account_closing_reporting
msgid "Sales Closings"
msgstr "Clôtures de Ventes"

#. module: l10n_fr_pos_cert
#: model_terms:ir.actions.act_window,help:l10n_fr_pos_cert.action_list_view_account_sale_closing
msgid ""
"Sales closings run automatically on a daily, monthly and annual basis. It "
"computes both period and cumulative totals from all the sales entries posted"
" in the system after the previous closing."
msgstr ""
"Les clôtures de ventes sont exécutées automatiquement de manière journalière, mensuelle et annuelle. "
"Des totaux cumulatifs et par période sont calculés à partir de toutes les entrées comptabilisées "
" dans le système depuis la clôture précédente."

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid ""
"Selon l’article 286 du code général des impôts français, toute livraison de bien ou prestation\n"
"                                        de services ne donnant pas lieu à facturation et étant enregistrée au moyen d’un logiciel ou\n"
"                                        d’un système de caisse doit satisfaire à des conditions d’inaltérabilité et de sécurisation des\n"
"                                        données en vue d’un contrôle de l’administration fiscale.\n"
"                                        <br/>\n"
"                                        <br/>\n"
"                                        Ces conditions sont respectées via une fonction de hachage des ventes du Point de Vente.\n"
"                                        <br/>\n"
"                                        <br/>"
msgstr ""

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__sequence_number
msgid "Sequence #"
msgstr "Numéro de Séquence"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,field_description:l10n_fr_pos_cert.field_account_sale_closing__date_closing_start
msgid "Starting Date"
msgstr "Date de début"

#. module: l10n_fr_pos_cert
#: model_terms:ir.actions.act_window,help:l10n_fr_pos_cert.action_list_view_account_sale_closing
msgid "The closings are created by Odoo"
msgstr "Les clôtures sont créées par Odoo"

#. module: l10n_fr_pos_cert
#. openerp-web
#: code:addons/l10n_fr_pos_cert/static/src/js/pos.js:0
#, python-format
msgid "The company %s doesn't have a country set."
msgstr "La société %s n'a pas de pays configuré."

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__currency_id
msgid "The company's currency"
msgstr "La devis de la société"

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/res_company.py:0
#, python-format
msgid ""
"There isn't any order flagged for data inalterability yet for the company "
"%s. This mechanism only runs for point of sale orders generated after the "
"installation of the module France - Certification CGI 286 I-3 bis. - POS"
msgstr ""
"Il n'y a pas encore de commande marquée pour l'inaltérabilité des données "
"pour la société %s. Cette fonction n'est utilisée que pour les commandes "
"générées après l'installation du module France - Certification CGI 286 I-3 bis. - POS"

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"This session has been opened another day. To comply with the French law, you"
" should close sessions on a daily basis. Please close session %s and open a "
"new one."
msgstr ""
"Cette session a été démarrée un autre jour. Pour satisfaire à la loi française, vous "
"devez clôturer les sessions chaque jour. Merci de clôturer la session %s et en ouvrir "
"une nouvelle."

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__total_interval
msgid ""
"Total in receivable accounts during the interval, excluding overlapping "
"periods"
msgstr ""
"Total sur des comptes débiteurs pendant l'intervalle, en excluant les "
"périodes qui se superposent"

#. module: l10n_fr_pos_cert
#: model:ir.model.fields,help:l10n_fr_pos_cert.field_account_sale_closing__cumulative_total
msgid "Total in receivable accounts since the beginnig of times"
msgstr "Total sur les comptes débiteurs depuis l'installation de la caisse"

#. module: l10n_fr_pos_cert
#: model_terms:ir.ui.view,arch_db:l10n_fr_pos_cert.report_pos_hash_integrity
msgid ""
"Toutes les ventes effectuées via le Point de Vente\n"
"                                                sont bien dans la chaîne de hachage."
msgstr ""

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_fiscal_position.py:0
#, python-format
msgid ""
"You cannot modify a fiscal position used in a POS order. You should archive "
"it and create a new one."
msgstr ""
"Vous ne pouvez pas modifer une position fiscale utilisée dans une commande de caisse. "
"Vous pouvez l'archiver et en créer une nouvelle."

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_bank_statement.py:0
#, python-format
msgid ""
"You cannot modify anything on a bank statement (name: %s) that was created "
"by point of sale operations."
msgstr ""
"Vous ne pouvez pas modifier un relevé bancaire (nom : %s) créé par une "
"opération de caisse."

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/account_bank_statement.py:0
#, python-format
msgid ""
"You cannot modify anything on a bank statement line (name: %s) that was "
"created by point of sale operations."
msgstr ""
"Vous ne pouvez pas modifier une ligne de relevé bancaire (nom : %s) créée "
"par une opération de caisse."

#. module: l10n_fr_pos_cert
#: code:addons/l10n_fr_pos_cert/models/pos.py:0
#, python-format
msgid ""
"You cannot overwrite the values ensuring the inalterability of the point of "
"sale."
msgstr "Vous ne pouvez pas modifier les valeurs assurant de l'inaltérabilité de la caisse."
