<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="PaymentScreen" t-inherit="point_of_sale.PaymentScreen" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[hasclass('button') and hasclass('next')]" position="attributes">
            <attribute name="t-att-hidden">env.pos.config.set_tip_after_payment and !currentOrder.is_paid()</attribute>
        </xpath>

        <xpath expr="//div[hasclass('button') and hasclass('back')]/span[hasclass('back_text')]" position="replace">
            <t t-if="env.pos.config.set_tip_after_payment and currentOrder.is_paid()">
                <span class="back_text">Keep Open</span>
            </t>
            <t t-else="">$0</t>
        </xpath>

        <xpath expr="//div[hasclass('button') and hasclass('next')]/span[hasclass('next_text')]" position="replace">
            <t t-if="env.pos.config.set_tip_after_payment and currentOrder.is_paid()">
                <span class="back_text">Close Tab</span>
            </t>
            <t t-else="">$0</t>
        </xpath>
    </t>

</templates>
