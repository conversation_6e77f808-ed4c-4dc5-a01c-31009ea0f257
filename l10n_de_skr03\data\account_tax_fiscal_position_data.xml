<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="tax_eu_19_purchase_skr03" model="account.tax.template">
            <field name="sequence">20</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Innergem. Erwerb 19%USt/19%VSt</field>
            <field name="description">innergem. Erwerb 19%</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_89')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1774'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_33')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1574'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_61')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_89')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1774'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_33')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1574'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_61')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_eu_7_purchase_skr03" model="account.tax.template">
            <field name="sequence">25</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Innergem. Erwerb 7%USt/7%VSt</field>
            <field name="description">innergem. Erwerb 7%</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_93')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1772'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_34')]
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1572'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_61')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_93')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1772'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_34')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1572'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_61')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_eu_19_purchase_no_vst_skr03" model="account.tax.template">
            <field name="sequence">20</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Innergem. Erwerb 19%USt/0%VSt</field>
            <field name="description">innergem. Erwerb 19% - 0% Vorsteuer</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_89')]
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1779'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_33')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_89')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1779'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_33')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_19"/>
        </record>

        <record id="tax_eu_7_purchase_no_vst_skr03" model="account.tax.template">
            <field name="sequence">20</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Innergem. Erwerb 7%USt/0%VSt</field>
            <field name="description">innergem. Erwerb 7% - 0% Vorsteuer</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">purchase</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_93')]
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1779'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_34')]
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_93')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1779'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_34')]
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_7"/>
        </record>

        <record id="tax_eu_car_purchase_skr03" model="account.tax.template">
            <field name="sequence">23</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Innergem. Erwerb Neufahrzeug 19%USt/19%VSt</field>
            <field name="description">innergem. Erwerb Neufahrzeug 19%</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_94')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_96')],
                    'account_id': ref('account_1772'),
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1572'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_59')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_94')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_96')],
                    'account_id': ref('account_1772'),
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1572'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_59')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_eu_sale_skr03" model="account.tax.template">
            <field name="sequence">21</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuerfreie innergem. Lieferung (§4 Abs. 1b UStG)</field>
            <field name="description">steuerfreie innergem. Lieferung</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_41')],
                    'tag_ids': [ref('l10n_de.tag_de_intracom_community_delivery')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_41')],
                    'tag_ids': [ref('l10n_de.tag_de_intracom_community_delivery')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_export_skr03" model="account.tax.template">
            <field name="sequence">22</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuerfreie Ausfuhr (§4 Nr. 1a UStG)</field>
            <field name="description">steuerfreie Ausfuhr</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_43')],
                    'tag_ids': [ref('l10n_de.tag_de_intracom_community_delivery')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_43')],
                    'tag_ids': [ref('l10n_de.tag_de_intracom_community_delivery')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_free_skr03_mit_vst" model="account.tax.template">
            <field name="sequence">23</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuerfreier Umsatz mit Vorsteuerabzug (§ 4 Nr. 2-7)</field>
            <field name="description">Steuerfr. Umsatz(§ 4 Nr. 2-7)</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_43')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_43')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_free_skr03_ohne_vst" model="account.tax.template">
            <field name="sequence">24</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuerfreier Umsatz ohne Vorsteuerabzug (§ 4 Nr. 8-28)</field>
            <field name="description">Steuerfr. Umsatz(§ 4 Nr. 8-28)</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_24')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_24')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_import_19_and_payable_skr03" model="account.tax.template">
            <field name="sequence">21</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">19% Einfuhrumsatzsteuer (§21 Abs.3 UstG)</field>
            <field name="description">Einfuhrumsatzsteuer 19%</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1588'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_62')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1788'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1588'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_62')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1788'),
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_19"/>
        </record>

        <record id="tax_import_7_and_payable_skr03" model="account.tax.template">
            <field name="sequence">21</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">7% Einfuhrumsatzsteuer (§21 Abs.3 UstG)</field>
            <field name="description">Einfuhrumsatzsteuer 7%</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">purchase</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1588'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_62')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1788'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1588'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_62')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1788'),
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_7"/>
        </record>

        <record id="tax_eu_purchase_tax_free_skr03" model="account.tax.template">
            <field name="sequence">22</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuerfr. innergem. Erwerb (§§ 4b und 25c UStG)</field>
            <field name="description">Steuerfr. innergem. Erwerb (§§ 4b und 25c UStG)</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_91')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_91')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_not_taxable_skr03" model="account.tax.template">
            <field name="sequence">20</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">nicht steuerbare Umsätze</field>
            <field name="description">nicht steuerbare Umsätze</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_45')],
                }),


                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_45')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_19_skr03" model="account.tax.template">
            <field name="sequence">10</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">19% Umsatzsteuer</field>
            <field name="description">19% USt</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="l10n_de_datev_code">3</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_81')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1776'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_26')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_81')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1776'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_26')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_19"/>
        </record>

        <record id="tax_ust_7_skr03" model="account.tax.template">
            <field name="sequence">15</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">7% Umsatzsteuer</field>
            <field name="description">7% USt</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="l10n_de_datev_code">2</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1771'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_27')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1771'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_27')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_7"/>
        </record>

        <record id="tax_ust_no_ustpflicht_skr03" model="account.tax.template">
            <field name="sequence">16</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">0% USt (Pflichtbefreit z.B. als Kleinunt. oder bei med. Leistg.)</field>
            <field name="description">0% USt (Pflichtbefreit)</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_19_taxinclusive_skr03" model="account.tax.template">
            <field name="sequence">17</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">19% Umsatzsteuer (inkludiert in Preis)</field>
            <field name="description">19% USt (inkludiert in Preis)</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_81')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1776'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_26')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_81')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1776'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_26')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_19"/>
            <field name="price_include" eval="True"/>
        </record>

        <record id="tax_ust_7_taxinclusive_skr03" model="account.tax.template">
            <field name="sequence">18</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">7% Umsatzsteuer (inkludiert in Preis)</field>
            <field name="description">7% USt (inkludiert in Preis)</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1771'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_27')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1771'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_27')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="price_include" eval="True"/>
        </record>

        <record id="tax_ust_55_farmer_skr03" model="account.tax.template">
            <field name="sequence">26</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">5,5 % Umsatzsteuer Land-/Forstwirtschaft</field>
            <field name="description">5,5% USt Land-/Forstwirtschaft</field>
            <field name="amount_type">percent</field>
            <field name="amount">5.5</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_77')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1770'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_77')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1770'),
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_55"/>
        </record>

        <record id="tax_ust_107_farmer_skr03" model="account.tax.template">
            <field name="sequence">27</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">10,7 % Umsatzsteuer Land-/Forstwirtschaft</field>
            <field name="description">10,7% USt Land-/Forstwirtschaft</field>
            <field name="amount_type">percent</field>
            <field name="amount">10.7</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_77')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1770'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_77')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1770'),
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_107"/>
        </record>

        <record id="tax_ust_19_farmer_skr03" model="account.tax.template">
            <field name="sequence">28</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">19% Umsatzsteuer Land-/Forstwirtschaft (Alkohol u.a.)</field>
            <field name="description">19% USt Land-/Forstwirtschaft (Alkohol u.a.)</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">False</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_76')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_80')],
                    'account_id': ref('account_1776'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_76')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_80')],
                    'account_id': ref('account_1776'),
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_19"/>
        </record>

        <record id="tax_ust_x_skr03" model="account.tax.template">
            <field name="sequence">21</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">x% Umsatzsteuer (zu anderen Steuersätzen)</field>
            <field name="description">x% USt (zu anderen Steuersätzen)</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">False</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_35')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1770'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_36')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_35')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1770'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_36')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_x"/>
        </record>

        <record id="tax_vst_19_skr03" model="account.tax.template">
            <field name="sequence">10</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">19% Vorsteuer</field>
            <field name="description">19% VSt</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="l10n_de_datev_code">9</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1576'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1576'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_19"/>
        </record>

        <record id="tax_vst_7_skr03" model="account.tax.template">
            <field name="sequence">15</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">7% Vorsteuer</field>
            <field name="description">7% VSt</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="l10n_de_datev_code">8</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1571'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1571'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_7"/>
        </record>

        <record id="tax_vst_no_ustpflicht_skr03" model="account.tax.template">
            <field name="sequence">16</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">0% VSt (Pflichtbefreit z.B. als Kleinunt. oder bei med. Leistg.)</field>
            <field name="description">0% VSt (Pflichtbefreit)</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_vst_19_taxinclusive_skr03" model="account.tax.template">
            <field name="sequence">16</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">19% Vorsteuer (inkludiert in Preis)</field>
            <field name="description">19% VSt (inkludiert in Preis)</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1576'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1576'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_19"/>
            <field name="price_include" eval="True"/>
        </record>

        <record id="tax_vst_7_taxinclusive_skr03" model="account.tax.template">
            <field name="sequence">17</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">7% Vorsteuer (inkludiert in Preis)</field>
            <field name="description">7% VSt (inkludiert in Preis)</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">purchase</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1571'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1571'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="price_include" eval="True"/>
        </record>

        <record id="tax_vst_55_farmer_skr03" model="account.tax.template">
            <field name="sequence">26</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">5,5% Vorsteuer Land-/Forstwirtschaft</field>
            <field name="description">5,5% VSt Land-/Forstwirtschaft</field>
            <field name="amount_type">percent</field>
            <field name="amount">5.5</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1570'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1570'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_55"/>
        </record>

        <record id="tax_vst_107_farmer_skr03" model="account.tax.template">
            <field name="sequence">27</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">10,7% Vorsteuer Land-/Forstwirtschaft</field>
            <field name="description">10,7% VSt Land-/Forstwirtschaft</field>
            <field name="amount_type">percent</field>
            <field name="amount">10.7</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1570'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1570'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_107"/>
        </record>

        <record id="tax_ust_19_eu_skr03" model="account.tax.template">
            <field name="sequence">21</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">19 % Umsatzsteuer EU Lieferung</field>
            <field name="description">19% USt EU</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_81')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1778'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_26')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_81')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1778'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_26')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_eu_skr03" model="account.tax.template">
            <field name="sequence">22</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">7% Umsatzsteuer EU Lieferung</field>
            <field name="description">7% USt EU</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1777'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_27')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1777'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_27')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_19_13b_ausland_ohne_vst_skr03" model="account.tax.template">
            <field name="sequence">23</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">19% USt gem. §13b UStG - ohne VSt. - (ausländ. Werklieferungen u.ä.)</field>
            <field name="description">19% USt EU gem. §13b UStG - ohne VSt. - (ausländ. Werklieferungen u.ä.)</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                    'account_id': ref('account_1787'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                    'account_id': ref('account_1787'),
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_7_13b_ausland_ohne_vst_skr03" model="account.tax.template">
            <field name="sequence">24</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">7% USt gem. §13b UStG - ohne VSt. - (ausländ. Werklieferungen u.ä.)</field>
            <field name="description">7% USt EU gem. §13b UStG - ohne VSt. - (ausländ. Werklieferungen u.ä.)</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                    'account_id': ref('account_1785'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                    'account_id': ref('account_1785'),
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_19_13b_eu_ohne_vst_skr03" model="account.tax.template">
            <field name="sequence">25</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">19% USt gem. §13b UStG - ohne VSt. - (sonst. Leistungen EU)</field>
            <field name="description">19% USt EU gem. §13b UStG - ohne VSt. - (sonst. Leistungen EU)</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_48')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_47')],
                    'account_id': ref('account_1787'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_48')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_47')],
                    'account_id': ref('account_1787'),
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_7_13b_eu_ohne_vst_skr03" model="account.tax.template">
            <field name="sequence">26</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">7% USt gem. §13b UStG - ohne VSt. - (sonst. Leistungen EU)</field>
            <field name="description">7% USt EU gem. §13b UStG - ohne VSt. - (sonst. Leistungen EU)</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_48')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_47')],
                    'account_id': ref('account_1785'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_48')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_47')],
                    'account_id': ref('account_1785'),
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_19_13b_bau_ohne_vst_skr03" model="account.tax.template">
            <field name="sequence">27</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">19% USt gem. §13b UStG - ohne VSt. - (empf. Bauleistungen)</field>
            <field name="description">19% USt EU gem. §13b UStG - ohne VSt. - (empf. Bauleistungen)</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                    'account_id': ref('account_1787'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                    'account_id': ref('account_1787'),
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_7_13b_bau_ohne_vst_skr03" model="account.tax.template">
            <field name="sequence">28</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">7% USt gem. §13b UStG - ohne VSt. - (empf. Bauleistungen)</field>
            <field name="description">7% USt EU gem. §13b UStG - ohne VSt. - (empf. Bauleistungen)</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                    'account_id': ref('account_1785'),
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                    'account_id': ref('account_1785'),
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_free_eu_skr03" model="account.tax.template">
            <field name="sequence">20</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">0% Steuerfreie Leistung EU</field>
            <field name="description">0% USt EU</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_21')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_21')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_free_third_country_skr03" model="account.tax.template">
            <field name="sequence">20</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">0% Steuerfreie Leistung Drittland</field>
            <field name="description">0% USt Drittland</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_45')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_45')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_free_newcar_skr03" model="account.tax.template">
            <field name="sequence">30</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">0% Steuerfreie Neufahrzeuglieferung EU</field>
            <field name="description">0% USt Neufahrzeug EU</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_44')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_44')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_19_3eck_first_skr03" model="account.tax.template">
            <field name="sequence">40</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">0% Umsatzsteuer Dreiecksgeschäft erster Abnehmer</field>
            <field name="description">0% USt Dreiecksgeschäft erster Abnehmer</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_42')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_42')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_free_bau_skr03" model="account.tax.template">
            <field name="sequence">50</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">0% Umsatzsteuer Bauleistung (Erbringer §13b)</field>
            <field name="description">0% Umsatzsteuer Bauleistung (Erbringer §13b)</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_60')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_60')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_free_mobil_skr03" model="account.tax.template">
            <field name="sequence">50</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">0% Umsatzsteuer Lieferung von Mobilfunkgeräten u.a. (§13b)</field>
            <field name="description">0% USt Lieferung von Mobilfunkgeräten u.a. (§13b)</field>
            <field name="amount_type">percent</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_60')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_60')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_eu_19_purchase_goods_skr03" model="account.tax.template">
            <field name="sequence">20</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuerpflichtige sonstige Leistungen EU 19%USt/19%VSt</field>
            <field name="description">Leistungen EU 19%Ust/19%VSt</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_48')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1577'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_67')]
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1768'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_47')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_48')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1577'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_67')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1768'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_47')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_eu_7_purchase_goods_skr03" model="account.tax.template">
            <field name="sequence">21</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuerpflichtige sonstige Leistungen EU 7%USt/7%VSt</field>
            <field name="description">Steuerpfl. sonst. Leistg. EU 7%Ust/7%VSt</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_48')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1577'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_67')]
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1768'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_47')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_48')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1577'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_67')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1768'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_47')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_vst_19_purchase_13b_bau_skr03" model="account.tax.template">
            <field name="sequence">22</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuer gem. §13b UStG 19%USt/19%VSt (Bauleistung Empfänger)</field>
            <field name="description">Steuer gem. §13b UStG 19%USt/19%VSt (Bauleistung Empfänger)</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_60'), ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1577'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_67')]
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1787'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_60'), ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1577'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_67')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1787'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_ust_vst_7_purchase_13b_bau_skr03" model="account.tax.template">
            <field name="sequence">23</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuer gem. §13b UStG 7%USt/7%VSt (Bauleistung Empfänger)</field>
            <field name="description">Steuer gem. §13b UStG 7%USt/7%VSt (Bauleistung Empfänger)</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_60'), ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1578'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_67')]
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1785'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_60'), ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1578'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_67')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1785'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_vst_ust_19_purchase_13b_mobil_skr03" model="account.tax.template">
            <field name="sequence">24</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuer gem. §13b 19%USt/19%VSt (Empfang von Mobilfunkgeräten u.a.)</field>
            <field name="description">Steuer gem. §13b 19%Ust/19%VSt (Empfang von Mobilfunkgeräten u.a.)</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_60'), ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1577'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_67')]
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1787'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_60'), ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1577'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_67')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1787'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_vst_ust_19_purchase_3eck_last_skr03" model="account.tax.template">
            <field name="sequence">105</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Dreiecksgeschäft Erwerb letzter Abnehmer 19%USt/19%VSt</field>
            <field name="description">Dreiecksgeschäft Erwerb letzter Abnehmer 19%Ust/19%VSt</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1576'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')]
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1783'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_69')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1576'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1783'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_69')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_vst_ust_19_purchase_13b_werk_ausland_skr03" model="account.tax.template">
            <field name="sequence">25</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuer gem. §13b 19%USt/19%VSt (ausländ. Werklieferungen u.ä.)</field>
            <field name="description">Steuer gem. §13b 19%USt/19%VSt (ausländ. Werklieferungen u.ä.)</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1576'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')]
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1783'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1576'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1783'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_vst_ust_7_purchase_13b_werk_ausland_skr03" model="account.tax.template">
            <field name="sequence">26</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuer gem. §13b 7%USt/7%VSt (ausländ. Werklieferungen u.ä.)</field>
            <field name="description">Steuer gem. §13b 7%USt/7%VSt (ausländ. Werklieferungen u.ä.)</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1571'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')]
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1783'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1571'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1783'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_85')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_vst_ust_19_purchase_13a_auslagerung_skr03" model="account.tax.template">
            <field name="sequence">27</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuer gem. §13a Abs. 1 Nr. 6 UStG 19%USt/19%VSt (Auslagerung)</field>
            <field name="description">Steuer gem. §13a Abs. 1 Nr. 6 UStG 19%USt/19%VSt (Auslagerung)</field>
            <field name="amount_type">percent</field>
            <field name="amount">19</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1585'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')]
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1783'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_69')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1585'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1783'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_69')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="tax_vst_ust_7_purchase_13a_auslagerung_skr03" model="account.tax.template">
            <field name="sequence">28</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Steuer gem. §13a Abs. 1 Nr. 6 UStG 7%USt/7%VSt (Auslagerung)</field>
            <field name="description">Steuer gem. §13a Abs. 1 Nr. 6 UStG 7%USt/7VSt (Auslagerung)</field>
            <field name="amount_type">percent</field>
            <field name="amount">7</field>
            <field name="type_tax_use">purchase</field>
            <field name="price_include">False</field>
            <field name="active">True</field>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1585'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1783'),
                    'minus_report_line_ids': [ref('l10n_de.tax_report_de_tag_69')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1585'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_66')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('account_1783'),
                    'plus_report_line_ids': [ref('l10n_de.tax_report_de_tag_69')],
                }),
            ]"/>
            <field name="tax_group_id" ref="tax_group_0"/>
        </record>

        <record id="fiscal_position_domestic_skr03" model="account.fiscal.position.template">
            <field name="sequence">1</field>
            <field name="name">Geschäftspartner Inland</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="auto_apply" eval="True" />
            <field name="country_id" ref="base.de"></field>
        </record>

        <record id="fiscal_position_non_eu_partner_service_skr03" model="account.fiscal.position.template">
            <field name="sequence">6</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Dienstleister Ausland (Nicht-EU)</field>
        </record>

        <record id="fiscal_position_non_eu_partner_skr03" model="account.fiscal.position.template">
            <field name="sequence">5</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Geschäftspartner Ausland (Nicht-EU)</field>
        </record>

        <record id="fiscal_position_eu_vat_id_partner_skr03" model="account.fiscal.position.template">
            <field name="sequence">2</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Geschäftspartner EU (mit USt-ID)</field>
            <field name="auto_apply" eval="True" />
            <field name="country_group_id" ref="base.europe"></field>
        </record>

        <record id="fiscal_position_eu_vat_id_partner_service_skr03" model="account.fiscal.position.template">
            <field name="sequence">3</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Dienstleister EU (mit USt-ID)</field>
            <field name="vat_required" eval="True" />
        </record>

        <record id="fiscal_position_eu_no_id_partner_skr03" model="account.fiscal.position.template">
            <field name="sequence">4</field>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
            <field name="name">Geschäftspartner EU (ohne USt-ID)</field>
        </record>

        <record id="account_fiscal_position_tax_eu_vat_id_sale_19_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_eu_sale_skr03"/>
            <field name="tax_src_id" ref="tax_ust_19_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_eu_vat_id_sale_7_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_eu_sale_skr03"/>
            <field name="tax_src_id" ref="tax_ust_7_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_eu_vat_id_purchase_19_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_eu_19_purchase_skr03"/>
            <field name="tax_src_id" ref="tax_vst_19_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_eu_vat_id_purchase_services_19_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_service_skr03"/>
            <field name="tax_dest_id" ref="tax_eu_19_purchase_goods_skr03"/>
            <field name="tax_src_id" ref="tax_vst_19_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_eu_vat_id_purchase_services_7_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_service_skr03"/>
            <field name="tax_dest_id" ref="tax_eu_7_purchase_goods_skr03"/>
            <field name="tax_src_id" ref="tax_vst_7_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_eu_vat_id_purchase_7_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_eu_7_purchase_skr03"/>
            <field name="tax_src_id" ref="tax_vst_7_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_eu_no_id_purchase_19_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_eu_19_purchase_no_vst_skr03"/>
            <field name="tax_src_id" ref="tax_vst_19_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_eu_no_id_purchase_7_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_eu_7_purchase_no_vst_skr03"/>
            <field name="tax_src_id" ref="tax_vst_7_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_eu_no_id_sale_19_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_ust_19_eu_skr03"/>
            <field name="tax_src_id" ref="tax_ust_19_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_eu_no_id_purchase_7_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_ust_eu_skr03"/>
            <field name="tax_src_id" ref="tax_ust_7_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_non_eu_sale_19_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_export_skr03"/>
            <field name="tax_src_id" ref="tax_ust_19_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_non_eu_sale_services_19_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_service_skr03"/>
            <field name="tax_dest_id" ref="tax_free_third_country_skr03"/>
            <field name="tax_src_id" ref="tax_ust_19_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_non_eu_sale_7_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_export_skr03"/>
            <field name="tax_src_id" ref="tax_ust_7_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_non_eu_purchase_19_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_import_19_and_payable_skr03"/>
            <field name="tax_src_id" ref="tax_vst_19_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_non_eu_purchase_services_19_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_service_skr03"/>
            <field name="tax_dest_id" ref="tax_vst_ust_19_purchase_13b_werk_ausland_skr03"/>
            <field name="tax_src_id" ref="tax_vst_19_skr03"/>
        </record>

        <record id="account_fiscal_position_tax_non_eu_purchase_7_skr03" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_skr03"/>
            <field name="tax_dest_id" ref="tax_import_7_and_payable_skr03"/>
            <field name="tax_src_id" ref="tax_vst_7_skr03"/>
        </record>

        <record id="account_fiscal_position_acc_eu_vat_id_partner_afa7a_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="account_src_id" ref="account_2401" />
            <field name="account_dest_id" ref="account_2402" />
        </record>

        <record id="account_fiscal_position_acc_eu_vat_id_partner_afa19a_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="account_src_id" ref="account_2406" />
            <field name="account_dest_id" ref="account_2402" />
        </record>

        <record id="account_fiscal_position_acc_eu_vat_id_partner_afa7b_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="account_src_id" ref="account_2431" />
            <field name="account_dest_id" ref="account_2430" />
        </record>

        <record id="account_fiscal_position_acc_eu_vat_id_partner_19b_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="account_src_id" ref="account_2436" />
            <field name="account_dest_id" ref="account_2430" />
        </record>

        <record id="account_fiscal_position_acc_eu_no_id_partner_afa7a_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="account_src_id" ref="account_2401" />
            <field name="account_dest_id" ref="account_2403" />
        </record>

        <record id="account_fiscal_position_acc_eu_no_id_partner_19a_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="account_src_id" ref="account_2406" />
            <field name="account_dest_id" ref="account_2408" />
        </record>

        <record id="account_fiscal_position_acc_eu_no_id_partner_7b_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="account_src_id" ref="account_2431" />
            <field name="account_dest_id" ref="account_2430" />
        </record>

        <record id="account_fiscal_position_acc_eu_no_id_partner_19b_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="account_src_id" ref="account_2436" />
            <field name="account_dest_id" ref="account_2430" />
        </record>

        <record id="account_fiscal_position_account_eu_vat_id_sale_19_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="account_src_id" ref="account_8400" />
            <field name="account_dest_id" ref="account_8125" />
        </record>

        <record id="account_fiscal_position_account_eu_vat_id_sale_7_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="account_src_id" ref="account_8300" />
            <field name="account_dest_id" ref="account_8125" />
        </record>

        <record id="account_fiscal_position_account_non_eu_sale_19_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_skr03"/>
            <field name="account_src_id" ref="account_8400" />
            <field name="account_dest_id" ref="account_8120" />
        </record>

        <record id="account_fiscal_position_account_non_eu_sale_services_19_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_service_skr03"/>
            <field name="account_src_id" ref="account_8400" />
            <field name="account_dest_id" ref="account_8338" />
        </record>

        <record id="account_fiscal_position_account_non_eu_sale_7_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_skr03"/>
            <field name="account_src_id" ref="account_8300" />
            <field name="account_dest_id" ref="account_8120" />
        </record>

        <record id="account_fiscal_position_account_non_eu_purchase_19_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_skr03"/>
            <field name="account_src_id" ref="account_3400" />
            <field name="account_dest_id" ref="account_3551" />
        </record>

        <record id="account_fiscal_position_acc_non_eu_purchase_services_19_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_service_skr03"/>
            <field name="account_src_id" ref="account_3400" />
            <field name="account_dest_id" ref="account_3125" />
        </record>

        <record id="account_fiscal_position_account_non_eu_purchase_7_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_non_eu_partner_skr03"/>
            <field name="account_src_id" ref="account_3300" />
            <field name="account_dest_id" ref="account_3557" />
        </record>

        <record id="account_fiscal_position_account_eu_no_id_sale_19_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="account_src_id" ref="account_8400" />
            <field name="account_dest_id" ref="account_8315" />
        </record>

        <record id="account_fiscal_position_account_no_id_sale_7_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="account_src_id" ref="account_8300" />
            <field name="account_dest_id" ref="account_8310" />
        </record>

        <record id="account_fiscal_position_account_eu_vat_id_purchase_19_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="account_src_id" ref="account_3400" />
            <field name="account_dest_id" ref="account_3425" />
        </record>

        <record id="account_fiscal_position_acc_eu_vat_id_purchase_service_19_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_service_skr03"/>
            <field name="account_src_id" ref="account_3400" />
            <field name="account_dest_id" ref="account_3123" />
        </record>

        <record id="account_fiscal_position_acc_eu_vat_id_purchase_service_7_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_service_skr03"/>
            <field name="account_src_id" ref="account_3300" />
            <field name="account_dest_id" ref="account_3113" />
        </record>

        <record id="account_fiscal_position_account_eu_vat_id_purchase_7_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_vat_id_partner_skr03"/>
            <field name="account_src_id" ref="account_3300" />
            <field name="account_dest_id" ref="account_3420" />
        </record>

        <record id="account_fiscal_position_account_eu_no_id_purchase_19_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="account_src_id" ref="account_3400" />
            <field name="account_dest_id" ref="account_3435" />
        </record>

        <record id="account_fiscal_position_account_no_id_purchase_7_skr03" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_eu_no_id_partner_skr03"/>
            <field name="account_src_id" ref="account_3300" />
            <field name="account_dest_id" ref="account_3430" />
        </record>

        <record id="account_2401" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_2402" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_sale_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_2403" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_2406" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_2431" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_2436" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_2751" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_2752" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3010" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3030" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3060" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_7_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3062" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_19_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3066" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_7_purchase_no_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3067" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_19_purchase_no_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3070" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_55_farmer_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3071" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_107_farmer_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3075" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_7_purchase_13a_auslagerung_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3076" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_19_purchase_13a_auslagerung_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3089" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_19_purchase_3eck_last_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3091" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3092" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3106" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3108" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3110" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_vst_7_purchase_13b_bau_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3113" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_7_purchase_goods_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3115" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_7_purchase_13b_werk_ausland_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3120" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_vst_19_purchase_13b_bau_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3123" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_19_purchase_goods_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3125" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_19_purchase_13b_werk_ausland_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3130" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_13b_bau_ohne_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3133" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_13b_eu_ohne_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3135" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_13b_ausland_ohne_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3140" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_13b_bau_ohne_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3143" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_13b_eu_ohne_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3145" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_13b_ausland_ohne_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3151" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_19_purchase_13b_werk_ausland_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3153" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_13b_eu_ohne_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3154" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_13b_eu_ohne_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3300" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3400" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3420" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_7_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3425" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_19_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3430" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_7_purchase_no_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3435" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_19_purchase_no_vst_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3440" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_car_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3505" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_55_farmer_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3540" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_107_farmer_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3550" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_purchase_tax_free_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3551" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_import_19_and_payable_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3553" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_19_purchase_3eck_last_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3557" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_import_7_and_payable_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3560" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_7_purchase_13a_auslagerung_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3565" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_19_purchase_13a_auslagerung_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3610" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3660" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3710" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3714" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3715" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3717" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_7_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3718" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_19_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3720" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3724" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_7_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3725" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_19_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3731" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3734" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3736" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3738" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3741" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_19_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3743" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_7_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3746" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_7_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3748" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_19_purchase_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3750" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3754" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3755" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3760" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3780" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3784" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3785" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3788" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_107_farmer_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3790" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3792" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_19_purchase_3eck_last_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3793" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_19_purchase_3eck_last_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3794" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_55_farmer_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3796" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_107_farmer_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_3798" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_55_farmer_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8100" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8105" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8110" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8120" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_export_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8125" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_sale_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8130" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_3eck_first_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8135" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_newcar_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8140" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_mit_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8150" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_mit_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8160" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8165" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8194" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_mit_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8195" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8196" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8300" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8310" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8315" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8331" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8335" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_free_mobil_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8336" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8337" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_free_bau_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8338" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_third_country_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8339" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8400" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8410" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8514" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8515" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_mit_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8516" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8519" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8574" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8575" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_mit_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8576" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8579" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8591" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8595" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8609" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8611" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8613" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8630" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8640" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8701" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8702" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_mit_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8703" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8704" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_mit_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8705" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_export_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8710" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8720" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8724" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_sale_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8725" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8726" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8731" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8736" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8738" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_vst_ust_19_purchase_13b_mobil_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8741" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_free_mobil_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8742" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_not_taxable_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8746" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8748" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8750" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8760" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8780" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8790" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8801" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8807" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_export_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8808" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_sale_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8819" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8820" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8827" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_export_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8828" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_eu_sale_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8850" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8851" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8852" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_skr03_ohne_vst')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8905" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8906" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8910" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8915" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8918" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8919" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_not_taxable_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8920" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8921" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8922" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8924" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8925" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8929" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8930" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8932" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8935" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8939" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8940" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_19_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8945" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_ust_7_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8949" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_free_eu_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

        <record id="account_8950" model="account.account.template">
            <field name="tax_ids" eval="[(6, 0, [ref('tax_not_taxable_skr03')])]"/>
            <field name="chart_template_id" ref="l10n_de_chart_template"/>
        </record>

</odoo>
