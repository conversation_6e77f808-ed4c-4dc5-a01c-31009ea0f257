<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ppn_tag" model="account.account.tag">
        <field name="name">PPN - 08</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.id"/>
    </record>

    <record id="tax_ST1" model="account.tax.template">
        <field name="description">ST1</field>
        <field name="chart_template_id" ref="l10n_id_chart"/>
        <field name="type_tax_use">sale</field>
        <field name="name">11%</field>
        <field name="amount_type">percent</field>
        <field name="amount">11.0</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [(4, ref('ppn_tag'))],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221020'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [(4, ref('ppn_tag'))],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221020'),
            }),
        ]"/>
    </record>

    <record id="tax_PT1" model="account.tax.template">
        <field name="description">PT1</field>
        <field name="chart_template_id" ref="l10n_id_chart"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">11%</field>
        <field name="amount_type">percent</field>
        <field name="amount">11.0</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [(4, ref('ppn_tag'))],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221010'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [(4, ref('ppn_tag'))],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221010'),
            }),
        ]"/>
    </record>

    <record id="tax_ST0" model="account.tax.template">
        <field name="description">ST0</field>
        <field name="chart_template_id" ref="l10n_id_chart"/>
        <field name="type_tax_use">sale</field>
        <field name="name">0%</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221020'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221020'),
            }),
        ]"/>
        </record>

    <record id="tax_ST2" model="account.tax.template">
        <field name="description">ST2</field>
        <field name="chart_template_id" ref="l10n_id_chart"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Exempt</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221020'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221020'),
            }),
        ]"/>
    </record>

    <record id="tax_PT2" model="account.tax.template">
        <field name="description">PT2</field>
        <field name="chart_template_id" ref="l10n_id_chart"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Exempt</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221010'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221010'),
            }),
        ]"/>
    </record>

    <record id="tax_PT0" model="account.tax.template">
        <field name="description">PT0</field>
        <field name="chart_template_id" ref="l10n_id_chart"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">0%</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221010'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_id_21221010'),
            }),
        ]"/>
    </record>

</odoo>
