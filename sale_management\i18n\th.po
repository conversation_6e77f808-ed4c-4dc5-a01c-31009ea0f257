# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_management
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> Thaidev <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:27+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_management
#: model:sale.order.template.line,name:sale_management.sale_order_template_line_1
msgid "4 Person Desk"
msgstr "โต๊ะทำงานสำหรับ 4 ท่าน"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
msgid "<span>Options</span>"
msgstr "<span>ตัวเลือก</span>"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a note"
msgstr "เพิ่มโน้ต"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a product"
msgstr "เพิ่มรายการสินค้า"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Add a section"
msgstr "เพิ่มส่วน"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Add one"
msgstr "เพิ่มหนึ่ง"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Add to cart"
msgstr "เพิ่มในรถเข็น"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Add to order lines"
msgstr "เพิ่มไลน์คำสั่ง"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_digest_digest__kpi_all_sale_total
msgid "All Sales"
msgstr "ขายทั้งหมด"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: sale_management
#: model:ir.ui.menu,name:sale_management.menu_product_attribute_action
msgid "Attributes"
msgstr "คุณลักษณะ"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__product_uom_category_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_category_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__product_uom_category_id
msgid "Category"
msgstr "หมวดหมู่"

#. module: sale_management
#: model:ir.model,name:sale_management.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__company_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__company_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__company_id
msgid "Company"
msgstr "บริษัท"

#. module: sale_management
#: model:ir.model,name:sale_management.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Confirmation"
msgstr "ยืนยัน"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__mail_template_id
msgid "Confirmation Mail"
msgstr "เมลยืนยัน"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__product_uom_category_id
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__product_uom_category_id
#: model:ir.model.fields,help:sale_management.field_sale_order_template_option__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"การแปลงระหว่างหน่วยวัดจะเกิดขึ้นได้ก็ต่อเมื่ออยู่ในหมวดหมู่เดียวกัน "
"การแปลงจะทำตามอัตราส่วน"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Create standardized offers with default products"
msgstr "สร้างข้อเสนอมาตรฐานด้วยค่าสินค้าเริ่มต้น"

#. module: sale_management
#: model_terms:ir.actions.act_window,help:sale_management.sale_order_template_action
msgid "Create your quotation template"
msgstr "สร้างเทมเพลตใบเสนอราคาของคุณ"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__create_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__create_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_company__sale_order_template_id
msgid "Default Sale Template"
msgstr "เทมเพลตการขายเริ่มต้น"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__company_so_template_id
msgid "Default Template"
msgstr "ค่าเริ่มต้นเทมเพลต"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__name
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Description"
msgstr "รายละเอียด"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid ""
"Design your quotation templates using building blocks<br/>\n"
"                            <em attrs=\"{'invisible': [('module_sale_quotation_builder','=',False)]}\">Warning: this option will install the Website app.</em>"
msgstr ""
"ออกแบบเทมเพลตใบเสนอราคาของคุณโดยใช้บล็อกสำเร็จรูป<br/>\n"
"                            <em attrs=\"{'invisible': [('module_sale_quotation_builder','=',False)]}\">คำเตือน: ตัวเลือกนี้จะติดตั้งแอปเว็บไซต์</em>"

#. module: sale_management
#: model:ir.model,name:sale_management.model_digest_digest
msgid "Digest"
msgstr "ไดเจส"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Disc.%"
msgstr "ลด%"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__discount
msgid "Discount (%)"
msgstr "ส่วนลด (%)"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__display_name
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__display_type
msgid "Display Type"
msgstr "ประเภทการแสดง"

#. module: sale_management
#: code:addons/sale_management/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "ไม่มีสิทธิ์เข้าถึง ข้ามข้อมูลนี้สำหรับอีเมลสรุปข้อมูลของผู้ใช้"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Documentation"
msgstr "เอกสารกำกับ"

#. module: sale_management
#: model:ir.model.constraint,message:sale_management.constraint_sale_order_template_line_non_accountable_fields_null
msgid ""
"Forbidden product, unit price, quantity, and UoM on non-accountable sale "
"quote line"
msgstr ""
"สินค้าต้องห้าม ราคาต่อหน่วย จำนวน และ UoM "
"ในไลน์ใบเสนอราคาขายที่ไม่ต้องรับผิดชอบ"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__sequence
msgid "Gives the sequence order when displaying a list of optional products."
msgstr "ให้ลำดับคำสั่งเมื่อแสดงรายการของสินค้าเสริม"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__sequence
msgid "Gives the sequence order when displaying a list of sale quote lines."
msgstr "ให้ลำดับของคำสั่งเมื่อแสดงรายการของรายการไลน์ใบเสนอราคาขาย"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__id
msgid "ID"
msgstr "ไอดี"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__active
msgid ""
"If unchecked, it will allow you to hide the quotation template without "
"removing it."
msgstr "หากไม่เลือก จะอนุญาตให้คุณซ่อนเทมเพลตใบเสนอราคาโดยไม่ต้องลบออก"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_digest_digest__kpi_all_sale_total_value
msgid "Kpi All Sale Total Value"
msgstr "ค่ารวมการขาย Kpi ทั้งหมด"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line____last_update
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__write_uid
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__write_date
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__line_id
msgid "Line"
msgstr "ไลน์"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__sale_order_template_line_ids
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Lines"
msgstr "ไลน์"

#. module: sale_management
#: model:ir.model.constraint,message:sale_management.constraint_sale_order_template_line_accountable_product_id_required
msgid "Missing required product and UoM on accountable sale quote line."
msgstr "สินค้าที่จำเป็นและหน่วยวัด ในรายการใบเสนอราคาขายที่รับผิดชอบหายไป"

#. module: sale_management
#: model:ir.model.fields.selection,name:sale_management.selection__sale_order_template_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Note"
msgstr "โน้ต"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__number_of_days
msgid "Number of days for the validity date computation of the quotation"
msgstr "จำนวนวันสำหรับการคำนวณวันที่มีผลบังคับใช้ของใบเสนอราคา"

#. module: sale_management
#: model:sale.order.template.option,name:sale_management.sale_order_template_option_1
msgid "Office Chair"
msgstr "เก้าอี้สำนักงาน"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__require_payment
msgid "Online Payment"
msgstr "ชำระเงินออนไลน์"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__require_signature
msgid "Online Signature"
msgstr "ลายเซ็นออนไลน์"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__sale_order_template_option_ids
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Optional Products"
msgstr "สินค้าตัวเลือก"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order__sale_order_option_ids
#: model:ir.model.fields,field_description:sale_management.field_sale_order_line__sale_order_option_ids
msgid "Optional Products Lines"
msgstr "ไลน์สินค้าที่ให้เลือก"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Options"
msgstr "ตัวเลือก"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__is_present
msgid "Present on Quotation"
msgstr "แสดงบนใบเสนอราคา"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__product_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__product_id
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Product"
msgstr "สินค้า"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__quantity
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_qty
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__quantity
msgid "Quantity"
msgstr "จำนวน"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Quantity:"
msgstr "จำนวน:"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__module_sale_quotation_builder
msgid "Quotation Builder"
msgstr "โอกาสในใบเสนอราคา"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__number_of_days
msgid "Quotation Duration"
msgstr "ระยะเวลาใบเสนอราคา"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template
#: model:ir.model.fields,field_description:sale_management.field_sale_order__sale_order_template_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__name
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_tree
msgid "Quotation Template"
msgstr "เทมเพลตใบเสนอราคา"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "ไลน์เทมเพลตใบเสนอราคา"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Quotation Template Lines"
msgstr "ไลน์เทมเพลตใบเสนอราคา"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "ตัวเลือกเทมเพลตใบเสนอราคา"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__sale_order_template_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__sale_order_template_id
msgid "Quotation Template Reference"
msgstr "อ้างอิงเทมเพลตใบเสนอราคา"

#. module: sale_management
#: model:ir.actions.act_window,name:sale_management.sale_order_template_action
#: model:ir.model.fields,field_description:sale_management.field_res_config_settings__group_sale_order_template
#: model:ir.ui.menu,name:sale_management.sale_order_template_menu
#: model:res.groups,name:sale_management.group_sale_order_template
#: model_terms:ir.ui.view,arch_db:sale_management.res_config_settings_view_form
msgid "Quotation Templates"
msgstr "เทมเพลตใบเสนอราคา"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Quotation expires after"
msgstr "ใบเสนอราคาหมดอายุหลังจาก"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Remove"
msgstr "ลบออก"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Remove one"
msgstr "นำออกหนึ่ง"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__require_signature
msgid ""
"Request a online signature to the customer in order to confirm orders "
"automatically."
msgstr "ร้องขอลายเซ็นออนไลน์ให้กับลูกค้าเพื่อยืนยันคำสั่งโดยอัตโนมัติ"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__require_payment
msgid ""
"Request an online payment to the customer in order to confirm orders "
"automatically."
msgstr "ร้องขอการชำระเงินออนไลน์ให้กับลูกค้าเพื่อยืนยันคำสั่งโดยอัตโนมัติ"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_option
msgid "Sale Options"
msgstr "ตัวเลือกการขาย"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.digest_digest_view_form
msgid "Sales"
msgstr "ขาย"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order
msgid "Sales Order"
msgstr "คำสั่งขาย"

#. module: sale_management
#: model:ir.model,name:sale_management.model_sale_order_line
msgid "Sales Order Line"
msgstr "ไลน์คำสั่งขาย"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__order_id
msgid "Sales Order Reference"
msgstr "การอ้างอิงคำสั่งขาย"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Sales Quotation Template Lines"
msgstr "ไลน์เทมเพลตใบเสนอราคาขาย"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_search
msgid "Search Quotation Template"
msgstr "ค้นหาเทมเพลตใบเสนอราคา"

#. module: sale_management
#: model:ir.model.fields.selection,name:sale_management.selection__sale_order_template_line__display_type__line_section
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "Section"
msgstr "ส่วน"

#. module: sale_management
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale_management_1
msgid ""
"Selling the same product in different sizes or colors? Try the product grid "
"and populate your orders with multiple quantities of each variant. This "
"feature also exists in the Purchase application."
msgstr ""
"ขายสินค้าตัวเดียวกันในขนาดหรือสีต่างกัน? "
"ลองใช้ตารางผลิตภัณฑ์และเติมคำสั่งซื้อของคุณด้วยจำนวนที่หลากหลายสำหรับรายละเอียดต่าง"
" ๆ ฟีเจอร์นี้มีอยู่ในแอปพลิเคชันการซื้อด้วย"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__sequence
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: sale_management
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale1_management_0
msgid ""
"Struggling with a complex product catalog? Try out the Product Configurator "
"to help sales configure a product with different options: colors, size, "
"capacity, etc. Make sale orders encoding easier and error-proof."
msgstr ""
"เหนื่อยกับแคตตาล็อกสินค้าที่ซับซ้อน? "
"ลองใช้ตัวกำหนดค่าสินค้าเพื่อช่วยฝ่ายขายกำหนดค่าสินค้าด้วยตัวเลือกต่างๆ เช่น "
"สี ขนาด ความจุ และอื่น ๆ "
"ทำให้การเข้ารหัสคำสั่งซื้อขายง่ายขึ้นและป้องกันข้อผิดพลาด"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template_line__display_type
msgid "Technical field for UX purpose."
msgstr "ฟิลด์เทคนิคสำหรับ UX เป้าหมาย"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template__note
msgid "Terms and conditions"
msgstr "เงื่อนไขและข้อกำหนด"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid ""
"The Administrator can set default Terms & Conditions in Sales Settings. "
"Terms set here will show up instead if you select this quotation template."
msgstr ""
"ผู้ดูแลระบบสามารถตั้งค่าข้อกำหนดและเงื่อนไขเริ่มต้นในการตั้งค่าการขาย "
"ข้อกำหนดที่ตั้งไว้ที่นี่จะปรากฏขึ้นแทนหากคุณเลือกเทมเพลตใบเสนอราคานี้"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_template__mail_template_id
msgid ""
"This e-mail template will be sent on confirmation. Leave empty to send "
"nothing."
msgstr "เทมเพลตอีเมลนี้จะถูกส่งไปเมื่อยืนยัน ปล่อยไว้ถ้าไม่ส่งอะไร"

#. module: sale_management
#: model:ir.model.fields,help:sale_management.field_sale_order_option__is_present
msgid ""
"This field will be checked if the option line's product is already present "
"in the quotation."
msgstr "ฟิลด์นี้จะถูกเลือกหากตัวเลือกไลน์สินค้าแสดงอยู่แล้วในใบเสนอราคา"

#. module: sale_management
#: model:digest.tip,name:sale_management.digest_tip_sale1_management_0
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale1_management_0
msgid "Tip: Odoo supports configurable products"
msgstr "เคล็ดลับ: Odoo รองรับสินค้าที่กำหนดค่าได้"

#. module: sale_management
#: model:digest.tip,name:sale_management.digest_tip_sale_management_1
#: model_terms:digest.tip,tip_description:sale_management.digest_tip_sale_management_1
msgid "Tip: Sell or buy products in bulk with matrixes"
msgstr "เคล็ดลับ: ขายหรือซื้อสินค้าจำนวนมากด้วยเมทริกซ์"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__price_unit
#: model_terms:ir.ui.view,arch_db:sale_management.report_saleorder_document_inherit_sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_portal_content_inherit_sale_management
msgid "Unit Price"
msgstr "ราคาต่อหน่วย"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "Unit Price:"
msgstr "ราคาต่อหน่วย:"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_line__product_uom_id
msgid "Unit of Measure"
msgstr "หน่วยวัด"

#. module: sale_management
#: model:ir.model.fields,field_description:sale_management.field_sale_order_option__uom_id
#: model:ir.model.fields,field_description:sale_management.field_sale_order_template_option__uom_id
msgid "Unit of Measure "
msgstr "หน่วยวัด"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_form_quote
msgid "UoM"
msgstr "หน่วยวัด"

#. module: sale_management
#: model_terms:ir.actions.act_window,help:sale_management.sale_order_template_action
msgid ""
"Use templates to create polished, professional quotes in minutes.\n"
"                Send these quotes by email and let your customers sign online.\n"
"                Use cross-selling and discounts to push and boost your sales."
msgstr ""
"ใช้เทมเพลตเพื่อสร้างใบเสนอราคาที่เป็นมืออาชีพและสวยงามในไม่กี่นาที\n"
"                 ส่งใบเสนอราคาเหล่านี้ทางอีเมลและให้ลูกค้าของคุณลงชื่อออนไลน์\n"
"                ใช้การขายไขว้และส่วนลดเพื่อผลักดันและเพิ่มยอดขายของคุณ"

#. module: sale_management
#: code:addons/sale_management/models/sale_order.py:0
#, python-format
msgid "You cannot add options to a confirmed order."
msgstr "คุณไม่สามารถเพิ่มตัวเลือกให้กับคำสั่งที่ยืนยันแล้ว"

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid ""
"You cannot change the type of a sale quote line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""
"คุณไม่สามารถเปลี่ยนประเภทของไลน์ใบเสนอราคาขายได้ "
"คุณควรลบไลน์ปัจจุบันและสร้างไลน์ใหม่ในประเภทที่เหมาะสมแทน"

#. module: sale_management
#: code:addons/sale_management/models/sale_order.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"ใบเสนอราคาของคุณมีสินค้าจากบริษัท %(product_company)s ในขณะที่ใบเสนอราคาของคุณเป็นของบริษัท %(quote_company)s \n"
"กรุณาเปลี่ยนบริษัทที่เสนอราคาหรือนำสินค้าออกจากบริษัทอื่น(%(bad_products)s)"

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid "Your template cannot contain products from multiple companies."
msgstr "เทมเพลตของคุณต้องไม่มีสินค้าจากหลายบริษัท"

#. module: sale_management
#: code:addons/sale_management/models/sale_order_template.py:0
#, python-format
msgid ""
"Your template contains products from company %(product_company)s whereas your template belongs to company %(template_company)s. \n"
" Please change the company of your template or remove the products from other companies."
msgstr ""
"เทมเพลตของคุณมีสินค้าจากบริษัท %(product_company)s ในขณะที่เทมเพลตของคุณเป็นของ บริษัท %(template_company)s\n"
"โปรดเปลี่ยนบริษัทของเทมเพลตของคุณหรือลบสินค้าออกจากบริษัทอื่น"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "days"
msgstr "วัน"

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_template_view_form
msgid "e.g. Standard Consultancy Package"
msgstr "เช่น แพ็คเกจที่ปรึกษามาตรฐาน"
