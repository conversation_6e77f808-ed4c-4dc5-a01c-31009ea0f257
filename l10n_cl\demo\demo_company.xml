<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_cl" model="res.partner">
        <field name="name">CL Company</field>
        <field name="vat">CL22060449-7</field>
        <field name="l10n_latam_identification_type_id" ref="l10n_cl.it_RUT"/>
        <field name="l10n_cl_sii_taxpayer_type">1</field>
        <field name="street">1</field>
        <field name="city">Santiago</field>
        <field name="country_id" ref="base.cl"/>
        <field name="state_id" ref="base.state_cl_13"/>
        <field name="zip"></field>
        <field name="phone">+57 321 1234567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.clexample.com</field>
    </record>

    <record id="demo_company_cl" model="res.company">
        <field name="name">CL Company</field>
        <field name="partner_id" ref="partner_demo_company_cl"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_cl')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_cl.demo_company_cl'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_cl.cl_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_cl.demo_company_cl')"/>
    </function>
</odoo>
