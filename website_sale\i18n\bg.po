# #-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# #-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale
#
# Translators:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-07-16 09:27+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Bulgarian (http://www.transifex.com/odoo/odoo-9/language/"
"bg/)\n"
"Language: bg\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "(extra fees apply)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
#, fuzzy
msgid ", go to the 'Sales' tab"
msgstr ", отиди на таб \"Продажби\""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ", go to the 'Variants' tab"
msgstr ", отиди на таб \"Варианти\""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "-- Create a new address --"
msgstr "-- Добави Адрес --"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"30-day money-back guarantee<br/>\n"
"                    Free Shipping in U.S.<br/>\n"
"                    Buy now, get in 2 days"
msgstr ""
"Връщане на парите до 30 дни<br/>\n"
"                    Безплатна доставка за София.<br/>\n"
"                    Купи сега и получи до 2 дни"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<br/>\n"
"                                <small>(don't forget to apply the changes)</"
"small>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<br/>\n"
"                                <small>(you'll have to subscribe directly on "
"each of the payment companies' websites)</small>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid ""
"<br/>\n"
"                                <strong>Payment Status:</strong>"
msgstr ""
"<br/>\n"
"                                <strong>Състояние на плащането:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "<i class=\"fa fa-cog\"/> Configure Transfer Details"
msgstr "<i class=\"fa fa-cog\"/> Настройка на Трансферните детайли"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<i class=\"fa fa-comment-o\"/> Website Live Chat on"
msgstr "<i class=\"fa fa-comment-o\"/> Лайв Чатът работи"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<i class=\"fa fa-envelope-o\"/> Email Our Website Expert"
msgstr "<i class=\"fa fa-envelope-o\"/> Изпрати е-мейл на нашите експерти"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Печат"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header
msgid ""
"<i class=\"fa fa-shopping-cart\"/>\n"
"              My cart"
msgstr ""
"<i class=\"fa fa-shopping-cart\"/>\n"
"              Моята Количка"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<span class=\"col-xs-6 text-right h4\">Total:</span>"
msgstr "<span class=\"col-xs-6 text-right h4\">Общо:</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid ""
"<span class=\"col-xs-6 text-right text-muted\" title=\"Taxes may be updated "
"after providing shipping address\"> Taxes:</span>"
msgstr ""
"<span class=\"col-xs-6 text-right text-muted\" title=“Данаците могат да се "
"променят, след като въведете адрес за доставка”> Данъци:</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<span class=\"col-xs-6 text-right text-muted\">Subtotal:</span>"
msgstr "<span class=\"col-xs-6 text-right text-muted\">Междинна сума:</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<span class=\"fa fa-arrow-right\"/> Change Address"
msgstr "<span class=\"fa fa-arrow-right\"/> Промени Адрес"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "<span class=\"fa fa-arrow-right\"/> change"
msgstr "<span class=\"fa fa-arrow-right\"/> промени"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<span class=\"fa fa-comment-o\"/> Website Live Chat on"
msgstr "<span class=\"fa fa-comment-o\"/> Лайв-чат включен"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        At cost price is a good option for heavy or "
"oversized packages."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        Offering free delivery with a minimum amount or "
"minimum number of items should drive up your average order value and help to "
"compensate for the delivery costs."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        You can also create different rates based on order "
"amount ranges, for example 10€ up to a 50€ order, then 5€ after."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.continue_shopping
msgid ""
"<span class=\"fa fa-long-arrow-left\"/> <span class=\"hidden-xs\">Continue "
"Shopping</span><span class=\"visible-xs-inline\">Continue</span>"
msgstr ""
"<span class=\"fa fa-long-arrow-left\"/> <span class=\"hidden-xs\">Продължи с "
"пазаруването</span><span class=\"visible-xs-inline\">Продължи</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "<span class=\"fa fa-long-arrow-left\"/> Return to Cart"
msgstr "<span class=\"fa fa-long-arrow-left\"/>Обратно към Количката"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"hidden-xs\">Process Checkout</span><span class=\"visible-xs-"
"inline\">Checkout</span> <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""
"<span class=\"hidden-xs\">Завърши Поръчката</span><span class=\"visible-xs-"
"inline\">Поръчай</span> <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                            <span class=\"fa fa-shopping-cart\"/>\n"
"                            <strong>2. On Add to Cart window:</strong> Show "
"accessories, services\n"
"                        </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                            <span class=\"fa fa-shopping-cart\"/>\n"
"                            <strong>2. В прозорец \"Добави в количката\":</"
"strong> покажи аксесоари и услуги\n"
"                        </span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                            <span class=\"fa fa-shopping-cart\"/>\n"
"                            <strong>3. On Check-out page:</strong> Show "
"optional products\n"
"                        </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                            <span class=\"fa fa-tag\"/>\n"
"                            <strong> 1. On Product pages:</strong> Show "
"suggested products\n"
"                        </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-cc-paypal\"/><strong> "
"Paypal</strong> (Recommended for starters)</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-credit-card\"/><strong> "
"Ogone, Adyen, Authorize.net, Buckaroo...</strong></span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-lock\"/><strong>Wire "
"transfer</strong> (Slow and inefficient)</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-pencil-square-o\"/><strong> "
"Web-Services</strong><br/>scripts development</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-shopping-cart\"/><strong> At "
"cost price</strong> (customer pay what you pay)</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-sitemap\"/><strong> "
"Importation</strong><br/>by using a CSV file</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-smile-o\"/><strong> Free "
"delivery</strong> (risky, but has best potential)</span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-smile-o\"/><strong> "
"Безплатна доставка</strong> (рисковано, но има голям потенциал)</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-table\"/><strong> Flat "
"rates</strong> (everybody pays the same)</span>"
msgstr ""
"<span class=\"panel-title\"><span class=\"fa fa-table\"/><strong> Статична "
"цена</strong> (всички плащат еднакво)</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"text-danger\">* </span>Field 2"
msgstr "<span class=\"text-danger\">* </span>Поле 2"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "<strong>Add to Cart</strong>"
msgstr "<strong>Добави в количката</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Bonuses:</strong> what you get on top of the offer"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Call to action</strong> short and clear: (Add to Cart, Ask for "
"quote,...)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Cons:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Cons:</strong> The delivery cost may be discouraging for your "
"cheapest items."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Cons:</strong> customers have to wait until checkout to find out the "
"delivery price."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Cons:</strong> will require you to either absorb the cost or "
"slightly increase your prices to cover it."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Contact us now:</strong><br/>"
msgstr "<strong>Свържете се с нас сега:</strong><br/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Customers review:</strong> what do the customers think of the product"
msgstr ""
"<strong>Клиентски менния:</strong> какво мислят куповачите за този продукт"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Features and benefits:</strong> what the product does and why that "
"is good"
msgstr ""
"<strong>Характеристики и ползи:</strong> Какво прави продукта и защо е добър"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>High-quality picture</strong>"
msgstr "<strong>Висококачествена снимка</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Key features, emotional and commercial content</strong><br/>\n"
"                            Recommended for at least for your top products, "
"because it can have a big impact on your sales and conversion rates."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Mandatory content</strong><br/>"
msgstr "<strong>Задължително съдържание</strong><br/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Name</strong> of your product"
msgstr "<strong>Име</strong> на вашия продукт"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Need help to import your products?</strong>"
msgstr ""
"<strong>Имате ли нужда от помощ при импортирането на продуктите?</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Order Details:</strong>"
msgstr "<strong>Детайли за Поръчката:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "<strong>Payment Method:</strong>"
msgstr "<strong>Метод за плащане:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Payment information:</strong>"
msgstr "<strong>Информация за плащане:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Pictures gallery of the product:</strong> all angles, detailed view, "
"package,etc."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Price</strong> with currency"
msgstr "<strong>Цена</strong> във валута"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Product variants</strong><br/>\n"
"                            Product variants are used to offer variations of "
"the same product to your customers on the products page.<br/>\n"
"                            For example, the customer choose a T-shirt and "
"then select its size or color."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Pros:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Pros:</strong> being transparent about your charges can win you the "
"trust of your customers."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Pros:</strong> gives you a significant advantage over any "
"competitors that don't offer the same perk."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Pros:</strong> simple for your customers to understand."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Reassurance arguments</strong><br/>\n"
"                            Anticipate your customers questions &amp; "
"worries on practical details like Shipping rates &amp; policies, Return "
"&amp; replacement policies, Payment methods &amp; security, General "
"Conditions, etc."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Recommended action:</strong>"
msgstr "<strong>Препоръчително действие:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Recommended actions:</strong>"
msgstr "<strong>Препоръчителни действия:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>See it in action</strong><br/>"
msgstr "<strong>Виж го в действие</strong><br/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Short description</strong> of the product or service"
msgstr "<strong>Кратко описание</strong> на продукта или усулугата"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Technical information:</strong> what do you get and how does it work?"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Total:</strong>"
msgstr "<strong>Общо:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Value proposition:</strong> what’s the end-benefit of this product "
"and who is it for?"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Variants</strong> of the product like size or color (see below)"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_accessory_product_ids
msgid "Accessory Products"
msgstr "Аксесоари"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Activate 'Suggested products' from the 'Customize' menu."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Activate the 'Support multiple variants per products' option in"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Activate the payment options you want to use"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Add as many variants as you need from 3 different types: radio buttons, drop-"
"down menu or color buttons."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Add to Cart"
msgstr "Добави в количката"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.option_collapse_products_categories
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid "All Products"
msgstr "Всички Продукти"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_pricelist_selectable
msgid "Allow the end user to choose this price list"
msgstr "Позволете на крайните потребители да избират ценова листа"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template_alternative_product_ids
msgid "Appear on the product page"
msgstr "Вижда се на продуктовата страница"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template_accessory_product_ids
msgid "Appear on the shopping cart"
msgstr "Вижда се в количката"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "Apply"
msgstr "Приложи"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux Pricelist"
msgstr "Ценова листа за Benelux"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Bill To:"
msgstr "Платец:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing Information"
msgstr "Информация за Плащане"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Billing<span class=\"chevron\"/>"
msgstr "Плащане<span class=\"chevron\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Can take up to several days for you to receive the money"
msgstr "Може да отнеме няколко дни за да получите парите"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_cart_quantity
msgid "Cart Quantity"
msgstr "Количество в Количката"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:59
#, python-format
msgid "Change the price"
msgstr "Промени цената"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_child_id
msgid "Children Categories"
msgstr "Подкатегории"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:72
#, python-format
msgid "Choose an image"
msgstr "Избери изображение"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:73
#, python-format
msgid "Choose an image from the library."
msgstr "изображение от библиотеката."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:39
#, python-format
msgid "Choose name"
msgstr "Избери Име"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas Pricelist"
msgstr "Коледна Ценова листа"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "City"
msgstr "Град"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:25
#, python-format
msgid "Click here to add a new product."
msgstr "Натиснете за да добавите нов продукт."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:67
#, python-format
msgid "Click here to set an image describing your product."
msgstr "Натиснете за да добавите изображение за вашия продукт."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:47
#, python-format
msgid "Click on <em>Continue</em> to create the product."
msgstr "Натисни <em>Продължи</em> за да създадете продукт."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:106
#, python-format
msgid "Click on <em>Publish</em> your product so your customers can see it."
msgstr "Натисни <em>Публикувай</em> за да стане видим за клиентите."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:84
#, python-format
msgid "Click on <em>Save</em> to add the image to the product description."
msgstr ""
"Натисни <em>Запис</em> За да добавиш изображението към описанието на "
"продукта."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Click to define a new category."
msgstr "Натиснете за да дефинирате нова категория"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:112
#, python-format
msgid "Close Tutorial"
msgstr "Затвори Ръководството"

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Color"
msgstr "Цвят"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Company Name"
msgstr "Име на фирма"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Configure your bank account(s)"
msgstr "Настройте вашите банкови сметки."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Configure your delivery prices"
msgstr "Конфигурирайте цените за доставка"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Confirm <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Потвърди <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm Order <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Потвърди Поръчка <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Confirmation<span class=\"chevron\"/>"
msgstr "Потвърждение<span class=\"chevron\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#, fuzzy
msgid "Confirmed"
msgstr ""
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Потвърден\n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Потвърдено"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:110
#, python-format
msgid "Congratulations"
msgstr "Поздравления"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:111
#, python-format
msgid "Congratulations! You just created and published your first product."
msgstr "Поздравления! Току що създадохте и публикувахте първия си продукт"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:53
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
#, python-format
msgid "Continue"
msgstr "Продължи"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Country"
msgstr "Държава"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country_group
msgid "Country Group"
msgstr "Група Държави"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_country_group_ids
msgid "Country Groups"
msgstr "Групи Държави"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Country..."
msgstr "Държава..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "Coupon Code"
msgstr "Код за отстъпка"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:46
#, python-format
msgid "Create Product"
msgstr "Създай Продукт"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:31
#, python-format
msgid "Create a new product"
msgstr "Създай нов продукт"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:14
#, python-format
msgid "Create a product"
msgstr "Създай продукт"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:24
#, python-format
msgid "Create your first product"
msgstr "Създай първия си продукт"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_style_create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_create_uid
msgid "Created by"
msgstr "Създадено от"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_create_date
#: model:ir.model.fields,field_description:website_sale.field_product_style_create_date
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_create_date
msgid "Created on"
msgstr "Създадено на"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Customize your Payment message"
msgstr "Персонализирайте Съобщенията за плащане"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_dhl
msgid "DHL integration"
msgstr "DHL интеграция"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_currency_id
msgid "Default Currency"
msgstr "Валута по подразбиране"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_id
msgid "Default Pricelist"
msgstr "Ценова листа по подразбиране"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Default Sales Team"
msgstr "Екип за Продажби по Подразбиране"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Default Salesperson"
msgstr "Продавач по подразбиране"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Defining a good delivery strategy is difficult: you don't want to cut into "
"your margins, but you want to be attractive to customers."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Delivery Strategy"
msgstr "Стратегия за Доставка"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_quote_description
msgid "Description for the quote"
msgstr "Описание за офертата"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_description
msgid "Description for the website"
msgstr "Описание за уебсайт"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template_website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr "Определи ред за показване в електронния магазин"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line_discounted_price
msgid "Discounted price"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_display_name
#: model:ir.model.fields,field_description:website_sale.field_product_style_display_name
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:90
#, python-format
msgid "Drag & Drop a block"
msgstr "Drag & Drop a block"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:91
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist_code
msgid "E-commerce Promotional Code"
msgstr "Промоционален Код за Електронна Търговия"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:60
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr "Edit the price of this product by clicking on the amount."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Electronic payments have the advantage of being integrated into the buying "
"process. This means your customers will be less likely to drop out before "
"the payment, resulting in a higher conversion rate at the end."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Email"
msgstr "Имейл"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:40
#, python-format
msgid "Enter a name for your new product"
msgstr "Въведете име за новия продукт"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Enter their identification credentials"
msgstr "Въведете идентификационни данни"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Enter your existing products into this CSV file, respecting its structure."
msgstr "Подгответе CSV файл с продуктите в правилна структура."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_split_method
msgid ""
"Equal : Cost will be equally divided.\n"
"By Quantity : Cost will be divided according to product's quantity.\n"
"By Current cost : Cost will be divided according to product's current cost.\n"
"By Weight : Cost will be divided depending on its weight.\n"
"By Volume : Cost will be divided depending on its volume."
msgstr ""

#. module: website_sale
#: constraint:product.public.category:0
msgid "Error ! You cannot create recursive categories."
msgstr "Грешка, не можете да създавате рекурсивни категории."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Example of Good Product Page"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Example: you sell T-shirts in the US only. You can offer free delivery "
"because your items are medium priced and the delivery costs are limited and "
"well defined."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Example: you sell cheap specialized electronic components. You choose flat "
"rates because the price of an item is sometimes lower than the delivery "
"costs."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Example: you sell custom-made wood sculptures, and because your customers "
"are all over the world, each delivery is different and at cost price."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Export the 3 products you have already created by checking them and choosing "
"'Export' from the 'Action' menu"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
#, fuzzy
msgid "Extra Info<span class=\"chevron\"/>"
msgstr "Плащане<span class=\"chevron\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Extra Step"
msgstr "Допълнителна Стъпка"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_fedex
msgid "Fedex integration"
msgstr "Fedex интеграция"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field 1"
msgstr "Поле 1"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field 3"
msgstr "Поле 3"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field not custom"
msgstr "Field not custom"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field not required"
msgstr "Не е задължително поле"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field required"
msgstr "Задължително поне"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Focus on adding content and improving the pages for your best-selling "
"products: don't try to create complete pages for all your products at first!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Free and easy to setup"
msgstr "Безплатно и лесно за настройка"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "From a"
msgstr "От"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr ""
"Показва в последователен ред при показване на списък с продуктови категории."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Go to the"
msgstr "Отиди на"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_style_html_class
msgid "HTML Classes"
msgstr "HTML Classes"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute_value_html_color
msgid "HTML Color Index"
msgstr "HTML Color Index"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP routing"
msgstr "HTTP Маршрутизиране"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "Have a coupon code? Fill in this field and apply."
msgstr "Имате код за отстъпка? Поставето го и го приложете."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Here are <strong>some pros and cons</strong> to help you decide:"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_attribute_value_html_color
msgid ""
"Here you can set a specific HTML color index (e.g. #ff0000) to display the "
"color on the website if the attibute type is 'Color'."
msgstr ""
"Here you can set a specific HTML color index (e.g. #ff0000) to display the "
"color on the website if the attibute type is 'Color'."

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Hidden"
msgstr "Скрит"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_id
#: model:ir.model.fields,field_description:website_sale.field_product_style_id
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_id_11000
msgid "ID"
msgstr "ID"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"If you have an eCommerce, one of your objectives is of course to grow your "
"revenues by\n"
"                    selling more and pricier products. Luckily for you, Odoo "
"integrates three powerful\n"
"                    customizations for that."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image
msgid "Image"
msgstr "Изображение"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Imagine a new customer who comes to your website, finds the product they "
"want and add it to their cart.<br/>\n"
"                        Then they get to the checkout page and are hit with "
"the delivery and handling charges.<br/>\n"
"                        Suddenly, a product that looked like it was a fair "
"price seems little expensive, and the customer leaves your website "
"disappointed."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Import Your Products"
msgstr "Въведете Вашите Продукти"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"In order to take money from customers, you need a way of accepting payments."
"<br/>\n"
"                        That's what a payment gateway is for: it helps you "
"make money, but that does cost money.<br/>\n"
"                        That why it's important to choose the right provider "
"for your online payments."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Increase your average cart amount by proposing complementary products to "
"your visitors."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Increase your chances to make a sale by displaying suggested products."
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:567
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "Невалиден е-мейл! Моля въведете валиден е-мейл адрес."

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:86
#, python-format
msgid "It is forbidden to modify a sale order which is not in draft status"
msgstr "Забранено е да променята поръчка, която не е в драфт статус"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"It's difficult to recommend one over the others. So, simply pick the one "
"that is more popular in your country!"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_landed_cost_ok
msgid "Landed Costs"
msgstr "Доставни разходи"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category___last_update
#: model:ir.model.fields,field_description:website_sale.field_product_style___last_update
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist___last_update
msgid "Last Modified on"
msgstr "Последно променено на"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner_last_website_so_id
msgid "Last Online Sale Order"
msgstr "Последна Поръчка"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_style_write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_write_uid
msgid "Last Updated by"
msgstr "Последно обновено от"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_write_date
#: model:ir.model.fields,field_description:website_sale.field_product_style_write_date
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_write_date
msgid "Last Updated on"
msgstr "Последно обновено на"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image_medium
msgid "Medium-sized image"
msgstr "Картинка със среден размер"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image_medium
msgid ""
"Medium-sized image of the category. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""
"Medium-sized image of the category. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Merchant Connectors"
msgstr "Интеграция с Търговци"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr "Съобщение за ред от поръчка за покупка"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Съобщение за ред от поръчка за продажба"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.js:15
#, python-format
msgid "My Cart"
msgstr "Моята количка"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_complete_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_name
msgid "Name"
msgstr "Име"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Name (Shipping)"
msgstr "Име (Доставка)"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/controllers/main.py:976
#: code:addons/website_sale/static/src/js/website_sale.editor.js:18
#: model_terms:ir.ui.view,arch_db:website_sale.content_new_product
#, python-format
msgid "New Product"
msgstr "Нов Продукт"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:51
#, python-format
msgid "New product created"
msgstr "Нов продукт е създаден"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "No monthly fees for standard offer"
msgstr "Без месечни такси за стандартните оферти"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined."
msgstr "Няма дефинирани продукти."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Now you can also <strong>import your existing products:</strong>"
msgstr ""
"Сега можете също да <strong>импортирате съществуващите продукти:</strong>"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale.xml:12
#, python-format
msgid "OK"
msgstr "Добре"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Odoo offers an importation service to handle the whole process for you!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Odoo's web-services allows developers to create scripts that will load data "
"automatically into the system."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"On your website, go to the product page where you want to add suggested "
"products."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:98
#, python-format
msgid "Once you click on <em>Save</em>, your product is updated."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_only_services
msgid "Only Services"
msgstr "Само Услуги"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Order"
msgstr "Ред"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_website_order_line
msgid "Order Lines displayed on Website"
msgstr "Order Lines displayed on Website"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order_website_order_line
msgid ""
"Order Lines to be displayed on the website. They should not be used for "
"computation purpose."
msgstr ""
"Order Lines to be displayed on the website. They should not be used for "
"computation purpose."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_parent_id
msgid "Parent Category"
msgstr "Родителска категория"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Partner"
msgstr "Партньор"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:779
#: code:addons/website_sale/controllers/main.py:859
#, python-format
msgid "Pay Now"
msgstr "Плати Сега"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay Now <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Плати Сега <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_payment_acquirer_id
msgid "Payment Acquirer"
msgstr "Обработчик на плащането"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "Payment Information"
msgstr "Информация за Плащането"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Payment Method:"
msgstr "Начин на плащане:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Payment Methods"
msgstr "Начини на плащане"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "Плащане"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Payment<span class=\"chevron\"/>"
msgstr "Плащане<span class=\"chevron\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Phone"
msgstr "Телефон"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Policies"
msgstr "Политика на сайта"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Price"
msgstr "Цена"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_website_pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "Ценова лист за Електронен Магазин"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_pricelist_id
#, fuzzy
msgid "Pricelist"
msgstr ""
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Ценоразпис\n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Ценова листа"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_name
msgid "Pricelist Name"
msgstr "Име на ценова листа"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_product
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Product"
msgstr "Продукт"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "Продуктови Атрибути"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Product Name"
msgstr "Име на продукт"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Product Pages"
msgstr "Продуктови Страници"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
#, fuzzy
msgid "Product Public Categories"
msgstr "Продуктови Категории"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template
msgid "Product Template"
msgstr "Шаблон за продукт"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Product detail form"
msgstr "Форма за продуктови детайли"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.product_price
msgid "Product not available"
msgstr "Продуктът не е наличен"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Product not found!"
msgstr "Не е намерен продукт!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Products"
msgstr "Продукти"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Products list"
msgstr "Списък с Продукти"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Products list view"
msgstr "Списачен изглед на Продуктите"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Promote"
msgstr "Промотирай"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:105
#, python-format
msgid "Publish your product"
msgstr "Публикувай продукта"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_rating_rating_website_published
msgid "Published"
msgstr "Публикуван"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_purchase_line_warn
msgid "Purchase Order Line"
msgstr "Ред от поръчка за покупка"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push down"
msgstr "Свали надолу"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push to bottom"
msgstr "Качи най-долу"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push to top"
msgstr "Качи най-горе"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push up"
msgstr "Качи нагоре"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Put the practical details (shipping, payment options,...) as links in the "
"footer; that way, they will be accessible from all your product pages."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Qty:"
msgstr "Кол:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Quantity"
msgstr "Количество"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Quick and easy to set up"
msgstr "Бързо и лесно за настройка"

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Radio"
msgstr "Радио"

#. module: website_sale
#: model:ir.model,name:website_sale.model_rating_rating
#: model:ir.model.fields,field_description:website_sale.field_product_product_rating_ids
msgid "Rating"
msgstr "Рейтинг"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Read the"
msgstr "Прочетете"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Return to the product list."
msgstr "Обратно към списъка с продукти."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Review Order<span class=\"chevron\"/>"
msgstr "Преглед на Поръчката<span class=\"chevron\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Sale"
msgstr "Продажба"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sales / Settings"
msgstr "Продажби / Настройки"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
#, fuzzy
msgid "Sales Order"
msgstr ""
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Поръчка за продажба\n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Нареждане за продажба"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
#: model:ir.model.fields,field_description:website_sale.field_product_product_sale_line_warn
msgid "Sales Order Line"
msgstr "Ред от нареждане за продажба"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website_salesteam_id
msgid "Sales Team"
msgstr "Търговски отдел"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website_salesperson_id
msgid "Salesperson"
msgstr "Търговец"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:83
#, python-format
msgid "Save"
msgstr "Запазване"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Save and import the modified CSV file from the 'More' menu of the"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:97
#, python-format
msgid "Save your modifications"
msgstr "Запази модификациите"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Secure Payment"
msgstr "Защитено Плащане"

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Select"
msgstr "Избор"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:32
#, python-format
msgid ""
"Select <em>New Product</em> to create it and manage its properties to boost "
"your sales."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Select a product from the"
msgstr "Избери продукт от"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_selectable
msgid "Selectable"
msgstr "Избираем"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_purchase_line_warn
#: model:ir.model.fields,help:website_sale.field_product_product_sale_line_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"При избор на опция \"Предупреждение\" потребителят ще бъде уведомен със "
"съобщение, при избор на \"Блокиращо съобщение\" , заедно със съобщението ще "
"бъде наложен отвод  и ще се прекъсне работния ход. Съобщението трябва да "
"бъде въведено в следващото поле."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sell"
msgstr "Продай"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sell More"
msgstr "Продай Повече"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_sequence
msgid "Sequence"
msgstr "Последователност"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Ship To:"
msgstr "Достави на:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Ship to the same address"
msgstr "Достави на същия адрес"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping"
msgstr "Доставка"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Shipping &amp;"
msgstr "Доставка &amp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Shipping Connectors"
msgstr "Интеграция с Доставчици"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping Information"
msgstr "Информация за Доставка"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model:website.menu,name:website_sale.menu_shop
msgid "Shop"
msgstr "Магазин"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "Магазин - Поръчай"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "Магазин - Потвърдено"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Method"
msgstr "Магазин - Избери Начин за Плащане"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Shopping Cart"
msgstr "Количка за пазаруване"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
#, fuzzy
msgid "Sign in"
msgstr ""
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Вписване\n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Вход"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Simply add one or more products as an <strong>'Accessory Product'</strong>."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Simply add the product you want as an <strong>'Optional Product'</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Size"
msgstr "Размер"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_size_x
msgid "Size X"
msgstr "Размер X"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_size_y
msgid "Size Y"
msgstr "Размер Y"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:19
#, python-format
msgid "Skip It"
msgstr "Пропусни"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image_small
msgid "Small-sized image"
msgstr "Картинка с малък размер"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image_small
msgid ""
"Small-sized image of the category. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is "
"required."
msgstr ""
"Small-sized image of the category. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is "
"required."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Some customers prefer to pay this way"
msgstr "Някой клиенти предпочитат да плащат по този начин"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:589
#, python-format
msgid "Some required fields are empty."
msgstr "Някой от задължителните полета са празни."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Sorry, this product is not available anymore."
msgstr "Съжеляваме, този продукт не е наличен вече."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_split_method
msgid "Split Method"
msgstr "Метод на разделяне"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:19
#, python-format
msgid "Start Tutorial"
msgstr "Започни Ръководство"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "State / Province"
msgstr "Област"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "State / Province..."
msgstr "Област ..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Street"
msgstr "Улица"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_style_name
msgid "Style Name"
msgstr "Име на стил"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_style_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_style_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Styles"
msgstr "Стилове"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Subtotal"
msgstr "Междинна сума"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_alternative_product_ids
msgid "Suggested Products"
msgstr "Препоръчани Продукти"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.recommended_products
msgid "Suggested alternatives:"
msgstr "Препоръчителни латернативи:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested products:"
msgstr "Препоръчителни продукти:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Taxes"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_temando
#, fuzzy
msgid "Temando integration"
msgstr "Fedex интеграция"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "Благодарим за вашата поръчка"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"The best way to start your online shop is by creating 3 products pages "
"directly in the website.<br/>\n"
"                        To help you, here are some guidelines that will "
"convert customers:"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_website_url
msgid "The full URL to access the document through the website."
msgstr "Пълен URL за достъп до документа през уебсайта."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "The payment seems to have been canceled."
msgstr "Плащането изглежда е отказано."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "There seems to be an error with your request."
msgstr "Изглежда че има грешка при изпълнението на вашата заявка."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image
msgid ""
"This field holds the image used as image for the category, limited to "
"1024x1024px."
msgstr ""
"This field holds the image used as image for the category, limited to "
"1024x1024px."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:52
#, python-format
msgid "This page contains all the information related to the new product."
msgstr "Тази страница съдържа цялата информация за новия продукт."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "This promo code is not available"
msgstr "Промо кодът не е достъпен"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template_public_categ_ids
msgid "Those categories are used to group similar products for e-commerce."
msgstr ""
"Тези категории се използват за групиране на продукти в електронния магазин."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "To use them:"
msgstr "За използване:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "Total"
msgstr "Общо"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_payment_tx_id
msgid "Transaction"
msgstr "Транзакция"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Try to apply what you've learned above by manually creating three Product "
"pages from the Content menu."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute_type
msgid "Type"
msgstr "Вид"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_ups
msgid "UPS integration"
msgstr "UPS интеграция"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_usps
msgid "USPS integration"
msgstr "USPS интеграция"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Unit Price"
msgstr "Единична цена"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:66
#, python-format
msgid "Update image"
msgstr "Обнови изображение"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Use the <i>'Content'</i> top menu to create a new product."
msgstr ""
"Използвайте главно меню <i>'Съдържание'</i> за да създадете нов продукт."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Users will buy more accessories and services if they can add them to their "
"cart in one click."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "VAT Number"
msgstr "ДДС Номер"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Validate Order"
msgstr "Валидирай Поръчка"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "View Cart ("
msgstr "Виж Количка ("

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_published
msgid "Visible in Website"
msgstr "Видим в уеб сайта"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_rating_rating_website_published
msgid "Visible on the website as a comment"
msgstr "Видим в уеб сайта, като коментар"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Web Service technical documentation."
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_website_id
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Website"
msgstr "Уеб-страница"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Website Categories"
msgstr "Уебсайт Категории"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_country_group_website_pricelist_ids
msgid "Website Price Lists"
msgstr "Уебсайт Ценови Листи"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_pricelist_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.website_pricelist_tree_view
msgid "Website PriceLists"
msgstr "Уебсайт Ценови Листи"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_pricelists_by_website
#: model:ir.model,name:website_sale.model_website_pricelist
#: model:ir.ui.menu,name:website_sale.menu_website_sale_pricelists
msgid "Website Pricelist"
msgstr "Уебсайт Ценови Листи"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:427
#, python-format
msgid "Website Pricelist for %s"
msgstr "Уебсайт Ценови Листи за %s"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_product_public_category
msgid "Website Product Categories"
msgstr "Категория Продукти в Уебсайт"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product_public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_public_categ_ids
msgid "Website Product Category"
msgstr "Категория продукти в Уебсайт"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "Публични Категория Продукти в Уебсайт"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "Електронен Магазин"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_url
msgid "Website URL"
msgstr "URL на Уебсайта"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_description
msgid "Website meta description"
msgstr "Уебсайт Мета описание"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_keywords
msgid "Website meta keywords"
msgstr "Уебсайт Мета ключови думи"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_title
msgid "Website meta title"
msgstr "Уебсайт Мета заглавие"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:17
#, python-format
msgid "Welcome to your shop"
msgstr "Добре дошли във вашия магазин"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"You can also define different prices for the variants you created by "
"activating the 'Use pricelists to adapt your price per customers' option in"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "You can setup 3 types of <strong>payment methods in Odoo:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "You have to reconcile the payment manually"
msgstr "Трябва да съгласувате плащането ръчно"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:18
#, python-format
msgid ""
"You successfully installed the e-commerce. This guide will help you to "
"create your product and promote your sales."
msgstr ""
"Вие успешно инсталирахте електронен магазин. Това упътване ще ви помогне да "
"създадете продукт и промотирате вашите стоки."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Your Address"
msgstr "Вашият Адрес"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Your Name"
msgstr "Вашето Име"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Your Order"
msgstr "Вашата поръчка"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Your cart is empty!"
msgstr "Количката ви е празна!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your order has been confirmed, thank you for your loyalty."
msgstr "Вашата поръчка е потвърдена, благодарим ви за лоялността."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your payment has been received."
msgstr "Вашето плащане е получено"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your transaction is waiting a manual confirmation."
msgstr "Вашата транзакция очаква ръчно потвърждение."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your transaction is waiting confirmation."
msgstr "Транзакцията чака за потвърждение."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Zip / Postal Code"
msgstr "Пощенски Код"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "and fill in one or more <strong>'Suggested Products'</strong>."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "code..."
msgstr "код..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "comment"
msgstr "коментар"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "comments"
msgstr "коментари"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_sale_ebay
msgid "eBay connector"
msgstr "eBay интеграция"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "eCommerce"
msgstr "Електронна Търговия"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "items)"
msgstr "продукта)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "of the Sales module"
msgstr "от модул Продажби"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "or"
msgstr "или"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "pagination form-inline"
msgstr "вградено страниране"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute_value
msgid "product.attribute.value"
msgstr "product.attribute.value"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_style
msgid "product.style"
msgstr "product.style"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "select..."
msgstr "избери..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "using one or several of the strategies above"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_config_settings
msgid "website.config.settings"
msgstr "website.config.settings"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ 256 bit encryption"
msgstr "☑ 256 битова защита"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ 30-days money-back guarantee"
msgstr "☑ 30 дни за върщане"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ Invoice sent by e-Mail"
msgstr "☑ Фактура по е-мейл"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ Processed by Ogone"
msgstr "☑ Обработено от Епей"

#~ msgid ", go in the 'Sales' tab"
#~ msgstr ", отиди на таб \"Продажби\""

#~ msgid "<span class=\"fa fa-envelope-o\"/> Email Our Website Expert"
#~ msgstr ""
#~ "<span class=\"fa fa-envelope-o\"/> Изпрати е-мейл на нашите експерти"

#~ msgid "Available in the Point of Sale"
#~ msgstr "Видим в ПОС"

#~ msgid ""
#~ "Check if the product should be weighted using the hardware scale "
#~ "integration"
#~ msgstr "Отбележи, ако продукта трябва да бъде премерен с кантар"

#~ msgid "Check if you want this product to appear in the Point of Sale"
#~ msgstr "Изберете, ако искате продукта да се вижда в ПОС"

#~ msgid ""
#~ "Check this box to generate Call for Tenders instead of generating "
#~ "requests for quotation from procurement."
#~ msgstr ""
#~ "Избери за да се създаде запитване за Търг, вместо да се създаде запитване/"
#~ "оферта за доставка."

#~ msgid "Extra Info"
#~ msgstr "Допълнителна информация"

#~ msgid "Payment"
#~ msgstr "Плащане"

#~ msgid "Point of Sale Category"
#~ msgstr "ПОС Категория"

#~ msgid "Procurement"
#~ msgstr "Снабдаване"

#~ msgid "Project"
#~ msgstr "Проект"

#~ msgid ""
#~ "Those categories are used to group similar products for point of sale."
#~ msgstr "Тези категории се използват за групиране на продукти в ПОС."

#~ msgid "To Weigh With Scale"
#~ msgstr "Да се премери на кантар"

#~ msgid "Website Comments"
#~ msgstr "Уебсайт Коментари"
