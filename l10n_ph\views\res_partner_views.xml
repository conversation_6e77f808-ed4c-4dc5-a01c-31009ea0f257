<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <record id="view_partner_form" model="ir.ui.view">
        <field name="name">res.partner.form.inherit.l10n_ph_bir</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='vat']" position="after">
                <field name="branch_code"/>
                <field name="first_name" attrs="{'invisible': [('is_company','=', True)]}"/>
                <field name="middle_name" attrs="{'invisible': [('is_company','=', True)]}"/>
                <field name="last_name" attrs="{'invisible': [('is_company','=', True)]}"/>
            </xpath>
        </field>
    </record>
</odoo>
