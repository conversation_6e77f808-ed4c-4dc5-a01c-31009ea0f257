# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_timesheet_attendance
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# UAB "Draugiš<PERSON> sprendimai" <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>as Versada <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__company_id
msgid "Company"
msgstr "Įmonė"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__date
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "Date"
msgstr "Data"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "Employee"
msgstr "Darbuotojas"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__id
msgid "ID"
msgstr "ID"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: hr_timesheet_attendance
#: model_terms:ir.actions.act_window,help:hr_timesheet_attendance.action_hr_timesheet_attendance_report
msgid "No data yet!"
msgstr "Jokių duomenų!"

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
msgid "Sum of Total Attendance"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
msgid "Sum of Total Difference"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
msgid "Sum of Total Timesheet"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.actions.act_window,name:hr_timesheet_attendance.action_hr_timesheet_attendance_report
#: model:ir.ui.menu,name:hr_timesheet_attendance.menu_hr_timesheet_attendance_report
msgid "Timesheet / Attendance"
msgstr "Darbo apskaitos žiniaraštis / Lankomumas"

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "Timesheet Attendance"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.model,name:hr_timesheet_attendance.model_hr_timesheet_attendance_report
msgid "Timesheet Attendance Report"
msgstr "Darbo žiniaraščio lankomumo ataskaita"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__total_attendance
msgid "Total Attendance"
msgstr "Bendras lankomumas"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__total_difference
msgid "Total Difference"
msgstr "Bendras skirtumas"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__total_timesheet
msgid "Total Timesheet"
msgstr "Bendras darbo laiko apskaitos žiniaraštis"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__user_id
msgid "User"
msgstr "Vartotojas"
