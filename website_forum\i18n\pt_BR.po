# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_forum
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON>scimento, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Layna Nascimento, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_answers
msgid "# Answers"
msgstr "# Respostas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_favorites
msgid "# Favorites"
msgstr "# Favoritos"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_posts
msgid "# Posts"
msgstr "# Postagens"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_views
msgid "# Views"
msgstr "# Visualizações"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to accept or refuse an answer."
msgstr "%d karma necessário para aceitar ou recusar uma resposta."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to answer a question."
msgstr "%d karma necessário para responder uma pergunta."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to close or reopen a post."
msgstr "%d karma necessário para fechar ou reabrir uma postagem."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to comment."
msgstr "%d karma necessário para comentar."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert a comment to an answer."
msgstr "%d karma necessário para converter um comentário em uma resposta."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert an answer to a comment."
msgstr "%d karma necessário para converter uma resposta em um comentário."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert your comment to an answer."
msgstr "%d karma necessário para converter seu comentário em uma resposta."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new Tag."
msgstr "%d karma necessário para criar um novo Marcador."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new question."
msgstr "%d karma necessário para criar uma nova pergunta."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to delete or reactivate a post."
msgstr "%d karma required para excluir ou reativar uma postagem."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to downvote."
msgstr "%d karma necessário para rebaixar."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to edit a post."
msgstr "%d karma necessário para editar uma postagem."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to flag a post."
msgstr "%d karma necessário para marcar uma postagem."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to mark a post as offensive."
msgstr "%d karma required necessário para marcar uma postagem como ofensiva."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to post an image or link."
msgstr "%d karma necessário para postar uma imagem ou link."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to refuse a post."
msgstr "%d karma necessário para recusar uma postagem."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to retag."
msgstr "%d karma necessário para remarcar."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a comment."
msgstr "%d karma necessário para desvincular um comentário."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a post."
msgstr "%d karma necessário para desvincular uma postagem."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to upvote."
msgstr "%d karma necessário para condecorar."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to validate a post."
msgstr "%d karma necessário para validar uma postagem."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "&amp;nbsp;and&amp;nbsp;"
msgstr "&amp;nbsp;and&amp;nbsp;"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr "(A seção acima foi adaptado de Stackoverflow FAQ)."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "(votes - 1) **"
msgstr "(votos - 1) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ", by"
msgstr ", por"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ", consider <b>adding an example</b>."
msgstr ", considere <b>adicionar um exemplo</b>."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "/ (days + 2) **"
msgstr "/ (dias + 2) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "0 Answers"
msgstr "0 Respostas"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "45% of questions shared"
msgstr "45% de perguntas compartilhada"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr ""
"65% a mais de chances de\n"
"obter uma resposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<b class=\"d-block\">You have a pending post</b>\n"
"                        Please wait for a moderator to validate your previous post to be allowed replying questions."
msgstr ""
"<b class=\"d-block\">Você possui uma postagem pendente</b>\n"
"                        Por favor, espere um moderador validar sua postagem anterior para poder responder a perguntas."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b> [Offensive]</b>"
msgstr "<b>[Ofensivo]</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""
"<b>As respostas não devem adicionar ou expandir as perguntas</b>. Em vez "
"disso, edite a pergunta ou adicione um comentário à pergunta."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead, either edit the "
"question or add a comment."
msgstr ""
"<b>As respostas não devem adicionar ou expandir as perguntas</b>. Em vez "
"disso, edite a pergunta ou adicione um comentário."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""
"<b>As respostas não devem comentar outras respostas</b>. Em vez disso, "
"adicione um comentário nas outras respostas."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not start debates</b> This community Q&amp;A is not a "
"discussion group. Please avoid holding debates in your answers as they tend "
"to dilute the essence of questions and answers. For brief discussions please"
" use commenting facility."
msgstr ""
"<b>As respostas não devem iniciar debates</b> Esta comunidade Q&amp;A não é "
"um grupo de discussão. Evite realizar debates em suas respostas, pois eles "
"tendem a diluir a essência das perguntas e respostas. Para discussões "
"breves, use o recurso de comentários."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""
"<b>As respostas não devem apontar apenas para outras questões</b>. Em vez "
"disso, adicione uma indicação de comentário de pergunta \"Possível duplicata"
" de ...\". No entanto, não há problema em incluir links para outras "
"perguntas ou respostas fornecendo informações adicionais relevantes."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other questions</b>.Instead add a comment"
" indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to "
"include links to other questions or answers providing relevant additional "
"information."
msgstr ""
"<b>As respostas não devem apenas apontar para outras questões</b>.Em vez "
"disso, adicione um comentário indicando <i>\"Possível duplicata de...\"</i>."
" No entanto, não há problema em incluir links para outras perguntas ou "
"respostas, fornecendo informações adicionais relevantes."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""
"<b>As respostas não devem apenas fornecer um link para uma solução</b>. Em "
"vez disso, forneça o texto de descrição da solução em sua resposta, mesmo "
"que seja apenas copiar / colar. Links são bem-vindos, mas devem ser "
"complementares à resposta, fontes de referência ou leituras adicionais."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can search questions by their title or tags. It’s also OK to answer your"
" own question."
msgstr ""
"<b>Antes de perguntar - certifique-se de pesquisar uma pergunta "
"semelhante.</b> Você pode pesquisar perguntas por seus títulos ou "
"marcadores. Também não há problema em responder à sua própria pergunta."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Please avoid asking questions that are too subjective and "
"argumentative</b> or not relevant to this community."
msgstr ""
"<b>Evite fazer perguntas muito subjetivas e argumentativas</b> ou não "
"relevantes a nossa comunidade."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on the question or answer, just\n"
"            <b>use the commenting tool.</b> Please remember that you can always <b>revise your answers</b>\n"
"            - no need to answer the same question twice. Also, please <b>don't forget to vote</b>\n"
"            - it really helps to select the best questions and answers!"
msgstr ""
"<b>Por favor, tente dar uma resposta substancial.</b> Se você quiser comentar sobre a pergunta ou resposta, basta\n"
"            <b>usar a ferramenta de comentários.</b> Lembre-se de que você sempre pode <b>revisar suas respostas</b>\n"
"            - não há necessidade de responder à mesma pergunta duas vezes. Tambem por favor <b>não se esqueça de votar</b>\n"
"            - isso realmente ajuda a selecionar as melhores perguntas e respostas!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<b>Tags</b> I Follow"
msgstr "<b>Marcadores</b> que sigo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid ""
"<b>This forum is empty.</b><br/>\n"
"                    Be the first one asking a question"
msgstr ""
"<b>This fórum está vazio.</b><br/>\n"
"                    Seja o primeiro a fazer uma pergunta"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What kinds of questions can I ask here?</b>"
msgstr "<b>Que tipo de perguntas posso fazer aqui?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What should I avoid in my answers?</b>"
msgstr "<b>O que devo evitar em minhas respostas?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What should I avoid in my questions?</b>"
msgstr "<b>O que devo evitar em minhas perguntas?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "<b>Why can other people edit my questions/answers?</b>"
msgstr ""
"<b>Por que outras pessoas podem editar minhas perguntas/respostas?</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<b>You already have a pending post.</b><br/>"
msgstr "<b>Você já tem uma postagem pendente.</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b>[Answer]</b>"
msgstr "<b>[Resposta]</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy badge-gold ml-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold badge\"/>"
msgstr ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy badge-gold ml-2\" role=\"img\" aria-label=\"Distintivo de ouro\" title=\"Distintivo de ouro\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<em class=\"d-block mb-2\">or</em>"
msgstr "<em class=\"d-block mb-2\">ou</em>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\" fa fa-comment text-muted mr-1\"/>Comment"
msgstr "<i class=\" fa fa-comment text-muted mr-1\"/>Comentário"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-bell fa-fw\"/> Followed Posts"
msgstr "<i class=\"fa fa-bell fa-fw\"/> Postagens Seguidas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-bug\"/> Filter Tool"
msgstr "<i class=\"fa fa-bug\"/> Ferramenta de Filtro"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-check fa-fw mr-1\"/>Accept"
msgstr "<i class=\"fa fa-check fa-fw mr-1\"/>Aceitar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-check text-success d-block display-2\"/>\n"
"            <b>You've Completely Caught Up!</b><br/>"
msgstr ""
"<i class=\"fa fa-check text-success d-block display-2\"/>\n"
"            <b>Você Pegou Completamente!</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-check\"/> How to configure TPS and TVQ's canadian taxes?"
msgstr ""
"<i class=\"fa fa-check\"/> Como configurar os impostos canadenses de TPS e "
"TVQ?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-check-square-o fa-fw\"/> To Validate"
msgstr "<i class=\"fa fa-check-square-o fa-fw\"/> Para Validar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<i class=\"fa fa-chevron-left mr-1\"/>Back"
msgstr "<i class=\"fa fa-chevron-left mr-1\"/>Voltar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-chevron-left mr-2\"/>Back to All Topics"
msgstr "<i class=\"fa fa-chevron-left mr-2\"/>Voltar para Todos os Tópicos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_sub_nav
msgid "<i class=\"fa fa-chevron-left small\"/> Back"
msgstr "<i class=\"fa fa-chevron-left small\"/> Voltar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-flag fa-fw\"/> Flagged"
msgstr "<i class=\"fa fa-flag fa-fw\"/> Sinalizado"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-flag ml-4 mr4\"/>\n"
"                                    Flagged"
msgstr ""
"<i class=\"fa fa-flag ml-4 mr4\"/>\n"
"                                    Sinalizado"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-flag\"/> Country"
msgstr "<i class=\"fa fa-flag\"/> País"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Edit<span class=\"d-none d-lg-inline\"> your answer</span>"
msgstr ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Editar<span class=\"d-none d-lg-inline\"> sua resposta</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-question-circle-o fa-fw\"/> My Posts"
msgstr "<i class=\"fa fa-question-circle-o fa-fw\"/> Minhas Postagens"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-reply mr-1\"/>Answer"
msgstr "<i class=\"fa fa-reply mr-1\"/>Resposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-share-alt text-muted mr-1\"/>Share"
msgstr "<i class=\"fa fa-share-alt text-muted mr-1\"/>Compartilhar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-share-alt text-muted\"/>\n"
"                                Share"
msgstr ""
"<i class=\"fa fa-share-alt text-muted\"/>\n"
"                                Compartilhar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-star fa-fw\"/> Favourites"
msgstr "<i class=\"fa fa-star fa-fw\"/> Favoritos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-tags fa-fw\"/> Followed Tags"
msgstr "<i class=\"fa fa-tags fa-fw\"/> Marcadores Favoritos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-down text-danger ml-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down text-danger ml-3\" role=\"img\" aria-"
"label=\"Votos negativos\" title=\"Votos negativos\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Votos "
"positivos\" title=\"Votos positivos\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-times fa-fw mr-1\"/>Reject"
msgstr "<i class=\"fa fa-times fa-fw mr-1\"/>Rejeitar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<i class=\"fa fa-times\"/> Good morning to all! Please, can someone help "
"solve my tax computation problem in Canada? Thanks!"
msgstr ""
"<i class=\"fa fa-times\"/> Bom Dia a todos! Por favor, alguém pode me ajudar"
" a resolver meu problema de cálculo de impostos no Canadá? Obrigado!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-user\"/> User"
msgstr "<i class=\"fa fa-user\"/> Usuário"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<small class=\"font-weight-bold\">Votes</small>"
msgstr "<small class=\"font-weight-bold\">Votos</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid ""
"<small class=\"text-muted\">\n"
"                    Flagged\n"
"                </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                    Sinalizado\n"
"                </small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<span aria-label=\"Close\">×</span>"
msgstr "<span aria-label=\"Close\">×</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span class=\"badge badge-info\">Closed</span>"
msgstr "<span class=\"badge badge-info\">Fechado</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy badge-bronze ml-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"Bronze badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy badge-bronze ml-2\" role=\"img\" aria-"
"label=\"Distintivo bronze\" title=\"Distintivo bronze\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy badge-silver ml-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Silver badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy badge-silver ml-2\" role=\"img\" aria-"
"label=\"Distintivo prata\" title=\"Distintivo prata\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"font-weight-bold\">No answer posted yet.</span>"
msgstr "<span class=\"font-weight-bold\">Nenhuma resposta postada ainda.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"font-weight-bold\">No question posted yet.</span>"
msgstr "<span class=\"font-weight-bold\">Nenhuma pergunta postada ainda.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<span class=\"mx-1 d-none d-sm-inline\">&amp;nbsp;|</span>"
msgstr "<span class=\"mx-1 d-none d-sm-inline\">&amp;nbsp;|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<span class=\"mx-1 text-400 d-none d-lg-block\">|</span>"
msgstr "<span class=\"mx-1 text-400 d-none d-lg-block\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<span class=\"mx-1\">|</span>"
msgstr "<span class=\"mx-1\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ""
"<span class=\"mx-1\">|</span>\n"
"                    <i class=\"fa fa-star\"/>"
msgstr ""
"<span class=\"mx-1\">|</span>\n"
"                    <i class=\"fa fa-star\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<span class=\"mx-3  mx-lg-2 text-400 d-none d-md-inline\">|</span>"
msgstr "<span class=\"mx-3  mx-lg-2 text-400 d-none d-md-inline\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<span class=\"navbar-text mr-1\">Go to:</span>"
msgstr "<span class=\"navbar-text mr-1\">Ir para:</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<span class=\"navbar-text mr-3\">Show Tags Starting By</span>"
msgstr "<span class=\"navbar-text mr-3\">Exibir Marcadores Iniciando Por</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "<span class=\"o_stat_text\">Favorites</span>"
msgstr "<span class=\"o_stat_text\">Favoritos</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "<span class=\"o_stat_text\">Go to <br/>Website</span>"
msgstr "<span class=\"o_stat_text\">Vá para o <br/>Site</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "<span class=\"o_stat_text\">Posts</span>"
msgstr "<span class=\"o_stat_text\">Postagens</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<span class=\"o_wforum_answer_correct_badge border small border-success rounded-pill font-weight-bold text-success ml-2 px-2\">\n"
"                            Best Answer\n"
"                        </span>"
msgstr ""
"<span class=\"o_wforum_answer_correct_badge border small border-success rounded-pill font-weight-bold text-success ml-2 px-2\">\n"
"                            Melhor Resposta\n"
"                        </span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "<span class=\"text-muted mx-3\">or</span>"
msgstr "<span class=\"text-muted mx-3\">ou</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<span>By </span>"
msgstr "<span>Por </span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "A clear, explicit and concise title"
msgstr "Um título claro, conciso e explícito"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr "Uma nova resposta em"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "A new question"
msgstr "Uma nova pergunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "About"
msgstr "Sobre"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr "Aceitar uma resposta sobre suas próprias perguntas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr "Aceite uma resposta para todas as perguntas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Accepted Answer"
msgstr "Resposta Aceita"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accept
msgid "Accepting an answer"
msgstr "Aceitando uma resposta"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Access Denied"
msgstr "Acesso Negado"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction
msgid "Action Needed"
msgstr "Ação Necessária"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__active
#: model:ir.model.fields,field_description:website_forum.field_forum_post__active
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__active
msgid "Active"
msgstr "Ativo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activities"
msgstr "Atividades"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activity"
msgstr "Atividade"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Add to menu"
msgstr "Adicionar no menu"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""
"Após a postagem, será proposto ao usuário compartilhar sua pergunta ou "
"resposta nas redes sociais, possibilitando a divulgação do conteúdo do fórum"
" nas redes sociais."

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#, python-format
msgid "All"
msgstr "Tudo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All Forums"
msgstr "Todos os Fóruns"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "All Tags"
msgstr "Todos os Marcadores"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "All Topics"
msgstr "Todos os Tópicos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All forums"
msgstr "Todos os fóruns"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_bump
msgid "Allow Bump"
msgstr "Permitir colisão"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Amazing! There are no unanswered questions left!"
msgstr "Impressionante! Não há nenhuma pergunta restante!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Answer"
msgstr "Resposta"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
#, python-format
msgid "Answer Edited"
msgstr "Resposta Editada"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accepted
msgid "Answer accepted"
msgstr "Resposta aceita"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr "Resposta aceita com 15 ou mais votos"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr "Resposta rebaixada"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_flagged
msgid "Answer flagged"
msgstr "Resposta sinalizada"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer
msgid "Answer questions"
msgstr "Responder a perguntas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr "Resposta mais votada"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr "Resposta votada até 15 vezes"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr "Resposta votou-se 4 vezes"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr "Resposta condecorada 6 vezes"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr "A resposta foi aceita com 3 ou mais votos"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__child_count_desc
msgid "Answered"
msgstr "Respondida"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answered Posts"
msgstr "Postagens Respondidas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Answered by"
msgstr "Respondida por"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr "Respondeu a própria pergunta com pelo menos 4 votos positivos"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_count
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answers"
msgstr "Respostas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Aparece em"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Archived"
msgstr "Arquivado"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_ask
msgid "Ask questions"
msgstr "Fazer perguntas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_post
msgid "Ask questions without validation"
msgstr "Fazer perguntas sem validação"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr "Fez uma pergunta e aceitou uma resposta"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr "Fez uma pergunta com ao menos 150 visualizações"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr "Fez uma pergunta com ao menos 250 visualizações"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr "Fez uma pergunta com ao menos 500 visualizações"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr "Fez uma pergunta com ao menos uma condecoração"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_date
msgid "Asked on"
msgstr "Perguntada em"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_new
msgid "Asking a question"
msgstr "Fazendo uma pergunta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_attachment_count
msgid "Attachment Count"
msgstr "Contagem de Anexos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Author"
msgstr "Autor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__authorized_group_id
msgid "Authorized Group"
msgstr "Grupo Autorizado"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr ""
"Autobiógrafo\n"
"\n"
" "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Avatar"
msgstr "Avatar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Back"
msgstr "Voltar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Back to Question"
msgstr "Voltar para Pergunta"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Bad Request"
msgstr "Má Solicitação"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_badges
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Badges"
msgstr "Distintivos"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__basic
msgid "Basic"
msgstr "Básico"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Be less specific in your wording for a wider search result"
msgstr ""
"Seja menos específico em suas palavras para um resultado de pesquisa mais "
"amplo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Browse All"
msgstr "Navegar em Tudo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__bump_date
msgid "Bumped on"
msgstr "Promovida em"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "By sharing you answer, you will get additional"
msgstr "Ao compartilhar sua resposta, você receberá"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_accept
msgid "Can Accept"
msgstr "Pode Aceitar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_answer
msgid "Can Answer"
msgstr "Pode Responder"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_ask
msgid "Can Ask"
msgstr "Pode Perguntar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_post
msgid "Can Automatically be Validated"
msgstr "Pode ser Validado Automaticamente"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_close
msgid "Can Close"
msgstr "Pode Fechar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment
msgid "Can Comment"
msgstr "Pode Comentar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment_convert
msgid "Can Convert to Comment"
msgstr "Pode Converter para Comentário"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_downvote
msgid "Can Downvote"
msgstr "Pode rebaixar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_edit
msgid "Can Edit"
msgstr "Pode Editar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_flag
msgid "Can Flag"
msgstr "Pode Sinalizar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_moderate
msgid "Can Moderate"
msgstr "Pode Moderar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_unlink
msgid "Can Unlink"
msgstr "Pode desvincular"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_upvote
msgid "Can Upvote"
msgstr "Pode Condecorar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_view
msgid "Can View"
msgstr "Pode Visualizar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_retag
msgid "Change question tags"
msgstr "Alterar marcadores de pergunta"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_bump
msgid ""
"Check this box to display a popup for posts older than 10 days without any "
"given answer. The popup will offer to share it on social networks. When "
"shared, a question is bumped at the top of the forum."
msgstr ""
"Marque esta caixa para exibir um popup nas postagens com mais de 10 dias sem"
" nenhuma resposta fornecida. O popup oferecerá para compartilhá-la em redes "
"sociais. Quando compartilhada, uma pergunta é promovida ao topo do fórum."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Check your spelling and try again"
msgstr "Verifique a ortografia e tente novamente"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr "Chefe dos comentários"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click here to accept this answer."
msgstr "Clique aqui para aceitar esta resposta."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"Click here to send a verification email allowing you to participate in the "
"forum."
msgstr ""
"Clique aqui para enviar um e-mail de verificação permitindo que você "
"participe do fórum."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to answer."
msgstr "Clique para responder."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your answer."
msgstr "Clique para postar sua resposta."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your question."
msgstr "Clique para postar sua pergunta."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
#, python-format
msgid "Close"
msgstr "Fechar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Close Post"
msgstr "Encerrar Postagem"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_reasons
msgid "Close Reasons"
msgstr "Motivos de encerramento"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_all
msgid "Close all posts"
msgstr "Fechar todas as postagens"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_own
msgid "Close own posts"
msgstr "Encerrar próprias postagens"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Close post"
msgstr "Encerrar postagem"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__close
msgid "Closed"
msgstr "Fechado"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_uid
msgid "Closed by"
msgstr "Fechado por"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_date
msgid "Closed on"
msgstr "Fechado às"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Closing"
msgstr "Encerrando"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__name
msgid "Closing Reason"
msgstr "Razões de Encerramento"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment"
msgstr "Comentário"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_all
msgid "Comment all posts"
msgstr "Comentar todas as postagens"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_own
msgid "Comment own posts"
msgstr "Comentário em próprias postagens"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post..."
msgstr "Comentar esta postagem..."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.challenge.line,name:website_forum.line_chief_commentator
#: model:gamification.challenge.line,name:website_forum.line_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr "Comentador"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comments"
msgstr "Comentários"

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr "Completar própria biografia"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_configure_profile
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
#: model_terms:gamification.badge,description:website_forum.badge_p_1
msgid "Completed own biography"
msgstr "Completou própria biografia"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "Contains offensive or malicious remarks"
msgstr "Contém comentários ofensivos ou maliciosos"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Content"
msgstr "Conteúdo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_all
msgid "Convert all answers to comments and vice versa"
msgstr "Converter todas respostas para comentários e vice versa"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Convert as a answer"
msgstr "Converter como uma resposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Convert as a comment"
msgstr "Converter como um comentário"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_accept
msgid "Convert comment to answer"
msgstr "Converter comentário em resposta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_own
msgid "Convert own answers to comments and vice versa"
msgstr "Converta as suas próprias respostas para comentários e vice-versa"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_correct
msgid "Correct"
msgstr "Revisar"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__is_correct
msgid "Correct answer or answer accepted"
msgstr "Resposta correta ou resposta aceita"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#, python-format
msgid "Create"
msgstr "Criar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_date
#: model:ir.model.fields,field_description:website_forum.field_res_users__create_date
msgid "Create Date"
msgstr "Data de Criação"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.action_forum_post
msgid "Create a new forum post"
msgstr "Crie uma nova postagem no fórum"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Create a new post in this forum by clicking on the button."
msgstr "Crie uma nova postagem neste fórum clicando no botão."

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_tag_action
msgid "Create a new tag"
msgstr "Criar uma nova etiqueta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_tag_create
msgid "Create new tags"
msgstr "Criar novos marcadores"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr "Criou um marcador usado em 15 perguntas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_date
msgid "Created on"
msgstr "Criado em"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr "Pergunta Confiável"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.challenge.line,name:website_forum.line_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr "Crítico"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date"
msgstr "Data"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (high to low)"
msgstr "Data (decrescente)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (low to high)"
msgstr "Data (crescente)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_order
msgid "Default"
msgstr "Padrão"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Default Sort"
msgstr "Ordem padrão"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Define a visibilidade dos desafios através de menus"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Delete"
msgstr "Excluir"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_all
msgid "Delete all posts"
msgstr "Apagar todas as postagens"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_own
msgid "Delete own posts"
msgstr "Excluir as próprias postagens"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Deleted"
msgstr "Excluído"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr "Própria postagem excluída com 3 ou mais votos negativos"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr "Própria postagem excluída com 3 ou mais votos positivos"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__description
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Description"
msgstr "Descrição"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Description visible on website"
msgstr "Descrição visível no site"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.challenge.line,name:website_forum.line_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr "Disciplinado"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Discussions"
msgstr "Discussões"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__discussions
msgid "Discussions (multiple answers)"
msgstr "Discussões (múltiplas respostas)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_user_bio
msgid "Display detailed user biography"
msgstr "Exibir biografia de usuário detalhada"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_downvote
msgid "Downvote"
msgstr "Rebaixar"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "Duplicate post"
msgstr "Duplicar postagem"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Edit"
msgstr "Editar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Answer"
msgstr "Editar Resposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Edit Forum in Backend"
msgstr "Editar Fórum no Backend"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Question"
msgstr "Editar Pergunta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_all
msgid "Edit all posts"
msgstr "Modificar todos posts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_own
msgid "Edit own posts"
msgstr "Editar próprias postagens"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr "Edite sua postagem"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.challenge.line,name:website_forum.line_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr "Editor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_editor
msgid "Editor Features: image and links"
msgstr "Recursos do Editor: imagens e links"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.challenge.line,name:website_forum.line_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr "Esclarecido"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Example\n"
"                        <i class=\"fa fa-question-circle\"/>"
msgstr ""
"Exemplo\n"
"                        <i class=\"fa fa-question-circle\"/>"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr "Pergunta Famosa"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_count
msgid "Favorite"
msgstr "Favorito"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr "Pergunta Favorita"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_ids
msgid "Favourite"
msgstr "Favorito"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_favorite_question_1
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr "Pergunta Favorita (1)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_stellar_question_25
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr "Pergunta Favorita (25)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_favorite_question_5
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr "Pergunta Favorita (5)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Favourite Questions"
msgstr "Perguntas Favoritas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Favourites"
msgstr "Favoritos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Filter by:"
msgstr "Filtrar por:"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_post_vote
msgid "First Relevance Parameter"
msgstr "Parâmetro de Primeira Relevância"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr "Primeiro rebaixar"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr "Primeira edição"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr "Primeiro condecorar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Flag"
msgstr "Sinalizar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_flag
msgid "Flag a post as offensive"
msgstr "Marcar um post como ofensivo"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__flagged
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Flagged"
msgstr "Sinalizada"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__flag_user_id
msgid "Flagged by"
msgstr "Sinalizada por"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Followed Questions"
msgstr "Perguntas Seguidas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Parceiros)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Following"
msgstr "Seguindo"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your input will be upvoted. On the other hand if the answer is misleading - "
"it will be downvoted. Each vote in favor will generate 10 points, each vote "
"against will subtract 10 points. There is a limit of 200 points that can be "
"accumulated for a question or answer per day. The table given at the end "
"explains reputation point requirements for each type of moderation task."
msgstr ""
"Por exemplo, se você fizer uma pergunta interessante ou der uma resposta "
"útil, sua opinião será condecorada. Por outro lado, se a resposta for "
"enganosa, será rebaixada. Cada voto condecorado gerará 10 pontos, cada voto "
"rebaixado subtrairá 10 pontos. Existe um limite de 200 pontos que podem ser "
"acumulados para uma pergunta ou resposta por dia. A tabela fornecida no "
"final explica os requisitos de pontos de reputação para cada tipo de tarefa "
"de moderação."

#. module: website_forum
#: code:addons/website_forum/models/website.py:0
#: code:addons/website_forum/models/website.py:0
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum
#: model:ir.ui.menu,name:website_forum.menu_website_forum_global
#: model:website.menu,name:website_forum.menu_website_forums
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
#, python-format
msgid "Forum"
msgstr "Fórum"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Forum Mode"
msgstr "Modo do Fórum"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__name
#, python-format
msgid "Forum Name"
msgstr "Nome do Fórum"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Forum Post"
msgstr "Mensagem de Fórum"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Forum Posts"
msgstr "Posts do Fórum"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr "Marcador do Fórum"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
msgid "Forums"
msgstr "Fóruns"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_website__forums_count
msgid "Forums Count"
msgstr "Contagem de Fóruns"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Desafio de Gamificação"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Give your post title."
msgstr "Forneça o título da sua postagem."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr "Boa Resposta"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_good_answer
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr "Boa Resposta (6)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr "Boa Pergunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_graph
msgid "Graph of Posts"
msgstr "Gráfico de Postagens"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr "Resposta Genial"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_great_answer
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr "Resposta Genial (15)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr "Pergunta Genial"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Group By"
msgstr "Agrupar Por"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__faq
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Guidelines"
msgstr "orientações"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr "Guru"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_guru
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr "Guru (15)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__uid_has_answered
msgid "Has Answered"
msgstr "Respondeu"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__has_message
msgid "Has Message"
msgstr "Análise de Previsão:"

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr "Ajuda"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "Here a table with the privileges and the karma level"
msgstr "Aqui está uma tabela com os privilégios e o nível de carma"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Hide Intro"
msgstr "Ocultar Introdução"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "I'm <b>Following</b>"
msgstr "Eu estou <b>Seguindo</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "I'm Following"
msgstr "Eu estou Seguindo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__id
msgid "ID"
msgstr "ID"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_unread
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_post__message_unread
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_unread
msgid "If checked, new messages require your attention."
msgstr "Se marcado, novas mensagens solicitarão sua atenção."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se marcado, algumas mensagens tem erro de entrega."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""
"Se o autor não tem karma suficiente, um atributo nofollow é adicionado aos "
"links"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "If this approach is not for you, please respect the community."
msgstr "Se essa abordagem não for para você, respeite a comunidade."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""
"Se você encerrar esta postagem, ela ficará oculta para a maioria dos usuários. Somente\n"
"            usuários com alto carma podem ver as postagens fechadas para moderá-las"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"If you fit in one of these example or if your motivation for asking the "
"question is “I would like to participate in a discussion about ______”, then"
" you should not be asking here but on our mailing lists. However, if your "
"motivation is “I would like others to explain ______ to me”, then you are "
"probably OK."
msgstr ""
"Se você se encaixa em um desses exemplos ou se sua motivação para fazer a "
"pergunta é “Eu gostaria de participar de uma discussão sobre ______”, então "
"você não deveria estar perguntando aqui, mas em nossas listas de discussão. "
"No entanto, se sua motivação for “Gostaria que outros explicassem ______ "
"para mim”, então provavelmente você está bem."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""
"Se você marcar esta postagem como ofensiva, ela ficará oculta para a maioria dos usuários. Somente\n"
"            usuários com alto karma podem ver postagens ofensivas para moderá-las."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1920
msgid "Image"
msgstr "Imagem"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1024
msgid "Image 1024"
msgstr "Imagem 1024"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_128
msgid "Image 128"
msgstr "Image 128"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_256
msgid "Image 256"
msgstr "Imagem 256"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_512
msgid "Image 512"
msgstr "Imagem 512"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "Inappropriate and unacceptable statements"
msgstr "Declarações inadequadas e inaceitáveis"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Insert tags related to your question."
msgstr "Insira marcadores relacionados a sua pergunta."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "Insulting and offensive language"
msgstr "Linguagem insultuosa e ofensiva"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_favourite
msgid "Is Favourite"
msgstr "É Favorita"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_is_follower
msgid "Is Follower"
msgstr "É um seguidor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_validated_answer
msgid "Is answered"
msgstr "Está respondida"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_display_biography
msgid "Is the author's biography visible from his post"
msgstr "A biografia do autor é visível em sua postagem"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to modify someone else's vote."
msgstr "Não é permitido modificar o voto de outra pessoa."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to vote for its own post."
msgstr "Não é permitido votar em sua própria postagem."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Karma"
msgstr "Karma"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Karma Error"
msgstr "Erro de Karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Gains"
msgstr "Karma Ganho"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Related Rights"
msgstr "Direitos Relacionados ao Karma"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_close
msgid "Karma to close"
msgstr "Karma para fechar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment
msgid "Karma to comment"
msgstr "Karma para comentar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr "Karma para converter comentário em resposta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_edit
msgid "Karma to edit"
msgstr "Karma para modificar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_unlink
msgid "Karma to unlink"
msgstr "Karma para desvincular"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_tag____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__last_post_id
msgid "Last Post"
msgstr "Última Postagem"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_options
msgid "Last Post:"
msgstr "Última Postagem:"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__write_date_desc
msgid "Last Updated"
msgstr "Última Atualização"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Last activity date"
msgstr "Data de última atividade"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr "Deixou 10 respostas com pontuação de 10 ou mais"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_main_attachment_id
msgid "Main Attachment"
msgstr "Anexo Principal"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Mark as Best Answer"
msgstr "Marcar como Melhor Resposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as offensive"
msgstr "Marcar como ofensivo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as spam"
msgstr "Marcar como spam"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__menu_id
msgid "Menu"
msgstr "Menu"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error
msgid "Message Delivery error"
msgstr "Erro na entrega da Mensagem"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_ids
msgid "Messages"
msgstr "Mensagens"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__mode
msgid "Mode"
msgstr "Modo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_moderate
msgid "Moderate posts"
msgstr "Moderar postagens"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Moderation"
msgstr "Moderação"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Moderation tools"
msgstr "Ferramentas de moderação"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "More"
msgstr "Mais"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "More over:"
msgstr "Mais sobre:"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__vote_count_desc
msgid "Most Voted"
msgstr "Mais Votado"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most answered"
msgstr "Mais respondido"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most voted"
msgstr "Mais votado"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"Move this question to the top of the list by sharing it on social networks."
msgstr ""
"Mova esta pergunta para o topo da lista compartilhando-a em redes sociais."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Favourites</b>"
msgstr "Meus <b>Favoritas</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Posts</b>"
msgstr "Minhas <b>Postagens</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Favourites"
msgstr "Minhas Favoritas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Posts"
msgstr "Minhas Postagens"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_vote
msgid "My Vote"
msgstr "Meu Voto"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "My profile"
msgstr "Meu Perfil"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__name
msgid "Name"
msgstr "Nome"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Negative vote"
msgstr "Voto negativo"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr "Nova Resposta"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#, python-format
msgid "New Forum"
msgstr "Novo Fórum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "New Post"
msgstr "Nova Postagem"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr "Nova Pergunta"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__create_date_desc
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Newest"
msgstr "Mais Recente"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr "Boa Resposta"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_nice_answer
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr "Boa Resposta (4)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr "Boa Pergunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_activities
msgid "No activities yet!"
msgstr "Nenhuma atividade ainda!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "No favourite questions in this forum (yet).<br/>"
msgstr "Nenhuma pergunta favorita neste fórum (ainda).<br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No flagged posts"
msgstr "Nenhuma postagem sinalizada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "No forum is available yet."
msgstr "Nenhum fórum está disponível ainda."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No post to be validated"
msgstr "Nenhuma postagem para validar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "No tags"
msgstr "Nenhum marcador"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "No vote given by you yet!"
msgstr "Nenhum voto fornecido a você ainda!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_dofollow
msgid "Nofollow links"
msgstr "Links Nofollow"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "Not a real post"
msgstr "Não é uma postagem real"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "Not relevant or out dated"
msgstr "Não é relevante ou é desatualizada"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr "Pergunta Notável"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de Ações"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__posts_count
msgid "Number of Posts"
msgstr "Número de Posts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of errors"
msgstr "Número de Erros"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_flagged_posts
msgid "Number of flagged posts"
msgstr "Número de postagens sinalizadas"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensagens que requer uma ação"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensagens com erro de entrega"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr "Numéro de postagens à espera de validação"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_unread_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_unread_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_unread_counter
msgid "Number of unread messages"
msgstr "Quantidade de mensagens não lidas"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "Off-topic or not relevant"
msgstr "Fora de Tópico ou irrelevante"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__offensive
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__offensive
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Offensive"
msgstr "Ofensivo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Offensive Post"
msgstr "Postagem Ofensiva"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "On average,"
msgstr "Em média,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Only one answer per question is allowed"
msgstr "É permitido apenas uma resposta por pergunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Options"
msgstr "Opções"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Order and Visibility"
msgstr "Ordem e Visibilidade"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Order by"
msgstr "Ordenar por"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Our forums"
msgstr "Nossos fóruns"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.challenge.line,name:website_forum.line_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr "Pressão De Grupo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "People"
msgstr "Pessoas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__plain_content
msgid "Plain Content"
msgstr "Conteúdo Simples"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "Por favor, preencha este campo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Please wait for a moderator to validate your previous post before "
"continuing."
msgstr ""
"Aguarde até que um moderador valide suas postagens anteriores antes de "
"continuar."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr "Pergunta Popular"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_popular_question
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr "Pergunta Popular (150)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_notable_question
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr "Pergunta Popular (250)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_famous_question
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr "Pergunta Popular (500)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Positive vote"
msgstr "Voto positivo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__post_id
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Post"
msgstr "Postar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Post Answer"
msgstr "Postar resposta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_ids
msgid "Post Answers"
msgstr "Respostas de Postagem"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_reasons_action
msgid "Post Close Reasons"
msgstr "Razões de Encerramento de Postagem"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr "Razão para fechar Post"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Post Comment"
msgstr "Comentário de Postagem"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
msgid "Post Vote"
msgstr "Voto de Postagem"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr "Postar pergunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Post:"
msgstr "Postagem:"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr "10 comentários postados"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr "100 comentários postados"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Posting answer on a [Deleted] or [Closed] question is not possible."
msgstr ""
"Não é possível postar a resposta em uma pergunta [Excluída] ou [Encerrada]."

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_posts
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__post_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__post_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_posts
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Posts"
msgstr "Postagens"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_options
msgid "Posts:"
msgstr "Postagens:"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__privacy
#, python-format
msgid "Privacy"
msgstr "Privacidade"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__public
#, python-format
msgid "Public"
msgstr "Público"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__privacy
msgid ""
"Public: Forum is public\n"
"Signed In: Forum is visible for signed in users\n"
"Some users: Forum and their content are hidden for non members of selected group"
msgstr ""
"Público: O Fórum é público\n"
"Em Assinatura: O Fórum é visível para usuários assinantes\n"
"Alguns Usuários: O Fórum e seu conteúdo são ocultos para não-membros de um grupo selecionado"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid ""
"Public: Forum is public\\nSigned In: Forum is visible for signed in "
"users\\nSome users: Forum and their content are hidden for non members of "
"selected group"
msgstr ""
"Público: O Fórum é público\\nEm Assinatura: O Fórum é visível para usuários "
"assinantes\\nAlguns Usuários: O Fórum e seu conteúdo são ocultos para não-"
"membros de um grupo selecionado"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.challenge.line,name:website_forum.line_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr "Pândita"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your answer here."
msgstr "Coloque sua resposta aqui."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your question here."
msgstr "Coloque sua pergunta aqui."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__parent_id
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Question"
msgstr "Pergunta"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
#, python-format
msgid "Question Edited"
msgstr "Pergunta Editada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Question by"
msgstr "Pergunta por"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_downvote
msgid "Question downvoted"
msgstr "Pergunta menos votada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr "Pergunta não encontrado!"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr "Pergunta definida como favorita por 1 usuário"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr "Pergunta definida como favorita por 25 usuários"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr "Pergunta definida como favorita por 5 usuários"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Question should not be empty."
msgstr "A pergunta não pode estar vazia."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_upvote
msgid "Question upvoted"
msgstr "Pergunta mais votada"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr "Pergunta condecorada 15 vezes"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr "Pergunta condecorada 4 vezes"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr "Pergunta condecorada 6 vezes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Questions"
msgstr "Perguntas"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__questions
msgid "Questions (1 answer)"
msgstr "Perguntas (1 resposta)"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Questions and Answers"
msgstr "Perguntas e Respostas"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid ""
"Questions and Answers mode: only one answer allowed\\n Discussions mode: "
"multiple answers allowed"
msgstr ""
"Modo de Perguntas e Respostas: apenas uma resposta é permitida\\n Modo de "
"Discussão: múltiplas respostas são permitidas."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__mode
msgid ""
"Questions mode: only one answer allowed\n"
" Discussions mode: multiple answers allowed"
msgstr ""
"Modo de Perguntas: apenas uma resposta é permitida\n"
" Modo de Discussão: múltiplas respostas são permitidas"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "Racist and hate speech"
msgstr "Racista e discurso de ódio"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_rank_global
msgid "Ranks"
msgstr "Classificações"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Re: %s"
msgstr "Re: %s"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Read the guidelines to know how to gain karma."
msgstr "Leia as diretrizes para saber como ganhar Karma."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Read: #{question.name}"
msgstr "Leu: #{question.name}"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_reason_id
msgid "Reason"
msgstr "Motivo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__reason_type
msgid "Reason Type"
msgstr "Tipo de Razão"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Reason:"
msgstr "Motivo:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_reason_view_list
msgid "Reasons"
msgstr "Motivos"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr ""
"Recebeu pelo menos 3 votos positivos para uma resposta pela primeira vez"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Refuse"
msgstr "Recusar"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Register"
msgstr "Inscrição"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__relevancy
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__relevancy_desc
msgid "Relevance"
msgstr "Relevância"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Relevance Computation"
msgstr "Cálculo de Relevância"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Reopen"
msgstr "Reabrir"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Reply should not be empty."
msgstr "A resposta não deve estar vazia."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__self_reply
msgid "Reply to own question"
msgstr "Responder a própria pergunta"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,help:website_forum.field_forum_post__website_id
msgid "Restrict publishing to this website."
msgstr "Restringir publicação a este site."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "Return to the forum."
msgstr "Retornar ao fórum."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Return to the question list."
msgstr "Retornar a lista de perguntas."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__moderator_id
msgid "Reviewed by"
msgstr "Revisado por"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "Mecanismos de Pesquisa Otimizados"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erro no envio de SMS"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save Changes"
msgstr "Salvar Alterações"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.challenge.line,name:website_forum.line_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr "estudioso"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Search Tips"
msgstr "Dicas de Pesquisa"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Search in Post"
msgstr "Pesquisar na postagem"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Search..."
msgstr "Pesquisar..."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr "Parâmetro de Relevância Secundária"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "See"
msgstr "Ver"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr "Ver postagem"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "See question"
msgstr "Ver pergunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Select All"
msgstr "Selecionar Tudo"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Select Authorized Group"
msgstr "Selecionar Grupo Autorizado"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.challenge.line,name:website_forum.line_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr "Autodidata"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__seo_name
msgid "Seo name"
msgstr "Nome Seo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"Share this content to increase your chances to be featured on the front page"
" and attract more visitors."
msgstr ""
"Compartilhe este conteúdo para aumentar suas chances de aparecer na primeira"
" página e atrair mais visitantes"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_share
msgid "Sharing Options"
msgstr "Opções de Compartilhamento"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Show"
msgstr "Exibir"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Show Tags Starting By"
msgstr "Exibir Marcadores Iniciando Por"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Showing results for"
msgstr "Mostrando resultados para"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Sign in"
msgstr "Entrar"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__connected
#, python-format
msgid "Signed In"
msgstr "Em Expediente"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Solved"
msgstr "Resolvida"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Some Users"
msgstr "Alguns Usuários"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__private
msgid "Some users"
msgstr "Alguns usuários"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged in to perform this action"
msgstr "Desculpe, você deve estar logado para executar esta ação"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to flag a post"
msgstr "Desculpe, você deve estar logado para sinalizar uma postagem"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to vote"
msgstr "Desculpe, você deve estar logado para votar"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, anonymous users cannot choose correct answer."
msgstr "Desculpe, usuários anônimos não podem escolher uma resposta correta."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr "Desculpe, essa pergunta não está mais disponível."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Sorry, we could not find any <b>%s</b> result <b>%s</b> %s%s%s."
msgstr "Não foi possível encontrar resultados <b>%s</b> <b>%s</b> %s%s%s."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, you cannot vote for your own posts"
msgstr "Desculpe, você não pode votar em suas próprias postagens"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Spam all post"
msgstr "Todos as postagens são spam"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "Spam or advertising"
msgstr "Spam ou anúncio"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__state
msgid "Status"
msgstr "Situação"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr "Pergunta Stellar"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr "Estudante"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.challenge.line,name:website_forum.line_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr "Apoiador"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Tag"
msgstr "Marcador"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Esse marcador já existe !"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
#: model:ir.model.fields,field_description:website_forum.field_forum_post__tag_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_tag_global
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Tags"
msgstr "Marcadores"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Tags I Follow"
msgstr "Marcadores que Sigo"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.challenge.line,name:website_forum.line_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr "Taxonomista"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.challenge.line,name:website_forum.line_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr "Professor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__teaser
msgid "Teaser"
msgstr "Prévia"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__bump_date
msgid ""
"Technical field allowing to bump a question. Writing on this field will "
"trigger a write on write_date and therefore bump the post. Directly writing "
"on write_date is currently not supported and this field is a workaround."
msgstr ""
"Campo técnico que permite promover uma questão. Escrever neste campo irá "
"desencadear uma escrita em write_date e, portanto, interromper o post. A "
"gravação direta em write_date não é suportada atualmente e este campo é uma "
"solução alternativa."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "Thanks for posting!"
msgstr "Obrigado por postar!"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"The goal of this site is create a relevant knowledge base that would answer "
"questions related to Odoo."
msgstr ""
"O objetivo deste site é criar uma base de conhecimentos relevantes que "
"respondam a perguntas relacionadas ao Odoo."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The question has been closed"
msgstr "A pergunta foi encerrada"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced"
" users of this site in order to improve the overall quality of the knowledge"
" base content. Such privileges are granted based on user karma level: you "
"will be able to do the same once your karma gets high enough."
msgstr ""
"Portanto, as perguntas e respostas podem ser editadas como páginas wiki por "
"usuários experientes deste site, a fim de melhorar a qualidade geral do "
"conteúdo da base de conhecimento. Esses privilégios são concedidos com base "
"no nível de karma do usuário: você será capaz de fazer o mesmo quando o seu "
"karma ficar alto o suficiente."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers."
msgstr ""
"Esta comunidade é para profissionais e usuários entusiastas, parceiros e "
"programadores."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers. You can ask questions about:"
msgstr ""
"Esta comunidade é para usuários profissionais e entusiastas, parceiros e "
"programadores. Você pode fazer perguntas sobre:"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and services.\n"
"                                        <br>Share and discuss the best content and new marketing ideas, build your professional profile and become a better marketer together."
msgstr ""
"Esta comunidade é para profissionais e entusiastas de nossos produtos e serviços.\n"
"                                        <br>Compartilhe e discuta o melhor conteúdo e novas ideias de marketing, construa seu perfil profissional e se tornem melhores profissionais de marketing juntos."

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""
"Esta comunidade é para profissionais e entusiastas de nossos produtos e "
"serviços. Compartilhe e discuta o melhor conteúdo e novas ideias de "
"marketing, construa seu perfil profissional e juntos tornem-se melhores "
"profissionais de marketing."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""
"Esta fórmula é usada para classificar por relevância. A variável 'votos' "
"representa o número de votos para uma postagem, e 'dias' é o número de dias "
"desde a criação da postagem"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "This forum has been archived."
msgstr "Este fórum foi arquivado."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post can not be flagged"
msgstr "Esta postagem não pode ser marcada"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post is already flagged"
msgstr "Esta postagem já está marcada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"This post is currently awaiting moderation and it's not published yet.<br/>\n"
"                Do you want <b>Accept</b> or <b>Reject</b> this post ?"
msgstr ""
"Esta postagem está aguardando moderação e ainda não foi publicada.<br/>\n"
"                Você deseja <b>Aceitar</b> ou <b>Rejeitar</b> esta postagem?"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "Threatening language"
msgstr "Linguagem ameaçadora"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__name
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title"
msgstr "Título"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr "O título não pode ficar vazio"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Title should not be empty."
msgstr "Informe um título."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__recipient_id
msgid "To"
msgstr "Para"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "To Validate"
msgstr "Para Validar"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking subjective questions where …"
msgstr ""
"Para evitar que sua pergunta seja sinalizada e possivelmente removida, evite"
" fazer perguntas subjetivas onde ..."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Toggle favorite status"
msgstr "Alternar status de favorito"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "Too localized"
msgstr "Muito localizado"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "Too subjective and argumentative"
msgstr "Muito subjetivo e argumentativo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Toolbar with button groups"
msgstr "Barra de ferramentas com grupos de botões"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Topics"
msgstr "Tópicos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Answers"
msgstr "Total de Respostas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Favorites"
msgstr "Total de Favoritos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Views"
msgstr "Total de Visualizações"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_count
msgid "Total Votes"
msgstr "Total de Votos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Trending"
msgstr "Trending"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Try searching for one or two words"
msgstr "Tente pesquisar por uma ou duas palavras"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unanswered"
msgstr "Sem resposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Undelete"
msgstr "Recuperar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_all
msgid "Unlink all comments"
msgstr "Desvincular todos os comentários"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_own
msgid "Unlink own comments"
msgstr "Desvincular os próprios comentários"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Unmark as Best Answer"
msgstr "Desmarcar como Melhor Resposta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_unread
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_unread
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_unread
msgid "Unread Messages"
msgstr "Mensagens não lidas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_unread_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_unread_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contador de mensagens não lidas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unsolved"
msgstr "Não Resolvida"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_uid
msgid "Updated by"
msgstr "Atualizado por"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_date
msgid "Updated on"
msgstr "Atualizada em"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_upvote
msgid "Upvote"
msgstr "Condecorar"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_student
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr "Pergunta mais votada (1)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_great_question
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr "Pergunta mais votada (15)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_nice_question
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr "Pergunta mais votada (4)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_good_question
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr "Pergunta mais votada (6)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Use a clear, explicit and concise title"
msgstr "Use um título claro, conciso e explícito"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__user_id
msgid "User"
msgstr "Usuário"

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
msgid "Users"
msgstr "Usuários"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_favorites
msgid "Users favorite posts"
msgstr "Postagens favoritas dos usuários"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Validate"
msgstr "Validar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr "Validar pergunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "View"
msgstr "Ver"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__views
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Views"
msgstr "Visualizações"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "Violent language"
msgstr "Linguagem violenta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__vote
msgid "Vote"
msgstr "Votar"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_post_vote_vote_uniq
msgid "Vote already exists !"
msgstr "O voto já existe!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Votes"
msgstr "Votos"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__pending
msgid "Waiting Validation"
msgstr "Esperando validação"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Waiting for validation"
msgstr "Esperando pela validação"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__forum_waiting_posts_count
msgid "Waiting post"
msgstr "Esperando postagem"

#. module: website_forum
#: model:ir.model,name:website_forum.model_website
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_id
msgid "Website"
msgstr "Website"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__gamification_challenge__challenge_category__forum
msgid "Website / Forum"
msgstr "Site/fórum"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_message_ids
msgid "Website Messages"
msgstr "Mensagens do site"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_tag__website_message_ids
msgid "Website communication history"
msgstr "Histórico de comunicação do site"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_description
msgid "Website meta description"
msgstr "Meta-descrição do site"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Palavras-chave do Site"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_title
msgid "Website meta title"
msgstr "Meta título site"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imagem de gráfico aberto do site"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__welcome_message
msgid "Welcome Message"
msgstr "Mensagem de Boas Vindas"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Welcome!"
msgstr "Bem vindo!"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some points, which are called \"karma points\". These points serve as a "
"rough measure of the community trust to him/her. Various moderation tasks "
"are gradually assigned to the users based on those points."
msgstr ""
"Quando uma pergunta ou resposta é condecorada, o usuário que as postou "
"ganhará alguns pontos, que são chamados de \"pontos de karma\". Esses pontos"
" servem como uma medida aproximada da confiança da comunidade nele. Várias "
"tarefas de moderação são gradualmente atribuídas aos usuários com base "
"nesses pontos."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You already have a pending post"
msgstr "Você já tem uma postagem pendente"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "You can share your question once it has been validated"
msgstr "Você pode compartilhar sua pergunta uma vez que ela seja validada"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "You cannot create recursive forum posts."
msgstr "Você pode criar postagens de fórum recursivas."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr "Você não pode postar uma resposta vazia"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You have no posts in this forum (yet)."
msgstr "Você não possui postagens neste fórum (ainda)."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "You may now participate in our forums."
msgstr "Você agora pode participar de nossos fóruns."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "You need to have sufficient karma to edit tags"
msgstr "Você precisa ter karma suficiente para editar marcadores"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"You should only ask practical, answerable questions based on actual problems"
" that you face. Chatty, open-ended questions diminish the usefulness of this"
" site and push other questions off the front page."
msgstr ""
"Você só deve fazer perguntas práticas e respondíveis com base nos problemas "
"reais que enfrenta. Perguntas tagarelas e abertas diminuem a utilidade deste"
" site e empurram outras perguntas para fora da página inicial."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You're not following any topic in this forum (yet).<br/>"
msgstr "Você não está seguindo nenhum tópico neste fórum (ainda).<br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr "Sua Resposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Your Reply"
msgstr "Sua Resposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Your favourite"
msgstr "Sua Favorita"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Closed]"
msgstr "[Encerrada]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Deleted]"
msgstr "[Excluída]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Offensive]"
msgstr "[Ofensiva]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "accept any answer"
msgstr "aceitar qualquer resposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "activity date"
msgstr "data da atividade"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "breadcrumb"
msgstr "Detalhes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "by"
msgstr "por"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "close any posts"
msgstr "encerrar qualquer postagem"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any comment"
msgstr "excluir qualquer comentário"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any question or answer"
msgstr "excluir qualquer pergunta ou resposta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete own comment"
msgstr "excluir próprio comentário"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "downvote"
msgstr "rebaixar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "e.g. Help"
msgstr "ex., Ajuda"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "e.g. Who to do this particular thing?"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "edit any post, view offensive flags"
msgstr "editar qualquer postagem, ver sinalizadores ofensivos"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr "cada resposta é igualmente válida: “Qual é a seu ______ favorito?”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "flag offensive, close own questions"
msgstr "sinalizador ofensivo, encerrar as próprias perguntas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr "por motivo:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""
"foi postada e requer sua validação. Clique aqui para acessar a pergunta:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr "foi postada. Clique aqui para acessar a postagem:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr "foi postada. Clique aqui para acessar a pergunta:"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "here"
msgstr "aqui"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""
"como configurar ou personalizar o Odoo para necessidades específicas de "
"negócios,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to develop modules for your own need,"
msgstr "como desenvolver módulos para sua própria necessidade,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to install Odoo on a specific infrastructure,"
msgstr "como instalar o Odoo em uma infraestrutura específica,"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""
"Se sua\n"
"        foi selecionada como a correta. Veja o que você pode fazer com o Karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your favourites"
msgstr "em suas favoritas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your followed list"
msgstr "em sua lista seguida"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your posts"
msgstr "em suas postagens"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "insert text link, upload files"
msgstr "inserir link de texto, carregar arquivos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "instead."
msgstr "."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr ""
"é um discurso retórico disfarçado em uma pergunta: “______ é uma merda, "
"estou certo?”"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "karma points"
msgstr "pontos de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "matching \""
msgstr "combinando ''"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most answered"
msgstr "mais respondida"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most voted"
msgstr "mais votada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "newest"
msgstr "novidade"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr "em"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""
"nas redes sociais obtenha uma resposta dentro\n"
"        de 5 horas. Perguntas compartilhadas em duas redes sociais possuem"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "post"
msgstr "postar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "solved"
msgstr "resolvida"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "specific questions about Odoo service offers, etc."
msgstr "perguntas específicas sobre ofertas de serviços Openerp, etc."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "tag"
msgstr "marcador"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr ""
"não há nenhum problema real a ser resolvido: \"Estou curioso para saber se "
"outras pessoas sentem como eu.\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "to partecipate"
msgstr "participar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "trending"
msgstr "trending"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unanswered"
msgstr "não respondida"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unsolved"
msgstr "não resolvida"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "upvote, add comments"
msgstr "condecorar, adicionar comentários"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "using the"
msgstr "usando o "

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""
"estamos sendo questionados sobre uma questão hipotética em aberto: “E se "
"______ acontecesse?”"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""
"Qual é a melhor maneira de usar Openerp para uma necessidade de negócios "
"específica,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "xp"
msgstr "xp"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""
"sua resposta é fornecida junto com a pergunta, e você espera mais respostas:"
" “Eu uso ______ para ______, o que você usa?”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "your biography can be seen as tooltip"
msgstr "sua biografia pode ser vista como dica"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "| Flagged"
msgstr "Eu Sinalizei"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "圾 Text"
msgstr "圾 Texto"
