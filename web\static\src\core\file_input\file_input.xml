<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.FileInput" owl="1">
        <span class="o_file_input" aria-atomic="true">
            <span class="o_file_input_trigger" t-on-click.prevent="onTriggerClicked">
                <t t-slot="default">
                    <button class="btn btn-primary">Choose File</button>
                </t>
            </span>
            <input type="file" name="ufile" class="o_input_file d-none"
                t-att="{multiple: props.multi_upload, accept: props.accepted_file_extensions}"
                t-ref="file-input"
                t-on-change="onFileInputChange"
            />
        </span>
    </t>

</templates>
