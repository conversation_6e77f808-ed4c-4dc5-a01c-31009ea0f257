<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="OrderReceipt" owl="1">
        <div class="pos-receipt">
            <t t-if="receipt.company.logo">
                <img class="pos-receipt-logo" t-att-src="receipt.company.logo" alt="Logo"/>
                <br/>
            </t>
            <t t-if="!receipt.company.logo">
                <h2 class="pos-receipt-center-align">
                    <t t-esc="receipt.company.name" />
                </h2>
                <br/>
            </t>
            <div class="pos-receipt-contact">
                <t t-if="receipt.company.contact_address">
                    <div><t t-esc="receipt.company.contact_address" /></div>
                </t>
                <t t-if="receipt.company.phone">
                    <div>Tel:<t t-esc="receipt.company.phone" /></div>
                </t>
                <t t-if="receipt.company.vat">
                    <div><t t-esc="receipt.company.vat_label"/>:<t t-esc="receipt.company.vat" /></div>
                </t>
                <t t-if="receipt.company.email">
                    <div><t t-esc="receipt.company.email" /></div>
                </t>
                <t t-if="receipt.company.website">
                    <div><t t-esc="receipt.company.website" /></div>
                </t>
                <t t-if="receipt.header_html">
                    <t t-raw="receipt.header_html" />
                </t>
                <t t-if="!receipt.header_html and receipt.header">
                    <div style="white-space:pre-line"><t t-esc="receipt.header" /></div>
                </t>
                <t t-if="receipt.cashier">
                    <div class="cashier">
                        <div>--------------------------------</div>
                        <div>Served by <t t-esc="receipt.cashier" /></div>
                    </div>
                </t>
            </div>
            <br /><br />

            <!-- Orderlines -->

            <div class="orderlines">
                <t t-call="OrderLinesReceipt"/>
            </div>

            <!-- Subtotal -->

            <t t-if="!isTaxIncluded">
                <div class="pos-receipt-right-align">--------</div>
                <br/>
                <div>Subtotal<span t-esc="env.pos.format_currency(receipt.subtotal)" class="pos-receipt-right-align"/></div>
                <t t-foreach="receipt.tax_details" t-as="tax" t-key="tax.name">
                    <div class="responsive-price">
                        <t t-esc="tax.name" />
                        <span t-esc='env.pos.format_currency_no_symbol(tax.amount)' class="pos-receipt-right-align"/>
                    </div>
                </t>
            </t>

            <!-- Total -->
            <div class="pos-receipt-right-align">--------</div>
            <br/>
            <div class="pos-receipt-amount">
                TOTAL
                <span t-esc="env.pos.format_currency(receipt.total_with_tax)" class="pos-receipt-right-align"/>
            </div>
            <t t-if="receipt.total_rounded != receipt.total_with_tax">
                <div class="pos-receipt-amount">
                  Rounding
                <span t-esc='env.pos.format_currency(receipt.rounding_applied)' class="pos-receipt-right-align"/>
                </div>
                <div class="pos-receipt-amount">
                  To Pay
                 <span t-esc='env.pos.format_currency(receipt.total_rounded)' class="pos-receipt-right-align"/>
              </div>
            </t>
            <br/><br/>

            <!-- Payment Lines -->

            <t t-foreach="receipt.paymentlines" t-as="line" t-key="line.cid">
                <div>
                    <t t-esc="line.name" />
                    <span t-esc="env.pos.format_currency_no_symbol(line.amount)" class="pos-receipt-right-align"/>
                </div>
            </t>
            <br/>

            <div class="pos-receipt-amount receipt-change">
                CHANGE
                <span t-esc="env.pos.format_currency(receipt.change)" class="pos-receipt-right-align"/>
            </div>
            <br/>

            <!-- Extra Payment Info -->

            <t t-if="receipt.total_discount">
                <div>
                    Discounts
                    <span t-esc="env.pos.format_currency(receipt.total_discount)" class="pos-receipt-right-align"/>
                </div>
            </t>
            <t t-if="isTaxIncluded">
                <t t-foreach="receipt.tax_details" t-as="tax" t-key="tax.name">
                    <div>
                        <t t-esc="tax.name" />
                        <span t-esc="env.pos.format_currency_no_symbol(tax.amount)" class="pos-receipt-right-align"/>
                    </div>
                </t>
                <div>
                    Total Taxes
                    <span t-esc="env.pos.format_currency(receipt.total_tax)" class="pos-receipt-right-align"/>
                </div>
            </t>

            <div class="before-footer" />

            <!-- Footer -->
            <div t-if="receipt.footer_html"  class="pos-receipt-center-align">
                <t t-raw="receipt.footer_html" />
            </div>

            <div t-if="!receipt.footer_html and receipt.footer"  class="pos-receipt-center-align" style="white-space:pre-line">
                <br/>
                <t t-esc="receipt.footer" />
                <br/>
                <br/>
            </div>

            <div class="after-footer">
                <t t-foreach="receipt.paymentlines" t-as="line">
                    <t t-if="line.ticket">
                        <br />
                        <div class="pos-payment-terminal-receipt">
                            <t t-raw="line.ticket" />
                        </div>
                    </t>
                </t>
            </div>

            <br/>
            <div class="pos-receipt-order-data">
                <div><t t-esc="receipt.name" /></div>
                <t t-if="receipt.date.localestring">
                    <div><t t-esc="receipt.date.localestring" /></div>
                </t>
                <t t-else="">
                    <div><t t-esc="receipt.date.validation_date" /></div>
                </t>
            </div>
        </div>
    </t>
    <t t-name="OrderLinesReceipt" owl="1">
        <t t-foreach="receipt.orderlines" t-as="line" t-key="line.id">
            <t t-if="isSimple(line)">
                <div class="responsive-price">
                    <t t-esc="line.product_name_wrapped[0]" />
                    <span t-esc="env.pos.format_currency_no_symbol(line.price_display)" class="price_display pos-receipt-right-align"/>
                </div>
                <WrappedProductNameLines line="line" />
            </t>
            <t t-else="">
                <div t-esc="line.product_name_wrapped[0]" />
                <WrappedProductNameLines line="line" />
                <t t-if="line.display_discount_policy == 'without_discount' and line.price != line.price_lst">
                    <div class="pos-receipt-left-padding">
                        <t t-esc="env.pos.format_currency_no_symbol(line.price_lst)" />
                        ->
                        <t t-esc="env.pos.format_currency_no_symbol(line.price)" />
                    </div>
                </t>
                <t t-elif="line.discount !== 0">
                    <div class="pos-receipt-left-padding">
                        <t t-if="env.pos.config.iface_tax_included === 'total'">
                            <t t-esc="env.pos.format_currency_no_symbol(line.price_with_tax_before_discount)"/>
                        </t>
                        <t t-else="">
                            <t t-esc="env.pos.format_currency_no_symbol(line.unitDisplayPriceBeforeDiscount)"/>
                        </t>
                    </div>
                </t>
                <t t-if="line.discount !== 0">
                    <div class="pos-receipt-left-padding">
                        Discount: <t t-esc="line.discount" />%
                    </div>
                </t>
                <div class="pos-receipt-qty-per-price pos-receipt-left-padding">
                    <t t-esc="Math.round(line.quantity * Math.pow(10, env.pos.dp['Product Unit of Measure'])) / Math.pow(10, env.pos.dp['Product Unit of Measure'])"/>
                    <t t-if="!line.is_in_unit" t-esc="line.unit_name" />
                    x
                    <t t-esc="env.pos.format_currency(line.price_display_one)" />
                    <span class="price_display pos-receipt-right-align">
                        <t t-esc="env.pos.format_currency_no_symbol(line.price_display)" />
                    </span>
                </div>
            </t>
            <t t-if="line.customer_note">
                <div class="pos-receipt-left-padding pos-receipt-customer-note">
                    <t t-esc="line.customer_note"/>
                </div>
            </t>
            <t t-if="line.pack_lot_lines">
                <div class="pos-receipt-left-padding">
                    <ul>
                        <t t-foreach="line.pack_lot_lines" t-as="lot" t-key="lot.cid">
                            <li>
                                SN <t t-esc="lot.attributes['lot_name']"/>
                            </li>
                        </t>
                    </ul>
                </div>
            </t>
        </t>
    </t>

</templates>
