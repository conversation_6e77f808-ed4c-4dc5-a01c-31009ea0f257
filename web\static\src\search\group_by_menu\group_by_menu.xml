<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.GroupByMenu" owl="1">
        <Dropdown class="o_group_by_menu btn-group"
            togglerClass="'btn btn-light'"
            t-props="dropdownProps"
            t-on-dropdown-item-selected="onGroupBySelected"
        >
            <t t-set-slot="toggler">
                <i class="small mr-1" t-att-class="icon"/>
                <span class="o_dropdown_title">Group By<t t-if="props.showCaretDown"> <i class="fa fa-caret-down ml-1"/></t></span>
            </t>
            <t t-set="currentGroup" t-value="null"/>
            <t t-foreach="items" t-as="item" t-key="item.id">
                <t t-if="currentGroup !== null and currentGroup !== item.groupNumber">
                    <div class="dropdown-divider" role="separator"/>
                </t>
                <t t-if="item.options">
                    <Dropdown togglerClass="'o_menu_item' + (item.isActive ? ' selected' : '')">
                        <t t-set-slot="toggler">
                            <t t-esc="item.description"/>
                        </t>
                        <t t-set="subGroup" t-value="null"/>
                        <t t-foreach="item.options" t-as="option" t-key="option.id">
                            <t t-if="subGroup !== null and subGroup !== option.groupNumber">
                                <div class="dropdown-divider" role="separator"/>
                            </t>
                            <DropdownItem class="o_item_option"
                                t-att-class="{ selected: option.isActive }"
                                checked="option.isActive ? true : false"
                                payload="{ itemId: item.id, optionId: option.id }"
                                parentClosingMode="'none'"
                                t-esc="option.description"
                            />
                            <t t-set="subGroup" t-value="option.groupNumber"/>
                        </t>
                    </Dropdown>
                </t>
                <t t-else="">
                    <DropdownItem class="o_menu_item"
                        t-att-class="{ selected: item.isActive }"
                        checked="item.isActive"
                        payload="{ itemId: item.id }"
                        parentClosingMode="'none'"
                        t-esc="item.description"
                    />
                </t>
                <t t-set="currentGroup" t-value="item.groupNumber"/>
            </t>
            <t t-if="fields.length">
                <div t-if="items.length" role="separator" class="dropdown-divider"/>
                <CustomGroupByItem fields="fields" t-on-add-custom-group="onAddCustomGroup"/>
            </t>
        </Dropdown>
    </t>

</templates>
