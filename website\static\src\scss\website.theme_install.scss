.o_theme_install_loader_container {
    padding: 7%;
    background-color: rgba($o-shadow-color, .9);
    font-size: 37px;
    font-family: "Montserrat", $font-family-sans-serif;
    pointer-events: all;
    justify-content: space-evenly;
    // above menu bar
    z-index: $zindex-modal - 1;
    .o_tooltip {
        top: auto !important;
        bottom: 11px !important;
        left: 0 !important;
        margin-right: 7px !important;
        padding-left: 33px !important;
    }
    .o_theme_install_loader_tip {
        font-size: 0.5em;
        @include media-breakpoint-down(md) {
            width: 50% !important;
        }
    }
}
.o_theme_install_loader {
    display: inline-block;
    width: 400px;
    height: 220px;
    background-image: url('/website/static/src/img/theme_loader.gif');
    background-size: cover;
    border-radius: 6px;
}
