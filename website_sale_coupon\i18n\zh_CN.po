# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_coupon
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/wizard/sale_coupon_share.py:0
#, python-format
msgid "A coupon is needed for coupon programs."
msgstr "优惠券需要有优惠券方案"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.layout
msgid "Could not apply the promo code:"
msgstr "无法应用此促销代码："

#. module: website_sale_coupon
#: model:ir.model,name:website_sale_coupon.model_coupon_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__coupon_id
msgid "Coupon"
msgstr "优惠券"

#. module: website_sale_coupon
#: model:ir.model,name:website_sale_coupon.model_coupon_program
msgid "Coupon Program"
msgstr "优惠券方案"

#. module: website_sale_coupon
#: model:ir.ui.menu,name:website_sale_coupon.menu_coupon_type_config
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.res_config_settings_view_form
msgid "Coupon Programs"
msgstr "优惠券方案"

#. module: website_sale_coupon
#: model:ir.model,name:website_sale_coupon.model_coupon_share
msgid "Create links that apply a coupon and redirect to a specific page"
msgstr "创建一个优惠券并重定向到特定应用链接"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__create_uid
msgid "Created by"
msgstr "创建人"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__create_date
msgid "Created on"
msgstr "创建时间"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.cart_discount
msgid "Discount:"
msgstr "折扣:"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.cart_discount
msgid "Discounted amount"
msgstr "折扣金额"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.coupon_share_view_form
msgid "Done"
msgstr "完成"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.coupon_share_view_form
msgid "Generate Short Link"
msgstr "生成短链接"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__id
msgid "ID"
msgstr "ID"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.sale_coupon_result
msgid "Invalid or expired promo code."
msgstr "促销代码无效或过期。"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__program_id
msgid "Program"
msgstr "方案"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__program_website_id
msgid "Program Website"
msgstr "方案网站"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__promo_code
msgid "Promo Code"
msgstr "优惠券码"

#. module: website_sale_coupon
#: model:ir.ui.menu,name:website_sale_coupon.menu_promotion_type_config
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.res_config_settings_view_form
msgid "Promotion Programs"
msgstr "促销方案"

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/wizard/sale_coupon_share.py:0
#, python-format
msgid "Provide either a coupon or a program."
msgstr "提供优惠券或方案"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__redirect
msgid "Redirect"
msgstr "重定向"

#. module: website_sale_coupon
#: model:ir.model.fields,help:website_sale_coupon.field_coupon_program__website_id
#: model:ir.model.fields,help:website_sale_coupon.field_coupon_share__program_website_id
msgid "Restrict publishing to this website."
msgstr "限制发布到本网站。"

#. module: website_sale_coupon
#: model:ir.model,name:website_sale_coupon.model_sale_order
msgid "Sales Order"
msgstr "销售订单"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.coupon_view_tree
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.sale_coupon_program_view_tree_website
msgid "Share"
msgstr "分享"

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/wizard/sale_coupon_share.py:0
#: code:addons/website_sale_coupon/wizard/sale_coupon_share.py:0
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.coupon_share_view_form
#, python-format
msgid "Share Coupon"
msgstr "分享优惠券"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__share_link
msgid "Share Link"
msgstr "分享链接"

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/controllers/main.py:0
#, python-format
msgid ""
"The coupon will be automatically applied when you add something in your "
"cart."
msgstr "当您在购物车中添加商品时，将自动使用优惠券。"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.layout
msgid "The following promo code was applied on your order:"
msgstr "以下优惠券已应用于您的订单："

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/models/sale_coupon_program.py:0
#, python-format
msgid "The program code must be unique by website!"
msgstr "方案代码必须是每个网站所独有的！"

#. module: website_sale_coupon
#: code:addons/website_sale_coupon/wizard/sale_coupon_share.py:0
#, python-format
msgid "The shared website should correspond to the website of the program."
msgstr "共享网站应与程序的网站相对应方案。"

#. module: website_sale_coupon
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_program__website_id
#: model:ir.model.fields,field_description:website_sale_coupon.field_coupon_share__website_id
msgid "Website"
msgstr "网站"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.coupon_share_view_form
msgid ""
"You can share this promotion with your customers.\n"
"                            It will be applied at checkout when the customer uses this link."
msgstr ""
"您可以将优惠券分享给您的客户.\n"
"                            当客户使用此链接结账时，将会自动使用此优惠券."

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.sale_coupon_result
msgid "You have successfully applied following promo code:"
msgstr "下面的优惠码已经使用成功："

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.sale_coupon_result
msgid "Your reward"
msgstr "您的报酬"

#. module: website_sale_coupon
#: model_terms:ir.ui.view,arch_db:website_sale_coupon.sale_coupon_result
msgid "is available on a next order with this promo code:"
msgstr "此优惠码在下个订单是否可用"
