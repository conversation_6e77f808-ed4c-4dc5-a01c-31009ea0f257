from odoo import models, fields, api, _
from odoo.exceptions import UserError


class BSSICTechnicalRequest(models.Model):
    """Technical Request Model - Proxy to bssic.request"""
    _name = 'bssic.technical.request'
    _description = 'BSSIC Technical Request'
    _inherit = 'bssic.request'
    _auto = False  # Don't create database table
    _table = 'bssic_request'  # Use the same table as bssic.request

    # Technical request specific fields
    request_nature = fields.Selection([
        ('technical', 'Technical'),
    ], string='Request Type', default='technical', tracking=True)
    technical_category_id = fields.Many2one('bssic.technical.category', string='Category', tracking=True)
    technical_subcategory_id = fields.Many2one('bssic.technical.category', string='Subcategory',
                                             domain="[('parent_id', '=', technical_category_id)]", tracking=True)
    priority = fields.Selection([
        ('0', 'Low'),
        ('1', 'Normal'),
        ('2', 'High'),
        ('3', 'Urgent'),
    ], string='Priority', default='1', tracking=True)

    @api.constrains('technical_category_id', 'state', 'show_technical_fields')
    def _check_required_technical_fields(self):
        """Validate required fields for technical requests"""
        for record in self:
            if record.show_technical_fields and record.state != 'draft':
                if not record.technical_category_id:
                    raise UserError(_('Category is required for Technical requests.'))

    @api.model
    def create(self, vals):
        """Redirect create to bssic.request with technical defaults"""
        # Set request type code for technical
        if not vals.get('request_type_code'):
            vals['request_type_code'] = 'technical'

        # Find and set the technical request type
        if not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'technical')
            ], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        # Set is_technical to True
        vals['is_technical'] = True

        return self.env['bssic.request'].create(vals)

    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):
        """Redirect search to bssic.request with technical filter"""
        request_model = self.env['bssic.request']
        # Add filter for technical requests
        technical_args = args + [('request_type_id.code', '=', 'technical')]
        return request_model.search(technical_args, offset=offset, limit=limit, order=order, count=count)

    def write(self, vals):
        """Redirect write to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).write(vals)

    def unlink(self):
        """Redirect unlink to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).unlink()

    @api.model
    def browse(self, ids):
        """Redirect browse to bssic.request"""
        return self.env['bssic.request'].browse(ids)

    def read(self, fields=None, load='_classic_read'):
        """Redirect read to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).read(fields=fields, load=load)

    @api.model
    def default_get(self, fields_list):
        """Set default values for technical requests"""
        res = super(BSSICTechnicalRequest, self).default_get(fields_list)
        
        # Set default request type for technical
        if 'request_type_code' in fields_list:
            res['request_type_code'] = 'technical'
        
        if 'request_type_id' in fields_list:
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'technical')
            ], limit=1)
            if request_type:
                res['request_type_id'] = request_type.id

        # Set is_technical to True
        if 'is_technical' in fields_list:
            res['is_technical'] = True

        return res
