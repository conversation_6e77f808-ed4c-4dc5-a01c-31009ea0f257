import base64
import pathlib
import time
import traceback
import hashlib
import firebase_admin
import sys
import json
import logging
import threading
from firebase_admin import credentials, firestore, messaging

import odoo
from odoo import models, fields, api, exceptions, _
from odoo.tools.safe_eval import safe_eval
from odoo.exceptions import UserError

ODOO_IMPORT_PATH = '_odoo_import'
apps = {}
listeners = {}
listener_param = 'firebase_listeners'


class OdooFirebase(models.Model):
    _name = 'firebase.account'
    _description = 'Firebase: Accounts'

    name = fields.Char(
        string='Name'
    )
    account_firebase = fields.Char(
        string='Account',
        required=True
    )
    bucket_url = fields.Char(
        string="Bucket Storage"
    )
    file_firebase = fields.Binary(
        string='Firebase Key File'
    )
    auth = fields.Selection(
        string="Sync. Auth",
        selection=[
            ('off', 'Off'),
            ('partner', 'Sync All Partners'),
        ], default="off",
    )
    auth_domain = fields.Text(
        string="Auth Domain",
        default="[]"
    )
    auth_field_user = fields.Many2one(
        string="Field for default user",
        comodel_name="ir.model.fields",
        domain="[('model_id.model','=','res.partner')]"
    )
    auth_field_user_sufix = fields.Char(
        string="Sufix for concatenate with user field"
    )
    auth_field_pass = fields.Many2one(
        string="Field for default pass",
        comodel_name="ir.model.fields",
        domain="[('model_id.model','=','res.partner')]"
    )
    rule_ids = fields.One2many(
        comodel_name='firebase.rule',
        inverse_name='account_id',
        string='Paths to Sync.'
    )
    storage_rule_ids = fields.One2many(
        comodel_name='firebase.storage',
        inverse_name='account_id',
        string='Storage Path to Sync.'
    )
    token = fields.Char(
        string="Token",
        compute="_compute_token",
        readonly=True
    )

    def _compute_token(self):
        for item in self:
            if not item.account_firebase or not item.id:
                item.token = ""
                continue
            item.token = hashlib.sha256(f"{item.account_firebase}+{item.id}".encode("utf-8")).hexdigest()

    def _get_auth_eval_domain(self):
        self.ensure_one()
        return safe_eval(self.auth_domain, {})

    @api.model
    def _get_local_filename(self):
        actual_route = pathlib.Path(__file__).parent.parent.absolute()
        return "{}/credentials/credentials_{}.json".format(actual_route, self.account_firebase)

    def _check_doc_data(self, data):
        if 'res_model' not in data:
            raise UserError("The res_model field is mandatory")
        if data['res_model'] not in self.env:
            raise UserError("The res_model {} does not exist.".format(data['res_model']))
        if 'res_method' not in data:
            raise UserError("The res_method field is mandatory")
        if 'res_params' not in data:
            raise UserError("The res_params field is mandatory")

    def _transform_doc_data(self, data):
        for key, value in data.items():
            if type(value) is list and key in ['domain']:
                new_value = []
                for v in value:
                    new_value.append((v[0], v[1], v[2]))
                if len(new_value) > 0:
                    data[key] = new_value
        return data

    def process_single_event(self, doc, ref):
        ref.update({
            'in_progress': True
        })
        try:
            dict = doc.to_dict()
            update_data = {
                'in_progress': False,
                'finished': True,
            }
            if 'count' in dict:
                update_data['count'] = dict['count'] + 1
            self._check_doc_data(dict)
            args = []
            res_ids = dict.get('res_ids', [])
            if res_ids:
                args.append(res_ids)
            else:
                res_id = dict.get('res_id', [])
                if res_id:
                    args.append([res_id])

            params = self._transform_doc_data(dict['res_params'])
            with odoo.registry(self._cr.dbname).cursor() as cr:
                env = odoo.api.Environment(cr, odoo.SUPERUSER_ID, {})
                model = dict['res_model']
                name = dict['res_method']
                update_data['result'] = api.call_kw(env[model], name, args, params)
                env.cr.commit()
        except Exception as e:
            dict = str(e)
            ref.update({
                'in_progress': False,
                'error': True,
                'error_message': str(e),
                'error_traceback': traceback.format_exc()
            })
        logging.info('End reception')
        logging.info(dict)
        ref.update(update_data)

    def receive_events(self, doc_snapshot, changes, read_time):
        if changes and changes[0].type.name == 'REMOVED':
            return False
        for doc in doc_snapshot:
            logging.info('Begin reception {}'.format(
                str(threading.get_ident()),
            ))
            ref = doc.reference
            self.process_single_event(doc, ref)

    def _get_bucket(self):
        from firebase_admin import storage
        app = self._get_app()
        bucket = storage.bucket(name=self.bucket_url, app=app)
        return bucket

    @api.model
    def cron_check_listener(self):
        for account in self.sudo().search([]):
            try:
                account.single_check_listener()
            except:
                pass

    def single_check_listener(self):
        IrConfigParameter = self.env['ir.config_parameter']
        app = self._get_app()
        db_listeners = json.loads(IrConfigParameter.get_param(listener_param, '{}'))
        db = firestore.client(app)
        u_date, ref = db.collection(ODOO_IMPORT_PATH).add({
            'is_ready': True,
            'finished': False,
            'in_progress': False,
            'error': False,
            'type': 'custom',
            'res_model': 'ir.config_parameter',
            'res_method': 'get_param',
            'res_params': {
                'key': 'web.base.url',
                'default': False
            },
            'count': 0
        })
        time.sleep(5)
        data = ref.get().to_dict()
        db_listeners['last_check'] = str(fields.Datetime.now())
        app_id = str(self.id)
        if 'finished' not in data or not data['finished']:
            db_listeners[app_id] = "BREAK"
            db_listeners['last_update'] = str(fields.Datetime.now())
            IrConfigParameter.set_param(listener_param, json.dumps(db_listeners))
            try:
                listeners[app_id].unsubscribe()
            except:
                pass

            try:
                del listeners[app_id]
            except:
                pass
            self.env.cr.commit()
            self._set_listener()
        elif 'count' in data and data['count'] > 1:
            try:
                logging.info(str(data))
                listeners[app_id].unsubscribe()
            except Exception as e:
                logging.info("Error al unsubscribe")
                logging.info(str(e))
                pass

            try:
                del listeners[app_id]
            except Exception as e:
                logging.info("Error delete")
                logging.info(str(e))
                pass
            self.env.cr.commit()

    def _set_listener(self):
        app_id = str(self.id)
        if app_id in listeners:
            return False
        main_thred = threading.main_thread().ident
        IrConfigParameter = self.env['ir.config_parameter']
        self.env.cr.execute("SELECT id FROM ir_config_parameter WHERE key = '{}' FOR UPDATE;".format(
            listener_param
        ))
        db_listeners = json.loads(IrConfigParameter.get_param(listener_param, '{}'))
        db_listeners['last_check'] = str(fields.Datetime.now())
        if app_id in db_listeners and db_listeners[app_id] == main_thred:
            IrConfigParameter.set_param(listener_param, json.dumps(db_listeners))
            logging.info("Ya hay un listener actual en la DB {}".format(str(db_listeners)))
            return False
        db_listeners['last_update'] = str(fields.Datetime.now())
        db_listeners[app_id] = main_thred
        IrConfigParameter.set_param(listener_param, json.dumps(db_listeners))
        self.env.cr.commit()
        db = firestore.client(apps[self.id])
        collection = db.collection(
            ODOO_IMPORT_PATH
        ).where(
            'is_ready', '==', True
        ).where(
            'finished', '==', False
        ).where(
            'in_progress', '==', False
        ).where(
            'error', '==', False
        )
        listeners[app_id] = collection.on_snapshot(self.receive_events)

    @api.model
    def _get_app(self):
        if self.id in apps:
            try:
                self._set_listener()
            except Exception as e:
                logging.error(str(e))
                pass
            return apps[self.id]
        credential_file = json.loads(base64.b64decode(self.file_firebase).decode('utf-8'))
        cred = credentials.Certificate(credential_file)
        if self.bucket_url:
            apps[self.id] = firebase_admin.initialize_app(cred, {
                'storageBucket': self.bucket_url
            })
        else:
            apps[self.id] = firebase_admin.initialize_app(cred)
        try:
            self._set_listener()
        except Exception as e:
            logging.error(str(e))
            pass
        if self.id in apps:
            return apps[self.id]
        else:
            return False

    @api.model
    def create_firebase_object(self, path, vals):
        try:
            app = self._get_app()
            store = firestore.client(app)
            store.collection(path).document(str(vals['id'])).set(vals)
        except ValueError:
            raise exceptions.UserError(_("""
                   Path has not a valid format.\n
                   Check path can not has any symbol first (like slash /)
               """))

    @api.model
    def update_firebase_object(self, path, vals, force_update=False):
        try:
            logging.info("update_firebase_object 309")
            app = self._get_app()
            logging.info("update_firebase_object 311")
            store = firestore.client(app)
            logging.info("update_firebase_object 313")
            try:
                logging.info("update_firebase_object 315")
                if force_update:
                    logging.info("update_firebase_object 317")
                    store.collection(path).document(str(vals['id'])).update(vals)
                    logging.info("update_firebase_object 319")
                else:
                    logging.info("update_firebase_object 321")
                    store.collection(path).document(str(vals['id'])).set(vals)
                    logging.info("update_firebase_object 323")
            except Exception as e:
                logging.info("update_firebase_object 325: " + str(e))
                ref = store.collection(path).document(str(vals['id']))
                logging.info("update_firebase_object 327")
                for key in vals:
                    logging.info("update_firebase_object 329")
                    val_dict = {}
                    val_dict[key] = vals[key]
                    try:
                        logging.info("update_firebase_object 333")
                        ref.update(val_dict)
                        logging.info("update_firebase_object 335")
                    except Exception as e:
                        logging.info("update_firebase_object 337:  " + str(e))
                        logging.info(sys.getsizeof(vals))
                        logging.info(sys.getsizeof(vals[key]))
                        logging.info(vals)
        except ValueError:
            raise exceptions.UserError(_("""
                   Path has not a valid format.\n
                   Check path can not has any symbol first (like slash /)
               """))

    @api.model
    def delete_firebase_object(self, path, id):
        try:
            app = self._get_app()
            store = firestore.client(app)
            store.collection(path).document(str(id)).delete()
        except ValueError:
            raise exceptions.UserError(_("""
                   Path has not a valid format.\n
                   Check path can not has any symbol first (like slash /)
               """))

    @api.model
    def delete_firebase_collection(self, path):
        try:
            app = self._get_app()
            store = firestore.client(app)
            coll_ref = store.collection(path)
            docs = coll_ref.limit(500).stream()
            deleted = 0

            for doc in docs:
                doc.reference.delete()
                deleted = deleted + 1

        except ValueError as e:
            raise exceptions.UserError(_("""
                   Path has not a valid format.\n
                   Check path can not has any symbol first (like slash /)
               """))

    @api.model
    def cron_import_data(self, reset=False):
        accounts = self.env[self._name].sudo().search([])
        for ac in accounts:
            if reset:
                ac._reset_odoo_import()
            ac._import_data()
        return accounts.ids


    def _import_data(self):
        logging.info("=============Import data==============")
        app = self._get_app()
        collection = ODOO_IMPORT_PATH
        store = firestore.client(app)
        query_ref = store.collection(collection).where(
            'in_progress', '==', False
        ).where(
            'is_ready', '==', True
        ).where(
            'finished', '==', False
        ).limit(500)
        index = 0
        for doc in query_ref.stream():
            logging.info("=============doc============== "+str(index))
            dict = doc.to_dict()
            logging.info(dict)
            if 'type' not in dict or dict['type'] not in ['create', 'write', 'delete']:
                self.process_single_event(doc, store.collection(collection).document(doc.id))
                continue

            store.collection(collection).document(doc.id).update({
                'in_progress': True,
                'finished': False
            })
            update_data = {
                'in_progress': False,
                'finished': False,
            }
            try:
                if dict['type'] == 'create':
                    new_obj = self._import_create(dict)
                    update_data['res_id'] = new_obj.id
                elif dict['type'] == 'write':
                    self._import_write(dict)
                elif dict['type'] == 'delete':
                    self._import_delete(dict)
                store.collection(collection).document(doc.id).update(update_data)
                self.env.cr.commit()
                index += 1
            except Exception as e:
                logging.info("=============ERROR============== " + str(e))
                self.env.cr.rollback()
                store.collection(collection).document(doc.id).update({
                    'in_progress': False,
                    'error': True,
                    'error_message': str(e)
                })

    def _import_create(self, data):
        model_obj = self.env[data['res_model']]
        dict = self._merge_data(data)
        obj = model_obj.sudo().create(dict)
        if 'after_execute' in data and 'method' in data['after_execute']:
            if 'model' in data['after_execute']:
                obj = self.env[data['after_execute']['model']].sudo()
            val = getattr(obj, data['after_execute']['method'])
            if val:
                val()
        return obj

    def _import_write(self, data):
        model_obj = self.env[data['res_model']]
        obj = model_obj.sudo().browse(int(data['res_id']))
        dict = self._merge_data(data)
        if obj:
            obj.write(dict)
            if 'after_execute' in data and 'method' in data['after_execute']:
                if 'model' in data['after_execute']:
                    obj = self.env[data['after_execute']['model']].sudo()
                val = getattr(obj, data['after_execute']['method'])
                if val:
                    val()
        return {}

    def _import_delete(self, data):
        model_obj = self.env[data['res_model']]
        obj = model_obj.sudo().browse(int(data['res_id']))
        if obj:
            obj.unlink()
            if 'after_execute' in data and 'method' in data['after_execute']:
                obj = self.env[data['after_execute']['model']].sudo()
                val = getattr(obj, data['after_execute']['method'])
                if val:
                    val()
        return {}

    def _merge_data(self, data):
        if 'data' in data:
            dict = data['data']
        else:
            dict = {}
        if 'related_data' in data:
            for rel in data['related_data']:
                kfield = rel['field']
                dict[kfield] = []
                if rel['type'] == 'set':
                    dict[kfield].append((6, 0, [int(val) for val in rel['value']]))
                elif rel['type'] == 'add':
                    for val in rel['value']:
                        dict[kfield].append((4, int(val)))
                elif rel['type'] == 'new':
                    for val in rel['value']:
                        dict[kfield].append((0, 0, val))
                elif rel['type'] == 'create':
                    for val in rel['value']:
                        dict[kfield].append((0, 0, val))
                elif rel['type'] == 'hot-create':
                    obj = self.env[rel['res_model']].sudo()
                    item = obj.search([
                        (rel['search_by'], 'like', rel['data'][rel['search_by']])
                    ], limit=1)
                    if not item:
                        item = obj.create(rel['data'])
                    dict[kfield] = item.id
                elif rel['type'] == 'if-exists':
                    obj = self.env[rel['res_model']].sudo()
                    item = obj.search([
                        (rel['search_key'], 'like', rel['search_value'])
                    ], limit=1)
                    if item:
                        dict[kfield] = item.id
        return dict

    def _reset_odoo_import(self):
        app = self._get_app()
        collection = ODOO_IMPORT_PATH
        store = firestore.client(app)
        query_ref = store.collection(collection).where(
            'in_progress', '==', True
        ).where(
            'is_ready', '==', True
        ).where(
            'finished', '==', False
        )
        for doc in query_ref.stream():
            store.collection(collection).document(doc.id).update({
                'in_progress': False
            })

    def messaging_send_notification(self, title, body, token):
        self.ensure_one()
        app = self._get_app()
        message = messaging.Message(
            notification=messaging.Notification(
                title=title,
                body=body
            ),
            token=token,
        )
        return messaging.send(message, app=app)
