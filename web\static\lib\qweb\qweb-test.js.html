<!doctype html>
<html>
<head>
    <script src="/web/static/lib/jquery/jquery.js"></script>
    <link rel="stylesheet" href="/web/static/lib/qunit/qunit.css" type="text/css" media="screen"/>
    <script type="text/javascript" src="/web/static/lib/qunit/qunit.js"></script>

    <script type="text/javascript" src="qweb2.js"></script>

    <script>
        QWeb = new QWeb2.Engine();
        function trim(s) {
            return s.replace(/(^\s+|\s+$)/g, '');
        }
        function render(template, context) {
            return trim(QWeb.render(template, context)).toLowerCase();
        }

        /**
         * Loads the template file, and executes all the test template in a
         * qunit module $title
         */
        function test(title, template) {
            QUnit.module(title, {
                setup: function () {
                    var self = this;
                    this.qweb = new QWeb2.Engine();
                    QUnit.stop();
                    this.qweb.add_template(template, function (_, doc) {
                        self.doc = doc;
                        QUnit.start();
                    })
                }
            });
            QUnit.test('autotest', function (assert) {
                var templates = this.qweb.templates;
                for (var template in templates) {
                    if (!templates.hasOwnProperty(template)) { continue; }
                    // ignore templates whose name starts with _, they're
                    // helpers/internal
                    if (/^_/.test(template)) { continue; }

                    var params = this.doc.querySelector('params#' + template);
                    var args = params ? JSON.parse(params.textContent) : {};

                    var results = this.doc.querySelector('result#' + template);
                    assert.equal(
                        trim(this.qweb.render(template, args)),
                        trim(results.textContent),
                        template);
                }
            });
        }
        $(document).ready(function() {
            test("Output", 'qweb-test-output.xml');
            test("Context-setting", 'qweb-test-set.xml');
            test("Conditionals", 'qweb-test-conditionals.xml');
            test("Attributes manipulation", 'qweb-test-attributes.xml');
            test("Template calling (to the faraway pages)",
                 'qweb-test-call.xml');
            test("Foreach", 'qweb-test-foreach.xml');
            test("Global", 'qweb-test-global.xml');

            test('Template inheritance', 'qweb-test-extend.xml');
        });
    </script>

</head>
<body>
  <div id="qunit"></div>
  <div id="qunit-fixture"></div>
</body>
</html>
