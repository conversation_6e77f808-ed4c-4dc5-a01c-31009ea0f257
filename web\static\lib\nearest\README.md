jQuery Nearest Element plugin
======

**Full documentation is at <http://gilmoreorless.github.io/jquery-nearest/>**

**Demo:** <http://gilmoreorless.github.io/jquery-nearest/demo.html>

Method signatures:

 * `$.nearest({x, y}, selector)` - find `$(selector)` closest to x/y point on screen
 * `$(elem).nearest(selector)` - find `$(selector)` closest to elem
 * `$(elemSet).nearest({x, y})` - filter `$(elemSet)` and return closest to x/y point on screen

Reverse logic:

 * `$.furthest()`
 * `$(elem).furthest()`

Intersecting/touching:

 * `$.touching()`
 * `$(elem).touching()`
