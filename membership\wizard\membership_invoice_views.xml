<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="view_membership_invoice_view" model="ir.ui.view">
            <field name="name">membership.invoice.view.form</field>
            <field name="model">membership.invoice</field>
            <field name="arch" type="xml">
                <form string="Membership Invoice">
                    <group>
                        <field name="product_id" domain="[('membership','=',True)]" options="{'no_open': True, 'no_create': True}"/>
                        <field name="member_price"/>
                    </group>
                    <footer>
                        <button string="Invoice Membership" name="membership_invoice" type="object" class="btn-primary" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z" />
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_membership_invoice_view" model="ir.actions.act_window">
            <field name="name">Join Membership</field>
            <field name="res_model">membership.invoice</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_membership_invoice_view"/>
            <field name="target">new</field>
        </record>

</odoo>
