<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="0">
    <record id="mailing_list_sms_0" model="mailing.list">
        <field name="name">Interested in Tree Promotions</field>
    </record>

    <record id="mailing_contact_0_0" model="mailing.contact">
        <field name="name"><PERSON></field>
        <field name="mobile">+32456001100</field>
        <field name="email"><EMAIL></field>
        <field name="list_ids" eval="[(4, ref('mass_mailing_sms.mailing_list_sms_0'))]"/>
    </record>
    <record id="mailing_contact_0_1" model="mailing.contact">
        <field name="name"><PERSON></field>
        <field name="mobile">+32456001111</field>
        <field name="email"><EMAIL></field>
        <field name="list_ids" eval="[(4, ref('mass_mailing_sms.mailing_list_sms_0'))]"/>
    </record>
    <record id="mailing_contact_0_2" model="mailing.contact">
        <field name="name">Turanga Leela</field>
        <field name="mobile">+32456001122</field>
        <field name="list_ids" eval="[(4, ref('mass_mailing_sms.mailing_list_sms_0'))]"/>
    </record>
    <record id="mailing_contact_0_3" model="mailing.contact">
        <field name="name">John Zoidberg</field>
        <field name="mobile">+32456001133</field>
        <field name="list_ids" eval="[(4, ref('mass_mailing_sms.mailing_list_sms_0'))]"/>
    </record>
    <record id="mailing_contact_0_4" model="mailing.contact">
        <field name="name">Zapp Brannigan</field>
        <field name="mobile">dummy</field>
        <field name="list_ids" eval="[(4, ref('mass_mailing_sms.mailing_list_sms_0'))]"/>
    </record>
</data></odoo>
