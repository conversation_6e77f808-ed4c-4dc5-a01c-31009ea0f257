# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_crm_partner_assign
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON>g <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: Samkhann Seang <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__nbr_opportunities
msgid "# of Opportunity"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<i class=\"fa fa-file-text-o\"/> I'm interested"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<i class=\"fa fa-fw fa-times\"/> I'm not interested"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<i class=\"fa fa-pencil mr-1\"/>Edit"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "<i class=\"fa fa-plus\"/> Create New"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/models/crm_lead.py:182
#, python-format
msgid "<p>I am interested by this lead.</p>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<small class=\"mr-2\"><b>Stage:</b></small>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<small class=\"text-muted\">Opportunity - </small>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid ""
"<span class=\"oe_grey\" attrs=\"{'invisible':[('partner_latitude','&lt;=',0)]}\">N </span>\n"
"                                <span class=\"oe_grey\" attrs=\"{'invisible':[('partner_latitude','&gt;=',0)]}\">S </span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid ""
"<span class=\"oe_grey\" attrs=\"{'invisible':[('partner_longitude','&lt;=',0)]}\">E </span>\n"
"                                <span class=\"oe_grey\" attrs=\"{'invisible':[('partner_longitude','&gt;=',0)]}\">W </span>\n"
"                                <span class=\"oe_grey\">) </span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid ""
"<span class=\"text-danger error_partner_assign_desinterested\" "
"style=\"display:none;\">You need to fill up the next action and contact the "
"customer before accepting the lead</span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid ""
"<span class=\"text-danger error_partner_assign_interested\" "
"style=\"display:none;\">You need to fill up the next action and contact the "
"customer before accepting the lead</span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-3\">Address</strong>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-3\">Customer</strong>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Deadline</strong>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Next Activity</strong>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Priority</strong>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong>Message and communication history</strong>"
msgstr ""

#. module: website_crm_partner_assign
#: model:mail.template,body_html:website_crm_partner_assign.email_template_lead_forward_mail
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your leads</span><br/>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${user.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            Hello,<br/>\n"
"                            We have been contacted by those prospects that are in your region. Thus, the following leads have been assigned to ${ctx['partner_id'].name}:<br/>\n"
"                            <ol>\n"
"                                % for lead in ctx['partner_leads']:\n"
"                                    <li><a href=\"${lead.lead_link}\">${lead.lead_id.name or 'Subject Undefined'}</a>, ${lead.lead_id.partner_name or lead.lead_id.contact_name or 'Contact Name Undefined'}, ${lead.lead_id.country_id and lead.lead_id.country_id.name or 'Country Undefined' }, ${lead.lead_id.email_from or 'Email Undefined'}, ${lead.lead_id.phone or ''} </li><br/>\n"
"                                % endfor\n"
"                            </ol>\n"
"                            % if ctx.get('partner_in_portal'):\n"
"                                Please connect to your <a href=\"${object.get_portal_url()}\">Partner Portal</a> to get details. On each lead are two buttons on the top left corner that you should press after having contacted the lead: \"I'm interested\" &amp; \"I'm not interested\".<br/>\n"
"                            % else:\n"
"                                You do not have yet a portal access to our database. Please contact\n"
"                                ${ctx['partner_id'].user_id and ctx['partner_id'].user_id.email and 'your account manager %s (%s)' % (ctx['partner_id'].user_id.name,ctx['partner_id'].user_id.email) or 'us'}.<br/>\n"
"                            % endif\n"
"                            The lead will be sent to another partner if you do not contact the lead before 20 days.<br/><br/>\n"
"                            Thank you,<br/>\n"
"                            ${ctx['partner_id'].user_id and ctx['partner_id'].user_id.signature | safe or ''}\n"
"                            <br/>\n"
"                            % if not ctx['partner_id'].user_id:\n"
"                                PS: It looks like you do not have an account manager assigned to you, please contact us.\n"
"                            % endif\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    ${user.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    ${user.company_id.phone}\n"
"                    % if user.company_id.phone and (user.company_id.email or user.company_id.website)\n"
"                    |\n"
"                    % endif\n"
"                    % if user.company_id.email\n"
"                    <a href=\"'mailto:%s' % ${user.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${user.company_id.email}</a>\n"
"                    % endif\n"
"                    % if user.company_id.email and user.company_id.website\n"
"                    |\n"
"                    % endif\n"
"                    % if user.company_id.website\n"
"                    <a href=\"'%s' % ${user.company_id.website}\" style=\"text-decoration:none; color: #454748;\">\n"
"                    ${user.company_id.website}\n"
"                    </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__activation
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__activation
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_tree
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_res_partner_filter_assign
msgid "Activation"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:100
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__active
#, python-format
msgid "Active"
msgstr "សកម្ម"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Add an opportunity"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Address"
msgstr "អាសយដ្ឋាន"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:245
#, python-format
msgid "All Categories"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:262
#, python-format
msgid "All Countries"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/models/crm_lead.py:254
#, python-format
msgid "All fields are required !"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.actions.server,name:website_crm_partner_assign.action_assign_salesman_according_assigned_partner
msgid "Assign salesman of assigned partner"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_assigned_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__partner_assigned_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_opportunity_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid "Assigned Partner"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid "Automatic Assignation"
msgstr "កំណត់ដោយស្វ័យប្រវត្តិ"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead_forward_to_partner__body
msgid "Automatically sanitized HTML contents"
msgstr ""

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_bronze
msgid "Bronze"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_partner_report_assign
msgid "CRM Partnership Analysis"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Campaign"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Cancel"
msgstr "លុបចោល"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "City"
msgstr "ក្រុង"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid "Close"
msgstr "បិទ"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Confirm"
msgstr "បញ្ជាក់"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Contact"
msgstr "ទំនាក់ទំនង"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:54
#: code:addons/website_crm_partner_assign/controllers/main.py:111
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#, python-format
msgid "Contact Name"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Contact a reseller"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Contact name"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__body
msgid "Contents"
msgstr "មាតិកា"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Countries..."
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__country_id
msgid "Country"
msgstr "ប្រទេស"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: website_crm_partner_assign
#: model:crm.lead.tag,name:website_crm_partner_assign.tag_portal_lead_own_opp
msgid "Created by Partner"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Current stage of the opportunity"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Customer"
msgstr "អតិថិជន"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Customer Name"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Date"
msgstr "កាលបរិច្ឆេត"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Date Partnership"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Date Review"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Deadline:"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Description"
msgstr "ការពិពណ៌​នា​"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Details Next Activity"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Do you have contacted the customer?"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Edit Contact"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Edit Opportunity"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Email"
msgstr "អុីម៉ែល"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Email Template"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Expected"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Expected Closing"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:112
#, python-format
msgid "Expected Revenue"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Country"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Level"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__partner_id
msgid "Forward Leads To"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__forward_type
msgid "Forward selected leads to"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.action_crm_send_mass_forward
#: model:ir.actions.act_window,name:website_crm_partner_assign.crm_lead_forward_to_partner_act
msgid "Forward to Partner"
msgstr ""

#. module: website_crm_partner_assign
#: model:mail.template,subject:website_crm_partner_assign.email_template_lead_forward_mail
msgid "Fwd: Lead: ${ctx['partner_id'].name}"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_latitude
msgid "Geo Latitude"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_longitude
msgid "Geo Longitude"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid "Geolocation"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner_grade__partner_weight
msgid ""
"Gives the probability to assign a lead to this partner. (0 means no "
"assignation.)"
msgstr ""

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_gold
msgid "Gold"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__grade_id
msgid "Grade"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Group By"
msgstr "ជា​ក្រុម​តាម"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/models/crm_lead.py:192
#, python-format
msgid "I am not interested by this lead. I contacted the lead."
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/models/crm_lead.py:194
#, python-format
msgid "I am not interested by this lead. I have not contacted the lead."
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__id
msgid "ID"
msgstr "ID"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__implemented_partner_ids
msgid "Implementation References"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__implemented_count
msgid "Implemented Count"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__assigned_partner_id
msgid "Implemented by"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date
msgid "Invoice Account Date"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__is_published
msgid "Is published"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation____last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner____last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign____last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation____last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead__date_assign
msgid "Last date this case was forwarded/assigned to a partner"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date_review
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_review
msgid "Latest Partner Review"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_id
msgid "Lead"
msgstr "Lead"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Lead -"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead_assignation
msgid "Lead Assignation"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Lead Feedback"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_location
msgid "Lead Location"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead_forward_to_partner
msgid "Lead forward to partner"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Leads/ឱកាស"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__grade_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
msgid "Level"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__name
msgid "Level Name"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__partner_weight
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__partner_weight
msgid "Level Weight"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_link
msgid "Link to Lead"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Looking For a Local Store?"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:106
#, python-format
msgid "Lost"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Medium"
msgstr "Medium"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Mobile"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_opportunity_partner_filter
msgid "My Assigned Partners"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_menu_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "My Leads"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_menu_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "My Opportunities"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:53
#: code:addons/website_crm_partner_assign/controllers/main.py:110
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__name
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#, python-format
msgid "Name"
msgstr "ឈ្មោះ"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "New Opportunity"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:52
#: code:addons/website_crm_partner_assign/controllers/main.py:109
#, python-format
msgid "Newest"
msgstr "ថ្មីបំផុត"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Next Activity"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Next Activity Date"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_review_next
msgid "Next Partner Review"
msgstr ""

#. module: website_crm_partner_assign
#: model:crm.lead.tag,name:website_crm_partner_assign.tag_portal_lead_partner_unavailable
msgid "No more partner available"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "No result found"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_graph
msgid "Opportunities Assignment Analysis"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.partner
msgid "Our Partners"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:104
#, python-format
msgid "Overdue Activities"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__partner_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.grade_in_detail
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Partner"
msgstr "ដៃគូ"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_activation
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_partner_assign_form
msgid "Partner Activation"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.res_partner_activation_act
#: model:ir.ui.menu,name:website_crm_partner_assign.res_partner_activation_config_mi
msgid "Partner Activations"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__forward_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__assignation_lines
msgid "Partner Assignation"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__date_assign
msgid "Partner Assignation Date"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_grade
msgid "Partner Grade"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.res_partner_grade_action
#: model:ir.ui.menu,name:website_crm_partner_assign.menu_res_partner_grade_action
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_tree
msgid "Partner Level"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__partner_location
msgid "Partner Location"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_partner_assign_form
msgid "Partner Review"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Partner assigned Analysis"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_declined_ids
msgid "Partner not interested"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead__partner_assigned_id
msgid "Partner this case has been forwarded/assigned to."
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Partners"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.action_report_crm_partner_assign
msgid "Partnership Analysis"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date_partnership
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_partnership
msgid "Partnership Date"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.ui.menu,name:website_crm_partner_assign.menu_report_crm_partner_assign_tree
msgid "Partnerships"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Phone"
msgstr "ទូរស័ព្ទ"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Planned Revenue"
msgstr ""

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_platinium
msgid "Platinum"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Priority:"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:113
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
#, python-format
msgid "Probability"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Rating: #{lead.priority} on 3"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: #{opportunity.priority} on 4"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 0 on 3"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 1 on 3"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 2 on 3"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 3 on 3"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.references_block
msgid "References"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.ui.menu,name:website_crm_partner_assign.crm_menu_resellers
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.layout
#: model:website.menu,name:website_crm_partner_assign.menu_resellers
msgid "Resellers"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__team_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Sales Team"
msgstr "ក្រុមលក់"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Salesperson"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Search"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Send"
msgstr "បញ្ជូន"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid "Send Email"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Send Mail"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__grade_sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__sequence
msgid "Sequence"
msgstr "លំដាប់"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:73
#, python-format
msgid "Set an email address for the partner %s"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:71
#, python-format
msgid "Set an email address for the partner(s): %s"
msgstr ""

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_silver
msgid "Silver"
msgstr ""

#. module: website_crm_partner_assign
#: model:crm.lead.tag,name:website_crm_partner_assign.tag_portal_lead_is_spam
msgid "Spam"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:114
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#, python-format
msgid "Stage"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "States..."
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Street"
msgstr "ផ្លូវ"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Street2"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Tags"
msgstr "ធែក"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:61
#, python-format
msgid "The Forward Email Template is not in the database"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner_grade__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "There are no leads."
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "There are no opportunities."
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:102
#, python-format
msgid "This Week Activities"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "This lead is a spam"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner__partner_weight
msgid ""
"This should be a numerical value greater than 0 which will decide the "
"contention for this partner to take this lead/opportunity."
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Title"
msgstr "ចំណងជើង​"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:101
#, python-format
msgid "Today Activities"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__turnover
msgid "Turnover"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__user_id
msgid "User"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__website_url
msgid "Website URL"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "What is the next action? When? What is the expected revenue?"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Why aren't you interested by this lead?"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:105
#, python-format
msgid "Won"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid "World Map"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "YYYY-MM-DD"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "ZIP"
msgstr ""

#. module: website_crm_partner_assign
#: selection:crm.lead.forward.to.partner,forward_type:0
msgid "a single partner: manual selection of partner"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "at"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
msgid "e.g. Gold Partner"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "float-left"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "on"
msgstr "នៅ"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "or"
msgstr "ឬ​"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "reference(s)"
msgstr ""

#. module: website_crm_partner_assign
#: selection:crm.lead.forward.to.partner,forward_type:0
msgid ""
"several partners: automatic assignation, using GPS coordinates and partner's"
" grades"
msgstr ""
