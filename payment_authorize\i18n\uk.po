# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_authorize
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "ABA Routing Number"
msgstr "Число округлень ABA"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_client_key
msgid "API Client Key"
msgstr "Клієнтський ключ API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_login
msgid "API Login ID"
msgstr "ID входу API "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_signature_key
msgid "API Signature Key"
msgstr "Ключ підпису API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_transaction_key
msgid "API Transaction Key"
msgstr "Ключ транзакції API"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Account Number"
msgstr "Номер рахунку"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_payment_method_type
msgid "Allow Payments From"
msgstr "Дозволити платежі з"

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "An error occurred when displayed this payment form."
msgstr "Виникла помилка під час відображення цієї платіжної форми."

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_currency_id
msgid "Authorize Currency"
msgstr "Валюта Authorize"

#. module: payment_authorize
#: model:account.payment.method,name:payment_authorize.payment_method_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__provider__authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_payment_method_type
msgid "Authorize.Net Payment Type"
msgstr "Тип платежу Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.Net Profile ID"
msgstr "ID профілю Authorize.Net"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "Bank (powered by Authorize)"
msgstr "Банк (від Authorize)"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__authorize_payment_method_type__bank_account
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_token__authorize_payment_method_type__bank_account
msgid "Bank Account (USA Only)"
msgstr "Банківський рахунок (Лише США)"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Account Type"
msgstr "Тип банківського рахунку"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Name"
msgstr "Назва банку"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Business Checking"
msgstr "Перевірка бізнесу"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Code"
msgstr "Код картки"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Number"
msgstr "Номер картки"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"Could not fetch merchant details:\n"
"%s"
msgstr ""
"Не вдалося отримати відомості про продавця:\n"
"%s"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__authorize_payment_method_type__credit_card
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_token__authorize_payment_method_type__credit_card
msgid "Credit Card"
msgstr "Кредитна картка"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "Credit Card (powered by Authorize)"
msgstr "Кредитна картка (Authorize)"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Currency"
msgstr "Валюта"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_payment_method_type
msgid "Determines with what payment method the customer can pay."
msgstr "Визначає, яким способом оплати клієнт може оплатити."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Expiration"
msgstr "Термін дії"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"Failed to authenticate.\n"
"%s"
msgstr ""
"Помилка автентифікації.\n"
"%s"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Generate Client Key"
msgstr "Створити клієнтський ключ"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "How to get paid with Authorize.Net"
msgstr "Як отримати оплату за допомогою Authorize.Net"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "MM"
msgstr "MM"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Name On Account"
msgstr "Ім'я на рахунку"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Не знайдено жодної транзакції, що відповідає референсу %s."

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Платіжний еквайєр"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_account_payment_method
msgid "Payment Methods"
msgstr "Способи оплати"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr "Токен оплати"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "Платіжна операція"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Personal Checking"
msgstr "Персональна перевірка"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Personal Savings"
msgstr "Персональні збереження"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__provider
msgid "Provider"
msgstr "Провайдер"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "Received data with status code \"%(status)s\" and error code \"%(error)s\""
msgstr "Отримані дані з кодом статусу \"%(status)s\" та кодом помилки \"%(error)s\""

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Received tampered payment request data."
msgstr "Отримано підроблені дані запиту на оплату."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_token.py:0
#, python-format
msgid "Saved payment methods cannot be restored once they have been deleted."
msgstr "Збережені способи оплати неможливо відновити після їх видалення."

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Помилка сервера"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Set Account Currency"
msgstr "Встановіть валюту рахунку"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_login
msgid "The ID solely used to identify the account with Authorize.Net"
msgstr ""
"ID, який використовується виключно для ідентифікації облікового запису з "
"Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "Постачальник платіжних послуг для використання з цим еквайєром"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_client_key
msgid ""
"The public client key. To generate directly from Odoo or from Authorize.Net "
"backend."
msgstr ""
"Публічний ключ клієнта. Щоби створити прямо з Odoo або з бекенду "
"Authorize.Net."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "Транзакція не зв'язана з токеном."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_payment_method_type
msgid "The type of payment method this token is linked to."
msgstr "Тип способу оплати, з яким пов’язано цей токен."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"The unique reference for the partner/token combination in the Authorize.net "
"backend."
msgstr ""
"Унікальне референс комбінації партнера/токена у серверній частині "
"Authorize.net."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"There are active tokens linked to this acquirer. To change the payment "
"method type, please disable the acquirer and duplicate it. Then, change the "
"payment method type on the duplicated acquirer."
msgstr ""
"Існують активні токени, пов’язані з цим еквайєром. Щоб змінити тип способу "
"оплати, будь ласка, вимкніть еквайєр і скопіюйте його. Потім змініть тип "
"методу оплати для дубльованого еквайєра."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "This action cannot be performed while the acquirer is disabled."
msgstr "Цю дію неможливо виконати, якщо еквайєр вимкнено."

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr "Ми не можемо обробити цей платіж."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "YY"
msgstr "рр"
