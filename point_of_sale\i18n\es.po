# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* point_of_sale
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# Pruebas, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# Wil Odoo, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-05 14:42+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid " REFUND"
msgstr "REEMBOLSO"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "%(pos_name)s (not used)"
msgstr "%(pos_name)s (no usado)"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s POS payment of %s in %s"
msgstr "%s Pago de TPV %s en %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"%s has a total amount of %s, are you sure you want to delete this order ?"
msgstr ""
"%s tiene una cantidad total de %s, ¿está seguro de que desea eliminar este "
"pedido?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "& invoice"
msgstr "y factura"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "(Both will be sent by email)"
msgstr "(Ambos se enviarán por correo electrónico)"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "(RESCUE FOR %(session)s)"
msgstr "(RESCATE DE %(session)s)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "(as of opening)"
msgstr "(desde la apertura)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "(update)"
msgstr "(actualizar)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid ". Please contact your manager to accept the closing difference."
msgstr ". Contacte a su gerente para que acepte la diferencia en el cierre."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to manage tax-included prices"
msgstr ""
"<i class=\"fa fa-fw fa-arrow-right\"/>Cómo gestionar precios con impuestos "
"incluidos"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_pos_kanban
msgid ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "<p>Dear %s,<br/>Here is your electronic ticket for the %s. </p>"
msgstr "<p>Estimado %s,<br/> Aquí tiene su recibo electrónico para %s. </p>"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#, python-format
msgid ""
"<p>Ready to have a look at the <b>POS Interface</b>? Let's start our first "
"session.</p>"
msgstr ""
"<p> ¿Listo para echar un vistazo a la <b>interfaz TPV</b>? Comencemos "
"nuestra primera sesión. </p>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid ""
"<span attrs=\"{'invisible': [('is_total_cost_computed','=', "
"True)]}\">TBD</span>"
msgstr ""
"<span attrs=\"{'invisible': [('is_total_cost_computed','=', True)]}\">Por "
"definir</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Los valores establecidos aquí"
" son específicos de la empresa.\" aria-label=\"Los valores establecidos aquí"
" son específicos de la empresa.\" groups=\"base.group_multi_company\" "
"role=\"img\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Authorized Employees</span>"
msgstr "<span class=\"o_form_label\">Empleados Autorizados</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Barcodes</span>"
msgstr "<span class=\"o_form_label\">Códigos de Barra</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Default Pricelist</span>"
msgstr "<span class=\"o_form_label\">Tarifa por defecto</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Journal Entries</span>"
msgstr "<span>Apuntes Contables</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Order Reference</span>"
msgstr "<span class=\"o_form_label\">Refencia de Orden</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Payment Methods</span>"
msgstr "<span class=\"o_form_label\">Métodos de Pago</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr "<span class=\"oe_inline\"><b/></span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Cash Balance</span>"
msgstr "<span>Último balance de cierre de efectivo</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Date</span>"
msgstr "<span>Última Fecha de Cierre</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Informes</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>Ver</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid ""
"<strong> &gt; Payment Terminals</strong>\n"
"                                    in order to install a Payment Terminal and make a fully integrated payment method."
msgstr ""
"<strong>Terminales de pago</strong>\n"
"                                    con el fin de| instalar una terminal de pago y crear un método de pago totalmente integrado."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "? Dando click en \"Confirmar\" validara el pago."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "A Customer Name Is Required"
msgstr "Se requiere el nombre de cliente"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data."
msgstr ""
"Un identificador único global para esta configuración del TPV, usado para "
"prevenir conflictos en datos generados por el cliente."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr ""
"Un número de secuencia que se incrementa cada vez que un usuario reanuda la "
"sesión de TPV"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr "Un número de secuencia que se incrementa con cada pedido"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which you sell "
"through the Point of Sale."
msgstr ""
"Una sesión es un periodo de tiempo, usualmente un día, durante el cual "
"vendes a través del Punto de Venta."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"A session is currently opened for this PoS. Some settings can only be "
"changed after the session is closed."
msgstr ""
"Actualmente hay una sesión abierta para este TPV. Algunas configuraciones "
"solo se pueden cambiar después de que se cierra la sesión."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr "Un número de secuencia único por sesión para el pedido"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr ""
"Un texto corto que se insertará como pie de página en el recibo impreso."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr ""
"Un texto corto que se insertará como encabezado de página en el recibo "
"impreso."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "AMOUNT"
msgstr "IMPORTE"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Accept customer tips or convert their change to a tip"
msgstr "Aceptar propinas de los clientes o convertir su cambio en una propina"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Accept payments difference and post a profit/loss journal entry"
msgstr ""
"Aceptar diferencias de pago y publicar un asiento en el diario de ganancias "
"y pérdidas "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Six payment terminal"
msgstr "Acepte pagos con una terminal de pago Six"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Vantiv payment terminal"
msgstr "Aceptar pagos con un terminal de pago Vantiv"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with an Adyen payment terminal"
msgstr "Aceptar pagos con una terminal de pago Adyen"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Account"
msgstr "Cuenta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "Redondeo para cuenta de efectivo"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plantilla de Plan de Cuentas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__account_move_id
msgid "Account Move"
msgstr "Movimiento de cuenta"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__bank_payment_ids
msgid "Account payments representing aggregated and bank split payments."
msgstr ""
"Pagos de cuenta que representan pagos agregados y divididos por banco."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting"
msgstr "Contabilidad"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr "Diario contable utilizado para crear facturas."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
msgid ""
"Accounting journal used to post POS session journal entries and POS invoice "
"payments."
msgstr ""
"Diario contable utilizado para publicar los asientos de sesiones y pagos de "
"TPV."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__active
msgid "Active"
msgstr "Activo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad  de Excepción"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícono de tipo de actvidad"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#, python-format
msgid "Add"
msgstr "Añadir"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.js:0
#, python-format
msgid "Add Customer Note"
msgstr "Agregar nota de cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Add Tip"
msgstr "Añadir Propina"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Add a custom message to header and footer"
msgstr "Añadir un mensaje personalizado en el encabezado y pie de página"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Add a customer"
msgstr "Añada el cliente"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid "Add a new payment method"
msgstr "Añadir un nuevo método de pago"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Add notes on order lines to be printed on receipt and invoice"
msgstr ""
"Agregue notas en las líneas de la orden para imprimirlas en el recibo y la "
"factura"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Address"
msgstr "Dirección"

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Administrator"
msgstr "Administrador"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
msgid "Advanced Cash Control"
msgstr "Control de efectivo avanzado"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Advanced Pricelists"
msgstr "Listas de precios avanzadas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adyen"
msgstr "Adyen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr "Terminal de pago Adyen"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "All active orders"
msgstr "Todas las órdenes activas"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All available pricelists must be in the same currency as the company or as "
"the Sales Journal set on this point of sale if you use the Accounting "
"application."
msgstr ""
"Las listas de todos los precios disponibles tienen que estar en la misma "
"moneda de la empresa o del diario de ventas indicado en este punto de venta "
"para utilizar la aplicación de contabilidad."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All payment methods must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"Todos los métodos de pago tienen que estar en la misma moneda del diario de "
"ventas o de la empresa, si esta no se ha establecido."

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "Todas las líneas de venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow discounts per line"
msgstr "Permitir descuentos por línea"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow global discounts on orders"
msgstr "Permitir descuentos globales en los pedidos"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_coupon
msgid "Allow the use of coupon and promotion programs in PoS."
msgstr "Permitir el uso de programas de cupones y promociones en PdV."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_gift_card
msgid "Allow the use of gift card"
msgstr "Permitir el uso de tarjetas de regalo"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_orderline_customer_notes
msgid ""
"Allow to write notes for customer on Orderlines. This will be shown in the "
"receipt."
msgstr ""
"Permitir escribir notas para los clientes en las líneas de órdenes. Esto se "
"mostrará en el recibo."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__allowed_pricelist_ids
msgid "Allowed Pricelists"
msgstr "Lista de precios permitidos"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__is_total_cost_computed
msgid ""
"Allows to know if all the total cost of the order lines have already been "
"computed"
msgstr ""
"Permite saber si ya se calculó el costo total de las líneas de la orden"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Allows to know if the total cost has already been computed or not"
msgstr "Permite saber si ya se calculó el costo total"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__amount
msgid "Amount"
msgstr "Importe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "Importe de la diferencia permitida"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__amount_to_balance
msgid "Amount to balance"
msgstr "Importe por balancear"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "Importe total"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid ""
"An error has occurred when trying to close the session.\n"
"You will be redirected to the back-end to manually close the session."
msgstr ""
"Ocurrió un error al tratar de cerrar la sesión\n"
"Será redireccionado al backend para cerrar la sesión de forma manual."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"An error occurred when loading product prices. Make sure all pricelists are "
"available in the POS."
msgstr ""
"Ocurrió un error cargando los precios de los productos. Asegúrese de que "
"todas las tarifas estén disponibles en la configuración del TPV."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "Identificación interna del punto de venta."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Another session is already opened for this point of sale."
msgstr "Otra sesión ya está abierta para este punto de venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Archived"
msgstr "Archivado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr "¿Está seguro que el cliente quiere pagar?"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__direct
msgid "As soon as possible"
msgstr "Lo antes posible"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__closing
msgid "At the session closing (recommended)"
msgstr "Al cierre de la sesión (recomendado)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__update_stock_quantities
msgid ""
"At the session closing: A picking is created for the entire session when it's closed\n"
" In real time: Each order sent to the server create its own picking"
msgstr ""
"Al cierre de la sesión: se crea un picking para toda la sesión cuando está cerrada\n"
"En tiempo real: cada pedido enviado al servidor crea su propio picking"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_attachment_count
msgid "Attachment Count"
msgstr "Nº de archivos adjuntos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
#, python-format
msgid "Attributes"
msgstr "Atributos"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Authorized Difference"
msgstr "Diferencia autorizada"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr ""
"Sesión auto-generada para órdenes huérfanas, ignoradas en las restricciones"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "Impresión automática del recibo"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr "Abrir automáticamente la caja registradora."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_available_categ_ids
msgid "Available PoS Product Categories"
msgstr "Categorías de productos TPV disponibles"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Available Pricelists"
msgstr "Listas de Precio Disponibles"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr "Disponible en TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "Precio medio"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ControlButtonPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Back"
msgstr "Volver"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/NumberPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenNumpad.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Backspace"
msgstr "Retroceso"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__bank
#, python-format
msgid "Bank"
msgstr "Banco"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__bank_payment_ids
msgid "Bank Payments"
msgstr "Pagos bancarios"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement
msgid "Bank Statement"
msgstr "Extracto bancario"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Barcode"
msgstr "Código de barras"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Barcode Nomenclature"
msgstr "Nomenclatura de código de barras"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr "Regla de código de barras"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Barcode Scanner"
msgstr "Lector de códigos de barras"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Barcode Scanner/Card Reader"
msgstr "Lector de código de barras / lector de tarjetas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Base Amount"
msgstr "Importe base"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Before Closing Difference"
msgstr "Antes de cerrar la diferencia"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "Bills"
msgstr "Facturas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Bills &amp; Receipts"
msgstr "Facturas y recibos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Buffer:"
msgstr "Buffer:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__limited_partners_loading
msgid ""
"By default, 100 partners are loaded.\n"
"When the session is open, we keep on loading all remaining partners in the background.\n"
"In the meantime, you can use the 'Load Customers' button to load partners from database."
msgstr ""
"Se cargan 100 partners de forma predeterminada.\n"
"Al abrir la sesión, seguimos cargado los partners restantes en segundo plano.\n"
"Mientras tanto, puede utilizar el botón de \"Cargar clientes\" para cargar partners desde la base de datos."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr ""
"Derivar la impresión del navegador e imprimir a través del proxy del "
"hardware."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "CASH"
msgstr "EFECTIVO"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "CHANGE"
msgstr "CAMBIO"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "CLOSING CONTROL"
msgstr "CONTROL DE CIERRE"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Can't change customer"
msgstr "No pude cambiar el cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Can't mix order with refund products with new products."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OrderImportPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Cancel Payment Request"
msgstr "Cancelar solicitud de pago"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__cancel
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Cannot Invoice"
msgstr "No puede facturar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ProductInfoPopup.js:0
#, python-format
msgid "Cannot access product information screen if offline."
msgstr ""
"No puede acceder a la pantalla de información de producto si no está en "
"línea."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Cannot close the session when offline."
msgstr "No se puede cerrar la sesión sin conexión."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Cannot invoice order from closed session."
msgstr "No se puede facturar el pedido de la sesión cerrada."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Cannot modify a tip"
msgstr "No puede modificar una propina"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr "No se puede devolver cambio sin un método de pago de caja"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__cardholder_name
#, python-format
msgid "Cardholder Name"
msgstr "Nombre del Titular de la Tarjeta"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__is_cash_count
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__cash
#, python-format
msgid "Cash"
msgstr "Efectivo"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_cash_box_out
msgid "Cash Box Out"
msgstr "Salida de caja"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Cash In"
msgstr "Entrada de efectivo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/CashMoveButton.xml:0
#, python-format
msgid "Cash In/Out"
msgstr "Entrada / salida de efectivo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr "Diario de caja"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Cash Out"
msgstr "Salida de efectivo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Cash Register"
msgstr "Registro de caja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_rounding
msgid "Cash Rounding"
msgstr "Redondeo de efectivo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "Redondeos de Efectivo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_ids
msgid "Cash Statements"
msgstr "Extractos de caja"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "Cash in/out of %s is ignored."
msgstr "Se ignora la entrada/salida de efectivo de %s "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash register for %s"
msgstr "Caja registradora para %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__rounding_method
msgid "Cash rounding"
msgstr "Redondeo de efectivo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.account_cashbox_line_view_tree
msgid "Cashbox balance"
msgstr "Saldo de caja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Cashdrawer"
msgstr "Cajón de monedas"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__cashier
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Cashier"
msgstr "Cajero"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"Las categorías son usadas para buscar productos a travez \n"
"de una interfaz táctil."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/CategoryButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
#, python-format
msgid "Category"
msgstr "Categoría"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
msgid "Category Name"
msgstr "Nombre de categoría"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Category Pictures"
msgstr "Imágenes de la categoría"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_id
msgid "Category used in the Point of Sale."
msgstr "Categoría utilizada en el Punto de Venta."

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr "Sillas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Change"
msgstr "Cambio"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Change Customer"
msgstr "Cambiar cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Change Tip"
msgstr "Cambiar Propina"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr ""
"Marque si el producto debe ser pesado usando la integración hardware de la "
"balanza"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr "Marque si quiere que este producto aparezca en el Punto de Venta"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid ""
"Check if you want to group products of this category in point of sale orders"
msgstr ""
"Indique si desea agrupar los productos de esta categoría en los pedidos del "
"punto de venta"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr "Revisar la cantidad en caja al inicio y al cierre."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Check the internet connection then try again."
msgstr "Compruebe la conexión a internet y vuelva a intentarlo."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"Check the internet connection then try to sync again by clicking on the red "
"wifi button (upper right of the screen)."
msgstr ""
"Verifique la conexión a Internet y luego intente sincronizar nuevamente "
"haciendo clic en el botón rojo de wifi (parte superior derecha de la "
"pantalla)."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_id
msgid "Children Categories"
msgstr "Categorías hijas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Choose a specific fiscal position at the order depending on the kind of "
"customer (tax exempt, onsite vs. takeaway, etc.)."
msgstr ""
"Elija una posición fiscal específica en el pedido en función del tipo de "
"cliente (exento de impuestos, \"en el lugar\" vs. \"para llevar\", etc.)."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Choose among fiscal positions when processing an order"
msgstr "Elija entre las posiciones fiscales al procesar un pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "City"
msgstr "Ciudad"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Click here to close the session"
msgstr "Haga clic aquí para cerrar la sesión"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__client
msgid "Client"
msgstr "Cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Connected"
msgstr "Pantalla del Cliente Conectada"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Disconnected"
msgstr "Pantalla del Cliente Desconectada"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Client Screen Unsupported. Please upgrade the IoT Box"
msgstr "Pantalla del Cliente No Admitida. Favor de Actualizar el IoT Box"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Warning"
msgstr "Advertencia de Pantalla del Cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/HeaderButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Close Session"
msgstr "Cerrar sesión"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Close Session & Post Entries"
msgstr "Cerrar sesión y publicar asientos"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_close_session_wizard
msgid "Close Session Wizard"
msgstr "Asistente para cerrar sesión"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closed
msgid "Closed & Posted"
msgstr "Cerrado y contabilizado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Closing ..."
msgstr "Cierre ..."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closing_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Closing Control"
msgstr "Control de cierre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Closing Date"
msgstr "Fecha de cierre"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Closing difference in %s (%s)"
msgstr "Diferencia de cierre en %s (%s)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Closing session error"
msgstr "Error al cerrar sesión"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__value
msgid "Coin/Bill Value"
msgstr "Valor de monedas y billetes"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_bill
#: model:ir.model,name:point_of_sale.model_pos_bill
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_bill_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_bill
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Coins/Bills"
msgstr "Monedas/billetes"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Combine %s POS payments from %s"
msgstr "Pagos combinados de TPV de %s desde %s"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "Compañía"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_has_template
msgid "Company has chart of accounts"
msgstr "La compañía tiene un plan contable"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de configuración"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "Configuración"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Configuration for journal entries of PoS orders"
msgstr ""
"Configuración de las entradas del diario para los pedidos del punto de venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Configurations &gt; Settings"
msgstr "Configuración y ajustes"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Configure at least one Point of Sale."
msgstr "Configure al menos un Punto de Venta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#, python-format
msgid "Confirm ?"
msgstr "¿Confirmar?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connect devices to your PoS directly without an IoT Box"
msgstr "Conecte dispositivos a su TPV directamente sin una IoT Box"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__other_devices
msgid "Connect devices to your PoS without an IoT Box."
msgstr "Conecte dispositivos a su TPV sin una IoT Box."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connect devices using an IoT Box"
msgstr "Conectar dispositivos utilizando una IoT Box"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connected Devices"
msgstr "Dispositivos conectados"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Connected, Not Owned"
msgstr "Conectado, no en propiedad"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Connecting to Proxy"
msgstr "Conectando al Proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Connecting to the IoT Box"
msgstr "Conectando al IoT Box"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Connection Error"
msgstr "Error de conexión"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Connection error"
msgstr "Error de conexión"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Connection is aborted"
msgstr "Se perdió la conexión"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Connection is lost"
msgstr "Se perdió la conexión"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "La conexión a la caja IoT falló"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "Fallo en la conexión a la impresora"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Continue Selling"
msgstr "Continuar venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Continue selling"
msgstr "Continuar venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion Rate"
msgstr "Tipo de cambio"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion rate from company currency to order currency."
msgstr "Tasa de conversión de la moneda de la empresa a la moneda del pedido."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Cost:"
msgstr "Coste:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Counted"
msgstr "Contado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Country"
msgstr "País"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_coupon
msgid "Coupon and Promotion Programs"
msgstr "Programas de cupones y promociones"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Coupons & Promotions"
msgstr "Cupones & Promociones"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Create"
msgstr "Crear"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "Create a new POS order"
msgstr "Crear un nuevo pedido de TPV"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Create a new PoS"
msgstr "Crear un nuevo TPV"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr "Crear una nueva variante del producto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "Creado el"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "Tasa monetaria"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr "Sesión actual"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_user_id
msgid "Current Session Responsible"
msgstr "Responsable de sesión actual"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr "Estado de la sesión actual"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_custom
msgid "Custom"
msgstr "Personalizado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__pay_later
#, python-format
msgid "Customer Account"
msgstr "Cuenta de cliente"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Customer Display"
msgstr "Pantalla del cliente"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Customer Facing Display"
msgstr "Pantalla orientada al cliente"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Customer Invoice"
msgstr "Factura de cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__customer_note
#, python-format
msgid "Customer Note"
msgstr "Nota del Cliente"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_orderline_customer_notes
msgid "Customer Notes"
msgstr "Notas del Cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Customer Required"
msgstr "Se Requiere Cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#, python-format
msgid "Customer is required for %s payment method."
msgstr "Se requiere el cliente para %s el método de pago"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Customer tips, cannot be modified directly"
msgstr "Propinas de los clientes, no se pueden modificar directamente"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr "Clientes"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "Tablero"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_date
#, python-format
msgid "Date"
msgstr "Fecha"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Days"
msgstr "Días"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Debug Window"
msgstr "Ventana de depuración"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__account_id
msgid "Default Account"
msgstr "Cuenta predeterminada"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__account_default_pos_receivable_account_id
msgid "Default Account Receivable (PoS)"
msgstr "Cuenta por cobrar predeterminada (TPV)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr "Posición fiscal por defecto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Intermediary Account"
msgstr "Cuenta intermediaria predeterminada"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
msgid "Default Pricelist"
msgstr "Tarifa por defecto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "Impuesto de venta por defecto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "Impuesto de ventas predeterminado"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr "Impuesto de venta de productos por defecto"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""
"Unidad de medida por defecto utilizada para todas las operaciones de stock"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr "Definir una nueva categoría"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Define the smallest coinage of the currency used to pay"
msgstr "Definir la moneda más pequeña de la moneda utilizada para pagar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr ""
"Definir la moneda más pequeña de la moneda utilizada para pagar en efectivo"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__name
msgid ""
"Defines the name of the payment method that will be displayed in the Point "
"of Sale when the payments are selected."
msgstr ""
"Define el nombre del método de pago que se mostrará en el Punto de venta al "
"seleccionar el pago."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__barcode_nomenclature_id
msgid ""
"Defines what kind of barcodes are available and how they are assigned to "
"products, customers and cashiers."
msgstr ""
"Indica qué tipo de códigos de barras están disponibles y cómo se asignan a "
"los productos, los clientes o las cajas registradoras."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "Retraso validación"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Delete"
msgstr "Suprimir"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Delete Paid Orders"
msgstr "Borrar Órdenes Pagadas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid "Delete Paid Orders ?"
msgstr "¿Borrar Órdenes Pagadas?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Delete Unpaid Orders"
msgstr "Borrar órdenes sin pagar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid "Delete Unpaid Orders ?"
msgstr "¿Borrar órdenes sin pagar?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Deselect Customer"
msgstr "Deseleccionar cliente"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.desk_organizer
#: model:product.template,name:point_of_sale.desk_organizer_product_template
msgid "Desk Organizer"
msgstr "Organizador de Escritorio"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.desk_pad
#: model:product.template,name:point_of_sale.desk_pad_product_template
msgid "Desk Pad"
msgstr "Alfombrilla de Escritorio"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr "Escritorios"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_id
msgid "Destination account"
msgstr "Cuenta destino"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_readonly
msgid "Destination account is readonly"
msgstr "La cuenta destino es de solo lectura"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_difference
#, python-format
msgid "Difference"
msgstr "Diferencia"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Difference at closing PoS session"
msgstr "Diferencia al cerrar la sesión de TPV"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr "Diferencia entre el saldo de cierre teórico y el saldo de cierre real"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr "Resumen"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Direct Devices"
msgstr "Dispositivos directos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Disc"
msgstr "Desc."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Disc.%"
msgstr "Desc.%"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Disc:"
msgstr "Desc:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Disconnected"
msgstr "Desconectado"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_consumable
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
msgid "Discount"
msgstr "Descuento"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "Descuento (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "Nota de descuento"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discount:"
msgstr "Descuento:"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__discount
msgid "Discounted Product"
msgstr "Producto con descuento"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discounts"
msgstr "Descuentos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Dismiss"
msgstr "Descartar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_display_categ_images
msgid "Display Category Pictures"
msgstr "Mostrar Imágenes de Categoría"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Display pictures of product categories"
msgstr "Mostrar las imágenes de las categorías de los productos"

#. module: point_of_sale
#: code:addons/point_of_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"No tiene acceso, saltar esta información para el email de resumen del "
"usuario"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Do you want to open the customer list to select customer?"
msgstr "¿Desea abrir la lista de clientes para seleccionar un cliente? "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Do you want to print using the web printer?"
msgstr "¿Desea imprimir usando la impresora web?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Documentation"
msgstr "Documentación"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Don't show again"
msgstr "No mostrar de nuevo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "Done by"
msgstr "Hecho por"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Download Paid Orders"
msgstr "Descargar Órdenes Pagadas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Download Unpaid Orders"
msgstr "Descargar Órdenes No Pagadas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Download error traceback"
msgstr "Descargar seguimiento de errores"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientLine.xml:0
#, python-format
msgid "EDIT"
msgstr "EDITAR"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Either the server is inaccessible or browser is not connected online."
msgstr ""
"El servidor no es accesible o el navegador no está conectado a la red."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Electronic Scale"
msgstr "Balanza electrónica"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Email"
msgstr "Correo electrónico"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "Email Receipt"
msgstr "Recibo por email"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Email sent."
msgstr "Email enviado."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Employee"
msgstr "Empleado"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Employees can scan their badge or enter a PIN to log in to a PoS session. "
"These credentials are configurable in the *HR Settings* tab of the employee "
"form."
msgstr ""
"Los empleados pueden escanear su credencial o ingresar un PIN para iniciar "
"sesión en una sesión de TPV. Estas credenciales se pueden configurar en la "
"pestaña * Configuración de recursos humanos * del formulario del empleado."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Empty Order"
msgstr "Pedido vacío"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""
"Habilite el escaneo de códigos de barras con un escáner de códigos de barras"
" conectado de forma remota y el deslizamiento de tarjetas con un lector de "
"tarjetas Vantiv."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "Habilita la integración de una balanza electrónica."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,help:point_of_sale.field_pos_order__invoice_group
msgid "Enables invoice generation from the Point of Sale."
msgstr "Habilita la generación de facturas desde el Punto de Venta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Encountered error when loading image. Please try again."
msgstr "Error encontrado al cargar la imagen. Inténtalo de nuevo."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "Fecha final"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "Saldo final"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error"
msgstr "Error"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "¡Error! No puede crear categorías recursivas."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Error with Traceback"
msgstr "Error con rastreo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error: no internet connection."
msgstr "Error: sin conexión a internet."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Existing orderlines"
msgstr "Orden existente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Exit Pos"
msgstr "Salir del TPV"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_expected
#, python-format
msgid "Expected"
msgstr "Previsto"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Export Paid Orders"
msgstr "Exportar órdenes pagadas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Export Unpaid Orders"
msgstr "Exportar órdenes no pagadas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "Información extra"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.fabric_attribute
msgid "Fabric"
msgstr "Fabric"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__failed_pickings
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__failed_pickings
msgid "Failed Pickings"
msgstr "Pickings fallidos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Financials"
msgstr "Finanzas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Finished Importing Orders"
msgstr "Finalizando importación de órdenes"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position"
msgstr "Posición fiscal"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Fiscal Position not found"
msgstr "No se encontró la posición fiscal "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position per Order"
msgstr "Posición fiscal por pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Positions"
msgstr "Posiciones fiscales"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Fiscal data module error"
msgstr "Error del módulo de datos fiscales"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Footer"
msgstr "Pie de página"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr "Para pantallas táctiles industriales imprecisas"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Force Close Session"
msgstr "Forzar el cierre de sesión"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Force Done"
msgstr "Forzar terminación"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Force done"
msgstr "Forzar terminación"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__force_outstanding_account_id
msgid "Forced Outstanding Account"
msgstr "Cuenta pendiente forzada"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__split_transactions
msgid ""
"Forces to set a customer when using this payment method and splits the "
"journal entries for each customer. It could slow down the closing process."
msgstr ""
"Obliga a establecer un cliente al utilizar este método de pago y divide los "
"asientos de diario para cada cliente. Podría ralentizar el proceso de "
"cierre."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "From invoice payments"
msgstr "De los pagos de factura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__full_product_name
msgid "Full Product Name"
msgstr "Nombre completo de producto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Generation of your order references"
msgstr "Creación de las referencias de su pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_gift_card
msgid "Gift Cards"
msgstr "Tarjetas de regalo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Gift card"
msgstr "Tarjeta de regalo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Give customer rewards, free samples, etc."
msgstr "Dar recompensas a los clientes, muestras gratis, etc."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr ""
"Indica el orden de secuencia cuando se muestra una lista de categorías de "
"producto."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
msgid "Global Discounts"
msgstr "Descuentos globales"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Go to"
msgstr "ir a"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Greater than allowed"
msgstr "Mayor a lo permitido"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "Agrupar por"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr "Productos grupales en punto de venta"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "HTTPS connection to IoT Box failed"
msgstr "Falló la conexión HTTPS al IoT Box"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Hardware Events"
msgstr "Eventos de hardware"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Hardware Status"
msgstr "Estado del hardware"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__has_active_session
msgid "Has Active Session"
msgstr "Tiene una sesión activa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr "Tiene control de caja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_refundable_lines
msgid "Has Refundable Lines"
msgstr "Tiene líneas reembolsables"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Header"
msgstr "Encabezado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
msgid "Header & Footer"
msgstr "Encabezado y pie de página"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid "Hide Use Payment Terminal"
msgstr "Ocultar Usar terminal de pago"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/HomeCategoryBreadcrumb.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/HomeCategoryBreadcrumb.xml:0
#, python-format
msgid "Home"
msgstr "Inicio"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "How would you like to receive your receipt"
msgstr "¿Cómo le gustaría recibir su recibo?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "ID"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr "IMPORTANTE: Informe de Error del Punto de Venta de Odoo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
msgid "IP Address"
msgstr "Dirección IP"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__split_transactions
msgid "Identify Customer"
msgstr "Identificar cliente"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si está marcado hay nuevos mensajes que requieren su atención."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra marcado, algunos mensajes tienen error de envío."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid ""
"If this orderline is a refund, then the refunded orderline is specified in "
"this field."
msgstr ""
"Si esta línea de orden es un reembolso entonces se especifica la línea de "
"orden reembolsada en este campo."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Si entrega todos los productos a la vez, el pedido de entrega se programará "
"en función del mayor tiempo de entrega del producto. De lo contrario, se "
"basará en el más corto."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Iface Customer Facing Display"
msgstr "Interfaz de pantalla orientada al cliente"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_128
msgid "Image"
msgstr "Imagen"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Import Orders"
msgstr "Importar pedidos"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr "Mejorar la navegación en pantallas táctiles industriales imprecisas"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opened
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "In Progress"
msgstr "En progreso"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "Para eliminar una venta, ésta debe ser nueva o cancelada"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__real
msgid "In real time"
msgstr "En tiempo real"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect address for shipping"
msgstr "Dirección de envío incorrecta"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect rounding"
msgstr "Redondeo incorrecto"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductItem.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductItem.xml:0
#, python-format
msgid "Info"
msgstr "Información"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__message
msgid "Information message"
msgstr "Mensaje de información"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_start_categ_id
msgid "Initial Category"
msgstr "Categoría inicial"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid ""
"Installing chart of accounts from the General Settings of\n"
"                Invocing/Accounting app will create Bank and Cash payment\n"
"                methods automatically."
msgstr ""
"La instalación del plan de cuentas desde la aplicación Configuración general \n"
"de facturación / contabilidad creará métodos de pago bancarios y en \n"
"efectivo automáticamente."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_mercury
msgid "Integrated Card Payments"
msgstr "Pagos con tarjeta integrados"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__receivable_account_id
msgid "Intermediary Account"
msgstr "Cuenta intermedia"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr "Categorías internas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__note
msgid "Internal Notes"
msgstr "Notas internas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Invalid action"
msgstr "Acción no válida"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#, python-format
msgid "Invalid amount"
msgstr "Importe Inválido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Invalid email."
msgstr "Email inválido."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Invalid product lot"
msgstr "Lote de producto inválido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Inventory"
msgstr "Inventario"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory Management"
msgstr "Gestión de inventario"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "Facturas de clientes"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Invoice Journal"
msgstr "Diario de factura"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid "Invoice payment for %s (%s) using %s"
msgstr "Pago de factura para %s (%s) utilizando %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Invoiced"
msgstr "Facturado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__invoice_group
msgid "Invoicing"
msgstr "Facturación"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IoT Box"
msgstr "IoT Box"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IoT Box IP Address"
msgstr "Dirección IP de la caja de IoT"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_invoiced
msgid "Is Invoiced"
msgstr "Se factura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_refunded
msgid "Is Refunded"
msgstr "Reembolsado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_total_cost_computed
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Is Total Cost Computed"
msgstr "Costo total calculado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__is_in_company_currency
msgid "Is Using Company Currency"
msgstr "Está utilizando la moneda de la empresa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Es un bar/restaurante"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr "Está instalada completamente la Contabilidad"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_tipped
msgid "Is this already tipped?"
msgstr "¿Se aplicó una propina?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__is_change
msgid "Is this payment change?"
msgstr "¿Es un cambio de pago?"

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_tax.py:0
#, python-format
msgid ""
"It is forbidden to modify a tax used in a POS order not posted. You must "
"close the POS sessions before modifying the tax."
msgstr ""
"Está prohibido modificar un impuesto utilizado en una orden de TPV no "
"publicada. Debes cerrar las sesiones de TPV antes de modificar el impuesto."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/debug_manager.js:0
#, python-format
msgid "JS Tests"
msgstr "Tests JS"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
msgid "Journal"
msgstr "Diario"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__move_id
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Journal Items"
msgstr "Apuntes contables"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Keep Session Open"
msgstr "Mantener la sesión abierta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr "Valor Total de KPI de TPV"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.led_lamp
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr "Lámpara LED"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__name
msgid "Label"
msgstr "Descripción"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Language"
msgstr "Idioma"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "Barras de desplazamiento grandes"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr "Efectivo en el cierre de la última sesión"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr "Fecha del cierre de la última sesión"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_leather
msgid "Leather"
msgstr "Cuero"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the default account from the company setting"
msgstr ""
"Dejar vacío para utilizar la cuenta predeterminada de los ajustes de la "
"empresa"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Account used as outstanding account when creating accounting payment records for bank payments."
msgstr ""
"Deje vacío para utilizar la cuenta predeterminada de los ajustes de la empresa.\n"
"La cuenta que se utiliza como cuenta pendiente al crear registros de pagos contables para los pagos bancarios."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__receivable_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Overrides the company's receivable account (for Point of Sale) used in the journal entries."
msgstr ""
"Dejar vacío para utilizar la cuenta predeterminada de los ajustes de la empresa.\n"
"Anula la cuenta por cobrar de la empresa (para el punto de venta) utilizada en los asientos de diario."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the receivable account of customer"
msgstr "Dejar vacío para utilizar la cuenta por cobrar del cliente"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__journal_id
msgid ""
"Leave empty to use the receivable account of customer.\n"
"Defines the journal where to book the accumulated payments (or individual payment if Identify Customer is true) after closing the session.\n"
"For cash journal, we directly write to the default account in the journal via statement lines.\n"
"For bank journal, we write to the outstanding account specified in this payment method.\n"
"Only cash and bank journals are allowed."
msgstr ""
"Dejar vacío para utilizar la cuenta por cobrar del cliente.\n"
"Define el diario en el que se anotan los pagos acumulados (o el pago individual si Identificar cliente es verdadero) después de cerrar la sesión.\n"
"Para el diario de efectivo, escribimos directamente en la cuenta predeterminada del diario a través de las líneas de estado de cuenta.\n"
"Para el diario de bancos, escribimos en la cuenta pendiente especificada en este método de pago.\n"
"Solo se permiten los diarios de efectivo y de bancos."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.letter_tray
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr "Bandeja de Cartas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_partners_amount
msgid "Limited Partners Amount"
msgstr "Importe de partners limitados"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_partners_loading
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Limited Partners Loading"
msgstr "Cargando partners limitados"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_products_loading
msgid "Limited Product Loading"
msgstr "Cargando producto limitado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_products_amount
msgid "Limited Products Amount"
msgstr "Importe de productos limitados"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Limited Products Loading"
msgstr "Cargando productos limitados"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr "Línea Nº"

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid "List of Cash Registers"
msgstr "Lista de cajas registradoras"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Load Customers"
msgstr "Cargar clientes"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Load all remaining partners in the background"
msgstr "Cargar todos los partners restantes en el fondo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Load all remaining products in the background"
msgstr "Cargar todos los productos restantes en el fondo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Loading"
msgstr "Cargando"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Loading Image Error"
msgstr "Error al cargar la imagen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_local
msgid "Local Customer Facing Display"
msgstr "Pantalla local orientada al cliente"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr "Nº de secuencia de ingreso"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Chrome.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Logo"
msgstr "Logo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr "Nombre del lote"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/OrderWidget.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Lot/Serial Number(s) Required"
msgstr "Lote / Número(s) serial(es)  necesario(s)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr "Lote / Número serial"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_loyalty
msgid "Loyalty Program"
msgstr "Programa de Lealtad"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Loyalty program to use for this point of sale."
msgstr "Programa de fidelidad para usar en este punto de venta."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.magnetic_board
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr "Pizarrón Magnético"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjuntos principales"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "Realizar pago"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid ""
"Make several pricelists available in the Point of Sale. You can also apply a"
" pricelist to specific customers from their contact form (in Sales tab). To "
"be valid, this pricelist must be listed here as an available pricelist. "
"Otherwise the default pricelist will apply."
msgstr ""
"Disponer de varias listas de precios disponibles en el punto de venta. "
"También puede aplicar una lista de precios para clientes específicos desde "
"su formulario de contacto (en la pestaña de Ventas). Para ser válida, esta "
"lista de precios tiene que enumerarse aquí como una lista de los precios "
"disponibles. De lo contrario, se aplicará la lista de precios por defecto."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"Make sure you are using IoT Box v18.12 or higher. Navigate to %s to accept "
"the certificate of your IoT Box."
msgstr ""
"Asegúrese  de estar usando IoT Box v18.12 o mayor. Navegue a %s para aceptar"
" el certificado de su IoT Box."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage gift card"
msgstr "Gestionar tarjeta regalo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage gift card."
msgstr "Gestionar tarjeta regalo."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr "Administrar promociones &amp; programas de cupones"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion and coupon programs."
msgstr "Gestionar programas de promoción y cupones."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__manual_discount
msgid "Manual Discounts"
msgstr "Descuentos manuales"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__margin
msgid "Margin"
msgstr "Margen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin_percent
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin_percent
msgid "Margin (%)"
msgstr "Margen (%)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Margin:"
msgstr "Margen:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Maximum Exceeded"
msgstr "Se excedió el máximo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Maximum value reached"
msgstr "Valor máximo alcanzado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error
msgid "Message Delivery error"
msgstr "Error de Envío de Mensaje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__name
msgid "Method"
msgstr "Método"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
msgid "Miscellaneous"
msgstr "Varios"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_hr
msgid "Module Pos Hr"
msgstr "Módulo Punto de venta para Recursos humanos"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.monitor_stand
#: model:product.template,name:point_of_sale.monitor_stand_product_template
msgid "Monitor Stand"
msgstr "Soporte de Monitor"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductScreen.xml:0
#, python-format
msgid "More..."
msgstr "Más..."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mi fecha límite de actividad"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "My Sessions"
msgstr "Mis sesiones"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Name"
msgstr "Nombre"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Need customer to invoice"
msgstr "Necesita un cliente para facturar"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need loss account for the following journals to post the lost amount: %s\n"
msgstr ""
"Necesita una cuenta de pérdidas para para publicar el importe perdido en los"
" siguientes diarios: %s\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need profit account for the following journals to post the gained amount: %s"
msgstr ""
"Necesita una cuenta de ganancias para publicar el importe ganado en los "
"siguientes diarios: %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ProductInfoPopup.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Network Error"
msgstr "Error de red"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__draft
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__draft
msgid "New"
msgstr "Nuevo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "New Order"
msgstr "Nuevo pedido"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr "Nueva sesión"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.newspaper_rack
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr "Estante de Periódico"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Siguiente plazo de actividad"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Next Order List"
msgstr "Siguiente lista de pedidos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "No"
msgstr "No"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "No Taxes"
msgstr "Sin Impuestos"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr ""
"No se ha encontrado ningún extracto de caja para esta sesión. Imposible "
"registrar el efectivo devuelto."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "No customer found"
msgstr "No se encontró ningún cliente"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "No data yet!"
msgstr "No hay información aún"

#. module: point_of_sale
#: code:addons/point_of_sale/report/pos_invoice.py:0
#, python-format
msgid "No link to an invoice for %s."
msgstr "Sin enlace a una factura para %s, "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
#, python-format
msgid "No orders found"
msgstr "Sin Órdenes Encontradas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#, python-format
msgid "No product found"
msgstr "No se ha encontrado ningún producto."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductList.xml:0
#, python-format
msgid "No results found for \""
msgstr "No se encontraron resultados de \""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid "No sequence defined on the journal"
msgstr "No hay una secuencia definida en el diario"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid "No sessions found"
msgstr "Sin sesiones encontradas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "None"
msgstr "Ninguno"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "No facturado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Notes"
msgstr "Notas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__number_of_opened_session
msgid "Number of Opened Session"
msgstr "Número de sesiones abiertas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Number of Partners Loaded"
msgstr "Número de partners cargados"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "Número de copias"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Number of Products Loaded"
msgstr "Número de productos cargados"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of Refund Orders"
msgstr "Número de órdenes reembolsadas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of errors"
msgstr "Numero de errores"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_qty
msgid "Number of items refunded in this orderline."
msgstr "Número de artículos reembolsados en esta línea de la orden"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Number of partners loaded can not be 0"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Number of product loaded can not be 0"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes no leidos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "OPENING CASH CONTROL"
msgstr "CONTROL DE EFECTIVO DE APERTURA"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Offline"
msgstr "Desconectado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Offline Error"
msgstr "Error sin conexión"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Offline Orders"
msgstr "Pedidos fuera de línea"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OrderImportPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Ok"
msgstr "Aceptar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Ongoing"
msgstr "En proceso"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"Only a negative quantity is allowed for this refund line. Click on +/- to "
"modify the quantity to be refunded."
msgstr ""
"Solo se permite una cantidad negativa para esta línea de reembolso. Haga "
"clic en +/- para modificar la cantidad que se reembolsará."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "Solo aplique redondeo en efectivo"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only journals of type 'Cash' or 'Bank' could be used with payment methods."
msgstr ""
"Solo los diarios de tipo \"Efectivo\" o \"Banco\" se pueden utilizar con "
"métodos de pago."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only load a limited number of customers at the opening of the PoS."
msgstr "Cargar únicamente un número limitado de clientes al inicio del TPV."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only load most common products at the opening of the PoS."
msgstr "Cargar únicamente los productos más comunes al abrir el TPV."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only on cash methods"
msgstr "Solo para métodos de efectivo"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""
"Solo los usuarios con permiso de responsable de TPV pueden modificar los "
"precios de los productos en los pedidos."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ""
"Solo se admiten formatos de imagen compatibles con la web, como .png o "
".jpeg."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Open Cashbox"
msgstr "Abrir caja"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Open POS Menu"
msgstr "Abrir menú TPV"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Open PoS sessions that are using this payment method."
msgstr "Sesiones de TPV abiertas que están utilizando este método de pago."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Open Session"
msgstr "Abrir sesión"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Open session"
msgstr "Abrir sesión"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Open the money details popup"
msgstr "Abrir la ventana emergente de detalles monetarios"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opened By"
msgstr "Abierta por"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Opened Sessions"
msgstr "Sesiones Abiertas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opened by"
msgstr "Abierta por"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Opening"
msgstr "Apertura"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opening_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opening Control"
msgstr "Control de apertura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opening Date"
msgstr "Fecha de apertura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__opening_notes
msgid "Opening Notes"
msgstr "Notas de apertura"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Opening cash"
msgstr "Efectivo de apertura"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.account_cashbox_line_action
msgid "Opening/Closing Values"
msgstr "Valores de apertura/cierre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation Type"
msgstr "Tipo de operación"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Operation type used to record product pickings <br/>\n"
"                                    Products will be taken from the default source location of this operation type"
msgstr ""
"Tipo de operación utilizada para registrar las selecciones de productos<br/>\n"
"                                     Los productos se tomarán de la ubicación de origen predeterminada de este tipo de operación"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr "Tipos de operación que se muestran en el tablero del inventario."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
#, python-format
msgid "Order"
msgstr "Pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Order %s"
msgstr "Orden %s"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Order %s is not fully paid."
msgstr "El pedido %s no está totalmente pagado."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_count
msgid "Order Count"
msgstr "Recuento de pedidos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "Fecha de pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
msgid "Order IDs Sequence"
msgstr "Secuencia de identificadores del pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr "Secuencia de los identificadores de la línea de pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "Líneas del pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
msgid "Order Ref"
msgstr "Ref. venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr "Nº de secuencia de pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Order is empty"
msgstr "El Pedido está vacío"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Order is not synced. Check your internet connection"
msgstr "La orden no está sincronizada. Compruebe su conexión a internet."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "Líneas de venta"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Orderlines in this field are the lines that refunded this orderline."
msgstr ""
"Las líneas de orden en este campo son las líneas que reembolsaron esta línea"
" de orden."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/TicketButton.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Orders"
msgstr "Pedidos"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "Análisis de pedidos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__other_devices
msgid "Other Devices"
msgstr "Otros dispositivos"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Other Information"
msgstr "Otra información"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Others"
msgstr "Otros"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid "Outstanding Account"
msgstr "Cuenta pendiente"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_procurement_group__pos_order_id
msgid "POS Order"
msgstr "Orden del PdV"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS Order %s"
msgstr "Pedido de TPV %s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "Línea de pedido de TPV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "Líneas de pedido de TPV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "Pedidos de TPV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "Líneas de pedido de TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_method_id
msgid "POS Payment Method"
msgstr "Método de pago de PdV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_tree_view
msgid "POS Product Category"
msgstr "Categoría de producto de PdV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
msgid "POS Sales"
msgstr "Ventas de TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_session_id
msgid "POS Session"
msgstr "Sesión de TPV"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "POS error"
msgstr "Error de TPV"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS order line %s"
msgstr "Línea de pedido de TPV %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__paid
#, python-format
msgid "Paid"
msgstr "Pagado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "Categoría padre"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Partner"
msgstr "Contacto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__partner_load_background
msgid "Partner Load Background"
msgstr "Cargar fondo de partner"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#, python-format
msgid "Pay"
msgstr "Pagar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "Pagar pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductScreen.xml:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "Pagos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "Fecha de pago"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Payment Method"
msgstr "Método de pago"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_method_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__payment_method_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_tree
msgid "Payment Methods"
msgstr "Métodos de pago"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__ticket
msgid "Payment Receipt Info"
msgstr "Información de Pagos recibidos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "Referencia del pago"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_status
msgid "Payment Status"
msgstr "Estado de Pago"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment Successful"
msgstr "Pago exitoso"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Terminals"
msgstr "Terminales de pago"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__transaction_id
msgid "Payment Transaction ID"
msgstr "ID de transacción de pago"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Payment methods available"
msgstr "Métodos de pago disponibles"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment request pending"
msgstr "Solicitud de pago pendiente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment reversed"
msgstr "Pago revertido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_form
#: model:ir.model,name:point_of_sale.model_account_payment
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__payment_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Payments"
msgstr "Pagos"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_payment_methods_tree
msgid "Payments Methods"
msgstr "Métodos de pago"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Payments in"
msgstr "Pagos en"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "Payments:"
msgstr "Pagos:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Pending Electronic Payments"
msgstr ""

#. module: point_of_sale
#: model:ir.filters,name:point_of_sale.filter_orders_per_session
msgid "Per session"
msgstr "Por sesión"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"Persona que utiliza la caja registradora. Puede ser un sustituto, un "
"estudiante o un empleado interino."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Phone"
msgstr "Teléfono"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Pick which product categories are available"
msgstr "Elija qué categorías de productos están disponibles"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_ids
msgid "Picking"
msgstr "Albarán"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr "Recuento de albaranes"

#. module: point_of_sale
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#, python-format
msgid "Picking POS"
msgstr "Seleccionar punto de venta"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Pickings"
msgstr "Albaranes"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Picture"
msgstr "Foto"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_plastic
msgid "Plastic"
msgstr "Plástico"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please Confirm Large Amount"
msgstr "Porfavor Confirme Cantidad Grande"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "Verifique si la caja de IoT todavía está conectada."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer is still connected."
msgstr "Verifique si la impresora todavía está conectada."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue"
msgstr ""
"Compruebe que la impresora sigue conectada. \n"
"Algunos navegadores no permiten llamadas HTTP desde sitios web a dispositivos en la red (por razones de seguridad). Si ese es el caso, consulte la documentación de Odoo para \"Certificados autofirmados para impresoras de PdV electrónicos\"' y \"Conexiones seguras (HTTPS)\" para resolver el problema"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Please check your internet connection and try again."
msgstr "Compruebe su conexión a internet y vuelva a intentarlo."

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_company.py:0
#, python-format
msgid ""
"Please close all the point of sale sessions in this period before closing "
"it. Open sessions are: %s "
msgstr ""
"Cierre todas las sesiones de punto de venta en este período antes de "
"cerrarlo. Las sesiones abiertas son:%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Please configure a payment method in your POS."
msgstr "Configure un método de pago en su TPV."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr ""
"Defina por favor la cuenta de ingresos para este producto:\"%s\"(id: %dd)."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Please print the invoice from the backend"
msgstr "Por favor, imprima su factura desde el backend."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please provide a partner for the sale."
msgstr "Por favor indique una empresa para la venta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Please select a payment method."
msgstr "Por favor seleccione un método de pago"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please select the Customer"
msgstr "Por favor seleccione el Cliente"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pos_categ_id
msgid "PoS Category"
msgstr "Categoría TPV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "PoS Interface"
msgstr "Interfaz TPV"

#. module: point_of_sale
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_pivot
#, python-format
msgid "PoS Orders"
msgstr "Pedidos TPV"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "PoS Product Categories"
msgstr "Categorías TPV de producto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "PoS Product Category"
msgstr "Categoría TPV de producto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "Point Of Sale"
msgstr "Punto de Venta"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_pos
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model:ir.ui.menu,name:point_of_sale.menu_pos_config_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Point of Sale"
msgstr "Punto de venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_pos_order_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "Análisis punto de venta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_id
msgid "Point of Sale Category"
msgstr "Categoría del TPV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "Configuración del TPV"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__config_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "Configuración del TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__config_ids
msgid "Point of Sale Configurations"
msgstr "Configuraciones de punto de venta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "Detalles del Punto de Venta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr "Informe de detalles del Punto de Venta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr "Informe de Facturas del Punto de Venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
msgid "Point of Sale Journal"
msgstr "Diario de Punto de venta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Make Payment Wizard"
msgstr "Asistente de pago de punto de venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "Punto de Venta Grupo de Administrador"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_warehouse__pos_type_id
msgid "Point of Sale Operation Type"
msgstr "Tipo de operación de punto de venta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Líneas de Orden de Punto de Venta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "Pedidos del TPV"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Informe de Órdenes del Punto de Venta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment_method
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__pos_payment_method_ids
msgid "Point of Sale Payment Methods"
msgstr "Métodos de pago en el punto de venta "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Pagos en puntos de venta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "Sesión TPV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.qunit_suite
msgid "Point of Sale Tests"
msgstr "Pruebas de punto de venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr "Punto de Venta Grupo de Usuario"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__pos_config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr "Configuración del TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_order_id
msgid "Pos Order"
msgstr "Pedido TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_count
msgid "Pos Order Count"
msgstr "Nº de pedidos de TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr "Línea de pedido TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_ids
msgid "Pos Payment"
msgstr "Pago de TPV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Product Categories"
msgstr "Categorías TPV de producto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_session_id
msgid "Pos Session"
msgstr "Sesión de TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr "Duración de la Sesión de TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr "Estado de la sesión del TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr "Nombre de usuario de la sesión del TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Pos Sessions"
msgstr "Sesiones de TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
msgid "PosBox"
msgstr "PosBox"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Positive quantity not allowed"
msgstr "No se permiten cantidades negativas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Postcode"
msgstr "Código postal"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__done
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__done
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Posted"
msgstr "Publicado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Previous Order List"
msgstr "Anterior lista de pedidos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Price"
msgstr "Precio"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Price Control"
msgstr "Control de precios"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Price Unit"
msgstr "Unidad de precio"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Price discount from %s -> %s"
msgstr "Descuento del precio de %s -> %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Price excl. VAT:"
msgstr "Precio sin incluir IVA:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetPricelistButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetPricelistButton.xml:0
#, python-format
msgid "Price list"
msgstr "Tarifa"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__price
msgid "Priced Product"
msgstr "Producto con precio"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
#, python-format
msgid "Pricelist"
msgstr "Tarifa"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Tarifas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Precio"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#, python-format
msgid "Print"
msgstr "Imprimir"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ControlButtons/ReprintReceiptButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#, python-format
msgid "Print Receipt"
msgstr "Imprimir recibo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#, python-format
msgid "Print a report with all the sales of the current PoS Session"
msgstr "Imprima un informe con todas las ventas de la sesión de TPV actual"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print invoices on customer request"
msgstr "Imprimir las facturas a solicitud del cliente"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr "Imprimir recibos automáticamente una vez registrado el pago"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "Imprimir vía proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Printer"
msgstr "Impresora"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "La impresión no es compatible con algunos navegadores."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid ""
"Printing is not supported on some browsers due to no default printing "
"protocol is available. It is possible to print your tickets by making use of"
" an IoT Box."
msgstr ""
"La impresión no es compatible con algunos navegadores debido a que no hay "
"disponible un protocolo de impresión predeterminado. Es posible imprimir sus"
" recibos haciendo uso de un IoT Box."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_procurement_group
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__procurement_group_id
msgid "Procurement Group"
msgstr "Grupo de abastecimiento"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product"
msgstr "Producto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "Categoría de producto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__product_configurator
msgid "Product Configurator"
msgstr "Configurador de productos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__product_load_background
msgid "Product Load Background"
msgstr "Cargar fondo de producto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product Prices"
msgstr "Precios del Producto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr "Categorías del producto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "Cantidad producto"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unidad de medida del producto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_uom_id
msgid "Product UoM"
msgstr "UdM de producto"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr "Categoria de productos UdM"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "Variantes de producto"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Product information"
msgstr "Información del producto"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#, python-format
msgid ""
"Product is not loaded. Tried loading the product from the server but there "
"is a network error."
msgstr ""
"No se cargó el producto. Se intentó cargar el producto desde el servidor "
"pero hay un error de red."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product prices on receipts"
msgstr "Precios de los productos en los recibos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
msgid "Product tips"
msgstr "Consejos de los productos"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "Productos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Connected"
msgstr "Proxy Conectado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Disconnected"
msgstr "Proxy Desconectado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Warning"
msgstr "Advertencia del Proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Qty"
msgstr "Cant."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Quantity"
msgstr "Cantidad"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "REASON"
msgstr "RAZÓN"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Read Weighing Scale"
msgstr "Leer balanza"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#, python-format
msgid "Ready to launch your <b>point of sale</b>?"
msgstr "¿Listo para lanzar su <b>punto de venta</b>?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Reason"
msgstr "Motivo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Receipt"
msgstr "Recepción"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Receipt %s"
msgstr "Recibo %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
msgid "Receipt Footer"
msgstr "Pie del recibo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
msgid "Receipt Header"
msgstr "Cabecera del recibo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
#, python-format
msgid "Receipt Number"
msgstr "Número de recibo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Receipt Printer"
msgstr "Impresora de recibos"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Record payments with a terminal on this journal."
msgstr "Registre los pagos en este diario con un terminal."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr "Sesión de recuperación"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Refresh Display"
msgstr "Actualizar la pantalla"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Refund"
msgstr "Reembolso"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Refund Order Lines"
msgstr "Líneas de orden de reembolso"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Refund Orders"
msgstr "Reembolsar Pedidos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#, python-format
msgid "Refunded"
msgstr "Reembolsado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_order_ids
msgid "Refunded Order"
msgstr "Pedido Reembolsado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "Refunded Order Line"
msgstr "Línea de orden reembolsada"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Refunded Orders"
msgstr "Pedidos Reembolsados"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_orders_count
msgid "Refunded Orders Count"
msgstr "Número de órdenes reembolsadas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_qty
msgid "Refunded Quantity"
msgstr "Cantidad Reembolsada"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "Refunding %s in "
msgstr "Reembolsando %s en"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunds"
msgstr "Reembolsos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Remaining"
msgstr "restantes"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Remaining unsynced orders"
msgstr "Pedidos restantes sin sincronizar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Remove"
msgstr "Eliminar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Replenishment"
msgstr "Reposición"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "Informes"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Reprint Invoice"
msgstr "Reimprimir factura"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Request sent"
msgstr "Solicitud enviada"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Reset"
msgstr "Restablecer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
msgid "Responsible"
msgstr "Responsable"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr "Restringir las modificaciones de los precios a los responsables"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limit_categories
msgid "Restrict Product Categories"
msgstr "Restringir categorías de productos"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Restrict price modification to managers"
msgstr "Restringir la modificación de los precios a los responsables"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Retry"
msgstr "Reintentar"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr "Devolver productos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr "Devuelto"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "Anulación de: %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reversal request sent to terminal"
msgstr "Solicitud de reversión enviada a la terminal"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse"
msgstr "Invertir"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "Pago reverso"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Review"
msgstr "Revisión"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Rounding"
msgstr "Redondeo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Rounding Method"
msgstr "Método de redondeo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Rounding error in payment lines"
msgstr "Error de redondeo en líneas de pago"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/debug_manager.js:0
#, python-format
msgid "Run Point of Sale JS Tests"
msgstr "Ejecutar pruebas JS en el punto de venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega del SMS"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "SN"
msgstr "NS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr "Conteo de Líneas de Venta"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "Línea de venta"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "Detalles de ventas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Sales Journal"
msgstr "Diario de ventas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Save"
msgstr "Guardar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Save this page and come back here to set up the feature."
msgstr ""
"Guarda esta página y luego regresa para que configures las características "
"habilitadas."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scale"
msgstr "Balanza"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Scan"
msgstr "Escanear"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Scan EAN-13"
msgstr "Scanear  EAN-13"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "Escanear vía proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scanner"
msgstr "Escáner"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Search Customers"
msgstr "Buscar clientes"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Search Orders..."
msgstr "Buscar órdenes..."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidgetControlPanel.xml:0
#, python-format
msgid "Search Products..."
msgstr "Buscar productos..."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "Buscar pedido de venta"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#, python-format
msgid "Select"
msgstr "Seleccionar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#, python-format
msgid "Select Fiscal Position"
msgstr "Seleccionar posición fiscal"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select an order"
msgstr "Seleccione un pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#, python-format
msgid "Select either Cash In or Cash Out before confirming."
msgstr "Seleccione entrada o salida de efectivo antes de confirmar."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Select product attributes"
msgstr "Seleccione los atributos del producto"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/HomeCategoryBreadcrumb.js:0
#, python-format
msgid "Select the category"
msgstr "Seleccione la categoría"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#, python-format
msgid "Select the pricelist"
msgstr "Seleccione la tarifa"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select the product(s) to refund and set the quantity"
msgstr ""
"Seleccione el producto o producto(s) a reembolsar y establezca la cantidad"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__selectable_categ_ids
msgid "Selectable Categ"
msgstr "Categoría seleccionable"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Sell products and deliver them later."
msgstr "Enviar productos y entregarlos después."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Send"
msgstr "Enviar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Send Payment Request"
msgstr "Enviar solicitud de pago"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Send by email"
msgstr "Enviar por correo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Sending email failed. Please try again."
msgstr ""
"Error al enviar el correo electrónico. Inténtelo nuevamente en unos minutos."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr "Número de secuencia"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Serial/Lot Number"
msgstr "Número de serie / de lote"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Served by"
msgstr "Servido por"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Server Error"
msgstr "Error de Servidor"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Session"
msgstr "Sesión"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr "Id. de sesión"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_move_id
msgid "Session Journal Entry"
msgstr "Entrada de diario de sesión"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Session ids:"
msgstr "ID de sesión :"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Session is closed"
msgstr "Sesión se encuentra cerrada"

#. module: point_of_sale
#: model:mail.activity.type,name:point_of_sale.mail_activity_old_session
msgid "Session open over 7 days"
msgstr "Sesión abierta durante 7 días."

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "Sesiones"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Set Customer"
msgstr "Establecer cliente"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__set_maximum_difference
msgid "Set Maximum Difference"
msgstr "Establecer la diferencia máxima"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Set Weight"
msgstr "Establecer peso"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session"
msgstr ""
"Establecer una diferencia máxima permitida entre el dinero esperado y el "
"dinero contado durante el cierre de la sesión"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__set_maximum_difference
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session."
msgstr ""
"Establecer una diferencia máxima permitida entre el dinero esperado y el "
"dinero contado durante el cierre de la sesión."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.xml:0
#, python-format
msgid "Set fiscal position"
msgstr "Establecer posición fiscal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr ""
"Establezca precios múltiples por producto, descuentos automáticos, etc."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set of coins/bills that will be used in opening and closing control"
msgstr ""
"Conjunto de monedas/billetes que se utilizarán en el control de apertura y "
"cierre"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set shop-specific prices, seasonal discounts, etc."
msgstr "Establecer precios propios de tienda, descuentos por temporada, etc."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Set the new quantity"
msgstr "Establezca una nueva cantidad"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Settings"
msgstr "Ajustes"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__ship_later
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Ship Later"
msgstr "Enviar más tarde"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_policy
msgid "Shipping Policy"
msgstr "Política de entrega"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Shopping cart"
msgstr "Carro de compras"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Show checkout to customers with a remotely-connected screen."
msgstr ""
"Mostrar la compra a los clientes mediante una pantalla conectada remoto."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Show customers checkout in a pop-up window. Can be moved to a second screen."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_local
msgid ""
"Show customers checkout in a pop-up window. Recommend to be moved to a "
"second screen visible to the client."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_hr
msgid "Show employee login screen"
msgstr "Mostrar pantalla de inicio de sesión de empleado"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Six"
msgstr "Six"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_six
msgid "Six Payment Terminal"
msgstr "Terminal de Pago Six"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.size_attribute
msgid "Size"
msgstr "Tamaño"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Chrome.xml:0
#, python-format
msgid "Skip"
msgstr "Saltar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr "Saltar pantalla de vista previa"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/CategoryBreadcrumb.xml:0
#, python-format
msgid "Slash"
msgstr "barra oblicua"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.small_shelf
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr "Estante Pequeño"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Some Cash Registers are already posted. Please reset them to new in order to close the session.\n"
"Cash Registers: %r"
msgstr ""
"Algunas cajas registradoras ya están publicadas. Restablézcalos a Nuevo para cerrar la sesión.\n"
"Caja registradora: %r"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Some Serial/Lot Numbers are missing"
msgstr "Faltan algunos números de serie/lote"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to configuration "
"errors. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Algunas órdenes no se pudieron enviar al servidor debido a errores de "
"configuración. Puede salir del punto de venta, pero no cierre la sesión "
"antes de que se resuelva el problema."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to internet connection "
"issues. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Algunos pedidos no se pudieron enviar al servidor debido a problemas de "
"conexión a Internet. Puede salir del punto de venta, pero no cierre la "
"sesión antes de que se resuelva el problema."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Some, if not all, post-processing after syncing order failed."
msgstr ""
"Algunos, si no todos, los posprocesos después de la orden de sincronización "
"fallaron."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Specific route"
msgstr "Ruta específica"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr "Especifique el número de serie/lote en la línea del pedido TPV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__route_id
msgid "Spefic route for products delivered later."
msgstr "Especifique la ruta para entregar los productos más tarde."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__start_category
msgid "Start Category"
msgstr "Categoría inicial"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Start selling from a default product category"
msgstr "Comenzar la venta con una categoría de producto por defecto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "Saldo inicial"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "State"
msgstr "Estado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#, python-format
msgid "Status"
msgstr "Estado"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha tope ya ha pasado\n"
"Hoy: La fecha tope es hoy\n"
"Planificada: futuras actividades."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_move
msgid "Stock Move"
msgstr "Movimiento de inventario"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_rule
msgid "Stock Rule"
msgstr "Regla de Inventario"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__update_stock_at_closing
msgid "Stock should be updated at closing"
msgstr "El stock debe actualizarse al cierre"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Street"
msgstr "Calle"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
#, python-format
msgid "Subtotal"
msgstr "Subtotal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Subtotal w/o Tax"
msgstr "Subtotal neto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr "Subtotal sin descuento"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Successfully imported"
msgstr "Importado con éxito"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "Successfully made a cash %s of %s."
msgstr "Se realizo un pago en efectivo%s de%scon éxito."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Sum of opening balance and transactions."
msgstr "Suma del saldo de apertura y las transacciones"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "Suma de subtotales"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connected"
msgstr "Sincronización Conectada"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connecting"
msgstr "Sincronización Conectando"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Disconnected"
msgstr "Sincronización Desconectada"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Error"
msgstr "Error de Sincronización"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "TOTAL"
msgstr "TOTAL"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_box_out
msgid "Take Money In/Out"
msgstr "Tomar dinero dentro / fuera"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: model:ir.model,name:point_of_sale.model_account_tax
#, python-format
msgid "Tax"
msgstr "Impuesto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Amount"
msgstr "Importe impuesto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
msgid "Tax Display"
msgstr "Mostrar impuestos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Tax ID"
msgstr "NIF"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime
msgid "Tax Regime"
msgstr "Posición fiscal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
msgid "Tax Regime Selection value"
msgstr "Valor de la selección de la posición fiscal"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__subtotal
msgid "Tax-Excluded Price"
msgstr "Precios sin impuestos"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__total
msgid "Tax-Included Price"
msgstr "Precio con Impuestos Incluidos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "Impuestos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr "Impuestos para aplicar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Taxes:"
msgstr "Impuestos:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid ""
"Technical field which is used to hide use_payment_terminal when no payment "
"interfaces are installed."
msgstr ""
"Campo técnico que se utiliza para ocultar use_payment_terminal cuando no hay"
" interfaces de pago instaladas."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Tel:"
msgstr "Tel.:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#, python-format
msgid ""
"The Point of Sale could not find any product, client, employee or action "
"associated with the scanned barcode."
msgstr ""
"El Punto de venta no encontró ningún producto, cliente, empleado o acción "
"asociada con el código de barras escaneado."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only supports the \"add a rounding line\" rounding "
"strategy."
msgstr ""
"El punto de venta solo admite la estrategia de redondeo \"agregar una línea "
"de redondeo\"."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"The amount cannot be higher than the due amount if you don't have a cash "
"payment method configured."
msgstr ""
"El importe no puede ser superior al importe adeudado si no tiene un método "
"de pago de efectivo configurado."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"The amount of your payment lines must be rounded to validate the "
"transaction."
msgstr ""
"El monto de sus líneas de pago debe redondearse para validar la transacción."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The cash rounding strategy of the point of sale %(pos)s must be: '%(value)s'"
msgstr ""
"La estrategia de redondeo de efectivo del punto de venta %(pos)s debe ser: "
"'%(value)s'"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default pricelist must be included in the available pricelists."
msgstr "La tarifa por defecto debe incluirse en las tarifas disponibles."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default pricelist must belong to no company or the company of the point "
"of sale."
msgstr ""
"La tarifa por defecto no debe pertenecer a ninguna compañía o a la compañía "
"del punto de venta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"The fiscal data module encountered an error while receiving your order."
msgstr ""
"El módulo de datos fiscales encontró un error al recibir su solicitud."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"The fiscal position used in the original order is not loaded. Make sure it "
"is loaded by adding it in the pos configuration."
msgstr ""
"La posición fiscal usada en el pedido original no está cargada. Asegúrese de"
" que se carga agregándola a la configuración del TPV."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty."
msgstr ""
"El nombre del equipo o la dirección IP del proxy del hardware se detectarán "
"automáticamente si estos se dejan vacíos."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"El registro de facturas tiene que estar en la misma moneda del diario de "
"ventas o de la empresa, si esta no se ha establecido."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal of the point of sale %s must belong to the same company."
msgstr ""
"El diario de factura del punto de venta %s debe pertenecer a la misma "
"empresa."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "The maximum difference allowed is"
msgstr "La diferencia máxima permitida es"

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_session_uniq_name
msgid "The name of this POS Session must be unique !"
msgstr "El nombre de esta sesión de TPV debe ser único."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr "El número de pedidos del punto de venta relacionados con este cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr "El orden no se pudo enviar al servidor debido a un error desconocido."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"The order has been synchronized earlier. Please make the invoice from the "
"backend for the order: "
msgstr ""
"La venta ya ha sido sincronizada antes. Por favor, haga la factura desde el "
"backend para el pedido:"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid ""
"The payment method selected is not allowed in the config of the POS session."
msgstr ""
"El método de pago seleccionado no está permitido en la configuración de la "
"sesión de TPV."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The payment methods for the point of sale %s must belong to its company."
msgstr ""
"Los métodos de pago para el punto de venta %s deben pertenecer a su empresa."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,help:point_of_sale.field_pos_session__config_id
msgid "The physical point of sale you will use."
msgstr "El TPV físico que utilizará."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown."
msgstr ""
"El punto de venta mostrará la categoría de este producto por defecto. Si no "
"se especifica ninguna categoría, se mostrarán todos los productos "
"disponibles."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_available_categ_ids
msgid ""
"The point of sale will only display products which are within one of the "
"selected category trees. If no category is specified, all available products"
" will be shown"
msgstr ""
"El punto de venta solo mostrará productos que estén dentro de uno de los "
"árboles de categorías seleccionados. Si no se especifica ninguna categoría, "
"se mostrarán todos los productos disponibles."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid ""
"The pricelist used if no customer is selected or if the customer has no Sale"
" Pricelist configured."
msgstr ""
"La tarifa que se utiliza si no se selecciona ningún cliente o si el cliente "
"no tiene configurada ninguna tarifa de venta."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_display_categ_images
msgid "The product categories will be displayed with pictures."
msgstr ""
"El punto de venta mostrará esta categoría de Productos por Defecto . Si no "
"se ESPECIFICA Una categoría , Todos los Productos Disponibles se mostrarán"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr ""
"El tipo de cambio de la moneda a la moneda del tipo de cambio aplicable en "
"la fecha del pedido"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr ""
"Se omitirá la pantalla de recibo si el recibo se puede imprimir de forma "
"automática."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr "El recibo se imprimirá automáticamente al final de cada pedido."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the ordered quantity. "
"%s is requested while only %s can be refunded."
msgstr ""
"La cantidad por reembolsar solicitada es mayor a la cantidad ordenada. Se "
"solicitan %s cuando solo se pueden reembolsar %s."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the refundable quantity"
" of %s."
msgstr ""
"La cantidad por reembolsar solicitada es mayor a la cantidad reembolsable "
"de%s."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The sales journal of the point of sale %s must belong to its company."
msgstr ""
"El diario de ventas del punto de venta  %s debe pertenecer a su empresa."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "The selected customer needs an address."
msgstr "El cliente seleccionado necesita una dirección."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The selected pricelists must belong to no company or the company of the "
"point of sale."
msgstr ""
"Las tarifas seleccionadas no deben pertenecer a ninguna compañía o a la "
"compañía del TPV."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr "El servidor encontró un error al recibir su pedido."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid ""
"The session has been opened for an unusually long period. Please consider "
"closing."
msgstr ""
"La sesión ha sido iniciada por un periodo inusualmente largo. Favor de "
"considerar cerrarla."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_adyen
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Adyen. Set your Adyen credentials on the "
"related payment method."
msgstr ""
"Las transacciones son procesadas por Adyen. Establezca sus credenciales de "
"Adyen en el método de pago relacionado."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_six
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Six. Set the IP address of the terminal on"
" the related payment method."
msgstr ""
"Six procesa las transacciones. Configure la dirección IP del terminal en el "
"método de pago relacionado."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercury
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Vantiv. Set your Vantiv credentials on the"
" related payment method."
msgstr ""
"Las transacciones son procesadas por Vantiv. Establezca sus credenciales de "
"Vantiv en el método de pago relacionado."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "Saldo de cierre teórico"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "There are"
msgstr "Hay"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductList.xml:0
#, python-format
msgid "There are no products in this category."
msgstr "No hay productos en esta categoría."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There are still orders in draft state in the session. Pay or cancel the following orders to validate the session:\n"
"%s"
msgstr ""
"Todavía hay órdenes en estado borrador en la sesión. Pague o cancele las siguientes órdenes para validar la sesión:\n"
"%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There are unsynced orders. Do you want to sync these orders?"
msgstr "Hay pedidos no sincronizados. ¿Desea sincronizarlos?"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There is a difference between the amounts to post and the amounts of the "
"orders, it is probably caused by taxes or accounting configurations changes."
msgstr ""
"Hay una diferencia entre los importes por publicar y los importes de las "
"órdenes, probablemente se deba a los impuestos o a los cambios de "
"configuraciones contables."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There is already an electronic payment in progress."
msgstr "Ya hay un pago electrónico en progreso."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is at least one pending electronic payment.\n"
"Please finish the payment with the terminal or cancel it then remove the payment line."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"There is no Chart of Accounts configured on the company. Please go to the "
"invoicing settings to install a Chart of Accounts."
msgstr ""
"No hay un plan de cuentas configurado en la empresa. Vaya a la configuración"
" de facturación para instalar un Plan de cuentas."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please add a cash payment method in the point of sale configuration."
msgstr ""
"No hay ningún método de pago de efectivo en este punto de venta para manejar el cambio.\n"
"\n"
"Por favor añada un método de pago de efectivo en la configuración del punto de venta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""
"No hay ningún método de pago de caja disponible en este TPV para manejar el cambio.\n"
"\n"
"Pague el importe exacto o añada un método de pago de caja en la configuración del TPV"

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_box.py:0
#, python-format
msgid "There is no cash register for this PoS Session"
msgstr "No hay caja registradora para esta sesión de TPV"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash register in this session."
msgstr "No hay caja registradora en esta sesión."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated "
"and invoiced."
msgstr ""
"Debe haber al menos un producto en su orden antes de poder validar y "
"facturarla."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"This account is used as intermediary account when nothing is set in a "
"payment method."
msgstr ""
"Esta cuenta se utiliza como una cuenta intermediaria cuando no hay un método"
" de pago establecido."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance"
" and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""
"Este campo representa la diferencia máxima permitida entre el saldo final y "
"el efectivo teórico al cerrar una sesión, para usuarios no administradores "
"TPV . Si se alcanza este máximo, el usuario tendrá un mensaje de error en el"
" cierre de su sesión diciendo que él tiene que ponerse en contacto con su "
"manager ."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client."
msgstr ""
"Este campo se encuentra aquí para pasar el identificador del grupo de "
"responsables del punto de venta al cliente del punto de venta."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client."
msgstr ""
"Este campo se encuentra aquí para pasar el identificador del grupo de "
"usuarios del punto de venta al cliente del punto de venta."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"This invoice has been created from the point of sale session: <a href=# "
"data-oe-model=pos.order data-oe-id=%d>%s</a>"
msgstr ""
"Esta factura se ha creado desde la sesión del punto de venta: <a href=# "
"data-oe-model=pos.order data-oe-id=%d>%s</a>"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__customer_note
msgid "This is a note destined to the customer"
msgstr "Esa es una nota destinada al cliente"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__allowed_pricelist_ids
msgid "This is a technical field used for the domain of pricelist_id."
msgstr "Este es un campo técnico utilizado para el dominio de pricelist_id."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"Esto es útil para los restaurantes que cuentan con servicio para llevar y "
"para consumir allí, y que suponen tasas de impuestos específicos."

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with a payment method. You cannot modify its type"
msgstr ""
"Este diario está asociado con un método de pago. No puede modificar su tipo."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid ""
"This operation will destroy all unpaid orders in the browser. You will lose "
"all the unsaved data and exit the point of sale. This operation cannot be "
"undone."
msgstr ""
"Esta operación eliminará todos los pedidos pendientes de pago del navegador."
" Perderá todos los datos que no estén guardados y abandonará el punto de "
"venta. Esta operación no se puede deshacer."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""
"Esta operación destruirá permanentemente todos los pedidos pagados desde el "
"almacenamiento local. Perderá todos los datos. Esta operación no se puede "
"deshacer."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid ""
"This order already has refund lines for %s. We can't change the customer "
"associated to it. Create a new order for the new customer."
msgstr ""
"Esta orden ya tiene líneas de reembolso para%s. No podemos cambiar el "
"cliente asociado a ella. Cree una nueva orden para el nuevo cliente."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#, python-format
msgid "This order is empty"
msgstr "El pedido está vacío"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"Este pedido no está sincronizado con el servidor. Asegúrese de que esté "
"sincronizado, e inténtelo de nuevo."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "This product is used as reference on customer receipts."
msgstr "Este producto se utiliza como referencia en los recibos del cliente."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders lines."
msgstr ""
"Odoo crea esta secuencia de manera automática pero puede cambiarla para "
"personalizar los números de referencia de sus líneas de pedido."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""
"La secuencia es creada automáticamente por Odoo, pero puede cambiarla para "
"personalizar los números de referencia de sus pedidos."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "This session is already closed."
msgstr "Esta sesión ya está cerrada."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr ""
"Este impuesto se aplica a todos los productos nuevos creados en el catálogo."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Tip"
msgstr "Propina"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tip_amount
msgid "Tip Amount"
msgstr "Importe de propina"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Tip Product"
msgstr "Producto Propina"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_tip
#: model:product.template,name:point_of_sale.product_product_tip_product_template
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Tips"
msgstr "Propinas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Tips:"
msgstr "Consejos:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr "Para cerrar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "To Pay"
msgstr "Por pagar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "To Refund: %s"
msgstr "Para reembolsar: %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr "Para pesar con balanza"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_invoice
msgid "To invoice"
msgstr "A facturar"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr "Para registrar nuevas órdenes, iniciar una sesión nueva."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "To return product(s), you need to open a session in the POS %s"
msgstr "Para devolver productos, debe abrir una sesión en el TPV %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_ship
msgid "To ship"
msgstr "Para enviar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total"
msgstr "Total"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr "Transacción de caja total"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Cost:"
msgstr "Costo total:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "Descuento total"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Total Due"
msgstr "Adeudo Total"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Margin:"
msgstr "Margen total:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Total Paid (with rounding)"
msgstr "Total Pagado (con redondeo)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__total_payments_amount
msgid "Total Payments Amount"
msgstr "Monto total de pagos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "Precio total"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Price excl. VAT:"
msgstr "Precio total sin incluir IVA:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Total Taxes"
msgstr "Total de Impuestos"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__amount
msgid "Total amount of the payment."
msgstr "Importe total del pago."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__total_cost
msgid "Total cost"
msgstr "Costo total"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total of all paid sales orders"
msgstr "Total de todos los pedidos pagados"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Total of closing cash control lines."
msgstr "Total de las lineas de cierre del control de caja"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Total of opening cash control lines."
msgstr "Total de las lineas de apertura del control de caja"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "Ctd. total"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total:"
msgstr "Total:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_transaction
msgid "Transaction"
msgstr "Transacción"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Transaction cancelled"
msgstr "Transacción cancelada"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking
msgid "Transfer"
msgstr "Albarán"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__type
msgid "Type"
msgstr "Tipo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_type
msgid "Type of card used"
msgstr "Tipo de tarjeta utilizada"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Unable to close and validate the session.\n"
"Please set corresponding tax account in each repartition line of the following taxes: \n"
"%s"
msgstr ""
"No se puede cerrar y validar la sesión.\n"
"Configure la cuenta de impuestos correspondiente en cada línea de reparto de los siguientes impuestos:\n"
"%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Unable to download invoice."
msgstr "No se puede descargar la factura."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Unable to invoice order."
msgstr "No se pudo facturar el pedido."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"Unable to modify this PoS Configuration because you can't modify %s while a "
"session is open."
msgstr ""
"No se puede cambiar esta configuración de TPV %s mientras una sesión está "
"abierta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Unable to save changes."
msgstr "No se pueden guardar los cambios."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Unable to show information about this error."
msgstr "No hay información disponible sobre estos errores."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unable to sync order"
msgstr "No se pudo sincronizar el pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "Precio unitario"

#. module: point_of_sale
#: model:product.product,uom_name:point_of_sale.desk_organizer
#: model:product.product,uom_name:point_of_sale.desk_pad
#: model:product.product,uom_name:point_of_sale.led_lamp
#: model:product.product,uom_name:point_of_sale.letter_tray
#: model:product.product,uom_name:point_of_sale.magnetic_board
#: model:product.product,uom_name:point_of_sale.monitor_stand
#: model:product.product,uom_name:point_of_sale.newspaper_rack
#: model:product.product,uom_name:point_of_sale.product_product_consumable
#: model:product.product,uom_name:point_of_sale.product_product_tip
#: model:product.product,uom_name:point_of_sale.small_shelf
#: model:product.product,uom_name:point_of_sale.wall_shelf
#: model:product.product,uom_name:point_of_sale.whiteboard
#: model:product.product,uom_name:point_of_sale.whiteboard_pen
#: model:product.template,uom_name:point_of_sale.desk_organizer_product_template
#: model:product.template,uom_name:point_of_sale.desk_pad_product_template
#: model:product.template,uom_name:point_of_sale.led_lamp_product_template
#: model:product.template,uom_name:point_of_sale.letter_tray_product_template
#: model:product.template,uom_name:point_of_sale.magnetic_board_product_template
#: model:product.template,uom_name:point_of_sale.monitor_stand_product_template
#: model:product.template,uom_name:point_of_sale.newspaper_rack_product_template
#: model:product.template,uom_name:point_of_sale.product_product_consumable_product_template
#: model:product.template,uom_name:point_of_sale.product_product_tip_product_template
#: model:product.template,uom_name:point_of_sale.small_shelf_product_template
#: model:product.template,uom_name:point_of_sale.wall_shelf_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_pen_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_product_template
msgid "Units"
msgstr "Unidades"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#, python-format
msgid "Unknown Barcode"
msgstr "Código de barras desconocido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unknown Error"
msgstr "Error desconocido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Nº de mensajes sin leer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Unsupported File Format"
msgstr "Formato de archivo no soportado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Unsynced order"
msgstr "Pedido no sincronizado"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "UoM"
msgstr "UdM"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__update_stock_quantities
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Update quantities in stock"
msgstr "Actualizar cantidades en stock"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Use a Payment Terminal"
msgstr "Use una terminal de pago"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use a default specific tax regime"
msgstr "Utilizar una posición fiscal específica por defecto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
msgid "Use a pricelist."
msgstr "Utilizar una lista de precios."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use barcodes to scan products, customer cards, etc."
msgstr ""
"Utilice códigos de barras para escanear productos, tarjetas de clientes, "
"etc."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Use employee credentials to log in to the PoS session and switch cashier"
msgstr ""
"Utilice las credenciales de los empleados para iniciar sesión en la sesión "
"de TPV y cambiar de cajero"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model:res.groups,name:point_of_sale.group_pos_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "User"
msgstr "Usuario"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "Etiquetas de usuario"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
msgid "Uuid"
msgstr "Uuid"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "VAT:"
msgstr "NIF:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Valid product lot"
msgstr "Lote de producto válido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Validate"
msgstr "Validar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Vantiv (US & Canada)"
msgstr "Tales como (EE. UU. y Canadá)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercury
msgid "Vantiv Payment Terminal"
msgstr "Terminal de pago de Vantiv"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Waiting for card"
msgstr "Esperando tarjeta"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.wall_shelf
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr "Estantería de Pared"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_warehouse
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__warehouse_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Warehouse"
msgstr "Almacén"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Weighing"
msgstr "Peso"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Producto Pesado"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__one
msgid "When all products are ready"
msgstr "Cuando todos los productos estén listos"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Whenever you close a session, one entry is generated in the following "
"accounting journal for all the orders not invoiced. Invoices are recorded in"
" accounting separately."
msgstr ""
"Cada vez que cierre una sesión, se genera una entrada en el siguiente "
"registro contable para todos los pedidos no facturados. Las facturas se "
"registran en la contabilidad por separado."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.whiteboard
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr "Pizarra"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.whiteboard_pen
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr "Pluma de Pizarra"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "With a"
msgstr "Con un"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Would you like to load demo data?"
msgstr "¿Le gustaría cargar datos de demostración?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Yes"
msgstr "Sí"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr ""
"No tiene permitido cambiar la configuración de redondeo de efectivo mientras"
" una sesión TPV que lo usa esté abierta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "You are not allowed to change this quantity"
msgstr "No se le permite cambiar esta cantidad"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid ""
"You are trying to sell products with serial/lot numbers, but some of them are not set.\n"
"Would you like to proceed anyway?"
msgstr ""
"Está intentando vender productos con números de serie/lote, pero algunos de ellos no están configurados.\n"
"¿Quiere proceder de todos modos?"

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_bank_statement.py:0
#, python-format
msgid ""
"You can't validate a bank statement that is used in an opened Session of a "
"Point of Sale."
msgstr ""
"No puede validar un extracto bancario que se utiliza en una sesión abierta "
"de un punto de venta."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can't: create a pos order from the backend interface, or unset the "
"pricelist, or create a pos.order in a python test with Form tool, or edit "
"the form view in studio if no PoS order exist"
msgstr ""
"No puede crear un pedido desde el backend, o desactivar la lista de precios,"
" o crear un pedido en un test de python con una herramienta, o editar la "
"vista formulario en el estudio si no existe ningún pedido del TPV"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You cannot close the POS when invoices are not posted.\n"
"Invoices: %s"
msgstr ""
"No puede cerrar el TPV cuando las facturas no están contabilizadas.\n"
"Facturas: %s"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot close the POS when orders are still in draft"
msgstr "No puede cerrar el TPV mientras aún haya pedidos en borrador"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot create a session before the accounting lock date."
msgstr ""
"No puede crear una sesión antes de la fecha de bloqueo de contabilidad."

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_bank_statement.py:0
#, python-format
msgid "You cannot delete a bank statement linked to Point of Sale session."
msgstr ""
"No puede eliminar un extracto bancario vinculado a la sesión de Punto de "
"venta."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid ""
"You cannot delete a point of sale category while a session is still opened."
msgstr ""
"No puede eliminar una categoría de punto de venta mientras una sesión aún "
"está abierta."

#. module: point_of_sale
#: code:addons/point_of_sale/models/product.py:0
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid ""
"You cannot delete a product saleable in point of sale while a session is "
"still opened."
msgstr ""
"No puede borrar un producto vendible en el punto de venta mientras la sesión"
" esté abierta."

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_partner.py:0
#, python-format
msgid ""
"You cannot delete contacts while there are active PoS sessions. Close the "
"session(s) %s first."
msgstr ""
"No puede eliminar contactos mientras haya sesiones activas de TPV. Cierre "
"las sesiones %s primero."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "You do not have any products"
msgstr "No tiene ningún producto"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You don't have the access rights to get the point of sale closing control "
"data."
msgstr ""
"No tiene los permisos de acceso para obtener los datos de control del cierre"
" del punto de venta."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You don't have the access rights to set the point of sale cash box."
msgstr ""
"No tiene los permisos de acceso para establecer la caja de efectivo del "
"punto de venta."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You have enabled the \"Identify Customer\" option for %s payment method,but "
"the order %s does not contain a customer."
msgstr ""
"Tiene habilitada la opción de \"Identificar cliente\" para el método de pago"
" %s, pero la orden %s no contiene un cliente."

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid ""
"You have to define which payment method must be available in the point of "
"sale by reusing existing bank and cash through \"Accounting / Configuration "
"/ Journals / Journals\". Select a journal and check the field \"PoS Payment "
"Method\" from the \"Point of Sale\" tab. You can also create new payment "
"methods directly from menu \"PoS Backend / Configuration / Payment "
"Methods\"."
msgstr ""
"Puede definir qué método de pago estará disponible en el TPV reusando los "
"bancos y cajas existentes a través de \"Contabilidad / Configuración / "
"Diarios / Diarios\". Seleccione un diario y marque la casilla \"Método de "
"pago de TPV\" desde la pestaña TPV. Puede crear también métodos de pago "
"directamente desde el menú \"Backend TPV / Configuración / Métodos de "
"pago\"."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "You have to round your payments lines. is not rounded."
msgstr ""
"Tienes que modificar las líneas de pago, porque el valor no está redondeado."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You have to select a pricelist in the sale form !\n"
"Please set one before choosing a product."
msgstr ""
"¡Debe seleccionar una tarifa en el formulario de ventas!\n"
"Por favor seleccione una antes de seleccionar un producto."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "You have to select a pricelist in the sale form."
msgstr "Tiene que seleccionar una tarifa en el formulario de ventas."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""
"Debe definir un producto para todo lo que usted vende a tráves\n"
"de TPV."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You must have at least one payment method configured to launch a session."
msgstr ""
"Debe tener al menos un método de pago configurado para iniciar una sesión."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You need a loss and profit account on your cash journal."
msgstr "Necesita una cuenta de pérdidas y ganancias en su diario de caja."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"You need to select the customer before you can invoice or ship an order."
msgstr ""
"Debe seleccionar el cliente antes de poder facturar o enviar una orden."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr "Debe asignar un TPV a su sesión."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Your PoS Session is open since %(date)s, we advise you to close it and to "
"create a new one."
msgstr ""
"Su sesión de TPV está abierta desde %(date)s, le recomendamos que la cierre "
"y cree una nueva."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "ZIP"
msgstr "C.P."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "at"
msgstr "en"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "available,"
msgstr "disponible,"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "belong to another session:"
msgstr "pertenecer a otra sesión :"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "discount"
msgstr "descuento"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "e.g. Cash"
msgstr "ej. efectivo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Company Address, Website"
msgstr "p. ej.: dirección de la empresa, página web"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. NYC Shop"
msgstr "ej. Tienda en Nueva York"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr "p. ej.: política de devoluciones, ¡Gracias por comprar con nosotros!"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "e.g. Soft Drinks"
msgstr "p.ej. Refrescos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "for an order of"
msgstr "una orden de"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "forecasted"
msgstr "pronosticado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "in"
msgstr "en"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#, python-format
msgid "items"
msgstr "artículos"

#. module: point_of_sale
#: model:mail.activity.type,summary:point_of_sale.mail_activity_old_session
msgid "note"
msgstr "Nota"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "open sessions"
msgstr "sesiones abiertas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "orders"
msgstr "órdenes"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "out"
msgstr "salida"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "paid orders"
msgstr "órdenes pagadas"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "return"
msgstr "devolver"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders"
msgstr "órdenes sin pagar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders could not be imported"
msgstr "pedidos pendientes de pago no pueden ser importados"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "using"
msgstr "usando"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__limited_products_loading
msgid ""
"we load all starred products (favorite), all services, recent inventory movements of products, and the most recently updated products.\n"
"When the session is open, we keep on loading all remaining products in the background.\n"
"In the meantime, you can click on the 'database icon' in the searchbar to load products from database."
msgstr ""
"cargamos todos los productos destacados (favoritos), todos los servicios, los últimos movimientos de inventario de los productos y los productos actualizados más recientes.\n"
"Cuando la sesión está abierta, seguimos cargando los productos restantes en segundo plano.\n"
"Mientras tanto, puede hacer clic en el icono de la base de datos en la barra de búsqueda para cargar productos desde la base de datos."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "were duplicates of existing orders"
msgstr "eran duplicados de órdenes existentes"
