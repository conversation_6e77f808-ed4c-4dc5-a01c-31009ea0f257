<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
  <rect id="path-1" width="12" height="2" x="0" y="0"/>
  	<filter id="filter-2" width="107.7%" height="200%" x="-3.8%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <rect id="path-3" width="15.059" height="19.07" x="0" y="0"/>
    <rect id="path-4" width="9.535" height="9.535" x="0" y="0"/>
    <linearGradient id="linearGradient-5" x1="72.875%" x2="40.332%" y1="46.704%" y2="35.129%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-6" x1="88.517%" x2="50%" y1="40.057%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <linearGradient id="linearGradient-7" x1="72.875%" x2="40.332%" y1="46.279%" y2="33.212%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-8" x1="88.517%" x2="50%" y1="38.775%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <rect id="path-9" width="6" height="2" x="0" y="0"/>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_mega_menu_thumbnails">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 11)">
        <g class="image_1_border">
          <rect width="10" height="10" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.25 .233)">
            <mask id="mask-8" fill="#fff">
              <use xlink:href="#path-4"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-4"/>
            <ellipse cx="8.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
            <ellipse cx="12.625" cy="10.465" fill="url(#linearGradient-5)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
            <ellipse cx=".125" cy="10.581" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
          </g>
          <path fill="#FFF" d="M10 0v10H0V0h10zm-4 1H1v8h8V1z" class="rectangle_2"/>
        </g>
        <g class="image_1_border" transform="translate(12)">
          <rect width="10" height="10" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.25 .233)">
            <mask id="mask-8" fill="#fff">
              <use xlink:href="#path-4"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-4"/>
            <ellipse cx="8.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
            <ellipse cx="12.625" cy="10.465" fill="url(#linearGradient-5)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
            <ellipse cx=".125" cy="10.581" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
          </g>
          <path fill="#FFF" d="M10 0v10H0V0h10zm-4 1H1v8h8V1z" class="rectangle_2"/>
        </g>
        <g class="image_1_border" transform="translate(24)">
          <rect width="10" height="10" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.25 .233)">
            <mask id="mask-8" fill="#fff">
              <use xlink:href="#path-4"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-4"/>
            <ellipse cx="8.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
            <ellipse cx="12.625" cy="10.465" fill="url(#linearGradient-5)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
            <ellipse cx=".125" cy="10.581" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
          </g>
          <path fill="#FFF" d="M10 0v10H0V0h10zm-4 1H1v8h8V1z" class="rectangle_2"/>
        </g>
        <g class="image_1_border" transform="translate(0 15)">
          <rect width="10" height="10" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.25 .233)">
            <mask id="mask-8" fill="#fff">
              <use xlink:href="#path-4"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-4"/>
            <ellipse cx="8.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
            <ellipse cx="12.625" cy="10.465" fill="url(#linearGradient-5)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
            <ellipse cx=".125" cy="10.581" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
          </g>
          <path fill="#FFF" d="M10 0v10H0V0h10zm-4 1H1v8h8V1z" class="rectangle_2"/>
        </g>
        <g class="image_1_border" transform="translate(12 15)">
          <rect width="10" height="10" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.25 .233)">
            <mask id="mask-8" fill="#fff">
              <use xlink:href="#path-4"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-4"/>
            <ellipse cx="8.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
            <ellipse cx="12.625" cy="10.465" fill="url(#linearGradient-5)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
            <ellipse cx=".125" cy="10.581" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
          </g>
          <path fill="#FFF" d="M10 0v10H0V0h10zm-4 1H1v8h8V1z" class="rectangle_2"/>
        </g>
        <g class="image_1_border" transform="translate(24 15)">
          <rect width="10" height="10" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.25 .233)">
            <mask id="mask-8" fill="#fff">
              <use xlink:href="#path-4"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-4"/>
            <ellipse cx="8.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
            <ellipse cx="12.625" cy="10.465" fill="url(#linearGradient-5)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
            <ellipse cx=".125" cy="10.581" fill="url(#linearGradient-6)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
          </g>
          <path fill="#FFF" d="M10 0v10H0V0h10zm-4 1H1v8h8V1z" class="rectangle_2"/>
        </g>
        <g class="image_2_border" transform="translate(37)">
          <rect width="16" height="20" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.47 .465)">
            <mask id="mask-6" fill="#fff">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-3"/>
            </mask>
            <use fill="#79D1F2" class="mask" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#path-3"/>
            <ellipse cx="9.647" cy="4.884" fill="#F3EC60" class="oval" mask="url(#mask-6)" rx="3.529" ry="3.488"/>
            <ellipse cx="15.294" cy="20.93" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-6)" rx="11.059" ry="6.977"/>
            <ellipse cx="-8.235" cy="21.163" fill="url(#linearGradient-8)" class="oval" mask="url(#mask-6)" rx="17.647" ry="10.93"/>
          </g>
          <path fill="#FFF" d="M16 0v20H0V0h16zm-1 1H1v18h14V1z" class="rectangle_2"/>
        </g>
		    <g class="rectangle" transform="translate(39 23)">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
        </g>
	    	<rect width="53" height="8" fill="#D8D8D8" class="rectangle_bg" opacity=".219" transform="translate(0 29)"/>
	    	<g class="rectangle" transform="translate(6 32)">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-9"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-9"/>
        </g>
	    	<g class="rectangle" transform="translate(18 32)">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-9"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-9"/>
        </g>
	    	<g class="rectangle" transform="translate(30 32)">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-9"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-9"/>
        </g>
	    	<g class="rectangle" transform="translate(42 32)">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-9"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-9"/>
        </g>
      </g>
    </g>
  </g>
</svg>
