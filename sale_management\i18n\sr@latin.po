# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_management
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:25+0000\n"
"PO-Revision-Date: 2017-10-02 11:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sale_management
#. openerp-web
#: code:addons/sale_management/static/src/js/tour.js:79
#, python-format
msgid ""
"<b>Invite salespeople or managers</b> via email.<br/><i>Enter one email per "
"line.</i>"
msgstr ""

#. module: sale_management
#. openerp-web
#: code:addons/sale_management/static/src/js/tour.js:60
#, python-format
msgid ""
"<p><b>Print this quotation.</b> If not yet done, you will be requested to "
"set your company data and to select a document layout.</p>"
msgstr ""

#. module: sale_management
#. openerp-web
#: code:addons/sale_management/static/src/js/tour.js:33
#, python-format
msgid "Click here to add some lines to your quotations."
msgstr ""

#. module: sale_management
#. openerp-web
#: code:addons/sale_management/static/src/js/tour.js:75
#, python-format
msgid "Configuration options are available in the Settings app."
msgstr ""

#. module: sale_management
#: model_terms:ir.ui.view,arch_db:sale_management.sale_order_view_form
msgid "Create Invoice"
msgstr "Kreiraj račun"

#. module: sale_management
#. openerp-web
#: code:addons/sale_management/static/src/js/tour.js:18
#, python-format
msgid ""
"Let's create a new quotation.<br/><i>Note that colored buttons usually point"
" to the next logical actions.</i>"
msgstr ""

#. module: sale_management
#. openerp-web
#: code:addons/sale_management/static/src/js/tour.js:55
#, python-format
msgid "Once your quotation is ready, you can save, print or send it by email."
msgstr ""

#. module: sale_management
#. openerp-web
#: code:addons/sale_management/static/src/js/tour.js:13
#, python-format
msgid "Organize your sales activities with the <b>Sales Management app</b>."
msgstr ""

#. module: sale_management
#. openerp-web
#: code:addons/sale_management/static/src/js/tour.js:38
#, python-format
msgid ""
"Select a product, or create a new one on the fly. The product will define "
"the default sales price (that you can change), taxes and description "
"automatically."
msgstr ""

#. module: sale_management
#. openerp-web
#: code:addons/sale_management/static/src/js/tour.js:65
#, python-format
msgid "Use the breadcrumbs to <b>go back to preceeding screens</b>."
msgstr ""

#. module: sale_management
#. openerp-web
#: code:addons/sale_management/static/src/js/tour.js:69
#, python-format
msgid "Use this menu to access quotations, sales orders and customers."
msgstr ""

#. module: sale_management
#. openerp-web
#: code:addons/sale_management/static/src/js/tour.js:23
#, python-format
msgid ""
"Write the name of your customer to create one on the fly, or select an "
"existing one."
msgstr ""
