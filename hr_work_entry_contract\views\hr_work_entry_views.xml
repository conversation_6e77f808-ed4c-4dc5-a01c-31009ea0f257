<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_work_entry_contract_view_calendar_inherit" model="ir.ui.view">
        <field name="name">hr.work.entry.contract.view.calendar.inherit</field>
        <field name="model">hr.work.entry</field>
        <field name="inherit_id" ref="hr_work_entry.hr_work_entry_view_calendar"/>
        <field name="arch" type="xml">
            <xpath expr="//calendar" position="attributes">
                <attribute name="js_class">work_entries_calendar</attribute>
            </xpath>
        </field>
    </record>

    <record id="hr_work_entry_contract_view_form_inherit" model="ir.ui.view">
        <field name="name">hr.work.entry.contract.view.form.inherit</field>
        <field name="model">hr.work.entry</field>
        <field name="inherit_id" ref="hr_work_entry.hr_work_entry_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//sheet" position="before">
                <div attrs="{'invisible': [('state', '!=', 'conflict')]}">
                    <div class="alert alert-warning mb-0" role="alert" attrs="{'invisible': ['!', ('work_entry_type_id', '=', False)]}" name="work_entry_undefined">
                        This work entry cannot be validated. The work entry type is undefined.
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="hr_work_entry_contract_type_view_form_inherit" model="ir.ui.view">
        <field name="name">hr.work.entry.type.contract.view.form.inherit</field>
        <field name="model">hr.work.entry.type</field>
        <field name="inherit_id" ref="hr_work_entry.hr_work_entry_type_view_form"/>
        <field name="arch" type="xml">
            <group name="time_off" position="inside">
                <field name="is_leave"/>
            </group>
        </field>
    </record>

</odoo>
