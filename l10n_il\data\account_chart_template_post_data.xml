<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="il_chart_template" model="account.chart.template">
        <field name="property_account_receivable_id" ref="il_account_101200"/>
        <field name="property_account_payable_id" ref="il_account_111100"/>
        <field name="property_account_expense_categ_id" ref="il_account_212200"/>
        <field name="property_account_income_categ_id" ref="il_account_200000"/>
        <field name="property_stock_account_input_categ_id" ref="il_account_101120"/>
        <field name="property_stock_account_output_categ_id" ref="il_account_101130"/>
        <field name="property_stock_valuation_account_id" ref="il_account_101110"/>
        <field name="income_currency_exchange_account_id" ref="il_account_201000"/>
        <field name="expense_currency_exchange_account_id" ref="il_account_202100"/>
        <field name="default_pos_receivable_account_id" ref="il_account_101201"/>
    </record>
</odoo>
