# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_attendance
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-02-11 10:14+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Tamil (http://www.transifex.com/odoo/odoo-9/language/ta/)\n"
"Language: ta\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.report_attendanceerrors
msgid ""
"(*) A positive delay means that the employee worked less than recorded.<br/"
">\n"
"(*) A negative delay means that the employee worked more than encoded."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.report_attendanceerrors
msgid "<strong>Total period</strong>"
msgstr ""

#. module: hr_attendance
#: selection:hr.employee,state:0
msgid "Absent"
msgstr ""

#. module: hr_attendance
#: selection:hr.attendance,action:0
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_action
msgid "Action"
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_action_reason
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_action_desc
msgid "Action Reason"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_action_reason_action_type
msgid "Action Type"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance_error_max_delay
msgid ""
"Allowed difference in minutes between the signin/signout and the timesheet "
"computation for one sheet. Set this to 0 for no tolerance."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_error
msgid "Analysis Information"
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_state
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_graph
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_pivot
msgid "Attendance"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_attendance_access
msgid "Attendance Access"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_graph
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_graph_filtered
msgid "Attendance Analysis"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_error
#: model:ir.actions.report.xml,name:hr_attendance.action_report_hrattendanceerror
msgid "Attendance Error Report"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.report_attendanceerrors
msgid "Attendance Errors:"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.open_view_attendance_reason
msgid "Attendance Reasons"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_reason
msgid "Attendance reasons"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.open_view_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model:ir.ui.menu,name:hr_attendance.menu_open_view_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
msgid "Attendances"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_error
msgid ""
"Below the maximum tolerance, the attendance error will not be taken into "
"account. Above the maximum tolerance, the error is considered to be "
"voluntary and will be taken into account."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_error
msgid "Cancel"
msgstr "ரத்து"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/attendance.js:41
#, python-format
msgid "Click to Sign In at %s."
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_config
msgid "Configuration"
msgstr "கட்டமைப்பு"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_action_reason_create_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_create_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_error_create_uid
msgid "Created by"
msgstr "உருவாக்கியவர்"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_action_reason_create_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_create_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_error_create_date
msgid "Created on"
msgstr ""
"உருவாக்கப்பட்ட \n"
"தேதி"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_filter
msgid "Current Month"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_name
msgid "Date"
msgstr "தேதி"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.report_attendanceerrors
msgid "Date Recorded"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.report_attendanceerrors
msgid "Date Signed"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.edit_attendance_reason
msgid "Define attendance reason"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.report_attendanceerrors
msgid "Delay"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_department_id
msgid "Department"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_action_reason_display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_error_display_name
#: model:ir.model.fields,field_description:hr_attendance.field_report_hr_attendance_report_attendanceerrors_display_name
msgid "Display Name"
msgstr "காட்சி பெயர்"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_filter
msgid "Employee"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_who
msgid "Employee attendances"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_error_end_date
msgid "Ending Date"
msgstr ""

#. module: hr_attendance
#: constraint:hr.attendance:0
msgid "Error ! Sign in (resp. Sign out) must follow Sign out (resp. Sign in)"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_filter
msgid "Group By"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_filter
msgid "Hr Attendance Search"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_action_reason_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_error_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_id
#: model:ir.model.fields,field_description:hr_attendance.field_report_hr_attendance_report_attendanceerrors_id
msgid "ID"
msgstr "ID"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_action_reason___last_update
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance___last_update
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_error___last_update
#: model:ir.model.fields,field_description:hr_attendance.field_report_hr_attendance_report_attendanceerrors___last_update
msgid "Last Modified on"
msgstr "கடைசியாக திருத்திய"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_last_sign
msgid "Last Sign"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_action_reason_write_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_error_write_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_write_uid
msgid "Last Updated by"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_action_reason_write_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_error_write_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_write_date
msgid "Last Updated on"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/attendance.js:39
#, python-format
msgid "Last sign in: %s,<br />%s.<br />Click to sign out."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_error_max_delay
msgid "Maximum Tolerance (in minutes)"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.report_attendanceerrors
msgid "Min Delay"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_filter
msgid "Month"
msgstr "மாதம்"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_filter
msgid "My Attendance"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/wizard/hr_attendance_error.py:34
#, python-format
msgid "No records are found for your selection!"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.report_attendanceerrors
msgid "Operation"
msgstr ""

#. module: hr_attendance
#: selection:hr.employee,state:0
msgid "Present"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_error
msgid "Print"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_error
msgid "Print Attendance Report Error"
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_error
msgid "Print Error Attendance Report"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_action_reason_name
msgid "Reason"
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_report
msgid "Reports"
msgstr "அறிக்கைகள்"

#. module: hr_attendance
#: code:addons/hr_attendance/hr_attendance.py:156
#: selection:hr.attendance,action:0
#, python-format
msgid "Sign In"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/hr_attendance.py:156
#: selection:hr.attendance,action:0
#, python-format
msgid "Sign Out"
msgstr ""

#. module: hr_attendance
#: selection:hr.action.reason,action_type:0
msgid "Sign in"
msgstr ""

#. module: hr_attendance
#: selection:hr.action.reason,action_type:0
msgid "Sign out"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance_action_desc
msgid "Specifies the reason for Signing In/Signing Out in case of extra hours."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_action_reason_name
msgid "Specifies the reason for Signing In/Signing Out."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_error_init_date
msgid "Starting Date"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.open_view_attendance
msgid ""
"The Time Tracking functionality aims to manage employee attendances from "
"Sign in/Sign out actions. You can also link this feature to an attendance "
"device using Odoo's web service features."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_filter
msgid "Today"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_worked_hours
msgid "Worked Hours"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/hr_attendance.py:163
#, python-format
msgid ""
"You tried to %s with a date anterior to another event !\n"
"Try to contact the HR Manager to correct attendances."
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_report_hr_attendance_report_attendanceerrors
msgid "report.hr_attendance.report_attendanceerrors"
msgstr ""
