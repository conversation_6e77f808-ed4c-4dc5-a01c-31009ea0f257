# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides_survey
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_slides_survey
#: model:mail.template,body_html:website_slides_survey.mail_template_user_input_certification_failed
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant' or ''\">participant</t><br/><br/>\n"
"        Unfortunately, you have failed the certification and are no longer a member of the course: <t t-out=\"object.slide_partner_id.channel_id.name or ''\">Basics of Gardening</t>.<br/><br/>\n"
"        Don't hesitate to enroll again!\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.slide_partner_id.channel_id.website_url)\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                Enroll now\n"
"            </a>\n"
"        </div>\n"
"        Thank you for your participation.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        عزيزي <t t-out=\"object.partner_id.name or 'participant' or ''\">المشارك</t><br/><br/>\n"
"        للأسف، لم تنجح في اختبار الشهادة ولم تعد عضواً في الدورة: <t t-out=\"object.slide_partner_id.channel_id.name or ''\">مبادئ العناية بالحديقة</t>.<br/><br/>\n"
"        لا تتردد في التسجيل مجدداً!\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.slide_partner_id.channel_id.website_url)\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                سجل الآن\n"
"            </a>\n"
"        </div>\n"
"        شكراً لك على مشاركتك.\n"
"    </p>\n"
"</div>\n"
"            "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "<i class=\"fa fa-arrow-right mr-1\"/>All Certifications"
msgstr "<i class=\"fa fa-arrow-right mr-1\"/>كافة الشهادات "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid ""
"<i class=\"fa fa-download\" aria-label=\"Download certification\" "
"title=\"Download Certification\"/>"
msgstr ""
"<i class=\"fa fa-download\" aria-label=\"Download certification\" "
"title=\"تنزيل الشهادة \"/>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download "
"certification\" title=\"Download certification\"/> Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download "
"certification\" title=\"تنزيل لشهادة \"/> تنزيل الشهادة "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_fill_form_done_inherit_website_slides
msgid ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share certification\" title=\"Share certification\"/>\n"
"                        Share your certification"
msgstr ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share certification\" title=\"مشاركة الشهادة \"/>\n"
"                        مشاركة شهادتك "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "<i class=\"fa fa-share-alt\" aria-label=\"Share\" title=\"Share\"/>"
msgstr "<i class=\"fa fa-share-alt\" aria-label=\"Share\" title=\"مشاركة \"/>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.badge_content
msgid "<i class=\"text-muted\"> awarded users</i>"
msgstr "<i class=\"text-muted\"> المستخدمون المكافأون</i>"

#. module: website_slides_survey
#: model:survey.question,description:website_slides_survey.furniture_certification_page_1
#: model:survey.survey,description:website_slides_survey.furniture_certification
msgid "<p>Test your furniture knowledge!</p>"
msgstr "<p>اختبر معرفتك بالأثاث!</p> "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.courses_home_inherit_survey
msgid "<span class=\"ml-1\">Certifications</span>"
msgstr "<span class=\"ml-1\">الشهادات</span> "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_channel_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('nbr_certification', '&gt;', 0)]}\">Finished</span>\n"
"                <span class=\"o_stat_text\" attrs=\"{'invisible': [('nbr_certification', '=', 0)]}\">Certified</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('nbr_certification', '&gt;', 0)]}\">تم الإكمال</span>\n"
"                <span class=\"o_stat_text\" attrs=\"{'invisible': [('nbr_certification', '=', 0)]}\">معتمد</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.all_user_card
msgid "<span class=\"text-muted small font-weight-bold\">Certifications</span>"
msgstr "<span class=\"text-muted small font-weight-bold\">الشهادات</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.top3_user_card
msgid "<span class=\"text-muted\">Certifications</span>"
msgstr "<span class=\"text-muted\">الشهادات</span> "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Certified</span>"
msgstr "<span class=\"text-muted\">معتمد</span> "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Finished</span>"
msgstr "<span class=\"text-muted\">تم الإكمال</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.course_main
msgid "<span>Start Now</span><i class=\"fa fa-chevron-right ml-2 align-middle\"/>"
msgstr ""
"<span>ابدأ الآن</span><i class=\"fa fa-chevron-right ml-2 align-middle\"/> "

#. module: website_slides_survey
#: model:ir.model.constraint,message:website_slides_survey.constraint_slide_slide_check_survey_id
msgid "A slide of type 'certification' requires a certification."
msgstr "تتطلب الشريحة من نوع \"شهادة\" وجود شهادة. "

#. module: website_slides_survey
#: model:ir.model.constraint,message:website_slides_survey.constraint_slide_slide_check_certification_preview
msgid "A slide of type certification cannot be previewed."
msgstr "لا يمكن معاينة شريحة من نوع شهادة. "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_channel_view_form
msgid "Add Certification"
msgstr "إضافة شهادة "

#. module: website_slides_survey
#: model_terms:ir.actions.act_window,help:website_slides_survey.slide_slide_action_certification
#: model_terms:ir.actions.act_window,help:website_slides_survey.survey_survey_action_slides
#: model_terms:ir.actions.act_window,help:website_slides_survey.survey_survey_action_slides_report
msgid "Add a new certification"
msgstr "إضافة شهادة جديدة "

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_3
msgid "Ash"
msgstr "خشب شجرة الدردار "

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_5
msgid "Bed"
msgstr "السرير "

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_4
msgid "Beech"
msgstr "خشب شجرة الزان "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
msgid "Begin Certification"
msgstr "بدء اختبار الشهادة "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.course_main
msgid "Begin your <b>certification</b> today!"
msgstr "ابدأ <b>الاختبار للحصول على شهادتك</b> اليوم! "

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/js/slides_upload.js:0
#: code:addons/website_slides_survey/static/src/js/slides_upload.js:0
#: code:addons/website_slides_survey/static/src/xml/website_slide_upload.xml:0
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__survey_id
#: model:ir.model.fields.selection,name:website_slides_survey.selection__slide_slide__slide_type__certification
#, python-format
msgid "Certification"
msgstr "الشهادة "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.badge_content
msgid "Certification Badges"
msgstr "شارات الشهادة "

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_survey__slide_channel_ids
msgid "Certification Courses"
msgstr "دورات الشهادة "

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_survey__slide_ids
msgid "Certification Slides"
msgstr "شرائح الشهادة "

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide_partner__survey_scoring_success
msgid "Certification Succeeded"
msgstr "تم اجتياز اختبار الشهادة بنجاح "

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide_partner__user_input_ids
msgid "Certification attempts"
msgstr "محاولات اجتياز اختبار الشهادة "

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/js/slides_certification_upload_toast.js:0
#, python-format
msgid "Certification created"
msgstr "تم إنشاء الشهادة "

#. module: website_slides_survey
#: model:mail.template,name:website_slides_survey.mail_template_user_input_certification_failed
msgid "Certification failed email"
msgstr "البريد الإلكتروني للفشل في اجتياز اختبار الشهادة "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.o_wss_certification_icon
msgid "Certification icon"
msgstr "أيقونة الشهادة "

#. module: website_slides_survey
#: code:addons/website_slides_survey/controllers/slides.py:0
#, python-format
msgid "Certification slides are completed when the survey is succeeded."
msgstr "تكون شرائح الشهادة مكتملة عندما يتم اجتياز الاستطلاع بنجاح. "

#. module: website_slides_survey
#: model:ir.actions.act_window,name:website_slides_survey.slide_slide_action_certification
#: model:ir.actions.act_window,name:website_slides_survey.survey_survey_action_slides
#: model:ir.actions.act_window,name:website_slides_survey.survey_survey_action_slides_report
#: model:ir.ui.menu,name:website_slides_survey.website_slides_menu_courses_certification
#: model:ir.ui.menu,name:website_slides_survey.website_slides_menu_report_certification
#: model_terms:ir.ui.view,arch_db:website_slides_survey.user_profile_content
msgid "Certifications"
msgstr "الشهادات"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_1
msgid "Chair"
msgstr "كرسي "

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_slide_channel
msgid "Course"
msgstr "دورة"

#. module: website_slides_survey
#: code:addons/website_slides_survey/models/survey_survey.py:0
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_survey_view_form
#, python-format
msgid "Courses"
msgstr "الدورات"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_survey__slide_channel_count
msgid "Courses Count"
msgstr "عدد الدورات "

#. module: website_slides_survey
#: model:slide.slide,name:website_slides_survey.slide_slide_demo_6_0
msgid "DIY Furniture Certification"
msgstr "شهادة صنع الأثاث بنفسك "

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_3
msgid "Desk"
msgstr "مكتب "

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#, python-format
msgid "Download certification"
msgstr "تحميل الشهادة "

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_1
msgid "Fir"
msgstr "خشب شجرة الشوح "

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/js/slides_certification_upload_toast.js:0
#, python-format
msgid ""
"Follow this link to add questions to your certification. <a href=\"%s\">Edit"
" certification</a>"
msgstr ""
"اتبع هذا الرابط لإضافة أسئلة إلى اختبار شهادتك. <a href=\"%s\">تحرير اختبار "
"الشهادة</a> "

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1
msgid "Furniture"
msgstr "الأثاث "

#. module: website_slides_survey
#: model:slide.slide,name:website_slides_survey.slide_slide_demo_5_4
#: model:survey.survey,title:website_slides_survey.furniture_certification
msgid "Furniture Creation Certification"
msgstr "شهادة صنع الأثاث "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_fill_form_done_inherit_website_slides
msgid "Go back to course"
msgstr "العودة إلى الدورة "

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/xml/website_slide_upload.xml:0
#, python-format
msgid "How to upload a certification on your course?"
msgstr "كيف تقوم برفع اختبار شهادة في دورتك؟ "

#. module: website_slides_survey
#: model:survey.question,comments_message:website_slides_survey.furniture_certification_page_1
#: model:survey.question,comments_message:website_slides_survey.furniture_certification_page_1_question_1
#: model:survey.question,comments_message:website_slides_survey.furniture_certification_page_1_question_2
#: model:survey.question,comments_message:website_slides_survey.furniture_certification_page_1_question_3
msgid "If other, please specify:"
msgstr "إذا كان هناك غير ذلك، يرجى التحديد: "

#. module: website_slides_survey
#: model_terms:slide.slide,description:website_slides_survey.slide_slide_demo_6_0
msgid "It's time to test your knowledge!"
msgstr "حان الوقت لاختبار معرفتك! "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "No certifications yet!"
msgstr "لا توجد شهادات بعد! "

#. module: website_slides_survey
#: model_terms:slide.slide,description:website_slides_survey.slide_slide_demo_5_4
msgid ""
"Now that you have completed the course, it's time to test your knowledge!"
msgstr "الآن، بما أنك أكملت الدورة، حان الوقت لتختبر معرفتك! "

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_channel__nbr_certification
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__nbr_certification
msgid "Number of Certifications"
msgstr "عدد الشهادات "

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_2
msgid "Oak"
msgstr "خشب السنديان "

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#, python-format
msgid "Pass Certification"
msgstr "اجتياز اختبار الشهادة "

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_user_input__slide_id
msgid "Related course slide"
msgstr "شريحة الدورة ذات الصلة "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "Score :"
msgstr "الدرجة: "

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1_question_2
msgid "Select all the furniture shown in the video"
msgstr "قم بتحدبد كافة قطع الأثاث المعروضة في مقطع الفيديو "

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_4
msgid "Shelf"
msgstr "رف "

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr "الشريحة / m2m المزين للشريك "

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_user_input__slide_partner_id
msgid "Slide membership information for the logged in user"
msgstr "معلومات عضوية الشريحة للمستخدم المسجل دخوله "

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_slide_slide
msgid "Slides"
msgstr "الشرائح"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_user_input__slide_partner_id
msgid "Subscriber information"
msgstr "معلومات المشترك "

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_survey_survey
msgid "Survey"
msgstr "استطلاع"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "مُدخلات مستخدم الاستطلاع"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_2
msgid "Table"
msgstr "طاولة "

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
#, python-format
msgid "Test Certification"
msgstr "اختبار الشهادة "

#. module: website_slides_survey
#: model:survey.question,validation_error_msg:website_slides_survey.furniture_certification_page_1
#: model:survey.question,validation_error_msg:website_slides_survey.furniture_certification_page_1_question_1
#: model:survey.question,validation_error_msg:website_slides_survey.furniture_certification_page_1_question_2
#: model:survey.question,validation_error_msg:website_slides_survey.furniture_certification_page_1_question_3
msgid "The answer you entered is not valid."
msgstr "الجواب الذي أدخلته غير صالح. "

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_survey__slide_channel_ids
msgid ""
"The courses this survey is linked to through the e-learning application"
msgstr "الدورات التي يرتبط بها هذا الاستطلاع من خلال تطبيق التعلم الإلكتروني "

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_slide_slide__slide_type
msgid ""
"The document type will be set automatically based on the document URL and "
"properties (e.g. height and width for presentation and document)."
msgstr ""
"سوف يتم تحديد نوع المستند تلقائيًا حسب رابط وخصائص المستند (مثل طول وعرض "
"العرض التقديمي والمستند)."

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_user_input__slide_id
msgid "The related course slide when there is no membership information"
msgstr "شريحة الدورة ذات الصلة عندما لا تكون هناك معلومات عضوية "

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_survey__slide_ids
msgid "The slides this survey is linked to through the e-learning application"
msgstr "الشرائح التي يرتبط بها هذا الاستطلاع من خلال تطبيق التعلم الإلكتروني "

#. module: website_slides_survey
#: model:survey.question,constr_error_msg:website_slides_survey.furniture_certification_page_1
#: model:survey.question,constr_error_msg:website_slides_survey.furniture_certification_page_1_question_1
#: model:survey.question,constr_error_msg:website_slides_survey.furniture_certification_page_1_question_2
#: model:survey.question,constr_error_msg:website_slides_survey.furniture_certification_page_1_question_3
msgid "This question requires an answer."
msgstr "هذا السؤال يحتاج إلى إجابة."

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__slide_type
msgid "Type"
msgstr "النوع"

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1_question_3
msgid "What do you think about the content of the course? (not rated)"
msgstr "ما رأيك في محتوى الدورة؟ (لم يتم التقييم) "

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1_question_1
msgid "What type of wood is the best for furniture?"
msgstr "أي أنواع الخشب أمثل لصناعة الأثاث؟ "

#. module: website_slides_survey
#: code:addons/website_slides_survey/controllers/slides.py:0
#, python-format
msgid "You are not allowed to create a survey."
msgstr "لا يُسمح لك بإنشاء استطلاع. "

#. module: website_slides_survey
#: code:addons/website_slides_survey/controllers/slides.py:0
#, python-format
msgid "You are not allowed to link a certification."
msgstr "لا يُسمح لك بربط شهادة. "

#. module: website_slides_survey
#. openerp-web
#: code:addons/website_slides_survey/static/src/xml/website_slide_upload.xml:0
#, python-format
msgid ""
"You can create your certification from here or use an existing one. Once "
"your certification is created, you can still edit it in backend."
msgstr ""
"بإمكانك إنشاء شهادتك من هنا أو استخدام واحدة موجودة بالفعل. بمجرد أن قد تم "
"إنشاء شهادتك، سيكون بإمكانك تحريرها في الواجهة الخلفية. "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.badge_content
msgid ""
"You can gain badges by passing certifications. Here is a list of all available certification badges.\n"
"                            <br/>Follow the links to reach new heights and skill up!"
msgstr ""
"بإمكانك اكتساب الشارات عن طريق اجتياز اختبارات الشهادات. إليك قائمة بشارات اختبارات الشهادات المتاحة.\n"
"                            <br/>اتبع الروابط للارتقاء إلى أعلى المراحل واكتساب المزيد من المهارات! "

#. module: website_slides_survey
#: model:mail.template,subject:website_slides_survey.mail_template_user_input_certification_failed
msgid ""
"You have failed the course: {{ object.slide_partner_id.channel_id.name }}"
msgstr ""
"لم تتمكن من اجتياز الدورة: {{ object.slide_partner_id.channel_id.name }} "
