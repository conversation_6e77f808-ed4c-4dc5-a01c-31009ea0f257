<?xml version="1.0"?>
<odoo>
    <!-- Actions -->
    <record id="gift_card_sale_order_action" model="ir.actions.act_window">
        <field name="name">Gift Cards</field>
        <field name="res_model">gift.card</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('buy_line_id.order_id', '=', active_id)]</field>
    </record>

    <record id="sale_order_view_extend_gift_card_form" model="ir.ui.view">
        <field name="name">sale.order.view.form.inherit.gift.card</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='preview_sale_order']" position="before">
                <button class="oe_stat_button"
                        name="%(gift_card_sale_order_action)d"
                        attrs="{'invisible': [('gift_card_count', '=', 0)]}"
                        icon="fa-gift"
                        type="action">
                    <field name="gift_card_count" widget="statinfo" string="Gift Cards"/>
                </button>
            </xpath>
        </field>
    </record>

    <record id="sale_gift_card_view_form" model="ir.ui.view">
        <field name="name">gift.card.form Website</field>
        <field name="model">gift.card</field>
        <field name="inherit_id" ref="gift_card.gift_card_view_form" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='code']" position="after">
                <field name="buy_line_id" options="{'no_create': True}"/>
            </xpath>
            <xpath expr="//group[@name='gift_card']" position="after">
                <group>
                    <field name="redeem_line_ids" options="{'no_create': True}" readonly="1">
                        <tree>
                            <field name="order_id"/>
                        </tree>
                    </field>
                </group>
            </xpath>
        </field>
    </record>
</odoo>
