# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_mollie
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: payment_mollie
#: model_terms:ir.ui.view,arch_db:payment_mollie.payment_acquirer_form
msgid "API Key"
msgstr "Cheie API"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Canceled payment with status: %s"
msgstr ""

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "Nu s-a putut stabili conexiunea cu API-ul."

#. module: payment_mollie
#: model:account.payment.method,name:payment_mollie.payment_method_mollie
#: model:ir.model.fields.selection,name:payment_mollie.selection__payment_acquirer__provider__mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_acquirer__mollie_api_key
msgid "Mollie API Key"
msgstr ""

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Nu s-a găsit nicio tranzacție care să se potrivească cu referința %s."

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Procesator Plată"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_account_payment_method
msgid "Payment Methods"
msgstr "Metode de plată"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_transaction
msgid "Payment Transaction"
msgstr "Tranzacție plată"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_acquirer__provider
msgid "Provider"
msgstr "Furnizor"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr ""

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "Furnizorul de servicii de plată de utilizat cu acest achizitor"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_acquirer__mollie_api_key
msgid ""
"The Test or Live API Key depending on the configuration of the acquirer"
msgstr ""
