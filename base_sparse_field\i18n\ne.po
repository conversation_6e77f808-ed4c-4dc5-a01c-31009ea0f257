# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_sparse_field
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Nepali (https://www.transifex.com/odoo/teams/41243/ne/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ne\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_boolean
msgid "Boolean"
msgstr ""

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:24
#, python-format
msgid "Changing the storing system for field \"%s\" is not allowed."
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_char
msgid "Char"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_create_uid
msgid "Created by"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_create_date
msgid "Created on"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_data
msgid "Data"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_display_name
msgid "Display Name"
msgstr ""

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_ir_model_fields
msgid "Fields"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_float
msgid "Float"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_id
msgid "ID"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,help:base_sparse_field.field_ir_model_fields_serialization_field_id
msgid ""
"If set, this field will be stored in the sparse structure of the "
"serialization field, instead of having its own database column. This cannot "
"be changed after creation."
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_integer
msgid "Integer"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test___last_update
msgid "Last Modified on"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_write_uid
msgid "Last Updated by"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_write_date
msgid "Last Updated on"
msgstr ""

#. module: base_sparse_field
#: selection:sparse_fields.test,selection:0
msgid "One"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_partner
msgid "Partner"
msgstr ""

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:26
#, python-format
msgid "Renaming sparse field \"%s\" is not allowed"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test_selection
msgid "Selection"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_ir_model_fields_serialization_field_id
msgid "Serialization Field"
msgstr ""

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:38
#, python-format
msgid "Serialization field `%s` not found for sparse field `%s`!"
msgstr ""

#. module: base_sparse_field
#: selection:sparse_fields.test,selection:0
msgid "Two"
msgstr ""

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_sparse_fields_test
msgid "sparse_fields.test"
msgstr ""
