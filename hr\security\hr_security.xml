<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="base.module_category_human_resources_employees" model="ir.module.category">
        <field name="description">Helps you manage your employees.</field>
        <field name="sequence">9</field>
    </record>

    <record id="group_hr_user" model="res.groups">
        <field name="name">Officer</field>
        <field name="category_id" ref="base.module_category_human_resources_employees"/>
        <field name="implied_ids" eval="[(6, 0, [ref('base.group_private_addresses'), ref('base.group_user')])]"/>
        <field name="comment">The user will be able to approve document created by employees.</field>
    </record>

    <record id="group_hr_manager" model="res.groups">
        <field name="name">Administrator</field>
        <field name="comment">The user will have access to the human resources configuration as well as statistic reports.</field>
        <field name="category_id" ref="base.module_category_human_resources_employees"/>
        <field name="implied_ids" eval="[(4, ref('group_hr_user'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

<data noupdate="1">
    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4,ref('group_hr_manager'))]"/>
    </record>

    <record id="hr_employee_comp_rule" model="ir.rule">
        <field name="name">Employee multi company rule</field>
        <field name="model_id" ref="model_hr_employee"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
    </record>

    <record id="hr_dept_comp_rule" model="ir.rule">
        <field name="name">Department multi company rule</field>
        <field name="model_id" ref="model_hr_department"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
    </record>

    <record id="hr_employee_public_comp_rule" model="ir.rule">
        <field name="name">Employee multi company rule</field>
        <field name="model_id" ref="model_hr_employee_public"/>
        <field name="domain_force">['|',('company_id', '=',False),('company_id', 'in', company_ids)]</field>
    </record>

    <record id="hr_job_comp_rule" model="ir.rule">
        <field name="name">Job multi company rule</field>
        <field name="model_id" ref="model_hr_job"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
    </record>
</data>
</odoo>
