<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="payment.payment_acquirer_adyen" model="payment.acquirer">
        <field name="provider">adyen</field>
        <field name="inline_form_view_id" ref="inline_form"/>
        <field name="support_authorization">False</field>
        <field name="support_fees_computation">False</field>
        <field name="support_refund">partial</field>
        <field name="support_tokenization">True</field>
        <field name="allow_tokenization">True</field>
    </record>

    <record id="payment_method_adyen" model="account.payment.method">
        <field name="name">Adyen</field>
        <field name="code">adyen</field>
        <field name="payment_type">inbound</field>
    </record>

</odoo>
