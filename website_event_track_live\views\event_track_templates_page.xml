<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- MAIN: REMOVE HEADER/FOOTER IF WIDESCREEN -->
<template id="event_track_main" inherit_id="website_event_track.event_track_main">
    <xpath expr="//t[@t-call='website_event.layout']" position="before">
        <t t-set="no_header" t-value="option_widescreen"/>
        <t t-set="no_footer" t-value="option_widescreen"/>
    </xpath>
</template>

<!-- MAIN: ADD VIDEO -->
<template id="event_track_content" inherit_id="website_event_track.event_track_content">
    <!-- Do not display "starting in" in case of replay mode as video is already available -->
    <xpath expr="//div[@t-elif='track.track_start_remaining']" position="attributes">
        <attribute name="t-elif">track.track_start_remaining and not track.is_youtube_replay</attribute>
    </xpath>
    <!-- Add video -->
    <xpath expr="//div[@name='o_wesession_track_main']//div" position="before">
        <t t-set="show_youtube_frame" t-value="track.youtube_video_url and (track.is_youtube_replay or track.is_track_soon or track.is_track_live or track.is_track_done)" />
        <div t-if="show_youtube_frame" class="flex-grow-1 o_wevent_event_track_live"
            t-att-data-track-id="track.id"
            t-att-data-track-name="track.name"
            t-att-data-track-website-image-url="track.website_image_url"
            t-att-data-youtube-video-id="track.youtube_video_id">
            <div class="position-absolute o_wevent_event_track_live_loading w-100 d-flex align-items-center justify-content-center text-white h4">
                <i class="fa fa-spin fa-circle-o-notch position-relative"/>
                <span class="pl-2">Loading Video...</span>
            </div>
            <div id="o_wevent_youtube_iframe_container" class="w-100"/>
        </div>
    </xpath>
</template>

<!-- ASIDE: ADD CHAT TAB FOR VIDEOS -->
<template id="event_track_aside" inherit_id="website_event_track.event_track_aside">
    <xpath expr="//ul[hasclass('o_wesession_track_aside_nav')]/li" position="before">
        <li t-if="track.is_youtube_chat_available and not is_mobile_chat_disabled" class="nav-item flex-grow-1">
            <a href="#track_chat" aria-controls="track_chat" class="nav-link active" role="tab" data-toggle="tab">
                Chat
            </a>
        </li>
    </xpath>
    <xpath expr="//a[@href='#track_list']" position="attributes">
        <attribute name="t-attf-class">#{'nav-link' if track.is_youtube_chat_available and not is_mobile_chat_disabled else 'nav-link active'}</attribute>
    </xpath>
    <xpath expr="//div[hasclass('o_wesession_track_aside_tabs')]" position="inside">
        <div t-if="track.is_youtube_chat_available and not is_mobile_chat_disabled" id="track_chat"
            class="tab-pane fade show active o_wesession_track_aside_tab_chat" role="tabpanel">
            <iframe t-attf-src="https://www.youtube.com/live_chat?v=#{track.youtube_video_id}&amp;embed_domain=#{hostname}"
                height="100%"
                width="100%"
                frameborder="0" allowfullscreen="allowfullscreen"/>
        </div>
    </xpath>
    <xpath expr="//div[@id='track_list']" position="attributes">
        <attribute name="t-attf-class">#{'tab-pane fade' if track.is_youtube_chat_available and not is_mobile_chat_disabled else 'tab-pane fade show active'}</attribute>
    </xpath>
</template>

</odoo>
