<?xml version="1.0" encoding="utf-8"?>
<odoo><data>
    <!-- Sub nav -->
    <template id="user_profile_sub_nav" name="User profile subnav">
        <div class="o_wprofile_all_users_nav">
            <div class="container">
                <div class="row align-items-center justify-content-between">
                    <!-- Desktop Mode -->
                    <nav aria-label="breadcrumb" class="col d-none d-md-flex">
                        <ol class="breadcrumb bg-transparent mb-0 pl-0 py-0">
                            <li t-attf-class="breadcrumb-item #{'active' if not view_user else ''}">
                                <a href="/profile/users">Users</a>
                            </li>
                            <li t-if="view_user" class="breadcrumb-item active">
                                <a><t t-esc="view_user"/></a>
                            </li>
                        </ol>
                    </nav>

                    <div class="col d-none d-md-flex flex-row align-items-center justify-content-end">
                        <!-- search -->
                        <form t-attf-action="/profile/users" role="search" method="get">
                            <div class="input-group o_wprofile_course_nav_search ml-1 position-relative">
                                <span class="input-group-prepend">
                                    <button class="btn btn-link text-white rounded-0 pr-1" type="submit" aria-label="Search" title="Search">
                                        <i class="fa fa-search"></i>
                                    </button>
                                </span>
                                <input type="text" class="form-control border-0 rounded-0 bg-transparent text-white" name="search" placeholder="Search users"/>
                                <input type="hidden" name="group_by" t-att-value="group_by"/>
                            </div>
                        </form>
                    </div>

                    <!-- Mobile Mode -->
                    <div class="col d-md-none py-1 o_wprofile_user_profile_sub_nav_mobile_col">
                        <div class="btn-group w-100 position-relative" role="group" aria-label="Mobile sub-nav">
                            <div class="btn-group w-100 ml-2">
                                <a class="btn bg-black-25 text-white dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Nav</a>

                                <ul class="dropdown-menu">
                                    <a class="dropdown-item" t-att-href="home_url or '/'">Home</a>
                                    <a class="dropdown-item" href="/profile/users">&#9492; Users</a>
                                    <a t-if="view_user" class="dropdown-item">&#9492; <t t-esc="view_user"/></a>
                                </ul>
                            </div>

                            <div class="btn-group ml-1 position-static mr-2">
                                <a class="btn bg-black-25 text-white dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="fa fa-search"></i></a>
                                <div class="dropdown-menu dropdown-menu-right w-100" style="right: 10px;">
                                    <form class="px-3" t-attf-action="/profile/users" role="search" method="get">
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="search" placeholder="Search users"/>
                                            <span class="input-group-append">
                                                <button class="btn btn-primary" type="submit" aria-label="Search" title="Search">
                                                    <i class="fa fa-search"/>
                                                </button>
                                            </span>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!--
    Single User Profile Page
    -->

    <!-- Edit Profile Page -->
    <template id="user_profile_edit_main" name="Edit Profile">
        <t t-set="body_classname" t-value="'o_wprofile_body'"/>
        <t t-call="website.layout">
            <div id="wrap" class="o_wprofile_wrap">
                <div class="container pt-4 pb-5">
                    <t t-call="website_profile.user_profile_edit_content"/>
                </div>
            </div>
        </t>
    </template>

    <template id="user_profile_edit_content" name="Edit Profile">
        <h1 class="o_page_header">Edit Profile</h1>
        <div>
            <form t-attf-action="/profile/user/save" method="post" role="form" class="o_wprofile_editor_form js_website_submit_form row" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                <input type="file" class="d-none o_forum_file_upload" name="ufile" accept="image/*"/>
                <input type="hidden" name="url_param" t-att-value="request.params.get('url_param')"/>
                <div class="col-3">
                    <div class="card o_card_people">
                        <div class="card-body">
                            <img class="o_forum_avatar_img w-100 mb-3" t-att-src="website.image_url(user, 'avatar_128')"/>
                            <div class="text-center">
                                <a href="#" class="o_forum_profile_pic_edit btn btn-primary" aria-label="Edit">
                                    <i class="fa fa-pencil fa-1g float-sm-none float-md-left" title="Edit"></i>
                                </a>
                                <a href="#" title="Clear" aria-label="Clear" class="btn border-primary o_forum_profile_pic_clear">
                                    <i class="fa fa-trash-o float-sm-none float-md-right"></i>
                                </a>
                            </div>
                            <div class="form-group mt-3 mb-0 pt-2 border-top">
                                <label class="text-primary" for="user_website_published" t-if="user.id == uid"><span class="font-weight-bold">Public profile</span></label>
                                <div class=" mb-0 float-right" t-if="user.id == uid">
                                    <input type="checkbox" class="mt8" name="website_published" id="user_website_published" value="True" t-if="not user.website_published"/>
                                    <input type="checkbox" class="mt8" name="website_published" id="user_website_published" value="True" checked="checked" t-if="user.website_published"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-9 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <input name="user_id" t-att-value="user.id" type="hidden"/>
                                <div class="form-group col-12">
                                    <label class="text-primary mb-1 d-block" for="user_name"><span class="font-weight-bold">Name</span></label>
                                    <div>
                                        <input type="text" class="form-control" name="name" id="user_name" required="True" t-attf-value="#{user.name}"/>
                                    </div>
                                </div>

                                <div class="form-group col-6">
                                    <label class="mb-1 text-primary" for="user_website"><span class="font-weight-bold">Website</span></label>
                                    <div>
                                        <input type="text" class="form-control" name="website" id="user_website" t-attf-value="#{user.partner_id.website or ''}"/>
                                    </div>
                                </div>
                                <div class="form-group col-6">
                                    <div t-if="email_required" class="alert alert-danger alert-dismissable oe_email_required" role="alert">
                                        <button type="button" class="close" data-dismiss="alert">x</button>
                                        <p>Please enter a valid email address in order to receive notifications from answers or comments.</p>
                                    </div>
                                    <label class="mb-1 text-primary" for="user_email"><span class="font-weight-bold">Email</span></label>
                                    <div>
                                        <input type="text" class="form-control" name="email" id="user_email" required="True" t-attf-value="#{user.partner_id.email}"/>
                                    </div>
                                </div>
                                <div class="form-group col-6">
                                    <label class="mb-1 text-primary" for="user_city"><span class="font-weight-bold">City</span></label>
                                    <div>
                                        <input type="text" class="form-control" name="city" id="user_city" t-attf-value="#{user.partner_id.city or ''}"/>
                                    </div>
                                </div>
                                <div class="form-group col-6">
                                    <label class="mb-1 text-primary"><span class="font-weight-bold">Country</span></label>
                                    <div>
                                        <select class="form-control" name="country">
                                            <option value="">Country...</option>
                                            <t t-foreach="countries or []" t-as="country">
                                                <option t-att-value="country.id" t-att-selected="country.id == user.partner_id.country_id.id"><t t-esc="country.name"/></option>
                                            </t>
                                         </select>
                                    </div>
                                </div>
                                <div class="form-group col-12">
                                    <label class="mb-1 text-primary" for="description"><span class="font-weight-bold">Biography</span></label>
                                    <textarea name="description" id="description" style="min-height: 120px"
                                        class="form-control o_wysiwyg_loader"><t t-esc="user.partner_id.website_description"/></textarea>
                                </div>
                                <div class="col">
                                    <button type="submit" class="btn btn-primary o_wprofile_submit_btn">Update</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </template>

    <!-- Profile Page -->
    <template id="user_profile_main" name="Profile Page">
        <t t-set="body_classname" t-value="'o_wprofile_body'"/>
        <t t-call="website.layout">
            <div id="wrap" class="o_wprofile_wrap mt-0">
                <t t-call="website_profile.user_profile_header"/>
                <t t-call="website_profile.user_profile_content"/>
            </div>
        </t>
    </template>

    <template id="user_profile_header" name="Profile Page Header">
        <div class="o_wprofile_header o_wprofile_gradient position-relative text-white">
            <t t-call="website_profile.user_profile_sub_nav">
                <t t-set="view_user"><t t-esc="user.name"/></t>
            </t>

            <div class="container pb-3 pb-md-0 pt-2 pt-md-5">
                <div class="row">
                    <!-- ==== Header Left ==== -->
                    <div class="col-12 col-md-4 col-lg-3">
                        <div t-attf-class="d-flex align-items-start h-100 #{'justify-content-between' if (request.env.user == user) else 'justify-content-around' }">
                            <div class="o_wprofile_pict d-inline-block mb-3 mb-md-0" t-attf-style="background-image: url(#{website.image_url(user, 'avatar_1024')});"/>
                            <a class="btn btn-primary d-inline-block d-md-none"
                                t-if="request.env.user == user and user.karma != 0"
                                t-attf-href="/profile/edit?url_param=#{edit_button_url_param}&amp;user_id=#{user.id}">
                                <i class="fa fa-pencil mr-1"/>EDIT
                            </a>
                        </div>
                    </div>

                    <!-- ==== Header Right ==== -->
                    <div class="col-12 col-md-8 col-lg-9 d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start">
                            <h1 class="o_card_people_name">
                                <span t-field="user.name"/><small t-if="user.karma == 0"> (not verified)</small>
                            </h1>
                            <a class="btn btn-primary d-none d-md-inline-block" t-if="request.env.user == user and user.karma != 0 or request.env.user._is_admin()" t-attf-href="/profile/edit?url_param=#{edit_button_url_param}&amp;user_id=#{user.id}">
                                <i class="fa fa-pencil mr-2"/>EDIT PROFILE
                            </a>
                        </div>

                        <div class="d-flex flex-column justify-content-center flex-grow-1 mb-0 mb-md-5">
                            <div t-if="user.partner_id.company_name" class="lead mb-2">
                                <i class="fa fa-building-o fa-fw mr-1"/><span t-field="user.partner_id.company_name"/>
                            </div>
                            <div t-if="user.city or user.country_id" class="lead mb-2">
                                <i class="fa fa-map-marker fa-fw mr-1"/>
                                <span t-field="user.city"/><span class="text-nowrap ml-1" t-if="user.country_id">(<span t-field="user.country_id"/>)</span>
                            </div>
                            <div t-if="user.website" class="lead mb-2">
                                <i class="fa fa-globe fa-fw mr-1"/><span t-field="user.website"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <template id="user_profile_content" name="Profile Page Content">
        <div class="container">
            <div class="row">

                <!-- ========== SIDEBAR ========== -->
                <div class="col-12 col-md-4 col-lg-3 mt-3 mt-md-0">
                    <div class="o_wprofile_sidebar bg-white px-3 py-2 py-md-3 mb-3 mb-md-5">
                        <div class="o_wprofile_sidebar_top d-flex justify-content-between">
                            <div t-if="user.rank_id" class="d-flex align-items-center">
                                <small class="font-weight-bold mr-2">Current rank:</small>
                                <img t-att-src="website.image_url(user.rank_id, 'image_128')" width="16" height="16" alt="" class="o_object_fit_cover mr-1"/>
                                <a t-attf-href="/profile/ranks_badges?url_origin=#{request.httprequest.path}&amp;name_origin=#{user.name}" t-field="user.rank_id"/>
                            </div>
                            <button class="btn btn-sm d-md-none bg-white border" type="button" data-toggle="collapse" data-target="#o_wprofile_sidebar_collapse" aria-expanded="false" aria-controls="o_wprofile_sidebar_collapse">More info</button>
                        </div>
                        <div class="collapse d-md-block" id="o_wprofile_sidebar_collapse">
                            <t t-set="next_rank_id" t-value="user._get_next_rank()"/>
                            <small t-if="next_rank_id" class="font-weight-bold mt-1">Next rank:</small>
                            <t t-if="next_rank_id or user.rank_id" t-call="website_profile.profile_next_rank_card">
                                <t t-set="img_max_width" t-valuef="40%"/>
                            </t>

                            <table class="table table-sm w-100" id="o_wprofile_sidebar_table">
                                <tbody>
                                    <tr>
                                        <th><small class="font-weight-bold">Joined</small></th>
                                        <td><span t-field="user.create_date" t-options='{"format": "d MMM Y"}'/></td>
                                    </tr>
                                    <tr>
                                        <th><small class="font-weight-bold">Badges</small></th>
                                        <td t-if="user.badge_ids" t-esc="len(user.badge_ids.filtered(lambda b: b.badge_id.website_published))"/>
                                        <td t-else="">0</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- ========== PROFILE CONTENT ========== -->
                <div class="col-12 col-md-8 col-lg-9">
                    <ul class="nav nav-tabs o_wprofile_nav_tabs flex-nowrap" role="tablist" id="profile_extra_info_tablist">
                        <li class="nav-item">
                            <a role="tab" aria-controls="about" href="#profile_tab_content_about" class="nav-link active" data-toggle="tab">About</a>
                        </li>
                    </ul>
                    <div class="tab-content py-4 o_wprofile_tabs_content mb-4" id="profile_extra_info_tabcontent">
                        <div role="tabpanel" class="tab-pane active" id="profile_tab_content_about">
                            <div class="o_wprofile_email_validation_container mb16 mt16">
                                <t t-call="website_profile.email_validation_banner">
                                    <t t-set="redirect_url" t-value="'/profile/user/%s' % user.id"/>
                                    <t t-set="send_validation_email_message">Click here to send a verification email.</t>
                                </t>
                            </div>
                            <div id="profile_about_badge" class="mb32">
                                <h5 class="border-bottom pb-1">Badges</h5>
                                <t t-call="website_profile.user_badges"></t>
                            </div>
                            <div t-if="user.partner_id.website_description" class="mb32">
                                <h5 class="border-bottom pb-1">Biography</h5>
                                <span t-field="user.partner_id.website_description"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <template id="profile_next_rank_card" name="Profile Next Rank Card">
        <div class="o_wprofile_progress_circle">
            <svg viewBox="0 0 36 36" class="o_pc_circular_chart">
                <t t-set="next_rank_id" t-value="next_rank_id or user._get_next_rank()"/>
                <t t-if="next_rank_id and user.rank_id">
                    <t t-if="(next_rank_id.karma_min - user.rank_id.karma_min) > 0">
                        <t t-set="user_points" t-value="int(100*(user.karma - user.rank_id.karma_min)/(next_rank_id.karma_min - user.rank_id.karma_min))"/>
                    </t>
                    <t t-else="">
                        <t t-set="user_points" t-value="0"/>
                    </t>
                </t>
                <t t-elif="user.rank_id">
                    <t t-set="user_points" t-value="100"/>
                </t>
                <t t-else="">
                    <t t-set="user_points" t-value="0"/>
                </t>
                <path class="o_pc_circle_bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                <path class="o_pc_circle" t-attf-stroke-dasharray="#{user_points}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" stroke="url(#gradient)" mask="url(#mask)"/>
                <mask id="mask">
                    <polygon points="0,0 17.2,0 17.2,10 18,10 18,0 36,0 36,36 0,36" fill="white"/>
                </mask>
                <linearGradient id="gradient">
                    <stop offset="0%" stop-color="var(--o-pc-color-stop-1)"/>
                    <stop offset="100%" stop-color="var(--o-pc-color-stop-2)"/>
                </linearGradient>
            </svg>
            <div class="o_pc_overlay d-flex flex-column align-items-center justify-content-center">
                <img class="img-fluid"
                    t-att-src="website.image_url(next_rank_id if next_rank_id else user.rank_id, 'image_128')"
                    t-att-alt="(next_rank_id.name if next_rank_id else user.rank_id.name) + ' badge'"
                    t-att-style="'max-width: ' + (img_max_width if img_max_width else '50%;')"/>
                <h4 class=" mb-0">
                    <span t-if="next_rank_id" t-field="next_rank_id.name"/>
                    <span t-else="" t-field="user.rank_id.name"/>
                </h4>
                <small>
                    <span class="font-weight-bold text-primary" t-field="user.karma"/> /
                    <span t-if="next_rank_id" class="font-weight-bold" t-field="next_rank_id.karma_min"/>
                    <span t-else="" class="font-weight-bold" t-field="user.rank_id.karma_min"/>
                     xp
                </small>
            </div>
        </div>
    </template>

    <template id="user_badges" name="User Bagdes">
        <div t-if="user.badge_ids" class="row mx-n1">
            <t t-foreach="user.badge_ids" t-as="badge">
                <t t-if="badge.badge_id.website_published">
                    <div class="col px-1 mb-2 col-xl-4">
                        <div class="card">
                            <div class="card-body p-2 pr-3">
                                <div class="media align-items-center">
                                    <img t-if="not badge.badge_id.image_1920 and badge.badge_id.level"
                                        width="38" height="38"
                                        t-attf-src="/website_profile/static/src/img/badge_#{badge.badge_id.level}.svg"
                                        class="o_object_fit_cover mr-0"
                                        t-att-alt="badge.badge_id.name"/>
                                    <img t-else=""
                                        width="38" height="38"
                                        t-att-src="website.image_url(badge.badge_id, 'image_128')"
                                        class="o_object_fit_cover mr-0"
                                        t-att-alt="badge.badge_id.name"/>
                                    <div class="media-body col-md-10 p-0">
                                        <h6 class="my-0 pl-1 text-truncate" t-field="badge.badge_id.name"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </t>
            </t>
        </div>
        <div class="mb-3 d-inline-block" t-if="not user.badge_ids">
            <p class="text-muted">No badges yet!</p>
        </div>
        <div class="text-right d-inline-block pull-right">
            <a t-if="not user.badge_ids and badge_category" t-attf-href="/profile/ranks_badges?badge_category=#{badge_category}&amp;url_origin=#{request.httprequest.path}&amp;name_origin=#{user.name}"
            class="btn btn-link btn-sm"><i class="fa fa-arrow-right"/> All Badges</a>
            <a t-else="" t-attf-href="/profile/ranks_badges?url_origin=#{request.httprequest.path}&amp;name_origin=#{user.name}"
            class="btn btn-link btn-sm"><i class="fa fa-arrow-right mr-1"/>All Badges</a>
        </div>
    </template>

    <!-- About Ranks and badges Page -->
    <template id="rank_badge_main" name="Ranks Page">
        <t t-call="website.layout">
            <div class="container mb32 mt48">
                <nav t-if="request.params.get('url_origin') and request.params.get('name_origin')" aria-label="breadcrumb">
                    <ol class="breadcrumb p-0 bg-white">
                        <li class="breadcrumb-item">
                            <a t-att-href="request.website._get_http_domain() + '/' + request.params.get('url_origin').lstrip('/')" t-esc="request.params.get('name_origin')"/>
                        </li>
                        <li class="breadcrumb-item">Badges</li>
                    </ol>
                </nav>
                <div class="row justify-content-between" t-if="ranks">
                    <div class="col-12 col-md-6 col-lg-5">
                        <h1>Ranks</h1>
                        <p class="lead mb-4">Keep learning with <t t-esc="website.company_id.name"/>. Collect points on the forum or on the eLearning platform. Those points will make you reach new ranks.</p>
                        <h5>How do I earn badges?</h5>
                        <p>When you finish a course or reach milestones, you're awarded badges.</p>
                        <h5>How do I score more points?</h5>
                        <p>You can score more points by answering quizzes at the end of each course content. Points can also be earned on the forum. Follow this link to the guidelines of the forum.</p>
                    </div>
                    <div class="col-12 col-md-5 col-lg-4">
                        <div class="card">
                            <div class="card-header border-bottom-0">Ranks</div>
                            <ul class="list-group list-group-flush">
                                <t t-foreach="ranks" t-as="rank">
                                    <li t-attf-class="list-group-item">
                                        <div class="media align-items-center">
                                            <img t-att-src="website.image_url(rank, 'image_128')" class="mr-2 o_image_40_cover" alt="Rank badge"/>
                                            <div class="media-body">
                                                <h5 class="mt-0 mb-0" t-field="rank.name"/>
                                                <span class="badge badge-success"><span t-field="rank.karma_min"/></span> point<span t-if="rank.karma_min">s</span>
                                            </div>
                                        </div>
                                    </li>
                                </t>
                            </ul>
                        </div>
                    </div>
                </div>
                <t t-call="website_profile.badge_content"/>
            </div>
        </t>
    </template>

    <template id="badge_content" name="Badges Page content">
        <div id="website_profile_badges">
            <div class="row">
                <div class="col-12">
                    <h1 class="mt-4 mt-lg-2">Badges</h1>
                    <p class="lead">
                        Besides gaining reputation with your questions and answers,
                        you receive badges for being especially helpful.<br class="d-none d-lg-inline-block"/>Badges
                        appear on your profile page, and your posts.
                    </p>
                </div>
            </div>
            <div class="row col-12 align-items-center p-0" t-foreach="badges" t-as="badge">
                <div class="col-3 d-flex align-items-center">
                    <t t-call="website_profile.badge_header"/>
                </div>
                <div class="col-6">
                    <span t-field="badge.description"/>
                </div>
                <div class="col-3 text-right">
                    <b t-esc="badge.granted_users_count"/>
                    <i class="text-muted"> awarded users</i>
                </div>
            </div>
        </div>
    </template>

    <template id="badge_header">
        <img t-if="not badge.image_1920 and badge.level" t-attf-src="/website_profile/static/src/img/badge_#{badge.level}.svg"
                class="my-1 mr-1 wprofile_badge_img" t-att-alt="badge.name"/>
        <img t-else="" t-att-src="website.image_url(badge, 'image_1024')" class="my-1 mr-1 wprofile_badge_img" t-att-alt="badge.name"/>
        <a t-if="badge_url" t-att-href="badge_url">
            <h6 t-field="badge.name" class="d-inline my-0"/>
        </a>
        <h6 t-else="" t-field="badge.name" class="d-inline my-0"/>
    </template>

    <!--Private profile-->
    <template id="private_profile" name="Private Profile Page">
        <t t-call="website.layout">
            <div class="container mb32 mt48">
                <h1 class="mt32">This profile is private!</h1>
                <div id="private_profile_return_link_container">
                    <p><a t-attf-href="/">Return to the website.</a></p>
                </div>
            </div>
        </t>
    </template>

    <!--
    All Users Page
    -->
    <template id="users_page_main" name="Users Page">
        <t t-set="body_classname" t-value="'o_wprofile_body'"/>
         <t t-call="website.layout">
            <div id="wrap" class="o_wprofile_wrap mt-0 pb-5">
                <t t-call="website_profile.users_page_header"/>
                <t t-call="website_profile.users_page_content"/>
            </div>
        </t>
    </template>

    <template id="users_page_header" name="Users Page Header">
        <div class="o_wprofile_all_users_header o_wprofile_gradient mb-n5 pb-5">
            <t t-call="website_profile.user_profile_sub_nav"/>
            <div class="container">
                <h1 class="py-4 text-white d-inline-block">All Users</h1>
                <div class="form-inline py-4 float-right">
                    <strong class="form-group text-white mr-2">Rank by :</strong>
                    <div class="form-group btn-group">
                        <a t-attf-class="btn btn-secondary #{ 'active' if group_by == 'week' else ''}"
                            t-att-href="'/profile/users?' + keep_query('search', group_by='week')">This week</a>
                        <a t-attf-class="btn btn-secondary #{ 'active' if group_by == 'month' else ''}"
                            t-att-href="'/profile/users?' + keep_query('search', group_by='month')">This month</a>
                        <a t-attf-class="btn btn-secondary #{ 'active' if group_by == 'all' else ''}"
                            t-att-href="'/profile/users?' + keep_query('search')">All time</a>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <template id="users_page_content">
        <div class="container mb32">
            <div class="row mb-3">
                <div class="col-md-4 d-flex flex-grow-1" t-foreach="top3_users" t-as="user" t-attf-onclick="location.href='/profile/user/#{user['id']}';">
                    <t t-call="website_profile.top3_user_card"></t>
                </div>
            </div>
            <table class="table table-sm" t-if='users'>
                <t t-foreach="users" t-as="user">
                    <tr t-attf-onclick="location.href='/profile/user/#{user['id']}';" t-attf-class="o_wprofile_pointer bg-white #{user['id'] == user_id.id and 'o_wprofile_border_focus'}">
                        <t t-call="website_profile.all_user_card"/>
                    </tr>
                </t>
                <t t-if="my_user">
                    <!-- keep same table to avoid missaligment -->
                    <tr>
                        <td colspan="7"></td>
                    </tr>
                    <t t-set='user' t-value='my_user'/>
                    <tr t-attf-onclick="location.href='/profile/user/#{user['id']}';" t-attf-class="o_wprofile_pointer bg-white o_wprofile_border_focus">
                        <t t-call="website_profile.all_user_card">
                        </t>
                    </tr>
                </t>
            </table>
            <t t-if='search and not users'>
                <div class='alert alert-warning'>No user found for <strong><t t-esc="search"/></strong>. Try another search.</div>
            </t>
            <div class="form-inline justify-content-center">
                <t t-call="website_profile.pager_nobox"/>
            </div>
        </div>
    </template>

    <template id="top3_user_card" name="Top 3 User Card">
        <div class="card w-100 text-center mb-2 border-bottom-0 o_wprofile_pointer">
            <div class="card-body">
                <div class="d-inline-block position-relative">
                    <img class="rounded-circle img-fluid"
                        style="width: 128px; height: 128px; object-fit: cover;"
                         t-att-src="'/profile/avatar/%s?field=avatar_256%s' % (user['id'], '&amp;res_model=%s&amp;res_id=%s' % (record._name, record.id) if record else '')"/>
                    <img class="position-absolute" t-attf-src="/website_profile/static/src/img/rank_#{user_index + 1}.svg" alt="User rank" style="bottom: 0; right: -10px"/>
                </div>
                <h3 class="mt-2 mb-0" t-esc="user['name']"></h3>
                <span class="badge badge-danger font-weight-normal px-2" t-if="not user['website_published']">Unpublished</span>
                <strong class="text-muted" t-esc="user['rank']"/>
                <div class="h3 my-2" t-if="user['karma_gain']">
                    <span class="badge badge-pill badge-success px-3 py-2" ><t t-if="user['karma_gain'] > 0">+ </t> <t t-esc="user['karma_gain']"/> XP</span>
                </div>
            </div>
            <div class="row mx-0 o_wprofile_top3_card_footer text-nowrap">
                <div class="col py-3"><b t-esc="user['karma']"/> <span class="text-muted">XP</span></div>
                <div class="col py-3"><b t-esc="user['badge_count']"/> <span class="text-muted">Badges</span></div>
            </div>
        </div>
    </template>

    <template id="all_user_card" name="All User Card">
        <td class="align-middle text-right text-muted" style="width: 0">
            <span t-esc="user['position']"/>
        </td>
        <td class="align-middle d-none d-sm-table-cell">
            <img class="o_object_fit_cover rounded-circle o_wprofile_img_small" width="30" height="30" t-att-src="'/profile/avatar/%s?field=avatar_128%s' % (user['id'], '&amp;res_model=%s&amp;res_id=%s' % (record._name, record.id) if record else '')"/>
        </td>
        <td class="align-middle w-md-75">
            <span class="font-weight-bold" t-esc="user['name']"/><br/>
            <span class="text-muted font-weight-bold" t-esc="user['rank']"></span>
        </td>
        <td class="align-middle text-nowrap">
            <t t-if="user['karma_gain']">
                <span class="badge badge-pill badge-success d-inline"><t t-if="user['karma_gain'] > 0">+ </t><t t-esc="user['karma_gain']"/> XP</span>
                <span class="text-muted pl-2 pr-3">
                    <t t-if="group_by == 'week'">this week</t>
                    <t t-elif="group_by == 'month'">this month</t>
                    <t t-else="">All time </t>
                </span>
            </t>
        </td>
        <td class="align-middle font-weight-bold text-right text-nowrap">
            <span t-if="not user['website_published']" class="badge badge-danger font-weight-normal px-2 py-1 m-1">Unpublished</span>
        </td>
        <td class="align-middle font-weight-bold text-right text-nowrap">
            <b t-esc="user['karma']"/> <span class="text-muted small font-weight-bold">XP</span>
        </td>
        <td class="align-middle font-weight-bold text-right pr-3 text-nowrap all_user_badge_count">
            <b t-esc="user['badge_count']"/> <span class="text-muted small font-weight-bold">Badges</span>
        </td>
    </template>

    <!-- Custom Pager: lighter than existing one, no box around number, first / end displayed -->
    <template id="pager_nobox" name="Pager (not box display)">
        <ul t-if="pager['page_count'] > 1" t-attf-class="o_wprofile_pager font-weight-bold pagination m-0">
            <li t-attf-class="page-item o_wprofile_pager_arrow #{'disabled' if pager['page']['num'] == 1 else ''}">
                <a t-att-href=" pager['page_first']['url'] if pager['page']['num'] != 1 else None" class="page-link"><i class="fa fa-step-backward"/></a>
            </li>
            <li t-attf-class="page-item o_wprofile_pager_arrow #{'disabled' if pager['page']['num'] == 1 else ''}">
                <a t-att-href=" pager['page_previous']['url'] if pager['page']['num'] != 1 else None" class="page-link"><i class="fa fa-caret-left"/></a>
            </li>
            <t t-foreach="pager['pages']" t-as="page">
                <li t-attf-class="page-item #{'active disabled bg-primary rounded-circle' if page['num'] == pager['page']['num'] else ''}"> <a t-att-href="page['url']" class="page-link" t-out="page['num']"/></li>
            </t>
            <li t-attf-class="page-item o_wprofile_pager_arrow #{'disabled' if pager['page']['num'] == pager['page_count'] else ''}">
                <a t-att-href="pager['page_next']['url'] if pager['page']['num'] != pager['page_count'] else None" class="page-link"><i class="fa fa-caret-right"/></a>
            </li>
            <li t-attf-class="page-item o_wprofile_pager_arrow #{'disabled' if pager['page']['num'] == pager['page_count'] else ''}">
                <a t-att-href=" pager['page_last']['url'] if pager['page']['num'] != pager['page_count'] else None" class="page-link"><i class="fa fa-step-forward"/></a>
            </li>
        </ul>
    </template>

    <template id="email_validation_banner">
        <t t-set="send_alert_classes" t-value="send_alert_classes if send_alert_classes else 'alert alert-danger alert-dismissable'"/>
        <t t-set="done_alert_classes" t-value="done_alert_classes if done_alert_classes else 'alert alert-success alert-dismissable'"/>

        <div t-if="not validation_email_sent and not is_public_user and user.karma == 0" t-att-class="send_alert_classes" role="alert">
            <button type="button" class="close validation_email_close" data-dismiss="alert" aria-label="Close">×</button>
            It appears your email has not been verified.<br/>
            <a class="send_validation_email alert-link" href="#" t-att-data-redirect_url="redirect_url">
                <span t-esc="send_validation_email_message"/>
            </a>
        </div>
        <div t-if="validation_email_done" t-att-class="done_alert_classes" role="status">
            <button type="button" class="close validated_email_close" data-dismiss="alert" aria-label="Close">×</button>
            <span id="email_validated_message">Congratulations! Your email has just been validated.</span>
            <span t-esc="additional_validated_email_message"/>
        </div>
    </template>

</data></odoo>
