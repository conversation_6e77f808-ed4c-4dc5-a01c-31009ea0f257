<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.MessageReactionGroup" owl="1">
        <div class="o_MessageReactionGroup d-flex" t-att-class="{'o-hasUserReacted': messageReactionGroup and messageReactionGroup.hasUserReacted}" t-att-title="messageReactionGroup and messageReactionGroup.summary" t-on-click="messageReactionGroup and messageReactionGroup.onClick">
            <t t-if="messageReactionGroup">
                <span class="o_MessageReactionGroup_content mx-1">
                    <t t-esc="messageReactionGroup.content"/>
                </span>
                <span class="o_MessageReactionGroup_count mx-1" t-att-class="{'o-hasUserReacted': messageReactionGroup.hasUserReacted}">
                    <t t-esc="messageReactionGroup.count"/>
                </span>
            </t>
        </div>
    </t>

</templates>
