# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_bot
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Aaaaaw that's really cute but, you know, bots don't work that way. You're "
"too human for me! Let's keep it professional ❤️"
msgstr ""
"Aaaaaw este foarte drăguț, dar, știi, roboții nu funcționează așa. Ești prea"
" uman pentru mine! Să-l păstrăm profesionist ❤️"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__disabled
msgid "Disabled"
msgstr "Dezactivat"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_channel
msgid "Discussion Channel"
msgstr "Canal Discuții"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_thread
msgid "Email Thread"
msgstr "Fir E-mail"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Great! 👍<br/>To access special commands, <b>start your sentence with</b> "
"<span class=\"o_odoobot_command\">/</span>. Try getting help."
msgstr ""
"Grozav! 👍<br/>Pentru a accesa comenzi speciale, <b> începeți propoziția "
"cu</b><span class=\"o_odoobot_command\"> /</span>. Încercați să primiți "
"ajutor."

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_ir_http
msgid "HTTP Routing"
msgstr "Rutare HTTP"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_channel.py:0
#, python-format
msgid ""
"Hello,<br/>Odoo's chat helps employees collaborate efficiently. I'm here to "
"help you discover its features.<br/><b>Try to send me an emoji</b> <span "
"class=\"o_odoobot_command\">:)</span>"
msgstr ""
"Bună ziua, <br/>chat-ul Odoo îi ajută pe angajați să colaboreze eficient. "
"Sunt aici pentru a vă ajuta să îi descoperiți caracteristicile. "
"<br/><b>Încercați să-mi trimiteți un emoticon </b><span "
"class=\"o_odoobot_command\">:)</span>"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "Hmmm..."
msgstr "Hmmm..."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"I am a simple bot, but if that's a dog, he is the cutest 😊 "
"<br/>Congratulations, you finished this tour. You can now <b>close this chat"
" window</b>. Enjoy discovering Odoo."
msgstr ""
"Sunt un robot simplu, dar dacă acesta este un câine, el este cel mai drăguț "
"😊<br/> Felicitări, ați terminat acest tur. Acum puteți <b> închide această "
"fereastră de chat </b>. Bucurați-vă de descoperirea Odoo."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "I'm afraid I don't understand. Sorry!"
msgstr "Mă tem că nu înțeleg. Îmi pare rău!"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"I'm not smart enough to answer your question.<br/>To follow my guide, ask: "
"<span class=\"o_odoobot_command\">start the tour</span>."
msgstr ""
"Nu sunt suficient de deștept să-ți răspund la întrebare.<br/> Pentru a-mi "
"urma ghidul, întreabă:<span class=\"o_odoobot_command\"> începe "
"turul</span>."

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__idle
msgid "Idle"
msgstr "Inactiv"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_bot
msgid "Mail Bot"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not exactly. To continue the tour, send an emoji: <b>type</b> <span "
"class=\"o_odoobot_command\">:)</span> and press enter."
msgstr ""
"Nu chiar. Pentru a continua turul, trimiteți un emoticon:<b> tastați</b> "
"<span class=\"o_odoobot_command\"> :)</span> și apăsați Enter."

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__not_initialized
msgid "Not initialized"
msgstr "Neinițializat"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not sure what you are doing. Please, type <span "
"class=\"o_odoobot_command\">/</span> and wait for the propositions. Select "
"<span class=\"o_odoobot_command\">help</span> and press enter"
msgstr ""
"Nu sunt sigur ce faceți. Vă rugăm, tastați <span "
"class=\"o_odoobot_command\"> /</span> și așteptați propunerile. "
"Selectați<span class=\"o_odoobot_command\"> ajutor</span> și apăsați Enter"

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_state
msgid "OdooBot Status"
msgstr "Stare OdooBot"

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_failed
msgid "Odoobot Failed"
msgstr "Odoobot nu a reușit"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_attachement
msgid "Onboarding attachement"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_command
msgid "Onboarding command"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_emoji
msgid "Onboarding emoji"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_ping
msgid "Onboarding ping"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Sorry I'm sleepy. Or not! Maybe I'm just trying to hide my unawareness of "
"human language...<br/>I can show you features if you write: <span "
"class=\"o_odoobot_command\">start the tour</span>."
msgstr ""
"Îmi pare rău că sunt adomir. Sau nu! Poate că încerc doar să ascund lipsa de"
" conștientizare a limbajului uman ...<br/> Vă pot arăta caracteristici dacă "
"scrieți: <span class=\"o_odoobot_command\">începeți turul</span>."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Sorry, I am not listening. To get someone's attention, <b>ping him</b>. "
"Write <span class=\"o_odoobot_command\">@OdooBot</span> and select me."
msgstr ""
"Îmi pare rău, nu ascult. Pentru a atrage atenția cuiva, <b>faceți-i "
"ping</b>. Scrie <span class=\"o_odoobot_command\"> @OdooBot</span> și "
"selectează-mă."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "That's not nice! I'm a bot but I have feelings... 💔"
msgstr "Asta nu-i frumos! Sunt robot dar am sentimente ... 💔"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"To <b>send an attachment</b>, click on the <i class=\"fa fa-paperclip\" "
"aria-hidden=\"true\"></i> icon and select a file."
msgstr ""
"Pentru <b> a trimite un atașament</b>, faceți clic pe<i class=\"fa fa-"
"paperclip\" aria-hidden=\"true\"></i> pictogramă și selectați un fișier."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "To start, try to send me an emoji :)"
msgstr "Pentru a începe, încercați să-mi trimiteți un emoticon :)"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Unfortunately, I'm just a bot 😞 I don't understand! If you need help "
"discovering our product, please check <a "
"href=\"https://www.odoo.com/documentation\" target=\"_blank\">our "
"documentation</a> or <a href=\"https://www.odoo.com/slides\" "
"target=\"_blank\">our videos</a>."
msgstr ""

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_res_users
msgid "Users"
msgstr "Utilizatori"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Wow you are a natural!<br/>Ping someone with @username to grab their "
"attention. <b>Try to ping me using</b> <span "
"class=\"o_odoobot_command\">@OdooBot</span> in a sentence."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Yep, I am here! 🎉 <br/>Now, try <b>sending an attachment</b>, like a picture"
" of your cute dog..."
msgstr ""
"Da, sunt aici! 🎉 <br/>Acum, încercați <b>să trimiteți un atașament</b>, cum "
"ar fi o fotografie a câinelui dvs. drăguț ..."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "fuck"
msgstr "La dracu"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "help"
msgstr "ajutor"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "i love you"
msgstr "te iubesc"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "love"
msgstr "iubire"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "start the tour"
msgstr "începeți turul"
