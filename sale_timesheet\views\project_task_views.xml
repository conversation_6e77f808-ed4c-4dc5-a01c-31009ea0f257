<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="project_project_view_form" model="ir.ui.view">
        <field name="name">project.project.form.inherit</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="hr_timesheet.project_invoice_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_so']" position="attributes">
                <attribute name="attrs">{'invisible': ['|', ('allow_billable', '=', False), ('sale_order_id', '=', False)]}</attribute>
            </xpath>
            <xpath expr="//button[@name='%(project.action_project_task_burndown_chart_report)d']" position="after">
                <button name="action_billable_time_button" type="object" class="oe_stat_button" icon="fa-clock-o" attrs="{'invisible': ['|', ('allow_timesheets', '=', False), ('analytic_account_id', '=', False)]}" groups="hr_timesheet.group_hr_timesheet_approver">
                    <div class="o_field_widget o_stat_info">
                        <div class="oe_inline">
                            <span class="o_stat_value mr-1">
                                <field name="billable_percentage" widget="statinfo" nolabel="1"/>%
                            </span>
                        </div>
                        <span class="o_stat_text">
                        Billable Time
                        </span>
                    </div>
                </button>
            </xpath>
            <xpath expr="//header" position="inside">
                <!-- To be removed in master -->
                <button name="action_make_billable" string="Create Sales Order" type="object" groups="sales_team.group_sale_salesman" invisible="1"/>
            </xpath>
            <xpath expr="//page[@name='settings']" position="after">
                <page name="billing_employee_rate" string="Invoicing" attrs="{'invisible': ['|', ('allow_billable', '=', False), ('partner_id', '=', False)]}">
                    <group>
                        <group>
                            <field name="display_create_order" invisible="1"/>
                            <field name="pricing_type" invisible="1" widget="radio"/>
                            <field name="timesheet_product_id" string="Default Service" invisible="1" context="{'default_detailed_type': 'service', 'default_service_policy': 'delivered_timesheet', 'default_service_type': 'timesheet'}"/>
                            <field name="sale_order_id" invisible="1" options="{'no_create': True, 'no_edit': True, 'delete': False, 'no_open': True}"/>
                            <field name="sale_line_id" string="Default Sales Order Item" options="{'no_create': True, 'no_edit': True, 'delete': False, 'no_open': True}"/>
                        </group>
                    </group>
                    <field name="sale_line_employee_ids">
                        <tree editable="bottom">
                            <field name="company_id" invisible="1"/>
                            <field name="partner_id" invisible="1"/>
                            <field name="employee_id" options="{'no_create': True}"/>
                            <field name="sale_line_id" attrs="{'required': True}" options="{'no_create': True}"/>
                            <field name="price_unit" widget="monetary" force_save="1" options="{'currency_field': 'currency_id'}"/>
                            <field name="cost"/>
                            <field name="is_cost_changed" invisible="1"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="cost_currency_id" invisible="1"/>
                        </tree>
                    </field>
                </page>
            </xpath>
            <xpath expr="//div[@id='timesheet_settings']" position="after">
                <div class="col-lg-6 o_setting_box" id="allow_billable_container">
                    <div class="o_setting_left_pane">
                        <field name="allow_billable"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="allow_billable"/>
                        <div class="text-muted" id="allow_billable_setting">
                            Invoice your time and material to customers
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="project_project_view_form_salesman" model="ir.ui.view">
        <field name="name">project.project.form.inherit.salesman</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="sale_timesheet.project_project_view_form"/>
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
        <field name="arch" type="xml">
            <field name="sale_line_id" position="attributes">
                <attribute name="options">{'no_create': True, 'no_edit': True, 'delete': False}</attribute>
            </field>
        </field>
    </record>

    <record id="project_project_view_form_simplified_inherit" model="ir.ui.view">
        <field name="name">project.project.view.form.simplified.inherit</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="hr_timesheet.project_project_view_form_simplified_inherit_timesheet"/>
        <field name="arch" type="xml">
            <field name="allow_timesheets" position="before">
                <field name="company_id" invisible="1"/>
                <field name="allow_billable"/>
            </field>
        </field>
    </record>

    <record id="project_project_view_kanban_inherit_sale_timesheet" model="ir.ui.view">
        <field name="name">project.project.kanban.inherit.sale.timesheet</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="hr_timesheet.view_project_kanban_inherited"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='allow_timesheets']" position="after">
                <field name="allow_billable"/>
                <field name="warning_employee_rate" invisible="1"/>
                <field name="sale_order_id" invisible="1"/>
                <field name="pricing_type" invisible="1"/>
            </xpath>
            <xpath expr="//div[hasclass('o_kanban_manage_reporting')]" position="inside">
                <div role="menuitem" t-if="record.rating_active.raw_value" groups="project.group_project_manager">
                   <a name="action_view_all_rating" type="object">
                    Customer Ratings
                    </a>
                </div> 
            </xpath>
            <xpath expr="//div[hasclass('o_kanban_manage_view')]" position="inside">
                <div t-if="record.allow_billable.raw_value and record.sale_order_id.raw_value and record.pricing_type.raw_value != 'task_rate'"
                    role="menuitem"
                    groups="sales_team.group_sale_salesman_all_leads">
                    <a name="action_view_so" type="object">Sales Order</a>
                </div>
            </xpath>
        </field>
    </record>

        <record id="view_sale_service_inherit_form2" model="ir.ui.view">
            <field name="name">sale.service.form.view.inherit</field>
            <field name="model">project.task</field>
            <field name="groups_id" eval="[(4, ref('base.group_user'))]"/>
            <field name="inherit_id" ref="project.view_task_form2"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position='inside'>
                    <field name="allow_billable" invisible="1"/>
                </xpath>
            </field>
        </record>

        <record id="project_task_view_form_inherit_sale_timesheet" model="ir.ui.view">
            <field name="name">project.task.form.inherit.timesheet</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_form2"/>
            <field name="groups_id" eval="[(6,0, (ref('hr_timesheet.group_hr_timesheet_user'),))]"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='timesheet_ids']/tree" position="attributes">
                    <attribute name="decoration-muted">timesheet_invoice_id != False</attribute>
                </xpath>
                <xpath expr="//field[@name='user_ids']" position="after">
                    <field name="is_project_map_empty" invisible="1"/>
                    <field name="has_multi_sol" invisible="1"/>
                </xpath>
                 <xpath expr="//field[@name='partner_phone']" position="after">
                    <field name="pricing_type" invisible="1"/>
                </xpath>
                <xpath expr="//field[@name='timesheet_ids']" position="attributes">
                    <attribute name="widget">so_line_one2many</attribute>
                </xpath>
                <xpath expr="//field[@name='timesheet_ids']/tree" position="inside">
                    <field name="is_so_line_edited" invisible="1" />
                </xpath>
                <xpath expr="//field[@name='timesheet_ids']/tree/field[@name='unit_amount']" position="before">
                    <field name="timesheet_invoice_id" invisible="1"/>
                    <field name="so_line"
                        attrs="{'column_invisible': [('parent.allow_billable', '=', False)]}"
                        context="{'with_remaining_hours': True, 'with_price_unit': True}" options="{'no_create': True, 'no_open': True}"
                        domain="[('is_service', '=', True), ('order_partner_id', 'child_of', parent.commercial_partner_id), ('is_expense', '=', False), ('state', 'in', ['sale', 'done'])]"
                        optional="hide"/>
                </xpath>
                <xpath expr="//field[@name='remaining_hours']" position="after">
                    <field name="remaining_hours_available" invisible="1"/>
                    <span id="remaining_hours_so_label" attrs="{'invisible': ['|', '|', '|', '|', ('allow_billable', '=', False), ('sale_order_id', '=', False), ('partner_id', '=', False), ('sale_line_id', '=', False), ('remaining_hours_available', '=', False)]}">
                        <label class="font-weight-bold" for="remaining_hours_so" string="Remaining Hours on SO"
                               attrs="{'invisible': ['|', ('encode_uom_in_days', '=', True), ('remaining_hours_so', '&lt;', 0)]}"/>
                        <label class="font-weight-bold" for="remaining_hours_so" string="Remaining Days on SO"
                               attrs="{'invisible': ['|', ('encode_uom_in_days', '=', False), ('remaining_hours_so', '&lt;', 0)]}"/>
                        <label class="font-weight-bold text-danger" for="remaining_hours_so" string="Remaining Hours on SO"
                               attrs="{'invisible': ['|', ('encode_uom_in_days', '=', True), ('remaining_hours_so', '&gt;=', 0)]}"/>
                        <label class="font-weight-bold text-danger" for="remaining_hours_so" string="Remaining Days on SO"
                               attrs="{'invisible': ['|', ('encode_uom_in_days', '=', False), ('remaining_hours_so', '&gt;=', 0)]}"/>
                    </span>
                    <field name="remaining_hours_so" nolabel="1" widget="timesheet_uom" attrs="{'invisible': ['|', '|', '|', '|', ('allow_billable', '=', False), ('sale_order_id', '=', False), ('partner_id', '=', False), ('sale_line_id', '=', False), ('remaining_hours_available', '=', False)]}"></field>
                </xpath>
            </field>
        </record>

        <record id="project_task_view_form_inherit_sale_timesheet_editable" model="ir.ui.view">
            <field name="name">project.task.form.view.form.inherit.sale.timesheet.editable</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project_task_view_form_inherit_sale_timesheet"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='timesheet_ids']/tree/field[@name='so_line']" position="attributes">
                    <attribute name="options">{'no_create': True}</attribute>
                </xpath>
            </field>
            <field name="groups_id" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
        </record>

        <record id="view_task_form2_inherit_sale_timesheet" model="ir.ui.view">
            <field name="name">view.task.form2.inherit</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_form2"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='sale_line_id']" position="attributes">
                    <attribute name="context">{'with_remaining_hours': True, 'with_price_unit': True}</attribute>
                    <attribute name="attrs">
                        {'invisible': ['|', ('allow_billable', '=', False), ('partner_id', '=', False)]}
                    </attribute>
                </xpath>
                <xpath expr="//field[@name='sale_order_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
            </field>
        </record>
</odoo>
