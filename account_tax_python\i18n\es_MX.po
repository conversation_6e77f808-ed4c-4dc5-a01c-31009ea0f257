# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"Grupo de impuestos: el impuesto es un conjunto de sub impuestos.\n"
"     - Fijo: el importe del impuesto permanece igual independientemente del precio.\n"
"     - Porcentaje del precio: el monto del impuesto es un % del precio:\n"
"         Por ejemplo, 100 * (1 + 10%) = 110 (sin precio incluido)\n"
"         por ejemplo, 110 / (1 + 10%) = 100 (precio incluido)\n"
"     - Porcentaje del precio impuesto incluido: El monto del impuesto es una división del precio:\n"
"         Por ejemplo, 180 / (1 - 10%) = 200 (sin precio incluido)\n"
"         Por ejemplo, 200 * (1-10%) =  180  (precio incluido)"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_applicable
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_applicable
msgid "Applicable Code"
msgstr "Código aplicable"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Calcule el importe del impuesto estableciendo la variable 'result'.\n"
"\n"
":parámetro base_amount: número con decimales, importe real sobre el que el impuesto se aplica\n"
":parámetro price_unit: número con decimales\n"
":parámetro quantity: número con decimales\n"
":parámetro company: recordset unitario de res.company\n"
":parámetro product: recordset unitario de product.product o None\n"
":parámetro partner: recordset unitario de res.partner o None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Calcule el importe del impuesto estableciendo la variable 'result'.\n"
"\n"
":parámetro base_amount: número con decimales, importe real sobre el que el impuesto se aplica\n"
":parámetro price_unit: número con decimales\n"
":parámetro quantity: número con decimales\n"
":parámetro company: recordset unitario de res.company\n"
":parámetro product: recordset unitario de product.product o None\n"
":parámetro partner: recordset unitario de res.partner o None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Determina si el impuesto será aplicado estableciendo la variable 'result' a True o False.\n"
"\n"
":parámetro price_unit: número con decimales\n"
":parámetro quantity: número con decimales\n"
":parámetro company: recordset unitario de res.company\n"
":parámetro product: recordset unitario de product.product o None\n"
":parámetro partner: recordset unitario de res.partner o None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Determine si se aplicará el impuesto estableciendo la variable 'result' a True o False.\n"
"\n"
":parámetro price_unit: número con decimales\n"
":parámetro quantity: número con decimales\n"
":parámetro company: recordset unitario de res.company\n"
":parámetro product: recordset unitario de product.product o None\n"
":parámetro partner: recordset unitario de res.partner o None"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_compute
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_compute
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax_template__amount_type__code
msgid "Python Code"
msgstr "Código Python"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Impuesto"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__amount_type
msgid "Tax Computation"
msgstr "Cálculo de impuestos"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Plantilla de impuestos"

#. module: account_tax_python
#: code:addons/account_tax_python/models/account_tax.py:0
#: code:addons/account_tax_python/models/account_tax.py:0
#, python-format
msgid ""
"You entered invalid code %r in %r taxes\n"
"\n"
"Error : %s"
msgstr ""
"Ingresó el código %r que no es válido en los impuestos %r \n"
"\n"
"Error: %s"
