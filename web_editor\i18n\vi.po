# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_editor
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Tr<PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Vo Thanh Thuy, 2022
# <PERSON> Tran <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-10 08:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%dpx (Original)"
msgstr "%dpx (Original)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%dpx (Suggested)"
msgstr "%dpx (Suggested)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%spx"
msgstr "%spx"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr ""
"'Tiêu đề Thẻ' được hiển thị dưới dạng chú giải công cụ khi bạn di chuột qua "
"ảnh."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(ALT Tag)"
msgstr "(ALT Tag)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(TITLE Tag)"
msgstr "(TITLE Tag)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(URL or Embed)"
msgstr "(URL hoặc được nhúng)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "100%"
msgstr "100%"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "1977"
msgstr "1977"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "1x"
msgstr "1x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "25"
msgstr "25"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "25%"
msgstr "25%"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "2x"
msgstr "2x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "3x"
msgstr "3x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "4x"
msgstr "4x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "50%"
msgstr "50%"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "5x"
msgstr "5x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "90"
msgstr "90"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ml-1 text-white-50\">%</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ml-1 text-white-50\">deg</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"mr-2 ml-3\">Y</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"mr-2\">X</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Blocks</span>"
msgstr "<span>Khối</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Style</span>"
msgstr "<span>Style</span>"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"A server error occured. Please check you correctly signed in and that the "
"file you are saving is correctly formatted."
msgstr ""
"Đã xảy ra lỗi máy chủ. Vui lòng kiểm tra xem bạn đã đăng nhập chính xác hay "
"chưa và file bạn đang lưu được định dạng đúng."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Above"
msgstr "Trên"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Accepts"
msgstr "Chấp nhận"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media_dialog.js:0
#, python-format
msgid "Add"
msgstr "Thêm"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Column"
msgstr "Thêm cột"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Row"
msgstr "Thêm dòng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Add URL"
msgstr "Thêm URL"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a blockquote section."
msgstr "Thêm một blockquote section."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a button."
msgstr "Thêm một nút."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a code section."
msgstr "Thêm một code section."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a column left"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a column right"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a link."
msgstr "Thêm một liên kết."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a row above"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a row below"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Add document"
msgstr "Thêm tài liệu"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Aden"
msgstr "Aden"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy & Zigs"
msgstr "Airy & Zigs"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Center"
msgstr "Căn giữa"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Left"
msgstr "Căn trái"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Right"
msgstr "Căn phải"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alignment"
msgstr "Căn chỉnh"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "All"
msgstr "Tất cả"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "All SCSS Files"
msgstr "Tất cả SCSS Files"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "All images have been loaded"
msgstr "Tất cả hình ảnh đã được tải"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alt tag"
msgstr "Thẻ Alt"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Angle"
msgstr "Góc độ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Animated"
msgstr "Animated"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Apply"
msgstr "Áp dụng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Are you sure you want to delete the snippet: %s ?"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "Are you sure you want to delete this file ?"
msgstr "Bạn có chắc chắn muốn xóa tệp này không?"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Aspect Ratio"
msgstr "Tỷ lệ khung hình"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_assets
msgid "Assets Utils"
msgstr "Assets Utils"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_attachment
msgid "Attachment"
msgstr "Đính kèm"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__local_url
msgid "Attachment URL"
msgstr "URL Đính kèm"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Auto"
msgstr "Tự động"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoconvert to relative link"
msgstr "Tự động chuyển đổi sang liên kết tương ứng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoplay"
msgstr "Chơi tự động"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background"
msgstr "Nền"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Background Color"
msgstr "Màu nền"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Position"
msgstr "Vị trí nền"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Basic blocks"
msgstr "Các khối cơ bản"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Basics"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Below"
msgstr "Dưới"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Big section heading."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blobs"
msgstr "Đốm màu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Block"
msgstr "Khoá"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blocks & Rainy"
msgstr "Khối & mưa"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Blur"
msgstr "Làm mờ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Bold"
msgstr "In Đậm"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Color"
msgstr "Border Color"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Style"
msgstr "Kiểu viền"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Width"
msgstr "Border Width"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brannan"
msgstr "Brannan"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brightness"
msgstr "Độ sáng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Bulleted list"
msgstr "Danh sách có dấu đầu dòng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Button"
msgstr "Nút"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Cancel"
msgstr "Hủy"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Careful !"
msgstr "Cẩn thận !"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Center"
msgstr "Trung tâm"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.js:0
#, python-format
msgid "Change media description and tooltip"
msgstr "Thay đổi mô tả và tooltip của media"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Checklist"
msgstr "Checklist"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Choose a record..."
msgstr "Chọn một tập dữ liệu..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Close"
msgstr "Đóng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Code"
msgstr "Mã"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_color_widget
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Color"
msgstr "Màu sắc"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Column"
msgstr "Cột"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Common colors"
msgstr "Các màu Phổ thông"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Confirm"
msgstr "Xác nhận"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Confirmation"
msgstr "Xác nhận"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Content conflict"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Contrast"
msgstr "Contrast"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Copy Link"
msgstr "Sao chép Link"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Copy-paste your URL or embed code here"
msgstr "Sao chép-dán URL của bạn hoặc mã nhúng tại đây"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Could not install module <strong>%s</strong>"
msgstr "Không thể cài đặt module <strong>%s</strong>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Cover"
msgstr "Cover"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Create"
msgstr "Tạo"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a list with numbering."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a simple bulleted list."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create an URL."
msgstr "Tạo một URL."

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_date
msgid "Created on"
msgstr "Thời điểm tạo"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Crop Image"
msgstr "Cắt ảnh"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Custom"
msgstr "Tùy chỉnh"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom %s"
msgstr "Chỉnh %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dailymotion"
msgstr "Hàng ngày"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dashed"
msgstr "Vượt qua"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Default"
msgstr "Mặc định"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Default + Rounded"
msgstr "Mặc định + Làm tròn"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Define a custom gradient"
msgstr "Xác định một gradient tùy chỉnh"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Delete %s"
msgstr "Xóa %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Delete current table"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Description"
msgstr "Mô tả"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/dialog.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Discard record"
msgstr "Hủy bản ghi"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Do you want to install the %s App?"
msgstr "Bạn có muốn cài đặt Ứng dụng %s không?"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Document"
msgstr "Tài liệu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dotted"
msgstr "Say mê"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Double"
msgstr "Gấp đôi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Double-click to edit"
msgstr "Bấm đúp để sửa"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Duplicate Container"
msgstr "Nhân bản Khung chứa"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Dynamic Colors"
msgstr "Màu động"

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "ERROR: couldn't get download urls from media library."
msgstr "LỖI: không thể lấy url tải xuống từ thư viện phương tiện."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "EarlyBird"
msgstr "EarlyBird"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Edit Link"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Edit image"
msgstr "Sửa ảnh"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Edit media description"
msgstr "Chỉnh sửa mô tả phương tiện"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."
msgstr ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Image"
msgstr "Hình ảnh được nhúng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Youtube Video"
msgstr "Video Youtube được nhúng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the image in the document."
msgstr "Đã nhúng hình ảnh vào tài liệu."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the youtube video in the document."
msgstr "Đã nhúng video Youtube vào tài liệu."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Empty quote"
msgstr "Trích dẫn trống"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Expected "
msgstr "Dự kiến "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest corner"
msgstr "Mở rộng đến góc gần nhất"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest side"
msgstr "Mở rộng đến phía gần nhất"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest corner"
msgstr "Mở rộng đến góc xa nhất"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest side"
msgstr "Mở rộng đến phía xa nhất"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "File could not be saved"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "File has been uploaded"
msgstr "Tệp đã được tải lên"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill"
msgstr "Điền vào"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill + Rounded"
msgstr "Fill + Rounded"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Filter"
msgstr "Bộ lọc"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "First Panel"
msgstr "First Panel"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flat"
msgstr "Mặt bằng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid "Flexible"
msgstr "Linh hoạt"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Horizontal"
msgstr "Flip Horizontal"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Vertical"
msgstr "Flip Vertical"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Floating shapes"
msgstr "Hình nổi"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Floats"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font Color"
msgstr "Màu Font"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font size"
msgstr "Cỡ chữ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "For technical reasons, this block cannot be dropped here"
msgstr "Vì lý do kỹ thuật, không thể thả khối này ở đây"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Format"
msgstr "Định dạng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Fullscreen"
msgstr "Toàn màn hình"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"Get the perfect image by searching in our library of copyright free photos "
"and illustrations."
msgstr ""
"Chọn lấy một hình ảnh hoàn hảo bằng cách tìm kiếm trong thư viện ảnh và hình"
" minh họa miễn phí bản quyền của chúng tôi."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Gradient"
msgstr "Gradient"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 1"
msgstr "Tiêu đề 1"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 2"
msgstr "Tiêu đề 2"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 3"
msgstr "Tiêu đề 3"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 4"
msgstr "Tiêu đề 4"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 5"
msgstr "Tiêu đề 5"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 6"
msgstr "Tiêu đề 6"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 1"
msgstr "Heading 1"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 2"
msgstr "Heading 2"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 3"
msgstr "Heading 3"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 4"
msgstr "Tiêu đề 4"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 5"
msgstr "Tiêu đề 5"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 6"
msgstr "Tiêu đề 6"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide Dailymotion logo"
msgstr "Ẩn biểu tượng Dailymotion"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide Youtube logo"
msgstr "Ẩn biểu tượng Youtube"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide fullscreen button"
msgstr "Ẩn nút Đầy màn hình"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide player controls"
msgstr "Ẩn nút điều khiển"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide sharing button"
msgstr "Ẩn nút chia sẽ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/backend/field_html.js:0
#, python-format
msgid "Html"
msgstr "Html"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__id
msgid "ID"
msgstr "ID"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon"
msgstr "Biểu tượng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon Formatting"
msgstr "Định dạng biểu tượng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 1x"
msgstr "Kích thước biểu tượng 1x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 2x"
msgstr "Kích thước biểu tượng 2x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 3x"
msgstr "Kích thước biểu tượng 3x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 4x"
msgstr "Kích thước biểu tượng 4x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 5x"
msgstr "Kích thước biểu tượng 5x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"Nếu bạn hủy các chỉnh sửa hiện tại, tất cả các thay đổi chưa được lưu sẽ bị "
"mất. Bạn có thể hủy để quay lại chế độ chỉnh sửa."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"If you reset this file, all your customizations will be lost as it will be "
"reverted to the default file."
msgstr ""
"Nếu bạn cài lại file này, tất cả các tùy chỉnh của bạn sẽ bị mất vì nó sẽ bị"
" đảo lại như file mặc định."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Illustrations"
msgstr "Hình minh hoạ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "Image"
msgstr "Hình ảnh"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Image Formatting"
msgstr "Định dạng hình ảnh"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_height
msgid "Image Height"
msgstr "Chiều cao ảnh"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_src
msgid "Image Src"
msgstr "Nguồn ảnh"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_width
msgid "Image Width"
msgstr "Chiều rộng ảnh"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Image padding"
msgstr "Padding hình ảnh"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Inkwell"
msgstr "Lọ mực"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Inline Text"
msgstr "Văn bản nội tuyến"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert a table."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert a video."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert an horizontal rule separator."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert an image."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert media"
msgstr "Chèn đa phương tiện"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert or edit link"
msgstr "Chèn hoặc chỉnh sửa liên kết"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert table"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install"
msgstr "Cài đặt"

#. module: web_editor
#: code:addons/web_editor/models/ir_ui_view.py:0
#, python-format
msgid "Invalid field value for %s: %s"
msgstr "Invalid field value for %s: %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install %s"
msgstr "Cài đặt %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install in progress"
msgstr "Đang cài đặt"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invisible Elements"
msgstr "Yếu tố vô hình"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Item"
msgstr "Mục"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "JS"
msgstr "JS"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "JS file: %s"
msgstr "JS file: %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Large"
msgstr "Rộng"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Left"
msgstr "Trái"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Linear"
msgstr "Đường thẳng"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Lines"
msgstr "Dòng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Link"
msgstr "Liên kết"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Link Label"
msgstr "Nhãn Liên kết"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "Link copied to clipboard."
msgstr "Liên kết được sao chép vào khay nhớ tạm."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Link to"
msgstr "Liên kết đến"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "List"
msgstr "Danh sách"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Load more..."
msgstr "Tải thêm..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Loop"
msgstr "Loop"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Main Color"
msgstr "Màu chính"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Maven"
msgstr "Maven"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Media"
msgstr "Truyền thông"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Medias"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Medium"
msgstr "Kênh trung gian"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Medium section heading."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "More info about this app."
msgstr "Thông tin thêm về ứng dụng này."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "My Images"
msgstr "Hình ảnh của tôi"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__name
msgid "Name"
msgstr "Tên"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Navigation"
msgstr "Điều hướng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No"
msgstr "Không"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "No URL specified"
msgstr "Không có URL nào được chỉ định"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "No documents found."
msgstr "Không tìm thấy tài liệu nào."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "No images found."
msgstr "Không tìm thấy hình ảnh."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No location to drop in"
msgstr "Không có điểm để thả vào"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "No more records"
msgstr "Không có thêm tập dữ liệu nào"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/powerbox/Powerbox.js:0
#, python-format
msgid "No results"
msgstr "Không có kết quả"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "None"
msgstr "Không dùng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Normal"
msgstr "Thông thường"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Numbered list"
msgstr "Danh sách được đánh số"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Custom SCSS Files"
msgstr "Only Custom SCSS Files"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Page SCSS Files"
msgstr "Only Page SCSS Files"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Views"
msgstr "Only Views"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Open in a new tab"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Open in new window"
msgstr "Mở trong cửa sổ mới"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Optimized"
msgstr "Tối ưu hóa"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "Tệp đính kèm gốc (chưa tối ưu hóa, chưa định kích thước)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Origins"
msgstr "Gốc"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline"
msgstr "Mục lục"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline + Rounded"
msgstr "Outline + Rounded"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Padding"
msgstr "Padding"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paragraph block."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paste as URL"
msgstr "Dán dưới dạng URL"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Patterns"
msgstr "Hoa văn"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Pictogram"
msgstr "Pictogram"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Position"
msgstr "Vị trí"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Preview"
msgstr "Xem trước"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Primary"
msgstr "Chủ đạo"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__c
msgid "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"
msgstr "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__a
msgid "Qu'il n'est pas arrivé à Toronto"
msgstr "Qu'il n'est pas arrivé à Toronto"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__b
msgid "Qu'il était supposé arriver à Toronto"
msgstr "Qu'il était supposé arriver à Toronto"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Quality"
msgstr "Chất lượng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Quote"
msgstr "Trích dẫn"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "Qweb Field"
msgstr "Qweb Field"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Liên hệ lĩnh vực Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "Qweb Field Date"
msgstr "Trường ngày Qwe"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "Qweb Field Datetime"
msgstr "Qweb Field Datetime"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "Qweb Field Duration"
msgstr "Qweb Field Duration"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "Qweb Field Float"
msgstr "Qweb Field Float"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr "Qweb Field HTML"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "Trường ảnh Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "Qweb Field Integer"
msgstr "Qweb Field Integer"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "Qweb Field Many to One"
msgstr "Qweb Field Many to One"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "Qweb Field Monetary"
msgstr "Qweb trường Monetary"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "Qweb Field Relative"
msgstr "Qweb Field Relative"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "Qweb Field Selection"
msgstr "Qweb Field Selection"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "Qweb Field Text"
msgstr "Qweb Field Text"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "Qweb Field qweb"
msgstr "Qweb Field qweb"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Radial"
msgstr "Xuyên tâm"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Readonly field"
msgstr "Trường chỉ đọc"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "Redirect the user elsewhere when he clicks on the media."
msgstr "Chuyển hướng người dùng đến nơi khác sau khi nhấp vào đa phương tiện."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove (DELETE)"
msgstr "Gỡ (XOÁ)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Remove Block"
msgstr "Gỡ Khối"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove Current"
msgstr "Gỡ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Remove Link"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Remove Selected Color"
msgstr "Gỡ màu đã chọn"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove current column"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove current row"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove format"
msgstr "Gỡ định dạng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove link"
msgstr "Xoá liên kết"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Rename %s"
msgstr "Đặt lại tên %s"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Repeat pattern"
msgstr "Lặp lại mẫu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Replace"
msgstr "Thay thế"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Replace media"
msgstr "Media thay thế"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Reset"
msgstr "Đặt lại"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Reset Image"
msgstr "Đặt lại ảnh"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset crop"
msgstr "Đặt lại tuỳ chọn cắt"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset transformation"
msgstr "Đặt lại tuỳ chọn chuyển đổi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Reseting views is not supported yet"
msgstr "Cài lại các chế độ xem chưa được hỗ trợ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Auto"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Full"
msgstr "Đổi kích thước đủ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Half"
msgstr "Đổi kích thước 1/2"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Quarter"
msgstr "Đổi kích thước 1/4"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Right"
msgstr "Phải"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Left"
msgstr "Xoay trái"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Right"
msgstr "Xoay phải"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Row"
msgstr "Dòng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "SCSS (CSS)"
msgstr "SCSS (CSS)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "SCSS file: %s"
msgstr "SCSS file: %s"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Saturation"
msgstr "Bão hòa"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/dialog.js:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Save"
msgstr "Lưu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Save and Install"
msgstr "Lưu và cài đặt"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save and Reload"
msgstr "Lưu và tải lại"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Save record"
msgstr "Lưu bản ghi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search"
msgstr "Tìm kiếm"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search a document"
msgstr "Tìm kiếm tài liệu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search a pictogram"
msgstr "Tìm kiếm từ tượng hình"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search an image"
msgstr "Tìm kiếm hình ảnh"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search for records..."
msgstr "Tìm kiếm tập dữ liệu..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search more..."
msgstr "Search more..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search to show more records"
msgstr "Tìm kiếm để hiển thị thêm tập dữ liệu"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Search..."
msgstr "Tìm..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Secondary"
msgstr "Secondary"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media_dialog.js:0
#, python-format
msgid "Select a Media"
msgstr "Chọn một Media"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Select a block on your page to style it."
msgstr "Chọn một khối trên trang của bạn để bắt đầu tạo kiểu."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Separator"
msgstr "Phần ngăn cách"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Sepia"
msgstr "Sepia"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Server error"
msgstr "Lỗi máy chủ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shadow"
msgstr "Đổ bóng"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Shape"
msgstr "Hình dáng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Circle"
msgstr "Hình: Tròn"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Rounded"
msgstr "Hình: Bo tròn"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Thumbnail"
msgstr "Hình: Thu nhỏ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Show optimized images"
msgstr "Show optimized images"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Size"
msgstr "Kích thước"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 1x"
msgstr "Kích thước 1x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 2x"
msgstr "Kích thước 2x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 3x"
msgstr "Kích thước 3x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 4x"
msgstr "Kích thước 4x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 5x"
msgstr "Kích thước 5x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Small"
msgstr "Nhỏ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Small section heading."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Solid"
msgstr "Rắn"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Solids"
msgstr "Solids"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Specials"
msgstr "Specials"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Style"
msgstr "Phong cách"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Suggestions"
msgstr "Đề xuất"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch direction"
msgstr "Chuyển hướng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch the text's direction."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Table"
msgstr "Bàn"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Table Options"
msgstr "Tuỳ chọn bảng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Table tools"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Template ID: %s"
msgstr "Template ID: %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Text"
msgstr "Văn bản"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text align"
msgstr "Căn chỉnh văn bản"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text style"
msgstr "Kiểu văn bản"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "The URL does not seem to work."
msgstr "URL dường như không hoạt động."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "The URL seems valid."
msgstr "URL có vẻ hợp lệ."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"The image could not be deleted because it is used in the\n"
"               following pages or views:"
msgstr ""
"Không thể xóa hình ảnh vì nó được sử dụng trong\n"
"               các trang sau hoặc dạng xem sau:"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "The provided url does not reference any supported video"
msgstr "The provided url does not reference any supported video"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "The provided url is not valid"
msgstr "The provided url is not valid"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid ""
"The version from the database will be used.\n"
"                    If you need to keep your changes, copy the content below and edit the new document."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Theme"
msgstr "Chủ đề"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Theme colors"
msgstr "Màu sắc Theme"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "There is a conflict between your version and the one in the database."
msgstr "Có xung đột giữa phiên bản của bạn và phiên bản trong cơ sở dữ liệu."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "This URL is invalid. Preview couldn't be updated."
msgstr "URL này không hợp lệ. Không thể cập nhật bản xem trước."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "This block is outdated"
msgstr "Khối này đã lỗi thời"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "This document is not saved!"
msgstr "Tài liệu này chưa được lưu!"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This file is a public view attachment."
msgstr "This file is a public view attachment."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This file is attached to the current record."
msgstr "This file is attached to the current record."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid "This image is an external image"
msgstr "This image is an external image"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid ""
"This type of image is not supported for cropping.<br/>If you want to crop "
"it, please first download it from the original source and upload it in Odoo."
msgstr ""
"This type of image is not supported for cropping.<br/>If you want to crop "
"it, please first download it from the original source and upload it in Odoo."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Title"
msgstr "Xưng hô"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Title tag"
msgstr "Thẻ tiêu đề"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"To apply this change, we need to save all your previous modifications and "
"reload the page."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid ""
"To make changes, drop this block and use the new options in the last "
"version."
msgstr ""
"To make changes, drop this block and use the new options in the last "
"version."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"To save a snippet, we need to save all your previous modifications and "
"reload the page."
msgstr ""
"Để lưu đoạn snippet, chúng tôi cần lưu tất cả các sửa đổi trước đó của bạn "
"và tải lại trang này."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "To-do"
msgstr "Việc cần làm"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Toaster"
msgstr "Toaster"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle bold"
msgstr "Chuyển đổi in đậm"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle checklist"
msgstr "Toggle checklist"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle icon spin"
msgstr "Toggle icon spin"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle italic"
msgstr "Toggle italic"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle ordered list"
msgstr "Toggle ordered list"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle strikethrough"
msgstr "Toggle strikethrough"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle underline"
msgstr "Toggle underline"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle unordered list"
msgstr "Toggle unordered list"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Tooltip"
msgstr "Tooltip"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Track tasks with a checklist."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform"
msgstr "Chuyển đổi"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform the picture"
msgstr "Chuyển đổi ảnh"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Transform the picture (click twice to reset transformation)"
msgstr "Chuyển đổi hình ảnh (nhấp hai lần để thiết lập lại chuyển đổi)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:0
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Translate"
msgstr "Dịch"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_translation
msgid "Translation"
msgstr "Bản dịch"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Transparent colors"
msgstr "Transparent colors"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Type"
msgstr "Loại"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Type \"/\" for commands"
msgstr "Nhập \"/\" cho các lệnh"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "URL or Email"
msgstr "URL or Email"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Unalign"
msgstr "Hủy căn chỉnh"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Unexpected "
msgstr "Unexpected "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Upload a document"
msgstr "Tải lên 1 tài liệu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Upload an image"
msgstr "Tải lên một hình"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Uploaded image's format is not supported. Try with:"
msgstr ""

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "Uploaded image's format is not supported. Try with: %s"
msgstr "Định dạng của hình ảnh đã tải lên không được hỗ trợ. Hãy thử với: %s"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Valencia"
msgstr "Valencia"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Video"
msgstr "Video"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Video Formatting"
msgstr "Định dạng video"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Video code"
msgstr "Video code"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Videos are muted when autoplay is enabled"
msgstr "Videos are muted when autoplay is enabled"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "View"
msgstr "Xem"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Views and Assets bundles"
msgstr "Views and Assets bundles"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Vimeo"
msgstr "Vimeo"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Walden"
msgstr "Walden"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid ""
"Warning: after closing this dialog, the version you were working on will be "
"discarded and will never be available anymore."
msgstr ""
"Cảnh báo: sau khi đóng hộp thoại này, phiên bản bạn đang làm sẽ bị loại bỏ "
"và không còn tồn tại."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Wavy"
msgstr "Gợn sóng"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "Web Editor Converter Subtest"
msgstr "Web Editor Converter Subtest"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "Web Editor Converter Test"
msgstr "Web Editor Converter Test"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Width"
msgstr "Chiều rộng"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "XL"
msgstr "XL"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "XML (HTML)"
msgstr "XML (HTML)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Xpro"
msgstr "Xpro"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Yes"
msgstr "Có"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"You can upload documents with the button located in the top left of the "
"screen."
msgstr ""
"You can upload documents with the button located in the top left of the "
"screen."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"You can upload images with the button located in the top left of the screen."
msgstr ""
"You can upload images with the button located in the top left of the screen."

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "You need to specify either data or url to create an attachment."
msgstr "You need to specify either data or url to create an attachment."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Youku"
msgstr "Youku"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Your URL"
msgstr "URL của bạn"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Youtube"
msgstr "Youtube"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom In"
msgstr "Phóng to"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom Out"
msgstr "Thu nhỏ"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "add"
msgstr "add"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "and"
msgstr "và"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "auto"
msgstr "tự động"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "darken"
msgstr "darken"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "default"
msgstr "mặc định"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "exclusion"
msgstr "exclusion"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "https://www.odoo.com/logo.png"
msgstr "https://www.odoo.com/logo.png"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "https://www.odoo.com/mydocument"
msgstr "https://www.odoo.com/mydocument"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "lighten"
msgstr "lighten"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "multiply"
msgstr "multiply"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "overlay"
msgstr "overlay"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "px"
msgstr "px"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "screen"
msgstr "screen"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "videos"
msgstr "videos"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "www.example.com"
msgstr "www.example.com"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Autoconvert to relative link"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Border"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Color filter"
msgstr "⌙ Color filter"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Colors"
msgstr "⌙ Colors"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Fill Color"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Flip"
msgstr "⌙ Flip"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Height"
msgstr "⌙ Height"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Image"
msgstr "⌙ Image"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Link Label"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Main Color"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Open in new window"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Position"
msgstr "⌙ Position"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "⌙ Shape"
msgstr "⌙ Shape"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Size"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Style"
msgstr "⌙ Style"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Text Color"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Width"
msgstr "⌙ Width"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "⌙ Your URL"
msgstr ""
