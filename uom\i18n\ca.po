# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* uom
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2021
# <PERSON> <<EMAIL>>, 2021
# j<PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
msgid ""
"<span class=\"oe_grey oe_inline\">\n"
"                                    e.g: 1*(reference unit)=ratio*(this unit)\n"
"                                </span>"
msgstr ""
"<span class=\"oe_grey oe_inline\">\n"
"                                  per exemple: 1(unitat de referència)=ratio(aquesta unitat)\n"
"                                </span>"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
msgid ""
"<span class=\"oe_grey oe_inline\">\n"
"                                    e.g: 1*(this unit)=ratio*(reference unit)\n"
"                                </span>"
msgstr ""
"<span class=\"oe_grey oe_inline\">\n"
"                                   per exemple: 1(aquesta unitat)=ratio(unitat de referència)\n"
"                                </span>"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__active
msgid "Active"
msgstr "Actiu"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_form_action
msgid "Add a new unit of measure"
msgstr "Afegeix una nova unitat de mesura"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_categ_form_action
msgid "Add a new unit of measure category"
msgstr "Afegeix una nova unitat de categoria de mesura"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Archived"
msgstr "Arxivat"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__factor_inv
msgid "Bigger Ratio"
msgstr "Major ràtio"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__bigger
msgid "Bigger than the reference Unit of Measure"
msgstr "Més gran que la unitat de mesura de referència"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__category_id
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Category"
msgstr "Categoria"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__color
msgid "Color"
msgstr "Color"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__ratio
msgid "Combined Ratio"
msgstr "Ràtio combinat"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"La conversió entre les unitats de mesura només és possible si pertanyen a la"
" mateixa categoria. La conversió es basarà en les ràtios establertes."

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__create_uid
#: model:ir.model.fields,field_description:uom.field_uom_uom__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__create_date
#: model:ir.model.fields,field_description:uom.field_uom_uom__create_date
msgid "Created on"
msgstr "Creat el"

#. module: uom
#: model:uom.uom,name:uom.product_uom_day
msgid "Days"
msgstr "Dies"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__display_name
#: model:ir.model.fields,field_description:uom.field_uom_uom__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: uom
#: model:uom.uom,name:uom.product_uom_dozen
msgid "Dozens"
msgstr "Desenes"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_category__reference_uom_id
msgid "Dummy field to keep track of reference uom change"
msgstr "Camp simulat per mantenir la pista del canvi d'uom de referència"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Group By"
msgstr "Agrupar per"

#. module: uom
#: model:uom.uom,name:uom.product_uom_hour
msgid "Hours"
msgstr "Hores"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__factor_inv
msgid ""
"How many times this Unit of Measure is bigger than the reference Unit of "
"Measure in this category: 1 * (this unit) = ratio * (reference unit)"
msgstr ""
"Quants cops la Unitat de Mesura és més gran que la unitat de mesura de "
"referència en aquesta categoria: 1 * (aquesta unitat) = rati * (unitat de "
"referència)"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__factor
msgid ""
"How much bigger or smaller this unit is compared to the reference Unit of "
"Measure for this category: 1 * (reference unit) = ratio * (this unit)"
msgstr ""
"Com de gran o de petita és aquesta unitat comparada amb la unitat de mesura "
"de referència d'aquesta categoria: 1 * (unitat de referència) = rati * "
"(aquesta unitat)"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__id
#: model:ir.model.fields,field_description:uom.field_uom_uom__id
msgid "ID"
msgstr "ID"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category____last_update
#: model:ir.model.fields,field_description:uom.field_uom_uom____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__write_uid
#: model:ir.model.fields,field_description:uom.field_uom_uom__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__write_date
#: model:ir.model.fields,field_description:uom.field_uom_uom__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: uom
#: model:uom.category,name:uom.uom_categ_length
msgid "Length / Distance"
msgstr "Longitud / Distància"

#. module: uom
#: model:res.groups,name:uom.group_uom
msgid "Manage Multiple Units of Measure"
msgstr "Gestionar múltiples unitats de mesura"

#. module: uom
#: model:ir.model,name:uom.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unitat de mesura del producte"

#. module: uom
#: model:ir.model,name:uom.model_uom_category
msgid "Product UoM Categories"
msgstr "Categories de producte UdM"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__factor
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_form_view
msgid "Ratio"
msgstr "Ràtio"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__reference
msgid "Reference Unit of Measure for this category"
msgstr "Unitat de mesura de referència per aquesta categoria"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__reference_uom_id
msgid "Reference UoM"
msgstr "Referència UoM"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__rounding
msgid "Rounding Precision"
msgstr "Precisió d'arrodoniment"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Search UOM"
msgstr "Cerca UOM"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_categ_view_search
msgid "Search UoM Category"
msgstr "Buscar en la categoria UoM"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__smaller
msgid "Smaller than the reference Unit of Measure"
msgstr "Més petita que la unitat de mesura de referència"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"Some critical fields have been modified on %s.\n"
"Note that existing data WON'T be updated by this change.\n"
"\n"
"As units of measure impact the whole system, this may cause critical issues.\n"
"E.g. modifying the rounding could disturb your inventory balance.\n"
"\n"
"Therefore, changing core units of measure in a running database is not recommended."
msgstr ""
"Alguns camps crítics han estat modificats en %s.\n"
"Tingueu en compte que les dades existents no s'actualitzaran amb aquest canvi.\n"
"\n"
"Com que les unitats de mesura afecten a tot el sistema, això pot causar problemes crítics.\n"
"P. ex. modificar l'arrodoniment podria pertorbar el vostre balanç d'inventari.\n"
"\n"
"Per tant, no es recomana canviar les unitats de mesura del nucli en una base de dades en execució."

#. module: uom
#: model:uom.category,name:uom.uom_categ_surface
msgid "Surface"
msgstr "Superfície"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__rounding
msgid ""
"The computed quantity will be a multiple of this value. Use 1.0 for a Unit "
"of Measure that cannot be further split, such as a piece."
msgstr ""
"La quantitat calculada serà un múltiple d'aquest valor. Utilitzi 1.0 per "
"unitat de mesura que no pot ser dividida, com una peça."

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_factor_gt_zero
msgid "The conversion ratio for a unit of measure cannot be 0!"
msgstr "El ràtio de conversió per a una unitat de mesura no pot ser 0!"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"The following units of measure are used by the system and cannot be deleted: %s\n"
"You can archive them instead."
msgstr ""
"Les següents unitats de mesura són utilitzades pel sistema i no poden ser eliminades: %s\n"
"En lloc d'això, els podeu arxivar."

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_factor_reference_is_one
msgid "The reference unit must have a conversion factor equal to 1."
msgstr "La unitat de referència ha de tenir un factor de conversió igual a 1."

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_rounding_gt_zero
msgid "The rounding precision must be strictly positive."
msgstr "La precisió d'arrodoniment ha d'ésser positiva."

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"The unit of measure %s defined on the order line doesn't belong to the same "
"category as the unit of measure %s defined on the product. Please correct "
"the unit of measure defined on the order line or on the product, they should"
" belong to the same category."
msgstr ""
"La unitat de mesura %s definida a la línia de comandes no pertany a la "
"mateixa categoria que la unitat de mesura %s definit al producte. Si us "
"plau, corregeixin la unitat de mesura definida en la línia d'ordre o en el "
"producte, haurien de pertànyer a la mateixa categoria."

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__uom_type
msgid "Type"
msgstr "Tipus"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__active
msgid ""
"Uncheck the active field to disable a unit of measure without deleting it."
msgstr ""
"Desactivi el camp actiu per desactivar una unitat de mesura sense esborrar-"
"la."

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_unit
msgid "Unit"
msgstr "Unitat de venda"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__name
msgid "Unit of Measure"
msgstr "Unitat de mesura"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__name
msgid "Unit of Measure Category"
msgstr "Categoria d'unitats de mesura"

#. module: uom
#: model:uom.uom,name:uom.product_uom_unit
msgid "Units"
msgstr "Unitats"

#. module: uom
#: model:ir.actions.act_window,name:uom.product_uom_form_action
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_tree_view
msgid "Units of Measure"
msgstr "Unitats de mesura"

#. module: uom
#: model:ir.actions.act_window,name:uom.product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Categories d'unitats de mesura"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_tree_view
msgid "Units of Measure categories"
msgstr "Categorias d'unitats de mesura"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_categ_form_action
msgid ""
"Units of measure belonging to the same category can be\n"
"            converted between each others. For example, in the category\n"
"            <i>'Time'</i>, you will have the following units of measure:\n"
"            Hours, Days."
msgstr ""
"Les unitats de mesura pertanyents a la mateixa categoria, poden ser "
"convertides entre elles. Per exemple, en la categoria <i>'Temps'</i>, poden "
"tenir les següents unitats de mesura: Hores, Dies."

#. module: uom
#: code:addons/uom/models/uom_uom.py:0 code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "UoM category %s should have a reference unit of measure."
msgstr "Categoria UoM %s hauria de tenir una unitat de mesura de referència."

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "UoM category %s should only have one reference unit of measure."
msgstr ""
"Categoria UoM %s només hauria de tenir una unitat de mesura de referència."

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__uom_ids
msgid "Uom"
msgstr "Uom"

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_vol
msgid "Volume"
msgstr "Volum"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "Warning for %s"
msgstr "Avís per a %s"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "Warning!"
msgstr "Avís. "

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_kgm
msgid "Weight"
msgstr "Pes"

#. module: uom
#: model:uom.category,name:uom.uom_categ_wtime
msgid "Working Time"
msgstr "Horari de treball"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_form_action
msgid ""
"You must define a conversion rate between several Units of\n"
"            Measure within the same category."
msgstr ""
"Ha de definir una ràtio de conversió entre diverses unitats de mesura dins "
"de la mateixa categoria."

#. module: uom
#: model:uom.uom,name:uom.product_uom_cm
msgid "cm"
msgstr "cm"

#. module: uom
#: model:uom.uom,name:uom.product_uom_floz
msgid "fl oz (US)"
msgstr "fl oz (US)"

#. module: uom
#: model:uom.uom,name:uom.product_uom_foot
msgid "ft"
msgstr "ft"

#. module: uom
#: model:uom.uom,name:uom.uom_square_foot
msgid "ft²"
msgstr "ft²"

#. module: uom
#: model:uom.uom,name:uom.product_uom_cubic_foot
msgid "ft³"
msgstr "ft³"

#. module: uom
#: model:uom.uom,name:uom.product_uom_gal
msgid "gal (US)"
msgstr "gal (US)"

#. module: uom
#: model:uom.uom,name:uom.product_uom_inch
msgid "in"
msgstr "en"

#. module: uom
#: model:uom.uom,name:uom.product_uom_cubic_inch
msgid "in³"
msgstr "in³"

#. module: uom
#: model:uom.uom,name:uom.product_uom_kgm
msgid "kg"
msgstr "kg"

#. module: uom
#: model:uom.uom,name:uom.product_uom_km
msgid "km"
msgstr "km"

#. module: uom
#: model:uom.uom,name:uom.product_uom_lb
msgid "lb"
msgstr "lb"

#. module: uom
#: model:uom.uom,name:uom.product_uom_mile
msgid "mi"
msgstr "mi"

#. module: uom
#: model:uom.uom,name:uom.product_uom_millimeter
msgid "mm"
msgstr "mm"

#. module: uom
#: model:uom.uom,name:uom.uom_square_meter
msgid "m²"
msgstr "m²"

#. module: uom
#: model:uom.uom,name:uom.product_uom_cubic_meter
msgid "m³"
msgstr "m³"

#. module: uom
#: model:uom.uom,name:uom.product_uom_oz
msgid "oz"
msgstr "oz"

#. module: uom
#: model:uom.uom,name:uom.product_uom_qt
msgid "qt (US)"
msgstr "qt (US)"
