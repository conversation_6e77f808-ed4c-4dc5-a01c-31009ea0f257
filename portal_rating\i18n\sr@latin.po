# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_rating
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:54+0000\n"
"PO-Revision-Date: 2017-09-20 09:54+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/website_mail.xml:24
#, python-format
msgid "Average"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/website_mail.xml:34
#, python-format
msgid "Details"
msgstr "Detalji"

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/js/website_mail.js:43
#, python-format
msgid "I don't like it"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/js/website_mail.js:42
#, python-format
msgid "I hate it"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/js/website_mail.js:45
#, python-format
msgid "I like it"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/js/website_mail.js:46
#, python-format
msgid "I love it"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/js/website_mail.js:44
#, python-format
msgid "It's okay"
msgstr ""

#. module: website_rating
#: model:ir.model,name:website_rating.model_mail_message
msgid "Message"
msgstr "Poruka"

#. module: website_rating
#: model:ir.model.fields,field_description:website_rating.field_rating_rating_website_published
msgid "Published"
msgstr ""

#. module: website_rating
#: model:ir.model,name:website_rating.model_rating_rating
msgid "Rating"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/website_mail.xml:52
#, python-format
msgid "Remove selection"
msgstr ""

#. module: website_rating
#: model:ir.model.fields,help:website_rating.field_rating_rating_website_published
msgid "Visible on the website as a comment"
msgstr ""

#. module: website_rating
#. openerp-web
#: code:addons/website_rating/static/src/xml/website_mail.xml:40
#, python-format
msgid "stars"
msgstr ""
