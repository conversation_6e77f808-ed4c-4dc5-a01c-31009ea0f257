# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_margin
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_num_invoiced
msgid "# Invoiced in Purchase"
msgstr "Nº de faturados na compra"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_num_invoiced
msgid "# Invoiced in Sale"
msgstr "Nº de faturados na venda"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "# Purchased"
msgstr "Nº comprado"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Analysis Criteria"
msgstr "Critérios de Análise"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_avg_price
msgid "Avg. Price in Customer Invoices."
msgstr "Preço Médio em faturas de clientes."

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_avg_price
msgid "Avg. Price in Vendor Bills "
msgstr "Média de Preço nas Contas do Fornecedor"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_avg_price
msgid "Avg. Purchase Unit Price"
msgstr "Média Preço unitário de compra"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_avg_price
msgid "Avg. Sale Unit Price"
msgstr "Média Preço unitário de venda"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Avg. Unit Price"
msgstr "Preço Unit. Médio"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Cancel"
msgstr "Cancelar"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Catalog Price"
msgstr "Preço de Catálogo"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__create_date
msgid "Created on"
msgstr "Criado em"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__draft_open_paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__draft_open_paid
msgid "Draft, Open and Paid"
msgstr "Provisório, Aberto e Pago"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__expected_margin
msgid "Expected Margin"
msgstr "Margem Esperada"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__expected_margin_rate
msgid "Expected Margin (%)"
msgstr "Margem Esperada (%)"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sale_expected
msgid "Expected Sale"
msgstr "Venda Esperada"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__expected_margin
msgid "Expected Sale - Normal Cost"
msgstr "Venda Esperada - Custo Normal"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sales_gap
msgid "Expected Sale - Turn Over"
msgstr "Venda Esperada - Rotatividade"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__expected_margin_rate
msgid "Expected margin * 100 / Expected Sale"
msgstr "Magem esperada * 100 / Venda Esperada"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__from_date
msgid "From"
msgstr "De"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "General Information"
msgstr "Informações gerais"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__id
msgid "ID"
msgstr "ID"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__invoice_state
#: model:ir.model.fields,field_description:product_margin.field_product_product__invoice_state
msgid "Invoice State"
msgstr "Situação da Fatura"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__date_from
msgid "Margin Date From"
msgstr "Margem Data de"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__date_to
msgid "Margin Date To"
msgstr "Margem Data para"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Margins"
msgstr "Margens"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__normal_cost
msgid "Normal Cost"
msgstr "Custo Normal"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_gap
msgid "Normal Cost - Total Cost"
msgstr "Custo Normal - Custo Total"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Open Margins"
msgstr "Margens Abertas"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__open_paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__open_paid
msgid "Open and Paid"
msgstr "Aberto e Pago"

#. module: product_margin
#: model:ir.model.fields.selection,name:product_margin.selection__product_margin__invoice_state__paid
#: model:ir.model.fields.selection,name:product_margin.selection__product_product__invoice_state__paid
msgid "Paid"
msgstr "Pago"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_product
msgid "Product"
msgstr "Produto"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_margin
msgid "Product Margin"
msgstr "Margem do Produto"

#. module: product_margin
#: code:addons/product_margin/wizard/product_margin.py:0
#: model:ir.actions.act_window,name:product_margin.product_margin_act_window
#: model:ir.ui.menu,name:product_margin.menu_action_product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_graph
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
#, python-format
msgid "Product Margins"
msgstr "Margens do Produto"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Properties categories"
msgstr "Categorias de Propriedades"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__purchase_gap
msgid "Purchase Gap"
msgstr "Abertura de compra"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Purchases"
msgstr "Compras"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Sales"
msgstr "Vendas"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__sales_gap
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Sales Gap"
msgstr "Vendas abertas"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Standard Price"
msgstr "Preço Padrão"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__normal_cost
msgid "Sum of Multiplication of Cost price and quantity of Vendor Bills"
msgstr ""
"Soma da Multiplicação do preço de Custo e quantidade de Contas do Fornecedor"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__turnover
msgid ""
"Sum of Multiplication of Invoice price and quantity of Customer Invoices"
msgstr ""
"Soma de multiplicação do preço da fatura e quantidade de faturas de clientes"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_cost
msgid "Sum of Multiplication of Invoice price and quantity of Vendor Bills "
msgstr ""
"Soma da Multiplicação do preço da Fatura e a quantidade de Contas do "
"Fornecedor"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_expected
msgid ""
"Sum of Multiplication of Sale Catalog price and quantity of Customer "
"Invoices"
msgstr ""
"Soma de multiplicação do preço de venda de catálogo e quantidade de faturas "
"de clientes"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__sale_num_invoiced
msgid "Sum of Quantity in Customer Invoices"
msgstr "Soma da Quantidade nas Faturas de Clientes"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__purchase_num_invoiced
msgid "Sum of Quantity in Vendor Bills"
msgstr "Soma da Quantidade em Contas de Fornecedor"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin__to_date
msgid "To"
msgstr "Para"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_cost
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Cost"
msgstr "Custo Total"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Margin"
msgstr "Margem total"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__total_margin_rate
msgid "Total Margin Rate(%)"
msgstr "Índice total de margem (%)"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_margin_rate
msgid "Total margin * 100 / Turnover"
msgstr "Margem total * 100/retorno"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product__turnover
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Turnover"
msgstr "Rotatividade"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product__total_margin
msgid "Turnover - Standard price"
msgstr "Volume de negócios - Preço Padrão"
