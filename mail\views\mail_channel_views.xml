<?xml version="1.0"?>
<odoo>
    <data>
        <!-- mail.channel -->
        <record id="mail_channel_view_kanban" model="ir.ui.view">
            <field name="name">mail.channel.kanban</field>
            <field name="model">mail.channel</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <kanban>
                    <field name="id"/>
                    <field name="description"/>
                    <field name="is_member"/>
                    <field name="group_ids"/>
                    <field name="public"/>
                    <templates>
                        <t t-name="kanban-description">
                            <div class="oe_group_description" t-if="record.description.raw_value">
                                <field name="description"/>
                            </div>
                        </t>
                        <t t-name="kanban-box">
                            <div class="oe_module_vignette oe_kanban_global_click">
                                <img t-att-src="kanban_image('mail.channel', 'avatar_128', record.id.raw_value)" class="oe_module_icon" alt="Channel"/>
                                <div class="oe_module_desc">
                                    <h4 class="o_kanban_record_title">#<field name="name"/></h4>
                                    <p class="oe_module_name">
                                        <field name="description"/>
                                    </p>
                                    <button type="object" attrs="{'invisible':['|', ('is_member','=',True), ('group_ids', '!=', [])]}" class="btn btn-primary float-right" name="channel_join">Join</button>
                                    <button type="object" attrs="{'invisible':['|', ('is_member','=',False), ('group_ids', '!=', [])]}" class="btn btn-secondary float-right" name="action_unfollow">Leave</button>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="mail_channel_view_form" model="ir.ui.view">
            <field name="name">mail.channel.form</field>
            <field name="model">mail.channel</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <form string="Mail Channel Form">
                    <sheet>
                        <div class="oe_button_box" name="button_box"/>
                        <field name="avatar_128" invisible="1"/>
                        <field name="image_128" widget="image" class="oe_avatar" options="{'size': [90, 90], 'preview_image':'avatar_128'}"/>
                        <div class="oe_title">
                            <label for="name" string="Group Name"/>
                            <h1>
                                #<field name="name" class="oe_inline" default_focus="1" placeholder="e.g. support" readonly="0"/>
                            </h1>
                        </div>
                        <group class="o_label_nowrap">
                            <field name="active" invisible="1"/>
                            <field name="description" placeholder="Topics discussed in this group..."/>
                        </group>
                        <group name="group_alias" attrs="{'invisible': [('alias_domain', '=', False)]}">
                            <label for="alias_id" string=" " class="fa fa-envelope-o" style="min-width: 20px;" aria-label="Email" title="Email" role="img"/>
                            <div name="alias_def">
                                <field name="alias_id" class="oe_read_only oe_inline"
                                        string="Email Alias" required="0"/>
                                <div class="oe_edit_only oe_inline" name="edit_alias" style="display: inline;" >
                                    <field name="alias_name" class="oe_inline"/>@<field name="alias_domain" class="oe_inline" readonly="1"/>
                                </div>
                            </div>
                            <field name="alias_contact" class="oe_inline" invisible="1"/>
                        </group>
                        <notebook>
                            <page string="Privacy" name="privacy">
                                <group class="o_label_nowrap">
                                    <field name="public" widget="radio" string="Who can follow the group's activities?"/>
                                    <field name="group_public_id"
                                        attrs="{'invisible': [('public','!=','groups')], 'required': [('public','=','groups')]}"
                                        />
                                    <field name="group_ids" widget="many2many_tags"
                                        string="Auto Subscribe Groups"/>
                                </group>
                            </page>
                            <page string="Members" name="members">
                                <field name="channel_type" invisible="1"/>
                                <field name="channel_last_seen_partner_ids" mode="tree" context="{'active_test': False}" attrs="{'readonly': [('channel_type', '=', 'chat')]}">
                                    <tree string="Members" editable="bottom">
                                        <field name="partner_id" required="1" attrs="{'readonly': [('id', '!=', False)]}"/>
                                        <field name="partner_email" readonly="1"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Integrations" invisible="1" name="mail_channel_integrations"></page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="mail_channel_view_tree" model="ir.ui.view">
            <field name="name">mail.channel.tree</field>
            <field name="model">mail.channel</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <tree string="Groups">
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <record id="mail_channel_view_search" model="ir.ui.view">
            <field name="name">mail.channel.search</field>
            <field name="model">mail.channel</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <search string="Search Groups">
                    <field name="name"/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                </search>
            </field>
        </record>

        <record id="mail_channel_action_view" model="ir.actions.act_window">
            <field name="name">Join a group</field>
            <field name="res_model">mail.channel</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="search_view_id" ref="mail_channel_view_search"/>
        </record>

    <record id="action_discuss" model="ir.actions.client">
        <field name="name">Discuss</field>
        <field name="tag">mail.widgets.discuss</field>
        <field name="res_model">mail.channel</field>
        <field name="params" eval="&quot;{ 'default_active_id': 'mail.box_inbox' }&quot;"/>
    </record>

    </data>
</odoo>
