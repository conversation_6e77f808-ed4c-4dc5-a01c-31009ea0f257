# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_email_template
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-01-13 08:54+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Costa Rica) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CR/)\n"
"Language: es_CR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_email_template
#: model:mail.template,body_html:product_email_template.product_online_training_email_template
msgid ""
" \n"
"    <div style=\"height:auto;text-align: center;font-size : 40px;color: "
"#333333;margin-top:30px;font-weight: 300;\">\n"
"            Online Training\n"
"    </div>\n"
"     <div style=\"height:auto;text-left: center;font-size : 16px;color: "
"#646464;margin-top:30px;margin-left:15px;margin-right:5px;\">\n"
"        These courses feature the same high quality course content found in "
"our traditional classroom trainings, supplemented with\n"
"        modular sessions and cloud-based labs. Many of our online learning "
"courses also include dozens of recorded webinars and live\n"
"        sessions by our senior instructors.\n"
"    </div>\n"
"    <div style=\"height: auto;margin-top:30px;margin-bottom:10px;margin-"
"left:20px;color: #646464;font-size:16px;\">\n"
"        <strong>Your advantages</strong>\n"
"        <ul>\n"
"            <li>Modular approach applied to the learning method</li>\n"
"            <li>New interactive learning experience</li>\n"
"            <li>Lower training budget for the same quality courses</li>\n"
"            <li>Better comfort to facilitate your learning process</li>\n"
"        </ul>\n"
"    </div>\n"
"    <hr color=\"DCDCFB\"/>\n"
"    <div style=\"height:auto;text-align: center;font-size : 40px;color: "
"#333333;margin-top:30px;font-weight: 300;\">\n"
"        Structure of the Training\n"
"    </div>\n"
"    <div>\n"
"        <table>\n"
"            <tr>\n"
"                <td>\n"
"                    <img src=\"/product_email_template/static/img/"
"online_training.png\"/>\n"
"                </td>\n"
"                <td style=\"height: auto;margin-top:10px;margin-bottom:10px;"
"font-size:18px;color: #646464;\">\n"
"                    <strong>There are three components to the training</"
"strong>\n"
"                    <ul>\n"
"                        <li>Videos with detailed demos</li>\n"
"                        <li>Hands-on exercises and their solutions</li>\n"
"                        <li>Live Q&A sessions with a trainer</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: left;font-size : 14px;color: "
"#646464;margin-top:10px;margin-left:15px;\">\n"
"        The 5-day training is modular, which means that you can choose to "
"participate in the full training or to just 1 or 2 modules.\n"
"        Nevertheless, <b>the first day of the training is compulsory</b> for "
"everyone. The Monday is compulsory because the\n"
"        introduction of Odoo is important before going through the other "
"modules.<br/><br/>\n"
"        Each day of the week starts from 9 AM (CEST/PST) to 12 PM (CEST/"
"PST). A Q&A session will be hosted by an Odoo trainer.\n"
"        Each day, the participants are expected to have done the following "
"<b>BEFORE</b> attending the Q&A session:\n"
"        <ul>\n"
"            <li>Watch the videos for this day</li>\n"
"            <li>Do the related exercises (written solutions will be provided "
"upfront as well)</li>\n"
"            <li>Send their questions by email before 5PM (CEST/PST) on the "
"previous business day</li>\n"
"        </ul><br/>\n"
"        The Q&A session will be hosted on a web conference service.<b> A "
"stable internet connection is required.</b> The trainer will\n"
"        screen the questions for the day and select the most relevant ones "
"to be answered during the session. The purpose of the\n"
"        <b>Q&A session is not to cover questions related to a specific "
"implementation project </b>or company-specific business\n"
"        workflows. The <b>questions should be about the material covered in "
"the videos </b>and the exercises and should benefit\n"
"        the other participants of the training as well.<br/>\n"
"        Each day of the week is dedicated to specific applications in Odoo "
"as per the following planning:\n"
"        <ul style=\"color: #6d57e0\">\n"
"            <li>\n"
"                <strong>Monday: </strong>\n"
"                Introduction, CRM, Sales Management\n"
"            </li>\n"
"            <li>\n"
"                <strong>Tuesday: </strong>\n"
"                Access rights, Purchase, Sales &amp; Purchase management, "
"Financial accounting\n"
"            </li>\n"
"            <li>\n"
"                <strong>Wednesday: </strong>\n"
"                Project management, Human resources, Contract management\n"
"            </li>\n"
"            <li>\n"
"                <strong>Thursday: </strong>\n"
"                Warehouse management, Manufacturing (MRP) &amp; Sales, "
"Import/Export\n"
"            </li>\n"
"            <li>\n"
"                <strong>Friday: </strong>\n"
"                Pricelists, Point of Sale (POS), Introduction to report "
"customization\n"
"            </li>\n"
"        </ul>\n"
"    </div>\n"
"    <hr color=\"DCDCFB\"/>\n"
"    <div style=\"height:auto;text-align: center;font-size : 40px;color: "
"#333333;margin-top:40px;font-weight: 300;\">\n"
"        Content of the Training\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: center;font-size : 24px;color: "
"#646464;margin-top:30px;\">\n"
"        <hr style=\"display: inline-block; width: 15%; margin-bottom: 4px;"
"margin-right: 25px;\" />Monday<hr style=\"display: inline-block; width: 15%; "
"margin-bottom: 4px;margin-left: 25px;\" /><br/><br/>\n"
"        Introduction, CRM & Sales\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Introduction: Get familiar with the V7.</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Odoo’s architecture</li>\n"
"                        <li>How to create an Odoo Online instance</li>\n"
"                        <li>List and form views, search and filter features</"
"li>\n"
"                        <li>Tooltips and drill-downs</li>\n"
"                        <li>Advanced navigation</li>\n"
"                        <li>Calendar, Graph, Kanban and Gantt views</li>\n"
"                        <li>Social network features: emailing, internal "
"communication</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                        The introduction exercises have been created to help "
"you discover Odoo in an easy way. Their main objective is to present the "
"different applications, menus, view types, shortcuts, field types, wizards, "
"actions, etc.  It will help you to discover how to navigate in Odoo!<br/></"
"br>\n"
"                        Therefore, the Introduction is compulsory for "
"everyone, even if you need to learn a specific module given on an other "
"date. This obligation has been made to make sure that you are not lost if "
"you arrive in the middle of the training for another module.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">CRM Module</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Manage Lead & Opportunities</li>\n"
"                        <li>Call, Meeting</li>\n"
"                        <li>Partners & contacts</li>\n"
"                        <li>Automated actions</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Track all leads coming in and push them to real "
"opportunities</li>\n"
"                        <li>Improve the productivity of the sales force</"
"li>\n"
"                        <li>Access all documents and messages related to one "
"lead/opportunity in one place</li>\n"
"                        <li>Track the sales pipeline by month and stage to "
"produce\n"
"                        accurate sales forecasts at the individual and "
"group\n"
"                        levels</li>\n"
"                        <li>Track individual performance of sales people</"
"li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Sales Module</div>\n"
"        <table>\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Customers</li>\n"
"                        <li>Products to sell</li>\n"
"                        <li>Manual discounts</li>\n"
"                        <li>Margins</li>\n"
"                        <li>Sales & Warehouse: Invoicing control (On "
"demand / On\n"
"                        Delivery Order), Shipping policy (At once / Each "
"product\n"
"                        when available)</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Register orders from customers and create "
"invoices</li>\n"
"                        <li>Schedule deliveries (for products) or tasks (for "
"service)</li>\n"
"                        <li>Ensure that sales orders are properly invoiced "
"by tracking pending sales orders</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <strong>CRM & Sales management:</strong>\n"
"                    <ul>\n"
"                        <li>From an opportunity to a sale order</li>\n"
"                        <li>Claims</li>\n"
"                        <li>Aftersale communication</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    Odoo can work with independent applications, so the\n"
"                    whole commercial process can be integrated together in\n"
"                    order to accomplish complete business flows.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: center;font-size : 24px;color: "
"#646464;margin-top:30px;\">\n"
"        <hr style=\"display: inline-block; width: 15%; margin-bottom: 4px;"
"margin-right: 25px;\" />Tuesday<hr style=\"display: inline-block; width: "
"15%; margin-bottom: 4px;margin-left: 25px;\" /><br/><br/>\n"
"        Access rights, Sales & Purchase, Financial accounting\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Access rights</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Users</li>\n"
"                        <li>Groups</li>\n"
"                        <li>Access rights and access rules</li>\n"
"                        <li>Modules configuration</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                        Thanks to the integrated access rights management "
"integrated, each user has an access to its relevant information. No "
"overloaded screen with a lot of irrelevant data, no unwanted menu, etc. Only "
"a clean interface!\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Purchase</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Vendors</li>\n"
"                        <li>Products</li>\n"
"                        <li>Request for Quotation</li>\n"
"                        <li>Purchase order</li>\n"
"                        <li>Invoicing & Invoice control</li>\n"
"                        <li>Incoming order (Complete/partial receipt)</li>\n"
"                        <li>Purchase requisition</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Better negotiate volumes and prices based on "
"historical data</li>\n"
"                        <li>Centralize all the data to be able to build "
"reporting and statistics</li>\n"
"                        <li>Control the invoicing process</li>\n"
"                        <li>Work with purchase requisitions to ask different "
"vendors to submit quotations</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <strong>Sales & Purchase management</strong>\n"
"                    <ul>\n"
"                        <li>Make to order product</li>\n"
"                        <li>Retailer process: buy a product and sell it "
"without any customization/manufacturing</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    Keep control of the replenishment process based on sales "
"demands! Thanks to this integration, you will be able to automate the "
"ordering for the missing units ordered by a customer\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Financial accounting</div>\n"
"        <table>\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Invoices</li>\n"
"                        <li>Refunds</li>\n"
"                        <li>Journal Entries & Reconciliations</li>\n"
"                        <li>Reports / Closing of fiscal year</li>\n"
"                        <li>Accounts</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Rapidly encode your financial operations or "
"accounting transactions</li>\n"
"                        <li>Carry out payments easily and adequately "
"reconcile these payments with invoices</li>\n"
"                        <li>Quick creation of invoices with pre-set defaults "
"on debtor/creditor and income/expense accounts</li>\n"
"                        <li>Multiple manners to reconcile</li>\n"
"                        <li>Configuration of accounts to ensure correct "
"display in balance sheet and profit & loss statement</li>\n"
"                        <li>Apply correct deferral methods to ensure the "
"close of the fiscal year</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: center;font-size : 24px;color: "
"#646464;margin-top:30px;\">\n"
"        <hr style=\"display: inline-block; width: 15%; margin-bottom: 4px;"
"margin-right: 25px;\" />Wednesday<hr style=\"display: inline-block; width: "
"15%; margin-bottom: 4px;margin-left: 25px;\" /><br/><br/>\n"
"        Project management, Human resources, Contract management\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Project Management</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Projects</li>\n"
"                        <li>Tasks</li>\n"
"                        <li>Issues</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Manage and follow your projects, tasks and "
"issues</li>\n"
"                        <li>Discover the Kanban view to quickly operate some "
"actions and to quickly detect any bottlenecks in your projects flows</li>\n"
"                        <li>Discover the Gantt view to see how your project "
"has been planned and adapt it according to new elements or to the people "
"agenda</li>\n"
"                        <li>Define project members & delegate tasks</li>\n"
"                        <li>Create automatically timesheet lines in the "
"Human Resources menu from recorded work activities</li>\n"
"                        <li>Track and invoice (to customers) the costs "
"associated to a project</li>\n"
"                        <li>Manage a support ticket system</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Human Resources</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Employees</li>\n"
"                        <li>Recruitment</li>\n"
"                        <li>Expenses</li>\n"
"                        <li>Allocations and leaves requests</li>\n"
"                        <li>Time management: timesheet and timesheet lines</"
"li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                     <strong>About Recruiting:</strong>\n"
"                    <ul>\n"
"                        <li>Better negotiate volumes and prices based on "
"historical data</li>\n"
"                        <li>Centralize all the data to be able to build "
"reporting and statistics</li>\n"
"                        <li>Control the invoicing process</li>\n"
"                        <li>Work with purchase requisitions to ask different "
"vendors to submit quotations</li>\n"
"                    </ul>\n"
"                    <strong>About Holidays:</strong>\n"
"                    <ul>\n"
"                        <li>Track the number of vacation days accrued by "
"each employee</li>\n"
"                        <li>Allow managers to approve leave requests and "
"plan backups for their teams</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Contract management</div>\n"
"        <table>\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Contracts</li>\n"
"                        <li>Invoicing methods\n"
"                            <li>Sale orders</li>\n"
"                            <li>Timesheets</li>\n"
"                            <li>Expenses</li>\n"
"                        </li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Centralize the data related to a specific "
"contract in one single point</li>\n"
"                        <li>Perform the invoicing from one single place "
"(Sales order / Timesheet / Expenses)</li>\n"
"                        <li>Follow the renewal process</li>\n"
"                        <li>Compute statistics based on expected revenues</"
"li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: center;font-size : 24px;color: "
"#646464;margin-top:30px;\">\n"
"        <hr style=\"display: inline-block; width: 15%; margin-bottom: 4px;"
"margin-right: 25px;\" />Thursday<hr style=\"display: inline-block; width: "
"15%; margin-bottom: 4px;margin-left: 25px;\" /><br/><br/>\n"
"        Warehouse management, Manufacturing (MRP) & Sales, Import/Export\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Warehouse management</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Stock moves</li>\n"
"                        <li>Inventory</li>\n"
"                        <li>Partial delivery / receipt</li>\n"
"                        <li>Units of measure</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    Odoo Warehouse Management is at once very simple, "
"flexible and complete. It is based on the concept of double entry that "
"revolutionized accounting: ‘Nothing lost, everything moved’. In Odoo, we "
"don’t talk about disappearance, consumption or loss of products: instead we "
"speak only of stock moves from one place to another.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Manufacturing (MRP)</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Bill of materials</li>\n"
"                        <li>Multi-level BoM</li>\n"
"                        <li>Routings</li>\n"
"                        <li>Work center operations</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Manage the production planning</li>\n"
"                        <li>Organize the different manufacturing orders to "
"optimize the workload of the different resources</li>\n"
"                        <li>From an operational perspective, track which "
"products and quantities need to be manufactured</li>\n"
"                    </ul>\n"
"                    <strong>As a manager:</strong>\n"
"                    <ul>\n"
"                        <li>Define products to be assembled or to be sold as "
"a kit</li>\n"
"                        <li>Organize the pipeline of the production</li>\n"
"                        <li>Track the cost of a manufacturing order and "
"measure the efficiency of the department</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <strong>MRP & Sales</strong>\n"
"                    <ul>\n"
"                        <li>Product configuration Make to Stock / to Order</"
"li>\n"
"                        <li>Commercial BoM (kit)</li>\n"
"                        <li>Just in Time</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    From the sales perspective, you will have a "
"manufacturing order generated based on a request coming from a customer "
"without any manual operation. This integration of those two aplications "
"allows you to perform the following flow in an automated way: Sale order –> "
"Stock level verification –> Production of the missing units –> Receival in "
"stock of the finished goods –> Delivery to the customer.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Import/Export</div>\n"
"        <table>\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Import from CSV files</li>\n"
"                        <li>Export as CSV or as Excel file</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Transfer data from one system to Odoo.</li>\n"
"                    </ul>\n"
"                    Odoo offers a simple way to import and export data. Odoo "
"will guide you for this phase thanks to a FAQ directly integrated into the "
"import screen\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: center;font-size : 24px;color: "
"#646464;margin-top:30px;\">\n"
"        <hr style=\"display: inline-block; width: 15%; margin-bottom: 4px;"
"margin-right: 25px;\" />Friday<hr style=\"display: inline-block; width: 15%; "
"margin-bottom: 4px;margin-left: 25px;\" /><br/><br/>\n"
"        Pricelists, Point of Sale (POS), Introduction to report "
"customization\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Pricelists</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Prices for returning customers</li>\n"
"                        <li>Prices in foreign currencies (with exchange "
"fees)</li>\n"
"                        <li>Retailer prices (based on a minimal margin)</"
"li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Keep control on the pricing policy</li>\n"
"                        <li>Allow discounts for some periods</li>\n"
"                        <li>Ensure your margins even when you define "
"discounts</li>\n"
"                        <li>Allow different pricing by customer and/or "
"vendor</li>\n"
"                        <li>Update the prices in an easy way</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Point of Sale (POS)</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Payment methods (journals)</li>\n"
"                        <li>POS Configuration</li>\n"
"                        <li>POS front-end</li>\n"
"                        <li>POS back-end</li>\n"
"                        <li>Shops</li>\n"
"                        <li>Sessions</li>\n"
"                        <li>POS Orders</li>\n"
"                        <li>Re-Invoicing</li>\n"
"                        <li>Scanning / Self-scanning</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    Odoo POS is the first POS running in a 100% web based "
"environment, which means any computer with a browser can host the POS. The "
"POS is a two-part system with a front-end (interaction with the client) and "
"a back-end (managers can configure the system, print reports, "
"analyse, ...)<br/><br/>\n"
"                     <strong>About the front-end:</strong>\n"
"                    <ul>\n"
"                        <li>Offline mode. Imagine several cases were having "
"an offline mode is of prime interest: Connexion to server shutdown at a big "
"supermarket / Use in environments that requires mobility but without wifi / "
"Use in large environments like restaurants and gardens / Use in low tech "
"environments</li>\n"
"                    </ul>\n"
"                    <strong>About the back-end:</strong>\n"
"                    <ul>\n"
"                        <li>Configure the products being sold in the POS</"
"li>\n"
"                        <li>Configure the payments methods</li>\n"
"                        <li>Print daily sales report</li>\n"
"                        <li>Analyze sales</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Introduction to report customization</div>\n"
"        <table>\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Customize the layout of your invoices</li>\n"
"                        <li>Change the footer/header of documents</li>\n"
"                        <li>Add a field in a standard document / report</"
"li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    This features will help you to fully customize the "
"standard documents made from Odoo.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"text-align: center;background-color:#FFFFFF;margin-top:30px;"
"\" >\n"
"        <table cellspacing=\"5\" align=\"center\" cellpadding=\"5\">\n"
"            <tr style=\"font-size : 20px;color: #333333;margin-top:30px;\">\n"
"                <td>English</td>\n"
"                <td>English</td>\n"
"                <td>Spanish</td>\n"
"            </tr>\n"
"            <tr style=\"font-size : 10px;color: #646464;\">\n"
"                <td>Europe</td>\n"
"                <td>USA & Canada</td>\n"
"                <td>USA & Latin America</td>\n"
"            </tr>\n"
"            <tr style=\"font-size : 10px;color: #FFFFFF;background-color:"
"#8b5bdd;\">\n"
"                <td><a href=\"http://onlinetrainingencet.eventbrite.com\" "
"style=\"text-decoration:none;color:#FFFFFF;\" target=_new>Register</a></td>\n"
"                <td><a href=\"http://onlinetrainingenpst.eventbrite.com\" "
"style=\"text-decoration:none;color:#FFFFFF;\" target=_new>Register</a></td>\n"
"                <td><a href=\"http://onlinetrainingespst.eventbrite.com\" "
"style=\"text-decoration:none;color:#FFFFFF;\" target=_new>Register</a></td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <hr color=\"DCDCFB\"/>\n"
"    <div style=\"height:auto;text-align: center;font-size : 30px;color: "
"#333333;margin-top:10px;\">\n"
"            Training Material\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: left;font-size : 16px;color: "
"#646464;margin-top:30px;\">\n"
"        As soon as registration will be finalized (ie. payment "
"confirmation), the participants will be provided with the material listed\n"
"        below:\n"
"        <ul>\n"
"            <li>Access to online videos with detailed demos</li>\n"
"            <li>Training material with exercises</li>\n"
"            <li>Written solutions to the exercises</li>\n"
"        </ul>\n"
"        Therefore, there is an advantage to registering early to the "
"training if you are interested in preparing your live sessions ahead of "
"time. Moreover, the number of participants is limited per online session "
"which is another reason to consider registering early and securing your "
"seat.\n"
"    </div>\n"
"    <div style=\"text-align: center;background-color:#FFFFFF;margin-top:30px;"
"font-size:20px;color:#4e66e7\">\n"
"        <table cellspacing=\"5\">\n"
"            <tr>\n"
"                <td>\n"
"                    With the live sessions, we learned a lot more than what "
"was covered in the videos. Definitely a good start for building up a solid "
"base of working knowledge of Odoo.\n"
"                </td>\n"
"                <td>\n"
"                    The trainer did a wonderful job with the training! He "
"was well informed and interactive online and offline. Thank you so much for "
"the experience! \n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    It would be very difficult to cover all of the "
"possibilities of Odoo and I believe the major aspects were covered very "
"well \n"
"                </td>\n"
"                <td>\n"
"                    The trainer has a very good knowledge of the subject, he "
"understands the issues of the trainees very fast and provided answers with "
"the right level of illustration.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</div>\n"
"            \n"
"            "
msgstr ""

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
msgid "Body"
msgstr "Contenido"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
msgid "Email Template"
msgstr "Plantilla email"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_account_invoice
msgid "Invoice"
msgstr "Factura"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Invoice Confirmation Email"
msgstr ""

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Miscellaneous"
msgstr "Varios"

#. module: product_email_template
#: model:mail.template,subject:product_email_template.product_online_training_email_template
#: model:product.product,name:product_email_template.product_online_training
#: model:product.template,name:product_email_template.product_online_training_product_template
msgid "Online Training"
msgstr ""

#. module: product_email_template
#: model:ir.model.fields,field_description:product_email_template.field_product_template_email_template_id
msgid "Product Email Template"
msgstr ""

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: product_email_template
#: model:ir.model.fields,help:product_email_template.field_product_template_email_template_id
msgid ""
"When validating an invoice, an email will be sent to the customer based on "
"this template. The customer will receive an email for each product linked to "
"an email template."
msgstr ""
