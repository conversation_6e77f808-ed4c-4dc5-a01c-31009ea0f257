<?xml version="1.0" ?>
<odoo>
   <data noupdate="1">
      <record id="pos_coupon.mail_coupon_template" model="mail.template">
         <field name="name">[POS] Coupon: Send by Email</field>
         <field name="model_id" ref="coupon.model_coupon_coupon"/>
         <field name="subject">Your reward coupon from {{ object.program_id.company_id.name }} </field>
         <field name="email_from">{{ object.program_id.company_id.email }}</field>
         <field name="partner_to">{{ object.source_pos_order_id.partner_id.id or object.partner_id.id }}</field>
         <field name="body_html" type="html">
<table border="0" cellpadding="0" cellspacing="0" style="width:100%; margin:0px auto;"><tbody>
    <tr><td valign="top" style="text-align: center; font-size: 14px;">
        <t t-if="object.partner_id.name">
            Congratulations <t t-out="object.partner_id.name or ''"><PERSON></t>,<br />
        </t>

        Here is your reward from <t t-out="object.program_id.company_id.name or ''">YourCompany</t>.<br />

        <t t-if="object.program_id.reward_type == 'discount'">
            <t t-if="object.program_id.discount_type == 'fixed_amount'">
                <span style="font-size: 50px; color: #875A7B; font-weight: bold;" t-out="'%s' % format_amount(object.program_id.discount_fixed_amount, object.program_id.currency_id) or ''">$ 10.0</span><br />
                <strong style="font-size: 24px;">off on your next order</strong><br />
            </t>
            <t t-else="">
                <span style="font-size: 50px; color: #875A7B; font-weight: bold;">
                    <t t-out="object.program_id.discount_percentage or ''">10.0</t> %
                </span>
                <t t-if="object.program_id.discount_apply_on == 'specific_products'">
                    <br />
                    <t t-if="len(object.program_id.discount_specific_product_ids) != 1">
                        <t t-set="display_specific_products" t-value="True"/>
                        <strong style="font-size: 24px;">
                            on some products*
                        </strong>
                    </t>
                    <t t-else="">
                        <strong style="font-size: 24px;" t-out="'on %s' % object.program_id.discount_specific_product_ids.name or ''">Chair floor protection</strong>
                    </t>
                </t>
                <t t-elif="object.program_id.discount_apply_on == 'cheapest_product'">
                    <br /><strong style="font-size: 24px;">
                        off on the cheapest product
                    </strong>
                </t>
                <t t-else="">
                    <br /><strong style="font-size: 24px;">
                        off on your next order
                    </strong>
                </t>
                <br />
            </t>
        </t>
        <t t-elif="object.program_id.reward_type == 'product'">
            <span style="font-size: 36px; color: #875A7B; font-weight: bold;" t-out="'get %s free %s' % (object.program_id.reward_product_quantity, object.program_id.reward_product_id.name) or ''">Chair floor protection</span><br />
            <strong style="font-size: 24px;">on your next order</strong><br />
        </t>
        <t t-elif="object.program_id.reward_type == 'free_shipping'">
            <span style="font-size: 36px; color: #875A7B; font-weight: bold;">
                get free shipping
            </span><br />
            <strong style="font-size: 24px;">on your next order</strong><br />
        </t>
    </td></tr>
    <tr style="margin-top: 16px"><td valign="top" style="text-align: center; font-size: 14px;">
        Use this promo code
        <t t-if="object.expiration_date">
            before <t t-out="object.expiration_date or ''">2021-06-05</t>
        </t>
        <p style="margin-top: 16px;">
            <strong style="padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;" t-out="object.code or ''">13996301932606901095</strong>
        </p>
        <t t-if="object.program_id.rule_min_quantity not in [0, 1]">
            <span style="font-size: 14px;">
                Minimum purchase of <t t-out="object.program_id.rule_min_quantity or ''">10</t> products
            </span><br />
        </t>
        <t t-if="object.program_id.rule_minimum_amount != 0.00">
            <span style="font-size: 14px;">
                Valid for purchase above <t t-out="object.program_id.company_id.currency_id.symbol or ''">$</t><t t-out="'%0.2f' % float(object.program_id.rule_minimum_amount) or ''">10.00</t>
            </span><br />
        </t>
        <t t-if="display_specific_products">
            <span>
                *Valid for following products: <t t-out="', '.join(object.program_id.discount_specific_product_ids.mapped('name')) or ''">Office Chair Black</t>
            </span><br />
        </t>
        <br/>
        Thank you,
        <t t-if="object.source_pos_order_id.user_id.signature">
            <br />
            <t t-out="object.source_pos_order_id.user_id.signature or ''">--<br/>Mitchell Admin</t>
        </t>
    </td></tr>
</tbody></table>
            </field>
            <field name="report_template" ref="coupon.report_coupon_code"/>
            <field name="report_name">Your Coupon Code</field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
      </record>
   </data>
</odoo>
