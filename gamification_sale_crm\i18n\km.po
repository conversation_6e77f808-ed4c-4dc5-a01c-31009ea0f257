# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * gamification_sale_crm
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_nbr_customer_refunds
msgid "Customer Credit Notes"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_lead_delay_close
msgid "Days to Close a Deal"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.challenge,name:gamification_sale_crm.challenge_crm_marketing
msgid "Lead Acquisition"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.challenge,name:gamification_sale_crm.challenge_crm_sale
msgid "Monthly Sales Targets"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.challenge.line,name:gamification_sale_crm.line_crm_marketing1
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_nbr_new_leads
msgid "New Leads"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.challenge.line,name:gamification_sale_crm.line_crm_marketing3
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_nbr_new_opportunities
msgid "New Opportunities"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_nbr_sale_order_created
msgid "New Sales Orders"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_nbr_paid_sale_order
msgid "Paid Sales Orders"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.challenge.line,name:gamification_sale_crm.line_crm_marketing2
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_lead_delay_open
msgid "Time to Qualify a Lead"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_tot_customer_refunds
msgid "Total Customer Credit Notes"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.challenge.line,name:gamification_sale_crm.line_crm_sale1
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_tot_invoices
msgid "Total Invoiced"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.goal.definition,name:gamification_sale_crm.definition_crm_tot_paid_sale_order
msgid "Total Paid Sales Orders"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.challenge.line,definition_suffix:gamification_sale_crm.line_crm_marketing2
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_lead_delay_close
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_lead_delay_open
msgid "days"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_nbr_customer_refunds
msgid "invoices"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.challenge.line,definition_suffix:gamification_sale_crm.line_crm_marketing1
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_nbr_new_leads
msgid "leads"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.challenge.line,definition_suffix:gamification_sale_crm.line_crm_marketing3
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_nbr_new_opportunities
msgid "opportunities"
msgstr ""

#. module: gamification_sale_crm
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_nbr_paid_sale_order
#: model:gamification.goal.definition,suffix:gamification_sale_crm.definition_crm_nbr_sale_order_created
msgid "orders"
msgstr ""
