# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_drive
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: <PERSON>l (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"<b>To create a new filter:</b><br/>\n"
"                                - Go to the Odoo document you want to filter. For instance, go to Opportunities and search on Sales Department.<br/>\n"
"                                - In this \"Search\" view, select the option \"Save Current Filter\", enter the name (Ex: Sales Department)<br/>\n"
"                                - If you select \"Share with all users\", link of google document in \"More\" options will appear for all users in opportunities of Sales Department.<br/>\n"
"                                - If you don't select \"Share with all users\", link of google document in \"More\" options will not appear for other users in opportunities of Sales Department.<br/>\n"
"                                - If filter is not specified, link of google document will appear in \"More\" option for all users for all opportunities."
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Reset token"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Set up token"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-exclamation-triangle text-warning\"/> &amp;nbsp; No refresh"
" token set"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"text-success fa fa-check\"/> &amp;nbsp; Refresh token set"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "<span>Get an authorization code and set it in the field below.</span>"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Active</strong>"
msgstr "<strong>Aktiv</strong>"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Model</strong>"
msgstr "<strong>Modell</strong>"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Template</strong>"
msgstr "<strong>Mal</strong>"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__active
msgid "Active"
msgstr "Aktiv"

#. module: google_drive
#: model_terms:ir.actions.act_window,help:google_drive.action_google_drive_users_config
msgid "Add a new template"
msgstr "Legg til en ny mal"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_config_view_search
msgid "Archived"
msgstr "Arkivert"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "At least one key cannot be found in your Google Drive name pattern."
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__google_drive_authorization_code
msgid "Authorization Code"
msgstr "Autorisasjonskode"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "Cancel"
msgstr "Kanseller"

#. module: google_drive
#: model:ir.model.fields,help:google_drive.field_google_drive_config__name_template
msgid ""
"Choose how the new google drive will be named, on google side. Eg. "
"gdoc_%(field_name)s"
msgstr ""

#. module: google_drive
#: model:ir.model,name:google_drive.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "Confirm"
msgstr "Bekreft"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Creating google drive may only be done by one at a time."
msgstr "Du kan bare opprette én Google Drive av gangen."

#. module: google_drive
#: model:ir.filters,name:google_drive.filter_partner
msgid "Customer"
msgstr "Kunde"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__filter_id
msgid "Filter"
msgstr "Filter"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "Get Authorization Code"
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "Gå til konfigurasjonspanelet"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_client_id
msgid "Google Client"
msgstr "Google-klient"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_tree
msgid "Google Drive Configuration"
msgstr "Google Drive-oppsett"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__name_template
msgid "Google Drive Name Pattern"
msgstr "Google Drive-navnemønster"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "Google Drive Templates"
msgstr "Google Drive-maler"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Google Drive is not yet configured. Please contact your administrator."
msgstr "Google Drive er ikke satt opp enda. Ta kontakt med administrator."

#. module: google_drive
#: model:ir.model,name:google_drive.model_google_drive_config
msgid "Google Drive templates config"
msgstr "Maloppsett for Google Drive"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__id
msgid "ID"
msgstr "ID"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"Incoherent Google Drive %(drive)s: the model of the selected filter "
"%(filter)r is not matching the model of current template (%(filter_model)r, "
"%(drive_model)r)"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: google_drive
#: model_terms:ir.actions.act_window,help:google_drive.action_google_drive_users_config
msgid ""
"Link your own google drive templates to any record of Odoo. If you have "
"really specific documents you want your collaborator fill in, e.g. Use a "
"spreadsheet to control the quality of your product or review the delivery "
"checklist for each order in a foreign country, ... Its very easy to manage "
"them, link them to Odoo and use them to collaborate with your employees."
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__model_id
msgid "Model"
msgstr "Modell"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Please enter a valid Google Document URL."
msgstr "Legg inn en gyldig Google Document-URL"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__is_google_drive_token_generated
msgid "Refresh Token Generated"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__model
msgid "Related Model"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_resource_id
msgid "Resource Id"
msgstr "Ressurs-ID"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_config_view_search
msgid "Search Google Drive Config"
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/res_config_settings.py:0
#, python-format
msgid "Set up refresh token"
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"Something went wrong during the token generation. Please request again an "
"authorization code ."
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__name
msgid "Template Name"
msgstr "Malnavn"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_template_url
msgid "Template URL"
msgstr "Mal-URL"

#. module: google_drive
#: model:ir.actions.act_window,name:google_drive.action_google_drive_users_config
msgid "Templates"
msgstr "Maler"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "The Google Template cannot be found. Maybe it has been deleted."
msgstr "Google-malen ble ikke funnet. Kanskje har den blitt slettet?"

#. module: google_drive
#: model:ir.model.fields,help:google_drive.field_res_config_settings__google_drive_uri
msgid "The URL to generate the authorization code from Google"
msgstr "URL for å generere autorisasjonskode fra Google"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"The document filter must not include any 'dynamic' part, so it should not be"
" based on the current time or current user, for example."
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"The name of the attached document can use fixed or variable data. To distinguish between documents in\n"
"                                Google Drive, use fixed words and fields. For instance, in the example above, if you wrote Deco_Addict_%(name)s_Sales\n"
"                                in the Google Drive name field, the document in your Google Drive and in Odoo attachment will be named\n"
"                                'Deco_Addict_SO0001_Sales'."
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"The permission 'reader' for 'anyone with the link' has not been written on "
"the document"
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"There is no refresh code set for Google Drive. You can set it up from the "
"configuration panel."
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "This module will stop working after the 3rd October 2022 due to"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__google_drive_uri
msgid "URI"
msgstr "URI"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "changes in Google Authentication API"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"https://docs.google.com/document/d/1vOtpJK9scIQz6taD9tJRIETWbEw3fSiaQHArsJYcua4/edit"
msgstr ""
