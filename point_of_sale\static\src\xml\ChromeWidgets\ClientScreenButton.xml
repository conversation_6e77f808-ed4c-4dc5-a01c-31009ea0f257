<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="ClientScreenButton" owl="1">
        <div class="oe_status" t-on-click="onClick">
            <span class="message"><t t-esc="message" /></span>
            <div t-if="state.status === 'warning'" class="js_warning oe_icon oe_orange">
                <i class="fa fa-fw fa-desktop" role="img" aria-label="Client Screen Warning" title="Client Screen Warning"></i>
            </div>
             <div t-if="state.status === 'failure'" class="js_disconnected oe_icon oe_red">
                <i class="fa fa-fw fa-desktop" role="img" aria-label="Client Screen Disconnected" title="Client Screen Disconnected"></i>
            </div>
            <div t-if="state.status === 'success'" class="js_connected oe_icon oe_green">
                <i class="fa fa-fw fa-desktop" role="img" aria-label="Client Screen Connected" title="Client Screen Connected"></i>
            </div>
        </div>
    </t>

</templates>
