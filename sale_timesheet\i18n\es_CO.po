# #-#-#-#-#  es_CO.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_timesheet
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# #-#-#-#-#  es_CO.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_timesheet
#
# Translators:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-10-23 21:08+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Colombia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CO/)\n"
"Language: es_CO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  es_CO.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  es_CO.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_hr_employee
msgid "Employee"
msgstr "Empleado"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product Template"
msgstr "Plantilla del Producto"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "Project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_project_project_id
#, fuzzy
msgid "Project associated to this sale"
msgstr "Actividades del parte de horas asociadas a esta venta"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order
#, fuzzy
msgid "Sales Order"
msgstr ""
"#-#-#-#-#  es_CO.po (Odoo 9.0)  #-#-#-#-#\n"
"Orden de Venta\n"
"#-#-#-#-#  es_CO.po (Odoo 9.0)  #-#-#-#-#\n"
"Pedido de Venta"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
#, fuzzy
msgid "Sales Order Line"
msgstr ""
"#-#-#-#-#  es_CO.po (Odoo 9.0)  #-#-#-#-#\n"
"Línea Orden de Venta\n"
"#-#-#-#-#  es_CO.po (Odoo 9.0)  #-#-#-#-#\n"
"Línea de Pedido de Venta"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.project_time_mode_id_duplicate_xmlid
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right "
"unit of measure in your employees."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_hr_employee_timesheet_cost
msgid "Timesheet Cost"
msgstr "Costo del Parte de Horas"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.project_time_mode_id_duplicate_xmlid
#, fuzzy
msgid "Timesheet UoM"
msgstr "Parte de Horas"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_timesheet_count
msgid "Timesheet activities"
msgstr "Actividades del parte de horas"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_timesheet_ids
msgid "Timesheet activities associated to this sale"
msgstr "Actividades del parte de horas asociadas a esta venta"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_employee_extd_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "Timesheets"
msgstr "Partes de Horas"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_timesheet.py:128
#, python-format
msgid ""
"You can use only one product on timesheet within the same sale order. You "
"should split your order to include only one contract based on time and "
"material."
msgstr ""
"Puede usar sólo un producto en el parte de horas dentro de la misma orden de "
"venta. Debería dividir su orden para incluir sólo un contrato basado en "
"tiempos y materiales."

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
#, fuzzy
msgid "account analytic line"
msgstr "Línea Analítica"
