<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="l10nse_chart_template" model="account.chart.template">
            <field name="name">Swedish BAS Chart of Account Minimalist</field>
            <field name="currency_id" ref="base.SEK"/>
            <field name="bank_account_code_prefix">193</field>
            <field name="cash_account_code_prefix">191</field>
            <field name="transfer_account_code_prefix">194</field>
            <field name="code_digits">4</field>
            <field name="country_id" ref="base.se"/>
        </record>

        <record id="l10nse_chart_template_K2" model="account.chart.template">
            <field name="name">Swedish BAS Chart of Account complete K2</field>
            <field name="parent_id" ref="l10nse_chart_template"/>
            <field name="currency_id" ref="base.SEK"/>
            <field name="bank_account_code_prefix">193</field>
            <field name="cash_account_code_prefix">191</field>
            <field name="transfer_account_code_prefix">194</field>
            <field name="code_digits">4</field>
            <field name="country_id" ref="base.se"/>
        </record>

        <record id="l10nse_chart_template_K3" model="account.chart.template">
            <field name="name">Swedish BAS Chart of Account complete K3</field>
            <field name="parent_id" ref="l10nse_chart_template_K2"/>
            <field name="currency_id" ref="base.SEK"/>
            <field name="bank_account_code_prefix">193</field>
            <field name="cash_account_code_prefix">191</field>
            <field name="transfer_account_code_prefix">194</field>
            <field name="code_digits">4</field>
            <field name="country_id" ref="base.se"/>
        </record>
    </data>
</odoo>
