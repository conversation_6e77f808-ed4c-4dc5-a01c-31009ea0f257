# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm
# 
# Translators:
# <PERSON> ALT <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-30 09:40+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: <PERSON> ALT <<EMAIL>>, 2019\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__meeting_count
#: model:ir.model.fields,field_description:crm.field_res_partner__meeting_count
#: model:ir.model.fields,field_description:crm.field_res_users__meeting_count
msgid "# Meetings"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "% (Estimated by Odoo)"
msgstr ""

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "% endif"
msgstr ""

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid ""
"% set email = object.env['crm.team'].search([('alias_name','!=', False)],limit=1).alias_id.display_name\n"
"    % if email\n"
"    <strong style=\"font-size: 16px;\">Try the mail gateway</strong>"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"<b>Choose a name</b> for your opportunity, example: <i>'Need a new "
"website'</i>"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"<b>Drag &amp; drop opportunities</b> between columns as you progress in your"
" sales cycle."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"<b>Invite coworkers</b> via email.<br/><i>Enter one email per line.</i>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "<b>Predictive Lead Scoring</b>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<i class=\"fa fa-ban\" style=\"color: red;\" role=\"img\" title=\"This email"
" is blacklisted for mass mailing\" aria-label=\"Blacklisted\" "
"attrs=\"{'invisible': ['|', ('is_blacklisted', '=', False), "
"('partner_address_email', '!=', False)]}\" groups=\"base.group_user\"/>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<i class=\"fa fa-ban\" style=\"color: red;\" role=\"img\" title=\"This email"
" is blacklisted for mass mailing\" aria-label=\"Blacklisted\" "
"attrs=\"{'invisible': ['|', ('partner_is_blacklisted', '=', False), "
"('partner_address_email', '=', False)]}\" groups=\"base.group_user\"/>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<i class=\"fa fa-ban\" style=\"color: red;\" role=\"img\" title=\"This email"
" is blacklisted for mass mailing\" aria-label=\"Blacklisted\" "
"attrs=\"{'invisible': [('is_blacklisted', '=', False)]}\" "
"groups=\"base.group_user\"/>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead_kanban
msgid "<i class=\"fa fa-comments\" aria-label=\"Messages\" role=\"img\"/>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "<i class=\"fa fa-comments\" aria-label=\"Unread messages\" role=\"img\"/>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-calendar\" aria-label=\"Meetings\" role=\"img\" "
"title=\"Meetings\"/>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-star\" aria-label=\"Favorites\" role=\"img\" "
"title=\"Favorites\"/>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<i class=\"fa fa-gear\" role=\"img\" title=\"Switch to automatic "
"probability\" aria-label=\"Switch to automatic probability\"/>"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"<p class='o_view_nocontent_smiling_face'>Add new opportunities</p><p>\n"
"    Looks like you are not a member of a Sales Team. You should add yourself\n"
"    as a member of one of the Sales Team.\n"
"</p>"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"<p><b>Send messages</b> to your prospect and get replies automatically "
"attached to this opportunity.</p><p class=\"mb0\">Type <i>'@'</i> to mention"
" people - it's like cc-ing on emails.</p>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_kanban
msgid "<small>Leads:</small>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_kanban
msgid "<small>Opportunities:</small>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('meeting_count', '&lt;', 2)]}\"> Meetings</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('meeting_count', '&gt;', 1)]}\"> Meeting</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "<span class=\"oe_grey p-2\"> at </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Accept Emails From"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction
msgid "Action Needed"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__active
#: model:ir.model.fields,field_description:crm.field_crm_lead__active
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__active
msgid "Active"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_ids
#: model:ir.ui.menu,name:crm.crm_activity_report_menu
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Activities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activities Analysis"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_done
msgid "Activities Done Target"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activity"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__body
msgid "Activity Description"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_state
msgid "Activity State"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__mail_activity_type_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activity Type"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_menu_config_activity_types
msgid "Activity Types"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Add a description..."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Add a qualification step before the creation of an opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Address"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_id
msgid "Alias"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Analysis"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Apply deduplication"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Archived"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Assign opportunities to"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Assign these opportunities to"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Assign this opportunity to"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Assign to"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_open
msgid "Assignation Date"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__author_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Assigned To"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__automated_probability
msgid "Automated Probability"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Avg. of Probability"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_blacklisted
msgid "Blacklist"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Boom! Team record for the past 30 days."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_bounce
msgid "Bounce"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_root
#: model_terms:ir.ui.view,arch_db:crm.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "CRM"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_activity_report
msgid "CRM Activity Analysis"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_stage
msgid "CRM Stages"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_calendar_event
msgid "Calendar Event"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__campaign_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Campaign"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Cancel"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_team_act_tree
msgid "Cases by Sales Team"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_tree
msgid "Channel"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_leads
msgid ""
"Check this box to filter and qualify incoming requests as leads before "
"converting them into opportunities and assigning them to a salesperson."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_opportunities
msgid "Check this box to manage a presales process with opportunities."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"Choose an activity type.<br/>You can customize them in the general settings."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__city
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "City"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__tag_ids
msgid ""
"Classify and analyze your lead/opportunity categories like: Training, "
"Service"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Click here to <b>add your opportunity</b>."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"Click here to <b>create your first opportunity</b> and add it to your "
"pipeline."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Click on the opportunity to zoom in."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_closed
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_closed
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Closed Date"
msgstr ""

#. module: crm
#: code:addons/crm/wizard/crm_lead_to_opportunity.py:0
#, python-format
msgid "Closed/Dead leads cannot be converted into opportunities."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__color
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__color
msgid "Color Index"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__company_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Company"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_name
msgid "Company Name"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Completed Last 365 Days"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Completion Date"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_config
msgid "Configuration"
msgstr "Konfiguratioun"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Configuration options are available in the Settings app."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Configure domain name"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Consider leads created as of the"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_res_partner
msgid "Contact"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Contact Information"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__contact_name
msgid "Contact Name"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__name
msgid "Conversion Action"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_conversion
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_conversion
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Conversion Date"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Conversion Date from Lead to Opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Conversion Options"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner_mass
msgid "Convert Lead to Opportunity (in mass)"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner
msgid "Convert Lead to Opportunity (not in mass)"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunity"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_crm_send_mass_convert
msgid "Convert to opportunities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.actions.act_window,name:crm.action_crm_lead2opportunity_partner
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__name__convert
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__name__convert
#, python-format
msgid "Convert to opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Convert visitors of your website into leads and perform data enrichment "
"based on their IP address"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__email_state__correct
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__phone_state__correct
msgid "Correct"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr ""

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_country_id
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__country_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__country_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Country"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_iap_lead_website
msgid "Create Leads/Opportunities from your website's traffic"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Create Opportunity"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__lead_mining_in_pipeline
msgid "Create a lead mining request directly from the opportunity pipeline."
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__create
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__create
#: model:ir.model.fields.selection,name:crm.selection__crm_partner_binding__action__create
msgid "Create a new customer"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
#, python-format
msgid "Create a new lead"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.relate_partner_opportunities
msgid "Create an new opportunity related to this customer"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Create an opportunity in your pipeline"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Create leads from incoming emails"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_tag_action
msgid "Create new tags for your opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_tag_action
msgid ""
"Create tags that fit your business (product structure, sales type, etc.) to "
"better manage and track your opportunities."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_uid
msgid "Created by"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_date
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__create_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_date
msgid "Created on"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_create_date
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Creation Date"
msgstr ""

#. module: crm
#: model:ir.actions.server,name:crm.action_your_pipeline
msgid "Crm: My Pipeline"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_currency
msgid "Currency"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__partner_id
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#, python-format
msgid "Customer"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Customer Email"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.res_partner_menu_customer
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Customers"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Date Closed"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_open
msgid "Days to Assign"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_close
msgid "Days to Close"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_alias_prefix
msgid "Default Alias Name for Leads"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid "Define a new lost reason"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Delete"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__name
msgid "Description"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_digest_digest
msgid "Digest"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__display_name
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__display_name
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__display_name
#: model:ir.model.fields,field_description:crm.field_crm_stage__display_name
msgid "Display Name"
msgstr ""

#. module: crm
#: code:addons/crm/models/digest.py:0 code:addons/crm/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__nothing
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__nothing
#: model:ir.model.fields.selection,name:crm.selection__crm_partner_binding__action__nothing
msgid "Do not link to a customer"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Dropdown menu"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Edit"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_from
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Email"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Email Alias"
msgstr ""

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_email_state
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_state
msgid "Email Quality"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__email_from
msgid "Email address of the contact"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_cc
msgid "Email cc"
msgstr ""

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid ""
"Email sent to <strong>${email}</strong> generate opportunities in your "
"pipeline.<br>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Emails received to that address generate new leads not assigned to any Sales"
" Team yet. This can be made when converting them into opportunities. "
"Incoming emails can be automatically assigned to specific Sales Teams. To do"
" so, set an email alias on the Sales Team."
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__lead_enrich_auto__auto
msgid "Enrich all leads automatically"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__lead_enrich_auto
msgid "Enrich lead automatically"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__lead_enrich_auto__manual
msgid "Enrich leads on demand only"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Enrich your leads automatically with company data based on their email "
"address"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_iap_lead_enrich
msgid ""
"Enrich your leads automatically with company data based on their email "
"address."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__requirements
msgid ""
"Enter here the internal requirements for this stage (ex: Offer sent to "
"customer). It will appear as a tooltip over the stage's name."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__date_deadline
msgid "Estimate of the date on which the opportunity will be won."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Exception Activities"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_deadline
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_deadline
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Expected Closing"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__planned_revenue
msgid "Expected Revenue"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Expected Revenues"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Extended Filters"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Extra Info"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__field_id
msgid "Field"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__name
msgid "Field Label"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_scoring_frequency_field
msgid "Fields that can be used for predictive lead scoring computation"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Fields used in probability computation:"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__fold
msgid "Folded in Pipeline"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Follow-up"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_follower_ids
msgid "Followers"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__force_assignation
msgid "Force assignation"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "From %s : %s"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Future Activities"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_iap_lead
msgid "Generate new leads based on their country, industries, size, etc."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Generate new leads based on their country, industry, size, etc."
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_lost
msgid "Get Lost Reason"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Give your team the requirements to move an opportunity to this stage."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Go, go, go! Congrats for your first deal."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Good job! You completed the tour of the CRM app."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Group By"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__2
msgid "High"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__id
#: model:ir.model.fields,field_description:crm.field_crm_lead__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__id
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__id
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__id
#: model:ir.model.fields,field_description:crm.field_crm_stage__id
msgid "ID"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction
#: model:ir.model.fields,help:crm.field_crm_lead__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_res_partner__team_id
#: model:ir.model.fields,help:crm.field_res_users__team_id
msgid ""
"If set, this Sales Team will be used for sales and assignations related to "
"this partner"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__is_blacklisted
#: model:ir.model.fields,help:crm.field_crm_lead__partner_is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__force_assignation
msgid "If unchecked, this will leave the salesman of duplicated opportunities"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.menu_import_crm
msgid "Import & Synchronize"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Import Template for Leads & Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
msgid "Include archived"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Incoming Emails"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__email_state__incorrect
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__phone_state__incorrect
msgid "Incorrect"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Internal Notes"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__is_won
msgid "Is Won Stage?"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_automated_probability
msgid "Is automated probability?"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__function
msgid "Job Position"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__kanban_state
msgid "Kanban State"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created_value
msgid "Kpi Crm Lead Created Value"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won_value
msgid "Kpi Crm Opportunities Won Value"
msgstr ""

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_lang_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__lang_id
msgid "Language"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__lang_id
msgid "Language of the lead."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_action_last
msgid "Last Action"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason____last_update
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity____last_update
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding____last_update
#: model:ir.model.fields,field_description:crm.field_crm_stage____last_update
msgid "Last Modified on"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_last_stage_update
msgid "Last Stage Update"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_uid
msgid "Last Updated by"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_date
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__write_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_date
msgid "Last Updated on"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Late Activities"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_activity_report__lead_type__lead
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__type__lead
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Lead"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_utm_campaign__lead_count
msgid "Lead Count"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Enrichment"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Generation"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Mining"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_scoring_frequency
msgid "Lead Scoring Frequency"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_fields
msgid "Lead Scoring Frequency Fields"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_fields_str
msgid "Lead Scoring Frequency Fields in String"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_start_date
msgid "Lead Scoring Starting Date"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_start_date_str
msgid "Lead Scoring Starting Date in String"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_tag
msgid "Lead Tag"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_tag_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_tag_tree
msgid "Lead Tags"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Lead or Opportunity"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_lead
#: model:ir.actions.act_window,name:crm.crm_lead_all_leads
#: model:ir.model.fields,field_description:crm.field_crm_team__use_leads
#: model:ir.model.fields,field_description:crm.field_res_config_settings__group_use_lead
#: model:ir.ui.menu,name:crm.crm_menu_leads
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_form
msgid "Leads"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_lead_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot_lead
msgid "Leads Analysis"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_lead_salesteam
msgid ""
"Leads Analysis allows you to check different CRM related information like "
"the treatment delays or number of leads per state. You can sort out your "
"leads analysis by different groups to get accurate grained analysis."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_calendar_view_leads
msgid "Leads Generation"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_activity
msgid "Leads or Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are assigned to me"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are not assigned"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid ""
"Leads that you selected that have duplicates. If the list is empty, it means"
" that no duplicates were found"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Leads with existing duplicates (for information)"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__opportunity_ids
msgid "Leads/Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Let statistical analysis determine the probability to close a lead"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Let's schedule an activity."
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__exist
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__exist
#: model:ir.model.fields.selection,name:crm.selection__crm_partner_binding__action__exist
msgid "Link to an existing customer"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_id
msgid ""
"Linked partner (optional). Usually created when converting the lead. You can"
" find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__email_cc
msgid "List of cc from incoming emails."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Lost"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__lost_count
msgid "Lost Count"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_lost_action
#: model:ir.model.fields,field_description:crm.field_crm_lead__lost_reason
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__lost_reason_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Lost Reason"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lost_reason_action
#: model:ir.ui.menu,name:crm.menu_crm_lost_reason
msgid "Lost Reasons"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__0
msgid "Low"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__generate_lead_from_alias
msgid "Manual Assignation of Emails"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mark Lost"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mark Won"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mark as Lost"
msgstr ""

#. module: crm
#: model:ir.actions.server,name:crm.action_mark_as_lost
msgid "Mark as lost"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Marketing"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__medium_id
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__1
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Medium"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Meeting scheduled at '%s'<br> Subject: %s <br> Duration: %s hours"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.act_crm_opportunity_calendar_event_new
#: model:ir.model.fields,field_description:crm.field_res_partner__meeting_ids
#: model:ir.model.fields,field_description:crm.field_res_users__meeting_ids
#: model_terms:ir.ui.view,arch_db:crm.view_partners_form_crm1
msgid "Meetings"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_merge_opportunities
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge Leads/Opportunities"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_merge_opportunity
msgid "Merge Opportunities"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Merge with existing leads/opportunities of each partner"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__name__merge
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__name__merge
msgid "Merge with existing opportunities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Merged lead"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Merged leads"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Merged opportunities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Merged opportunity"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_ids
msgid "Messages"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Misc"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__mobile
msgid "Mobile"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "My Leads"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "My Opportunities"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.menu_crm_opportunities
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "My Pipeline"
msgstr ""

#. module: crm
#: model:crm.stage,name:crm.stage_lead1
msgid "New"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created
msgid "New Leads/Opportunities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "New Opportunities"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_opportunity_form
msgid "New Opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_tree_activity
msgid "Next Activities"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__kanban_state__green
msgid "Next activity is planned"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__kanban_state__red
msgid "Next activity late"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "No Subject"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__kanban_state__grey
msgid "No next activity planned"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "No salesperson"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_normalized
msgid "Normalized Email"
msgstr ""

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_3
msgid "Not enough stock"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__description
msgid "Notes"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_count
msgid "Number of open opportunities"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__overdue_opportunities_count
msgid "Number of overdue opportunities"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"Odoo helps you keep track of your sales pipeline to follow\n"
"                    up potential sales and better forecast your future revenues."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Open Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Open Opportunity"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lost_reason
msgid "Opp. Lost Reason"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_opportunity
#: model:ir.actions.act_window,name:crm.crm_lead_opportunities
#: model:ir.actions.act_window,name:crm.relate_partner_opportunities
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__opportunity_ids
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__opportunity_ids
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_ids
#: model:ir.model.fields,field_description:crm.field_res_users__opportunity_ids
#: model:ir.ui.menu,name:crm.menu_crm_config_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_partners_form_crm1
msgid "Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Opportunities Analysis"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_opportunity_salesteam
msgid ""
"Opportunities Analysis gives you an instant access to your opportunities "
"with information such as the expected revenue, planned cost, missed "
"deadlines or the number of interactions per opportunity. This report is "
"mainly used by the sales manager in order to do the periodic review with the"
" channels of the sales pipeline."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_amount
msgid "Opportunities Revenues"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won
msgid "Opportunities Won"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunities that are assigned to me"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_calendar_event__opportunity_id
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__name
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_count
#: model:ir.model.fields,field_description:crm.field_res_users__opportunity_count
#: model:ir.model.fields.selection,name:crm.selection__crm_activity_report__lead_type__opportunity
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__type__opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunity"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_utm_campaign__opportunity_count
msgid "Opportunity Count"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_create
#: model:mail.message.subtype,name:crm.mt_salesteam_lead
msgid "Opportunity Created"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_lost
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_lost
msgid "Opportunity Lost"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_restored
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_restored
msgid "Opportunity Restored"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_stage
msgid "Opportunity Stage Changed"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_won
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_won
msgid "Opportunity Won"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_create
msgid "Opportunity created"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_lost
msgid "Opportunity lost"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_restored
msgid "Opportunity restored"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_won
msgid "Opportunity won"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_team_overdue_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Overdue Opportunities"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__overdue_opportunities_amount
msgid "Overdue Opportunities Revenues"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Overdue Opportunity"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_user_id
msgid "Owner"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_address_email
msgid "Partner Contact Email"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_address_name
msgid "Partner Contact Name"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_address_phone
msgid "Partner Contact Phone"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_is_blacklisted
msgid "Partner is blacklisted"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_partner_binding
msgid "Partner linking/binding in CRM wizard"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Phone"
msgstr ""

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_phone_state
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_state
msgid "Phone Quality"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#: model:ir.actions.act_window,name:crm.crm_lead_action_pipeline
#: model:ir.model.fields,field_description:crm.field_crm_team__use_opportunities
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu
#: model:ir.ui.menu,name:crm.menu_crm_config_lead
#, python-format
msgid "Pipeline"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action
#: model:ir.actions.act_window,name:crm.crm_activity_report_action_team
msgid "Pipeline Activities"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_opportunity_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot
msgid "Pipeline Analysis"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action
msgid ""
"Pipeline Analysis gives you an instant access to\n"
"                your opportunities with information such as the expected revenue, planned cost,\n"
"                missed deadlines or the number of interactions per opportunity. This report is\n"
"                mainly used by the sales manager in order to do the periodic review with the\n"
"                teams of the sales pipeline."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid ""
"Please select more than one element (lead or opportunity) from the list "
"view."
msgstr ""

#. module: crm
#: model:ir.actions.server,name:crm.website_crm_score_cron_ir_actions_server
#: model:ir.cron,cron_name:crm.website_crm_score_cron
#: model:ir.cron,name:crm.website_crm_score_cron
msgid "Predictive Lead Scoring: Rebuild Frequencies table"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__priority
msgid "Priority"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__probability
msgid "Probability"
msgstr ""

#. module: crm
#: model:crm.stage,name:crm.stage_lead3
msgid "Proposition"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__expected_revenue
msgid "Prorated Revenue"
msgstr ""

#. module: crm
#: model:crm.stage,name:crm.stage_lead2
msgid "Qualified"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"Ready to boost your sales? Your <b>Pipeline</b> can be found here, under the"
" <b>CRM</b> app."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__referred
msgid "Referred By"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__action
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__action
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__action
msgid "Related Customer"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_report
msgid "Reporting"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__requirements
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Requirements"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Restore"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_sales
msgid "Sales"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
msgid "Sales Person"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_team
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__team_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__team_id
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_id
#: model:ir.model.fields,field_description:crm.field_res_partner__team_id
#: model:ir.model.fields,field_description:crm.field_res_users__team_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Sales Team"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Sales Team Settings"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_config
msgid "Sales Teams"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_ids
msgid "Salesmen"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__user_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Salesperson"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Search Leads"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Search Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Select Leads/Opportunities"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_lead_mass_mail
msgid "Send email"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__sequence
msgid "Sequence"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid "Set a new stage in your opportunity pipeline"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_config_settings_action
#: model:ir.ui.menu,name:crm.crm_config_settings_menu
msgid "Settings"
msgstr ""

#. module: crm
#: model:res.groups,name:crm.group_use_lead
msgid "Show Lead Menu"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Show all opportunities for which the next action date is before today"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only lead"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid ""
"Some examples of lost reasons: \"We don't have people/skill\", \"Price too "
"high\""
msgstr ""

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_source_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__source_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Source"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__team_id
msgid ""
"Specific team that uses this stage. Other teams will not be able to see or "
"use this stage."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__stage_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__stage_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Stage"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_stage
msgid "Stage Changed"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__name
msgid "Stage Name"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_stage_search
msgid "Stage Search"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_stage
msgid "Stage changed"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_stage_action
#: model:ir.ui.menu,name:crm.menu_crm_lead_stage_act
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_tree
msgid "Stages"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid ""
"Stages allow salespersons to easily track how a specific opportunity\n"
"            is positioned in the sales cycle."
msgstr ""

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_state_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__state_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "State"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street
msgid "Street"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Street 2..."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Street..."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street2
msgid "Street2"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "Submit"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__subtype_id
msgid "Subtype"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Tag"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__name
msgid "Tag Name"
msgstr ""

#. module: crm
#: model:ir.model.constraint,message:crm.constraint_crm_lead_tag_name_uniq
msgid "Tag name already exists !"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_tag_action
#: model:ir.model.fields,field_description:crm.field_crm_lead__tag_ids
#: model:ir.ui.menu,name:crm.menu_crm_lead_categ
msgid "Tags"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.sales_team_menu_team_pipeline
msgid "Team Pipelines"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_id
msgid ""
"The email address associated with this channel. New emails received will "
"automatically create new leads assigned to the channel."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_name
msgid ""
"The name of the future partner company that will be created while converting"
" the lead into opportunity"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""

#. module: crm
#: model:ir.model.constraint,message:crm.constraint_crm_lead_check_probability
msgid "The probability of closing the deal should be between 0% and 100%!"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid ""
"This bar allows to filter the opportunities based on scheduled activities."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "This can be used to compute statistical probability to close a lead"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "This opportunity has <b>no activity planned</b>."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_lead
msgid "This report analyses the source of your leads."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "This target does not exist."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__title
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Title"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Today Activities"
msgstr ""

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_1
msgid "Too expensive"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Tracking"
msgstr ""

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Try Now"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_type
#: model:ir.model.fields,field_description:crm.field_crm_lead__type
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Type"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_activity_report__lead_type
#: model:ir.model.fields,help:crm.field_crm_lead__type
msgid "Type is used to separate Leads and Opportunities"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_utm_campaign
msgid "UTM Campaign"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Unassigned"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Unassigned Lead"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__unassigned_leads_count
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Unassigned Leads"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_unread
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead_kanban
msgid "Unread Messages"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_utm_campaign__crm_lead_activated
msgid "Use Leads"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Use an External Email Server"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__each_exist_or_create
msgid "Use existing partner or create"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
msgid ""
"Use leads if you need a qualification step before creating an\n"
"                    opportunity or a customer. It can be a business card you received,\n"
"                    a contact form filled in your website, or a file of unqualified\n"
"                    prospects you import, etc."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Use leads if you need a qualification step before creating an opportunity or"
" a customer. It can be a business card you received, a contact form filled "
"in your website, or a file of unqualified prospects you import, etc. Once "
"qualified, the lead can be converted into a business opportunity and/or a "
"new customer in your address book."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid "Use lost reasons to explain why an opportunity is lost."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.relate_partner_opportunities
msgid ""
"Use opportunities to keep track of your sales pipeline, follow\n"
"                up potential sales and better forecast your future revenues."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Use the breadcrumbs to <b>go back to your sales pipeline</b>."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__user_login
msgid "Used to log into the system"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__sequence
msgid "Used to order stages. Lower is better."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_email
msgid "User Email"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_login
msgid "User Login"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_res_users
msgid "Users"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__value
msgid "Value"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__variable
msgid "Variable"
msgstr ""

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__3
msgid "Very High"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Visits to Leads"
msgstr ""

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_2
msgid "We don't have people/skills"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website
msgid "Website"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website
msgid "Website of the contact"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__team_id
msgid ""
"When sending mails, the default email address is taken from the Sales Team."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0 model:crm.stage,name:crm.stage_lead4
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Won"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__won_count
msgid "Won Count"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_won
msgid "Won in Opportunities Target"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Yeah! Deal of the last 7 days for the team."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "You don't have the access needed to run this cron."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "You just beat your personal record for the past 30 days."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "You just beat your personal record for the past 7 days."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.relate_partner_opportunities
msgid ""
"You will be able to plan meetings and log activities from\n"
"                opportunities, convert them into quotations, attach related\n"
"                documents, track all discussions, and much more."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"You will be able to plan meetings and phone calls from\n"
"                    opportunities, convert them into quotations, attach related\n"
"                    documents, track all discussions, and much more."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "ZIP"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__zip
msgid "Zip"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "e.g. Product Pricing"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "e.g. https://www.odoo.com"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "or send an email to %s"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_count
msgid "team_count"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "unknown"
msgstr ""
