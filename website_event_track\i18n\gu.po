# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_event_track
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: Spellbound Soft Solutions <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_track
#: model:mail.template,body_html:website_event_track.mail_template_data_track_confirmation
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div>\n"
"    Dear ${object.partner_name or ''}<br/>\n"
"    We are pleased to inform you that your proposal ${object.name} has been accepted and confirmed for the event ${object.event_id.name}.\n"
"    <br/>\n"
"    You will find more details here:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"/event/${object.event_id.id}/\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            View Event\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    Thank you,\n"
"</div>\n"
"            "
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "<b>Date</b><br/>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Mail</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Phone</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Proposed By</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Speakers Biography</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Talk Introduction</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid ""
"<br/>\n"
"                            <b>Duration</b><br/>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid ""
"<br/>\n"
"                            <b>Location</b><br/>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda
msgid "<span id=\"search_number\">0</span> Found"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Lightning Talks</strong>. These are 30 minutes talks on many\n"
"                                    different topics. Most topics are accepted in lightning talks."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid "<strong>Participate on Twitter</strong>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Regular Talks</strong>. These are standard talks with slides,\n"
"                                    alocated in slots of 60 minutes."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track7
msgid "A technical explanation of how to use computer design apps"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__kanban_state
msgid ""
"A track's kanban state indicates special situations affecting it:\n"
" * Grey is the default situation\n"
" * Red indicates something is preventing the progress of this track\n"
" * Green indicates the track is ready to be pulled to the next stage"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "About The Author"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_done
msgid "Accepted Stage"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction
msgid "Action Needed"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__active
msgid "Active"
msgstr "સક્રિય"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_ids
msgid "Activities"
msgstr "પ્રવૃત્તિઓ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_state
msgid "Activity State"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid "Add a new stage in the task pipeline"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid "Add a new track"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track19
msgid "Advanced lead management : tips and tricks from the fields"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track13
msgid "Advanced reporting"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/event.py:97
#, python-format
msgid "Agenda"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_filter
msgid "All Tags"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Allow video and audio recording of their\n"
"                                    presentation, for publishing on our website."
msgstr ""

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage2
msgid "Announced"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Application"
msgstr "કાર્યક્રમ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Archived"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__allowed_track_tag_ids
msgid "Available Track Tags"
msgstr ""

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type1
msgid "Bronze"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Call for Proposals"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_cancel
msgid "Canceled Stage"
msgstr ""

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage5
msgid "Cancelled"
msgstr "રદ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__color
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__color
msgid "Color Index"
msgstr ""

#. module: website_event_track
#: model:event.type,name:website_event_track.event_type_data_tracks
msgid "Conference"
msgstr ""

#. module: website_event_track
#: model:mail.template,subject:website_event_track.mail_template_data_track_confirmation
msgid "Confirmation of ${object.name}"
msgstr ""

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage1
msgid "Confirmed"
msgstr "સમર્થિત"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__create_uid
msgid "Created by"
msgstr "બનાવનાર"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_date
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__create_date
msgid "Created on"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Date"
msgstr "તારીખ"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid ""
"Define the steps that will be used in the event from the\n"
"                creation of the track, up to the closing of the track.\n"
"                You will use these stages in order to track the progress in\n"
"                solving an event track."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Delete"
msgstr "કાઢી નાંખો"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track25
msgid "Design contest (entire afternoon)"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track24
msgid "Design contest (entire day)"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track6
msgid "Detailed roadmap of our new products"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track11
msgid "Discover our new design team"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__display_name
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "Documents"
msgstr "દસ્તાવેજો"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Dropdown menu"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__duration
msgid "Duration"
msgstr "સમયગાળો"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Edit Track"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__mail_template_id
msgid "Email Template"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_event
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__event_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_id
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event"
msgstr "ઘટના"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_type
msgid "Event Category"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "Event Location"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_location
msgid "Event Locations"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_proposal_menu_ids
#: selection:website.event.menu,menu_type:0
msgid "Event Proposals Menus"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_sponsor
msgid "Event Sponsor"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_sponsor_type
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_sponsor_type_tree
msgid "Event Sponsor Type"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_sponsor_type_form
msgid "Event Sponsor Types"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_sponsor_search
msgid "Event Sponsors"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tree
msgid "Event Track"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_location
msgid "Event Track Location"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_stage
msgid "Event Track Stage"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_tree
msgid "Event Track Tag"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_sponsor_from_event
#: model:ir.actions.act_window,name:website_event_track.action_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_from_event
#: model:ir.ui.menu,name:website_event_track.menu_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_calendar
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event Tracks"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_menu_ids
#: selection:website.event.menu,menu_type:0
msgid "Event Tracks Menus"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Fill this form to propose your talk."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda
msgid "Filter Tracks..."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid ""
"Find out what people see and say about this event,\n"
"                        and join the conversation."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__fold
msgid "Folded in Kanban"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_follower_ids
msgid "Followers"
msgstr "અનુયાયીઓ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Future Activities"
msgstr ""

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type3
msgid "Gold"
msgstr "સોનું"

#. module: website_event_track
#: selection:event.track,kanban_state:0
msgid "Green"
msgstr ""

#. module: website_event_track
#: selection:event.track,kanban_state:0
msgid "Grey"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Group By"
msgstr ""

#. module: website_event_track
#: selection:event.track,priority:0
msgid "High"
msgstr "વધારે"

#. module: website_event_track
#: selection:event.track,priority:0
msgid "Highest"
msgstr "ઉચ્ચતમ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track21
msgid "House of World Cultures"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track18
msgid "How to build your marketing strategy within a competitive environment"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track15
msgid "How to communicate with your community"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track1
msgid "How to design a new piece of furniture"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track4
msgid "How to develop automated processes"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track16
msgid "How to follow us on the social media"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track9
msgid "How to improve your quality processes"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track2
msgid "How to integrate hardware materials in your pieces of furniture"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track8
msgid "How to optimize your sales, from leads to sales orders"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__id
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__id
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__id
msgid "ID"
msgstr "ઓળખ"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_unread
msgid "If checked new messages require your attention."
msgstr "જો ચેક કરેલા નવા સંદેશા માટે તમારું ધ્યાન આવશ્યક છે"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction
msgid "If checked, new messages require your attention."
msgstr "જો ચેક કરેલું હોય, તો નવા સંદેશા માટે તમારું ધ્યાન આવશ્યક છે."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "જો ચેક કરેલું હોય, તો કેટલાક મેસેજીસમાં ડિલિવરીની ભૂલ છે."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__mail_template_id
msgid ""
"If set an email will be sent to the customer when the track reaches this "
"step."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__image
msgid "Image"
msgstr "ચિત્ર"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Introduction"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_published
msgid "Is published"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state
msgid "Kanban State"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track23
msgid "Key Success factors selling our furniture"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag____last_update
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu____last_update
msgid "Last Modified on"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__write_uid
msgid "Last Updated by"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_date
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__write_date
msgid "Last Updated on"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Late Activities"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track12
msgid "Latest trends"
msgstr ""

#. module: website_event_track
#: model:ir.ui.menu,name:website_event_track.menu_event_track_location
msgid "Locations"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__image_medium
msgid "Logo"
msgstr "લોગો"

#. module: website_event_track
#: selection:event.track,priority:0
msgid "Low"
msgstr "નીચુ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track31
msgid "Lunch"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: website_event_track
#: selection:event.track,priority:0
msgid "Medium"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor__image_medium
#: model:ir.model.fields,help:website_event_track.field_event_track__image
msgid ""
"Medium-sized image of this contact. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__menu_id
msgid "Menu"
msgstr "મેનૂ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_ids
msgid "Messages"
msgstr "સંદેશાઓ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track22
msgid "Minimal but efficient design"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track30
msgid "Morning break"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "My Activities"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track27
msgid "My Company global presentation"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track20
msgid "New Certification Program"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_event_track
msgid "New Track"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_event_track
msgid "New Track Created"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "New track proposal"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_summary
msgid "Next Activity Summary"
msgstr "આગલું પ્રવૃત્તિ સારાંશ"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks
msgid "No tracks found!"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error_counter
msgid "Number of error"
msgstr "ભૂલની સંખ્યા"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "સંદેશાઓની સંખ્યા જે ક્રિયા માટે જરૂરી છે"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "વિતરણ ભૂલ સાથે સંદેશાઓની સંખ્યા"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_unread_counter
msgid "Number of unread messages"
msgstr "ન વાંચેલા સંદેશાની સંખ્યા"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor
msgid "Our Sponsors"
msgstr ""

#. module: website_event_track
#: selection:event.track,activity_state:0
msgid "Overdue"
msgstr "મુદતવીતી"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track14
msgid "Partnership programs"
msgstr ""

#. module: website_event_track
#: selection:event.track,activity_state:0
msgid "Planned"
msgstr "આયોજિત"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track3
msgid "Portfolio presentation"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "Practical Info"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__priority
msgid "Priority"
msgstr "પ્રાથમિકતા"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage0
msgid "Proposal"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Proposals are closed!"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track_proposal
msgid "Proposals on Website"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "Proposed by"
msgstr ""

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage3
msgid "Published"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid ""
"Put here the list of documents, like slides of\n"
"                            the presentations. Remove the above t-if when\n"
"                            it's implemented."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track10
msgid "Raising qualitive insights from your customers"
msgstr ""

#. module: website_event_track
#: selection:event.track,kanban_state:0
msgid "Red"
msgstr "લાલ"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage4
msgid "Refused"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__user_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Responsible"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_user_id
msgid "Responsible User"
msgstr "જવાબદાર વપરાશકર્તા"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__location_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__name
msgid "Room"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__sequence
msgid "Sequence"
msgstr "ક્રમ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type2
msgid "Silver"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid "Social Stream"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_id
msgid "Speaker"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_biography
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Speaker Biography"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:164
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_email
#, python-format
msgid "Speaker Email"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_name
msgid "Speaker Name"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_phone
msgid "Speaker Phone"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:184
#, python-format
msgid "Speakers"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__sponsor_count
msgid "Sponsor Count"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type__name
msgid "Sponsor Type"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_sponsor_type
#: model:ir.ui.menu,name:website_event_track.menu_event_sponsor_type
msgid "Sponsor Types"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__url
msgid "Sponsor Website"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__partner_id
msgid "Sponsor/Customer"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor__sponsor_type_id
msgid "Sponsoring Type"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__sponsor_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_form
msgid "Sponsors"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__stage_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Stage"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__name
msgid "Stage Name"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track28
msgid "Status & Strategy"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submission Agreement"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submit Proposal"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__name
msgid "Tag"
msgstr ""

#. module: website_event_track
#: sql_constraint:event.track.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__tag_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Tags"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talk Introduction"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/event.py:102
#, python-format
msgid "Talk Proposals"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talk Title"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/event.py:96
#, python-format
msgid "Talks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talks Types"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_success
msgid "Thank you for your proposal."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track17
#: model:event.track,name:website_event_track.event_track29
msgid "The new marketing strategy"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track5
msgid "The new way to promote your creations"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "This event does not accept proposals."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Timely release of presentation material (slides),\n"
"                                    for publishing on our website."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__name
msgid "Title"
msgstr "શીર્ષક"

#. module: website_event_track
#: selection:event.track,activity_state:0
msgid "Today"
msgstr "આજે"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Today Activities"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Track"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_blocked
msgid "Track Blocked"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_count
msgid "Track Count"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date
msgid "Track Date"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__description
msgid "Track Description"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_form
msgid "Track Proposals on Website"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_ready
msgid "Track Ready"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_ready
msgid "Track Ready for Next Stage"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_tree
msgid "Track Stage"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_stage_action
#: model:ir.ui.menu,name:website_event_track.event_track_stage_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
msgid "Track Stages"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_tag
#: model:ir.model.fields,field_description:website_event_track.field_event_event__tracks_tag_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track_tag
msgid "Track Tags"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_blocked
msgid "Track blocked"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__track_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_graph
msgid "Tracks"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track_proposal
msgid "Tracks Proposals on Website"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid ""
"Tracks define the schedule of your event. These can be a talk, a round "
"table, a meeting, etc."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_form
msgid "Tracks on Website"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_unread
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Unread Messages"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid "Use this tag:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "View Track"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We require speakers to accept an agreement in which they commit to:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"We will accept a broad range of\n"
"                                presentations, from reports on academic and\n"
"                                commercial projects to tutorials and case\n"
"                                studies. As long as the presentation is\n"
"                                interesting and potentially useful to the\n"
"                                audience, it will be considered for\n"
"                                inclusion in the programme."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_success
msgid "We will evaluate your proposition and get back to you shortly."
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_event_menu
msgid "Website Event Menu"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_url
msgid "Website URL"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_description
msgid "Website meta description"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_title
msgid "Website meta title"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Your Email"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Your Name"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Your Phone"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Inspiring Business Talk"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "hours"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda
msgid "talks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks
msgid "unpublished"
msgstr ""
