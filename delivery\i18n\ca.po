# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Harcogourmet, 2022
# jabelchi, 2022
# Jonatan Gk, 2022
# marc<PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid " (Estimated Cost: %s )"
msgstr " (Cost Estimat: %s )"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_quant_package_weight_form
msgid "(computed:"
msgstr "(computat:"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "<i class=\"fa fa-arrow-right mr-1\"/>Get rate"
msgstr "<i class=\"fa fa-arrow-right mr-1\"/>Obtenir tarifa"

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent\">\n"
"                    Buy Odoo Enterprise now to get more providers.\n"
"                </p>"
msgstr ""
"<p class=\"o_view_nocontent\">\n"
"                    Compra Odoo Enterprise ara per obtenir més proveïdors.\n"
"                </p>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"o_warning_text\">Test</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"o_warning_text\">Prova</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-danger\">No debug</span>"
msgstr "<span class=\"text-danger\">Sense depuració</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-success\">Debug requests</span>"
msgstr "<span class=\"text-success\">Sol·licituds de depuració</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"text-success\">Production</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"text-success\">Producció</span>\n"
"                                    <span class=\"o_stat_text\">Entorn</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight (estimated): </span>"
msgstr "<span> - Pes (estimat): </span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_stock_report_delivery_no_package_section_line
#: model_terms:ir.ui.view,arch_db:delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight: </span>"
msgstr "<span>- Pes:</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_small_delivery
msgid "<span>Shipping Weight: </span>"
msgstr "<span>Pes d'enviament:</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid "<strong>Carrier:</strong>"
msgstr "<strong>Portador:</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid "<strong>HS Code</strong>"
msgstr "<strong>HS Codi</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_delivery
msgid ""
"<strong>Shipping Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>Pes d'enviament:</strong>\n"
"                <br/>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid ""
"<strong>Total Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>Pes total:</strong>\n"
"                <br/>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid "<strong>Tracking Number:</strong>"
msgstr "<strong>Número de seguiment:</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid ""
"<strong>Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>Pes:</strong>\n"
"                <br/>"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__integration_level
msgid "Action while validating Delivery Orders"
msgstr "Acció al validar ordres d'entrega"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__active
msgid "Active"
msgstr "Actiu"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Add"
msgstr "Afegir"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "Add a shipping method"
msgstr "Afegeix un mètode d'enviament"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Add shipping"
msgstr "Afegeix un enviament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__amount
msgid "Amount"
msgstr "Import"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__amount
msgid ""
"Amount of the order to benefit from a free shipping, expressed in the "
"company currency"
msgstr ""
"Import de la comanda que és beneficiaria d'una entrega gratuïta, expressada "
"en la moneda de la companyia."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Archived"
msgstr "Arxivat"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__available_carrier_ids
msgid "Available Carriers"
msgstr "Transportistes disponibles"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__base_on_rule
msgid "Based on Rules"
msgstr "En base a regles"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr "Pes a granel"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__can_generate_return
msgid "Can Generate Return"
msgstr "Pot generar retorn"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Cancel"
msgstr "Cancel·lar"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__carrier_id
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__carrier_id
#: model:ir.model.fields,field_description:delivery.field_stock_package_type__package_carrier_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_tree
msgid "Carrier"
msgstr "Transportista"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_package_type__shipper_package_code
msgid "Carrier Code"
msgstr "Codi del transportista"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_choose_delivery_carrier__carrier_id
msgid "Choose the method to deliver your goods"
msgstr "Trieu el mètode per lliurar els vostres béns"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__company_id
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__company_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__company_id
msgid "Company"
msgstr "Empresa"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Condition"
msgstr "Condició"

#. module: delivery
#: model:ir.model,name:delivery.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_price
msgid "Cost"
msgstr "Cost"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__country_ids
msgid "Countries"
msgstr "Països"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_date
msgid "Created on"
msgstr "Creat el"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__partner_id
msgid "Customer"
msgstr "Client"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__debug_logging
msgid "Debug logging"
msgstr "Registre de depuració"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,help:delivery.field_res_users__property_delivery_carrier_id
msgid "Default delivery method used in sales orders."
msgstr "Mètode d'entrega predeterminat utilitzat en les comandes de vendes."

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid "Define a new delivery method"
msgstr "Defineix un nou mètode de lliurament"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Delivery Carrier"
msgstr "Transportista"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Assistent de selecció del transportista de lliurament"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Delivery Cost"
msgstr "Cost d'entrega"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_message
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_message
msgid "Delivery Message"
msgstr "Missatge de lliurament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__name
#: model:ir.model.fields,field_description:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_res_users__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_sale_order__carrier_id
msgid "Delivery Method"
msgstr "Mètode de lliurament"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "Assistent de selecció del paquet de lliurament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__delivery_package_type_id
msgid "Delivery Package Type"
msgstr "Tipus de paquet de lliurament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_price
msgid "Delivery Price"
msgstr "Preu d'entrega"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_price_rule
msgid "Delivery Price Rules"
msgstr "Producte d'enviament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__product_id
msgid "Delivery Product"
msgstr "Producte d'enviament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_rating_success
msgid "Delivery Rating Success"
msgstr "Èxit de qualificació d'entrega"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_set
msgid "Delivery Set"
msgstr "Set de lliurament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__recompute_delivery_price
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__recompute_delivery_price
msgid "Delivery cost should be recomputed"
msgstr "El cost de l'enviament s'ha de tornar a calcular"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.vpicktree_view_tree
msgid "Destination"
msgstr "Destinació"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Destination Availability"
msgstr "Disponibilitat de destinació"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,field_description:delivery.field_stock_picking__destination_country_code
msgid "Destination Country"
msgstr "País de destinació"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__sequence
msgid "Determine the display order"
msgstr "Determini l'ordre de visualització"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Discard"
msgstr "Descartar"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.act_delivery_trackers_url
msgid "Display tracking links"
msgstr "Mostrar enllaços de seguiment"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"Each carrier (e.g. UPS) can have several delivery methods (e.g.\n"
"                UPS Express, UPS Standard) with a set of pricing rules attached\n"
"                to each method."
msgstr ""
"Cada transportador (per exemple UPS) pot tenir múltiple mètodes de "
"repartiment (per exemple, UPS Express, UPS normal) amb una sèrie de regles "
"de preu adjunt a cada mètode."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__prod_environment
msgid "Environment"
msgstr "Entorn"

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:0
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "Error: this delivery method is not available for this address."
msgstr "Error: Aquest mètode d'entrega no està disponible per aquesta adreça."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of the shipping will be updated on the SO after the delivery."
msgstr ""
"Cost estimat: el client serà facturat el cost estimat del transport.\n"
"Cost real: el client serà facturat el cost real del transport, el cost del transport s'actualitzarà després del lliurament."

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__estimated
msgid "Estimated cost"
msgstr "Cost estimat"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_sale_order__carrier_id
msgid "Fill this field if you plan to invoice the shipping based on picking."
msgstr "Empleni aquest camp si facturarà l'enviament basat en la recol·lecció"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Filling this form allows you to filter delivery carriers according to the "
"delivery address of your customer."
msgstr ""
"Aquest formulari li permet filtrar els transportistes segons la direcció "
"d'entrega del seu client."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__fixed
msgid "Fixed Price"
msgstr "Preu fix"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid "Free Shipping"
msgstr "Enviament gratuït"

#. module: delivery
#: model:delivery.carrier,name:delivery.free_delivery_carrier
#: model:product.product,name:delivery.product_product_delivery
#: model:product.template,name:delivery.product_product_delivery_product_template
msgid "Free delivery charges"
msgstr "Entrega gratuïta"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__free_over
msgid "Free if order amount is above"
msgstr "Gratuït si l'import de la comanda és superior a"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__return_label_on_delivery
msgid "Generate Return Label"
msgstr "Genera una etiqueta de retorn"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate
msgid "Get Rate"
msgstr "Obtenir tarifa"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate_and_ship
msgid "Get Rate and Create Shipment"
msgstr "Obtenir tarifes i crear enviaments"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Group By"
msgstr "Agrupar per"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_product__hs_code
#: model:ir.model.fields,field_description:delivery.field_product_template__hs_code
msgid "HS Code"
msgstr "Codi HS"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__id
msgid "ID"
msgstr "ID"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__free_over
msgid ""
"If the order total amount (shipping excluded) is above or equal to this "
"value, the customer benefits from a free shipping"
msgstr ""
"Si l'import total de la comanda (excloent-hi l'enviament) és superior o "
"igual a aquest valor, el client es beneficia amb un enviament gratuït. "

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Install more Providers"
msgstr "Instal·lar més proveïdors"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__integration_level
msgid "Integration Level"
msgstr "Nivell d'integració"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__invoicing_message
msgid "Invoicing Message"
msgstr "Missatge de factura"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "Política de facturació"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__is_return_picking
msgid "Is Return Picking"
msgstr "És la selecció de retorn"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__is_delivery
msgid "Is a Delivery"
msgstr "És un enviament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier____last_update
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package____last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier____last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__debug_logging
msgid "Log requests in order to ease debugging"
msgstr "Sol·licituds de registre per facilitar la depuració"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__margin
msgid "Margin"
msgstr "Marge"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_margin_not_under_100_percent
msgid "Margin cannot be lower than -100%"
msgstr "El marge no pot ser inferior a -100%"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Margin on Rate"
msgstr "Margin on Rate"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__max_value
msgid "Maximum Value"
msgstr "Valor màxim"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__name
msgid "Name"
msgstr "Nom"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__stock_package_type__package_carrier_type__none
msgid "No carrier integration"
msgstr "Sense integració de transportista"

#. module: delivery
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "No price rule matching this order; delivery cost cannot be computed."
msgstr ""
"Cap regla de preus que coincideixi amb aquesta comanda; no es pot calcular "
"el cost de lliurament."

#. module: delivery
#: model:delivery.carrier,name:delivery.normal_delivery_carrier
#: model:product.product,name:delivery.product_product_delivery_normal
#: model:product.template,name:delivery.product_product_delivery_normal_product_template
msgid "Normal Delivery Charges"
msgstr "Despeses de lliurament normals."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "OK"
msgstr "D'acord"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__operator
msgid "Operator"
msgstr "Operador"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__order_id
msgid "Order"
msgstr "Comanda"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Package"
msgstr "Paquet"

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid "Package Details"
msgstr "Detalls del paquet"

#. module: delivery
#: code:addons/delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid "Package too heavy!"
msgstr "Paquet massa pesat"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_quant_package
#: model:ir.model.fields,field_description:delivery.field_stock_picking__package_ids
msgid "Packages"
msgstr "Paquets"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__picking_id
msgid "Picking"
msgstr "Albarà"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__price
msgid "Price"
msgstr "Preu"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_tree
msgid "Price Rules"
msgstr "Regles de preus"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Pricing"
msgstr "Fixar preu"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__price_rule_ids
msgid "Pricing Rules"
msgstr "Normes per posar preus."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale_delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Print Return Label"
msgstr "Imprimeix l'etiqueta de retorn"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Moviment del producte (línia de moviment d'estoc)"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__product_qty
msgid "Product Qty"
msgstr "Qtat producte"

#. module: delivery
#: model:ir.model,name:delivery.model_product_template
msgid "Product Template"
msgstr "Plantilla de producte"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking__delivery_type
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Provider"
msgstr "Proveïdor"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__quantity
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__quantity
msgid "Quantity"
msgstr "Quantitat"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__real
msgid "Real cost"
msgstr "Cost real"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__return_label_ids
msgid "Return Label"
msgstr "Etiqueta de retorn"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__get_return_label_from_portal
msgid "Return Label Accessible from Customer Portal"
msgstr "Retorna l'etiqueta accessible des del portal del client"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_return_picking
msgid "Return Picking"
msgstr "Retorna albarà"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_base_price
msgid "Sale Base Price"
msgstr "Preu de venda base"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_price
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__sale_price
msgid "Sale Price"
msgstr "Preu de venda"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order
msgid "Sales Order"
msgstr "Comanda de venda"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línia comanda de venda"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Save"
msgstr "Desar"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Send to Shipper"
msgstr "Enviar al transportista"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__sequence
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__is_all_service
msgid "Service Product"
msgstr "Producte del servei"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__prod_environment
msgid "Set to True if your credentials are certified for production."
msgstr ""
"Establiu a verdader si les vostres credencials estan certificades per "
"producció."

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Shipment sent to carrier %(carrier_name)s for shipping with tracking number "
"%(ref)s<br/>Cost: %(price).2f %(currency)s"
msgstr ""
"Enviament enviat a l'operador %(carrier_name)s per a l'enviament amb número "
"de seguiment%(ref)s<br/>Cost: %(price).2f %(currency)s"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_price
msgid "Shipping Cost"
msgstr "Cost d'enviament"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Shipping Information"
msgstr "Informació Enviament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Shipping Method"
msgstr "Mètode d'enviament"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_carrier_form
#: model:ir.model,name:delivery.model_delivery_carrier
#: model:ir.ui.menu,name:delivery.menu_action_delivery_carrier_form
#: model:ir.ui.menu,name:delivery.sale_menu_action_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Mètodes d'enviament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__shipping_weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr "Pes de l'enviament"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_product_product__hs_code
#: model:ir.model.fields,help:delivery.field_product_template__hs_code
msgid ""
"Standardized code for international shipping and goods declaration. At the "
"moment, only used for the FedEx shipping provider."
msgstr ""
"Codi estandarditzat per a la declaració internacional d'enviaments i "
"mercaderies. En aquest moment, només s'utilitzava per al proveïdor de "
"transport de FedEx."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__state_ids
msgid "States"
msgstr "Estats"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move
msgid "Stock Move"
msgstr "Moviment d'estoc"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_package_type
msgid "Stock package type"
msgstr "Tipus de paquet principal"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,help:delivery.field_stock_picking__destination_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"EL codi ISO del país de dos caràcters.\n"
"Podeu utilitzar aquest camp per la cerca ràpida."

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_carrier
#: model:product.product,name:delivery.product_product_delivery_poste
#: model:product.template,name:delivery.product_product_delivery_poste_product_template
msgid "The Poste"
msgstr "The Poste"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__get_return_label_from_portal
msgid ""
"The return label can be downloaded by the customer from the customer portal."
msgstr ""
"El client pot descarregar l'etiqueta de retorn des del portal del client."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__return_label_on_delivery
msgid "The return label is automatically generated at the delivery."
msgstr "L'etiqueta de retorn es genera automàticament al lliurament."

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid "The shipping is free since the order amount exceeds %.2f."
msgstr ""
"L'enviament és gratuït ja que l'import de la comanda és superior %.2f."

#. module: delivery
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "The shipping price will be set once the delivery is done."
msgstr ""
"El preu del transport s'establirà una vegada que es faci el lliurament."

#. module: delivery
#: code:addons/delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid ""
"The weight of your package is higher than the maximum weight authorized for "
"this package type. Please choose another package type."
msgstr ""
"El pes del teu paquet és major que el pes màxim autoritzat per aquest tipus "
"de paquets. Si us plau, selecciona un altre tipus de paquet. "

#. module: delivery
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "There is no matching delivery rule."
msgstr "No hi ha cap regla de lliurament que coincideixi."

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"These methods allow to automatically compute the delivery price\n"
"                according to your settings; on the sales order (based on the\n"
"                quotation) or the invoice (based on the delivery orders)."
msgstr ""
"Aquests mètodes permeten calcular automàticament el preu de l'entrega "
"d'acord a la seva configuració; a la comanda de venda (basada en el "
"pressupost) o la factura (basada en les ordres d'entrega)."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__margin
msgid "This percentage will be added to the shipping price."
msgstr "Aquest percentatge s'agregarà al preu de l'enviament."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_quant_package__weight
msgid "Total weight of all the products contained in the package."
msgstr "Pes total de tots els productes continguts en el paquet."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__shipping_weight
msgid ""
"Total weight of packages and products not in a package. Packages with no "
"shipping weight specified will default to their products' total weight. This"
" is the weight used to compute the cost of the shipping."
msgstr ""
"Pes total dels paquets i productes que no estan en un paquet. Els paquets "
"sense pes d'enviament especificat tindran per defecte el pes total dels seus"
" productes. Aquest és el pes utilitzat per a calcular el cost de "
"l'enviament."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__weight_bulk
msgid "Total weight of products which are not in a package."
msgstr "El pes total dels productes que no estan en un paquet."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_quant_package__shipping_weight
msgid "Total weight of the package."
msgstr "Pes total del paquet."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__weight
msgid "Total weight of the products in the picking."
msgstr "Pes total dels productes en el picking."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "Trackers URL"
msgstr "URL de seguiment"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Tracking"
msgstr "Seguiment"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_tracking_ref
msgid "Tracking Reference"
msgstr "Referència del seguiment"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_tracking_url
msgid "Tracking URL"
msgstr "URL de seguiment"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale_delivery
msgid "Tracking:"
msgstr "Seguiment:"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_picking
msgid "Transfer"
msgstr "Transferència"

#. module: delivery
#: model:product.product,uom_name:delivery.product_product_delivery
#: model:product.product,uom_name:delivery.product_product_delivery_normal
#: model:product.product,uom_name:delivery.product_product_delivery_poste
#: model:product.template,uom_name:delivery.product_product_delivery_normal_product_template
#: model:product.template,uom_name:delivery.product_product_delivery_poste_product_template
#: model:product.template,uom_name:delivery.product_product_delivery_product_template
msgid "Units"
msgstr "Unitats"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Update"
msgstr "Actualitza"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
#, python-format
msgid "Update shipping cost"
msgstr "Actualitza el cost d'enviament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable
msgid "Variable"
msgstr "Variable"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable_factor
msgid "Variable Factor"
msgstr "Factor variable"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__volume
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__volume
msgid "Volume"
msgstr "Volum"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_move__weight
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__weight
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Weight"
msgstr "Pes"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__wv
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__wv
msgid "Weight * Volume"
msgstr "Pes * Volum"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr "Pes per enviament"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Weight for shipping"
msgstr "Pes per enviament"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__weight_uom_name
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight_uom_name
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Unitat de pes de l'etiqueta de mesura"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid ""
"You can not update the shipping costs on an order where it was already invoiced!\n"
"\n"
"The following delivery lines (product, invoiced quantity and price) have already been processed:\n"
"\n"
msgstr ""
"No podeu actualitzar els costos d'enviament en una comanda on ja estava facturat!\n"
"\n"
"Les següents línies de lliurament (producte, quantitat facturada i preu) ja s'han processat:\n"
"\n"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "You have multiple tracker links, they are available in the chatter."
msgstr "Tens múltiples enllaços de seguiment, estan disponibles al xat"

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."
msgstr ""
"El mètode d'entrega no té redirecció al lloc web del transportista per "
"realitzar el seguiment d'aquesta comanda."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_from
msgid "Zip From"
msgstr "Zip des de"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_to
msgid "Zip To"
msgstr "Zip fins"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "e.g. UPS Express"
msgstr "p. ex., UPS Express"
