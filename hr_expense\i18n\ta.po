# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_expense
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-02-20 10:40+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Tamil (http://www.transifex.com/odoo/odoo-9/language/ta/)\n"
"Language: ta\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
#, fuzzy
msgid "<strong>Currency:</strong>"
msgstr "<strong>மொத்தம்</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "<strong>Employee:</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
#, fuzzy
msgid "<strong>Manager:</strong>"
msgstr "<strong>மொத்தம்</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
#, fuzzy
msgid "<strong>Status:</strong>"
msgstr "<strong>மொத்தம்</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "<strong>Total</strong>"
msgstr "<strong>மொத்தம்</strong>"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_account_id
msgid "Account"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.air_ticket
#: model:product.template,name:hr_expense.air_ticket_product_template
msgid "Air Flight"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_analytic_account_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Analytic Account"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
msgid "Approve"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,state:0
#: model:mail.message.subtype,name:hr_expense.mt_expense_approved
msgid "Approved"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_bank_journal_id
msgid "Bank Journal"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_template_can_be_expensed
msgid "Can be expensed"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Cancel"
msgstr "ரத்து"

#. module: hr_expense
#: model:product.product,name:hr_expense.car_travel
#: model:product.template,name:hr_expense.car_travel_product_template
msgid "Car Travel Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "Certified honest and conform,<br/>(Date and signature).<br/><br/>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_my_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_expenses_tree
msgid "Click here to approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_approve_expense
#: model_terms:ir.actions.act_window,help:hr_expense.expense_all
msgid "Click here to create new expenses."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_my_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_expenses_tree
msgid "Click here to refuse"
msgstr ""

#. module: hr_expense
#: model:web.tip,description:hr_expense.expense_tip_2
msgid "Click on Action to submit multiple expenses to your manager."
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "Click to create a new expense category."
msgstr ""

#. module: hr_expense
#: selection:hr.expense,payment_mode:0
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_company_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Company"
msgstr "நிறுவனம்"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_configuration
msgid "Configuration"
msgstr "கட்டமைப்பு"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Confirmed Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_create_uid
msgid "Created by"
msgstr "உருவாக்கியவர்"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_create_date
msgid "Created on"
msgstr ""
"உருவாக்கப்பட்ட \n"
"தேதி"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_currency_id
msgid "Currency"
msgstr "நாணயம்"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_date
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "Date"
msgstr "தேதி"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_department
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_department_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Department"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_description
msgid "Description"
msgstr "விளக்கம்"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_display_name
#: model:ir.model.fields,field_description:hr_expense.field_report_hr_expense_report_expense_display_name
msgid "Display Name"
msgstr "காட்சி பெயர்"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
msgid "Documents"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_employee_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Employee"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,payment_mode:0
msgid "Employee (to reimburse)"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_approved_expense
#: model:ir.ui.menu,name:hr_expense.menu_expense_approved
msgid "Employee Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Expense"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_name
msgid "Expense Description"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_journal_id
msgid "Expense Journal"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_product
#: model:ir.ui.menu,name:hr_expense.menu_hr_product
msgid "Expense Products"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_approved
msgid "Expense approved"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_confirmed
msgid "Expense confirmed, waiting confirmation"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Expense refuse reason"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_refused
msgid "Expense refused"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_root
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_my_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_expenses_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model:product.category,name:hr_expense.cat_expense
msgid "Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_report_filtered
#: model:ir.actions.act_window,name:hr_expense.hr_expense_action
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_pivot
msgid "Expenses Analysis"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Expenses Month"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_department_expense_confirmed
msgid "Expenses To Approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Expenses by Month"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:209
#, python-format
msgid "Expenses must belong to the same Employee."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:212
#, python-format
msgid ""
"Expenses must have an expense journal specified to generate accounting "
"entries."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Expenses of Your Team Member"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_request_approve_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_action_from_department
#: model:ir.model.fields,field_description:hr_expense.field_hr_department_expense_to_approve_count
msgid "Expenses to Approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Expenses to Invoice"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Former Employees"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_approved_expense
msgid ""
"From here the accountant will be able to approve as well as refuse the "
"expenses which are verified by the HR Manager."
msgstr ""

#. module: hr_expense
#: model:ir.actions.server,name:hr_expense.hr_expense_entry_action_server
msgid "Generate Accounting Entries"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Group By"
msgstr ""

#. module: hr_expense
#: model:ir.actions.report.xml,name:hr_expense.action_report_hr_expense
msgid "HR Expense"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "HR Expenses"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.hotel_rent
#: model:product.template,name:hr_expense.hotel_rent_product_template
msgid "Hotel Accommodation"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_refuse_wizard
msgid "Hr Expense refuse Reason wizard"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_id
#: model:ir.model.fields,field_description:hr_expense.field_report_hr_expense_report_expense_id
msgid "ID"
msgstr "ID"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_account_move_id
msgid "Journal Entry"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense___last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard___last_update
#: model:ir.model.fields,field_description:hr_expense.field_report_hr_expense_report_expense___last_update
msgid "Last Modified on"
msgstr "கடைசியாக திருத்திய"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_write_uid
msgid "Last Updated by"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_write_date
msgid "Last Updated on"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.expense_all
#: model:ir.ui.menu,name:hr_expense.menu_expense_all
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "My Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "My Team Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "Name"
msgstr "பெயர்"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "New"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "New Expense"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "New Mail"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:276
#, python-format
msgid ""
"No Expense account found for the product %s (or for it's category), please "
"configure one."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:244
#, python-format
msgid "No Home Address found for the employee %s, please configure one."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:240
#, python-format
msgid "No credit account found for the %s journal, please configure one."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
msgid "Notes..."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_attachment_number
msgid "Number of Attachments"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_approve_expense
#: model_terms:ir.actions.act_window,help:hr_expense.expense_all
msgid ""
"Once you have created your expense, submit it to your manager who will "
"validate it."
msgstr ""

#. module: hr_expense
#: selection:hr.expense,state:0
msgid "Paid"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_payment_mode
msgid "Payment By"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:280
#, python-format
msgid ""
"Please configure Default Expense account for Product expense: "
"`property_account_expense_categ_id`."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
msgid "Post Journal Entries"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "Price"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_product_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Product"
msgstr "தயாரிப்பு"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_template
msgid "Product Template"
msgstr "தயாரிப்பு டெம்ப்ளேட்"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "Qty"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_quantity
msgid "Quantity"
msgstr "அளவு"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard_description
msgid "Reason"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Reason to refuse expense."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Refuse"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_refuse_wizard_action
msgid "Refuse Expense"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,state:0
#: model:mail.message.subtype,name:hr_expense.mt_expense_refused
msgid "Refused"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense
msgid "Reporting"
msgstr ""

#. module: hr_expense
#: model:web.tip,description:hr_expense.expense_tip_1
msgid "Select the lines you want to submit to be reimbursed."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:77
#, python-format
msgid ""
"Selected Unit of Measure does not belong to the same category as the product "
"Unit of Measure"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
msgid "Set to Draft"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_product_template_can_be_expensed
msgid "Specify whether the product can be selected in an HR expense."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_state
msgid "Status"
msgstr ""

#. module: hr_expense
#: model:ir.actions.server,name:hr_expense.hr_expense_submit_action_server
msgid "Submit Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_my_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_expenses_tree
msgid "Submit to Manager"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,state:0
msgid "Submitted"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_untaxed_amount
msgid "Subtotal"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "Taxe(s)"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_tax_ids
msgid "Taxes"
msgstr "வரி"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_journal_id
msgid "The journal used when the expense is done."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_bank_journal_id
msgid "The payment method used when the expense is paid by the company."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "This document must be dated and signed for reimbursement."
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_expense_to_approve
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model:mail.message.subtype,name:hr_expense.mt_expense_confirmed
msgid "To Approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "To Pay"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,state:0
msgid "To Submit"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_total_amount
msgid "Total"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_my_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_expenses_tree
msgid "Total Amount"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_unit_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "Unit Price"
msgstr "அலகு விலை"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_product_uom_id
msgid "Unit of Measure"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense
msgid "UoM"
msgstr ""

#. module: hr_expense
#: model:web.tip,description:hr_expense.expense_tip_3
msgid ""
"Use the messaging tool to log a note and attach a document to the expense. "
"(e.g. scan of the bill)"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,state:0
msgid "Waiting Payment"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_state
msgid ""
"When the expense request is created the status is 'To Submit'.\n"
" It is submitted by the employee and request is sent to manager, the status "
"is 'Submitted'.        \n"
"If the manager approve it, the status is 'Approved'.\n"
" If the accountant genrate the accounting entries for the expense request, "
"the status is 'Waiting Payment'."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:111
#, python-format
msgid "You can only delete draft or refused expenses!"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:206
#, python-format
msgid "You can only generate accounting entry for approved expense(s)."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:117
#, python-format
msgid "You can only submit draft expenses!"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:128
#, python-format
msgid ""
"Your Expense %s has been refused.<br/><ul "
"class=o_timeline_tracking_value_list><li>Reason<span> : </span><span "
"class=o_timeline_tracking_value>%s</span></li></ul>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_form_view
msgid "e.g. Business lunch with X"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "or"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_report_hr_expense_report_expense
msgid "report.hr_expense.report_expense"
msgstr ""
