# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_questions
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2021\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_2
msgid "A friend"
msgstr "เพื่อน"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_1
msgid "Allergies"
msgstr "โรคภูมิแพ้"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__name
msgid "Answer"
msgstr "ตอบ"

#. module: website_event_questions
#: model:ir.actions.act_window,name:website_event_questions.action_event_registration_report
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_graph
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_pivot
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_tree
msgid "Answer Breakdown"
msgstr "เฉลยคำตอบ"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__answer_ids
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_question_view_form
msgid "Answers"
msgstr "ตอบ"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__once_per_order
msgid "Ask only once per order"
msgstr "ถามเพียงครั้งเดียวต่อการสั่งซื้อ"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration__registration_answer_ids
msgid "Attendee Answers"
msgstr "คำตอบผู้เข้าร่วม"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__partner_id
msgid "Booked by"
msgstr "จองแล้วโดย"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_1
msgid "Commercials"
msgstr "โฆษณา"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_0
msgid "Consumers"
msgstr "ผู้บริโภค"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_event
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__event_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__event_id
msgid "Event"
msgstr "อีเวนต์"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question
msgid "Event Question"
msgstr "คำถามอีเวนต์"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question_answer
msgid "Event Question Answer"
msgstr "คำถามคำตอบอีเวนต์"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration
msgid "Event Registration"
msgstr "การลงทะเบียนอีเวนต์"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration_answer
msgid "Event Registration Answer"
msgstr "คำตอบการลงทะเบียนอีเวนต์"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_type
msgid "Event Template"
msgstr "เทมเพลตอีเวนต์"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__event_type_id
msgid "Event Type"
msgstr "ประเภทอีเวนต์"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__general_question_ids
msgid "General Questions"
msgstr "คำถามทั่วไป"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_7_question_1
msgid "How did you hear about us ?"
msgstr "คุณรู้จักเราได้อย่างไร ?"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_2
msgid "How did you learn about this event?"
msgstr "คุณเรียนรู้เกี่ยวกับอีเวนต์นี้ได้อย่างไร"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__id
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__id
msgid "ID"
msgstr "ไอดี"

#. module: website_event_questions
#: model:ir.model.fields,help:website_event_questions.field_event_question__once_per_order
msgid ""
"If True, this question will be asked only once and its value will be "
"propagated to every attendees.If not it will be asked for every attendee of "
"a reservation."
msgstr ""
"หากเป็นจริง คำถามนี้จะถูกถามเพียงครั้งเดียว "
"และมูลค่าของคำถามจะถูกถ่ายทอดไปยังผู้เข้าร่วมทุกคน หากไม่เป็นเช่นนั้น "
"ระบบจะถามผู้เข้าร่วมที่ทำการจองทุกคน"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question____last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer____last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งสุดท้ายโดย"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งสุดท้ายเมื่อ"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_0
msgid "Meal Type"
msgstr "ประเภทอาหาร"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_0
msgid "Mixed"
msgstr "ผสม"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_type_data_conference_question_0_answer_1
msgid "No"
msgstr "ไม่"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_0
msgid "Our website"
msgstr "เว็บไซต์ของเรา"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_type_data_conference_question_0
msgid "Participate in Social Event"
msgstr "เข้าร่วมกิจกรรมทางสังคม"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_2
msgid "Pastafarian"
msgstr "Pastafarian"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__question_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_question_view_form
msgid "Question"
msgstr "คำถาม"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__question_type
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__question_type
msgid "Question Type"
msgstr "ประเภทคำถาม"

#. module: website_event_questions
#: code:addons/website_event_questions/models/event_question.py:0
#, python-format
msgid "Question cannot belong to both the event category and itself."
msgstr "คำถามต้องไม่อยู่ในทั้งหมวดหมู่อีเวนต์และตัวมันเอง"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__question_ids
#: model:ir.model.fields,field_description:website_event_questions.field_event_type__question_ids
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_view_form_inherit_question
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_type_view_form_inherit_question
msgid "Questions"
msgstr "คำถาม"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__registration_id
msgid "Registration"
msgstr "การลงทะเบียน"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_2
msgid "Research"
msgstr "วิจัย"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_1
msgid "Sales"
msgstr "ขาย"

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_tree
msgid "Selected answer"
msgstr "คำตอบที่เลือก"

#. module: website_event_questions
#: model:ir.model.fields.selection,name:website_event_questions.selection__event_question__question_type__simple_choice
msgid "Selection"
msgstr "เลือก"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__sequence
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr "คำถามเฉพาะ"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__value_answer_id
msgid "Suggested answer"
msgstr "คำตอบที่แนะนำ"

#. module: website_event_questions
#: model:ir.model.fields.selection,name:website_event_questions.selection__event_question__question_type__text_box
msgid "Text Input"
msgstr "ป้อนข้อความ"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__value_text_box
msgid "Text answer"
msgstr "ข้อความตอบกลับ"

#. module: website_event_questions
#: model:ir.model.constraint,message:website_event_questions.constraint_event_registration_answer_value_check
msgid "There must be a suggested value or a text value."
msgstr "จะต้องมีค่าที่แนะนำหรือค่าข้อความ"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__title
msgid "Title"
msgstr "คำนำหน้าชื่อ"

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_view_form_inherit_question
msgid "Type"
msgstr "ประเภท"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_1
msgid "Vegetarian"
msgstr "มังสวิรัติ"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_7_question_0
msgid "Which field are you working in"
msgstr "คุณทำงานด้านไหน"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_type_data_conference_question_0_answer_0
msgid "Yes"
msgstr "ใช่"

#. module: website_event_questions
#: code:addons/website_event_questions/models/event_question.py:0
#, python-format
msgid ""
"You cannot change the question type of a question that already has answers!"
msgstr "คุณไม่สามารถเปลี่ยนประเภทคำถามของคำถามที่มีคำตอบอยู่แล้ว!"
