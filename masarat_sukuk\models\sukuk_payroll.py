# -*- coding:utf-8 -*-

from dateutil.relativedelta import relativedelta
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from datetime import date, datetime
import base64



class SukukReportWizard(models.TransientModel):
    _name = "payslip.sukuk.wizard.report2"


    dateFrom = fields.Date(default=lambda self: fields.Date.to_string(date.today().replace(day=1)),required=True)
    dateTo = fields.Date(default=lambda self: fields.Date.to_string((datetime.now() + relativedelta(months=+1, day=1, days=-1)).date()), required=True)

    # sukuk_book_id = fields.Many2one('sukuk.management.item',string="دفتر الشيكات" ,required=True)
    # bank_id = fields.Many2one('res.bank', related="sukuk_book_id.bank_id", string='اسم المصرف')
    # branch_id = fields.Many2one('bank.branch', related="sukuk_book_id.branch_id", string="أسم الفرع")
    # account_no_id = fields.Many2one('account.account', related="sukuk_book_id.account_no_id", string="رقم الحساب")

    bank_id = fields.Many2one('res.bank', string='اسم المصرف',required=True)
    branch_id = fields.Many2one('bank.branch', string="أسم الفرع", domain="[('bank_id','=',bank_id)]",required=True)
    account_no_id = fields.Many2one('account.account', string="رقم الحساب",required=True)

    dialog_box = fields.Text()

    user_id = fields.Many2one('res.users', string='مقدم الطلب', default=lambda self: self.env.uid)
    sukuk_computed = fields.Boolean(default=False)
    sukuk_page_ids = fields.One2many('payslip.sukuk.wizard.page','sukuk_wizard_id')

    @api.onchange('bank_id','branch_id','account_no_id')
    def get_dialog_box(self):
        if self.bank_id and self.branch_id and self.account_no_id:
            sukuk_books = self.env['sukuk.management.item'].search([('bank_id', '=', self.bank_id.id), ('branch_id', '=', self.branch_id.id),('account_no_id', '=', self.account_no_id.id)])
            if sukuk_books:
                self.dialog_box = str(len(sukuk_books))+" دفاتر متاحة "
            else:
                self.dialog_box = "لا يوجد دفاتر متاحة بالمعلومات المرفقة"
        else:
            self.dialog_box = ""


    def get_available_suke(self,bank,branch,amount,used_page):
        sukuk_books = self.env['sukuk.management.item'].search([('state', '=', 'confirmed'), ('bank_id', '=', self.bank_id.id),('branch_id', '=', self.branch_id.id),('account_no_id','=',self.account_no_id.id)])
        for each_book in sukuk_books:
            serial_no_from = each_book.serial_no_from
            serial_no_to = each_book.serial_no_to
            while serial_no_from <= serial_no_to:
                sukuk_page = self.env['sukuk.management.page'].search([('sukuk_book_id','=',each_book.id),('serial_no','=',serial_no_from)])
                if sukuk_page or (str(each_book.suke_book_number)+'_'+str(serial_no_from) in used_page):
                    serial_no_from+=1
                else:
                    suke = {
                        'sukuk_book_id':each_book.id,
                        'state':'draft',
                        'bank_id':each_book.bank_id.id,
                        'branch_id':each_book.branch_id.id,
                        'paid_to_bank_id':bank,
                        'paid_to_branch_id':branch,
                        'user_id':self.env.uid,
                        'person_signature':each_book.person_signature.id,
                        'suke_book_number':each_book.suke_book_number,
                        'serial_no':serial_no_from,
                        'time_stamp':str(fields.Date.to_string(date.today())),
                        'amount':amount,
                        'note':''
                        }
                    used_page.append(str(each_book.suke_book_number)+'_'+str(serial_no_from))
                    return suke,used_page
        return {'paid_to_bank_id':bank,
                'paid_to_branch_id':branch,
                'note':'لا يوجد صك متاح'}, used_page

    def generate_payslips_sukuk(self):
        self.sukuk_computed = True
        if self.dateFrom and self.dateTo:
            payslips = self.env['hr.payslip'].search([('date_from', '>=', str(self.dateFrom)), ('date_to', '<=', str(self.dateTo)), ('cash_or_not', '=', False)])
            self.env.cr.execute("select distinct(employee_id) from hr_payslip where date_from >= '" + str(self.dateFrom) + "' and date_from <= '" + str(self.dateTo) + "' and cash_or_not = false")
            employees = {}
            branch_bank_dict = {}
            for elem in self.env.cr.fetchall():
                account_number = self.env['hr.employee'].search([('id', '=', elem[0])]).bank_account_id.acc_number
                if not account_number:
                    continue
                bank = self.env['hr.employee'].search([('id', '=', elem[0])]).bank_account_id.bank_id
                branch = self.env['hr.employee'].search([('id', '=', elem[0])]).bank_account_id.bank_branch
                name = str(self.env['hr.employee'].search([('id', '=', elem[0])]).name)
                employees.setdefault(str(elem[0]),
                                     {'bank_branch': str(bank.name) + '_' + str(branch.name), 'account_number': account_number,
                                      'name': name, 'payslips': []})
                branch_bank_dict.setdefault(str(bank.name) + '_' + str(branch.name),
                                            {'amount': 0, 'amount_text': '', 'branch': branch.id, 'bank': bank.id})

            for elem in payslips:
                account_number = self.env['hr.employee'].search([('id', '=', str(elem.employee_id.id))]).bank_account_id.acc_number
                if not account_number:
                    continue

                # To be changed net = elem.line_ids.filtered(lambda line: line.code == 'NET').total
                net = elem.line_ids.filtered(lambda line: line.code == 'NETSALARY').total

                get_branch = employees[str(elem.employee_id.id)]['bank_branch']
                branch_bank_dict[get_branch]['amount'] += float(net)

            sukuk_pages = []
            sukuk_seris_used = []
            for elem in branch_bank_dict.keys():
                suke, sukuk_seris_used = self.get_available_suke(branch_bank_dict[elem]['bank'],branch_bank_dict[elem]['branch'],branch_bank_dict[elem]['amount'],sukuk_seris_used)
                sukuk_pages.append((0, 0, suke))
            self.sukuk_page_ids = False
            self.sukuk_page_ids = sukuk_pages

        return {"type": "ir.actions.act_window",
                "view_mode":"form",
                "res_model":"payslip.sukuk.wizard.report2",
                "target":"new",
                "res_id":self.id}


    def get_report_action(self):
        sukuk = []
        for elem in self.sukuk_page_ids:
            sukuk.append({
                "sukuk_book_id":elem.sukuk_book_id.id,
                "state":"draft",
                "bank_id":elem.bank_id.id,
                "branch_id":elem.branch_id.id,
                "paid_to_bank_id":elem.paid_to_bank_id.id,
                "paid_to_branch_id":elem.paid_to_branch_id.id,
                "user_id":elem.user_id.id,
                "person_signature":elem.person_signature.id,
                "suke_book_number":elem.suke_book_number,
                "serial_no":elem.serial_no,
                "time_stamp":elem.time_stamp,
                "amount":elem.amount,
                })
        data = {'model': self._name, 'ids': self.ids, 'date_from': str(self.dateFrom), 'date_to': str(self.dateTo) ,'sukuk':sukuk}
        #return self.env.ref('masarat_sukuk.report_sukuk_payroll').report_action(self, data=data)
        # print(data)
        # return self.env.ref('masarat_sukuk.report_sukuk_payroll').report_action(self, data=data)

        ########## Saving PDF as Attachment##
        hafeda = self.env['sukuk.management.hafida'].create({
            'name':" حافظة "+str(self.dateFrom)+"_"+str(self.dateTo)+'.pdf',
        })
        report = self.env.ref('masarat_sukuk.report_sukuk_payroll')._render_qweb_pdf(self, data=data)
        b64_pdf = base64.b64encode(report[0])
        attachment = self.env['ir.attachment'].create({
            'name': " حافظة "+str(self.dateFrom)+"_"+str(self.dateTo)+'.pdf',
            'type': 'binary',
            'datas': b64_pdf,
            'store_fname': " حافظة "+str(self.dateFrom)+"_"+str(self.dateTo)+'.pdf',
            'res_model': self._name,
            'res_id': self.id,
            'mimetype': 'application/x-pdf'
        })
        hafeda.attachment_id = attachment.id
        return {"type": "ir.actions.act_window",
                "view_mode":"form",
                "res_model":"sukuk.management.hafida",
                "target":"current",
                "res_id":hafeda.id}



class SukukReporPagetWizard(models.TransientModel):
    _name = "payslip.sukuk.wizard.page"

    sukuk_wizard_id = fields.Many2one('payslip.sukuk.wizard.report2')

    sukuk_book_id = fields.Many2one('sukuk.management.item', ondelete='cascade')
    state = fields.Selection([('draft', 'مسودة'), ('confirmed', 'مؤكدة'), ('cancel', 'ملغية')], default="draft")
    bank_id = fields.Many2one('res.bank', string='اسم المصرف')
    branch_id = fields.Many2one('bank.branch', string="أسم الفرع")
    paid_to_bank_id = fields.Many2one('res.bank', string='اسم المصرف المدفوع له')
    paid_to_branch_id = fields.Many2one('bank.branch', string="أسم الفرع المدفوع له")
    user_id = fields.Many2one('res.users', string='مقدم الطلب')
    person_signature = fields.Many2one('sukuk.management.signature',string="اسم المخول بالتوقيع")
    suke_book_number = fields.Integer( string='رقم الدفتر')
    serial_no = fields.Integer(string="رقم تسلسلي")
    time_stamp = fields.Date(string="التاريخ")
    amount = fields.Float(string="القيمة")
    note=fields.Text(string='ملاحظة')

    @api.onchange('sukuk_book_id')
    def change_suke_book_number(self):
        for elem in self:
            if elem.sukuk_book_id:
                elem.suke_book_number = elem.sukuk_book_id.suke_book_number
                elem.serial_no = False
                elem.note = False

class PayslipDetailsSkukReport_x1(models.AbstractModel):
    _name = 'report.masarat_sukuk.report_banks_payslip_sukuk'
    _description = 'Banks Branch Payslips Details'

    def _get_report_values(self, docids, data=None):
        payslips = self.env['hr.payslip'].search([('date_from', '>=', data['date_from']), ('date_to', '<=', data['date_to']),('cash_or_not','=',False)])
        self.env.cr.execute("select distinct(employee_id) from hr_payslip where date_from >= '"+data['date_from']+"' and date_from <= '"+data['date_to']+"'")
        employees = {}
        branch_bank_dict = {}
        for elem in self.env.cr.fetchall():
            account_number = self.env['hr.employee'].search([('id', '=', elem[0])]).bank_account_id.acc_number
            if not account_number:
                continue
            bank = self.env['hr.employee'].search([('id', '=', elem[0])]).bank_account_id.bank_id.name
            branch = self.env['hr.employee'].search([('id', '=', elem[0])]).bank_account_id.bank_branch.name
            account_number = self.env['hr.employee'].search([('id', '=', elem[0])]).bank_account_id.acc_number
            name = str(self.env['hr.employee'].search([('id','=',elem[0])]).name)
            employees.setdefault(str(elem[0]),{'bank_branch':str(bank)+'_'+str(branch), 'account_number':account_number ,'name' : name, 'payslips':[]})
            branch_bank_dict.setdefault(str(bank)+'_'+str(branch), {'amount':0, 'amount_text':'','branch':branch,'bank': bank, 'payslips': []})

        for elem in payslips:
            account_number = self.env['hr.employee'].search([('id', '=', str(elem.employee_id.id))]).bank_account_id.acc_number
            if not account_number:
                continue
            # net = elem.line_ids.filtered(lambda line: line.code == 'NET').total
            # gross = elem.line_ids.filtered(lambda line: line.code == 'GROSS').total
            net = elem.line_ids.filtered(lambda line: line.code == 'NETSALARY').total
            gross = elem.line_ids.filtered(lambda line: line.code == 'TOBASIC').total
            date_from_m = elem.date_from
            date_to_m = elem.date_to
            barcode = self.env['hr.employee'].search([('id','=',elem.employee_id.id)]).barcode
            #work_location = str(self.env['hr.employee'].search([('id', '=', elem.employee_id.id)]).work_location.name)
            #national_number = str(self.env['hr.employee'].search([('id', '=', elem.employee_id.id)]).identification_id)

            #################################################
            ############### Account_Holder Info

            acc_holder_national_number = self.env['hr.employee'].search([('id', '=', str(elem.employee_id.id))]).bank_account_id.acc_holder_NN

            if acc_holder_national_number:
                national_number=acc_holder_national_number
            else:
                national_number = str(self.env['hr.employee'].search([('id', '=', str(elem.employee_id.id))]).national_number).replace('False','')

            acc_holder_name = self.env['hr.employee'].search([('id', '=', str(elem.employee_id.id))]).bank_account_id.acc_holder_name
            if acc_holder_name:
                pass
            else:
                acc_holder_name=elem.employee_id.name
            ############### Account_Holder Info
            #################################################



            contract_start = str(self.env['hr.contract'].search([('employee_id','=',elem.employee_id.id)]).date_start)

            employees[str(elem.employee_id.id)]['barcode']= barcode
            employees[str(elem.employee_id.id)]['contract_start'] = contract_start
            employees[str(elem.employee_id.id)]['payslips'].append({'date_from_m':str(date_from_m),'date_to_m':str(date_to_m),'net': net, 'gross': gross})

            get_branch = employees[str(elem.employee_id.id)]['bank_branch']
            get_account_number = employees[str(elem.employee_id.id)]['account_number']
            branch_bank_dict[get_branch]['amount']+=float(net)
            #branch_bank_dict[get_branch]['payslips'].append({'name': elem.employee_id.name, 'net': net, 'gross': gross, 'barcode': barcode,'account_number': get_account_number, 'work_location':work_location, 'national_number':national_number})
            branch_bank_dict[get_branch]['payslips'].append({'payslip_id':elem.id, 'name': acc_holder_name, 'net': net, 'gross': gross, 'barcode': barcode,'account_number': get_account_number,'national_number': national_number})

        ### Append Sukuk numbers with bank_branch_dict
        sukuk_numbers = {}

        for ele in data['sukuk']:
            ## make sure suke is exected
            search_for_suke = self.env['sukuk.management.item'].search(
                [('id', '=', ele['sukuk_book_id']), ('suke_book_number', '=', ele['suke_book_number'])])
            if len(search_for_suke) != 1:
                raise ValidationError("لديك خطأ في ادخال البيانات , يرجى التأكد من صحة رقم دفتر الصكوك !")
            ## make_sure the serial_number is in range

            if (ele['serial_no'] > search_for_suke.serial_no_to) or (
                    ele['serial_no'] < search_for_suke.serial_no_from):
                raise ValidationError("لديك خطأ في ادخال البيانات , يرجى التأكد من صحة الرقم التسلسلي !")
            ## make_sure the suke is note used
            search_for_suke = self.env['sukuk.management.page'].search(
                [('sukuk_book_id', '=', ele['sukuk_book_id']), ('serial_no', '=', ele['serial_no'])])
            if search_for_suke:
                raise ValidationError(" تم استعماله مسبقا %s  دفتر رقم , %s الصك رقم" % (
                str(ele['suke_book_number']), str(ele['serial_no'])))


        for elem in data['sukuk']:
            bank = self.env['res.bank'].search([('id', '=', elem['paid_to_bank_id'])]).name
            branch = self.env['bank.branch'].search([('id', '=', elem['paid_to_branch_id'])]).name

            sukuk_numbers.setdefault(str(bank) + '_' + str(branch), '   '+str(elem['serial_no'])+'   ')
            self.env['sukuk.management.page'].create({
            "sukuk_book_id":elem['sukuk_book_id'],
            "wallet_name": "حافظة مصرف %s/%s للفترة من %s الى %s"%(branch,bank,data['date_from'],data['date_to']),
            "state":"draft",
            "paid_to_bank_id":elem['paid_to_bank_id'],
            "paid_to_branch_id":elem['paid_to_branch_id'],
            "user_id":elem['user_id'],
            # "person_signature":elem['person_signature'],
            # "suke_book_number":elem.suke_book_number,
            "serial_no":elem['serial_no'],
            "time_stamp":elem['time_stamp'],
            "amount":elem['amount'],
            })



       ###########################################3

        currency_id = self.env['res.currency'].search([('id','=',self.env.user.company_id.currency_id.id)])
        # for key in branch_bank_dict.keys():
        finel_branch_bank_dict={}
        for key in sukuk_numbers.keys():
            finel_branch_bank_dict[key] = branch_bank_dict[key]
            finel_branch_bank_dict[key]['amount_text'] = currency_id.amount_to_text(branch_bank_dict[key]['amount'])
            finel_branch_bank_dict[key]['serial_no'] = sukuk_numbers[key]
            #### but Lable
            for payslip in finel_branch_bank_dict[key]['payslips']:
                paysl = self.env['hr.payslip'].search([('id','=', payslip['payslip_id'])])
                if paysl:
                    paysl.suke_dicription = "payslip of "+str(paysl.date_from)[:7]+" Cheque number "+str(sukuk_numbers[key])+' to '+str(finel_branch_bank_dict[key]['bank'])+'-'+str(finel_branch_bank_dict[key]['branch'])


        ######## Save Report as attachement
        #repo_base = {'doc_ids':data['ids'], 'doc_model':data['model'], 'date_from':data['date_from'], 'date_to':data['date_to'], 'docs':finel_branch_bank_dict}
        return {'doc_ids':data['ids'], 'doc_model':data['model'], 'date_from':data['date_from'], 'date_to':data['date_to'], 'docs':finel_branch_bank_dict}
