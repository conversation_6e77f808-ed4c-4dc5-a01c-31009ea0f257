# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime, timedelta, date
from odoo.exceptions import ValidationError
from dateutil.relativedelta import relativedelta


class HrEmployeeCarAllowance(models.Model):
    _name = "hr.masarat.car"

    _inherit = ['mail.thread', 'mail.activity.mixin']


    name = fields.Char(compute='get_allowance_name', store=True)

    state = fields.Selection(selection=[('draft', 'Draft'),
                                        ('manager_approval', 'Manager Approval'),
                                        ('manager_refused', 'Manager Refused'),
                                        ('hr_approval', 'HR Approval'),
                                        ('hr_refused', 'HR Refused')], default='draft', string="State")

    request_date = fields.Date(string="تاريخ الطلب", readonly=True, default=lambda self: fields.Date.to_string(date.today()))
    employee_id = fields.Many2one('hr.employee', string="الموظف",store=True)
    manager_id = fields.Many2one('hr.employee', readonly=True, related='employee_id.parent_id', string="المدير",store=True)
    car_line_ids = fields.One2many('hr.masarat.car.line','car_allowance_id', ondelete='cascade')

    car_allowance_total_hours = fields.Float(string='مجموع ساعات الحركة',compute='compute_total_allowance',store=True)
    Note = fields.Text(string="ملاحظات اضافية")

    is_manager = fields.Char(compute='call_with_sudo_is_manager')
    is_hr_group = fields.Char(compute='call_with_sudo_is_hr_group')

    def get_if_hr_group(self):
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        for rec in self:
            if hr_group:
                rec.is_hr_group = 'yes'
            else:
                rec.is_hr_group = 'no'

    def compute_button_visible(self):
        for rec in self:
            if rec.manager_id.user_id.id == self._uid:
                rec.is_manager = '1'
            else:
                rec.is_manager = '0'

    @api.model
    def call_with_sudo_is_manager(self):
        self.sudo().compute_button_visible()

    @api.depends('is_hr_group')
    def call_with_sudo_is_hr_group(self):
        self.sudo().get_if_hr_group()


    @api.depends('employee_id','request_date')
    def get_allowance_name(self):
        for elem in self:
            elem.name = False
            if elem.employee_id and elem.request_date:
                elem.name = elem.employee_id.name+'-Car Allowance Request-'+str(elem.request_date)[:10]


    @api.depends('car_line_ids')
    def compute_total_allowance(self):
        for elem in self:
            elem.car_allowance_total_hours = 0.0
            for ele in elem.car_line_ids:
                elem.car_allowance_total_hours+=ele.allowance_hours

    # @api.constrains('car_line_ids')
    # def overtime_date_constrains(self):
    #     hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
    #     for elem in self.car_line_ids:
    #         date_gap = (datetime.now().date() - elem.overtime_date).days
    #         if ((date_gap > 4) or (date_gap < 1)) and not self.is_manager and (not hr_group):
    #             raise ValidationError("Overtime Date "+str(elem.overtime_date)+" is out of allowed range!")
    #         if ((date_gap > 4) or (date_gap < 1)) and (not hr_group):
    #             raise ValidationError("Overtime Date "+str(elem.overtime_date)+" is out of allowed range!")
    #         other_dates = self.env['hr.masarat.overtime.line'].search_count([('overtime_date','=',elem.overtime_date),('overtime_type','=',elem.overtime_type),('employee_id','=',elem.employee_id.id)])
    #         if other_dates > 1:
    #             raise ValidationError("Overtime "+str(elem.overtime_type)+" Date " + str(elem.overtime_date) + " is already requested!")

    def make_cancel_approval(self):
        self.state = 'draft'
    def make_manager_approval(self):
        self.state = 'manager_approval'
    def make_manager_refused(self):
        self.state = 'manager_refused'
    def make_hr_approval(self):
        self.state = 'hr_approval'
    def make_hr_refused(self):
        self.state = 'hr_refused'

    @api.model
    def default_get(self, fields):
        res = super(HrEmployeeCarAllowance, self).default_get(fields)
        user_id = self._context.get('uid')
        employee_id = self.env['hr.employee'].search([('user_id', '=', user_id)])
        res.update({'employee_id': employee_id.id})
        ## Check For Hr Group
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        if hr_group:
            res['is_hr_group'] = 'yes'
        else:
            res['is_hr_group'] = 'no'
        ########################
        return res

    def unlink(self):
        for elem in self:
            if elem.state != 'draft':
                raise ValidationError('You cannot delete a Car Allowance request which is not in draft state')
            return super(HrEmployeeCarAllowance, self).unlink()

    # def action_send_notification_to_maneger(self):
    #     template_id = self.env.ref('hr_approvales_masarat.car_allowance_request_template').id
    #     self.env['mail.template'].browse(template_id).send_mail(self.id, force_send=True)

    # @api.model
    # def create(self, vals_list):
    #     obj = super(HrEmployeeCarAllowance, self).create(vals_list)
    #     self.sudo().action_send_notification_to_maneger()
    #     return obj

    def action_send_notification_to_maneger(self,employee_id,recode_id):
        employee = self.env['hr.employee'].search([('id', '=', employee_id)])
        email_to = employee.parent_id.work_email
        email_from = employee.work_email
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recode_id, self._name)
        body = """
        <div dir="rtl">
            <p><font style="font-size: 14px;">Your Employee """+employee.name+""", requested car allowance approval, </font></p>
            <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            <a href="%s">Request Link</a>
        </div>""" % (web_base_url)
        template_id = self.env['mail.mail'].create({
            'subject':'حركة السيارة',
            'email_from':email_from,
            'email_to': email_to,
            'body_html':body})
        template_id.send()

    @api.model
    def create(self, vals_list):
        obj = super(HrEmployeeCarAllowance, self).create(vals_list)
        recode_id = obj.id
        employee_id = obj.employee_id.id
        self.sudo().action_send_notification_to_maneger(employee_id, recode_id)
        return obj


class HrEmployeeCarAllowanceLine(models.Model):
    _name = "hr.masarat.car.line"

    car_allowance_id = fields.Many2one('hr.masarat.car', ondelete='cascade')
    employee_id = fields.Many2one('hr.employee',related='car_allowance_id.employee_id')
    request_date = fields.Date(string='تاريخ', required=True)
    allowance_hours = fields.Float(string='ساعات الحركة', default=0.0, required=True)
    location = fields.Char(string='الموقع', required=True)
    state = fields.Selection(selection=[('draft', 'Draft'),
                                        ('manager_approval', 'Manager Approval'),
                                        ('manager_refused', 'Manager Refused'),
                                        ('hr_approval', 'HR Approval'),
                                        ('hr_refused', 'HR Refused')], related='car_allowance_id.state')

    mission_type = fields.Char(string='نوع المهمة', required=True)
    located_by = fields.Many2one('hr.employee', string="اسم المكلف",required=True)


class HrPayslipX(models.Model):
    _inherit = 'hr.payslip'

    car_allawance_amount = fields.Float(compute="get_car_allawance_amount")

    def get_car_allawance_amount(self):
        for elem in self:
            elem.car_allawance_amount = 0
            counter = 0
            if elem.date_from and elem.date_to:
                fmt = '%Y-%m-%d'
                pre_date = str((datetime.strptime(str(elem.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01'  ## to make sure there is no requists un collected from previus month
                if pre_date:
                    attendance_date_from = (datetime.strptime(pre_date, fmt)).date()
                    attendance_date_to = (datetime.strptime(pre_date, fmt).date() + relativedelta(months=1, day=1, days=-1))  ## to make sure there is no requists un collected from previus month
                    search_car_allawance_amount = self.env['hr.masarat.car.line'].search_count([('employee_id','=',elem.employee_id.id),('request_date','>=',attendance_date_from),('request_date','<=',attendance_date_to),('state','in',('hr_approval','manager_approval'))])
                    counter = search_car_allawance_amount
            if counter >= 12: ## Daily User
                elem.car_allawance_amount = 200
            else:
                elem.car_allawance_amount = counter*10 ## 2023-07-01




