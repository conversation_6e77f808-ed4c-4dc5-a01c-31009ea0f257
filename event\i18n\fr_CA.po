# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * event
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-16 08:08+0000\n"
"PO-Revision-Date: 2017-11-16 08:08+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: French (Canada) (https://www.transifex.com/odoo/teams/41243/fr_CA/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr_CA\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: event
#: model:mail.template,body_html:event.event_subscription
msgid ""
"\n"
"<div style=\"background:#F3F5F6;color:#515166;padding:25px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"% set date_begin = format_tz(object.event_id.date_begin, tz='UTC', format='%Y%m%dT%H%M%SZ')\n"
"% set date_end = format_tz(object.event_id.date_end, tz='UTC', format='%Y%m%dT%H%M%SZ')\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr>\n"
"                <td>\n"
"                    <a href=\"/\">\n"
"                        <img src=\"/logo\" alt=\"${object.company_id.name}\" style=\"vertical-align:baseline;max-width:100px;\" />\n"
"                    </a>\n"
"                </td>\n"
"                <td style=\"text-align:right;vertical-align:middle;\">\n"
"                    % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"                        <a href=\"${object.event_id.website_url}\" style=\"background-color: #1abc9c; padding: 12px; font-weight: 12px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;\">View Event</a>\n"
"                    % endif\n"
"                </td>\n"
"            </tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr>\n"
"                <td style=\"padding:15px 20px 0px 20px; font-size:16px; font-weight:300;\">\n"
"                    <p style=\"font-size:16px;\">\n"
"                        Hi,\n"
"                    </p><p style=\"font-size:16px;\">\n"
"                        We are happy to confirm your registration to the event:\n"
"                    </p>\n"
"                    <ul>\n"
"                        <li>Event: \n"
"                        % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"                            <a href=\"${object.event_id.website_url}\" style=\"color:#875A7B;text-decoration:none;\">${object.event_id.name}</a>\n"
"                        % else:\n"
"                            <strong>${object.event_id.name}</strong>\n"
"                        % endif\n"
"                        </li>\n"
"                        <li>Attendee: ${object.name}</li>\n"
"                    </ul>\n"
"                    <p style=\"font-size:16px;\">\n"
"                        See you soon,\n"
"                    </p>\n"
"                    <p style=\"font-size:16px; margin-bottom: 30px\">\n"
"                        <i>\n"
"                           -- <br/>\n"
"                        % if object.event_id.organizer_id:\n"
"                            ${object.event_id.organizer_id.name}\n"
"                        % else:\n"
"                            The organizers.\n"
"                        % endif\n"
"                        </i>\n"
"                    </p>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"padding:15px 20px 0px 20px;\">\n"
"                    <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                        <tr>\n"
"                            <td style=\"padding:25px 0px; text-align:center;\">\n"
"                                <p>\n"
"                                    Don't forget to <strong>add it to your calendar</strong>:\n"
"                                </p>\n"
"                                <a href=\"https://www.google.com/calendar/render?action=TEMPLATE&text=${object.event_id.name}&dates=${date_begin}/${date_end}&location=${location}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\"> Google</a>\n"
"                                <a href=\"https://bay02.calendar.live.com/calendar/calendar.aspx?rru=addevent&summary=${object.event_id.name}&dtstart=${date_begin}&dtend=${date_end}&location=${location}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\"> Outlook</a>\n"
"                                <a href=\"https://calendar.yahoo.com/?v=60&view=d&type=20&title=${object.event_id.name}&in_loc=${location}&st=${format_tz(object.event_id.date_begin, tz='UTC', format='%Y%m%dT%H%M%S')}&et=${format_tz(object.event_id.date_end, tz='UTC', format='%Y%m%dT%H%M%S')}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\"> Yahoo</a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                        <tr>\n"
"                            <td style=\"padding:25px 0px;vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\">\n"
"                            </td>\n"
"                            <td style=\"padding:25px 10px 25px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> ${object.event_id.date_begin_located}</div>\n"
"                                <div><strong>To</strong> ${object.event_id.date_end_located}</div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>TZ</strong> ${object.event_id.date_tz}</i></div>\n"
"                            </td>\n"
"                            % if object.event_id.address_id.country_id.name:\n"
"                                <td style=\"padding:25px 0px;vertical-align:top;\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\">\n"
"                                </td>\n"
"                                <td style=\"padding:30px 10px 25px 10px;width:50%;vertical-align:top;\">\n"
"                                    % set location = ''\n"
"                                    % if object.event_id.address_id.name:\n"
"                                        <p style=\"margin:0px 0px 0px 0px;\">${object.event_id.address_id.name}</p>\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street:\n"
"                                        <p style=\"margin:0px 0px 0px 0px;\">${object.event_id.address_id.street}</p>\n"
"                                        % set location = object.event_id.address_id.street\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street2:\n"
"                                        <p style=\"margin:0px 0px 0px 0px;\">${object.event_id.address_id.street2}</p>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.street2)\n"
"                                    % endif\n"
"                                    <p style=\"margin:0px 0px 0px 0px;\">\n"
"                                    % if object.event_id.address_id.city:\n"
"                                        ${object.event_id.address_id.city},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.city)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.state_id.name:\n"
"                                        ${object.event_id.address_id.state_id.name},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.state_id.name)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.zip:\n"
"                                        ${object.event_id.address_id.zip}\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.zip)\n"
"                                    % endif\n"
"                                    </p>\n"
"                                    % if object.event_id.address_id.country_id.name:\n"
"                                        <p style=\"margin:0px 0px 0px 0px;\">${object.event_id.address_id.country_id.name}</p>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.country_id.name)\n"
"                                    % endif\n"
"                                </td>\n"
"                            % endif\n"
"                        </tr>\n"
"                    </table>\n"
"                    % if object.event_id.organizer_id:\n"
"                        <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                            <tr>\n"
"                                <td style=\"padding:10px 0px 25px 0px;\">\n"
"                                    <h2 style=\"font-weight:300;margin:10px 0px\">Questions about this event?</h2>\n"
"                                    <p>Please contact the organizer:</p>\n"
"                                    <ul>\n"
"                                        <li>${object.event_id.organizer_id.name}</li>\n"
"                                        % if object.event_id.organizer_id.email\n"
"                                            <li>Mail: <a href=\"mailto:${object.event_id.organizer_id.email}\" style=\"text-decoration:none;color:#875A7B;\">${object.event_id.organizer_id.email}</a></li>\n"
"                                        % endif\n"
"                                        % if object.event_id.organizer_id.phone\n"
"                                            <li>Phone: ${object.event_id.organizer_id.phone}</li>\n"
"                                        % endif\n"
"                                    </ul>\n"
"\n"
"                                </td>\n"
"                            </tr>\n"
"                        </table>\n"
"                    % endif\n"
"                </td>\n"
"            </tr>\n"
"        </tbody>\n"
"    </table>\n"
"    % if object.event_id.address_id:\n"
"    <div style=\"width:598px;margin:0px auto;border-left:1px solid #dddddd;border-right:1px solid #dddddd;border-bottom:1px solid #dddddd;\">\n"
"        <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"            <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&size=598x200&maptype=roadmap&format=png&visual_refresh=true&markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom;\" />\n"
"        </a>\n"
"    </div>\n"
"    % endif\n"
"    <table style=\"width:600px;margin:0px auto;text-align:center;\">\n"
"        <tbody>\n"
"            <tr>\n"
"                <td style=\"padding-top:10px;font-size: 12px;\">\n"
"                    <div>Sent by ${object.company_id.name}</div>\n"
"                    % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"                    <div>\n"
"                        Discover <a href=\"/event\" style=\"text-decoration:none;color:#717188;\">all our events</a>.\n"
"                    </div>\n"
"                    % endif\n"
"                </td>\n"
"            </tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_reminder
msgid ""
"\n"
"<div style=\"background:#F3F5F6;color:#515166;padding:25px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"% set date_begin = format_tz(object.event_id.date_begin, tz='UTC', format='%Y%m%dT%H%M%SZ')\n"
"% set date_end = format_tz(object.event_id.date_end, tz='UTC', format='%Y%m%dT%H%M%SZ')\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr>\n"
"                <td>\n"
"                    <a href=\"/\">\n"
"                        <img src=\"/logo\" alt=\"${object.company_id.name}\" style=\"vertical-align:baseline;max-width:100px;\" />\n"
"                    </a>\n"
"                </td>\n"
"                <td style=\"text-align:right;vertical-align:middle;\">\n"
"                    % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"                        <a href=\"${object.event_id.website_url}\" style=\"background-color: #1abc9c; padding: 12px; font-weight: 12px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;\">View Event</a>\n"
"                    % endif\n"
"                </td>\n"
"            </tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr>\n"
"                <td style=\"padding:15px 20px 0px 20px; font-size:16px;\">\n"
"                    <p style=\"font-size:16px;\">\n"
"                        Hello,\n"
"                    </p>\n"
"                    <p style=\"font-size:16px;\">\n"
"                        We are excited to remind you that the event\n"
"                        % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"                            <a href=\"${object.event_id.website_url}\" style=\"color:#875A7B;text-decoration:none;\">${object.event_id.name}</a>\n"
"                        % else:\n"
"                            ${object.event_id.name}\n"
"                        % endif\n"
"                        is starting\n"
"                        <strong>${object.get_date_range_str()}</strong>.\n"
"                    </p><p style=\"font-size:16px;\">\n"
"                        We confirm your registration and hope to meet you there,\n"
"                    </p><p style=\"font-size:16px; margin-bottom: 30px\">\n"
"                        <i>\n"
"                           -- <br/>\n"
"                        % if object.event_id.organizer_id:\n"
"                            ${object.event_id.organizer_id.name}\n"
"                        % else:\n"
"                            The organizers.\n"
"                        % endif\n"
"                        </i>\n"
"                    </p>\n"
"\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"padding:15px 20px 0px 20px;\">\n"
"                    <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                        <tr>\n"
"                            <td style=\"padding:25px 0px;vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\">\n"
"                            </td>\n"
"                            <td style=\"padding:25px 10px 25px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> ${object.event_id.date_begin_located}</div>\n"
"                                <div><strong>To</strong> ${object.event_id.date_end_located}</div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>TZ</strong> ${object.event_id.date_tz}</i></div>\n"
"                            </td>\n"
"                            % if object.event_id.address_id:\n"
"                                <td style=\"padding:25px 0px;vertical-align:top;\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\">\n"
"                                </td>\n"
"                                <td style=\"padding:30px 10px 25px 10px;width:50%;vertical-align:top;\">\n"
"                                    % set location = ''\n"
"                                    % if object.event_id.address_id.name:\n"
"                                        <p style=\"margin:0px 0px 0px 0px;\">${object.event_id.address_id.name}</p>\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street:\n"
"                                        <p style=\"margin:0px 0px 0px 0px;\">${object.event_id.address_id.street}</p>\n"
"                                        % set location = object.event_id.address_id.street\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street2:\n"
"                                        <p style=\"margin:0px 0px 0px 0px;\">${object.event_id.address_id.street2}</p>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.street2)\n"
"                                    % endif\n"
"                                    <p style=\"margin:0px 0px 0px 0px;\">\n"
"                                    % if object.event_id.address_id.city:\n"
"                                        ${object.event_id.address_id.city},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.city)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.state_id.name:\n"
"                                        ${object.event_id.address_id.state_id.name},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.state_id.name)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.zip:\n"
"                                        ${object.event_id.address_id.zip}\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.zip)\n"
"                                    % endif\n"
"                                    </p>\n"
"                                    % if object.event_id.address_id.country_id.name:\n"
"                                        <p style=\"margin:0px 0px 0px 0px;\">${object.event_id.address_id.country_id.name}</p>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.country_id.name)\n"
"                                    % endif\n"
"                                </td>\n"
"                            % endif\n"
"                        </tr>\n"
"                    </table>\n"
"                    % if object.event_id.organizer_id.email:\n"
"                        <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                            <tr>\n"
"                                <td style=\"padding:10px 0px 25px 0px;\">\n"
"                                    <h2 style=\"font-weight:300;margin:10px 0px\">Questions about this event?</h2>\n"
"                                    <p>Please contact the organizer:</p>\n"
"                                    <ul>\n"
"                                        <li>${object.event_id.organizer_id.name}</li>\n"
"                                        % if object.event_id.organizer_id.email\n"
"                                            <li>Mail: <a href=\"mailto:${object.event_id.organizer_id.email}\" style=\"text-decoration:none;color:#875A7B;\">${object.event_id.organizer_id.email}</a></li>\n"
"                                        % endif\n"
"                                        % if object.event_id.organizer_id.phone\n"
"                                            <li>Phone: ${object.event_id.organizer_id.phone}</li>\n"
"                                        % endif\n"
"                                    </ul>\n"
"                                </td>\n"
"                            </tr>\n"
"                        </table>\n"
"                    % endif\n"
"                    <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                        <tr>\n"
"                            <td style=\"padding:25px 0px;\">\n"
"                                <strong>Add this event to your calendar</strong>\n"
"                                <a href=\"https://www.google.com/calendar/render?action=TEMPLATE&text=${object.event_id.name}&dates=${date_begin}/${date_end}&location=${location}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\"> Google</a>\n"
"                                <a href=\"https://bay02.calendar.live.com/calendar/calendar.aspx?rru=addevent&summary=${object.event_id.name}&dtstart=${date_begin}&dtend=${date_end}&location=${location}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\"> Outlook</a>\n"
"                                <a href=\"https://calendar.yahoo.com/?v=60&view=d&type=20&title=${object.event_id.name}&in_loc=${location}&st=${format_tz(object.event_id.date_begin, tz='UTC', format='%Y%m%dT%H%M%S')}&et=${format_tz(object.event_id.date_end, tz='UTC', format='%Y%m%dT%H%M%S')}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\"> Yahoo</a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td>\n"
"            </tr>\n"
"        </tbody>\n"
"    </table>\n"
"    % if object.event_id.address_id:\n"
"    <div style=\"width:598px;margin:0px auto;border-left:1px solid #dddddd;border-right:1px solid #dddddd;border-bottom:1px solid #dddddd;\">\n"
"        <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"            <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&size=598x200&maptype=roadmap&format=png&visual_refresh=true&markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom;\" />\n"
"        </a>\n"
"    </div>\n"
"    % endif\n"
"    <table style=\"width:600px;margin:0px auto;text-align:center;\">\n"
"        <tbody>\n"
"            <tr>\n"
"                <td style=\"padding-top:10px;font-size: 12px;\">\n"
"                    <p style=\"margin:0px 0px 9px 0px;padding-top:10px;\">Sent by ${object.company_id.name}</p>\n"
"                    % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"                    <div>\n"
"                        Discover <a href=\"/event\" style=\"text-decoration:none;color:#717188;\">all our events</a>.\n"
"                    </div>\n"
"                    % endif\n"
"                </td>\n"
"            </tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_registration_mail_template_badge
msgid ""
"\n"
"<p>Dear ${object.name},</p>\n"
"<p>Thank you for your inquiry.</p>\n"
"<p>Here is your badge for the event ${object.event_id.name}.</p>\n"
"<p>If you have any questions, please let us know.</p>\n"
"<p>Best regards,</p>"
msgstr ""

#. module: event
#: model:mail.template,subject:event.event_reminder
msgid "${object.event_id.name}: ${object.get_date_range_str()}"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"1 business room: to discuss implementation methodologies, best sales "
"practices, etc."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "1 workshop room: mainly for developers."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"2 technical rooms: one dedicated to advanced Odoo developers, one for new "
"developers."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"<em>(Chamber Works reserves the right to cancel, re-name or re-locate<br>the"
" event or change the dates on which it is held.)</em>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<em>(OpenElec Applications reserves the right to cancel, re-name or re-"
"locate<br>the event or change the dates on which it is held.)</em>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid ""
"<em>(YourCompany reserves the right to cancel, re-name or re-locate<br>the "
"event or change the dates on which it is held.)</em>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<em>If you wish to make a presentation, please send your topic proposal as "
"soon as possible for approval to Mr. Famke Jenssens at ngh (a) yourcompany "
"(dot) com. The presentations should be, for example, a presentation of a "
"community module, a case study, methodology feedback, technical, etc. Each "
"presentation must be in English.</em>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid ""
"<i class=\"fa fa-clock-o\"/>\n"
"                                        <b>To</b>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event.event_registration_report_template_badge
msgid "<i>to</i>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<span attrs=\"{'invisible': [('seats_availability', '=', 'unlimited')]}\" class=\"oe_read_only\">\n"
"                                        to \n"
"                                    </span>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "<strong>5-days Technical Training</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "<strong>Cancellation Policy: </strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<strong>Conference on Business Apps</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "<strong>Course summary:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "<strong>Functional Webinar</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"<strong>Having attended this course, participants should be able "
"to:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<strong>Join us to our main event of the year: the Open Days</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "<strong>Objective:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
msgid "<strong>Objectives:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "<strong>Our prices include:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
msgid "<strong>Program:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "<strong>Requirements</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<strong>What's new for this year?</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
msgid "<strong>Where to find us:</strong>"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_active
msgid "Active"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Add a navigation menu to your event web pages with schedule, tracks, a track"
" proposal form, etc."
msgstr ""

#. module: event
#: selection:event.mail,interval_type:0
#: selection:event.type.mail,interval_type:0
msgid "After each registration"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"After registering, you will receive a link and password by email before the "
"start of the session. If you have a problem to connect, please contact us at"
msgstr ""

#. module: event
#: selection:event.mail,interval_type:0
#: selection:event.type.mail,interval_type:0
msgid "After the event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Archived"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"Are you sure you want to cancel this event? All the linked attendees will be"
" cancelled as well."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "Ask your questions to our expert;"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "Assess whether your expectations are met;"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Attendance"
msgstr ""

#. module: event
#: selection:event.registration,state:0
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Attended"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_date_closed
msgid "Attended Date"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Attended the Event"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_registration
#: model:ir.model.fields,field_description:event.field_event_mail_registration_registration_id
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Attendee"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_name
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
msgid "Attendee Name"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.act_event_registration_from_event
#: model:ir.actions.act_window,name:event.action_registration
#: model:ir.model.fields,field_description:event.field_event_event_registration_ids
#: model:ir.ui.menu,name:event.menu_action_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Attendees"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_auto_confirm
msgid "Autoconfirm Registrations"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_auto_confirm
msgid "Automatically Confirm Registrations"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_use_mail_schedule
msgid "Automatically Send Emails"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_available
msgid "Available Seats"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_badge_back
msgid "Badge Back"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_badge_front
msgid "Badge Front"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_badge_innerleft
msgid "Badge Inner Left"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_badge_innerright
msgid "Badge Inner Right"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings_module_event_barcode
msgid "Barcode"
msgstr ""

#. module: event
#: selection:event.mail,interval_type:0
#: selection:event.type.mail,interval_type:0
msgid "Before the event"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
msgid "Best regards,"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Bring your own laptop."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Cancel"
msgstr "Annuler"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Cancel Event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Cancel Registration"
msgstr ""

#. module: event
#: selection:event.event,state:0 selection:event.registration,state:0
msgid "Cancelled"
msgstr "Annulé"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_event_type_id
msgid "Category"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Chamber Works 60, Rosewood Court Detroit, MI 48212 (United States)"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Chaussée de Namur 69, 1300 Wavre, Belgium"
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
msgid "Click to add a new attendee."
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid "Click to add a new event."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:264
#, python-format
msgid "Closing Date cannot be set before Beginning Date."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Communication"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_company_id
#: model:ir.model.fields,field_description:event.field_event_registration_company_id
msgid "Company"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:475
#, python-format
msgid "Compose Email"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_2
msgid "Conference"
msgstr ""

#. module: event
#: model:event.event,name:event.event_2
msgid "Conference on Business Apps"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Conferences, workshops and trainings will be organized in 6 rooms:"
msgstr ""

#. module: event
#: model:ir.ui.menu,name:event.menu_event_configuration
msgid "Configuration"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Confirm"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Confirm Anyway"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Confirm Event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Confirm Registration"
msgstr ""

#. module: event
#: selection:event.event,state:0 selection:event.registration,state:0
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Confirmed"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Confirmed attendees"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Confirmed events"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_res_partner
#: model:ir.model.fields,field_description:event.field_event_registration_partner_id
msgid "Contact"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_country_id
msgid "Country"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm_create_uid
#: model:ir.model.fields,field_description:event.field_event_event_create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration_create_uid
#: model:ir.model.fields,field_description:event.field_event_registration_create_uid
#: model:ir.model.fields,field_description:event.field_event_type_create_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail_create_uid
msgid "Created by"
msgstr "Créé par"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm_create_date
#: model:ir.model.fields,field_description:event.field_event_event_create_date
#: model:ir.model.fields,field_description:event.field_event_mail_create_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration_create_date
#: model:ir.model.fields,field_description:event.field_event_registration_create_date
#: model:ir.model.fields,field_description:event.field_event_type_create_date
#: model:ir.model.fields,field_description:event.field_event_type_mail_create_date
msgid "Created on"
msgstr "Créé le"

#. module: event
#: code:addons/event/models/event.py:438
#, python-format
msgid "Customer"
msgstr "Client"

#. module: event
#: code:addons/event/models/event.py:440
#, python-format
msgid "Customer Email"
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
#: selection:event.type.mail,interval_unit:0
msgid "Day(s)"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Delete"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_description
msgid "Description"
msgstr "Description"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Develop a new module for a particular application."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "Discover how to navigate in our software;"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm_display_name
#: model:ir.model.fields,field_description:event.field_event_event_display_name
#: model:ir.model.fields,field_description:event.field_event_mail_display_name
#: model:ir.model.fields,field_description:event.field_event_mail_registration_display_name
#: model:ir.model.fields,field_description:event.field_event_registration_display_name
#: model:ir.model.fields,field_description:event.field_event_type_display_name
#: model:ir.model.fields,field_description:event.field_event_type_mail_display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_sequence
msgid "Display order"
msgstr ""

#. module: event
#: selection:event.event,state:0
msgid "Done"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"During this conference, our team will give a detailed overview of our "
"business applications. You’ll know all the benefits of using it."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"Each session lasts approximately one hour and is free, we just ask you to "
"register to receive access codes."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_email
msgid "Email"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Email Schedule"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_template_id
#: model:ir.model.fields,field_description:event.field_event_type_mail_template_id
msgid "Email Template"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_date_end
msgid "End Date"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_date_end_located
msgid "End Date Located"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_event
#: model:ir.model.fields,field_description:event.field_event_mail_event_id
#: model:ir.model.fields,field_description:event.field_event_registration_event_id
#: model_terms:ir.ui.view,arch_db:event.event_event_view_pivot
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_mail
msgid "Event Automated Mailing"
msgstr ""

#. module: event
#: model:ir.actions.report,name:event.report_event_event_badge
msgid "Event Badge"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_type
#: model:ir.ui.menu,name:event.menu_event_type
msgid "Event Categories"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_type
#: model:ir.model.fields,field_description:event.field_event_type_name
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_tree
msgid "Event Category"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_confirm
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Event Confirmation"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_event_end_date
msgid "Event End Date"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Event Information"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_event_logo
msgid "Event Logo"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Event Mail Scheduler"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_tree
msgid "Event Mail Schedulers"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_name
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Event Name"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_calendar
msgid "Event Organization"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_calendar
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Registration"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_event_begin_date
msgid "Event Start Date"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail_event_type_id
msgid "Event Type"
msgstr ""

#. module: event
#: model:ir.actions.server,name:event.event_mail_scheduler_ir_actions_server
#: model:ir.cron,cron_name:event.event_mail_scheduler
#: model:ir.cron,name:event.event_mail_scheduler
msgid "Event: Mail Scheduler"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_view
#: model:ir.model.fields,field_description:event.field_res_partner_event_count
#: model:ir.model.fields,field_description:event.field_res_users_event_count
#: model:ir.ui.menu,name:event.event_event_menu_pivot_report
#: model:ir.ui.menu,name:event.event_main_menu
#: model:ir.ui.menu,name:event.menu_event_event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.res_partner_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Events"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.event_event_action_pivot
msgid "Events Analysis"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_mail
msgid "Events Mail Schedulers"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid ""
"Events and registrations will automatically be confirmed\n"
"                                            upon creation, easing the flow for simple events."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type_auto_confirm
msgid ""
"Events and registrations will automatically be confirmedupon creation, "
"easing the flow for simple events."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Events in New state"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"Every year we invite our community, partners and end-users to come and meet "
"us! It's the ideal event to get together and present new features, roadmap "
"of future versions, achievements of the software, workshops, training "
"sessions, etc.... This event is also an opportunity to showcase our "
"partners' case studies, methodology or developments. Be there and see "
"directly from the source the features of the version 8!"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_1
msgid "Exhibition"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Expected"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Expected attendees"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Finish Event"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
msgid "For any additional information, please contact us at"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event_seats_max
msgid ""
"For each event you can define a maximum registration of seats(number of "
"attendees), above this numbers the registrations are not accepted."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event_seats_min
msgid ""
"For each event you can define a minimum reserved seats (number of "
"attendees), if it does not reach the mentioned registrations the event can "
"not be confirmed (keep 0 to ignore this rule)"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "For more information on the program, please explore"
msgstr ""

#. module: event
#: model:event.event,name:event.event_1
msgid "Functional Webinar"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Functional flow of the main applications;"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Group By"
msgstr "Grouper par"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Having attended this conference, participants should be able to:"
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
#: selection:event.type.mail,interval_unit:0
msgid "Hour(s)"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm_id
#: model:ir.model.fields,field_description:event.field_event_event_id
#: model:ir.model.fields,field_description:event.field_event_mail_id
#: model:ir.model.fields,field_description:event.field_event_mail_registration_id
#: model:ir.model.fields,field_description:event.field_event_registration_id
#: model:ir.model.fields,field_description:event.field_event_type_id
#: model:ir.model.fields,field_description:event.field_event_type_mail_id
msgid "ID"
msgstr "Identifiant"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_state
msgid ""
"If event is created, the status is 'Draft'. If event is confirmed for the "
"particular dates the status is set to 'Confirmed'. If the event is over, the"
" status is set to 'Done'. If event is cancelled the status is set to "
"'Cancelled'."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"If you have a question<strong> concerning the content of the "
"training</strong>, please contact"
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
#: selection:event.type.mail,interval_unit:0
msgid "Immediately"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Install and administer your own server;"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_interval_nbr
#: model:ir.model.fields,field_description:event.field_event_type_mail_interval_nbr
msgid "Interval"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Introduction, CRM, Sales Management"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type_default_registration_max
msgid "It will select this default maximum value when you choose this event"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type_default_registration_min
msgid "It will select this default minimum value when you choose this event"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_color
msgid "Kanban Color Index"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm___last_update
#: model:ir.model.fields,field_description:event.field_event_event___last_update
#: model:ir.model.fields,field_description:event.field_event_mail___last_update
#: model:ir.model.fields,field_description:event.field_event_mail_registration___last_update
#: model:ir.model.fields,field_description:event.field_event_registration___last_update
#: model:ir.model.fields,field_description:event.field_event_type___last_update
#: model:ir.model.fields,field_description:event.field_event_type_mail___last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm_write_uid
#: model:ir.model.fields,field_description:event.field_event_event_write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration_write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_write_uid
#: model:ir.model.fields,field_description:event.field_event_registration_write_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail_write_uid
#: model:ir.model.fields,field_description:event.field_event_type_write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm_write_date
#: model:ir.model.fields,field_description:event.field_event_event_write_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration_write_date
#: model:ir.model.fields,field_description:event.field_event_mail_write_date
#: model:ir.model.fields,field_description:event.field_event_registration_write_date
#: model:ir.model.fields,field_description:event.field_event_type_mail_write_date
#: model:ir.model.fields,field_description:event.field_event_type_write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: event
#: selection:event.event,seats_availability:0
msgid "Limited"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_has_seats_limitation
msgid "Limited Seats"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_address_id
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Location"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
msgid "Luigi Roni, Senior Event Manager"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_mail_registration_ids
msgid "Mail Registration"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_event_mail_ids
#: model:ir.model.fields,field_description:event.field_event_type_event_type_mail_ids
msgid "Mail Schedule"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration_scheduler_id
msgid "Mail Scheduler"
msgstr ""

#. module: event
#: model:ir.ui.menu,name:event.menu_event_mail_schedulers
msgid "Mail Schedulers"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_type_mail
msgid "Mail Scheduling on Event Type"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration_mail_sent
msgid "Mail Sent"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_mail_sent
msgid "Mail Sent on Event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Manage &amp; publish a schedule with tracks"
msgstr ""

#. module: event
#: model:res.groups,name:event.group_event_manager
msgid "Manager"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_availability
msgid "Maximum Attendees"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_max
msgid "Maximum Attendees Number"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_default_registration_max
msgid "Maximum Registrations"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:253
#, python-format
msgid ""
"Maximum attendees number should be greater than minimum attendees number."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_min
msgid "Minimum Attendees"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_default_registration_min
msgid "Minimum Registrations"
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
#: selection:event.type.mail,interval_unit:0
msgid "Month(s)"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"More information about our Headquarter office (directions, transports, "
"parking, hotels, ...), please have a look at"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "My Events"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:258
#, python-format
msgid "No more available seats."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:357
#, python-format
msgid "No more seats available for this event."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_expected
msgid "Number of Expected Attendees"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_used
msgid "Number of Participants"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_res_partner_event_count
#: model:ir.model.fields,help:event.field_res_users_event_count
msgid "Number of events the partner has participated."
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid ""
"Odoo helps you schedule and efficiently organize your events:\n"
"    track registrations and participations, automate the confirmation emails,\n"
"    sell tickets, etc."
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_data_online
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Online"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_is_online
#: model:ir.model.fields,field_description:event.field_event_type_is_online
msgid "Online Event"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings_module_website_event_sale
msgid "Online Ticketing"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid ""
"Online events like webinars do not require a specific location\n"
"                                            and are hosted online."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type_is_online
msgid ""
"Online events like webinars do not require a specific location and are "
"hosted online."
msgstr ""

#. module: event
#: model:event.event,name:event.event_0
msgid "Open Days in Los Angeles"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"OpenElec Applications 23 Rockwell Lane, Los Angeles, CA 90001, United States"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_organizer_id
msgid "Organizer"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Participant"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"Participants are expected to have some knowledge in programming. A basic "
"knowledge of the Python programming is recommended."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"Participants preferably have a functional knowledge of our software (see "
"Functional Training)."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Partner"
msgstr "Partenaire"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_phone
msgid "Phone"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_data_physical
msgid "Physical Event"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Point of Sale (POS), Introduction to report customization."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Project management, Human resources, Contract management."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Purchase, Sales &amp; Purchase management, Financial accounting."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_registration_origin
msgid ""
"Reference of the document that created the registration, for example a sales"
" order"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Register with this event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_graph
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Registration"
msgstr ""

#. module: event
#: model:ir.actions.report,name:event.report_event_registration_badge
msgid "Registration Badge"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_date_open
msgid "Registration Date"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Day"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration ID"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration Mails"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Month"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings_module_website_event_questions
msgid "Registration Survey"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration mail"
msgstr ""

#. module: event
#: model:ir.ui.menu,name:event.menu_reporting_events
msgid "Reporting"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_reserved
msgid "Reserved Seats"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_user_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Responsible"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Scan badges to confirm attendances"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Schedule & Tracks"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_scheduled_date
msgid "Scheduled Sent Mail"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration_scheduled_date
msgid "Scheduled Time"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets on your website"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets with sales orders"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_0
msgid "Seminar"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Send by Email"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_done
msgid "Sent"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Set To Draft"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Set To Unconfirmed"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_configuration
#: model:ir.ui.menu,name:event.menu_event_global_settings
msgid "Settings"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_3
msgid "Show"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_origin
msgid "Source Document"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_date_begin
msgid "Start Date"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_date_begin_located
msgid "Start Date Located"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Start Month"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_state
#: model:ir.model.fields,field_description:event.field_event_registration_state
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Status"
msgstr "Statut"

#. module: event
#: model:ir.actions.act_window,name:event.act_register_event_partner
msgid "Subscribe"
msgstr ""

#. module: event
#: model:event.event,name:event.event_3
msgid "Technical Training"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"The Open Days are preceded by 2 days of optional training sessions for "
"experts! We propose 3 different training sessions, 2 days each."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"The organization of the training session has related costs. Due to these "
"costs, cancellations made less than 2 weeks (14 calendar days) prior to the "
"start of the training session is a subject to a fee. This fee can be up to a"
" maximum of 1000€ per cancellation request."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"The plenary sessions in the morning will be shorter and we will give more "
"time for thematical meetings, conferences, workshops and tutorial sessions "
"in the afternoon."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"The whole event is open to all public! We ask a participation fee of 49.50€ "
"for the costs for the 3 days (morning coffee, coffee breaks, drinks, "
"sandwiches for lunch and the surprising beer party of Wednesday evening) but"
" it's optional. For those who do not want to contribute, there is a free "
"ticket, therefore, catering is not inclued."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:301
#, python-format
msgid ""
"There are already attendees who attended this event. Please reset it to "
"draft if you want to cancel this event."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"These webinars allow companies interested in our software, to assess whether"
" the solution meets their needs, and can adapt to the scope of their "
"project."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"This course is dedicated to partners, integrators and developers who need to"
" grasp knowledge about the business applications development process and for"
" new developers or for IT professionals eager to learn more about technical "
"aspects."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "This event and all the conferences are in english!"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_mail_template_id
#: model:ir.model.fields,help:event.field_event_type_mail_template_id
msgid ""
"This field contains the template of the mail that will be automatically sent"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "This webinar helps participants to:"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings_module_event_sale
msgid "Tickets"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_date_tz
#: model:ir.model.fields,field_description:event.field_event_type_default_timezone
msgid "Timezone"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings_module_website_event_track
msgid "Tracks and Agenda"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_4
msgid "Training"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail_interval_type
msgid "Trigger"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_interval_type
msgid "Trigger "
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_twitter_hashtag
#: model:ir.model.fields,field_description:event.field_event_type_default_hashtag
msgid "Twitter Hashtag"
msgstr ""

#. module: event
#: selection:event.event,state:0 selection:event.registration,state:0
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Unconfirmed"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_unconfirmed
msgid "Unconfirmed Seat Reservations"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Understand the development concepts and architecture;"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Understand the various modules;"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_interval_unit
#: model:ir.model.fields,field_description:event.field_event_type_mail_interval_unit
msgid "Unit"
msgstr ""

#. module: event
#: selection:event.event,seats_availability:0
msgid "Unlimited"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unread Messages"
msgstr "Messages non-lus"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming events from today"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_use_hashtag
msgid "Use Default Hashtag"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_use_timezone
msgid "Use Default Timezone"
msgstr ""

#. module: event
#: model:res.groups,name:event.group_event_user
msgid "User"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "View full flow: purchasing, sales, project management, accounting;"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Visibility"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Warehouse management, Manufacturing (MRP) &amp; Sales, Import/Export."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid ""
"Warning: This Event has not reached its Minimum Registration Limit. Are you "
"sure you want to confirm it?"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "We are looking forward to meeting you online,"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"We strongly recommend to book your flight tickets and/or hotel reservations "
"2 weeks prior to the training. If the training is cancelled 2 weeks in "
"advance, you'll be notified by email."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"Webinars are online demonstrations where one of our team members explains "
"the main features and benefits of our online offer through an online "
"conference. We can therefore directly answer any questions you may have "
"through a Q&amp;A."
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
#: selection:event.type.mail,interval_unit:0
msgid "Week(s)"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:414
#, python-format
msgid "You must wait the event confirmation before doing this action."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:416
#, python-format
msgid "You must wait the event starting day before doing this action."
msgstr ""

#. module: event
#: model:mail.template,subject:event.event_registration_mail_template_badge
msgid "Your badge for ${object.event_id.name}"
msgstr ""

#. module: event
#: model:mail.template,subject:event.event_subscription
msgid "Your registration at ${object.event_id.name}"
msgstr ""

#. module: event
#: model:mail.template,report_name:event.event_registration_mail_template_badge
msgid "badge_of_${(object.event_id.name or '').replace('/','_')}"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "drinks and lunch;"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_confirm
msgid "event.confirm"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
msgid "<EMAIL>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "<EMAIL>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "http://bit.ly/VD8J67."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "https://www.odoo.com/services/technical-training"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:497
#, python-format
msgid "in %d days"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:501
#, python-format
msgid "next month"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:499
#, python-format
msgid "next week"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:503
#, python-format
msgid "on "
msgstr ""

#. module: event
#: model:ir.model,name:event.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:493
#, python-format
msgid "today"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:495
#, python-format
msgid "tomorrow"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "training material."
msgstr ""
