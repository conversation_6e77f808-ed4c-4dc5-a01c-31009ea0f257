# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_timesheet
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2015-11-18 13:41+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: French (Belgium) (http://www.transifex.com/odoo/odoo-9/"
"language/fr_BE/)\n"
"Language: fr_BE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line_by_project
msgid "Activities"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user_hours_delay
msgid "Avg. Plan.-Eff."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project_subtask_project_id
msgid ""
"Choosing a sub-tasks project will both enable sub-tasks and set their "
"default project (possibly the project itself)"
msgstr ""

#. module: hr_timesheet
#: constraint:project.task:0
msgid "Circular references are not permitted between tasks and sub-tasks"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
msgid "Click to record activities."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_timesheet_report_company_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_report_search
msgid "Company"
msgstr "Société"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task_delay_hours
msgid ""
"Computed as difference between planned hours by the project manager and the "
"total hours of the task."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task_total_hours
msgid "Computed as: Time Spent + Remaining Time."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task_effective_hours
msgid "Computed using the sum of the task work done."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_timesheet_report_cost
msgid "Cost"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_timesheet_report_date
msgid "Date"
msgstr "Date"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.action_define_analytic_structure
msgid "Define your Analytic Structure"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_delay_hours
msgid "Delay Hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_working_hours
msgid "Detailed Activities"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_timesheet_report_display_name
msgid "Display Name"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Duration"
msgstr "Durée"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user_hours_effective
msgid "Effective Hours"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_report_search
msgid "Extended Filters..."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_report_search
msgid "Group By"
msgstr "Grouper par"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_report_search
msgid "Group by month of date"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_effective_hours
msgid "Hours Spent"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_timesheet_report_id
msgid "ID"
msgstr "ID"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task_progress
msgid ""
"If the task has a progress of 99.99% you should close the task if it's "
"finished or reevaluate the time"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_timesheet_report___last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_report_search
msgid "Month"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_time_tracking
msgid "My Timesheet"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_parent_id
msgid "Parent Task"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user_hours_planned
msgid "Planned Hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_timesheet_report_product_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Product"
msgstr "Produit"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user_progress
msgid "Progress"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_project
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line_project_id
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_timesheet_report_project_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Project"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user_remaining_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Remaining Hours"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_kanban_inherited_progress
msgid "Remaining hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports
msgid "Reports"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Spent Hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project_subtask_project_id
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_subtask_project_id
msgid "Sub-task Project"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_subtask_count
msgid "Sub-task count"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_project_task_sub_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_children_hours
msgid "Sub-tasks Hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task_children_hours
msgid ""
"Sum of the planned hours of all sub-tasks (when a sub-task is closed or its "
"spent hours exceed its planned hours, spent hours are counted instead)"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_task
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line_task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Task"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_report_project_task_user
msgid "Tasks by user and project"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_report_search
msgid "This Month"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_timesheet_report_quantity
msgid "Time"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_hr_timesheet_report
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_report_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_report_search
msgid "Timesheet"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheet Activities"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.action_hr_timesheet_report_stat_all
#: model:ir.actions.act_window,name:hr_timesheet.action_hr_timesheet_report_stat_filtered
msgid "Timesheet Analysis"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Timesheet Month"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Timesheet by Month"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_timesheet_ids
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_timesheet_report_all
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_root
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_kanban_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_total_hours
msgid "Total"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user_total_hours
msgid "Total Hours"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
msgid "Total time"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_timesheet_report_user_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_report_search
msgid "User"
msgstr "Utilisateur"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Users"
msgstr "Utilisateurs"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_progress
msgid "Working Time Progress (%)"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
msgid ""
"You can register and track your workings hours by project every\n"
"                day. Every time spent on a project will become a cost and "
"can be re-invoiced to\n"
"                customers if required."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.action_define_analytic_structure
msgid ""
"You should create an analytic account structure depending on your needs to "
"analyse costs and revenues. In Odoo, analytic accounts are also used to "
"track customer contracts."
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_line
#, fuzzy
msgid "account analytic line"
msgstr "Ligne analytique"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_report_search
msgid "month"
msgstr ""

#~ msgid "Accounting"
#~ msgstr "Comptabilité"

#~ msgid "Analytic account"
#~ msgstr "Compte analytique"
