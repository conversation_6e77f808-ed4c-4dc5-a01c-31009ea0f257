# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_in_edi_ewaybill
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-06 21:57+0000\n"
"PO-Revision-Date: 2023-01-06 21:57+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/ewaybill_type.py:0
#, python-format
msgid " (Sub-Type: %s)"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid "%s number should be set and not more than 16 characters"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid "- Document Type"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid "- Selected Transporter is missing GSTIN"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid ""
"- Transport document number and date is required when Transportation Mode is"
" Rail,Air or Ship"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid "- Transportation Mode"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid "- Transporter is required when E-waybill is managed by transporter"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid ""
"- Vehicle Number and Type is required when Transportation Mode is By Road"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.res_config_settings_view_form_inherit_l10n_in_edi_ewaybill
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.invoice_form_inherit_l10n_in_edi_ewaybill
msgid "<span>km</span>"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.l10n_in_einvoice_report_invoice_document_inherit
msgid "<strong>E-waybill Number:</strong>"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.l10n_in_einvoice_report_invoice_document_inherit
msgid "<strong>E-waybill Valid Till:</strong>"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid ""
"A username and password still needs to be set or it's wrong for the "
"E-waybill(IN). It needs to be added and verify in the Settings."
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__active
msgid "Active"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields.selection,name:l10n_in_edi_ewaybill.selection__account_move__l10n_in_mode__3
msgid "Air"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__allowed_supply_type
msgid "Allowed for supply type"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields.selection,name:l10n_in_edi_ewaybill.selection__account_move__l10n_in_mode__1
msgid "By Road"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.invoice_form_inherit_l10n_in_edi_ewaybill
msgid "Cancel Reason"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model,name:l10n_in_edi_ewaybill.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model,name:l10n_in_edi_ewaybill.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__create_date
msgid "Created on"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,help:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_transportation_doc_date
#: model:ir.model.fields,help:l10n_in_edi_ewaybill.field_account_move__l10n_in_transportation_doc_date
#: model:ir.model.fields,help:l10n_in_edi_ewaybill.field_account_payment__l10n_in_transportation_doc_date
msgid "Date on the transporter document"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_distance
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_move__l10n_in_distance
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_payment__l10n_in_distance
msgid "Distance"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_transportation_doc_date
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_move__l10n_in_transportation_doc_date
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_payment__l10n_in_transportation_doc_date
msgid "Document Date"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_transportation_doc_no
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_move__l10n_in_transportation_doc_no
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_payment__l10n_in_transportation_doc_no
msgid "Document Number"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_type_id
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_move__l10n_in_type_id
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_payment__l10n_in_type_id
msgid "Document Type"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_res_company__l10n_in_edi_ewaybill_password
msgid "E-Waybill (IN) Password"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_res_company__l10n_in_edi_ewaybill_username
msgid "E-Waybill (IN) Username"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_res_company__l10n_in_edi_ewaybill_auth_validity
msgid "E-Waybill (IN) Valid Until"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model,name:l10n_in_edi_ewaybill.model_l10n_in_ewaybill_type
msgid "E-Waybill Document Type"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_move.py:0
#, python-format
msgid "E-waybill is already created"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_edi_ewaybill_direct_api
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_move__l10n_in_edi_ewaybill_direct_api
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_payment__l10n_in_edi_ewaybill_direct_api
msgid "E-waybill(IN) direct API"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model,name:l10n_in_edi_ewaybill.model_account_edi_format
msgid "EDI format"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid "Error"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid "HSN code is not set in product %s"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__id
msgid "ID"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid "Impossible to send the Ewaybill."
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields.selection,name:l10n_in_edi_ewaybill.selection__l10n_in_ewaybill_type__allowed_supply_type__in
msgid "Incoming"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields.selection,name:l10n_in_edi_ewaybill.selection__l10n_in_ewaybill_type__allowed_supply_type__both
msgid "Incoming and Outgoing"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/res_config_settings.py:0
#, python-format
msgid ""
"Incorrect username or password, or the GST number on company does not match."
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_res_config_settings__l10n_in_edi_ewaybill_password
msgid "Indian EDI Stock password"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_res_config_settings__l10n_in_edi_ewaybill_username
msgid "Indian EDI Stock username"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.res_config_settings_view_form_inherit_l10n_in_edi_ewaybill
msgid "Indian Electronic WayBill"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid "Invalid HSN Code (%s) in product %s"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_move.py:0
#, python-format
msgid ""
"Invalid invoice configuration:\n"
"\n"
"%s"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model,name:l10n_in_edi_ewaybill.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields.selection,name:l10n_in_edi_ewaybill.selection__account_move__l10n_in_mode__0
msgid "Managed by Transporter"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields.selection,name:l10n_in_edi_ewaybill.selection__account_move__l10n_in_vehicle_type__o
msgid "ODC"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.res_config_settings_view_form_inherit_l10n_in_edi_ewaybill
msgid "Only check if you are in production."
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields.selection,name:l10n_in_edi_ewaybill.selection__l10n_in_ewaybill_type__allowed_supply_type__out
msgid "Outgoing"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.res_config_settings_view_form_inherit_l10n_in_edi_ewaybill
msgid "Password"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.res_config_settings_view_form_inherit_l10n_in_edi_ewaybill
msgid "Production Environment"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields.selection,name:l10n_in_edi_ewaybill.selection__account_move__l10n_in_mode__2
msgid "Rail"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields.selection,name:l10n_in_edi_ewaybill.selection__account_move__l10n_in_vehicle_type__r
msgid "Regular"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.invoice_form_inherit_l10n_in_edi_ewaybill
msgid "Send E-waybill"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.res_config_settings_view_form_inherit_l10n_in_edi_ewaybill
msgid "Setup E-Waybill"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.res_config_settings_view_form_inherit_l10n_in_edi_ewaybill
msgid "Setup E-Waybill Service for this company"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields.selection,name:l10n_in_edi_ewaybill.selection__account_move__l10n_in_mode__4
msgid "Ship"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_edi_ewaybill_show_send_button
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_move__l10n_in_edi_ewaybill_show_send_button
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_payment__l10n_in_edi_ewaybill_show_send_button
msgid "Show Send E-waybill Button"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid ""
"Somehow this E-waybill has been canceled in the government portal before. "
"You can verify by checking the details into the government "
"(https://ewaybillgst.gov.in/Others/EBPrintnew.asp)"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid ""
"Somehow this E-waybill has been generated in the government portal before. "
"You can verify by checking the invoice details into the government "
"(https://ewaybillgst.gov.in/Others/EBPrintnew.asp)"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__sub_type
msgid "Sub-type"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__sub_type_code
msgid "Sub-type Code"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid ""
"The following information are missing on the invoice (see eWayBill tab):"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_move.py:0
#, python-format
msgid ""
"To cancel E-waybill set cancel reason and remarks at E-waybill tab in: \n"
"%s"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.invoice_form_inherit_l10n_in_edi_ewaybill
msgid "Transaction Details"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,help:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_transportation_doc_no
#: model:ir.model.fields,help:l10n_in_edi_ewaybill.field_account_move__l10n_in_transportation_doc_no
#: model:ir.model.fields,help:l10n_in_edi_ewaybill.field_account_payment__l10n_in_transportation_doc_no
msgid ""
"Transport document number. If it is more than 15 chars, last 15 chars may be"
" entered"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.invoice_form_inherit_l10n_in_edi_ewaybill
msgid "Transportation Details"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_mode
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_move__l10n_in_mode
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_payment__l10n_in_mode
msgid "Transportation Mode"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_transporter_id
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_move__l10n_in_transporter_id
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_payment__l10n_in_transporter_id
msgid "Transporter"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__name
msgid "Type"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_l10n_in_ewaybill_type__code
msgid "Type Code"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid ""
"Unable to connect to the E-WayBill service.The web service may be temporary "
"down. Please try again in a moment."
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.res_config_settings_view_form_inherit_l10n_in_edi_ewaybill
msgid "Username"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_vehicle_no
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_move__l10n_in_vehicle_no
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_payment__l10n_in_vehicle_no
msgid "Vehicle Number"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_bank_statement_line__l10n_in_vehicle_type
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_move__l10n_in_vehicle_type
#: model:ir.model.fields,field_description:l10n_in_edi_ewaybill.field_account_payment__l10n_in_vehicle_type
msgid "Vehicle Type"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.res_config_settings_view_form_inherit_l10n_in_edi_ewaybill
msgid "Verify Username and Password"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_move.py:0
#, python-format
msgid "You can only create E-waybill from posted invoice"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid ""
"You need at least one product having \"Product Type\" as stockable or "
"consumable."
msgstr ""

#. module: l10n_in_edi_ewaybill
#: model_terms:ir.ui.view,arch_db:l10n_in_edi_ewaybill.invoice_form_inherit_l10n_in_edi_ewaybill
msgid "eWayBill"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid "product is required to get HSN code"
msgstr ""

#. module: l10n_in_edi_ewaybill
#: code:addons/l10n_in_edi_ewaybill/models/account_edi_format.py:0
#, python-format
msgid "waiting For IRN generation To create E-waybill"
msgstr ""
