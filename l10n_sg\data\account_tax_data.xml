<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Sales Tax -->
        <record id="sg_sale_tax_es_0" model="account.tax.template">
            <field name="name">Sales Tax 0% ES33</field>
            <field name="description">0% ES33</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_exempt_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_exempt_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
        </record>

        <record id="sg_sale_tax_ds_7" model="account.tax.template">
            <field name="name">Sales Tax 7% DS</field>
            <field name="description">7% DS</field>
            <field name="price_include" eval="0"/>
            <field name="amount">7</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_792'),
                        'plus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_792'),
                        'minus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
        </record>

        <record id="sg_sale_tax_ds_8" model="account.tax.template">
            <field name="name">Sales Tax 8% DS</field>
            <field name="description">8% DS</field>
            <field name="price_include" eval="0"/>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_8"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_792'),
                        'plus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_792'),
                        'minus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
        </record>

        <record id="sg_sale_tax_esn_0" model="account.tax.template">
            <field name="name">Sales Tax 0% ESN33</field>
            <field name="description">0% ESN33</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_exempt_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_exempt_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
        </record>

        <record id="sg_sale_tax_os_0" model="account.tax.template">
            <field name="name">Sales Tax 0% OS</field>
            <field name="description">0% OS</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
        </record>

        <record id="sg_sale_tax_zr_0" model="account.tax.template">
            <field name="name">Sales Tax 0% ZR</field>
            <field name="description">0% ZR</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_zero_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_zero_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
        </record>

        <record id="sg_sale_tax_sr_7" model="account.tax.template">
            <field name="name">Sales Tax 7% SR</field>
            <field name="description">7% SR</field>
            <field name="price_include" eval="0"/>
            <field name="amount">7</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_796'),
                        'plus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_796'),
                        'minus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
        </record>

        <record id="sg_sale_tax_sr_8" model="account.tax.template">
            <field name="name">Sales Tax 8% SR</field>
            <field name="description">8% SR</field>
            <field name="price_include" eval="0"/>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_8"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_796'),
                        'plus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_796'),
                        'minus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
        </record>

        <record id="sg_sale_tax_srca_s_na" model="account.tax.template">
            <field name="name">Sales Tax SRCA-S</field>
            <field name="description">SRCA-S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
        </record>

        <record id="sg_sale_tax_srca_c_7" model="account.tax.template">
            <field name="name">Sales Tax 7% SRCA-C</field>
            <field name="description">7% SRCA-C</field>
            <field name="price_include" eval="0"/>
            <field name="amount">7</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_798'),
                        'plus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_798'),
                        'minus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
        </record>

        <record id="sg_sale_tax_srca_c_8" model="account.tax.template">
            <field name="name">Sales Tax 8% SRCA-C</field>
            <field name="description">8% SRCA-C</field>
            <field name="price_include" eval="0"/>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_8"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_798'),
                        'plus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_std_rated_supplies')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_798'),
                        'minus_report_line_ids': [ref('account_tax_report_line_output_tax_due')],
                    }),
                ]"/>
        </record>

        <!-- Purchases Tax -->

        <record id="sg_purchase_tax_txn33_7" model="account.tax.template">
            <field name="name">Purchase Tax 7% TX-N33</field>
            <field name="description">7% TX-N33</field>
            <field name="price_include" eval="0"/>
            <field name="amount">7</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_738'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_738'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_txn33_8" model="account.tax.template">
            <field name="name">Purchase Tax 8% TX-N33</field>
            <field name="description">8% TX-N33</field>
            <field name="price_include" eval="0"/>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_8"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_738'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_738'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_txe33_7" model="account.tax.template">
            <field name="name">Purchase Tax 7% TX-E33</field>
            <field name="description">7% TX-E33</field>
            <field name="price_include" eval="0"/>
            <field name="amount">7</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_739'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_739'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_txe33_8" model="account.tax.template">
            <field name="name">Purchase Tax 8% TX-E33</field>
            <field name="description">8% TX-E33</field>
            <field name="price_include" eval="0"/>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_8"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_739'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_739'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_bl_7" model="account.tax.template">
            <field name="name">Purchase Tax 7% BL</field>
            <field name="description">7% BL</field>
            <field name="price_include" eval="0"/>
            <field name="amount">7</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_740'),
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_740'),
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_bl_8" model="account.tax.template">
            <field name="name">Purchase Tax 8% BL</field>
            <field name="description">8% BL</field>
            <field name="price_include" eval="0"/>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_8"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_740'),
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_740'),
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_im_7" model="account.tax.template">
            <field name="name">Purchase Tax 7% IM</field>
            <field name="description">7% IM</field>
            <field name="price_include" eval="0"/>
            <field name="amount">7</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_741'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_741'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_im_8" model="account.tax.template">
            <field name="name">Purchase Tax 8% IM</field>
            <field name="description">8% IM</field>
            <field name="price_include" eval="0"/>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_8"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_741'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_741'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_txre_7" model="account.tax.template">
            <field name="name">Purchase Tax 7% TX-RE</field>
            <field name="description">7% TX-RE</field>
            <field name="price_include" eval="0"/>
            <field name="amount">7</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_742'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_742'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_txre_8" model="account.tax.template">
            <field name="name">Purchase Tax 8% TX-RE</field>
            <field name="description">8% TX-RE</field>
            <field name="price_include" eval="0"/>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_8"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_742'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_742'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_me_0" model="account.tax.template">
            <field name="name">Purchase Tax 7% IGDS</field>
            <field name="description">7% IGDS</field>
            <field name="price_include" eval="0"/>
            <field name="amount">7</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_743'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_743'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_me_08" model="account.tax.template">
            <field name="name">Purchase Tax 8% IGDS</field>
            <field name="description">8% IGDS</field>
            <field name="price_include" eval="0"/>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_8"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_743'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_743'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_nr_0" model="account.tax.template">
            <field name="name">Purchase Tax 0% NR</field>
            <field name="description">0% NR</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_zp_0" model="account.tax.template">
            <field name="name">Purchase Tax 0% ZP</field>
            <field name="description">0% ZP</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_op_0" model="account.tax.template">
            <field name="name">Purchase Tax 0% OP</field>
            <field name="description">0% OP</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_ep_0" model="account.tax.template">
            <field name="name">Purchase Tax 0% EP</field>
            <field name="description">0% EP</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_mes_0" model="account.tax.template">
            <field name="name">Purchase Tax MES</field>
            <field name="description">0% MES</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_applicable_goods_imported_value'), ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_applicable_goods_imported_value'), ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_tx7_7" model="account.tax.template">
            <field name="name">Purchase Tax 7% TX7</field>
            <field name="description">7% TX7</field>
            <field name="price_include" eval="0"/>
            <field name="amount">7</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_749'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_749'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_tx8_8" model="account.tax.template">
            <field name="name">Purchase Tax 8% TX8</field>
            <field name="description">8% TX8</field>
            <field name="price_include" eval="0"/>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_8"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_749'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_749'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_txca_7" model="account.tax.template">
            <field name="name">Purchase Tax 7% TXCA</field>
            <field name="description">7% TXCA</field>
            <field name="price_include" eval="0"/>
            <field name="amount">7</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_7"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_750'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_750'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>

        <record id="sg_purchase_tax_txca_8" model="account.tax.template">
            <field name="name">Purchase Tax 8% TXCA</field>
            <field name="description">8% TXCA</field>
            <field name="price_include" eval="0"/>
            <field name="amount">8</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="sg_chart_template"/>
            <field name="tax_group_id" ref="tax_group_8"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'plus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_750'),
                        'plus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'base',
                        'minus_report_line_ids': [ref('account_tax_report_line_total_taxable_purchases')],
                    }),
                    (0,0, {
                        'factor_percent': 100,
                        'repartition_type': 'tax',
                        'account_id': ref('account_account_750'),
                        'minus_report_line_ids': [ref('account_tax_report_line_inp_tax_refund_claim')],
                    }),
                ]"/>
        </record>
    </data>
</odoo>
