# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_fr_fec
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-27 14:43+0000\n"
"PO-Revision-Date: 2021-10-27 14:45+0000\n"
"Last-Translator: Rémi CAZENAVE <<EMAIL>>, 2021\n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Cancel"
msgstr "Annuler"

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Column"
msgstr "Colonne"

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Comment"
msgstr "Note"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__create_date
msgid "Created on"
msgstr "Créé le"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__display_name
msgid "Display Name"
msgstr "Nom Affiché"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__date_to
msgid "End Date"
msgstr "Date de fin"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__export_type
msgid "Export Type"
msgstr "Type d'Export"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__fec_data
msgid "FEC File"
msgstr "Fichier FEC"

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "FEC File Generation"
msgstr "Génération du fichier FEC"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__filename
msgid "Filename"
msgstr "Nom du fichier"

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Generate"
msgstr "Générer"

#. module: l10n_fr_fec
#: code:addons/l10n_fr_fec/wizard/account_fr_fec.py:0
#, python-format
msgid "Invalid VAT number for company %s"
msgstr "Numéro de TVA invalide sur la société %s"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: l10n_fr_fec
#: code:addons/l10n_fr_fec/wizard/account_fr_fec.py:0
#, python-format
msgid "Missing VAT number for company %s"
msgstr "Numéro de TVA manquant pour la société %s"

#. module: l10n_fr_fec
#: model:ir.model.fields.selection,name:l10n_fr_fec.selection__account_fr_fec__export_type__nonofficial
msgid "Non-official FEC report (posted and unposted entries)"
msgstr ""
"Rapport FEC non-officiel (avec à la fois les entrées comptabilisées et non "
"comptabilisées)"

#. module: l10n_fr_fec
#: model:ir.model.fields.selection,name:l10n_fr_fec.selection__account_fr_fec__export_type__official
msgid "Official FEC report (posted entries only)"
msgstr "Rapport FEC officiel (uniquement les entrées comptabilisées)"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__date_from
msgid "Start Date"
msgstr "Date de début"

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Technical Info"
msgstr "Information Technique"

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Technical Name"
msgstr "Nom Technique"

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__test_file
msgid "Test File"
msgstr "Fichier de Test"

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid ""
"The encoding of this text file is UTF-8. The structure of file is CSV "
"separated by pipe '|'."
msgstr ""
"L'encodage de ce fichier texte est UTF-8. La structure du fichier est CSV "
"séparé par un pipe '|'."

#. module: l10n_fr_fec
#: code:addons/l10n_fr_fec/wizard/account_fr_fec.py:0
#, python-format
msgid "The start date must be inferior to the end date."
msgstr "La date de début doit être avant la date de fin."

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "We use partner.id"
msgstr "Nous utilisons partner.id"

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid ""
"When you download a FEC file, the lock date is set to the end date.\n"
"                If you want to test the FEC file generation, please tick the "
"test file checkbox."
msgstr ""
"Quand vous téléchargez un fichier FEC, la date de verrouillage est "
"configurée avec la date de fin.\n"
"                Si vous voulez tester la génération du fichier FEC, cliquez "
"sur la coche Fichier de Test."

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid ""
"You are in test mode. The FEC file generation will not set the lock date."
msgstr ""
"Vous êtes en mode test. La génération du fichier FEC ne configurera la date "
"de verrouillage."

#. module: l10n_fr_fec
#: code:addons/l10n_fr_fec/wizard/account_fr_fec.py:0
#, python-format
msgid "You could not set the start date or the end date in the future."
msgstr "Vous ne pouvez pas utiliser une date de début ou de fin dans le futur."
