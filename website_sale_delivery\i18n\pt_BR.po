# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_delivery
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> Nascimento, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: Layna Nascimento, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "<b>Shipping Method: </b>"
msgstr "<b>Método de Envio: </b>"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"Uma descrição do produto que você deseja exibir para os seus clientes. Esta "
"descrição será copiada para todos os pedidos de venda, pedidos de entrega e "
"faturas de cliente/notas de crédito."

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__can_publish
msgid "Can Publish"
msgstr "Pode Publicar"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "Choose a delivery method"
msgstr "Escolha um método de envio"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_res_country
msgid "Country"
msgstr "País"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "DHL Shipping Methods"
msgstr "Métodos de Envio DHL"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_sale_order__amount_delivery
msgid "Delivery Amount"
msgstr "Montante de entrega"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid "Delivery will be updated after choosing a new delivery method"
msgstr "A entrega será atualizada após a escolha de um novo método de entrega"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid "Delivery:"
msgstr "Entrega:"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description"
msgstr "Descrição"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr "Descrição exibida no comércio eletrônico e nas cotações online."

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_description
msgid "Description for Online Quotations"
msgstr "Descrição para Cotações On-line"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "Easypost Shipping Methods"
msgstr "Métodos de entrega Easypost"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "FedEx Shipping Methods"
msgstr "Métodos de Envio FedEx"

#. module: website_sale_delivery
#. openerp-web
#: code:addons/website_sale_delivery/static/src/js/website_sale_delivery.js:0
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery_methods
#, python-format
msgid "Free"
msgstr "Livre"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__is_published
msgid "Is Published"
msgstr "Está publicado"

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"It seems that a delivery method is not compatible with your address. Please "
"refresh the page and try again."
msgstr ""
"Parece que um método de entrega não é compatível com seu endereço. Atualize "
"a página e tente novamente."

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"It seems that there is already a transaction for your order, you can not "
"change the delivery method anymore"
msgstr ""
"Já existe uma transação para o seu pedido, não é mais possível alterar o "
"método de entrega."

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr ""
"Nenhum método de transporte está disponível para sua ordem atual e endereço "
"de entrega. Entre em contato conosco para obter mais informações."

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_search_inherit_website_sale_delivery
msgid "Published"
msgstr "Publicado"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_id
msgid "Restrict publishing to this website."
msgstr "Restringir publicação a este site."

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_sale_order
msgid "Sales Order"
msgstr "Pedido de venda"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery_methods
msgid "Select to compute delivery rate"
msgstr "Selecione para calcular a taxa de entrega"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_delivery_carrier
#: model:ir.ui.menu,name:website_sale_delivery.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Métodos de Envios"

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid "Sorry, we are unable to ship your order"
msgstr "Desculpa, não somos capazes de enviar seu pedido"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_sale_order__amount_delivery
msgid "The amount without tax."
msgstr "Valor sem impostos."

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_url
msgid "The full URL to access the document through the website."
msgstr "A URL completa para acessar o documento através do site."

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "USPS Shipping Methods"
msgstr "Métodos de Envio USPS"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_published
msgid "Visible on current website"
msgstr "Visível neste site"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_id
msgid "Website"
msgstr "Website"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_url
msgid "Website URL"
msgstr "URL do site"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "bpost Shipping Methods"
msgstr "Métodos de Envio bpost"
