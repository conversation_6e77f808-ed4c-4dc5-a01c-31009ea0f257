# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_iban
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: JH CHOI <<EMAIL>>, 2021\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_iban
#. openerp-web
#: code:addons/base_iban/static/src/js/iban_widget.js:0
#, python-format
msgid "Account isn't IBAN compliant."
msgstr "계정이 IBAN을 준수하지 않습니다."

#. module: base_iban
#: model:ir.model,name:base_iban.model_res_partner_bank
msgid "Bank Accounts"
msgstr "은행 계좌"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "Cannot compute the BBAN because the account number is not an IBAN."
msgstr "계정 번호가 IBAN이 아니므로 BBAN을 계산할 수 없습니다."

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "IBAN"
msgstr "IBAN(국제은행계좌번호)"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid ""
"The IBAN does not seem to be correct. You should have entered something like this %s\n"
"Where B = National bank code, S = Branch code, C = Account No, k = Check digit"
msgstr ""
"국제 은행 계좌 번호가 올바르지 않은 경우 다음과 같이 입력해야 합니다 : %s\n"
"B = 국가 은행 코드, S = 지점 코드, C = 계좌 번호, k = 수표 번호"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "The IBAN is invalid, it should begin with the country code"
msgstr "IBAN이 유효하지 않습니다. 국가 코드로 시작해야 합니다."

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "There is no IBAN code."
msgstr "IBAN 코드가 없습니다."

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "This IBAN does not pass the validation check, please verify it."
msgstr "이 IBAN이 유효성 검사를 통과하지 못했습니다. 확인하시기 바랍니다."
