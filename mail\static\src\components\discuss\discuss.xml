<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.Discuss" owl="1">
        <div class="o_Discuss"
            t-att-class="{
                'o-adding-item': discuss ? discuss.isAddingChannel or discuss.isAddingChat : false,
                'o-mobile': messaging ? messaging.device.isMobile : false,
            }"
        >
            <t t-if="!discuss or !messaging.isInitialized">
                <div class="o_Discuss_messagingNotInitialized"><i class="o_Discuss_messagingNotInitializedIcon fa fa-circle-o-notch fa-spin"/>Please wait...</div>
            </t>
            <t t-else="">
                <t t-if="!messaging.device.isMobile">
                    <DiscussSidebar class="o_Discuss_sidebar bg-light border-right" localId="discuss.localId"/>
                </t>
                <t t-if="messaging.device.isMobile" t-call="mail.Discuss.content"/>
                <t t-else="">
                    <div class="o_Discuss_content">
                        <t t-call="mail.Discuss.content"/>
                    </div>
                </t>
            </t>
        </div>
    </t>

    <t t-name="mail.Discuss.content" owl="1">
        <t t-if="messaging.device.isMobile and discuss.activeMobileNavbarTabId === 'mailbox'">
            <DiscussMobileMailboxSelection class="o_Discuss_mobileMailboxSelection border-bottom"/>
        </t>
        <t t-if="messaging.device.isMobile and (discuss.isAddingChannel or discuss.isAddingChat)">
            <div class="o_Discuss_mobileAddItemHeader border-bottom">
                <AutocompleteInput
                    class="o_Discuss_mobileAddItemHeaderInput"
                    isFocusOnMount="true"
                    isHtml="discuss.isAddingChannel"
                    placeholder="discuss.isAddingChannel ? addChannelInputPlaceholder : addChatInputPlaceholder"
                    select="_onMobileAddItemHeaderInputSelect"
                    source="_onMobileAddItemHeaderInputSource"
                    t-on-o-hide="_onHideMobileAddItemHeader"
                />
            </div>
        </t>
        <t t-if="discuss.threadView">
            <ThreadView
                class="o_Discuss_thread"
                t-att-class="{ 'o-mobile': messaging.device.isMobile }"
                hasComposerCurrentPartnerAvatar="!messaging.device.isMobile"
                hasComposerDiscardButton="discuss.thread === messaging.inbox"
                hasComposerThreadName="discuss.thread === messaging.inbox"
                hasComposerThreadTyping="true"
                threadViewLocalId="discuss.threadView.localId"
            />
        </t>
        <t t-if="!discuss.thread and (!messaging.device.isMobile or discuss.activeMobileNavbarTabId === 'mailbox')">
            <div class="o_Discuss_noThread">
                No conversation selected.
            </div>
        </t>
        <t t-if="messaging.device.isMobile and discuss.activeMobileNavbarTabId !== 'mailbox'">
            <t t-if="discuss.activeMobileNavbarTabId === 'chat'">
                <button class="o_Discuss_mobileNewChatButton btn btn-secondary" t-on-click="discuss.onClickMobileNewChatButton.bind(discuss)">
                    Start a conversation
                </button>
            </t>
            <t t-if="discuss.activeMobileNavbarTabId === 'channel'">
                <button class="o_Discuss_mobileNewChannelButton btn btn-secondary" t-on-click="discuss.onClickMobileNewChannelButton.bind(discuss)">
                    New Channel
                </button>
            </t>
            <NotificationList
                class="o_Discuss_notificationList"
                filter="discuss.activeMobileNavbarTabId"
            />
        </t>
        <t t-if="messaging.device.isMobile and !(discuss.threadView and discuss.threadView.replyingToMessageView)">
            <MobileMessagingNavbar
                class="o_Discuss_mobileNavbar"
                activeTabId="discuss.activeMobileNavbarTabId"
                tabs="mobileNavbarTabs()"
                t-on-o-select-mobile-messaging-navbar-tab="_onSelectMobileNavbarTab"
            />
        </t>
    </t>

</templates>
