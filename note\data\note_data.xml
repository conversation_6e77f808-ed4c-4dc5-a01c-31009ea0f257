<?xml version="1.0"?>
<odoo>
    <data>
    <record model="note.stage" id="note_stage_00">
        <field name="name">New</field>
        <field name="sequence">0</field>
        <field name="user_id" ref="base.user_admin"/>
    </record>

    <record model="note.stage" id="note_stage_01">
        <field name="name">Meeting Minutes</field>
        <field name="sequence">5</field>
        <field name="user_id" ref="base.user_admin"/>
    </record>

    <record model="note.stage" id="note_stage_02">
        <field name="name">Notes</field>
        <field name="sequence">10</field>
        <field name="user_id" ref="base.user_admin"/>
    </record>

    <record model="note.stage" id="note_stage_03">
        <field name="name">Todo</field>
        <field name="sequence">50</field>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    </data>
</odoo>
