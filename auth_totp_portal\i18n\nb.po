# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_portal
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_totp_portal
#. openerp-web
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid " Copy"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "(Disable two-factor authentication)"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                        Two-factor authentication not enabled"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<span class=\"text-success\">\n"
"                        <i class=\"fa fa-check-circle\"/>\n"
"                        Two-factor authentication enabled\n"
"                    </span>"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Added On</strong>"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Trusted Device</strong>"
msgstr ""

#. module: auth_totp_portal
#. openerp-web
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid "Copied !"
msgstr "Kopiert!"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Enable two-factor authentication"
msgstr ""

#. module: auth_totp_portal
#. openerp-web
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid "Operation failed for unknown reason."
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Revoke All"
msgstr ""

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Two-factor authentication"
msgstr ""

#. module: auth_totp_portal
#: model:ir.model,name:auth_totp_portal.model_res_users
msgid "Users"
msgstr "Brukere"
