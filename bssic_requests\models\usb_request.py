from odoo import models, fields, api, _
from odoo.exceptions import UserError


class BSSICUSBRequest(models.Model):
    """USB Usage Request Model - Proxy to bssic.request"""
    _name = 'bssic.usb.request'
    _description = 'BSSIC USB Usage Request'
    _inherit = 'bssic.request'
    _auto = False  # Don't create database table
    _table = 'bssic_request'  # Use the same table as bssic.request

    # USB request specific fields
    usb_purpose = fields.Char('Purpose of USB Usage', tracking=True)
    usb_duration = fields.Char('Required Duration', tracking=True)
    data_type = fields.Char('Type of Data to Transfer', tracking=True)

    @api.constrains('usb_purpose', 'usb_duration', 'data_type', 'state', 'show_usb_fields')
    def _check_required_usb_fields(self):
        """Validate required fields for USB requests"""
        for record in self:
            if record.show_usb_fields and record.state != 'draft':
                if not record.usb_purpose:
                    raise UserError(_('Purpose of USB Usage is required for USB requests.'))
                if not record.usb_duration:
                    raise UserError(_('Required Duration is required for USB requests.'))
                if not record.data_type:
                    raise UserError(_('Type of Data to Transfer is required for USB requests.'))

    @api.model
    def create(self, vals):
        """Redirect create to bssic.request with USB defaults"""
        # Set request type code for USB
        if not vals.get('request_type_code'):
            vals['request_type_code'] = 'usb'

        # Find and set the USB request type
        if not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'usb')
            ], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        return self.env['bssic.request'].create(vals)

    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):
        """Redirect search to bssic.request with USB filter"""
        request_model = self.env['bssic.request']
        # Add filter for USB requests
        usb_args = args + [('request_type_id.code', '=', 'usb')]
        return request_model.search(usb_args, offset=offset, limit=limit, order=order, count=count)

    def write(self, vals):
        """Redirect write to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).write(vals)

    def unlink(self):
        """Redirect unlink to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).unlink()

    @api.model
    def browse(self, ids):
        """Redirect browse to bssic.request"""
        return self.env['bssic.request'].browse(ids)

    def read(self, fields=None, load='_classic_read'):
        """Redirect read to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).read(fields=fields, load=load)

    @api.model
    def default_get(self, fields_list):
        """Set default values for USB requests"""
        res = super(BSSICUSBRequest, self).default_get(fields_list)
        
        # Set default request type for USB
        if 'request_type_code' in fields_list:
            res['request_type_code'] = 'usb'
        
        if 'request_type_id' in fields_list:
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'usb')
            ], limit=1)
            if request_type:
                res['request_type_id'] = request_type.id

        return res
