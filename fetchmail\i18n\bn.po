# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fetchmail
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Bengali (https://app.transifex.com/odoo/teams/41243/bn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__active
msgid "Active"
msgstr "সক্রিয়"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Advanced"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Advanced Options"
msgstr ""

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Archived"
msgstr "আর্কাইভ করা"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__configuration
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Configuration"
msgstr "কনফিগারেশন"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "নিশ্চিত"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid "Connection test failed: %s"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__create_uid
msgid "Created by"
msgstr "দ্বারা সৃষ্টি"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__create_date
msgid "Created on"
msgstr "তৈরি"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__display_name
msgid "Display Name"
msgstr "প্রদর্শন নাম"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_tree
msgid "Email Count"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Fetch Now"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__id
msgid "ID"
msgstr "আইডি "

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "IMAP"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "If SSL required."
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr ""

#. module: fetchmail
#: model:ir.model,name:fetchmail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Incoming Mail Server"
msgstr ""

#. module: fetchmail
#: model:ir.actions.act_window,name:fetchmail.action_email_server_tree
#: model:ir.ui.menu,name:fetchmail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr ""

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"Invalid server name !\n"
" %s"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__original
msgid "Keep Original"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server____last_update
msgid "Last Modified on"
msgstr "সর্বশেষ সংশোধিত"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__write_uid
msgid "Last Updated by"
msgstr "সর্বশেষ আপডেট করেছেন"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__write_date
msgid "Last Updated on"
msgstr "সর্বশেষ আপডেট হয়েছে"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Login Information"
msgstr ""

#. module: fetchmail
#: model:ir.actions.server,name:fetchmail.ir_cron_mail_gateway_action_ir_actions_server
#: model:ir.cron,cron_name:fetchmail.ir_cron_mail_gateway_action
#: model:ir.cron,name:fetchmail.ir_cron_mail_gateway_action
msgid "Mail: Fetchmail Service"
msgstr ""

#. module: fetchmail
#: model:ir.actions.act_window,name:fetchmail.act_server_history
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__message_ids
msgid "Messages"
msgstr "বার্তা সমূহ"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__name
msgid "Name"
msgstr "নাম"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr ""

#. module: fetchmail
#: model:ir.model,name:fetchmail.model_mail_mail
msgid "Outgoing Mails"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "POP"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__password
msgid "Password"
msgstr "শব্দচাবি"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__port
msgid "Port"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Reset Confirmation"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "SSL"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__script
msgid "Script"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Server & Login"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Server Information"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__server
msgid "Server Name"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr ""

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Server type IMAP."
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Server type POP."
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__state
msgid "Status"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Test & Confirm"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__user
msgid "Username"
msgstr "ব্যবহারকারীর নাম"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
