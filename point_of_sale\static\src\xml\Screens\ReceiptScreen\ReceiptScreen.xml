<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="ReceiptScreen" owl="1">
        <div class="receipt-screen screen">
            <div class="screen-content">
                <div class="top-content">
                    <div class="top-content-center">
                        <h1 t-if="!env.isMobile">
                            <t t-esc="orderAmountPlusTip" />
                        </h1>
                    </div>
                    <div class="button next" t-att-class="{ highlight: !locked }"
                        t-on-click="orderDone">
                        New Order <i class="fa fa-angle-double-right"></i>
                    </div>
                </div>
                <div class="default-view">
                    <div class="actions">
                        <h1>How would you like to receive your receipt<t t-if="currentOrder.is_to_invoice()"> &amp; invoice</t>?</h1>
                        <div class="buttons">
                            <div class="button print" t-on-click="printReceipt">
                                <i class="fa fa-print"></i> Print Receipt
                            </div>
                        </div>
                        <form t-on-submit.prevent="onSendEmail" class="send-email">
                            <div class="input-email">
                                <input type="email" placeholder="Email Receipt" t-model="orderUiState.inputEmail" />
                                <button class="send" t-att-class="{ highlight: is_email(orderUiState.inputEmail) }" type="submit">
                                    <i class="fa fa-paper-plane" aria-hidden="true"/>
                                </button>
                            </div>
                        </form>
                        <div class="notice">
                            <div t-if="orderUiState.emailSuccessful !== null" t-attf-class="{{ orderUiState.emailSuccessful ? 'successful' : 'failed' }}">
                                <t t-esc="orderUiState.emailNotice"></t>
                            </div>
                            <div class="send-mail-info" t-if="currentOrder.is_to_invoice()">
                                (Both will be sent by email)
                            </div>
                        </div>
                    </div>
                    <div class="pos-receipt-container">
                        <OrderReceipt order="currentOrder" t-ref="order-receipt" />
                    </div>
                </div>
            </div>
        </div>
    </t>

</templates>
