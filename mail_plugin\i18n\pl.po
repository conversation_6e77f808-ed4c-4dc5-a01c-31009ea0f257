# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_plugin
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.war<PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "%s employees"
msgstr "%spracownicy"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Add Contact To Database"
msgstr "Dodaj kontakt do bazy danych"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Address"
msgstr "Adres"

#. module: mail_plugin
#: model_terms:ir.ui.view,arch_db:mail_plugin.app_auth
msgid "Allow"
msgstr "Zezwól"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "An error has occurred when trying to fetch translations."
msgstr "Wystąpił błąd podczas próby pobrania tłumaczeń."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Annual Revenue"
msgstr "Roczny przychód"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid ""
"Attachments could not be logged in Odoo because their total size exceeded "
"the allowed maximum."
msgstr ""
"Załączniki nie mogły zostać zalogowane w Odoo, ponieważ ich łączny rozmiar "
"przekroczył dozwolony limit."

#. module: mail_plugin
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#, python-format
msgid "Bad Email."
msgstr "Zły e-mail."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Buy More"
msgstr "Kup więcej"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Buy new credits"
msgstr "Kup nowe kredyty"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Can not save the contact"
msgstr "Nie można zapisać kontaktu"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Clear Translations Cache"
msgstr "Wyczyść pamięć podręczną tłumaczeń"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Close"
msgstr "Zamknij"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Company"
msgstr "Firma"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Company Created."
msgstr "Utworzono firmę."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Company Insights"
msgstr "Informacje o firmie"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Company Type"
msgstr "Typ firmy"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Company created"
msgstr "Utworzono firmę"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Company updated"
msgstr "Firma zaktualizowana"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: model:ir.model,name:mail_plugin.model_res_partner
#, python-format
msgid "Contact"
msgstr "Kontakt"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Contact Details"
msgstr "Szczegóły kontaktu"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Contact created"
msgstr "Utworzono kontakt"

#. module: mail_plugin
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#, python-format
msgid "Contact has no valid email"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Contacts Found (%(count)s)"
msgstr "Znaleziono kontakty (%(count)s)"

#. module: mail_plugin
#: model:ir.model.fields,help:mail_plugin.field_res_partner_iap__partner_id
msgid "Corresponding partner"
msgstr ""

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Could not autocomplete the company. Internal error. Try again later..."
msgstr ""
"Nie można autouzupełnić firmy. Błąd wewnętrzny. Spróbuj ponownie później..."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Could not connect to database. Try to log out and in."
msgstr ""
"Nie można połączyć się z bazą danych. Spróbuj się wylogować i zalogować."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Could not connect to your database. Please try again."
msgstr "Nie można połączyć się z bazą danych. Spróbuj ponownie."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Could not display image %(attachmentName)s, size is over limit."
msgstr ""
"Nie można wyświetlić obrazu %(attachmentName)s, rozmiar przekracza limit."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Create"
msgstr "Utwórz"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Create a Company"
msgstr "Utwórz firmę"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Create a company"
msgstr "Utwórz firmę"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Debug"
msgstr "Debugowanie"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Debug Zone"
msgstr "Strefa debugowania"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Debug zone for development purpose."
msgstr "Strefa debugowania do celów programistycznych."

#. module: mail_plugin
#: model_terms:ir.ui.view,arch_db:mail_plugin.app_auth
msgid "Deny"
msgstr "Odrzuć"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Description"
msgstr "Opis"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: mail_plugin
#: model:ir.model.fields,help:mail_plugin.field_res_partner_iap__iap_search_domain
msgid "Domain used to find the company"
msgstr "Domena użyta do znalezienia firmy"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Email already logged on the contact"
msgstr "E-mail już zarejestrowany w kontakcie"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Employees"
msgstr "Pracownicy"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Enrich Company"
msgstr "Wzbogać firmę"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Error during enrichment"
msgstr "Błąd podczas wzbogacania"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Founded Year"
msgstr "Rok założenia"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "From : %(email)s"
msgstr "Od : %(email)s"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "From:"
msgstr "Od:"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Gmail Inbox"
msgstr "Skrzynka odbiorcza Gmail"

#. module: mail_plugin
#: model:ir.model,name:mail_plugin.model_ir_http
msgid "HTTP Routing"
msgstr "Wytyczanie HTTP"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner__iap_enrich_info
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__iap_enrich_info
#: model:ir.model.fields,field_description:mail_plugin.field_res_users__iap_enrich_info
msgid "IAP Enrich Info"
msgstr "IAP informacje o wzbogacaniu"

#. module: mail_plugin
#: model:ir.actions.act_window,name:mail_plugin.res_partner_iap_action
#: model_terms:ir.ui.view,arch_db:mail_plugin.res_partner_iap_view_form
#: model_terms:ir.ui.view,arch_db:mail_plugin.res_partner_iap_view_tree
msgid "IAP Partner"
msgstr "Partner IAP"

#. module: mail_plugin
#: model:ir.ui.menu,name:mail_plugin.res_partner_iap_menu
msgid "IAP Partners"
msgstr "Partnerzy IAP"

#. module: mail_plugin
#: model:ir.model.fields,help:mail_plugin.field_res_partner__iap_enrich_info
#: model:ir.model.fields,help:mail_plugin.field_res_partner_iap__iap_enrich_info
#: model:ir.model.fields,help:mail_plugin.field_res_users__iap_enrich_info
msgid "IAP response stored as a JSON string"
msgstr "Odpowiedź IAP zapisana jako ciąg JSON"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__id
msgid "ID"
msgstr "ID"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Industry"
msgstr "Sektor"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Invalid URL"
msgstr "Nieprawidłowy adres URL"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Keywords"
msgstr "Słowa kluczowe"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: mail_plugin
#: model_terms:ir.ui.view,arch_db:mail_plugin.app_auth
msgid "Let"
msgstr "Niech"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Log Email Into Contact"
msgstr "Zaloguj wiadomość e-mail do Kontaktu"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Log email"
msgstr "Dziennik e-mail"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Logged from"
msgstr "Zalogowano z"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Login"
msgstr "Login"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Logout"
msgstr "Wyloguj"

#. module: mail_plugin
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#, python-format
msgid "No Access"
msgstr "Brak dostępu"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No additional insights were found for this company"
msgstr "Nie znaleziono żadnych dodatkowych informacji dla tej firmy"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No company attached to this contact"
msgstr "Do tego kontaktu nie jest przypisana żadna firma"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "No company attached to this contact."
msgstr "Do tego kontaktu nie jest przypisana żadna firma."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No company linked to this contact could be enriched"
msgstr "Żadna firma powiązana z tym kontaktem nie mogła zostać wzbogacona"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No company linked to this contact could be enriched or found in Odoo"
msgstr ""
"Żadna firma powiązana z tym kontaktem nie mogła zostać wzbogacona ani "
"znaleziona w Odoo"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "No contact found."
msgstr "Nie znaleziono kontaktu."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No data found for this email address."
msgstr "Nie znaleziono danych dla tego adresu e-mail."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No extra information found"
msgstr "Nie znaleziono żadnych dodatkowych informacji"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "No insights for this company."
msgstr "Brak informacji na temat tej firmy."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "No insights found for this address."
msgstr "Nie znaleziono informacji dla tego adresu."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Not enough credits to enrich."
msgstr "Za mało kredytów do wzbogacenia."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Odoo Access Token"
msgstr "Token dostępu do Odoo"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Odoo Server URL"
msgstr "Adres URL serwera Odoo"

#. module: mail_plugin
#: model:ir.model.constraint,message:mail_plugin.constraint_res_partner_iap_unique_partner_id
msgid "Only one partner IAP is allowed for one partner"
msgstr "Tylko jeden partner IAP jest dozwolony dla jednego partnera"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid ""
"Oops, looks like you have exhausted your free enrichment requests. Please "
"log in to try again."
msgstr ""
"Ups, wygląda na to, że wyczerpałeś swoje darmowe prośby o wzbogacenie. "
"Zaloguj się, aby spróbować ponownie."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Our IAP server is down, please come back later."
msgstr "Nasz serwer IAP nie działa, prosimy o powrót później."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Outlook Inbox"
msgstr "Skrzynka odbiorcza programu Outlook"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__partner_id
msgid "Partner"
msgstr "Kontrahent"

#. module: mail_plugin
#: model:ir.model,name:mail_plugin.model_res_partner_iap
msgid "Partner IAP"
msgstr "Partner IAP"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Phones"
msgstr "Telefony"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Read more"
msgstr "Czytaj więcej"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Refresh"
msgstr "Odśwież"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Refresh Contact"
msgstr "Odśwież kontakt"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Revenues"
msgstr "Zyski"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Save in Odoo"
msgstr "Zapisywanie w Odoo"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Save the contact to create the company"
msgstr "Zapisz kontakt, aby utworzyć firmę"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Search"
msgstr "Szukaj"

#. module: mail_plugin
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner__iap_search_domain
#: model:ir.model.fields,field_description:mail_plugin.field_res_partner_iap__iap_search_domain
#: model:ir.model.fields,field_description:mail_plugin.field_res_users__iap_search_domain
msgid "Search Domain / Email"
msgstr "Wyszukiwanie domeny / adresu e-mail"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Search In Database"
msgstr "Szukaj w bazie danych"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Search In Odoo"
msgstr "Szukaj w Odoo"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Search contact"
msgstr "Wyszukaj kontakt"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Search contact in Odoo..."
msgstr "Wyszukiwanie kontaktu w Odoo..."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Something bad happened. Please, try again later."
msgstr "Stało się coś złego. Spróbuj ponownie później."

#. module: mail_plugin
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#, python-format
msgid "The partner already has a company related to him"
msgstr "Partner ma już powiązaną z nim spółkę"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "This company has no email address and could not be enriched."
msgstr "Ta firma nie ma adresu e-mail i nie może zostać wzbogacona."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "This contact does not exist in the Odoo database."
msgstr "Ten kontakt nie istnieje w bazie danych Odoo."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "This contact has no email address, no company could be enriched."
msgstr "Ten kontakt nie ma adresu e-mail, nie można wzbogacić firmy."

#. module: mail_plugin
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#, python-format
msgid "This partner does not exist"
msgstr "Ten partner nie istnieje"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid ""
"Warning: Attachments could not be logged in Odoo because their total size "
"exceeded the allowed maximum."
msgstr ""
"Ostrzeżenie: Załączniki nie mogły zostać zalogowane w Odoo, ponieważ ich "
"łączny rozmiar przekroczył dozwolony limit."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid ""
"Warning: Could not fetch the attachments %(attachments)s as their sizes are "
"bigger then the maximum size of %(size)sMB per each attachment."
msgstr ""
"Ostrzeżenie: Nie można pobrać załączników %(attachments)s, ponieważ ich "
"rozmiary przekraczają maksymalny rozmiar %(size)s MB dla każdego załącznika."

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Website"
msgstr "Strona internetowa"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Year founded"
msgstr "Rok założenia"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "You don't have enough credit to enrich."
msgstr "Nie masz wystarczającego kredytu, aby się wzbogacić."

#. module: mail_plugin
#: code:addons/mail_plugin/controllers/mail_plugin.py:0
#, python-format
msgid "You need to specify at least the partner_id or the name and the email"
msgstr "Musisz podać co najmniej partner_id lub nazwę i adres e-mai"

#. module: mail_plugin
#: model_terms:ir.ui.view,arch_db:mail_plugin.app_auth
msgid "access your Odoo database?"
msgstr "uzyskać dostęp do bazy danych Odoo?"

#. module: mail_plugin
#. openerp-web
#: code:addons/mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "employees"
msgstr "pracownicy"
