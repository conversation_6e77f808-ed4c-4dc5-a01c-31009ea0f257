from odoo import fields, models, api


class HrEmployeePrivate(models.Model):
    _inherit = 'hr.employee'

    marital = fields.Selection([
        ('single', 'Single'),
        ('married', 'Married'),
        ('married_and_provide', 'متجوز ويعول'),
        ('cohabitant', 'Legal Cohabitant'),
        ('widower', 'Widower'),
        ('divorced', 'Divorced')
    ], string='Marital Status', groups="hr.group_hr_user", default='single', tracking=True)

    providers_ids = fields.One2many('hr.employee.provide','employee_id',string='المعولين')

class HrEmployeeProvides(models.Model):
        _name = 'hr.employee.provide'

        employee_id = fields.Many2one('hr.employee' , ondelete='cascade')

        name=fields.Char('اسم المعول')
        dob = fields.Date('تاريخ الميلاد')
        relation = fields.Char('العلاقة')

    #work_location = fields.Many2one('work.location','Work Location')

#
# class WorkLocation(models.Model):
#     _name = 'work.location'
#
#     name = fields.Char("Work Location")


#access_hr_payroll_work_location,hr.employee.payroll.work.location,libya_hr_payroll.model_work_location,base.group_user,1,1,1,1
#access_work_location,access.work.location,model_work_location,,1,1,1,1
