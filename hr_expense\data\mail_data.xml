<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="mail_act_expense_approval" model="mail.activity.type">
            <field name="name">Expense Approval</field>
            <field name="icon">fa-dollar</field>
            <field name="res_model">hr.expense.sheet</field>
        </record>

        <!-- default alias for expenses -->
        <record id="mail_alias_expense" model="mail.alias">
            <field name="alias_name">expense</field>
            <field name="alias_model_id" ref="model_hr_expense"/>
            <field name="alias_user_id" ref="base.user_admin"/>
            <field name="alias_contact">employees</field>
        </record>

        <!-- Expense-related subtypes for messaging / Chatter -->
        <record id="mt_expense_approved" model="mail.message.subtype">
            <field name="name">Approved</field>
            <field name="res_model">hr.expense.sheet</field>
            <field name="default" eval="True"/>
            <field name="description">Expense report approved</field>
        </record>
        <record id="mt_expense_refused" model="mail.message.subtype">
            <field name="name">Refused</field>
            <field name="res_model">hr.expense.sheet</field>
            <field name="default" eval="True"/>
            <field name="description">Expense report refused</field>
        </record>
        <record id="mt_expense_paid" model="mail.message.subtype">
            <field name="name">Paid</field>
            <field name="res_model">hr.expense.sheet</field>
            <field name="description">Expense report paid</field>
            <field name="default" eval="True"/>
        </record>
    </data>
</odoo>
