<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="project_collaborator_view_form" model="ir.ui.view">
        <field name="name">project.collaborator.view.form</field>
        <field name="model">project.collaborator</field>
        <field name="arch" type="xml">
            <form string="Project Collaborator" create="0" edit="0">
                <sheet>
                    <group>
                        <field name="project_id" options="{'no_create': True}" />
                        <field name="partner_id" options="{'no_create': True}" />
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="project_sharing_access_view_tree" model="ir.ui.view">
        <field name="name">project.collaborator.view.tree</field>
        <field name="model">project.collaborator</field>
        <field name="arch" type="xml">
            <tree string="Project Collaborators" create="0">
                <field name="project_id" options="{'no_create': True}"/>
                <field name="partner_id" options="{'no_create': True}"/>
            </tree>
        </field>
    </record>

    <record id="project_collaborator_view_search" model="ir.ui.view">
        <field name="name">project.collaborator.view.search</field>
        <field name="model">project.collaborator</field>
        <field name="arch" type="xml">
            <search>
                <field name="partner_id" />
                <field name="project_id" />
                <group expand="0" string="Group By">
                    <filter name="project" string="Project" context="{'group_by': 'project_id'}" />
                    <filter name="collaborator" string="Collaborator" context="{'group_by': 'partner_id'}" />
                </group>
            </search>
        </field>
    </record>

    <record id="project_collaborator_action" model="ir.actions.act_window">
        <field name="name">Project Collaborators</field>
        <field name="res_model">project.collaborator</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('project_id', '=', active_id)]</field>
        <field name="search_view_id" ref="project_collaborator_view_search"/>
    </record>

</odoo>
