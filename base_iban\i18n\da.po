# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_iban
# 
# Translators:
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_iban
#. openerp-web
#: code:addons/base_iban/static/src/js/iban_widget.js:0
#, python-format
msgid "Account isn't IBAN compliant."
msgstr "Konto er ikke IBAN-kompatible."

#. module: base_iban
#: model:ir.model,name:base_iban.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Bankkonti"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "Cannot compute the BBAN because the account number is not an IBAN."
msgstr "Kan ikke udregne BBAN fordi kontonummeret ikke er et IBAN."

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "IBAN"
msgstr "IBAN"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid ""
"The IBAN does not seem to be correct. You should have entered something like this %s\n"
"Where B = National bank code, S = Branch code, C = Account No, k = Check digit"
msgstr ""
"IBAN ser ikke ud til at være korrekt. Du bør indtaste noget i denne stil %s\n"
"Hvor B = National bankkode, S = Afdelingskode, C = Kontonummer, k = Beregnet tal"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "The IBAN is invalid, it should begin with the country code"
msgstr "IBAN er ikke gyldigt. Det skal starte med landekoden."

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "There is no IBAN code."
msgstr "Der er ikke nogen IBAN kode."

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "This IBAN does not pass the validation check, please verify it."
msgstr "IBAN nummeret kunne ikke valideres. Kontrollér det venligst. "
