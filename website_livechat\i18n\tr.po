# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_livechat
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>rtu<PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Ertuğrul Güreş <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__session_count
msgid "# Sessions"
msgstr "# Oturum"

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "%s has left the conversation."
msgstr "%s görüşmeden ayrıldı."

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid ""
"%s has started a conversation with %s. \n"
"                        The chat request has been canceled."
msgstr ""
"%s, %s ile bir görüşme başlattı.\n"
"                         Sohbet isteği iptal edildi."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<small>%</small>"
msgstr "<small>%</small>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Live Chat</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Canlı Sohbet</span>\n"
"                         <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<span>Livechat Channel</span>"
msgstr "<span>Canlı Yardım Kanalı</span>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "Available"
msgstr "Uygun"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Bad"
msgstr "Kötü"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__can_publish
msgid "Can Publish"
msgstr "Yayımlanabilir"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Channel"
msgstr "Kanal"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_tree
msgid "Chat"
msgstr "Sohbet"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Chats"
msgstr "Sohbetler"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_description
msgid "Description of the channel displayed on the website page"
msgstr "Websitesinde gösterilen kanal açıklaması"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_mail_channel
msgid "Discussion Channel"
msgstr "Mesajlaşma Kanalı"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Great"
msgstr "Harika"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Yönlendirme"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Happy face"
msgstr "Mutlu yüz"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "History"
msgstr "Geçmiş"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "In Conversation"
msgstr "Sohbette"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__is_published
msgid "Is Published"
msgstr "Yayınlandı"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Lang"
msgstr "Dil"

#. module: website_livechat
#: code:addons/website_livechat/models/website.py:0
#, python-format
msgid "Live Support"
msgstr "Canlı Destek"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Live chat channel of your website"
msgstr "Web sitenizin canlı sohbet kanalları"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_im_livechat_channel
msgid "Livechat Channel"
msgstr "Canlı Yardım Kanalı"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "Livechat Support Channels"
msgstr "Canlı Yardım Destek Kanalları"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/js/website_livechat.editor.js:0
#, python-format
msgid "Name"
msgstr "Adı"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Neutral face"
msgstr "Tarafsız yüz"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/js/website_livechat.editor.js:0
#, python-format
msgid "New Channel"
msgstr "Yeni Kanal"

#. module: website_livechat
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid "No Livechat Channel allows you to send a chat request for website %s."
msgstr ""
"Hiçbir Livechat Kanalı %sweb sitesi için bir sohbet isteği göndermenize izin"
" vermez."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Not rated yet"
msgstr "Henüz derecelendirilmedi"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Okay"
msgstr "Tamam"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Online"
msgstr "Çevrimiçi"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Operator Avatar"
msgstr "Operator Avatar"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_name
msgid "Operator Name"
msgstr "Operatör ismi"

#. module: website_livechat
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid ""
"Recipients are not available. Please refresh the page to get latest visitors"
" status."
msgstr ""
"Alıcılar mevcut değil. En son ziyaretçilerin durumunu görmek için lütfen "
"sayfayı yenileyin."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Sad face"
msgstr "Üzgün Yüz"

#. module: website_livechat
#: model:ir.actions.server,name:website_livechat.website_livechat_send_chat_request_action_server
msgid "Send Chat Requests"
msgstr "Sohbet İstekleri Gönder"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
msgid "Send chat request"
msgstr "Sohbet isteği gönder"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Speaking With"
msgstr "İle Konuşmak"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_id
msgid "Speaking with"
msgstr "İle Konuşmak"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Statistics"
msgstr "İstatistikler"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The"
msgstr "Bu"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The Team"
msgstr "Ekip"

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_url
msgid "The full URL to access the document through the website."
msgstr "Belgeye web sitesinden erişim için tam URL adresi."

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "The visitor"
msgstr "Ziyaretçi"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "There are no public livechat channels to show."
msgstr "Gösterilecek hiçbir kamuya açık kanal yok."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "There are no ratings for this channel for now."
msgstr "Şimdilik bu kanal için derecelendirme yok."

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_published
msgid "Visible on current website"
msgstr "Mevcut web sitesinde görülebilir"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_mail_channel__livechat_visitor_id
msgid "Visitor"
msgstr "Ziyaretçi"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#, python-format
msgid "Visitor is online"
msgstr "Ziyaretçi çevrimiçi"

#. module: website_livechat
#: model:ir.actions.act_window,name:website_livechat.website_visitor_livechat_session_action
msgid "Visitor's Sessions"
msgstr "Ziyaretçinin Oturumları"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__mail_channel_ids
msgid "Visitor's livechat channels"
msgstr "Ziyaretçinin Livechat Kanalları"

#. module: website_livechat
#: model:ir.ui.menu,name:website_livechat.website_livechat_visitor_menu
msgid "Visitors"
msgstr "Ziyaretçiler"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/components/visitor_banner/visitor_banner.xml:0
#: model:ir.model,name:website_livechat.model_website
#, python-format
msgid "Website"
msgstr "Websitesi"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_res_config_settings__channel_id
msgid "Website Live Channel"
msgstr "Websitesi Canlı Kanalı"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website__channel_id
msgid "Website Live Chat Channel"
msgstr "Websitesi Canlı Yardım Kanalı"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_url
msgid "Website URL"
msgstr "Web Sitesi URL Adresi"

#. module: website_livechat
#: code:addons/website_livechat/tests/test_livechat_basic_flow.py:0
#: model:ir.model,name:website_livechat.model_website_visitor
#, python-format
msgid "Website Visitor"
msgstr "Websitesi Ziyaretçi"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_description
msgid "Website description"
msgstr "Web Sitesi açıklaması"

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "an operator"
msgstr "operatör"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "last feedbacks"
msgstr "son geri bildirimler"
