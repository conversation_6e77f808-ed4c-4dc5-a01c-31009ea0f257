<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
  <defs>
    <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
      <use xlink:href="#filterPath" fill="none"></use>
    </clipPath>
    <path id="filterPath"
      d="M0.1831,0.3127C-0.5843,1.8563,1.3335,0.2534,0.9483,0.0368,0.847-0.0201,0.3686-0.061,0.1831,0.3127Z"></path>
  </defs><svg viewBox="34.86399841308594 100.27824401855469 233.13612365722656 122.38334655761719"
    preserveAspectRatio="none">
    <path class="background"
      d="M165,203.24C4.46,258.67,20.75,181.65,65.21,143.92,173.87,51.72,400.22,122.06,165,203.24Z" fill="#383E45">
    </path>
  </svg><svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
    <use xlink:href="#filterPath" fill="darkgrey"></use>
  </svg>
  <image xlink:href="" clip-path="url(#clip-path)"></image>
</svg>
