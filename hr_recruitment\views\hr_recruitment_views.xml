<?xml version="1.0"?>
<odoo>
    <data>

    <!-- Stage -->
    <record id="hr_job_stage_act" model="ir.actions.act_window">
        <field name="name">Recruitment / Applicants Stages</field>
        <field name="res_model">hr.recruitment.stage</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Add a new stage in the recruitment process
          </p><p>
            Define here your stages of the recruitment process, for example:
            qualification call, first interview, second interview, refused,
            hired.
          </p>
        </field>
    </record>

    <!-- Applicants -->
    <record model="ir.ui.view" id="crm_case_tree_view_job">
        <field name="name">Applicants</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <tree string="Applicants" multi_edit="1" sample="1">
                <field name="message_needaction" invisible="1"/>
                <field name="last_stage_id" invisible="1"/>
                <field name="date_last_stage_update" invisible="1"/>
                <field name="partner_name" readonly="1"/>
                <field name="job_id"/>
                <field name="name" readonly="1"/>
                <field name="stage_id"/>
                <field name="priority" widget="priority" optional="show"/>
                <field name="partner_mobile" widget="phone" readonly="1" optional="show"/>
                <field name="categ_ids" widget="many2many_tags" options="{'color_field': 'color'}" optional="show"/>
                <field name="user_id" widget="many2one_avatar_user" optional="show"/>
                <field name="create_date" readonly="1" widget="date" optional="show"/>
                <field name="partner_phone" widget="phone" readonly="1" optional="hide"/>
                <field name="email_from" readonly="1" optional="hide"/>
                <field name="medium_id" optional="hide"/>
                <field name="source_id" readonly="1" optional="hide"/>
                <field name="salary_expected" optional="hide"/>
                <field name="salary_proposed" optional="hide"/>
                <field name="type_id" invisible="1"/>
                <field name="availability" optional="hide"/>
                <field name="department_id" invisible="context.get('invisible_department', True)" readonly="1"/>
                <field name="company_id" groups="base.group_multi_company" readonly="1" optional="hide"/>
            </tree>
        </field>
    </record>

    <record id="hr_applicant_view_tree_activity" model="ir.ui.view">
        <field name="name">hr.applicant.view.tree.activity</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <tree string="Next Activities" decoration-danger="activity_date_deadline &lt; current_date" default_order="activity_date_deadline">
                <field name="name"/>
                <field name="partner_id"/>
                <field name="activity_date_deadline"/>
                <field name="activity_type_id"/>
                <field name="activity_summary"/>
                <field name="stage_id"/>
                <field name="activity_exception_decoration" widget="activity_exception"/>
            </tree>
        </field>
    </record>

    <record model="ir.ui.view" id="hr_applicant_view_form">
        <field name="name">Jobs - Recruitment Form</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
          <form string="Jobs - Recruitment Form" class="o_applicant_form">
            <header>
                <button string="Create Employee" name="create_employee_from_applicant" type="object" data-hotkey="v"
                        class="oe_highlight o_create_employee" attrs="{'invisible': ['|',('emp_id', '!=', False),('active', '=', False)]}"/>
                <button string="Refuse" name="archive_applicant" type="object" attrs="{'invisible': [('active', '=', False)]}" data-hotkey="x"/>
                <button string="Restore" name="toggle_active" type="object" attrs="{'invisible': [('active', '=', True)]}" data-hotkey="z"/>
                <field name="stage_id" widget="statusbar" options="{'clickable': '1', 'fold_field': 'fold'}" attrs="{'invisible': [('active', '=', False),('emp_id', '=', False)]}"/>
            </header>
            <sheet>
                <div class="oe_button_box" name="button_box">
                    <button name="action_applications_email"
                            class="oe_stat_button"
                            icon="fa-pencil"
                            type="object"
                            context="{'active_test': False}"
                            attrs="{'invisible': [('application_count', '=' , 0)]}">
                        <field name="application_count" widget="statinfo" string="Other applications"/>
                    </button>
                    <button name="action_makeMeeting" class="oe_stat_button" icon="fa-calendar" type="object"
                         >
                         <div class="o_field_widget o_stat_info">
                             <span class="o_stat_value"><field name="meeting_display_text" /></span>
                             <span class="o_stat_text"><field name="meeting_display_date" readonly="1"/></span>
                         </div>
                    </button>
                </div>
                <widget name="web_ribbon" title="Refused" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                <widget name="web_ribbon" title="Hired" attrs="{'invisible': [('date_closed', '=', False)]}" />
                <field name="kanban_state" widget="kanban_state_selection"/>
                <field name="active" invisible="1"/>
                <field name="legend_normal" invisible="1"/>
                <field name="legend_blocked" invisible="1"/>
                <field name="legend_done" invisible="1"/>
                <div class="oe_title">
                    <label for="name" class="oe_edit_only"/>
                    <h1><field name="name" placeholder="e.g. Sales Manager 2 year experience"/></h1>
                    <h2 class="o_row">
                        <div>
                            <label for="partner_name" class="oe_edit_only"/>
                            <field name="partner_name"/>
                        </div>
                    </h2>
                </div>
                <group>
                    <group>
                        <field name="partner_id" invisible="1" />
                        <field name="refuse_reason_id" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="email_from" widget="email"/>
                        <field name="email_cc" groups="base.group_no_one"/>
                        <field name="partner_phone" widget="phone"/>
                        <field name="partner_mobile" widget="phone"/>
                        <field name="type_id" placeholder="Degree"/>
                    </group>
                    <group>
                        <field name="categ_ids" placeholder="Tags" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}"/>
                        <field name="user_id" domain="[('share', '=', False)]"/>
                        <field name="date_closed" attrs="{'invisible': [('date_closed', '=', False)]}" />
                        <field name="priority" widget="priority"/>
                        <field name="medium_id" groups="base.group_no_one" />
                        <field name="source_id"/>
                    </group>
                    <group string="Job">
                        <field name="job_id"/>
                        <field name="department_id"/>
                        <field name="company_id" groups="base.group_multi_company" options='{"no_open":True}' />
                    </group>
                    <group string="Contract">
                        <label for="salary_expected"/>
                        <div class="o_row">
                            <field name="salary_expected"/>
                            <span attrs="{'invisible':[('salary_expected_extra','=',False)]}"> + </span>
                            <field name="salary_expected_extra" placeholder="Extra advantages..."/>
                        </div>
                        <label for="salary_proposed"/>
                        <div class="o_row">
                            <field name="salary_proposed"/>
                            <span attrs="{'invisible':[('salary_proposed_extra','=',False)]}"> + </span>
                            <field name="salary_proposed_extra" placeholder="Extra advantages..."/>
                        </div>
                        <field name="availability"/>
                        <field name="emp_id" invisible="1"/>
                    </group>
                </group>
                <notebook>
                    <page string="Application Summary">
                        <field name="description" placeholder="Motivations..."/>
                    </page>
                </notebook>
            </sheet>
            <div class="oe_chatter">
                <field name="message_follower_ids"/>
                <field name="activity_ids"/>
                <field name="message_ids" options="{'open_attachments': True}"/>
            </div>
          </form>
        </field>
    </record>

    <record model="ir.ui.view" id="crm_case_pivot_view_job">
        <field name="name">Jobs - Recruitment</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
              <pivot string="Job Applications" sample="1">
                <field name="create_date" type="row"/>
                <field name="stage_id" type="col"/>
                <field name="color" invisible="1"/>
            </pivot>
        </field>
    </record>

    <record model="ir.ui.view" id="crm_case_graph_view_job">
        <field name="name">Jobs - Recruitment Graph</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
              <graph string="Cases By Stage and Estimates" sample="1">
                <field name="stage_id"/>
            </graph>
        </field>
    </record>

    <record id="hr_applicant_view_search_bis" model="ir.ui.view">
        <field name="name">hr.applicant.view.search</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <search string="Search Applicants">
                <field string="Applicant" name="partner_name"
                    filter_domain="['|', '|', ('name', 'ilike', self), ('partner_name', 'ilike', self), ('email_from', 'ilike', self)]"/>
                <field string="Email" name="email_from" filter_domain="[('email_from', 'ilike', self)]"/>
                <field name="job_id"/>
                <field name="department_id" operator="child_of"/>
                <field name="user_id"/>
                <field name="stage_id" domain="[]"/>
                <field name="categ_ids"/>
                <field name="refuse_reason_id"/>
                <field name="attachment_ids" filter_domain="[('attachment_ids.index_content', 'ilike', self)]" string="Attachments"/>
                <filter string="My Applications" name="my_applications" domain="[('user_id', '=', uid)]"/>
                <filter string="Unassigned" name="unassigned" domain="[('user_id', '=', False)]"/>
                <separator/>
                <filter string="Ready for Next Stage" name="done" domain="[('kanban_state', '=', 'done')]"/>
                <filter string="Blocked" name="blocked" domain="[('kanban_state', '=', 'blocked')]"/>
                <separator/>
                <filter string="Creation Date" name="filter_create" date="create_date"/>
                <filter string="Last Stage Update" name="filter_date_last_stage_update" date="date_last_stage_update"/>
                <separator/>
                <filter string="Unread Messages" name="message_needaction" domain="[('message_needaction', '=', True)]"/>
                <separator/>
                <filter string="Archived / Refused" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter invisible="1" string="Late Activities" name="activities_overdue"
                    domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                    help="Show all records which has next action date is before today"/>
                <filter invisible="1" string="Today Activities" name="activities_today"
                    domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                    domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Responsible" name="responsible" domain="[]"  context="{'group_by': 'user_id'}"/>
                    <filter string="Job" name="job" domain="[]" context="{'group_by': 'job_id'}"/>
                    <filter string="Degree" name="degree" domain="[]" context="{'group_by': 'type_id'}"/>
                    <filter string="Stage" name="stage" domain="[]" context="{'group_by': 'stage_id'}"/>
                    <filter string="Refuse Reason" name="refuse_reason_id" domain="[]" context="{'group_by': 'refuse_reason_id'}"/>
                    <filter string="Creation Date" name="creation_date" context="{'group_by': 'create_date'}"/>
                    <filter string="Last Stage Update" name="last_stage_update" context="{'group_by': 'date_last_stage_update'}"/>
                </group>
           </search>
        </field>
    </record>

     <record id="hr_recruitment_source_view_search" model="ir.ui.view">
        <field name="name">hr.recruitment.source.view.search</field>
        <field name="model">hr.recruitment.source</field>
        <field name="arch" type="xml">
            <search string="Search Source">
                <field name="source_id"/>
                <field name="job_id"/>
           </search>
        </field>
    </record>

    <record model="ir.ui.view" id="hr_applicant_calendar_view">
        <field name="name">Hr Applicants Calendar</field>
        <field name="model">hr.applicant</field>
        <field name="priority" eval="2"/>
        <field name="arch" type="xml">
            <calendar string="Applicants" mode="month" date_start="activity_date_deadline" color="user_id" event_limit="5" hide_time="true">
                <field name="partner_name"/>
                <field name="job_id"/>
                <field name="priority" widget="priority"/>
                <field name="activity_summary"/>
                <field name="user_id" filters="1" invisible="1"/>
            </calendar>
        </field>
    </record>

    <record id="quick_create_applicant_form" model="ir.ui.view">
        <field name="name">hr.applicant.form.quick_create</field>
        <field name="model">hr.applicant</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="name"/>
                    <field name="partner_name"/>
                    <field name="email_from"/>
                    <field name="job_id" options="{'no_open': True}"/>
                    <field name="company_id" invisible="1"/>
                </group>
            </form>
        </field>
    </record>

    <!-- Hr Applicant Kanban View -->
    <record model="ir.ui.view" id="hr_kanban_view_applicant">
        <field name="name">Hr Applicants kanban</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <kanban default_group_by="stage_id" class="o_kanban_applicant" quick_create_view="hr_recruitment.quick_create_applicant_form" sample="1">
                <field name="stage_id" options='{"group_by_tooltip": {"requirements": "Requirements"}}'/>
                <field name="date_closed"/>
                <field name="color"/>
                <field name="priority"/>
                <field name="user_id"/>
                <field name="user_email"/>
                <field name="partner_name"/>
                <field name="type_id"/>
                <field name="partner_id"/>
                <field name="job_id"/>
                <field name="department_id"/>
                <field name="attachment_number"/>
                <field name="active"/>
                <field name="activity_ids" />
                <field name="activity_state" />
                <progressbar field="activity_state" colors='{"planned": "success", "overdue": "danger", "today": "warning"}'/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="{{!selection_mode ? 'oe_kanban_color_' + kanban_getcolor(record.color.raw_value) : ''}} oe_kanban_card oe_kanban_global_click oe_applicant_kanban oe_semantic_html_override">
                            <field name="date_closed" invisible="1"/>
                            <div class="ribbon ribbon-top-right" style="pointer-events: none;" attrs="{'invisible': [('date_closed', '=', False)]}">
                                <span class="bg-success">Hired</span>
                            </div>
                            <span class="badge badge-pill badge-danger pull-right mr-4" attrs="{'invisible': [('active', '=', True)]}">Refused</span>
                            <div class="o_dropdown_kanban dropdown">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-toggle="dropdown" href="#" aria-label="Dropdown menu" title="Dropdown menu" data-display="static">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <t t-if="widget.deletable"><a role="menuitem" type="delete" class="dropdown-item">Delete</a></t>
                                    <a role="menuitem" name="action_makeMeeting" type="object" class="dropdown-item">Schedule Interview</a>
                                    <div role="separator" class="dropdown-divider"></div>
                                    <ul class="oe_kanban_colorpicker text-center" data-field="color"/>
                                </div>
                            </div>
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <b class="o_kanban_record_title mt8" t-if="record.partner_name.raw_value">
                                            <field name="partner_name"/><br/>
                                        </b><t t-else="1">
                                            <i class="o_kanban_record_title"><field name="name"/></i><br/>
                                        </t>
                                        <div class="o_kanban_record_subtitle" invisible="context.get('search_default_job_id', False)">
                                            <field name="job_id"/>
                                        </div>
                                    </div>
                                </div>
                                <field name="categ_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                                <t t-if="record.partner_mobile.raw_value"><i class="fa fa-mobile mr4" role="img" aria-label="Mobile" title="Mobile"/><field name="partner_mobile" widget="phone"/><br/></t>
                                <div class="o_kanban_record_bottom mt4">
                                    <div class="oe_kanban_bottom_left">
                                        <div class="float-left mr4" groups="base.group_user">
                                            <field name="priority" widget="priority"/>
                                        </div>
                                        <div class="o_kanban_inline_block mr8">
                                            <field name="activity_ids" widget="kanban_activity"/>
                                        </div>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <a name="action_get_attachment_tree_view" type="object">
                                            <span title='Documents'><i class='fa fa-paperclip' role="img" aria-label="Documents"/>
                                                <t t-esc="record.attachment_number.raw_value"/>
                                            </span>
                                        </a>
                                        <div class="o_kanban_state_with_padding ml-1 mr-2" >
                                            <field name="kanban_state" widget="kanban_state_selection"/>
                                            <field name="legend_normal" invisible="1"/>
                                            <field name="legend_blocked" invisible="1"/>
                                            <field name="legend_done" invisible="1"/>
                                        </div>
                                        <field name="user_id" widget="many2one_avatar_user"/>
                                    </div>

                                </div>
                            </div>
                            <div class="oe_clear"></div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="hr_applicant_view_activity" model="ir.ui.view">
        <field name="name">hr.applicant.activity</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <activity string="Applicants">
                <templates>
                    <div t-name="activity-box">
                        <div>
                            <field name="name" display="full"/>
                            <field name="partner_name" muted="1" display="full"/>
                        </div>
                    </div>
                </templates>
            </activity>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_hr_job_applications">
        <field name="name">Applications</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">kanban,tree,form,graph,calendar,pivot,activity</field>
        <field name="search_view_id" ref="hr_applicant_view_search_bis"/>
        <field name="context">{'search_default_job_id': [active_id], 'default_job_id': active_id}</field>
        <field name="help" type="html">
              <p class="o_view_nocontent_empty_folder">
                No applications yet
              </p><p>
                Odoo helps you track applicants in the recruitment
                process and follow up all operations: meetings, interviews, etc.
              </p><p>
                Applicants and their attached CV are created automatically when an email is sent.
                If you install the document management modules, all resumes are indexed automatically,
                so that you can easily search through their content.
              </p>
         </field>
    </record>

    <record model="ir.actions.act_window" id="action_hr_job_sources">
        <field name="name">Jobs Sources</field>
        <field name="res_model">hr.recruitment.source</field>
        <field name="view_mode">tree,kanban</field>
        <field name="search_view_id" ref="hr_recruitment_source_view_search"/>
        <field name="context">{'search_default_job_id': [active_id], 'default_job_id': active_id}</field>
        <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Want to analyse where applications come from ?
              </p><p>
                Use emails and links trackers
              </p>
         </field>
    </record>

    <!-- Jobs -->
    <record id="view_job_filter_recruitment" model="ir.ui.view">
        <field name="name">Job</field>
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr.view_job_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='in_position']" position="before">
                <filter string="My Favorites" name="my_favorite_jobs" domain="[('favorite_user_ids', 'in', uid)]"/>
                <separator/>
            </xpath>
        </field>
    </record>

    <record id="hr_job_simple_form" model="ir.ui.view">
        <field name="name">hr.job.simple.form</field>
        <field name="model">hr.job</field>
        <field name="priority">200</field>
        <field name="arch" type="xml">
            <form string="Create a Job Position" class="o_hr_job_simple_form" >
                <sheet>
                    <group>
                        <field name="name" class="o_job_name oe_inline" placeholder="e.g. Sales Manager"/>
                        <label for="alias_name" string="Application email" attrs="{'invisible': [('alias_domain', '=', False)]}" help="Define a specific contact address for this job position. If you keep it empty, the default email address will be used which is in human resources settings"/>
                        <div name="alias_def" attrs="{'invisible': [('alias_domain', '=', False)]}">
                            <field name="alias_id" class="oe_read_only" string="Email Alias" required="0"/>
                            <div class="oe_edit_only" name="edit_alias">
                                <field name="alias_name" class="oe_inline o_job_alias" placeholder="e.g. sales-manager"/>@<field name="alias_domain" class="oe_inline" readonly="1"/>
                            </div>
                            <div class="text-muted" attrs="{'invisible': [('alias_domain', '=', False)]}">Applicants can send resume to this email address,<br/>it will create an application automatically</div>
                        </div>
                    </group>
                    <footer>
                        <button string="Create" name="close_dialog" type="object" class="btn-primary o_create_job" data-hotkey="q"/>
                        <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>
    <record id="create_job_simple" model="ir.actions.act_window">
        <field name="name">Create a Job Position</field>
        <field name="res_model">hr.job</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_job_simple_form"/>
        <field name="target">new</field>
    </record>


    <record id="hr_job_survey" model="ir.ui.view">
        <field name="name">hr.job.form1</field>
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr.view_hr_job_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='department_id']" position="after">
                <field name="address_id" context="{'show_address': 1}" domain= "[('is_company', '=', True )]" options="{'always_reload': True}"/>
                <label for="alias_name" string="Email Alias" attrs="{'invisible': [('alias_domain', '=', False)]}" help="Define a specific contact address for this job position. If you keep it empty, the default email address will be used which is in human resources settings"/>
                <div name="alias_def" attrs="{'invisible': [('alias_domain', '=', False)]}">
                    <field name="alias_id" class="oe_read_only" string="Email Alias" required="0"/>
                    <div class="oe_edit_only" name="edit_alias">
                        <field name="alias_name" class="oe_inline"/>@<field name="alias_domain" class="oe_inline" readonly="1"/>
                    </div>
                </div>
            </xpath>
            <field name="no_of_recruitment" position="after">
                <field name="user_id" domain="[('share', '=', False)]"/>
            </field>
            <div name="button_box" position="inside">
                <button class="oe_stat_button"
                    icon="fa-pencil"
                    name="%(action_hr_job_applications)d"
                    context="{'default_user_id': user_id, 'active_test': False}"
                    type="action">
                    <field name="all_application_count" widget="statinfo" string="Job Applications"/>
                </button>
                <button class="oe_stat_button"
                    icon="fa-file-text-o"
                    name="action_get_attachment_tree_view"
                    type="object">
                    <field name="documents_count" widget="statinfo" string="Documents"/>
                </button>
                <button class="oe_stat_button" type="action"
                    name="%(action_hr_job_sources)d" icon="fa-bar-chart-o"
                    context="{'default_job_id': active_id}">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">Trackers</span>
                    </div>
                </button>
            </div>
        </field>
    </record>

        <!-- hr related job position menu action -->
         <record model="ir.actions.act_window" id="action_hr_job_config">
            <field name="name">Job Positions</field>
            <field name="res_model">hr.job</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('hr.view_hr_job_tree')}),
                (0, 0, {'view_mode': 'kanban', 'view_id': ref('hr.hr_job_view_kanban')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('hr.view_hr_job_form')})]"/>
            <field name="context">{'search_default_in_recruitment': 1}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Ready to recruit more efficiently?
              </p><p>
                Let's create a job position.
              </p>
            </field>
        </record>

     <!--
        hr.applicant.refuse.reason views
    -->
    <record id="hr_applicant_refuse_reason_view_form" model="ir.ui.view">
        <field name="name">Applicant refuse reason form</field>
        <field name="model">hr.applicant.refuse.reason</field>
        <field name="arch" type="xml">
            <form string="Refuse Reason">
                <sheet>
                    <widget name="web_ribbon" text="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_title">
                        <div class="oe_edit_only">
                            <label for="name"/>
                        </div>
                        <h1>
                            <field name="name"/>
                        </h1>
                        <field name="active" invisible="1"/>
                    </div>
                    <group>
                        <field name="template_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hr_applicant_refuse_reason_view_tree" model="ir.ui.view">
        <field name="name">Applicant refuse reason tree</field>
        <field name="model">hr.applicant.refuse.reason</field>
        <field name="arch" type="xml">
            <tree string="Refuse Reason" editable="bottom">
                <field name="name"/>
                <field name="template_id"/>
            </tree>
        </field>
    </record>

    <record id="hr_applicant_refuse_reason_action" model="ir.actions.act_window">
        <field name="name">Refuse Reasons</field>
        <field name="res_model">hr.applicant.refuse.reason</field>
        <field name="view_mode">tree,form</field>
    </record>

    ######################## JOB OPPORTUNITIES (menu) ###########################
    <record model="ir.actions.act_window" id="crm_case_categ0_act_job">
        <field name="name">Applications</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">kanban,tree,form,pivot,graph,calendar,activity</field>
        <field name="view_id" eval="False"/>
        <field name="search_view_id" ref="hr_applicant_view_search_bis"/>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_empty_folder">
            No applications yet
          </p><p>
            Odoo helps you track applicants in the recruitment
            process and follow up all operations: meetings, interviews, etc.
          </p><p>
            Applicants and their attached CV are created automatically when an email is sent.
            If you install the document management modules, all resumes are indexed automatically,
            so that you can easily search through their content.
          </p>
        </field>
    </record>

    <record model="ir.actions.act_window.view" id="action_hr_sec_kanban_view_act_job">
        <field name="sequence" eval="1"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="hr_kanban_view_applicant"/>
        <field name="act_window_id" ref="crm_case_categ0_act_job"/>
    </record>

    <record model="ir.actions.act_window.view" id="action_hr_sec_tree_view_act_job">
        <field name="sequence" eval="0"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="crm_case_tree_view_job"/>
        <field name="act_window_id" ref="crm_case_categ0_act_job"/>
    </record>

    <record model="ir.actions.act_window.view" id="action_hr_sec_form_view_act_job">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_applicant_view_form"/>
        <field name="act_window_id" ref="crm_case_categ0_act_job"/>
    </record>

    <record  id="hr_applicant_action_view_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="3"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="crm_case_pivot_view_job"/>
        <field name="act_window_id" ref="crm_case_categ0_act_job"/>
    </record>

    <record id="action_hr_sec_graph_view_act_job" model="ir.actions.act_window.view">
        <field name="sequence" eval="4"/>
        <field name="view_mode">graph</field>
        <field name="view_id" ref="crm_case_graph_view_job"/>
        <field name="act_window_id" ref="crm_case_categ0_act_job"/>
    </record>

    <menuitem
        name="Recruitment"
        id="menu_hr_recruitment_root"
        web_icon="hr_recruitment,static/description/icon.png"
        groups="hr_recruitment.group_hr_recruitment_user"
        sequence="210"/>

    <menuitem id="menu_hr_recruitment_configuration" name="Configuration" parent="menu_hr_recruitment_root"
        sequence="100"/>

    <!-- ALL JOBS REQUESTS -->
    <menuitem parent="menu_hr_recruitment_configuration" id="menu_hr_job_position_config" action="action_hr_job_config" sequence="10"/>

    <menuitem
        id="menu_hr_applicant_refuse_reason"
        action="hr_applicant_refuse_reason_action"
        parent="menu_hr_recruitment_configuration"
        sequence="10"/>

    <menuitem
        name="Applications"
        parent="menu_hr_recruitment_root"
        id="menu_crm_case_categ0_act_job" sequence="2"/>

    <menuitem
        name="All Applications"
        parent="menu_crm_case_categ0_act_job"
        id="menu_crm_case_categ_all_app" action="crm_case_categ0_act_job" sequence="2"/>

    <!-- Resume and Letters -->
    <record id="ir_attachment_view_search_inherit_hr_recruitment" model="ir.ui.view">
        <field name="name">ir.attachment.search.inherit.recruitment</field>
        <field name="model">ir.attachment</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="base.view_attachment_search"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='create_date']" position="after">
                <field name="index_content" string="Content"/>
            </xpath>
            <xpath expr="//filter[@name='my_documents_filter']" position="attributes">
                <attribute name='invisible'>1</attribute>
            </xpath>
            <xpath expr="//filter[@name='url_filter']" position="attributes">
                <attribute name='invisible'>1</attribute>
            </xpath>
            <xpath expr="//filter[@name='binary_filter']" position="attributes">
                <attribute name='invisible'>1</attribute>
            </xpath>
        </field>
    </record>

    <record model="ir.actions.server" id="hr_applicant_resumes_server">
        <field name="name">hr.applicant.resumes.server</field>
        <field name="model_id" ref="hr_recruitment.model_hr_applicant"/>
        <field name="state">code</field>
        <field name="code">
act = env.ref('hr_recruitment.hr_applicant_resumes').read()[0]
act['domain'] = [('res_model', '=', 'hr.applicant'), '|', ('company_id', '=', False), ('company_id', '=', env.user.company_id.id)]
action = act
        </field>
    </record>


    <!-- Stage Tree View -->
    <record model="ir.ui.view" id="hr_recruitment_stage_tree">
        <field name="name">hr.recruitment.stage.tree</field>
        <field name="model">hr.recruitment.stage</field>
        <field name="arch" type="xml">
            <tree string="Stages">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="fold"/>
                <field name="hired_stage"/>
            </tree>
        </field>
    </record>

    <!-- Stage Kanban View -->
    <record id="view_hr_recruitment_stage_kanban" model="ir.ui.view">
        <field name="name">hr.recruitment.stage.kanban</field>
        <field name="model">hr.recruitment.stage</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="fold"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div>
                                <strong><field name="name"/></strong>
                            </div>
                            <div>
                                <span>Folded in Recruitment Pipe: </span>
                                <field name="fold" widget="boolean"/>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Stage Form View -->
    <record model="ir.ui.view" id="hr_recruitment_stage_form">
        <field name="name">hr.recruitment.stage.form</field>
        <field name="model">hr.recruitment.stage</field>
        <field name="arch" type="xml">
            <form string="Stage">
            <sheet>
                <group name="stage_definition" string="Stage Definition">
                    <group>
                        <field name="name"/>
                        <field name="sequence" groups="base.group_no_one"/>
                        <field name="template_id" domain= "[('model_id.model', '=', 'hr.applicant')]"/>
                    </group>
                    <group name="stage_details">
                        <field name="fold"/>
                        <field name="hired_stage"/>
                        <field name="is_warning_visible" invisible="1"/>
                        <span attrs="{'invisible': [('is_warning_visible', '=', False)]}">
                            <span
                                class="fa fa-exclamation-triangle text-danger pl-3">
                            </span>
                            <span class="text-danger">
                                All applications will lose their hired date and hired status.
                            </span>
                        </span>
                        <field name="job_ids" widget="many2many_tags"/>
                    </group>
                </group>
                <group name="tooltips" string="Tooltips">
                    <p class="text-muted" colspan="2">
                        You can define here the labels that will be displayed for the kanban state instead
                        of the default labels.
                    </p>
                    <label for="legend_normal" string=" " class="o_status"/>
                    <field name="legend_normal" nolabel="1"/>
                    <label for="legend_blocked" string=" " class="o_status o_status_red"/>
                    <field name="legend_blocked" nolabel="1"/>
                    <label for="legend_done" string=" " class="o_status o_status_green"/>
                    <field name="legend_done" nolabel="1"/>
                </group>
                <separator string="Requirements"/>
                <field name="requirements"/>
            </sheet>
            </form>
        </field>
    </record>

    <!-- Stage Action -->
    <record id="hr_recruitment_stage_act" model="ir.actions.act_window">
        <field name="name">Stages</field>
        <field name="res_model">hr.recruitment.stage</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="view_id" ref="hr_recruitment_stage_tree"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Add a new stage in the recruitment process
          </p><p>
            Don't forget to specify the department if your recruitment process
            is different according to the job position.
          </p>
        </field>
    </record>

    <menuitem
        id="menu_hr_recruitment_stage"
        name="Stages"
        parent="menu_hr_recruitment_configuration"
        action="hr_recruitment_stage_act"
        groups="base.group_no_one"
        sequence="1"/>

    <!-- Tag Form View -->
    <record id="hr_applicant_category_view_form" model="ir.ui.view">
        <field name="name">hr.applicant.category.form</field>
        <field name="model">hr.applicant.category</field>
        <field name="arch" type="xml">
            <form string="Tags">
            <sheet>
                <group>
                    <field name="name"/>
                    <field name="color"/>
                </group>
            </sheet>
            </form>
        </field>
    </record>

    <record id="hr_applicant_category_view_tree" model="ir.ui.view">
        <field name="name">hr.applicant.category.tree</field>
        <field name="model">hr.applicant.category</field>
        <field name="arch" type="xml">
            <tree string="Tags" editable="bottom">
                <field name="name"/>
                <field name="color" groups="base.group_no_one"/>
            </tree>
        </field>
    </record>

    <!-- Tag Action -->
    <record id="hr_applicant_category_action" model="ir.actions.act_window">
        <field name="name">Tags</field>
        <field name="res_model">hr.applicant.category</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new tag
            </p>
        </field>
    </record>

    <menuitem
        id="hr_applicant_category_menu"
        parent="menu_hr_recruitment_configuration"
        action="hr_applicant_category_action"
        sequence="2" groups="base.group_no_one"/>

    <!-- Degree Tree View -->
    <record model="ir.ui.view" id="hr_recruitment_degree_tree">
        <field name="name">hr.recruitment.degree.tree</field>
        <field name="model">hr.recruitment.degree</field>
        <field name="arch" type="xml">
            <tree string="Degree" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <!-- Degree Form View -->
    <record model="ir.ui.view" id="hr_recruitment_degree_form">
        <field name="name">hr.recruitment.degree.form</field>
        <field name="model">hr.recruitment.degree</field>
        <field name="arch" type="xml">
            <form string="Degree">
            <sheet>
                <group>
                    <field name="name"/>
                    <field name="sequence" groups="base.group_no_one"/>
                </group>
            </sheet>
            </form>
        </field>
    </record>

    <!-- Degree Action -->
    <record id="hr_recruitment_degree_action" model="ir.actions.act_window">
        <field name="name">Degree</field>
        <field name="res_model">hr.recruitment.degree</field>
        <field name="view_id" ref="hr_recruitment_degree_tree"/>
    </record>

     <menuitem
            id="menu_hr_recruitment_degree"
            name="Degrees"
            parent="menu_hr_recruitment_configuration"
            action="hr_recruitment_degree_action"
            sequence="5" groups="base.group_no_one"/>

    <!-- Source Kanban View -->
    <record id="hr_recruitment_source_kanban" model="ir.ui.view">
        <field name="name">hr.recruitment.source.kanban</field>
        <field name="model">hr.recruitment.source</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile" create="0" sample="1">
                <field name="job_id"/>
                <field name="email"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings row">
                                        <div class="col-4">
                                            <h3 class="o_kanban_record_title"><field name="source_id"/></h3>
                                        </div>
                                        <div class="col-8 text-right">
                                            <div><field name="job_id"/></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body mt-3">
                                    <div class="text-right">
                                        <field name="email" attrs="{'invisible': [('email', '=', False)]}" widget="email"/>
                                        <button name="create_alias" class="btn btn-primary" type="object" attrs="{'invisible': [('email', '!=', False)]}">Generate Email</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Source Tree View -->
    <record model="ir.ui.view" id="hr_recruitment_source_tree">
        <field name="name">hr.recruitment.source.tree</field>
        <field name="model">hr.recruitment.source</field>
        <field name="arch" type="xml">
            <tree string="Sources of Applicants" editable="top" class="o_recruitment_list" sample="1">
                <field name="source_id" placeholder="e.g. LinkedIn" decoration-bf="1" attrs="{'readonly': [('id', '!=', False)]}"/>
                <field name="job_id" attrs="{'readonly': [('id', '!=', False)]}"/>
                <field name="email" attrs="{'invisible': [('email', '=', False)]}" widget="email"/>
                <button name="create_alias" string="Generate Email" class="btn btn-primary" type="object" attrs="{'invisible': [('email', '!=', False)]}"/>
            </tree>
        </field>
    </record>
    <record id="hr_recruitment_source_action" model="ir.actions.act_window">
        <field name="name">Sources of Applicants</field>
        <field name="res_model">hr.recruitment.source</field>
    </record>

    <menuitem
        id="menu_hr_recruitment_source"
        parent="menu_hr_recruitment_configuration"
        action="hr_recruitment_source_action"
        groups="base.group_no_one"
        sequence="10"/>

    <record id="hr_applicant_action_from_department" model="ir.actions.act_window">
        <field name="name">New Applications</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">kanban,tree,form,graph,calendar,pivot</field>
        <field name="context">{
            'search_default_department_id': active_id,
            'default_department_id': active_id}
        </field>
        <field name="domain">[('stage_id.sequence','&lt;=','1')]</field>
    </record>

    <!--Hr Employee inherit search view-->
    <record id="hr_employee_view_search" model="ir.ui.view">
        <field name="name">hr.employee.search.inherit</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='job_id']" position="after">
                <filter name="newly_hired_employee" string="Newly Hired" domain="[('newly_hired_employee', '=', True)]" groups="hr_recruitment.group_hr_recruitment_user"/>
            </xpath>
        </field>
    </record>

    <record id="hr_employee_action_from_department" model="ir.actions.act_window">
        <field name="name">Newly Hired Employees</field>
        <field name="res_model">hr.employee</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="context">{
            'search_default_newly_hired_employee': 1,
            'search_default_department_id': [active_id],
            'default_department_id': active_id}
        </field>
        <field name="search_view_id" ref="hr_employee_view_search"/>
    </record>

    <record id="hr_applicant_view_pivot" model="ir.ui.view">
         <field name="name">hr.applicant.pivot</field>
         <field name="model">hr.applicant</field>
         <field name="arch" type="xml">
             <pivot string="Recruitment Analysis" sample="1">
                 <field name="stage_id" type="row"/>
                 <field name="job_id" type="col"/>
             </pivot>
         </field>
    </record>

    <record id="hr_applicant_view_graph" model="ir.ui.view">
         <field name="name">hr.applicant.graph</field>
         <field name="model">hr.applicant</field>
         <field name="arch" type="xml">
             <graph string="Recruitment Analysis" sample="1">
                 <field name="stage_id"/>
                 <field name="job_id"/>
             </graph>
         </field>
    </record>

    <record id="hr_applicant_view_search" model="ir.ui.view">
        <field name="name">hr.applicant.search</field>
        <field name="model">hr.applicant</field>
        <field name="priority">32</field>
        <field name="arch" type="xml">
            <search string="Recruitment Analysis">
                <field name="job_id"/>
                <field name="department_id" operator="child_of"/>
                <field name="user_id"/>
                <filter string="Creation Date" name="year" date="create_date" default_period="this_year"/>
                <separator/>
                <filter string="Unassigned" name="unassigned" domain="[('user_id', '=', False)]"/>
                <separator/>
                <filter string="New" name="new" domain="[('stage_id.sequence', '=', 1)]"/>
                <separator/>
                <filter string="Ongoing" name="ongoing" domain="[('active', '=', True)]"/>
                <filter string="Refused" name="refused" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Archived" name="archived" domain="[('active', '=', False)]"/>
                <separator/>
                <group expand="0" string="Extended Filters">
                    <field name="priority"/>
                    <field name="stage_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="create_date"/>
                    <field name="date_closed"/>
                </group>
                <group expand="1" string="Group By">
                   <filter string="Responsible" name='User' context="{'group_by':'user_id'}"/>
                   <filter string="Company" name="company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                   <filter string="Jobs" name="job" context="{'group_by':'job_id'}"/>
                   <filter string="Department" name="department" context="{'group_by':'department_id'}"/>
                   <filter string="Stage" name="stage" context="{'group_by':'stage_id'}" />
                   <separator/>
                   <filter string="Creation Date" name="creation_month" context="{'group_by':'create_date:month'}" help="Creation Date"/>
                </group>
            </search>
        </field>
    </record>

    <record id="hr_applicant_action_analysis" model="ir.actions.act_window">
        <field name="name">Recruitment Analysis</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">graph,pivot</field>
        <field name="search_view_id" ref="hr_applicant_view_search"/>
        <field name="view_ids" eval="[
            (5, 0, 0),
            (0, 0, {'view_mode': 'graph', 'view_id': ref('hr_applicant_view_graph')}),
            (0, 0, {'view_mode': 'pivot', 'view_id': ref('hr_applicant_view_pivot')})]"/>
        <field name="context">{'search_default_creation_month': 1, 'search_default_job': 2}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data yet!
            </p>
        </field>
    </record>
    <menuitem name="Reporting" id="report_hr_recruitment" parent="menu_hr_recruitment_root"
    sequence="99"/>

    <menuitem name="Recruitment Analysis" id="hr_applicant_report_menu" parent="report_hr_recruitment"
    sequence="50" action="hr_applicant_action_analysis"/>
    <record id="action_hr_recruitment_report_filtered_department" model="ir.actions.act_window">
        <field name="name">Recruitment Analysis</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">graph,pivot</field>
        <field name="search_view_id" ref="hr_applicant_view_search"/>
        <field name="context">{
            'search_default_department_id': [active_id],
            'default_department_id': active_id}
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data yet!
            </p>
        </field>
    </record>

    <record id="action_hr_recruitment_report_filtered_job" model="ir.actions.act_window">
        <field name="name">Recruitment Analysis</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">graph,pivot</field>
        <field name="search_view_id" ref="hr_applicant_view_search"/>
        <field name="context">{
            'search_default_job_id': [active_id],
            'default_job_id': active_id}
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data yet!
            </p>
        </field>
    </record>

    <!-- Custom reports (aka filters) -->
    <record id="hr_applicant_filter_recruiter" model="ir.filters">
        <field name="name">By Recruiter</field>
        <field name="model_id">hr.applicant</field>
        <field name="user_id" eval="False"/>
        <field name="action_id" ref="hr_applicant_action_analysis"/>
        <field name="context">{'group_by': ['create_date:month', 'user_id']}</field>
    </record>
    <record id="hr_applicant_filter_job" model="ir.filters">
        <field name="name">By Job</field>
        <field name="model_id">hr.applicant</field>
        <field name="user_id" eval="False"/>
        <field name="action_id" ref="hr_applicant_action_analysis"/>
        <field name="context">{'group_by': ['create_date:month', 'job_id']}</field>
    </record>
    <record id="hr_applicant_filter_department" model="ir.filters">
        <field name="name">By Department</field>
        <field name="model_id">hr.applicant</field>
        <field name="user_id" eval="False"/>
        <field name="action_id" ref="hr_applicant_action_analysis"/>
        <field name="context">{'group_by': ['create_date:month', 'department_id']}</field>
    </record>

    </data>
</odoo>
