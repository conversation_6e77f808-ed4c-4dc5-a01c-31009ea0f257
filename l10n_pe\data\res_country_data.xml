<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="pe_partner_address_form" model="ir.ui.view">
        <field name="name">pe.partner.form.address</field>
        <field name="model">res.partner</field>
        <field name="priority" eval="900"/>
        <field name="arch" type="xml">
            <form>
                <div class="o_address_format">
                    <field name="parent_id" invisible="1"/>
                    <field name="type" invisible="1"/>
                    <field name="street" placeholder="Street" class="o_address_street"
                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                    <field name="street2" invisible="1"/>
                    <field name="l10n_pe_district" placeholder="District..." class="o_address_street"/>
                    <field name="city"/>
                    <field name="state_id" class="o_address_state" placeholder="State..." options='{"no_open": True}'
                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                    <field name="zip" placeholder="ZIP" class="o_address_zip"
                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                    <field name="country_id" placeholder="Country" class="o_address_country" options='{"no_open": True, "no_create": True}'
                           attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
                </div>
            </form>
        </field>
    </record>
    <record id="base.pe" model="res.country">
        <field name="enforce_cities" eval="1" />
        <field name="address_view_id" ref="pe_partner_address_form" />
        <field name="address_format" eval="'%(street)s\n%(zip)s%(city)s\n%(state_name)s\n%(country_name)s'"/>
        <field name="street_format" eval="'%(street_name)s %(street_number)s, %(street_number2)s'"/>
    </record>
</odoo>
