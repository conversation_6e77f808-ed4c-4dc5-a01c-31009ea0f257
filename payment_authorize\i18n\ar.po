# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_authorize
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "ABA Routing Number"
msgstr "رقم مسار ABA "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_client_key
msgid "API Client Key"
msgstr "مفتاح عميل API "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_login
msgid "API Login ID"
msgstr "معرف تسجيل دخول API  "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_signature_key
msgid "API Signature Key"
msgstr "مفتاح توقيع API "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_transaction_key
msgid "API Transaction Key"
msgstr "مفتاح معاملة الواجهة البرمجية للتطبيق "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Account Number"
msgstr "رقم الحساب"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_payment_method_type
msgid "Allow Payments From"
msgstr "السماح باستمارة المدفوعات "

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "An error occurred when displayed this payment form."
msgstr "حدث خطأ أثناء عرض استمارة الدفع هذه. "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_currency_id
msgid "Authorize Currency"
msgstr "العملة المصرح بها "

#. module: payment_authorize
#: model:account.payment.method,name:payment_authorize.payment_method_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__provider__authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_payment_method_type
msgid "Authorize.Net Payment Type"
msgstr "نوع دفع Authorize.Net "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.Net Profile ID"
msgstr "معرف الملف التعريفي لـ Authorize.Net "

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "Bank (powered by Authorize)"
msgstr "البنك (مشغل بواسطة Authorize) "

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__authorize_payment_method_type__bank_account
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_token__authorize_payment_method_type__bank_account
msgid "Bank Account (USA Only)"
msgstr "الحساب البنكي (الولايات المتحة الأمريكية) "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Account Type"
msgstr "نوع الحساب البنكي "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Name"
msgstr "اسم البنك"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Business Checking"
msgstr "حساب جاري للأعمال "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Code"
msgstr "رمز البطاقة "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Number"
msgstr "رقم البطاقة"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"Could not fetch merchant details:\n"
"%s"
msgstr ""
"تعذر إحضار بيانات التاجر: \n"
"%s "

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__authorize_payment_method_type__credit_card
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_token__authorize_payment_method_type__credit_card
msgid "Credit Card"
msgstr "البطاقة الائتمانية"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "Credit Card (powered by Authorize)"
msgstr "البطاقة الائتمانية (مشغلة بواسطة Authorize) "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Currency"
msgstr "العملة"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_payment_method_type
msgid "Determines with what payment method the customer can pay."
msgstr "يحدد طرق الدفع التي يمكن أن يستخدمها العميل "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Expiration"
msgstr "تاريخ الانتهاء "

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"Failed to authenticate.\n"
"%s"
msgstr ""
"فشلت المصادقة. \n"
"%s"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Generate Client Key"
msgstr "إنشاء مفتاح العميل "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "How to get paid with Authorize.Net"
msgstr "كيفية تلقي أموال من خلال Authorize.Net"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "MM"
msgstr "MM"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Name On Account"
msgstr "الاسم في الحساب "

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "لم يتم العثور على معاملة تطابق المرجع %s. "

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "بوابة الدفع "

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_account_payment_method
msgid "Payment Methods"
msgstr "طرق الدفع "

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr "رمز الدفع "

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "معاملة الدفع "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Personal Checking"
msgstr "حساب جاري شخصي "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Personal Savings"
msgstr "حساب توفير شخصي "

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__provider
msgid "Provider"
msgstr "المزود"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "Received data with status code \"%(status)s\" and error code \"%(error)s\""
msgstr "البيانات المستلمة مع الحالة \"%(status)s\" وكود الخطأ \"%(error)s\" "

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Received tampered payment request data."
msgstr "بيانات طلب الدفع المستلمة المتلاعب بها. "

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_token.py:0
#, python-format
msgid "Saved payment methods cannot be restored once they have been deleted."
msgstr "لا يمكن استعادة طرق الدفع المحفوظة بمجرد أن يتم حذفها. "

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "خطأ في الخادم "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Set Account Currency"
msgstr "تعيين عملة الحساب "

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_login
msgid "The ID solely used to identify the account with Authorize.Net"
msgstr "المعرف مُستخدم فقط لتعريف الحساب مع Authorize.Net "

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "مقدم خدمة الدفع لاستخدامه مع بوابة الدفع هذه "

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_client_key
msgid ""
"The public client key. To generate directly from Odoo or from Authorize.Net "
"backend."
msgstr ""
"مفتاح العميل العام. للإنشاء مباشرة من أودو أو من الواجهة الخلفية لـ "
"Authorize.Net. "

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "المعاملة غير مرتبطة برمز. "

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_payment_method_type
msgid "The type of payment method this token is linked to."
msgstr "نوع طريقة الدفع التي يرتبط بها هذا الرمز. "

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"The unique reference for the partner/token combination in the Authorize.net "
"backend."
msgstr ""
"المرجع الفريد للمجموع بين الشريك/الرمز في الواجهة الخلفية لـ Authorize.net. "

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"There are active tokens linked to this acquirer. To change the payment "
"method type, please disable the acquirer and duplicate it. Then, change the "
"payment method type on the duplicated acquirer."
msgstr ""
"توجد رموز نشطة مرتبطة ببوابة الدفع هذه. لتغيير نوع طريقة الدفع، يرجى تفعيل "
"بوابة الدفع واستنساخها، ثم تغيير نوع طريقة الدفع في بوابة الدفع المستنسخة. "

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "This action cannot be performed while the acquirer is disabled."
msgstr "لا يمكن تنفيذ هذا الإجراء بينما بوابة الدفع معطلة. "

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr "لم نتمكن من معالجة الدفع الخاص بك. "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "YY"
msgstr "YY"
