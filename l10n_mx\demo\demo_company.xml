<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_mx" model="res.partner">
        <field name="name">MX Company</field>
        <field name="vat">EKU9003173C9</field>
        <field name="street">Campobasso Norte 3206/9000</field>
        <field name="street2">Fraccionamiento Montecarlo</field>
        <field name="zip">85134</field>
        <field name="city">Ciudad Obregón</field>
        <field name="state_id" ref="base.state_mx_son"/>
        <field name="country_id" ref="base.mx"/>
        <field name="phone">+52 ************</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.mxexample.com</field>
    </record>

    <record id="demo_company_mx" model="res.company">
        <field name="name">MX Company</field>
        <field name="partner_id" ref="partner_demo_company_mx"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_mx')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_mx.demo_company_mx'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_mx.mx_coa')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_mx.demo_company_mx')"/>
    </function>
</odoo>
