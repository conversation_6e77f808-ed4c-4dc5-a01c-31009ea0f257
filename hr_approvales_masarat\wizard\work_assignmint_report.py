# -*- coding:utf-8 -*-
from odoo import api, fields, models, _
from datetime import datetime, timedelta
from pytz import timezone
from odoo.exceptions import ValidationError


class ManagerEmployeeRepo(models.TransientModel):
    _name = "hr.masarat.work.assignmint.wizard"

    # employee_id = fields.Many2one('hr.employee', string="الموظف")
    # all_employee = fields.Boolean(string="كل الموظفين")
    date_start = fields.Date(string='تاريخ البدأ')
    date_end = fields.Date(string='تاريخ الانتهاء')

    def get_report_action(self):
        data = {'model': self._name, 'date_from':str(self.date_start), 'date_to':str(self.date_end)}
        return self.sudo().env.ref('hr_approvales_masarat.work_assignmint_report_x1').report_action(self, data=data)


class ManagerEmployeeAlla(models.AbstractModel):
    _name = "report.hr_approvales_masarat.work_assignmint_report_id"

    def _get_report_values(self, docids, data=None):
        all_requists = self.env['hr.masarat.work.assignment'].search([('request_date', '>=', data['date_from']), ('request_date', '<=', data['date_to']),('state', 'in', ('hr_approval', 'manager_approval'))])
        vals = {}
        for elem in all_requists:
            vals.setdefault(str(elem.employee_id.id),{'name':elem.employee_id.name, 'total_count':0, 'total_hours':0})
            vals[str(elem.employee_id.id)]['total_count']+=1
            hours = (elem.end_date_time - elem.start_date_time ).total_seconds()/60/60
            vals[str(elem.employee_id.id)]['total_hours']+=hours
        for line in vals:
            vals[line]['total_hours'] = '{0:02.0f}:{1:02.0f}'.format(*divmod(vals[line]['total_hours']* 60, 60))

        return { 'employees_dict': vals, 'start_date': data['date_from'], 'end_date': data['date_to']}



