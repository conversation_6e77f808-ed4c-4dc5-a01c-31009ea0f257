# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> Jaengsawang <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Odo<PERSON> Thaidev <<EMAIL>>, 2021
# <PERSON>, 2021
# Kris<PERSON>, 2021
# <PERSON><PERSON><PERSON>n Jamwutthipreecha, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__state
msgid ""
" * Draft: The MO is not confirmed yet.\n"
" * Confirmed: The MO is confirmed, the stock rules and the reordering of the components are trigerred.\n"
" * In Progress: The production has started (on the MO or on the WO).\n"
" * To Close: The production is done, the MO has to be closed.\n"
" * Done: The MO is closed, the stock moves are posted. \n"
" * Cancelled: The MO has been cancelled, can't be confirmed anymore."
msgstr ""
"* ร่าง: MO ยังไม่ได้รับการยืนยัน\n"
"* ยืนยันแล้ว: MO ได้รับการยืนยัน กฎสต๊อกและการเติมสต๊อกส่วนประกอบจะถูกเปิดใช้\n"
"* กำลังดำเนินการ: การผลิตได้เริ่มต้นขึ้นแล้ว (ใน MO หรือ WO)\n"
"* เพื่อปิด: การผลิตเสร็จสิ้น MO จะต้องปิด\n"
"* เสร็จสิ้น: MO ถูกปิด มีการบันทึกการย้ายสต๊อก \n"
"* ยกเลิก: MO ถูกยกเลิก ไม่สามารถยืนยันได้อีกต่อไป"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid " <br/><br/> The components will be taken from <b>%s</b>."
msgstr " <br/><br/> ส่วนประกอบจะถูกนำมาจาก<b>%s</b>"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__all_available
msgid " When all components are available"
msgstr "เมื่อทุกส่วนประกอบพร้อมใช้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_count
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_count
msgid "# Bill of Material"
msgstr "# บิลวัสดุุ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__used_in_bom_count
msgid "# BoM Where Used"
msgstr "#BOM ที่ใช้แล้ว "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_ready_count
msgid "# Read Work Orders"
msgstr "# อ่านคำสั่งงาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_count
msgid "# Work Orders"
msgstr "# คำสั่งงาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_template__used_in_bom_count
msgid "# of BoM Where is Used"
msgstr "# ของ BoM ที่ถูกใช้"

#. module: mrp
#: code:addons/mrp/models/mrp_routing.py:0
#, python-format
msgid "%i work orders"
msgstr "%iคำสั่งงาน "

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "%s %s unbuilt in"
msgstr "%s %s ยกเลิกการสร้างใน"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "%s (new) %s"
msgstr "%s (ใหม่) %s"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "%s Child MO's"
msgstr "%s ของ MO ย่อย"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "%s cannot be deleted. Try to cancel them before."
msgstr "%s ไม่สามารถลบได้ ลองยกเลิกก่อนหน้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "&amp; Cost"
msgstr "และต้นทุน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "&gt;"
msgstr "&gt;"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_bom_structure
msgid "'Bom Structure - %s' % object.display_name"
msgstr "'โครงสร้าง Bom - %s' % object.display_name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_finished_product
msgid "'Finished products - %s' % object.name"
msgstr "'สินค้าสำเร็จรูป - %s' % object.name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_production_order
msgid "'Production Order - %s' % object.name"
msgstr "'ใบสั่งผลิต - %s' % object.name"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr "+ %d วัน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
" \n"
"             อาจจำเป็นต้องดำเนินการด้วยตัวเอง"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_leg
#: model_terms:product.template,description:mrp.product_product_computer_desk_leg_product_template
msgid "18″ x 2½″ Square Leg"
msgstr "18″ x 2½″ ขาเหลี่ยม"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ": Insufficient Quantity To Unbuild"
msgstr ":ปริมาณไม่เพียงพอที่จะรื้อ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"จัดการ\" title=\"จัดการ\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"
msgstr "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"หยุด\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-play\" role=\"img\" aria-label=\"Run\" title=\"Run\"/>"
msgstr "<i class=\"fa fa-play\" role=\"img\" aria-label=\"ดำเนินการ\" title=\"Run\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"
msgstr "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"หยุด\" title=\"หยุด\"/>"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your product\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                       อัปโหลดไฟล์ไปยังสินค้าของคุณ\n"
"                    </p><p>\n"
"                       ใช้ฟีเจอร์นี้เพื่อจัดเก็บไฟล์ใดๆ เช่น ภาพวาดหรือข้อมูลจำเพาะ\n"
"                    </p>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะบริษัท\" "
"groups=\"base.group_multi_company\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"font-weight-bold\">To Produce</span>"
msgstr "<span class=\"font-weight-bold\">ผลิต</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Backorders</span>"
msgstr "<span class=\"o_stat_text\">คำสั่งล่วงหน้า</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Child MO</span>"
msgstr "<span class=\"o_stat_text\">MO ย่อย</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Load</span>"
msgstr "<span class=\"o_stat_text\">โหลด</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Lost</span>"
msgstr "<span class=\"o_stat_text\">สูญเสีย</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "<span class=\"o_stat_text\">Manufactured</span>"
msgstr "<span class=\"o_stat_text\">การผลิต</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">OEE</span>"
msgstr "<span class=\"o_stat_text\">OEE</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Performance</span>"
msgstr "<span class=\"o_stat_text\">ประสิทธิภาพ</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">Routing<br/>Performance</span>"
msgstr "<span class=\"o_stat_text\">เส้นทาง<br/>ประสิทธิภาพ</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span class=\"o_stat_text\">Scraps</span>"
msgstr "<span class=\"o_stat_text\">เศษซากผลิตภัณฑ์</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Source MO</span>"
msgstr "<span class=\"o_stat_text\">แหล่งที่มา MO</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "<span><strong>Unit Cost</strong></span>"
msgstr "<span><strong>หน่วยต้นทุน</strong></span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Actions</span>"
msgstr "<span>การดำเนินการ</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "<span>Generate</span>"
msgstr "<span>สร้าง</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_operation_line
msgid "<span>Minutes</span>"
msgstr "<span>นาที</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>New</span>"
msgstr "<span>ใหม่</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>Orders</span>"
msgstr "<span>คำสั่ง</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>PLAN ORDERS</span>"
msgstr "<span>แผนคำสั่ง</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_report_delivery_no_kit_section
msgid "<span>Products not associated with a kit</span>"
msgstr "<span>สินค้าที่ไม่เกี่ยวข้องกับชุดอุปกรณ์</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Reporting</span>"
msgstr "<span>รายงาน</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>WORK ORDERS</span>"
msgstr "<span>คำสั่งงาน</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>minutes</span>"
msgstr "<span>นาที</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<strong class=\"mr8 oe_inline\">to</strong>"
msgstr "<strong class=\"mr8 oe_inline\">ถึง</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Description:</strong><br/>"
msgstr "<strong>คำอธิบาย:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Effectiveness Category: </strong>"
msgstr "<strong>หมวดหมู่ประสิทธิผล:</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Finished Product:</strong><br/>"
msgstr "<strong>สินค้าสำเร็จรูป:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Is a Blocking Reason? </strong>"
msgstr "<strong>เป็นเหตุผลในการบล็อกหรือไม่?</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>No. Of Minutes</strong>"
msgstr "<strong>จำนวนนาที</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Operation</strong>"
msgstr "<strong>ปฏิบัติการ</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity to Produce:</strong><br/>"
msgstr "<strong>จำนวนที่จะผลิต:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Reason: </strong>"
msgstr "<strong>เหตุผล: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Responsible:</strong><br/>"
msgstr "<strong>รับผิดชอบ:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source Document:</strong><br/>"
msgstr "<strong>เอกสารต้นทาง:</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Start Date: </strong>"
msgstr "<strong>วันที่เริ่ม: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Stop Date: </strong>"
msgstr "<strong>วันที่หยุด: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>WorkCenter</strong>"
msgstr "<strong>ศูนย์งาน</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
msgid "<strong>Workcenter: </strong>"
msgstr "<strong>ศูนย์งาน: </strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? ซึ่งอาจนำไปสู่ความไม่สอดคล้องกันในคลังสินค้าของคุณ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "A BoM of type kit is used to split the product into its components."
msgstr "ประเภทอุปกรณ์ BoM ใช้เพื่อแยกสินค้าออกเป็นส่วนประกอบ"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "A Manufacturing Order is already done or cancelled."
msgstr "ใบสั่งผลิตเสร็จสิ้นหรือยกเลิกแล้ว"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid ""
"A product with a kit-type bill of materials can not have a reordering rule."
msgstr ""
"สินค้าที่มีรายการวัสดุประเภทชุดอุปกรณ์ไม่สามารถมีกฎการเติมสต๊อกโดยอัตโนมัติ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__access_token
msgid "Access Token"
msgstr "เข้าถึงโทเคน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_rule__action
msgid "Action"
msgstr "การดำเนินการ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__active
#: model:ir.model.fields,field_description:mrp.field_mrp_document__active
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__active
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Add a description..."
msgstr "เพิ่มคำอธิบาย"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Add by-products to bills of materials. This can be used to get several "
"finished products as well. Without this option you only do: A + B = C. With "
"the option: A + B = C + D."
msgstr ""
"เพิ่มผลิตภัณฑ์พลอยได้ในรายการวัสดุ "
"สามารถใช้เพื่อให้ได้สินค้าสำเร็จรูปหลายอย่างเช่นกัน หากไม่มีตัวเลือกนี้ "
"คุณจะทำได้เพียง: A + B = C ด้วยตัวเลือก: A + B = C + D"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Add quality checks to your work orders"
msgstr "เพิ่มการตรวจสอบคุณภาพให้กับคำสั่งงานของคุณ"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_tag
msgid "Add tag for the workcenter"
msgstr "เพิ่มแท็กสำหรับศูนย์งาน"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_manager
msgid "Administrator"
msgstr "ผู้ดูแลระบบ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "All"
msgstr "ทั้งหมด"

#. module: mrp
#: code:addons/mrp/controller/main.py:0
#, python-format
msgid "All files uploaded"
msgstr "อัปโหลดไฟล์ทั้งหมด"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_line_bom_qty_zero
msgid ""
"All product quantities must be greater or equal to 0.\n"
"Lines with 0 quantities can be used as optional lines. \n"
"You should install the mrp_byproduct module if you want to manage extra products on BoMs !"
msgstr ""
"จำนวนสินค้าทั้งหมดต้องมากกว่าหรือเท่ากับ 0\n"
"ไลน์ที่มีจำนวน 0 สามารถใช้เป็นไลน์เสริม\n"
"คุณควรติดตั้งโมดูล mrp_byproduct หากคุณต้องการจัดการผลิตสินค้าเพิ่มเติมบน BoMs !"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Allow manufacturing users to modify quantities to consume, without the need "
"for prior approval"
msgstr ""
"อนุญาตให้ผู้ใช้ในการผลิตปรับเปลี่ยนปริมาณเพื่อบริโภคโดยไม่ต้องขออนุมัติล่วงหน้า"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,help:mrp.field_stock_picking_type__use_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr "อนุญาตให้สร้างล็อต/หมายเลขซีเรียลใหม่สำหรับส่วนประกอบ"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__flexible
msgid "Allowed"
msgstr "อนุญาต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reserve_visible
msgid "Allowed to Reserve Production"
msgstr "อนุญาตให้สำรองการผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr "ได้รับอนุญาตให้ไม่สำรองการผลิต"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__warning
msgid "Allowed with warning"
msgstr "อนุญาตและมีคำเตือน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid "Alternative Workcenters"
msgstr "ศูนย์งานทางเลือก"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid ""
"Alternative workcenters that can be substituted to this one in order to "
"dispatch production"
msgstr "ศูนย์งานทางเลือกที่สามารถทดแทนศูนย์นี้เพื่อจัดส่งการผลิต"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid ""
"An unbuild order is used to break down a finished product into its "
"components."
msgstr "คำสั่งรื้อใช้เพื่อแยกสินค้าสำเร็จรูปออกเป็นส่วนประกอบ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Apply"
msgstr "ใช้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "Apply on Variants"
msgstr "ใช้ในตัวแปร"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Approve"
msgstr "อนุมัติ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_assign_serial_numbers_production
msgid "Assign Serial Numbers"
msgstr "กำหนดหมายเลขซีเรียล"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "At the creation of a Manufacturing Order."
msgstr "ที่การสร้างใบสั่งผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "At the creation of a Stock Transfer."
msgstr "เมื่อสร้างการโอนสต๊อก"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "Attached To"
msgstr "แนบกับ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_attachment_count
msgid "Attachment Count"
msgstr "จํานวนสิ่งที่แนบมา"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__local_url
msgid "Attachment URL"
msgstr "แนบไฟล์ URL"

#. module: mrp
#. openerp-web
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/static/src/js/mrp_bom_report.js:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
#, python-format
msgid "Attachments"
msgstr "แนบ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__attachments_count
msgid "Attachments Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__availability
msgid "Availability"
msgstr "ความพร้อม"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Availability Losses"
msgstr "การสูญเสียความพร้อม"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__available
#, python-format
msgid "Available"
msgstr "พร้อมใช้"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "Avatar"
msgstr "อวตาร"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_product_product__produce_delay
#: model:ir.model.fields,help:mrp.field_product_template__produce_delay
msgid ""
"Average lead time in days to manufacture this product. In the case of multi-"
"level BOM, the manufacturing lead times of the components will be added."
msgstr ""
"ระยะเวลานำการผลิตโดยเฉลี่ยเป็นวันในการผลิตสินค้านี้ ในกรณีของ BOM "
"แบบหลายระดับ เวลานำการผลิตของส่วนประกอบจะถูกเพิ่มเข้าไป"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__variant_bom_ids
msgid "BOM Product Variants"
msgstr "BOM ตัวแปรสินค้า"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "BOM Product Variants needed to apply this line."
msgstr "ตัวแปรสินค้า BOM จำเป็นต้องใช้ไลน์นี้"

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_bom_structure
msgid "BOM Structure Report"
msgstr "รายงานโครงสร้าง BOM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_line_ids
msgid "BOM lines of the referred bom"
msgstr "ไลน์ BOM ของ bom ที่อ้างอิง"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder_line
msgid "Backorder Confirmation Line"
msgstr "ไลน์ยืนยันคำสั่งซื้อล่วงหน้า"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_backorder_line_ids
msgid "Backorder Confirmation Lines"
msgstr "ไลน์ยืนยันคำสั่งซื้อล่วงหน้า"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Backorder MO"
msgstr "คำสั่งซื้อล่วงหน้า MO"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Backorder MO's"
msgstr "คำสั่งซื้อล่วงหน้าของ MO"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__backorder_sequence
msgid "Backorder Sequence"
msgstr "ลำดับคำสั่งซื้อล่วงหน้า"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__backorder_sequence
msgid ""
"Backorder sequence, if equals to 0 means there is not related backorder"
msgstr ""
"ลำดับคำสั่งซื้อล่วงหน้า ถ้าเท่ากับ 0 "
"หมายความว่าไม่มีสินค้าค้างส่งที่เกี่ยวข้อง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Barcode"
msgstr "บาร์โค้ด"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode_batch
msgid "Based on"
msgstr "อิงตาม"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom
#: model:ir.model.fields,field_description:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Bill of Material"
msgstr "บิลวัสดุ"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "ไลน์บิลวัสดุ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
msgid "Bill of Material line"
msgstr "ไลน์บิลวัสดุ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_bom_id
msgid "Bill of Material used on the Production Order"
msgstr "บิลวัสดุที่ใช้กับใบสั่งผลิต"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_open_bom
#: model:ir.actions.act_window,name:mrp.template_open_bom
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_ids
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Bill of Materials"
msgstr "บิลวัสดุ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__mo_bom_id
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_bom_id
msgid ""
"Bill of Materials allow you to define the list of required components to "
"make a finished product."
msgstr ""
"บิสวัสดุช่วยให้คุณสามารถกำหนดรายการส่วนประกอบที่จำเป็นเพื่อสร้างสินค้าสำเร็จรูปได้"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_bom_form_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom_form_action
msgid "Bills of Materials"
msgstr "บิลวัสดุ"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Bills of materials allow you to define the list of required raw\n"
"                materials used to make a finished product; through a manufacturing\n"
"                order or a pack of products."
msgstr ""
"บิลวัสดุช่วยให้คุณสามารถกำหนดรายการวัตถุดิบที่ต้องการได้\n"
"               วัสดุที่ใช้ทำสินค้าสำเร็จรูป ผ่านใบสั่งผลิต\n"
"                หรือแพ็คสินค้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block"
msgstr "บล็อก"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter_wo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block Workcenter"
msgstr "บล็อกศูนย์งาน"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__blocked
msgid "Blocked"
msgstr "บล็อก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked Time"
msgstr "บล็อกเวลาแล้ว"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked hours over the last month"
msgstr "ชั่วโมงที่ถูกบล็อกในช่วงเดือนที่ผ่านมา"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__name
msgid "Blocking Reason"
msgstr "สาเหตุการบล็อก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM"
msgstr "BoM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_line_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_line_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "BoM Components"
msgstr "ส่วนประกอบ BoM "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Cost"
msgstr "ต้นทุน BoM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__bom_line_id
msgid "BoM Line"
msgstr "ไลน์ BoM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__bom_line_ids
msgid "BoM Lines"
msgstr "ไลน์ BoM"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#: model:ir.actions.report,name:mrp.action_report_bom_structure
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#, python-format
msgid "BoM Structure"
msgstr "โครงสร้าง BoM"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#: model:ir.actions.client,name:mrp.action_report_mrp_bom
#, python-format
msgid "BoM Structure & Cost"
msgstr "โครงสร้าง BoM และต้นทุน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__type
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "BoM Type"
msgstr "ประเภท BoM"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "BoM line product %s should not be the same as BoM product."
msgstr "ไลน์สินค้า BoM %sไม่ควรเป็นสินค้า BoM เดียวกัน"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_bolt
#: model:product.template,name:mrp.product_product_computer_desk_bolt_product_template
msgid "Bolt"
msgstr "โบลท์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
msgid "By-Products"
msgstr "ผลิตภัณฑ์พลอยได้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_id
msgid "By-product"
msgstr "ผลิตภัณฑ์พลอยได้"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "By-product %s should not be the same as BoM product."
msgstr "ผลิตภัณฑ์พลอยได้ %s ไม่ควรเป็นสินค้า BoM เดียวกัน"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__byproduct_id
msgid "By-product line that generated the move in a manufacturing order"
msgstr "ไลน์ผลิตภัณฑ์พลอยได้ที่สร้างการเคลื่อนไหวในใบสั่งผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__byproduct_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__byproduct_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "By-products"
msgstr "ผลิตภัณฑ์พลอยได้"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "By-products cost shares must be positive."
msgstr "ส่วนแบ่งต้นทุนผลิตภัณฑ์พลอยได้ต้องเป็นค่าบวก"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_byproduct
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_byproduct_form_view
msgid "Byproduct"
msgstr "ผลิตภัณฑ์พลอยได้"

#. module: mrp
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#, python-format
msgid "Byproducts"
msgstr "ผลิตภัณฑ์พลอยได้"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any production location."
msgstr "ไม่พบสถานที่ผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Cancel"
msgstr "ยกเลิก"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__cancel
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__cancel
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Cancelled"
msgstr "ยกเลิกแล้ว"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Cannot delete a manufacturing order in done state."
msgstr "ไม่สามารถลบใบสั่งผลิตในสถานะเสร็จสิ้นได้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__capacity
msgid "Capacity"
msgstr "ความจุ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__loss_type
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Category"
msgstr "หมวดหมู่"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Change Product Qty"
msgstr "เปลี่ยนจำนวนสินค้า"

#. module: mrp
#: model:ir.model,name:mrp.model_change_production_qty
msgid "Change Production Qty"
msgstr "เปลี่ยนจำนวนการผลิต"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_change_production_qty
msgid "Change Quantity To Produce"
msgstr "เปลี่ยนจำนวนที่จะผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Check availability"
msgstr "ตรวจสอบความพร้อม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__checksum
msgid "Checksum/SHA1"
msgstr "ตรวจสอบ/SHA1"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_stop
msgid "Cleanup Time"
msgstr "เวลาการทำความสะอาด"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__code
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view_kanban
msgid "Code"
msgstr "โค้ด"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__color
msgid "Color"
msgstr "สี"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__color
msgid "Color Index"
msgstr "ดัชนีสี"

#. module: mrp
#: model:ir.model,name:mrp.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_document__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__company_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Company"
msgstr "บริษัท"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Component"
msgstr "ส่วนประกอบ"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Component Lots must be unique for mass production. Please review reservation"
" for:\n"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability
msgid "Component Status"
msgstr "สถานะส่วนประกอบ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Component of Draft MO"
msgstr "ส่วนประกอบของร่าง MO"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_raw_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Components"
msgstr "ส่วนประกอบ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability_state
msgid "Components Availability State"
msgstr "สถานะความพร้อมส่วนประกอบ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_src_id
msgid "Components Location"
msgstr "ตำแหน่งของส่วนประกอบ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__priority
msgid ""
"Components will be reserved first for the MO with the highest priorities."
msgstr "ส่วนประกอบจะถูกสำรองไว้ก่อนสำหรับ MO ที่มีลำดับความสำคัญสูงสุด"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__auto
msgid "Compute based on tracked time"
msgstr "คำนวณตามเวลาที่ติดตาม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_computed_on
msgid "Computed on last"
msgstr "คำนวณรายการสุดท้ายของ"

#. module: mrp
#: model:ir.model,name:mrp.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_configuration
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Confirm"
msgstr "ยืนยัน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__confirm_cancel
msgid "Confirm Cancel"
msgstr "ยืนยันการยกเลิก"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Confirmed"
msgstr "ยืนยันแล้ว"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "Consume"
msgstr "บริโภค"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_consumed_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Consumed"
msgstr "บริโภคแล้ว"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__consume_line_ids
msgid "Consumed Disassembly Lines"
msgstr "ไลน์การถอดชิ้นส่วนที่ใช้แล้ว"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__consume_unbuild_id
msgid "Consumed Disassembly Order"
msgstr "คำสั่งถอดแยกชิ้นส่วนที่ใช้แล้ว"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Consumed Products"
msgstr "สินค้าที่ใช้ไป"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__operation_id
msgid "Consumed in Operation"
msgstr "ใช้ในการปฏิบัติการ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_production__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__consumption
msgid "Consumption"
msgstr "การบริโภค"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_consumption_warning
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Consumption Warning"
msgstr "คำเตือนการบริโภค"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_production__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"การแปลงระหว่างหน่วยวัดจะเกิดขึ้นได้ก็ต่อเมื่ออยู่ในหมวดหมู่เดียวกัน "
"การแปลงจะทำตามอัตราส่วน"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_field_one2many_with_copy.js:0
#, python-format
msgid "Copy Existing Operations"
msgstr "คัดลอกปฏิบัติการที่มีอยู่"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_copy_to_bom_tree_view
msgid "Copy selected operations"
msgstr "คัดลอกการปฏิบัติการที่เลือก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__cost_share
#: model:ir.model.fields,field_description:mrp.field_stock_move__cost_share
msgid "Cost Share (%)"
msgstr "ส่วนแบ่งต้นทุน (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__costs_hour
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__costs_hour
msgid "Cost per hour"
msgstr "ต้นทุนต่อชั่วโมง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Costing Information"
msgstr "ข้อมูลการคิดต้นทุน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_backorder_count
msgid "Count of linked backorder"
msgstr "จำนวนคำสั่งล่วงหน้าที่เชื่อมโยง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Create Backorder"
msgstr "สร้างคำสั่งล่วงหน้า"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__use_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr "สร้างล็อต/หมายเลขซีเรียลใหม่สำหรับส่วนประกอบ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create a Backorder"
msgstr "สร้างคำสั่งล่วงหน้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid ""
"Create a backorder if you expect to process the remaining products later. Do"
" not create a backorder if you will not process the remaining products."
msgstr ""
"สร้างคำสั่งล่วงหน้าถ้าคุณคาดว่าจะดำเนินการกับสินค้าที่เหลืออยู่ในภายหลัง "
"อย่าสร้างคำสั่งล่วงหน้าหากคุณจะไม่ดำเนินการกับสินค้าที่เหลืออยู่"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_report
msgid "Create a new manufacturing order"
msgstr "สร้างใบสั่งผลิตใหม่"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid "Create a new operation"
msgstr "สร้างปฏิบัติการใหม่"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid "Create a new work center"
msgstr "สร้างศูนย์งานใหม่"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_workcenter_report
msgid "Create a new work orders performance"
msgstr "สร้างประสิทธิภาพคำสั่งงานใหม่"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create backorder"
msgstr "สร้างคำสั่งล่วงหน้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "สร้างแผ่นงานที่ปรับแต่งได้สำหรับการตรวจสอบคุณภาพของคุณ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__created_production_id
msgid "Created Production Order"
msgstr "สร้างใบสั่งผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Creates a new serial/lot number"
msgstr "สร้างหมายเลขซีเรียล/ล็อตใหม่"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "Creation"
msgstr "การสร้าง"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"จำนวนสินค้าในปัจจุบัน\n"
"ในบริบทที่มีตำแหน่งสต๊อกแห่งเดียว ซึ่งรวมถึงสินค้าที่จัดเก็บไว้ที่ตำแหน่งนั้นด้วย หรือรายการย่อยอื่น ๆ                                                                                                                                                                                                                                                                                                                                                                                                                                         \n"
"ในบริบทของโกดังแห่งเดียว ซึ่งรวมถึงสินค้าที่จัดเก็บไว้ในตำแหน่งสต๊อกของโกดังสินค้านี้ หรือรายการย่อยอื่น ๆ \n"
"ที่จัดเก็บไว้ที่ตำแหน่งสต๊อกโกดังสินค้าของร้านค้านี้หรือรายการย่อยอื่น ๆ \n"
"มิเช่นนั้น จะรวมถึงรายการสินค้าที่จัดเก็บไว้ในตำแหน่งสต๊อกใด ๆ ที่มีประเภท 'ภายใน'"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_producing
msgid "Currently Produced Quantity"
msgstr "จำนวนที่ผลิตในปัจจุบัน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_description_variants
msgid "Custom Description"
msgstr "คำอธิบายที่กำหนดเอง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__db_datas
msgid "Database Data"
msgstr "ฐานข้อมูล"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Date"
msgstr "วันที่"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_planned_finished
msgid "Date at which you plan to finish the production."
msgstr "วันที่คุณวางแผนจะสิ้นสุดการผลิต"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_planned_start
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_date
msgid "Date at which you plan to start the production."
msgstr "วันที่คุณวางแผนที่จะเริ่มการผลิต"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_start
msgid "Date of the WO"
msgstr "วันที่ของ WO"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_finished
msgid "Date when the MO has been close"
msgstr "วันที่ MO ปิด"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_deadline
msgid "Deadline"
msgstr "วันครบกำหนด"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Default Duration"
msgstr "ระยะเวลาเริ่มต้น"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr "เวลานำการผลิตเริ่มต้น"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_consumption_warning_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "หน่วยวัดเริ่มต้นที่ใช้สำหรับการปฏิบัติการสต๊อกทั้งหมด"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid ""
"Define the components and finished products you wish to use in\n"
"                bill of materials and manufacturing orders."
msgstr ""
"กำหนดส่วนประกอบและสินค้าสำเร็จรูปที่คุณต้องการใช้ใน\n"
"              บิลวัสดุและใบสั่งผลิต"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Define the schedule of resource"
msgstr "ระบุการกำหนดเวลาของทรัพยากร"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__consumption
msgid ""
"Defines if you can consume more or less components than the quantity defined on the BoM:\n"
"  * Allowed: allowed for all manufacturing users.\n"
"  * Allowed with warning: allowed for all manufacturing users with summary of consumption differences when closing the manufacturing order.\n"
"  * Blocked: only a manager can close a manufacturing order when the BoM consumption is not respected."
msgstr ""
"กำหนดว่าคุณสามารถใช้ส่วนประกอบมากหรือน้อยกว่าจำนวนที่กำหนดไว้ใน BoM:\n"
"  * อนุญาต: อนุญาตสำหรับผู้ใช้การผลิตทั้งหมด\n"
"  * อนุญาตโดยมีคำเตือน: อนุญาตสำหรับผู้ใช้การผลิตทุกคนโดยสรุปความแตกต่างของการใช้เมื่อปิดใบสั่งผลิต\n"
"* ถูกบล็อก: มีเพียงผู้จัดการเท่านั้นที่สามารถปิดใบสั่งผลิตเมื่อไม่ปฏิบัติตามจำนวนการใช้ BoM"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_type
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_type
msgid "Defines if you want to use a PDF or a Google Slide as work sheet."
msgstr "กำหนดว่าคุณต้องการใช้ PDF หรือ Google สไลด์เป็นแผ่นงาน"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__ready_to_produce
msgid ""
"Defines when a Manufacturing Order is considered as ready to be started"
msgstr "กำหนดว่าเมื่อใดที่ใบสั่งผลิตจะถูกพิจารณาว่าพร้อมที่จะเริ่มต้น"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delay_alert_date
msgid "Delay Alert Date"
msgstr "วันที่แจ้งเตือนล่าช้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Delete"
msgstr "ลบ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delivery_count
msgid "Delivery Orders"
msgstr "คำสั่งจัดส่ง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__description
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__description
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_note
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description"
msgstr "คำอธิบาย"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__note
msgid "Description of the Work Center."
msgstr "คำอธิบายของศูนย์งาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description of the work center..."
msgstr "คำอธิบายของศูนย์งาน..."

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_dest_id
msgid "Destination Location"
msgstr "ตำแหน่งปลายทาง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unbuild_id
msgid "Disassembly Order"
msgstr "คำสั่งถอดแยกชิ้นส่วน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Discard"
msgstr "ละทิ้ง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_document__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__display_name
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_serial_mass_produce
msgid "Display the serial mass product wizard action moves"
msgstr "แสดงตัวช่วยการดำเนินการเคลื่อนซีเรียลสินค้าจำนวนมาก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lot_ids
msgid "Display the serial number shortcut on the moves"
msgstr "แสดงทางลัดหมายเลขซีเรียลที่อยู่ระหว่างคลื่อนย้าย"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "Do you confirm you want to unbuild"
msgstr "คุณยืนยันหรือไม่ว่าต้องการรื้อ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Document"
msgstr "เอกสาร"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Documentation"
msgstr "เอกสารกำกับ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__is_done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Download"
msgstr "ดาวน์โหลด"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__draft
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__draft
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Draft"
msgstr "ร่าง"

#. module: mrp
#: model:product.product,name:mrp.product_product_drawer_drawer
#: model:product.template,name:mrp.product_product_drawer_drawer_product_template
msgid "Drawer Black"
msgstr "ลิ้นชัก สีดำ"

#. module: mrp
#: model:product.product,name:mrp.product_product_drawer_case
#: model:product.template,name:mrp.product_product_drawer_case_product_template
msgid "Drawer Case Black"
msgstr "ตู้ลิ้นชัก สีดำ"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_drawer_drawer
#: model_terms:product.template,description:mrp.product_product_drawer_drawer_product_template
msgid "Drawer on casters for great usability."
msgstr "ลิ้นชักบนล้อเลื่อนเพื่อการใช้งานที่ยอดเยี่ยม"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Dropdown menu"
msgstr "เมนูแบบดรอปดาว์น"

#. module: mrp
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "Duplicate Serial Numbers (%s)"
msgstr "หมายเลขซีเรียลที่ซ้ำกัน (%s)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__duration
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Duration"
msgstr "ระยะเวลา"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Duration (minutes)"
msgstr "ระยะเวลา (นาที)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode
msgid "Duration Computation"
msgstr "การคำนวณระยะเวลา"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_percent
msgid "Duration Deviation (%)"
msgstr "ค่าเบี่ยงเบนระยะเวลา (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_unit
msgid "Duration Per Unit"
msgstr "ระยะเวลาต่อหน่วย"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Edit"
msgstr "แก้ไข"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_type
msgid "Effectiveness"
msgstr "ประสิทธิผล"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_type
msgid "Effectiveness Category"
msgstr "หมวดหมู่ประสิทธิผล"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_end
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_finished
msgid "End Date"
msgstr "วันสิ้นสุด"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,help:mrp.field_mrp_workorder__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "แน่ใจว่ามีการตรวจสอบย้อนกลับของสินค้าที่จัดเก็บได้ในคลังสินค้าของคุณ"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_documents_controller_mixin.js:0
#, python-format
msgid "Error"
msgstr "ผิดพลาด"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s) occurred on the manufacturing order(s):"
msgstr "ข้อยกเว้นที่เกิดขึ้นในใบสั่งผลิต:"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s):"
msgstr "ข้อยกเว้น:"

#. module: mrp
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "Existing Serial Numbers (%s)"
msgstr "หมายเลขซีเรียลที่มีอยู่ (%s)"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Exp %s"
msgstr "หมดอายุ %s"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__expected
msgid "Expected"
msgstr "ควาดหวัง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_duration_expected
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_expected
msgid "Expected Duration"
msgstr "ระยะเวลาที่คาดหวัง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Expected Duration (minutes)"
msgstr "ระยะเวลาที่คาดหวัง (นาที)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__expected_qty
msgid "Expected Quantity"
msgstr "จำนวนที่คาดหวัง"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__duration_expected
msgid "Expected duration (in minutes)"
msgstr "ระยะเวลาที่คาดหวัง (เป็นนาที)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__datas
msgid "File Content (base64)"
msgstr "เนื้อหาไฟล์ (base64)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__raw
msgid "File Content (raw)"
msgstr "เนื้อหาไฟล์ (raw)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__file_size
msgid "File Size"
msgstr "ขนาดไฟล์"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Files attached to the product"
msgstr "ไฟล์ที่แนบมากับสินค้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Filters"
msgstr "ตัวกรอง"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__done
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Finished"
msgstr "เสร็จสิ้นแล้ว"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__order_finished_lot_ids
msgid "Finished Lot/Serial Number"
msgstr "ล็อตที่เสร็จสิ้น/หมายเลขซีเรียล"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_finished_ids
msgid "Finished Moves"
msgstr "การย้ายเสร็จสิ้น"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__finished_move_line_ids
msgid "Finished Product"
msgstr "สินค้าสำเร็จรูป"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_finished_product
msgid "Finished Product Label (PDF)"
msgstr "ฉลากสินค้าสำเร็จรูป (PDF)"

#. module: mrp
#: model:ir.actions.report,name:mrp.label_manufacture_template
msgid "Finished Product Label (ZPL)"
msgstr "ฉลากสินค้าสำเร็จรูป  (ZPL)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_finished_ids
msgid "Finished Products"
msgstr "สินค้าสำเร็จรูป"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_dest_id
msgid "Finished Products Location"
msgstr "ตำแหน่งของสินค้าสำเร็จรูป"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__consumption
msgid "Flexible Consumption"
msgstr "การบริโภคที่ยืดหยุ่น"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบอักษรที่ยอดเยี่ยมเช่น fa-tasks"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Force"
msgstr "ผลักดัน"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"จำนวนที่คาดการณ์ (คำนวณเป็นจำนวนในมือ - ขาออก + ขาเข้า)\n"
"ในบริบทที่มีสถานที่เก็บสินค้าแห่งเดียว ซึ่งรวมถึงสินค้าที่จัดเก็บไว้ในสถานที่นี้ หรือสถานที่ย่อยอื่นๆ\n"
"ในบริบทของคลังสินค้าเดียวเดียว ซึ่งรวมถึงสินค้าที่จัดเก็บไว้ในสถานที่เก็บสินค้าของคลังสินค้านี้ หรือสถานที่ย่อยอื่น ๆ \n"
"มิเช่นนั้น จะรวมถึงสินค้าที่จัดเก็บไว้ในสถานที่เก็บสินค้าที่มีประเภท 'ภายใน'"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Forecasted"
msgstr "พยากรณ์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__forecasted_issue
msgid "Forecasted Issue"
msgstr "ปัญหาที่คาดการณ์"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "From"
msgstr "จาก"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Fully Productive"
msgstr "ประสิทธิผลอย่างเต็มที่"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Future Activities"
msgstr "กิจกรรมในอนาคต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "General Information"
msgstr "ข้อมูลทั่วไป"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Generate Serial Numbers"
msgstr "สร้างหมายเลขซีเรียล"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
msgid "Get statistics about the work orders duration related to this routing."
msgstr ""
"รับสถิติเกี่ยวกับระยะเวลาของคำสั่งงานที่เกี่ยวข้องกับการกำหนดเส้นทางนี้"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__priority
msgid "Gives the sequence order when displaying a list of MRP documents."
msgstr "ให้ลำดับคำสั่งเมื่อแสดงรายการเอกสาร MRP"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__sequence
msgid "Gives the sequence order when displaying a list of bills of material."
msgstr "ให้ลำดับคำสั่งเมื่อแสดงรายการของบิลวัสดุ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__sequence
msgid ""
"Gives the sequence order when displaying a list of routing Work Centers."
msgstr "ให้ลำดับคำสั่งเมื่อแสดงรายการการกำหนดเส้นทางของศูนย์งาน"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__sequence
msgid "Gives the sequence order when displaying a list of work centers."
msgstr "ให้ลำดับคำสั่งเมื่อแสดงรายการศูนย์งาน"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__sequence
msgid "Gives the sequence order when displaying."
msgstr "ให้ลำดับคำสั่งเมื่อแสดง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__google_slide
msgid "Google Slide"
msgstr "Google Slide"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Google Slide Link"
msgstr "ลิงก์ Google Slide"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Group By"
msgstr "จัดกลุ่มตาม"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Group By..."
msgstr "จัดกลุ่มตาม..."

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Group by..."
msgstr "จัดกลุ่มโดย…"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_produced
msgid "Has Been Produced"
msgstr "ได้ถูกผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__has_kits
msgid "Has Kits"
msgstr "มีชุดอุปกรณ์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_production__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__2
msgid "High"
msgstr "สูง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "History"
msgstr "ประวัติ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Hours"
msgstr "ชั่วโมง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_document__id
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__id
msgid "ID"
msgstr "ไอดี"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุกิจกรรมการยกเว้น"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_id
msgid ""
"If a product variant is defined the BOM is available only for this product."
msgstr "หากมีการกำหนดตัวแปรสินค้า BOM จะพร้อมใช้งานสำหรับสินค้านี้เท่านั้น"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_unread
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_production__message_unread
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_unread
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__propagate_cancel
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr ""
"หากเลือก เมื่อการย้ายครั้งก่อนของการย้าย (ซึ่งเกิดจากการจัดซื้อครั้งต่อไป) "
"ถูกยกเลิกหรือแยกออก การย้ายที่เกิดจากการย้ายครั้งนี้ก็จะเป็นเช่นกัน"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__active
msgid ""
"If the active field is set to False, it will allow you to hide the bills of "
"material without removing it."
msgstr ""
"หากฟิลด์ที่ใช้งานอยู่ถูกตั้งค่าเป็น \"เท็จ\" "
"จะช่วยให้คุณสามารถซ่อนบิลวัสดุโดยไม่ต้องลบออก"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"หากฟิลด์ที่ใช้งานอยู่ถูกตั้งค่าเป็น เท็จ "
"ฟิลด์นี้จะอนุญาตให้คุณซ่อนการบันทึกทรัพยากรโดยไม่ต้องลบออก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_height
msgid "Image Height"
msgstr "ความสูงของภาพ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_src
msgid "Image Src"
msgstr "รูปภาพ Src"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__image_width
msgid "Image Width"
msgstr "ความกว้างรูปภาพ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_document_file_kanban_mrp
msgid "Image is a link"
msgstr "รูปภาพคือลิงก์"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_immediate_production
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__immediate_production_id
msgid "Immediate Production"
msgstr "การผลิตทันที"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_immediate_production_line
msgid "Immediate Production Line"
msgstr "ไลน์การผลิตทันที"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__immediate_production_line_ids
msgid "Immediate Production Lines"
msgstr "ไลน์การผลิตทันที"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Immediate Production?"
msgstr "การผลิตทันที ?"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid "Immediate production?"
msgstr "การผลิตทันที ?"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Impacted Transfer(s):"
msgstr "การโอนที่ได้รับผลกระทบ:"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "Import Template for Bills of Materials"
msgstr "นำเข้าเทมเพลตบิลวัสดุ"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Impossible to plan the workorder. Please check the workcenter "
"availabilities."
msgstr "ไม่สามารถวางแผนคำสั่งงานได้ กรุณาตรวจสอบความพร้อมของศูนย์งาน"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__progress
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__progress
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "In Progress"
msgstr "กำลังดำเนินการ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__index_content
msgid "Indexed Content"
msgstr "ดัชนีเนื้อหา"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_deadline
msgid ""
"Informative date allowing to define when the manufacturing order should be "
"processed at the latest to fulfill delivery on time."
msgstr ""
"วันที่ให้ข้อมูลช่วยให้กำหนดได้ว่าควรดำเนินการใบสั่งผลิตอย่างช้าที่สุดเพื่อส่งมอบให้ตรงเวลาเมื่อใด"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_moves
msgid "Inventory Moves"
msgstr "การย้ายสินค้าคงคลัง"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr "การย้ายสินค้าคงคลังที่คุณต้องสแกนหมายเลขล็อตที่คำสั่งงานนี้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__is_kits
#: model:ir.model.fields,field_description:mrp.field_product_template__is_kits
msgid "Is Kits"
msgstr "คือชุดอุปกรณ์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_locked
msgid "Is Locked"
msgstr "ถูกล็อก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__manual
msgid "Is a Blocking Reason"
msgstr "คือสาเหตุการบล็อก"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "Is a Blocking Reason?"
msgstr "คือสาเหตุการบล็อก?"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__public
msgid "Is public document"
msgstr "เป็นเอกสารสาธารณะ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_user_working
msgid "Is the Current User Working"
msgstr "คือผู้ใช้งานปัจจุบัน"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "It has already been unblocked."
msgstr "มันถูกปลดล็อกแล้ว"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"It is not possible to unplan one single Work Order. You should unplan the "
"Manufacturing Order instead in order to unplan all the linked operations."
msgstr ""
"ไม่สามารถยกเลิกการวางแผนใบสั่งงานเดียวได้ คุณควรยกเลิกการวางแผนใบสั่งผลิตแทน"
" เพื่อยกเลิกการวางแผนการดำเนินงานที่เชื่อมโยงทั้งหมด"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_planned
msgid "Its Operations are Planned"
msgstr "มีการวางแผนการปฏิบัติงาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__json_popover
msgid "JSON data for the popover widget"
msgstr "ข้อมูล JSON สำหรับวิดเจ็ต popover"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__key
msgid "Key"
msgstr "คีย์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__description_bom_line
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__phantom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Kit"
msgstr "ชุดอุปกรณ์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_document____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag____last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder____last_update
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_document__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_document__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__last_working_user_id
msgid "Last user that worked on this work order."
msgstr "ผู้ใช้รายล่าสุดที่ทำงานบนคำสั่งงานนี้"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__late
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Late"
msgstr "ล่าช้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Activities"
msgstr "กิจกรรมล่าช้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late MO or Late delivery of components"
msgstr "MO ล่าช้าหรือการส่งมอบส่วนประกอบล่าช้า"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__components_availability
msgid ""
"Latest component availability status for this MO. If green, then the MO's "
"readiness status is ready, as per BOM configuration."
msgstr ""
"สถานะความพร้อมใช้งานของส่วนประกอบล่าสุดสำหรับ MO นี้ หากเป็นสีเขียว "
"แสดงว่าสถานะความพร้อมของ MO พร้อมแล้ว ตามการกำหนดค่า BOM"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_wood_ply
#: model_terms:product.template,description:mrp.product_product_wood_ply_product_template
msgid "Layers that are stick together to assemble wood panels."
msgstr "ชั้นที่ติดกันเพื่อประกอบแผ่นไม้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__leave_id
msgid "Leave"
msgstr "ลา"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning_line
msgid "Line of issue consumption"
msgstr "ไลน์ปัญหาการบริโภค"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Lines need to be deleted, but can not as you still have some quantities to "
"consume in them. "
msgstr "จำเป็นต้องลบไลน์ แต่ทำไม่ได้เนื่องจากคุณยังมีจำนวนที่จะบริโภค"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__location_id
msgid "Location"
msgstr "ตำแหน่ง"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_id
msgid "Location where the product you want to unbuild is."
msgstr "สถานที่ที่สินค้าที่คุณต้องการรื้ออยู่"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_src_id
msgid "Location where the system will look for components."
msgstr "ตำแหน่งที่ระบบจะค้นหาส่วนประกอบ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "ตำแหน่งที่ระบบจะสต๊อกสินค้าเสร็จสิ้น"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_dest_id
msgid ""
"Location where you want to send the components resulting from the unbuild "
"order."
msgstr "สถานที่ที่คุณต้องการส่งส่วนประกอบที่เกิดจากคำสั่งรื้อ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lock"
msgstr "ล็อก"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Lock the manufacturing order to prevent changes to what has been consumed or"
" produced."
msgstr "ล็อกใบสั่งผลิตเพื่อป้องกันการเปลี่ยนแปลงของสิ่งที่ถูกใช้หรือผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_id
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Loss Reason"
msgstr "สาเหตุการสูญเสีย"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_production_lot
msgid "Lot/Serial"
msgstr "ล็อต/ซีเรียล"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__lot_producing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__finished_lot_id
msgid "Lot/Serial Number"
msgstr "ล็อต/หมายเลขซีเรียล"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__lot_id
msgid "Lot/Serial Number of the product to unbuild."
msgstr "ล็อต/หมายเลขซีเรียลของสินค้าที่จะยกเลิกการสร้าง"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_traceability
msgid "Lots/Serial Numbers"
msgstr "ล็อต/หมายเลขซีเรียล"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__1
msgid "Low"
msgstr "ต่ำ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_backorder_id
msgid "MO Backorder"
msgstr "คำสั่งล่วงหน้า MO"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "MO Generated by %s"
msgstr "MO สร้างโดย  %s"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reservation_state
msgid "MO Readiness"
msgstr "ความพร้อม MO"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_routings
msgid "MRP Work Orders"
msgstr "MRP คำสั่งงาน"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss_type
msgid "MRP Workorder productivity losses"
msgstr "MRP การสูญเสียผลผลิตของคำสั่งงาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_main_attachment_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_main_attachment_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "ทำตามคำสั่ง"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Make sure enough quantities of these components are reserved to carry on "
"production:\n"
msgstr "ตรวจสอบให้แน่ใจว่าส่วนประกอบเหล่านี้เพียงพอสำหรับการผลิต:\n"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Makes confirmed manufacturing orders locked rather than unlocked by default."
" This only applies to new manufacturing orders, not previously created ones."
msgstr ""
"ทำให้ใบสั่งผลิตที่ยืนยันแล้วถูกล็อกแทนที่จะปลดล็อกตามค่าเริ่มต้น "
"สิ่งนี้ใช้ได้กับใบสั่งผลิตใหม่เท่านั้น ไม่สามารถใช้กับใบสั่งก่อนหน้า"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_routings
msgid "Manage Work Order Operations"
msgstr "จัดการการปฏิบัติการคำสั่งงาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid "Manual Duration"
msgstr "ระยะเวลาด้วยตนเอง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "Manuf. Lead Time"
msgstr "Manuf. เวลานำผลิต"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_steps
#: model:ir.model.fields.selection,name:mrp.selection__stock_rule__action__manufacture
#: model:stock.location.route,name:mrp.route_warehouse0_manufacture
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_stock_rule
#, python-format
msgid "Manufacture"
msgstr "การผลิต"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__mrp_one_step
#, python-format
msgid "Manufacture (1 step)"
msgstr "การผลิต (ขั้นตอน 1)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_mto_pull_id
msgid "Manufacture MTO Rule"
msgstr "การผลิตกฎ MTO "

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_pull_id
msgid "Manufacture Rule"
msgstr "กฎการผลิต"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "Manufacture Security Lead Time"
msgstr "ระยะเวลานำการผลิตที่ปลอดภัย"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__normal
msgid "Manufacture this product"
msgstr "ผลิตสินค้ารายการนี้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_to_resupply
msgid "Manufacture to Resupply"
msgstr "การผลิตเพื่อการจัดหาใหม่"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__mrp_product_qty
#: model:ir.model.fields,field_description:mrp.field_product_template__mrp_product_qty
msgid "Manufactured"
msgstr "ผลิตแล้ว"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "Manufactured Products"
msgstr "สินค้าที่ผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Manufactured in the last 365 days"
msgstr "ผลิตใน 365 วันที่ผ่านมา"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__code__mrp_operation
#: model:ir.ui.menu,name:mrp.menu_mrp_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#, python-format
msgid "Manufacturing"
msgstr "การผลิต"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#: model:ir.model.fields,field_description:mrp.field_product_product__produce_delay
#: model:ir.model.fields,field_description:mrp.field_product_template__produce_delay
#: model:ir.model.fields,field_description:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__manufacturing_lead
#, python-format
msgid "Manufacturing Lead Time"
msgstr "เวลานำการผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manu_type_id
msgid "Manufacturing Operation Type"
msgstr "ประเภทการปฏิบัติการผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__production_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Manufacturing Order"
msgstr "ใบสั่งผลิต"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production_workcenter
#: model:ir.actions.act_window,name:mrp.action_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_production_action
#: model:ir.actions.act_window,name:mrp.mrp_production_action_picking_deshboard
#: model:ir.actions.act_window,name:mrp.mrp_production_report
#: model:ir.ui.menu,name:mrp.menu_mrp_production_action
#: model:ir.ui.menu,name:mrp.menu_mrp_production_report
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_production_calendar
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_production_pivot
msgid "Manufacturing Orders"
msgstr "ใบสั่งผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are in confirmed state."
msgstr "คำสั่งผลิตที่มีสถานะเป็น 'ยืนยัน'"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__ready_to_produce
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manufacturing Readiness"
msgstr "ความพร้อมการผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Manufacturing Reference"
msgstr "ข้อมูลอ้างอิงการผลิต"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc."
msgstr ""
"การปฏิบัติการผลิตจะได้รับการดำเนินการที่ศูนย์ปฏิบัติงาน ศูนย์งานสามารถประกอบด้วย\n"
"                พนักงานและ/หรือเครื่องจักรที่ใช้ในการคิดต้นทุน การจัดกำหนดการ การวางแผนกำลังการผลิต และอื่น ๆ "

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.\n"
"                They can be defined via the configuration menu."
msgstr ""
"ปฏิบัติการผลิตที่ศูนย์งาน ศูนย์งานสามารถประกอบด้วย\n"
"            พนักงานและ/หรือเครื่องจักรที่ใช้ในการคิดต้นทุน การจัดกำหนดการ การวางแผนกำลังการผลิต และอื่น ๆ \n"
"                สามารถกำหนดได้ผ่านเมนูการกำหนดค่า"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reservation_state
msgid ""
"Manufacturing readiness for this MO, as per bill of material configuration:\n"
"            * Ready: The material is available to start the production.\n"
"            * Waiting: The material is not available to start the production.\n"
msgstr ""
"ความพร้อมในการผลิตสำหรับ MO นี้ ต่อรายการกำหนดค่าบิลวัสดุ:\n"
"            * พร้อม: มีวัสดุเพื่อเริ่มการผลิต\n"
"            * รอ: วัสดุไม่พร้อมใช้งานเพื่อเริ่มการผลิต\n"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_mark_done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Mark as Done"
msgstr "ทำเครื่องหมายว่าสำเร็จ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Mass Produce"
msgstr "การผลิตจำนวนมาก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_mps
msgid "Master Production Schedule"
msgstr "กำหนดการการผลิตหลัก"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Material Availability"
msgstr "ความพร้อมของวัตถุดิบ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error
msgid "Message Delivery error"
msgstr "เกิดการผิดพลาดในการส่งข้อความ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__mimetype
msgid "Mime Type"
msgstr "ประเภท Mime"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "กฎขั้นต่ำของสินค้าคงคลัง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Minutes"
msgstr "นาที"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Miscellaneous"
msgstr "เบ็ดเตล็ด"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__mo_ids
msgid "Mo"
msgstr "Mo"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_byproduct_ids
msgid "Move Byproduct"
msgstr "เคลื่อนสินค้าพลอยได้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "ย้ายไปติดตาม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_consumption_warning_line_ids
msgid "Mrp Consumption Warning Line"
msgstr "ไลน์คำเตือนการบริโภค Mrp"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_procurement_group__mrp_production_ids
msgid "Mrp Production"
msgstr "การผลิต Mrp"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_count
msgid "Mrp Production Count"
msgstr "จำนวนการผลิต Mrp"

#. module: mrp
#: model:ir.actions.server,name:mrp.production_order_server_action
msgid "Mrp: Plan Production Orders"
msgstr "Mrp: วางแผนใบสั่งผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมของฉัน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__name
msgid "Name"
msgstr "ชื่อ"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "New"
msgstr "ใหม่"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Next Activity"
msgstr "กิจกรรมถัดไป"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมถัดไป"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__next_work_order_id
msgid "Next Work Order"
msgstr "คำสั่งงานถัดไป"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "No Backorder"
msgstr "ไม่สร้างคำสั่งล่วงหน้า"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid "No bill of materials found. Let's create one!"
msgstr "ไม่พบบิลวัสดุ มาสร้างกัน!"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "No data available."
msgstr "ไม่มีข้อมูลอยู่"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_report
msgid "No data yet!"
msgstr "ยังไม่มีข้อมูล!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "No manufacturing order found. Let's create one."
msgstr "ไม่พบใบสั่งผลิต มาสร้างกัน"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid "No product found. Let's create one!"
msgstr "ไม่พบสินค้า มาสร้างกันเถอะ!"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_blocked
msgid "No productivity loss for this equipment"
msgstr "ไม่มีการสูญเสียผลผลิตสำหรับอุปกรณ์นี้"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid "No unbuild order found"
msgstr "ไม่พบคำสั่งรื้อ"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid "No work orders to do!"
msgstr "ไม่มีคำสั่งงานให้ทำ!"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid ""
"No workorder currently in progress. Click to mark work center as blocked."
msgstr ""
"ไม่มีคำสั่งงานที่กำลังดำเนินการอยู่ "
"คลิกเพื่อทำเครื่องหมายที่ศูนย์งานว่าถูกบล็อก"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__normal
msgid "Normal"
msgstr "ปกติ"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Not Available"
msgstr "ไม่พร้อมใช้"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_scrap__workorder_id
msgid "Not to restrict or prefer quants, but informative."
msgstr "ไม่จำกัดหรือวิเคราะห์เชิงปริมาณ แต่เป็นเชิงข้อมูล"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid ""
"Note that archived work center(s): '%s' is/are still linked to active Bill "
"of Materials, which means that operations can still be planned on it/them. "
"To prevent this, deletion of the work center is recommended instead."
msgstr ""
"โปรดทราบว่าศูนย์งานที่เก็บถาวร: '%s' ยังคงเชื่อมโยงกับบิลวัสดุที่ใช้งานอยู่ "
"ซึ่งหมายความว่ายังคงสามารถวางแผนการปฏิบัติการได้ เพื่อป้องกันสิ่งนี้ "
"ขอแนะนำให้ลบศูนย์งานแทน"

#. module: mrp
#: code:addons/mrp/models/product.py:0 code:addons/mrp/models/product.py:0
#, python-format
msgid ""
"Note that product(s): '%s' is/are still linked to active Bill of Materials, "
"which means that the product can still be used on it/them."
msgstr ""
"โปรดทราบว่าสินค้า: '%s' ยังคงเชื่อมโยงกับรายการวัสดุที่ใช้งานอยู่ "
"ซึ่งหมายความว่าสินค้ายังคงสามารถใช้กับสินค้านั้นได้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_late
msgid "Number of Manufacturing Orders Late"
msgstr "จำนวนใบสั่งผลิตที่ล่าช้า"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_waiting
msgid "Number of Manufacturing Orders Waiting"
msgstr "จำนวนใบสั่งผลิตที่รอ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_todo
msgid "Number of Manufacturing Orders to Process"
msgstr "จำนวนใบสั่งผลิตที่จะดำเนินการ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_child_count
msgid "Number of generated MO"
msgstr "จำนวน MO ที่ถูกสร้าง"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "จํานวนข้อความที่ต้องการการดําเนินการ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__capacity
msgid ""
"Number of pieces (in product UoM) that can be produced in parallel  (at the "
"same time) at this work center. For example: the capacity is 5 and you need "
"to produce 10 units, then the operation time listed on the BOM will be "
"multiplied by two. However, note that both time before and after production "
"will only be counted once. "
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_qty
msgid ""
"This should be the smallest quantity that this product can be produced in. "
"If the BOM contains operations, make sure the work center capacity is "
"accurate. "
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_source_count
msgid "Number of source MO"
msgstr "จำนวนแหล่งที่มา MO"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_unread_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_unread_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_unread_counter
msgid "Number of unread messages"
msgstr "จํานวนข้อความที่ยังไม่ได้อ่าน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "OEE"
msgstr "OEE"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee_target
msgid "OEE Target"
msgstr "เป้าหมาย OEE"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee
msgid "Oee"
msgstr "Oee"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "On Hand"
msgstr "ที่มีอยู่"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Operation"
msgstr "ปฏิบัติการ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__operation_id
msgid "Operation To Consume"
msgstr "ปฏิบัติการที่จะใช้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__picking_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_type_id
msgid "Operation Type"
msgstr "ประเภทการปฏิบัติการ"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid ""
"Operation define that need to be done to realize a Work Order.\n"
"                Each operation is done at a specific Work Center and has a specific duration."
msgstr ""
"การปฏิบัติการที่ระบุและต้องทำเพื่อให้ตระหนักถึงคำสั่งงาน\n"
"                การปฏิบัติการแต่ละครั้งจะทำที่ศูนย์งานเฉพาะและมีระยะเวลาเฉพาะ"

#. module: mrp
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model:ir.actions.act_window,name:mrp.mrp_routing_action
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__allowed_operation_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_manufacturing
#: model:ir.ui.menu,name:mrp.menu_mrp_routing_action
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workorder_view_gantt
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_calendar
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_gantt_production
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
#, python-format
msgid "Operations"
msgstr "การปฏิบัติการ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Done"
msgstr "ปฏิบัติการสำเร็จ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Planned"
msgstr "วางแผนการปฏิบัติการ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Operations Search Filters"
msgstr "ตัวกรองการค้นหาปฏิบัติการ"

#. module: mrp
#: model:ir.actions.report,name:mrp.label_production_order
msgid "Order Label"
msgstr "ป้ายคำสั่ง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__orderpoint_id
msgid "Orderpoint"
msgstr "จุดคำสั่ง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__order_ids
msgid "Orders"
msgstr "คำสั่ง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "ต้นฉบับ (ไม่ได้ปรับให้เหมาะสม ไม่ได้ปรับขนาด) แนบไฟล์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_production
msgid "Original Production Quantity"
msgstr "จำนวนการผลิตเดิม"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee_target
msgid "Overall Effective Efficiency Target in percentage"
msgstr "เป้าหมายประสิทธิภาพเป็นเปอร์เซ็นต์โดยรวม"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_oee
#: model:ir.ui.menu,name:mrp.menu_mrp_workcenter_productivity_report
msgid "Overall Equipment Effectiveness"
msgstr "ประสิทธิภาพของอุปกรณ์โดยรวม"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee
msgid "Overall Equipment Effectiveness, based on the last month"
msgstr "ประสิทธิภาพของอุปกรณ์โดยรวมอิงตามเดือนที่แล้ว"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_oee
msgid "Overall Equipment Effectiveness: no working or blocked time"
msgstr "ประสิทธิภาพของอุปกรณ์โดยรวม: ไม่มีเวลาทำงานหรือถูกบล็อก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_id
msgid "Parent BoM"
msgstr "BoM หลัก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__parent_product_tmpl_id
msgid "Parent Product Template"
msgstr "เทมเพลตสินค้าหลัก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_consumption_warning_id
msgid "Parent Wizard"
msgstr "ตัวช่วยหลัก"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr ""
"วาง URL ของ Google สไลด์ของคุณ "
"ตรวจสอบให้แน่ใจว่าการเข้าถึงเอกสารเป็นแบบสาธารณะ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Pause"
msgstr "หยุด"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Pending"
msgstr "รอดำเนินการ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__performance
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__performance
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Performance"
msgstr "ประสิทธิภาพ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Performance Losses"
msgstr "การสูญเสียประสิทธิภาพ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__performance
msgid "Performance over the last month"
msgstr "ประสิทธิภาพในเดือนที่ผ่านมา"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pick Components"
msgstr "การรับส่วนประกอบ"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pick components and then manufacture"
msgstr "รับส่วนประกอบแล้วผลิต"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm
msgid "Pick components and then manufacture (2 steps)"
msgstr "รับส่วนประกอบแล้วผลิต (2 ขั้นตอน)"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm_sam
#, python-format
msgid "Pick components, manufacture and then store products (3 steps)"
msgstr "รับส่วนประกอบ ผลิต แล้วจัดเก็บสินค้า (3 ขั้นตอน)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_mto_pull_id
msgid "Picking Before Manufacturing MTO Rule"
msgstr "การรับก่อนการผลิตกฎ MTO"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_type_id
msgid "Picking Before Manufacturing Operation Type"
msgstr "รับก่อนประเภทการปฏิบัติการผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_route_id
msgid "Picking Before Manufacturing Route"
msgstr "การรับก่อนเส้นทางการผลิต"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking_type
msgid "Picking Type"
msgstr "ประเภทการรับ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_ids
msgid "Picking associated to this manufacturing order"
msgstr "การรับที่เกี่ยวข้องกับใบสั่งผลิตนี้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_loc_id
msgid "Picking before Manufacturing Location"
msgstr "การรับก่อนตำแหน่งผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Plan"
msgstr "แผน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Plan Orders"
msgstr "แผนคำสั่ง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Plan manufacturing or purchase orders based on forecasts"
msgstr "วางแผนการผลิตหรือใบสั่งซื้ออิงตามการคาดการณ์"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planned"
msgstr "แผน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Planned Date"
msgstr "วันที่ตามแผน"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Planned at the same time as other workorder(s) at %s"
msgstr "วางแผนควบคู่ไปกับคำสั่งงานอื่น ๆ ที่%s"

#. module: mrp
#: model:ir.ui.menu,name:mrp.mrp_planning_menu_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Planning"
msgstr "การวางแผน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planning Issues"
msgstr "ปัญหาการวางแผน"

#. module: mrp
#: model:product.product,name:mrp.product_product_plastic_laminate
#: model:product.template,name:mrp.product_product_plastic_laminate_product_template
msgid "Plastic Laminate"
msgstr ""
"พลาสติกลามิเนต"
"                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  "

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_field_one2many_with_copy.js:0
#, python-format
msgid "Please click on the \"save\" button first"
msgstr "กรุณาคลิกที่ปุ่ม \"บันทึก\" ก่อน"

#. module: mrp
#: model:product.product,name:mrp.product_product_wood_ply
#: model:product.template,name:mrp.product_product_wood_ply_product_template
msgid "Ply Layer"
msgstr "ชั้นอัด"

#. module: mrp
#: model:product.product,name:mrp.product_product_ply_veneer
#: model:product.template,name:mrp.product_product_ply_veneer_product_template
msgid "Ply Veneer"
msgstr "แผ่นไม้ธรรมชาติบาง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__json_popover
msgid "Popover Data JSON"
msgstr "ข้อมูลป๊อปโอเวอร์ JSON"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__possible_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__possible_bom_product_template_attribute_value_ids
msgid "Possible Product Template Attribute Value"
msgstr "ค่าคุณลักษณะเทมเพลตสินค้าที่เป็นไปได้"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Post-Production"
msgstr "หลังการผลิต"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Pre-Production"
msgstr "ก่อนการผลิต"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Print"
msgstr "พิมพ์"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Print All Variants"
msgstr "พิมพ์ตัวแปร"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Print Unfolded"
msgstr "พิมพ์แบบกางออก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__priority
#: model:ir.model.fields,field_description:mrp.field_mrp_production__priority
msgid "Priority"
msgstr "ระดับความสำคัญ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process operations at specific work centers"
msgstr "ดำเนินการปฏิบัติการที่ศูนย์งานเฉพาะ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__produce_line_ids
msgid "Processed Disassembly Lines"
msgstr "ไลน์การถอดชิ้นส่วนที่ดำเนินการแล้ว"

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_group
#: model:ir.model.fields,field_description:mrp.field_mrp_production__procurement_group_id
msgid "Procurement Group"
msgstr "กลุ่มการจัดซื้อ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_steps
msgid ""
"Produce : Move the components to the production location        directly and start the manufacturing process.\n"
"Pick / Produce : Unload        the components from the Stock to Input location first, and then        transfer it to the Production location."
msgstr ""
"ผลิต : ย้ายส่วนประกอบไปยังสถานที่ผลิตโดยตรงและเริ่มกระบวนการผลิต\n"
"เลือก / ผลิต : ยกเลิกการโหลดส่วนประกอบจากสต๊อกไปยังตำแหน่งนำเข้าก่อนแล้วจึงโอนไปยังสถานที่ผลิต"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_byproducts
msgid "Produce residual products"
msgstr "ผลิตผลิตภัณฑ์ตกค้าง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Produce residual products (A + B -&gt; C + D)"
msgstr "ผลิตผลิตภัณฑ์ตกค้าง (A + B -&gt; C + D)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "Produced"
msgstr "ผลิตแล้ว"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__produced_qty
msgid "Produced Quantity"
msgstr "จำนวนที่ผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__serial_numbers
msgid "Produced Serial Numbers"
msgstr "ผลิตหมายเลขซีเรียล"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__operation_id
msgid "Produced in Operation"
msgstr "การผลิตอยู่ระหว่างการปฏิบัติการ"

#. module: mrp
#: model:ir.model,name:mrp.model_product_product
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Product"
msgstr "สินค้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Product Attachments"
msgstr "การแนบสินค้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Product Cost"
msgstr "ต้นทุนสินค้า"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_virtual_available
msgid "Product Forecasted Quantity"
msgstr "จำนวนสินค้าที่คาดการณ์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr "การจัดการวงจรชีวิตผลิตภัณฑ์ (PLM)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Product Moves"
msgstr "ย้ายสินค้า"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "ย้ายสินค้า ( ไลน์เคลื่อนย้ายสต๊อก )"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_qty_available
msgid "Product On Hand Quantity"
msgstr "จำนวนสินค้าในมือ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Product Quantity"
msgstr "จำนวนสินค้า"

#. module: mrp
#: model:ir.model,name:mrp.model_product_template
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tmpl_id
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_id
msgid "Product Unit of Measure"
msgstr "หน่วยวัดสินค้า"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_id
msgid "Product Variant"
msgstr "ตัวแปรสินค้า"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_product_variant_action
#: model:ir.ui.menu,name:mrp.product_variant_mrp
msgid "Product Variants"
msgstr "ตัวแปรสินค้า"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__production_id
msgid "Production"
msgstr "การผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_date
msgid "Production Date"
msgstr "วันที่การผลิต"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_document
msgid "Production Document"
msgstr "เอกสารการผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Production Information"
msgstr "ข้อมูลการผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_location_id
msgid "Production Location"
msgstr "ตำแหน่งการผลิต"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_production_order
#: model:ir.model,name:mrp.model_mrp_production
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__production_id
msgid "Production Order"
msgstr "คำสั่งผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__raw_material_production_id
msgid "Production Order for components"
msgstr "คำสั่งผลิตส่วนประกอบ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__production_id
msgid "Production Order for finished products"
msgstr "คำสั่งผลิตสินค้าสำเร็จรูป"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_state
msgid "Production State"
msgstr "สถานะการผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Production Workcenter"
msgstr "ศูนย์การผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Production of Draft MO"
msgstr "การผลิตฉบับร่าง MO"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Production started late"
msgstr "เริ่มการผลิตล่าช้า"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__productive
msgid "Productive"
msgstr "ประสิทธิผล"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__productive_time
msgid "Productive Time"
msgstr "เวลาในการผลิต"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__productive_time
msgid "Productive hours over the last month"
msgstr "ชั่วโมงการผลิตในเดือนที่ผ่านมา"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Productivity"
msgstr "ผลิตผล"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_blocked
msgid "Productivity Losses"
msgstr "การสูญเสียผลผลิต"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_template_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom
#: model:ir.ui.menu,name:mrp.menu_mrp_product_form
msgid "Products"
msgstr "สินค้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Products to Consume"
msgstr "สินค้าที่จะบริโภค"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__progress
msgid "Progress Done (%)"
msgstr "ความคืบหน้าที่เสร็จสิ้น (%)"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__propagate_cancel
msgid "Propagate cancel and split"
msgstr "การส่งผ่านยกเลิกและแยกออก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__quality
msgid "Quality"
msgstr "คุณภาพ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Quality Losses"
msgstr "การสูญเสียคุณภาพ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "แผ่นงานคุณภาพ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quant_ids
msgid "Quant"
msgstr "วิเคราะห์เชิงปริมาณ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_produced
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quantity
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Quantity"
msgstr "จำนวน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Quantity Produced"
msgstr "จำนวนที่ผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_producing
msgid "Quantity Producing"
msgstr "จำนวนการผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_remaining
msgid "Quantity To Be Produced"
msgstr "จำนวนที่จะถูกผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__should_consume_qty
msgid "Quantity To Consume"
msgstr "จำนวนที่บริโภค"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_qty
msgid "Quantity To Produce"
msgstr "จำนวนที่จะผลิต"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Quantity:"
msgstr "จำนวน:"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_raw_ids
msgid "Raw Moves"
msgstr "การเคลื่อนย้ายวัตถุดิบ"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__assigned
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__ready
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Ready"
msgstr "พร้อม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_real_duration
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration
msgid "Real Duration"
msgstr "ระยะเวลาจริง"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"Recursion error!  A product with a Bill of Material should not have itself "
"in its BoM or child BoMs!"
msgstr ""
"การเรียกซ้ำผิดพลาด! สินค้าที่มีบิลวัสดุไม่ควรมีอยู่ใน BoM หรือ BoM ย่อย!"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__code
#: model:ir.model.fields,field_description:mrp.field_mrp_production__name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__name
msgid "Reference"
msgstr "อ้างอิง"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_name_uniq
msgid "Reference must be unique per Company!"
msgstr "การอ้างอิงจะต้องไม่ซ้ำกันสำหรับแต่ละบริษัท!"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__origin
msgid ""
"Reference of the document that generated this production order request."
msgstr "การอ้างอิงของเอกสารที่สร้างคำร้องขอคำสั่งผลิตนี้"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Reference:"
msgstr "อ้างอิง:"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__ir_attachment_id
msgid "Related attachment"
msgstr "ไฟล์แนบที่เกี่ยวข้อง"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Replan"
msgstr "วางแผนใหม่"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Report:"
msgstr "รายงาน:"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_reporting
msgid "Reporting"
msgstr "การรายงาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Reserve"
msgstr "สำรอง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Reserved"
msgstr "สำรองแล้ว"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_id
msgid "Resource"
msgstr "ทรัพยากร"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_field
msgid "Resource Field"
msgstr "ฟิลด์ทรัพยากร"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_id
msgid "Resource ID"
msgstr "ไอดีทรัพยากร"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_model
msgid "Resource Model"
msgstr "โมเดลทรัพยากร"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__res_name
msgid "Resource Name"
msgstr "ชื่อทรัพยากร"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__user_id
msgid "Responsible"
msgstr "รับผิดชอบ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__routing_line_ids
msgid "Routing Lines"
msgstr "ไลน์เส้นทาง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
msgid "Routing Work Centers"
msgstr "เส้นทางศูนย์งาน"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_procurement_compute_mrp
msgid "Run Scheduler"
msgstr "เปิดใช้งานผู้กำหนดเวลา"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Schedule manufacturing orders earlier to avoid delays"
msgstr "จัดกำหนดการใบสั่งผลิตให้เร็วขึ้นเพื่อหลีกเลี่ยงความล่าช้า"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_planned_start
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Scheduled Date"
msgstr "วันที่ตามกำหนดการ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Scheduled Date by Month"
msgstr "วันที่กำหนดเป็นเดือน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_planned_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_planned_finished
msgid "Scheduled End Date"
msgstr "วันที่สิ้นสุดตามกำหนดการ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_planned_start
msgid "Scheduled Start Date"
msgstr "วันที่เริ่มต้นตามกำหนดการ"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"Scheduled before the previous work order, planned from %(start)s to %(end)s"
msgstr "จัดกำหนดการก่อนคำสั่งงานก่อนหน้านี้ วางแผนจาก%(start)s ถึง %(end)s"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_workorder_popover.js:0
#, python-format
msgid "Scheduling Information"
msgstr "ข้อมูลกำหนดการ"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
#: model:ir.model,name:mrp.model_stock_scrap
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_scrap
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#, python-format
msgid "Scrap"
msgstr "เศษซากผลิตภัณฑ์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_count
msgid "Scrap Move"
msgstr "ย้ายเศษซากผลิตภัณฑ์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_ids
msgid "Scraps"
msgstr "เศษซากผลิตภัณฑ์"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_screw
#: model:product.template,name:mrp.product_product_computer_desk_screw_product_template
msgid "Screw"
msgstr "สกรู"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Search"
msgstr "ค้นหา"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Search Bill Of Material"
msgstr "ค้นจากบิลวัสดุ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Search Production"
msgstr "ค้นหาการผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Search Work Orders"
msgstr "ค้นหาคำสั่งงาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Search for mrp workcenter"
msgstr "ค้นหาศูนย์งาน mrp"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Security Lead Time"
msgstr "ความปลอดภัยเวลานำสินค้า"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,help:mrp.field_res_config_settings__manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr "วันที่ปลอดภัยสำหรับการปฏิบัติการผลิตแต่ละครั้ง"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_field_one2many_with_copy.js:0
#, python-format
msgid "Select Operations to Copy"
msgstr "เลือกปฏิบัติการที่จะคัดลอก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence picking before manufacturing"
msgstr "ลำดับสต๊อกก่อนการผลิต"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence production"
msgstr "ลำดับการผลิต"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Sequence stock after manufacturing"
msgstr "ลำดับสต๊อกหลังการผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "Serial Mass Produce"
msgstr "ซีเรียลสินค้าจำนวนมาก"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__manual
msgid "Set duration manually"
msgstr "ตั้งระยะเวลาด้วยตนเอง"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_configuration
#: model:ir.ui.menu,name:mrp.menu_mrp_config
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Settings"
msgstr "ตั้งค่า"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_start
msgid "Setup Time"
msgstr "ตั้งเวลา"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__show_apply
msgid "Show Apply"
msgstr "แสดงการใช้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_assign_serial__show_backorders
msgid "Show Backorders"
msgstr "แสดงคำสั่งล่วงหน้า"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__show_bom
msgid "Show BoM column"
msgstr "แสดงคอลัมน์ BoM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_final_lots
msgid "Show Final Lots"
msgstr "แสดงล็อตสุดท้าย"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lock
msgid "Show Lock/unlock buttons"
msgstr "แสดงปุ่มที่ล็อก/ไม่ได้ล็อก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__show_json_popover
msgid "Show Popover?"
msgstr "แสดง ป๊อปโอเวอร์?"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production__show_productions
msgid "Show Productions"
msgstr "แสดงการผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"100%match\n"
"แสดงบันทึกทั้งหมดที่มีวันที่ดำเนินการถัดไปคือก่อนวันนี้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__show_backorder_lines
msgid "Show backorder lines"
msgstr "แสดงไลน์คำสั่งล่วงหน้า"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__leave_id
msgid "Slot into workcenter calendar once planned"
msgstr "สล็อตลงในปฏิทินศูนย์งานเมื่อวางแผนไว้"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_head
#: model_terms:product.template,description:mrp.product_product_computer_desk_head_product_template
msgid "Solid wood is a durable natural material."
msgstr "ไม้เนื้อแข็งเป็นวัสดุธรรมชาติที่ทนทาน"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk
#: model_terms:product.template,description:mrp.product_product_computer_desk_product_template
msgid "Solid wood table."
msgstr "โต๊ะไม้เนื้อแข็ง"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ""
"Some of your byproducts are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct byproducts."
msgstr ""
"มีการติดตามสินค้าพลอยได้บางส่วนของคุณ "
"คุณต้องระบุใบสั่งผลิตเพื่อเรียกข้อมูลสินค้าพลอยได้ที่ถูกต้อง"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ""
"Some of your components are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct components."
msgstr ""
"มีการติดตามส่วนประกอบบางส่วนของคุณ "
"คุณต้องระบุใบสั่งผลิตเพื่อเรียกส่วนประกอบที่ถูกต้อง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Some product moves have already been confirmed, this manufacturing order "
"can't be completely cancelled. Are you still sure you want to process ?"
msgstr ""
"มีการยืนยันการย้ายสินค้าบางรายการแล้ว "
"ใบสั่งผลิตนี้ไม่สามารถยกเลิกได้อย่างสมบูรณ์ "
"คุณยังแน่ใจหรือไม่ว่าต้องการดำเนินการ?"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Some work orders are already done, you cannot unplan this manufacturing "
"order."
msgstr ""
"คำสั่งงานบางรายการเสร็จสิ้นแล้ว คุณไม่สามารถยกเลิกการวางแผนใบสั่งผลิตนี้ได้"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"Some work orders have already started, you cannot unplan this manufacturing "
"order."
msgstr ""
"คำสั่งงานบางรายการได้เริ่มต้นขึ้นแล้ว "
"คุณไม่สามารถยกเลิกการวางแผนใบสั่งผลิตนี้ได้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__origin
msgid "Source"
msgstr "แหล่งที่มา"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_id
msgid "Source Location"
msgstr "ตำแหน่งแหล่งที่มา"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__costs_hour
msgid "Specify cost of work center per hour."
msgstr "ระบุต้นทุนของศูนย์งานต่อชั่วโมง"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_screw
#: model_terms:product.template,description:mrp.product_product_computer_desk_screw_product_template
msgid "Stainless steel screw"
msgstr "สกรูสแตนเลส"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_computer_desk_bolt
#: model_terms:product.template,description:mrp.product_product_computer_desk_bolt_product_template
msgid "Stainless steel screw full (dia - 5mm, Length - 10mm)"
msgstr "สกรูสแตนเลส เต็ม(เส้นผ่านศูนย์กลาง - 5 มม. ยาว - 10 มม.)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Starred"
msgstr "ติดดาว"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Start"
msgstr "เริ่ม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_start
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__state
msgid "State"
msgstr "สถานะ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__state
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Status"
msgstr "สถานะ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"สถานะตามกิจกรรม\n"
"เกินกำหนด: วันที่ครบกำหนดผ่านไปแล้ว\n"
"วันนี้: วันที่จัดกิจกรรมคือวันนี้\n"
"วางแผน: กิจกรรมในอนาคต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_type_id
msgid "Stock After Manufacturing Operation Type"
msgstr "สต๊อกก่อนประเภทการปฏิบัติการผลิต"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_rule_id
msgid "Stock After Manufacturing Rule"
msgstr "สต๊อกหลังกฎการผลิต"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_assign_serial
msgid "Stock Assign Serial Numbers"
msgstr "กำหนดหมายเลขซีเรียลในสต๊อก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_availability
msgid "Stock Availability"
msgstr "ความพร้อมสต๊อก"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move
msgid "Stock Move"
msgstr "ย้ายสต๊อก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_dest_ids
msgid "Stock Movements of Produced Goods"
msgstr "การเคลื่อนย้ายสต๊อกของสินค้าที่ผลิต"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_unbuild_moves
msgid "Stock Moves"
msgstr "ย้ายสต๊อก"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "รายงานการรับสต๊อก"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "รายงานการเติมสต๊อก"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_rule
msgid "Stock Rule"
msgstr "กฎสต๊อก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_loc_id
msgid "Stock after Manufacturing Location"
msgstr "สต๊อกหลังตำแหน่งการผลิต"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "รายงานกฎสต๊อก"

#. module: mrp
#: code:addons/mrp/models/stock_warehouse.py:0
#, python-format
msgid "Store Finished Product"
msgstr "เก็บสินค้าสำเร็จรูป"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__store_fname
msgid "Stored Filename"
msgstr "ชื่อไฟล์ที่จัดเก็บ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Structure & Cost"
msgstr "โครงสร้างและต้นทุน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_bom_id
msgid "Sub BoM"
msgstr "BoM ย่อย"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Subcontract the production of some products"
msgstr "สัญญาช่วงการผลิตสำหรับสินค้าบางรายการ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_subcontracting
msgid "Subcontracting"
msgstr "สัญญาช่วง"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk
#: model:product.template,name:mrp.product_product_computer_desk_product_template
msgid "Table"
msgstr "โต๊ะ"

#. module: mrp
#: model:product.product,name:mrp.product_product_table_kit
#: model:product.template,name:mrp.product_product_table_kit_product_template
msgid "Table Kit"
msgstr "ชุดอุปกรณ์โต๊ะ"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_leg
#: model:product.template,name:mrp.product_product_computer_desk_leg_product_template
msgid "Table Leg"
msgstr "ขาโต๊ะ"

#. module: mrp
#: model:product.product,name:mrp.product_product_computer_desk_head
#: model:product.template,name:mrp.product_product_computer_desk_head_product_template
msgid "Table Top"
msgstr "ท็อปโต๊ะ"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_table_kit
#: model_terms:product.template,description:mrp.product_product_table_kit_product_template
msgid "Table kit"
msgstr "ชุดอุปกรณ์โต๊ะ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tag_ids
msgid "Tag"
msgstr "แท็ก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__name
msgid "Tag Name"
msgstr "ชื่อแท็ก"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__is_done
msgid "Technical Field to order moves"
msgstr "ฟิลด์เทคนิคในการย้ายคำสั่ง"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__is_user_working
msgid "Technical field indicating whether the current user is working. "
msgstr "ฟิลด์เทคนิคระบุว่าผู้ใช้ปัจจุบันกำลังทำงานอยู่หรือไม่"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr "ฟิลด์เทคนิคเพื่อตรวจสอบเมื่อตรวจสอบจำนวนการสำรอง"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr "ฟิลด์เทคนิคในการตรวจสอบว่าเราจะยกเลิกการสำรองได้เมื่อใด"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_assign_serial__show_apply
msgid "Technical field to show the Apply button"
msgstr "ฟิลด์เทคนิคเพื่อแสดงปุ่มใช้"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_assign_serial__show_backorders
msgid "Technical field to show the Create Backorder and No Backorder buttons"
msgstr "ฟิลด์ทางเทคนิคแสดงปุ่มสร้างคำสั่งล่วงหน้าและห้ามคำสั่งล่วงหน้า"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__costs_hour
msgid ""
"Technical field to store the hourly cost of workcenter at time of work order"
" completion (i.e. to keep a consistent cost)."
msgstr ""
"ฟิลด์เทคนิคเพื่อจัดเก็บต้นทุนรายชั่วโมงของศูนย์งานเมื่อเวลาที่คำสั่งงานเสร็จสิ้น"
" (กล่าวคือ รักษาต้นทุนให้สม่ำเสมอ)"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__key
msgid ""
"Technical field used to resolve multiple attachments in a multi-website "
"environment."
msgstr "ฟิลด์เทคนิคที่ใช้ในการแก้ไขไฟล์แนบหลายรายการในสภาพแวดล้อมหลายเว็บไซต์"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_availability
msgid "Technical: used in views and domains only."
msgstr "ทางเทคนิค: ใช้ในมุมมองและโดเมนเท่านั้น"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__working_state
msgid "Technical: used in views only"
msgstr "ทางเทคนิค: ใช้ในมุมมองเท่านั้น"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_state
msgid "Technical: used in views only."
msgstr "ทางเทคนิค: ใช้ในมุมมองเท่านั้น"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__text
msgid "Text"
msgstr "ข้อความ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,help:mrp.field_mrp_workorder__operation_note
msgid "Text worksheet description"
msgstr "ข้อควาามอธิบายแผ่นงาน"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_id
msgid "The Bill of Material this operation is linked to"
msgstr "บิลวัสดุการปฏิบัติการนี้เชื่อมโยงกับ"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0 code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The Product Unit of Measure you chose has a different category than in the "
"product form."
msgstr "หน่วยวัดสินค้าที่คุณเลือกอยู่คนละหมวดหมู่กับที่อยู่ในแบบฟอร์มสินค้า"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "The Workorder (%s) cannot be started twice!"
msgstr "ลำดับงาน (%s) ไม่สามารถเริ่มได้สองครั้ง!"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The attribute value %(attribute)s set on product %(product)s does not match "
"the BoM product %(bom_product)s."
msgstr ""
"ค่าคุณลักษณะ%(attribute)s ที่ตั้งบนสินค้า %(product)s ไม่ตรงกับ BoM "
"ของสินค้า%(bom_product)s"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "The capacity must be strictly positive."
msgstr "ความจุจำเป็นต้องเป็นบวก"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "The component %s should not be the same as the product to produce."
msgstr "ส่วนประกอบ %s ไม่ควรเหมือนกับสินค้าที่ผลิต"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The current configuration is incorrect because it would create a cycle "
"between these products: %s."
msgstr ""
"การกำหนดค่าปัจจุบันไม่ถูกต้อง "
"เนื่องจากจะสร้างการหมุนเวียนระหว่างสินค้าเหล่านี้: %s"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__res_model
msgid "The database object this attachment will be attached to."
msgstr "ออบเจ็กต์ฐานข้อมูลที่จะแนบไฟล์แนบนี้"

#. module: mrp
#: code:addons/mrp/models/stock_orderpoint.py:0
#, python-format
msgid "The following replenishment order has been generated"
msgstr "สร้างคำสั่งเติมสินค้าต่อไปนี้แล้ว"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_produced
msgid "The number of products already handled by this work order"
msgstr "จำนวนสินค้าที่จัดการโดยคำสั่งงานนี้แล้ว"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__operation_id
msgid ""
"The operation where the components are consumed, or the finished products "
"created."
msgstr "การปฏิบัติการที่ใช้ส่วนประกอบหรือสินค้าสำเร็จรูปที่สร้างขึ้น"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__cost_share
msgid ""
"The percentage of the final production cost for this by-product line "
"(divided between the quantity produced).The total of all by-products' cost "
"share must be less than or equal to 100."
msgstr ""
"เปอร์เซ็นต์ของต้นทุนการผลิตขั้นสุดท้ายสำหรับไลน์ผลิตภัณฑ์พลอยได้นี้ "
"(หารด้วยปริมาณที่ผลิตได้) "
"ส่วนแบ่งต้นทุนรวมของผลิตภัณฑ์พลอยได้ทั้งหมดต้องน้อยกว่าหรือเท่ากับ 100"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__cost_share
msgid ""
"The percentage of the final production cost for this by-product. The total "
"of all by-products' cost share must be smaller or equal to 100."
msgstr ""
"เปอร์เซ็นต์ของต้นทุนการผลิตขั้นสุดท้ายสำหรับผลิตภัณฑ์พลอยได้นี้ "
"ส่วนแบ่งต้นทุนรวมของผลิตภัณฑ์พลอยได้ทั้งหมดต้องน้อยกว่าหรือเท่ากับ 100"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"The planned end date of the work order cannot be prior to the planned start "
"date, please correct this to save the work order."
msgstr ""
"วันที่สิ้นสุดตามแผนของคำสั่งงานไม่สามารถอยู่ก่อนวันที่เริ่มต้นที่วางแผนไว้ "
"โปรดแก้ไขให้ถูกต้องเพื่อบันทึกคำสั่งงาน"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"The product has already been used at least once, editing its structure may "
"lead to undesirable behaviours. You should rather archive the product and "
"create a new one with a new bill of materials."
msgstr ""
"สินค้ามีการใช้งานมาแล้วอย่างน้อยหนึ่งครั้ง "
"การแก้ไขโครงสร้างอาจทำให้เกิดพฤติกรรมที่ไม่พึงประสงค์ได้ "
"คุณควรเก็บถาวรสินค้าและสร้างสินค้าใหม่โดยใช้รายการวัสดุใหม่"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "The quantity produced of by-products must be positive."
msgstr "ปริมาณผลิตผลิตภัณฑ์พลอยได้ต้องเป็นบวก"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_qty_positive
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_qty_positive
#, python-format
msgid "The quantity to produce must be positive!"
msgstr "จำนวนที่จะผลิตต้องเป็นบวก"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__res_id
msgid "The record id this is attached to."
msgstr "ไอดีบันทึกนี้แนบมา"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid ""
"The selected serial number does not correspond to the one used in the "
"manufacturing order, please select another one."
msgstr ""

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The serial number %(number)s used for byproduct %(product_name)s has already"
" been produced"
msgstr ""
"หมายเลขซีเรียล %(number)s ที่ใช้สำหรับผลิตภัณฑ์พลอยได้%(product_name)s "
"ถูกผลิตแล้ว"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The serial number %(number)s used for component %(component)s has already "
"been consumed"
msgstr ""
"หมายเลขซีเรียล%(number)s ที่ใช้สำหรับส่วนประกอบ%(component)sที่ถูกใช้ไปแล้ว"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_tag_tag_name_unique
msgid "The tag name must be unique."
msgstr "ชื่อแท็กต้องไม่ซ้ำกัน"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "The total cost share for a BoM's by-products cannot exceed 100."
msgstr "ส่วนแบ่งต้นทุนรวมสำหรับผลิตภัณฑ์พลอยได้ของ BoM ต้องไม่เกิน 100"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"The total cost share for a manufacturing order's by-products cannot exceed "
"100."
msgstr "ส่วนแบ่งต้นทุนรวมสำหรับผลิตภัณฑ์พลอยได้ของใบสั่งผลิตต้องไม่เกิน 100"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "The work order should have already been processed."
msgstr "คำสั่งงานควรได้รับการประมวลผลแล้ว"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__theme_template_id
msgid "Theme Template"
msgstr "เทมเพลตธีม"

#. module: mrp
#: code:addons/mrp/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "There are more Serial Numbers than the Quantity to Produce"
msgstr "มีหมายเลขซีเรียลมากกว่าจำนวนที่จะผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"There are no components to consume. Are you still sure you want to continue?"
msgstr "ไม่มีส่วนประกอบที่จะบริโภค คุณยังแน่ใจว่าต้องการดำเนินการต่อหรือไม่?"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid "There's no product move yet"
msgstr "ยังไม่มีการเคลื่อนย้ายสินค้า"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr "ฟิลด์นี้ใช้เพื่อกำหนดเขตเวลาที่ทรัพยากรจะทำงาน"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"ฟิลด์นี้ใช้เพื่อคำนวณระยะเวลาที่คาดไว้ของคำสั่งงานที่ศูนย์งานนี้ "
"ตัวอย่างเช่น หากคำสั่งงานใช้เวลาหนึ่งชั่วโมงและปัจจัยด้านประสิทธิภาพคือ 100%"
" ระยะเวลาที่คาดไว้จะเป็นหนึ่งชั่วโมง หากปัจจัยด้านประสิทธิภาพเท่ากับ 200% "
"อย่างไรก็ตาม ระยะเวลาที่คาดไว้จะเท่ากับ 30 นาที"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
msgid "This is a BoM of type Kit!"
msgstr "BoM ของประเภทอุปกรณ์!"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid ""
"This is the cost based on the BoM of the product. It is computed by summing "
"the costs of the components and operations needed to build the product."
msgstr ""
"นี่คือต้นทุนที่อิงตาม BoM ของสินค้า "
"คำนวณโดยการรวมต้นทุนของส่วนประกอบและการปฏิบัติการที่จำเป็นในการสร้างสินค้า"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "This is the cost defined on the product."
msgstr "นี่คือต้นทุนที่กำหนดไว้ในสินค้า"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                You can filter on the product to see all the past movements for the product."
msgstr ""
"เมนูนี้ช่วยให้คุณตรวจสอบย้อนกลับได้อย่างสมบูรณ์ของการปฏิบัติการคลังสินค้าคงคลังของสินค้าเฉพาะ\n"
"               คุณสามารถกรองสินค้าเพื่อดูการเคลื่อนไหวที่ผ่านมาทั้งหมดสำหรับสินค้า"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid "This production order has been created from Replenishment Report."
msgstr "ใบสั่งผลิตนี้ถูกสร้างขึ้นจากรายงานการเติมสินค้า"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "This serial number for product %s has already been produced"
msgstr "มีการผลิตหมายเลขซีเรียลสำหรับสินค้า %s นี้แล้ว"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__time_ids
msgid "Time"
msgstr "เวลา"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_efficiency
msgid "Time Efficiency"
msgstr "ประสิทธิภาพตามเวลา"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_ids
msgid "Time Logs"
msgstr "บันทึกเวลา"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Time Tracking"
msgstr "ติดตามเวลา"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Time Tracking: %(user)s"
msgstr "ติดตามเวลา: %(user)s"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_stop
msgid "Time in minutes for the cleaning."
msgstr "เวลาเป็นนาทีสำหรับการทำความสะอาด"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_start
msgid "Time in minutes for the setup."
msgstr "เวลาเป็นนาทีสำหรับการตั้งค่า"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid ""
"Time in minutes:- In manual mode, time used- In automatic mode, supposed "
"first time when there aren't any work orders yet"
msgstr ""
"เวลาเป็นนาที:- ในโหมดกำหนดเอง เวลาที่ใช้- ในโหมดอัตโนมัติ "
"ควรจะเป็นครั้งแรกที่ยังไม่มีคำสั่งงาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tz
msgid "Timezone"
msgstr "เขตเวลา"

#. module: mrp
#: model:digest.tip,name:mrp.digest_tip_mrp_0
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid "Tip: Use tablets in the shop to control manufacturing"
msgstr "เคล็ดลับ: ใช้แท็บเล็ตในร้านค้าเพื่อควบคุมการผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "To"
msgstr "ถึง"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__to_backorder
msgid "To Backorder"
msgstr "ออกคำสั่งล่วงหน้า"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__to_close
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Close"
msgstr "ที่ปิด"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_expected_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "To Consume"
msgstr "การบริโภค"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Do"
msgstr "ที่จะทำ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "To Launch"
msgstr "ที่จะเปิดตัว"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_immediate_production_line__to_immediate
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "To Process"
msgstr "ที่จะดำเนินการ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "To Produce"
msgstr "ผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today Activities"
msgstr "กิจกรรมวันนี้"

#. module: mrp
#: model_terms:product.product,description:mrp.product_product_wood_wear
#: model_terms:product.template,description:mrp.product_product_wood_wear_product_template
msgid "Top layer of a wood panel."
msgstr "ชั้นบนของแผ่นไม้"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Total Duration"
msgstr "ระยะเวลารวม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_late_count
msgid "Total Late Orders"
msgstr "คำสั่งล่าช้าทั้งหมด"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_pending_count
msgid "Total Pending Orders"
msgstr "คำสั่งที่รอดำเนินการทั้งหมด"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Qty"
msgstr "จำนวนรวม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_qty
msgid "Total Quantity"
msgstr "จำนวนรวม"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_progress_count
msgid "Total Running Orders"
msgstr "คำสั่งที่ทำงานอยู่ทั้งหมด"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Total To Consume"
msgstr "การบริโภคทั้งหมด"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Total duration"
msgstr "ระยะเวลารวม"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total expected duration"
msgstr "ระยะเวลาที่คาดหวังรวม"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_duration_expected
msgid "Total expected duration (in minutes)"
msgstr "ระยะเวลาที่คาดหวังรวม (เป็นนาที)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total real duration"
msgstr "ระยะเวลาจริงรวม"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_real_duration
msgid "Total real duration (in minutes)"
msgstr "ระยะเวลาจริงทั้งหมด (เป็นนาที)"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Traceability"
msgstr "การติดตามย้อนกลับ"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_traceability_report
msgid "Traceability Report"
msgstr "รายงานการติดตามาย้อนกลับ"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_tracking
msgid "Tracking"
msgstr "การติดตาม"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking
msgid "Transfer"
msgstr "โอน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Transfers"
msgstr "โอน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__type
msgid "Type"
msgstr "ประเภท"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "ประเภทการปฏิบัติการ"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "ประเภทกิจกรรมข้อยกเว้นบนบันทึก"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Unable to split with more than the quantity to produce."
msgstr "ไม่สามารถแบ่งได้มากกว่าจำนวนที่ผลิตได้"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Unblock"
msgstr "เลิกบล็อก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__unbuild_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild"
msgstr "รื้อ"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild Order"
msgstr "คำสั่งรื้อ"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "Unbuild Order product quantity has to be strictly positive."
msgstr "จำนวนสินค้าที่ถูกรื้อจำเป็นต้องเป็นค่าบวก"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_unbuild
#: model:ir.ui.menu,name:mrp.menu_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild Orders"
msgstr "คำสั่งรื้อ"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "Unbuild: %s"
msgstr "รื้อ: %s"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_line
msgid "Unfold"
msgstr "กางออก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unit_factor
msgid "Unit Factor"
msgstr "หน่วยปัจจัย"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_uom_name
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Unit of Measure"
msgstr "หน่วยวัด"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_id
msgid ""
"Unit of Measure (Unit of Measure) is the unit of measurement for the "
"inventory control"
msgstr "หน่วยวัด (หน่วยวัด) คือหน่วยวัดสำหรับการควบคุมคลังสินค้า"

#. module: mrp
#: model:product.product,uom_name:mrp.product_product_computer_desk
#: model:product.product,uom_name:mrp.product_product_computer_desk_bolt
#: model:product.product,uom_name:mrp.product_product_computer_desk_head
#: model:product.product,uom_name:mrp.product_product_computer_desk_leg
#: model:product.product,uom_name:mrp.product_product_computer_desk_screw
#: model:product.product,uom_name:mrp.product_product_drawer_case
#: model:product.product,uom_name:mrp.product_product_drawer_drawer
#: model:product.product,uom_name:mrp.product_product_plastic_laminate
#: model:product.product,uom_name:mrp.product_product_ply_veneer
#: model:product.product,uom_name:mrp.product_product_table_kit
#: model:product.product,uom_name:mrp.product_product_wood_panel
#: model:product.product,uom_name:mrp.product_product_wood_ply
#: model:product.product,uom_name:mrp.product_product_wood_wear
#: model:product.template,uom_name:mrp.product_product_computer_desk_bolt_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_head_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_leg_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_product_template
#: model:product.template,uom_name:mrp.product_product_computer_desk_screw_product_template
#: model:product.template,uom_name:mrp.product_product_drawer_case_product_template
#: model:product.template,uom_name:mrp.product_product_drawer_drawer_product_template
#: model:product.template,uom_name:mrp.product_product_plastic_laminate_product_template
#: model:product.template,uom_name:mrp.product_product_ply_veneer_product_template
#: model:product.template,uom_name:mrp.product_product_table_kit_product_template
#: model:product.template,uom_name:mrp.product_product_wood_panel_product_template
#: model:product.template,uom_name:mrp.product_product_wood_ply_product_template
#: model:product.template,uom_name:mrp.product_product_wood_wear_product_template
msgid "Units"
msgstr "หน่วย"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unlock"
msgstr "ปลดล็อก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_unlocked_by_default
msgid "Unlock Manufacturing Orders"
msgstr "ปลดล็อกใบสั่งผลิต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"Unlock the manufacturing order to adjust what has been consumed or produced."
msgstr "ปลดล็อกใบสั่งผลิตเพื่อปรับสิ่งที่ใช้หรือผลิต"

#. module: mrp
#: model:res.groups,name:mrp.group_unlocked_by_default
msgid "Unlocked by default"
msgstr "ปลดล็อกโดยค่าเริ่มต้น"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unplan"
msgstr "ไม่ได้วางแผน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_unread
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_unread
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_unread
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_unread_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_unread_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_unread_counter
msgid "Unread Messages Counter"
msgstr "ตัวนับข้อความที่ยังไม่ได้อ่าน"

#. module: mrp
#: model:ir.actions.server,name:mrp.mrp_production_action_unreserve_tree
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_product_product_replenishment
msgid "Unreserve"
msgstr "ยกเลิกการสำรอง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "UoM"
msgstr "หน่วยวัด"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp_document_template.xml:0
#, python-format
msgid "Upload"
msgstr "อัปโหลด"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Upload your PDF file."
msgstr "อัปโหลดไฟล์ PDF ของคุณ"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__1
msgid "Urgent"
msgstr "เร่งด่วน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__url
msgid "Url"
msgstr "Url"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Used In"
msgstr "ใช้ใน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__user_id
#: model:res.groups,name:mrp.group_mrp_user
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "User"
msgstr "ผู้ใช้งาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Using a MPS report to schedule your reordering and manufacturing operations "
"is useful if you have long lead time and if you produce based on sales "
"forecasts."
msgstr ""
"การใช้รายงาน MPS "
"เพื่อจัดกำหนดการการเติมสต๊อกและการปฏิบัติการผลิตจะมีประโยชน์หากคุณใช้เวลานำสินค้านานและหากคุณผลิตตามการพยากรณ์ยอดขาย"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Validate"
msgstr "ตรวจสอบ"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/xml/mrp.xml:0
#, python-format
msgid "Variant:"
msgstr "ตัวแปร:"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_document__priority__3
msgid "Very High"
msgstr "สูงมาก"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Waiting"
msgstr "รอ"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__waiting
msgid "Waiting Another Operation"
msgstr "กำลังรอการปฏิบัติการอื่น"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Waiting Availability"
msgstr "กำลังรอความพร้อม"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp.js:0
#, python-format
msgid "Waiting Materials"
msgstr "กำลังรอวัสดุ"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__pending
msgid "Waiting for another WO"
msgstr "กำลังรอ WO อื่น "

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__waiting
msgid "Waiting for components"
msgstr "กำลังรอส่วนประกอบ"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "Waiting the previous work order, planned from %(start)s to %(end)s"
msgstr "รอคำสั่งงานก่อนหน้า วางแผนจาก %(start)s ถึง %(end)s"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse
msgid "Warehouse"
msgstr "คลังสินค้า"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warn_insufficient_qty_unbuild
msgid "Warn Insufficient Unbuild Quantity"
msgstr "คำเตือนจำนวนการรื้อไม่เพียงพอ"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0 code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/stock_scrap.py:0
#, python-format
msgid "Warning"
msgstr "คำเตือน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Warnings"
msgstr "คำเตือน"

#. module: mrp
#: model:product.product,name:mrp.product_product_wood_wear
#: model:product.template,name:mrp.product_product_wood_wear_product_template
msgid "Wear Layer"
msgstr "ใส่ชั้น"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__website_id
msgid "Website"
msgstr "เว็บไซต์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_document__website_url
msgid "Website URL"
msgstr "เว็บไซต์ URL"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__picking_type_id
msgid ""
"When a procurement has a ‘produce’ route with a operation type set, it will "
"try to create a Manufacturing Order for that product using a BoM of the same"
" operation type. That allows to define stock rules which trigger different "
"manufacturing orders with different BoMs."
msgstr ""
"เมื่อการจัดซื้อมีเส้นทาง 'ผลิต' ที่มีชุดประเภทการปฏิบัติการแล้ว "
"จะพยายามสร้างใบสั่งผลิตสำหรับสินค้านั้นโดยใช้ BoM "
"ของประเภทการปฏิบัติการเดียวกัน "
"ที่อนุญาตให้กำหนดกฎสต๊อกที่เปิดใช้คำสั่งการผลิตที่แตกต่างกันด้วย BoM "
"ที่แตกต่างกัน"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__asap
msgid "When components for 1st operation are available"
msgstr "เมื่อมีส่วนประกอบสำหรับการปฏิบัติการครั้งแรก"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_to_resupply
msgid ""
"When products are manufactured, they can be manufactured in this warehouse."
msgstr "เมื่อสินค้าถูกผลิตขึ้น ก็สามารถผลิตได้ในคลังสินค้านี้"

#. module: mrp
#: code:addons/mrp/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> a manufacturing order is "
"created to fulfill the need."
msgstr ""
"เมื่อต้องการสินค้าใน<b>%s</b> "
"<br/>มีการสร้างใบสั่งผลิตเพื่อตอบสนองความต้องการ"

#. module: mrp
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid ""
"With the Odoo work center control panel, your worker can start work orders "
"in the shop and follow instructions of the worksheet. Quality tests are "
"perfectly integrated into the process. Workers can trigger feedback loops, "
"maintenance alerts, scrap products, etc."
msgstr ""
"ด้วยแผงควบคุมศูนย์งานของ Odoo "
"พนักงานของคุณสามารถเริ่มคำสั่งงานในร้านค้าและปฏิบัติตามคำแนะนำของแผ่นงานได้ "
"การทดสอบคุณภาพถูกรวมเข้ากับกระบวนการอย่างสมบูรณ์แบบ "
"ผู้ปฏิบัติงานสามารถเปิดใช้งานวงจรการตอบกลับ การแจ้งเตือนการบำรุงรักษา "
"เศษซากผลิตภัณฑ์ และ อื่น ๆ "

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr ""
"ตัวช่วยในกรณีการบริโภคสำหรับการเตือน / จำกัดและส่วนประกอบอื่น ๆ "
"จะถูกนำมาใช้สำหรับ MO (ที่เกี่ยวข้องกับ bom)"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder
msgid "Wizard to mark as done or create back order"
msgstr "ตัวช่วยเพื่อทำเครื่องหมายว่าเสร็จสิ้นหรือสร้างคำสั่งล่วงหน้า"

#. module: mrp
#: model:product.product,name:mrp.product_product_wood_panel
#: model:product.template,name:mrp.product_product_wood_panel_product_template
msgid "Wood Panel"
msgstr "แผ่นไม้"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Work Center"
msgstr "ศูนย์งาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workcenter_load
msgid "Work Center Load"
msgstr "โหลดศูนย์งาน"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Work Center Loads"
msgstr "โหลดศูนย์งาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Work Center Name"
msgstr "ชื่อศูนย์งาน"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "การใช้ศูนย์งาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
msgid "Work Center load"
msgstr "โหลดศูนย์งาน"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_action
#: model:ir.ui.menu,name:mrp.menu_view_resource_search_mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Centers"
msgstr "ศูนย์งาน"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_kanban_action
msgid "Work Centers Overview"
msgstr "ภาพรวมศูนย์งาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Work Instruction"
msgstr "คำแนะนำการทำงาน"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workorder_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__name
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__workorder_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__workorder_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Work Order"
msgstr "คำสั่งงาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Work Order Operations allow you to create and manage the manufacturing "
"operations that should be followed within your work centers in order to "
"produce a product. They are attached to bills of materials that will define "
"the required components."
msgstr ""
"การปฏิบัติการคำสั่งงานช่วยให้คุณสร้างและจัดการการปฏิบัติการด้านการผลิตที่ควรปฏิบัติตามภายในศูนย์งานของคุณเพื่อผลิตสินค้า"
" ซึ่งแนบมากับบิลวัสดุที่จะกำหนดส่วนประกอบที่จำเป็น"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__workorder_id
msgid "Work Order To Consume"
msgstr "คำสั่งงานที่ใช้"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_routing_time
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production_specific
#: model:ir.actions.act_window,name:mrp.action_work_orders
#: model:ir.actions.act_window,name:mrp.mrp_workorder_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_workorder_report
#: model:ir.actions.act_window,name:mrp.mrp_workorder_todo
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_workorder
#: model:ir.ui.menu,name:mrp.menu_mrp_work_order_report
#: model:ir.ui.menu,name:mrp.menu_mrp_workorder_todo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Orders"
msgstr "คำสั่งงาน"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workorder_workcenter_report
msgid "Work Orders Performance"
msgstr "ประสิทธิภาพคำสั่งงาน"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_workcenter
msgid "Work Orders Planning"
msgstr "การวางแผนคำสั่งงาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_type
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Work Sheet"
msgstr "ใบงาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Work center"
msgstr "ศูนย์งาน"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"                    Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"คำสั่งงานคือการปฏิบัติการที่ต้องทำโดยเป็นส่วนหนึ่งของใบสั่งผลิต\n"
"                    การปฏิบัติการถูกกำหนดไว้ในบิลรวัสดุหรือเพิ่มในใบสั่งผลิตโดยตรง"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"คำสั่งงานคือปฏิบัติการที่ต้องทำโดยเป็นส่วนหนึ่งของใบสั่งผลิต\n"
"         การปฏิบัติการถูกกำหนดไว้ในบิลวัสดุหรือเพิ่มในใบสั่งผลิตโดยตรง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Work orders in progress. Click to block work center."
msgstr "อยู่ระหว่างคำสั่งงาน คลิกเพื่อบล็อกศูนย์งาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Workcenter"
msgstr "ศูนย์งาน"

#. module: mrp
#: code:addons/mrp/models/mrp_workcenter.py:0
#, python-format
msgid "Workcenter %s cannot be an alternative of itself."
msgstr "ศูนย์งาน%sไม่สามารถเป็นตัวเลือกของตัวเอง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Workcenter Productivity"
msgstr "ผลผลิตของศูนย์งาน"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "บันทึกผลผลิตของศูนย์งาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Workcenter Productivity Loss"
msgstr "การสูญเสียผลผลิตของศูนย์งาน"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss
msgid "Workcenter Productivity Losses"
msgstr "การสูญเสียผลผลิตของศูนย์งาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__working_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_state
msgid "Workcenter Status"
msgstr "สถานะศูนย์งาน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Workcenter blocked, click to unblock."
msgstr "ศุนย์งานถูกบล็อก คลิกเพื่อปลดบล็อก"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Working Hours"
msgstr "ชั่วโมงทำงาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_user_ids
msgid "Working user on this work order."
msgstr "ผู้ใช้ที่ทำงานในคำสั่งงานนี้"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet
msgid "Worksheet"
msgstr "แผ่นงาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_type
msgid "Worksheet Type"
msgstr "ประเภทแผ่นงาน"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_google_slide
msgid "Worksheet URL"
msgstr "URL แผ่นงาน"

#. module: mrp
#: code:addons/mrp/models/stock_production_lot.py:0
#, python-format
msgid ""
"You are not allowed to create or edit a lot or serial number for the "
"components with the operation type \"Manufacturing\". To change this, go on "
"the operation type and tick the box \"Create New Lots/Serial Numbers for "
"Components\"."
msgstr ""
"คุณไม่ได้รับอนุญาตให้สร้างหรือแก้ไขล็อตหรือหมายเลขซีเรียลสำหรับส่วนประกอบที่มีประเภทการปฏิบัติการ"
" \"การผลิต\" หากต้องการเปลี่ยนแปลง "
"ให้ไปที่ประเภทการปฏิบัติการและทำเครื่องหมายในช่อง "
"\"สร้างล็อตใหม่/หมายเลขซีเรียลสำหรับส่วนประกอบ\""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"คุณสามารถอัปโหลดไฟล์จากคอมพิวเตอร์ของคุณ "
"หรือคัดลอก/วางอินเตอร์เน็ตลิงก์ไปยังไฟล์ของคุณ"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You can not create a kit-type bill of materials for products that have at "
"least one reordering rule."
msgstr ""
"คุณไม่สามารถสร้างบิลวัสดุประเภทชุดอุปกรณ์สำหรับสินค้าที่มีกฎการเติมสต๊อกอย่างน้อยหนึ่งกฎ"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You can not delete a Bill of Material with running manufacturing orders.\n"
"Please close or cancel it first."
msgstr ""
"คุณไม่สามารถลบบิลวัสดุที่มีใบสั่งผลิตอยู่ได้\n"
"กรุณาปิดหรือยกเลิกก่อน"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You cannot change the workcenter of a work order that is in progress or "
"done."
msgstr ""
"คุณไม่สามารถเปลี่ยนศูนย์งานของคำสั่งงานที่กำลังดำเนินการหรือเสร็จสิ้นได้"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid "You cannot create a new Bill of Material from here."
msgstr "คุณไม่สามารถสร้างบิลวัสดุใหม่จากที่นี่"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You cannot delete an unbuild order if the state is 'Done'."
msgstr "คุณไม่สามารถลบคำสั่งรื้อได้หากสถานะเป็น 'เสร็จสิ้น'"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "You cannot have %s  as the finished product and in the Byproducts"
msgstr "คุณไม่สามารถมี %s เป็นสินค้าสำเร็จรูปและเป็นสินค้าพลอยได้"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You cannot link this work order to another manufacturing order."
msgstr "คุณไม่สามารถเชื่อมโยงคำสั่งงานนี้กับใบสั่งผลิตอื่นได้"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "You cannot move a manufacturing order once it is cancelled or done."
msgstr "คุณไม่สามารถย้ายใบสั่งผลิตที่ถูกยกเลิกหรือเสร็จสิ้นแล้ว"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You cannot produce the same serial number twice."
msgstr "คุณไม่สามารถใช้หมายเลขซีเรียลซ้ำสองครั้งได้"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You cannot unbuild a undone manufacturing order."
msgstr "คุณไม่สามารถรื้อใบสั่งผลิตที่ยังไม่เสร็จได้"

#. module: mrp
#: code:addons/mrp/models/mrp_bom.py:0
#, python-format
msgid ""
"You cannot use the 'Apply on Variant' functionality and simultaneously "
"create a BoM for a specific variant."
msgstr ""
"คุณไม่สามารถใช้ฟังก์ชัน 'ใช้ในตัวแปร' และสร้าง BoM "
"สำหรับตัวแปรเฉพาะได้พร้อมกัน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid ""
"You consumed a different quantity than expected for the following products.\n"
"                        <b attrs=\"{'invisible': [('consumption', '=', 'strict')]}\">\n"
"                            Please confirm it has been done on purpose.\n"
"                        </b>\n"
"                        <b attrs=\"{'invisible': [('consumption', '!=', 'strict')]}\">\n"
"                            Please review your component consumption or ask a manager to validate \n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '!=', 1)]}\">this manufacturing order</span>\n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '=', 1)]}\">these manufacturing orders</span>.\n"
"                        </b>"
msgstr ""
"คุณใช้จำนวนที่แตกต่างจากที่คาดไว้สำหรับสินค้าต่อไปนี้\n"
"                        <b attrs=\"{'invisible': [('consumption', '=', 'strict')]}\">\n"
"                            โปรดยืนยันว่ากระทำโดยตั้งใจ\n"
"                        </b>\n"
"                        <b attrs=\"{'invisible': [('consumption', '!=', 'strict')]}\">\n"
"                            โปรดตรวจสอบการใช้ส่วนประกอบของคุณหรือขอให้ผู้จัดการตรวจสอบ \n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '!=', 1)]}\">ใบสั่งผลิตนี้</span>\n"
"                            <span attrs=\"{'invisible':[('mrp_production_count', '=', 1)]}\">ใบสั่งผลิตเหล่านี้</span>\n"
"                        </b>"

#. module: mrp
#: code:addons/mrp/wizard/change_production_qty.py:0
#, python-format
msgid ""
"You have already processed %(quantity)s. Please input a quantity higher than"
" %(minimum)s "
msgstr ""
"คุณได้ดำเนินการ%(quantity)sแล้ว  กรุณานำเข้าจำนวนที่มากกว่า %(minimum)s "

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid ""
"You have entered less serial numbers than the quantity to produce.<br/>\n"
"                        Create a backorder if you expect to process the remaining quantities later.<br/>\n"
"                        Do not create a backorder if you will not process the remaining products."
msgstr ""
"คุณป้อนหมายเลขซีเรียลน้อยกว่าจำนวนที่จะผลิต<br/>\n"
"                        สร้างรายการค้างส่งหากคุณคาดว่าจะดำเนินการกับจำนวนที่เหลือในภายหลัง<br/>\n"
"                        อย่าสร้างสินค้าค้างส่งหากคุณจะไม่ดำเนินการกับสินค้าที่เหลืออยู่"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_immediate_production
msgid ""
"You have not recorded <i>produced</i> quantities yet, by clicking on "
"<i>apply</i> Odoo will produce all the finished products and consume all "
"components."
msgstr ""
"คุณยังไม่ได้บันทึก <i>จำนวนที่ผลิต</i> กดปุ่ม <i>ใช้</i> เพื่อที่ Odoo "
"จะผลิตสินค้าสำเร็จรูปและใช้ส่วนประกอบ"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid ""
"You must indicate a non-zero amount consumed for at least one of your "
"components"
msgstr ""
"คุณต้องระบุจำนวนที่ไม่เป็นศูนย์สำหรับส่วนประกอบของคุณอย่างน้อยหนึ่งรายการ"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You need to define at least one productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"คุณต้องกำหนดการสูญเสียผลิตผลอย่างน้อยหนึ่งรายการในหมวดหมู่ 'ประสิทธิภาพ' "
"สร้างหนึ่งรายการจากแอปการผลิต เมนู: การกำหนดค่า / การสูญเสียผลผลิต"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You need to define at least one productivity loss in the category "
"'Productivity'. Create one from the Manufacturing app, menu: Configuration /"
" Productivity Losses."
msgstr ""
"คุณต้องกำหนดการสูญเสียผลผลิตอย่างน้อยหนึ่งรายการในหมวดหมู่ 'ผลผลิต' "
"สร้างหนึ่งรายการจากแอปการผลิต เมนู: การกำหนดค่า / การสูญเสียผลผลิต"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid ""
"You need to define at least one unactive productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr ""
"คุณต้องกำหนดการสูญเสียผลผลิตที่ไม่ได้ใช้งานอย่างน้อยหนึ่งรายการในหมวดหมู่ "
"'ประสิทธิภาพ' สร้างหนึ่งรายการจากแอปการผลิต เมนู: การกำหนดค่า / "
"การสูญเสียผลผลิต"

#. module: mrp
#: code:addons/mrp/models/mrp_workorder.py:0
#, python-format
msgid "You need to provide a lot for the finished product."
msgstr "คุณต้องจัดเตรียมล็อตสำหรับสินค้าสำเร็จรูป"

#. module: mrp
#: code:addons/mrp/wizard/mrp_immediate_production.py:0
#, python-format
msgid "You need to supply Lot/Serial Number for products:"
msgstr "คุณต้องระบุ ล็อต/หมายเลขซีเรียล สำหรับสินค้า:"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_backorder
msgid "You produced less than initial demand"
msgstr "คุณผลิตได้น้อยกว่าจำนวนความต้องการเริ่มต้น"

#. module: mrp
#: code:addons/mrp/models/mrp_unbuild.py:0
#, python-format
msgid "You should provide a lot number for the final product."
msgstr "คุณควรระบุหมายเลขล็อตสำหรับสินค้าขั้นสุดท้าย"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "and build finished products using"
msgstr "และสร้างสินค้าสำเร็จรูปโดยใช้"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "bills of materials"
msgstr "บิลของวัสดุ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "cancelled"
msgstr "ยกเลิกแล้ว"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "components"
msgstr "ส่วนประกอบ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_assign_serial_numbers_production
msgid "copy paste a list and/or use Generate"
msgstr "คัดลอกวางรายการและ/หรือใช้สร้าง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "days"
msgstr "วัน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "expected duration"
msgstr "ระยะเวลาที่คาดหวัง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "from location"
msgstr "จากตำแหน่ง"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "last"
msgstr "ล่าสุด"

#. module: mrp
#: code:addons/mrp/models/mrp_production.py:0
#, python-format
msgid "manufacturing order"
msgstr "คำสั่งผลิต"

#. module: mrp
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#, python-format
msgid "minutes"
msgstr "นาที"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "of"
msgstr "ของ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_document_form
msgid "on"
msgstr "บน"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "ordered instead of"
msgstr "คำสั่งแทนที่ของ"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "quantity has been updated."
msgstr "จำนวนที่ได้รับการอัปเดต"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "real duration"
msgstr "ระยะเวลาจริง"

#. module: mrp
#. openerp-web
#: code:addons/mrp/static/src/js/mrp_documents_controller_mixin.js:0
#, python-format
msgid "status code: %s, message: %s"
msgstr "โค้ดสถานะ: %s ข้อความ: %s"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "work orders"
msgstr "คำสั่งงาน"
