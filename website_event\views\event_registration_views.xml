<?xml version="1.0"?>
<odoo><data>
    <record id="event_registration_action_from_visitor" model="ir.actions.act_window">
        <field name="name">Registrations</field>
        <field name="res_model">event.registration</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[('visitor_id', 'in', [active_id])]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No registration linked to this visitor
            </p>
        </field>
    </record>

    <record id="event_registration_view_form" model="ir.ui.view">
        <field name="name">event.registration.view.form.inherit.online</field>
        <field name="model">event.registration</field>
        <field name="inherit_id" ref="event.view_event_registration_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="visitor_id" groups="base.group_no_one"/>
            </xpath>
        </field>
    </record>

    <record id="event_registration_view_tree" model="ir.ui.view">
        <field name="name">event.registration.view.tree.inherit.online</field>
        <field name="model">event.registration</field>
        <field name="inherit_id" ref="event.view_event_registration_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="visitor_id" optional="hide" groups="base.group_no_one"/>
            </xpath>
        </field>
    </record>
</data></odoo>
