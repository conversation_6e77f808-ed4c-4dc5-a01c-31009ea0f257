<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="ir.ui.view" id="res_config_settings_view_form">
        <field name="name">res.config.settings.view.form.inherit.l10n_sa_edi</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@data-key='account']/div" position="after">
                <field name="country_code" invisible="1"/>
                <h2 attrs="{'invisible':[('country_code', '!=', 'SA')]}">ZATCA E-Invoicing Settings</h2>
                <div class="row mt16 o_settings_container" name="saudi_zatca_edi" attrs="{'invisible':[('country_code', '!=', 'SA')]}">
                    <div class="col-12 o_setting_box">
                        <div class="o_setting_left_pane"/>
                        <div class="o_setting_right_pane">
                            <span class="o_form_label">ZATCA API Integration</span>
                            <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." aria-label="Values set here are company-specific." groups="base.group_multi_company" role="img"/>
                            <div class="text-muted">
                                You can select the API used for submissions down below. There are three modes available: Sandbox, Pre-Production and Production.
                                Once you have selected the correct API, you can start the Onboarding process by going to the Journals and checking the options under the ZATCA tab.
                            </div>
                            <div class="content-group">
                                 <div class="row mt8">
                                    <label for="l10n_sa_api_mode" class="col-2 o_light_label" string="API Mode"/>
                                    <field name="l10n_sa_api_mode" help="Set whether the system should use the Production API"/>
                                </div>
                            </div>
                            <div class="alert alert-warning mt8" role="alert">
                                <h4 class="alert-heading" role="alert">
                                    <i class="fa fa-warning mr-2" /> Warning
                                </h4>
                                Once you change the submission mode to <strong>Production</strong>, you cannot change it anymore.
                                Be very careful, as any invoice submitted to ZATCA in Production mode will be accounted for
                                and might lead to <strong>Fines &amp; Penalties</strong>.
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

</odoo>
