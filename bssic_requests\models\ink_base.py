from odoo import models, fields, api, _
from odoo.exceptions import UserError
from lxml import etree


class InkBase(models.AbstractModel):
    """Base model for ink requests with common fields and methods"""
    _name = 'bssic.ink.base'
    _description = 'Ink Request Base'
    _order = 'request_date desc, id desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char('Request Number', required=True, copy=False, readonly=True,
                      default=lambda self: _('New'))

    # Employee fields
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                   tracking=True, ondelete="restrict", default=lambda self: self._get_default_employee())
    employee_number = fields.Char(string='Employee Number (ID)', tracking=True,
                                help="Enter employee ID number to automatically fetch employee details")
    department_id = fields.Many2one(related='employee_id.department_id',
                                   string='Department', store=True, readonly=True)
    job_id = fields.Many2one(related='employee_id.job_id', string='Job Position',
                            store=True, readonly=True)

    # Request basic info
    request_date = fields.Date('Request Date', default=fields.Date.context_today,
                              required=True, tracking=True)

    # State and workflow
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager', 'Direct Manager Approval'),
        ('warehouse_approval', 'Warehouse Manager Approval'),
        ('hr_approval', 'HR Approval'),
        ('pending_receipt', 'Pending Receipt Confirmation'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)

    # Ink request lines
    printer_ink_line_ids = fields.One2many('bssic.printer.ink.line', 'request_id', string='Printer Ink Items')
    copier_ink_line_ids = fields.One2many('bssic.copier.ink.line', 'request_id', string='Copier Ink Items')

    # Approval fields
    direct_manager_id = fields.Many2one('hr.employee', string='Direct Manager',
                                       related='employee_id.parent_id', store=True, readonly=True)
    warehouse_manager_id = fields.Many2one('hr.employee', string='Warehouse Manager', tracking=True)
    hr_manager_id = fields.Many2one('hr.employee', string='HR Manager', tracking=True)
    rejection_reason = fields.Text('Rejection Reason', tracking=True)
    notes = fields.Text('Notes', tracking=True)

    # Approval logs
    submission_user_id = fields.Many2one('res.users', string='Submitted by', readonly=True, tracking=True)
    submission_date = fields.Datetime(string='Submission Date', readonly=True, tracking=True)
    direct_manager_approval_user_id = fields.Many2one('res.users', string='Approved by (Manager)', readonly=True, tracking=True)
    direct_manager_approval_date = fields.Datetime(string='Manager Approval Date', readonly=True, tracking=True)
    warehouse_approval_user_id = fields.Many2one('res.users', string='Approved by (Warehouse)', readonly=True, tracking=True)
    warehouse_approval_date = fields.Datetime(string='Warehouse Approval Date', readonly=True, tracking=True)
    hr_approval_user_id = fields.Many2one('res.users', string='Approved by (HR)', readonly=True, tracking=True)
    hr_approval_date = fields.Datetime(string='HR Approval Date', readonly=True, tracking=True)
    receipt_confirmation_user_id = fields.Many2one('res.users', string='Receipt Confirmed by', readonly=True, tracking=True)
    receipt_confirmation_date = fields.Datetime(string='Receipt Confirmation Date', readonly=True, tracking=True)
    receipt_notes = fields.Text('Receipt Notes', tracking=True)

    # Computed permission fields
    is_manager = fields.Boolean(string='Is Manager', compute='_compute_is_manager', store=False)
    is_hr_manager = fields.Boolean(string='Is HR Manager', compute='_compute_is_hr_manager', store=False)
    is_warehouse_manager = fields.Boolean(string='Is Warehouse Manager', compute='_compute_is_warehouse_manager', store=False)

    # Activity Log
    activity_log_ids = fields.One2many('bssic.request.activity.log', 'ink_request_id', string='Activity Log')

    @api.constrains('printer_ink_line_ids', 'copier_ink_line_ids')
    def _check_line_ids(self):
        """Check that the request has at least one ink item when saving"""
        for record in self:
            if record.state != 'draft' and not record.printer_ink_line_ids and not record.copier_ink_line_ids:
                raise UserError(_('You cannot save a request without any ink items.'))

    @api.depends('employee_id')
    def _compute_is_manager(self):
        """Check if current user is the direct manager of the employee"""
        for record in self:
            current_user = self.env.user
            employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
            record.is_manager = (employee and record.employee_id.parent_id == employee)

    @api.depends()
    def _compute_is_hr_manager(self):
        """Check if current user is HR manager"""
        for record in self:
            record.is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')

    @api.depends()
    def _compute_is_warehouse_manager(self):
        """Check if current user is warehouse manager"""
        for record in self:
            record.is_warehouse_manager = self.env.user.has_group('bssic_requests.group_bssic_warehouse_manager')

    @api.onchange('employee_number')
    def _onchange_employee_number(self):
        """Handle employee number change with permission checks"""
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        current_user = self.env.user

        if self.employee_number:
            employee = self.env['hr.employee'].search([('int_id', '=', self.employee_number)], limit=1)

            if employee:
                if is_manager:
                    manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if (employee.id == manager_employee.id or
                        (employee.department_id.id == manager_employee.department_id.id and
                         employee.parent_id.id == manager_employee.id)):
                        self.employee_id = employee.id
                    else:
                        if manager_employee.int_id:
                            self.employee_number = manager_employee.int_id
                        self.employee_id = manager_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only select employees in your department who report to you.')
                        }}
                else:
                    user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if employee.id == user_employee.id:
                        self.employee_id = employee.id
                    else:
                        if user_employee.int_id:
                            self.employee_number = user_employee.int_id
                        self.employee_id = user_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only create requests for yourself.')
                        }}
            else:
                self.employee_id = False
                return {'warning': {
                    'title': _('Warning'),
                    'message': _('No employee found with this employee number.')
                }}

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        """Check permissions when employee_id is changed directly"""
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        current_user = self.env.user

        if self.employee_id:
            if is_manager:
                manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                if (self.employee_id.id == manager_employee.id or
                    (self.employee_id.department_id.id == manager_employee.department_id.id and
                     self.employee_id.parent_id.id == manager_employee.id)):
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    self.employee_id = manager_employee
                    if manager_employee.int_id:
                        self.employee_number = manager_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only select employees in your department who report to you.')
                    }}
            else:
                user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                if self.employee_id.id == user_employee.id:
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    self.employee_id = user_employee
                    if user_employee.int_id:
                        self.employee_number = user_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only create requests for yourself.')
                    }}

    @api.model
    def default_get(self, fields_list):
        """Override default_get to set default employee and employee_number"""
        defaults = super(InkBase, self).default_get(fields_list)

        # Get current user's employee
        employee = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
        if employee:
            if 'employee_id' in fields_list:
                defaults['employee_id'] = employee.id
            if 'employee_number' in fields_list:
                defaults['employee_number'] = employee.int_id

        return defaults

    @api.model
    def fields_view_get(self, view_id=None, view_type='form', toolbar=False, submenu=False):
        """Override to dynamically set readonly attribute for approved_quantity field and employee_id domain"""
        res = super(InkBase, self).fields_view_get(view_id=view_id, view_type=view_type, toolbar=toolbar, submenu=submenu)

        # Only modify form view
        if view_type == 'form':
            doc = etree.XML(res['arch'])

            # Set domain for employee_id field
            for node in doc.xpath("//field[@name='employee_id']"):
                # Check if user is a manager
                is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
                current_user = self.env.user

                if is_manager:
                    # Get manager's employee record
                    manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if manager_employee:
                        # Get all employees in the same department that report to this manager
                        department_employees = self.env['hr.employee'].search([
                            '|',
                            ('id', '=', manager_employee.id),  # Include the manager
                            '&',
                            ('department_id', '=', manager_employee.department_id.id),  # Same department
                            ('parent_id', '=', manager_employee.id)  # Reports to this manager
                        ])
                        node.set('domain', str([('id', 'in', department_employees.ids)]))
                else:
                    # Regular employee can only select themselves
                    employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if employee:
                        node.set('domain', str([('id', '=', employee.id)]))
                    else:
                        node.set('domain', str([('id', '=', -1)]))  # No employee should match this domain

            res['arch'] = etree.tostring(doc, encoding='unicode')
        return res

    def unlink(self):
        """Prevent deletion of non-draft requests"""
        for record in self:
            if record.state != 'draft':
                raise UserError(_('You cannot delete a request that has been submitted.'))
        return super(InkBase, self).unlink()

    def _get_default_employee(self):
        """Get the default employee for the current user"""
        employee = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
        return employee.id if employee else False
