// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_ChatWindowHiddenMenu {
    position: fixed;
    bottom: 0;
    display: flex;
    width: 50px;
    height: 28px;
    align-items: stretch;
}

.o_ChatWindowHiddenMenu_chatWindowHeader {
    max-width: 200px;
}

.o_ChatWindowHiddenMenu_dropdownToggle {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1 1 auto;
    max-width: map-get($sizes, 100);
}

.o_ChatWindowHiddenMenu_dropdownToggleItem {
    margin: map-get($spacers, 0) map-get($spacers, 1);
}

.o_ChatWindowHiddenMenu_list {
    overflow: auto;
    margin: map-get($spacers, 0);
    padding: map-get($spacers, 0);
}

.o_ChatWindowHiddenMenu_listItem {

    &:not(:last-child) {
        border-bottom: $border-width solid $border-color;
    }
}

.o_ChatWindowHiddenMenu_unreadCounter {
    position: absolute;
    right: 0;
    top: 0;
    transform: translate(50%, -50%);
    z-index: 1001; // on top of bootstrap dropup menu
}

.o_ChatWindowHiddenMenu_windowCounter {
    margin-left: map-get($spacers, 1);
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_ChatWindowHiddenMenu {
    background-color: gray('900');
    border-radius: 6px 6px 0 0;
    color: $white;
    cursor: pointer;
}

.o_ChatWindowHiddenMenu_chatWindowHeader {
    opacity: 0.95;

    &:hover {
        opacity: 1;
    }
}

.o_ChatWindowHiddenMenu_dropdownToggle.show {
    opacity: 0.5;
}

.o_ChatWindowHiddenMenu_unreadCounter {
    background-color: $o-brand-primary;
}

.o_ChatWindowHiddenMenu_windowCounter {
    user-select: none;
}
