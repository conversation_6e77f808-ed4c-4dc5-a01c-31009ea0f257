# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock
# 
# Translators:
# Baskhu<PERSON> Lo<PERSON>huu <baskhuu<PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# hish, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# 486aab72cd7cb5d98a82d40b0db4c5f2_4397374 <25b041e3e1ecd3f217ca34118196bd3e_836923>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Munkhbaatar g <<EMAIL>>, 2021\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid ""
".\n"
"                Manual actions may be needed."
msgstr ""
".\n"
"                Магадгүй гар ажиллагаа шаардлагатай."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Preparation</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> <b>Бэлтгэх ажиллагаа</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/> <b>Cancelled</b>"
msgstr "<i class=\"fa fa-fw fa-times\"/> <b>Цуцлагдсан</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-truck\"/> <b>Shipped</b>"
msgstr "<i class=\"fa fa-fw fa-truck\"/> <b>Хүргэгдсэн</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Борлуулалт</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.product_template_view_form_inherit_stock
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">Зарагдсан</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "<strong>Customer Reference:</strong>"
msgstr "<strong>Захиалагчийн код:</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<strong>Delivery Orders</strong>"
msgstr "<strong>Хүргэх захиалга</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<strong>Incoterm: </strong>"
msgstr "<strong>Тээврийн нөхцөл: </strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_invoice_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Тээврийн нөхцөл:</strong>"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Үлдэгдэл тооцох барааны хувьд та тэдгээр барааны нөөцийг хянах боломжтой юм. Үүний тулд Inventory app суулгасан байх шаардлагатай.\n"
"Үлдэгдэл тооцохгүй бараа бол нөөцийг хянаж бүртгэх шаардлагагүй бараа юм.\n"
"Үйлчилгээ бол таны ханган үзүүлдэг материаллаг бус ажил үйлчилгээ болно."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Барааны хэрэглэгчийн гарт хүргэсэн тоо хэмжээг тооцоолох аргыг дараах байдлаар тохируулж болно:\n"
"  - Гараар: захиалгын мөр дээр хүргэсэн тоог гараар хөтлөх\n"
"  - Зардлын тайлангаас тооцох: хүргэсэн тоо нь батлагдсан зардлын тайлангаас тодорхойлогдох\n"
"  - Цагийн хуудас: захиалгын мөрт холбогдсон төслийн даалгаврын нийт зарцуулсан цагаар хэмжигдэх\n"
"  - Барааны хөдөлгөөн: агуулахын баримт батлагдах үед хүргэсэн тоо автоматаар хөтлөгдөх\n"

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_aftersale
msgid "After-Sale"
msgstr "Борлуулалтын дараах"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "All planned operations included"
msgstr "Бүх төлөвлөгдсөн орлого зарлагууд багтсан"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_rules_report__so_route_ids
msgid "Apply specific routes"
msgstr "Тусгайлан дамжлага тохируулах"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__direct
msgid "As soon as possible"
msgstr "Боломжтой болмогц"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/js/qty_at_date_widget.js:0
#, python-format
msgid "Availability"
msgstr "Бэлэн байдал"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Available"
msgstr "Идэвхтэй"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Available in stock"
msgstr "Нөөцөд боломжтой"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_stock_rules_report__so_route_ids
msgid "Choose to apply SO lines specific routes."
msgstr "Борлуулалтын захиалгын мөрүүдэд тусгайлан дамжлага сонгож зааж өгөх."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Компаниуд"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__effective_date
msgid "Completion date of the first delivery order."
msgstr "Эхний хүргэлтийн баримтыг гүйцэтгэх хугацаа."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Тохиргооны тохируулга"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Date:"
msgstr "Огноо:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_users__property_warehouse_id
msgid "Default Warehouse"
msgstr "Агуулах"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Хүргэлт"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_cancel__display_delivery_alert
msgid "Delivery Alert"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_count
msgid "Delivery Orders"
msgstr "Хүргэлтийн захиалгууд"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Хүргэлтийн огноог та захиалагчид амлаж болно, Үйлчилгээ барааны хувьд "
"захиалгын мөрүүдийн хамгийн бага урьтал хугацаанд үндэслэн тооцогдоно. "
"Хүргэгддэг барааны хувьд хүргэлтийн бодлогоос хамааран захиалгын мөрүүдийн "
"хамгийн бага эсвэл хамгийн өндөр урьтал хугацаагаар тооцоологдоно."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__display_qty_widget
msgid "Display Qty Widget"
msgstr "Тоо харуулах талбар"

#. module: sale_stock
#: model:res.groups,name:sale_stock.group_display_incoterm
msgid "Display incoterms on Sales Order and related invoices"
msgstr ""
"Борлуулалтын Захиалга ба холбоотой нэхэмжлэлүүд дээр инкотерм харуулах"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Display incoterms on orders &amp; invoices"
msgstr "Захиалга &amp; нэхэмжлэл дээр хүргэлтийн нөхцөлийг харуулах"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Do not forget to change the partner on the following delivery orders: %s"
msgstr ""
"Дараах хүргэлтийн баримт дээрх харилцагчийг солихоо бүү мартаарай:  %s"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Documentation"
msgstr "Баримтжуулалт"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__effective_date
msgid "Effective Date"
msgstr "Хэрэгжсэн огноо"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "Exception(s) occurred on the picking:"
msgstr "Бэлтгэх баримтанд саад(ууд) учирлаа:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Борлуулалтын захиалганд тохиосон саатал:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s):"
msgstr "Саатал:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Төлөвлөсөн огноо"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Expected Delivery"
msgstr "Товлосон хүргэлт"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Expected:"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__forecast_expected_date
msgid "Forecast Expected Date"
msgstr ""

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Forecasted Stock"
msgstr "Таамагласан нөөц"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__free_qty_today
msgid "Free Qty Today"
msgstr "Өнөөдрийн үнэгүй тоо"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__show_json_popover
msgid "Has late picking"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Хэрэв та бүх барааг нэгэн зэрэг хүргэхээр бол, хүргэлтийн товлосон огноо нь "
"хамгийн их хүлээлтийн хугацаатай барааны хүргэх огноогоор тооцогдоно. Нэгэн "
"зэрэг хүргэхгүй нөхцөлд, хамгийн бага хүлээлт хугацаатай барааны хүргэх "
"огноогоор тооцогдоно."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Impacted Transfer(s):"
msgstr "Холбогдох баримт(ууд):"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm
msgid "Incoterm"
msgstr "Худалдааны нөхцөл"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__group_display_incoterm
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Incoterms"
msgstr "Худалдааны нөхцөл"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Олон улсын худалдааны нөхцөл нь улс дамнасан арилжаа худалдаанд зориулан "
"тодорхойлогдсон нөхцлүүдийн жагсаалт юм."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_users_view_form
msgid "Inventory"
msgstr "Агуулах"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "Тооллогын дамжлага"

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_invoiced
msgid "Invoicing"
msgstr "Нэхэмжлэл"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_mto
msgid "Is Mto"
msgstr "MTO эсэх"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__json_popover
msgid "JSON data for the popover widget"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move
msgid "Journal Entry"
msgstr "Ажил гүйлгээ"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move_line
msgid "Journal Item"
msgstr "Журналын бичилт"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Серийн дугаар"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Make To Order"
msgstr "Захиалга дээр үндэслэж нөөц бүрдүүлэх"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Manual actions may be needed."
msgstr "Гар ажиллагаа шаардагдаж магадгүй."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr ""
"Захиалагчид хүргэхээр амласан хугацаанаас хоцрохоос сэргийлж захиалагчид "
"амласан огнооноос ийм тооны хоногоор өмнөх хугацаанд бэлтгэх баримтын огноог"
" товлоно."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company__security_lead
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__security_lead
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised"
" date, to cope with unexpected delays in the supply chain."
msgstr ""
"Захиалагчид амласан огнооны зөрүүний алдаа. Хангалтын алдаа гаргахгүйн тулд "
"барааны татан авахаар товлосон огноо, хүргэх огноо нь жинхэнэ амласан "
"огнооноос олон өдрийн өмнө байх хэрэгтэй."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Хүргэгдсэн тоог хөтлөх арга"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr "Худалдан авалтын орлогыг огноог төлөвлөхдөө нөөц хоногийг"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "No enough future availaibility"
msgstr ""

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "No future availaibility"
msgstr ""

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "On"
msgstr "Асаах"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Ordered quantity decreased!"
msgstr "Захиалсан тоо багассан!"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr "Бэлтгэх бодлого"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_group
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__procurement_group_id
msgid "Procurement Group"
msgstr "Нөөц хангалтын бүлэг"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_template
msgid "Product Template"
msgstr "Барааны загвар"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__product_type
msgid "Product Type"
msgstr "Барааны төрөл"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_available_today
msgid "Qty Available Today"
msgstr "Өнөөдөр боломжтой тоо"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Хүргэх тоо"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_report_product_product_replenishment
msgid "Quotations"
msgstr "Үнийн саналууд"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Remaining demand available at"
msgstr ""

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Reserved"
msgstr "Нөөцлөгдсөн"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__route_id
msgid "Route"
msgstr "Дамжлага"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_move__sale_line_id
msgid "Sale Line"
msgstr "Борлуулалтын мөр"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_procurement_group__sale_id
msgid "Sale Order"
msgstr "Борлуулалтын захиалга"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "Sale Orders"
msgstr "Борлуулалтын захиалга"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_production_lot__sale_order_count
msgid "Sale order count"
msgstr "Борлуулалтын захиалгын тоо"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Analysis Report"
msgstr "Борлуулалтын шинжилгээ тайлан"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking__sale_id
msgid "Sales Order"
msgstr "Борлуулалтын захиалга"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Борлуулалтын захиалгын мөр"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_view_form_inherit_sale_stock
msgid "Sales Order Lines"
msgstr "Борлуулалтын захиалгын мөр"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_production_lot__sale_order_ids
msgid "Sales Orders"
msgstr "Борлуулалтын захиалга"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company__security_lead
msgid "Sales Safety Days"
msgstr "Борлуулалтын аюулгүй хоног"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Schedule deliveries earlier to avoid delays"
msgstr "Хоцролтоос сэргийлж хүргэлтийг урьдчилан төлөвлөх"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__scheduled_date
msgid "Scheduled Date"
msgstr "Товлогдсон огноо"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__security_lead
msgid "Security Lead Time"
msgstr "Аюулгүйн урьтал хугацаа"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr "Борлуулалтын аюулгүйн урьтал хугацаа"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_location_route__sale_selectable
msgid "Selectable on Sales Order Line"
msgstr "Борлуулалтын захиалгын мөрүүд дээр сонгох боломжтой"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__one
msgid "Ship all products at once"
msgstr "Бүх бараа бэлэн болсон үед хүргэнэ"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__direct
msgid "Ship products as soon as available, with back orders"
msgstr ""
"Бэлэн байгаа барааг түрүүлж хүргэнэ, дутуу бараанд нөхөн баримт үүсгэнэ"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_policy
msgid "Shipping Policy"
msgstr "Хүргэлтийн бодлого"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.product_template_view_form_inherit_stock
msgid "Sold in the last 365 days"
msgstr "Сүүлийн 365 хоногт зарагдсан"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_cancel_view_form_inherit
msgid ""
"Some products have already been delivered. Returns can be created from the "
"Delivery Orders."
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Барааны хөдөлгөөн"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__move_ids
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order_line__qty_delivered_method__stock_move
msgid "Stock Moves"
msgstr "Барааны хөдөлгөөнүүд"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "Нөөц дүүргэлтийн тайлан"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Агуулахын дүрэм"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Агуулахын дүрмийн тайлан"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Агуулахын дүрмийн тайлан"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "The delivery"
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"The delivery address has been changed on the Sales Order<br/>\n"
"                        From <strong>\"%s\"</strong> To <strong>\"%s\"</strong>,\n"
"                        You should probably update the partner on this document."
msgstr ""
"Борлуулалтын захиалга дээрх хүргэлтийн хаяг шинэчлэгдсэн<br/>\n"
"                        <strong>\"%s\"</strong> байснаас <strong>\"%s\"</strong>боловч,\n"
"                        Та энэ баримтанд сонгогдсон харилцагчийг шинэчлэх шаардлагатай байж мэднэ."

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "This product is replenished on demand."
msgstr "Энэ барааны эрэлт үүсэхэд татан авалт хийгдэнэ."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Шилжүүлэг"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_ids
msgid "Transfers"
msgstr "Гүйлгээ"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_users
msgid "Users"
msgstr "Хэрэглэгчид"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "View Forecast"
msgstr "Урьдчилсан таамгийг харах"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr "Огноонд харгалзах ирээдүйн нөөц"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report__warehouse_id
msgid "Warehouse"
msgstr "Агуулах"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Warning!"
msgstr "Анхааруулга!"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__one
msgid "When all products are ready"
msgstr "Бүх бараа бэлэн үед"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to start shipping"
msgstr "Хэзээ хүргэлт хийж эхлэх вэ"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the delivery order if needed."
msgstr ""
"Захиалсан тоо хэмжээг та буурууллаа! Хүргэлтийн захиалгыг шаардлагатай бол "
"гараараа шинэчилэхээ мартаж болохгүй шүү."

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"You cannot decrease the ordered quantity of a sale order line below its delivered quantity.\n"
"Create a return in your inventory first."
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "cancelled"
msgstr "цуцлагдсан"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "өдөр"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "of"
msgstr "ын"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "ordered instead of"
msgstr "оронд нь захиалагдсан"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "processed instead of"
msgstr "бэлтгэв, Бэлтгэх ёстой нь:"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "will be late."
msgstr ""
