<templates>
    <t t-name="set-from-attribute-literal">
        <t t-set="value" t-value="'ok'"/>
        <t t-esc="value"/>
    </t>
    <result id="set-from-attribute-literal">
        ok
    </result>

    <t t-name="set-from-body-literal">
        <t t-set="value">ok</t>
        <t t-esc="value"/>
    </t>
    <result id="set-from-body-literal">
        ok
    </result>

    <t t-name="set-from-attribute-lookup">
        <t t-set="stuff" t-value="value"/>
        <t t-esc="stuff"/>
    </t>
    <params id="set-from-attribute-lookup">
        {"value": "ok"}
    </params>
    <result id="set-from-attribute-lookup">
        ok
    </result>

    <t t-name="set-from-body-lookup">
        <t t-set="stuff">
            <t t-esc="value"/>
        </t>
        <t t-esc="stuff"/>
    </t>
    <params id="set-from-body-lookup">
        {"value": "ok"}
    </params>
    <result id="set-from-body-lookup">
        ok
    </result>

    <t t-name="set-from-body-arch-lookup">
        <t t-set="stuff"><div><span t-esc="value"/></div></t>
        <t t-raw="stuff"/>
    </t>
    <params id="set-from-body-arch-lookup">
        {"value": "ok"}
    </params>
    <result id="set-from-body-arch-lookup"><![CDATA[<div><span>ok</span></div>]]></result>

    <t t-name="set-empty-body">
        <t t-set="stuff"/>
        <t t-esc="stuff"/>
    </t>
    <result id="set-empty-body"/>

    <t t-name="t-value-priority">
        <t t-set="value" t-value="1">2</t>
        <t t-esc="value"/>
    </t>
    <result id="t-value-priority">1</result>
</templates>
