# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_discount
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:16+0000\n"
"PO-Revision-Date: 2018-09-21 13:16+0000\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__iface_discount
msgid "Allow the cashier to give discounts on the whole order."
msgstr ""

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/xml/discount_templates.xml:6
#, python-format
msgid "Discount"
msgstr ""

#. module: pos_discount
#: model_terms:ir.ui.view,arch_db:pos_discount.pos_config_view_form_inherit_pos_discount
msgid "Discount %"
msgstr ""

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/discount.js:14
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_pc
#, python-format
msgid "Discount Percentage"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_product_id
#: model_terms:ir.ui.view,arch_db:pos_discount.pos_config_view_form_inherit_pos_discount
msgid "Discount Product"
msgstr ""

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/discount.js:28
#, python-format
msgid "No discount product found"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__iface_discount
msgid "Order Discounts"
msgstr ""

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_config
msgid "Point of Sale Configuration"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_pc
msgid "The default discount percentage"
msgstr ""

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/discount.js:29
#, python-format
msgid ""
"The discount product seems misconfigured. Make sure it is flagged as 'Can be"
" Sold' and 'Available in Point of Sale'."
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_product_id
msgid "The product used to model the discount."
msgstr ""
