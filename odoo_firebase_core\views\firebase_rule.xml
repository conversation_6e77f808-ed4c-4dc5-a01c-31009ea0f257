<?xml version="1.0"?>
<odoo>
    <data>
        <!-- Firebase Rule -->
        <record id="view_form_firebase_rule" model="ir.ui.view">
            <field name="name">Firebase Rule</field>
            <field name="model">firebase.rule</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button
                                name="force_sync"
                                string="Sync."
                                type="object"
                                confirm="Are you sure you want to synchronize all the records of this model (it may take a while)?"
                        />
                    </header>
                    <sheet>
                        <group string="Datos de Odoo">
                            <field name="model_id" options="{'no_create': True, 'no_edit': True}"/>
                            <field name="model_name" invisible="1"/>
                            <field name="domain" widget="domain" options="{'model': 'model_name'}"/>
                        </group>
                        <group string="Datos de Firebase">
                            <field name="path_firebase"/>
                            <field name="result_type"/>
                            <field name="method" attrs="{'invisible':[('result_type','!=','method')]}"/>
                            <field name="custom_field_ids" attrs="{'invisible':[('result_type','!=','custom')]}"/>
                            <field name="search_field_ids" />
                        </group>
                        <group col="4">
                            <field name="allow_create"/>
                            <field name="allow_update"/>
                            <field name="allow_delete"/>
                            <field name="active"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
    </data>
</odoo>