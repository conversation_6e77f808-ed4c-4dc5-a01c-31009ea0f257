# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_ldap
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_filter
msgid ""
"    Filter used to look up user accounts in the LDAP database. It is an    arbitrary LDAP filter in string representation. Any `%s` placeholder    will be replaced by the login (identifier) provided by the user, the filter    should contain at least one such placeholder.\n"
"\n"
"    The filter must result in exactly one (1) result, otherwise the login will    be considered invalid.\n"
"\n"
"    Example (actual attributes depend on LDAP server and setup):\n"
"\n"
"        (&(objectCategory=person)(objectClass=user)(sAMAccountName=%s))\n"
"\n"
"    or\n"
"\n"
"        (|(mail=%s)(uid=%s))\n"
"    "
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__create_user
msgid ""
"Automatically create local user accounts for new users authenticating via "
"LDAP"
msgstr "LDAPを使用して新しいユーザを認証するためのローカルユーザアカウントを自動的に作成します。"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company
msgid "Companies"
msgstr "会社"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__company
msgid "Company"
msgstr "会社"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company_ldap
msgid "Company LDAP configuration"
msgstr ""

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_user
msgid "Create User"
msgstr "ユーザ作成"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_uid
msgid "Created by"
msgstr "作成者"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_date
msgid "Created on"
msgstr "作成日"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_base
msgid ""
"DN of the user search scope: all descendants of this base will be searched "
"for users."
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__display_name
msgid "Display Name"
msgstr "表示名"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__id
msgid "ID"
msgstr "ID"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_company_ldap_view_tree
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "LDAP Configuration"
msgstr "LDAP設定"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company__ldaps
#: model:ir.model.fields,field_description:auth_ldap.field_res_config_settings__ldaps
msgid "LDAP Parameters"
msgstr "LDAPパラメータ"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_config_settings_view_form
msgid "LDAP Server"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_server
msgid "LDAP Server address"
msgstr "LDAPサーバアドレス"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_server_port
msgid "LDAP Server port"
msgstr "LDAPサーバポート"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_base
msgid "LDAP base"
msgstr "LDAPベース"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_binddn
msgid "LDAP binddn"
msgstr "LDAPバインディング"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_filter
msgid "LDAP filter"
msgstr "LDAPフィルタ"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_password
msgid "LDAP password"
msgstr "LDAPパスワード"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Login Information"
msgstr "ログイン情報"

#. module: auth_ldap
#: code:addons/auth_ldap/models/res_company_ldap.py:0
#, python-format
msgid "No local user found for LDAP login and not configured to create one"
msgstr ""

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Process Parameter"
msgstr "処理パラメータ"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_tls
msgid ""
"Request secure TLS/SSL encryption when connecting to the LDAP server. This "
"option requires a server with STARTTLS enabled, otherwise all authentication"
" attempts will fail."
msgstr ""
"LDAPサーバへの接続時に安全なTLS/SSL暗号化を要求します。このオプションはサーバがSTARTTLSが使用可能であることを要求します。そうでない場合は全ての認証要求は失敗します。"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__sequence
msgid "Sequence"
msgstr "付番"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Server Information"
msgstr "サーバ情報"

#. module: auth_ldap
#: model:ir.actions.act_window,name:auth_ldap.action_ldap_installer
msgid "Setup your LDAP Server"
msgstr "LDAPサーバの設定"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__user
msgid "Template User"
msgstr "テンプレートユーザ"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_password
msgid ""
"The password of the user account on the LDAP server that is used to query "
"the directory."
msgstr "LDAPサーバのユーザアカウントのパスワードはディレクトリへのクエリー使用されます。"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_binddn
msgid ""
"The user account on the LDAP server that is used to query the directory. "
"Leave empty to connect anonymously."
msgstr "LDAPサーバ上のユーザアカウントはディレクトリへのクエリーのために使用されます。匿名接続する場合は空のままにして下さい。"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_tls
msgid "Use TLS"
msgstr "TLS を使用"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "User Information"
msgstr "ユーザ情報"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__user
msgid "User to copy when creating new users"
msgstr "新規ユーザ作成時にユーザにコピーされます。"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_users
msgid "Users"
msgstr "ユーザ"
