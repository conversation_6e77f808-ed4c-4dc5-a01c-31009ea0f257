# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_setup
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "(Community Edition)"
msgstr "(الإصدار المجتمعي) "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "<span class=\"fa fa-lg fa-users\" aria-label=\"Number of active users\"/>"
msgstr "<span class=\"fa fa-lg fa-users\" aria-label=\"Number of active users\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&gt;', '1')]}\">\n"
"                                            Active User\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&lt;=', '1')]}\">\n"
"                                            Active Users\n"
"                                        </span>"
msgstr ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&gt;', '1')]}\">\n"
"                                            مستخدم نشط\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&lt;=', '1')]}\">\n"
"                                            مستخدمين نشطين\n"
"                                        </span>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('company_count', '&gt;', '1')]}\">\n"
"                                            Company\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" attrs=\"{'invisible':[('company_count', '&lt;=', '1')]}\">\n"
"                                            Companies\n"
"                                        </span>\n"
"                                        <br/>"
msgstr ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('company_count', '&gt;', '1')]}\">\n"
"                                            الشركة\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" attrs=\"{'invisible':[('company_count', '&lt;=', '1')]}\">\n"
"                                            الشركات\n"
"                                        </span>\n"
"                                        <br/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('language_count', '&gt;', '1')]}\">\n"
"                                                Language\n"
"                                            </span>\n"
"                                            <span class=\"o_form_label\" attrs=\"{'invisible':[('language_count', '&lt;=', '1')]}\">\n"
"                                                Languages\n"
"                                            </span>"
msgstr ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('language_count', '&gt;', '1')]}\">\n"
"                                                اللغة\n"
"                                            </span>\n"
"                                            <span class=\"o_form_label\" attrs=\"{'invisible':[('language_count', '&lt;=', '1')]}\">\n"
"                                                اللغات\n"
"                                            </span>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Document Layout</span>\n"
"                                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"o_form_label\">مخطط المستند</span>\n"
"                                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to choose your Geo "
"Provider."
msgstr "<strong>احفظ</strong> هذه الصفحة وعد إلى هنا لاختيار مزودك الجغرافي. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up reCaptcha."
msgstr "<strong>احفظ</strong> هذه الصفحة وعد إلى هنا لضبط reCaptcha. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr "قم <strong>بحفظ</strong> هذه الصفحة ثم عُد إلى هنا لضبط الخاصية. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "About"
msgstr "عن "

#. module: base_setup
#: code:addons/base_setup/controllers/main.py:0
#, python-format
msgid "Access Denied"
msgstr "تم رفض الوصول "

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode"
msgstr "تفعيل وضع المطور"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with assets)"
msgstr "تفعيل وضع المطوِّر (مع الأصول) "

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with tests assets)"
msgstr "تفعيل وضع المطوِّر (مع أصول الاختبار) "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Add Language"
msgstr "إضافة لغة "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Add fun feedback and motivate your employees"
msgstr "أضف ملاحظات لطيفة لتحفيز موظفيك "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_mail_plugin
msgid "Allow integration with the mail plugins"
msgstr "السماح بالتكامل مع المكونات الإضافية للبريد الإلكتروني "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_calendar
msgid "Allow the users to synchronize their calendar  with Google Calendar"
msgstr "السماح للمستخدمين بمزامنة التقويم مع تقويم Google "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_microsoft_calendar
msgid "Allow the users to synchronize their calendar with Outlook Calendar"
msgstr "أتح للمستخدمين مزامنة تقويمهم مع تقويم Outlook "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_import
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
msgstr "السماح للمستخدمين باستيراد البيانات من ملفات CSV/XLS/XLSX/ODS"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__group_multi_currency
msgid "Allows to work in a multi currency environment"
msgstr "يتيح لك العمل في بيئة متعددة العملات "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_voip
msgid "Asterisk (VoIP)"
msgstr "علامة النجمة (VoIP)"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_drive
msgid "Attach Google documents to any record"
msgstr "إرفاق مستندات Google بأي سجل "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Automatically enrich your contact base with company data"
msgstr "قم بإثراء قاعدة جهات اتصالك بمعلومات الشركة "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Automatically generate counterpart documents for orders/invoices between "
"companies"
msgstr "إنشاء مستندات مقابلة تلقائياً للطلبات/الفواتير بين الشركات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"By default, new users get highest access rights for all installed apps."
msgstr ""
"يحصل المستخدمون الجدد على أعلى صلاحيات للوصول لكافة التطبيقات المثبتة بشكل "
"افتراضي. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Choose the layout of your documents"
msgstr "اختر تخطيط مستنداتك "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_pad
msgid "Collaborative Pads"
msgstr "منصات تعاونية"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Companies"
msgstr "الشركات "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_id
msgid "Company"
msgstr "الشركة "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_informations
msgid "Company Informations"
msgstr "معلومات الشركة "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_name
msgid "Company Name"
msgstr "اسم الشركة "

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Configure Document Layout"
msgstr "تهيئة تخطيط المستند "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Configure company rules to automatically create SO/PO when one of your "
"company sells/buys to another of your company."
msgstr ""
"قم بضبط قواعد شركتك لإنشاء أوامر بيع أو شراء تلقائياً عندما يقوم أحد موظفي "
"شركتك بعملية بيع أو شراء لشخص آخر في شركتك. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Contacts"
msgstr "جهات الاتصال"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "Copyright © 2004"
msgstr "حقوق النشر © 2004 "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Create and attach Google Drive documents to any record"
msgstr "إنشاء وإرفاق مستندات Google بأي سجل "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__external_email_server_default
msgid "Custom Email Servers"
msgstr "خوادم بريد إلكتروني مخصصة "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__report_footer
msgid "Custom Report Footer"
msgstr "تذييل مخصص للتقرير "

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Deactivate the developer mode"
msgstr "إلغاء تفعيل وضع المطوِّر "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__user_default_rights
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "صلاحيات الوصول الافتراضية"

#. module: base_setup
#: code:addons/base_setup/models/res_config_settings.py:0
#, python-format
msgid "Default User Template not found."
msgstr "لم يتم العثور على قالب المستخدِم الافتراضي. "

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Developer Tools"
msgstr "أدوات المطورين "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__external_report_layout_id
msgid "Document Template"
msgstr "قالب المستند "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Documentation"
msgstr "التوثيق "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Edit Layout"
msgstr "تحرير المخطط"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/js/res_config_invite_users.js:0
#, python-format
msgid "Email addresses already existing: %s."
msgstr "عنوان البريد الإلكتروني موجود بالفعل: %s. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Enable the profiling tool. Profiling may impact performance while being "
"active."
msgstr "تفعيل أداة التحليل. قد يؤثر التحليل على الأداء عندما تكون نشطاً. "

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Enter e-mail address"
msgstr "إدخال البريد الإلكتروني "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Extract and analyze Odoo data from Google Spreadsheet"
msgstr "استخلاص وتحليل بيانات أودو من جدول بيانات Google "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Find free high-resolution images from Unsplash"
msgstr "احصل على صور مجانية عالية الدقة على Unsplash "

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "تذييل النص يُعرض في الجزء السفلي لكافة التقارير. "

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "GNU LGPL Licensed"
msgstr "حاصل على رخصة GNU  العمومية الصغرى "

#. module: base_setup
#: model:ir.ui.menu,name:base_setup.menu_config
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "General Settings"
msgstr "الإعدادات العامة"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Geo Localization"
msgstr "الأقلمة الجغرافية "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_geolocalize
msgid "GeoLocalize"
msgstr "الأقلمة الجغرافية "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "GeoLocalize your partners"
msgstr "قم بأقلمة شركائك جغرافياً "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_product_images
msgid "Get product pictures using barcode"
msgstr "احصل على صور للمنتج باستخدام الباركود "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Google Calendar"
msgstr "تقويم Google "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Google Drive"
msgstr "Google Drive "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_spreadsheet
msgid "Google Spreadsheet"
msgstr "جدول بيانات Google"

#. module: base_setup
#: model:ir.model,name:base_setup.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Import & Export"
msgstr "استيراد وتصدير"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrate with mail client plugins"
msgstr "التكامل مع المكونات الإضافية للبريد الإلكتروني "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrations"
msgstr "التكاملات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Inter-Company Transactions"
msgstr "المعاملات بين الشركات "

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/js/res_config_invite_users.js:0
#, python-format
msgid "Invalid email addresses: %s."
msgstr "عنوان البريد الإلكتروني غير صالح: %s. "

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Invite"
msgstr "دعوة"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Invite New Users"
msgstr "دعوة مستخدمين جدد "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_ldap
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "LDAP Authentication"
msgstr "المصادقة من خلال LDAP"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Languages"
msgstr "اللغات"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Layout"
msgstr "المخطط "

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Load demo data"
msgstr "تحميل البيانات التجريبية"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Mail Plugin"
msgstr "المكون الإضافي للبريد الإلكتروني "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage API Keys"
msgstr "إدارة مفاتيح الواجهة البرمجية للتطبيق "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Companies"
msgstr "إدارة الشركات "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_account_inter_company_rules
msgid "Manage Inter Company"
msgstr "إدارة ما بين الشركات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Languages"
msgstr "إدارة اللغات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Users"
msgstr "إدارة المستخدمين "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__group_multi_currency
msgid "Multi-Currencies"
msgstr "متعدد العملات "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__active_user_count
msgid "Number of Active Users"
msgstr "عدد المستخدمين النشطين "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_count
msgid "Number of Companies"
msgstr "عدد الشركات "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__language_count
msgid "Number of Languages"
msgstr "عدد اللغات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "OAuth Authentication"
msgstr "المصادقة من خلال OAuth"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "Odoo"
msgstr "أودو"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "Odoo S.A."
msgstr "أودو S.A. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Apple Store"
msgstr "في متجر Apple "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Google Play"
msgstr "في متجر Goople Play "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Outlook Calendar"
msgstr "تقويم Outlook "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_partner_autocomplete
msgid "Partner Autocomplete"
msgstr "الإكمال التلقائي للشريك"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Pending Invitations:"
msgstr "الدعوات المعلقة "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Performance"
msgstr "الأداء"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Permissions"
msgstr "الأذونات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Preview Document"
msgstr "معاينة المستند"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__profiling_enabled_until
msgid "Profiling enabled until"
msgstr "التحليل ممكّن حتى "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Protect your forms from spam and abuse."
msgstr "احمِ استماراتك من العشوائيات وإساءة الاستخدام "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send SMS"
msgstr "إرسال رسالة نصية قصيرة SMS"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send texts to your contacts"
msgstr "أرسل رسائل لجهات اتصالك "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Set custom access rights for new users"
msgstr "تعيين صلاحيات وصول مخصصة للمستخدمين الجدد "

#. module: base_setup
#: model:ir.actions.act_window,name:base_setup.action_general_configuration
msgid "Settings"
msgstr "الإعدادات"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__show_effect
msgid "Show Effect"
msgstr "إظهار التأثير"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Synchronize your calendar with Google Calendar"
msgstr "مزامنة تقويمك مع تقويم Google "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Synchronize your calendar with Outlook"
msgstr "مزامنة تقويمك مع Outlook "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_gengo
msgid "Translate Your Website with Gengo"
msgstr "ترجم موقعك باستخدام Gengo"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_web_unsplash
msgid "Unsplash Image Library"
msgstr "مكتبة صور Unsplash"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Update Info"
msgstr "تحديث المعلومات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use LDAP credentials to log in"
msgstr "استخدام بيانات LDAP لتسجيل الدخول"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use external accounts to log in (Google, Facebook, etc.)"
msgstr "تسجيل الدخول باستخدام حسابات خارجية (Google، Facebook، إلخ.) "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_oauth
msgid "Use external authentication providers (OAuth)"
msgstr "استخدام مزودي مُصادقة خارجيين (OAuth)"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use external pads in Odoo Notes"
msgstr "استخدام منصات خارجية في ملاحظات أودو"

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_users
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Users"
msgstr "المستخدمين "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"When populating your address book, Odoo provides a list of matching "
"companies. When selecting one item, the company data and logo are auto-"
"filled."
msgstr ""
"عند ملء كتيب عنوانينك، يوفر لك أودو قائمة بالشركات المتطابقة. عند اختيار "
"عنصر واحد، يتم ملء بيانات وشعار الشركة تلقائياً. "

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "more"
msgstr "المزيد "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_recaptcha
msgid "reCAPTCHA"
msgstr "reCAPTCHA"
