# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:58
#, python-format
msgid ""
"\"Add to\n"
"                Dashboard\""
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:30
#, python-format
msgid "&nbsp;"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/add_to_board_menu.js:132
#, python-format
msgid "'%s' added to dashboard"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:88
#: code:addons/board/static/src/xml/board.xml:98
#, python-format
msgid "Add"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:83
#: code:addons/board/static/src/xml/board.xml:93
#, python-format
msgid "Add to my Dashboard"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:378
#, python-format
msgid "Are you sure you want to remove this item?"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:432
#: model:ir.model,name:board.model_board_board
#, python-format
msgid "Board"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:11
#, python-format
msgid "Change Layout"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:9
#, python-format
msgid "Change Layout.."
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:41
#, python-format
msgid "Choose dashboard layout"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/add_to_board_menu.js:136
#, python-format
msgid "Could not add filter to dashboard"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__display_name
msgid "Display Name"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:83
#, python-format
msgid "Edit Layout"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board____last_update
msgid "Last Modified on"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:46
#, python-format
msgid "Layout"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:46
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
#, python-format
msgid "My Dashboard"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/add_to_board_menu.js:133
#, python-format
msgid "Please refresh your browser for the changes to take effect."
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:56
#, python-format
msgid ""
"To add your first report into this dashboard, go to any\n"
"                menu, switch to list or graph view, and click"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:60
#, python-format
msgid ""
"You can filter and group data before inserting into the\n"
"                dashboard using the search options."
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:54
#, python-format
msgid "Your personal dashboard is empty"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:58
#, python-format
msgid "in the extended search options."
msgstr ""
