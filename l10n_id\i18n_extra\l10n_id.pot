# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_id
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-12-03 12:38+0000\n"
"PO-Revision-Date: 2019-12-03 12:38+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_id
#: model:account.tax.template,name:l10n_id.tax_PT1
#: model:account.tax.template,name:l10n_id.tax_ST1
msgid "10%"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_121001
msgid "Account Receivable"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_1210011
msgid "Account Receivable (PoS)"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511006
msgid "Accrued Payable Bank"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511008
msgid "Accrued Payable Business License"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511010
msgid "Accrued Payable Education"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511001
msgid "Accrued Payable Electricity"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511011
msgid "Accrued Payable Health Insurance/BPJS"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511009
msgid "Accrued Payable Insurance"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511002
msgid "Accrued Payable Jamsostek"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511007
msgid "Accrued Payable PBB"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511005
msgid "Accrued Payable Security Management"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511004
msgid "Accrued Payable Telp & Internet"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511003
msgid "Accrued Payable Water"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_228101
msgid "Accumulation Building Depreciation"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_228105
msgid "Accumulation Office Furniture Depreciation"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_228103
msgid "Accumulation Office Supplies Depreciation"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_228104
msgid "Accumulation Software Depreciation"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_228102
msgid "Accumulation Vehicle Depreciation"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_811001
msgid "Advance Sales"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_211003
msgid "Advertising"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511010
msgid "Asset Maintenance Costs"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_110001
msgid "Authorized Capital"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112005
msgid "BCA"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112004
msgid "BNI"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112006
msgid "BNI Giro"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_9_110002
msgid "Bank Administration Expense"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511002
msgid "Bank Administration Fees"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_211001
msgid "Bank Loan"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_811003
msgid "Bonus Point"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130012
msgid "Book, Office Stationery, Accessories Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511006
msgid "Building Maintenance Costs"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_141001
msgid "Building Rent"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112002
msgid "Business Mandiri"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_121001
msgid "Capital Reserves"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_111002
msgid "Cash in Hand"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130007
msgid "Cigarette Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311016
msgid "Cleaning Equipment"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130014
msgid "Cleaning Supplies Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511003
msgid "Consultant Fees"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_5_100001
msgid "Cost of Goods Sold"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_811002
msgid "Customer Deposit"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_8_110002
msgid "Deposit Income"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311004
msgid "Donation"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_180000
msgid "Down Payment"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130004
msgid "Dried Goods Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130009
msgid "Drink Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311001
msgid "Drinking Water"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311008
msgid "Electricity"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511007
msgid "Electricity, Telephone, and Internet Installation Maintenance Costs"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130016
msgid "Electronic Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110008
msgid "Employee Birthday Benefit"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110002
msgid "Employee Bonus / Benefits"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110003
msgid "Employee Health Benefits"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_121002
msgid "Employee Liabilities"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110004
msgid "Employee Meal (Catering)"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110005
msgid "Employee Overtime Pay"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110001
msgid "Employee Salary"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_211002
msgid "Event"
msgstr ""

#. module: l10n_id
#: model:account.tax.template,name:l10n_id.tax_PT0
#: model:account.tax.template,name:l10n_id.tax_ST2
msgid "Exempt"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311002
msgid "Exercise Necessities"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130013
msgid "Fashion & Textile Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311013
msgid "First Aid Kit"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130002
msgid "Fish Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130008
msgid "Food Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_8_110003
msgid "Foreign Exchange Gain"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_9_110003
msgid "Foreign Exchange Loss"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_211001
msgid "Free Gift"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130006
msgid "Fresh Drink Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130005
msgid "Fruit Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_8_110009
msgid "Gain on Sale of Fixed Assets"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511009
msgid "Guest Accomodation"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_900000
msgid "Historical Balance"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130015
msgid "House Supplies Inventory"
msgstr ""

#. module: l10n_id
#: model:account.chart.template,name:l10n_id.l10n_id_chart
msgid "Indonesian Account Chart Template"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_9_110001
msgid "Interest Expense"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_8_110001
msgid "Interest Income"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_900000
msgid "Interim Stock"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311005
msgid "Internet"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_411003
msgid "Jilid & Photocopy"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_411004
msgid "Job Recruitment Advertisement"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311015
msgid "K3 (Fire Extinguisher)"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311011
msgid "Kitchen Necessities"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221001
#: model:account.account.template,name:l10n_id.a_6_710001
msgid "Land"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_211002
msgid "Leasing Deposit"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511001
msgid "Licensing Fees"
msgstr ""

#. module: l10n_id
msgid "Liquidity Transfer"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_9_110009
msgid "Loss on Sale of Fixed Assets"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112007
msgid "Mandiri Giro"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110009
msgid "Maternity Benefit"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130001
msgid "Meat Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311003
msgid "Monthly Fee"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112003
msgid "Muamalat"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221002
#: model:account.account.template,name:l10n_id.a_6_710002
msgid "Office Building"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311012
msgid "Office Equipment"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221006
#: model:account.account.template,name:l10n_id.a_6_710006
msgid "Office Furniture"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_411001
msgid "Office Stationery"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221004
#: model:account.account.template,name:l10n_id.a_6_710004
msgid "Office Supplies"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_151002
msgid "Ongoing Profit & Loss"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_900000
msgid "Other Expenses"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_8_110004
msgid "Other Income"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130018
msgid "Other Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311014
msgid "Other Necessities"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_211004
msgid "Other Receivable"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311018
msgid "Owner Necessities"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_211003
msgid "Owner Receivable"
msgstr ""

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_PT0
msgid "PT0"
msgstr ""

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_PT1
msgid "PT1"
msgstr ""

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_PT2
msgid "PT2"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_110002
msgid "Paid Capital"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_151001
msgid "Past Profit & Loss"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112001
msgid "Personal Mandiri"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_111001
msgid "Petty Cash"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311006
msgid "Phone"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_411002
msgid "Post Necessities"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110010
msgid "Pph 21 Benefit"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_141003
msgid "Prepaid Advertisement-Free"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_141002
msgid "Prepaid Insurance"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311007
msgid "Prepaid Phone Bills"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_151001
msgid "Prepaid Tax Pph 22"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_151002
msgid "Prepaid Tax Pph 23"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_151003
msgid "Prepaid Tax Pph 25"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_110004
msgid "Prive (Personal Retrieval)"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130010
msgid "Processed Food Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511004
msgid "Rental Costs"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311010
msgid "Research & Development"
msgstr ""

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_ST0
msgid "ST0"
msgstr ""

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_ST1
msgid "ST1"
msgstr ""

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_ST2
msgid "ST2"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_110004
msgid "Salary Deposit"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_4_100001
msgid "Sales"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_4_200007
msgid "Sales Discount"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_4_200006
msgid "Sales Refund"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110006
msgid "Security Service Fee"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_110002
msgid "Shareholder Deposit"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511011
msgid "Shipping Costs"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_211004
msgid "Shipping Merchandise"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221005
#: model:account.account.template,name:l10n_id.a_6_710005
msgid "Software"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_411005
msgid "Stamp"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_121004
msgid "Tax Payable 4 (2)"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_121001
msgid "Tax Payable Pph 21"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_121002
msgid "Tax Payable Pph 23"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_121003
msgid "Tax Payable Pph 25"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_121005
msgid "Tax Payable Pph 29"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511008
msgid "Taxes"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_110003
msgid "Third-Party Deposit"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130011
msgid "Toiletries Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130017
msgid "Toys Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_110001
msgid "Trade Receivable"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_110003
msgid "Unpaid Capital"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_122101
msgid "VAT Purchase"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_122102
msgid "VAT Sales"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130003
msgid "Vegetables Inventory"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221003
#: model:account.account.template,name:l10n_id.a_6_710003
msgid "Vehicle"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_611001
msgid "Vehicle Fuel"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_611005
msgid "Vehicle Insurance"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_611003
msgid "Vehicle Parking & Toll Fee"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_611002
msgid "Vehicle Service"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_611004
msgid "Vehicle Taxes"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311009
msgid "Water (PDAM)"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110007
msgid "Work Uniform"
msgstr ""
