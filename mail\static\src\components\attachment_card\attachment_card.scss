// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_AttachmentCard:hover .o_AttachmentCard_asideItemUnlink.o-pretty {
    transform: translateX(0);
}

.o_AttachmentCard_action {
    min-width: 20px;
}

.o_AttachmentCard_aside {
    &:not(.o-has-multiple-action) {
        min-width: 50px;
    }

    &.o-has-multiple-action {
        min-width: 30px;
    }
}

.o_AttachmentCard_asideItem {
    width: 100%;
    height: 100%;
}

.o_AttachmentCard_asideItemUnlink.o-pretty {
    top: 0;
    transform: translateX(100%);
}

.o_AttachmentCard_details {
    flex-flow: column;
    min-width: 0; /* This allows the text ellipsis in the flex element */
    /* prevent hover delete button & attachment image to be too close to the text */
    padding-left : map-get($spacers, 1);
    padding-right : map-get($spacers, 1);
}

.o_AttachmentCard_filename {
    text-overflow: ellipsis;
}

.o_AttachmentCard_image {
    flex-shrink: 0;
    margin: 3px;
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_AttachmentCard.o-has-card-details {
    background-color: gray('300');
    border-radius: 5px;
}

.o_AttachmentCard_aside {
    border-radius: 0 5px 5px 0;
}

.o_AttachmentCard_asideItemDownload {
    cursor: pointer;

    &:hover {
        background-color: gray('400');
    }
}

.o_AttachmentCard_asideItemUnlink {
    cursor: pointer;

    &:not(.o-pretty):hover {
        background-color: gray('400');
    }

    &.o-pretty {
        color: white;
        background-color: $o-brand-primary;

        &:hover {
            background-color: darken($o-brand-primary, 10%);
        }
    }

}

.o_AttachmentCard_asideItemUploaded {
    color: $o-brand-primary;
}

.o_AttachmentCard_extension {
    font-size: 80%;
    font-weight: 400;
}

.o_AttachmentCard_image.o-attachment-viewable {
    cursor: zoom-in;

    &:not(.o-details-overlay):hover {
        opacity: 0.7;
    }
}

// ------------------------------------------------------------------
// Animation
// ------------------------------------------------------------------

.o_AttachmentCard_asideItemUnlink.o-pretty {
    transition: transform 0.3s ease 0s;
}
