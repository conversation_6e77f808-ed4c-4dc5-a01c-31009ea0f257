
.o_background_footer, .o_background_header, .o_report_layout_striped {
    color: gray('700');
}
.o_background_header {
    min-width: 900px;
    border-bottom: 1px solid gray('200');
    img {
        max-height: 96px;
        max-width: 200px;
        margin-right: 16px;
    }
    h3 {
        color: $o-default-report-primary-color;
        font-weight: 700;
        font-size: 1.25rem;
        max-width: 300px;
    }
}
.o_background_footer {
    .list-inline-item {
        white-space: nowrap;
    }
    ul {
        border-top: 1px solid $o-default-report-secondary-color;
        border-bottom: 1px solid $o-default-report-secondary-color;
        padding: 4px 0;
        margin: 0 0 4px 0;
        li {
            color: $o-default-report-secondary-color;
        }
    }
}
.o_layout_background {
    background-size: cover;
    background-position: bottom center;
    background-repeat: no-repeat;
    min-height: 620px
}
.o_report_layout_striped {
    strong {
        color: $o-default-report-secondary-color;
    }
    .table {
        border-top: 1px solid gray('300');
    }

    .table td, .table th {
        border-top: none;
    }
    h2 {
        color: $o-default-report-primary-color;
    }
    thead tr th {
        color: $o-default-report-secondary-color
    }
    tbody {
        color: gray('700');
        tr {
            &:nth-child(odd) {
                background-color: rgba(220, 205, 216, 0.2);
            }
            &.o_line_section {
                color: $o-brand-odoo;
                background-color: rgba(73, 80, 87, 0.2) !important;
            }
        }
    }
    /*Total table*/
    /* row div rule compat 12.0 */
    .row > div > table,
    div#total table {
        tr {
            &:nth-child(odd) {
                background-color: transparent !important;
            }
            /* first-child & last-child rule compat 12.0 */
            &:first-child,
            &:last-child,
            &.o_subtotal,
            &.o_total {
                border-top: none !important;
                td {
                    border-top: 1px solid gray('400') !important;
                }
                strong {
                    color: $o-default-report-primary-color;
                }
            }
        }
    }
}

/* special case for displaying report in iframe */
.o_in_iframe {
    .o_background_header {
        min-width: 0;
    }
}
