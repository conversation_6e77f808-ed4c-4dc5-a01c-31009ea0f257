<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_nz" model="res.partner">
        <field name="name">NZ Company</field>
        <field name="vat"></field>
        <field name="street">Victoria Street</field>
        <field name="city">Hamilton</field>
        <field name="country_id" ref="base.nz"/>
        <field name="state_id" ref="base.state_nz_wtc"/>
        <field name="zip">3247</field>
        <field name="phone">+64 21 123 4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.nzexample.com</field>
    </record>

    <record id="demo_company_nz" model="res.company">
        <field name="name">NZ Company</field>
        <field name="partner_id" ref="partner_demo_company_nz"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_nz')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_nz.demo_company_nz'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_nz.l10n_nz_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_nz.demo_company_nz')"/>
    </function>
</odoo>
