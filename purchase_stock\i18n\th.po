# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_stock
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Khwunch<PERSON> Jaengsawang <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Wichanon Jamwutthipreecha, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "% On-Time Delivery"
msgstr "v"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#: code:addons/purchase_stock/models/stock_rule.py:0
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr "+ %d วัน"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid ""
"<span attrs=\"{'invisible': [('on_time_rate', '&gt;=', 0)]}\">No On-time "
"Delivery Data</span>"
msgstr ""
"<span attrs=\"{'invisible': [('on_time_rate', '&gt;=', "
"0)]}\">ไม่มีข้อมูลการจัดส่งตรงเวลา</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_text\">On-time Rate</span>"
msgstr "<span class=\"o_stat_text\">อัตราตรงต่อเวลา</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">การซื้อ</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_value\">%</span>"
msgstr "<span class=\"o_stat_value\">%</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Incoterm:</strong>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>สถานที่จัดส่ง:</strong>"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_rule__action
msgid "Action"
msgstr "การดำเนินการ"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_report__avg_receipt_delay
msgid ""
"Amount of time between expected and effective receipt date. Due to a hack "
"needed to calculate this,               every record will show the same "
"average value, therefore only use this as an aggregated value with "
"group_operator=avg"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__avg_receipt_delay
msgid "Average Receipt Delay"
msgstr ""

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock.py:0
#: model:ir.model.fields.selection,name:purchase_stock.selection__stock_rule__action__buy
#: model:stock.location.route,name:purchase_stock.route_warehouse0_buy
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_report_stock_rule
#, python-format
msgid "Buy"
msgstr "ซื้อ"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_pull_id
msgid "Buy rule"
msgstr "กฎการซื้อ"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "Buy to Resupply"
msgstr "ซื้อเพื่อเติมสินค้า"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__effective_date
msgid "Completion date of the first receipt order."
msgstr "วันที่เสร็จสมบูรณ์ของใบสั่งรับสินค้าครั้งแรก"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_config_settings
msgid "Config Settings"
msgstr "การตั้งค่า"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "Corresponding receipt not found."
msgstr "ไม่พบใบเสร็จรับเงินที่เกี่ยวข้อง"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__created_purchase_line_id
msgid "Created Purchase Order Line"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__product_description_variants
msgid "Custom Description"
msgstr "คำอธิบายที่กำหนดเอง"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,help:purchase_stock.field_res_config_settings__days_to_purchase
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Days needed to confirm a PO, define when a PO should be validated"
msgstr "จำนวนวันที่ต้องใช้ในการยืนยัน PO กำหนดว่าเมื่อใดควรตรวจสอบ PO"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__days_to_purchase
#, python-format
msgid "Days to Purchase"
msgstr "วันที่จะซื้อ"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_type_id
msgid "Deliver To"
msgstr "ส่งถึง"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,help:purchase_stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"ขึ้นอยู่กับโมดูลที่ติดตั้งสิ่งนี้จะช่วยให้คุณกำหนดเส้นทางของผลิตภัณฑ์ได้ไม่ว่าจะซื้อผลิตเติมตามคำสั่งซื้อ"
" ฯลฯ"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Destination Location Type"
msgstr "ประเภทสถานที่ปลายทาง"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__display_name
msgid "Display Name"
msgstr "ชื่อที่ใช้แสดง"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Documentation"
msgstr "เอกสารกำกับ"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_dest_ids
msgid "Downstream Moves"
msgstr ""

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "ดรอปชิป"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__effective_date
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__effective_date
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__date
msgid "Effective Date"
msgstr "วันที่มีผล"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "Effective Date Last Year"
msgstr "วันที่มีผลบังคับใช้เมื่อปีที่แล้ว"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "ข้อยกเว้นที่เกิดขึ้นในคำสั่งซื้อ:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s):"
msgstr "ข้อยกเว้น:"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"For the product %s, the warehouse of the operation type (%s) is inconsistent"
" with the location (%s) of the reordering rule (%s). Change the operation "
"type or cancel the request for quotation."
msgstr ""
"สำหรับสินค้า %s คลังสินค้าของชนิดการดำเนินงาน (%s) "
"ไม่สอดคล้องกับตำแหน่งที่ตั้ง (%s) ของกฎการสั่งซื้อซ้ำ (%s) "
"เปลี่ยนประเภทการดำเนินการหรือยกเลิกการขอใบเสนอราคา"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__forecasted_issue
msgid "Forecasted Issue"
msgstr "ปัญหาที่คาดการณ์"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Generate the draft vendor bill."
msgstr "สร้างการร่างใบเรียกเก็บเงินของผู้ขาย"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Go back to the purchase order to generate the vendor bill."
msgstr "กลับไปที่ใบสั่งซื้อเพื่อสร้างใบเรียกเก็บเงินของผู้ขาย"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__id
msgid "ID"
msgstr "รหัส"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoming_picking_count
msgid "Incoming Shipment count"
msgstr "จำนวนการจัดส่งที่เข้ามา"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Incoming Shipments"
msgstr "การขนส่งขาเข้า"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoterm_id
msgid "Incoterm"
msgstr "ค่าเริ่มต้น"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__is_shipped
msgid "Is Shipped"
msgstr "จัดส่งแล้ว"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__is_installed_sale
msgid "Is the Sale Module Installed"
msgstr "มีการติดตั้งโมดูลการขายแล้ว"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_account_move
msgid "Journal Entry"
msgstr "รายการบันทึกบัญชี"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__last_purchase_date
msgid "Last Purchase"
msgstr "การซื้อครั้งล่าสุด"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Logistics"
msgstr "โลจิสติกส์"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr "ล็อต/ซีเรียล"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Manual actions may be needed."
msgstr "อาจจำเป็นต้องดำเนินการด้วยตนเอง"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"อัตราข้อผิดพลาดขั้นต้นสำหรับระยะเวลารอคอยสินค้าของผู้จำหน่าย "
"เมื่อระบบสร้างใบสั่งซื้อสำหรับการเรียงลำดับสินค้าใหม่ "
"พวกเขาจะถูกกำหนดเวลาล่วงหน้าหลายวัน "
"เพื่อรับมือกับความล่าช้าของผู้จำหน่ายที่คาดไม่ถึง"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "กฎขั้นต่ำของสินค้าคงคลัง"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Move forward expected request creation date by"
msgstr "เลื่อนวันที่สร้างคำขอที่คาดหวังไว้ภายในวันที่"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Next transfer(s) impacted:"
msgstr "การโอนย้ายครั้งต่อไปได้รับผลกระทบ:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "No data yet"
msgstr "ยังไม่มีข้อมูล"

#. module: purchase_stock
#: code:addons/purchase_stock/models/account_invoice.py:0
#, python-format
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr ""
"Odoo ไม่สามารถสร้างรายการ anglo saxon ได้ การประเมินมูลค่ารวมของ %s "
"เป็นศูนย์"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_view_graph
msgid "On-Time Delivery"
msgstr "การส่งมอบตรงเวลา"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__on_time_rate
msgid "On-Time Delivery Rate"
msgstr "อัตราการส่งมอบตรงเวลา"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_on_time
msgid "On-Time Quantity"
msgstr "จำนวนที่ตรงเวลา"

#. module: purchase_stock
#: model:ir.actions.act_window,name:purchase_stock.action_purchase_vendor_delay_report
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "On-time Delivery"
msgstr "การจัดส่งตรงเวลา"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "On-time Rate"
msgstr "อัตราตรงต่อเวลา"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__orderpoint_id
msgid "Orderpoint"
msgstr "จุดคำสั่ง"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_users__on_time_rate
msgid ""
"Over the past 12 months; the number of products received on time divided by "
"the number of ordered products."
msgstr ""

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Process all the receipt quantities."
msgstr "ดำเนินการปริมาณการรับสินค้าทั้งหมด"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__group_id
msgid "Procurement Group"
msgstr "กลุ่มจัดซื้อ"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_product
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__product_id
msgid "Product"
msgstr "สินค้า"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__category_id
msgid "Product Category"
msgstr "หมวดหมู่สินค้า"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__supplier_id
msgid "Product Supplier"
msgstr "ผู้จำหน่ายสินค้า"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_template
msgid "Product Template"
msgstr "รูปแบบสินค้า"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__propagate_cancel
msgid "Propagate cancellation"
msgstr "เผยแพร่การยกเลิก"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__purchase_line_ids
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__purchase_line_ids
msgid "Purchase Lines"
msgstr "รายการจัดซื้อ"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order
msgid "Purchase Order"
msgstr "ใบสั่งซื้อ"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__purchase_order_line_ids
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__purchase_line_id
msgid "Purchase Order Line"
msgstr "รายการสั่งซื้อ"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__purchase_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_production_lot__purchase_order_ids
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "Purchase Orders"
msgstr "ใบสั่งซื้อ"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_report
msgid "Purchase Report"
msgstr "Purchase Report"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "Purchase Security Lead Time"
msgstr "จัดซื้อระยะเวลานำสินค้าที่ปลอดภัย"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_production_lot__purchase_order_count
msgid "Purchase order count"
msgstr "จำนวนใบสั่งซื้อ"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receipt"
msgstr "การรับ"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receive Products"
msgstr "รับสินค้า"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Receive the ordered products."
msgstr "รับสินค้าที่สั่ง"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "Received Qty Method"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_ids
msgid "Receptions"
msgstr "การรับสินค้า"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Request your vendors to deliver to your customers"
msgstr "ขอให้ผู้ขายจัดส่งให้กับลูกค้าของคุณ"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_report_product_product_replenishment
msgid "Requests for quotation"
msgstr "ร้องขอใบเสนอราคา"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_ids
msgid "Reservation"
msgstr "การจองสินค้า"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "ส่งคืนการหยิบ"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:purchase_stock.field_product_template__route_ids
msgid "Routes"
msgstr "เส้นทาง"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"Schedule automatically generated request for quotations earlier to avoid "
"delays"
msgstr ""

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.product_supplierinfo_replenishment_tree_view
msgid "Set as Supplier"
msgstr "ตั้งเป็นซัพพลายเออร์"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__show_set_supplier_button
msgid "Show Set Supplier Button"
msgstr "แสดงปุ่มตั้งค่าซัพพลายเออร์"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__show_supplier
msgid "Show supplier column"
msgstr "แสดงคอลัมน์ซัพพลายเออร์"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_move
msgid "Stock Move"
msgstr "ย้ายสต็อก"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order_line__qty_received_method__stock_moves
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_line_view_form_inherit
msgid "Stock Moves"
msgstr "การเคลื่อนย้ายสต็อก"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "รายงานการเติมสต๊อก"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_rule
msgid "Stock Rule"
msgstr "กฎสต็อค"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "รายงานกฎสต๊อก"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "ข้อมูลการเติมสต็อคซัพพลายเออร์"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Supplier Pricelist"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_ids
msgid "Supplierinfo"
msgstr "ข้อมูลซัพพลายเออร์"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Technical field used to display the Drop Ship Address"
msgstr ""
"ข้อมูลทางเทคนิคที่ใช้เพื่อแสดงที่อยู่ของดรอปชิป "
"ข้อมูลทางเทคนิคที่ใช้เพื่อแสดงที่อยู่ของดรอปชิป"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__purchase_order_line_ids
msgid "Technical: used to compute quantities."
msgstr ""

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock.py:0
#, python-format
msgid "The following replenishment order has been generated"
msgstr "สร้างคำสั่งเติมสินค้าต่อไปนี้แล้ว"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"The quantities on your purchase order indicate less than billed. You should "
"ask for a refund."
msgstr ""
"จำนวนในใบสั่งซื้อของคุณระบุว่ามีจำนวนน้อยกว่าที่เรียกเก็บเงิน "
"คุณควรดำเนินการขอเงินคืน"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid ""
"There is no matching vendor price to generate the purchase order for product"
" %s (no vendor defined, minimum quantity not reached, dates not valid, ...)."
" Go on the product form and complete the list of vendors."
msgstr ""
"ไม่มีราคาของผู้ขายที่ตรงกันเพื่อสร้างใบสั่งซื้อสำหรับสินค้า %s "
"(ไม่มีการกำหนดผู้ขาย จำนวนไม่ถึงปริมาณขั้นต่ำ วันที่ไม่ถูกต้อง ...) "
"ไปที่แบบฟอร์มสินค้าและกรอกรายชื่อผู้ขาย"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"นี่เป็นการเพิ่มเส้นทางการดรอปชิปที่จะใช้กับสินค้า "
"เพื่อขอให้ผู้ขายจัดส่งให้กับลูกค้าของคุณ "
"สินค้าที่จัดส่งจะสร้างคำขอซื้อเพื่อขอใบเสนอราคาเมื่อคำสั่งซื้อได้รับการยืนยันแล้ว"
" นี่คือโฟลว์ตามความต้องการ "
"ที่อยู่สำหรับจัดส่งที่ร้องขอจะเป็นที่อยู่สำหรับจัดส่งของลูกค้า "
"ซึ่งไม่ใช่คลังสินค้าของคุณ"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__picking_type_id
msgid "This will determine operation type of incoming shipment"
msgstr "ซึ่งจะกำหนดประเภทการดำเนินการของการจัดส่งขาเข้า"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"Those dates couldn’t be modified accordingly on the receipt %s which had "
"already been validated."
msgstr ""
"วันที่เหล่านั้นไม่สามารถแก้ไขได้ตามใบเสร็จรับเงิน %s ที่ได้รับการตรวจสอบแล้ว"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "Those dates have been updated accordingly on the receipt %s."
msgstr "วันที่เหล่านั้นได้รับการปรับปรุงตามใบเสร็จรับเงิน %s แล้ว"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_total
msgid "Total Quantity"
msgstr "ปริมาณรวม"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_picking
msgid "Transfer"
msgstr "การโอน"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"Unable to cancel purchase order %s as some receptions have already been "
"done."
msgstr ""
"ไม่สามารถยกเลิกใบสั่งซื้อ %s ได้ เนื่องจากได้ดำเนินการรับรองบางอย่างแล้ว"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Validate the receipt of all ordered products."
msgstr "ตรวจสอบการรับสินค้าที่สั่งทั้งหมด"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__vendor_id
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.view_warehouse_orderpoint_tree_editable_inherited_mrp
msgid "Vendor"
msgstr "ผู้ขาย"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_vendor_delay_report
msgid "Vendor Delay Report"
msgstr "รายงานความล่าช้าของผู้ขาย"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "Vendor Lead Time"
msgstr "เวลานำร่องของผู้ขาย"

#. module: purchase_stock
#: model_terms:ir.actions.act_window,help:purchase_stock.action_purchase_vendor_delay_report
msgid "Vendor On-time Delivery analysis"
msgstr "การวิเคราะห์การจัดส่งตรงเวลาของผู้ขาย"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse_orderpoint__vendor_id
msgid "Vendor of this product"
msgstr ""

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__picking_type_id
msgid "Warehouse"
msgstr "คลังสินค้า"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "When products are bought, they can be delivered to this warehouse"
msgstr "เมื่อซื้อสินค้าแล้วก็สามารถจัดส่งมาที่คลังสินค้าแห่งนี้ได้"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> a request for quotation is "
"created to fulfill the need.<br/>Note: This rule will be used in combination"
" with the rules<br/>of the reception route(s)"
msgstr ""
"เมื่อผลิตภัณฑ์มีความจำเป็นใน <b>%s</b> <br/> "
"คำขอใบเสนอราคาจะถูกสร้างขึ้นเพื่อตอบสนองความต้องการ <br/>หมายเหตุ: "
"กฎนี้จะใช้ร่วมกับกฎ <br/> ของเส้นทางการรับสินค้า"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"You cannot decrease the ordered quantity below the received quantity.\n"
"Create a return first."
msgstr ""
"คุณไม่สามารถลดปริมาณที่สั่งให้ต่ำกว่าปริมาณที่ได้รับได้\n"
"สร้างการส่งคืนก่อน"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "You must set a Vendor Location for this partner %s"
msgstr "คุณต้องกำหนดสถานที่ตั้งของผู้ขายสำหรับพาร์ทเนอร์รายนี้ %s"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "วัน"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "of"
msgstr "ของ"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "ordered instead of"
msgstr "คำสั่งแทนที่ของ"
