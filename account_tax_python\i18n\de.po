# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"- Steuergruppen: Die Steuer ist eine Gruppe von Untersteuern.\n"
"- Fest: Der Steuerbetrag bleibt unabhängig vom Preis gleich.\n"
"- Prozentsatz des Preises: Der Steuerbetrag ist ein Prozentsatz des Preises:\n"
"   z. B. 100 * (1 + 10 %) = 110 (nicht im Preis enthalten)\n"
"   z. B. 110 / (1 + 10 %) = 100 (im Preis enthalten)\n"
"- Prozentsatz des Preises inkusive Steuern: Der Steuerbetrag ist eine Division des Preises:\n"
"   z. B. 180 / (1 - 10 %) = 200 (nicht im Preis enthalten)\n"
"   z. B. 200 * (1 - 10 %) = 180 (im Preis enthalten)"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_applicable
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_applicable
msgid "Applicable Code"
msgstr "Anwendbarer Code"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Berechnen Sie den Steuerbetrag durch setzen der Variable „result“.\n"
"\n"
":param base_amount: Gleitkommazahl, tatsächlicher Betrag, auf den die Steuer angewendet wird\n"
":param price_unit: Gleitkommazahl\n"
":param quantity: Gleitkommazahl\n"
":param company: res.company-Datensatz singleton\n"
":param product: product.product-Datensatz singleton oder keiner\n"
":param partner: res.partner-Datensatz singleton oder keiner"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Berechne den Steuerbetrag durch setzen der Variable 'result'.\n"
"\n"
":param base_amount: float, Anzuwendender Steuer-Basisbetrag\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product Datensatz singleton oder keiner\n"
":param partner: res.partner Datensatz singleton oder keiner"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Bestimmen Sie, ob die Steuer angewendet wird, durch setzen der Variable „result“ auf True oder False.\n"
"\n"
":param price_unit: Gleitkommazahl\n"
":param quantity: Gleitkommazahl,\n"
":param company: res.company-Datensatz singleton\n"
":param product: product.product-Datensatz singleton oder keiner\n"
":param partner: res.partner-Datensatz singleton oder keiner"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Lege fest, ob die Steuer angesetzt wird, durch setzen der Variable 'result' auf True oder False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product Datensatz singleton oder keiner\n"
":param partner: res.partner Datensatz singleton oder keiner"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_compute
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_compute
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax_template__amount_type__code
msgid "Python Code"
msgstr "Python-Code"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Steuer"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__amount_type
msgid "Tax Computation"
msgstr "Steuerberechnung"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Vorlagen für Steuern"

#. module: account_tax_python
#: code:addons/account_tax_python/models/account_tax.py:0
#: code:addons/account_tax_python/models/account_tax.py:0
#, python-format
msgid ""
"You entered invalid code %r in %r taxes\n"
"\n"
"Error : %s"
msgstr ""
"Sie haben einen ungültigen Code %r in Steuern %r eingegeben\n"
"\n"
"Fehler: %s"
