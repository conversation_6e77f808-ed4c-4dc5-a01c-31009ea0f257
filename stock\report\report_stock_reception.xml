<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Reception Report Labels -->
    <template id="report_reception_report_label">
        <t t-if="quantity" t-set="qtys" t-value="[int(q) for q in quantity.split(',')]"/>
        <t t-else="" t-set="qtys" t-value="[1 for q in range(len(docs))]"/>
        <t t-call="web.basic_layout">
            <div class="page">
                <t t-foreach="range(len(docs))" t-as="index">
                    <t t-set="move" t-value="docs[index]"/>
                    <t t-set="qty" t-value="qtys[index]"/>
                    <t t-set="source" t-value="move._get_source_document()"/>
                    <t t-foreach="range(qty)" t-as="j">
                        <div class="o_label_page o_label_dymo">
                            <div t-out="move.product_id.display_name"/>
                            <div t-if="source" t-out="source.name"/>
                            <div class="address"
                                t-if="move.picking_id and move.picking_id.partner_id"
                                t-field="move.picking_id.partner_id"
                                t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True}'/>
                        </div>
                    </t>
                </t>
            </div>
        </t>
    </template>

    <!-- Reception Report -->
    <record id="stock_reception_report_action" model="ir.actions.report">
        <field name="name">Reception Report</field>
        <field name="model">stock.picking</field>
        <field name="report_type">qweb-html</field>
        <field name="report_name">stock.report_reception</field>
    </record>

    <record id="stock_reception_action" model="ir.actions.client">
        <field name="name">Reception Report</field>
        <field name="tag">reception_report</field>
        <field name="res_model">report.stock.report_reception</field>
    </record>

    <template id="report_reception_body">
        <div class="o_report_reception justify-content-between">
            <div class="o_report_reception_header my-4">
                <h3>
                    <t t-if="pickings">
                        <t t-foreach="pickings" t-as="receipt">
                            <div>
                                <a href="#" res-model="stock.picking" view-type="form" t-att-res-id="receipt.id">
                                    <t t-esc="receipt.display_name"/>
                                </a>
                                <span t-field="receipt.state" t-attf-class="badge badge-pill #{'bg-success-light' if receipt.state == 'done' else 'bg-info-light'}"/>
                            </div>
                        </t>
                    </t>
                    <t t-else="">
                        <span t-esc="reason"/>
                    </t>
                </h3>
            </div>
            <div><table t-if="sources_to_lines" class="o_report_reception_table table table-sm">
                <t t-foreach="sources_to_lines" t-as="source">
                    <thead t-if="report_type == 'html' or any(line['is_assigned'] for line in sources_to_lines[source])">
                        <tr class="bg-light">
                            <th>
                                <i t-if="source[0].priority == '1'" class="o_priority o_priority_star fa fa-star"/>
                                <a name="source_link" href="#" t-att-res-model="source[0]._name" view-type="form" t-att-res-id="source[0].id" t-out="source[0].display_name"/>
                                <span t-if="len(source) > 1">
                                    (<a name="origin_link" href="#" t-att-res-model="source[1]._name" view-type="form" t-att-res-id="source[1].id" t-out="source[1].display_name"/>)
                                </span>
                                <span t-if="source[0]._name == 'stock.picking' and source[0].partner_id">:
                                    <a name="source_link" href="#"
                                        t-att-res-model="source[0].partner_id._name"
                                        t-att-res-id="source[0].partner_id.id"
                                        view-type="form"
                                        t-out="source[0].partner_id.name"/>
                                </span>
                            </th>
                            <th>Expected Delivery: <t t-esc="sources_to_formatted_scheduled_date[source]"/></th>
                            <th t-if="report_type == 'html' and any(s['move_ins'] for s in sources_to_lines[source])">
                                <button t-if="any(not s['is_assigned'] and s['is_qty_assignable'] for s in sources_to_lines[source])"
                                        class="btn btn-sm btn-primary o_report_reception_assign o_assign_all"
                                        name="assign_source_link">
                                        Assign All
                                </button>
                            </th>
                            <th t-if="report_type == 'html'">
                                <button t-if="any(s['move_ins'] for s in sources_to_lines[source])"
                                        class="btn btn-sm btn-primary o_print_label o_print_label_all"
                                        t-att-disabled="not any(s['is_assigned'] for s in sources_to_lines[source])"
                                        name="print_labels">
                                        <span class="d-none d-sm-block o_print_label_text">Print Labels</span>
                                        <span class="d-block d-sm-none fa fa-print"/>
                                </button>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-set="possible_moves" t-value="sources_to_lines[source]"/>
                        <t t-foreach="possible_moves" t-as="line">
                            <!-- don't print non-assigned lines when printing pdf -->
                            <tr t-if="report_type == 'html' or line['is_assigned']">
                                <td>
                                    <t t-esc="line['product']['display_name']"/>
                                </td>
                                <td>
                                    <t t-esc="line['quantity']"/>
                                    <t groups="uom.group_uom" t-esc="line['uom']"/>
                                    <button t-if="report_type == 'html'"
                                        class="o_report_reception_forecasted btn btn-link fa fa-area-chart"
                                        t-att-move-id="line['move_out'].id"
                                        name="forecasted_report_link">
                                    </button>
                                </td>
                                <td t-if="report_type == 'html' and line['is_qty_assignable']">
                                    <button t-if="not line['is_assigned']"
                                        class="btn btn-sm btn-primary o_report_reception_assign"
                                        t-attf-model="stock.move"
                                        t-att-move-id="line['move_out'].id"
                                        t-att-move-ins-ids="line['move_ins']"
                                        t-att-qty="line['quantity']"
                                        name="assign_link">
                                        Assign
                                    </button>
                                    <button t-if="line['is_assigned']"
                                        class="btn btn-sm btn-primary o_report_reception_unassign"
                                        t-attf-model="stock.move"
                                        t-att-move-id="line['move_out'].id"
                                        t-att-move-ins-ids="line['move_ins']"
                                        t-att-qty="line['quantity']"
                                        name="unassign_link">
                                        Unassign
                                    </button>
                                </td>
                                <td t-if="report_type == 'html'">
                                    <button t-if="line['is_qty_assignable'] and line['source']"
                                        t-att-move-id="line['move_out'].id"
                                        t-att-qty="line['quantity']"
                                        t-attf-class="btn btn-sm btn-primary o_print_label"
                                        t-att-disabled="not line['is_assigned']"
                                        name="print_label">
                                        <span class="d-none d-sm-block o_print_label_text">Print Label</span>
                                        <span class="d-block d-sm-none fa fa-print"/>
                                    </button>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </t>
            </table>
            <p t-else="">
                No allocation need found for incoming products.
            </p></div>
        </div>
    </template>

    <template id="report_reception">
        <t t-call="web.html_container">
            <t t-if="report_type == 'pdf'" t-call="web.internal_layout">
                <t t-call="stock.report_reception_body"/>
            </t>
            <t t-else="">
                <t t-call="stock.report_reception_body"/>
            </t>
        </t>
    </template>
</odoo>
