# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* uom
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> (Quartile) <ta<PERSON><PERSON>@roomsfor.hk>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
msgid ""
"<span class=\"oe_grey oe_inline\">\n"
"                                    e.g: 1*(reference unit)=ratio*(this unit)\n"
"                                </span>"
msgstr ""
"<span class=\"oe_grey oe_inline\">\n"
"                                    例: 1*(reference unit)=ratio*(this unit)\n"
"                                </span>"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
msgid ""
"<span class=\"oe_grey oe_inline\">\n"
"                                    e.g: 1*(this unit)=ratio*(reference unit)\n"
"                                </span>"
msgstr ""
"<span class=\"oe_grey oe_inline\">\n"
"                                    例: 1*(this unit)=ratio*(reference unit)\n"
"                                </span>"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__active
msgid "Active"
msgstr "有効"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_form_action
msgid "Add a new unit of measure"
msgstr "新しい単位を追加"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_categ_form_action
msgid "Add a new unit of measure category"
msgstr "新しい単位カテゴリを追加"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Archived"
msgstr "アーカイブ済"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__factor_inv
msgid "Bigger Ratio"
msgstr "比率"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__bigger
msgid "Bigger than the reference Unit of Measure"
msgstr "基準単位より大きい"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__category_id
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Category"
msgstr "カテゴリ"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__color
msgid "Color"
msgstr "色"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__ratio
msgid "Combined Ratio"
msgstr "複合比率"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr "単位間の変換は同じカテゴリに属している場合のみ可能です。変換は比率に基づいて行われます。"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__create_uid
#: model:ir.model.fields,field_description:uom.field_uom_uom__create_uid
msgid "Created by"
msgstr "作成者"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__create_date
#: model:ir.model.fields,field_description:uom.field_uom_uom__create_date
msgid "Created on"
msgstr "作成日"

#. module: uom
#: model:uom.uom,name:uom.product_uom_day
msgid "Days"
msgstr "日"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__display_name
#: model:ir.model.fields,field_description:uom.field_uom_uom__display_name
msgid "Display Name"
msgstr "表示名"

#. module: uom
#: model:uom.uom,name:uom.product_uom_dozen
msgid "Dozens"
msgstr "ダース"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_category__reference_uom_id
msgid "Dummy field to keep track of reference uom change"
msgstr ""

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Group By"
msgstr "グループ化"

#. module: uom
#: model:uom.uom,name:uom.product_uom_hour
msgid "Hours"
msgstr "時間"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__factor_inv
msgid ""
"How many times this Unit of Measure is bigger than the reference Unit of "
"Measure in this category: 1 * (this unit) = ratio * (reference unit)"
msgstr "このカテゴリの基準単位に比べて、この単位は何倍大きいか：1 * (この単位) = 比率 * (基準単位)"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__factor
msgid ""
"How much bigger or smaller this unit is compared to the reference Unit of "
"Measure for this category: 1 * (reference unit) = ratio * (this unit)"
msgstr "この単位がどのくらい大きいか小さいかは、このカテゴリの基準単位と比較されます。: 1 * (基準単位) = 比率 * (この単位)"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__id
#: model:ir.model.fields,field_description:uom.field_uom_uom__id
msgid "ID"
msgstr "ID"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category____last_update
#: model:ir.model.fields,field_description:uom.field_uom_uom____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__write_uid
#: model:ir.model.fields,field_description:uom.field_uom_uom__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__write_date
#: model:ir.model.fields,field_description:uom.field_uom_uom__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: uom
#: model:uom.category,name:uom.uom_categ_length
msgid "Length / Distance"
msgstr "長さ / 距離"

#. module: uom
#: model:res.groups,name:uom.group_uom
msgid "Manage Multiple Units of Measure"
msgstr "複数の単位を管理"

#. module: uom
#: model:ir.model,name:uom.model_uom_uom
msgid "Product Unit of Measure"
msgstr "プロダクト単位"

#. module: uom
#: model:ir.model,name:uom.model_uom_category
msgid "Product UoM Categories"
msgstr "プロダクト単位カテゴリ"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__factor
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_form_view
msgid "Ratio"
msgstr "比率"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__reference
msgid "Reference Unit of Measure for this category"
msgstr "このカテゴリの基準単位"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__reference_uom_id
msgid "Reference UoM"
msgstr "参照単位"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__rounding
msgid "Rounding Precision"
msgstr "丸め精度"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Search UOM"
msgstr "単位を検索"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_categ_view_search
msgid "Search UoM Category"
msgstr "単位カテゴリを検索"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__smaller
msgid "Smaller than the reference Unit of Measure"
msgstr "基準単位より小さい"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"Some critical fields have been modified on %s.\n"
"Note that existing data WON'T be updated by this change.\n"
"\n"
"As units of measure impact the whole system, this may cause critical issues.\n"
"E.g. modifying the rounding could disturb your inventory balance.\n"
"\n"
"Therefore, changing core units of measure in a running database is not recommended."
msgstr ""
"いくつかの重要なフィールドが%sで変更されました。\n"
"既存のデータはこの変更によって更新されないことに注意して下さい。\n"
"\n"
"単位はシステム全体に影響するため、この変更は重大な問題を引き起こす可能性があります。\n"
"例えば、四捨五入を変更すると在庫残高を乱す可能性があります。\n"
"\n"
"よって、実行中のデータベースで中心的な単位を変更することは推奨されません。"

#. module: uom
#: model:uom.category,name:uom.uom_categ_surface
msgid "Surface"
msgstr "表面"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__rounding
msgid ""
"The computed quantity will be a multiple of this value. Use 1.0 for a Unit "
"of Measure that cannot be further split, such as a piece."
msgstr "計算された数量はこの値の倍数になります。小片のような更に分割できない単位には1.0を使用します。"

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_factor_gt_zero
msgid "The conversion ratio for a unit of measure cannot be 0!"
msgstr "単位の変換比率は0にできません。"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"The following units of measure are used by the system and cannot be deleted: %s\n"
"You can archive them instead."
msgstr ""
"以下の単位はシステムで使用されており、削除することはできません:%s \n"
"代わりにアーカイブすることができます。"

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_factor_reference_is_one
msgid "The reference unit must have a conversion factor equal to 1."
msgstr "参照単位は、換算係数が1に等しい必要があります。"

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_rounding_gt_zero
msgid "The rounding precision must be strictly positive."
msgstr "丸め精度は必ず正の値にして下さい。"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"The unit of measure %s defined on the order line doesn't belong to the same "
"category as the unit of measure %s defined on the product. Please correct "
"the unit of measure defined on the order line or on the product, they should"
" belong to the same category."
msgstr ""
"オーダ明細で定義された単位%sが、プロダクトで定義された単位と同じカテゴリ%sに属していません。オーダ明細またはプロダクトで定義された単位を修正して下さい。"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "The value of ratio could not be Zero"
msgstr "比率の値がゼロになることはありません"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__uom_type
msgid "Type"
msgstr "タイプ"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__active
msgid ""
"Uncheck the active field to disable a unit of measure without deleting it."
msgstr "アクティブ項目のチェックを外すことで、単位を削除することなく非表示にできます。"

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_unit
msgid "Unit"
msgstr "単位"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__name
msgid "Unit of Measure"
msgstr "単位"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__name
msgid "Unit of Measure Category"
msgstr "単位カテゴリ"

#. module: uom
#: model:uom.uom,name:uom.product_uom_unit
msgid "Units"
msgstr "個"

#. module: uom
#: model:ir.actions.act_window,name:uom.product_uom_form_action
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_tree_view
msgid "Units of Measure"
msgstr "単位"

#. module: uom
#: model:ir.actions.act_window,name:uom.product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "単位カテゴリ"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_tree_view
msgid "Units of Measure categories"
msgstr "単位カテゴリ"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_categ_form_action
msgid ""
"Units of measure belonging to the same category can be\n"
"            converted between each others. For example, in the category\n"
"            <i>'Time'</i>, you will have the following units of measure:\n"
"            Hours, Days."
msgstr ""
"同じカテゴリに属する単位は、互いの間で変換することができます。\n"
"           たとえば、<i>時間</i>であれば以下の単位を持っています:\n"
"            時間, 日数."

#. module: uom
#: code:addons/uom/models/uom_uom.py:0 code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "UoM category %s should have a reference unit of measure."
msgstr "単位カテゴリ%sには基準となる単位を設定する必要があります。"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "UoM category %s should only have one reference unit of measure."
msgstr "単位カテゴリ%sは参照単位を1つのみ有する必要があります。"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__uom_ids
msgid "Uom"
msgstr "単位"

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_vol
msgid "Volume"
msgstr "容積"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "Warning for %s"
msgstr "%s の警告です。"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "Warning!"
msgstr "警告！"

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_kgm
msgid "Weight"
msgstr "重量"

#. module: uom
#: model:uom.category,name:uom.uom_categ_wtime
msgid "Working Time"
msgstr "作業時間"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_form_action
msgid ""
"You must define a conversion rate between several Units of\n"
"            Measure within the same category."
msgstr "同じカテゴリ内の各計量単位の変換レートを定義しなくてはなりません。"

#. module: uom
#: model:uom.uom,name:uom.product_uom_cm
msgid "cm"
msgstr "cm"

#. module: uom
#: model:uom.uom,name:uom.product_uom_floz
msgid "fl oz (US)"
msgstr "fl oz (US)"

#. module: uom
#: model:uom.uom,name:uom.product_uom_foot
msgid "ft"
msgstr "ft"

#. module: uom
#: model:uom.uom,name:uom.uom_square_foot
msgid "ft²"
msgstr "ft²"

#. module: uom
#: model:uom.uom,name:uom.product_uom_cubic_foot
msgid "ft³"
msgstr "ft³"

#. module: uom
#: model:uom.uom,name:uom.product_uom_gal
msgid "gal (US)"
msgstr "gal (US)"

#. module: uom
#: model:uom.uom,name:uom.product_uom_inch
msgid "in"
msgstr "in"

#. module: uom
#: model:uom.uom,name:uom.product_uom_cubic_inch
msgid "in³"
msgstr "in³"

#. module: uom
#: model:uom.uom,name:uom.product_uom_kgm
msgid "kg"
msgstr "kg"

#. module: uom
#: model:uom.uom,name:uom.product_uom_km
msgid "km"
msgstr "km"

#. module: uom
#: model:uom.uom,name:uom.product_uom_lb
msgid "lb"
msgstr "lb"

#. module: uom
#: model:uom.uom,name:uom.product_uom_mile
msgid "mi"
msgstr "mi"

#. module: uom
#: model:uom.uom,name:uom.product_uom_millimeter
msgid "mm"
msgstr "mm"

#. module: uom
#: model:uom.uom,name:uom.uom_square_meter
msgid "m²"
msgstr "m²"

#. module: uom
#: model:uom.uom,name:uom.product_uom_cubic_meter
msgid "m³"
msgstr "m³"

#. module: uom
#: model:uom.uom,name:uom.product_uom_oz
msgid "oz"
msgstr "oz"

#. module: uom
#: model:uom.uom,name:uom.product_uom_qt
msgid "qt (US)"
msgstr "qt (US)"
