<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_partner_iap_action" model="ir.actions.act_window">
        <field name="name">IAP Partner</field>
        <field name="res_model">res.partner.iap</field>
        <field name='view_mode'>tree,form</field>
    </record>

    <record id="res_partner_iap_view_form" model="ir.ui.view">
        <field name="name">res.partner.iap.view.form</field>
        <field name="model">res.partner.iap</field>
        <field name="arch" type="xml">
            <form string="IAP Partner">
                <sheet>
                    <h1><field name="partner_id"/></h1>
                    <group>
                        <field name="iap_search_domain"/>
                        <field name="iap_enrich_info"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="res_partner_iap_view_tree" model="ir.ui.view">
        <field name="name">res.partner.iap.view.tree</field>
        <field name="model">res.partner.iap</field>
        <field name="arch" type="xml">
            <tree string="IAP Partner">
                <field name="partner_id"/>
                <field name="iap_search_domain"/>
            </tree>
        </field>
    </record>

    <menuitem
        id="res_partner_iap_menu"
        name="IAP Partners"
        parent="iap.iap_root_menu"
        action="res_partner_iap_action"
        sequence="50"/>
</odoo>
