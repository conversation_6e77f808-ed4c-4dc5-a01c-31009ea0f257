# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_org_chart
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_base
msgid "Basic Employee"
msgstr "Základný zamestnanec"

#. module: hr_org_chart
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Direct and indirect subordinates"
msgstr "Priami a nepriami podriadení"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "Direct subordinates"
msgstr "Priamy podriadení"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee
msgid "Employee"
msgstr "Zamestnanec"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "In order to get an organigram, set a manager and save the record."
msgstr ""
"Aby ste získali graf organizačnej štruktúry, nastavte správcu a uložte "
"záznam."

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_all_count
msgid "Indirect Subordinates Count"
msgstr "Počet nepriamych podriadených"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "Indirect subordinates"
msgstr "Nepriami podriadení"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "More managers"
msgstr "Viac manažérov"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "No hierarchy position."
msgstr "Žiadne hierarchické postavenie."

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_public_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.res_users_view_form
msgid "Organization Chart"
msgstr "Organizačná štruktúra"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_public
msgid "Public Employee"
msgstr "Verejný zamestnanec"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "Redirect"
msgstr "Presmerovať"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:108
#, python-format
msgid "See All"
msgstr ""

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Subordinates"
msgstr "Podriadený"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/js/hr_org_chart.js:181
#, python-format
msgid "Team"
msgstr "Tím"

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "This employee has no manager or subordinate."
msgstr "Tento zamestnanec nemá vedúceho ani podriadeného."

#. module: hr_org_chart
#. openerp-web
#: code:addons/hr_org_chart/static/src/xml/hr_org_chart.xml:0
#, python-format
msgid "Total"
msgstr "Celkom"
