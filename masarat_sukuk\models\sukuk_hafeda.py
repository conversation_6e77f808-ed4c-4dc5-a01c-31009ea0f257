# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import date, datetime
from odoo.exceptions import ValidationError

class SukukHafeda(models.Model): ##### حافظة
    _name = "sukuk.management.hafida"

    name = fields.Char(string='الاسم', readonly=True)
    attachment_id = fields.Many2one("ir.attachment")
    attachment = fields.Binary(related='attachment_id.datas', string="Attachments")