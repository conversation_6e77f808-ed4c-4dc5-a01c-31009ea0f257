# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event
# 
# Translators:
# <PERSON> <must<PERSON><PERSON>@cubexco.com>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-27 13:05+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_count
msgid "# Registrations"
msgstr "عدد التسجيلات "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "'. Showing results for '"
msgstr "'. يتم عرض النتائج لـ '"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "(Ref:"
msgstr "(المرجع: "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "(only"
msgstr "(فقط "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid ", .oe_country_events, .s_speaker_bio"
msgstr ", .oe_country_events, .s_speaker_bio"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "<b>Drag and Drop</b> this snippet below the event title."
msgstr "<b>قم بسحب وإفلات</b> هذه القصاصة أسفل عنوان الفعالية. "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>End</b>"
msgstr "<b>النهاية</b> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>Start</b>"
msgstr "<b>البداية</b> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<b>View all</b>"
msgstr "<b>عرض الكل</b> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid ""
"<em>Write here a quote from one of your attendees. It gives confidence in "
"your events.</em>"
msgstr ""
"<em>اكتب اقتباساً هنا من أحد حاضريك. يعطي ذلك الثقة في فعالياتك.</em> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
msgid ""
"<font style=\"font-size: 62px;\" "
"class=\"o_default_snippet_text\">Introduction</font>"
msgstr ""
"<font style=\"font-size: 62px;\" "
"class=\"o_default_snippet_text\">المقدمة</font> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<i class=\"fa fa-ban mr-2\"/>Sold Out"
msgstr "<i class=\"fa fa-ban mr-2\"/>نفدت الكمية "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban mr-2\"/>غير منشور "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-check mr-2\"/>Registered"
msgstr "<i class=\"fa fa-check mr-2\"/>تم التسجيل "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"
msgstr ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook \"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<i class=\"fa fa-flag mr-2\"/>Events:"
msgstr "<i class=\"fa fa-flag mr-2\"/>الفعاليات: "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to Google Calendar"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> إضافة إلى تقويم Google "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to iCal/Outlook"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> إضافة إلى iCal أو Outlook "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid ""
"<i class=\"fa fa-gear mr-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event tickets\"/><em>Configure Tickets</em>"
msgstr ""
"<i class=\"fa fa-gear mr-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"تهيئة تذاكر الفعالية \"/><em>تهيئة التذاكر</em> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github \"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
#: model_terms:ir.ui.view,arch_db:website_event.s_country_events
msgid "<i class=\"fa fa-globe mr-2\"/>Upcoming Events"
msgstr "<i class=\"fa fa-globe mr-2\"/>الفعاليات القادمة "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"إنستغرام \"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn \"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid ""
"<i class=\"fa fa-long-arrow-left text-primary mr-2\"/>\n"
"                            <span>All Events</span>"
msgstr ""
"<i class=\"fa fa-long-arrow-left text-primary mr-2\"/>\n"
"                            <span>كافة الفعاليات</span> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"تويتر \"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"يوتيوب \"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"<span class=\"badge badge-secondary text-uppercase "
"o_wevent_badge\">Speaker</span>"
msgstr ""
"<span class=\"badge badge-secondary text-uppercase "
"o_wevent_badge\">المتحدث</span> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "<span class=\"navbar-brand h4 my-0 mr-auto\">Events</span>"
msgstr "<span class=\"navbar-brand h4 my-0 mr-auto\">الفعاليات</span> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_days pr-1\">0</span><span "
"class=\"o_countdown_metric pr-1\">days</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_days pr-1\">0</span><span "
"class=\"o_countdown_metric pr-1\">أيام</span> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"py-2 o_wevent_registration_title text-left\">Tickets</span>"
msgstr ""
"<span class=\"py-2 o_wevent_registration_title text-left\">التذاكر</span> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"text-dark font-weight-bold align-middle px-2\">Qty</span>"
msgstr ""
"<span class=\"text-dark font-weight-bold align-middle px-2\">الكمية</span> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                    <i class=\"fa fa-ban mr-2\"/>Sold Out\n"
"                                </span>"
msgstr ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                    <i class=\"fa fa-ban mr-2\"/>نفدت الكمية\n"
"                                </span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr "<span>الفعاليات عبر الإنترنت</span> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span>Tickets</span>\n"
"                        <span class=\"btn p-0 close d-none\">×</span>"
msgstr ""
"<span>التذاكر</span>\n"
"                        <span class=\"btn p-0 close d-none\">×</span> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong> You ordered more tickets than available seats</strong>"
msgstr "<strong> لقد طلبت عدد تذاكر أكبر من عدد المقاعد المتاحة</strong> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "A past event"
msgstr "فعالية سابقة "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "About us"
msgstr "من نحن"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Add to Calendar"
msgstr "إضافة إلى التقويم "

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "كافة الدول"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "All countries"
msgstr "كافة الدول"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid "Allows to display and manage event-specific menus on website."
msgstr "يتيح لك عرض وإدارة القوائم الخاصة بالفعالية في الموقع الإلكتروني. "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Apply"
msgstr "تطبيق"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Ask questions to attendees when registering online"
msgstr "اطرح الأسئلة على الحاضرين عند التسجيل عبر الإنترنت "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"بعمر 13 فقط، كان جون دو قد بدأ بتطوير تطبيقات الأعمال الأولى الخاصة به "
"بالفعل للعملاء. بعد حصوله على شهادة في في الهندسة المدنية، قام بتأسيس "
"TinyERP، وكانت تلك المرحلة الأولى لـOpenERP والذي تمت تسميته أودو (Odoo) بعد"
" ذلك، وهو أكثر تطبيقات الأعمال تثبيتاً في العالم بأسره. "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendees"
msgstr "الحاضرين "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "Author"
msgstr "الكاتب "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__can_publish
msgid "Can Publish"
msgstr "بإمكانه النشر "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
#, python-format
msgid "Cancel"
msgstr "إلغاء "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr "إغلاق "

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
#, python-format
msgid "Community"
msgstr "المجتمع "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu
#: model:ir.model.fields,field_description:website_event.field_event_type__community_menu
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__community
msgid "Community Menu"
msgstr "قائمة المجتمع "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "Company"
msgstr "الشركة "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Continue"
msgstr "متابعة"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__cover_properties
msgid "Cover Properties"
msgstr "خصائص الغلاف"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Create"
msgstr "إنشاء"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Create \"%s\""
msgstr "إنشاء \"%s\" "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Custom Range"
msgstr "نطاق مخصص "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Date &amp; Time"
msgstr "الوقت والتاريخ "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "التاريخ (من الأحدث إلى الأقدم) "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "التاريخ (من الأقدم إلى الأحدث) "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Description"
msgstr "الوصف"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Discard"
msgstr "إهمال "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr "قم بعرض قائمة مخصصة على الموقع الإلكتروني "

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__community_menu
#: model:ir.model.fields,help:website_event.field_event_type__community_menu
msgid "Display community tab on website"
msgstr "عرض شريط المجتمع ءفي الموقع الإلكتروني "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Don't forget to click <b>save</b> when you're done."
msgstr "لا تنسَ الضغط على <b>حفظ</b> عند انتهائك. "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "End -"
msgstr "النهاية -"

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__event_id
msgid "Event"
msgstr "الفعالية"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu_ids
msgid "Event Community Menus"
msgstr "قوائم مجتمع الفعالية "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Event Date"
msgstr "تاريخ الفعالية "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr "موقع الفعالية"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr "قائمة الفعالية"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Event Name"
msgstr "اسم الفعالية"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration
msgid "Event Registration"
msgstr "تسجيل الفعالية"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_ids
msgid "Event Registrations"
msgstr "تسجيلات الفعالية"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/customize_options.xml:0
#, python-format
msgid "Event Specific"
msgstr "خاص بفعالية محددة "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/customize_options.xml:0
#, python-format
msgid "Event Sub-menu"
msgstr "القائمة الفرعية للفعالية "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__subtitle
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Subtitle"
msgstr "العنوان الفرعي للفعالية "

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag_category
msgid "Event Tag Category"
msgstr "فئة علامة تصنيف الفعالية "

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Template"
msgstr "قالب الفعالية "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Title"
msgstr "عنوان الفعالية "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr "لم يتم العثور على الفعالية! "

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr "تم نشر الفعالية"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr "الفعالية غير منشورة"

#. module: website_event
#: code:addons/website_event/models/website.py:0
#: model:website.menu,name:website_event.menu_events
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
#, python-format
msgid "Events"
msgstr "الفعاليات"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Expired"
msgstr "منتهي الصلاحية"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_register_cta
#: model:ir.model.fields,field_description:website_event.field_event_type__menu_register_cta
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Extra Register Button"
msgstr "زر تسجيل إضافي "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event, and join the "
"conversation."
msgstr "تعرف على آراء الجمهور عن هذه الفعالية، ثم انضم إلى المحادثة.  "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "Follow Us"
msgstr "تابعنا"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Following content will appear on all events."
msgstr "سيظهر المحتوى التالي في كافة الفعاليات. "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Get the direction"
msgstr "احصل على الاتجاهات "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Google"
msgstr "Google"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__id
msgid "ID"
msgstr "المُعرف"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__introduction
#, python-format
msgid "Introduction"
msgstr "المقدمة"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu
msgid "Introduction Menu"
msgstr "قائمة المقدمة "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu_ids
msgid "Introduction Menus"
msgstr "قوائم المقدمة "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_done
msgid "Is Done"
msgstr "منتهي "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "مستمر "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr "يشارك"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__is_published
msgid "Is Published"
msgstr "تم نشره "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "John DOE"
msgstr "جون دو "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: website_event
#. openerp-web
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__location
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
#, python-format
msgid "Location"
msgstr "الموقع "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu
msgid "Location Menu"
msgstr "قائمة الموقع "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu_ids
msgid "Location Menus"
msgstr "قوائم الموقع "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Looking great! Let's now <b>publish</b> this page so that it becomes "
"<b>visible</b> on your website!"
msgstr ""
"يبدو رائعاً! فلنقم الآن <b>بنشر</b> هذه الصفحة حتى تصبح <b>مرئية</b> في "
"موقعك الإلكتروني! "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_search
msgid "Main Contact"
msgstr "جهة الاتصال الرئيسية "

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_visitor__parent_id
msgid "Main identity"
msgstr "الهوية الرئيسية "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_id
msgid "Menu"
msgstr "القائمة"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "نوع القائمة "

#. module: website_event
#: model:ir.actions.act_window,name:website_event.website_event_menu_action
msgid "Menus"
msgstr "القوائم"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "N/A"
msgstr "غير منطبق "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Name"
msgstr "الاسم"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "New Event"
msgstr "فعالية جديدة"

#. module: website_event
#: code:addons/website_event/models/website.py:0
#, python-format
msgid "Next Events"
msgstr "الفعاليات التالية"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "No Website Menu Items yet!"
msgstr "لا توجد عناصر قائمة للموقع الإلكتروني بعد! "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No events found."
msgstr "لم يتم العثور على فعاليات. "

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.event_registration_action_from_visitor
msgid "No registration linked to this visitor"
msgstr "لا يوجد تسجيل مرتبط بهذا الزائر "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No results found for '"
msgstr "لم يتم العثور على نتائج لـ’"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "On Site"
msgstr "في الموقع "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Online"
msgstr "عبر الإنترنت "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "المنظِّم"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Our Trainings"
msgstr "تدريباتنا"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__parent_id
msgid "Parent"
msgstr "الأصل"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Past Events"
msgstr "الفعاليات السابقة"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Phone <small>(Optional)</small>"
msgstr "رقم الهاتف <small>(اختياري)</small> "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Photos"
msgstr "الصور "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "يرجى ملء هذا الحقل"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.js:0
#, python-format
msgid "Please select at least one ticket."
msgstr "يرجى تحديد تذكرة واحدة على الأقل. "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Questions"
msgstr "الأسئلة"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Ref:"
msgstr "المرجع: "

#. module: website_event
#. openerp-web
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/js/register_toaster_widget.js:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__register
#: model_terms:ir.ui.view,arch_db:website_event.layout
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
#, python-format
msgid "Register"
msgstr "تسجيل"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Register Button"
msgstr "زر التسجيل "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu
msgid "Register Menu"
msgstr "قائمة التسجيل "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu_ids
msgid "Register Menus"
msgstr "قوائم التسجيل "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registered_ids
msgid "Registered Events"
msgstr "الفعاليات المسجلة "

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
#, python-format
msgid "Registration"
msgstr "التسجيل"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Registration confirmed!"
msgstr "تم تأكيد التسجيل! "

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_registration_action_from_visitor
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_form
msgid "Registrations"
msgstr "التسجيلات"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations Closed"
msgstr "تم إغلاق التسجيلات "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations are <b>closed</b>"
msgstr "تم <b>إغلاق</b> التسجيلات "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations not yet open"
msgstr "لم تُفتح التسجيلات بعد "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_remaining
msgid "Remaining before start"
msgstr "متبقي قبل البدء "

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_remaining
msgid "Remaining time before event starts (minutes)"
msgstr "الوقت المتبقي قبل بدء الفعالية (بالدقائق) "

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_id
msgid "Restrict publishing to this website."
msgstr "قصر إمكانية النشر على هذا الموقع الإلكتروني. "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr "العودة إلى قائمة الفعاليات."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_seo_optimized
msgid "SEO optimized"
msgstr "تم تحسين محركات البحث"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "SHARE"
msgstr "مشاركة "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales end on"
msgstr "تنتهي المبيعات في "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales start on"
msgstr "تبدأ المبيعات في "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_search_box_input
msgid "Search an event..."
msgstr "البحث عن فعالية... "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all events from"
msgstr "انظر إلى كافة الفعاليات من "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Select Venue"
msgstr "تحديد المكان "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__seo_name
msgid "Seo name"
msgstr "اسم محسنات محرك البحث "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_tree
msgid "Show on Website"
msgstr "إظهار في الموقع الإلكتروني "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr "نفدت الكمية "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr "عذراً، لم تعد الفعالية المطلوبة متوفرة. "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Start -"
msgstr "البداية -"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_today
msgid "Start Today"
msgstr "ابدأ اليوم "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Start → End"
msgstr "النهاية → البداية "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
msgid "Starts <span/>"
msgstr "يبدأ في <span/>"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_url
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_url
msgid "The full URL to access the document through the website."
msgstr "رابطURL الكامل للوصول إلى المستند من خلال الموقع الإلكتروني. "

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "This month"
msgstr "هذا الشهر"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "This shortcut will bring you right back to the event form."
msgstr "سيعيدك هذا الاختصار إلى استمارة الفعالية. "

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "This technical menu displays all event sub-menu items."
msgstr "تعرض هذه القائمة التقنية كافة عناصر القائمة الفرعية. "

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "This ticket is not available for sale for this event"
msgstr "هذه التذكرة غير متاحة للبيع لهذه الفعالية "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket #"
msgstr "رقم التذكرة "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Sales starting on"
msgstr "تبدأ مبيعات التذاكر في "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets for this Event are <b>Sold Out</b>"
msgstr "<b>نفدت كمية</b> التذاكر لهذه الفعالية "

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Today"
msgstr "اليوم"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Toggle navigation"
msgstr "تنقل التبديل "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "Unpublished"
msgstr "غير منشور"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#, python-format
msgid "Upcoming Events"
msgstr "الفعاليات القادمة "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Use the top button '<b>+ New</b>' to create an event."
msgstr "استخدم الزر العلوي '<b>+ جديد</b>' لإنشاء فعالية. "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Use this <b>shortcut</b> to easily access your event web page."
msgstr "استخدم هذا <b>الاختصار</b> للوصول إلى صفحة الويب لفعاليتك بكل سهولة. "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "Use this paragrah to write a short text about your events or company."
msgstr "استخدم هذه الفقرة لكتابة نص قصير عن فعالياتك أو شركتك. "

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_event_menu__view_id
msgid "Used when not being an url based menu"
msgstr "يُستخدم عندما لا يكون قائمة مبنية على رابط URL "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Venue"
msgstr "المبنى "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__view_id
msgid "View"
msgstr "عرض "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_published
msgid "Visible on current website"
msgstr "مرئي في الموقع الإلكتروني الحالي "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__visitor_id
msgid "Visitor"
msgstr "الزائر"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Want to change your event configuration? Let's go back to the event form."
msgstr "أترغب في تغيير تهيئة فعاليتك؟ فلنعد إلى استمارة الفعالية. "

#. module: website_event
#: model:ir.model,name:website_event.model_website
#: model:ir.model.fields,field_description:website_event.field_event_event__website_id
msgid "Website"
msgstr "الموقع الإلكتروني"

#. module: website_event
#: model:ir.model,name:website_event.model_website_event_menu
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_form
msgid "Website Event Menu"
msgstr "قائمة فعالية الموقع الإلكتروني "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_search
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_tree
msgid "Website Event Menus"
msgstr "قوائم فعالية الموقع الإلكتروني "

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr "الصفحة الرئيسية للموقع الإلكتروني "

#. module: website_event
#: model:ir.model,name:website_event.model_website_menu
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Website Menu"
msgstr "قائمة الموقع الإلكتروني "

#. module: website_event
#: model:ir.ui.menu,name:website_event.menu_website_event_menu
msgid "Website Menus"
msgstr "قوائم الموقع الإلكتروني "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Website Submenu"
msgstr "القائمة الفرعية للموقع الإلكتروني "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_url
msgid "Website URL"
msgstr "رابط URL للموقع الإلكتروني "

#. module: website_event
#: model:ir.model,name:website_event.model_website_visitor
msgid "Website Visitor"
msgstr "زائر الموقع الإلكتروني "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_description
msgid "Website meta description"
msgstr "الوصف الدلالي في الموقع الإلكتروني "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_keywords
msgid "Website meta keywords"
msgstr "الكلمات الدلالية في الموقع الإلكتروني "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_title
msgid "Website meta title"
msgstr "العنوان الدلالي في الموقع الإلكتروني "

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_og_img
msgid "Website opengraph image"
msgstr "صورة الرسم البياني المفتوح في الموقع الإلكتروني "

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "ما إذا كانت الفعالية قد بدأت أم لا "

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_done
msgid "Whether event is finished"
msgstr "ما إذا كانت الفعالية قد انتهت أم لا "

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_today
msgid "Whether event is going to start today if still not ongoing"
msgstr "ما إذا كانت الفعالية ستبدأ اليوم إذا لم تكن جارية أم لا "

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"With the Edit button, you can <b>customize</b> the web page visitors will "
"see when registering."
msgstr ""
"باستخدام زر التحرير، بإمكانك <b>تخصيص</b> صفحة الويب التي سيراها زوارك عند "
"التسجيل. "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "available)"
msgstr "متاح) "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "iCal/Outlook"
msgstr "iCal/Outlook"
