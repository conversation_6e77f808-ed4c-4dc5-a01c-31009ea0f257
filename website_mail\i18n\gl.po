# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_mail
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-09-30 09:29+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Galician (http://www.transifex.com/odoo/odoo-9/language/gl/)\n"
"Language: gl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_mail
#: model:mail.template,body_html:website_mail.mail_template_data_module_install_website_mail
msgid ""
"\n"
"            <div style=\"margin: 10px auto;\">\n"
"            <table cellspacing=\"0\" cellpadding=\"0\" style=\"width:100%;"
"\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td style=\"padding:2px;width:30%;\">\n"
"                            <img src=\"web/static/src/img/logo.png\"/>\n"
"                        </td>\n"
"                        <td style=\"vertical-align: top; padding: 8px 10px;"
"text-align: left;font-size: 14px;\">\n"
"                            <a href=\"web/login\" style=\"float:right ;"
"margin:15px auto;background: #a24689;border-radius: 5px;color: #ffffff;font-"
"size: 16px;padding: 10px 20px 10px 20px;text-decoration: none;\">Auto Login</"
"a>\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"            <table style=\"width:100%;text-align:justify;margin:0 auto;"
"border-collapse:collapse;border-top:1px solid lightgray\"\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td style=\"padding:15px 10px;font-size:20px\">\n"
"                            <p style=\"color:#a24689;margin:0\" >Awesome!</"
"p><br>\n"
"                            <p dir=\"ltr\" style=\"font-size:15px;margin-"
"top:0pt;margin-bottom:0pt;\">\n"
"                                <span>Your website is now online !</span></"
"p><br>\n"
"                            <p dir=\"ltr\" style=\"margin-top:0pt;margin-"
"bottom:8pt;\">\n"
"                                <span style=\"font-size:13px;font-weight:"
"bold; \">Did you know ?</span></p>\n"
"                            <ul style=\"margin-top:0pt;margin-bottom:0pt;"
"font-size:13px;list-style-type:disc;\">\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:0pt;\">\n"
"                                        <span>Enhance your online business "
"with astonishing apps like : Forum, Blog, e-Commerce, Events, online jobs..."
"</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:8pt;\">\n"
"                                        <span>Customize the look of your "
"website with our </span>\n"
"                                        <span style=\"font-weight:bold;text-"
"decoration:underline;\"><a href=\"https://www.odoo.com/apps/themes"
"\">magnificent themes</a></span>\n"
"                                        <span>to easily create a unique "
"website.</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                            </ul> <br>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;margin-"
"top:0pt;margin-bottom:8pt;\">\n"
"                                <span style=\"font-weight:bold;\">Discover "
"the </span>\n"
"                                <span><a href=\"/"
"web#view_type=kanban&model=project.project&action=project."
"open_view_project_all\">\n"
"                                    <span style=\"font-weight:bold; text-"
"decoration:underline;\">Website planner</span></a></span>\n"
"                                <span> to activate extra features</span>\n"
"                                <span style=\"color:#a24689;margin:0;font-"
"weight:bold\">(${user.env['web.planner']."
"get_planner_progress('planner_website')}% done)</span>\n"
"                            </p>\n"
"                            <ul style=\"margin-top:0pt;margin-bottom:0pt;"
"font-size:13px;list-style-type:disc;\">\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:0pt;\">\n"
"                                        <span>Grow your traffic by "
"optimizing your referencing with our SEO tool,</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:0pt;\">\n"
"                                        <span>Test different versions of a "
"page to perform better in reaching your business objectives,</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:0pt;\">\n"
"                                        <span>Improve your conversion from "
"visitor to customer with the Website Live support,</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:0pt;\">\n"
"                                        <span>Broaden your audience by "
"translating your website automatically with Gengo,</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:0pt;\">\n"
"                                        <span>Go further into the "
"customizing with our advanced HTML editor,</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;"
"margin-bottom:8pt;\">\n"
"                                        <span>And much more...</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                            </ul>\n"
"                            <br>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;line-"
"height:1.3;margin-top:0pt;margin-bottom:8pt;\">\n"
"                                <span style=\"font-weight:bold;\">Need Help?"
"</span>\n"
"                                <span style=\"font-style:italic;\">You’re "
"not alone</span>\n"
"                            </p>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;margin-"
"top:0pt;margin-bottom:8pt;\">\n"
"                                <span>We would be delighted to assist you "
"along the way. Contact us at \n"
"                                <a href=\"mailto:<EMAIL>\"><span style="
"\"text-decoration:underline;\">\n"
"                                <EMAIL></span></a> if you have any "
"question. You can also discover \n"
"                                how to get the best out of Odoo with our </"
"span>\n"
"                                <a href=\"https://www.odoo.com/documentation/"
"user/9.0/project.html\">\n"
"                                <span style=\"text-decoration:underline;"
"\">User Documentation</span></a>\n"
"                                </span><span> or with our </span>\n"
"                                <a href=\"https://www.odoo.com/"
"documentation/9.0/\">\n"
"                                <span style=\"text-decoration:underline;"
"\">API Documentation</span></a>\n"
"                            </p>\n"
"                            <br>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;margin-"
"top:0pt;margin-bottom:8pt;\"><span>Enjoy your Odoo experience,</span></p>\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"            <div dir=\"ltr\" style=\"font-size:13px;margin-top:0pt;margin-"
"bottom:8pt;color:grey\">\n"
"                <span><br/>-- <br/>The Odoo Team<br/>PS: People love Odoo, "
"check </span><a href=\"https://twitter.com/odoo/favorites\"><span style="
"\"text-decoration:underline;\">what they say about it.</span></a></span>\n"
"            </div>\n"
"        </div>"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_account_analytic_account
msgid "Analytic Account"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_event_registration
msgid "Attendee"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_hr_department
msgid "Department"
msgstr ""

#. module: website_mail
#: model:mail.channel,name:website_mail.channel_public
msgid "Discussion Group"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_mail_channel
msgid "Discussion channel"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_mail_thread
msgid "Email Thread"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_survey_mail_compose_message
msgid "Email composition wizard for Survey"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_hr_employee
msgid "Employee"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_event_event
msgid "Event"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_gamification_badge
msgid "Gamification badge"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_gamification_challenge
msgid "Gamification challenge"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_fleet_vehicle
msgid "Information on a vehicle"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_hr_job
msgid "Job Position"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.message_thread
msgid "Leave a comment"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_mail_mass_mailing_contact
msgid "Mass Mailing Contact"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_mail_message
msgid "Message"
msgstr "Mensaxe"

#. module: website_mail
#: model:ir.model.fields,help:website_mail.field_mail_compose_message_description
#: model:ir.model.fields,help:website_mail.field_mail_message_description
#: model:ir.model.fields,help:website_mail.field_survey_mail_compose_message_description
msgid "Message description: either the subject, or the beginning of the body"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_note_note
msgid "Note"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.message_thread
msgid "Oops! Something went wrong. Try to reload the page and to log in."
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_res_partner
msgid "Partner"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_product_product
msgid "Product"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_product_template
msgid "Product Template"
msgstr ""

#. module: website_mail
#: model:ir.model.fields,field_description:website_mail.field_mail_compose_message_website_published
#: model:ir.model.fields,field_description:website_mail.field_mail_message_website_published
#: model:ir.model.fields,field_description:website_mail.field_survey_mail_compose_message_website_published
msgid "Published"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_crm_team
msgid "Sales Team"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.message_thread
msgid "Send"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Subscribe"
msgstr ""

#. module: website_mail
#: model:ir.model,name:website_mail.model_survey_survey
msgid "Survey"
msgstr ""

#. module: website_mail
#: code:addons/website_mail/models/mail_message.py:69
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. "
"Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "Unsubscribe"
msgstr ""

#. module: website_mail
#: model:ir.model.fields,help:website_mail.field_mail_compose_message_website_published
#: model:ir.model.fields,help:website_mail.field_mail_message_website_published
#: model:ir.model.fields,help:website_mail.field_survey_mail_compose_message_website_published
msgid "Visible on the website as a comment"
msgstr ""

#. module: website_mail
#: model:ir.model.fields,field_description:website_mail.field_product_template_website_message_ids
msgid "Website Comments"
msgstr ""

#. module: website_mail
#: model:ir.model.fields,field_description:website_mail.field_account_analytic_account_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_account_asset_asset_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_account_bank_statement_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_account_invoice_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_account_voucher_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_blog_blog_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_calendar_event_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_crm_lead_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_crm_team_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_crossovered_budget_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_event_event_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_event_registration_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_event_track_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_fleet_vehicle_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_forum_forum_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_forum_tag_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_gamification_badge_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_gamification_challenge_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_hr_applicant_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_hr_contract_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_hr_department_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_hr_employee_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_hr_equipment_category_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_hr_equipment_request_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_hr_equipment_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_hr_expense_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_hr_holidays_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_hr_job_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_hr_timesheet_sheet_sheet_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_mail_channel_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_mail_mass_mailing_contact_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_mail_thread_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_mrp_bom_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_mrp_production_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_mrp_production_workcenter_line_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_mrp_repair_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_note_note_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_procurement_order_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_product_product_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_project_issue_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_project_project_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_project_task_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_purchase_order_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_purchase_requisition_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_res_partner_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_sale_order_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_slide_channel_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_stock_landed_cost_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_stock_picking_wave_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_stock_picking_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_stock_production_lot_website_message_ids
#: model:ir.model.fields,field_description:website_mail.field_survey_survey_website_message_ids
msgid "Website Messages"
msgstr ""

#. module: website_mail
#: model:ir.model.fields,help:website_mail.field_account_analytic_account_website_message_ids
#: model:ir.model.fields,help:website_mail.field_account_asset_asset_website_message_ids
#: model:ir.model.fields,help:website_mail.field_account_bank_statement_website_message_ids
#: model:ir.model.fields,help:website_mail.field_account_invoice_website_message_ids
#: model:ir.model.fields,help:website_mail.field_account_voucher_website_message_ids
#: model:ir.model.fields,help:website_mail.field_blog_blog_website_message_ids
#: model:ir.model.fields,help:website_mail.field_calendar_event_website_message_ids
#: model:ir.model.fields,help:website_mail.field_crm_lead_website_message_ids
#: model:ir.model.fields,help:website_mail.field_crm_team_website_message_ids
#: model:ir.model.fields,help:website_mail.field_crossovered_budget_website_message_ids
#: model:ir.model.fields,help:website_mail.field_event_event_website_message_ids
#: model:ir.model.fields,help:website_mail.field_event_registration_website_message_ids
#: model:ir.model.fields,help:website_mail.field_event_track_website_message_ids
#: model:ir.model.fields,help:website_mail.field_fleet_vehicle_website_message_ids
#: model:ir.model.fields,help:website_mail.field_forum_forum_website_message_ids
#: model:ir.model.fields,help:website_mail.field_forum_tag_website_message_ids
#: model:ir.model.fields,help:website_mail.field_gamification_badge_website_message_ids
#: model:ir.model.fields,help:website_mail.field_gamification_challenge_website_message_ids
#: model:ir.model.fields,help:website_mail.field_hr_applicant_website_message_ids
#: model:ir.model.fields,help:website_mail.field_hr_contract_website_message_ids
#: model:ir.model.fields,help:website_mail.field_hr_department_website_message_ids
#: model:ir.model.fields,help:website_mail.field_hr_employee_website_message_ids
#: model:ir.model.fields,help:website_mail.field_hr_equipment_category_website_message_ids
#: model:ir.model.fields,help:website_mail.field_hr_equipment_request_website_message_ids
#: model:ir.model.fields,help:website_mail.field_hr_equipment_website_message_ids
#: model:ir.model.fields,help:website_mail.field_hr_expense_website_message_ids
#: model:ir.model.fields,help:website_mail.field_hr_holidays_website_message_ids
#: model:ir.model.fields,help:website_mail.field_hr_job_website_message_ids
#: model:ir.model.fields,help:website_mail.field_hr_timesheet_sheet_sheet_website_message_ids
#: model:ir.model.fields,help:website_mail.field_mail_channel_website_message_ids
#: model:ir.model.fields,help:website_mail.field_mail_mass_mailing_contact_website_message_ids
#: model:ir.model.fields,help:website_mail.field_mail_thread_website_message_ids
#: model:ir.model.fields,help:website_mail.field_mrp_bom_website_message_ids
#: model:ir.model.fields,help:website_mail.field_mrp_production_website_message_ids
#: model:ir.model.fields,help:website_mail.field_mrp_production_workcenter_line_website_message_ids
#: model:ir.model.fields,help:website_mail.field_mrp_repair_website_message_ids
#: model:ir.model.fields,help:website_mail.field_note_note_website_message_ids
#: model:ir.model.fields,help:website_mail.field_procurement_order_website_message_ids
#: model:ir.model.fields,help:website_mail.field_product_product_website_message_ids
#: model:ir.model.fields,help:website_mail.field_project_issue_website_message_ids
#: model:ir.model.fields,help:website_mail.field_project_project_website_message_ids
#: model:ir.model.fields,help:website_mail.field_project_task_website_message_ids
#: model:ir.model.fields,help:website_mail.field_purchase_order_website_message_ids
#: model:ir.model.fields,help:website_mail.field_purchase_requisition_website_message_ids
#: model:ir.model.fields,help:website_mail.field_res_partner_website_message_ids
#: model:ir.model.fields,help:website_mail.field_sale_order_website_message_ids
#: model:ir.model.fields,help:website_mail.field_slide_channel_website_message_ids
#: model:ir.model.fields,help:website_mail.field_stock_landed_cost_website_message_ids
#: model:ir.model.fields,help:website_mail.field_stock_picking_wave_website_message_ids
#: model:ir.model.fields,help:website_mail.field_stock_picking_website_message_ids
#: model:ir.model.fields,help:website_mail.field_stock_production_lot_website_message_ids
#: model:ir.model.fields,help:website_mail.field_survey_survey_website_message_ids
msgid "Website communication history"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.message_thread
msgid "Write a message..."
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.message_thread
msgid "You must be"
msgstr ""

#. module: website_mail
#: model:mail.template,subject:website_mail.mail_template_data_module_install_website_mail
msgid "Your business is reachable from anywhere, anytime !"
msgstr ""

#. module: website_mail
#. openerp-web
#: code:addons/website_mail/static/src/xml/chatter_message.xml:8
#, python-format
msgid "just now"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.message_thread
msgid "logged in"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.message_thread
msgid "on"
msgstr "en"

#. module: website_mail
#: model:ir.model,name:website_mail.model_publisher_warranty_contract
msgid "publisher_warranty.contract"
msgstr ""

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.message_thread
msgid "to post a comment."
msgstr ""

#. module: website_mail
#: model:ir.model.fields,field_description:website_mail.field_mail_compose_message_description
#: model:ir.model.fields,field_description:website_mail.field_mail_message_description
#: model:ir.model.fields,field_description:website_mail.field_survey_mail_compose_message_description
msgid "unknown"
msgstr "descoñecido"

#. module: website_mail
#: model_terms:ir.ui.view,arch_db:website_mail.follow
msgid "your email..."
msgstr ""
