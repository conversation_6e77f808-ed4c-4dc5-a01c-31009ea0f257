<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="1">

    <record id="analytic_comp_rule" model="ir.rule">
        <field name="name">Analytic multi company rule</field>
        <field name="model_id" ref="model_account_analytic_account"/>
        <field eval="True" name="global"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
    </record>
     
    <record id="analytic_line_comp_rule" model="ir.rule">
        <field name="name">Analytic line multi company rule</field>
        <field name="model_id" ref="model_account_analytic_line"/>
        <field eval="True" name="global"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
    </record>

    <record id="analytic_group_comp_rule" model="ir.rule">
        <field name="name">Analytic line multi company rule</field>
        <field name="model_id" ref="model_account_analytic_group"/>
        <field eval="True" name="global"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
    </record>

    <record id="analytic_tag_comp_rule" model="ir.rule">
        <field name="name">Analytic line multi company rule</field>
        <field name="model_id" ref="model_account_analytic_tag"/>
        <field eval="True" name="global"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
    </record>
</data>
<data noupdate="0">

    <record id="group_analytic_accounting" model="res.groups">
        <field name="name">Analytic Accounting</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_analytic_tags" model="res.groups">
        <field name="name">Analytic Accounting Tags</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

</data>
</odoo>
