# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_sms
# 
# Translators:
# <PERSON><PERSON><PERSON> <stre<PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.war<PERSON>@gmail.com>, 2021
# <PERSON><PERSON> <mlyn<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <piotr.w.c<PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: stock_sms
#: model:sms.template,body:stock_sms.sms_template_data_stock_delivery
msgid ""
"\n"
"                {{ (object.company_id.name + ': We are glad to inform you that your order n° ' + object.origin + ' has been shipped.' if object.origin else object.company_id.name + ': We are glad to inform you that your order has been shipped.') + (' Your tracking reference is ' + (object.carrier_tracking_ref) + '.' if hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref else '') }}\n"
"            "
msgstr ""
"\n"
" {{ (object.company_id.name + ': Z przyjemnością informujemy, że zamówienie nr ' + object.origin + ' zostało wysłane.' if object.origin else object.company_id.name + ': Miło nam poinformować, że Twoje zamówienie zostało wysłane.') + ('Twój numer referencyjny to ' + (object.carrier_tracking_ref) + '.' if hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref else '') }}"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Cancel"
msgstr "Anuluj"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_res_company
msgid "Companies"
msgstr "Firmy"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Confirm"
msgstr "Potwierdź"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_confirm_stock_sms
msgid "Confirm Stock SMS"
msgstr "Potwierdź SMS o stanie magazynowym"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: stock_sms
#: model:sms.template,name:stock_sms.sms_template_data_stock_delivery
msgid "Delivery: Send by SMS Text Message"
msgstr "Otrzymano: Wysłano przez wiadomość tekstową SMS"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Disable SMS"
msgstr "Wyłącz SMSy"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__has_received_warning_stock_sms
msgid "Has Received Warning Stock Sms"
msgstr "Otrzymał ostrzeżenie magazynowe SMS"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__id
msgid "ID"
msgstr "ID"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__pick_ids
msgid "Pick"
msgstr "Pobranie"

#. module: stock_sms
#: code:addons/stock_sms/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
#, python-format
msgid "SMS"
msgstr "SMS"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__stock_move_sms_validation
#: model_terms:ir.ui.view,arch_db:stock_sms.res_config_settings_view_form_stock
msgid "SMS Confirmation"
msgstr "Potwierdzenie SMS"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__stock_sms_confirmation_template_id
#: model:ir.model.fields,field_description:stock_sms.field_res_config_settings__stock_sms_confirmation_template_id
#: model_terms:ir.ui.view,arch_db:stock_sms.res_config_settings_view_form_stock
msgid "SMS Template"
msgstr "Szablon SMS"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_config_settings__stock_move_sms_validation
msgid "SMS Validation with stock move"
msgstr "Walidacja SMS przy ruchu towarów"

#. module: stock_sms
#: model:ir.model.fields,help:stock_sms.field_res_company__stock_sms_confirmation_template_id
#: model:ir.model.fields,help:stock_sms.field_res_config_settings__stock_sms_confirmation_template_id
msgid "SMS sent to the customer once the order is done."
msgstr ""

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_stock_picking
msgid "Transfer"
msgstr "Przekaz"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid ""
"You are about to confirm this Delivery Order by SMS Text Message.<br/>\n"
"                This feature can easily be disabled from the Settings of Inventory or by clicking on \"Disable SMS\".<br/>"
msgstr ""
"Za chwilę potwierdzisz to zamówienie dostawy za pomocą wiadomości tekstowej SMS.<br/>\n"
"Tę funkcję można łatwo wyłączyć z poziomu ustawień w Inventory lub klikając na \"Wyłącz SMS\".<br/>"
