<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="hr_loan_inherited" model="ir.ui.view">
            <field name="name">HR LOAN</field>
            <field name="model">hr.loan</field>
            <field name="inherit_id" ref="ohrms_loan.hr_loan_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="replace">
                   <header>
                       <button name="compute_installment" type="object" string="Compute Installment" class="oe_highlight"  attrs="{'invisible':[('state','in',('approve', 'refuse'))]}"/>
                       <!--requested by Elbahry at 2023/02/12-->
                        <button name="make_draft" type="object" string="Make Draft"
                            attrs="{'invisible':[('is_admin','=',False)]}"/>
                        <button name="action_submit" type="object" string="Manager Approval" attrs="{'invisible': ['|',('is_manager','!=',True),('state','!=','draft')]}" class="oe_highlight"/>
<!--                        <button name="action_submit" type="object" string="Manager Approval" attrs="{'invisible': [('is_manager','!=',True)]}" states="draft" class="oe_highlight"/><button name="action_submit" type="object" string="Manager Approval" attrs="{'invisible': [('is_manager','!=',True)]}" states="draft" class="oe_highlight"/>-->
                        <button name="action_cancel" type="object" string="Cancel" states="draft" />
<!--                       <button name="action_approve" type="object" string="Approve" states="waiting_approval_1" class="oe_highlight" groups="hr.group_hr_manager,hr.group_hr_user,masarat_expenses_requist.group_finance_approvales"/>-->
                        <button name="action_approve" type="object" string="HR Approval" states="waiting_approval_1" class="oe_highlight" groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
                        <button name="action_double_approve" type="object" string="Finance Approval" states="waiting_approval_2" class="oe_highlight" groups="masarat_expenses_requist.group_finance_approvales"/>
                        <button name="action_refuse" type="object" string="Refuse" states="waiting_approval_1,waiting_approval_2" class="oe_highlight" groups="hr.group_hr_manager,hr.group_hr_user"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,waiting_approval_1,approve" />
                    </header>
                </xpath>
                <xpath expr="//field[@name='loan_amount']" position="after">

                    <field name="is_admin" invisible="1"/>
                    <field name="manager_id" options="{'no_open':True}" invisible="0"/>
                    <field name="is_manager" invisible="1"/>

                    <field name="employee_account_id"  attrs="{'invisible':[('state', '=','draft')],'readonly':[('state','in',('approve', 'refuse'))]}"/>
                    <field name="treasury_account_id"  attrs="{'invisible':[('state', '=','draft')],'readonly':[('state','in',('approve', 'refuse'))]}"/>
                    <field name="middle_account_id" />
                    <field name="payment_method" attrs="{'invisible':[('state', '!=','waiting_approval_2')],'required':[('state','=','waiting_approval_2')]}"/>
                    <field name="suke_id"/>
                    <field name="journal_id"  attrs="{'invisible':[('state', '=','draft')],'readonly':[('state','in',('approve', 'refuse'))]}"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
