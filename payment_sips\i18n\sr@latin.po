# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_sips
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:153
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:151
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:74
#, python-format
msgid "Currency not supported by Wordline"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:55
#, python-format
msgid "Incorrect payment acquirer provider"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer_sips_version
msgid "Interface Version"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer_sips_merchant_id
msgid "Merchant ID"
msgstr ""

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer_sips_prod_url
msgid "Prod's url"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer_sips_secret
msgid "Secret Key"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/payment.py:149
#, python-format
msgid "Sips: received data for reference %s"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer_sips_test_url
msgid "Test's url"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer_sips_merchant_id
msgid "Used for production only"
msgstr ""
