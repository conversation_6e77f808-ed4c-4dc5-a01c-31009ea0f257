//------------------------------------------------------------------------------
// Inputs and selects (note: put the o_input class to have the style)
//------------------------------------------------------------------------------
[type="text"],
[type="password"],
[type="number"],
textarea,
select {
  width: 100%;
  display: block;
  outline: none;
}
.o_input {
  border-radius: $border-radius;
  color: #1f1f1f;
}
select {
  background: white;
}

//------------------------------------------------------------------------------
// Loading
//------------------------------------------------------------------------------

.o_loading > span {
  background-color: $o-brand-odoo;
  color: white;
  padding: 4px;
}

//------------------------------------------------------------------------------
// User menu in navbar
//------------------------------------------------------------------------------
#oe_main_menu_navbar .oe_user_menu_placeholder .o_user_menu .oe_topbar_name {
  @include o-text-overflow;
  max-width: 150px;
}

.o_rtl {
  .navbar-right {
    padding: 0; // fix for user agent stylesheet -webkit-padding-start: 40px in rtl language
  }
}

.btn-secondary {
  // Bootstrap variables do not control the color for btn-secondary border
  border-color: $border-color;
}
