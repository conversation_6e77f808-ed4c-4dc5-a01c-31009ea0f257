# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-27 15:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Connect Stripe"
msgstr "توصيل Stripe "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "تعذر إنشاء الاتصال بالواجهة البرمجية للتطبيق. "

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Generate your webhook"
msgstr "إنشاء Webhook الخاص بك "

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Get your Secret and Publishable keys"
msgstr "احصل على مفاتيحك السرية والقابلة للنشر "

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid ""
"If a webhook is enabled on your Stripe account, this signing secret must be "
"set to authenticate the messages sent from Stripe to Odoo."
msgstr ""
"إذا كان webhook مفعلاً على حساب Stripe الخاص بك، يجب تعيين سر التوقيع هذا "
"لمصادقة الرسالة المرسلة من Stripe إلى أودو. "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "لم يتم العثور على معاملة تطابق المرجع %s. "

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "بوابة الدفع "

#. module: payment_stripe
#: model:ir.actions.act_window,name:payment_stripe.action_payment_acquirer_onboarding
msgid "Payment Acquirers"
msgstr "بوابات الدفع "

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_account_payment_method
msgid "Payment Methods"
msgstr "طرق الدفع "

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr "رمز الدفع "

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr "معاملة الدفع "

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__provider
msgid "Provider"
msgstr "المزود"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "Publishable Key"
msgstr "المفتاح القابل للنشر "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid intent status: %s"
msgstr "تم استلام البيانات مع حالة هدف غير صالحة: %s  "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing intent status."
msgstr "تم استلام البيانات دون حالة الهدف. "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr "تم استلام البيانات دون مرجع التاجر "

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_secret_key
msgid "Secret Key"
msgstr "المفتاح السري "

#. module: payment_stripe
#: model:account.payment.method,name:payment_stripe.payment_method_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_acquirer__provider__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__stripe_payment_intent
msgid "Stripe Payment Intent ID"
msgstr "معرف هدف دفع Stripe "

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Stripe Payment Method ID"
msgstr "معرف طريقة دفع Stripe "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy error: %(error)s"
msgstr "خطأ في وكيل Stripe: %(error)s "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy: An error occurred when communicating with the proxy."
msgstr "وكيل Stripe: حدث خطأ أثناء التواصل مع الوكيل. "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy: Could not establish the connection."
msgstr "وكيل Stripe: تعذر إنشاء الاتصال. "

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "مقدم خدمة الدفع لاستخدامه مع بوابة الدفع هذه "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid ""
"The communication with the API failed.\n"
"Stripe gave us the following info about the problem:\n"
"'%s'"
msgstr ""
"فشل التواصل مع الواجهة البرمجية للتطبيق.\n"
"لقد منحنا Stripe المعلومات التالية عن المشكلة:\n"
"'%s' "

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "The key solely used to identify the account with Stripe"
msgstr "المفتاح مُستخدم فقط لتعريف الحساب مع Stripe "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "المعاملة غير مرتبطة برمز. "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_token.py:0
#, python-format
msgid "Unable to convert payment token to new API."
msgstr "تعذر تحويل رمز الدفع إلى واجهة برمجية جديدة. "

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid "Webhook Signing Secret"
msgstr "سر توقيع Webhook "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "You Stripe Webhook was successfully set up!"
msgstr "لقد تم تعيين Stripe Webhook الخاص بك بنجاح! "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot create a Stripe Webhook if your Stripe Secret Key is not set."
msgstr ""
"لا يمكنك إنشاء Stripe Webhook إذا لم يكن مفتاح Stripe السري الخاص بك معيناً."
" "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot set the acquirer state to Enabled until your onboarding to Stripe"
" is completed."
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot set the acquirer to Test Mode while it is linked with your Stripe"
" account."
msgstr ""
"لا يمكنك وضع بوابة الدفع في وضع الاختبار عندما تكون مرتبطة بحساب Stripe "
"الخاص بك. "

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Your Stripe Webhook is already set up."
msgstr "تم ضبط Stripe Webhook الخاص بك بالفعل. "
