# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rating
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_count
msgid "# Ratings"
msgstr "# Derecelendirmeler"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__access_token
msgid "Access token to set the rating of the value"
msgstr "Değerlendirmenin değerini ayarlamak için erişim anahtarı"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__message_id
msgid ""
"Associated message when posting a review. Mainly used in website addons."
msgstr ""
"Değerlendirme gönderirken ilişkili mesaj. Genellikle websitesi "
"eklentilerinde kullanılır."

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__partner_id
msgid "Author of the rating"
msgstr "Değerlendirmeyi yapan"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__feedback
msgid "Comment"
msgstr "Yorum"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Customer"
msgstr "Müşteri"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Date"
msgstr "Tarih"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ko
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Dissatisfied"
msgstr "Memnun değilim"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Document"
msgstr "Belge"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model
msgid "Document Model"
msgstr "Belge Modeli"

#. module: rating
#: model:ir.model,name:rating.model_mail_thread
msgid "Email Thread"
msgstr "E-Posta İşlemleri"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__consumed
msgid "Enabled if the rating has been filled."
msgstr "Değerlendirme gönderildi ise aktif olur."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Feel free to write a feedback on your experience:"
msgstr "Deneyiminiz hakkında bir geri bildirim yazmaktan çekinmeyin:"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__consumed
msgid "Filled Rating"
msgstr "Gönderilmiş Değerlendirme"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Go back to the Homepage"
msgstr "Go back to the Homepage"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Group By"
msgstr "Grupla"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""
"Alt tür yapılandırmasından bağımsız olarak genel / portal kullanıcılarına "
"gizleyin."

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__id
msgid "ID"
msgstr "ID"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_id
msgid "Identifier of the rated object"
msgstr "Değerlendirilen nesneni belirteci"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image
msgid "Image"
msgstr "Görsel"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 30 days"
msgstr "Son 30 gün"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 7 days"
msgstr "Son 7 gün"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: rating
#: model:ir.model,name:rating.model_mail_message
#: model:ir.model.fields,field_description:rating.field_rating_rating__message_id
msgid "Message"
msgstr "Mesaj"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_model_id
msgid "Model of the followed resource"
msgstr "İzlenen kaynağın modeli"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "My Ratings"
msgstr "Değerlendirmelerim"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_name
msgid "Name"
msgstr "Adı"

#. module: rating
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__none
msgid "No Rating yet"
msgstr "Henüz değerlendirilmedi"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_view
msgid "No rating yet"
msgstr "Henüz Değerlendirme Yok"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ok
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Okay"
msgstr "Tamam"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__rated_partner_id
msgid "Owner of the rated resource"
msgstr "Değerlendirilen kaynağın sahibi"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_id
msgid "Parent Document"
msgstr "Üst Belge"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model
msgid "Parent Document Model"
msgstr "Üste Belge Modeli"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_name
msgid "Parent Document Name"
msgstr "Üst Belge Adı"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Parent Holder"
msgstr "Üst Sahip"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_ref
msgid "Parent Ref"
msgstr "Üst Referans"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model_id
msgid "Parent Related Document Model"
msgstr "İlgili Üst Belge Modeli"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Mutlu derecelendirmelerin yüzdesi"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rated Operator"
msgstr "Değerlendirilmiş Operatörü"

#. module: rating
#: model:ir.model,name:rating.model_rating_rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rating"
msgstr "Değerlendirme"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg
msgid "Rating Average"
msgstr "Değerlendirme Ortalaması"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Son Geribildirimleri Derecelendirme"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_image
msgid "Rating Last Image"
msgstr "Son Resmin Değerlendirilmesi"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_value
msgid "Rating Last Value"
msgstr "Değerlendirme Son Değer"

#. module: rating
#: model:ir.model,name:rating.model_rating_mixin
msgid "Rating Mixin"
msgstr "Değerlendirme Karışımı"

#. module: rating
#: model:ir.model,name:rating.model_rating_parent_mixin
msgid "Rating Parent Mixin"
msgstr "Derecelendirme Ana Karışımı"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Puanlama Memnuniyeti"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_value
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_value
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating
msgid "Rating Value"
msgstr "Değerlendirme Puanı"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
msgid "Rating Value (/5)"
msgstr "Değerlendirme Değeri (/5)"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_count
msgid "Rating count"
msgstr "Değerlendirme sayısı"

#. module: rating
#: model:ir.model.constraint,message:rating.constraint_rating_rating_rating_range
msgid "Rating should be between 0 and 5"
msgstr "Değerlendirme 0 ile 5 arasında olmalıdır"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__rating
msgid "Rating value: 0=Unhappy, 5=Happy"
msgstr "Değerlendirme değeri: 0=Mutsuz, 5=Mutlu"

#. module: rating
#: model:ir.actions.act_window,name:rating.rating_rating_view
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_ids
#: model:ir.ui.menu,name:rating.rating_rating_menu_technical
#: model_terms:ir.ui.view,arch_db:rating.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_tree
msgid "Ratings"
msgstr "Değerlendirmeler"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__rating_last_feedback
#: model:ir.model.fields,help:rating.field_rating_rating__feedback
msgid "Reason of the rating"
msgstr "Değerlendirme nedeni"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model_id
msgid "Related Document Model"
msgstr "İlgili Döküman Modeli"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_ids
msgid "Related ratings"
msgstr "İlişkili değerlendirmeler"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Resource"
msgstr "Kaynak"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__resource_ref
msgid "Resource Ref"
msgstr "Kaynak Referans"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_name
msgid "Resource name"
msgstr "Kaynak adı"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__top
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Satisfied"
msgstr "Memnun"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__access_token
msgid "Security Token"
msgstr "Güvenlik Token"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Send Feedback"
msgstr "Geri Bildirim Gönder"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_date
msgid "Submitted on"
msgstr "Gönderildi"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Thank you for rating our services!"
msgstr "Hizmetlerimizi derecelendirdiğiniz için teşekkür ederiz!"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Thank you, we appreciate your feedback!"
msgstr "Teşekkür ederiz, geri bildiriminiz için teşekkür ederiz!"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_name
msgid "The name of the rated resource."
msgstr "Değerlendirilen kaynağın ismi."

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_view
msgid "There is no rating for this object at the moment."
msgstr "Bu nesne için şu anda herhangi bir değerlendirme yok."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Today"
msgstr "Bugün"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__is_internal
msgid "Visible Internally Only"
msgstr "Yalnızca Dahili Olarak Görünür"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "by"
msgstr "tarafından"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "for"
msgstr "için"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "on"
msgstr "tarihi"
