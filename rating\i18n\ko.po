# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rating
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Sarah <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_count
msgid "# Ratings"
msgstr "# 평점"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__access_token
msgid "Access token to set the rating of the value"
msgstr "평가 값을 설정하기 위한 접근 토큰"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__message_id
msgid ""
"Associated message when posting a review. Mainly used in website addons."
msgstr "소감을 게시할 때 관련 메시지가 표시됩니다. 주로 웹 사이트 애드온에서 사용됩니다."

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__partner_id
msgid "Author of the rating"
msgstr "평가 작성자"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__feedback
msgid "Comment"
msgstr "댓글"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_uid
msgid "Created by"
msgstr "작성자"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Customer"
msgstr "고객"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Date"
msgstr "일자"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__display_name
msgid "Display Name"
msgstr "표시명"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ko
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Dissatisfied"
msgstr "불만족"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Document"
msgstr "문서"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model
msgid "Document Model"
msgstr "문서 모델"

#. module: rating
#: model:ir.model,name:rating.model_mail_thread
msgid "Email Thread"
msgstr "이메일 스레드"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__consumed
msgid "Enabled if the rating has been filled."
msgstr "평가가 채워지면 활성화됩니다."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Feel free to write a feedback on your experience:"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__consumed
msgid "Filled Rating"
msgstr "채워진 평가"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Go back to the Homepage"
msgstr "홈페이지로 돌아가기"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Group By"
msgstr "그룹별"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr "일반 및 포털 사용자에게 숨기며, 하위 환경 설정과는 무관합니다."

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__id
msgid "ID"
msgstr "ID"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_id
msgid "Identifier of the rated object"
msgstr "평가된 객체의 식별자"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_image
msgid "Image"
msgstr "이미지"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 30 days"
msgstr "최근 30일"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Last 7 days"
msgstr "최근 7일"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: rating
#: model:ir.model,name:rating.model_mail_message
#: model:ir.model.fields,field_description:rating.field_rating_rating__message_id
msgid "Message"
msgstr "메시지"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_model_id
msgid "Model of the followed resource"
msgstr "팔로우하는 자원의 모델"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "My Ratings"
msgstr "내 평가"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_name
msgid "Name"
msgstr "이름"

#. module: rating
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__none
msgid "No Rating yet"
msgstr "아직 평가 없음"

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_view
msgid "No rating yet"
msgstr "아직 평가 없음"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__ok
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Okay"
msgstr "괜찮아요"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__rated_partner_id
msgid "Owner of the rated resource"
msgstr "평가된 자원의 소유자"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_id
msgid "Parent Document"
msgstr "상위 문서"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model
msgid "Parent Document Model"
msgstr "상위 문서 모델"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_name
msgid "Parent Document Name"
msgstr "상위 문서명"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
msgid "Parent Holder"
msgstr "상위 보관"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_ref
msgid "Parent Ref"
msgstr "상위 참조"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__parent_res_model_id
msgid "Parent Related Document Model"
msgstr "관련된 상위 문서 모델"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "좋음 평가 비율"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__rated_partner_id
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rated Operator"
msgstr "평점 연산자"

#. module: rating
#: model:ir.model,name:rating.model_rating_rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating_text
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Rating"
msgstr "평가"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_avg
msgid "Rating Average"
msgstr "평균 평가"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "최근 의견의 평가"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_image
msgid "Rating Last Image"
msgstr "최근 이미지 평가"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_last_value
msgid "Rating Last Value"
msgstr "최근 값 평가"

#. module: rating
#: model:ir.model,name:rating.model_rating_mixin
msgid "Rating Mixin"
msgstr "평가 믹스인"

#. module: rating
#: model:ir.model,name:rating.model_rating_parent_mixin
msgid "Rating Parent Mixin"
msgstr "평가 상위 믹스인"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "만족도 평가"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_value
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_value
#: model:ir.model.fields,field_description:rating.field_rating_rating__rating
msgid "Rating Value"
msgstr "평가 값"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
msgid "Rating Value (/5)"
msgstr "평가 값(/5)"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin__rating_count
msgid "Rating count"
msgstr "평가 수 "

#. module: rating
#: model:ir.model.constraint,message:rating.constraint_rating_rating_rating_range
msgid "Rating should be between 0 and 5"
msgstr "평점은 0에서 5 사이에 있어야 합니다."

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__rating
msgid "Rating value: 0=Unhappy, 5=Happy"
msgstr ""

#. module: rating
#: model:ir.actions.act_window,name:rating.rating_rating_view
#: model:ir.model.fields,field_description:rating.field_rating_parent_mixin__rating_ids
#: model:ir.ui.menu,name:rating.rating_rating_menu_technical
#: model_terms:ir.ui.view,arch_db:rating.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_form
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_pivot
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_tree
msgid "Ratings"
msgstr "평가"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_mixin__rating_last_feedback
#: model:ir.model.fields,help:rating.field_rating_rating__feedback
msgid "Reason of the rating"
msgstr "평가 사유"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_model_id
msgid "Related Document Model"
msgstr "관련 문서 모델"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_mail_mail__rating_ids
#: model:ir.model.fields,field_description:rating.field_mail_message__rating_ids
msgid "Related ratings"
msgstr "관련된 평가"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Resource"
msgstr "리소스"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__resource_ref
msgid "Resource Ref"
msgstr "리소스 참조"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__res_name
msgid "Resource name"
msgstr "자원명"

#. module: rating
#: code:addons/rating/controllers/main.py:0
#: model:ir.model.fields.selection,name:rating.selection__rating_rating__rating_text__top
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
#, python-format
msgid "Satisfied"
msgstr "만족"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__access_token
msgid "Security Token"
msgstr "보안 토큰"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Send Feedback"
msgstr "의견 보내기"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__create_date
msgid "Submitted on"
msgstr "제출일"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_submit
msgid "Thank you for rating our services!"
msgstr "저희 서비스를 평가해주셔서 감사합니다!"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Thank you, we appreciate your feedback!"
msgstr "감사합니다. 피드백 주셔서 감사드립니다!"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating__res_name
msgid "The name of the rated resource."
msgstr "평가된 자원의 이름입니다."

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.rating_rating_view
msgid "There is no rating for this object at the moment."
msgstr "현재 이 대상에 대한 평가가 없습니다."

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_search
msgid "Today"
msgstr "오늘"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating__is_internal
msgid "Visible Internally Only"
msgstr "내부에서만 확인 가능"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "by"
msgstr "작성자"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "for"
msgstr "for"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_rating_view_kanban
msgid "on"
msgstr "있음"
