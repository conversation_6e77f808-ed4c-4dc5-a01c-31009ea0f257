# -*- coding: utf-8 -*-

from odoo import fields, models
from dateutil.relativedelta import relativedelta
from datetime import date, datetime
# import datetime


class CreateWizard(models.TransientModel):
    _name = 'payroll.cheque'
    _description = 'Payroll Cheque'


    currency_id = fields.Many2one('res.currency',default=lambda self: self.env.user.company_id.currency_id)
    date_from = fields.Date("Date From",default=lambda self: fields.Date.to_string(date.today().replace(day=1)),required=True)
    date_to = fields.Date("Date To",default=lambda self: fields.Date.to_string((datetime.now() + relativedelta(months=+1, day=1, days=-1)).date()),required=True)
    bank_id = fields.Many2one('res.bank',"Bank Name",required=True)
    branch_id = fields.Many2one('bank.branch',"Branch Name",domain="[('bank_id','=',bank_id)]",required=True)

    def _get_report_pdf(self):
        exist_payslip = self.env['hr.payslip'].search([('date_from','>=',self.date_from),('date_to','<=',self.date_to),
                                                       ('employee_id.bank_account_id.bank_id','=',self.bank_id.id),
                                                       ('employee_id.bank_account_id.bank_branch','=',self.branch_id.id)])
        date_month = datetime.strptime(
            self.date_from.strftime('%Y-%m-%d'), '%Y-%m-%d')
        payslip_ids = []
        amount = 0
        for payslip in exist_payslip:
            payslip_ids.append({
                'net_salary': payslip.line_ids.filtered(lambda line: line.code == 'NETSALARY').total,
            })
            amount += payslip.line_ids.filtered(lambda line: line.code == 'NETSALARY').total

        value = self.currency_id.amount_to_text(amount)
        return {
            'payslip_ids': payslip_ids,
            'amount' : value,
            'bank_name': self.bank_id.name,
            'branch_name': self.branch_id.name,

        }

    def generate_payroll_cheques(self):
        data = self._get_report_pdf()
        return self.env.ref('libya_hr_payroll.report_payroll_cheque_pdf').report_action([],data=data)
