# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_quotation_builder
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Bengali (https://app.transifex.com/odoo/teams/41243/bn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
":\n"
"                                        this content will appear on the quotation only if this\n"
"                                        product is put on the quote."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
":\n"
"                                        this content will appear on the quotation only if this\n"
"                                        product is used in the quote."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
msgid ""
":\n"
"                        the content below will disappear if this\n"
"                        product is removed from the quote."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
"<strong>Template Header:</strong> this content\n"
"                                    will appear on all quotations using this\n"
"                                    template."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "About us"
msgstr "আমাদের সম্পর্কে"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.brand_promotion
msgid "An awesome"
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"As a leading professional services firm,\n"
"                                we know that success is all about the\n"
"                                commitment we put on strong services."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
msgid "Close"
msgstr "বদ্ধ"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_res_company
msgid "Companies"
msgstr "কোম্পানি সমূহ "

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_template_view_form_inherit_sale_quotation_builder
msgid "Design Template"
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"Great quotation templates will significantly\n"
"                                <strong>boost your success rate</strong>. The\n"
"                                first section is usually about your company,\n"
"                                your references, your methodology or\n"
"                                guarantees, your team, SLA, terms and conditions, etc."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"If you edit a quotation from the 'Preview' of a quotation, you will\n"
"                        update that quotation only. If you edit the quotation\n"
"                        template (from the Configuration menu), all future quotations will\n"
"                        use this modified template."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.brand_promotion
msgid "Open Source CRM"
msgstr ""

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Optional Product:"
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Offer"
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Quality"
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Service"
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Price"
msgstr ""

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_product_template
msgid "Product Template"
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"Product quality is the foundation we\n"
"                                stand on; we build it with a relentless\n"
"                                focus on fabric, performance and craftsmanship."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Product:"
msgstr ""

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_product__quotation_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_template__quotation_description
msgid "Quotation Description"
msgstr ""

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_product__quotation_only_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_template__quotation_only_description
msgid "Quotation Only Description"
msgstr ""

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template
msgid "Quotation Template"
msgstr ""

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr ""

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr ""

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_option
msgid "Sale Options"
msgstr ""

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order
msgid "Sales Order"
msgstr "বিক্রয় আদেশ"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_line
msgid "Sales Order Line"
msgstr "বিক্রয় আদেশ লাইন"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Terms &amp; Conditions"
msgstr ""

#. module: sale_quotation_builder
#: model:ir.model.fields,help:sale_quotation_builder.field_product_product__quotation_only_description
#: model:ir.model.fields,help:sale_quotation_builder.field_product_template__quotation_only_description
#: model:ir.model.fields,help:sale_quotation_builder.field_sale_order_template_line__website_description
msgid "The quotation description (not used on eCommerce)"
msgstr ""

#. module: sale_quotation_builder
#: model:ir.model.fields,help:sale_quotation_builder.field_product_product__quotation_description
#: model:ir.model.fields,help:sale_quotation_builder.field_product_template__quotation_description
msgid ""
"This field uses the Quotation Only Description if it is defined, otherwise "
"it will try to read the eCommerce Description."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"This is a <strong>sample quotation template</strong>. You should\n"
"                                customize it to fit your own needs from the <i>Sales</i>\n"
"                                application, using the menu: Configuration /\n"
"                                Quotation Templates."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "This is a preview of the sale order template."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
"Titles with style <i>Heading 2</i> and\n"
"                                    <i>Heading 3</i> will be used to generate the\n"
"                                    table of content automatically."
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"We always ensure that our products are\n"
"                                set at a fair price so that you will be\n"
"                                happy to buy them."
msgstr ""

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_line__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_option__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template_line__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template_option__website_description
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_template_view_form_inherit_sale_quotation_builder
msgid "Website Description"
msgstr ""

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"You can <strong>set a description per product</strong>. Odoo will\n"
"                        automatically create a quotation using the descriptions\n"
"                        of all products in the proposal. The table of content\n"
"                        on the left is generated automatically using the styles you\n"
"                        used in your description (heading 1, heading 2, ...)"
msgstr ""
