# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_test
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Mongolian (https://www.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid ""
"<br/>\n"
"                        <strong>Description:</strong>"
msgstr ""
"<br/>\n"
"                        <strong>Тайлбар:</strong>"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid "<strong>Name:</strong>"
msgstr "<strong>Нэр:</strong>"

#. module: account_test
#: model:ir.model,name:account_test.model_report_account_test_report_accounttest
msgid "Account Test Report"
msgstr ""

#. module: account_test
#: model:ir.model,name:account_test.model_accounting_assert_test
msgid "Accounting Assert Test"
msgstr ""

#. module: account_test
#: model:ir.actions.act_window,name:account_test.action_accounting_assert
#: model:ir.actions.report,name:account_test.account_assert_test_report
#: model:ir.ui.menu,name:account_test.menu_action_license
msgid "Accounting Tests"
msgstr "Санхүү Тэстүүд"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid "Accouting tests on"
msgstr "Дараах дээрх санхүүгийн тестүүд"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__active
msgid "Active"
msgstr "Идэвхитэй"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_03
msgid "Check if movement lines are balanced and have the same date and period"
msgstr ""
"Хөдөлгөөний мөрүүд нь баланслагдсан, нэг огноо ба мөчлөгтэй байгаа эсэхийг "
"шалгана уу"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_07
msgid ""
"Check on bank statement that the Closing Balance = Starting Balance + sum of"
" statement lines"
msgstr ""
"Банкны хуулга дээр Хаах Баланс = Эхлэх Баланс + хуулгын мөрүүдийн нийлбэр "
"байгаа эсэхийг шалгана уу"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_06
msgid "Check that paid/reconciled invoices are not in 'Open' state"
msgstr ""
"Төлсөн/тулгагсан нэхэмжлэлүүд 'Нээлттэй' төлөвт байгаа эсэхийг шалган уу."

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_05_2
msgid ""
"Check that reconciled account moves, that define Payable and Receivable "
"accounts, are belonging to reconciled invoices"
msgstr ""
"Өглөг ба Авлагын дансыг тодорхойлогч тулгасан дансны хөдөлгөөнүүд нь "
"тулгасан нэхэмжлэлд харьяалагдаж буй эсэхийг шалгана уу"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_05
msgid ""
"Check that reconciled invoice for Sales/Purchases has reconciled entries for"
" Payable and Receivable Accounts"
msgstr ""
"Борлуулалт/Худалдан авалтын тулгасан нэхэмжлэл доторх Өглөг ба Авлагын "
"дансын оролтуудыг тулгасан эсэхийг шалгана уу"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_01
msgid "Check the balance: Debit sum = Credit sum"
msgstr "Баланс шалгах: Дебитийн нийлбэр = Кредитийн нийлбэр"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Code Help"
msgstr "код туслалцаа"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid ""
"Code should always set a variable named `result` with the result of your test, that can be a list or\n"
"a dictionary. If `result` is an empty list, it means that the test was succesful. Otherwise it will\n"
"try to translate and print what is inside `result`.\n"
"\n"
"If the result of your test is a dictionary, you can set a variable named `column_order` to choose in\n"
"what order you want to print `result`'s content.\n"
"\n"
"Should you need them, you can also use the following variables into your code:\n"
"    * cr: cursor to the database\n"
"    * uid: ID of the current user\n"
"\n"
"In any ways, the code must be legal python statements with correct indentation (if needed).\n"
"\n"
"Example: \n"
"    sql = '''SELECT id, name, ref, date\n"
"             FROM account_move_line \n"
"             WHERE account_id IN (SELECT id FROM account_account WHERE type = 'view')\n"
"          '''\n"
"    cr.execute(sql)\n"
"    result = cr.dictfetchall()"
msgstr ""
"Код нь үргэлж `result` нэртэй хувьсагчид жагсаалт эсвэл толь бичиг төрлийн таны тестийн үр \n"
"дүнг оноох ёстой. Хэрэв `result` нь хоосон жагсаалт байвал тест амжилттай болсон гэсэн үг. \n"
"Үгүй бол `result` доторхийг орчуулж хэвлэх гэж оролдоно.\n"
"\n"
"Хэрэв таны тестийн хариу толь бичиг бол `result`-н агуулгыг хэвлэх дарааллыг сонгохын тулд \n"
"`column_order` хувьсагчид утга оноох боломжтой.\n"
"\n"
"Шаардлагатай бол мөн дараах хувьсагчуудыг ашиглах боломжтой:\n"
"    * cr: өгөгдлийн сан руу заах курсор\n"
"    * uid: идэвхитэй хэрэглэгчийн ID\n"
"\n"
"Ямар ч байсан, код нь (шаардлагатай бол) зөв догол мөртэй албан ёсны python илэрхийлэл \n"
"байх ёстой.\n"
"\n"
"Жишээ: \n"
"    sql = '''SELECT id, name, ref, date\n"
"             FROM account_move_line \n"
"             WHERE account_id IN (SELECT id FROM account_account WHERE type = 'view')\n"
"          '''\n"
"    cr.execute(sql)\n"
"    result = cr.dictfetchall()"

#. module: account_test
#: model_terms:ir.actions.act_window,help:account_test.action_accounting_assert
msgid "Create a new accounting test"
msgstr ""

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__create_uid
msgid "Created by"
msgstr "Үүсгэгч"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__create_date
msgid "Created on"
msgstr "Үүсгэсэн"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Description"
msgstr "Тодорхойлолт"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__display_name
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest__display_name
msgid "Display Name"
msgstr "Дэлгэцийн Нэр"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Expression"
msgstr "Илэрхийлэл"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__id
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest__id
msgid "ID"
msgstr "ID"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test____last_update
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest____last_update
msgid "Last Modified on"
msgstr "Сүүлийн засвар хийсэн огноо"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__write_uid
msgid "Last Updated by"
msgstr "Сүүлийн засвар хийсэн"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__write_date
msgid "Last Updated on"
msgstr "Сүүлийн засвар хийсэн огноо"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Python Code"
msgstr "Python код"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__code_exec
msgid "Python code"
msgstr "Python код"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__sequence
msgid "Sequence"
msgstr "Дараалал"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_01
msgid "Test 1: General balance"
msgstr "Тест 1: Ерөнхий баланс"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_03
msgid "Test 3: Movement lines"
msgstr "Тест 3: Хөдөлгөөний мөрүүд"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_05
msgid ""
"Test 5.1 : Payable and Receivable accountant lines of reconciled invoices"
msgstr ""
"Тест 5.1 : Тулгасан нэхэмжлэлүүдийн авлага ба өглөгийн нягтлан бодогчийн "
"мөрүүд"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_05_2
msgid "Test 5.2 : Reconcilied invoices and Payable/Receivable accounts"
msgstr "Тэст 5.2 : Тулгагдсан нэхэмжлэлүүд ба  Авлага/Өглөгийн данснууд"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_06
msgid "Test 6 : Invoices status"
msgstr "Тэст 6 : Нэхэмжлэлийн төлөв"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_07
msgid "Test 7 : Closing balance on bank statements"
msgstr "Тест 7 : Банкны хуулганд баланс хаах"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__desc
msgid "Test Description"
msgstr "Тест тайлбар"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__name
msgid "Test Name"
msgstr "Тест нэр"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_tree
msgid "Tests"
msgstr "Тест"

#. module: account_test
#: code:addons/account_test/report/report_account_test.py:53
#, python-format
msgid "The test was passed successfully"
msgstr "Энэ тэст амжилттай давлаа"
