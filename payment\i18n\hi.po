# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment
#
# Translators:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-08 06:26+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Hindi (http://www.transifex.com/odoo/odoo-9/language/hi/)\n"
"Language: hi\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  hi.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  hi.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction_callback_eval
msgid ""
"            Will be safe_eval with `self` being the current transaction. i."
"e.:\n"
"                self.env['my.model'].payment_validated(self)"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_config_settings_module_payment_adyen
msgid "-This installs the module payment_adyen."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_config_settings_module_payment_authorize
msgid "-This installs the module payment_authorize."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_config_settings_module_payment_buckaroo
msgid "-This installs the module payment_buckaroo."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_config_settings_module_payment_ogone
msgid "-This installs the module payment_ogone."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_config_settings_module_payment_paypal
msgid "-This installs the module payment_paypal."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_config_settings_module_payment_sips
msgid "-This installs the module payment_sips."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_config_settings_module_payment_transfer
msgid "-This installs the module payment_transfer."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_html_3ds
msgid "3D Secure HTML"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"<span class=\"text-danger\">Test</span>\n"
"                                    <span class=\"o_stat_text\">Environment</"
"span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"<span class=\"text-success\">Production</span>\n"
"                                    <span class=\"o_stat_text\">Environment</"
"span>"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_acquirer_id
msgid "Acquirer"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method_acquirer_id
msgid "Acquirer Account"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method_acquirer_ref
msgid "Acquirer Ref."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_acquirer_reference
msgid "Acquirer Reference"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method_active
msgid "Active"
msgstr "सक्रिय"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_fees_active
msgid "Add Extra Fees"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_address
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Address"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_config_settings_module_payment_adyen
msgid "Adyen"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_amount
#: model:ir.model.fields,help:payment.field_payment_transaction_amount
msgid "Amount"
msgstr ""

#. module: payment
#: selection:payment.acquirer,auto_confirm:0
msgid "At payment no acquirer confirmation needed"
msgstr ""

#. module: payment
#: selection:payment.acquirer,auto_confirm:0
msgid "At payment with acquirer confirmation"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_config_settings_module_payment_authorize
msgid "Authorize.Net"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_config_settings_module_payment_buckaroo
msgid "Buckaroo"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_cancel_msg
msgid "Cancel Message"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Canceled"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_city
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "City"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_company_id
msgid "Company"
msgstr "संस्था"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Configuration"
msgstr "कॉन्फ़िगरेशन"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_installation
msgid "Configure payment acquiring methods"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner_payment_method_count
msgid "Count Payment Method"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Country"
msgstr "देश"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_create_uid
#: model:ir.model.fields,field_description:payment.field_payment_method_create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction_create_uid
msgid "Created by"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_create_date
#: model:ir.model.fields,field_description:payment.field_payment_method_create_date
msgid "Created on"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_create_date
msgid "Creation Date"
msgstr "निर्माण दिनांक"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Credentials"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "Credit card(s)"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_currency_id
msgid "Currency"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Customer Details"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_sequence
msgid "Determine the display order"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_display_name
#: model:ir.model.fields,field_description:payment.field_payment_method_display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction_display_name
msgid "Display Name"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Done"
msgstr "हो गया"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_done_msg
msgid "Done Message"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Draft"
msgstr "मसौदा"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "E-mail"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_email
msgid "Email"
msgstr "ईमेल"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_environment
msgid "Environment"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Error"
msgstr "त्रुटि!"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_error_msg
msgid "Error Message"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_fees
msgid "Fees"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction_fees
msgid "Fees amount; set by the system because depends on the acquirer"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction_state_message
msgid "Field used to store error and/or validation messages for information"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_fees_dom_fixed
msgid "Fixed domestic fees"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_fees_int_fixed
msgid "Fixed international fees"
msgstr ""

#. module: payment
#: selection:payment.transaction,type:0
msgid "Form"
msgstr "फार्म"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_view_template_id
msgid "Form Button Template"
msgstr ""

#. module: payment
#: selection:payment.transaction,type:0
msgid "Form with credentials storage"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Group By"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_pre_msg
msgid "Help Message"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_id
#: model:ir.model.fields,field_description:payment.field_payment_method_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction_id
msgid "ID"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_image
msgid "Image"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction_reference
msgid "Internal reference of the TX"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_lang
msgid "Language"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer___last_update
#: model:ir.model.fields,field_description:payment.field_payment_method___last_update
#: model:ir.model.fields,field_description:payment.field_payment_transaction___last_update
msgid "Last Modified on"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_write_uid
#: model:ir.model.fields,field_description:payment.field_payment_method_write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction_write_uid
msgid "Last Updated by"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_write_date
#: model:ir.model.fields,field_description:payment.field_payment_method_write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction_write_date
msgid "Last Updated on"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_website_published
msgid "Make this payment acquirer available (Customer invoices, etc.)"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_image_medium
msgid "Medium-sized image"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_image_medium
msgid ""
"Medium-sized image of this provider. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_state_message
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Message"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_post_msg
msgid "Message displayed after having done the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_pre_msg
msgid "Message displayed to explain and help the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_error_msg
msgid "Message displayed, if error is occur during the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_cancel_msg
msgid "Message displayed, if order is cancel during the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_done_msg
msgid ""
"Message displayed, if order is done successfully after having done the "
"payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_pending_msg
msgid ""
"Message displayed, if order is in pending state after having done the "
"payment process."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Messages"
msgstr "संदेश"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_name
#: model:ir.model.fields,field_description:payment.field_payment_method_name
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Name"
msgstr "नाम"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method_name
msgid "Name of the payment method"
msgstr ""

#. module: payment
#: selection:payment.acquirer,auto_confirm:0
msgid "No automatic confirmation"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_config_settings_module_payment_ogone
msgid "Ogone"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_auto_confirm
msgid "Order Confirmation"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_res_partner
#: model:ir.model.fields,field_description:payment.field_payment_method_partner_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_id
msgid "Partner"
msgstr "साथी"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_name
msgid "Partner Name"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Payment Acquirer"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_acquirer
#: model:ir.ui.menu,name:payment.payment_acquirer_menu
#: model_terms:ir.ui.view,arch_db:payment.acquirer_list
msgid "Payment Acquirers"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_payment_method_id
msgid "Payment Method"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner_payment_method_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form_view
#: model_terms:ir.ui.view,arch_db:payment.payment_method_tree_view
#: model_terms:ir.ui.view,arch_db:payment.payment_method_view_search
msgid "Payment Methods"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.actions.act_window,name:payment.action_payment_tx_ids
#: model:ir.actions.act_window,name:payment.payment_transaction_action_child
#: model:ir.model.fields,field_description:payment.field_payment_method_payment_ids
#: model:ir.ui.menu,name:payment.payment_transaction_menu
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
#: model_terms:ir.ui.view,arch_db:payment.transaction_list
msgid "Payment Transactions"
msgstr ""

#. module: payment
#: model:ir.ui.menu,name:payment.root_payment_menu
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form_view
msgid "Payments"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_config_settings_module_payment_paypal
msgid "Paypal"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Pending"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_pending_msg
msgid "Pending Message"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_phone
msgid "Phone"
msgstr ""

#. module: payment
#: selection:payment.acquirer,environment:0
msgid "Production"
msgstr "उत्पादन"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_provider
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Provider"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_reference
msgid "Reference"
msgstr "संदर्भ"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction_acquirer_reference
msgid "Reference of the TX as stored in the acquirer database"
msgstr ""

#. module: payment
#: constraint:payment.acquirer:0
msgid "Required fields not filled"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_callback_eval
msgid "S2S Callback"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_registration_view_template_id
msgid "S2S Form Template"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.payment_method_action
#: model:ir.ui.menu,name:payment.payment_method_menu
msgid "Saved Payment Data"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_sequence
msgid "Sequence"
msgstr "अनुक्रम"

#. module: payment
#: selection:payment.transaction,type:0
msgid "Server To Server"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_config_settings_module_payment_sips
msgid "Sips"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_image_small
msgid "Small-sized image"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_image_small
msgid ""
"Small-sized image of this provider. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is "
"required."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_state
msgid "Status"
msgstr "स्थिति"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_registration_view_template_id
msgid "Template for method registration"
msgstr ""

#. module: payment
#: selection:payment.acquirer,environment:0
msgid "Test"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_post_msg
msgid "Thanks Message"
msgstr ""

#. module: payment
#: constraint:payment.transaction:0
msgid "The payment transaction reference must be unique!"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer_image
msgid ""
"This field holds the image used for this provider, limited to 1024x1024px"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"This template renders the acquirer button with all necessary values.\n"
"                                            It is be rendered with qWeb with "
"the following evaluation context:"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_type
msgid "Type"
msgstr "प्रकार"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_date_validate
msgid "Validation Date"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_fees_int_var
msgid "Variable international fees (in percents)"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_website_published
msgid "Visible in Portal / Website"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_config_settings_module_payment_transfer
msgid "Wire Transfer"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "ZIP"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction_partner_zip
msgid "Zip"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_account_config_settings
msgid "account.config.settings"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "acquirer: payment.acquirer browse record"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "amount: the transaction amount, a float"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "context: the current context dictionary"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "currency: the transaction currency browse record"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "partner: the buyer partner browse record, not necessarily set"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"partner_values: specific values about the buyer, for example coming from a "
"shipping form"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_method
msgid "payment.method"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "reference: the transaction reference number"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "tx_url: transaction URL to post the form"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "tx_values: transaction values"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "user: current user browse record"
msgstr ""
