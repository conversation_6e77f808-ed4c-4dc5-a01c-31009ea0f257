# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* survey
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_4
msgid "$100"
msgstr "100 $"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_1
msgid "$20"
msgstr "$20"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_5
msgid "$200"
msgstr "200 $"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_6
msgid "$300"
msgstr "300 $"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_2
msgid "$50"
msgstr "50 $"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_3
msgid "$80"
msgstr "80 $"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "% completed"
msgstr "% complet"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copie)"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s certification passed"
msgstr "%s certificat trecut"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s challenge certification"
msgstr "%s provocare certificare"

#. module: survey
#: model:ir.actions.report,print_report_name:survey.certification_report
msgid "'Certification - %s' % (object.survey_id.display_name)"
msgstr "'Certificare - %s' % (object.survey_id.display_name)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug2
msgid "10 kg"
msgstr "10 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug2
msgid "100 years"
msgstr "100 ani"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug3
msgid "1055"
msgstr "1055"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug3
msgid "116 years"
msgstr "116 ani"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug1
msgid "1227"
msgstr "1227"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug4
msgid "127 years"
msgstr "127 ani"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug2
msgid "1324"
msgstr "1324"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug1
msgid "1450 km"
msgstr "1450 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug3
msgid "16.2 kg"
msgstr "16.2 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug2
msgid "3700 km"
msgstr "3700 km"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "403: Forbidden"
msgstr "403: Interzis"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug4
msgid "47 kg"
msgstr "47 kg"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "4812"
msgstr "4812"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug1
msgid "5.7 kg"
msgstr "5.7 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug3
msgid "6650 km"
msgstr "6650 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug1
msgid "99 years"
msgstr "99 ani"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"<b>Certificate</b>\n"
"                            <br/>"
msgstr ""
"<b>Certificat</b>\n"
"                            <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<br/>\n"
"                        <span>Score:</span>"
msgstr ""
"<br/>\n"
"<span>Scor:</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "<br/>by"
msgstr "<br/>de"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q6
msgid ""
"<div class=\"text-center\">\n"
"                <div class=\"media_iframe_video\" data-oe-expression=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" style=\"width: 50%;\">\n"
"                    <div class=\"css_editable_mode_display\"/>\n"
"                    <div class=\"media_iframe_video_size\" contenteditable=\"false\"/>\n"
"                    <iframe src=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" frameborder=\"0\" contenteditable=\"false\"/>\n"
"                </div><br/>\n"
"            </div>\n"
"        "
msgstr ""
"<div class=\"text-center\">\n"
"                <div class=\"media_iframe_video\" data-oe-expression=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" style=\"width: 50%;\">\n"
"                    <div class=\"css_editable_mode_display\"/>\n"
"                    <div class=\"media_iframe_video_size\" contenteditable=\"false\"/>\n"
"                    <iframe src=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" frameborder=\"0\" contenteditable=\"false\"/>\n"
"                </div><br/>\n"
"            </div>\n"
"        "

#. module: survey
#: model:mail.template,body_html:survey.mail_template_certification
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certification: <t t-out=\"object.survey_id.display_name or ''\">Feedback Form</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Dear <span t-out=\"object.partner_id.name or 'participant'\">participant</span></p>\n"
"                <p>\n"
"                    Here is, in attachment, your certification document for\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Feedback Form</strong>\n"
"                </p>\n"
"                <p>Congratulations for succeeding the test!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model:mail.template,body_html:survey.mail_template_user_input_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant'\">participant</t><br/><br/>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            You have been invited to take a new certification.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            We are conducting a survey and your response would be appreciated.\n"
"        </t>\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Start Certification\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Start Survey\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the survey for <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br/><br/>\n"
"        </t>\n"
"        Thank you for your participation.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123..</i>"
msgstr "<i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123..</i>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple "
"lines\" title=\"Multiple Lines\"/>"
msgstr ""
"<i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple "
"lines\" title=\"Multiple Lines\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "<i class=\"fa fa-bar-chart\"/> Graph"
msgstr "<i class=\"fa fa-bar-chart\"/> Graph"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-bar-chart\"/> Results"
msgstr "<i class=\"fa fa-bar-chart\"/> Rezultate"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg\"/> răspuns"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg\"/> answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg\"/>răspuns"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"
msgstr ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-close\"/> Close"
msgstr "<i class=\"fa fa-close\"/> Închidere"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"
msgstr ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg\"/> răspuns"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Certificare Descărcare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<i class=\"fa fa-list-alt\"/> All Data"
msgstr "<i class=\"fa fa-list-alt\"/> Toate Datele"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "<i class=\"fa fa-list-alt\"/> Data"
msgstr "<i class=\"fa fa-list-alt\"/> Data"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<i class=\"fa fa-list-ol\"/> Most Common"
msgstr "<i class=\"fa fa-list-ol\"/>Cel mai comun"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" "
"title=\"Single Line\"/>"
msgstr ""
"<i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" "
"title=\"Single Line\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-square-o fa-lg\"/> răspuns"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "<i class=\"fa fa-times\"/> Clear All Filters"
msgstr "<i class=\"fa fa-times\"/> Stergeți toate Filtrele"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q3
msgid ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/coniferous.jpg\"/><br/>\n"
"            </p>\n"
"        "
msgstr ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/coniferous.jpg\"/><br/>\n"
"            </p>\n"
"        "

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q4
msgid ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/pinus_sylvestris.jpg\" style=\"width: 100%;\"/><br/>\n"
"            </p>\n"
"        "
msgstr ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/pinus_sylvestris.jpg\" style=\"width: 100%;\"/><br/>\n"
"            </p>\n"
"        "

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4
msgid ""
"<p>\n"
"                We like to say that the apple doesn't fall far from the tree, so here are trees.\n"
"            </p>\n"
"        "
msgstr ""
"<p>\n"
"               Ne place să spunem că mărul nu cade departe de copac, așa că aici sunt copaci.\n"
"            </p>\n"
"        "

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3
msgid "<p>An apple a day keeps the doctor away.</p>"
msgstr "<p>Un măr pe zi ține doctorul la distanță.</p>"

#. module: survey
#: model:survey.survey,description:survey.survey_demo_burger_quiz
msgid ""
"<p>Choose your favourite subject and show how good you are. Ready ?</p>"
msgstr ""
"<p>Alege-ți subiectul preferat și arată cât de bun ești. Pregătit?</p>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p1_q4
msgid "<p>Just to categorize your answers, don't worry.</p>"
msgstr "<p>Doar pentru a vă clasifica răspunsurile, nu vă faceți griji.</p>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p1
msgid ""
"<p>Some general information about you. It will be used internally for "
"statistics only.</p>"
msgstr ""
"<p>Câteva informații generale despre tine. Acesta va fi utilizat intern doar"
" pentru statistici.</p>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p2
msgid "<p>Some questions about our company. Do you really know us?</p>"
msgstr ""
"<p>Câteva întrebări despre compania noastră. Știi cu adevărat despre "
"noi?</p>"

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_3
msgid "<p>Test your knowledge of our policies.</p>"
msgstr "<p>Testați-vă cunoștințele despre politicile noastre.</p>"

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_2
msgid "<p>Test your knowledge of our prices.</p>"
msgstr "<p>Testați-vă cunoștințele despre prețurile noastre.</p>"

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_1
msgid "<p>Test your knowledge of your products!</p>"
msgstr "<p>Testează-ți cunoștințele despre produsele tale!</p>"

#. module: survey
#: model:survey.survey,description:survey.vendor_certification
msgid "<p>Test your vendor skills!</p>"
msgstr "<p>Testați-vă abilitățile de vânzător!</p>"

#. module: survey
#: model:survey.question,description:survey.survey_feedback_p1
msgid ""
"<p>This section is about general information about you. Answering them helps"
" qualifying your answers.</p>"
msgstr ""
"<p>Această secțiune este despre informații generale despre tine. "
"Răspunsurile ajută la calificarea răspunsurilor tale.</p>"

#. module: survey
#: model:survey.question,description:survey.survey_feedback_p2
msgid "<p>This section is about our eCommerce experience itself.</p>"
msgstr ""
"<p>Această secțiune este despre experiența noastră eCommerce în sine.</p>"

#. module: survey
#: model:survey.survey,description:survey.survey_demo_quiz
msgid ""
"<p>This small quiz will test your knowledge about our Company. Be prepared "
"!</p>"
msgstr ""
"<p>Acest mic test vă va testa cunoștințele despre compania noastră. Fiți "
"pregătit !</p>"

#. module: survey
#: model:survey.survey,description:survey.survey_feedback
msgid ""
"<p>This survey allows you to give a feedback about your experience with our products.\n"
"    Filling it helps us improving your experience.</p>"
msgstr ""
"<p>Acest sondaj vă permite să oferiți feedback despre experiența dvs. cu produsele noastre.\n"
"    Completarea acestuia ne ajută să vă îmbunătățim experiența.</p>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p5
msgid "<p>We may be interested by your input.</p>"
msgstr "<p>Putem fi interesați de opinia ta.</p>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span attrs=\"{'invisible': [('is_time_limited', '=', False)]}\"> "
"seconds</span>"
msgstr ""
"<span attrs=\"{'invisible': [('is_time_limited', '=', False)]}\"> "
"secunde</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid ""
"<span class=\"badge badge-primary only_left_radius\"><i class=\"fa fa-"
"filter\" role=\"img\" aria-label=\"Filter\" title=\"Filter\"/></span>"
msgstr ""
"<span class=\"badge badge-primary only_left_radius\"><i class=\"fa fa-"
"filter\" role=\"img\" aria-label=\"Filter\" title=\"Filter\"/></span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Average </span>"
msgstr "<span class=\"badge badge-secondary only_left_radius\">Mediu </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Maximum </span>"
msgstr "<span class=\"badge badge-secondary only_left_radius\">Maxim</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Minimum </span>"
msgstr "<span class=\"badge badge-secondary only_left_radius\">Minim </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "<span class=\"fa fa-filter\"/>  Filters"
msgstr "<span class=\"fa fa-filter\"/>  Filtre"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\"> or "
"press Enter</span>"
msgstr ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\"> sau "
"apăsați Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\">or press"
" Enter</span>"
msgstr ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\">sau "
"apăsați Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&lt;', 2)]}\">Certificări</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&gt;', 1)]}\">Certificare</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&lt;', 2)]}\">Certificări</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&gt;', 1)]}\">Certificare</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid ""
"<span class=\"o_survey_enter font-weight-bold text-muted ml-2\">or press "
"Enter</span>"
msgstr ""
"<span class=\"o_survey_enter font-weight-bold text-muted ml-2\">sau apăsați "
"Enter</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_selection_key
msgid ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"left py-0 pl-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Key</span></span>"
msgstr ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"left py-0 pl-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Cheie</span></span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"
msgstr ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid ""
"<span class=\"o_survey_session_error_invalid_code d-none\">Code is incorrect.</span>\n"
"                                    <span class=\"o_survey_session_error_closed d-none\">Session is finished.</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "<span class=\"text-muted\">Answers</span>"
msgstr "<span class=\"text-muted\">Răspunsuri</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "<span class=\"text-muted\">Success</span>"
msgstr "<span class=\"text-muted\">Success</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q1
msgid ""
"<span>\"Red\" is not a category, I know what you are trying to do ;)</span>"
msgstr "<span>\"Roșu\" nu este o categorie, știu ce încercați să faceți ;)</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q6
msgid "<span>Best time to do it, is the right time to do it.</span>"
msgstr ""
"<span>Cel mai bun moment pentru a o face, este momentul potrivit pentru a o "
"face.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "<span>Date</span>"
msgstr "<span>Dată</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p5_q1
msgid ""
"<span>If you don't like us, please try to be as objective as "
"possible.</span>"
msgstr ""
"<span>Dacă nu vă place de noi, vă rugăm să fiți cât mai obiectiv "
"posibil.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "<span>Number of attemps left</span>:"
msgstr "<span>Numărul de atenții rămase</span>:"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p2_q1
msgid "<span>Our famous Leader !</span>"
msgstr "<span>Faimosul nostru lider !</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q3
msgid "<span>Our sales people have an advantage, but you can do it !</span>"
msgstr ""
"<span>Oamenii noștri de vânzări au un avantaj, dar puteți face acest lucru !"
" </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span>Right answer:</span>"
msgstr "<span>Răspuns corect:</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "<span>Time limit for this survey: </span>"
msgstr "<span>Termenul limită pentru acest sondaj: </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "<span>Waiting for attendees...</span>"
msgstr "<span>Așteptare participanți...</span>"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q4
msgid "A \"Citrus\" could give you ..."
msgstr "O \"Citrică\" ar putea să-ți dea ..."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "A label must be attached to only one question."
msgstr "O singură întrebare trebuie să fie atașată unei etichete."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_max
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_min
msgid "A length must be positive!"
msgstr "O lungime trebuie să fie pozitivă!"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_4
msgid "A little bit overpriced"
msgstr "Un pic supraevaluat"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_5
msgid "A lot overpriced"
msgstr "Mult prea scump"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__answer_score
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""
"Un scor pozitiv indică o alegere corectă; un scor negativ sau nul indică un "
"răspuns greșit"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "A problem has occurred"
msgstr "A apărut o problemă"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "A question can either be skipped or answered, not both."
msgstr "O întrebare poate fi omisă sau răspunsă, nu ambele."

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2
msgid "About our ecommerce"
msgstr "Despre ecommerce"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1
msgid "About you"
msgstr "Despre tine"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_access_mode
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_mode
msgid "Access Mode"
msgstr "Mod Acces"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_token
msgid "Access Token"
msgstr "Access Token"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_access_token_unique
msgid "Access token should be unique"
msgstr "Token Acces trebuie să fie unic"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
msgid "Action Needed"
msgstr "Intervenție necesară"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "Activ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
msgid "Activities"
msgstr "Activități"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitate Excepție Decorare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
msgid "Activity State"
msgstr "Stare activitate"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_icon
msgid "Activity Type Icon"
msgstr "Pictograma tipului de activitate"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Add a list of email of recipients (will not be converted into contacts). "
"Separated by commas, semicolons or newline..."
msgstr ""
"Adăugați o listă de e-mail a destinatarilor (nu vor fi convertiți în "
"contacte). Separat prin virgule, virgule și linie nouă..."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add a new survey"
msgstr "Adăugare sondaj new"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Add a question"
msgstr "Adăugați o întrebare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Add a section"
msgstr "Adaugă o secțiune"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Add existing contacts..."
msgstr "Adăugați contacte existente ..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__emails
msgid "Additional emails"
msgstr "E-mailuri suplimentare"

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Administrator"
msgstr "Administrator"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug1
msgid "Africa"
msgstr "Africa"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q6
msgid ""
"After watching this video, will you swear that you are not going to "
"procrastinate to trim your hedge this year ?"
msgstr ""
"După ce ați urmărit acest videoclip, veți jura că nu veți amâna să vă tăiați"
" gardul viu anul acesta?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col3
msgid "Agree"
msgstr "Deacord"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_date_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Date\" questions "
"need an answer"
msgstr ""
"Toate întrebările \"Este o întrebare notată = Adevărat\" și \"Tipul de "
"întrebare: Dată\" necesită un răspuns"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_datetime_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Datetime\" "
"questions need an answer"
msgstr ""
"Toate întrebările \"Este o întrebare notată = Adevărat\" și \"Tipul de "
"întrebare: Datetime\" necesită un răspuns"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__all
msgid "All questions"
msgstr "Toate întrebările"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "All surveys"
msgstr "Toate sondajele"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Allow Comments"
msgstr "Permite comentarii"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug2
msgid "Amenhotep"
msgstr "Amenhotep"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_user_input_unique_token
msgid "An access token must be unique!"
msgstr "Un token de acces trebuie să fie unic!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_answer_score
msgid "An answer score for a non-multiple choice question cannot be negative!"
msgstr ""
"Un punctaj de răspuns pentru o întrebare cu opțiuni non-multiple nu poate fi"
" negativ!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_leaderboard
msgid "Anonymous"
msgstr "Anonim"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Answer"
msgstr "Raspuns"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr "Tip de răspuns"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__deadline
msgid "Answer deadline"
msgstr "Termenul răspuns"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_answer_id
msgid "Answer that will trigger the display of the current question."
msgstr "Răspuns care va declanșa afișarea întrebării curente."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Answered"
msgstr "S-a raspuns"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Answers"
msgstr "Răspunsuri"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_answer_count
msgid "Answers Count"
msgstr "Număr răspunsuri"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__public
msgid "Anyone with the link"
msgstr "Oricine care are legătura"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Apare în"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug1
msgid "Apple Trees"
msgstr "Măr"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row1
msgid "Apples"
msgstr "Mere"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "Arhivat"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug4
msgid "Art & Culture"
msgstr "Artă & Cultură"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug1
msgid "Arthur B. McDonald"
msgstr "Arthur B. McDonald"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug2
msgid "Asia"
msgstr "Asia"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__attachment_ids
msgid "Attachments"
msgstr "Atașamente"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_number
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempt n°"
msgstr "Încercare nr."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_done_count
msgid "Attempts"
msgstr "Încercări"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Attempts Limit"
msgstr "Limită Încercări"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__nickname
msgid ""
"Attendee nickname, mainly used to identify him in the survey session "
"leaderboard."
msgstr ""
"Nickname participant, folosită în principal pentru a-l identifica în "
"clasamentul sesiunii de sondaj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Attendees are answering the question..."
msgstr "Participanții răspund la întrebare..."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating
msgid "Attendees get more points if they answer quickly"
msgstr "Participanții obțin mai multe puncte dacă răspund rapid"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__author_id
msgid "Author"
msgstr "Autor"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__author_id
msgid "Author of the message."
msgstr "Autor al mesajului."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__certification_mail_template_id
msgid ""
"Automated email sent to the user when he succeeds the certification, "
"containing his certification document."
msgstr ""
"E-mail automat trimis utilizatorului atunci când reușește certificarea, "
"conținând documentul său de certificare."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug3
msgid "Autumn"
msgstr "Toamnă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_duration_avg
msgid "Average Duration"
msgstr "Durată medie"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__answer_duration_avg
msgid "Average duration of the survey (in hours)"
msgstr "Durata medie a chestionarului (în ore)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_score_avg
msgid "Avg Score %"
msgstr "Scor Mediu %"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug3
msgid "Avicii"
msgstr "Avicii"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Back Button"
msgstr "Buton de mers înapoi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image
msgid "Background Image"
msgstr "Imagine fundal"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge"
msgstr "Insignă"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug3
msgid "Baobab Trees"
msgstr "Copaci Baobab"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug1
msgid "Bees"
msgstr "Albine"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug4
msgid "Bricks"
msgstr "Cărămizi"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_burger_quiz
msgid "Burger Quiz"
msgstr "Test Burger"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_3
msgid "Cabinet with Doors"
msgstr "Dulap cu Uși"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row1
msgid "Cactus"
msgstr "Cactus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__can_edit_body
msgid "Can Edit Body"
msgstr "Poate edita corpul"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q3
msgid "Can Humans ever directly see a photon ?"
msgstr "Oamenii pot vedea vreodată în mod direct un foton?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Cancel"
msgstr "Anulează"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Candidates"
msgstr "Candidați"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
msgid "Certification"
msgstr "Certificare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id
msgid "Certification Badge"
msgstr "Insignă Certificare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id_dummy
msgid "Certification Badge "
msgstr "Insignă Certificare"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Certification Badge is not configured for the survey %(survey_name)s"
msgstr ""
"Insigna de certificare nu este configurată pentru chestionarul "
"%(survey_name)s"

#. module: survey
#: model:mail.template,report_name:survey.mail_template_certification
msgid "Certification Document"
msgstr "Document Certificare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "Certification Failed"
msgstr "Certificare eșuată"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Certification Template"
msgstr "Model Certificare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "Certification n°"
msgstr "Certificare nr."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_report_layout
msgid "Certification template"
msgstr "Model Certificat"

#. module: survey
#: model:mail.template,subject:survey.mail_template_certification
msgid "Certification: {{ object.survey_id.display_name }}"
msgstr "Certificare: {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.report,name:survey.certification_report
#: model:ir.model.fields.selection,name:survey.selection__gamification_challenge__challenge_category__certification
msgid "Certifications"
msgstr "Certificări"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_count
msgid "Certifications Count"
msgstr "Număr Certificări"

#. module: survey
#: model:ir.actions.act_window,name:survey.res_partner_action_certifications
msgid "Certifications Succeeded"
msgstr "Certificări Reușite"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Certified"
msgstr "Certificat"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_1
msgid "Chair floor protection"
msgstr "Protecție podea scaun"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Chart"
msgstr "Grafic"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,help:survey.field_survey_user_input__is_attempts_limited
msgid "Check this option if you want to limit the number of attempts per user"
msgstr ""
"Bifează această opțiune dacă vrei să limitezi numărul de încercări pe "
"utilizator"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug2
msgid "China"
msgstr "China"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "Alegeri"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_blue
msgid "Classic Blue"
msgstr "Albastru Clasic"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_gold
msgid "Classic Gold"
msgstr "Aur Clasic"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_purple
msgid "Classic Purple"
msgstr "Violet Clasic"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row3
msgid "Clementine"
msgstr "Clementină"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug4
msgid "Cliff Burton"
msgstr "Cliff Burton"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Close"
msgstr "Închide"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Close Live Session"
msgstr "Închidere Sesiune Live"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_1
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Color"
msgstr "Color"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr "Index Culori"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_comments
msgid "Comment"
msgstr "Comentariu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment Field is an Answer Choice"
msgstr "Câmpul de comentariu este o opțiune de răspuns"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr "Mesaj comentariu"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_company_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_company_count
msgid "Company Certifications Count"
msgstr "Număr Certificări Companie"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__done
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Completed"
msgstr "Finalizat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Compose Email"
msgstr "Compune e-mail"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "Computing score requires a question in arguments."
msgstr "Calcularea scorului necesită o întrebare în argumente."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_conditional
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Conditional Display"
msgstr "Afișaj Condiționat"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_3
msgid "Conference chair"
msgstr "Scaun de conferință"

#. module: survey
#: model_terms:gamification.badge,description:survey.vendor_certification_badge
msgid "Congratulations, you are now official vendor of MyCompany"
msgstr "Felicitări, sunteți acum furnizor oficial al MyCompany"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Congratulations, you have passed the test!"
msgstr "Felicitări, ați trecut testul!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "Constrangeri"

#. module: survey
#: model:ir.model,name:survey.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_conditional_questions
msgid "Contains conditional questions"
msgstr "Conține întrebări condiționale"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body
msgid "Contents"
msgstr "Conținut"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Continue"
msgstr "Continuați"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "Continue here"
msgstr "Continuați aici"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug4
msgid "Cookies"
msgstr "Cookie-uri"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#, python-format
msgid "Copied !"
msgstr "Copiat !"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug3
msgid "Cornaceae"
msgstr "Cornaceae"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_1
msgid "Corner Desk Right Sit"
msgstr "Masa de colț Dreapta"

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_is_correct
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#, python-format
msgid "Correct"
msgstr "Corect"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Correct Answer"
msgstr "Răspuns Corect"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_datetime
msgid "Correct date and time answer for this question."
msgstr "Răspundeți corect la data și ora la această întrebare."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_date
msgid "Correct date answer"
msgstr "Răspuns dată corect"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_date
msgid "Correct date answer for this question."
msgstr "Răspuns corect la această întrebare."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_datetime
msgid "Correct datetime answer"
msgstr "Răspuns dată și oră corect"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_numerical_box
msgid "Correct number answer for this question."
msgstr "Răspunsul la numărul corect pentru această întrebare."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_numerical_box
msgid "Correct numerical answer"
msgstr "Răspuns numeric corect"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_3
msgid "Correctly priced"
msgstr "Preț corect"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug3
msgid "Cosmic rays"
msgstr "Raze cosmice"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Create Live Session"
msgstr "Creare Sesiune Live"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "Creat în"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating test token is not allowed for you."
msgstr "Crearea jetonului de testare nu este permisă pentru dvs."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for anybody else than employees is not allowed for internal "
"surveys."
msgstr ""
"Crearea de jeton pentru altcineva decât angajații nu este permisă pentru "
"sondaje interne."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating token for closed/archived surveys is not allowed."
msgstr "Crearea de jeton pentru sondaje închise/arhivate nu este permisă."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for external people is not allowed for surveys requesting "
"authentication."
msgstr ""
"Crearea de jeton pentru persoane externe nu este permisă pentru sondaje care"
" solicită autentificare."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_id
msgid "Current Question"
msgstr "Întrebare Curentă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_start_time
msgid "Current Question Start Time"
msgstr "Oră Începere Întrebare Curentă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_start_time
msgid "Current Session Start Time"
msgstr "Oră Începere Sesiune Curentă"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_time_limited
msgid "Currently only supported for live sessions."
msgstr "În prezent este acceptat doar pentru sesiuni live."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Customers will receive a new token and be able to completely retake the "
"survey."
msgstr "Clienții vor primi un nou token și vor putea relua sondaj complet."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Customers will receive the same token."
msgstr "Clienții vor primi același token."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_5
msgid "Customizable Lamp"
msgstr "Lampă personalizabilă"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__date
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__date
msgid "Date"
msgstr "Dată"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr "Dată răspuns"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__datetime
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__datetime
msgid "Datetime"
msgstr "Dată și oră"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_datetime
msgid "Datetime answer"
msgstr "Dată și oră răspuns"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Datetime until customer can open the survey and submit answers"
msgstr ""
"Dată și oră până când clientul poate deschide sondajul și trimite răspunsuri"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr "Termen limita"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__null_value
msgid "Default Value"
msgstr "Valoare implicită"

#. module: survey
#: model:ir.model.fields,help:survey.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Definiți vizibilitate provocare prin meniuri"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Delete"
msgstr "Șterge"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Description"
msgstr "Descriere"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Design easily your survey, send invitations and analyze answers."
msgstr ""
"Proiectați cu ușurință sondaj, trimiteți invitații și analizați "
"răspunsurile."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_2
msgid "Desk Combination"
msgstr "Combinație de birou"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_user_input_line_action
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "Detailed Answers"
msgstr "Răspunsuri Detaliate"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col2
msgid "Disagree"
msgstr "Dezaprobă"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Display"
msgstr "Afișare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__allow_value_image
msgid ""
"Display images in addition to answer label. Valid only for simple / multiple"
" choice questions."
msgstr ""
"Afișare imagini pe lângă eticheta de răspuns. Valabil numai pentru întrebări"
" cu alegeri simple / multiple."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_1
msgid "Do we sell Acoustic Bloc Screens?"
msgstr "Vândem ecrane Acoustic Bloc?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q3
msgid "Do you have any other comments, questions, or concerns ?"
msgstr "Aveți alte comentarii, întrebări sau nelămuriri?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_5
msgid "Do you think we have missing products in our catalog? (not rated)"
msgstr "Crezi că lipsesc produse în catalogul nostru? (Nu a fost votat)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug2
msgid "Dogs"
msgstr "Câini"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q1
msgid "Dogwood is from which family of trees ?"
msgstr "Dogwood face parte din ce familie de arbori?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug1
msgid "Douglas Fir"
msgstr "Douglas Fir"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_4
msgid "Drawer"
msgstr "Sertar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Dropdown menu"
msgstr "Meniul derulant"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Edit Survey"
msgstr "Editeaza Sondajul"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "Edit in backend"
msgstr "Editează în spate"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Email"
msgstr "Email"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_mail_template_id
msgid "Email Template"
msgstr "Șablon email"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__email_from
msgid "Email address of the sender."
msgstr "Adresă e-mail expeditor."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__description_done
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "End Message"
msgstr "Finalizare Mesaj"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__end_datetime
msgid "End date and time"
msgstr "Data și ora de încheiere"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Enter Session Code"
msgstr "Introduceți codul sesiunii"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr "Mesaj de eroare"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug3
msgid "Europe"
msgstr "Europa"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug3
msgid "European Yew"
msgstr "European Yew"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Except Test Entries"
msgstr "Cu Excepția Intrărilor de Testare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_partner_ids
msgid "Existing Partner"
msgstr "Partener Existent"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_emails
msgid "Existing emails"
msgstr "E-mailuri Existente"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug2
msgid "Eyjafjallajökull (Iceland)"
msgstr "Eyjafjallajökull (Islanda)"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_2
msgid "Fanta"
msgstr "Fanta"

#. module: survey
#: model:survey.survey,title:survey.survey_feedback
msgid "Feedback Form"
msgstr "Formular feedback"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row2
msgid "Ficus"
msgstr "Ficus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__model_object_field
msgid "Field"
msgstr "Câmp"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Filter question"
msgstr "Filtrare întrebare"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#, python-format
msgid "Final Leaderboard"
msgstr "Clasament Final"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Expresie înlocuitoare finală, care va fi copiată in câmpul șablon dorit."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Finished surveys"
msgstr "Sondaje finalizate"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
msgid "Followers"
msgstr "Persoane interesate"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Pictogramă minunată pentru font, de ex. fa-sarcini"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__text_box
msgid "Free Text"
msgstr "Text liber"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text_box
msgid "Free Text answer"
msgstr "Răspuns în text"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__email_from
msgid "From"
msgstr "De la"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q4
msgid "From which continent is native the Scots pine (pinus sylvestris) ?"
msgstr "De pe ce continent este nativ pinul scotian (pinus sylvestris)?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug1
msgid "Fruits"
msgstr "Fructe"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3
msgid "Fruits and vegetables"
msgstr "Fructe și legume"

#. module: survey
#: model:ir.model,name:survey.model_gamification_badge
msgid "Gamification Badge"
msgstr "Insignă Competiție"

#. module: survey
#: model:ir.model,name:survey.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Provocare Competiție"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug1
msgid "Geography"
msgstr "Geografie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_give_badge
msgid "Give Badge"
msgstr "Dă Insigna"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q3
msgid "Give the list of all types of wood we sell."
msgstr "Oferiți lista cu toate tipurile de lemn pe care le vindem."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "Go to"
msgstr "Mergi la"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug1
msgid "Good"
msgstr "Bun/bine"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug4
msgid "Good value for money"
msgstr "Bună valoare pentru bani"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug2
msgid "Grapefruits"
msgstr "Grapefruit"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Graph"
msgstr "Grafic"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Group By"
msgstr "Grupează după"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Handle existing"
msgstr "Manevrare existentă"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug1
msgid "Hard"
msgstr "Tare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_2
msgid "Height"
msgstr "Înălțime"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug3
msgid "Hemiunu"
msgstr "Hemiunu"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug1
msgid "High quality"
msgstr "Calitate superioară"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug2
msgid "History"
msgstr "Istoric"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q3
msgid "How frequently do you buy products online ?"
msgstr "Cât de des cumpărați produse online?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q1
msgid "How long is the White Nile river?"
msgstr "Cât de lung este râul Nilul Alb?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_6
msgid ""
"How many chairs do you think we should aim to sell in a year (not rated)?"
msgstr ""
"Câte scaune credeți că ar trebui să ne propunem să vindem într-un an (fără "
"rating)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_1
msgid "How many days is our money-back guarantee?"
msgstr "Câte zile este garanția noastră de returnare a banilor?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q4
msgid "How many times did you order products on our website ?"
msgstr "De câte ori ați comandat produse pe site-ul nostru ?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_4
msgid "How many versions of the Corner Desk do we have?"
msgstr "Câte versiuni de Birou de Colț avem?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q3
msgid "How many years did the 100 years war last ?"
msgstr "Câți ani a durat războiul de 100 de ani?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_1
msgid "How much do we sell our Cable Management Box?"
msgstr "Cât vindem caseta noastră de gestionare a cablurilor?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q5
msgid "How often should you water those plants"
msgstr "Cât de des ar trebui să udați acele plante"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q4
msgid "How old are you ?"
msgstr "Câți ani aveți ?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug4
msgid ""
"I actually don't like thinking. I think people think I like to think a lot. "
"And I don't. I do not like to think at all."
msgstr ""
"De fapt nu-mi place să gândesc. Cred că oamenii cred că îmi place să gândesc"
" mult. Și eu nu. Nu-mi place deloc să gândesc."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug2
msgid ""
"I am fascinated by air. If you remove the air from the sky, all the birds "
"would fall to the ground. And all the planes, too."
msgstr ""
"Sunt fascinat de aer. Dacă îndepărtați aerul din cer, toate păsările ar "
"cădea la pământ. Și toate avioanele, de asemenea."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row5
msgid "I have added products to my wishlist"
msgstr "Am adăugat produse la lista mea de favorite"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug4
msgid "I have no idea, I'm a dog!"
msgstr "Habar n-am, sunt câine!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug3
msgid "I've been noticing gravity since I was very young !"
msgstr "Remarc gravitatea de când eram foarte tânăr!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_icon
msgid "Icon"
msgstr "Pictogramă"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Pictograma pentru a indica o activitate de excepție."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__access_token
msgid "Identification token"
msgstr "Cod identificare"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__progression_mode
msgid ""
"If Number is selected, it will display the number of questions answered on "
"the total number of question to answer."
msgstr ""
"Dacă este selectat Numărul, acesta va afișa numărul de întrebări răspuns la "
"numărul total de întrebări de răspuns."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_3
msgid ""
"If a customer purchases a 1 year warranty on 6 January 2020, when do we "
"expect the warranty to expire?"
msgstr ""
"Dacă un client achiziționează o garanție de 1 an la 6 ianuarie 2020, când ne"
" așteptăm să expire garanția?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_2
msgid ""
"If a customer purchases a product on 6 January 2020, what is the latest day "
"we expect to ship it?"
msgstr ""
"Dacă un client achiziționează un produs pe 6 ianuarie 2020, care este ultima"
" zi în care ne așteptăm să îl livrăm?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread
msgid "If checked, new messages require your attention."
msgstr "Dacă este selectat, mesajele noi necesită atenția dumneavoastră."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, unele mesaje au o eroare de livrare."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_email
msgid ""
"If checked, this option will save the user's answer as its email address."
msgstr ""
"Dacă este bifată, această opțiune va salva răspunsul utilizatorului ca "
"adresă de e-mail."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_nickname
msgid "If checked, this option will save the user's answer as its nickname."
msgstr ""
"Dacă este bifată, această opțiune va salva răspunsul utilizatorului ca "
"pseudonim."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_conditional
msgid ""
"If checked, this question will be displayed only \n"
"        if the specified conditional answer have been selected in a previous question"
msgstr ""
"Dacă este bifată, această întrebare va fi afișată numai\n"
"       acă răspunsul condiționat specificat a fost selectat într-o întrebare anterioară"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr ""
"Dacă este bifată, utilizatorii se pot întoarce la paginile anterioare."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,help:survey.field_survey_survey__users_login_required
msgid ""
"If checked, users have to login before answering even with a valid token."
msgstr ""
"Dacă este bifat, utilizatorii trebuie să se autentifice înainte de a "
"răspunde chiar și cu un toekn valabil."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q6
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q6
#: model:survey.question,comments_message:survey.survey_demo_quiz_p5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p5_q1
#: model:survey.question,comments_message:survey.survey_feedback_p1
#: model:survey.question,comments_message:survey.survey_feedback_p1_q1
#: model:survey.question,comments_message:survey.survey_feedback_p1_q2
#: model:survey.question,comments_message:survey.survey_feedback_p1_q3
#: model:survey.question,comments_message:survey.survey_feedback_p1_q4
#: model:survey.question,comments_message:survey.survey_feedback_p2
#: model:survey.question,comments_message:survey.survey_feedback_p2_q1
#: model:survey.question,comments_message:survey.survey_feedback_p2_q2
#: model:survey.question,comments_message:survey.survey_feedback_p2_q3
#: model:survey.question,comments_message:survey.vendor_certification_page_1
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_4
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_5
#: model:survey.question,comments_message:survey.vendor_certification_page_2
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_4
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_5
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_6
#, python-format
msgid "If other, please specify:"
msgstr "Dacă  este altul, vă  rugă m specificați:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__questions_selection
msgid ""
"If randomized is selected, add the number of random questions next to the "
"section."
msgstr ""
"Dacă este selectat randomizat, adăugați numărul de întrebări aleatoare lângă"
" secțiune."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__questions_selection
msgid ""
"If randomized is selected, you can configure the number of random questions "
"by section. This mode is ignored in live session."
msgstr ""
"Dacă este selectat randomizat, puteți configura numărul de întrebări "
"aleatorii pe secțiuni. Acest mod este ignorat în sesiunea live."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "If you wish, you can"
msgstr "Dacă doriți, puteți"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image
msgid "Image"
msgstr "Imagine"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__allow_value_image
msgid "Images on answers"
msgstr "Imagini pe răspuns"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug1
msgid "Imhotep"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug6
msgid "Impractical"
msgstr "Nepractic"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__in_progress
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__in_progress
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "In Progress"
msgstr "În desfășurare"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q5
msgid "In the list below, select all the coniferous."
msgstr "În lista de mai jos, selectați toate coniferele."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q2
msgid "In which country did the bonsai technique develop ?"
msgstr "În ce țară s-a dezvoltat tehnica bonsaiului?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_scored_question
msgid ""
"Include this question as part of quiz scoring. Requires an answer and answer"
" score to be taken into account."
msgstr ""
"Includeți această întrebare ca parte a scorului de testare. Necesită un "
"răspuns și un scor de răspuns pentru a fi luat în considerare."

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Incorrect"
msgstr "Incorect"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug7
msgid "Ineffective"
msgstr "Ineficient"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr "Intrarea trebuie să fie un e-mail"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__invite_token
msgid "Invite token"
msgstr "Token Invitație"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__token
msgid "Invited people only"
msgstr "Numai persoane invitate"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "Este Editor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification
msgid "Is a Certification"
msgstr "Este o Certificare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__is_correct
msgid "Is a correct answer"
msgstr "Este un răspuns corect"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_page
msgid "Is a page?"
msgstr "Este o pagină?"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_session_answer
msgid "Is in a Session"
msgstr "Este intr-o Sesiune"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__is_session_answer
msgid "Is that user input part of a survey session or not."
msgstr ""
"Este această intrare a utilizatorului parte a unei sesiuni de sondaj sau nu."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q3
msgid "Is the wood of a coniferous hard or soft ?"
msgstr "Lemnul unei conifere este tare sau moale?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug4
msgid "Istanbul"
msgstr "Istanbul"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row2
msgid "It is easy to find the product that I want"
msgstr "Este ușor să găsesc produsul pe care îl doresc"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug3
msgid "Iznogoud"
msgstr "Iznogoud"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug1
msgid ""
"I’ve never really wanted to go to Japan. Simply because I don’t like eating "
"fish. And I know that’s very popular out there in Africa."
msgstr ""
"Nu mi-am dorit niciodată să merg în Japonia. Pur și simplu pentru că nu-mi "
"place să mănânc pește. Și știu că este foarte popular acolo în Africa."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug1
msgid "Japan"
msgstr "Japonia"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Join Session"
msgstr "Alătură-te Sesiunii"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug2
msgid "Kim Jong-hyun"
msgstr "Kim Jong-hyun"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug1
msgid "Kurt Cobain"
msgstr "Kurt Cobain"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__sequence
msgid "Label Sequence order"
msgstr "Secvență de ordonare"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__matrix_row_ids
msgid "Labels used for proposed choices: rows of matrix"
msgstr "Etichete utilizate pentru alegerile propuse: rânduri de matrice"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__suggested_answer_ids
msgid ""
"Labels used for proposed choices: simple choice, multiple choice and columns"
" of matrix"
msgstr ""
"Etichete utilizate pentru alegerile propuse: alegere simplă, alegere "
"multiplă și coloane de matrice"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__lang
msgid "Language"
msgstr "Limba"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_4
msgid "Large Desk"
msgstr "Birou mare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question_answer____last_update
#: model:ir.model.fields,field_description:survey.field_survey_survey____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed question/page"
msgstr "Ultima întrebare / pagină afișată"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr "Activități întârziate"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_layout
msgid "Layout"
msgstr "Aspect"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Leaderboard"
msgstr "Clasament"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_4
msgid "Legs"
msgstr "Picioare"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug2
msgid "Lemon Trees"
msgstr "Lămâi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_attempts_limited
msgid "Limited number of attempts"
msgstr "Număr limitat de încercări"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Live Session"
msgstr "Sesiune Live"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Live Sessions"
msgstr "Sesiuni Live"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_login_required
msgid "Login Required"
msgstr "Logare Obligatorie"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "Login required"
msgstr "Necesară autentificare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__template_id
msgid "Mail Template"
msgstr "Șablon de e-mail"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_main_attachment_id
msgid "Main Attachment"
msgstr "Atașament principal"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "Răspuns obligatoriu"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__matrix
msgid "Matrix"
msgstr "Matrice"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_row_ids
msgid "Matrix Rows"
msgstr "Rânduri Matrice"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr "Tip matrice"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_date
msgid "Max date cannot be smaller than min date!"
msgstr "Data maximă nu poate fi mai mică decât data minimă!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_datetime
msgid "Max datetime cannot be smaller than min datetime!"
msgstr "Data și ora maximă nu poate fi mai mică decât data și ora minimă!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_length
msgid "Max length cannot be smaller than min length!"
msgstr "Lungimea maximă nu poate fi mai mică decât lungimea minimă!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_float
msgid "Max value cannot be smaller than min value!"
msgstr "Valoarea maximă nu poate fi mai mică decât valoarea minimă!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr "Dată maximă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_datetime
msgid "Maximum Datetime"
msgstr "Data și ora maximă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr "Lungime Maximă Text"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr "Valoare Maximă"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "Maybe you were looking for"
msgstr "Poate căutai"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
msgid "Message Delivery error"
msgstr "Eroare livrare mesaj"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr "Dată minimă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_datetime
msgid "Minimum Datetime"
msgstr "Data și ora minimă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr "Lungime Minimă Text"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr "Valoare minimă"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Missed"
msgstr "Apel Ratat"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_blue
msgid "Modern Blue"
msgstr "Alabstru Modern"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_gold
msgid "Modern Gold"
msgstr "Auriu Modern"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_purple
msgid "Modern Purple"
msgstr "Violet Modern"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug3
msgid "Mooses"
msgstr "Elani"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug4
msgid "Mount Elbrus (Russia)"
msgstr "Muntele Elbrus (Rusia)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug3
msgid "Mount Etna (Italy - Sicily)"
msgstr "Muntele Etna (Italia - Sicilia)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug1
msgid "Mount Teide (Spain - Tenerife)"
msgstr "Muntele Teide (Spania - Tenerife)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug4
msgid "Mountain Pine"
msgstr "Pinul de munte"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__text_box
msgid "Multiple Lines Text Box"
msgstr "Casetă Text cu Linii Multiple"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr "Alegere multiplă cu răspunsuri multiple"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr "Alegere multiplă cu un singur răspuns"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__multiple_choice
msgid "Multiple choice: multiple answers allowed"
msgstr "Alegere multiplă: permise multiple răspusuri"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__simple_choice
msgid "Multiple choice: only one answer"
msgstr "Alegere multiplă: permis numai un răspuns"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__multiple
msgid "Multiple choices per row"
msgstr "Multiple variante pe rând"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Data limită a activității mele"

#. module: survey
#: model:gamification.badge,name:survey.vendor_certification_badge
msgid "MyCompany Vendor"
msgstr "Furnizorul MyCompany"

#. module: survey
#: model:survey.survey,title:survey.vendor_certification
msgid "MyCompany Vendor Certification"
msgstr "Certificarea furnizorului MyCompany"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "New"
msgstr "Nou(ă)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug3
msgid "New York"
msgstr "New York"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__new
msgid "New invite"
msgstr "Invitație nouă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Următoarea activitate din calendar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data limită pentru următoarea activitate"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
msgid "Next Activity Summary"
msgstr "Sumarul următoarei activități"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
msgid "Next Activity Type"
msgstr "Tip de activitate urmatoare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__nickname
msgid "Nickname"
msgstr "Poreclă"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug2
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_1
msgid "No"
msgstr "Nu"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "No attempts left."
msgstr "Nicio încercare rămasă."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "No question yet, come back later."
msgstr "Nu există încă întrebări, reveniți mai târziu."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No questions found"
msgstr "Nu au fost găsite întrebări"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__no_scoring
msgid "No scoring"
msgstr "Fără punctaj"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_question_answer_action
msgid "No survey labels found"
msgstr "Nu s-au găsit etichete de sondaj"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_user_input_line_action
msgid "No user input lines found"
msgstr "Nu s-au găsit linii de introducere a utilizatorului"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug2
msgid "No, it's to small for the human eye."
msgstr "Nu, este prea mic pentru ochiul uman."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid "Nobody has replied to your surveys yet"
msgstr "Nimeni nu a răspuns la sondajele dvs. încă"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug2
msgid "Norway Spruce"
msgstr "Molid norvegian"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug2
msgid "Not Good, Not Bad"
msgstr "Nici bun nici rău"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__new
msgid "Not started yet"
msgstr "Nepornit încă"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__number
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__numerical_box
msgid "Number"
msgstr "Număr"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__attempts_limit
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_limit
msgid "Number of attempts"
msgstr "Number of attempts"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__column_nb
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Number of columns"
msgstr "Număr de coloane"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_5
msgid "Number of drawers"
msgstr "Numărul de sertare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
msgid "Number of errors"
msgstr "Numărul de erori"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Număr de mesaje ce necesită intervenție"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread_counter
msgid "Number of unread messages"
msgstr "Număr de mesaje necitite"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__numerical_box
msgid "Numerical Value"
msgstr "Valoare numerică"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_numerical_box
msgid "Numerical answer"
msgstr "Valoare numerică"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "Occurrence"
msgstr "Apariție"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_5
msgid "Office Chair Black"
msgstr "Scaun de birou negru"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug1
msgid "Once a day"
msgstr "O dată pe zi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug1
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug3
msgid "Once a month"
msgstr "O dată pe lună"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug2
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug2
msgid "Once a week"
msgstr "O dată pe săptămână"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug4
msgid "Once a year"
msgstr "O dată pe an"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__simple
msgid "One choice per row"
msgstr "O variantă pe rând"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_question
msgid "One page per question"
msgstr "O pagină pe întrebare"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_section
msgid "One page per section"
msgstr "O pagină pe secțiune"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__one_page
msgid "One page with all the questions"
msgstr "O pagină cu toate întrebările"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Only survey users can manage sessions."
msgstr "Doar utilizatori unui sondaj pot gestiona sesiunile"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Open Session Manager"
msgstr "Deschidere Sesiune Administrator"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Limba de traducere opțională (cod ISO) pentru a selecta când se trimite un "
"e-mail. Dacă nu este setat, versiunea engleză va fi folosită. Aceasta ar "
"trebui să fie de obicei o expresie rezervată care oferă limba potrivită, de "
"exemplu {{ object.partner_id.lang }}."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__null_value
msgid "Optional value to use if the target field is empty"
msgstr ""
"Valoarea optionala care va fi folosita in cazul in care campul tinta este "
"gol"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Options"
msgstr "Opțiuni"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "Other (see comments)"
msgstr "Altele (vezi comentarii)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2
msgid "Our Company in a few questions ..."
msgstr "Compania noastră în câteva întrebări ..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__mail_server_id
msgid "Outgoing mail server"
msgstr "Server trimitere e-mail-uri"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Overall Performance"
msgstr "Performanța generală"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug5
msgid "Overpriced"
msgstr "Prea scump"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Page"
msgstr "Pagina"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
msgid "Pages"
msgstr "Pagini"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug4
msgid "Papyrus"
msgstr "Papirus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Partial"
msgstr "Parțial"

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Partially"
msgstr "Partial"

#. module: survey
#: model:mail.template,subject:survey.mail_template_user_input_invite
msgid "Participate to {{ object.survey_id.display_name }} survey"
msgstr "Participa la sondajul {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model:ir.ui.menu,name:survey.survey_menu_user_inputs
msgid "Participations"
msgstr "Participări"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partner"
msgstr "Partener"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
#, python-format
msgid "Passed"
msgstr "Trecut"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Pay attention to the host screen until the next question."
msgstr "Acordați atenție ecranului gazdă până la următoarea întrebare."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__percent
msgid "Percentage"
msgstr "Procentaj"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Performance by Section"
msgstr "Performanță după secțiune"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug3
msgid "Perhaps"
msgstr "Poate"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug2
msgid "Peter W. Higgs"
msgstr "Peter W. Higgs"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1_q1
msgid "Pick a subject"
msgstr "Alege un subiect"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Pie Graph"
msgstr "Grafic Pie"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug1
msgid "Pinaceae"
msgstr "Pinacee"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__copyvalue
msgid "Placeholder Expression"
msgstr "Expresie Substituenta"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Please enter at least one valid recipient."
msgstr "Vă rugăm să introduceți cel puțin un destinatar valid."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid ""
"Please make sure you have at least one question in your survey. You also "
"need at least one section if you chose the \"Page per section\" layout.<br/>"
msgstr ""
"Vă rugăm să vă asigurați că aveți cel puțin o întrebare în sondaj. De "
"asemenea, aveți nevoie de cel puțin o secțiune dacă ați ales Aspect „Pagina "
"pe secțiune”.<br/>"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3
msgid "Policies"
msgstr "Politici"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug1
msgid "Pomelos"
msgstr "Pomelo"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug8
msgid "Poor quality"
msgstr "Calitate slabă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__predefined_question_ids
msgid "Predefined Questions"
msgstr "Întrebări Predefinite"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Preview"
msgstr "Previzualizare"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2
msgid "Prices"
msgstr "Prețuri"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Print"
msgstr "Tipăriți"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1
msgid "Products"
msgstr "Produse"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__progression_mode
msgid "Progression Mode"
msgstr "Mod de progresie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question"
msgstr "Întrebare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__matrix_question_id
msgid "Question (as matrix row)"
msgstr "Întrebare (ca rânduri matrice)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_answer_count
msgid "Question Answers Count"
msgstr "Număr de răspunsuri la întrebări"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question Time Limit"
msgstr "Limită de timp pentru întrebări"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__question_time_limit_reached
msgid "Question Time Limit Reached"
msgstr "Limită de Timp pentru Întrebări Atinsă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_type
msgid "Question Type"
msgstr "Tip Întrebare"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_question_id
msgid ""
"Question containing the triggering answer to display the current question."
msgstr ""
"Întrebare care conține răspunsul declanșator pentru a afișa întrebarea "
"curentă."

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_question__question_ids
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
#: model:ir.ui.menu,name:survey.survey_menu_questions
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Questions"
msgstr "Întrebări"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_quiz
msgid "Quiz about our Company"
msgstr "Test despre compania noastră"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_success
msgid "Quizz Passed"
msgstr "Chestionar Trecut"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Quizz passed"
msgstr "Chestionar Trecut"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__random_questions_count
msgid "Random questions count"
msgstr "Nr. Întrebări aleatorii "

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__random
msgid "Randomized per section"
msgstr "Aleatorizat pe secțiune"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__ready
msgid "Ready"
msgstr "Pregătit"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__partner_ids
msgid "Recipients"
msgstr "Destinatari"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_count
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Registered"
msgstr "Înregistrat"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__render_model
msgid "Rendering Model"
msgstr "Model de redare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Reopen"
msgstr "Redeschidere"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_text
msgid "Resend Comment"
msgstr "Retrimtere Comentariu"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Resend Invitation"
msgstr "Retrimitere Invitație"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__resend
msgid "Resend invite"
msgstr "Retrimitere invitație"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_id
msgid "Responsible"
msgstr "Responsabil"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
msgid "Responsible User"
msgstr "Utilizator responsabil"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Results Overview"
msgstr "Prezentare Generală Rezultate"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Retry"
msgstr "Reîncearcă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating
msgid "Reward quick answers"
msgstr "Recompensare răspunsuri rapide"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Rewards for challenges"
msgstr "Recompense pentru provocări"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Right answer:"
msgstr "Răspuns corect:"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Right answers:"
msgstr "Răspunsuri corecte:"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__matrix_row_id
msgid "Row answer"
msgstr "Rând răspuns"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "Rând1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "Rând2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "Rând3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "Rânduri"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Eroare livrare SMS"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug4
msgid "Salicaceae"
msgstr "Salicaceae"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_email
msgid "Save as user email"
msgstr "Salvare ca email utilizator"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_nickname
msgid "Save as user nickname"
msgstr "Salvare ca poreclă utilizator"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug3
msgid "Sciences"
msgstr "Științe"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_score
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Score"
msgstr "Scor"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_percentage
msgid "Score (%)"
msgstr "Punctaj (%)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__answer_score
msgid "Score for this choice"
msgstr "Scor pentru această alegere"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_score
msgid "Score value for a correct answer to this question."
msgstr "Valoare punctaj pentru un răspuns corect la această întrebare."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_scored_question
msgid "Scored"
msgstr "Punctat"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_type
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Scoring"
msgstr "Punctare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scoring_type
msgid "Scoring Type"
msgstr "Tip Punctare"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers
msgid "Scoring with answers at the end"
msgstr "Punctare cu răspunsuri la final"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_without_answers
msgid "Scoring without answers at the end"
msgstr "Punctare fără răspunsuri la final"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
msgid "Search Label"
msgstr "Căutare Etichetă"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "Caută întrebare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Search Survey"
msgstr "Cauta Sondaj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "Search User input lines"
msgstr "Căutare linii introducere utilizator"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Section"
msgstr "Secțiune"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_and_page_ids
msgid "Sections and Questions"
msgstr "Secțiuni și întrebări"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "See results"
msgstr "Vezi rezultate"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_3
msgid "Select all the available customizations for our Customizable Desk"
msgstr ""
"Selectați toate personalizările disponibile pentru biroul nostru "
"personalizabil"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_2
msgid "Select all the existing products"
msgstr "Selectați toate produsele existente"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_2
msgid "Select all the products that sell for $100 or more"
msgstr "Selectați toate produsele care se vând cu 100 $ sau mai mult"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Selectati campul tinta din modelul documentului asociat.\n"
"Daca este un camp de relatie, veti putea sa selectati un camp tinta la destinatia relatiei."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q3
msgid "Select trees that made more than 20K sales this year"
msgstr "Selectați copaci care au realizat peste 20.000 de vânzări anul acesta"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__questions_selection
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_selection
msgid "Selection"
msgstr "Selecție"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send"
msgstr "Trimite"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_sequence
msgid "Sequence"
msgstr "Secvență"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_code
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Session Code"
msgstr "Cod Sesiune"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_link
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Session Link"
msgstr "Link Sesiune"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_state
msgid "Session State"
msgstr "Stare Sesiune"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_code_unique
msgid "Session code should be unique"
msgstr "Cod sesiune trebuie să fie unic"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug1
msgid "Shanghai"
msgstr "Shanghai"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Share"
msgstr "Partajează"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr "Afișare câmp comentarii"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_show_leaderboard
msgid "Show Session Leaderboard"
msgstr "Arată Clasament Sesiune"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Afișați toate înregistrările care au data următoarei acțiuni în trecut"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__char_box
msgid "Single Line Text Box"
msgstr "Casetă Text cu o Singură Linie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Skipped"
msgstr "Omis"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug2
msgid "Soft"
msgstr "Moale"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Some emails you just entered are incorrect: %s"
msgstr "Unele e-mailuri pe care tocmai le-ați introdus sunt incorecte: %s"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Sorry, no one answered this survey yet."
msgstr "Ne pare rău, nimeni nu a răspuns la acest sondaj încă."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Sorry, you have not been fast enough."
msgstr "Îmi pare rău, nu ai fost suficient de rapid."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug4
msgid "South America"
msgstr "America de Sud"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug4
msgid "South Korea"
msgstr "Coreea de Sud"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug3
msgid "Space stations"
msgstr "Stații spațiale"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug1
msgid "Spring"
msgstr "Primăvară"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1
msgid "Start"
msgstr "Start"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Certification"
msgstr "Start Certificare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Survey"
msgstr "Start sondaj"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__start_datetime
msgid "Start date and time"
msgstr "Data și ora de început"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr "Stare"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stare bazată pe activități\n"
"Întârziată: data scadentă este deja trecută\n"
"Astăzi: activității pentru astăzi\n"
"Planificate: activități viitoare."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row2
msgid "Strawberries"
msgstr "Căpșuni"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__sub_model_object_field
msgid "Sub-field"
msgstr "Sub-câmp"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__sub_object
msgid "Sub-model"
msgstr "Submodel"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__subject
msgid "Subject"
msgstr "Subiect"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Subject..."
msgstr "Subiect..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Submit"
msgstr "Trimite"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_count
msgid "Success"
msgstr "Succes"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_success_min
msgid "Success %"
msgstr "Success %"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_ratio
msgid "Success Ratio"
msgstr "Rată Succes"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Success rate:"
msgstr "Rată Succes"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_question_answer_action
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Suggested Values"
msgstr "Valori sugerate"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__suggested_answer_id
msgid "Suggested answer"
msgstr "Răspuns sugerat"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value
msgid "Suggested value"
msgstr "Valoare sugerată"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__suggestion
msgid "Suggestion"
msgstr "Sugestie"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug2
msgid "Summer"
msgstr "Vară"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Survey"
msgstr "Sondaj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_view_tree
msgid "Survey Answer Line"
msgstr "Linie Răspuns Sondaj"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_ids
msgid "Survey Ids"
msgstr "ID-uri Sondaj"

#. module: survey
#: model:ir.model,name:survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "Asistent Invitație Sondaj"

#. module: survey
#: model:ir.model,name:survey.model_survey_question_answer
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
msgid "Survey Label"
msgstr "Etichetă Sondaj"

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "Întrebare sondaj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Survey Time Limit"
msgstr "Limită Timp Sondaj"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_time_limit_reached
msgid "Survey Time Limit Reached"
msgstr "Limită Timp Sondaj Atinsă"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
msgid "Survey Title"
msgstr "Titlu Sondaj"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_start_url
msgid "Survey URL"
msgstr "URL Sondaj"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Răspuns Utilizator Sondaj"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr "Linie Răspuns Utilizator Sondaj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_tree
msgid "Survey User inputs"
msgstr "Răspunsuri utilizator sondaj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "Survey filter"
msgstr "Filtru Sondaj"

#. module: survey
#: model:ir.actions.server,name:survey.survey_action_server_clean_test_answers
msgid "Survey: Clean test answers"
msgstr "Sondaj: răspunsuri curate la test"

#. module: survey
#: model:mail.template,name:survey.mail_template_user_input_invite
msgid "Survey: Invite"
msgstr "Sondaj: Invitație"

#. module: survey
#: model:mail.template,name:survey.mail_template_certification
msgid "Survey: Send certification by email"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "Sondaje"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug3
msgid "Takaaki Kajita"
msgstr "Takaaki Kajita"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Test"
msgstr "Test"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Test Entries"
msgstr "Testare Intrări "

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
msgid "Test Entry"
msgstr "Testare Intrare"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__char_box
msgid "Text"
msgstr "Text"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_char_box
msgid "Text answer"
msgstr "Răspuns text"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Thank you!"
msgstr "Vă mulțumim!"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "The answer must be in the right type"
msgstr "Răspunsul trebuie să fie de tipul corect"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q6
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q6
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p5_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q3
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q4
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_4
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_5
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_4
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_5
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_6
#, python-format
msgid "The answer you entered is not valid."
msgstr "Răspunsul pe care l-ați introdus nu este valid."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_attempts_limit_check
msgid ""
"The attempts limit needs to be a positive number if the survey has a limited"
" number of attempts."
msgstr ""
"Limita încercărilor trebuie să fie un număr pozitiv dacă un sondaj are un "
"număr limitat de încercări."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_badge_uniq
msgid "The badge for each survey should be unique!"
msgstr "Insigna pentru fiecare sondaj ar trebui să fie unică!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row4
msgid "The checkout process is clear and secure"
msgstr "Procesul de plată este clar și sigur"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_id
msgid "The current question of the survey session."
msgstr "Întrebare Actuală Sesiune Sondaj"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "The date you selected is greater than the maximum date: "
msgstr "Data selectată este mai mare decât data maximă:"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "The date you selected is lower than the minimum date: "
msgstr "Data selectată este mai mică decât data minimă:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid ""
"The description will be displayed on the home page of the survey. You can "
"use this to give the purpose and guidelines to your candidates before they "
"start it."
msgstr ""
"Descrierea va fi afișată pe pagina principală de sondaj. Puteți utiliza "
"acest lucru pentru a oferi scopul și îndrumările candidaților dvs. înainte "
"de începerea acestuia."

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following customers have already received an invite"
msgstr "Următorii clienți au primit deja o invitație"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following emails have already received an invite"
msgstr "Următoarele e-mailuri au primit deja o invitație"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external signup in configuration."
msgstr ""
"Următorii destinatari nu au cont de utilizator: %s. Ar trebui să creați "
"conturi de utilizator pentru ei sau să permiteți înscrierea externă în "
"configurare."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row1
msgid "The new layout and design is fresh and up-to-date"
msgstr "Noul aspect și design sunt noi și actualizate"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "The page you were looking for could not be authorized."
msgstr "Pagina pe care ați căutat nu a putut fi autorizată."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_scoring_success_min_check
msgid "The percentage of success has to be defined between 0 and 100."
msgstr "Procentul de succes trebuie definit între 0 și 100."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_limited
msgid "The question is limited in time"
msgstr "Întrebarea este limitată în timp"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "The session will begin automatically when the host starts."
msgstr "Sesiunea va începe automat la începerea gazdei."

#. module: survey
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "The survey has already started."
msgstr "Sondaj început deja."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_time_limited
msgid "The survey is limited in time"
msgstr "Sondaj limitat în timp"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_start_time
msgid ""
"The time at which the current question has started, used to handle the timer"
" for attendees."
msgstr ""
"Momentul la care a început întrebarea curentă, folosit pentru a gestiona "
"cronometrul pentru participanți."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_time_limit_check
msgid ""
"The time limit needs to be a positive number if the survey is time limited."
msgstr ""
"Limita de timp trebuie să fie un număr pozitiv dacă un sondaj este limitat."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row3
msgid "The tool to compare the products is useful to make a choice"
msgstr ""
"Instrumentul de comparare a produselor este util pentru a face o alegere"

#. module: survey
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "The user has not succeeded the certification"
msgstr "Utilizatorul nu a reușit certificarea"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "There was an error during the validation of the survey."
msgstr "A apărut o eroare în timpul validării de sondaj."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__column_nb
msgid ""
"These options refer to col-xx-[12|6|4|3|2] classes in Bootstrap for "
"dropdown-based simple and multiple choice questions."
msgstr ""
"Aceste opțiuni se referă la clasele col-xx- [12 | 6 | 4 | 3 | 2] din "
"Bootstrap pentru întrebări cu alegere simplă și bazate pe dropdown."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This answer must be an email address"
msgstr "Acest răspuns trebuie să fie o adresă de e-mail"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "This answer must be an email address."
msgstr "Acest răspuns trebuie să fie o adresă de e-mail"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
msgid "This certificate is presented to"
msgstr "Acest certificat este prezentat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"This certificate is presented to\n"
"                                <br/>"
msgstr ""
"Acest certificat este prezentat\n"
"                                <br/>"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_code
msgid ""
"This code will be used by your attendees to reach your session. Feel free to"
" customize it however you like!"
msgstr ""
"Acest cod va fi utilizat de către participanții dvs. pentru a ajunge la "
"sesiunea dvs. Simte-te liber să-l personalizezi oricum îți place!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "This is a test survey."
msgstr "Acesta este un sondaj de testare."

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/static/src/js/survey_form.js:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a date"
msgstr "Aceasta nu este o întâlnire"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a number"
msgstr "Acesta nu este un număr"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__emails
msgid ""
"This list of emails of recipients will not be converted in contacts.        "
"Emails must be separated by commas, semicolons or newline."
msgstr ""
"Această listă de e-mailuri a destinatarilor nu va fi convertită în contacte."
"             E-mailurile trebuie separate prin virgule, virgule sau linie "
"nouă."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description_done
msgid "This message will be displayed when survey is completed"
msgstr "Acest mesaj va fi afișat la finalizare de sondaj"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "This question depends on another question's answer."
msgstr "Această întrebare depinde de răspunsul altei întrebări."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q6
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q6
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p5_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q3
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q4
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_4
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_5
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_4
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_5
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_6
#, python-format
msgid "This question requires an answer."
msgstr "Această întrebare necesită un răspuns."

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"This survey does not allow external people to participate. You should create"
" user accounts or update survey access mode accordingly."
msgstr ""
"Acest sondaj nu permite participanților externi. Ar trebui să creați conturi"
" de utilizator sau să actualizați modul de acces la sondaj în consecință."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "This survey is now closed. Thank you for your interest !"
msgstr "Acest sondaj este acum închis. Îți mulțumesc pentru interes !"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "This survey is open only to registered people. Please"
msgstr "Acest sondaj este deschis numai persoanelor înregistrate. Vă rog"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__time_limit
msgid "Time limit (minutes)"
msgstr "Limită de timp (minute)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__time_limit
msgid "Time limit (seconds)"
msgstr "Limită de timp (secunde)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__title
msgid "Title"
msgstr "Titlu"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid ""
"To take this survey, please close all other tabs on <strong class=\"text-"
"danger\"/>."
msgstr ""
"Pentru a participa la acest sondaj, vă rugăm să închideți toate celelalte "
"file de pe <strong class=\"text-danger\"/>."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr "Activitățile de astăzi"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug2
msgid "Tokyo"
msgstr "Tokyo"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_total
msgid "Total Score"
msgstr "Scor Total"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col4
msgid "Totally agree"
msgstr "În totalitate de acord"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col1
msgid "Totally disagree"
msgstr "Total dezacord"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4
msgid "Trees"
msgstr "Copaci"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_answer_id
msgid "Triggering Answer"
msgstr "Declanșare răspuns"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_question_id
msgid "Triggering Question"
msgstr "Declanșare întrebare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "Tip"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipul activității de excepție din înregistrare."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__suggested_answer_ids
msgid "Types of answers"
msgstr "Tipuri de răspuns"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug2
msgid "Ulmaceae"
msgstr "Ulmaceae"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"Nu se poate posta mesajul, vă rugăm să configurați adresa de e-mail a "
"expeditorului."

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Unanswered"
msgstr "Fără răspuns"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "Uncategorized"
msgstr "Neclasificat"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_2
msgid "Underpriced"
msgstr "Subevaluat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Unfortunately, you have failed the test."
msgstr "Din păcate, ați eșuat testul."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug3
msgid "Unique"
msgstr "Unic"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread
msgid "Unread Messages"
msgstr "Mesaje necitite"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contor mesaje necitite"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr "Activități viitoare"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid ""
"Use this field to add additional explanations about your question or to "
"illustrate it with pictures or a video"
msgstr ""
"Utilizați acest câmp pentru a adăuga explicații suplimentare despre "
"întrebarea dvs. sau pentru a ilustra cu imagini sau un videoclip"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__random_questions_count
msgid ""
"Used on randomized sections to take X random questions from all the "
"questions of that section."
msgstr ""
"Folosit pe secțiuni aleatorii pentru a prelua X întrebări aleatorii din "
"toate întrebările secțiunii respective."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug2
msgid "Useful"
msgstr "Util"

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr "Operator"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "User Choice"
msgstr "Alegere Utilizator"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "User Input"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "User Responses"
msgstr "Răspunsuri utilizator"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_form
msgid "User input line details"
msgstr "Detalii linie de intrare utilizator"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr "Răspunsuri utilizator"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr "Utilizatorii pot merge înapoi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_can_signup
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_signup
msgid "Users can signup"
msgstr "Utilizatorii se pot înscrie"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr "Validare intrare"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error message"
msgstr "Validare Mesaj Eroare"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug2
msgid "Vegetables"
msgstr "Legume"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_1
msgid "Very underpriced"
msgstr "Foarte ieftin"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug3
msgid "Vietnam"
msgstr "Vietnam"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"We have registered your answer! Please wait for the host to go to the next "
"question."
msgstr ""
"V-am înregistrat răspunsul! Vă rugăm să așteptați ca gazda să treacă la "
"următoarea întrebare."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
msgid "Website Messages"
msgstr "Mesaje Website"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
msgid "Website communication history"
msgstr "Istoric comunicare website"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_5
msgid ""
"What day and time do you think most customers are most likely to call "
"customer service (not rated)?"
msgstr ""
"În ce zi și oră credeți că majoritatea clienților sunt cei mai susceptibili "
"să apeleze serviciul clienți (fără rating)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_4
msgid ""
"What day to you think is best for us to start having an annual sale (not "
"rated)?"
msgstr ""
"Ce zi crezi că este cea mai bună pentru noi să începem să avem o vânzare "
"anuală (fără evaluare)?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q2
msgid "What do you think about our new eCommerce ?"
msgstr "Ce părere aveți despre noul nostru eCommerce?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_3
msgid "What do you think about our prices (not rated)?"
msgstr "Ce părere aveți despre prețurile noastre (fără evaluare)?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5_q1
msgid "What do you think about this survey ?"
msgstr "Ce părere aveți despre acest sondaj ?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q2
msgid "What is the biggest city in the world ?"
msgstr "Care este cel mai mare oraș din lume ?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q1
msgid "What is your email ?"
msgstr "Care este adresa ta de e-mail?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q2
msgid "What is your nickname ?"
msgstr "Care e porecla ta ?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q2
msgid "What is, approximately, the critical mass of plutonium-239 ?"
msgstr "Care este, aproximativ, masa critică de plutoniu-239 ?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Atunci cand un camp de relatie este selectat drept primul camp, acest camp "
"va permite sa selectati campul tinta din cadrul modelului documentului "
"destinatie (sub-model)."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Atunci cand un camp de relatie este selectat ca prim camp, acest camp arata "
"modelul documentului la care se refera relatia."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q1
msgid "When did Genghis Khan die ?"
msgstr "Când a murit Genghis Khan?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q2
msgid "When did precisely Marc Demo crop its first apple tree ?"
msgstr "Când a recoltat Marc Demo primul său măr?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q6
msgid "When do you harvest those fruits"
msgstr "Când recoltați acele fructe"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q1
msgid "When is Mitchell Admin born ?"
msgstr "Când s-a născut Mitchell Admin?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q2
msgid "When is your date of birth ?"
msgstr "Când este data nașterii?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q3
msgid "Where are you from ?"
msgstr "De unde sunteți ?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q1
msgid "Where do you live ?"
msgstr "Unde locuiți ?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_show_leaderboard
msgid ""
"Whether or not we want to show the attendees leaderboard for this survey."
msgstr ""
"Dacă dorim sau nu să arătăm clasamentul participanților pentru acest sondaj"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q1
msgid "Which Musician is not in the 27th Club ?"
msgstr "Care muzician nu face parte din clubul 27?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q1
msgid "Which category does a tomato belong to"
msgstr "La ce categorie aparține o roșie"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q3
msgid "Which is the highest volcano in Europe ?"
msgstr "Care este cel mai înalt vulcan din Europa ?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q1
msgid "Which of the following words would you use to describe our products ?"
msgstr ""
"Care dintre următoarele cuvinte ați folosi pentru a descrie produsele "
"noastre ?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q2
msgid "Which of the following would you use to pollinate"
msgstr "Pe care dintre următoarele le-ați folosi pentru a poleniza"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q2
msgid "Which painting/drawing was not made by Pablo Picasso ?"
msgstr "Ce pictură / desen nu a fost realizat de Pablo Picasso?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q3
msgid "Which quote is from Jean-Claude Van Damme"
msgstr "Care citat este de la Jean-Claude Van Damme"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1
msgid "Who are you ?"
msgstr "Cine ești tu ?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q2
msgid "Who is the architect of the Great Pyramid of Giza ?"
msgstr "Cine este arhitectul Marii Piramide din Giza?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q1
msgid ""
"Who received a Nobel prize in Physics for the discovery of neutrino "
"oscillations, which shows that neutrinos have mass ?"
msgstr ""
"Cine a primit un premiu Nobel pentru fizică pentru descoperirea oscilațiilor"
" neutrino, ceea ce arată că neutrinii au masă?"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_3
msgid "Width"
msgstr "Lățime"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug4
msgid "Willard S. Boyle"
msgstr "Willard S. Boyle"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug4
msgid "Winter"
msgstr "Iarnă"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"AAAA-LL-ZZ\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"AAAA-LL-ZZ oo:mm:ss\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug1
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_2
msgid "Yes"
msgstr "Da"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug1
msgid "Yes, that's the only thing a human eye can see."
msgstr "Da, acesta este singurul lucru pe care un ochi uman îl poate vedea."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid ""
"You can create surveys for different purposes: customer opinion, services "
"feedback, recruitment interviews, employee's periodical evaluations, "
"marketing campaigns, etc."
msgstr ""
"Puteți crea sondaj în diferite scopuri: opinia clienților, feedback-ul "
"serviciilor, interviuri de recrutare, evaluări periodice ale angajaților, "
"campanii de marketing etc."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_certification_check
msgid ""
"You can only create certifications for surveys that have a scoring "
"mechanism."
msgstr ""
"Puteți crea certificări numai pentru sondaj care are un mecanism de notare."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey has no sections."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey only contains empty sections."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send an invitation for a survey that has no questions."
msgstr "Nu puteți trimite o invitație pentru un sondaj care nu are întrebări."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send invitations for closed surveys."
msgstr "Nu puteți trimite invitații pentru sondaj închis."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You received the badge"
msgstr "Ai primit insigna"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You scored"
msgstr "Ai punctat"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5
msgid "Your feeling"
msgstr "Your feeling"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "answered"
msgstr "răspuns"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "attempts"
msgstr "încercări"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. No one can solve challenges like you do"
msgstr "ex. Nimeni nu poate rezolva provocările ca tine"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. Problem Solver"
msgstr "ex. Rezolvator de probleme"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "e.g. Satisfaction Survey"
msgstr "de exemplu. Sondaj de satisfacție"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"for successfully completing\n"
"                                <br/>"
msgstr ""
"pentru a fi completat cu succes\n"
"                                <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "log in"
msgstr "Autentificare"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "minutes"
msgstr "minute"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "of"
msgstr "de"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "of achievement"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "or press CTRL+Enter"
msgstr "sau apăsați CTRL+Enter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "or press Enter"
msgstr "sau apăsați Enter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "pages"
msgstr "pagini"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "review your answers"
msgstr "consultați-vă răspunsurile"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "survey expired"
msgstr "Sondaj Expirat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "survey is empty"
msgstr "Sondajul este gol"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "this page"
msgstr "această pagină"
