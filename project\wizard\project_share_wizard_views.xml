<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="project_share_wizard_view_form" model="ir.ui.view">
        <field name="name">project.share.wizard.view.form</field>
        <field name="model">project.share.wizard</field>
        <field name="arch" type="xml">
            <form string="Share Project">
                <field name="res_model" invisible="1"/>
                <field name="res_id" invisible="1"/>
                <field name="display_access_mode" invisible="1" />
                <p class="alert alert-warning" attrs="{'invisible': [('access_warning', '=', '')]}" role="alert"><field name="access_warning"/></p>
                <group attrs="{'invisible': [('display_access_mode', '=', False)]}">
                    <field class="flex-row" name="access_mode" widget="radio"/>
                </group>
                <group name="share_link" attrs="{'invisible': [('access_mode', '=', 'edit')]}">
                    <field name="share_link" widget="CopyClipboardChar" options="{'string': 'Copy Link'}"/>
                </group>
                <group>
                    <div class="o_td_label">
                        <label for="partner_ids" string="Invite People" attrs="{'invisible': [('access_mode', '=', 'read')]}"/>
                        <label for="partner_ids" attrs="{'invisible': [('access_mode', '=', 'edit')]}"/>
                    </div>
                    <field name="partner_ids" widget="many2many_tags_email" placeholder="Add contacts to share the project..." nolabel="1"/>
                </group>
                <group>
                    <field name="note" placeholder="Add a note"/>
                </group>
                <footer>
                    <button string="Send" name="action_send_mail" attrs="{'invisible': [('access_warning', '!=', '')]}" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-default" special="cancel" data-hotkey="z" />
                </footer>
            </form>
        </field>
    </record>

    <record id="project_share_wizard_action" model="ir.actions.act_window">
        <field name="name">Share Project</field>
        <field name="res_model">project.share.wizard</field>
        <field name="binding_model_id" ref="model_project_project"/>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

</odoo>
