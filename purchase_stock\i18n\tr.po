# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_stock
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>ILMAZ <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>zle<PERSON>alay <<EMAIL>>, 2021
# Oz<PERSON>ik<PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Ediz Duman <<EMAIL>>, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "% On-Time Delivery"
msgstr "% Zamanında Teslim"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#: code:addons/purchase_stock/models/stock_rule.py:0
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr "+ %d gün(ler)"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid ""
"<span attrs=\"{'invisible': [('on_time_rate', '&gt;=', 0)]}\">No On-time "
"Delivery Data</span>"
msgstr ""
"<span attrs=\"{'invisible': [('on_time_rate', '&gt;=', 0)]}\">Zamanında "
"Teslim Verisi Yok</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\"/>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_text\">On-time Rate</span>"
msgstr "<span class=\"o_stat_text\">Zaman-Oranı</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">Satınalma</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "<span class=\"o_stat_value\">%</span>"
msgstr "<span class=\"o_stat_value\">%</span>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Teslimat Koşulu:</strong>"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase_stock.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Sevkiyat adresi:</strong>"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Ürün konfigürasyonuna göre, alınan miktar otomatik olarak mekanizma ile hesaplanabilir:\n"
" Manuel: miktar hatta manuel olarak ayarlanır\n"
"- Stok Hareketleri: Miktar teyit edilen toplamalardan gelir\n"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_rule__action
msgid "Action"
msgstr "Aksiyon"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_report__avg_receipt_delay
msgid ""
"Amount of time between expected and effective receipt date. Due to a hack "
"needed to calculate this,               every record will show the same "
"average value, therefore only use this as an aggregated value with "
"group_operator=avg"
msgstr ""
"Beklenen ve geçerli giriş tarihi arasındaki süre. Bunu hesaplamak için "
"gereken bir hack nedeniyle, her kayıt aynı ortalama değeri gösterecektir, bu"
" nedenle bunu yalnızca group_operator=avg ile toplu bir değer olarak "
"kullanın."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__avg_receipt_delay
msgid "Average Receipt Delay"
msgstr "Ortalama Makbuz Gecikmesi"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock.py:0
#: model:ir.model.fields.selection,name:purchase_stock.selection__stock_rule__action__buy
#: model:stock.location.route,name:purchase_stock.route_warehouse0_buy
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_report_stock_rule
#, python-format
msgid "Buy"
msgstr "Satınal"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_pull_id
msgid "Buy rule"
msgstr "Alım Kuralı"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "Buy to Resupply"
msgstr "İkmal için Satınalın "

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__effective_date
msgid "Completion date of the first receipt order."
msgstr "İlk makbuz siparişinin tamamlanma tarihi."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "Corresponding receipt not found."
msgstr "Karşılık gelen makbuz bulunamadı."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__created_purchase_line_id
msgid "Created Purchase Order Line"
msgstr "Oluşturulan Satın Alma Sipariş Satırları"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__product_description_variants
msgid "Custom Description"
msgstr "Özel Açıklama"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,help:purchase_stock.field_res_config_settings__days_to_purchase
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Days needed to confirm a PO, define when a PO should be validated"
msgstr ""
"Bir PO'yu onaylamak için gereken gün sayısı, bir PO'nun ne zaman "
"doğrulanması gerektiğini tanımlayın "

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:purchase_stock.field_res_company__days_to_purchase
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__days_to_purchase
#, python-format
msgid "Days to Purchase"
msgstr "Satınalma Günler"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_type_id
msgid "Deliver To"
msgstr "Teslim Alacak"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,help:purchase_stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"Kurulu modüllere dayalı olarak, bu, ürünün rotasını tanımlamanızı sağlar: "
"satın alınacak, üretilecek, sipariş üzerine yenilenecek vb."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Destination Location Type"
msgstr "Hedef Konum Türü"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Documentation"
msgstr "Belgeleme"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_dest_ids
msgid "Downstream Moves"
msgstr "Aşağı Yönlü Hareketler"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "Transit Satış"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__effective_date
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__effective_date
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__date
msgid "Effective Date"
msgstr "Geçerlilik Tarihi"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "Effective Date Last Year"
msgstr "Geçerlilik Tarihi Son Yıl"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "Satınalma siparişlerinde istisnalar oluştu:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Exception(s):"
msgstr "İstisna(lar):"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"For the product %s, the warehouse of the operation type (%s) is inconsistent"
" with the location (%s) of the reordering rule (%s). Change the operation "
"type or cancel the request for quotation."
msgstr ""
"%s Ürünü için, işlem türünün (%s) deposu, yeniden sıralama kuralının (%s) "
"konumu (%s) ile tutarsız. İşlem türünü değiştirin veya teklif talebini iptal"
" edin."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__forecasted_issue
msgid "Forecasted Issue"
msgstr "Öngörülen Sayı"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Generate the draft vendor bill."
msgstr "Tedarikçi taslak faturası oluşturun. "

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Go back to the purchase order to generate the vendor bill."
msgstr "Tedarikçi faturasını oluşturmak için satınalma siparişine geri dönün."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__id
msgid "ID"
msgstr "ID"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoming_picking_count
msgid "Incoming Shipment count"
msgstr "Gelen Sevkiyat Sayısı"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Incoming Shipments"
msgstr "Gelen Sevkiyatlar"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__incoterm_id
msgid "Incoterm"
msgstr "Teslimat Koşulu"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Uluslararası Ticari Terimler, uluslararası işlemlerde kullanılan önceden "
"tanımlanmış ticari  dizinlerdir."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__is_shipped
msgid "Is Shipped"
msgstr "Sevk Edildi"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_config_settings__is_installed_sale
msgid "Is the Sale Module Installed"
msgstr "Satış modülü yüklü mü"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_account_move
msgid "Journal Entry"
msgstr "Yevmiye Kaydı"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__last_purchase_date
msgid "Last Purchase"
msgstr "Son Satınalma"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Logistics"
msgstr "Lojistik"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Lot/Seri"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Manual actions may be needed."
msgstr "Manuel işlem gerekebilir."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Tedarikçi teslimat süreleri için hata payı. Sistem, tekrar sipariş edilen "
"ürünler için Satın Alma Siparişleri olşturduğunda, beklenmedik tedarikçi "
"gecikmelerini günler önceden planlayacak."

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Min. Envanter Kuralı"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "Move forward expected request creation date by"
msgstr "Beklenen istek oluşturma tarihini şu tarihe kadar ilerlet:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "Next transfer(s) impacted:"
msgstr "Etkilenen sonraki transferler:"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "No data yet"
msgstr "Henüz veri yok"

#. module: purchase_stock
#: code:addons/purchase_stock/models/account_invoice.py:0
#, python-format
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr ""
"Odoo, anglo sakson girişlerini oluşturamıyor.%s toplam değerlemesi "
"sıfırdır. "

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_view_graph
msgid "On-Time Delivery"
msgstr "Zamanında Teslim"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__on_time_rate
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__on_time_rate
msgid "On-Time Delivery Rate"
msgstr "Zamanında Teslim Oranı"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_on_time
msgid "On-Time Quantity"
msgstr "Zamanında Miktar"

#. module: purchase_stock
#: model:ir.actions.act_window,name:purchase_stock.action_purchase_vendor_delay_report
#: model_terms:ir.ui.view,arch_db:purchase_stock.vendor_delay_report_filter
msgid "On-time Delivery"
msgstr "Zamanında Teslim"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_partner_view_purchase_buttons_inherit
msgid "On-time Rate"
msgstr "Zaman Oranı"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__orderpoint_id
msgid "Orderpoint"
msgstr "Sipariş Noktası"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_partner__on_time_rate
#: model:ir.model.fields,help:purchase_stock.field_res_users__on_time_rate
msgid ""
"Over the past 12 months; the number of products received on time divided by "
"the number of ordered products."
msgstr ""
"Son 12 ayda; zamanında alınan ürün sayısı, sipariş edilen ürün sayısına "
"bölünür. "

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Process all the receipt quantities."
msgstr "Tüm makbuz miktarlarını işleyin."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__group_id
msgid "Procurement Group"
msgstr "Tedarik Grubu"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_product
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__product_id
msgid "Product"
msgstr "Ürün"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__category_id
msgid "Product Category"
msgstr "Ürün Kategorisi"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__supplier_id
msgid "Product Supplier"
msgstr "Ürün Tedarikçisi"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_template
msgid "Product Template"
msgstr "Ürün Şablonu"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__propagate_cancel
msgid "Propagate cancellation"
msgstr "Yayılım iptali"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_res_partner__purchase_line_ids
#: model:ir.model.fields,field_description:purchase_stock.field_res_users__purchase_line_ids
msgid "Purchase Lines"
msgstr "Satınalma Satırları"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order
msgid "Purchase Order"
msgstr "Satınalma Siparişi"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__purchase_order_line_ids
#: model:ir.model.fields,field_description:purchase_stock.field_stock_move__purchase_line_id
msgid "Purchase Order Line"
msgstr "Satınalma Sipariş Satırı"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_picking__purchase_id
#: model:ir.model.fields,field_description:purchase_stock.field_stock_production_lot__purchase_order_ids
#: model_terms:ir.ui.view,arch_db:purchase_stock.stock_production_lot_view_form
msgid "Purchase Orders"
msgstr "Satınalma Siparişleri"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_purchase_report
msgid "Purchase Report"
msgstr "Satınalma Raporu"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "Purchase Security Lead Time"
msgstr "Satınalma Güvenliği Tedarik Süresi"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_production_lot__purchase_order_count
msgid "Purchase order count"
msgstr "Satınalma siparişi sayısı"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receipt"
msgstr "Alım"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_view_form_inherit
msgid "Receive Products"
msgstr "Ürün Kabulleri"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Receive the ordered products."
msgstr "Sipariş edilen ürünleri alın."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr "Alınan Miktar Yöntemi"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order__picking_ids
msgid "Receptions"
msgstr "Mal Kabul"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid "Request your vendors to deliver to your customers"
msgstr "Tedarikçiler müşterilerinize sizin adınıza direk teslimat yaparlar"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_report_product_product_replenishment
msgid "Requests for quotation"
msgstr "Teklif Talepleri"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_order_line__move_ids
msgid "Reservation"
msgstr "Rezervasyon"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Ters Transfer"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:purchase_stock.field_product_template__route_ids
msgid "Routes"
msgstr "Rotalar"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid ""
"Schedule automatically generated request for quotations earlier to avoid "
"delays"
msgstr ""
"Gecikmeleri önlemek için otomatik olarak oluşturulan teklif talebini daha "
"erken planlayın"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.product_supplierinfo_replenishment_tree_view
msgid "Set as Supplier"
msgstr "Tedarikçi Olarak Ayarla"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_product_supplierinfo__show_set_supplier_button
msgid "Show Set Supplier Button"
msgstr "Tedarikçi Ayarla Düğmesini Göster"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__show_supplier
msgid "Show supplier column"
msgstr "Tedarikçi sütununu göster"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_move
msgid "Stock Move"
msgstr "Stok Hareketi"

#. module: purchase_stock
#: model:ir.model.fields.selection,name:purchase_stock.selection__purchase_order_line__qty_received_method__stock_moves
#: model_terms:ir.ui.view,arch_db:purchase_stock.purchase_order_line_view_form_inherit
msgid "Stock Moves"
msgstr "Stok Hareketleri"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "Stok İkmal Raporu"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Stok Kuralı"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Stok kuralı raporu"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "Stok tedarikçisi ikmal verisi"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Tedarikçi Fiyat Listesi"

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_replenishment_info__supplierinfo_ids
msgid "Supplierinfo"
msgstr "Tedarikçi bilgisi"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__default_location_dest_id_usage
msgid "Technical field used to display the Drop Ship Address"
msgstr "Teslimat Bırakma Adresini görüntülemek için kullanılan teknik alan"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_product_product__purchase_order_line_ids
msgid "Technical: used to compute quantities."
msgstr "Teknik: miktarları hesaplamak için kullanılır."

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock.py:0
#, python-format
msgid "The following replenishment order has been generated"
msgstr "Aşağıdaki yenileme siparişi oluşturuldu"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"The quantities on your purchase order indicate less than billed. You should "
"ask for a refund."
msgstr ""
"Satınalma siparişinizdeki miktarlar faturalanandan daha azını gösteriyor. "
"Geri ödeme talebinde bulunmalısınız. "

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid ""
"There is no matching vendor price to generate the purchase order for product"
" %s (no vendor defined, minimum quantity not reached, dates not valid, ...)."
" Go on the product form and complete the list of vendors."
msgstr ""
" %s ürünü için satınalma siparişi oluşturmak için uygun tedarikçi fiyatı yok"
" (tedarikçi tanımlanmamış, minimum miktara ulaşılmadı, tarihler geçerli "
"değil, ...). Ürün formuna gidin ve tedarikçi listesini tamamlayın."

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_purchase
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"Bu, tedarikçilerinizden müşterilerinize teslim etmelerini istemek için "
"ürünlere uygulanacak bir dropshipping rotası ekler. Dropship ürünü, satış "
"siparişi onaylandıktan sonra teklif için bir satınalma talebi "
"oluşturacaktır. Bu isteğe bağlı bir akış. Talep edilen teslimat adresi, "
"deponuz değil müşteri teslimat adresi olacaktır."

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_purchase_order__picking_type_id
msgid "This will determine operation type of incoming shipment"
msgstr "Bu, gelen teslimatın operasyon türünü belirleyecektir."

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"Those dates couldn’t be modified accordingly on the receipt %s which had "
"already been validated."
msgstr ""
"Bu tarihler, halihazırda doğrulanmış olan %s makbuzunda uygun şekilde "
"değiştirilemedi."

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "Those dates have been updated accordingly on the receipt %s."
msgstr "Bu tarihler, %s makbuzunda buna göre güncellenmiştir."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__qty_total
msgid "Total Quantity"
msgstr "Toplam Miktar"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_picking
msgid "Transfer"
msgstr "Aktarım"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"Unable to cancel purchase order %s as some receptions have already been "
"done."
msgstr ""
"Bazı alımlar çoktan yapıldığı için %s satın alma siparişini iptal "
"edemezsiniz"

#. module: purchase_stock
#. openerp-web
#: code:addons/purchase_stock/static/src/js/tours/purchase_stock.js:0
#, python-format
msgid "Validate the receipt of all ordered products."
msgstr "Sipariş edilen tüm ürünlerin alındığını doğrulayın."

#. module: purchase_stock
#: model:ir.model.fields,field_description:purchase_stock.field_stock_warehouse_orderpoint__vendor_id
#: model:ir.model.fields,field_description:purchase_stock.field_vendor_delay_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_stock.view_warehouse_orderpoint_tree_editable_inherited_mrp
msgid "Vendor"
msgstr "Tedarikçi"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_vendor_delay_report
msgid "Vendor Delay Report"
msgstr "Tedarikçi Gecikme Raporu"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid "Vendor Lead Time"
msgstr "Tedarikçi Teslim Süresi"

#. module: purchase_stock
#: model_terms:ir.actions.act_window,help:purchase_stock.action_purchase_vendor_delay_report
msgid "Vendor On-time Delivery analysis"
msgstr "Tedarikçi Zamanında Teslim Analizi"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse_orderpoint__vendor_id
msgid "Vendor of this product"
msgstr "Bu ürününün tedarikçisi"

#. module: purchase_stock
#: model:ir.model,name:purchase_stock.model_stock_warehouse
#: model:ir.model.fields,field_description:purchase_stock.field_purchase_report__picking_type_id
msgid "Warehouse"
msgstr "Depo"

#. module: purchase_stock
#: model:ir.model.fields,help:purchase_stock.field_stock_warehouse__buy_to_resupply
msgid "When products are bought, they can be delivered to this warehouse"
msgstr "Ürünler satın alındığında bu depoya sevk edilebilir"

#. module: purchase_stock
#: code:addons/purchase_stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> a request for quotation is "
"created to fulfill the need.<br/>Note: This rule will be used in combination"
" with the rules<br/>of the reception route(s)"
msgstr ""
"Ürünlere ihtiyaç duyulduğunda <b>%s</b>, <br/> ihtiyacı karşılamak için bir "
"teklif talebi oluşturulur.<br/>Not: Bu kural, kurallarla birlikte "
"kullanılacaktır.<br/>alım rota(ları)"

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid ""
"You cannot decrease the ordered quantity below the received quantity.\n"
"Create a return first."
msgstr ""
"Sipariş edilen miktarı alınan miktarın altına indiremezsiniz. Önce bir dönüş"
" oluşturun."

#. module: purchase_stock
#: code:addons/purchase_stock/models/purchase.py:0
#, python-format
msgid "You must set a Vendor Location for this partner %s"
msgstr "Bu iş ortağı %s için bir tedarikçi konumu belirlemelisiniz"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "gün"

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "of"
msgstr " "

#. module: purchase_stock
#: model_terms:ir.ui.view,arch_db:purchase_stock.exception_on_po
msgid "ordered instead of"
msgstr "yerine sipariş edildi"
