<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_vn" model="res.partner">
        <field name="name">VN Company</field>
        <field name="vat"></field>
        <field name="street">Lê A</field>
        <field name="city">Phường Bảo Vinh</field>
        <field name="country_id" ref="base.vn"/>
        <field name="state_id" ref="base.state_vn_VN-06"/>
        <field name="zip">76463</field>
        <field name="phone">+84 91 234 56 78</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.vnexample.com</field>
    </record>

    <record id="demo_company_vn" model="res.company">
        <field name="name">VN Company</field>
        <field name="partner_id" ref="partner_demo_company_vn"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_vn')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_vn.demo_company_vn'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_vn.vn_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_vn.demo_company_vn')"/>
    </function>
</odoo>
