# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_mail_group
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_mail_group
#: model_terms:ir.ui.view,arch_db:website_mail_group.mail_group_view_form
msgid "<span class=\"o_stat_text\">Go to <br/>Website</span>"
msgstr "<span class=\"o_stat_text\">Vai al <br/>sito web</span>"

#. module: website_mail_group
#: model_terms:ir.ui.view,arch_db:website_mail_group.s_group_options
msgid "Create a public discussion group in your backend"
msgstr ""
"Crea un gruppo di discussione pubblico nell'interfaccia amministrativa"

#. module: website_mail_group
#: model:ir.ui.menu,name:website_mail_group.mail_group_menu_website_root
msgid "Groups"
msgstr "Gruppi"

#. module: website_mail_group
#: model:ir.ui.menu,name:website_mail_group.mail_group_moderation_menu_website
msgid "List Moderation Rulings"
msgstr ""

#. module: website_mail_group
#: model:ir.model,name:website_mail_group.model_mail_group
msgid "Mail Group"
msgstr "Mail Group"

#. module: website_mail_group
#: model:ir.ui.menu,name:website_mail_group.mail_group_menu_website
msgid "Mailing List Groups"
msgstr ""

#. module: website_mail_group
#. openerp-web
#: code:addons/website_mail_group/static/src/snippets/s_group/options.js:0
#, python-format
msgid "Name"
msgstr "Nome"

#. module: website_mail_group
#. openerp-web
#: code:addons/website_mail_group/static/src/snippets/s_group/options.js:0
#, python-format
msgid "New Mail Group"
msgstr "Nuovo gruppo di posta"

#. module: website_mail_group
#. openerp-web
#: code:addons/website_mail_group/static/src/snippets/s_group/options.js:0
#: model_terms:ir.ui.view,arch_db:website_mail_group.s_group
#, python-format
msgid "Subscribe"
msgstr "Iscriviti"

#. module: website_mail_group
#. openerp-web
#: code:addons/website_mail_group/static/src/snippets/s_group/000.js:0
#, python-format
msgid "Unsubscribe"
msgstr "Annulla iscrizione"

#. module: website_mail_group
#: model_terms:ir.ui.view,arch_db:website_mail_group.s_group
msgid "your email..."
msgstr "E-mail..."
