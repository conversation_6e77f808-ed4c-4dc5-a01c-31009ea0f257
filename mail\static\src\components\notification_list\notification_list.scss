// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

 .o_NotificationList {
    display: flex;
    flex-flow: column;
    overflow: auto;

    &.o-empty {
        justify-content: center;
    }
}

.o_NotificationList_noConversation {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: map-get($spacers, 4) map-get($spacers, 2);
}

.o_NotificationList_separator {
    flex: 0 0 auto;
    width: map-get($sizes, 100);
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_NotificationList_separator {
    border-bottom: $border-width solid $border-color;
}

.o_NotificationList_noConversation {
    color: $text-muted;
}
