<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

<t t-name="PaymentScreenStatus" owl="1">
    <div t-if="props.paymentLines.length === 0" class="paymentlines-empty">
        <div class="total">
            <t t-esc="totalDueText" />
        </div>
        <div class="message">
            Please select a payment method.
        </div>
    </div>

    <div t-else="">
        <div class="payment-status-container">
            <div>
                <div class="payment-status-remaining">
                    <span class="label">Remaining</span>
                    <span class="amount"
                          t-att-class="{ highlight: currentOrder.get_due() > 0 }">
                        <t t-esc="remainingText" />
                    </span>
                </div>
                <div class="payment-status-total-due">
                    <span class="label">Total Due</span>
                    <span>
                        <t t-esc="totalDueText" />
                    </span>
                </div>
            </div>
            <div>
                <div class="payment-status-change">
                    <span class="label">Change</span>
                    <span class="amount"
                          t-att-class="{ highlight: currentOrder.get_change() > 0 }">
                        <t t-esc="changeText" />
                    </span>
                </div>
            </div>
        </div>
    </div>
</t>
</templates>
