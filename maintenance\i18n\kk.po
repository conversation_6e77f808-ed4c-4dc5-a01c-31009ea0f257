# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_equipment
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-09-30 09:25+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Kazakh (http://www.transifex.com/odoo/odoo-9/language/kk/)\n"
"Language: kk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_kanban
msgid "<b>Category:</b>"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_kanban
msgid "<b>Model Number:</b>"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_kanban
msgid "<b>Request to:</b>"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_kanban
msgid "<b>Serial Number:</b>"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,help:hr_equipment.field_hr_equipment_category_alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""

#. module: hr_equipment
#: model:hr.equipment,name:hr_equipment.hr_equipment_computer3
#: model:hr.equipment,name:hr_equipment.hr_equipment_computer5
msgid "Acer Laptop"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_active
msgid "Active"
msgstr "Белсенді"

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_alias_id
msgid "Alias"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_config_settings_alias_domain
msgid "Alias Domain"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_alias_name
msgid "Alias Name"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_alias_domain
msgid "Alias domain"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: hr_equipment
#: sql_constraint:hr.equipment:0
msgid "Another asset already exists with this serial number!"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_config_settings_view_form
msgid "Apply"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_equipment_id
msgid "Asset"
msgstr "Белсенді"

#. module: hr_equipment
#: model:ir.model,name:hr_equipment.model_hr_equipment_category
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_id_5764
msgid "Asset Category"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_name
msgid "Asset Name"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_tree
msgid "Assign To Employee"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Assigned"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_assign_date
msgid "Assigned Date"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_user_id
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Assigned to"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_department_id
msgid "Assigned to Department"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Assigned to Departments"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_employee_id
msgid "Assigned to Employee"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Assigned to Employees"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Assigned to Employees and Departments"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Available"
msgstr ""

#. module: hr_equipment
#: selection:hr.equipment.request,kanban_state:0
msgid "Blocked"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_form
msgid "Cancel"
msgstr "Бас тарту"

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_category_id
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Category"
msgstr "Санат"

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_name
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_category_view_search
msgid "Category Name"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.actions.act_window,help:hr_equipment.hr_equipment_action_from_category_form
msgid "Click to add a new Engine."
msgstr ""

#. module: hr_equipment
#: model_terms:ir.actions.act_window,help:hr_equipment.hr_equipment_category_action
msgid "Click to add a new equipment category."
msgstr ""

#. module: hr_equipment
#: model_terms:ir.actions.act_window,help:hr_equipment.hr_equipment_action
msgid "Click to add a new equipment."
msgstr ""

#. module: hr_equipment
#: model_terms:ir.actions.act_window,help:hr_equipment.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:hr_equipment.hr_equipment_request_action_link
msgid "Click to add a new maintenance request."
msgstr ""

#. module: hr_equipment
#: model_terms:ir.actions.act_window,help:hr_equipment.hr_equipment_stage_action
msgid "Click to add a stage in the maintenance request."
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_close_date
msgid "Close Date"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_color
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_color
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_color
msgid "Color Index"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_note
msgid "Comments"
msgstr ""

#. module: hr_equipment
#: model:hr.equipment.category,name:hr_equipment.hr_equipment_computer
msgid "Computers"
msgstr ""

#. module: hr_equipment
#: model:ir.ui.menu,name:hr_equipment.menu_equipment_configuration
msgid "Configuration"
msgstr "Баптау"

#. module: hr_equipment
#: model:ir.actions.act_window,name:hr_equipment.action_hr_equipment_configuration
msgid "Configure Equipments"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_config_settings_view_form
msgid "Configure Equipments Settings"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_cost
msgid "Cost"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_create_uid
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_config_settings_create_uid
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_create_uid
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_create_uid
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_stage_create_uid
msgid "Created by"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_create_date
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_config_settings_create_date
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_create_date
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_create_date
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_stage_create_date
msgid "Created on"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_maintenance_open_count
msgid "Current Maintenance"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_alias_defaults
msgid "Default Values"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_kanban
msgid "Delete"
msgstr ""

#. module: hr_equipment
#: selection:hr.equipment,equipment_assign_to:0
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_department_id
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_tree
msgid "Department"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_description
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_form
msgid "Description"
msgstr "Сипаттамасы"

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_display_name
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_config_settings_display_name
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_display_name
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_display_name
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_stage_display_name
msgid "Display Name"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_kanban
msgid "Edit..."
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_category_view_form
msgid "Email Alias"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,help:hr_equipment.field_hr_equipment_category_alias_id
msgid ""
"Email alias for this equipment category. New emails will automatically "
"create new maintenance request for this equipment category."
msgstr ""

#. module: hr_equipment
#: selection:hr.equipment,equipment_assign_to:0
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_employee_id
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_tree
msgid "Employee"
msgstr ""

#. module: hr_equipment
#: model:ir.model,name:hr_equipment.model_hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_equipment_count
msgid "Equipment"
msgstr ""

#. module: hr_equipment
#: model:mail.message.subtype,description:hr_equipment.mt_mat_assign
#: model:mail.message.subtype,name:hr_equipment.mt_cat_mat_assign
#: model:mail.message.subtype,name:hr_equipment.mt_mat_assign
msgid "Equipment Assigned"
msgstr ""

#. module: hr_equipment
#: model:ir.actions.act_window,name:hr_equipment.hr_equipment_category_action
#: model:ir.ui.menu,name:hr_equipment.menu_equipment_cat
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_category_view_form
msgid "Equipment Categories"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_form
msgid "Equipment Name"
msgstr ""

#. module: hr_equipment
#: model:ir.actions.act_window,name:hr_equipment.hr_equipment_action
#: model:ir.actions.act_window,name:hr_equipment.hr_equipment_action_from_category_form
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_equipment_ids
#: model:ir.ui.menu,name:hr_equipment.menu_equipment_title
#: model:ir.ui.menu,name:hr_equipment.menu_hr_equipment_form
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_form
msgid "Equipments"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_fold
msgid "Folded in Maintenance Pipe"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_stage_fold
msgid "Folded in Recruitment Pipe"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.actions.act_window,help:hr_equipment.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:hr_equipment.hr_equipment_request_action_link
msgid ""
"Follow the process of the request and communicate with the collaborator."
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Group by..."
msgstr ""

#. module: hr_equipment
#: model:hr.equipment,name:hr_equipment.hr_equipment_printer1
msgid "HP Inkjet printer"
msgstr ""

#. module: hr_equipment
#: model:hr.equipment,name:hr_equipment.hr_equipment_computer11
#: model:hr.equipment,name:hr_equipment.hr_equipment_computer9
msgid "HP Laptop"
msgstr ""

#. module: hr_equipment
#: selection:hr.equipment.request,priority:0
msgid "High"
msgstr "Жоғары"

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_id
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_config_settings_id
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_id
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_id
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_stage_id
msgid "ID"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,help:hr_equipment.field_hr_equipment_category_alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task "
"creation alias)"
msgstr ""

#. module: hr_equipment
#: selection:hr.equipment.request,kanban_state:0
#: model:hr.equipment.stage,name:hr_equipment.stage_1
msgid "In Progress"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_form
msgid "Internal Note ......."
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_kanban_state
msgid "Kanban State"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment___last_update
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category___last_update
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_config_settings___last_update
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request___last_update
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_stage___last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_write_uid
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_config_settings_write_uid
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_write_uid
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_stage_write_uid
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_write_date
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_config_settings_write_date
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_write_date
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_stage_write_date
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_location
msgid "Location"
msgstr ""

#. module: hr_equipment
#: selection:hr.equipment.request,priority:0
msgid "Low"
msgstr "Төмен"

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_maintenance_count
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_maintenance_count
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_form
msgid "Maintenance"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_form
msgid "Maintenance Request"
msgstr ""

#. module: hr_equipment
#: model:mail.message.subtype,name:hr_equipment.mt_cat_req_created
msgid "Maintenance Request Created"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_stage_view_tree
msgid "Maintenance Request Stage"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_stage_view_search
msgid "Maintenance Request Stages"
msgstr ""

#. module: hr_equipment
#: model:mail.message.subtype,description:hr_equipment.mt_req_created
msgid "Maintenance Request created"
msgstr ""

#. module: hr_equipment
#: model:ir.actions.act_window,name:hr_equipment.hr_equipment_request_action
#: model:ir.actions.act_window,name:hr_equipment.hr_equipment_request_action_from_equipment
#: model:ir.actions.act_window,name:hr_equipment.hr_equipment_request_action_link
#: model:ir.model,name:hr_equipment.model_hr_equipment_request
#: model:ir.ui.menu,name:hr_equipment.menu_m_request_form
msgid "Maintenance Requests"
msgstr ""

#. module: hr_equipment
#: model:ir.model,name:hr_equipment.model_hr_equipment_stage
msgid "Maintenance Stage"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_form
msgid "Maintenance Subject"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_maintenance_ids
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_maintenance_ids
msgid "Maintenance ids"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_model
msgid "Model"
msgstr "Түрі"

#. module: hr_equipment
#: model:hr.equipment.category,name:hr_equipment.hr_equipment_monitor
msgid "Monitors"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "My Equipments"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_search
msgid "My Requests"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_stage_name
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_form
msgid "Name"
msgstr "Атауы"

#. module: hr_equipment
#: model:hr.equipment.stage,name:hr_equipment.stage_0
msgid "New Request"
msgstr ""

#. module: hr_equipment
#: selection:hr.equipment.request,priority:0
msgid "Normal"
msgstr "Қәдімгі"

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_note
msgid "Note"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,help:hr_equipment.field_hr_equipment_category_alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_alias_user_id
msgid "Owner"
msgstr "Иесі"

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_alias_parent_model_id
msgid "Parent Model"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,help:hr_equipment.field_hr_equipment_category_alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not "
"necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: hr_equipment
#: model:hr.equipment.category,name:hr_equipment.hr_equipment_phone
msgid "Phones"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,help:hr_equipment.field_hr_equipment_category_alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following "
"channels\n"
msgstr ""

#. module: hr_equipment
#: model:hr.equipment.category,name:hr_equipment.hr_equipment_printer
msgid "Printers"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_priority
msgid "Priority"
msgstr "Маңыздылығы"

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_form
msgid "Product Information"
msgstr ""

#. module: hr_equipment
#: selection:hr.equipment.request,kanban_state:0
msgid "Ready for next stage"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_form
msgid "Repair Responsible"
msgstr ""

#. module: hr_equipment
#: model:hr.equipment.stage,name:hr_equipment.stage_3
msgid "Repaired"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_kanban
msgid "Request"
msgstr "Сұрақ"

#. module: hr_equipment
#: model:mail.message.subtype,name:hr_equipment.mt_req_created
msgid "Request Created"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_request_date
msgid "Request Date"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_stage_done
msgid "Request Done"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_config_settings_view_form
msgid "Requests Alias"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_form
msgid "Reset Request"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_category_user_id
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_category_view_search
msgid "Responsible"
msgstr ""

#. module: hr_equipment
#: model:hr.equipment,name:hr_equipment.hr_equipment_monitor1
#: model:hr.equipment,name:hr_equipment.hr_equipment_monitor4
#: model:hr.equipment,name:hr_equipment.hr_equipment_monitor6
msgid "Samsung Monitor 15\""
msgstr ""

#. module: hr_equipment
#: model:hr.equipment.stage,name:hr_equipment.stage_4
msgid "Scrap"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_scrap_date
msgid "Scrap Date"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Search"
msgstr "Іздеу"

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_form
msgid "Sender"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_stage_sequence
msgid "Sequence"
msgstr "Тізбек"

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_serial_no
msgid "Serial Number"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,help:hr_equipment.field_hr_equipment_request_active
msgid ""
"Set active to false to hide the maintenance request without deleting it."
msgstr ""

#. module: hr_equipment
#: model:ir.ui.menu,name:hr_equipment.menu_equipment_global_settings
msgid "Settings"
msgstr "Баптау"

#. module: hr_equipment
#: model:hr.equipment.category,name:hr_equipment.hr_equipment_software
msgid "Software"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_stage_id
msgid "Stage"
msgstr ""

#. module: hr_equipment
#: model:ir.actions.act_window,name:hr_equipment.hr_equipment_stage_action
#: model:ir.ui.menu,name:hr_equipment.menu_equipment_stage_configuration
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_search
msgid "Stages"
msgstr ""

#. module: hr_equipment
#: model:mail.message.subtype,name:hr_equipment.mt_req_status
msgid "Status Changed"
msgstr ""

#. module: hr_equipment
#: model:mail.message.subtype,description:hr_equipment.mt_req_status
msgid "Status changed"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_form
msgid "Subject"
msgstr "Нысаны"

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_request_name
msgid "Subjects"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_user_id
msgid "Technician"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,help:hr_equipment.field_hr_equipment_category_alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming "
"email that does not reply to an existing record will cause the creation of a "
"new record of this model (e.g. a Project Task)"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,help:hr_equipment.field_hr_equipment_category_alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,help:hr_equipment.field_hr_equipment_category_alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""

#. module: hr_equipment
#: model_terms:ir.actions.act_window,help:hr_equipment.hr_equipment_action
msgid ""
"This application helps you to track equipments used by employees.\n"
"                Create some laptop, printer or phone and link each equipment "
"to\n"
"                an employee or departement. You will manage allocations, "
"issues\n"
"                and maintenance of equipment."
msgstr ""

#. module: hr_equipment
#: model_terms:ir.actions.act_window,help:hr_equipment.hr_equipment_action_from_category_form
msgid ""
"This application helps you to track equipments used by employees.\n"
"                Create some laptop, printer or phone and link each equipment "
"to an employee or departement.\n"
"                You will manage allocations, issues and maintenance of "
"equipment."
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_config_settings_view_form
msgid "Track Equipments"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_kanban
msgid "Unassigned"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Under Maintenance"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Unread Messages"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_config_settings_equipment_alias_prefix
msgid "Use the following alias to report internal equipment issue"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_equipment_assign_to
msgid "Used By"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Users"
msgstr "Пайдаланушылар"

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_partner_id
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_view_search
msgid "Vendor"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_partner_ref
msgid "Vendor Reference"
msgstr ""

#. module: hr_equipment
#: selection:hr.equipment.request,priority:0
msgid "Very Low"
msgstr ""

#. module: hr_equipment
#: model:ir.model.fields,field_description:hr_equipment.field_hr_equipment_warranty
msgid "Warranty"
msgstr ""

#. module: hr_equipment
#: code:addons/hr_equipment/models/hr_equipment.py:71
#, python-format
msgid ""
"You cannot delete an equipment category containing equipments or maintenance "
"requests."
msgstr ""

#. module: hr_equipment
#: model:ir.model,name:hr_equipment.model_hr_equipment_config_settings
msgid "hr.equipment.config.settings"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_tree
msgid "maintenance Request"
msgstr ""

#. module: hr_equipment
#: model_terms:ir.ui.view,arch_db:hr_equipment.hr_equipment_request_view_search
msgid "maintenance Request Search"
msgstr ""
