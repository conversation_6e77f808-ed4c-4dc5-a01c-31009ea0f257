# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ar
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.3alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-03-30 18:37+0000\n"
"PO-Revision-Date: 2020-03-30 18:37+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "- Activities Start:"
msgstr "- Inicio actividades:"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "- CUIT:"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__01
msgid "01 - National Taxes"
msgstr "01 - Impuestos nacionales"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__02
msgid "02 - Provincial Taxes"
msgstr "02 - Impuestos provinciales"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__03
msgid "03 - Municipal Taxes"
msgstr "03 - Impuestos Municipales"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__04
msgid "04 - Internal Taxes"
msgstr "04 - Impuestos internos"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__06
msgid "06 - VAT perception"
msgstr "06 - Percepción de IVA"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__07
msgid "07 - IIBB perception"
msgstr "07 - Percepción de IIBB"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__08
msgid "08 - Municipal Taxes Perceptions"
msgstr "08 - Percepciones por Impuestos Municipales"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__09
msgid "09 - Other Perceptions"
msgstr "09 - Otras Percepciones"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__4
msgid "10.5%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__9
msgid "2,5%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__5
msgid "21%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__6
msgid "27%"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_tribute_afip_code__99
msgid "99 - Others"
msgstr "99 - Otros"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>CBU for payment: </strong>"
msgstr "<br/><strong>CBU de pago: </strong>"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Currency: </strong>"
msgstr "<br/><strong>Moneda: </strong>"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Exchange rate: </strong>"
msgstr "<br/><strong>Tipo de cambio: </strong>"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Incoterm:</strong>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Payment Terms: </strong>"
msgstr "<br/><strong>Plazos de Pago: </strong>"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Reference:</strong>"
msgstr "<br/><strong>Referencia:</strong>"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<br/><strong>Source:</strong>"
msgstr "<br/><strong>Origen:</strong>"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid ""
"<span "
"groups=\"account.group_show_line_subtotals_tax_included\">Amount</span>"
msgstr ""
"<span "
"groups=\"account.group_show_line_subtotals_tax_included\">Importe</span>"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<span>% VAT</span>"
msgstr "<span>% IVA</span>"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<span>NCM</span>"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>Customer: </strong>"
msgstr "<strong>Cliente: </strong>"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>Due Date: </strong>"
msgstr "<strong>Fecha Vencimiento: </strong>"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>Invoiced period: </strong>"
msgstr "<strong>Periodo facturado: </strong>"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "<strong>VAT Cond: </strong>"
msgstr "<strong>Cond. IVA: </strong>"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_concept
#: model:ir.model.fields,help:l10n_ar.field_account_move__l10n_ar_afip_concept
#: model:ir.model.fields,help:l10n_ar.field_account_payment__l10n_ar_afip_concept
msgid ""
"A concept is suggested regarding the type of the products on the invoice but"
" it is allowed to force a different type if required."
msgstr ""
"Un concepto es sugerido tomando en cuenta el tipo de productos en la "
"factura, pero es permitido forzar un concepto diferente si es requerido."

#. module: l10n_ar
#: model:ir.ui.menu,name:l10n_ar.menu_afip_config
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_move_form
msgid "AFIP"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_identification_type__l10n_ar_afip_code
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_afip_code
#: model:ir.model.fields,field_description:l10n_ar.field_res_currency__l10n_ar_afip_code
#: model:ir.model.fields,field_description:l10n_ar.field_uom_uom__l10n_ar_afip_code
msgid "AFIP Code"
msgstr "Código AFIP"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_concept
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_concept
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_concept
msgid "AFIP Concept"
msgstr "Concepto AFIP"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_afip_pos_partner_id
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_afip_pos_partner_id
msgid "AFIP POS Address"
msgstr "Dirección PdV AFIP"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_afip_pos_number
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_afip_pos_number
msgid "AFIP POS Number"
msgstr "Número PdV AFIP"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_afip_pos_system
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_afip_pos_system
msgid "AFIP POS System"
msgstr "Sistema PdV AFIP"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.base_view_partner_form
msgid "AFIP Responsibility"
msgstr "Responsabilidad AFIP"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_l10n_ar_afip_responsibility_type
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_afip_responsibility_type_id
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_afip_responsibility_type_form
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_afip_responsibility_type_tree
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_res_partner_filter
msgid "AFIP Responsibility Type"
msgstr "Tipo de Responsabilidad AFIP"

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_afip_responsibility_type
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position__l10n_ar_afip_responsibility_type_ids
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position_template__l10n_ar_afip_responsibility_type_ids
msgid "AFIP Responsibility Types"
msgstr "Tipos de Responsabilidad AFIP"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_service_end
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_service_end
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_service_end
msgid "AFIP Service End Date"
msgstr "Fecha Fin del servicio AFIP"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_service_start
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_afip_service_start
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_afip_service_start
msgid "AFIP Service Start Date"
msgstr "Fecha Inicio del servicio AFIP"

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_AN
msgid "AN"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_invoice_report_search_inherit
msgid "Account"
msgstr "Cuenta"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plantilla de Plan de Cuentas"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report__date
msgid "Accounting Date"
msgstr "Fecha contable"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_partner_property_form
msgid "Accounting Documents"
msgstr "Documentos contables"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__active
msgid "Active"
msgstr "Activo"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_afip_start_date
msgid "Activities Start"
msgstr "Inicio actividades"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_document_type_filter
msgid "Argentinean Documents"
msgstr "Documentos Argentinos"

#. module: l10n_ar
#: model:ir.ui.menu,name:l10n_ar.account_reports_ar_statements_menu
msgid "Argentinean Statements"
msgstr "Informes Argentinos"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Cuentas bancarias"

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_exento
#: model:product.template,name:l10n_ar.product_product_exento_product_template
msgid "Book \"Development in Odoo\" (VAT Exempt)"
msgstr "Libro \"Desarrollo en Odoo\" (IVA Exento)"

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CBA
msgid "CBA"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner_bank.py:0
#, python-format
msgid "CBU"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CCat
msgid "CCat"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CCor
msgid "CCor"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CCorr
msgid "CCorr"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CDI
msgid "CDI"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIBAR
msgid "CIBAR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CICha
msgid "CICha"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIChu
msgid "CIChu"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIER
msgid "CIER"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIF
msgid "CIF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIJ
msgid "CIJ"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CILP
msgid "CILP"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CILR
msgid "CILR"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIMen
msgid "CIMen"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIMis
msgid "CIMis"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIN
msgid "CIN"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIRN
msgid "CIRN"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIS
msgid "CIS"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISC
msgid "CISC"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISF
msgid "CISF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISJ
msgid "CISJ"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISL
msgid "CISL"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CISdE
msgid "CISdE"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CIT
msgid "CIT"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CITdF
msgid "CITdF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CPF
msgid "CPF"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CUIL
msgid "CUIL"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_cuit
msgid "CUIT"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"Can not change the POS number, you can only change the first number for "
"document type that you are creating in odoo"
msgstr ""
"No puede cambiar el número de POS, solo puede cambiar el el primer número "
"para el tipo de document que esta creando en odoo"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid ""
"Can not create chart of account until you configure your company AFIP "
"Responsibility and VAT."
msgstr ""
"No puede crear el plan de cuentas hasta que configure la Responsabilidad "
"AFIP y CUIT en su compañía."

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_CdM
msgid "CdM"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__code
msgid "Code"
msgstr "Código"

#. module: l10n_ar
#: model:ir.model.constraint,message:l10n_ar.constraint_l10n_ar_afip_responsibility_type_code
msgid "Code must be unique!"
msgstr "El código debe ser único!"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_company_requires_vat
msgid "Company Requires Vat?"
msgstr "Compañía requiere IVA?"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_vat
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_vat
msgid ""
"Computed field that returns VAT or nothing if this one is not set for the "
"partner"
msgstr ""
"Campo computado que retorna el CUIT o nada si este no esta definido en el "
"contacto"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_formatted_vat
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_formatted_vat
msgid ""
"Computed field that will convert the given VAT number to the format "
"{person_category:2}-{number:10}-{validation_number:1}"
msgstr ""
"Campo computado que convertirá el número CUIT al formato "
"{categoria_persona:2}-{número:10}-{número_validación:1}"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_company.py:0
#, python-format
msgid ""
"Could not change the AFIP Responsibility of this company because there are "
"already accounting entries."
msgstr ""
"No se puede cambiar la responsabilidad AFIP de esta compañía porque ya "
"existen movimientos contables."

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_country
msgid "Country"
msgstr "País"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__create_date
msgid "Created on"
msgstr "Creado el"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_res_currency
msgid "Currency"
msgstr "Moneda"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_line__l10n_ar_currency_rate
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__l10n_ar_currency_rate
#: model:ir.model.fields,field_description:l10n_ar.field_account_payment__l10n_ar_currency_rate
msgid "Currency Rate"
msgstr "Tasa de la moneda"

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_dni
msgid "DNI"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "Date:"
msgstr "Fecha:"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_line__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_account_move__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_account_payment__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_res_company__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_afip_responsibility_type_id
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_afip_responsibility_type_id
msgid ""
"Defined by AFIP to identify the type of responsibilities that a person or a "
"legal entity could have and that impacts in the type of operations and "
"requirements they need."
msgstr ""
"Definido por AFIP para identificar el tipo de responsabilidad que puede "
"tener una persona física o jurídica y que impacta en el tipo de operaciones "
"y requerimientos que dicha persona necesita."

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position_template__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_identification_type__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_res_currency__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner_bank__display_name
#: model:ir.model.fields,field_description:l10n_ar.field_uom_uom__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_document_type_filter
msgid "Document Letter"
msgstr "Letra del Documento"

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_document_type_argentina
#: model:ir.ui.menu,name:l10n_ar.menu_document_type_argentina
msgid "Document Types"
msgstr "Tipos de Documentos"

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_ET
msgid "ET"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Electronic Fiscal Bond - Online Invoice"
msgstr "Bonos Fiscales Electrónicos - Factura en Linea"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__2
#: model:ir.model.fields.selection,name:l10n_ar.selection__res_partner__l10n_ar_gross_income_type__exempt
msgid "Exempt"
msgstr "Exento"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Export Voucher - Billing Plus"
msgstr "Comprobantes de Exportacion - Facturador Plus"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Export Voucher - Online Invoice"
msgstr "Comprobantes de Exportacion - Factura en Linea"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Posición fiscal"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_formatted_vat
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_formatted_vat
msgid "Formatted VAT"
msgstr "CUIT Formateado"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_legal_entity_vat
msgid ""
"Generic VAT number defined by AFIP in order to recognize partners from this "
"country that are legal entity"
msgstr ""
"Número de CUIT genérico definido por AFIP para reconocer los contactos de "
"este país que son personas jurídicas"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_natural_vat
msgid ""
"Generic VAT number defined by AFIP in order to recognize partners from this "
"country that are natural persons"
msgstr ""
"Número de CUIT genérico definido por AFIP para reconocer los contactos de "
"este país que son personas fìsicas"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_other_vat
msgid ""
"Generic VAT number defined by AFIP in order to recognize partners from this "
"country that are not natural persons or legal entities"
msgstr ""
"Número de CUIT genérico definido por AFIP para reconocer los contactos de "
"este país que son personas físicas"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Go to Companies"
msgstr "Ir a Compañías"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Go to Journals"
msgstr "Ir a Diarios"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_gross_income_type
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_company_form
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_partner_property_form
msgid "Gross Income"
msgstr "Ingresos Brutos"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__l10n_ar_gross_income_number
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_gross_income_number
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_gross_income_number
msgid "Gross Income Number"
msgstr "Número de Ingresos Brutos"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_gross_income_type
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_gross_income_type
msgid "Gross Income Type"
msgstr "Tipo de Ingresos Brutos"

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_servicio_de_guarda
#: model:product.template,name:l10n_ar.product_product_servicio_de_guarda_product_template
msgid "Guard Service"
msgstr "Servicio de Guarda"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_chart_template__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position_template__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_move__id
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group__id
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__id
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type__id
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_identification_type__id
#: model:ir.model.fields,field_description:l10n_ar.field_res_company__id
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__id
#: model:ir.model.fields,field_description:l10n_ar.field_res_currency__id
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__id
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner_bank__id
#: model:ir.model.fields,field_description:l10n_ar.field_uom_uom__id
msgid "ID"
msgstr "ID (identificación)"

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_iibb_purchases_by_state_and_account_pivot
#: model:ir.ui.menu,name:l10n_ar.menu_iibb_purchases_by_state_and_account
msgid "IIBB - Purchases by jurisdiction"
msgstr "IIBB - Compras por Jurisdicción"

#. module: l10n_ar
#: model:ir.actions.act_window,name:l10n_ar.action_iibb_sales_by_state_and_account_pivot
#: model:ir.ui.menu,name:l10n_ar.menu_iibb_sales_by_state_and_account
msgid "IIBB - Sales by jurisdiction"
msgstr "IIBB - Ventas por Jurisdicción"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "IIBB:"
msgstr ""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_l10n_latam_identification_type
msgid "Identification Types"
msgstr "Tipos de Identificación"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_fiscal_position.py:0
#, python-format
msgid ""
"If use AFIP Responsibility then the country / zip codes will be not take "
"into account"
msgstr ""
"Si utiliza Responsabilidad AFIP entonces no se tomará en cuenta el pais / "
"códigos zip "

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_quote_despacho
#: model:product.template,name:l10n_ar.product_product_quote_despacho_product_template
msgid "Import Clearance"
msgstr "Despacho de importación"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_impuestos_internos
msgid "Internal Taxes"
msgstr "Impuestos Internos"

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "Invalid length for \"%s\""
msgstr "Longitud invalida para \"%s\""

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Estadísticas de facturas"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_journal
msgid "Journal"
msgstr "Diario"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_LC
msgid "LC"
msgstr ""

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_LE
msgid "LE"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_chart_template____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_fiscal_position_template____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_move____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_identification_type____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_res_company____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_res_country____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_res_currency____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner_bank____last_update
#: model:ir.model.fields,field_description:l10n_ar.field_uom_uom____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_l10n_latam_document_type
msgid "Latam Document Type"
msgstr "Tipo de Documento Latam"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_legal_entity_vat
msgid "Legal Entity VAT"
msgstr "CUIT Persona Jurídica"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type__l10n_ar_letter
msgid "Letters"
msgstr "Letras"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_l10n_latam_document_type__l10n_ar_letter
msgid ""
"Letters defined by the AFIP that can be used to identify the documents "
"presented to the government and that depends on the operation type, the "
"responsibility of both the issuer and the receptor of the document"
msgstr ""
"Letras definidas por la AFIP de pueden ser usadas para identificar los "
"documentos presentados al gobierno y que depende del tipo de operación, la "
"responsabilidad de ambos tanto del emisor como el receptor del documento"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_fiscal_position__l10n_ar_afip_responsibility_type_ids
msgid ""
"List of AFIP responsibilities where this fiscal position should be auto-"
"detected"
msgstr ""
"Lista de responsabilidades AFIP donde esta posición fiscal debe ser auto-"
"detectada"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__res_partner__l10n_ar_gross_income_type__local
msgid "Local"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "Logo"
msgstr ""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Missing Partner Configuration"
msgstr "Falta configuración del Contacto"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__res_partner__l10n_ar_gross_income_type__multilateral
msgid "Multilateral"
msgstr ""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_percepcion_municipal
msgid "Municipal Taxes Perceptions"
msgstr "Percepciones por Impuestos Municipales"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__name
msgid "Name"
msgstr "Nombre"

#. module: l10n_ar
#: model:ir.model.constraint,message:l10n_ar.constraint_l10n_ar_afip_responsibility_type_name
msgid "Name must be unique!"
msgstr "El nombre debe ser único!"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_natural_vat
msgid "Natural Person VAT"
msgstr "IVA Persona Física"

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "No VAT configured for partner [%i] %s"
msgstr "No hay CUIT configurado para el contacto [%i] %s"

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_cero
#: model:product.template,name:l10n_ar.product_product_cero_product_template
msgid "Non-industrialized animals and vegetables (VAT Zero)"
msgstr "Animales y vegetales no industrializados (IVA Cero)"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__0
msgid "Not Applicable"
msgstr "No Corresponde"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__l10n_latam_document_type__purchase_aliquots__not_zero
msgid "Not Zero"
msgstr "No Cero"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.custom_header
msgid "Nro:"
msgstr ""

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_company_form
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_partner_property_form
msgid "Number..."
msgstr "Número..."

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "On invoice id \"%s\" you must use VAT Not Applicable on every line."
msgstr "En la factura con id \"%s\" debe usar IVA NO Corresponde en cada línea."

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"On invoice id \"%s\" you must use VAT taxes different than VAT Not "
"Applicable."
msgstr ""
"En la factura con id \"%s\" debes usar impuestos de IVA diferentes de IVA No"
" Corresponde."

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Online Invoice"
msgstr "Factura en Linea"

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "Only numbers allowed for \"%s\""
msgstr "Sólo números son permitidos para \"%s\""

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_otras_percepciones
msgid "Other Perceptions"
msgstr "Otras Percepciones"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_special_purchase_document_type_ids
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_special_purchase_document_type_ids
msgid "Other Purchase Documents"
msgstr "Otros Documentos de Compra"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_country__l10n_ar_other_vat
msgid "Other VAT"
msgstr "CUIT Otros"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr "Página: <span class=\"page\"/> / <span class=\"topage\"/>"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__company_partner
msgid "Partner"
msgstr "Empresa"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "Please configure the AFIP Responsibility for \"%s\" in order to continue"
msgstr ""
"Por favor configure la Responsabilidad AFIP de \"%s\" para poder continuar"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Please define a valid AFIP POS number (5 digits max)"
msgstr "Por favor defina un número de PdV AFIP válido (5 dígitos máximo)"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Please define an AFIP POS number"
msgstr "Por favor defina un número de PdV AFIP"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Pre-printed Invoice"
msgstr "Factuweb (Imprenta)"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid "Product Coding - Online Voucher"
msgstr "Codificación de Productos - Factura en Línea"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unidad de medida del producto"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_percepcion_ganancias
msgid "Profit Perceptions"
msgstr "Percepción de Ganancias"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_latam_document_type__purchase_aliquots
msgid "Purchase Aliquots"
msgstr "Alícuotas de Compra"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_l10n_latam_document_type__purchase_aliquots
msgid ""
"Raise an error if a vendor bill is miss encoded. \"Not Zero\" means the VAT "
"taxes are required for the invoices related to this document type, and those"
" with \"Zero\" means that only \"VAT Not Applicable\" tax is allowed."
msgstr ""
"Lanze un error si la factura de vendedor esta mal cargada. \"No Cero\" "
"significa que los impuestos de IVA son requeridos en las facturas "
"relacionadas a este Tipo  de Documento, y aquellas con \"Cero\" significa "
"que solo  \"IVA No Corresponde\" esta permitido."

#. module: l10n_ar
#: model:ir.ui.menu,name:l10n_ar.menu_afip_responsibility_type
msgid "Responsibility Types"
msgstr "Tipos de Responsabilidad"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_l10n_ar_afip_responsibility_type__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_sequence_ids
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_sequence_ids
msgid "Sequences"
msgstr "Secuencias"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_move_form
msgid "Service Date"
msgstr "Fecha del Servicio"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_special_purchase_document_type_ids
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_special_purchase_document_type_ids
msgid ""
"Set here if this partner can issue other documents further than invoices, "
"credit notes and debit notes"
msgstr ""
"Defina acá si la empresa puede emitir otros documentos más alla que "
"facturas, notas de crédito o notas de débito"

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_Sigd
msgid "Sigd"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_invoice_report__l10n_ar_state_id
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_invoice_report_search_inherit
msgid "State"
msgstr "Estado"

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_tasa_estadistica
#: model:product.template,name:l10n_ar.product_product_tasa_estadistica_product_template
msgid "Statistics Rate"
msgstr "Tasa Estadística"

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_arancel
#: model:product.template,name:l10n_ar.product_product_arancel_product_template
msgid "Tariff"
msgstr "Arancel"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_tax_group
msgid "Tax Group"
msgstr "Grupo de impuestos"

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_telefonia
#: model:product.template,name:l10n_ar.product_product_telefonia_product_template
msgid "Telephone service (VAT 27)"
msgstr "Servicio Telefónico (IVA 27)"

#. module: l10n_ar
#: model:ir.model,name:l10n_ar.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Plantilla de posición fiscal"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"The document number can not be changed for this journal, you can only modify"
" the POS number if there is not posted (or posted before) invoices"
msgstr ""
"El número de documento no puede ser modificado para este diario, sólo puedes modificar"
" el número de AFIP POS si no tiene facturas validadas o que hayan sido validadas previamente"

#. module: l10n_ar
#: code:addons/l10n_ar/models/l10n_latam_document_type.py:0
#, python-format
msgid ""
"The document number must be entered with a dash (-) and a maximum of 5 characters for the first partand 8 for the second. The following are examples of valid numbers:\n"
"* 1-1\n"
"* 0001-********\n"
"* 00001-********"
msgstr ""
"El número de facturas debe ingresarse con un guión (-) y máximo 5 caracteres para la primer parte y 8 para la segunda. Los siguientes son ejemplos de números válidos:\n"
"* 1-1\n"
"* 0001-********\n"
"* 00001-********"

#. module: l10n_ar
#: code:addons/l10n_ar/models/l10n_latam_document_type.py:0
#, python-format
msgid "The number of import Dispatch must be 16 characters"
msgstr "El número de Despacho de importación debe ser de 16 caracteres"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"The selected Journal can't be used in this transaction, please select one "
"that doesn't use documents as these are just for Invoices."
msgstr ""
"El Diario seleccionado no se puede usar en esta transacción, por favor "
"seleccione uno que no use documentos, ya que estos son solo para Facturas."

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "The validation digit is not valid for \"%s\""
msgstr "El dígito de validación no es válido para \"%s\""

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid "There should be a single tax from the \"VAT\" tax group per line, add it to \"%s\". If you already have it, please check the tax configuration, in advanced options, in the corresponding field \"Tax Group\"."
msgstr ""
"Debe haber un único impuesto del grupo de impuestos \"IVA\" por línea, agréguelo a \"%s\". En caso de tenerlo, revise la configuración del impuesto, en opciones avanzadas, en el campo correspondiente \"Grupo de Impuestos\"."

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_country__l10n_ar_afip_code
#: model:ir.model.fields,help:l10n_ar.field_res_currency__l10n_ar_afip_code
#: model:ir.model.fields,help:l10n_ar.field_uom_uom__l10n_ar_afip_code
msgid "This code will be used on electronic invoice"
msgstr "Este código será utilizado en facturación electrónica"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_company__l10n_ar_gross_income_number
#: model:ir.model.fields,help:l10n_ar.field_res_company__l10n_ar_gross_income_type
msgid "This field is required in order to print the invoice report properly"
msgstr ""
"Este campo es requerido para poder imprimir las facturas correctamente"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_afip_pos_partner_id
#: model:ir.model.fields,help:l10n_ar.field_account_journal__l10n_ar_afip_pos_partner_id
msgid "This is the address used for invoice reports of this POS"
msgstr ""
"Esta dirección es la usada para los reportes de facturación de este PdV"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_afip_pos_number
#: model:ir.model.fields,help:l10n_ar.field_account_journal__l10n_ar_afip_pos_number
msgid ""
"This is the point of sale number assigned by AFIP in order to generate "
"invoices"
msgstr ""
"Este es el número de PdV asignado por la AFIP para poder generar facturas"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group__l10n_ar_tribute_afip_code
msgid "Tribute AFIP Code"
msgstr "Código AFIP Tributos"

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_res_partner__l10n_ar_gross_income_type
#: model:ir.model.fields,help:l10n_ar.field_res_users__l10n_ar_gross_income_type
msgid "Type of gross income: exempt, local, multilateral"
msgstr "Tipo de ingreso bruto: exento, local, multilateral"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_share_sequences
#: model:ir.model.fields,field_description:l10n_ar.field_account_journal__l10n_ar_share_sequences
msgid "Unified Book"
msgstr "Libro Unificado"

#. module: l10n_ar
#: model:product.product,uom_name:l10n_ar.product_product_arancel
#: model:product.product,uom_name:l10n_ar.product_product_cero
#: model:product.product,uom_name:l10n_ar.product_product_exento
#: model:product.product,uom_name:l10n_ar.product_product_no_gravado
#: model:product.product,uom_name:l10n_ar.product_product_quote_despacho
#: model:product.product,uom_name:l10n_ar.product_product_servicio_de_guarda
#: model:product.product,uom_name:l10n_ar.product_product_tasa_estadistica
#: model:product.product,uom_name:l10n_ar.product_product_telefonia
#: model:product.template,uom_name:l10n_ar.product_product_arancel_product_template
#: model:product.template,uom_name:l10n_ar.product_product_cero_product_template
#: model:product.template,uom_name:l10n_ar.product_product_exento_product_template
#: model:product.template,uom_name:l10n_ar.product_product_no_gravado_product_template
#: model:product.template,uom_name:l10n_ar.product_product_quote_despacho_product_template
#: model:product.template,uom_name:l10n_ar.product_product_servicio_de_guarda_product_template
#: model:product.template,uom_name:l10n_ar.product_product_tasa_estadistica_product_template
#: model:product.template,uom_name:l10n_ar.product_product_telefonia_product_template
msgid "Units"
msgstr "Unidades"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__account_tax_group__l10n_ar_vat_afip_code__1
msgid "Untaxed"
msgstr "No Gravado"

#. module: l10n_ar
#: model:product.product,name:l10n_ar.product_product_no_gravado
#: model:product.template,name:l10n_ar.product_product_no_gravado_product_template
msgid "Untaxed concepts (VAT NT)"
msgstr "Conceptos No gravados"

#. module: l10n_ar
#: model:l10n_latam.identification.type,name:l10n_ar.it_UpApP
msgid "UpApP"
msgstr ""

#. module: l10n_ar
#: model:ir.model.fields,help:l10n_ar.field_account_bank_statement_import_journal_creation__l10n_ar_share_sequences
#: model:ir.model.fields,help:l10n_ar.field_account_journal__l10n_ar_share_sequences
msgid "Use same sequence for documents with the same letter"
msgstr "Usar la misma secuencia para documentos con misma letra"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_res_partner__l10n_ar_vat
#: model:ir.model.fields,field_description:l10n_ar.field_res_users__l10n_ar_vat
msgid "VAT"
msgstr "IVA"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_0
msgid "VAT 0%"
msgstr "IVA 0%"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_105
msgid "VAT 10.5%"
msgstr "IVA 10.5%"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_025
msgid "VAT 2,5%"
msgstr "IVA 2,5%"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_21
msgid "VAT 21%"
msgstr "IVA 21%"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_27
msgid "VAT 27%"
msgstr "IVA 27%"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_5
msgid "VAT 5%"
msgstr "IVA 5%"

#. module: l10n_ar
#: model:ir.model.fields,field_description:l10n_ar.field_account_tax_group__l10n_ar_vat_afip_code
msgid "VAT AFIP Code"
msgstr "Código AFIP de IVA"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_exento
msgid "VAT Exempt"
msgstr "IVA Exento"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_no_corresponde
msgid "VAT Not Applicable"
msgstr "IVA No Corresponde"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_percepcion_iva
msgid "VAT Perception"
msgstr "Percepción de IVA"

#. module: l10n_ar
#: model:account.tax.group,name:l10n_ar.tax_group_iva_no_gravado
msgid "VAT Untaxed"
msgstr "IVA No Gravado"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_fiscal_position.py:0
#, python-format
msgid "Warning"
msgstr "Alerta"

#. module: l10n_ar
#: code:addons/l10n_ar/models/res_partner.py:0
#, python-format
msgid "We were not able to sanitize the identification number"
msgstr "No pudimos limpiar el número de identificación"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_account_invoice_report_search_inherit
msgid "With Document"
msgstr "Con Documento"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"You are trying to create an invoice for domestic partner but you don't have "
"a domestic market journal"
msgstr ""
"Estas intentando crear una factura para un cliente local pero no tienes "
"un diario de tipo mercado interno"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_move.py:0
#, python-format
msgid ""
"You are trying to create an invoice for foreign partner but you don't have "
"an exportation journal"
msgstr ""
"Estas intentando crear una factura para un cliente del exterior pero no tienes "
"un diario de tipo exportación"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_chart_template.py:0
#, python-format
msgid ""
"You are trying to install a chart of account for the %s responsibility but "
"your company is configured as %s type"
msgstr ""
"Estás intentando instalar un plan de cuentas para la responsabilidad %s pero"
" tu compañía ha sido configurada con el tipo %s"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid ""
"You can not change the journal's configuration if it already has validated "
"invoices"
msgstr ""
"No puedes cambiar la configuración del diario si ya tiene facturas validadas"

#. module: l10n_ar
#: code:addons/l10n_ar/models/account_journal.py:0
#, python-format
msgid ""
"You can not change the journal's configuration if journal already have "
"validated invoices"
msgstr ""
"No puedes cambiar la confguración de un diario si el diario ya tiene "
"facturas validadas"

#. module: l10n_ar
#: model:ir.model.fields.selection,name:l10n_ar.selection__l10n_latam_document_type__purchase_aliquots__zero
msgid "Zero"
msgstr "Cero"

#. module: l10n_ar
#: code:addons/l10n_ar/models/l10n_latam_document_type.py:0
#, python-format
msgid "is not a valid value for"
msgstr "no es un valor valido para"

#. module: l10n_ar
#: model_terms:ir.ui.view,arch_db:l10n_ar.report_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_ar.view_move_form
msgid "to"
msgstr "al"
