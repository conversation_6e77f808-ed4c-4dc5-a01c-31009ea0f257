<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record forcecreate="True" id="ir_cron_presence_control" model="ir.cron">
            <field name="name">HR Presence: cron</field>
            <field name="model_id" ref="hr.model_hr_employee"/>
            <field name="state">code</field>
            <field name="code">model._check_presence()</field>
            <field eval="True" name="active" />
            <field name="user_id" ref="base.user_root" />
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
        </record>
    </data>
</odoo>
