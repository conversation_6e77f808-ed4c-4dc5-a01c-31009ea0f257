<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_acquirer_form" model="ir.ui.view">
        <field name="name">PayU latam Acquirer Form</field>
        <field name="model">payment.acquirer</field>
        <field name="inherit_id" ref="payment.payment_acquirer_form"/>
        <field name="arch" type="xml">
            <xpath expr='//group[@name="acquirer"]' position='inside'>
                <group attrs="{'invisible': [('provider', '!=', 'payulatam')]}">
                    <field name="payulatam_merchant_id"
                           attrs="{'required':[('provider', '=', 'payulatam'), ('state', '!=', 'disabled')]}"/>
                    <field name="payulatam_account_id"
                           attrs="{'required':[('provider', '=', 'payulatam'), ('state', '!=', 'disabled')]}"/>
                    <field name="payulatam_api_key"
                           attrs="{'required':[('provider', '=', 'payulatam'), ('state', '!=', 'disabled')]}"/>
                </group>
            </xpath>
        </field>
    </record>

</odoo>
