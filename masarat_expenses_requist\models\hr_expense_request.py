# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime, timedelta, date
from odoo.exceptions import ValidationError
from dateutil.relativedelta import relativedelta

class HrEmployeeExpenseRequest(models.Model):
    _name = "hr.masarat.expense"

    _inherit = ['mail.thread', 'mail.activity.mixin']

    _rec_name = 'expense_name'
    expense_name = fields.Char(compute='get_expense_name', store=True)

    state = fields.Selection(selection=[('draft', 'Draft'),
                                        ('manager_approval', 'Manager Approval'),
                                        ('manager_refused', 'Manager Refused'),
                                        ('finance_approval', 'Finance Approval'),
                                        ('finance_refused', 'Finance Refused')], default='draft', string="State")
    payment_state = fields.Selection(selection=[('not_paid', 'Not Paid'),
                                        ('paid', 'Paid')], default='not_paid', string="Payment State")

    is_closed = fields.Boolean(string='مقفلة', default=False)

    request_date = fields.Date(string="تاريخ الطلب", readonly=False, default=lambda self: fields.Date.to_string(date.today()))

    amount = fields.Float(string = "القيمة", default = 0)
    closer_date = fields.Date(string = "تاريخ الاقفال (متوقع)", required=True)



    employee_id = fields.Many2one('hr.employee', string="Employee")
    manager_id = fields.Many2one('hr.employee', readonly=True, related='employee_id.parent_id', string="المدير المباشر")

    # company_id = fields.Many2one('res.company', string='Company', readonly=True, default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', string='العملة', required=True)
    payment_type = fields.Selection([('cash','كاش'),('cheque','شيك')],required=True)

    expense_use = fields.Char(string="الغرض من العهدة",required=True)

    Note = fields.Text(string="ملاحظة")

    is_manager = fields.Char(compute='call_with_sudo_is_manager')
    is_finance_group = fields.Char(compute='call_with_sudo_is_finance_group')


    def make_payment_state(self):
        self.payment_state = 'paid'
        employee = self.env['hr.employee'].search([('id', '=', self.employee_id.id)])
        email_from = employee.work_email
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (self.id, self._name)
        body = """
                        <div dir="rtl">
                            <p><font style="font-size: 14px;">Your Employee """ + employee.name + """, requested BT cash approval, </font></p>
                            <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
                            <a href="%s">Request Link</a>
                        </div>""" % (web_base_url)
        ##### Sending to Finance Approval
        finance_manager = self.env.ref("masarat_expenses_requist.group_finance_approvales").users
        for each in finance_manager:
            template_id = self.sudo().env['mail.mail'].create({
                'subject': 'طلب عهدة',
                'email_from': email_from,
                'email_to': each.login,
                'body_html': body
            })
            template_id.sudo().send()


    def make_cancel_payment_state(self):
        self.payment_state = 'not_paid'

    def get_if_finance_group(self):
        finance_group = self.env.user.has_group('masarat_expenses_requist.group_finance_approvales')
        for rec in self:
            if finance_group:
                rec.is_finance_group = 'yes'
            else:
                rec.is_finance_group = 'no'

    def compute_button_visible(self):
        for rec in self:
            if rec.manager_id.user_id.id == self._uid:
                rec.is_manager = '1'
            else:
                rec.is_manager = '0'

    @api.onchange('employee_id')
    def call_with_sudo_is_manager(self):
        self.sudo().compute_button_visible()

    @api.depends('is_finance_group')
    def call_with_sudo_is_finance_group(self):
        self.sudo().get_if_finance_group()

    @api.depends('employee_id', 'request_date')
    def get_expense_name(self):
        for elem in self:
            elem.expense_name = False
            if elem.employee_id and elem.expense_use:
                elem.expense_name = elem.employee_id.name + '- BT -' + str(elem.expense_use)

    def make_cancel_approval(self):
        self.state = 'draft'

    def make_manager_approval(self):
        self.state = 'manager_approval'
        employee = self.env['hr.employee'].search([('id', '=', self.employee_id.id)])
        email_from = employee.work_email
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (self.id, self._name)
        body = """
                <div dir="rtl">
                    <p><font style="font-size: 14px;">Your Employee """ + employee.name + """, requested BT cash approval, </font></p>
                    <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
                    <a href="%s">Request Link</a>
                </div>""" % (web_base_url)
        ##### Sending to Finance Manager
        finance_manager = self.env.ref("masarat_expenses_requist.group_finance_manager").users
        for each in finance_manager:
            template_id = self.sudo().env['mail.mail'].create({
                'subject': 'طلب عهدة',
                'email_from': email_from,
                'email_to': each.login,
                'body_html': body
            })
            template_id.sudo().send()

    def make_manager_refused(self):
        self.state = 'manager_refused'
    def make_finance_approval(self):
        try:
            self.state = 'finance_approval'
            form_id = self.env.ref("account.view_account_journal_form")
            return {
                'type': 'ir.actions.act_window',
                'name': 'Journal',
                'view_type': 'form',
                'view_mode': 'form',
                'res_model': 'account.journal',
                'context':{
                    'default_name':self.expense_name,
                    'default_currency_id': self.currency_id.id,
                    'default_expense_use': self.expense_use,
                    'default_grant_id': self.id,
                },
                'views': [(form_id.id, 'form')],
                'target': 'current'}
        except:
            raise ValidationError('There is Error')



    def make_finance_refused(self):
        self.state = 'finance_refused'

    @api.model
    def default_get(self, fields):
        res = super(HrEmployeeExpenseRequest, self).default_get(fields)
        user_id = self._context.get('uid')
        employee_id = self.env['hr.employee'].search([('user_id', '=', user_id)])
        res['employee_id'] = employee_id.id
        finance_group = self.env.user.has_group('masarat_expenses_requist.group_finance_approvales')
        if finance_group:
            res['is_finance_group'] = 'yes'
        else:
            res['is_finance_group'] = 'no'
        ########################
        return res

    def unlink(self):
        for elem in self:
            if elem.state != 'draft':
                raise ValidationError('You cannot delete this request which is not in draft state')
            return super(HrEmployeeExpenseRequest, self).unlink()

    def action_send_notification_to_maneger(self,employee_id,recode_id):
        employee = self.env['hr.employee'].search([('id', '=', employee_id)])
        email_to = employee.parent_id.work_email
        email_from = employee.work_email
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recode_id, self._name)
        body = """
        <div dir="rtl">
            <p><font style="font-size: 14px;">Your Employee """+employee.name+""", requested BT cash approval, </font></p>
            <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            <a href="%s">Request Link</a>
        </div>""" % (web_base_url)

        template_id = self.env['mail.mail'].create({
            'subject':'طلب عهدة',
            'email_from':email_from,
            'email_to': email_to,
            'body_html':body
        })
        template_id.send()
        #### freaa


    @api.model
    def create(self, vals_list):
        obj = super(HrEmployeeExpenseRequest, self).create(vals_list)
        recode_id = obj.id
        employee_id = obj.employee_id.id
        self.sudo().action_send_notification_to_maneger(employee_id, recode_id)
        return obj


    def check_for_closer_notification(self):
        current_date = date.today()
        all_closer = self.env['hr.masarat.expense'].search([('is_closed','=',False),('closer_date','<',current_date)])
        for elem in all_closer:
            body = """
                                    <div dir="rtl">
                                        <p><font style="font-size: 14px;">The Following BT needs to be closed """ + str(elem.expense_name) + """,, </font></p>
                                        <p><font style="font-size: 14px;">Its Closer Date at """ + str(elem.closer_date) + """</font></p>
                                    </div>"""
            ##### Sending to Finance Approval
            finance_manager = self.env.ref("masarat_expenses_requist.group_finance_approvales").users
            for each in finance_manager:
                template_id = self.sudo().env['mail.mail'].create({
                    'subject': 'إشعار اغلاق عهدة',
                    'email_from': elem.employee_id.work_email,
                    'email_to': each.login,
                    'body_html': body
                })
                template_id.sudo().send()
            elem.is_closed = True


class AccountJournl(models.Model):
    _inherit = "account.journal"

    expense_use = fields.Char(string="الغرض من العهدة" , readonly=True)

    type = fields.Selection([
        ('sale', 'Sales'),
        ('purchase', 'Purchase'),
        ('cash', 'Cash'),
        ('bank', 'Bank'),
        ('general', 'Miscellaneous'),
    ], required=True,
        help="Select 'Sale' for customer invoices journals.\n" \
             "Select 'Purchase' for vendor bills journals.\n" \
             "Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n" \
             "Select 'General' for miscellaneous operations journals.")

    default_account_id = fields.Many2one(
        comodel_name='account.account', check_company=True, copy=False, ondelete='restrict',
        string='Default Account',
        domain="[('deprecated', '=', False), ('company_id', '=', company_id),"
               "'|', ('user_type_id', '=', default_account_type), ('user_type_id', 'in', type_control_ids),"
               "('user_type_id.type', '!=' ,'payable' )]")

    payment_debit_account_id = fields.Many2one(
        comodel_name='account.account', check_company=True, copy=False, ondelete='restrict',
        help="Incoming payments entries triggered by invoices/refunds will be posted on the Outstanding Receipts Account "
             "and displayed as blue lines in the bank reconciliation widget. During the reconciliation process, concerned "
             "transactions will be reconciled with entries on the Outstanding Receipts Account instead of the "
             "receivable account.", string='Outstanding Receipts Account',
        domain=lambda self: "[('deprecated', '=', False), ('company_id', '=', company_id), \
                                 ('user_type_id.type', '!=', 'payable'), \
                                 '|', ('user_type_id', '=', %s), ('id', '=', default_account_id)]" % self.env.ref(
            'account.data_account_type_current_assets').id)
    payment_credit_account_id = fields.Many2one(
        comodel_name='account.account', check_company=True, copy=False, ondelete='restrict',
        help="Outgoing payments entries triggered by bills/credit notes will be posted on the Outstanding Payments Account "
             "and displayed as blue lines in the bank reconciliation widget. During the reconciliation process, concerned "
             "transactions will be reconciled with entries on the Outstanding Payments Account instead of the "
             "payable account.", string='Outstanding Payments Account',
        domain=lambda self: "[('deprecated', '=', False), ('company_id', '=', company_id), \
                                 ('user_type_id.type', '!=', 'payable'), \
                                 '|', ('user_type_id', '=', %s), ('id', '=', default_account_id)]" % self.env.ref(
            'account.data_account_type_current_assets').id)


    @api.model
    def _prepare_liquidity_account_vals(self, company, code, vals):
        return {
            'name': vals.get('name'),
            'code': code,
            'reconcile':True,
            'user_type_id': self.env.ref('account.data_account_type_receivable').id,
            'currency_id': vals.get('currency_id'),
            'company_id': company.id,
        }

    @api.depends('type')
    def _compute_default_account_type(self):
        default_account_id_types = {
            'bank': 'account.data_account_type_liquidity',
            'cash': 'account.data_account_type_receivable',
            'sale': 'account.data_account_type_revenue',
            'purchase': 'account.data_account_type_expenses'
        }

        for journal in self:
            if journal.type in default_account_id_types:
                journal.default_account_type = self.env.ref(default_account_id_types[journal.type]).id
            else:
                journal.default_account_type = False



    @api.model
    def _fill_missing_values(self, vals):
        journal_type = vals.get('type')

        # 'type' field is required.
        if not journal_type:
            return

        # === Fill missing company ===
        company = self.env['res.company'].browse(vals['company_id']) if vals.get('company_id') else self.env.company
        vals['company_id'] = company.id

        # Don't get the digits on 'chart_template_id' since the chart template could be a custom one.
        random_account = self.env['account.account'].search([('company_id', '=', company.id)], limit=1)
        digits = len(random_account.code) if random_account else 6

        liquidity_type = self.env.ref('account.data_account_type_liquidity')
        current_assets_type = self.env.ref('account.data_account_type_current_assets')

        if journal_type in ('bank', 'cash'):
            has_liquidity_accounts = vals.get('default_account_id')
            has_payment_accounts = vals.get('payment_debit_account_id') or vals.get('payment_credit_account_id')
            has_profit_account = vals.get('profit_account_id')
            has_loss_account = vals.get('loss_account_id')

            if journal_type == 'bank':
                liquidity_account_prefix = company.bank_account_code_prefix or ''
            else:
                liquidity_account_prefix = company.cash_account_code_prefix or company.bank_account_code_prefix or ''

            # === Fill missing name ===
            vals['name'] = vals.get('name') or vals.get('bank_acc_number')

            # === Fill missing code ===
            if 'code' not in vals:
                vals['code'] = self.get_next_bank_cash_default_code(journal_type, company)
                if not vals['code']:
                    raise UserError(_("Cannot generate an unused journal code. Please fill the 'Shortcode' field."))

            # === Fill missing accounts ===
            if not has_liquidity_accounts:
                default_account_code = self.env['account.account']._search_new_account_code(company, digits, liquidity_account_prefix)
                default_account_vals = self._prepare_liquidity_account_vals(company, default_account_code, vals)
                vals['default_account_id'] = self.env['account.account'].create(default_account_vals).id
            if not has_payment_accounts:
                vals['payment_debit_account_id'] = vals['default_account_id']
                vals['payment_credit_account_id'] = vals['default_account_id']
            if journal_type == 'cash' and not has_profit_account:
                vals['profit_account_id'] = company.default_cash_difference_income_account_id.id
            if journal_type == 'cash' and not has_loss_account:
                vals['loss_account_id'] = company.default_cash_difference_expense_account_id.id

        # === Fill missing refund_sequence ===
        if 'refund_sequence' not in vals:
            vals['refund_sequence'] = vals['type'] in ('sale', 'purchase')
