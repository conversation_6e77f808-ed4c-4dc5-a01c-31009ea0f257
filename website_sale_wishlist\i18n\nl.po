# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_wishlist
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_add_to_wishlist
msgid "<i class=\"fa fa-heart-o fa-2x\" role=\"img\" aria-label=\"Add to wishlist\"/>"
msgstr ""
"<i class=\"fa fa-heart-o fa-2x\" role=\"img\" aria-label=\"Toevoegen aan "
"verlanglijst\"/>"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "<small><i class=\"fa fa-trash-o\"/> Remove</small>"
msgstr "<small><i class=\"fa fa-trash-o\"/> Verwijder</small>"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.add_to_wishlist
msgid "<span class=\"fa fa-heart\" role=\"img\" aria-label=\"Add to wishlist\"/>"
msgstr "<span class=\"fa fa-heart\" role=\"img\" aria-label=\"Voeg toe aan whitelist\"/>"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__active
msgid "Active"
msgstr "Actief"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Add <span class=\"d-none d-md-inline\">to Cart</span>"
msgstr "Voeg toe <span class=\"d-none d-md-inline\">aan winkelmandje</span>"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Add product to my cart but keep it in my wishlist"
msgstr ""
"Voeg product toe aan mijn winkelmandje maar hou het product in de wenslijst"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.add_to_wishlist
msgid "Add to Wishlist"
msgstr "Toevoegen aan wenslijst"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_add_to_wishlist
msgid "Add to wishlist"
msgstr "Toevoegen aan verlanglijst"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: website_sale_wishlist
#: model:ir.model.constraint,message:website_sale_wishlist.constraint_product_wishlist_product_unique_partner_id
msgid "Duplicated wishlisted product for this partner."
msgstr "Gedupliceerd wenslijst product voor deze relatie."

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__id
msgid "ID"
msgstr "ID"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "My Wishlist"
msgstr "Mijn wenslijst"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__partner_id
msgid "Owner"
msgstr "Eigenaar"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__price
msgid "Price"
msgstr "Prijs"

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist__price
msgid "Price of the product when it has been added in the wishlist"
msgstr "Prijs van het product wanneer het is toegevoegd aan de wenslijst"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__pricelist_id
msgid "Pricelist"
msgstr "Prijslijst"

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist__pricelist_id
msgid "Pricelist when added"
msgstr "Prijslijst wanneer toegevoegd"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_product
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__product_id
msgid "Product"
msgstr "Product"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_template
msgid "Product Template"
msgstr "Productsjabloon"

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_wishlist
msgid "Product Wishlist"
msgstr "Product wenslijst"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Product image"
msgstr "Productafbeelding"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Shop Wishlist"
msgstr "Shop wenslijst"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.snippet_options
msgid "Show Empty Wishlist"
msgstr "Toon lege wenslijst."

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_res_users
msgid "Users"
msgstr "Gebruikers"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist__website_id
msgid "Website"
msgstr "Website"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_res_partner__wishlist_ids
#: model:ir.model.fields,field_description:website_sale_wishlist.field_res_users__wishlist_ids
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.header_wishlist_link
msgid "Wishlist"
msgstr "Wenslijst"
