<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="0">
    <record id="mailing_sms_0" model="mailing.mailing">
        <field name="name">XMas Promo</field>
        <field name="subject">XMas Promo</field>
        <field name="mailing_type">sms</field>
        <field name="state">done</field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="sent_date" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        <field name="campaign_id" ref="utm_campaign_0"/>
        <field name="mailing_model_id" ref="base.model_res_partner"/>
        <field name="mailing_domain" eval="[('parent_id', '=', ref('base.res_partner_4'))]"/>
        <field name="body_plaintext">This week-end, incredible promotion for XMas ! See http://sms.example.com !</field>
    </record>
    <!-- Generate link tracker information from it -->
    <function model="mailing.mailing" name="convert_links" eval="[ref('mass_mailing_sms.mailing_sms_0')]"/>
    <!-- Simulate traces -->
    <record id="mailing_sms_0_trace_0" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_0"/>
        <field name="trace_type">sms</field>
        <field name="sms_number">+32465000000</field>
        <field name="model">res.partner</field>
        <field name="res_id" ref="base.res_partner_address_7"/>
        <field name="trace_status">sent</field>
        <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        <field name="write_date" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
    </record>
    <record id="mailing_sms_0_trace_1" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_0"/>
        <field name="trace_type">sms</field>
        <field name="sms_number">+32465000001</field>
        <field name="model">res.partner</field>
        <field name="res_id" ref="base.res_partner_address_13"/>
        <field name="trace_status">sent</field>
        <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        <field name="write_date" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
    </record>
    <record id="mailing_sms_0_trace_2" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_0"/>
        <field name="trace_type">sms</field>
        <field name="sms_number">+32465000002</field>
        <field name="model">res.partner</field>
        <field name="res_id" ref="base.res_partner_address_14"/>
        <field name="trace_status">sent</field>
        <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        <field name="write_date" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
    </record>
    <record id="mailing_sms_0_trace_3" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_0"/>
        <field name="trace_type">sms</field>
        <field name="sms_number">+32465000003</field>
        <field name="model">res.partner</field>
        <field name="res_id" ref="base.res_partner_address_24"/>
        <field name="trace_status">sent</field>
        <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        <field name="write_date" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
    </record>
    <record id="mailing_sms_0_trace_4" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_0"/>
        <field name="trace_type">sms</field>
        <field name="sms_number">+32465000004</field>
        <field name="model">res.partner</field>
        <field name="res_id" ref="base.res_partner_address_33"/>
        <field name="trace_status">error</field>
        <field name="failure_type">sms_server</field>
        <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        <field name="write_date" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
    </record>
    <record id="mailing_sms_0_trace_5" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_0"/>
        <field name="trace_type">sms</field>
        <field name="sms_number">123123</field>
        <field name="model">res.partner</field>
        <field name="res_id" ref="base.res_partner_address_33"/>
        <field name="trace_status">bounce</field>
        <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        <field name="write_date" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
    </record>
    <record id="mailing_sms_0_trace_6" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_0"/>
        <field name="trace_type">sms</field>
        <field name="sms_number"></field>
        <field name="model">res.partner</field>
        <field name="res_id" ref="base.res_partner_address_34"/>
        <field name="trace_status">error</field>
        <field name="failure_type">sms_number_missing</field>
        <field name="sent_datetime" eval="False"/>
    </record>

    <!-- Generate some clicks -->
    <function model="link.tracker.click" name="add_click">
        <value model="link.tracker.code"
            search="[('link_id.url', '=', 'http://sms.example.com')]"
            use="code"/>
        <value name="ip">************</value>
        <value name="country_code">BE</value>
        <value name="mailing_trace_id" eval="ref('mailing_sms_0_trace_0')"/>
    </function>
    <function model="link.tracker.click" name="add_click">
        <value model="link.tracker.code"
            search="[('link_id.url', '=', 'http://sms.example.com')]"
            use="code"/>
        <value name="ip">************</value>
        <value name="country_code">BE</value>
        <value name="mailing_trace_id" eval="ref('mailing_sms_0_trace_0')"/>
    </function>
    <function model="link.tracker.click" name="add_click">
        <value model="link.tracker.code"
            search="[('link_id.url', '=', 'http://sms.example.com')]"
            use="code"/>
        <value name="ip">************</value>
        <value name="country_code">BE</value>
        <value name="mailing_trace_id" eval="ref('mailing_sms_0_trace_1')"/>
    </function>
    <function model="link.tracker.click" name="add_click">
        <value model="link.tracker.code"
            search="[('link_id.url', '=', 'http://sms.example')]"
            use="code"/>
        <value name="ip">************</value>
        <value name="country_code">BE</value>
        <value name="mailing_trace_id" eval="ref('mailing_sms_0_trace_2')"/>
    </function>

    <record id="mailing_sms_1" model="mailing.mailing">
        <field name="name">Extra Promo</field>
        <field name="subject">Extra Promo</field>
        <field name="mailing_type">sms</field>
        <field name="state">done</field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="sent_date" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        <field name="campaign_id" ref="utm_campaign_0"/>
        <field name="mailing_model_id" ref="mass_mailing.model_mailing_list"/>
        <field name="contact_list_ids" eval="[(5, 0), (4, ref('mass_mailing_sms.mailing_list_sms_0'))]"/>
        <field name="body_plaintext">Extra promotion for you !</field>
    </record>
    <!-- Simulate traces -->
    <record id="mailing_sms_1_trace_0" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_1"/>
        <field name="trace_type">sms</field>
        <field name="sms_number">+32456001100</field>
        <field name="model">mailing.contact</field>
        <field name="res_id" ref="mass_mailing_sms.mailing_contact_0_0"/>
        <field name="trace_status">sent</field>
        <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
    </record>
    <record id="mailing_sms_1_trace_1" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_1"/>
        <field name="trace_type">sms</field>
        <field name="sms_number">+32456001111</field>
        <field name="model">mailing.contact</field>
        <field name="res_id" ref="mass_mailing_sms.mailing_contact_0_1"/>
        <field name="trace_status">sent</field>
        <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
    </record>
    <record id="mailing_sms_1_trace_2" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_1"/>
        <field name="trace_type">sms</field>
        <field name="sms_number">+32456001122</field>
        <field name="model">mailing.contact</field>
        <field name="res_id" ref="mass_mailing_sms.mailing_contact_0_2"/>
        <field name="trace_status">sent</field>
        <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
    </record>
    <record id="mailing_sms_1_trace_3" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_1"/>
        <field name="trace_type">sms</field>
        <field name="sms_number">+32456001133</field>
        <field name="model">mailing.contact</field>
        <field name="res_id" ref="mass_mailing_sms.mailing_contact_0_3"/>
        <field name="trace_status">error</field>
        <field name="failure_type">sms_credit</field>
    </record>
    <record id="mailing_sms_1_trace_4" model="mailing.trace">
        <field name="mass_mailing_id" ref="mailing_sms_1"/>
        <field name="trace_type">sms</field>
        <field name="sms_number">dummy</field>
        <field name="model">mailing.contact</field>
        <field name="res_id" ref="mass_mailing_sms.mailing_contact_0_4"/>
        <field name="trace_status">error</field>
        <field name="failure_type">sms_number_format</field>
    </record>

</data></odoo>
