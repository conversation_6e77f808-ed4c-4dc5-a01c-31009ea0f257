# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale_wishlist
# 
# Translators:
# <PERSON><PERSON><PERSON> <djord<PERSON><EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.header
msgid ""
"<i class=\"fa fa-heart\"/>\n"
"                        Wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "<small><i class=\"fa fa-trash-o\"/> Remove</small>"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_active
msgid "Active"
msgstr "Aktivan"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Add <span class=\"hidden-xs\">to Cart</span>"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Add product to my cart but keep it in my wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.add_to_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_add_to_wishlist
msgid "Add to Wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_create_date
msgid "Added Date"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_currency_id
msgid "Currency"
msgstr "Valuta"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_res_users_current_session
msgid "Current Session"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_price_new
msgid "Current price"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist_price_new
msgid "Current price of this product, using same pricelist, ..."
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_id
msgid "ID"
msgstr "ID"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "My Wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_partner_id
msgid "Owner"
msgstr "Vlasnik"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_price
msgid "Price"
msgstr "Cijena"

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist_price
msgid "Price of the product when it has been added in the wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_pricelist_id
msgid "Pricelist"
msgstr "Cjenovnik"

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist_pricelist_id
msgid "Pricelist when added"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_product_id
msgid "Product"
msgstr "Proizvod"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_session
msgid "Session"
msgstr ""

#. module: website_sale_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_wishlist.product_wishlist
msgid "Shop Wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_product_wishlist_website_id
msgid "Website"
msgstr "Internet stranica"

#. module: website_sale_wishlist
#: model:ir.model.fields,help:website_sale_wishlist.field_product_wishlist_session
msgid "Website session identifier where this product was wishlisted."
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model.fields,field_description:website_sale_wishlist.field_res_partner_wishlist_ids
#: model:ir.model.fields,field_description:website_sale_wishlist.field_res_users_wishlist_ids
msgid "Wishlist"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_ir_autovacuum
msgid "ir.autovacuum"
msgstr ""

#. module: website_sale_wishlist
#: model:ir.model,name:website_sale_wishlist.model_product_wishlist
msgid "product.wishlist"
msgstr ""
