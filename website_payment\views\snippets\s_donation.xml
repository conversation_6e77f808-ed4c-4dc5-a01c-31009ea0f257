<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_donation_button" name="Don<PERSON> Button">
    <div class="s_donation"
            data-name="Donation Button"
            data-donation-email="<EMAIL>"
            data-custom-amount="freeAmount"
            t-att-data-display-options="display_options"
            data-prefilled-options="true"
            data-descriptions="true"
            data-donation-amounts='["10", "25", "50", "100"]'
            data-minimum-amount="5"
            data-maximum-amount="100"
            data-slider-step="5"
            data-default-amount="25">
        <form class="s_donation_form" action="/donation/pay" method="post" enctype="multipart/form-data">
            <span id="s_donation_description_inputs">
                <input type="hidden" class="o_translatable_input_hidden d-block mb-1 w-100" name="donation_descriptions" value="A year of cultural awakening."/>
                <input type="hidden" class="o_translatable_input_hidden d-block mb-1 w-100" name="donation_descriptions" value="Caring for a baby for 1 month."/>
                <input type="hidden" class="o_translatable_input_hidden d-block mb-1 w-100" name="donation_descriptions" value="One year in elementary school."/>
                <input type="hidden" class="o_translatable_input_hidden d-block mb-1 w-100" name="donation_descriptions" value="One year in high school."/>
            </span>
            <a href="#" type="button" class="s_donation_donate_btn btn btn-secondary btn-lg mb-2">Donate Now</a>
        </form>
    </div>
</template>

<template id="s_donation" name="Donation">
    <section class="pt32 pb32 o_cc o_cc1">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-7 pt16 pb16">
                    <h2>Make a Donation</h2>
                    <p>Small or large, your contribution is essential.</p>
                    <t t-snippet-call="website_payment.s_donation_button">
                        <t t-set="display_options" t-value="'true'"/>
                    </t>
                </div>
                <div class="col-lg-5 pt16 pb16 d-none d-md-block">
                    <img src="/web_editor/shape/website_payment/s_donation_gift.svg?c1=o-color-1" class="img img-fluid mx-auto" style="width: 75%;" alt=""/>
                </div>
            </div>
        </div>
    </section>
</template>

<template id="s_donation_options"  inherit_id="website.snippet_options">
    <xpath expr="." position="inside">
        <div data-js="Donation" data-selector=".s_donation">
            <we-input class="o_we_large" string="Recipient Email" data-select-data-attribute=""
                data-attribute-name="donationEmail"/>
            <we-checkbox string="Display Options"
                    data-name="display_options_opt"
                    data-display-options="true"
                    data-no-preview="true">
            </we-checkbox>
            <we-checkbox string="Pre-filled Options"
                    data-name="pre_filled_opt"
                    data-toggle-prefilled-options="true"
                    data-dependencies="!no_input_opt"
                    data-no-preview="true">
            </we-checkbox>
            <we-checkbox string="⌙ Descriptions"
                    data-toggle-option-description="true"
                    data-dependencies="pre_filled_opt"
                    data-no-preview="true">
            </we-checkbox>
            <we-select string="Custom Amount" data-no-preview="true">
                <we-button data-name="free_amount_opt" data-select-amount-input="freeAmount">Input</we-button>
                <we-button data-name="slider_opt" data-select-amount-input="slider" data-dependencies="display_options_opt">Slider</we-button>
                <we-button data-name="no_input_opt" data-select-amount-input="" data-dependencies="pre_filled_opt">None</we-button>
            </we-select>
            <we-input string="⌙ Minimum" data-step="1" data-set-minimum-amount="" data-dependencies="!no_input_opt"/>
            <we-input string="⌙ Maximum" data-step="1" data-set-maximum-amount="" data-dependencies="slider_opt"/>
            <we-input string="⌙ Step" data-step="1" data-set-slider-step="" data-dependencies="slider_opt"/>
            <we-input string="Default Amount" data-step="1" data-attribute-default-value="25"
                data-select-data-attribute="" data-attribute-name="defaultAmount"/>
        </div>
    </xpath>
</template>

<record id="website_payment.s_donation_000_js" model="ir.asset">
    <field name="name">Donation 000 JS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">website_payment/static/src/snippets/s_donation/000.js</field>
</record>

<record id="website_payment.s_donation_000_scss" model="ir.asset">
    <field name="name">Donation 000 SCSS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">website_payment/static/src/snippets/s_donation/000.scss</field>
</record>

</odoo>
