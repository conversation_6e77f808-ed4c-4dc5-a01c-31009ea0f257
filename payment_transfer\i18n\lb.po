# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_transfer
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:0
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:0
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:0
#, python-format
msgid ""
"<div>\n"
"<h3>Please use the following transfer details</h3>\n"
"<h4>%(bank_title)s</h4>\n"
"%(bank_accounts)s\n"
"<h4>Communication</h4>\n"
"<p>Please use the order name as communication reference.</p>\n"
"</div>"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:0
#, python-format
msgid "Bank Account"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:0
#, python-format
msgid "Bank Accounts"
msgstr ""

#. module: payment_transfer
#: model:ir.model.fields.selection,name:payment_transfer.selection__payment_acquirer__provider__transfer
msgid "Manual Payment"
msgstr ""

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_transfer
#: model:ir.model.fields,field_description:payment_transfer.field_payment_acquirer__provider
msgid "Provider"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:0
#, python-format
msgid "received data for reference %s"
msgstr ""
