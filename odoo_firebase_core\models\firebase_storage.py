import base64
from datetime import datetime, timedelta

from odoo import models, fields, api
from odoo.tools.safe_eval import safe_eval


class OdooFirebaseAttachmentItem(models.Model):
    _name = "firebase.storage.item"
    _description = "Firebase: Item of attachment rules"
    _sql_constraints = [
        ('storage_item_uniq',
         'unique (rule_id,attachment_id)',
         'A attachment could be defined only one time on same rule_id.')
    ]

    name = fields.Char(
        string="Name"
    )
    attachment_id = fields.Many2one(
        string="Attachment",
        comodel_name="ir.attachment"
    )
    rule_id = fields.Many2one(
        string="Storage rule",
        comodel_name="firebase.storage"
    )
    cloud_key = fields.Char(
        string="Cloud key",
        copy=False
    )
    cloud_path = fields.Char(
        string="Cloud path",
        copy=False
    )
    cloud_url = fields.Char(
        string="Cloud URL",
        copy=False
    )
    cloud_last_sync = fields.Datetime(
        string="Cloud Last Sync",
        copy=False
    )

    def _send_attachment_to_gcloud(self, force_update=False):
        self.ensure_one()
        item = self.sudo()
        rule = item.rule_id
        attach = item.attachment_id
        bucket = rule.account_id._get_bucket()
        url = False
        if self.cloud_key and self.cloud_path and not force_update:
            res = bucket.get_blob(self.cloud_path)
        else:
            name = "{}/{}-{}".format(
                rule.path if rule.path else "",
                str(attach.id),
                str(attach.store_fname).replace("/", "_")
            )
            content = base64.urlsafe_b64decode(attach.datas)
            res = bucket.blob(name)
            res.upload_from_string(
                data=content,
                content_type=attach.mimetype
            )
        if rule.is_public:
            res.make_public()
        else:
            current_date = datetime.today()
            url = res.generate_signed_url(
                expiration=current_date + timedelta(days=rule.expiration)
            )
        if res:
            self.write({
                'cloud_key': res.id,
                'cloud_path': res.name,
                'cloud_url': url if url else res.public_url,
                'cloud_last_sync': fields.Datetime.now()
            })
        return res

    def _delete_attachment_from_gcloud(self):
        self.ensure_one()
        item = self.sudo()
        bucket = item.rule_id.account_id._get_bucket()
        res = bucket.get_blob(item.cloud_path)
        if res:
            del_data = res.delete()
            self.unlink()
            return del_data
        return False


class OdooFirebaseAttachment(models.Model):
    _name = 'firebase.storage'
    _description = "Firebase: Rule of Attachment"

    name = fields.Char(
        string="Name"
    )
    active = fields.Boolean(
        string="Active",
        default=True
    )
    path = fields.Char(
        string="Path"
    )
    account_id = fields.Many2one(
        comodel_name='firebase.account',
        string='Account'
    )
    domain = fields.Text(
        string="Domain",
        default='[]',
        required=False
    )
    is_public = fields.Boolean(
        string="Make public",
        default=False
    )
    expiration = fields.Integer(
        string="Days to URL expiration",
        default=30
    )
    create_automation_id = fields.Many2one(
        string="Automation rule when create",
        comodel_name="base.automation"
    )
    write_automation_id = fields.Many2one(
        string="Automation rule when write",
        comodel_name="base.automation"
    )
    unlink_automation_id = fields.Many2one(
        string="Automation rule when unlink",
        comodel_name="base.automation"
    )

    def _get_eval_domain(self):
        self.ensure_one()
        return safe_eval(self.domain, {})

    def cron_sync_attachments(self):
        pass

    def force_sync(self, res_id=False):
        for rule in self:
            domain = rule._get_eval_domain()
            if res_id and isinstance(res_id, int):
                domain.append(('res_id', '=', res_id))
            items = self.env['ir.attachment'].sudo().search(domain)
            rule._execute_firebase_sync(items, 'write')

    def _get_data_base_automation(self, operation):
        return {
            'name': "Firebase Storage {} {}".format(str(self.id), operation),
            'active': True,
            'trigger': operation,
            'model_id': self.env.ref('base.model_ir_attachment').id,
            'filter_domain': self.domain,
            'state': 'code',
            'code': "env['firebase.storage'].browse({}).base_automation_trigger(records, '{}')".format(
                str(self.id),
                operation
            )
        }

    def _create_base_automation(self, operation):
        self.ensure_one()
        BaseAutoObj = self.env['base.automation'].sudo()
        data = self._get_data_base_automation(operation)
        return BaseAutoObj.create(data)

    def check_automation(self):
        self.ensure_one()
        if not self.create_automation_id:
            self.create_automation_id = self._create_base_automation('on_create')
        else:
            self.create_automation_id.write(self._get_data_base_automation('on_create'))

        if not self.write_automation_id:
            self.write_automation_id = self._create_base_automation('on_write')
        else:
            self.write_automation_id.write(self._get_data_base_automation('on_write'))

        if not self.unlink_automation_id:
            self.unlink_automation_id = self._create_base_automation('on_unlink')
        else:
            self.unlink_automation_id.write(self._get_data_base_automation('on_unlink'))

    def _execute_firebase_sync(self, records, operation=False):
        self.ensure_one()
        StorageItem = self.env['firebase.storage.item'].sudo()
        rule = self.sudo()
        for attach in records:
            item = StorageItem.search([
                ('rule_id', '=', self.id),
                ('attachment_id', '=', attach.id)
            ], limit=1)
            if not item:
                item = StorageItem.create({
                    'rule_id': self.id,
                    'attachment_id': attach.id,
                    'name': attach.name
                })
            log = self.env['firebase.log'].sudo().create({
                'model_name': item._name,
                'path_name': rule.path,
                'res_id': item.id,
                'path_id': str(item.id),
                'operation_type': operation,
                'is_processed': False,
            })
            if operation == 'create':
                item._send_attachment_to_gcloud(force_update=False)
            elif operation == 'write':
                item._send_attachment_to_gcloud(force_update=True)
            elif operation == 'unlink':
                item._delete_attachment_from_gcloud()
            log.write({
                'is_processed': True,
                'date_processed': fields.Datetime.now()
            })

    def base_automation_trigger(self, records, operation):
        self.ensure_one()
        if not self.active:
            return False
        if operation == 'on_create':
            self._execute_firebase_sync(records, operation='create')
        if operation == 'on_write':
            self._execute_firebase_sync(records, operation='write')
        if operation == 'on_unlink':
            self._execute_firebase_sync(records, operation='unlink')

    @api.model
    def create(self, vals_list):
        res = super(OdooFirebaseAttachment, self).create(vals_list)
        res.check_automation()
        return res

    def write(self, vals):
        res = super(OdooFirebaseAttachment, self).write(vals)
        for item in self:
            item.check_automation()
        return res
