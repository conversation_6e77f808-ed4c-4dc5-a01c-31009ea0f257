# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* portal
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Lasse L, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:55+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Lasse L, 2023\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:0
#, python-format
msgid "%1d days overdue"
msgstr "%1d dagar försenad"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "%s is not the reference of a report"
msgstr "%s är inte referensen till en rapport"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "<i class=\"fa fa-arrow-right mr-1\"/>Back to edit mode"
msgstr "<i class=\"fa fa-arrow-right mr-1\"/>tillbaka till redigering"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid ""
"<i class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""
"<i class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid "<i class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr "<i class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "<i class=\"fa fa-pencil mx-1\"/>Edit Security Settings"
msgstr "<i class=\"fa fa-pencil mx-1\"/>Redigera säkerhetsinställningarna"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "<i class=\"fa fa-pencil\"/> Edit"
msgstr "<i class=\"fa fa-pencil\"/> Ändra"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"
msgstr "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Land...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "<option value=\"\">select...</option>"
msgstr "<option value=\"\">välja...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid ""
"<small class=\"form-text text-muted\">Changing company name or VAT number is"
" not allowed once document(s) have been issued for your account. <br/>Please"
" contact us directly for this operation.</small>"
msgstr ""
"<small class=\"form-text text-muted\">Det är inte tillåtet att ändra "
"företagsnamn eller momsregistreringsnummer när dokument har utfärdats för "
"ditt konto. <br/>Vänligen kontakta oss direkt för denna åtgärd.</small>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Filter By:</span>"
msgstr "<span class=\"small mr-1 navbar-text\">Filtrera med:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Group By:</span>"
msgstr "<span class=\"small mr-1 navbar-text\">Gruppera på:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Sort By:</span>"
msgstr "<span class=\"small mr-1 navbar-text\">Sortera med:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "<strong>Open </strong>"
msgstr "<strong>Öppna </strong>"

#. module: portal
#: model:mail.template,body_html:portal.mail_template_data_portal_welcome
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.user_id.name or ''\">Marc Demo</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.user_id.name or ''\">Marc Demo</t>,<br/> <br/>\n"
"                        Welcome to <t t-out=\"object.user_id.company_id.name\">YourCompany</t>'s Portal!<br/><br/>\n"
"                        An account has been created for you with the following login: <t t-out=\"object.user_id.login\">demo</t><br/><br/>\n"
"                        Click on the button below to pick a password and activate your account.\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.user_id.signup_url\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>Activate Account</strong>\n"
"                            </a>\n"
"                            <a href=\"/web/login\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px;\">\n"
"                                <strong>Log in</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-out=\"object.wizard_id.welcome_message or ''\">Welcome to our company's portal.</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "API Key Ready"
msgstr "API-nyckel redo"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_signature.js:0
#, python-format
msgid "Accept & Sign"
msgstr "Acceptera & Signera"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_warning
#: model:ir.model.fields,field_description:portal.field_portal_share__access_warning
msgid "Access warning"
msgstr "Åtkomstvarning"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "Account Security"
msgstr "Kontosäkerhet"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add a note"
msgstr "Lägg till notering"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Add attachment"
msgstr "Lägg till bilaga"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add contacts to share the document..."
msgstr "Lägg till kontakter att dela dokument med..."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_share__note
msgid "Add extra content to display in the email"
msgstr "Lägg till extra innehåll i e-posten"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Added On"
msgstr "Tillagt den"

#. module: portal
#: code:addons/portal/controllers/mail.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "En åtkomsttoken måste anges för respektive dokument."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
#, python-format
msgid "Cancel"
msgstr "Avbryt"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Change Password"
msgstr "Ändra lösenord"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
#, python-format
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Att ändra momsregistreringsnummer är inte tillåtet när ditt konto har "
"tilldelats dokument. Vänligen kontakta oss direkt för detta."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Det är inte tillåtet att ändra företagsnamn när dokument har utfärdats för "
"ditt konto. Kontakta oss direkt för denna åtgärd."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Check failed"
msgstr "Kontroll misslyckades"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "City"
msgstr "Stad"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_signature.xml:0
#, python-format
msgid "Click here to see your document."
msgstr "Klicka här för att se ditt dokument"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
#, python-format
msgid "Close"
msgstr "Stäng"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Company Name"
msgstr "Bolagsnamn"

#. module: portal
#: model:ir.model,name:portal.model_res_config_settings
msgid "Config Settings"
msgstr "Inställningar"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Confirm"
msgstr "Bekräfta"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid ""
"Confirm\n"
"                                <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""
"Bekräfta\n"
"                                <span class=\"fa fa-long-arrow-right\"/>"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Confirm Password"
msgstr "Bekräfta lösenord"

#. module: portal
#: model:ir.model,name:portal.model_res_partner
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__partner_id
msgid "Contact"
msgstr "Kontakt"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Contact Details"
msgstr "Kontaktuppgifter"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Contacts"
msgstr "Kontakter"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_composer.js:0
#, python-format
msgid "Could not save file <strong>%s</strong>"
msgstr "Kunde inte spara fil <strong>%s</strong>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Country"
msgstr "Land"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr ""
"I dagsläget tillgängligt för alla att granska dokumentet. Klicka på begränsa"
" till interna anställda."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr ""
"Just nu begränsat till interna användare. Klicka för att göra det "
"tillgängligt för alla."

#. module: portal
#: model:ir.model.fields,field_description:portal.field_res_config_settings__portal_allow_api_keys
msgid "Customer API Keys"
msgstr "API-nycklar för kunder"

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_mixin__access_url
msgid "Customer Portal URL"
msgstr "URL till kundportal"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "Dear"
msgstr "Bästa"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Delete"
msgstr "Ta bort"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Description"
msgstr "Beskrivning"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "Details"
msgstr "Detaljer"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Developer API Keys"
msgstr "API-nycklar för utvecklare"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Discard"
msgstr "Avbryt"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "Documents"
msgstr "Dokument"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:0
#, python-format
msgid "Due in %1d days"
msgstr "Förfaller till betalning om %1d dagar"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:0
#, python-format
msgid "Due today"
msgstr "Förfaller idag"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Email"
msgstr "E-post"

#. module: portal
#: model:ir.model,name:portal.model_mail_thread
msgid "Email Thread"
msgstr "E-posttråd"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Employees Only"
msgstr "Anställda enbart"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Enter a description of and purpose for the key."
msgstr "Ange en beskrivning av och ett syfte med nyckeln."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Forgot password?"
msgstr "Glömt lösenord?"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Grant Access"
msgstr "Bevilja tillgång"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard
msgid "Grant Portal Access"
msgstr "Bevilja åtkomst till portal"

#. module: portal
#: model:ir.actions.act_window,name:portal.partner_wizard_action
#: model:ir.actions.server,name:portal.partner_wizard_action_create_and_open
msgid "Grant portal access"
msgstr "Bevilja åtkomst till portal"

#. module: portal
#: model:ir.model,name:portal.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-rutt"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid ""
"Here is your new API key, use it instead of a password for RPC access.\n"
"                Your login is still necessary for interactive usage."
msgstr ""
"Här är din nya API-nyckel, använd den istället för ett lösenord för RPC-åtkomst.\n"
"                Din inloggning är fortfarande nödvändig för interaktiv användning."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Home"
msgstr "Hem"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__id
msgid "ID"
msgstr "ID"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Important:"
msgstr "Viktigt:"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Internal User"
msgstr "Intern användare"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Internal Note"
msgstr "Intern notering"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Internal notes are only displayed to internal users."
msgstr "Interna anteckningar visas endast för interna användare."

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "Felaktig epost! Vänligen fyll i en giltig epost."

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "Invalid report type: %s"
msgstr "Ogiltigsvarstyp: %s"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__welcome_message
msgid "Invitation Message"
msgstr "Inbjudningsmeddelande"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_internal
msgid "Is Internal"
msgstr "Är intern"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_portal
msgid "Is Portal"
msgstr "Är Portal"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid ""
"It is very important that this description be clear\n"
"                and complete,"
msgstr ""
"Det är mycket viktigt att denna beskrivning är tydlig\n"
"                och fullständig,"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share____last_update
#: model:ir.model.fields,field_description:portal.field_portal_wizard____last_update
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user____last_update
msgid "Last Modified on"
msgstr "Senast redigerad"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__login_date
msgid "Latest Authentication"
msgstr "Senaste autentiseringen"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Leave a comment"
msgstr "Lämna en kommentar"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.res_config_settings_view_form
msgid "Let your customers create developer API keys"
msgstr "Låt dina kunder skapa API-nycklar för utvecklare"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__share_link
msgid "Link"
msgstr "Länk"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid "Logout"
msgstr "Logga ut"

#. module: portal
#: model:ir.model,name:portal.model_mail_message
msgid "Message"
msgstr "Meddelande"

#. module: portal
#: code:addons/portal/models/mail_thread.py:0
#, python-format
msgid ""
"Model %(model_name)s does not support token signature, as it does not have "
"%(field_name)s field."
msgstr ""
"Model %(model_name)s stöder inte symbolisk signatur så den har "
"%(field_name)sfält."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.my_account_link
msgid "My Account"
msgstr "Mitt konto"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Name"
msgstr "Namn"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Name your key"
msgstr "Namnge din nyckel"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
#, python-format
msgid "New API Key"
msgstr "Ny API-nyckel"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New Password:"
msgstr "Nytt lösenord:"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: model_terms:ir.ui.view,arch_db:portal.pager
#, python-format
msgid "Next"
msgstr "Nästa"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__note
msgid "Note"
msgstr "Anteckning"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Odoo Logo"
msgstr "Odoo Logo"

#. module: portal
#: code:addons/portal/models/res_users_apikeys_description.py:0
#, python-format
msgid "Only internal and portal users can create API keys"
msgstr "Endast interna användare och portalanvändare kan skapa API-nycklar"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr ""
"Hoppan! Nu gick något fel! Försök att ladda om sidan och logga in igen."

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__partner_ids
msgid "Partners"
msgstr "Kontakter"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password Updated!"
msgstr "Lösenord uppdaterat!"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password:"
msgstr "Lösenord:"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Phone"
msgstr "Telefon"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Please enter your password to confirm you own this account"
msgstr "Ange ditt lösenord för att bekräfta att du äger detta konto"

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
#, python-format
msgid "Portal Access Management"
msgstr "Hantering av portalåtkomst"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_url
msgid "Portal Access URL"
msgstr "URL till portal"

#. module: portal
#: model:ir.model,name:portal.model_portal_mixin
msgid "Portal Mixin"
msgstr "Portal mixin"

#. module: portal
#: model:ir.model,name:portal.model_portal_share
msgid "Portal Sharing"
msgstr "Portaldelning"

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard_user
msgid "Portal User Config"
msgstr "Konfigurerings portalanvändare"

#. module: portal
#: model:mail.template,name:portal.mail_template_data_portal_welcome
msgid "Portal: User Invite"
msgstr "Portal: Inbjudan till användare"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Powered by"
msgstr "Drivs med"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid "Prev"
msgstr "Föreg"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Previous"
msgstr "Föregående"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_chatter.js:0
#, python-format
msgid "Published on %s"
msgstr "Publicerad på %s"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Re-Invite"
msgstr "Bjud in igen"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__partner_ids
msgid "Recipients"
msgstr "Mottagare"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__resource_ref
msgid "Related Document"
msgstr "Relaterade dokument"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_id
msgid "Related Document ID"
msgstr "ID relaterat dokument"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_model
msgid "Related Document Model"
msgstr "Modell för relaterade dokument"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Revoke Access"
msgstr "Återkalla åtkomst"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Scope"
msgstr "Omfattning"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Search"
msgstr "Sök"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Security"
msgstr "Säkerhet"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Security Control"
msgstr "Säkerhetskontroll"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_token
msgid "Security Token"
msgstr "Säkerhetstoken"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"Select which contacts should belong to the portal in the list below.\n"
"                        The email address of each selected contact must be valid and unique.\n"
"                        If necessary, you can fix any contact's email address directly in the list."
msgstr ""
"Välj vilka kontakter som ska tillhöra portalen i listan nedan.\n"
"E-postadressen till varje vald kontakt måste vara giltig och unik.\n"
"Om det behövs kan du fixa någon kontakts e-postadress direkt i listan."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
#, python-format
msgid "Send"
msgstr "Skicka"

#. module: portal
#: model:ir.actions.act_window,name:portal.portal_share_action
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Share Document"
msgstr "Dela dokument"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_ir_ui_view__customize_show
msgid "Show As Optional Inherit"
msgstr "Visa som extra arv"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_sign_in
msgid "Sign in"
msgstr "Logga in"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_composer.js:0
#, python-format
msgid ""
"Some fields are required. Please make sure to write a message or attach a "
"document"
msgstr ""
"Vissa fält är obligatoriska. Se till att skriva ett meddelande eller bifoga "
"ett dokument"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "Some required fields are empty."
msgstr "Vissa nödvändiga fält är toma."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "State / Province"
msgstr "Län"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Street"
msgstr "Gata"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_signature.xml:0
#, python-format
msgid "Thank You!"
msgstr "Tack!"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "The attachment %s cannot be removed because it is linked to a message."
msgstr ""
"Bilagan %s kan inte raderas eftersom den är länkad till ett meddelande."

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The attachment %s cannot be removed because it is not in a pending state."
msgstr "Bilagan %s kan inte raderas eftersom den är i ett väntande tillstånd."

#. module: portal
#: code:addons/portal/controllers/mail.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "Bilagan %s finns inte eller så har du inte åtkomst till den."

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The attachment does not exist or you do not have the rights to access it."
msgstr ""
"Bilagan existerar inte eller så har du inte rätt rättigheter till den."

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The contact \"%s\" does not have a valid email."
msgstr "Kontakten \"%s\" har inte ett giltigt e-postmeddelande."

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The contact \"%s\" has the same email has an existing user (%s)."
msgstr ""

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The document does not exist or you do not have the rights to access it."
msgstr "Dokumentet finns inte eller så saknar du rättigheter till det."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "The key cannot be retrieved later and provides"
msgstr "Nyckeln kan inte återskapas senare och ger"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "Det nya lösenordet och bekräftelsen måste vara lika."

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr ""
"Det gamla lösenordet du angav är ej korrekt, ditt lösenord ändrades inte."

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The partner \"%s\" already has the portal access."
msgstr "Partnern \"%s\" har redan tillgång till portalen."

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The partner \"%s\" has no portal access."
msgstr ""

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid ""
"The template \"Portal: new user\" not found for sending email to the portal "
"user."
msgstr ""
"Mallen \"Portal: ny användare\" hittades inte för att skicka e-post till "
"portalanvändaren."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "There are no comments for now."
msgstr "Det finns inga kommentarer just nu."

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "This document does not exist."
msgstr "Dokumentet finns inte."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "This is a preview of the customer portal."
msgstr "Detta är en förhandsvisning av kundportalen"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This partner is linked to an internal User and already has access to the "
"Portal."
msgstr ""
"Denna partner är kopplad till en intern användare och har redan tillgång "
"till portalen."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This text is included at the end of the email sent to new portal users."
msgstr ""
"Denna text ingår i slutet av det e-postmeddelande som skickas till nya "
"portalanvändare."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_wizard__welcome_message
msgid "This text is included in the email sent to new users of the portal."
msgstr ""
"Denna text inkluderas i det e-post som skickas till nya användaren av "
"portalen."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Toggle filters"
msgstr "Växla filter"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__user_id
msgid "User"
msgstr "Användare"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__user_ids
msgid "Users"
msgstr "Användare"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "VAT Number"
msgstr "Skatteregisteringsnummer"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Verify New Password:"
msgstr "Ange nytt lösenord:"

#. module: portal
#: model:ir.model,name:portal.model_ir_ui_view
msgid "View"
msgstr "Visa"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Visible"
msgstr "Synlig"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_contract__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_department__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_employee__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_job__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_leave__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_leave_allocation__website_message_ids
#: model:ir.model.fields,field_description:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_channel__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,field_description:portal.field_note_note__website_message_ids
#: model:ir.model.fields,field_description:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_product__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_template__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_users__website_message_ids
msgid "Website Messages"
msgstr "Webbplatsmeddelanden"

#. module: portal
#: model:ir.model.fields,help:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,help:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_contract__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_department__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_employee__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_job__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_leave__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_leave_allocation__website_message_ids
#: model:ir.model.fields,help:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_channel__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,help:portal.field_note_note__website_message_ids
#: model:ir.model.fields,help:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_product__website_message_ids
#: model:ir.model.fields,help:portal.field_product_template__website_message_ids
#: model:ir.model.fields,help:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,help:portal.field_res_users__website_message_ids
msgid "Website communication history"
msgstr "Webbplatsens kommunikationshistorik"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "What's this key for?"
msgstr "Vad är denna nyckel till?"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__wizard_id
msgid "Wizard"
msgstr "Guide"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Write a message..."
msgstr "Skriv ett meddelande..."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Write down your key"
msgstr "Skriv ner din nyckel"

#. module: portal
#: code:addons/portal/wizard/portal_share.py:0
#: code:addons/portal/wizard/portal_share.py:0
#, python-format
msgid "You are invited to access %s"
msgstr "Du har getts åtkomst till %s"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "You cannot leave any password empty."
msgstr "Du kan inte ange ett tomt lösenord"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "You have been invited to access the following document:"
msgstr "Du har bjudits in att följa dokumentet:"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "You must be"
msgstr "Du måste vara"

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "You should first grant the portal access to the partner \"%s\"."
msgstr "Du bör först ge portalen åtkomst till partnern \"%s\"."

#. module: portal
#: model:mail.template,subject:portal.mail_template_data_portal_welcome
msgid "Your account at {{ object.user_id.company_id.name }}"
msgstr "Ditt konto på {{ object.user_id.company_id.name }}"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_contact
msgid "Your contact"
msgstr "Din kontakt"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Zip / Postal Code"
msgstr "Postnummer"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "avatar"
msgstr "avatar"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "comment"
msgstr "kommentar"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "comments"
msgstr "kommentarer"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "full access"
msgstr "full tillgång"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid ""
"it will be the only way to\n"
"                identify the key once created"
msgstr ""
"det kommer att vara det enda sättet att\n"
"                identifiera nyckeln när den har skapats"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "logged in"
msgstr "inloggad"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "odoo"
msgstr "odoo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "password"
msgstr "lösenord"

#. module: portal
#: model:ir.model,name:portal.model_res_users_apikeys_description
msgid "res.users.apikeys.description"
msgstr "res.users.apikeys.description"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "to post a comment."
msgstr "för att kommentera"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "to your user account, it is very important to store it securely."
msgstr ""
"till ditt användarkonto är det mycket viktigt att du förvarar den på ett "
"säkert sätt."
