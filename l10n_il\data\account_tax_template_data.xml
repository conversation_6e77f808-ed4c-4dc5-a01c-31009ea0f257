<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Sales Taxes -->
    <record id="il_vat_sales_17" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="description">17%</field>
        <field name="name">VAT Sales</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_out_base')]
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_111110'),
                'plus_report_line_ids': [ref('account_tax_report_line_out_balance')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_out_base')]
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_111110'),
                'minus_report_line_ids': [ref('account_tax_report_line_out_balance')]
            }),
        ]"/>
    </record>

    <record id="il_vat_pa_sales_17" model="account.tax.template">
        <field name="sequence">8</field>
        <field name="description">17%</field>
        <field name="name">VAT PA Sales</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_out_base')]
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_111120'),
                'plus_report_line_ids': [ref('account_tax_report_line_out_balance_pa')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_out_base')]
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_111120'),
                'minus_report_line_ids': [ref('account_tax_report_line_out_balance_pa')]
            }),
        ]"/>
    </record>

    <record id="il_vat_sales_exempt" model="account.tax.template">
        <field name="sequence">9</field>
        <field name="description">0%</field>
        <field name="name">VAT exempt sales</field>
        <field name="price_include" eval="1"/>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_exempt"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_out_base_exempt')]
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_111110'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_out_base_exempt')]
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_111110'),
            }),
        ]"/>
    </record>

    <record id="il_vat_self_inv_purchase" model="account.tax.template">
        <field name="sequence">10</field>
        <field name="description">17%</field>
        <field name="name">Self Invoice</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_out_base')]
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
                'plus_report_line_ids': [ref('account_tax_report_line_in_balance_17')]
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_111110'),
                'minus_report_line_ids': [ref('account_tax_report_line_out_balance')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_out_base')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
                'minus_report_line_ids': [ref('account_tax_report_line_in_balance_17')]
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_111110'),
                'plus_report_line_ids': [ref('account_tax_report_line_out_balance')]
            }),
        ]"/>
    </record>

    <!-- Purchase Taxes -->
    <record id="il_vat_inputs_17" model="account.tax.template">
        <field name="sequence">2</field>
        <field name="description">17%</field>
        <field name="name">VAT inputs</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
                'plus_report_line_ids': [ref('account_tax_report_line_in_balance_17')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
                'minus_report_line_ids': [ref('account_tax_report_line_in_balance_17')]
            }),
        ]"/>
    </record>

    <record id="il_vat_pa_purchase_16" model="account.tax.template">
        <field name="sequence">3</field>
        <field name="description">16%</field>
        <field name="name">VAT 16% (PA)</field>
        <field name="amount">16</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_16"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101340'),
                'plus_report_line_ids': [ref('account_tax_report_line_in_balance_pa_16')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101340'),
                'minus_report_line_ids': [ref('account_tax_report_line_in_balance_pa_16')]
            }),
        ]"/>
    </record>

    <record id="il_vat_inputs_2_3_17" model="account.tax.template">
        <field name="sequence">4</field>
        <field name="description">17%</field>
        <field name="name">VAT Inputs 2/3</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 66.67,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
                'plus_report_line_ids':[ ref('account_tax_report_line_in_balance_2_3')]
            }),
            (0,0, {
                'factor_percent': 33.33,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 66.67,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
                'minus_report_line_ids':[ ref('account_tax_report_line_in_balance_2_3')]
            }),
            (0,0, {
                'factor_percent': 33.33,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="il_vat_inputs_1_4_17" model="account.tax.template">
        <field name="sequence">5</field>
        <field name="description">17%</field>
        <field name="name">VAT Inputs 1/4</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 75,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
                'plus_report_line_ids':[ ref('account_tax_report_line_in_balance_1_4')]
            }),
            (0,0, {
                'factor_percent': 25,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 75,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
                'minus_report_line_ids':[ ref('account_tax_report_line_in_balance_1_4')]
            }),
            (0,0, {
                'factor_percent': 25,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="il_vat_inputs_fa_17" model="account.tax.template">
        <field name="sequence">6</field>
        <field name="description">17%</field>
        <field name="name">VAT inputs for fixed assets</field>
        <field name="amount">17</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_17"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101320'),
                'plus_report_line_ids':[ref('account_tax_report_line_vat_in_fa')]
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101320'),
                'minus_report_line_ids':[ref('account_tax_report_line_vat_in_fa')]
            }),
        ]"/>
    </record>

    <record id="il_vat_purchase_exempt" model="account.tax.template">
        <field name="sequence">7</field>
        <field name="description">0%</field>
        <field name="name">VAT exempt purchase</field>
        <field name="amount">0</field>
        <field name="price_include" eval="1"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_exempt_purchase"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
            }),
        ]"/>
    </record>


    <record id="il_vat_only_purchase" model="account.tax.template">
        <field name="sequence">17</field>
        <field name="description">0%</field>
        <field name="name">VAT Import Line</field>
        <field name="description">VAT Import Line</field>
        <field name="amount">100</field>
        <field name="amount_type">division</field>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_17"/>
        <field name="price_include" eval="True"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101840'),
                'plus_report_line_ids': [ref('account_tax_report_line_in_balance_17')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101840'),
                'minus_report_line_ids': [ref('account_tax_report_line_in_balance_17')],
            }),
        ]"/>
    </record>

    <record id="il_vat_purchase_zero" model="account.tax.template">
        <field name="sequence">7</field>
        <field name="description">0%</field>
        <field name="name">VAT Zero</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="price_include" eval="1"/>
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_exempt_purchase"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_101310'),
            }),
        ]"/>
    </record>

    <record id="il_vat_sales_zero" model="account.tax.template">
        <field name="sequence">9</field>
        <field name="description">0%</field>
        <field name="name">VAT Zero</field>
        <field name="price_include" eval="1"/>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="chart_template_id" ref="il_chart_template"/>
        <field name="tax_group_id" ref="tax_group_vat_exempt"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('account_tax_report_line_out_base_exempt')]
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_111110'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('account_tax_report_line_out_base_exempt')]
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('il_account_111110'),
            }),
        ]"/>
    </record>
</odoo>
