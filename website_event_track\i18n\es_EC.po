# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_event_track
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-02-22 05:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_count_sponsor
msgid "# Sponsors"
msgstr "# Patrocinadores"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "<b>Date</b><br/>"
msgstr "<b>Fecha</b><br/>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Mail</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Phone</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
#, fuzzy
msgid "<b>Proposed By</b>:"
msgstr "Propuesto por"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
#, fuzzy
msgid "<b>Speakers Biography</b>:"
msgstr "Biografías de Expositores"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
#, fuzzy
msgid "<b>Talk Introduction</b>:"
msgstr "Introducción de la charla"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
#, fuzzy
msgid "<b>Title</b>:"
msgstr "<b>Fecha</b><br/>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid ""
"<br/>\n"
"                            <b>Duration</b><br/>"
msgstr ""
"<br/>\n"
"                            <b>Duración</b><br/>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid ""
"<br/>\n"
"                            <b>Location</b><br/>"
msgstr ""
"<br/>\n"
"                            <b>Locación</b><br/>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda
msgid "<span id=\"search_number\">0</span> Found"
msgstr "<span id=\"search_number\">0</span> Encontrados"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Lightning Talks</strong>. These are 30 minutes talks on many\n"
"                                    different topics. Most topics are "
"accepted in lightning talks."
msgstr ""
"<strong> Conferencias básicas </strong>. Son 30 minutos conversaciones "
"sobre \n"
"diferentes temas. La mayoría de los temas son aceptados en charlas relámpago."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid "<strong>Participate on Twitter</strong>"
msgstr "<strong>Participar en Twitter</strong>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Regular Talks</strong>. These are standard talks with slides,\n"
"                                    alocated in slots of 60 minutes."
msgstr ""
"<strong>Conversaciones Normales</strong>. Son conversaciones normales con "
"presentaciones, asignadas en espacios de 60 minutos."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track7
msgid ""
"A technical explanation of Odoo as a CMS and a eCommerce platform for "
"version 8."
msgstr ""
"Una explicación técnica de Odoo como un CMS y una plataforma de comercio "
"electrónico para la versión 9."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "About The Author"
msgstr "Sobre el autor"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track19
msgid "Advanced lead management with Odoo: tips and tricks from the fields"
msgstr "Gestión avanzada de iniciativas con Odoo: trucos y consejos"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track13
msgid "Advanced reporting with Google Spreadsheets integration."
msgstr ""
"Informes avanzados con la integración con las hojas de cálculo de Google."

#. module: website_event_track
#: code:addons/website_event_track/models/event.py:160
#, python-format
msgid "Agenda"
msgstr "Agenda"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_filter
msgid "All Tags"
msgstr "Todas las etiquetas"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Allow video and audio recording of their\n"
"                                    presentation, for publishing on our "
"website."
msgstr ""
"Permitir grabación del audio y vídeo de su presentación , para publicarlo en "
"nuestro sitio web."

#. module: website_event_track
#: selection:event.track,state:0
msgid "Announced"
msgstr "Anunciado"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Application"
msgstr "Aplicación"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_allowed_track_tag_ids
msgid "Available Track Tags"
msgstr "Etiquetas de Agenda disponibles"

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type1
msgid "Bronze"
msgstr "Bronce"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Call for Proposals"
msgstr "Convocatoria de propuestas"

#. module: website_event_track
#: selection:event.track,state:0
msgid "Cancelled"
msgstr "Cancelada"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid "Click to add a track."
msgstr "Presione para añadir una Agenda"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_color
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_color
msgid "Color Index"
msgstr "Índice de colores"

#. module: website_event_track
#: selection:event.track,state:0
msgid "Confirmed"
msgstr "Confirmado"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_create_date
msgid "Created on"
msgstr "Creado en"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Date"
msgstr "Fecha"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Delete"
msgstr "Suprimir"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track6
msgid ""
"Detailed roadmap of accounting new modules and improvements for version 8."
msgstr ""
"Hoja de ruta detallada de los nuevos módulos de contabilidad y mejoras para "
"la versión 8."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track8
msgid ""
"Discover Odoo CRM: How to optimize your sales, from leads to sales orders."
msgstr ""
"Descubra Odoo CRM: Cómo optimizar sus ventas, desde iniciativas a los "
"pedidos de venta."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track11
msgid "Discover Odoo Point-of-Sale: Your shop ready to use in 30 minutes."
msgstr "Descubra el POS de Odoo: Su tienda lista para usar en 30 minutos."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "Documents"
msgstr "Documentos"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_duration
msgid "Duration"
msgstr "Duración"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Edit Track"
msgstr "Editar sesión"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_event
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_event_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_event_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event"
msgstr "Evento"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "Event Location"
msgstr "Localización del evento"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_location
msgid "Event Locations"
msgstr "Localizaciones del evento"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_sponsor_type_tree
msgid "Event Sponsor Type"
msgstr "Tipo de patrocinador del evento"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_sponsor_type_form
msgid "Event Sponsor Types"
msgstr "Tipos de patrocinador del evento"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_sponsor_search
msgid "Event Sponsors"
msgstr "Auspiciantes del evento"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tree
msgid "Event Track"
msgstr "Sesión del evento"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_tree
msgid "Event Track Tag"
msgstr "Etiqueta de sesión del evento"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_sponsor_from_event
#: model:ir.actions.act_window,name:website_event_track.action_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_from_event
#: model:ir.ui.menu,name:website_event_track.menu_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_calendar
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event Tracks"
msgstr "Sesiones del evento"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Fill this form to propose your talk."
msgstr "Rellenar este formulario para proponer su charla."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda
msgid "Filter Tracks..."
msgstr "Filtrar sesiones..."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid ""
"Find out what people see and say about this event, \n"
"                        and join the conversation."
msgstr ""
"Encuentre qué personas ven y hablan sobre este evento, y únase a la "
"conversación."

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type3
msgid "Gold"
msgstr "Oro"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website_event_track
#: selection:event.track,priority:0
msgid "High"
msgstr "Alta"

#. module: website_event_track
#: selection:event.track,priority:0
msgid "Highest"
msgstr "La más alta"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track18
msgid ""
"How to build your marketing strategy for the purpose of generating leads "
"with Odoo."
msgstr ""
"Cómo construir su estrategia de marketing para el propósito de generar "
"iniciativas con Odoo."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track1
msgid "How to develop a website module."
msgstr "Cómo desarrollar un módulo para el sitio web."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track4
msgid "How to develop automated tests in the Odoo web client."
msgstr "Cómo desarrollar tests automáticos en el cliente web de Odoo."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track3
msgid "How to develop real time apps, the website live chat module explained."
msgstr ""
"Cómo desarrollar aplicaciones en tiempo real, ha explicado el módulo del "
"chat ."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track2
msgid "How to integrate hardware materials with the Odoo point of sale."
msgstr "Cómo integrar materiales hardware en su POS de Odoo."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track9
msgid ""
"How to use Odoo for your HR process: recruitment, leaves management, "
"appraisals, expenses, etc."
msgstr ""
"Cómo usar Odoo para su proceso de RRHH: contratación, gestión de ausencias, "
"valoraciones, gastos, etc."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_id
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_id
msgid "ID"
msgstr "ID (identificación)"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_image
msgid "Image"
msgstr "Imagen"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Introduction"
msgstr "Introducción"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track23
msgid "Key Success factors selling Odoo."
msgstr "Factores clave de éxito vendiendo Odoo."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor___last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type___last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track___last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location___last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: website_event_track
#: model:ir.ui.menu,name:website_event_track.menu_event_track_location
msgid "Locations"
msgstr "Ubicaciones"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_image_medium
msgid "Logo"
msgstr "Logo"

#. module: website_event_track
#: selection:event.track,priority:0
msgid "Low"
msgstr "Baja"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track31
msgid "Lunch"
msgstr "Comidas"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track22
msgid "Manage your KPIs (recomended to openERP partners)."
msgstr "Gestione sus KPIs (recomendado para socios OpenERP)."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track12
msgid "Manage your events with Odoo, the new training modules."
msgstr "Gestione sus eventos con Odoo, los nuevos módulos de formación."

#. module: website_event_track
#: selection:event.track,priority:0
msgid "Medium"
msgstr "Media"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor_image_medium
#: model:ir.model.fields,help:website_event_track.field_event_track_image
msgid ""
"Medium-sized image of this contact. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""
"Imagen mediana de este contacto. Se redimensiona automáticamente a 128x128 "
"px, con el ratio de aspecto preservado. Se usa este campo en las vistas los "
"formularios y en algunas vistas de kanban."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track25
msgid "Merge proposals review, code sprint (entire afternoon)"
msgstr ""
"Revisión de las propuestas de inclusión, sprint de código (toda la mañana)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track24
msgid "Merge proposals review, code sprint (entire day)."
msgstr ""
"Revisión de las propuestas de inclusión, sprint de código (toda el día)"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "More"
msgstr "Más"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track30
msgid "Morning break"
msgstr "Pausa de la mañana"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track20
msgid "New Certification Program (valid from Oct. 2013)."
msgstr "Nuevo programa de certificación (válido desde Oct. 2013)."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track14
msgid "New Paypal modules (portal, handling, installments)."
msgstr "Nuevos módulos Paypal (portal, manejo, cuotas)."

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_event_track
msgid "New Track"
msgstr "Nuevo rastreo"

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_event_track
msgid "New Track Created"
msgstr "Nuevo rastreo creado"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
#, fuzzy
msgid "New track proposal"
msgstr "Nuevo programa propuesto"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks
msgid "No tracks found!"
msgstr "No se han encontrado sesiones"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track15
msgid "Odoo Mobile for Notes, Meetings and Messages."
msgstr "Odoo móvil para notas, reuniones y mensajes."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track28
msgid "Odoo Status & Strategy 2014"
msgstr "Estado y estrategia de Odoo 2014"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track16
msgid "Odoo as your Enterprise Social Network."
msgstr "Odoo como su red social de empresa."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track27
msgid "Odoo in 2014"
msgstr "Odoo en 2014"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor
msgid "Our Sponsors"
msgstr "Nuestros patrocinadores"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_partner_biography
msgid "Partner Biography"
msgstr "Biografía del Expositor"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_partner_email
msgid "Partner Email"
msgstr "Correo electrónico del Expositor"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_partner_name
msgid "Partner Name"
msgstr "Nombre del cliente"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_partner_phone
msgid "Partner Phone"
msgstr "Teléfono empresa"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "Practical Info"
msgstr "Información práctica"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_priority
msgid "Priority"
msgstr "Prioridad"

#. module: website_event_track
#: selection:event.track,state:0
msgid "Proposal"
msgstr "Propuesta"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Proposals are closed!"
msgstr "Las propuestas están ya cerradas"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_partner_id
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "Proposed by"
msgstr "Propuesto por"

#. module: website_event_track
#: selection:event.track,state:0
msgid "Published"
msgstr "Publicado"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid ""
"Put here the list of documents, like slides of\n"
"                            the presentations. Remove the above t-if when\n"
"                            it's implemented."
msgstr ""
"Ponga aquí una lista de documentos, como las transparencias de las "
"presentaciones. Elimine el t-if siguiente cuando se implementa."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track10
msgid "Raising qualitive insights with the survey app"
msgstr ""
"Incrementando la información cualitativa con la aplicación de encuestas"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track21
msgid "Recruiting high skilled talents with Odoo HR apps"
msgstr "Contratando grandes talentos con las aplicaciones de RRHH de Odoo"

#. module: website_event_track
#: selection:event.track,state:0
msgid "Refused"
msgstr "Rechazada"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_user_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Responsible"
msgstr "Responsable"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_id_8478
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_name
msgid "Room"
msgstr "Sala"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_show_tracks
msgid "Show Tracks on Website"
msgstr "Mostrar agenda en el sitio web"

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type2
msgid "Silver"
msgstr "Plata"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid "Social Stream"
msgstr "Flujo social"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Speaker Biography"
msgstr "Biografía del ponente"

#. module: website_event_track
#: code:addons/website_event_track/models/event.py:116
#: model:ir.model.fields,field_description:website_event_track.field_event_track_speaker_ids
#, python-format
msgid "Speakers"
msgstr "Ponentes"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_name
msgid "Sponsor Type"
msgstr "Tipo de patrocinador"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_sponsor_type
#: model:ir.ui.menu,name:website_event_track.menu_event_sponsor_type
msgid "Sponsor Types"
msgstr "Tipos de patrocinadores"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_url
msgid "Sponsor Website"
msgstr "Sitio web del patrocinador"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_partner_id
msgid "Sponsor/Customer"
msgstr "Patrocinador/Cliente"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_sponsor_type_id
msgid "Sponsoring Type"
msgstr "Tipo de patrocinio"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_sponsor_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_form
msgid "Sponsors"
msgstr "Patrocinadores"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_state
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Status"
msgstr "Estado"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submission Agreement"
msgstr "Acuerdo de presentación"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submit Proposal"
msgstr "Enviar propuesta"

#. module: website_event_track
#: model:web.tip,description:website_event_track.event_track_tip_1
msgid "Switch to the calendar view to see the availability of each room."
msgstr ""
"Cambie a la vista del calendario para ver la disponibilidad de cada salón."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_name
msgid "Tag"
msgstr "Etiqueta"

#. module: website_event_track
#: sql_constraint:event.track.tag:0
msgid "Tag name already exists !"
msgstr "¡El nombre de etiqueta ya existe!"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Tags"
msgstr "Etiquetas"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talk Introduction"
msgstr "Introducción de la charla"

#. module: website_event_track
#: code:addons/website_event_track/models/event.py:162
#, python-format
msgid "Talk Proposals"
msgstr "Propuestas de charlas"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talk Title"
msgstr "Título de la charla"

#. module: website_event_track
#: code:addons/website_event_track/models/event.py:159
#, python-format
msgid "Talks"
msgstr "Charlas"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talks Types"
msgstr "Tipos de charlas"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_success
msgid "Thank you for your proposal."
msgstr "Gracias por su propuesta."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track17
msgid "The Art of Making an Odoo Demo."
msgstr "El arte de hacer una demo de Odoo."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track29
msgid "The new marketing strategy."
msgstr "La nueva estrategia de marketing"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track5
msgid ""
"The new way to promote your modules in the Apps platform and Odoo website."
msgstr ""
"La nueva forma de promover sus módulos en la plataforma de aplicaciones y el "
"sitio web de Odoo."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "This event does not accept proposals."
msgstr "Este evento no acepta propuestas."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Timely release of presentation material (slides),\n"
"                                    for publishing on our website."
msgstr ""
"Publicación temporalizada de material (diapositivas), para publicar en "
"nuestro sitio web."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_name
msgid "Title"
msgstr "Título"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Track"
msgstr "Sesión"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_date
msgid "Track Date"
msgstr "Fecha de la sesión"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_description
msgid "Track Description"
msgstr "Descripción de la sesión"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_location
msgid "Track Location"
msgstr "Rastrear ubicación"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag
msgid "Track Tag"
msgstr "Etiqueta de Agenda"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_tag
#: model:ir.model.fields,field_description:website_event_track.field_event_event_tracks_tag_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track_tag
msgid "Track Tags"
msgstr "Etiquetas de la sesión"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_count_tracks
#: model:ir.model.fields,field_description:website_event_track.field_event_event_track_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_track_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_graph
msgid "Tracks"
msgstr "Sesiones"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_show_track_proposal
msgid "Tracks Proposals"
msgstr "Propuestas de Agenda"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid ""
"Tracks define the agenda of your event. These can be a talk, a round table, "
"a meeting, etc."
msgstr ""
"Etapas que definen la agenda de su evento. Estos pueden ser una charla, una "
"mesa redonda, una reunión, etc."

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
msgid ""
"Tracks define the agenda of your event. These can bea talk, a round table, a "
"meeting, etc."
msgstr ""
"Etapas que definen la agenda de su evento. Estos pueden ser una charla, una "
"mesa redonda, una reunión, etc."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid "Use this tag:"
msgstr "Utilice esta etiqueta:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "View Track"
msgstr "Ver sesión"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We require speakers to accept an agreement in which they commit to:"
msgstr ""
"Requerimos ponentes para aceptar un acuerdo en el que se comprometen a:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"We will accept a broad range of\n"
"                                presentations, from reports on academic and\n"
"                                commercial projects to tutorials and case\n"
"                                studies. As long as the presentation is\n"
"                                interesting and potentially useful to the\n"
"                                audience, it will be considered for\n"
"                                inclusion in the programme."
msgstr ""
"Aceptaremos un amplio rango de presentaciones, desde informes en proyectos "
"comerciales o académicos, a tutoriales y casos de uso, mientras que la "
"presentación sea interesante y potencialmente útil a la audiencia, será "
"considerado para la inclusión en el programa."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_success
msgid "We will evaluate your proposition and get back to you shortly."
msgstr "Evaluaremos su propuesta y le responderemos en breve."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Your Email"
msgstr "Su correo electrónico"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Your Name"
msgstr "Su nombre"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Your Phone"
msgstr "Su teléfono"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Inspiring Business Talk"
msgstr "por ejemplo: Inspiring Business Talk"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_sponsor
msgid "event.sponsor"
msgstr "event.sponsor"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_sponsor_type
msgid "event.sponsor.type"
msgstr "event.sponsor.type"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "hours"
msgstr "horas"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks
msgid "not published"
msgstr "no publicado"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda
msgid "talks"
msgstr "sesiones"

#~ msgid "Action Needed"
#~ msgstr "Necesaria acción"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del último mensaje publicado en el registro."

#~ msgid "Event Blog"
#~ msgstr "Blog del evento"

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Empresas)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención."

#~ msgid "Is Follower"
#~ msgstr "Es un seguidor"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del último mensaje"

#~ msgid "Mail"
#~ msgstr "Correo electrónico"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Mensajes e historial de comunicación"

#~ msgid "News"
#~ msgstr "Noticias"

#~ msgid "Number of Actions"
#~ msgstr "Número de acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leidos"

#~ msgid "Phone"
#~ msgstr "Teléfono"

#~ msgid "The full URL to access the document through the website."
#~ msgstr "La URL completa para acceder al documento a través de la web."

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de mensajes no leidos"

#~ msgid "Visible in Website"
#~ msgstr "Visible en el sitio web"

#~ msgid "Website Messages"
#~ msgstr "Mensajes del sitio web"

#~ msgid "Website URL"
#~ msgstr "URL del sitio web"

#~ msgid "Website communication history"
#~ msgstr "Historial de comunicaciones del sitio web"

#~ msgid "Website meta description"
#~ msgstr "Meta descripción del sitio web"

#~ msgid "Website meta keywords"
#~ msgstr "Meta palabras clave del sitio web"

#~ msgid "Website meta title"
#~ msgstr "Meta título del sitio web"
