from odoo import models,api
from datetime import datetime, timedelta

class activity(models.Model):
    _name = "hr.contract_expiry_notification"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "HR Contract Expiry Notification"

    
    @api.model
    def cron_contract_expiry_notification(self):
       
        contracts = self.env['hr.contract'].search([])
        for contract in contracts:
            
            date_end = datetime.strptime(contract.date_end.strftime("%Y-%m-%d"), "%Y-%m-%d")

            delta  = date_end - datetime.today()
            if (contract.state == 'open'):
                if (delta.days == 30 or delta.days == 10):
                    _summary = "تنبيه بإنتهاء مدة عقد"
                    _note =  "ستنتهي مدة هذا العقد خلال ({}) يوم.".format(delta.days)
                    #print(contract.name , delta.days)
                    contract.activity_schedule('hr_contract_expiry_notification.mail_activity_contract_expiry', 
                                               user_id=contract.hr_responsible_id.id,
                                               summary = _summary,
                                               note = _note,
                                               date_deadline=datetime.today() )
