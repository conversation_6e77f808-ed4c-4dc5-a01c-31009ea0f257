<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="payroll_expenses_form_wizard" model="ir.ui.view">
            <field name="name">Payroll Expenses</field>
            <field name="model">payroll.expenses</field>
            <field name="arch" type="xml">
                <form string="Payroll Expenses Report">

                    <group>
                        <group>
                            <field name="employee_id"/>
                        </group>
                        <group></group>
                        <group>
                            <field name="bank_id" class="oe_inline"/>
                        </group>
                        <group>
                            <field name="branch_id" class="oe_inline"/>
                        </group>

                        <group>
                            <field name="date_from" class="oe_inline"/>
                        </group>
                        <group>
                            <field name="date_to" class="oe_inline"/>
                        </group>
                    </group>


                    <footer>
                        <button name="generate_payroll_expenses" type="object" string="Print" class="oe_highlight"/>
                        <button type="object" string="Cancel" special="cancel"/>
                    </footer>

                </form>
            </field>
        </record>

        <record id="payroll_expenses_action_wizard" model="ir.actions.act_window">
            <field name="name">حوافظ الإرفاق للمصارف</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">payroll.expenses</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="payroll_expenses_form_wizard"/>
            <field name="target">new</field>
        </record>


        <menuitem id="create_payroll_expenses" name="حوافظ الإرفاق للمصارف"
                  parent="payroll_reports_categ"
                  action="payroll_expenses_action_wizard"
                  sequence="5"/>


    </data>
</odoo>