# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_cl
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-14 12:35+0000\n"
"PO-Revision-Date: 2021-01-14 12:35+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_cl
#: model:ir.model.fields,help:l10n_cl.field_res_partner__l10n_cl_sii_taxpayer_type
#: model:ir.model.fields,help:l10n_cl.field_res_users__l10n_cl_sii_taxpayer_type
msgid ""
"1 - VAT Affected (1st Category) (Most of the cases)\n"
"2 - Fees Receipt Issuer (Applies to suppliers who issue fees receipt)\n"
"3 - End consumer (only receipts)\n"
"4 - Foreigner"
msgstr ""
"1 - IVA Afecto (la mayoría de los casos)\n"
"2 - Emisor Boletas (aplica solo para proveedores emisores de boleta)\n"
"3 - Consumidor Final (se le emitirán siempre boletas)\n"
"4 - Extranjero"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid ""
"<br/>\n"
"\n"
"                <strong>Customer:</strong>"
msgstr ""
"<br/>\n"
"\n"
"                <strong>Cliente:</strong>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.custom_header
msgid ""
"<br/>\n"
"                                            <span style=\"line-height: 180%;\">RUT:</span>"
msgstr ""

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.custom_header
msgid ""
"<br/>\n"
"                                            <span>Nº:</span>"
msgstr ""

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid ""
"<br/>\n"
"                    <strong>Incoterm:</strong>"
msgstr ""

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid ""
"<br/>\n"
"                <strong>Address:</strong>"
msgstr ""
"<br/>\n"
"                <strong>Dirección:</strong>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.report_invoice_document
msgid "<span>Taxes</span>"
msgstr "<span>Impuestos</span>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid "<strong>Due Date:</strong>"
msgstr "<strong>Fecha de vencimiento:</strong>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid "<strong>GIRO:</strong>"
msgstr ""

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid "<strong>Payment Terms:</strong>"
msgstr "<strong>Plazos de pago:</strong>"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plantilla de Plan de Cuentas"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Accounting Date"
msgstr "Fecha contable"

#. module: l10n_cl
#: model:product.product,name:l10n_cl.product_product_ad_valorem
#: model:product.template,name:l10n_cl.product_product_ad_valorem_product_template
msgid "Ad-Valorem"
msgstr "Ad-Valorem"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Amount Due"
msgstr "Monto adeudado"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Amount Untaxed"
msgstr "Monto sin impuestos"

#. module: l10n_cl
#: model:ir.model.fields,help:l10n_cl.field_account_bank_statement_line__l10n_latam_internal_type
#: model:ir.model.fields,help:l10n_cl.field_account_move__l10n_latam_internal_type
#: model:ir.model.fields,help:l10n_cl.field_account_payment__l10n_latam_internal_type
#: model:ir.model.fields,help:l10n_cl.field_l10n_latam_document_type__internal_type
msgid ""
"Analog to odoo account.move.move_type but with more options allowing to "
"identify the kind of document we are working with. (not only related to "
"account.move, could be for documents of other models like stock.picking)"
msgstr ""
"Análogo a account.move.type de Odoo pero con más opciones, permitiendo "
"identificar el tipo de documento sobre el que estamos trabajando. (no "
"solamente relativo a account.move, podría ser relativo a otros modelos, como"
" por ejemplo stock.picking)"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_bar
msgid "BAR"
msgstr "BAR"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_res_bank
msgid "Bank"
msgstr "Banco"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_carton
msgid "CARTON"
msgstr "CARTON"

#. module: l10n_cl
#: model:product.product,description:l10n_cl.product_product_ad_valorem
#: model:product.template,description:l10n_cl.product_product_ad_valorem_product_template
msgid "Cargo para calculo de Ad-Valorem en DIN"
msgstr ""

#. module: l10n_cl
#: model:ir.ui.menu,name:l10n_cl.account_reports_cl_statements_menu
msgid "Chile"
msgstr ""

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.res_config_settings_view_form
msgid "Chilean Localization"
msgstr "Localización Chilena"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_bank__l10n_cl_sbif_code
msgid "Cod. SBIF"
msgstr ""

#. module: l10n_cl
#: model:ir.model.fields,help:l10n_cl.field_account_bank_statement_line__l10n_latam_document_type_id_code
#: model:ir.model.fields,help:l10n_cl.field_account_move__l10n_latam_document_type_id_code
#: model:ir.model.fields,help:l10n_cl.field_account_payment__l10n_latam_document_type_id_code
msgid "Code used by different localizations"
msgstr "Código usado por diferentes localizaciones"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_res_country
msgid "Country"
msgstr "País"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__l10n_latam_document_type__internal_type__credit_note
msgid "Credit Notes"
msgstr "Notas de Crédito"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_res_currency
msgid "Currency"
msgstr "Moneda"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_currency__l10n_cl_currency_code
msgid "Currency Code"
msgstr "Código de moneda"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_country__l10n_cl_customs_abbreviation
msgid "Customs Abbreviation"
msgstr "Abreviatura de aduana"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_country__l10n_cl_customs_code
msgid "Customs Code"
msgstr "Código de aduana"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_country__l10n_cl_customs_name
msgid "Customs Name"
msgstr "Nombre de aduana"

#. module: l10n_cl
#: model:l10n_latam.identification.type,name:l10n_cl.it_DNI
msgid "DNI"
msgstr ""

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid "Date:"
msgstr "Fecha:"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_move_form
msgid "Datos adic. dirección y Ciudad"
msgstr ""

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__l10n_latam_document_type__internal_type__debit_note
msgid "Debit Notes"
msgstr "Notas de Débito"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_account_journal__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_account_tax__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_account_tax_template__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_ir_sequence__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_l10n_latam_document_type__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_res_bank__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_res_company__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_res_country__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_res_currency__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_res_partner__display_name
#: model:ir.model.fields,field_description:l10n_cl.field_uom_uom__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_account_bank_statement_line__l10n_latam_document_type_id_code
#: model:ir.model.fields,field_description:l10n_cl.field_account_move__l10n_latam_document_type_id_code
#: model:ir.model.fields,field_description:l10n_cl.field_account_payment__l10n_latam_document_type_id_code
msgid "Doc Type"
msgstr "T.doc"

#. module: l10n_cl
#: code:addons/l10n_cl/models/account_move.py:0
#, python-format
msgid ""
"Document types for foreign customers must be export type (codes 110, 111 or "
"112) or you                             should define the customer as an end"
" consumer and use receipts (codes 39 or 41)"
msgstr "Los tipos de documento para clientes extranjeros deben ser de exportación. "
"(Códigos 110, 111 o 112) o debe definir al cliente como consumidor final y utilizar "
"recibos (códigos 39 o 41)"

#. module: l10n_cl
#: code:addons/l10n_cl/models/res_partner.py:0
#: model:ir.model.fields.selection,name:l10n_cl.selection__res_partner__l10n_cl_sii_taxpayer_type__3
#, python-format
msgid "End Consumer"
msgstr "Consumidor final"

#. module: l10n_cl
#: model:uom.category,name:l10n_cl.uom_categ_energy
msgid "Energía"
msgstr ""

#. module: l10n_cl
#: code:addons/l10n_cl/models/res_partner.py:0
#: model:ir.model.fields.selection,name:l10n_cl.selection__res_partner__l10n_cl_sii_taxpayer_type__2
#, python-format
msgid "Fees Receipt Issuer (2nd category)"
msgstr "Emisor de boleta 2da categoría"

#. module: l10n_cl
#: code:addons/l10n_cl/models/res_partner.py:0
#: model:ir.model.fields.selection,name:l10n_cl.selection__res_partner__l10n_cl_sii_taxpayer_type__4
#, python-format
msgid "Foreigner"
msgstr "Extranjero"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_hl
msgid "HL"
msgstr ""

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_account_chart_template__id
#: model:ir.model.fields,field_description:l10n_cl.field_account_journal__id
#: model:ir.model.fields,field_description:l10n_cl.field_account_move__id
#: model:ir.model.fields,field_description:l10n_cl.field_account_tax__id
#: model:ir.model.fields,field_description:l10n_cl.field_account_tax_template__id
#: model:ir.model.fields,field_description:l10n_cl.field_ir_sequence__id
#: model:ir.model.fields,field_description:l10n_cl.field_l10n_latam_document_type__id
#: model:ir.model.fields,field_description:l10n_cl.field_res_bank__id
#: model:ir.model.fields,field_description:l10n_cl.field_res_company__id
#: model:ir.model.fields,field_description:l10n_cl.field_res_country__id
#: model:ir.model.fields,field_description:l10n_cl.field_res_currency__id
#: model:ir.model.fields,field_description:l10n_cl.field_res_partner__id
#: model:ir.model.fields,field_description:l10n_cl.field_uom_uom__id
msgid "ID"
msgstr "ID (identificación)"

#. module: l10n_cl
#: model:account.tax.group,name:l10n_cl.tax_group_ila
msgid "ILA"
msgstr ""

#. module: l10n_cl
#: model:account.tax.group,name:l10n_cl.tax_group_iva_19
msgid "IVA 19%"
msgstr ""

#. module: l10n_cl
#: model:ir.model.fields,help:l10n_cl.field_account_bank_statement_line__partner_id_vat
#: model:ir.model.fields,help:l10n_cl.field_account_move__partner_id_vat
#: model:ir.model.fields,help:l10n_cl.field_account_payment__partner_id_vat
msgid "Identification Number for selected type"
msgstr "Número de identificación para el tipo seleccionado"

#. module: l10n_cl
#: model:account.tax.group,name:l10n_cl.tax_group_impuestos_especificos
msgid "Impuestos Específicos"
msgstr ""

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_l10n_latam_document_type__internal_type
msgid "Internal Type"
msgstr "Tipo interno"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__l10n_latam_document_type__internal_type__invoice
msgid "Invoices"
msgstr "Facturas"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Invoices and Refunds"
msgstr "Facturas y notas de crédito"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_account_journal
msgid "Journal"
msgstr "Diario"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_ir_sequence__l10n_cl_journal_ids
msgid "Journals"
msgstr "Diarios"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_knfc
msgid "KNFC"
msgstr ""

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_kwh
msgid "KWH"
msgstr ""

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_account_bank_statement_line__l10n_latam_internal_type
#: model:ir.model.fields,field_description:l10n_cl.field_account_move__l10n_latam_internal_type
#: model:ir.model.fields,field_description:l10n_cl.field_account_payment__l10n_latam_internal_type
msgid "L10n Latam Internal Type"
msgstr "L10n Tipo Interno (Latam)"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_account_chart_template____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_account_journal____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_account_move____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_account_tax____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_account_tax_template____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_ir_sequence____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_l10n_latam_document_type____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_res_bank____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_res_company____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_res_country____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_res_currency____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_res_partner____last_update
#: model:ir.model.fields,field_description:l10n_cl.field_uom_uom____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_l10n_latam_document_type
msgid "Latam Document Type"
msgstr "Tipo Documento (LA)"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.custom_header
msgid "Logo"
msgstr ""

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_mm
msgid "M2/1MM"
msgstr ""

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_mcub
msgid "MCUB"
msgstr ""

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_mkwh
msgid "MKWH"
msgstr ""

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_mt2
msgid "MT2"
msgstr ""

#. module: l10n_cl
#: model:uom.category,name:l10n_cl.uom_categ_others
msgid "Otros"
msgstr ""

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_par
msgid "PAR"
msgstr ""

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unidad de medida del producto"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__l10n_latam_document_type__internal_type__invoice_in
msgid "Purchase Invoices"
msgstr "Facturas de proveedores"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_qmb
msgid "QMB"
msgstr ""

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_qnt
msgid "QNT"
msgstr ""

#. module: l10n_cl
#: model:l10n_latam.identification.type,name:l10n_cl.it_RUN
msgid "RUN"
msgstr ""

#. module: l10n_cl
#: model:l10n_latam.identification.type,name:l10n_cl.it_RUT
msgid "RUT"
msgstr "RUT"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__l10n_latam_document_type__internal_type__receipt_invoice
msgid "Receipt Invoice"
msgstr ""

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_move_form
msgid "Región"
msgstr ""

#. module: l10n_cl
#: model:account.tax.group,name:l10n_cl.tax_group_retenciones
msgid "Retenciones"
msgstr ""

#. module: l10n_cl
#: model:account.tax.group,name:l10n_cl.tax_group_2da_categ
msgid "Retención de 2da Categoría"
msgstr ""

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_sum
#: model:uom.uom,name:l10n_cl.product_uom_sum_99
msgid "S.U.M"
msgstr ""

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_account_tax__l10n_cl_sii_code
#: model:ir.model.fields,field_description:l10n_cl.field_account_tax_template__l10n_cl_sii_code
#: model:ir.model.fields,field_description:l10n_cl.field_uom_uom__l10n_cl_sii_code
msgid "SII Code"
msgstr "Código SII"

#. module: l10n_cl
#: model:ir.actions.act_window,name:l10n_cl.sale_invoices_credit_notes
msgid "Sale Invoices and Credit Notes"
msgstr "Facturas de Venta y Notas de Crédito"

#. module: l10n_cl
#: model:ir.ui.menu,name:l10n_cl.menu_sale_invoices_credit_notes
msgid "Sale Invoices and Credit Notes (CL)"
msgstr "Facturas de Vta y Notas de Crédito (CL)"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Sales Person"
msgstr "Comercial"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_ir_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_currency__l10n_cl_short_name
msgid "Short Name"
msgstr "Nombre corto"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Source Document"
msgstr "Documento origen"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_tmb
msgid "TMB"
msgstr ""

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_tmn
msgid "TMN"
msgstr ""

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_account_tax
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Tax"
msgstr "Impuesto"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_move_form
msgid "Tax Payer Type"
msgstr "Tipo de Contribuyente"

#. module: l10n_cl
#: code:addons/l10n_cl/models/account_move.py:0
#, python-format
msgid ""
"Tax payer type and vat number are mandatory for this type of document. "
"Please set the current tax payer type of this customer"
msgstr ""
"El tipo de contribuyente y el número de RUT son requeridos para este tipo de"
" documento. Por favor establezca un valor para el tipo de contribuyente de "
"este Cliente"

#. module: l10n_cl
#: code:addons/l10n_cl/models/account_move.py:0
#, python-format
msgid ""
"Tax payer type and vat number are mandatory for this type of document. "
"Please set the current tax payer type of this supplier"
msgstr ""
"El tipo de contribuyente y el número de RUT son requeridos para este tipo de"
" documento. Por favor establezca un valor para el tipo de contribuyente de "
"este Proveedor"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_partner__l10n_cl_sii_taxpayer_type
#: model:ir.model.fields,field_description:l10n_cl.field_res_users__l10n_cl_sii_taxpayer_type
msgid "Taxpayer Type"
msgstr "Tipo de Contribuyente"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Plantilla de impuestos"

#. module: l10n_cl
#: code:addons/l10n_cl/models/account_move.py:0
#, python-format
msgid ""
"The DIN document is intended to be used only with RUT ********-0 (Tesorería "
"General de La República)"
msgstr ""
"El documento “declaración de ingreso” (DIN) debe ser usado solamente para "
"“Tesorería General de La República” (RUT ********-0)"

#. module: l10n_cl
#: code:addons/l10n_cl/models/account_move.py:0
#, python-format
msgid ""
"The tax payer type of this supplier is incorrect for the selected type of "
"document."
msgstr ""
"El tipo de contribuyente de este proveedor es incorrecto para el tipo de "
"documento seleccionado."

#. module: l10n_cl
#: code:addons/l10n_cl/models/account_move.py:0
#, python-format
msgid ""
"The tax payer type of this supplier is not entitled to deliver fees "
"documents"
msgstr ""
"El tipo de contribuyente para este proveedor no puede emitir boletas de "
"honorarios"

#. module: l10n_cl
#: code:addons/l10n_cl/models/account_move.py:0
#, python-format
msgid ""
"The tax payer type of this supplier is not entitled to deliver imports "
"documents"
msgstr ""
"El tipo de contribuyente para este proveedor no puede emitir documentos de "
"importación"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Total"
msgstr "Total"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_u
msgid "U(JGO)"
msgstr ""

#. module: l10n_cl
#: code:addons/l10n_cl/models/res_partner.py:0
#: model:ir.model.fields.selection,name:l10n_cl.selection__res_partner__l10n_cl_sii_taxpayer_type__1
#, python-format
msgid "VAT Affected (1st Category)"
msgstr "IVA afecto 1ª categoría"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_account_bank_statement_line__partner_id_vat
#: model:ir.model.fields,field_description:l10n_cl.field_account_move__partner_id_vat
#: model:ir.model.fields,field_description:l10n_cl.field_account_payment__partner_id_vat
msgid "VAT No"
msgstr "RUT Nº"

#. module: l10n_cl
#: model:ir.actions.act_window,name:l10n_cl.vendor_bills_and_refunds
msgid "Vendor Bills and Refunds"
msgstr "Facturas y Notas de Créd de Proveedores"

#. module: l10n_cl
#: model:ir.ui.menu,name:l10n_cl.menu_vendor_bills_and_refunds
msgid "Vendor Bills and Refunds (CL)"
msgstr "Facturas de Proveedor y Notas de Crédito (CL)"

#. module: l10n_cl
#: code:addons/l10n_cl/models/account_move.py:0
#, python-format
msgid "You need a journal without the use of documents for foreign suppliers"
msgstr ""
"Ud. necesita un diario que no use documentos para registrar facturas de "
"proveedores extranjeros"
