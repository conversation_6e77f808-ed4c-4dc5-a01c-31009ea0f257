.o_form_view.o_mass_mailing_mailing_form {

    input.o_field_char.o_field_widget {
        padding-right: 30px; // Avoid overlapping subject text on emoji widget
    }

    .o_notebook .tab-content .tab-pane .o_mail_body {
        // cancel the padding of the form_sheet
        margin-top: -$o-sheet-cancel-hpadding;
        margin-left: -$o-sheet-cancel-hpadding;
        margin-right: -$o-sheet-cancel-hpadding;
        margin-bottom: -40px;
    }
}

@include media-breakpoint-between(lg, xl, $o-extra-grid-breakpoints) {
    .o_form_view .o_form_sheet .o_notebook .tab-content .tab-pane .o_mail_body {
        // cancel the padding of the form_sheet when breakpoints are reached
        margin-left: -$o-sheet-cancel-hpadding*2;
        margin-right: -$o-sheet-cancel-hpadding*2;
    }
}

.o_form_view.o_mass_mailing_mailing_form.o_mass_mailing_form_full_width {
    .o_form_statusbar {
        margin-bottom: 0;

        .o_statusbar_status {
            border-left: solid $o-gray-300 1px;
        }
    }
    .o_mass_mailing_mailing_group {
        margin-top: $o-sheet-cancel-tpadding + 33px;
    }
    .o_FormRenderer_chatterContainer {
        margin: 0;
        max-width: unset;
        width: 100%;
        border: solid $o-gray-300 1px;
    }
    .wysiwyg_iframe {
        border: none;
    }
    .oe_button_box {
        margin-top: unset;

        > .btn.oe_stat_button {
            max-width: 135px;
            border-left: 1px solid $border-color;
        }
        > .o_dropdown_more {
            width: 135px;
        }
    }
    // For large devices, limit the width of subject and adjust the emoji widget accordingly
    @include media-breakpoint-up(md) {
        input.o_field_char.o_field_widget {
            max-width: 86%;
        }
        .o_mail_add_emoji {
            right: 14%;
            bottom: 4px;
        }
    }
}

.o_kanban_view {
    .oe_kanban_mass_mailing {
        .o_title {
            margin-bottom: 16px;
        }
        .o_kanban_primary_bottom {
            margin-top: 16px;
        }
        .oe_margin_top_8 {
            margin-top: 8px;
        }
        .oe_margin_bottom_8 {
            margin-bottom: 8px;
        }
    }
    &.o_kanban_mailing_list {
        .o_kanban_group:not(.o_column_folded) {
            width: 350px + $o-kanban-group-padding;
        }
        .o_kanban_record {
            width: 350px;
        }
    }
}

.o_form_view .o_group.o_inner_group.o_mass_mailing_mailing_group {
    // Used to gain extra space in form.
    display: block;
}

.o_mass_mailing_mailing_form:not(.o_mass_mailing_form_full_width) {
    .alert:not(.o_invisible_modifier) + .clearfix.position-relative.o_form_sheet {
        //hides extra space between form and alert messages
        margin-top: -12px;
    }
}

.o_mass_mailing_mailing_form.o_mass_mailing_form_full_width .alert.alert-info:not(.o_invisible_modifier) {
    margin: 0 0 0 12px;
    flex: 1;
    padding: unset;
    border-left-width: 1px;
    border-left-color: #dee2e6;

    > div {
        min-height: 100%;
        padding: 6px;
    }
}

.o_form_view.o_xxl_form_view.o_mass_mailing_mailing_form {
    // This will hide the chatter scroll bar
    height: initial;
}

.o_form_view {
    // This will display the emoji widget in the right position after a text field with sms option.
    .o_sms_container ~ .o_mail_add_emoji{
        bottom: 55px;
    }
}
.o_white_body {
    background-color: white;
}

.o_mass_mailing_unsubscribed {
    margin-left: 20px;
    color: #005326;
    font-size: 90%;
}

@media only screen and (min-width: 1200px) {
    .o_utm_campaign_mass_mailing_substats {
        padding-right: 210px;
        div {
            margin-left: 50px;
        }
    }
}

@media only screen and (min-width: 768px) and (max-width: 1200px) {
    .o_utm_campaign_mass_mailing_substats {
        padding-right: 180px;
        div {
            margin-left: 30px;
        }
    }
}

