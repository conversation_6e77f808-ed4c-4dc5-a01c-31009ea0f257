# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_quiz
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <r<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_questions_count
msgid "# Quiz Questions"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid ". Try another search."
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.all_visitor_card
msgid "<span class=\"text-muted small font-weight-bold\">Points</span>"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "<span class=\"text-muted\">Points</span>"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_view_form
msgid "Add Quiz"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "All questions must be answered !"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
msgid "Allow multiple tries"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__text_value
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__answer_ids
msgid "Answer"
msgstr "Resposta"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Check answers"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Check your answers"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track_visitor__quiz_completed
msgid "Completed"
msgstr "Concluído"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_0
msgid "Concrete Blocks Wall"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Congratulations, you scored a total of"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz_question
msgid "Content Quiz Question"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__is_correct
msgid "Correct"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__correct_answer_id
msgid "Correct Answer"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Correct."
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__create_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__create_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__create_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__create_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__create_date
msgid "Created on"
msgstr "Criado em"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__display_name
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__display_name
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__display_name
msgid "Display Name"
msgstr "Nome"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_1
msgid "Even if there will be some."
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_0_1
msgid "Even if you have a big trunk, some long products need to be secured."
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_event
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Event"
msgstr "Evento"

#. module: website_event_track_quiz
#: model:ir.actions.act_window,name:website_event_track_quiz.event_quiz_question_action
msgid "Event Quiz Questions"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.actions.act_window,name:website_event_track_quiz.event_quiz_action
msgid "Event Quizzes"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_track
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__event_track_id
msgid "Event Track"
msgstr "Tema do Evento"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__comment
msgid "Extra Comment"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_question_action
msgid ""
"From here you will be able to examine all quiz questions you have linked to "
"Tracks."
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_action
msgid ""
"From here you will be able to overview all quizzes you have linked to "
"Tracks."
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_view_form
msgid "Go to Quiz"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_0
msgid "Hammer"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_1_0
msgid "Hammer won't be of any help here!"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__id
msgid "ID"
msgstr "ID"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_0_0
msgid ""
"In order to avoid accident, you need to secure any product of this kind "
"during transportation!"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Incorrect."
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__is_quiz_completed
msgid "Is Quiz Done"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz____last_update
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer____last_update
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question____last_update
msgid "Last Modified on"
msgstr "Última Modificação em"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__write_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__write_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__write_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__write_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: website_event_track_quiz
#: model:ir.model.fields,help:website_event_track_quiz.field_event_quiz__repeatable
msgid "Let attendees reset the quiz and try again."
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_1_1
msgid "Lumbers need first to be cut from trees!"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Mobile sub-nav"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_2
msgid "Mud Wall"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_1
msgid "Music"
msgstr "Música"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__name
msgid "Name"
msgstr "Nome"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_0_1
msgid "No"
msgstr "Não"

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_question_action
msgid "No Quiz Question yet!"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_action
msgid "No Quiz added yet!"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid "No user found for"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__awarded_points
msgid "Number of Points"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Oopsie, you did not score any point on this quiz."
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_2
msgid "Open Source Apps"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_2
msgid "OpenWood is not an Open Source congres about Apps."
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Point"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__awarded_points
#, python-format
msgid "Points"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_13_quiz
msgid "Pretty. Ugly. Lovely."
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__question_id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__name
msgid "Question"
msgstr "Pergunta"

#. module: website_event_track_quiz
#: code:addons/website_event_track_quiz/models/event_quiz.py:0
#, python-format
msgid ""
"Question \"%s\" must have 1 correct answer and at least 1 incorrect answer "
"to be valid."
msgstr ""

#. module: website_event_track_quiz
#: code:addons/website_event_track_quiz/models/event_quiz.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer to be valid."
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz_answer
msgid "Question's Answer"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__question_ids
msgid "Questions"
msgstr "Perguntas"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__quiz_id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_id
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_content
msgid "Quiz"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_points
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track_visitor__quiz_points
msgid "Quiz Points"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_form
msgid "Quiz Question"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.ui.menu,name:website_event_track_quiz.event_quiz_question_menu
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_tree
msgid "Quiz Questions"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "Quiz validation error"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_ids
#: model:ir.ui.menu,name:website_event_track_quiz.event_quiz_menu
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_tree
msgid "Quizzes"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Reset"
msgstr "Reiniciar"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_2
msgid "Scotch tape"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search"
msgstr "Procurar"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search Attendees"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search courses"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_5_quiz
msgid "Securing your Lumber during transport"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__sequence
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_1
msgid "Steel Wall"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_1_1
msgid "Stores !"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_content
msgid "Take the Quiz"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "The correct answer was:"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid "There is currently no leaderboard available"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "There was an error validating this quiz."
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,help:website_event_track_quiz.field_event_quiz_answer__comment
msgid ""
"This comment will be displayed to the user if he selects this answer, after submitting the quiz.\n"
"                It is used as a small informational text helping to understand why this answer is correct / incorrect."
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "This quiz is already done. Retaking it is not possible."
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_1
msgid "Tie-down straps and other wooden blocks"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Track"
msgstr "Tema"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_track_visitor
msgid "Track / Visitor Link"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_5_question_0
msgid "Transporting lumber from stores to your house is safe."
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_1_0
msgid "Trees !"
msgstr ""

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__repeatable
msgid "Unlimited Tries"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "User rank"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_1_2
msgid "Well, it could work but you will need a lot of tape!"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_1_quiz
msgid "What This Event Is All About"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_5_question_1
msgid "What kind of tool are needed to secure your lumber ?"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_13_question_0
msgid "What kind of wall is transformed here ?"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_1_question_0
msgid "What will we talk about during this event ?"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_1_question_1
msgid "Where does lumber comes from ?"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_0
msgid "Wood"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_0_0
msgid "Yes"
msgstr "Sim"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.all_visitor_card
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "You"
msgstr ""

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_0
msgid "You're really smart !"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
msgid "e.g. Test your Knowledge"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_form
msgid "e.g. What is Joe's favorite motto?"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "point!"
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "points!"
msgstr ""
