<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="event_quiz_question_view_search" model="ir.ui.view">
        <field name="name">event.quiz.question.view.search</field>
        <field name="model">event.quiz.question</field>
        <field name="arch" type="xml">
            <search string="Quiz Questions">
                <field name="name"/>
                <field name="quiz_id"/>
                <group string="Group By" expand="0">
                    <filter string="Quiz" name="groupby_quiz_id" context="{'group_by': 'quiz_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="event_quiz_question_view_tree" model="ir.ui.view">
        <field name="name">event.quiz.question.view.tree</field>
        <field name="model">event.quiz.question</field>
        <field name="arch" type="xml">
            <tree string="Quiz Questions">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="quiz_id"/>
                <field name="awarded_points"/>
            </tree>
        </field>
    </record>

    <record id="event_quiz_question_view_tree_from_quiz" model="ir.ui.view">
        <field name="name">event.quiz.question.view.tree.from.quiz</field>
        <field name="model">event.quiz.question</field>
        <field name="inherit_id" ref="website_event_track_quiz.event_quiz_question_view_tree"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='quiz_id']" position="replace">
            </xpath>
        </field>
    </record>

    <record id="event_quiz_question_view_form" model="ir.ui.view">
        <field name="name">event.quiz.question.view.form</field>
        <field name="model">event.quiz.question</field>
        <field name="arch" type="xml">
            <form string="Quiz Question">
                <sheet>
                    <h1>
                        <field name="name" default_focus="1"
                            placeholder="e.g. What is Joe's favorite motto?"/>
                    </h1>
                    <group>
                        <field name="quiz_id"/>
                        <field name="awarded_points" invisible="1"/>
                    </group>
                    <group name="questions">
                        <field name="answer_ids" nolabel="1">
                            <tree editable="bottom" create="true" delete="true">
                                <field name="sequence" widget="handle"/>
                                <field name="text_value"/>
                                <field name="is_correct"/>
                                <field name="awarded_points"/>
                                <field name="comment"/>
                            </tree>
                        </field>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="event_quiz_question_view_form_from_quiz" model="ir.ui.view">
        <field name="name">event.quiz.question.view.form.from.quiz</field>
        <field name="model">event.quiz.question</field>
        <field name="inherit_id" ref="website_event_track_quiz.event_quiz_question_view_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='quiz_id']" position="replace">
            </xpath>
        </field>
    </record>

    <record id="event_quiz_question_action" model="ir.actions.act_window">
        <field name="name">Event Quiz Questions</field>
        <field name="res_model">event.quiz.question</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'create': False}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Quiz Question yet!
            </p><p>
                From here you will be able to examine all quiz questions you have linked to Tracks.
            </p>
        </field>
    </record>
</odoo>
