<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_ca" model="res.partner">
        <field name="name">CA Company</field>
        <field name="vat">*********</field>
        <field name="street">112 Place Charles-Le <PERSON></field>
        <field name="city">Longueuil</field>
        <field name="country_id" ref="base.ca"/>
        <field name="state_id" ref="base.state_ca_yt"/>
        <field name="zip">J4K4Y9</field>
        <field name="phone">******-234-5678</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.caexample.com</field>
    </record>

    <record id="demo_company_ca" model="res.company">
        <field name="name">CA Company</field>
        <field name="partner_id" ref="partner_demo_company_ca"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_ca')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_ca.demo_company_ca'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_ca.ca_en_chart_template_en')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_ca.demo_company_ca')"/>
    </function>
</odoo>
