# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_recruitment
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "<span class=\"o_stat_text\">Meetings</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "<span class=\"o_stat_text\">Trackers</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_recruitment_stage_kanban
msgid "<span>Folded in Recruitment Pipe: </span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "<strong style=\"font-size: 16px;\">Try the mail gateway</strong>"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_congratulations
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,\n"
"                <br/><br/>\n"
"                We confirm we successfully received your application to the job\n"
"                \"<a href=\"${object.job_id.website_url or ''}\" style=\"color:#9A6C8E;\"><strong>${object.job_id.name}</strong></a>\" at <strong>${object.company_id.name}</strong>.\n"
"                <br/><br/>\n"
"                We will come back to you shortly.\n"
"\n"
"                % if 'website_url' in object.job_id and object.job_id.website_url:\n"
"                    <div style=\"margin: 16px 8px 16px 8px;\">\n"
"                        <a href=\"${object.job_id.website_url}\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                    </div>\n"
"                % endif\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                % if object.user_id:\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>Your Contact:</strong></h3>\n"
"                    <table>\n"
"                        <tr>\n"
"                            <td width=\"75\">\n"
"                                <img src=\"/web/image/res.users/${object.user_id.id}/image_small\" alt=\"Avatar\" style=\"vertical-align:baseline;max-width:64px;\"/>\n"
"                            </td>\n"
"                            <td>\n"
"                                <strong>${object.user_id.name}</strong><br/>\n"
"                                <span>Email: ${object.user_id.email or ''}</span><br/>\n"
"                                <span>Phone: ${object.user_id.phone or ''}</span>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                % endif\n"
"\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within 3 days.</strong><br/><br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                % set location = ''\n"
"                % if object.job_id.address_id.name:\n"
"                    <strong>${object.job_id.address_id.name}</strong><br/>\n"
"                % endif\n"
"                % if object.job_id.address_id.street:\n"
"                    ${object.job_id.address_id.street}<br/>\n"
"                    % set location = object.job_id.address_id.street\n"
"                % endif\n"
"                % if object.job_id.address_id.street2:\n"
"                    ${object.job_id.address_id.street2}<br/>\n"
"                    % set location = '%s, %s' % (location, object.job_id.address_id.street2)\n"
"                % endif\n"
"                % if object.job_id.address_id.city:\n"
"                    ${object.job_id.address_id.city},\n"
"                    % set location = '%s, %s' % (location, object.job_id.address_id.city)\n"
"                % endif\n"
"                % if object.job_id.address_id.state_id.name:\n"
"                    ${object.job_id.address_id.state_id.name},\n"
"                    % set location = '%s, %s' % (location, object.job_id.address_id.state_id.name)\n"
"                % endif\n"
"                % if object.job_id.address_id.zip:\n"
"                    ${object.job_id.address_id.zip}\n"
"                    % set location = '%s, %s' % (location, object.job_id.address_id.zip)\n"
"                % endif\n"
"                <br/>\n"
"                % if object.job_id.address_id.country_id.name:\n"
"                    ${object.job_id.address_id.country_id.name}<br/>\n"
"                    % set location = '%s, %s' % (location, object.job_id.address_id.country_id.name)\n"
"                % endif\n"
"                <br/>\n"
"                % if object.job_id.address_id:\n"
"                    <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"                        <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom;\"/>\n"
"                    </a>\n"
"                % endif\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_interest
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"text-align: center\">\n"
"                <h2>Congratulations!</h2>\n"
"                <div style=\"color:grey;\">Your resume has been positively reviewed.</div>\n"
"                <img src=\"/hr_recruitment/static/src/img/congratulations.png\" alt=\"Congratulations!\" style=\"width:175px;margin:20px 0;\"/>\n"
"            </div>\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                We just reviewed your resume, and it caught our\n"
"                attention. As we think you might be great for the\n"
"                position, your application has been short listed for a\n"
"                call or an interview.\n"
"                <br/><br/>\n"
"                % if 'website_url' in object.job_id and object.job_id.website_url:\n"
"                    <div style=\"margin: 16px 8px 16px 8px;\">\n"
"                        <a href=\"${object.job_id.website_url}\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                    </div>\n"
"                % endif\n"
"\n"
"                % if object.user_id:\n"
"                    You will soon be contacted by:\n"
"                    <table>\n"
"                        <tr>\n"
"                            <td width=\"75\">\n"
"                                <img src=\"/web/image/res.users/${object.user_id.id}/image_small\" alt=\"Avatar\" style=\"vertical-align:baseline;max-width:64px;\"/>\n"
"                            </td>\n"
"                            <td>\n"
"                                <strong>${object.user_id.name}</strong><br/>\n"
"                                <span>Email: ${object.user_id.email or ''}</span><br/>\n"
"                                <span>Phone: ${object.user_id.phone or ''}</span>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <br/><br/>\n"
"                % endif\n"
"                See you soon,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    -- <br/>\n"
"                    The HR Team\n"
"                    % if 'website_url' in object.job_id and object.job_id.website_url\n"
"                        Discover <a href=\"/jobs\" style=\"text-decoration:none;color:#717188;\">all our jobs</a>.<br/>\n"
"                    % endif\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within 3 days</strong>.\n"
"                <br/><br/>\n"
"                The next step is either a call or a meeting in our offices.\n"
"                <br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"                <br/>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                % set location = ''\n"
"                % if object.job_id.address_id.name:\n"
"                    <strong>${object.job_id.address_id.name}</strong><br/>\n"
"                % endif\n"
"                % if object.job_id.address_id.street:\n"
"                    ${object.job_id.address_id.street}<br/>\n"
"                    % set location = object.job_id.address_id.street\n"
"                % endif\n"
"                % if object.job_id.address_id.street2:\n"
"                    ${object.job_id.address_id.street2}<br/>\n"
"                    % set location = '%s, %s' % (location, object.job_id.address_id.street2)\n"
"                % endif\n"
"                % if object.job_id.address_id.city:\n"
"                    ${object.job_id.address_id.city},\n"
"                    % set location = '%s, %s' % (location, object.job_id.address_id.city)\n"
"                % endif\n"
"                % if object.job_id.address_id.state_id.name:\n"
"                    ${object.job_id.address_id.state_id.name},\n"
"                    % set location = '%s, %s' % (location, object.job_id.address_id.state_id.name)\n"
"                % endif\n"
"                % if object.job_id.address_id.zip:\n"
"                    ${object.job_id.address_id.zip}\n"
"                    % set location = '%s, %s' % (location, object.job_id.address_id.zip)\n"
"                % endif\n"
"                <br/>\n"
"                % if object.job_id.address_id.country_id.name:\n"
"                    ${object.job_id.address_id.country_id.name}<br/>\n"
"                    % set location = '%s, %s' % (location, object.job_id.address_id.country_id.name)\n"
"                % endif\n"
"                <br/>\n"
"\n"
"                % if object.job_id.address_id:\n"
"                    <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"                        <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom;\"/>\n"
"                    </a>\n"
"                % endif\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_refuse
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,<br/><br/>\n"
"                Thank you for your interest in joining the\n"
"                <b>${object.company_id.name}</b> team.  We wanted to\n"
"                let you know that, although your resume is competitive,\n"
"                our hiring team reviewed your application and <b>did not\n"
"                select it for further consideration</b>.\n"
"                <br/><br/>\n"
"                Please note that recruiting is hard, and we can do\n"
"                mistake. Do not hesitate to reply to this email if you\n"
"                think we did a mistake, or if you want more information\n"
"                about our decision.\n"
"                <br/><br/>\n"
"                We will, however, keep your resume on record and get in\n"
"                touch with you about future opportunities that may be a\n"
"                better fit for your skills and experience.\n"
"                <br/><br/>\n"
"                We wish you all the best in your job search and hope we\n"
"                will have the chance to consider you for another role\n"
"                in the future.\n"
"                <br/><br/>\n"
"                Thank you,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    % if object.user_id:\n"
"                        -- <br/>\n"
"                        <strong>${object.user_id.name}</strong><br/>\n"
"                        Email: ${object.user_id.email or ''}<br/>\n"
"                        Phone: ${object.user_id.phone or ''}\n"
"                    % else:\n"
"                        -- <br/>\n"
"                        ${object.company_id.name}<br/>\n"
"                        The HR Team\n"
"                    % endif\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction
msgid "Action Needed"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__active
msgid "Active"
msgstr "Активно"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_ids
msgid "Activities"
msgstr "Активности"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_state
msgid "Activity State"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.mail_activity_type_action_config_hr_applicant
#: model:ir.ui.menu,name:hr_recruitment.hr_recruitment_menu_config_activity_type
msgid "Activity Types"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_no_employee
msgid "Add a new employee"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid "Add a new stage in the recruitment process"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_category_action
msgid "Add a new tag"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__address_id
msgid "Address where employees are working"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_id
msgid "Alias"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__alias_id
msgid "Alias ID"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_name
msgid "Alias Name"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_domain
msgid "Alias domain"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ_all_app
msgid "All Applications"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment.field_calendar_event__applicant_id
msgid "Applicant"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_degree
msgid "Applicant Degree"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_hired
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_hired
msgid "Applicant Hired"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_stage_changed
msgid "Applicant Stage Changed"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_new
msgid "Applicant created"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_hired
msgid "Applicant hired"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_name
msgid "Applicant's Name"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_tree_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_calendar_view
msgid "Applicants"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
msgid ""
"Applicants and their attached CV are created automatically when an email is sent.\n"
"                If you install the document management modules, all resumes are indexed automatically,\n"
"                so that you can easily search through their content."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Applicants and their attached CV are created automatically when an email is sent.\n"
"            If you install the document management modules, all resumes are indexed automatically,\n"
"            so that you can easily search through their content."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid ""
"Applicants can send resume to this email address,<br/>it will create an "
"application automatically"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_count
msgid "Application Count"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Application Summary"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Application email"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Application(s)"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_applications
#: model:ir.actions.act_window,name:hr_recruitment.crm_case_categ0_act_job
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ0_act_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Applications"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__job_id
msgid "Applied Job"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__priority
msgid "Appreciation"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Archived"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_open
msgid "Assigned"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Attachments"
msgstr "Прилози"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__template_id
msgid "Automated Email"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__availability
msgid "Availability"
msgstr "Dostupnost"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bachelor
msgid "Bachelor Degree"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:65
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
#, python-format
msgid "Blocked"
msgstr "Blokiran"

#. module: hr_recruitment
#: model:ir.filters,name:hr_recruitment.hr_applicant_filter_department
msgid "By Department"
msgstr ""

#. module: hr_recruitment
#: model:ir.filters,name:hr_recruitment.hr_applicant_filter_job
msgid "By Job"
msgstr ""

#. module: hr_recruitment
#: model:ir.filters,name:hr_recruitment.hr_applicant_filter_recruiter
msgid "By Recruiter"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__campaign_id
msgid "Campaign"
msgstr "Kampanja"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_graph_view_job
msgid "Cases By Stage and Estimates"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_category
msgid "Category of applicant"
msgstr ""

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tour.js:22
#, python-format
msgid "Click here to create a new job position."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_closed
msgid "Closed"
msgstr "Zatvoren"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__color
msgid "Color Index"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Company"
msgstr "Kompanija"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_configuration
msgid "Configuration"
msgstr "Konfiguracija"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:371
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_id
#, python-format
msgid "Contact"
msgstr "Kontakt"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:373
#, python-format
msgid "Contact Email"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_view_search_inherit_hr_recruitment
msgid "Content"
msgstr "Sadržaj"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Contract"
msgstr "Ugovor"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job4
msgid "Contract Proposal"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job5
msgid "Contract Signed"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create"
msgstr "Kreiraj"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Create Employee"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.create_job_simple
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create a Job Position"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Create alias"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid "Create some aliases to track where applicants come from"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_uid
msgid "Created by"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_date
msgid "Created on"
msgstr "Kreiran"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_date
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Creation Date"
msgstr "Datum Kreiranja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_close
msgid "Days to Close"
msgstr "Dana do Zatvaranja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_open
msgid "Days to Open"
msgstr "Dana do Otvaranja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_defaults
msgid "Default Values"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid ""
"Define a specific contact address for this job position. If you keep it "
"empty, the default email address will be used which is in human resources "
"settings"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
msgid ""
"Define here your stages of the recruitment process, for example:\n"
"            qualification call, first interview, second interview, refused,\n"
"            hired."
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_degree_action
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__type_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__name
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Degree"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_degree
msgid "Degrees"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__delay_close
msgid "Delay to Close"
msgstr "Kasnjenje do Zatvaranja"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Delete"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__department_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Department"
msgstr "Odeljenje"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__manager_id
msgid "Department Manager"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_department
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_department
msgid "Departments"
msgstr "Odeljenja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__description
msgid "Description"
msgstr "Opis"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_digest_digest
msgid "Digest"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Discard"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__display_name
msgid "Display Name"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/digest.py:16
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bac5
msgid "Doctoral Degree"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__documents_count
msgid "Document Count"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__document_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Documents"
msgstr "Dokumenti"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid ""
"Don't forget to specify the department if your recruitment process\n"
"            is different according to the job position."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Dropdown menu"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Edit"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_from
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__email
msgid "Email"
msgstr "E-mail"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Email Alias"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Email alias"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_id
msgid ""
"Email alias for this job position. New emails will automatically create new "
"applicants for this job position."
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_employee
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__emp_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Employee"
msgstr "Zapošljeni"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__employee_name
msgid "Employee Name"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__emp_id
msgid "Employee linked to the applicant."
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_no_employee
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Employees"
msgstr "Započljeni"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_calendar_event
msgid "Event"
msgstr "Događaj"

#. module: hr_recruitment
#: selection:hr.applicant,priority:0
msgid "Excellent"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__expected_employee
msgid "Expected Employee"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected
msgid "Expected Salary"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Expected Salary Extra"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Extended Filters"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Extra advantages..."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Feedback of interviews..."
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job2
msgid "First Interview"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__fold
msgid "Folded in Recruitment Pipe"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_follower_ids
msgid "Followers"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Future Activities"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_degree__sequence
msgid "Gives the sequence order when displaying a list of degrees."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__sequence
msgid "Gives the sequence order when displaying a list of stages."
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,priority:0
msgid "Good"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_graduate
msgid "Graduate"
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,kanban_state:0
msgid "Green"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_done
msgid "Green Kanban Label"
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,kanban_state:0
msgid "Grey"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_normal
msgid "Grey Kanban Label"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_department
msgid "HR Department"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__hr_responsible_id
msgid "HR Responsible"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Hired Employees"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_unread
msgid "If checked new messages require your attention."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__template_id
msgid ""
"If set, a message is posted on the applicant using the template when the "
"applicant is set to the stage."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__active
msgid ""
"If the active field is set to false, it will allow you to hide the case "
"without removing it."
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:69
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job5
#, python-format
msgid "In Progress"
msgstr "U Progresu"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "In progress"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job1
msgid "Initial Qualification"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Interview Forms"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_job.py:94
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
#, python-format
msgid "Job"
msgstr "Posao"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_pivot_view_job
msgid "Job Applications"
msgstr ""

#. module: hr_recruitment
#: model:utm.campaign,name:hr_recruitment.utm_campaign_job
msgid "Job Campaign"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__job_id
msgid "Job ID"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__address_id
msgid "Job Location"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "Pozicija"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_department_new
msgid "Job Position Created"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_job_new
msgid "Job Position created"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_config
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position_config
msgid "Job Positions"
msgstr "Pozicije Posla"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Job Posting"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__job_id
msgid "Job Specific"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Jobs"
msgstr "Poslovi"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Jobs - Recruitment Form"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_sources
msgid "Jobs Sources"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_blocked
msgid "Kanban Blocked"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_normal
msgid "Kanban Ongoing"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__kanban_state
msgid "Kanban State"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_done
msgid "Kanban Valid"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues_value
msgid "Kpi Hr Recruitment New Colleagues Value"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage____last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__last_stage_id
msgid "Last Stage"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Last Stage Update"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Late Activities"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Let's create a job position."
msgstr ""

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tour.js:27
#, python-format
msgid ""
"Let's have a look at the <b>applications pipeline</b> for this job position."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_manager
msgid "Manager"
msgstr "Menadžer"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_licenced
msgid "Master Degree"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__medium_id
msgid "Medium"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_mobile
msgid "Mobile"
msgstr "Mobilni"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "My Activities"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "My Applications"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_activity
msgid "My Next Activities"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__name
msgid "Name"
msgstr "Ime"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "New"
msgstr "Novi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_applicant_count
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_new
msgid "New Applicant"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
msgid "New Applicants"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_new_application
msgid "New Application"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_from_department
msgid "New Applications"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:447
#, python-format
msgid "New Employee %s Hired"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_hired_employee
msgid "New Hired Employee"
msgstr ""

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid ""
"New applicants can be generated from incoming emails. You just need to set "
"email aliases in configuration of job positions.<br>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_employee_view_search
msgid "Newly Hired"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_employee_action_from_department
msgid "Newly Hired Employees"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_employee__newly_hired_employee
msgid "Newly hired employee"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_nmenu_activity
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_tree_activity
msgid "Next Activities"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:389
#, python-format
msgid "No Subject"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid "No applications yet"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_resumes
msgid "No document yet"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_action_activity
msgid "No scheduled activities for the moment"
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,priority:0
msgid "Normal"
msgstr "Uobičajen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_number
msgid "Number of Attachments"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__delay_close
msgid "Number of days to close"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"                process and follow up all operations: meetings, interviews, etc."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"            process and follow up all operations: meetings, interviews, etc."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Offer"
msgstr ""

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_user
msgid "Officer"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Ongoing"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Online Posting"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,activity_state:0
msgid "Overdue"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_user_id
msgid "Owner"
msgstr "Vlasnik"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_model_id
msgid "Parent Model"
msgstr "Roditeljski Model"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__hr_responsible_id
msgid "Person responsible of validating the employee's contracts."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_phone
msgid "Phone"
msgstr "Telefon"

#. module: hr_recruitment
#: selection:hr.applicant,activity_state:0
msgid "Planned"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__probability
msgid "Probability"
msgstr "Verovatnoca"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Proposed Salary"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Proposed Salary Extra"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Publish available jobs on your website"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:67
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
#, python-format
msgid "Ready for Next Stage"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Ready to recruit more efficiently?"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_root
#: model_terms:ir.ui.view,arch_db:hr_recruitment.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Recruitment"
msgstr "Zapošljavanje"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_job_stage_act
msgid "Recruitment / Applicants Stages"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_analysis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Recruitment Analysis"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Recruitment Done"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Recruitment Process"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__user_id
msgid "Recruitment Responsible"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_stage
msgid "Recruitment Stages"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
msgid "Recruitments"
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,kanban_state:0
msgid "Red"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_blocked
msgid "Red Kanban Label"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__reference
msgid "Referred By"
msgstr "Prema preporuci"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Refuse"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Refused"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Reopen Application"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.report_hr_recruitment
msgid "Reports"
msgstr "Izveštaji"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__requirements
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Requirements"
msgstr "Zahtevi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Responsible"
msgstr "Odgovoran"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_resumes
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ0_act_job02
msgid "Resumes and Letters"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected
msgid "Salary Expected by Applicant"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Salary Expected by Applicant, extra advantages"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Salary Proposed by the Organisation"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Salary Proposed by the Organisation, extra advantages"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Schedule Interview"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Schedule interview with this applicant"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Search Applicants"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job3
msgid "Second Interview"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__sequence
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__sequence
msgid "Sequence"
msgstr "Niz"

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "Set Email Aliases"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_configuration
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_global_settings
msgid "Settings"
msgstr "Podešavanje"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__source_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__source_id
msgid "Source"
msgstr "Izvorno"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__name
msgid "Source Name"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_source_action
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_source
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Sources of Applicants"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__job_id
msgid "Specific job that uses this stage. Other jobs will not use this stage."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__stage_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Stage"
msgstr "Nivo"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_stage_changed
msgid "Stage Changed"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage Definition"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_stage_changed
msgid "Stage changed"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__name
msgid "Stage name"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__last_stage_id
msgid ""
"Stage of the applicant before being in the current stage. Used for lost "
"cases analysis."
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_stage_act
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_stage
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_tree
msgid "Stages"
msgstr "Nivoi"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Start Recruitment"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Start recruitment"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Subject / Applicant"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__name
msgid "Subject / Application Name"
msgstr ""

#. module: hr_recruitment
#: sql_constraint:hr.applicant.category:0
msgid "Tag name already exists !"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_category_action
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__categ_ids
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_category_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_form
msgid "Tags"
msgstr "Oznake"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__availability
msgid "The date at which the applicant will be available to start working"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: hr_recruitment
#: sql_constraint:hr.recruitment.degree:0
msgid "The name of the Degree of Recruitment must be unique!"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid ""
"These aliases can be emails or urls for every source. When the applicant "
"arrives here through one of these you'll know where he came from."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__email_cc
msgid ""
"These email addresses will be added to the CC field of all inbound and "
"outbound emails for this record before being sent. Separate multiple email "
"addresses with a comma"
msgstr ""
"Ove ce Email adrese bti pridodane CC polju za sve odlazne i dolazne Emailove"
" za ovaj zapis pre nego je poslat. Razdvoj vise Email adresa zarezom"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__email_from
msgid "These people will receive email."
msgstr "Ovi ce ljudi primiti Email."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_resumes
msgid ""
"This menu helps you search through resumes and motivation\n"
"                letters. Odoo automatically indexes .PDF, .DOC, DOCX, .TXT\n"
"                files so that you can search keywords from the content of these\n"
"                files."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,activity_state:0
msgid "Today"
msgstr "Danas"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Today Activities"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Tooltips"
msgstr ""

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tour.js:37
#, python-format
msgid ""
"Try to send an email to this address, it will create an application "
"automatically."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Unassigned"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_unread
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_job_filter_recruitment
msgid "Unread Messages"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Use interview forms during recruitment process"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Use interview forms tailored to each job position during the recruitment "
"process. Select the form to use in the job position detail form. This relies"
" on Survey app."
msgstr ""

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tour.js:32
#, python-format
msgid "Use the breadcrumbs to <b>go back to the dashboard</b>."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_email
msgid "User Email"
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,priority:0
msgid "Very Good"
msgstr ""

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tour.js:11
#: code:addons/hr_recruitment/static/src/js/tour.js:16
#, python-format
msgid "Want to <b>start recruiting</b> like a pro? <i>Start here.</i>"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_cc
msgid "Watchers Emails"
msgstr "Praceni Emailovi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_no_employee
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                can easily find all the information you need for each person;\n"
"                contact data, job position, availability, etc."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"You can define here the labels that will be displayed for the kanban state instead\n"
"                        of the default labels."
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:450
#, python-format
msgid "You must define an Applied Job and a Contact Name for this applicant."
msgstr ""

#. module: hr_recruitment
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_congratulations
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_interest
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_refuse
msgid "Your Job Application: ${object.job_id.name | safe}"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. Sales Manager"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:295
#, python-format
msgid "job applicant"
msgstr ""
