<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.DiscussSidebarMailbox" owl="1">
        <div class="o_DiscussSidebarMailbox"
            t-att-class="{
                'o-active': mailbox and mailbox === messaging.discuss.thread,
                'o-starred-box': mailbox and mailbox === messaging.starred,
            }" t-on-click="mailbox.onClick" t-att-data-thread-local-id="mailbox.localId" t-att-data-thread-name="mailbox.displayName"
        >
            <ThreadIcon class="o_DiscussSidebarMailbox_item" threadLocalId="mailbox.localId"/>
            <div class="o_DiscussSidebarMailbox_item o_DiscussSidebarMailbox_name">
                <t t-esc="mailbox.displayName"/>
            </div>
            <div class="o-autogrow o_DiscussSidebarMailbox_item"/>
            <t t-if="mailbox.counter > 0">
                <div class="o_DiscussSidebarMailbox_counter o_DiscussSidebarMailbox_item badge badge-pill">
                    <t t-esc="mailbox.counter"/>
                </div>
            </t>
        </div>
    </t>
</templates>
