<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <data>
        <record id="sale_tax_25_goods" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> moms 25%</field>
            <field name="description">ST25</field>
            <field name="amount">25</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2611'),
                    'plus_report_line_ids': [ref('tax_report_line_10')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2611'),
                    'minus_report_line_ids': [ref('tax_report_line_10')]
                })]"/>
        </record>
        <record id="sale_tax_25_services" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Utgående moms Tjänst 25%</field>
            <field name="description">ST25</field>
            <field name="amount">25</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2611'),
                    'plus_report_line_ids': [ref('tax_report_line_10')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2611'),
                    'minus_report_line_ids': [ref('tax_report_line_10')]
                })]"/>
        </record>
        <record id="purchase_tax_25_goods" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Ingående moms 25%</field>
            <field name="description">PT25</field>
            <field name="amount">25</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
        </record>
        <record id="purchase_tax_25_services" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Ingående moms Tjänst 25%</field>
            <field name="description">PT25</field>
            <field name="amount">25</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
        </record>
        <record id="sale_tax_12_goods" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Utgående moms 12%</field>
            <field name="description">ST12</field>
            <field name="amount">12</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2621'),
                    'plus_report_line_ids': [ref('tax_report_line_11')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2621'),
                    'minus_report_line_ids': [ref('tax_report_line_11')]
                })]"/>
        </record>
        <record id="sale_tax_12_services" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Utgående moms Tjänst 12%</field>
            <field name="description">ST12</field>
            <field name="amount">12</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2621'),
                    'plus_report_line_ids': [ref('tax_report_line_11')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2621'),
                    'minus_report_line_ids': [ref('tax_report_line_11')]
                })]"/>
        </record>
        <record id="purchase_tax_12_goods" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Ingående moms 12%</field>
            <field name="description">PT12</field>
            <field name="amount">12</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
        </record>
        <record id="purchase_tax_12_services" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Ingående moms Tjänst 12%</field>
            <field name="description">PT12</field>
            <field name="amount">12</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
        </record>
        <record id="sale_tax_6_goods" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Utgående moms 6%</field>
            <field name="description">ST6</field>
            <field name="amount">6</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2631'),
                    'plus_report_line_ids': [ref('tax_report_line_12')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2631'),
                    'minus_report_line_ids': [ref('tax_report_line_12')]
                })]"/>
        </record>
        <record id="sale_tax_6_services" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Utgående moms Tjänst 6%</field>
            <field name="description">ST6</field>
            <field name="amount">6</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2631'),
                    'plus_report_line_ids': [ref('tax_report_line_12')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_05')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2631'),
                    'minus_report_line_ids': [ref('tax_report_line_12')]
                })]"/>
        </record>
        <record id="purchase_tax_6_goods" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Ingående moms 6%</field>
            <field name="description">PT6</field>
            <field name="amount">6</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
        </record>
        <record id="purchase_tax_6_services" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Ingående moms Tjänst 6%</field>
            <field name="description">PT6</field>
            <field name="amount">6</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2641'),
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                })]"/>
        </record>
        <!-- Tax template VAT in EC goods -->
        <record id="sale_tax_services_EC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Momsfri försäljning av tjänst EU</field>
            <field name="description">SE0</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_39')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax'
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_39')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax'
                })]"/>
        </record>
        <record id="sale_tax_goods_EC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Momsfri Försäljning av varor EU</field>
            <field name="description">SE0</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_35')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax'
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_35')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax'
                })]"/>
        </record>
        <record id="purchase_goods_tax_25_EC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköp av varor EU moms 25%</field>
            <field name="description">PE25</field>
            <field name="amount">25</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_20')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_30')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2614')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_20')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_30')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2614')
                })]"/>
        </record>
        <record id="purchase_goods_tax_12_EC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköp av varor EU moms 12%</field>
            <field name="description">PE12</field>
            <field name="amount">12</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_20')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_31')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2624')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_20')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_31')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2624')
                })]"/>
        </record>
        <record id="purchase_goods_tax_6_EC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköp av varor EU moms 6%</field>
            <field name="description">PE6</field>
            <field name="amount">6</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_20')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_32')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2634')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_20')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_32')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2634')
                })]"/>
        </record>
        <!-- Tax template VAT in EC services -->
        <record id="purchase_services_tax_25_EC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköp av tjänst EU moms 25%</field>
            <field name="description">PE25</field>
            <field name="amount">25</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_21')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_30')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2614')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_21')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_30')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2614')
                })]"/>
        </record>
        <record id="purchase_services_tax_12_EC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköp av tjänst EU moms 12%</field>
            <field name="description">PE12</field>
            <field name="amount">12</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_21')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_31')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2624')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_21')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_31')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2624')
                })]"/>
        </record>
        <record id="purchase_services_tax_6_EC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköp av tjänst EU moms 6%</field>
            <field name="description">PE6</field>
            <field name="amount">6</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_21')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_32')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2634')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_21')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_32')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2634')
                })]"/>
        </record>
        <!-- Construction services -->
        <record id="purchase_construction_services_tax_25_EC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköpta tjänster i Sverige, omvändskattskyldighet, 25 %</field>
            <field name="description">PCS25</field>
            <field name="amount">25</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_24')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2647'),
                    'plus_report_line_ids': [ref('tax_report_line_30')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2614')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_24')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2647'),
                    'minus_report_line_ids': [ref('tax_report_line_30')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2614')
                })]"/>
        </record>
        <record id="purchase_construction_services_tax_12_EC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköpta tjänster i Sverige, omvändskattskyldighet, 12 %</field>
            <field name="description">PCS12</field>
            <field name="amount">12</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_24')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a4426'),
                    'plus_report_line_ids': [ref('tax_report_line_31')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2624')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_24')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a4426'),
                    'minus_report_line_ids': [ref('tax_report_line_31')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2624')
                })]"/>
        </record>
        <record id="purchase_construction_services_tax_6_EC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköpta tjänster i Sverige, omvändskattskyldighet, 6 %</field>
            <field name="description">PCS6</field>
            <field name="amount">6</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_24')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a4427'),
                    'plus_report_line_ids': [ref('tax_report_line_32')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2634')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_24')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a4427'),
                    'minus_report_line_ids': [ref('tax_report_line_32')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2634')
                })]"/>
        </record>
        <!-- Tax template VAT Export -->
        <record id="sale_tax_services_NEC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Momsfri försäljning av tjänst utanför EU</field>
            <field name="description">SE0</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_39')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax'
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_39')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax'
                })]"/>
        </record>
        <record id="sale_tax_goods_NEC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Momsfri försäljning av varor utanför EU</field>
            <field name="description">SE0</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_36')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax'
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_36')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax'
                })]"/>
        </record>
        <record id="purchase_goods_tax_25_NEC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Beskattningsunderlag vid import 25%</field>
            <field name="description">PN25</field>
            <field name="amount">25</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_50')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_60')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2615')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_50')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_60')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2615')
                })]"/>
        </record>
        <record id="purchase_goods_tax_12_NEC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Beskattningsunderlag vid import 12%</field>
            <field name="description">PN12</field>
            <field name="amount">12</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_50')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_61')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2625')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_50')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_61')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2625')
                })]"/>
        </record>
        <record id="purchase_goods_tax_6_NEC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Beskattningsunderlag vid import 6%</field>
            <field name="description">PN6</field>
            <field name="amount">6</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_50')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_62')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2635')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_50')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_62')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2635')
                })]"/>
        </record>
        <record id="purchase_services_tax_25_NEC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköp av tjänster utanför EU 25%</field>
            <field name="description">PN25</field>
            <field name="amount">25</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_22')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_30')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2614')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_22')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_30')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2614')
                })]"/>
        </record>
        <record id="purchase_services_tax_12_NEC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköp av tjänster utanför EU 12%</field>
            <field name="description">PN12</field>
            <field name="amount">12</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_22')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_31')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2624')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_22')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_31')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2624')
                })]"/>
        </record>
        <record id="purchase_services_tax_6_NEC" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Inköp av tjänster utanför EU 6%</field>
            <field name="description">PN6</field>
            <field name="amount">6</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_22')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'plus_report_line_ids': [ref('tax_report_line_32')],
                    'minus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2634')
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_22')]
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                    'minus_report_line_ids': [ref('tax_report_line_32')],
                    'plus_report_line_ids': [ref('tax_report_line_48')]
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2634')
                })]"/>
        </record>

        <!--Tax template in triangular trade-->
        <record id="triangular_tax_25_goods" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Trepartshandel - moms 25%</field>
            <field name="description">T25</field>
            <field name="amount">25</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_37')],
                    'minus_report_line_ids': [ref('tax_report_line_38')],
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2615'),
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_37')],
                    'plus_report_line_ids': [ref('tax_report_line_38')],
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2615'),
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                })]"/>
        </record>
        <record id="triangular_tax_12_goods" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Trepartshandel - moms 12%</field>
            <field name="description">T12</field>
            <field name="amount">12</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_37')],
                    'minus_report_line_ids': [ref('tax_report_line_38')],
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2625'),
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_37')],
                    'plus_report_line_ids': [ref('tax_report_line_38')],
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2625'),
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                })]"/>
        </record>
        <record id="triangular_tax_6_goods" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Trepartshandel - moms 6%</field>
            <field name="description">T6</field>
            <field name="amount">6</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_37')],
                    'minus_report_line_ids': [ref('tax_report_line_38')],
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2635'),
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_37')],
                    'plus_report_line_ids': [ref('tax_report_line_38')],
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2635'),
                }),
                (0, 0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a2645'),
                })]"/>
        </record>
        <record id="triangular_tax_0_goods" model="account.tax.template">
            <field name="chart_template_id" ref="l10nse_chart_template"/>
            <field name="name">Trepartshandel - momsfrei</field>
            <field name="description">T0</field>
            <field name="amount">0</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_37')],
                    'minus_report_line_ids': [ref('tax_report_line_38')],
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
            })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_37')],
                    'plus_report_line_ids': [ref('tax_report_line_38')],
                }),
                (0, 0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                })]"/>
        </record>
    </data>
</odoo>
