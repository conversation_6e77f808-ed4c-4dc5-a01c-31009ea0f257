
.oe_pad_switch_positioner {
    position: relative;
}

.oe_pad_switch {
    position: absolute;
    top: 5px;
    left: 383px;
    width: 28px;
    height: 28px;
    background-image: -webkit-linear-gradient(top, white, #f0f0f0);
    border: solid 1px #ccc;
    border-radius:3px;
    text-align: center;
    line-height: 28px;
    overflow: hidden;
    -webkit-box-sizing: border-box;
    color: #666666;
    cursor: pointer;
    font-size: 14px;
}

@media (max-width: 767px) {
    .oe_pad_switch {
        left: auto;
        right: 5px;
    }
    html .o_scroll_hidden {
        overflow: hidden;
    }
}

.oe_pad_switch:hover{
    background-image: -webkit-linear-gradient(top, #f4f4f4, #e4e4e4);
}

.oe_pad_fullscreen .oe_pad_switch {
    top:4px;
}

.oe_pad_fullscreen {
    position: fixed;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    background-color: white;
    margin:0;
    padding:0;
    border:none;
    z-index: 1001;
}

.oe_pad .oe_pad_content.oe_editing{
    border: solid 1px #c4c4c4;
    height:500px;
    -webkit-box-shadow: 0 5px 10px rgba(0,0,0,0.1);
       -moz-box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        -ms-box-shadow: 0 5px 10px rgba(0,0,0,0.1);
         -o-box-shadow: 0 5px 10px rgba(0,0,0,0.1);
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
}

.oe_pad.oe_pad_fullscreen .oe_pad_content {
    height: 100%;
    border: none;
    -webkit-box-shadow: none;
       -moz-box-shadow: none;
        -ms-box-shadow: none;
         -o-box-shadow: none;
            box-shadow: none;
}

.oe_pad .oe_unconfigured {
    text-align: center;
    opacity: 0.75;
}

.oe_pad_loading{
    text-align: center;
    opacity: 0.75;
    font-style: italic;
}

.etherpad_readonly ul, .etherpad_readonly ol {
    margin: 0;
    margin-left: 1.5em;
    padding: 0;
}
.etherpad_readonly ul li{
    list-style-type: disc;
}
.etherpad_readonly ol li{
    list-style-type: decimal;
}
.etherpad_readonly .indent li{
    list-style-type: none !important;
}

.etherpad_readonly{ 
    font-family: arial, sans-serif;
    font-size: 15px;
    line-height: 19px; 
    word-wrap: break-word;
}

.openerp .oe_form_nomargin .etherpad_readonly{ 
    padding: 10px;
}

.etherpad_readonly ul.indent { list-style-type: none !important; }
.etherpad_readonly ol li{ list-style-type: decimal !important; }
.etherpad_readonly ol ol li{ list-style-type: lower-latin !important; }
.etherpad_readonly ol ol ol li{ list-style-type: lower-roman !important; }
.etherpad_readonly ol ol ol ol li{ list-style-type: decimal !important; }
.etherpad_readonly ol ol ol ol ol li{ list-style-type: lower-latin !important; }
.etherpad_readonly ol ol ol ol ol ol li{ list-style-type: lower-roman !important; }
.etherpad_readonly ol ol ol ol ol ol ol li{ list-style-type: decimal !important; }
.etherpad_readonly ol ol ol ol ol ol ol ol li{ list-style-type: lower-latin !important; }
.etherpad_readonly ul li { list-style-type: disc !important; }
.etherpad_readonly ul ul li { list-style-type: circle !important; }
.etherpad_readonly ul ul ul li { list-style-type: square !important; }
.etherpad_readonly ul ul ul ul li { list-style-type: disc !important; }
.etherpad_readonly ul ul ul ul ul li { list-style-type: circle !important; }
.etherpad_readonly ul ul ul ul ul ul li { list-style-type: square !important; }
.etherpad_readonly ul ul ul ul ul ul ul li { list-style-type: disc !important; }
.etherpad_readonly ul ul ul ul ul ul ul ul li { list-style-type: circle !important; }
.etherpad_readonly ul ul ul ul ul ul ul ul ul li { list-style-type: square !important; }
