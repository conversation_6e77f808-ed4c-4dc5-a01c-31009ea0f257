<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="Configurator.language_promote">
        <t t-foreach="language" t-as="lang">
            <option t-att-value="lang[0]" t-att-selected="lang[0] == def_lang ? 'selected' : null"><t t-esc="lang[2]" /></option>
        </t>
    </t>

    <div t-name="website.seo_configuration" role="form">
        <section class="js_seo_meta_title_description"/>
        <section class="js_seo_meta_keywords"/>
        <section class="js_seo_image"/>
    </div>

    <t t-name="website.seo_suggestion_list">
        <ul class="list-inline mb0">
            <!-- filled in JS -->
        </ul>
    </t>

    <t t-name="website.seo_list">
        <tbody>
            <!-- filled in JS -->
        </tbody>
    </t>

    <t t-name="website.seo_keyword">
        <tr class="js_seo_keyword" t-att-data-keyword="widget.keyword">
            <td t-esc="widget.keyword"/>
            <td class="text-center"><i t-if="widget.used_h1" class="fa fa-check" t-attf-title="{{ widget.keyword }} is used in page first level heading"/></td>
            <td class="text-center"><i t-if="widget.used_h2" class="fa fa-check" t-attf-title="{{ widget.keyword }} is used in page second level heading"/></td>
            <td class="text-center"><i class="js_seo_keyword_title fa fa-check" style="visibility: hidden;" t-attf-title="{{ widget.keyword }} is used in page title"/></td>
            <td class="text-center"><i class="js_seo_keyword_description fa fa-check" style="visibility: hidden;" t-attf-title="{{ widget.keyword }} is used in page description"/></td>
            <td class="text-center"><i t-if="widget.used_content" class="fa fa-check" t-attf-title="{{ widget.keyword }} is used in page content"/></td>
            <td class="o_seo_keyword_suggestion"/>
            <td class="text-center"><a href="#" class="oe_remove" data-action="remove-keyword" t-attf-title="Remove {{ widget.keyword }}"><i class="fa fa-trash"/></a></td>
        </tr>
    </t>

    <t t-name="website.seo_suggestion">
        <li class="list-inline-item">
            <span class="o_seo_suggestion badge badge-info" t-att-data-keyword="widget.keyword" t-attf-title="Add {{ widget.keyword }}" t-esc="widget.keyword"/>
        </li>
    </t>

    <t t-name="website.seo_preview">
        <div class="oe_seo_preview_g">
            <div class="rc">
                <div class="r"><t t-esc="widget.title"/></div>
                <div class="s">
                    <div class="kv"><t t-esc="widget.url"/></div>
                    <div class="st"><t t-esc="widget.description"/></div>
                </div>
            </div>
        </div>
    </t>

    <div t-name="website.seo_meta_title_description">
        <div class="row">
            <div class="col-lg-6">
                <div class="form-group">
                    <label for="website_meta_title">
                        Title <i class="fa fa-question-circle-o" title="The title will take a default value unless you specify one."/>
                    </label>
                    <input type="text" name="website_meta_title" id="website_meta_title" class="form-control" placeholder="Keep empty to use default value" maxlength="70" size="70"/>
                </div>
                <div class="form-group">
                    <label for="website_meta_description">
                        Description <i class="fa fa-question-circle-o" t-att-title="widget.previewDescription"/>
                    </label>
                    <textarea name="website_meta_description" id="website_meta_description" placeholder="Keep empty to use default value" class="form-control"/>
                    <div class="alert alert-warning mt16 mb0 small" id="website_meta_description_warning" style="display: none;"/>
                </div>
                <div class="form-group" t-if='widget.canEditUrl'>
                    <label for="website_seo_name">
                        Custom Url <i class="fa fa-question-circle-o" t-att-title="widget.seoNameHelp" />
                    </label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text seo_name_pre"></span>
                        </div>
                        <input type="text" name="website_seo_name" id="website_seo_name" class="form-control" t-att-placeholder="widget.seoNameDefault" />
                        <div class="input-group-append" title="Unalterable unique identifier">
                            <span class="input-group-text seo_name_post"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card-header">Preview</div>
                <div class="card mb0 p-0">
                    <div class="card-body">
                        <div class="js_seo_preview"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <t t-name="website.seo_meta_keywords">
        <label for="website_meta_keywords">
            Keywords
        </label>
        <div class="form-inline" role="form">
            <div class="input-group">
                <input type="text" name="website_meta_keywords" id="website_meta_keywords" class="form-control" placeholder="Keyword" maxlength="30"/>
                <span title="The language of the keyword and related keywords." class="input-group-append">
                    <select name="seo_page_language" id="language-box" class="btn form-control"/>
                </span>
                <span class="input-group-append">
                    <button data-action="add" class="btn btn-primary btn-sm" type="button">Add</button>
                </span>
            </div>
        </div>
        <div class="table-responsive mt16">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Keyword</th>
                        <th class="text-center" title="Used in page first level heading">H1</th>
                        <th class="text-center" title="Used in page second level heading">H2</th>
                        <th class="text-center" title="Used in page title">T</th>
                        <th class="text-center" title="Used in page description">D</th>
                        <th class="text-center" title="Used in page content">C</th>
                        <th title="Most searched topics related to your keyword, ordered by importance">Related keywords</th>
                        <th class="text-center"></th>
                    </tr>
                </thead>
                <!-- body inserted in JS -->
            </table>
        </div>
    </t>

    <div t-name="website.seo_meta_image_selector" class="o_seo_og_image">
        <t t-call="website.og_image_body"/>
    </div>

    <t t-name="website.og_image_body">
        <h4><small>Select an image for social share</small></h4>
        <div class="row">
            <div class="col-lg-6">
                <t t-foreach="widget.images" t-as="image">
                    <div t-attf-class="o_meta_img mt4 #{new window.URL(image, window.location.origin).pathname === new window.URL(widget.activeMetaImg, window.location.origin).pathname and ' o_active_image' or ''}">
                        <img t-att-src="image"/>
                    </div>
                </t>
                <div t-if="widget.customImgUrl" t-attf-class="o_meta_img mt4 #{widget.customImgUrl === widget.activeMetaImg and ' o_active_image' or ''}">
                    <span class="o-custom-label w-100 text-white text-center">Custom</span>
                    <img t-att-src="widget.customImgUrl"/>
                </div>
                <div class="o_meta_img_upload mt4" title="Click to choose more images">
                    <i class="fa fa-upload"/>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card p-0 mb16">
                    <div class="card-header">Social Preview</div>
                    <img class="card-img-top o_meta_active_img" t-att-src="widget.activeMetaImg"/>
                    <div class="card-body px-3 py-2">
                        <h6 class="text-primary card-title mb0"><t t-esc="widget.metaTitle"/></h6>
                        <small class="card-subtitle text-muted"><t t-esc="widget.serverUrl"/></small>
                        <p t-esc="widget.metaDescription"/>
                  </div>
                </div>
            </div>
        </div>
    </t>
</templates>
