# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime
from odoo.exceptions import ValidationError
from odoo.tools.float_utils import float_round


####### Custom Class Leave Constrains
class HolidaysConstrains(models.Model):
    _name = "hr.leave.constrain"
    _order = "duration desc"

    holiday_type_id = fields.Many2one("hr.leave.type")
    duration = fields.Float(string='مدة الاجازة')
    interval = fields.Integer(string='تطلب قبل')


class HolidaysTypeEdited(models.Model):
    _inherit = "hr.leave.type"

    has_monetry_amount = fields.<PERSON><PERSON>an(string='مربوطة بقيمة مالية')
    salary_rule_id = fields.Many2one('hr.salary.rule', string="قاعدة المرتب المربوطة")
    leave_monetry_amount = fields.Float(string='القيمة المالية')

    has_constrains = fields.<PERSON><PERSON><PERSON>('يوجد ضوابط اجازة')
    has_emergency = fields.<PERSON><PERSON>an('يمكن طلبها طارئة')
    constrain_ids = fields.One2many('hr.leave.constrain','holiday_type_id')


class HolidaysAllocationEdited(models.Model):
    _inherit = "hr.leave.allocation"

    monetry_state = fields.Selection([('paid','Paid'),('not_paid','Not Paid')],default='not_paid')
    allocated_amount_paid = fields.Float('القيمة المالية المخصصة')
    is_allocated_amount_paid = fields.Boolean(default=False)

    has_monetry_amount = fields.Boolean(related='holiday_status_id.has_monetry_amount', string='مربوطة بقيمة مالية')
    leave_monetry_amount = fields.Float(related='holiday_status_id.leave_monetry_amount', string='القيمة المالية')

    leave_remaining_display = fields.Char('الأيام المتبقية', compute='get_leave_remaining')
    leave_remaining = fields.Char('الأيام المتبقية', compute='get_leave_remaining')

    def get_leave_remaining(self):
        for elem in self:
            elem.leave_remaining_display = '0 Days'
            elem.leave_remaining = 0.0
            if (type(elem.leaves_taken) == float) and  (type(elem.max_leaves) == float):
                elem.leave_remaining_display = '%g %s' % (float_round(float(elem.max_leaves) - float(elem.leaves_taken), precision_digits=2),'Days')
                elem.leave_remaining = float_round(float(elem.max_leaves) - float(elem.leaves_taken), precision_digits=2)

    def get_allocation_topaid_view(self):
        return {
            'name': 'تحويل رصيد الاجازات الى قيمة مالية',
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'hr.leave.allocation.topaid',
            'view_id':self.env.ref('hr_employees_masarat.hr_leave_allocation_topaid_form').id,
            'target': 'new',
            'context': {'default_allocation_id': self.id}
        }

class HolidayMonetryAllocation(models.TransientModel):
    _name = "hr.leave.allocation.topaid"

    allocation_id = fields.Many2one("hr.leave.allocation")
    employee_id = fields.Many2one('hr.employee', related="allocation_id.employee_id")
    daystocompute = fields.Float(string="الأيام المحتسبة")
    daily_wage = fields.Float(compute="get_daily_wage")
    max_days = fields.Float(string="أقصى ما يمكن احتسابه", compute="get_max_daystocompute")
    monetry_amount = fields.Float(string="القيمة المالية" , default=0.0, readonly=True)

    @api.depends('allocation_id')
    def get_max_daystocompute(self):
        # self.daystocompute = 0.0
        self.max_days = 0.0
        leaves_taken = self.allocation_id.leaves_taken
        max_leaves = self.allocation_id.max_leaves
        if (type(leaves_taken) == float) and (type(max_leaves) == float):
            # self.daystocompute = float_round(float(max_leaves) - float(leaves_taken), precision_digits=2)
            self.max_days = float_round(float(max_leaves) - float(leaves_taken), precision_digits=2)

    @api.depends('employee_id')
    def get_daily_wage(self):
        self.daily_wage = 0
        if self.employee_id:
            if self.employee_id.contract_id:
                wage = self.employee_id.contract_id.wage
                self.daily_wage = wage/22


    @api.constrains('daystocompute')
    def max_daystocompute(self):
        if (self.daystocompute > self.max_days) or (self.daystocompute <= 0.0):
            raise ValidationError('Days of Allocation should be greater than 0 or less than '+str(self.max_days))

    def get_amount(self):
        self.max_daystocompute()
        self.monetry_amount = self.daily_wage * self.daystocompute

        return {"type": "ir.actions.act_window",
                "view_mode": "form",
                "res_model": "hr.leave.allocation.topaid",
                "target": "new",
                "res_id": self.id}

    def get_confirm(self):
        self.allocation_id.monetry_state = 'paid'
        self.allocation_id.allocated_amount_paid = self.monetry_amount





class HolidaysRequestEdited(models.Model):
    _inherit = "hr.leave"

    has_monetry_amount = fields.Boolean(related='holiday_status_id.has_monetry_amount',string='مربوطة بقيمة مالية')
    leave_monetry_amount = fields.Float(related="holiday_status_id.leave_monetry_amount", string='القيمة المالية')
    is_been_taken = fields.Boolean(default=False, string='تم صرفها !', readonly=True)

    could_be_emergency = fields.Boolean(related='holiday_status_id.has_emergency', store=True)
    is_emergency = fields.Boolean(string='Emergency')
    emergency_type = fields.Selection([('3', 'Less than 3 Days'),('8', 'Less than 8 Days')], string='Emergency Type')



    @api.constrains('number_of_days','request_date_from')
    def ensure_requist(self):
        for elem in self:
            hr_group = self.env.user.has_group('hr_employees_masarat.group_hr_masarat_assessment')
            if elem.holiday_status_id.has_constrains and not hr_group and not elem.is_emergency:
                request_interval = (elem.request_date_from - elem.create_date.date()).days
                rule = elem.holiday_status_id.constrain_ids.search([('duration','<=',elem.number_of_days)],limit=1)
                if rule:
                    if request_interval >= rule.interval:
                        pass
                    else:
                        raise ValidationError(' مدة الاجازة  %s يجب ان يتم طلبها قبل %s أيام من تاريخ فتح النموذج' % (str(elem.number_of_days) , str(rule.interval)))


    @api.constrains('emergency_type')
    def ensure_emergency(self):
        for elem in self:
            hr_group = self.env.user.has_group('hr_employees_masarat.group_hr_masarat_assessment')
            if not hr_group and elem.is_emergency:
                if (elem.number_of_days > 3) and (elem.emergency_type == '3'):
                    raise ValidationError('في حالة الاجازة الطارئة لا يجب أن تتجاوز 3 أيام')
                if (elem.number_of_days > 8) and (elem.emergency_type == '8'):
                    raise ValidationError('في حالة الاجازة الطارئة لا يجب أن تتجاوز 8 أيام')

                ######### Emergency count validation
                current_year = datetime(year=datetime.now().date().year, month=1, day=1)
                e3_leaves = self.env['hr.leave'].search([('employee_id','=',elem.employee_id.id),('request_date_from','>',str(current_year)),('is_emergency','=',True),('emergency_type','=','3')])
                e8_leaves = self.env['hr.leave'].search([('employee_id', '=', elem.employee_id.id), ('request_date_from', '>', str(current_year)),('is_emergency', '=', True),('emergency_type', '=', '8')])
                if len(e3_leaves) > 3 or len(e8_leaves) >1 :
                    raise ValidationError('تجاوزت عدد مرات طلب الاجازة الطارئة لهذه السنة')


    def action_send_notification_to_maneger(self, recode_id):
        employee = self.env['hr.employee'].search([('id', '=', recode_id)])
        email_to = employee.parent_id.work_email
        email_from = employee.work_email
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recode_id, self._name)
        body = """
        <div dir="rtl">
            <p><font style="font-size: 14px;">Your Employee """ + employee.name + """, requested leave, </font></p>
            <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            <a href="%s">Request Link</a>
        </div>""" % (web_base_url)
        template_id = self.env['mail.mail'].create({
            'subject': 'Leave Request',
            'email_from': email_from,
            'email_to': email_to,
            'body_html': body})
        template_id.send()

    @api.model
    def create(self, vals_list):
        res = super(HolidaysRequestEdited, self).create(vals_list)
        self.sudo().action_send_notification_to_maneger(res.employee_id.id)
        return res








































    # @api.constrains('number_of_days','emergency_type')
    # def check_emergency_days_validation(self):
    #     for elem in self:
    #         if elem.is_emergency and elem.emergency_type and elem.number_of_days:
    #             if elem.number_of_days > 3 and (elem.emergency_type == '3'):
    #                 raise ValidationError('Your Leave exceeded 3 Days ')
    #             if elem.number_of_days > 8 and (elem.emergency_type == '8'):
    #                 raise ValidationError('Your Leave exceeded 8 Days ')
    #
    # @api.constrains('request_date_from', 'request_date_to', 'holiday_status_id','number_of_days')
    # def check_emergency_constrains_date_state(self):
    #     for elem in self:
    #         current_year = datetime(year=datetime.now().date().year, month=1, day=1)
    #         if elem.is_emergency and (elem.emergency_type == '3'):
    #             e_leaves = self.env['hr.leave'].search_count([('employee_id','=',elem.employee_id.id),
    #                                                       ('request_date_from','>',str(current_year)),
    #                                                       ('state', 'in', ['validate1', 'validate']),
    #                                                       ('is_emergency','=',True),
    #                                                       ('emergency_type','=','3')])
    #             if e_leaves >= 3:
    #                 raise ValidationError('You asked for emergency leave 3 time this year!')
    #         if elem.is_emergency and (elem.emergency_type == '8'):
    #
    #             e_leaves = self.env['hr.leave'].search_count([('employee_id','=',elem.employee_id.id),
    #                                                       ('request_date_from','>',str(current_year)),
    #                                                       ('state', 'in', ['validate1', 'validate']),
    #                                                       ('is_emergency','=',True),
    #                                                       ('emergency_type','=','8')])
    #             print('check for 8 days emergency leave',e_leaves)
    #             if e_leaves >= 1:
    #                 raise ValidationError('You asked for emergency leave 3 time this year!')


