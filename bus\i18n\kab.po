# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * bus
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Kaby<PERSON> (https://www.transifex.com/odoo/teams/41243/kab/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: kab\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: bus
#: sql_constraint:bus.presence:0
msgid "A user can only have one IM status."
msgstr ""

#. module: bus
#: selection:bus.presence,status:0
msgid "Away"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus_channel
msgid "Channel"
msgstr ""

#. module: bus
#: model:ir.model,name:bus.model_res_partner
msgid "Contact"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus_create_date
msgid "Create date"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus_create_uid
msgid "Created by"
msgstr "Yerna-t"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus_display_name
#: model:ir.model.fields,field_description:bus.field_bus_presence_display_name
msgid "Display Name"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus_id
#: model:ir.model.fields,field_description:bus.field_bus_presence_id
msgid "ID"
msgstr "Asulay"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence_status
#: model:ir.model.fields,field_description:bus.field_res_partner_im_status
#: model:ir.model.fields,field_description:bus.field_res_users_im_status
msgid "IM Status"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus___last_update
#: model:ir.model.fields,field_description:bus.field_bus_presence___last_update
msgid "Last Modified on"
msgstr "Aleqqem aneggaru di"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence_last_poll
msgid "Last Poll"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence_last_presence
msgid "Last Presence"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus_write_uid
msgid "Last Updated by"
msgstr "Aleqqem aneggaru sɣuṛ"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus_write_date
msgid "Last Updated on"
msgstr "Aleqqem aneggaru di"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus_message
msgid "Message"
msgstr ""

#. module: bus
#: selection:bus.presence,status:0
msgid "Offline"
msgstr ""

#. module: bus
#: selection:bus.presence,status:0
msgid "Online"
msgstr ""

#. module: bus
#: model:ir.model,name:bus.model_bus_presence
msgid "User Presence"
msgstr ""

#. module: bus
#: model:ir.model,name:bus.model_res_users
#: model:ir.model.fields,field_description:bus.field_bus_presence_user_id
msgid "Users"
msgstr ""

#. module: bus
#: model:ir.model,name:bus.model_bus_bus
msgid "bus.bus"
msgstr ""
