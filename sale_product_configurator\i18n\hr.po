# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_configurator
# 
# Translators:
# <PERSON><PERSON><PERSON> <karol<PERSON>.ton<PERSON>@storm.hr>, 2022
# <PERSON><PERSON><PERSON> <ana-maria.oluji<PERSON>@slobodni-programi.hr>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <i<PERSON><PERSON>.dim<PERSON><PERSON><PERSON>@storm.hr>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "<i class=\"fa fa-shopping-cart add-optionnal-item\"/> Add to cart"
msgstr "<i class=\"fa fa-shopping-cart add-optionnal-item\"/> Dodaj u košaricu"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Price</span>"
msgstr "<span class=\"label\">Cijena</span>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Product</span>"
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Quantity</span>"
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<strong>Total:</strong>"
msgstr "<strong>Ukupno:</strong>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Add"
msgstr "Dodaj"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_quantity_config
msgid "Add one"
msgstr "Dodaj jedan"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Vrijednosti značajki"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "Available Options:"
msgstr ""

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Back"
msgstr "Natrag"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Cancel"
msgstr "Odustani"

#. module: sale_product_configurator
#: model:product.template,name:sale_product_configurator.product_product_1_product_template
msgid "Chair floor protection"
msgstr ""

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Configure"
msgstr "Postavke"

#. module: sale_product_configurator
#: model:ir.actions.act_window,name:sale_product_configurator.sale_product_configurator_action
msgid "Configure a product"
msgstr "Konfiguriraj proizvod"

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Confirm"
msgstr "Potvrdi"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr "Podesive vrijednosti"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr "Dodatne vrijednosti"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__id
msgid "ID"
msgstr "ID"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "Da li je proizvod konfigurabilan?"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator____last_update
msgid "Last Modified on"
msgstr "Zadnja promjena"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__write_date
msgid "Last Updated on"
msgstr "Vrijeme promjene"

#. module: sale_product_configurator
#: model:product.template,description_sale:sale_product_configurator.product_product_1_product_template
msgid "Office chairs can harm your floor: protect it."
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "Option not available"
msgstr ""

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_template__optional_product_ids
msgid "Optional Products"
msgstr "Mogući proizvodi"

#. module: sale_product_configurator
#: model:ir.model.fields,help:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,help:sale_product_configurator.field_product_template__optional_product_ids
msgid ""
"Optional Products are suggested whenever the customer hits *Add to Cart* "
"(cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr ""
"Opcionalni proizvodi su predloženi kad god kupac klikne \"Dodaj u košaricu\""
" (strategija križne prodaje, npr. za računala: jamstvo, programi itd.)."

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__pricelist_id
msgid "Pricelist"
msgstr "Cjenik"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_template_id
msgid "Product"
msgstr "Proizvod"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "Product Image"
msgstr "Slika proizvoda"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_product_template
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_order_view_form
msgid "Product Variant"
msgstr "Varijanta proizvoda"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__quantity
msgid "Quantity"
msgstr "Količina"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_template_view_form
msgid "Recommend when 'Adding to Cart' or quotation"
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_quantity_config
msgid "Remove one"
msgstr "Ukloni jedan"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_sale_product_configurator
msgid "Sale Product Configurator"
msgstr ""

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_sale_order_line
msgid "Sales Order Line"
msgstr "Stavka prodajnog naloga"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Save"
msgstr "Spremi"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
msgid "This combination does not exist."
msgstr ""

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
msgid "This product has no valid combination."
msgstr ""

#. module: sale_product_configurator
#: model:product.template,uom_name:sale_product_configurator.product_product_1_product_template
msgid "Units"
msgstr "Jedinice"
