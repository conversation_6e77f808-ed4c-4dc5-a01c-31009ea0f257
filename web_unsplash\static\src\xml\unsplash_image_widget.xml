<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">

<t t-extend="wysiwyg.widgets.file">
    <t t-jquery=".o_we_load_more" t-operation="after">
        <div class="unsplash_error"></div>
    </t>
</t>
<t t-extend="wysiwyg.widgets.image">
    <t t-jquery="option[value='media-library']" t-operation="after">
        <option value="unsplash">Photos (via Unsplash)</option>
    </t>
</t>
<t t-extend="wysiwyg.widgets.image.existing.attachments">
    <t t-jquery="t[t-foreach='libraryMedia']" t-operation="after">
        <t t-if="['all', 'unsplash'].includes(widget.searchService)" t-foreach="widget.unsplashRecords" t-as="record">
            <t t-call="web_unsplash.dialog.image.content"/>
        </t>
    </t>
</t>

<t t-name="web_unsplash.dialog.image.content">
    <div class="o_existing_attachment_cell o_unsplash_attachment_cell position-relative align-items-center justify-content-center bg-light" t-att-data-imgid="record.id" t-att-data-id="record.id" t-att-data-url="record.urls.regular" t-att-data-download-url="record.links.download_location" t-att-data-description="record.alt_description">
        <img class="img img-fluid o_we_attachment_highlight" t-att-src="record.url" t-att-alt="record.alt_description" style="max-height: 100%;"/>
        <a class="o_we_media_author" t-att-href="record.user.links.html" target="_blank" t-esc="record.user.name" t-att-title="record.user.name"/>
    </div>
</t>

<t t-name="web_unsplash.dialog.error.credentials">
    <div class="alert alert-info w-100">
        <h4><t t-esc="title"/></h4>
        <p><t t-esc="subtitle"/></p>
        <t t-if="showUnsplashSetup">
            <div class="form-inline">
                <div class="form-group">
                    <span>
                        <a href="https://www.odoo.com/documentation/15.0/applications/websites/website/optimize/unsplash.html#generate-an-unsplash-access-key" target="_blank">Get an Access key</a>
                        and paste it here:
                        <input type="text" class="form-control w-auto" id="accessKeyInput" placeholder="Paste your access key here"/>
                    </span>
                </div>
                <div class="form-group my-md-3">
                    <span class="ml-xl-1">
                        and paste
                        <a href="https://www.odoo.com/documentation/15.0/applications/websites/website/optimize/unsplash.html#generate-an-unsplash-application-id" target="_blank">Application ID</a>
                        here:
                        <input type="text" class="form-control w-auto" id="appIdInput" placeholder="Paste your application ID here"/>
                    </span>
                </div>
                <button type="button" class="btn btn-primary btn-block w-auto ml-2 save_unsplash">Apply</button>
            </div>
        </t>
    </div>
</t>

<t t-name="web_unsplash.dialog.error.content">
    <div class="d-flex mt-2 unsplash_error">
        <t t-call="web_unsplash.dialog.error.credentials">
            <t t-if="status == 'key_not_found'">
                <t t-set="title">
                    Setup Unsplash to access royalty free photos.
                </t>
                <t t-set="showUnsplashSetup" t-value="true"/>
            </t>
            <t t-elif="status == 403">
                <t t-set="title">
                    Search is temporarily unavailable
                </t>
                <t t-set="subtitle">
                    The max number of searches is exceeded. Please retry in an hour or extend to a better account.
                </t>
            </t>
            <t t-elif="status == 401">
                <t t-set="title">
                    Unauthorized Key
                </t>
                <t t-set="subtitle">
                    Please check your Unsplash access key and application ID.
                </t>
                <t t-set="showUnsplashSetup" t-value="true"/>
            </t>
            <t t-else="">
                <t t-set="title">
                    Something went wrong
                </t>
                <t t-set="subtitle">
                    Please check your internet connection or contact administrator.
                </t>
            </t>
        </t>
    </div>
</t>

</templates>
