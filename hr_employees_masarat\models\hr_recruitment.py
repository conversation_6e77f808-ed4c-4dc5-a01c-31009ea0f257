# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from odoo.exceptions import ValidationError


class Job(models.Model):

    _inherit = "hr.job"

    requisted_speciality_ids = fields.One2many('hr.job.speciality', 'speciality_id', string='التخصص المطلوب')
    scientific_qualification = fields.Many2one('scientific.qualification',string='المؤهل العلمي')
    years_of_experiance = fields.Integer(string='سنوات الخبرة')
    requisted_skills_ids = fields.Many2many('hr.job.skill', string='المهارات')
    reason_for_reqruitment = fields.Text(string='سبب الاحتياج')
    #requisted_skills_ids = fields.One2many('hr.job.skill', 'skill_id', string='المهارات')




class Speciality(models.Model):
    _name = 'hr.job.speciality'

    name = fields.Char(string='التخصص')
    speciality_id = fields.Many2one("hr.job")
    description = fields.Text()

class Skill(models.Model):
    _name = 'hr.job.skill'
    name = fields.Char(string='المهارة')
    skill_id = fields.Many2one('hr.job')
    description = fields.Text()

class ScientificQualification(models.Model):
    _name = 'scientific.qualification'

    name = fields.Char(string="المؤهل العلمي")
    description = fields.Text()