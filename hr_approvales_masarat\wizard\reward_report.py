# -*- coding:utf-8 -*-
from odoo import api, fields, models, _
from datetime import datetime, timedelta
from pytz import timezone
from odoo.exceptions import ValidationError


class MasaratEmployeeRewardaRepo(models.TransientModel):
    _name = "hr.masarat.reward.report"

    employee_id = fields.Many2one('hr.employee', string="الموظف")
    all_employee = fields.Boolean(string="كل الموظفين")
    date_start = fields.Date(string='تاريخ البدأ')
    date_end = fields.Date(string='تاريخ الانتهاء')

    def get_report_action(self):
        if self.all_employee:
            ee = False
        else:
            ee = self.employee_id.id

        data = {'model': self._name, 'employee_id': ee,'all_employee':self.all_employee, 'date_start':str(self.date_start), 'date_end':str(self.date_end)}
        return self.sudo().env.ref('hr_approvales_masarat.reward_report_x1').report_action(self, data=data)


class MasaratEmployeeRewardaRepoAbs(models.AbstractModel):
    _name = "report.hr_approvales_masarat.reward_report_id"
    _description = 'Car Allawance Report'

    def _get_report_values_all(self,date_from, date_to):
        rewards = self.env['hr.masarat.reward'].search([('request_date','>=',date_from),('request_date','<=',date_to),('state','in',('gm_approval','gm_refuse'))])
        vals = {}
        for elem in rewards:
            vals.setdefault(str(elem.employee_id.id),{'name':str(elem.employee_id.name), 'gm_approval':0, 'gm_refuse':0, 'total':0})
            if elem.state == 'gm_refuse':
                vals[str(elem.employee_id.id)]['gm_refuse']+=elem.reward_amount
            if elem.state == 'gm_approval':
                vals[str(elem.employee_id.id)]['gm_approval'] += elem.reward_amount
            vals[str(elem.employee_id.id)]['total'] += elem.reward_amount

        return {'all_employee': True, 'employees_dict': vals, 'start_date': date_from, 'end_date': date_to}

    def _get_by_employee(self,date_from, date_to, employee_id):
        rewards = self.env['hr.masarat.reward'].search([('employee_id','=',employee_id),('request_date', '>=', date_from), ('request_date', '<=', date_to),('state', 'in', ('gm_approval','gm_refuse'))])
        employee_id = self.env['hr.employee'].search([('id','=',employee_id)])
        vals = []
        for line in rewards:
            if line.reward_reason and (line.reward_reason != 'd'):
                reason = str(line.reward_reason).replace('a','الموظف انجز العمل قبل الوقت المحدد').replace('b','القيام بإضافات مهمة ساهمت في زيادة جودة العمل').replace('c','إنجاز العمل بإتقان وحرفية').replace('d','أسباب أخرى')
            else:
                reason = str(line.reward_reason_other)
            vals.append({'reward_reason':str(reason),
                         'request_date':str(line.request_date),
                         'reward_amount':str(line.reward_amount),
                         'state':str(line.state).replace('gm_approval', 'موافقة').replace('gm_refuse', 'رفض')})
        vals = sorted(vals,key=lambda x:x['request_date'])
        return {'all_employee': False, 'employee_name':employee_id.name, 'employees_dict': vals, 'start_date': date_from, 'end_date': date_to}

    def _get_report_values(self, docids, data=None):
        if data['all_employee']:
            all_employee = self._get_report_values_all(data['date_start'],data['date_end'])
            return all_employee
        else:
            one_employee = self._get_by_employee(data['date_start'],data['date_end'], data['employee_id'])
            return one_employee


