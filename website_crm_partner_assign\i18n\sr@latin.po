# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_crm_partner_assign
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON>v <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website_crm_partner_assign
#: model:mail.template,body_html:website_crm_partner_assign.email_template_lead_forward_mail
msgid ""
"\n"
"<p>Hello,</p>\n"
"\n"
"\n"
"<p>We have been contacted by those prospects that are in your region. Thus, the following leads have been assigned to ${ctx['partner_id'].name}:</p>\n"
"\n"
"<ol>\n"
"% for lead in ctx['partner_leads']:\n"
"     <li><a href=\"${lead.lead_link}\">${lead.lead_id.name or 'Subject Undefined'}</a>, ${lead.lead_id.partner_name or lead.lead_id.contact_name or 'Contact Name Undefined'}, ${lead.lead_id.country_id and lead.lead_id.country_id.name or 'Country Undefined' }, ${lead.lead_id.email_from or 'Email Undefined'}, ${lead.lead_id.phone or ''} </li><br/>\n"
"% endfor\n"
"</ol>\n"
"\n"
"% if ctx.get('partner_in_portal'):\n"
"<p>Please connect to your <a href=\"${object.get_portal_url()}\">Partner Portal</a> to get details. On each lead are two buttons on the top left corner that you should press after having contacted the lead: \"I'm interested\" & \"I'm not interested\".</p>\n"
"% else:\n"
"<p>\n"
"    You do not have yet a portal access to our database. Please contact \n"
"    ${ctx['partner_id'].user_id and ctx['partner_id'].user_id.email and 'your account manager %s (%s)' % (ctx['partner_id'].user_id.name,ctx['partner_id'].user_id.email) or 'us'}.\n"
"</p>\n"
"% endif\n"
"<p>The lead will be sent to another partner if you do not contact the lead before 20 days.</p>\n"
"\n"
"<p>Thanks,</p>\n"
"\n"
"${ctx['partner_id'].user_id and ctx['partner_id'].user_id.signature | safe or ''}\n"
"\n"
"% if not ctx['partner_id'].user_id:\n"
"PS: It looks like you do not have an account manager assigned to you, please contact us.\n"
"% endif\n"
"            "
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_nbr_cases
msgid "# of Cases"
msgstr "# Slucajeva"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_nbr_opportunities
msgid "# of Opportunity"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "&amp;times;"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<i class=\"fa fa-file-text-o\"/> I'm interested"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<i class=\"fa fa-fw fa-times\"/> I'm not interested"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/models/crm_lead.py:191
#, python-format
msgid "<p>I am interested by this lead.</p>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid ""
"<span class=\"oe_grey\" attrs=\"{'invisible':[('partner_latitude','&lt;=',0)]}\">N </span>\n"
"                                <span class=\"oe_grey\" attrs=\"{'invisible':[('partner_latitude','&gt;=',0)]}\">S </span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid ""
"<span class=\"oe_grey\" attrs=\"{'invisible':[('partner_longitude','&lt;=',0)]}\">E </span>\n"
"                                <span class=\"oe_grey\" attrs=\"{'invisible':[('partner_longitude','&gt;=',0)]}\">W </span>\n"
"                                <span class=\"oe_grey\">) </span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid ""
"<span class=\"text-danger error_partner_assign_desinterested\" "
"style=\"display:none;\">You need to fill up the next action and contact the "
"customer before accepting the lead</span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid ""
"<span class=\"text-danger error_partner_assign_interested\" "
"style=\"display:none;\">You need to fill up the next action and contact the "
"customer before accepting the lead</span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<span>at </span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong>Message and communication history</strong>"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_activation
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users_activation
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_tree
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_res_partner_filter_assign
msgid "Activation"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:100
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_active
#, python-format
msgid "Active"
msgstr "Aktivan"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Address"
msgstr "Adrese"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:245
#, python-format
msgid "All Categories"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:262
#, python-format
msgid "All Countries"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/models/crm_lead.py:262
#, python-format
msgid "All fields are required !"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_date_assign
msgid "Assign Date"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_lead_assign_filter
msgid "Assign Month"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.actions.server,name:website_crm_partner_assign.action_assign_salesman_according_assigned_partner
msgid "Assign salesman of assigned partner"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_date_assign
msgid "Assignation Date"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_partner_assigned_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_partner_assigned_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_opportunity_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid "Assigned Partner"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid "Automatic Assignation"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead_forward_to_partner_body
msgid "Automatically sanitized HTML contents"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_probability
msgid "Avg Probability"
msgstr ""

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_bronze
msgid "Bronze"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead_report_assign
msgid "CRM Lead Report"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_partner_report_assign
msgid "CRM Partner Report"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Campaign"
msgstr "Kampanja"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Cancel"
msgstr "Odustani"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "City"
msgstr "Grad"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_date_closed
msgid "Close Date"
msgstr "Datum Zatvaranja"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_company_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_lead_assign_filter
msgid "Company"
msgstr "Preduzeće"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Confirm"
msgstr "Potvrdi"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:54
#: code:addons/website_crm_partner_assign/controllers/main.py:111
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#, python-format
msgid "Contact Name"
msgstr "Ime Kontakta"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Contact a reseller"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Contact name"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner_body
msgid "Contents"
msgstr "Sadržaj"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Countries..."
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_country_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_country_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_lead_assign_filter
msgid "Country"
msgstr "Država"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_create_date
msgid "Create Date"
msgstr "Kreiraj Datum"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner_create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation_create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: website_crm_partner_assign
#: model:crm.lead.tag,name:website_crm_partner_assign.tag_portal_lead_own_opp
msgid "Created by Partner"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner_create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation_create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Current stage of the opp"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_partner_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Customer"
msgstr "Kupac"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Customer Name"
msgstr "Ime Klijenta"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Date"
msgstr "Datum"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Date Partnership"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Date Review"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Deadline:"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_delay_open
msgid "Delay to Assign"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_delay_close
msgid "Delay to Close"
msgstr "Kasnjenje do Zatvaranja"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Description"
msgstr "Opis"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Details Next Activity"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner_display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation_display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Do you have contacted the customer?"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Edit Contact"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Edit Opportunity"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Email"
msgstr "E-mail"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Email Template"
msgstr "Šablon poruka"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Expected"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Expected Closing"
msgstr "Ocekivano Zatvaranje"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Expected Closing:"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:112
#, python-format
msgid "Expected Revenue"
msgstr "Ocekivani Prihod"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_lead_assign_filter
msgid "Extended Filters..."
msgstr "Prošireni filteri..."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Country"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by Level"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner_partner_id
msgid "Forward Leads To"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner_forward_type
msgid "Forward selected leads to"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.action_crm_send_mass_forward
#: model:ir.actions.act_window,name:website_crm_partner_assign.crm_lead_forward_to_partner_act
msgid "Forward to Partner"
msgstr ""

#. module: website_crm_partner_assign
#: model:mail.template,subject:website_crm_partner_assign.email_template_lead_forward_mail
msgid "Fwd: Lead: ${ctx['partner_id'].name}"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_partner_latitude
msgid "Geo Latitude"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_partner_longitude
msgid "Geo Longitude"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid "Geolocation"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner_grade_partner_weight
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner_partner_weight
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_users_partner_weight
msgid ""
"Gives the probability to assign a lead to this partner. (0 means no "
"assignation.)"
msgstr ""

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_gold
msgid "Gold"
msgstr "Ili"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_grade_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_grade_id
msgid "Grade"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_lead_assign_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: website_crm_partner_assign
#: selection:crm.lead.report.assign,priority:0
msgid "High"
msgstr "Visok"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/models/crm_lead.py:201
#, python-format
msgid "I am not interested by this lead. I contacted the lead."
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/models/crm_lead.py:203
#, python-format
msgid "I am not interested by this lead. I have not contacted the lead."
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_id
msgid "ID"
msgstr "ID"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_implemented_partner_ids
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users_implemented_partner_ids
msgid "Implementation References"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_implemented_count
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users_implemented_count
msgid "Implemented Count"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_assigned_partner_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users_assigned_partner_id
msgid "Implemented by"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_date
msgid "Invoice Account Date"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation___last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner___last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign___last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign___last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation___last_update
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner_write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation_write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner_write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation_write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead_date_assign
msgid "Last date this case was forwarded/assigned to a partner"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_date_review
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_date_review
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users_date_review
msgid "Latest Partner Review"
msgstr ""

#. module: website_crm_partner_assign
#: selection:crm.lead.report.assign,type:0
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_lead_id
msgid "Lead"
msgstr "Trag"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Lead -"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_lead_assign_graph
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_lead_assign_pivot
msgid "Lead Assign"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Lead Feedback"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_lead_location
msgid "Lead Location"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_lead_link
msgid "Lead Single Links"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead
msgid "Lead/Opportunity"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_lead_assign_filter
msgid "Leads Analysis"
msgstr "Analiza Tragova"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_id_10761
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users_grade_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
msgid "Level"
msgstr "Nivo"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_name
msgid "Level Name"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_partner_weight
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_partner_weight
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users_partner_weight
msgid "Level Weight"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Looking For a Local Store?"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:106
#, python-format
msgid "Lost"
msgstr "Izgubljen"

#. module: website_crm_partner_assign
#: selection:crm.lead.report.assign,priority:0
msgid "Low"
msgstr "Nizak"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_probability_max
msgid "Max Probability"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Medium"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Mobile"
msgstr "Mobilni"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_opportunity_partner_filter
msgid "My Assigned Partners"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_menu_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "My Leads"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_menu_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "My Opportunities"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:53
#: code:addons/website_crm_partner_assign/controllers/main.py:110
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation_name
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#, python-format
msgid "Name"
msgstr "Naziv"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "New Opportunity"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:52
#: code:addons/website_crm_partner_assign/controllers/main.py:109
#, python-format
msgid "Newest"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Next Activity"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Next Activity Date"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_date_review_next
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users_date_review_next
msgid "Next Partner Review"
msgstr ""

#. module: website_crm_partner_assign
#: model:crm.lead.tag,name:website_crm_partner_assign.tag_portal_lead_partner_unavailable
msgid "No more partner available"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "No result found"
msgstr ""

#. module: website_crm_partner_assign
#: selection:crm.lead.report.assign,priority:0
msgid "Normal"
msgstr "Normalni"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead_report_assign_delay_close
msgid "Number of Days to close the case"
msgstr "Broj dana do zatvaranja slucaja"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead_report_assign_delay_open
msgid "Number of Days to open the case"
msgstr "Br Dana do otvaranja slucaja"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_opening_date
msgid "Opening Date"
msgstr "Datum Otvaranja"

#. module: website_crm_partner_assign
#: model:ir.ui.menu,name:website_crm_partner_assign.menu_report_crm_opportunities_assign_tree
msgid "Opp. Assignment"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.action_report_crm_opportunity_assign
msgid "Opp. Assignment Analysis"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_graph
msgid "Opportunities Assignment Analysis"
msgstr ""

#. module: website_crm_partner_assign
#: selection:crm.lead.report.assign,type:0
msgid "Opportunity"
msgstr "Prilika"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Opportunity -"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.partner
msgid "Our Partners"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:104
#, python-format
msgid "Overdue Activities"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_delay_expected
msgid "Overpassed Deadline"
msgstr "Proslo Krajnji Rok"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_partner_assigned_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_partner_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.grade_in_detail
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Partner"
msgstr "Partner"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_partner_assign_form
msgid "Partner Activation"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.res_partner_activation_act
#: model:ir.ui.menu,name:website_crm_partner_assign.res_partner_activation_config_mi
msgid "Partner Activations"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_forward_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner_assignation_lines
msgid "Partner Assignation"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.res_partner_grade_action
#: model:ir.ui.menu,name:website_crm_partner_assign.menu_res_partner_grade_action
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_tree
msgid "Partner Level"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation_partner_location
msgid "Partner Location"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_partner_assign_form
msgid "Partner Review"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Partner assigned Analysis"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_partner_declined_ids
msgid "Partner not interested"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead_partner_assigned_id
msgid "Partner this case has been forwarded/assigned to."
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Partners"
msgstr "Partneri"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.action_report_crm_partner_assign
msgid "Partnership Analysis"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_date_partnership
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_date_partnership
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users_date_partnership
msgid "Partnership Date"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.ui.menu,name:website_crm_partner_assign.menu_report_crm_partner_assign_tree
msgid "Partnerships"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Phone"
msgstr "Telefon:"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_planned_revenue
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Planned Revenue"
msgstr "Planirani Prihod"

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_platinium
msgid "Platinum"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_priority
msgid "Priority"
msgstr "Prioritet"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Priority:"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:113
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
#, python-format
msgid "Probability"
msgstr "Verovatnoca"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_probable_revenue
msgid "Probable Revenue"
msgstr "Moguci Prihod"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.references_block
msgid "References"
msgstr "Reference"

#. module: website_crm_partner_assign
#: model:ir.ui.menu,name:website_crm_partner_assign.crm_menu_resellers
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.footer_custom
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.layout
msgid "Resellers"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_team_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_team_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_lead_assign_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Sales Channel"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_lead_assign_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Salesperson"
msgstr "Prodavač"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Search"
msgstr "Pronađi"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Send"
msgstr "Pošalji"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_geo_assign_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_opportunity_geo_assign_form
msgid "Send Email"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Send Mail"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation_sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_sequence_10762
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users_grade_sequence
msgid "Sequence"
msgstr "Prioritet"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:72
#, python-format
msgid "Set an email address for the partner %s"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:70
#, python-format
msgid "Set an email address for the partner(s): %s"
msgstr ""

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_silver
msgid "Silver"
msgstr ""

#. module: website_crm_partner_assign
#: model:crm.lead.tag,name:website_crm_partner_assign.tag_portal_lead_is_spam
msgid "Spam"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:114
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#, python-format
msgid "Stage"
msgstr "Nivo"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "States..."
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Street"
msgstr "Ulica"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Street2"
msgstr "Ulica2"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_tag_ids
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Tags"
msgstr "Oznake"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:60
#, python-format
msgid "The Forward Email Template is not in the database"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "There are no leads."
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "There are no opportunities."
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:102
#, python-format
msgid "This Week Activities"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "This lead is a spam"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Title"
msgstr "Naslov"

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:101
#, python-format
msgid "Today Activities"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_turnover
msgid "Turnover"
msgstr "Obrt"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_type
msgid "Type"
msgstr "Tip"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead_report_assign_type
msgid "Type is used to separate Leads and Opportunities"
msgstr "Tip se koristi za razdvajanja Tragova od Prilika"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_report_assign_user_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign_user_id
msgid "User"
msgstr "Korisnik"

#. module: website_crm_partner_assign
#: selection:crm.lead.report.assign,priority:0
msgid "Very High"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade_website_published
msgid "Visible in Website"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "What is the next action? When? What is the expected revenue?"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Why aren't you interested by this lead?"
msgstr ""

#. module: website_crm_partner_assign
#: code:addons/website_crm_partner_assign/controllers/main.py:105
#, python-format
msgid "Won"
msgstr "Dobitak"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid "World Map"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "YYYY-MM-DD"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "ZIP"
msgstr ""

#. module: website_crm_partner_assign
#: selection:crm.lead.forward.to.partner,forward_type:0
msgid "a single partner: manual selection of partner"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "at"
msgstr "u"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead_assignation
msgid "crm.lead.assignation"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead_forward_to_partner
msgid "crm.lead.forward.to.partner"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
msgid "e.g. Gold Partner"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "on"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "or"
msgstr "ili"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "pull-left"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "reference(s)"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_activation
msgid "res.partner.activation"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_grade
msgid "res.partner.grade"
msgstr ""

#. module: website_crm_partner_assign
#: selection:crm.lead.forward.to.partner,forward_type:0
msgid ""
"several partners: automatic assignation, using GPS coordinates and partner's"
" grades"
msgstr ""
