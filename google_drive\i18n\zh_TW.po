# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_drive
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2021\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"<b>To create a new filter:</b><br/>\n"
"                                - Go to the Odoo document you want to filter. For instance, go to Opportunities and search on Sales Department.<br/>\n"
"                                - In this \"Search\" view, select the option \"Save Current Filter\", enter the name (Ex: Sales Department)<br/>\n"
"                                - If you select \"Share with all users\", link of google document in \"More\" options will appear for all users in opportunities of Sales Department.<br/>\n"
"                                - If you don't select \"Share with all users\", link of google document in \"More\" options will not appear for other users in opportunities of Sales Department.<br/>\n"
"                                - If filter is not specified, link of google document will appear in \"More\" option for all users for all opportunities."
msgstr ""
"<b>建立一個新篩選:</b><br/>\n"
"                                - 去到您要過濾的Odoo文件. 比如, 比如在銷售部門搜尋商機.<br/>\n"
"                                -在「搜尋」視圖, 選擇 \"儲存現有篩選\", 輸入名字 (比如: 銷售部門)<br/>\n"
"                                - 如果您在 \"更多\" 選項中選擇 \"和所有使用者共享\", Google文件連結就會顯示給所有銷售部門的使用者.<br/>\n"
"                                - 如果您沒有在 \"更多\" 選項中選擇 \"和所有使用者共享\", Google文件連結則不會顯示給所有銷售部門的使用者.<br/>\n"
"                                -如果篩選沒有被指定, Google文件的連結就會顯示在 \"更多\" 選項中為所有的使用者和商機."

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Reset token"
msgstr "<i class=\"fa fa-arrow-right\"/> 重置金鑰"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Set up token"
msgstr "<i class=\"fa fa-arrow-right\"/>設置金鑰"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-exclamation-triangle text-warning\"/> &amp;nbsp; No refresh"
" token set"
msgstr ""
"<i class=\"fa fa-exclamation-triangle text-warning\"/> &amp;nbsp; 未設置refresh"
" 金鑰"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"text-success fa fa-check\"/> &amp;nbsp; Refresh token set"
msgstr "<i class=\"text-success fa fa-check\"/> &amp;nbsp; 設置Refresh 金鑰"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "<span>Get an authorization code and set it in the field below.</span>"
msgstr "<span>獲取授權代碼並將其設置在下面的欄位中。</span>"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Active</strong>"
msgstr "<strong>主動</strong>"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Model</strong>"
msgstr "<strong>模型</strong>"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Template</strong>"
msgstr "<strong>模板</strong>"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__active
msgid "Active"
msgstr "啟用"

#. module: google_drive
#: model_terms:ir.actions.act_window,help:google_drive.action_google_drive_users_config
msgid "Add a new template"
msgstr "添加新範本"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_config_view_search
msgid "Archived"
msgstr "歸檔"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "At least one key cannot be found in your Google Drive name pattern."
msgstr "在您的 Google Drive名稱模式中找不到至少一個金鑰。"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__google_drive_authorization_code
msgid "Authorization Code"
msgstr "授權碼"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "Cancel"
msgstr "取消"

#. module: google_drive
#: model:ir.model.fields,help:google_drive.field_google_drive_config__name_template
msgid ""
"Choose how the new google drive will be named, on google side. Eg. "
"gdoc_%(field_name)s"
msgstr "選擇新的Google Drive 命名方式，比如gdoc_%(field_name)s"

#. module: google_drive
#: model:ir.model,name:google_drive.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "Confirm"
msgstr "確認"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__create_uid
msgid "Created by"
msgstr "創立者"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__create_date
msgid "Created on"
msgstr "建立於"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Creating google drive may only be done by one at a time."
msgstr "建立Google驅動器只能一次完成。"

#. module: google_drive
#: model:ir.filters,name:google_drive.filter_partner
msgid "Customer"
msgstr "客戶"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__filter_id
msgid "Filter"
msgstr "篩選"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "Get Authorization Code"
msgstr "獲取授權碼"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "至配置面板"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_client_id
msgid "Google Client"
msgstr "Google客戶"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_tree
msgid "Google Drive Configuration"
msgstr "Google Drive設定"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__name_template
msgid "Google Drive Name Pattern"
msgstr "Google Drive名稱模式"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "Google Drive Templates"
msgstr "Google Drive 模板"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Google Drive is not yet configured. Please contact your administrator."
msgstr "Google Drive還沒有被配置成功,請與管理員聯繫。"

#. module: google_drive
#: model:ir.model,name:google_drive.model_google_drive_config
msgid "Google Drive templates config"
msgstr "Google Drive 模板配置"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__id
msgid "ID"
msgstr "ID"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"Incoherent Google Drive %(drive)s: the model of the selected filter "
"%(filter)r is not matching the model of current template (%(filter_model)r, "
"%(drive_model)r)"
msgstr ""
"不連貫的 Google Drive %(drive)s：所選過濾器 %(filter)r 的模型與當前模板的模型不匹配 "
"(%(filter_model)r, %(drive_model)r)"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: google_drive
#: model_terms:ir.actions.act_window,help:google_drive.action_google_drive_users_config
msgid ""
"Link your own google drive templates to any record of Odoo. If you have "
"really specific documents you want your collaborator fill in, e.g. Use a "
"spreadsheet to control the quality of your product or review the delivery "
"checklist for each order in a foreign country, ... Its very easy to manage "
"them, link them to Odoo and use them to collaborate with your employees."
msgstr ""
"連結您的Google Drive模板到任何Odoo的記錄。如果您有很特定的文件需要您的合作者填寫, "
"比如：使用電子試算表來控制您的產品的品質或者檢查每個外國訂單的發貨清單, ...這很容易管理他們, 把它們連結到Odoo並且和您的員工一起使用它們。"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__model_id
msgid "Model"
msgstr "模型"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Please enter a valid Google Document URL."
msgstr "請輸入一個有效的Google文件網址。"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__is_google_drive_token_generated
msgid "Refresh Token Generated"
msgstr "Refresh 金鑰生成"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__model
msgid "Related Model"
msgstr "相關的模型"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_resource_id
msgid "Resource Id"
msgstr "資源標識"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_config_view_search
msgid "Search Google Drive Config"
msgstr "搜尋Google Drive 設置"

#. module: google_drive
#: code:addons/google_drive/models/res_config_settings.py:0
#, python-format
msgid "Set up refresh token"
msgstr "設置 refresh 金鑰"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"Something went wrong during the token generation. Please request again an "
"authorization code ."
msgstr "金鑰生成過程中出錯了。請重新申請授權碼。"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__name
msgid "Template Name"
msgstr "模板名稱"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_template_url
msgid "Template URL"
msgstr "模板網址"

#. module: google_drive
#: model:ir.actions.act_window,name:google_drive.action_google_drive_users_config
msgid "Templates"
msgstr "模板"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "The Google Template cannot be found. Maybe it has been deleted."
msgstr "沒有找到Google模板。也許它已被刪除。"

#. module: google_drive
#: model:ir.model.fields,help:google_drive.field_res_config_settings__google_drive_uri
msgid "The URL to generate the authorization code from Google"
msgstr "以生成Google的授權碼的網址"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"The document filter must not include any 'dynamic' part, so it should not be"
" based on the current time or current user, for example."
msgstr "文檔過濾器不得包含任何“動態”部分，因此它不應基於當前時間或當前用戶，例如。"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"The name of the attached document can use fixed or variable data. To distinguish between documents in\n"
"                                Google Drive, use fixed words and fields. For instance, in the example above, if you wrote Deco_Addict_%(name)s_Sales\n"
"                                in the Google Drive name field, the document in your Google Drive and in Odoo attachment will be named\n"
"                                'Deco_Addict_SO0001_Sales'."
msgstr ""
"附加文檔的名稱可以使用固定或可變資料。要區分 Google Drive中的文檔, 請使用固定的單詞和欄位。\n"
"例如, 在上面的示例中, 如果您在 Google Drive名稱欄位中編寫了 Deco_Addict_%(name)s_Sales, \n"
"則 Google Drive和 Odoo 附件中的文檔將被命名為 \"Deco_Addict_SO0001_Sales\"。"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"The permission 'reader' for 'anyone with the link' has not been written on "
"the document"
msgstr "'任何人的聯繫'的'讀取'許可尚未寫入文件"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"There is no refresh code set for Google Drive. You can set it up from the "
"configuration panel."
msgstr "沒有為 Google Drive設置金鑰。可以從配置面板進行設置。"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "This module will stop working after the 3rd October 2022 due to"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__google_drive_uri
msgid "URI"
msgstr "URI"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "changes in Google Authentication API"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"https://docs.google.com/document/d/1vOtpJK9scIQz6taD9tJRIETWbEw3fSiaQHArsJYcua4/edit"
msgstr ""
"https://docs.google.com/document/d/1vOtpJK9scIQz6taD9tJRIETWbEw3fSiaQHArsJYcua4/edit"
