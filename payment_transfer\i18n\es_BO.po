# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_transfer
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-24 19:58+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Bolivia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_BO/)\n"
"Language: es_BO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_acquirer.py:68
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_acquirer.py:66
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_transfer
#: model_terms:payment.acquirer,cancel_msg:payment_transfer.payment_acquirer_transfer
msgid "<span><i>Cancel,</i> Your payment has been cancelled.</span>"
msgstr ""

#. module: payment_transfer
#: model_terms:payment.acquirer,done_msg:payment_transfer.payment_acquirer_transfer
msgid ""
"<span><i>Done,</i> Your online payment has been successfully processed. "
"Thank you for your order.</span>"
msgstr ""

#. module: payment_transfer
#: model_terms:payment.acquirer,error_msg:payment_transfer.payment_acquirer_transfer
msgid ""
"<span><i>Error,</i> Please be aware that an error occurred during the "
"transaction. The order has been confirmed but won't be paid. Don't hesitate "
"to contact us if you have any questions on the status of your order.</span>"
msgstr ""

#. module: payment_transfer
#: model_terms:payment.acquirer,pending_msg:payment_transfer.payment_acquirer_transfer
msgid ""
"<span><i>Pending,</i> Your online payment has been successfully processed. "
"But your order is not validated yet.</span>"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_acquirer.py:30
#: model_terms:payment.acquirer,post_msg:payment_transfer.payment_acquirer_transfer
#, python-format
msgid "Bank Account"
msgstr "Cuenta bancaria"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_acquirer.py:30
#, python-format
msgid "Bank Accounts"
msgstr "Cuentas de banco"

#. module: payment_transfer
#: model_terms:payment.acquirer,post_msg:payment_transfer.payment_acquirer_transfer
msgid "Communication"
msgstr ""

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_transfer
#: model_terms:payment.acquirer,post_msg:payment_transfer.payment_acquirer_transfer
msgid "Please use the following transfer details"
msgstr ""

#. module: payment_transfer
#: model_terms:payment.acquirer,post_msg:payment_transfer.payment_acquirer_transfer
msgid "Please use the order name as communication reference."
msgstr ""

#. module: payment_transfer
#: model_terms:payment.acquirer,pre_msg:payment_transfer.payment_acquirer_transfer
msgid "Transfer information will be provided after choosing the payment mode."
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_acquirer.py:19
#: model:payment.acquirer,name:payment_transfer.payment_acquirer_transfer
#, python-format
msgid "Wire Transfer"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_acquirer.py:64
#, python-format
msgid "received data for reference %s"
msgstr ""
