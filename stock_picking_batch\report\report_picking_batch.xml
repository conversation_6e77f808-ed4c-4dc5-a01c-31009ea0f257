<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="stock_picking_batch.report_picking_batch">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-set="move_line_ids" t-value="o.picking_ids.mapped('move_line_ids')"/>
                    <t t-set="has_package" t-value="move_line_ids.filtered('result_package_id')" groups="stock.group_tracking_lot"/>
                    <t t-set="has_serial_number" t-value="move_line_ids.filtered('lot_id')" groups="stock.group_production_lot"/>
                    <t t-set="has_barcode" t-value="move_line_ids.mapped('product_id').filtered('barcode')"/>
                    <t t-set="locations" t-value="move_line_ids.mapped('location_id').sorted(lambda location: location.complete_name)"/>
                    <t t-call="web.external_layout">
                        <div class="page">
                            <div class="d-flex">
                                <div><h3>Summary: <span t-field="o.name"/></h3></div>
                                <div class="mr-auto">
                                    <div t-field="o.name" t-options="{'widget': 'barcode', 'width': 600, 'height': 150, 'img_style': 'width:300px;height:50px;'}"/>
                                </div>
                            </div>
                            <div t-if="o.user_id">
                                <strong>Responsible:</strong>
                                <span t-field="o.user_id"/>
                            </div><br/>
                            <table class="table table-condensed">
                                <thead>
                                    <tr>
                                        <th>Transfer</th>
                                        <th>Barcode</th>
                                        <th>Status</th>
                                        <th>Scheduled Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr t-foreach="o.picking_ids" t-as="pick">
                                        <td>
                                            <span t-field="pick.name"/>
                                        </td>
                                        <td>
                                            <div t-field="pick.name" t-options="{'widget': 'barcode', 'quiet': 0, 'width': 400, 'height': 100, 'img_style': 'width:200px;height:50px;'}"/>
                                        </td>
                                        <td>
                                            <span t-field="pick.state"/>
                                        </td>
                                        <td >
                                            <span t-field="pick.scheduled_date"/>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <p style="page-break-after: always;"/>
                            <h3><span t-field="o.name"/></h3>
                            <div t-if="o.user_id">
                                <strong>Responsible:</strong>
                                <span t-field="o.user_id"/>
                            </div><br/>
                            <table class="table table-condensed">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Quantity</th>
                                        <th width="20%">Transfer</th>
                                        <th t-if="has_serial_number" width="15%">
                                            <strong>Lot/Serial Number</strong>
                                        </th>
                                        <th t-if="has_barcode" width="15%" class="text-center">
                                            <strong>Product Barcode</strong>
                                        </th>
                                        <th t-if="has_package" width="15%">
                                            <strong>Package</strong>
                                        </th>
                                    </tr>
                                </thead>
                                <t t-foreach="locations" t-as="location_from">
                                    <t t-set="loc_move_line" t-value="move_line_ids.filtered(lambda x: x.location_id==location_from)"/>
                                    <t t-foreach="loc_move_line.location_dest_id" t-as="location_dest">
                                        <t t-set="move_lines" t-value="loc_move_line.filtered(lambda x: x.location_dest_id==location_dest)"/>
                                        <t t-set="products" t-value="move_lines.product_id"/>
                                        <tbody>
                                            <tr>
                                                <td style="background-color:lightgrey" colspan="6" >
                                                    <p style="margin:0px"><strong>FROM</strong>
                                                    <span t-esc="move_lines.location_id.display_name"/></p>
                                                    <p style="margin:0px"><strong>TO</strong>
                                                    <span t-esc="move_lines.location_dest_id.display_name"/></p>
                                                </td>
                                            </tr>
                                            <tr t-foreach="move_lines" t-as="move_operation">
                                                <td>
                                                    <span t-field="move_operation.display_name"/>
                                                </td>
                                                <td>
                                                    <span t-if="has_package or move_operation.state == 'done'"
                                                          t-esc="sum(move_operation.mapped('qty_done'))"/>
                                                    <span t-else="" t-esc="sum(move_operation.mapped('product_uom_qty'))"/>
                                                    <span t-field="move_operation.uom_id" groups="move_operation.group_uom"/>
                                                </td>
                                                <td>
                                                    <span t-esc="move_operation.picking_id.display_name"/>
                                                </td>
                                                <td t-if="has_serial_number" class="text-center h6" width="15%">
                                                    <div t-if="move_operation.lot_id or move_operation.lot_name" t-field="move_operation.lot_id.name" t-options="{'widget': 'barcode', 'humanreadable': 1, 'width': 600, 'height': 100, 'img_style': 'width:100%;height:35px;'}"/>
                                                </td>
                                                <td width="15%" class="text-center" t-if="has_barcode">
                                                    <span t-if="move_operation.product_id and move_operation.product_id.barcode">
                                                        <div t-field="move_operation.product_id.barcode" t-options="{'widget': 'barcode', 'symbology': 'auto', 'width': 600, 'height': 100, 'img_style': 'width:100%;height:35px;'}"/>
                                                    </span>
                                                </td>
                                                <td t-if="has_package" width="15%">
                                                    <span t-field="move_operation.package_id"/>
                                                    <t t-if="move_operation.result_package_id">
                                                        <strong>→</strong> <span t-field="move_operation.result_package_id"/>
                                                    </t>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </t>
                                </t>
                            </table>
                         </div>
                     </t>
                 </t>
            </t>
        </template>
    </data>
</odoo>
