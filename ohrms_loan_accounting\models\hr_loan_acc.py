# -*- coding: utf-8 -*-
import time
from odoo import models, api, fields
from odoo.exceptions import UserError, ValidationError
from datetime import datetime

class HrLoanAcc(models.Model):
    _inherit = 'hr.loan'

    employee_account_id = fields.Many2one('account.account', string="Loan Account")
    treasury_account_id = fields.Many2one('account.account', string="Treasury Account")
    middle_account_id = fields.Many2one('account.account', required = True, string="Middle Account for Loan")
    journal_id = fields.Many2one('account.journal', string="Journal")

    state = fields.Selection([
        ('draft', 'Draft'),
        ('waiting_approval_1', 'Manager Approval'),
        ('waiting_approval_2', 'HR Approval'),
        ('approve', 'Finance Approved'),
        ('refuse', 'Finance Refused'),
        ('cancel', 'Canceled'),
    ], string="State", default='draft', track_visibility='onchange', copy=False, )

    manager_id = fields.Many2one('hr.employee', readonly=True, related='employee_id.parent_id', string="المدير المباشر")
    is_manager = fields.Boolean(compute='call_with_sudo_is_manager')

    payment_method = fields.Selection([('cash','Cash'),('check','Check')])
    suke_id = fields.Many2one('sukuk.management.page')


    ########## requested by elbahry 2023-02-12
    is_admin = fields.Boolean(compute="get_is_admin")
    def get_is_admin(self):
        if self.env.user.id == 2:
            self.is_admin = True
        else:
            self.is_admin = False

    def make_draft(self):
        self.state = 'draft'



    #############################################3

    def compute_button_visible(self):
        for rec in self:
            rec.is_manager = False
            if rec.manager_id.user_id.id == self._uid:
                rec.is_manager = True

    @api.onchange('employee_id')
    def call_with_sudo_is_manager(self):
        self.sudo().compute_button_visible()


    def action_submit(self):
        self.sudo().action_send_notification_to_hr()
        return super(HrLoanAcc,self).action_submit()


    def action_approve(self):
        """This create account move for request.
            """
        #######3 Added at 2023-06-21 to link Loan with Sukuke
        if (self.payment_method == 'check') and (not self.suke_id):
            raise ValidationError("This type of Payment Requires to link with Check")
        ##############################
        contract_obj = self.env['hr.contract'].search([('employee_id', '=', self.employee_id.id)])
        if not contract_obj:
            raise UserError('You must Define a contract for employee')
        if not self.loan_lines:
            raise UserError('You must compute installment before Approved')
        ########### HR Approvales made by Abdulwahed Freaa 2023-07-08
        loan_approve = self.env['ir.config_parameter'].sudo().get_param('account.loan_approve')
        # if loan_approve:
        #     self.write({'state': 'waiting_approval_2'})
        if True:
            self.write({'state': 'waiting_approval_2'})
        ####################################
        else:
            if not self.employee_account_id or not self.treasury_account_id or not self.journal_id:
                raise UserError("You must enter employee account & Treasury account and journal to approve ")
            if not self.loan_lines:
                raise UserError('You must compute Loan Request before Approved')
            timenow = time.strftime('%Y-%m-%d')
            for loan in self:
                amount = loan.loan_amount
                ## by freaa
                loan_name = loan.employee_id.name

                reference = loan.name
                journal_id = loan.journal_id.id
                debit_account_id = loan.treasury_account_id.id
                credit_account_id = loan.employee_account_id.id
                if self.suke_id:
                    loan_name1 = loan_name + ' check' + '-' + str(self.suke_id.bank_id.name) + '-' + str(self.suke_id.branch_id.name) + '- Serial No:' + str(self.suke_id.serial_no)
                else:
                    loan_name1 = loan_name
                ###
                debit_vals = {
                    'name': loan_name1+'_'+ str(datetime.now().date()).replace('-','')+'_D',
                    'account_id': debit_account_id,
                    'journal_id': journal_id,
                    'date': timenow,
                    'debit': amount > 0.0 and amount or 0.0,
                    'credit': amount < 0.0 and -amount or 0.0,
                    'loan_id': loan.id,
                }
                credit_vals = {
                    'name': loan_name1+'_'+ str(datetime.now().date()).replace('-','')+'_C',
                    'account_id': credit_account_id,
                    'journal_id': journal_id,
                    'date': timenow,
                    'debit': amount < 0.0 and -amount or 0.0,
                    'credit': amount > 0.0 and amount or 0.0,
                    'loan_id': loan.id,
                }
                vals = {
                    'name': 'Loan For' + ' ' + loan_name+'_'+ str(datetime.now().date()).replace('-','')+'_E',
                    'narration': loan_name,
                    'ref': reference,
                    'journal_id': journal_id,
                    'date': timenow,
                    'line_ids': [(0, 0, debit_vals), (0, 0, credit_vals)]
                }
                move = self.env['account.move'].create(vals)
                move.post()
            self.write({'state': 'approve'})
        self.sudo().action_send_notification_to_finance()
        return True

    def action_double_approve(self):
        """This create account move for request in case of double approval.
            """
        if not self.employee_account_id or not self.treasury_account_id or not self.journal_id:
            raise UserError("You must enter employee account & Treasury account and journal to approve ")
        if not self.loan_lines:
            raise UserError('You must compute Loan Request before Approved')
        timenow = time.strftime('%Y-%m-%d')
        for loan in self:
            amount = loan.loan_amount
            loan_name = loan.employee_id.name
            reference = loan.name
            journal_id = loan.journal_id.id
            debit_account_id = loan.treasury_account_id.id
            credit_account_id = loan.employee_account_id.id
            debit_vals = {
                'name': loan_name,
                'account_id': debit_account_id,
                'journal_id': journal_id,
                'date': timenow,
                'debit': amount > 0.0 and amount or 0.0,
                'credit': amount < 0.0 and -amount or 0.0,
                'loan_id': loan.id,
            }
            credit_vals = {
                'name': loan_name,
                'account_id': credit_account_id,
                'journal_id': journal_id,
                'date': timenow,
                'debit': amount < 0.0 and -amount or 0.0,
                'credit': amount > 0.0 and amount or 0.0,
                'loan_id': loan.id,
            }
            vals = {
                'name': 'Loan For' + ' ' + loan_name,
                'narration': loan_name,
                'ref': reference,
                'journal_id': journal_id,
                'date': timenow,
                'line_ids': [(0, 0, debit_vals), (0, 0, credit_vals)]
            }
            move = self.env['account.move'].create(vals)
            move.post()
        self.write({'state': 'approve'})
        return True

    def action_send_notification_to_hr(self):
        email_from = self.employee_id
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (self.id, self._name)
        body = """
        <div dir="rtl">
        <p><font style="font-size: 14px;"> لديك طلب موافقة على سلفة </font></p>
                  <p><font style="font-size: 14px;"> مقدم الطلب """+str(email_from.name)+"""</font></p>
                  <a href="%s">Request Link</a>
        </div>""" % (web_base_url)

        hr = self.env['res.users'].search([("groups_id", "=", self.env.ref("hr_approvales_masarat.group_hr_approvales_masarat").id)])
        for elem in hr:
            email_to = elem.login
            template_id = self.env['mail.mail'].create({
                'subject':'طلب سلفة',
                'email_from':email_from.work_email,
                'email_to': email_to,
                'body_html':body})
            #### freaa
            template_id.sudo().send()

    def action_send_notification_to_finance(self):
        email_from = self.employee_id
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (self.id, self._name)
        body = """
        <div dir="rtl">
        <p><font style="font-size: 14px;"> لديك طلب موافقة على سلفة </font></p>
                  <p><font style="font-size: 14px;"> مقدم الطلب """+str(email_from.name)+"""</font></p>
                  <a href="%s">Request Link</a>
        </div>""" % (web_base_url)

        finance = self.env['res.users'].search([("groups_id", "=", self.env.ref("masarat_expenses_requist.group_finance_approvales").id)])
        for elem in finance:
            email_to = elem.login
            template_id = self.env['mail.mail'].create({
                'subject':'طلب سلفة',
                'email_from':email_from.work_email,
                'email_to': email_to,
                'body_html':body})
            #### freaa
            template_id.sudo().send()

    def action_send_notification_to_maneger(self,recorde_id):
        # gm =  self.env['res.users'].search([("groups_id", "=", self.env.ref("hr_approvales_masarat.group_gm_reward_masarat").id)], limit=1)
        # if gm:
        #     email_to = gm.login
        # else:
        #     email_to = False
        loan_id = self.env['hr.loan'].search([('id','=',recorde_id)])
        email_from = loan_id.employee_id
        email_to = loan_id.manager_id.work_email
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recorde_id, self._name)

        body = """
        <div dir="rtl">
        <p><font style="font-size: 14px;"> لديك طلب موافقة على سلفة </font></p>
                  <p><font style="font-size: 14px;"> مقدم الطلب """+str(email_from.name)+"""</font></p>
                  <a href="%s">Request Link</a>
        </div>""" % (web_base_url)
        template_id = self.env['mail.mail'].create({
            'subject':'طلب سلفة',
            'email_from':email_from.work_email,
            'email_to': email_to,
            'body_html':body
        })
        #### freaa
        template_id.sudo().send()

    @api.model
    def create(self,vals):
        res = super(HrLoanAcc, self).create(vals)
        recode_id = res.id
        self.sudo().action_send_notification_to_maneger(recode_id)
        return res


class HrLoanLineAcc(models.Model):
    _inherit = "hr.loan.line"

    def action_paid_amount(self):
        """This create the account move line for payment of each installment.
            """
        timenow = time.strftime('%Y-%m-%d')
        for line in self:
            if line.loan_id.state != 'approve':
                raise UserError("Loan Request must be approved")
            amount = line.amount
            loan_name = line.loan_id.employee_id.name
            reference = line.loan_id.name
            journal_id = line.loan_id.journal_id.id
            debit_account_id = line.loan_id.employee_account_id.id
            middle_account_id = line.loan_id.middle_account_id.id ## Added By abdalwahed at 2022-12-05
            credit_account_id = line.loan_id.treasury_account_id.id
            debit_vals = {
                'name': loan_name+'_'+ str(datetime.now()).replace('-','').replace(' ','').replace(':','')+'_D',
                # 'account_id': debit_account_id,
                'account_id': middle_account_id,
                'journal_id': journal_id,
                'date': timenow,
                'debit': amount > 0.0 and amount or 0.0,
                'credit': amount < 0.0 and -amount or 0.0,
            }
            credit_vals = {
                'name': loan_name+'_'+ str(datetime.now()).replace('-','').replace(' ','').replace(':','')+'_C',
                'account_id': credit_account_id,
                'journal_id': journal_id,
                'date': timenow,
                'debit': amount < 0.0 and -amount or 0.0,
                'credit': amount > 0.0 and amount or 0.0,
            }
            vals = {
                'name': 'Loan For' + ' ' + loan_name+'_'+ str(datetime.now()).replace('-','').replace(' ','').replace(':','')+'_V',
                'narration': loan_name,
                'ref': reference,
                'journal_id': journal_id,
                'date': timenow,
                'line_ids': [(0, 0, debit_vals), (0, 0, credit_vals)]
            }
            move = self.env['account.move'].create(vals)
            move.post()
        return True


class HrPayslipAcc(models.Model):
    _inherit = 'hr.payslip'

    def action_payslip_done(self):
        for line in self.input_line_ids:
            if line.loan_line_id:
                print(line.code, line.name)
                line.loan_line_id.action_paid_amount()
        return super(HrPayslipAcc, self).action_payslip_done()
