# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fetchmail_gmail
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-25 14:36+0000\n"
"PO-Revision-Date: 2022-03-03 13:46+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Connect your Gmail account"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Conectar su cuenta de Gmail"

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid ""
"<i class=\"fa fa-cog\"/>\n"
"                        Edit Settings"
msgstr ""
"<i class=\"fa fa-cog\"/>\n"
"                        Editar ajustes"

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '=', False)]}\" class=\"badge badge-success\">\n"
"                        Gmail Token Valid\n"
"                    </span>"
msgstr ""

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid "Gmail"
msgstr "Gmail"

#. module: fetchmail_gmail
#: code:addons/fetchmail_gmail/models/fetchmail_server.py:0
#, python-format
msgid "Gmail authentication only supports IMAP server type."
msgstr "La autenticación de Gmail solo admite el tipo de servidor IMAP."

#. module: fetchmail_gmail
#: model:ir.model,name:fetchmail_gmail.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr "Servidor de correo entrante"

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid ""
"Setup your Gmail API credentials in the general settings to link a Gmail "
"account."
msgstr ""
"Configure sus credenciales API de Gmail en los ajustes generales para "
"vincular una cuenta de Gmail."
