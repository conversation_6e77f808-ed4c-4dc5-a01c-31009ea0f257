# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON> <brenci<PERSON><PERSON><EMAIL>>, 2021
# ka<PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON> <dak<PERSON>@gmail.com>, 2021\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Create Invoice"
msgstr "Vytvořit fakturu"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__visible_project
msgid "Display project"
msgstr "Zobrazit projekt"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__project_id
msgid "Generated Project"
msgstr "Vytvořený projekt"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__task_id
msgid "Generated Task"
msgstr "Vytvořený úkol"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_to_invoice
msgid "Has SO to Invoice"
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice as soon as this service is sold, and create a task in an existing "
"project to track the time spent."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold, and create a "
"project for the order with a task for each sales order line to track the "
"time spent."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold, and create an "
"empty project for the order to track the time spent."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__is_service
msgid "Is a Service"
msgstr "Je služba"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid "New"
msgstr "Nové"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "None"
msgstr "Nic"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "Nic"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_count
msgid "Number of Projects"
msgstr "Počet projektů"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__invoice_count
msgid "Number of invoices"
msgstr "Počet daňových dokladů"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_project.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product"
msgstr "Produkt"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product Template"
msgstr "Šablona produktu"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "Projekt"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "Šablona projektu"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__project_id
msgid "Project generated by the sales order item"
msgstr "Projekt vygenerován položkou prodejní objednávky"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "Projektová prodejní objednávka"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_ids
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Projects"
msgstr "Projekty"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr "Projekty použité v této zakázce odběratele."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
msgid "Sale Order"
msgstr "Prodejní objednávka"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#: code:addons/sale_project/models/project.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order"
msgstr "Prodejní objednávka"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
msgid "Sales Order Id"
msgstr ""

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order Item"
msgstr "Položka objednávky"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added, in "
"order to be invoiced to your customer."
msgstr ""

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "Řádek zakázky"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__is_service
msgid ""
"Sales Order item should generate a task and/or a project, depending on the "
"product settings."
msgstr ""
"Položka prodejní objednávky by měla generovat úkol a/nebo projekt, závisí na"
" nastavení produktu."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item to which the project is linked. Link the timesheet entry to"
" the sales order item defined on the project. Only applies on tasks without "
"sale order item defined, and if the employee is not in the 'Employee/Sales "
"Order Item Mapping' of the project."
msgstr ""
"Položka prodejní objednávky, ke které je projekt propojen. Propojte položku "
"časového rozvrhu s položkou prodejní objednávky definovanou v projektu. "
"Platí pouze pro úkoly bez definované položky prodejní objednávky a pokud "
"zaměstnanec není v projektu „Mapování položky zaměstnance / prodejní "
"objednávky“."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,help:sale_project.field_project_task__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "Zakázka odběratele, ke které je projekt propojen."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Zakázka odběratele, ke které je úkol propojen."

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr ""

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr ""

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order Item"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__project_id
#: model:ir.model.fields,help:sale_project.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr ""
"Vyberte zúčtovatelný projekt, na kterém lze vytvářet úkoly. Toto nastavení "
"musí být nastaveno pro každou společnost."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,help:sale_project.field_product_template__project_template_id
msgid ""
"Select a billable project to be the skeleton of the new created project when"
" selling the current product. Its stages and tasks will be duplicated."
msgstr ""
"Vyberte zúčtovatelný projekt, který bude kostrou nově vytvořeného projektu "
"při prodeji aktuálního produktu. Jeho fáze a úkoly budou duplikovány."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr "Vybrat neúčtovatelný projekt, na kterém lze vytvářet úkoly."

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "Úloha"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"Task Created (%s): <a href=# data-oe-model=project.task data-oe-id=%d>%s</a>"
msgstr ""
"Úkol vytvořen (%s): <a href=# data-oe-model=project.task data-oe-"
"id=%d>%s</a>"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Opakování úkolu"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__task_id
msgid "Task generated by the sales order item"
msgstr "Úkol vytvořen položkou prodejní objednávky."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Tasks"
msgstr "Úkoly"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Analýza úkolů"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr "Úkoly spojeny s tímto prodejem"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "There is nothing to invoice in this project."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"This task has been created from: <a href=# data-oe-model=sale.order data-oe-"
"id=%d>%s</a> (%s)"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "Fakturovat"

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You have to unlink the task from the sale order item in order to delete it."
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "for sale order item:"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "for sale order:"
msgstr ""
