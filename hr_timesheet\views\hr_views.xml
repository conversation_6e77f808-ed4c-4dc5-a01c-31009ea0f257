<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="timesheet_action_from_employee" model="ir.actions.act_window">
        <field name="name">Timesheets</field>
        <field name="res_model">account.analytic.line</field>
        <field name="search_view_id" ref="hr_timesheet_line_search"/>
        <field name="domain">[('project_id', '!=', False)]</field>
        <field name="context">{
            'search_default_month':1,
            'search_default_employee_id': [active_id],
            'default_employee_id': active_id
        }</field>
    </record>

    <record id="timesheet_action_view_from_employee_list" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="hr_timesheet_line_tree"/>
        <field name="act_window_id" ref="timesheet_action_from_employee"/>
    </record>

    <record id="timesheet_action_view_from_employee_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_timesheet_line_form"/>
        <field name="act_window_id" ref="timesheet_action_from_employee"/>
    </record>

    <record id="hr_employee_view_form_inherit_timesheet" model="ir.ui.view">
        <field name="name">hr.employee.form.timesheet</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <group name="application_group" position="attributes">
                <attribute name="string">Application Settings</attribute>
            </group>
            <group name="application_group" position="inside">
                <label for="timesheet_cost" groups="hr_timesheet.group_timesheet_manager"/>
                <div name="timesheet" groups="hr_timesheet.group_timesheet_manager">
                    <field name="timesheet_cost" class="oe_inline"/> per hour
                    <field name="currency_id" invisible="1"/>
                </div>
            </group>
            <div name="button_box" position="inside">
                <button class="oe_stat_button" type="action" name="%(timesheet_action_from_employee)d" icon="fa-calendar" groups="hr_timesheet.group_hr_timesheet_user">
                    <div class="o_stat_info">
                        <span class="o_stat_text">Timesheets</span>
                    </div>
                </button>
            </div>
        </field>
    </record>

    <record id="hr_department_view_kanban" model="ir.ui.view">
        <field name="name">hr.department.kanban.inherit</field>
        <field name="model">hr.department</field>
        <field name="inherit_id" ref="hr.hr_department_view_kanban"/>
        <field name="groups_id" eval="[(4,ref('hr_timesheet.group_timesheet_manager'))]"/>
        <field name="arch" type="xml">
            <data>
                <xpath expr="//div[hasclass('o_kanban_manage_reports')]" position="inside">
                    <a name="%(act_hr_timesheet_report)d" type="action" context="{ 'search_default_department_id': [active_id], 'default_department_id': active_id}">
                        Timesheets
                    </a>
                </xpath>
            </data>
        </field>
    </record>
</odoo>
