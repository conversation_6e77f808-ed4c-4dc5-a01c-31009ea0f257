# -*- coding:utf-8 -*-
from odoo import api, fields, models, _
from datetime import datetime, timedelta
from pytz import timezone


class HrAttendanceJustify(models.TransientModel):
    _name = "hr.attendance.justify"

    attendance_ids = fields.Many2many('hr.attendance')

    def make_justify(self):
        for elem in self.attendance_ids:
            elem.hr_justify = True
            elem.hr_justify_user = str(self.env.user.name)
