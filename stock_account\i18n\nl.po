# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_account
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid " Product cost updated from %(previous)s to %(new_cost)s."
msgstr " Product kostprijs bijgewerkt van %(previous)s naar %(new_cost)s."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "%(user)s changed cost from %(previous)s to %(new_price)s - %(product)s"
msgstr ""
"%(user)s heeft kostprijs gewijzigd van %(previous)s naar %(new_price)s - "
"%(product)s"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid ""
"%(user)s changed stock valuation from  %(previous)s to %(new_value)s - "
"%(product)s"
msgstr ""
"%(user)s heeft voorraadwaarde aangepast van  %(previous)s naar %(new_value)s"
" - %(product)s"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid ""
")\n"
"                            <small class=\"mx-2 font-italic\">Use a negative added value to record a decrease in the product value</small>"
msgstr ""
")\n"
"                            <small class=\"mx-2 font-italic\">Gebruik een negatieve toegevoegde waarde om een daling van de productwaarde vast te leggen</small>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "<b>Set other input/output accounts on specific </b>"
msgstr ""
"<b>Stel overige voorraadtoename- en voorraadafname rekeningen in op "
"specifieke </b>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Product</span>"
msgstr "<span>Product</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Quantity</span>"
msgstr "<span>Hoeveelheid</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>SN/LN</span>"
msgstr "<span>SN/LN</span>"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "Grootboekschema sjabloon"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__account_move_ids
msgid "Account Move"
msgstr "Boeking"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr "Voorraadrekeningen"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_request_count__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__date
msgid "Accounting Date"
msgstr "Boekhouddatum"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_form_inherit
msgid "Accounting Entries"
msgstr "Financiële boekingen"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr "Boekhoudinformatie"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Add Manual Valuation"
msgstr "Voeg handmatige waardering toe"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Add additional cost (transport, customs, ...) in the value of the product."
msgstr ""
"Voeg extra kosten toe (transport, douane, ...) in de waarde van het product."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Added Value"
msgstr "Toegevoegde waarde"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__added_value
msgid "Added value"
msgstr "Toegevoegde waarde"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_res_config_settings__module_stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Affect landed costs on reception operations and split them among products to"
" update their cost price."
msgstr ""
"Heeft effect op de logistieke kosten bij ontvangsten en wordt gedeeld over "
"de producten om de kostprijs bij te werken."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__analytic_account_line_id
msgid "Analytic Account Line"
msgstr "Kostenplaatsregel"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__real_time
msgid "Automated"
msgstr "Geautomatiseerd"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__average
msgid "Average Cost (AVCO)"
msgstr "Gemiddelde kostprijs (AVCO)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Cancel"
msgstr "Annuleren"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Cannot find a stock input account for the product %s. You must define one on"
" the product category, or on the location, before processing this operation."
msgstr ""
"Kan geen voorraadtoename rekening vinden voor het product %s. Deze moet "
"ingesteld worden op de productcategorie, of op de locatie, vóór het "
"verwerken van deze bewerking."

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr ""
"Kan geen voorraadtoename rekening vinden voor het product %s. Deze moet "
"ingesteld worden op de productcategorie, of op de locatie, vóór het "
"verwerken van deze bewerking."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"Changing your cost method is an important change that will impact your "
"inventory valuation. Are you sure you want to make that change?"
msgstr ""
"Het wijzigen van je kostprijsmethode is een belangrijke verandering die van "
"invloed is op je voorraadwaardering. Weet je zeker dat je de wijziging wilt "
"aanbrengen?"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__company_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr ""
"Configuratiefout. Configureer de prijsverschillen rekening op het product of"
" de categorie om deze bewerking te verwerken."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product__cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template__cost_method
msgid "Costing Method"
msgstr "Kostprijsbepaling"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Costing method change for product category %s: from %s to %s."
msgstr ""
"Wijziging van de kostenmethode voor productcategorie %s: van %s naar %s."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_id
msgid "Counterpart Account"
msgstr "Tegenrekening"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_input_categ_id
msgid ""
"Counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account\n"
"                set on the source location. This is the default value for all products in this category. It can also directly be set on each product."
msgstr ""
"Tegenrekening boekingen voor alle inkomende voorraadbewegingen worden op deze rekening geboekt, maar niet als er een specifieke waarderingsrekening is\n"
"ingesteld op de bronlocatie. Dit is de standaardwaarde voor alle producten in deze categorie. Het kan ook direct op elk product worden ingesteld."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_picking__country_code
msgid "Country Code"
msgstr "Landcode"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_quantity_svl
msgid "Current Quantity"
msgstr "Huidige hoeveelheid"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_value_svl
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Current Value"
msgstr "Huidige waarde"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Date"
msgstr "Datum"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_quant__accounting_date
msgid ""
"Date at which the accounting entries will be created in case of automated "
"inventory valuation. If empty, the inventory date will be used."
msgstr ""
"Datum waarop de financiële boekingen gemaakt worden bij geautomatiseerde "
"voorraadwaardering. Indien leeg wordt de voorraaddatum gebruikt."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Standaard maateenheid gebruikt voor alle voorraadbewegingen."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__description
msgid "Description"
msgstr "Omschrijving"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_lot_on_invoice
msgid "Display Lots & Serial Numbers on Invoices"
msgstr "Toon partijen & serienummers op facturen"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__display_name
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: stock_account
#: model:res.groups,name:stock_account.group_lot_on_invoice
msgid "Display Serial & Lot Number on Invoices"
msgstr "Toon serie & partijnummer op factuuren"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Documentation"
msgstr "Documentatie"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"Due to a change of product category (from %s to %s), the costing method"
"                                has changed for product template %s: from %s"
" to %s."
msgstr ""
"Vanwege een wijziging van de productcategorie (van %s naar %s) is de "
"kostprijsbepaling voor productsjabloon %s: van %s naar %sgewijzigd."

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__fifo
msgid "First In First Out (FIFO)"
msgstr "First In First Out (FIFO)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Group by..."
msgstr "Groeperen op..."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__id
msgid "ID"
msgstr "ID"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr "Voorraadlocaties"

#. module: stock_account
#: code:addons/stock_account/__init__.py:0
#: code:addons/stock_account/__init__.py:0
#: code:addons/stock_account/models/account_chart_template.py:0
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product__valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template__valuation
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__property_valuation
#: model:ir.ui.menu,name:stock_account.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form_stock
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
#, python-format
msgid "Inventory Valuation"
msgstr "Voorraadwaardering"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__is_anglo_saxon_line
msgid "Is Anglo Saxon Line"
msgstr "Is Angelsaksische regel"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_journal_id
msgid "Journal"
msgstr "Dagboek"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_id
msgid "Journal Entry"
msgstr "Boeking"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move_line
msgid "Journal Item"
msgstr "Boekingsregel"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__module_stock_landed_costs
msgid "Landed Costs"
msgstr "Logistieke kosten"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer____last_update
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_id
msgid "Linked To"
msgstr "Gelinkt aan"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Lots &amp; Serial numbers will appear on the invoice"
msgstr "Partij- &amp; Serienummers worden afgedrukt op de factuur"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__manual_periodic
msgid "Manual"
msgstr "Handmatig"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Manual Stock Valuation: %s."
msgstr "Handmatige voorraadwaardering: %s."

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Manual Stock Valuation: No Reason Given."
msgstr "Handmatige voorraadwaardering: Geen reden opgegeven"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,help:stock_account.field_product_product__valuation
#: model:ir.model.fields,help:stock_account.field_product_template__valuation
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""
"Handmatig: de boekingen om de voorraad te waarderen worden niet automatisch geboekt.\n"
"Realtime: een boeking wordt automatisch gemaakt om de voorraad te waarderen wanneer een product het bedrijf binnenkomt of verlaat."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value
msgid "New value"
msgstr "Nieuwe waarde"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value_by_qty
msgid "New value by quantity"
msgstr "Nieuwe waarde per hoeveelheid"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_product_product_replenishment
msgid "On Hand Value"
msgstr "Beschikbare voorraadwaarde"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
msgid "Other Info"
msgstr "Overige info"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product"
msgstr "Product"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__categ_id
msgid "Product Category"
msgstr "Productcategorie"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Productverplaatsingen (voorraadverplaatsingsregels)"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
#, python-format
msgid "Product Revaluation"
msgstr "Productherwaardering"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_tmpl_id
msgid "Product Template"
msgstr "Productsjabloon"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Product value manually modified (from %s to %s)"
msgstr "Productwaarde handmatig gewijzigd (vanaf %s naar %s)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__quantity
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__quantity
msgid "Quantity"
msgstr "Aantal"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__quantity_svl
msgid "Quantity Svl"
msgstr "Hoeveelheid Svl"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quant
msgid "Quants"
msgstr "Aantallen"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason"
msgstr "Reden"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason of the revaluation"
msgstr "Reden van herwaardering"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_id
msgid "Related product"
msgstr "Gerelateerd product"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_qty
msgid "Remaining Qty"
msgstr "Overgebleven hoeveelheid"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_value
msgid "Remaining Value"
msgstr "Resterende waarde"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking
msgid "Return Picking"
msgstr "Retour boeken"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "Retour pickingregel"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Revaluation of %s"
msgstr "Herwaardering van %s"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Revalue"
msgstr "Herwaardering"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__categ_id
msgid "Select category for the current product"
msgstr "Selecteer een categorie voor het huidige product"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__standard
msgid "Standard Price"
msgstr "Kostprijs"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_product__cost_method
#: model:ir.model.fields,help:stock_account.field_product_template__cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""
"Vaste verreken prijs: De producten worden gewaardeerd tegen de kostprijs zoals ingesteld op het product\n"
"Gemiddelde kostprijs (AVCO): De producten worden gewaardeerd tegen de gewogen gemiddelde waarde.\n"
"First In First Out (FIFO): De producten worden gewaardeerd, waarbij ervan uit wordt gegaan dat de producten die het bedrijf als eerste binnenkomen ook als eerste het bedrijf verlaten.\n"
"        "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr "Voorraadtoename rekening"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_journal
msgid "Stock Journal"
msgstr "Voorraad dagboek"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_payment__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_move_id
msgid "Stock Move"
msgstr "Voorraadverplaatsing"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr "Voorraadafname rekening"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "Voorraadhoeveelheid geschiedenis"

#. module: stock_account
#: model:ir.model,name:stock_account.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "Voorraadaanvullen rapportage"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "Voorraad Een voorraadtelling aanvragen"

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_action
msgid "Stock Valuation"
msgstr "Voorraadwaarde"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "Voorraadwaarde rekening"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr "Voorraadwaarde rekening (inkomend)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr "Voorraadwaarde rekening (uitgaand)"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_payment__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_product_product__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr "Voorraadwaardelaag"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_account_move_line__is_anglo_saxon_line
msgid "Technical field used to retrieve the anglo-saxon lines."
msgstr ""
"Technisch veld dat wordt gebruikt om de anglo-Saksische regels op te halen."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_picking__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"De ISO-landcode in twee letters.\n"
"Je kunt dit veld gebruiken voor snelzoeken."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"The Stock Input and/or Output accounts cannot be the same as the Stock "
"Valuation account."
msgstr ""
"De voorraadtoename- en/of voorraadafnamerekening kunnen niet hetzelfde zijn "
"als de voorraadwaarderingsrekening."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"The action leads to the creation of a journal entry, for which you don't "
"have the access rights."
msgstr ""
"De actie leidt tot het aanmaken van een financiële boeking, waarvoor je geen"
" toegangsrechten heeft."

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "The added value doesn't have any impact on the stock valuation"
msgstr "De toegevoegde waarde heeft geen invloed op de voorraadwaardering"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent state: some are entering and other "
"are leaving the company."
msgstr ""
"De boekingsregels bevinden zich niet in een consistente staat: sommige "
"worden ontvangen en andere verlaten het bedrijf."

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent states: they are doing an "
"intercompany in a single step while they should go through the intercompany "
"transit location."
msgstr ""
"De boekingsregels bevinden zich niet in een consistente staat: ze doen "
"intercompany in één stap terwijl ze door de intercompany transit locatie "
"moeten gaan."

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent states: they do not share the same "
"origin or destination company."
msgstr ""
"De boekingsregels bevinden zich niet in een consistente staat: ze delen niet"
" dezelfde bron of bestemmingsbedrijf."

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_action
msgid ""
"There is no valuation layers. Valuation layers are created when some product"
" moves should impact the valuation of the stock."
msgstr ""
"Er zijn geen waarderingslagen. Waarderingslagen worden aangemaakt wanneer "
"sommige productbewegingen de waardering van de voorraad zouden moeten "
"beïnvloeden."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__value
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Value"
msgstr "Totale waarde"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_picking
msgid "Transfer"
msgstr "Verplaatsing"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,help:stock_account.field_stock_return_picking_line__to_refund
msgid ""
"Trigger a decrease of the delivered/received quantity in the associated Sale"
" Order/Purchase Order"
msgstr ""
"Leidt tot een daling van de geleverde/ontvangen hoeveelheid in de "
"bijbehorende verkooporder/inkooporder"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__unit_cost
msgid "Unit Value"
msgstr "Eenheidswaarde"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__uom_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_uom_name
msgid "Unit of Measure"
msgstr "Maateenheid"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,field_description:stock_account.field_stock_return_picking_line__to_refund
msgid "Update quantities on SO/PO"
msgstr "Hoeveelheden op SO/PO bijwerken"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"Gebruik real-time voorraadwaardering. Wanneer dit is ingesteld op een "
"virtuele locatie (niet een interne locatie), dan zal deze rekening de "
"inkoopwaarde bevatten van de producten welke worden verplaatst uit een "
"interne locatie naar deze locatie, in plaats van de standaard voorraadafname"
" rekening, zoals ingesteld bij het product. Dit heeft geen effect voor "
"interne locaties."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"Gebruik real-time voorraadwaardering. Wanneer dit is ingesteld op een "
"virtuele locatie (niet een interne locatie), dan zal deze rekening de "
"inkoopwaarde bevatten van de producten welke worden verplaatst uit deze "
"locatie naar een interne locatie, in plaats van de standaard voorraadafname"
"  rekening, zoals ingesteld bij het product. Dit heeft geen effect voor "
"interne locaties."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_picking
msgid "Valuation"
msgstr "Waarde"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Valuation method change for product category %s: from %s to %s."
msgstr ""
"Wijziging van de waarderingsmethode voor productcategorie %s: van %s naar "
"%s."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__value
msgid "Value"
msgstr "Waarde"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__value_svl
msgid "Value Svl"
msgstr "Waarde Svl"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Warning"
msgstr "Waarschuwing"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_valuation_account_id
msgid ""
"When automated inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""
"Wanneer automatische voorraadwaardering is geactiveerd bij een product, dan "
"zal deze rekening de huidige waarde van de producten bevatten."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_output_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the destination location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""
"Bij het uitvoeren van automatische voorraadwaardering, zal de tegenboeking "
"voor alle uitgaande voorraadverplaatsingen worden geboekt op deze rekening, "
"maar niet als er een specifieke waarderingsrekening is ingesteld op de "
"bestemmingslocatie. Dit is de standaardwaarde voor alle producten in deze "
"categorie. Het kan ook direct worden ingesteld bij een product"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_journal
msgid ""
"When doing automated inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""
"Wanneer je automatische voorraadwaardering doet, dan is dit het dagboek "
"waarin automatisch de boekingen worden gemaakt, wanneer de "
"voorraadverplaatsingen worden verwerkt."

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer_revaluation
msgid "Wizard model to reavaluate a stock inventory for a product"
msgstr ""
"Wizard-model om een voorraadvoorraad voor een product opnieuw te evalueren"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "You cannot revalue a product with a standard cost method."
msgstr "Je kunt een product niet herwaarderen met een standaardkostenmethode."

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "You cannot revalue a product with an empty or negative stock."
msgstr "Je kunt een product met geen of negatieve voorraad niet herwaarderen."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You cannot update the cost of a product in automated valuation as it leads "
"to the creation of a journal entry, for which you don't have the access "
"rights."
msgstr ""
"Je kunt de kosten van een product niet bijwerken bij geautomatiseerde "
"waardering, omdat dit leidt tot het maken van een financiële boeking "
"waarvoor je geen toegangsrechten heeft."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You don't have any input valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"Er is geen voorraadtoename rekening gedefinieerd op je productcategorie. Je "
"moet een er vóór de verwerking van deze bewerking definiëren."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You don't have any stock input account defined on your product category. You"
" must define one before processing this operation."
msgstr ""
"Je hebt geen voorraadtoename rekening gedefinieerd voor de productcategorie."
" Je moet er een definiëren voordat je deze bewerking kunt verwerken."

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts."
msgstr ""
"Je hebt geen voorraadboek gedefinieerd op je productcategorie, controleer of"
" je een rekeningschema heeft geïnstalleerd."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"You don't have any stock valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"Er is geen voorraadwaardering rekening gedefinieerd op je productcategorie. "
"Je moet een er vóór de verwerking van deze bewerking definiëren."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "You must set a counterpart account on your product category."
msgstr "Je moet een tegenrekening instellen voor je productcategorie."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "by"
msgstr "in"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "for"
msgstr "voor"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "locations"
msgstr "locaties"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"'The value of a stock valuation layer cannot be negative. Landed cost could "
"be use to correct a specific transfer."
msgstr ""
"'De waarde van een voorraadwaarderingslaag kan niet negatief zijn. "
"Logistieke kosten kunnen worden gebruikt om een specifieke overboeking te "
"corrigeren."
