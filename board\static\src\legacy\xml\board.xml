<?xml version="1.0" encoding="utf-8"?>
<template>
<t t-name="DashBoard">
    <t t-if="!isMobile">
        <div class="oe_dashboard_links">
            <button type="button" class="button oe_dashboard_link_change_layout btn btn-secondary"
                title="Change Layout..">
                <img src="/board/static/src/legacy/img/layout_1-1-1.png" width="16" height="16" alt=""/>
                <span> Change Layout </span>
            </button>
        </div>
    </t>
    <table t-att-data-layout="node.attrs.layout" t-attf-class="oe_dashboard oe_dashboard_layout_#{node.attrs.layout}" cellspacing="0" cellpadding="0" border="0">
    <tr>
        <td t-foreach="node.children" t-as="column" t-if="column.tag == 'column'"
             t-att-id="'column_' + column_index" t-attf-class="oe_dashboard_column index_#{column_index}">

            <t t-foreach="column.children" t-as="action" t-if="action.tag == 'action'" t-call="DashBoard.action"/>
        </td>
    </tr>
    </table>
</t>
<t t-name="DashBoard.action">
    <div t-att-data-id="action.attrs.id" class="oe_action">
        <h2 t-attf-class="oe_header #{action.attrs.string ? '' : 'oe_header_empty'}">
            <span class="oe_header_txt"> <t t-esc="action.attrs.string"/> </span>
            <input class = "oe_header_text" type="text"/>
            <t t-if="!action.attrs.string">&amp;nbsp;</t>
            <span class='oe_icon oe_close'></span>
            <span class='oe_icon oe_minimize oe_fold' t-if="!action.attrs.fold"></span>
            <span class='oe_icon oe_maximize oe_fold' t-if="action.attrs.fold"></span>
        </h2>
        <div t-att-class="'oe_content' + (action.attrs.fold ? ' oe_folded' : '')"/>
    </div>
</t>
<t t-name="DashBoard.layouts">
    <div class="oe_dashboard_layout_selector">
        <p>
            <strong>Choose dashboard layout</strong>
        </p>
        <ul>
            <li t-foreach="'1 1-1 1-1-1 1-2 2-1'.split(' ')" t-as="layout" t-att-data-layout="layout">
                <img t-attf-src="/board/static/src/legacy/img/layout_#{layout}.png" alt=""/>
                <i t-if="layout == currentLayout" class="oe_dashboard_selected_layout fa fa-check fa-lg text-success" aria-label='Layout' role="img" title="Layout"/>
            </li>
        </ul>
    </div>
</t>
<t t-name="DashBoard.NoContent">
    <div class="o_view_nocontent">
        <div class="o_nocontent_help">
            <p class="o_view_nocontent_neutral_face">
                Your personal dashboard is empty
            </p><p>
                To add your first report into this dashboard, go to any
                menu, switch to list or graph view, and click <i>"Add to
                Dashboard"</i> in the extended search options.
            </p><p>
                You can filter and group data before inserting into the
                dashboard using the search options.
            </p>
        </div>
    </div>
</t>
<t t-name="DashBoard.xml">
    <form t-att-string="form_title">
        <board t-att-style="style">
            <column t-foreach="columns" t-as="column">
                <action t-foreach="column" t-as="action" t-att="action"/>
            </column>
        </board>
    </form>
</t>
<div t-name="HomeWidget" class="oe_dashboard_home_widget"/>
<t t-name="HomeWidget.content">
    <h3><t t-esc="widget.title"/></h3>
    <iframe width="100%" frameborder="0" t-att-src="url"/>
</t>

<t t-name="SearchView.addtodashboard">
    <a href="#" class="dropdown-item o_add_to_dashboard_link o_closed_menu">Add to my Dashboard</a>
    <div class="dropdown-item-text o_add_to_dashboard">
        <input class="o_input o_add_to_dashboard_input" type="text"/>
    </div>
    <div class="dropdown-item-text o_add_to_dashboard">
        <button type="button" class="btn btn-primary o_add_to_dashboard_button">Add</button>
    </div>
</t>
</template>
