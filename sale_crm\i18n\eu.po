# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_crm
#
# Translators:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-10-14 13:25+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Basque (http://www.transifex.com/odoo/odoo-9/language/eu/)\n"
"Language: eu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  eu.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  eu.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_issue_count
msgid "# Issues"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_task_count
msgid "# Tasks"
msgstr "Ataza kop."

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_sale_order_count
msgid "# of Sales Order"
msgstr "# of Sales Order"

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_case_form_view_oppor
msgid "<span class=\"o_stat_text\"> Orders</span>"
msgstr ""

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_case_form_view_oppor
msgid "<span class=\"o_stat_text\"> Quote(s) </span>"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_associate_member
msgid ""
"A member with whom you want to associate your membership.It will consider "
"the membership state of the associated member."
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_activation
#, fuzzy
msgid "Activation"
msgstr "Aurrekontua"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_associate_member
msgid "Associate Member"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_membership_cancel
msgid "Cancel Membership Date"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_payment_method_count
msgid "Count Payment Method"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_membership_state
msgid "Current Membership Status"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_property_stock_customer
msgid "Customer Location"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_membership_start
msgid "Date from which membership becomes active."
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_membership_cancel
msgid "Date on which membership has been cancelled"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_membership_stop
msgid "Date until which membership remains active."
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_free_member
msgid "Free Member"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_partner_weight
msgid ""
"Gives the probability to assign a lead to this partner. (0 means no "
"assignation.)"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_implemented_partner_ids
msgid "Implementation References"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_assigned_partner_id
msgid "Implemented by"
msgstr ""

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_account_invoice
msgid "Invoice"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_target_sales_invoiced
msgid "Invoiced in Sale Orders Target"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_membership_state
msgid ""
"It indicates the membership state.\n"
"-Non Member: A partner who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose "
"invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paying member: A member who has paid the membership fee."
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_date_review
msgid "Latest Partner Review"
msgstr ""

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_grade_id
msgid "Level"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_partner_weight
msgid "Level Weight"
msgstr ""

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.sale_view_inherit123
msgid "Log in the chatter from which opportunity the order originates"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_member_lines
msgid "Membership"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_membership_amount
msgid "Membership Amount"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_membership_stop
msgid "Membership End Date"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_membership_start
msgid "Membership Start Date"
msgstr ""

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_case_form_view_oppor
msgid "New Quotation"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_date_review_next
msgid "Next Partner Review"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_lead_sale_number
msgid "Number of Quotations"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_sale_order_opportunity_id
msgid "Opportunity"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_lead_order_ids
msgid "Orders"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_date_partnership
msgid "Partnership Date"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_payment_method_ids
msgid "Payment Methods"
msgstr ""

#. module: sale_crm
#: model:ir.actions.act_window,name:sale_crm.sale_action_quotations_new
msgid "Quotation"
msgstr "Aurrekontua"

#. module: sale_crm
#: model:ir.actions.act_window,name:sale_crm.sale_action_quotations
msgid "Quotations"
msgstr "Quotations"

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_sale_order
#: model:ir.model.fields,field_description:sale_crm.field_res_users_sale_order_ids
msgid "Sales Order"
msgstr "Salmenta eskaria"

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_free_member
msgid "Select if you want to give free membership."
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_lead_sale_amount_total
msgid "Sum of Orders"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_sale_order_tag_ids
msgid "Tags"
msgstr "Etiketak"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_task_ids
msgid "Tasks"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_membership_amount
msgid "The price negotiated by the partner"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_property_stock_customer
msgid ""
"This stock location will be used, instead of the default one, as the "
"destination location for goods you send to this partner"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_res_users_property_stock_supplier
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for goods you receive from the current partner"
msgstr ""

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_res_users
msgid "Users"
msgstr "Erabiltzaileak"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users_property_stock_supplier
msgid "Vendor Location"
msgstr ""

#~ msgid "# Claims"
#~ msgstr "Erreklamazio zkia"
