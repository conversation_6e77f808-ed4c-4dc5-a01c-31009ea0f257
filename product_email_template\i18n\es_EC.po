# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_email_template
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-02-15 07:35+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_email_template
#: model:mail.template,body_html:product_email_template.product_online_training_email_template
#, fuzzy
msgid ""
" \n"
"    <div style=\"height:auto;text-align: center;font-size : 40px;color: "
"#333333;margin-top:30px;font-weight: 300;\">\n"
"            Online Training\n"
"    </div>\n"
"     <div style=\"height:auto;text-left: center;font-size : 16px;color: "
"#646464;margin-top:30px;margin-left:15px;margin-right:5px;\">\n"
"        These courses feature the same high quality course content found in "
"our traditional classroom trainings, supplemented with\n"
"        modular sessions and cloud-based labs. Many of our online learning "
"courses also include dozens of recorded webinars and live\n"
"        sessions by our senior instructors.\n"
"    </div>\n"
"    <div style=\"height: auto;margin-top:30px;margin-bottom:10px;margin-"
"left:20px;color: #646464;font-size:16px;\">\n"
"        <strong>Your advantages</strong>\n"
"        <ul>\n"
"            <li>Modular approach applied to the learning method</li>\n"
"            <li>New interactive learning experience</li>\n"
"            <li>Lower training budget for the same quality courses</li>\n"
"            <li>Better comfort to facilitate your learning process</li>\n"
"        </ul>\n"
"    </div>\n"
"    <hr color=\"DCDCFB\"/>\n"
"    <div style=\"height:auto;text-align: center;font-size : 40px;color: "
"#333333;margin-top:30px;font-weight: 300;\">\n"
"        Structure of the Training\n"
"    </div>\n"
"    <div>\n"
"        <table>\n"
"            <tr>\n"
"                <td>\n"
"                    <img src=\"/product_email_template/static/img/"
"online_training.png\"/>\n"
"                </td>\n"
"                <td style=\"height: auto;margin-top:10px;margin-bottom:10px;"
"font-size:18px;color: #646464;\">\n"
"                    <strong>There are three components to the training</"
"strong>\n"
"                    <ul>\n"
"                        <li>Videos with detailed demos</li>\n"
"                        <li>Hands-on exercises and their solutions</li>\n"
"                        <li>Live Q&A sessions with a trainer</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: left;font-size : 14px;color: "
"#646464;margin-top:10px;margin-left:15px;\">\n"
"        The 5-day training is modular, which means that you can choose to "
"participate in the full training or to just 1 or 2 modules.\n"
"        Nevertheless, <b>the first day of the training is compulsory</b> for "
"everyone. The Monday is compulsory because the\n"
"        introduction of Odoo is important before going through the other "
"modules.<br/><br/>\n"
"        Each day of the week starts from 9 AM (CEST/PST) to 12 PM (CEST/"
"PST). A Q&A session will be hosted by an Odoo trainer.\n"
"        Each day, the participants are expected to have done the following "
"<b>BEFORE</b> attending the Q&A session:\n"
"        <ul>\n"
"            <li>Watch the videos for this day</li>\n"
"            <li>Do the related exercises (written solutions will be provided "
"upfront as well)</li>\n"
"            <li>Send their questions by email before 5PM (CEST/PST) on the "
"previous business day</li>\n"
"        </ul><br/>\n"
"        The Q&A session will be hosted on a web conference service.<b> A "
"stable internet connection is required.</b> The trainer will\n"
"        screen the questions for the day and select the most relevant ones "
"to be answered during the session. The purpose of the\n"
"        <b>Q&A session is not to cover questions related to a specific "
"implementation project </b>or company-specific business\n"
"        workflows. The <b>questions should be about the material covered in "
"the videos </b>and the exercises and should benefit\n"
"        the other participants of the training as well.<br/>\n"
"        Each day of the week is dedicated to specific applications in Odoo "
"as per the following planning:\n"
"        <ul style=\"color: #6d57e0\">\n"
"            <li>\n"
"                <strong>Monday: </strong>\n"
"                Introduction, CRM, Sales Management\n"
"            </li>\n"
"            <li>\n"
"                <strong>Tuesday: </strong>\n"
"                Access rights, Purchase, Sales &amp; Purchase management, "
"Financial accounting\n"
"            </li>\n"
"            <li>\n"
"                <strong>Wednesday: </strong>\n"
"                Project management, Human resources, Contract management\n"
"            </li>\n"
"            <li>\n"
"                <strong>Thursday: </strong>\n"
"                Warehouse management, Manufacturing (MRP) &amp; Sales, "
"Import/Export\n"
"            </li>\n"
"            <li>\n"
"                <strong>Friday: </strong>\n"
"                Pricelists, Point of Sale (POS), Introduction to report "
"customization\n"
"            </li>\n"
"        </ul>\n"
"    </div>\n"
"    <hr color=\"DCDCFB\"/>\n"
"    <div style=\"height:auto;text-align: center;font-size : 40px;color: "
"#333333;margin-top:40px;font-weight: 300;\">\n"
"        Content of the Training\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: center;font-size : 24px;color: "
"#646464;margin-top:30px;\">\n"
"        <hr style=\"display: inline-block; width: 15%; margin-bottom: 4px;"
"margin-right: 25px;\" />Monday<hr style=\"display: inline-block; width: 15%; "
"margin-bottom: 4px;margin-left: 25px;\" /><br/><br/>\n"
"        Introduction, CRM & Sales\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Introduction: Get familiar with the V7.</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Odoo’s architecture</li>\n"
"                        <li>How to create an Odoo Online instance</li>\n"
"                        <li>List and form views, search and filter features</"
"li>\n"
"                        <li>Tooltips and drill-downs</li>\n"
"                        <li>Advanced navigation</li>\n"
"                        <li>Calendar, Graph, Kanban and Gantt views</li>\n"
"                        <li>Social network features: emailing, internal "
"communication</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                        The introduction exercises have been created to help "
"you discover Odoo in an easy way. Their main objective is to present the "
"different applications, menus, view types, shortcuts, field types, wizards, "
"actions, etc.  It will help you to discover how to navigate in Odoo!<br/></"
"br>\n"
"                        Therefore, the Introduction is compulsory for "
"everyone, even if you need to learn a specific module given on an other "
"date. This obligation has been made to make sure that you are not lost if "
"you arrive in the middle of the training for another module.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">CRM Module</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Manage Lead & Opportunities</li>\n"
"                        <li>Call, Meeting</li>\n"
"                        <li>Partners & contacts</li>\n"
"                        <li>Automated actions</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Track all leads coming in and push them to real "
"opportunities</li>\n"
"                        <li>Improve the productivity of the sales force</"
"li>\n"
"                        <li>Access all documents and messages related to one "
"lead/opportunity in one place</li>\n"
"                        <li>Track the sales pipeline by month and stage to "
"produce\n"
"                        accurate sales forecasts at the individual and "
"group\n"
"                        levels</li>\n"
"                        <li>Track individual performance of sales people</"
"li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Sales Module</div>\n"
"        <table>\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Customers</li>\n"
"                        <li>Products to sell</li>\n"
"                        <li>Manual discounts</li>\n"
"                        <li>Margins</li>\n"
"                        <li>Sales & Warehouse: Invoicing control (On "
"demand / On\n"
"                        Delivery Order), Shipping policy (At once / Each "
"product\n"
"                        when available)</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Register orders from customers and create "
"invoices</li>\n"
"                        <li>Schedule deliveries (for products) or tasks (for "
"service)</li>\n"
"                        <li>Ensure that sales orders are properly invoiced "
"by tracking pending sales orders</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <strong>CRM & Sales management:</strong>\n"
"                    <ul>\n"
"                        <li>From an opportunity to a sale order</li>\n"
"                        <li>Claims</li>\n"
"                        <li>Aftersale communication</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    Odoo can work with independent applications, so the\n"
"                    whole commercial process can be integrated together in\n"
"                    order to accomplish complete business flows.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: center;font-size : 24px;color: "
"#646464;margin-top:30px;\">\n"
"        <hr style=\"display: inline-block; width: 15%; margin-bottom: 4px;"
"margin-right: 25px;\" />Tuesday<hr style=\"display: inline-block; width: "
"15%; margin-bottom: 4px;margin-left: 25px;\" /><br/><br/>\n"
"        Access rights, Sales & Purchase, Financial accounting\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Access rights</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Users</li>\n"
"                        <li>Groups</li>\n"
"                        <li>Access rights and access rules</li>\n"
"                        <li>Modules configuration</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                        Thanks to the integrated access rights management "
"integrated, each user has an access to its relevant information. No "
"overloaded screen with a lot of irrelevant data, no unwanted menu, etc. Only "
"a clean interface!\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Purchase</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Vendors</li>\n"
"                        <li>Products</li>\n"
"                        <li>Request for Quotation</li>\n"
"                        <li>Purchase order</li>\n"
"                        <li>Invoicing & Invoice control</li>\n"
"                        <li>Incoming order (Complete/partial receipt)</li>\n"
"                        <li>Purchase requisition</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Better negotiate volumes and prices based on "
"historical data</li>\n"
"                        <li>Centralize all the data to be able to build "
"reporting and statistics</li>\n"
"                        <li>Control the invoicing process</li>\n"
"                        <li>Work with purchase requisitions to ask different "
"vendors to submit quotations</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <strong>Sales & Purchase management</strong>\n"
"                    <ul>\n"
"                        <li>Make to order product</li>\n"
"                        <li>Retailer process: buy a product and sell it "
"without any customization/manufacturing</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    Keep control of the replenishment process based on sales "
"demands! Thanks to this integration, you will be able to automate the "
"ordering for the missing units ordered by a customer\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Financial accounting</div>\n"
"        <table>\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Invoices</li>\n"
"                        <li>Refunds</li>\n"
"                        <li>Journal Entries & Reconciliations</li>\n"
"                        <li>Reports / Closing of fiscal year</li>\n"
"                        <li>Accounts</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Rapidly encode your financial operations or "
"accounting transactions</li>\n"
"                        <li>Carry out payments easily and adequately "
"reconcile these payments with invoices</li>\n"
"                        <li>Quick creation of invoices with pre-set defaults "
"on debtor/creditor and income/expense accounts</li>\n"
"                        <li>Multiple manners to reconcile</li>\n"
"                        <li>Configuration of accounts to ensure correct "
"display in balance sheet and profit & loss statement</li>\n"
"                        <li>Apply correct deferral methods to ensure the "
"close of the fiscal year</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: center;font-size : 24px;color: "
"#646464;margin-top:30px;\">\n"
"        <hr style=\"display: inline-block; width: 15%; margin-bottom: 4px;"
"margin-right: 25px;\" />Wednesday<hr style=\"display: inline-block; width: "
"15%; margin-bottom: 4px;margin-left: 25px;\" /><br/><br/>\n"
"        Project management, Human resources, Contract management\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Project Management</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Projects</li>\n"
"                        <li>Tasks</li>\n"
"                        <li>Issues</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Manage and follow your projects, tasks and "
"issues</li>\n"
"                        <li>Discover the Kanban view to quickly operate some "
"actions and to quickly detect any bottlenecks in your projects flows</li>\n"
"                        <li>Discover the Gantt view to see how your project "
"has been planned and adapt it according to new elements or to the people "
"agenda</li>\n"
"                        <li>Define project members & delegate tasks</li>\n"
"                        <li>Create automatically timesheet lines in the "
"Human Resources menu from recorded work activities</li>\n"
"                        <li>Track and invoice (to customers) the costs "
"associated to a project</li>\n"
"                        <li>Manage a support ticket system</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Human Resources</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Employees</li>\n"
"                        <li>Recruitment</li>\n"
"                        <li>Expenses</li>\n"
"                        <li>Allocations and leaves requests</li>\n"
"                        <li>Time management: timesheet and timesheet lines</"
"li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                     <strong>About Recruiting:</strong>\n"
"                    <ul>\n"
"                        <li>Better negotiate volumes and prices based on "
"historical data</li>\n"
"                        <li>Centralize all the data to be able to build "
"reporting and statistics</li>\n"
"                        <li>Control the invoicing process</li>\n"
"                        <li>Work with purchase requisitions to ask different "
"vendors to submit quotations</li>\n"
"                    </ul>\n"
"                    <strong>About Holidays:</strong>\n"
"                    <ul>\n"
"                        <li>Track the number of vacation days accrued by "
"each employee</li>\n"
"                        <li>Allow managers to approve leave requests and "
"plan backups for their teams</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Contract management</div>\n"
"        <table>\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Contracts</li>\n"
"                        <li>Invoicing methods\n"
"                            <li>Sale orders</li>\n"
"                            <li>Timesheets</li>\n"
"                            <li>Expenses</li>\n"
"                        </li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Centralize the data related to a specific "
"contract in one single point</li>\n"
"                        <li>Perform the invoicing from one single place "
"(Sales order / Timesheet / Expenses)</li>\n"
"                        <li>Follow the renewal process</li>\n"
"                        <li>Compute statistics based on expected revenues</"
"li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: center;font-size : 24px;color: "
"#646464;margin-top:30px;\">\n"
"        <hr style=\"display: inline-block; width: 15%; margin-bottom: 4px;"
"margin-right: 25px;\" />Thursday<hr style=\"display: inline-block; width: "
"15%; margin-bottom: 4px;margin-left: 25px;\" /><br/><br/>\n"
"        Warehouse management, Manufacturing (MRP) & Sales, Import/Export\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Warehouse management</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Stock moves</li>\n"
"                        <li>Inventory</li>\n"
"                        <li>Partial delivery / receipt</li>\n"
"                        <li>Units of measure</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    Odoo Warehouse Management is at once very simple, "
"flexible and complete. It is based on the concept of double entry that "
"revolutionized accounting: ‘Nothing lost, everything moved’. In Odoo, we "
"don’t talk about disappearance, consumption or loss of products: instead we "
"speak only of stock moves from one place to another.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Manufacturing (MRP)</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Bill of materials</li>\n"
"                        <li>Multi-level BoM</li>\n"
"                        <li>Routings</li>\n"
"                        <li>Work center operations</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Manage the production planning</li>\n"
"                        <li>Organize the different manufacturing orders to "
"optimize the workload of the different resources</li>\n"
"                        <li>From an operational perspective, track which "
"products and quantities need to be manufactured</li>\n"
"                    </ul>\n"
"                    <strong>As a manager:</strong>\n"
"                    <ul>\n"
"                        <li>Define products to be assembled or to be sold as "
"a kit</li>\n"
"                        <li>Organize the pipeline of the production</li>\n"
"                        <li>Track the cost of a manufacturing order and "
"measure the efficiency of the department</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <strong>MRP & Sales</strong>\n"
"                    <ul>\n"
"                        <li>Product configuration Make to Stock / to Order</"
"li>\n"
"                        <li>Commercial BoM (kit)</li>\n"
"                        <li>Just in Time</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    From the sales perspective, you will have a "
"manufacturing order generated based on a request coming from a customer "
"without any manual operation. This integration of those two aplications "
"allows you to perform the following flow in an automated way: Sale order –> "
"Stock level verification –> Production of the missing units –> Receival in "
"stock of the finished goods –> Delivery to the customer.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Import/Export</div>\n"
"        <table>\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Import from CSV files</li>\n"
"                        <li>Export as CSV or as Excel file</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Transfer data from one system to Odoo.</li>\n"
"                    </ul>\n"
"                    Odoo offers a simple way to import and export data. Odoo "
"will guide you for this phase thanks to a FAQ directly integrated into the "
"import screen\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: center;font-size : 24px;color: "
"#646464;margin-top:30px;\">\n"
"        <hr style=\"display: inline-block; width: 15%; margin-bottom: 4px;"
"margin-right: 25px;\" />Friday<hr style=\"display: inline-block; width: 15%; "
"margin-bottom: 4px;margin-left: 25px;\" /><br/><br/>\n"
"        Pricelists, Point of Sale (POS), Introduction to report "
"customization\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Pricelists</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Prices for returning customers</li>\n"
"                        <li>Prices in foreign currencies (with exchange "
"fees)</li>\n"
"                        <li>Retailer prices (based on a minimal margin)</"
"li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Keep control on the pricing policy</li>\n"
"                        <li>Allow discounts for some periods</li>\n"
"                        <li>Ensure your margins even when you define "
"discounts</li>\n"
"                        <li>Allow different pricing by customer and/or "
"vendor</li>\n"
"                        <li>Update the prices in an easy way</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Point of Sale (POS)</div>\n"
"        <table cellspacing=\"5\">\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Payment methods (journals)</li>\n"
"                        <li>POS Configuration</li>\n"
"                        <li>POS front-end</li>\n"
"                        <li>POS back-end</li>\n"
"                        <li>Shops</li>\n"
"                        <li>Sessions</li>\n"
"                        <li>POS Orders</li>\n"
"                        <li>Re-Invoicing</li>\n"
"                        <li>Scanning / Self-scanning</li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    Odoo POS is the first POS running in a 100% web based "
"environment, which means any computer with a browser can host the POS. The "
"POS is a two-part system with a front-end (interaction with the client) and "
"a back-end (managers can configure the system, print reports, "
"analyse, ...)<br/><br/>\n"
"                     <strong>About the front-end:</strong>\n"
"                    <ul>\n"
"                        <li>Offline mode. Imagine several cases were having "
"an offline mode is of prime interest: Connexion to server shutdown at a big "
"supermarket / Use in environments that requires mobility but without wifi / "
"Use in large environments like restaurants and gardens / Use in low tech "
"environments</li>\n"
"                    </ul>\n"
"                    <strong>About the back-end:</strong>\n"
"                    <ul>\n"
"                        <li>Configure the products being sold in the POS</"
"li>\n"
"                        <li>Configure the payments methods</li>\n"
"                        <li>Print daily sales report</li>\n"
"                        <li>Analyze sales</li>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <div style=\"text-align: left;font-size : 24px;color: #646464;margin-"
"top:30px;\">Introduction to report customization</div>\n"
"        <table>\n"
"            <tr style=\"text-align: center;font-size : 20px;color: #FFFFFF;"
"background-color:#969696;\">\n"
"                <th>Content</th>\n"
"                <th>What will you learn?</th>\n"
"            </tr>\n"
"            <tr style=\"text-align: left;font-size : 16px;color: #646464;"
"\">\n"
"                <td style=\"width:50%;\">\n"
"                    <ul>\n"
"                        <li>Customize the layout of your invoices</li>\n"
"                        <li>Change the footer/header of documents</li>\n"
"                        <li>Add a field in a standard document / report</"
"li>\n"
"                    </ul>\n"
"                </td>\n"
"                <td style=\"width:50%;\">\n"
"                    This features will help you to fully customize the "
"standard documents made from Odoo.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"text-align: center;background-color:#FFFFFF;margin-top:30px;"
"\" >\n"
"        <table cellspacing=\"5\" align=\"center\" cellpadding=\"5\">\n"
"            <tr style=\"font-size : 20px;color: #333333;margin-top:30px;\">\n"
"                <td>English</td>\n"
"                <td>English</td>\n"
"                <td>Spanish</td>\n"
"            </tr>\n"
"            <tr style=\"font-size : 10px;color: #646464;\">\n"
"                <td>Europe</td>\n"
"                <td>USA & Canada</td>\n"
"                <td>USA & Latin America</td>\n"
"            </tr>\n"
"            <tr style=\"font-size : 10px;color: #FFFFFF;background-color:"
"#8b5bdd;\">\n"
"                <td><a href=\"http://onlinetrainingencet.eventbrite.com\" "
"style=\"text-decoration:none;color:#FFFFFF;\" target=_new>Register</a></td>\n"
"                <td><a href=\"http://onlinetrainingenpst.eventbrite.com\" "
"style=\"text-decoration:none;color:#FFFFFF;\" target=_new>Register</a></td>\n"
"                <td><a href=\"http://onlinetrainingespst.eventbrite.com\" "
"style=\"text-decoration:none;color:#FFFFFF;\" target=_new>Register</a></td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <hr color=\"DCDCFB\"/>\n"
"    <div style=\"height:auto;text-align: center;font-size : 30px;color: "
"#333333;margin-top:10px;\">\n"
"            Training Material\n"
"    </div>\n"
"    <div style=\"height:auto;text-align: left;font-size : 16px;color: "
"#646464;margin-top:30px;\">\n"
"        As soon as registration will be finalized (ie. payment "
"confirmation), the participants will be provided with the material listed\n"
"        below:\n"
"        <ul>\n"
"            <li>Access to online videos with detailed demos</li>\n"
"            <li>Training material with exercises</li>\n"
"            <li>Written solutions to the exercises</li>\n"
"        </ul>\n"
"        Therefore, there is an advantage to registering early to the "
"training if you are interested in preparing your live sessions ahead of "
"time. Moreover, the number of participants is limited per online session "
"which is another reason to consider registering early and securing your "
"seat.\n"
"    </div>\n"
"    <div style=\"text-align: center;background-color:#FFFFFF;margin-top:30px;"
"font-size:20px;color:#4e66e7\">\n"
"        <table cellspacing=\"5\">\n"
"            <tr>\n"
"                <td>\n"
"                    With the live sessions, we learned a lot more than what "
"was covered in the videos. Definitely a good start for building up a solid "
"base of working knowledge of Odoo.\n"
"                </td>\n"
"                <td>\n"
"                    The trainer did a wonderful job with the training! He "
"was well informed and interactive online and offline. Thank you so much for "
"the experience! \n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    It would be very difficult to cover all of the "
"possibilities of Odoo and I believe the major aspects were covered very "
"well \n"
"                </td>\n"
"                <td>\n"
"                    The trainer has a very good knowledge of the subject, he "
"understands the issues of the trainees very fast and provided answers with "
"the right level of illustration.\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</div>\n"
"            \n"
"            "
msgstr ""
"<div style=\"height:auto;text-align:center;font-size:40px;color:#333333;"
"margin-top:30px;font-weight:300;\">\n"
"Entrenamiento en línea + Certificación\n"
"</div>\n"
"<div style=\"height:auto;text-left:center;font-size:16px;color:#646464;"
"margin-top:30px;margin-left:15px;margin-right:5px;\">\n"
"Estos cursos cuentan con el mismo contenido de los cursos de alta calidad "
"que se encuentra en nuestros entrenamientos en el aula tradicional, "
"complementado con\n"
"sesiones modulares y laboratorios basados ​​en la nube. Muchos de nuestros "
"cursos de aprendizaje en línea también incluye docenas de seminarios web "
"grabados y sesiones en vivo por nuestros instructores de alto nivel. Al "
"final del entrenamiento, usted puede pasar el examen de certificación Odoo "
"en uno de los 5000\n"
"Centros de prueba de Pearson VUE en todo el mundo.\n"
"</div>\n"
"<div style=\"height:auto; margin-top:30px; margin-bottom:10px; margin-"
"left:20px; color:#646464; font-size:16px;\">\n"
"<strong> Sus ventajas </strong>\n"
"<ul>\n"
"<li>enfoque modular aplicado al método de aprendizaje </li>\n"
"<li>Nueva experiencia de aprendizaje interactivo </li>\n"
"<li>Bajo presupuesto de capacitación para los cursos misma calidad </li>\n"
"<li>Mayor comodidad para facilitar su proceso de aprendizaje </li>\n"
"<li><a href=\"https://www.openerp.com/certification\">Odoo:</a> Examen de "
"certificación incluido (pre-ordenado, disponible a partir del 01 de "
"noviembre 2013) </li>\n"
"</ul>\n"
"</div>\n"
"<color hr=\"DCDCFB\"/>\n"
"<div style=\"height:auto; text-align:center; font-size:40px; color:#333333; "
"margin-top:30px; font-weight:300;\">\n"
"Estructura de la Formación\n"
"</div>\n"
"<div>\n"
"<table>\n"
"<tr>\n"
"<td>\n"
"<img src=\"/product_email_template/static/img/online_training.png\"/>\n"
"</td>\n"
"<td style=\"height:auto; margin-top:10px; margin-bottom:10px; font-"
"size:18px; color:#646464;\">\n"
"<strong> Hay tres componentes a la formación </strong>\n"
"<ul>\n"
"<li>Videos con demostraciones detalladas </li>\n"
"<li>Ejercicios prácticos y sus soluciones </li>\n"
"<li>Sesiones en vivo de preguntas y respuestas con un capacitador </li>\n"
"</ul>\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"</div>\n"
"<div style=\"height:auto; text-align:left; font-size:14px; color:#646464; "
"margin-top:10px; margin-left:15px;\">\n"
"El entrenamiento de 5 días es modular, lo que significa que usted puede "
"optar por participar en la formación completa o sólo 1 o 2 módulos.\n"
"Sin embargo, <b> el primer día de la capacitación es obligatoria </b> para "
"todos. El lunes es obligatoria ya que la\n"
"introducción de Odoo es importante antes de ir a través de los otros módulos."
"<br/>\n"
"Cada día de la semana se inicia desde las 9 horas (CET/PST) a las 12:00 "
"(CEST/PST). Una sesión Preguntas y Respuestas será organizada por un "
"capacitador Odoo.\n"
"Cada día, se espera que los participantes hayan hecho lo siguiente <b> ANTES "
"</b> de asistir a la sesión Preguntas y Respuestas:\n"
"<ul>\n"
"<li>Ver los vídeos de este día</li>\n"
"<li>Hacer los ejercicios relacionados (soluciones escritas serán "
"proporcionados por adelantado también)</li>\n"
"<li>Enviar sus preguntas por correo electrónico antes de 17:00 (CET/PST) en "
"el día hábil anterior</li>\n"
"</ul><br/>\n"
"La sesión Preguntas y Respuestas serán alojados en un servicio de "
"conferencia web. <b> Se requiere una conexión a Internet estable. </b> El "
"Capacitador\n"
"revisa las preguntas para el día y selecciona las más relevantes para ser "
"respondidas durante la sesión. El propósito de la\n"
"<b> sesión Preguntas y Respuestas no es para cubrir las preguntas "
"relacionadas con un proyecto específico aplicación </b> o específica de la "
"empresa de negocios ni\n"
"los flujos de trabajo. Las <b> preguntas deben ser sobre el material "
"cubierto en los vídeos </b> y los ejercicios y debe beneficiar\n"
"a los demás participantes de la capacitación también. <br/>\n"
"Cada día de la semana se dedica a aplicaciones específicas en Odoo de "
"acuerdo a la siguiente planificación:\n"
"<ul style=\"color: #6d57e0\">\n"
"<li>\n"
"<strong><a href=\"https://www.openerp.com/online-training#Monday\" style="
"\"text-decoration:none;\" target=_new>Lunes:</a></strong>\n"
"<a href=\"https://www.openerp.com/online-training#Monday_1\" style=\"text-"
"decoration:none;\" target=_new> Introducción, </a>\n"
"<a href=\"https://www.openerp.com/online-training#Monday_2\" style=\"text-"
"decoration:none;\" target=_new></a>CRM,\n"
"<a href=\"https://www.openerp.com/online-training#Monday_3\" style=\"text-"
"decoration:none;\" target=_new></a>Gestión de Ventas\n"
"</li>\n"
"<li>\n"
"<strong> <a href=\"https://www.openerp.com/online-training#Tuesday\" style="
"\"text-decoration:none;\" target=_new> Martes:</a> </strong>\n"
"<a href=\"https://www.openerp.com/online-training#Tuesday_1\" style=\"text-"
"decoration:none;\" target=_new>Los derechos de acceso,</a> \n"
"<a href=\"https://www.openerp.com/online-training#Tuesday_2\" style=\"text-"
"decoration:none;\" target=_new>Compra </a>,\n"
"<a href=\"https://www.openerp.com/online-training#Tuesday_2\" style=\"text-"
"decoration:none;\" target=_new>Ventas y Gestión de compras,</a> \n"
"<a href=\"https://www.openerp.com/online-training#Tuesday_4\" style=\"text-"
"decoration:none;\" target=_new>Contabilidad financiera</a> \n"
"</li>\n"
"<li>\n"
"<strong> <a href=\"https://www.openerp.com/online-training#Wednesday\" style="
"\"text-decoration:none;\" target=_new> Miércoles:</a> </strong>\n"
"<a href=\"https://www.openerp.com/online-training#Wednesday_1\" style=\"text-"
"decoration:none;\" target=_new> Gestión de proyectos,</a> \n"
"<a href=\"https://www.openerp.com/online-training#Wednesday_2\" style=\"text-"
"decoration:none;\" target=_new> Recursos Humanos,</a> \n"
"<a href=\"https://www.openerp.com/online-training#Wednesday_3\" style=\"text-"
"decoration:none;\" target=_new> Gestión de Contratos</a>\n"
"</li>\n"
"<li>\n"
"<strong> <a href=\"https://www.openerp.com/online-training#Thursday\" style="
"\"text-decoration:none;\" target=_new> Jueves:</a></strong>\n"
"<a href=\"https://www.openerp.com/online-training#Thursday_1\" style=\"text-"
"decoration:none;\" target=_new> Gestión de almacén,</a> \n"
"<a href=\"https://www.openerp.com/online-training#Thursday_2\" style=\"text-"
"decoration:none;\" target=_new> Fabricación (MRP) y Ventas,</a>\n"
"<a href=\"https://www.openerp.com/online-training#Thursday_4\" style=\"text-"
"decoration:none;\" target=_new> Importación /Exportacíon de datos</a>\n"
"</li>\n"
"<li>\n"
"<strong> <a href=\"https://www.openerp.com/online-training#Friday\" style="
"\"text-decoration:none;\" target=_new> Viernes:</a> </strong>\n"
"<a href=\"https://www.openerp.com/online-training#Friday_1\" style=\"text-"
"decoration:none;\" target=_new> Listas de precios, </a>\n"
"<a href=\"https://www.openerp.com/online-training#Friday_2\" style=\"text-"
"decoration:none;\" target=_new> Punto de Venta (POS), </a>\n"
"<a href=\"https://www.openerp.com/online-training#Friday_3\" style=\"text-"
"decoration:none;\" target=_new> Introducción a la personalización de "
"reportes </a>\n"
"</li>\n"
"</ul>\n"
"</div>\n"
"<div style=\"text-align:center; margin-top:30px;\">\n"
"<table cellspacing=\"5\" align=\"center\" cellpadding=\"5\">\n"
"<tr style=\"font-size:20px; color:#333333; margin-top:30px;\">\n"
"<td>Inglés</td></td>\n"
"<td>Inglés</td></td>\n"
"<td>Español</td>\n"
"</tr>\n"
"<tr style=\"font-size:10px; color:#646464;\">\n"
"<td>Europa</td>\n"
"<td>EE.UU. y Canadá</td>\n"
"<td>EE.UU. y América Latina</td>\n"
"</tr>\n"
"<tr style=\"font-size:10px; de color:#FFFFFF; background-color:#8b5bdd;\">\n"
"<td><a href=\"http://onlinetrainingencet.eventbrite.com\" style=\"text-"
"decoration:none; el color:#FFFFFF;\" target=_new>Registrarse</a></td>\n"
"<td><a href=\"http://onlinetrainingenpst.eventbrite.com\" style=\"text-"
"decoration:none; el color:#FFFFFF;\" target=_new>Registrarse</a></td>\n"
"<td><a href=\"http://onlinetrainingespst.eventbrite.com\" style=\"text-"
"decoration:none; el color:#FFFFFF;\" target=_new>Registrarse</a></td>\n"
"</tr>\n"
"</table>\n"
"</div>\n"
"<color hr=\"DCDCFB\"/>\n"
"<div style=\"height:auto; text-align:center; font-size:40px; color:#333333; "
"margin-top:40px; font-weight:300;\">\n"
"Contenido de la Formación\n"
"</div>\n"
"<div style=\"height:auto; text-align:center; font-size:24px; color:#646464; "
"margin-top:30px;\">\n"
"<style hr=\"display:inline-block; anchura:15%; margin-bottom:4px; margin-"
"right:25px;\"/>Lunes <hr=\"display:inline-block; anchura:15%; margin-"
"bottom:4px; margin-left:25px;\"/> <br/>\n"
"Introducción, CRM y Ventas\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Introducción:familiarizarse con el V7 </div>.\n"
"<table cellspacing=\"5\">\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>La arquitectura de Odoo </li>\n"
"<li>¿Cómo crear una instancia Online Odoo </li>\n"
"<li>Lista y formar opiniones, búsqueda y funciones de filtro </li>\n"
"<li>La información sobre herramientas y desgloses </li>\n"
"<li>Navegación avanzada </li>\n"
"vistas <li>Calendario, Gráfico, Kanban y Gantt </li>\n"
"<li>características de la red social:correo electrónico, la comunicación "
"interna </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"Los ejercicios de introducción se han creado para ayudarle a descubrir Odoo "
"de una manera fácil. Su principal objetivo es dar a conocer las diferentes "
"aplicaciones, menús, ver tipos, accesos directos, tipos de campo, magos, "
"acciones, etc. le ayudará a descubrir cómo navegar en Odoo! <br/> </br>\n"
"Por lo tanto, la introducción es obligatoria para todos, incluso si usted "
"necesita aprender un módulo específico dado en una otra fecha. Esta "
"obligación se ha hecho para asegurarse de que no estás perdido si llega en "
"medio de la formación de otro módulo.\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Módulo CRM </div>\n"
"<table cellspacing=\"5\">\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Gestión de Prospectos y Oportunidades </li>\n"
"<li>Llamadas, Reunión </li>\n"
"<li>Empresas y contactos </li>\n"
"<li>Acciones automatizadas </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Seguir todos los prospectos que entran y los empujan a las oportunidades "
"reales </li>\n"
"<li>Mejorar la productividad de la fuerza de ventas </li>\n"
"<li>Acceda a todos los documentos y mensajes relacionados con un contacto/"
"oportunidad en un solo lugar </li>\n"
"<li>Realizar el seguimiento del flujo de ventas por mes y \n"
"las previsiones de ventas precisas a nivel individual y de grupo\n"
"niveles </li>\n"
"<li>Seguimiento de desempeño individual de la gente de ventas </li>\n"
"</ul>\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Módulo Ventas </div>\n"
"<table>\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Los clientes </li>\n"
"<li>Productos para vender </li>\n"
"<li>Manual descuentos </li>\n"
"<li>Márgenes </li>\n"
"<li>Ventas y Almacén:Control de facturación (A petición/En\n"
"Orden de entrega), Política del transporte marítimo (En vez/Cada producto\n"
"si está disponible) </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>órdenes Registrarse de clientes y crear facturas </li>\n"
"<li>entregas Horario (para productos) o tareas (por servicio) </li>\n"
"<li>Asegúrese de que las órdenes de venta están debidamente facturados "
"mediante el seguimiento de las órdenes de venta pendientes </li>\n"
"</ul>\n"
"</td>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<strong> CRM y gestión de ventas:</strong>\n"
"<ul>\n"
"<li>En una oportunidad a una orden de venta </​​li>\n"
"<li>Reclamos </li>\n"
"<li>la comunicación posterior a la venta </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"Odoo puede trabajar con aplicaciones independientes, por lo que el\n"
"proceso comercial conjunto se puede integrar juntos en\n"
"Para lograr los flujos comerciales complejos.\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"</div>\n"
"<div style=\"height:auto; text-align:center; font-size:24px; color:#646464; "
"margin-top:30px;\">\n"
"<style hr=\"display:inline-block; anchura:15%; margin-bottom:4px; margin-"
"right:25px;\"/>Martes <hr=\"display:inline-block; anchura:15%; margin-"
"bottom:4px; margin-left:25px;\"/> <br/>\n"
"Los derechos de acceso, Ventas y Compras, Contabilidad financiera\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Los derechos de acceso </div>\n"
"<table cellspacing=\"5\">\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Usuarios </li>\n"
"<li>Grupos </li>\n"
"<li>Los derechos de acceso y reglas de acceso </li>\n"
"configuración <li>Módulos </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"Gracias a la gestión integrada de los derechos de acceso integrado, cada "
"usuario tiene un acceso a la información relevante. No aparece la pantalla "
"sobrecargado con una gran cantidad de datos irrelevantes, no hay menú "
"deseado, etc. Sólo una interfaz limpia!\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Compra </div>\n"
"<table cellspacing=\"5\">\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Los vendedores </li>\n"
"<li>Productos </li>\n"
"<li>Solicitud de Cotización </li>\n"
"<li>Orden de compra </li>\n"
"<li>Facturación y control de factura </li>\n"
"<li>para entrante (recibo completo/parcial) </li>\n"
"<li>Solicitud de pedido </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Mejor negociar volúmenes y precios a partir de datos históricos </li>\n"
"<li>Centralizar todos los datos para poder construir informes y estadísticas "
"</li>\n"
"<li>Controlar el proceso de facturación </li>\n"
"<li>Trabajar con solicitudes de compra para pedir diferentes proveedores que "
"le presente, citas </li>\n"
"</ul>\n"
"</td>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<strong> Ventas y Gestión de compras </strong>\n"
"<ul>\n"
"<li>Realizar pedido del producto </li>\n"
"<li>Proceso Minorista:comprar un producto y lo venden sin ninguna "
"personalización/fabricación </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"Mantenga el control del proceso de reposición con base en las demandas de "
"ventas! Gracias a esta integración, que será capaz de automatizar el "
"ordenamiento de las unidades que faltan ordenados por un cliente\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> La contabilidad financiera </div>\n"
"<table>\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Facturas </li>\n"
"<li>Los reembolsos </li>\n"
"<li>Las entradas y Conciliaciones Diario </li>\n"
"<li>Informes/cierre del año fiscal </li>\n"
"<li>Cuentas </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>El rápido codificar sus operaciones financieras o transacciones "
"contables </li>\n"
"<li>Realizar los pagos de forma fácil y adecuada conciliar estos pagos con "
"facturas </li>\n"
"<li>Creación rápida de facturas con preestablecidas impagos de deudor/"
"acreedor y cuentas de ingresos/gastos </li>\n"
"<li>Múltiples maneras para conciliar </li>\n"
"<li>Configuración de cuentas para garantizar la correcta visualización de "
"balance y la cuenta de pérdidas y ganancias </li>\n"
"<li>Aplicar métodos de diferimiento correctas para asegurar el cierre del "
"año fiscal </li>\n"
"</ul>\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"</div>\n"
"<div style=\"height:auto; text-align:center; font-size:24px; color:#646464; "
"margin-top:30px;\">\n"
"<style hr=\"display:inline-block; anchura:15%; margin-bottom:4px; margin-"
"right:25px;\"/>Miércoles <hr=\"display:inline-block; anchura:15%; margin-"
"bottom:4px; margin-left:25px;\"/> <br/>\n"
"La gestión de proyectos, recursos humanos, gestión de contratos\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Gestión de Proyectos </div>\n"
"<table cellspacing=\"5\">\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Proyectos </li>\n"
"<li>Tareas </li>\n"
"<li>Problemas </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Gestión y seguir sus proyectos, tareas y temas </li>\n"
"<li>Descubra la vista Kanban para operar rápidamente algunas acciones y "
"detectar rápidamente los cuellos de botella en sus proyectos flujos </li>\n"
"<li>Descubra la vista de Gantt para ver cómo se ha planificado su proyecto y "
"adaptarlo de acuerdo a los nuevos elementos o para el orden del día la gente "
"</li>\n"
"<li>Definir los miembros del proyecto y delegar tareas </li>\n"
"<li>Crear líneas automáticamente parte de horas en el menú de Recursos "
"Humanos de las actividades de trabajo registrados </li>\n"
"<li>Pista y la factura (a los clientes) los costes asociados a un proyecto </"
"li>\n"
"<li>Administrar un sistema de ticket de soporte </li>\n"
"</ul>\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Recursos Humanos </div>\n"
"<table cellspacing=\"5\">\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Los empleados </li>\n"
"<li>Reclutamiento </li>\n"
"<li>Gastos </li>\n"
"<li>Asignaciones y hojas solicitudes </li>\n"
"<li>Gestión del tiempo:las líneas de parte de horas y parte de horas </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"<strong> Acerca de Reclutamiento:</strong>\n"
"<ul>\n"
"<li>Mejor negociar volúmenes y precios a partir de datos históricos </li>\n"
"<li>Centralizar todos los datos para poder construir informes y estadísticas "
"</li>\n"
"<li>Controlar el proceso de facturación </li>\n"
"<li>Trabajar con solicitudes de compra para pedir diferentes proveedores que "
"le presente, citas </li>\n"
"</ul>\n"
"<strong> Sobre Vacaciones:</strong>\n"
"<ul>\n"
"<li>Realizar el seguimiento del número de días de vacaciones acumulados por "
"cada empleado </li>\n"
"<li>Permitir a los administradores para aprobar las solicitudes de licencia "
"y copias de seguridad del plan para sus equipos </li>\n"
"</ul>\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Gestión de la contratación </div>\n"
"<table>\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Contratos </li>\n"
"<li>métodos de facturación\n"
"<li>órdenes de venta </​​li>\n"
"<li>Las Hojas de Registro </li>\n"
"<li>Gastos </li>\n"
"</li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Centralizar los datos relacionados con un contrato específico en un solo "
"punto </li>\n"
"<li>Realizar la facturación de un solo lugar (orden Ventas/parte de horas/"
"gastos) </li>\n"
"<li>Siga el proceso de renovación </li>\n"
"<li>Estadísticas Calcular con base en los ingresos esperados </li>\n"
"</ul>\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"</div>\n"
"<div style=\"height:auto; text-align:center; font-size:24px; color:#646464; "
"margin-top:30px;\">\n"
"<style hr=\"display:inline-block; anchura:15%; margin-bottom:4px; margin-"
"right:25px;\"/>Jueves <hr=\"display:inline-block; anchura:15%; margin-"
"bottom:4px; margin-left:25px;\"/> <br/>\n"
"Gestión de almacén, fabricación (MRP) y ventas, importación/exportación\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Gestión de almacén </div>\n"
"<table cellspacing=\"5\">\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Imagenes mueve </li>\n"
"<li>Inventario </li>\n"
"<li>Entrega parcial/recepción </li>\n"
"<li>Las unidades de medida </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"La adminitración de Inventarios es a la vez muy simple, flexible y completa. "
"Se basa en el concepto de doble entrada que revolucionó la "
"contabilidad:'nada se pierde, todo se mueve. En Odoo, no hablamos sobre la "
"desaparición, el consumo o la pérdida de los productos:en vez hablamos "
"solamente de la acción se mueve de un lugar a otro.\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Fabricación (MRP) </div>\n"
"<table cellspacing=\"5\">\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Lista de materiales </li>\n"
"<li>Lista de materiales Multi-nivel </li>\n"
"<li>Hojas de ruta </​​li>\n"
"<li>Operaciones del centro de trabajo </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Gestión de la planificación de la producción </li>\n"
"<li>Organizar los diferentes órdenes de fabricación para optimizar la carga "
"de trabajo de los diferentes recursos </li>\n"
"<li>Desde una perspectiva operativa, la pista que necesitan productos y "
"cantidades a fabricar </li>\n"
"</ul>\n"
"<strong> Como gerente:</strong>\n"
"<ul>\n"
"<li>Definir productos que se ensamblan o para ser vendidos como un kit </"
"li>\n"
"<li>Organizar la tubería de la producción </li>\n"
"<li>Siga el costo de una orden de producción y medir la eficiencia del "
"departamento </li>\n"
"</ul>\n"
"</td>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<strong> MRP y Ventas </strong>\n"
"<ul>\n"
"<li>Configuración del producto - Reservar inventario/Preparar Ordenes </li>\n"
"<li>Lista de materiales Comercial (kit) </li>\n"
"<li>Just in Time </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"Desde la perspectiva de ventas, tendrá una orden de producción generada en "
"base a una solicitud procedente de un cliente sin ninguna operación manual. "
"Esta integración de estas dos aplicaciones permite llevar a cabo el "
"siguiente flujo de forma automatizada:fin Venta -> verificación nivel de "
"inventario -> La producción de las unidades que faltan -> Receival en stock "
"de los productos terminados -> Entrega al cliente.\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Importar/Exportar </div>\n"
"<table>\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Importar desde archivos CSV </li>\n"
"<li>Exportar como CSV o como archivo de Excel </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Transferir datos de un sistema a Odoo. </li>\n"
"</ul>\n"
"Odoo ofrece una forma sencilla de importar y exportar datos. Odoo le guiará "
"para esta fase gracias a un FAQ directamente integrados en la pantalla de "
"importación\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"</div>\n"
"<div style=\"height:auto; text-align:center; font-size:24px; color:#646464; "
"margin-top:30px;\">\n"
"<style hr=\"display:inline-block; anchura:15%; margin-bottom:4px; margin-"
"right:25px;\"/>Viernes <hr=\"display:inline-block; anchura:15%; margin-"
"bottom:4px; margin-left:25px;\"/> <br/>\n"
"Listas de precios, punto de venta (POS), Introducción a reportar "
"personalización\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Listas de precios </div>\n"
"<table cellspacing=\"5\">\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Los precios para el retorno de los clientes </li>\n"
"<li>Los precios en divisas extranjeras (con tasas de cambio) </li>\n"
"<li>precios de minorista (basado en un margen mínimo) </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Mantenga el control sobre la política de precios </li>\n"
"<li>Permitir descuentos para algunos períodos </li>\n"
"<li>Asegúrese de que sus márgenes, incluso cuando se define descuentos </"
"li>\n"
"<li>Permitir diferentes precios por el cliente y/o proveedor </li>\n"
"<li>Modificar los precios de una manera fácil </li>\n"
"</ul>\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Punto de Venta (POS) </div>\n"
"<table cellspacing=\"5\">\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Formas de pago (revistas) </li>\n"
"<li>Configuración POS </li>\n"
"<li>POS front-end </li>\n"
"<li>POS de fondo </li>\n"
"<li>Tiendas </li>\n"
"<li>Sesiones </li>\n"
"<li>Órdenes POS </li>\n"
"<li>Re-facturación </li>\n"
"<li>Escaneado/Auto-escaneo </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"Odoo POS es el primer punto de venta se ejecuta en un entorno basado en 100% "
"web, lo que significa que cualquier ordenador con un navegador puede acoger "
"el POS. El punto de venta es un sistema de dos partes con un front-end "
"(interacción con el cliente) y un back-end (los administradores pueden "
"configurar el sistema, imprimir informes, analizar, ...) <br/>\n"
"<strong> Sobre el front-end:</strong>\n"
"<ul>\n"
"<li>El modo sin conexión. Imagínese varios casos estaban teniendo un modo "
"fuera de línea es de interés primordial:Conexión al cierre del servidor en "
"un supermercado grande/uso en ambientes que requiere movilidad pero sin wifi/"
"uso en entornos grandes como restaurantes y jardines/uso en entornos de baja "
"tecnología </li>\n"
"</ul>\n"
"<strong> Sobre el back-end:</strong>\n"
"<ul>\n"
"<li>Configure los productos que se venden en el POS </li>\n"
"<li>Configurar los métodos de pago </li>\n"
"<li>Imprimir informe de ventas diario </li>\n"
"<li>Analizar ventas </li>\n"
"</ul>\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"<div style=\"text-align:left; font-size:24px; color:#646464; margin-top:30px;"
"\"> Introducción a la personalización de reportes </div>\n"
"<table>\n"
"<tr style=\"text-align:center; font-size:20px; de color:#FFFFFF; background-"
"color:#969696;\">\n"
"<th> Contenido </th>\n"
"<th> ¿Qué vas a aprender? </th>\n"
"</tr>\n"
"<tr style=\"text-align:left; font-size:16px; color:#646464;\">\n"
"<td style=\"width:50%;\">\n"
"<ul>\n"
"<li>Personalizar el diseño de sus facturas </li>\n"
"<li>Cambie el pie/cabecera de los documentos </li>\n"
"<li>Añadir un campo en un documento/informe estándar </li>\n"
"</ul>\n"
"</td>\n"
"<td style=\"width:50%;\">\n"
"Esta cuenta le ayudará a personalizar totalmente los documentos estándar "
"hechos de Odoo.\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"</div>\n"
"<div style=\"text-align:center; background-color:#FFFFFF; margin-top:30px;"
"\">\n"
"<table cellspacing=\"5\" align=\"center\" cellpadding=\"5\">\n"
"<tr style=\"font-size:20px; color:#333333; margin-top:30px;\">\n"
"<td>Inglés</td>\n"
"<td>Inglés</td>\n"
"<td>español</td>\n"
"</tr>\n"
"<tr style=\"font-size:10px; color:#646464;\">\n"
"<td>Europa</td>\n"
"<td>EE.UU. y Canadá</td>\n"
"<td>EE.UU. y América Latina</td>\n"
"</tr>\n"
"<tr style=\"font-size:10px; de color:#FFFFFF; background-color:#8b5bdd;\">\n"
"<td><a href=\"http://onlinetrainingencet.eventbrite.com\" style=\"text-"
"decoration:none; el color:#FFFFFF;\" target=_new> Registrarse </a></td>\n"
"<td><a href=\"http://onlinetrainingenpst.eventbrite.com\" style=\"text-"
"decoration:none; el color:#FFFFFF;\" target=_new> Registrarse </a></td>\n"
"<td><a href=\"http://onlinetrainingespst.eventbrite.com\" style=\"text-"
"decoration:none; el color:#FFFFFF;\" target=_new> Registrarse </a></td>\n"
"</tr>\n"
"</table>\n"
"</div>\n"
"<color hr=\"DCDCFB\"/>\n"
"<div style=\"height:auto; text-align:center; font-size:30px; color:#333333; "
"margin-top:10px;\">\n"
"Material de entrenamiento\n"
"</div>\n"
"<div style=\"height:auto; text-align:left; font-size:16px; color:#646464; "
"margin-top:30px;\">\n"
"Tan pronto como se finalizará la inscripción (es decir. La confirmación del "
"pago), a los participantes se les proporcionará el material listado\n"
"abajo:\n"
"<ul>\n"
"<li>El acceso a videos en línea con demostraciones detalladas </li>\n"
"<li>Material de entrenamiento con ejercicios </li>\n"
"<li>soluciones escritas a los ejercicios </li>\n"
"</ul>\n"
"Por lo tanto, hay una ventaja por registrarse temprano al entrenamiento si "
"usted está interesado en la preparación de sus sesiones en directo antes de "
"tiempo. Por otra parte, el número de participantes está limitado por sesión "
"en línea, que es otra razón para considerar registración temprana y la "
"seguridad de su asiento.\n"
"</div>\n"
"<div style=\"text-align:center; background-color:#FFFFFF; margin-top:30px; "
"font-size:20px; color:#4e66e7\">\n"
"<table cellspacing=\"5\">\n"
"<tr>\n"
"<td>\n"
"Con las sesiones en vivo, hemos aprendido mucho más de lo que estaba "
"cubierto de los videos. Sin duda, un buen comienzo para la construcción de "
"una base sólida de conocimiento de Odoo trabajo.\n"
"</td>\n"
"<td>\n"
"El capacitador hizo un trabajo maravilloso con el entrenamiento! Él estaba "
"bien informado y en línea y fuera de línea interactiva. Muchas gracias por "
"la experiencia!\n"
"</td>\n"
"</tr>\n"
"<tr>\n"
"<td>\n"
"Sería muy difícil cubrir todas las posibilidades de Odoo y creo que los "
"principales aspectos fueron cubiertos muy bien\n"
"</td>\n"
"<td>\n"
"El capacitador tiene un muy buen conocimiento de la materia, que entiende "
"los problemas de los alumnos muy rápidas y proporcionadas respuestas con el "
"nivel adecuado de ilustración.\n"
"</td>\n"
"</tr>\n"
"</table>\n"
"</div>\n"
"</div>"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
msgid "Body"
msgstr "Contenido"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
msgid "Email Template"
msgstr "Plantilla de Correo Electrónico"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_account_invoice
msgid "Invoice"
msgstr "Factura"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Invoice Confirmation Email"
msgstr "Correo de Confirmación de Factura"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Miscellaneous"
msgstr "Miscellaneous"

#. module: product_email_template
#: model:mail.template,subject:product_email_template.product_online_training_email_template
#: model:product.product,name:product_email_template.product_online_training
#: model:product.template,name:product_email_template.product_online_training_product_template
msgid "Online Training"
msgstr "Entrenamiento en línea"

#. module: product_email_template
#: model:ir.model.fields,field_description:product_email_template.field_product_template_email_template_id
msgid "Product Email Template"
msgstr "Plantilla del Correo electrónico del Producto"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: product_email_template
#: model:ir.model.fields,help:product_email_template.field_product_template_email_template_id
msgid ""
"When validating an invoice, an email will be sent to the customer based on "
"this template. The customer will receive an email for each product linked to "
"an email template."
msgstr ""
"Al validar una factura, un correo electrónico será enviado al cliente basado "
"en esta plantilla. El cliente recibirá un correo electrónico para cada "
"producto vinculado a una plantilla de correo electrónico."
