# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_blog
# 
# Translators:
# <PERSON><PERSON><PERSON>, <PERSON><PERSON> <jong<PERSON><PERSON><EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> CHOI <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (사본)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "' page header."
msgstr "' 페이지 머리말."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "'. Showing results for '"
msgstr "'. 다음에 대한 결과 표시 중 '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "-- All dates"
msgstr "-- 모든 날짜"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Binoculars are lightweight and portable.</b> Unless you have the luxury "
"to set up and operate an observatory from your deck, you are probably going "
"to travel to perform your viewings. Binoculars go with you much easier and "
"they are more lightweight to carry to the country and use while you are "
"there than a cumbersome telescope set up kit."
msgstr ""
"<b>쌍안경은 가볍고 휴대할 수 있습니다.</b> 만약 여러분이 갑판에서 전망대를 세우고 운영할 수 있는 사치가 없다면, 여러분은 아마 "
"여러분의 시야를 보여주기 위해 여행을 할 것입니다. 쌍안경은 여러분에게 훨씬 더 쉽게 어울리며 여러분이 거추장스러운 망원경 설치 키트보다"
" 여러분이 그곳에 있는 동안 사용하기 더 가볍습니다."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Click on Save</b> to record your changes."
msgstr "<b>저장을 클릭하면</b> 변경사항을 기록합니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Pick the brains of the experts</b>. If you are not already active in an "
"astronomy society or club, the sales people at the telescope store will be "
"able to guide you to the active societies in your area. Once you have "
"connections with people who have bought telescopes, you can get advice about"
" what works and what to avoid that is more valid than anything you will get "
"from a web article or a salesperson at Wal-Mart."
msgstr ""
"<b>전문가의 두뇌를 골라보세요.</b> 만약 여러분이 이미 천문학 사회나 클럽에서 활동하지 않는다면, 망원경 가게에 있는 영업 사원들은"
" 여러분이 속한 지역의 활동적인 사회로 여러분을 안내할 수 있을 것입니다. 일단 여러분이 망원경을 구입한 사람들과 연결되면, 여러분은 웹"
" 기사나 월마트의 영업 사원으로부터 얻을 수 있는 어떤 것보다 더 효과적이고 어떤 것을 피해야 하는지에 대한 조언을 얻을 수 있습니다."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr "<b>블로그 게시물을 게시</b>하여 방문자가 볼 수 있도록 합니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "<b>Sign in</b>"
msgstr "<b>로그인</b>"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>That's it, your blog post is published!</b> Discover more features "
"through the <i>Customize</i> menu."
msgstr ""
"<b>바로 그겁니다, 당신의 블로그 게시물이 공개됐습니다!</b> <i>맞춤형</i> 메뉴를 통해 더 많은 기능을 검색할 수 있습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Try before you buy.</b> This is another advantage of going on some field "
"trips with the astronomy club. You can set aside some quality hours with "
"people who know telescopes and have their rigs set up to examine their "
"equipment, learn the key technical aspects, and try them out before you sink"
" money in your own set up."
msgstr ""
"<b>사기 전에 한번 해보세요.</b> 이것은 천문학 클럽과 함께 견학을 가는 또 다른 장점입니다. 여러분은 망원경을 아는 사람들과 함께"
" 좋은 시간을 예약할 수 있습니다. 그리고 장비를 설치해서 장비를 검사하고, 핵심 기술적 측면을 배우고, 돈을 여러분 자신의 시설에 "
"투자하기 전에 그것들을 시험해 볼 수 있습니다."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""
"<b>여기에 당신의 이야기를 쓰세요.</b> 상단 도구 모음을 사용하여 이미지 또는 테이블을 추가하고 굵게 또는 기울임꼴로 설정하는 등의"
" 텍스트를 스타일링할 수 있습니다. 더 많은 그래픽 블로그를 위해 빌딩 블록을 끌어다 놓습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"<em class=\"h4 my-0\">Apart from the native population, the local wildlife "
"is also a major crowd puller.</em>"
msgstr "<em class=\"h4 my-0\">원주민과는 별도로, 지역 야생동물들도 주요 관광원입니다.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<em class=\"h4 my-0\">It is critically important that you get just the right"
" telescope.</em>"
msgstr "<em class=\"h4 my-0\">여러분이 올바른 망원경을 얻는 것은 매우 중요합니다.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"<em class=\"h4 my-0\">That “Wow” moment is what astrology is all about.</em>"
msgstr "<em class=\"h4 my-0\">“우와”하는 순간은 점성술의 전부입이다.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"
msgstr ""
"<em class=\"h4 my-0\">더 많은 리뷰를 읽을수록, 여러분은 그 리뷰들이 어떻게 극단적인 의견으로 군집하는지를 더 많이 "
"알게 됩니다.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "<em class=\"h4 my-0\">There is something timeless about the cosmos.</em>"
msgstr "<em class=\"h4 my-0\">우주에는 시대를 초월한 무언가가 있습니다.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"<em class=\"h4 my-0\">Your study of the moon, like anything else, can go "
"from the simple to the very complex.</em>"
msgstr ""
"<em class=\"h4 my-0\">달에 대한 여러분의 연구는 다른 것들과 마찬가지로 단순한 것에서 아주 복잡한 것으로 갈 수 "
"있습니다.</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "<em>No tags defined</em>"
msgstr "<em>정의된 태그 없음</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"
msgstr ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Post date\" title=\"Post date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"
msgstr "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""
"<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" "
"title=\"Twitter\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-sm\">Read "
"Next</span>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-sm text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"
msgstr ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-sm text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "<span class=\"mr-1\">Show:</span>"
msgstr "<span class=\"mr-1\">보기 :</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "<span class=\"nav-link disabled pl-0\">Blogs:</span>"
msgstr "<span class=\"nav-link disabled pl-0\">블로그 :</span>"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "A great way to discover hidden places"
msgstr "숨겨진 장소를 발견하는 좋은 방법"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A holiday to the Copper Canyon promises to be an exciting mix of relaxation,"
" culture, history, wildlife and hiking."
msgstr "코퍼 캐년으로의 휴가는 휴식, 문화, 역사, 야생 동물 및 하이킹의 흥미로운 혼합이 될 것을 약속합니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr "새로운 게시물"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A traveler may choose to explore the area by hiking around the canyon or "
"venturing into it. Detailed planning is required for those who wish to "
"venture into the depths of the canyon. There are a number of travel "
"companies that specialize in organizing tours to the region. Visitors can "
"fly to Copper Canyon using a tourist visa, which is valid for 180 days. "
"Travelers can also drive from anywhere in the United States and acquire a "
"visa at the Mexican customs station at the border."
msgstr ""
"여행자는 협곡 주변을 하이킹하거나 주변을 뚫어 주변을 탐험 할 수 있습니다. 협곡 깊이를 탐험하려는 사람들에게는 자세한 계획이 "
"필요합니다. 이 지역으로 여행을 조직하는 여행 회사가 많이 있습니다. 방문객들은 180 일 동안 유효한 관광 비자를 사용하여 코퍼 "
"캐년으로 비행 할 수 있습니다. 여행자는 미국 어느 곳에서나 운전할 수 있으며 국경의 멕시코 세관에서 비자를 취득 할 수 있습니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "About us"
msgstr "회사 소개"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Above all, <b>establish a relationship with a reputable telescope shop</b> "
"that employs people who know their stuff. If you buy your telescope at a "
"Wal-Mart or department store, the odds you will get the right thing are "
"remote."
msgstr ""
"무엇보다도, 그들의 물건을 잘아는 사람들을 고용하는 <b>평판이 좋은 망원경 가게</b>와 관계를 맺으십시오.\n"
"월마트나 백화점에서 망원경을 구입하면 옳은 것을 얻을 확률은 멀기만 합니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr "게시물 열기"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "활성"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Add some"
msgstr "일부 추가"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "All"
msgstr "전체"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "All Blogs"
msgstr "모든 블로그"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "All blogs"
msgstr "모든 블로그"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Alone in the ocean"
msgstr "바다에서 홀로"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Along those lines, how difficult is the set up and break down?"
msgstr "그 선들을 따라 설치와 고장이 얼마나 어렵습니까?"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.js:0
#, python-format
msgid "Amazing blog article: %s! Check it live: %s"
msgstr "놀라운 블로그 기사: %s! 실시간 확인 : %s"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "An exciting mix of relaxation, culture, history, wildlife and hiking."
msgstr "휴식, 문화, 역사, 야생동물 그리고 하이킹이 흥미진진하게 혼합되어 있습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"And when all is said and done,<b> get equipped</b>. Your quest for newer and"
" better telescopes will be a lifelong one. Let yourself get addicted to "
"astronomy and the experience will enrich every aspect of life. It will be an"
" addiction you never want to break."
msgstr ""
"그리고 모든 것이 말되고 완료되면 <b>장비를 갖추십시오.</b> 새롭고 더 나은 망원경에 대한 당신의 탐구는 평생 지속될 것입니다. "
"천문학에 중독되면 인생의 모든면이 풍요로워질 것입니다. 절대 끊고 싶지 않은 중독이 될 것입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Another unique feature of Copper Canyon is the presence of the Tarahumara "
"Indian culture. These semi-nomadic people live in cave dwellings. Their "
"livelihood chiefly depends on farming and cattle ranching."
msgstr ""
"쿠퍼 캐논의 또 다른 독특한 특징은 타라후마라 인디언 문화의 존재이다. 이 반유목민들은 동굴에서 살고 있습니다. 그들의 생계는 주로 "
"농사와 소 목축에 달려 있습니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_archive_display
msgid "Archive"
msgstr "보관"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "보관됨"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_archives
msgid "Archives"
msgstr "보관"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Article"
msgstr "기사"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Articles"
msgstr "기사"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_2
msgid "Astronomy"
msgstr "천문학"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Astronomy clubs are lively places full of knowledgeable amateurs who love to"
" share their knowledge with you. For the price of a coke and snacks, they "
"will go star gazing with you and overwhelm you with trivia and great "
"knowledge."
msgstr ""
"천문학 클럽은 지식을 당신과 나누기를 좋아하는 지식이 풍부한 아마추어들로 가득한 활기찬 곳입니다. 코카콜라와 간식의 가격을 위해, 그들은"
" 당신을 응시하고 별과 훌륭한 지식으로 당신을 압도할 것입니다."

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_2
msgid "Astronomy is “stargazing\""
msgstr "천문학은 “별자리\"입니다"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "아톰 피드"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "작성자"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_name
msgid "Author Name"
msgstr "작성자 이름"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr "아바타"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Awesome hotel rooms"
msgstr "Aw근사한 호텔 객실"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_4
msgid "Be aware of this thing called “astronomy”"
msgstr " “천문학”이라고 불리는 것을 알아두세요."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Becoming part of the society of devoted amateur astronomers will give you "
"access to these organized efforts to reach new levels in our ability to "
"study the Earth’s moon. And it will give you peers and friends who share "
"your passion for astronomy and who can share their experience and areas of "
"expertise as you seek to find where you might look next in the huge night "
"sky, at the moon and beyond it in your quest for knowledge about the "
"seemingly endless universe above us."
msgstr ""
"헌신적인 아마추어 천문학자들의 사회의 일부가 되는 것은 여러분이 지구의 달을 연구하는 우리의 능력에 새로운 수준에 도달하기 위한 조직적인"
" 노력에 접근할 수 있도록 해줄 것입니다. 그리고 그것은 여러분에게 천문학에 대한 열정을 공유하고, 거대한 밤하늘, 달 그리고 그 "
"너머에서 우리 위에 끝이 없어 보이는 우주에 대한 지식을 찾기 위한 여러분의 탐색에서 그들의 경험과 전문분야를 공유할 수 있는 동료와 "
"친구들에게 줄 것입니다."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_7
msgid "Becoming part of the society of devoted amateur astronomers."
msgstr "헌신적인 아마추어 천문학자들의 사회의 일부가 되었습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Bedroom Facilities"
msgstr "침실 시설"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"Before you go to that big expense, it might be a better next step from the "
"naked eye to invest in a good set of binoculars. There are even binoculars "
"that are suited for star gazing that will do just as good a job at giving "
"you that extra vision you want to see just a little better the wonders of "
"the universe. A well designed set of binoculars also gives you much more "
"mobility and ability to keep your “enhanced vision” at your fingertips when "
"that amazing view just presents itself to you."
msgstr ""
"그 큰 비용을 들이기 전에, 좋은 쌍안경에 투자하는 것이 육안으로부터 다음 단계로 나아질지도 모릅니다. 심지어 별을 관찰하는데 적합한 "
"쌍안경도 있습니다. 이 쌍안경은 우주의 경이로움을 조금 더 잘 보고 싶은 시력을 더 잘 제공할 것입니다. 또한 잘 설계된 쌍안경 세트는 "
"놀라운 시야가 사용자에게 보여질 때 \"강화된 비전\"을 손끝에 유지할 수 있는 이동성과 능력을 훨씬 더 많이 제공합니다."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_6
msgid "Before you make your first purchase…"
msgstr "첫 구매를 하기 전에 말이죠..."

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_7
msgid "Beyond The Eye"
msgstr "이면"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model:website.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog"
msgstr "블로그"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr "블로그 이름"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog Post"
msgstr "블로그 게시물"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to be calling this file !"
msgstr " <b>%s</b> 블로그 게시물이 이 파일을 호출하는 것 같습니다!"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to have a link to this page !"
msgstr "<b>%s</b> 블로그 게시물이 이 페이지에 대한 링크가 있는 것 같습니다!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Blog Post Cover"
msgstr "블로그 게시물 표지"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "블로그 게시물 제목"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.actions.act_window,name:website_blog.action_blog_post
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_ids
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
#, python-format
msgid "Blog Posts"
msgstr "블로그 게시물"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "블로그 소제목"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "블로그 태그"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr "블로그 태그 범주"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr "블로그 태그"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Blog Title"
msgstr "블로그 제목"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Blog's Title"
msgstr "블로그 제목"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
msgid "Blogs"
msgstr "블로그"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"But how do you sift through the amazing choices on offer? And more "
"importantly, do you really trust the photographs and descriptions of the "
"hotels that they have awarded themselves with the motivation of getting "
"bookings? Traveler reviews can be helpful, but you need to exercise caution."
" They are often biased, sometimes out of date, and may not serve your "
"interests at all. How do you know that the features that are important to "
"the reviewer are important to you?"
msgstr ""
"하지만 여러분은 어떻게 제안된 놀라운 선택들을 걸러낼 수 있을까요? 그리고 더 중요한 것은, 호텔 예약의 동기를 부여받은 호텔의 사진과 "
"설명을 정말로 신뢰하고 있습니까? 여행자 리뷰는 도움이 될 수 있지만, 주의할 필요가 있습니다. 그들은 종종 편견을 가지고, 때로는 "
"시대에 뒤떨어져, 여러분의 이익에 전혀 도움이 되지 않을 수도 있습니다. 검토자에게 중요한 기능이 귀하에게 중요하다는 것을 어떻게 알 수"
" 있습니까?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_6
msgid "Buying A Telescope"
msgstr "망원경 사기"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Buying the right telescope to take your love of astronomy to the next level "
"is a big next step in the development of your passion for the stars."
msgstr ""
"천문학에 대한 당신의 사랑을 다음 단계로 끌어 올리기 위해 올바른 망원경을 구입하는 것은 별에 대한 열정을 발전시키는 데 있어 큰 다음 "
"단계입니다."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__can_publish
msgid "Can Publish"
msgstr "게시 가능"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "카테고리"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Children’s’ Facilities"
msgstr "어린이 시설"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Choose an image from the library."
msgstr "라이브러리에서 이미지를 선택하십시오."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Click here to add new content to your website."
msgstr "웹사이트에 새 콘텐츠를 추가하려면 여기를 클릭하세요."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid ""
"Click on \"<b>New</b>\" in the top-right corner to write your first blog "
"post."
msgstr "첫 번째 블로그 게시물을 작성하려면 오른쪽 상단 모서리에 있는 \"<b>새로 만들기</b>\"를 클릭하십시오."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Close"
msgstr "닫기"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Comment"
msgstr "댓글"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Comments"
msgstr "의견"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__content
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "내용"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Copper Canyon is one of the six gorges in the area. Although the name "
"suggests that the gorge might have some relevance to copper mining, this is "
"not the case. The name is derived from the copper and green lichen covering "
"the canyon. Copper Canyon has two climatic zones. The region features an "
"alpine climate at the top and a subtropical climate at the lower levels. "
"Winters are cold with frequent snowstorms at the higher altitudes. Summers "
"are dry and hot. The capital city, Chihuahua, is a high altitude desert "
"where weather ranges from cold winters to hot summers. The region is unique "
"because of the various ecosystems that exist within it."
msgstr ""
"구리 협곡은 이 지역에 있는 여섯 개의 협곡 중 하나입니다. 비록 그 이름이 그 협곡은 구리 채굴과 어느 정도 관련이 있을 수 있다는 "
"것을 암시하지만, 사실은 그렇지 않습니다. 그 이름은 협곡을 덮고 있는 구리와 녹색 이끼에서 유래되었습니다. 구리 협곡에는 두 개의 기후"
" 지역이 있습니다. 이 지역은 상층부에 고산 기후가 있고 하층부에 아열대 기후가 있습니다. 높은 고도에서 잦은 눈보라로 겨울은 춥습니다."
" 여름은 건조하고 덥습니다. 수도 치와와는 추운 겨울에서 더운 여름까지 날씨가 이어지는 고지대 사막입니다. 그 지역은 그 안에 존재하는 "
"다양한 생태계 때문에 독특합니다."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__cover_properties
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr "표지 속성"

#. module: website_blog
#: model_terms:ir.actions.act_window,help:website_blog.action_blog_post
msgid "Create a new blog post"
msgstr "새 블로그 게시물 만들기"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "작성자"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "작성일자"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "날짜 (최근 날짜 순)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "날짜 (오래된 날짜 순)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Description"
msgstr "설명"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Dexter"
msgstr "덱스터"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "표시명"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_edit_options
msgid "Duplicate"
msgstr "복제"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "East Maui"
msgstr "서 마우이"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"East Maui helicopter tours will give you a view of the ten thousand foot "
"volcano, Haleakala or House of the sun. This volcano is dormant and last "
"erupted in 1790. You will be able to see the crater of the volcano and the "
"dry, arid earth surrounding the south side of the volcano’s slop with Maui "
"helicopter tours."
msgstr ""
"이스트 마우이 헬리콥터 투어는 당신에게 만 피트의 화산, 할레아칼라 또는 태양의 집을 볼 수 있게 해 줄 것입니다. 이 화산은 "
"1790년에 마지막으로 분출되었습니다. 여러분은 마우이 헬리콥터 투어를 통해 화산의 분화구와 화산 슬롭의 남쪽을 둘러싸고 있는 건조하고 "
"건조한 대지를 볼 수 있을 것입니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Edit in backend"
msgstr "백엔드에서 편집하기"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the '"
msgstr "편집 '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'All Blogs' page header."
msgstr "'모든 블러그' 페이지 머릿말을 편집하기."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'Filter Results' page header."
msgstr "'필터 결과' 페이지 헤더를 편집하기."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Facebook"
msgstr "페이스북"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_3
msgid "Facts you should bear in mind."
msgstr "당신이 명심해야 할 사실."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Finally and most importantly, the quality hotel directory inspection team "
"should have visited the hotel in question on a regular basis, met the staff,"
" slept in a bedroom and tried the food. They should experience the hotel as "
"only a hotel guest can and it is only then that they are really in a strong "
"position to write about the hotel."
msgstr ""
"마지막으로 가장 중요한 것은 품질 좋은 호텔 디렉토리 검사팀이 정기적으로 해당 호텔을 방문해서 직원들을 만나고 침실에서 잠을 자고 음식을"
" 먹어봤어야 한다는 것입니다. 그들은 호텔 투숙객만이 할 수 있는 것처럼 호텔을 경험해야 하며, 그때서야 비로소 호텔에 대해 글을 쓸 수"
" 있는 유리한 위치에 서게 됩니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Follow Us"
msgstr "팔로우하세요"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (파트너)"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"For many of us who are city dwellers, we don’t really notice that sky up "
"there on a routine basis. The lights of the city do a good job of disguising"
" the amazing display that is above all of our heads all of the time."
msgstr ""
"도시 거주자인 우리들 중 많은 사람들은 일상적으로 저 위의 하늘을 보지 못합니다. 도시의 불빛은 항상 우리 머리 위에 있는 놀라운 전시를"
" 잘 위장합니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"For many of us, our very first experience of learning about the celestial "
"bodies begins when we saw our first full moon in the sky. It is truly a "
"magnificent view even to the naked eye."
msgstr ""
"우리들 중 많은 사람들에게, 천체에 대한 우리의 첫 번째 경험은 하늘에 있는 우리의 첫 보름달을 보았을 때 시작됩니다. 육안으로도 정말 "
"장관입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"From the tiniest baby to the most advanced astrophysicist, there is "
"something for anyone who wants to enjoy astronomy. In fact, it is a science "
"that is so accessible that virtually anybody can do it virtually anywhere "
"they are. All they have to know how to do is to look up."
msgstr ""
"가장 작은 아기부터 가장 발달된 천체물리학자에 이르기까지, 천문학적인 것을 즐기려는 사람이라면 누구나 할 수 있는 것이 있습니다. 사실,"
" 과학은 매우 접근하기 쉬운 과학입니다. 사실상 누구나 어디서든 거의 할 수 있습니다. 그들이 아는 것은 위를 올려다보는 것 뿐입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get a geek"
msgstr "괴짜를 잡으세요"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get a telescope"
msgstr "망원경을 받으세요"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get some history"
msgstr "역사를 좀 찾아보세요."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get started"
msgstr "시작함"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "그룹별"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__has_message
#: model:ir.model.fields,field_description:website_blog.field_blog_post__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Here are some of the key facts you should bear in mind:"
msgstr "기억해야 할 주요 사실은 다음과 같습니다."

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Holiday tips"
msgstr "휴일 팁"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_4
msgid "How To Look Up"
msgstr "찾는 방법"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"How complex is the telescope and will you have trouble with maintenance? "
"Network to get the answers to these and other questions. If you do your "
"homework like this, you will find just the right telescope for this next big"
" step in the evolution of your passion for astronomy."
msgstr ""
"망원경은 얼마나 복잡하고 유지관리에 문제가 있습니까? 네트워크로 이러한 질문과 기타 질문에 대한 답을 얻을 수 있습니다. 만약 여러분이 "
"이렇게 숙제를 한다면, 여러분은 천문학에 대한 열정의 진화에 있어 이 다음 큰 단계를 위한 적절한 망원경을 찾을 수 있을 것입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "How mobile must your telescope be?"
msgstr "당신의 망원경은 얼마나 이동성이 있어야 합니까?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_3
msgid "How to choose the right hotel"
msgstr "올바른 호텔을 선택하는 방법"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"If it matters that your hotel is, for example, on the beach, close to the "
"theme park, or convenient for the airport, then location is paramount. Any "
"decent directory should offer a location map of the hotel and its "
"surroundings. There should be distance charts to the airport offered as well"
" as some form of interactive map."
msgstr ""
"호텔이 해변이나 테마 파크 근처에 있거나 공항이 편리하다는 점이 중요한 경우, 위치가 가장 중요합니다. 괜찮은 디렉토리는 호텔과 호텔 "
"주변의 위치 지도를 제공해야 합니다. 공항까지의 거리 도표와 대화형 맵이 제공되어야 합니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"If the night is clear, you can see amazing detail of the lunar surface just star gazing on in your back yard.\n"
"Naturally, as you grow in your love of astronomy, you will find many celestial bodies fascinating. But the moon may always be our first love because is the one far away space object that has the unique distinction of flying close to the earth and upon which man has walked."
msgstr ""
"만약 밤이 맑다면, 여러분은 달 표면의 놀라운 디테일을 볼 수 있습니다. 별들이 뒷마당을 바라보고 있을 뿐이죠.\n"
"자연적으로, 여러분이 천문학에 대한 사랑에 자라면서, 여러분은 많은 천체들이 매혹적이라는 것을 발견하게 될 거예요. 하지만 달은 항상 우리의 첫사랑일 수 있습니다. 왜냐하면 달은 지구 가까이에서 인간이 걸어온 유일한 우주 물체이기 때문입니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
msgid "In"
msgstr "In"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"In many ways, it is a big step from someone who is just fooling around with "
"astronomy to a serious student of the science. But you and I both know that "
"there is still another big step after buying a telescope before you really "
"know how to use it."
msgstr ""
"여러 면에서, 이것은 천문학으로 장난치고 있는 사람에서 진지한 과학의 학생으로 가는 큰 진전입니다. 하지만 여러분이나 저나 모두 망원경을"
" 산 후에 여러분이 망원경을 정말 어떻게 사용하는지 알기 전에 또 다른 큰 단계가 있다는 것을 알고 있습니다."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
msgid "Is Published"
msgstr "게시 여부"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Islands"
msgstr "섬"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It is great fun to start learning the constellations, how to navigate the "
"night sky and find the planets and the famous stars. There are web sites and"
" books galore to guide you."
msgstr ""
"별자리, 밤하늘을 항해하는 법, 그리고 행성과 유명한 별들을 찾는 법을 배우기 시작하는 것은 아주 재미있어요. 여러분을 안내할 웹사이트와"
" 책 갈로어가 있습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"It is important to choose a hotel that makes you feel comfortable – "
"contemporary or traditional furnishings, local decor or international, "
"formal or relaxed. The ideal hotel directory should let you know of the "
"options available."
msgstr ""
"현대적이든 전통적이든, 지역적인 장식이나 국제적이든, 형식적이든 편안함을 느낄 수 있는 호텔을 선택하는 것이 중요합니다. 이상적인 호텔 "
"디렉토리에서 사용 가능한 옵션을 알 수 있습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"It is safe to say that at some point on our lives, each and every one of us "
"has that moment when we are suddenly stunned when we come face to face with "
"the enormity of the universe that we see in the night sky."
msgstr ""
"우리 삶의 어느 순간, 우리 모두는 밤하늘에서 볼 수 있는 우주의 거대함과 마주쳤을 때 갑자기 기절할 때가 있다고 해도 과언이 아닙니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It really is amazing when you think about it that just by looking up on any "
"given night, you could see virtually hundreds of thousands of stars, star "
"systems, planets, moons, asteroids, comets and maybe a even an occasional "
"space shuttle might wander by. It is even more breathtaking when you realize"
" that the sky you are looking up at is for all intents and purposes the "
"exact same sky that our ancestors hundreds and thousands of years ago "
"enjoyed when they just looked up."
msgstr ""
"생각해보면 정말 놀랍습니다. 어떤 특정한 밤을 올려다보는 것 만으로도, 사실상 수십만 개의 별, 별 시스템, 행성, 달, 소행성, 혜성,"
" 그리고 가끔 우주 왕복선이 떠돌 수도 있다는 것을 볼 수 있습니다. 여러분이 올려다보고 있는 하늘이 모든 면에서 그리고 목적상 우리의 "
"조상들이 방금 올려다 보았을 때 즐겼던 바로 그 하늘이라는 것을 알게 되면 훨씬 더 숨이 막힐 것입니다."

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Jungle"
msgstr "정글"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know what you are looking at"
msgstr "당신이 보고 있는 것을 이해하세요."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know when to look"
msgstr "언제 찾아야 하는지 알고 있습니다."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Large"
msgstr "크게"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "마지막 기여자"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Latest"
msgstr "최근"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_latest_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_latest_blog_posts
msgid "Latest Blog Posts"
msgstr "최신 블로그 게시물"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Learning the background to the great discoveries in astronomy will make your"
" moments star gazing more meaningful. It is one of the oldest sciences on "
"earth so find out the greats of history who have looked at these stars "
"before you."
msgstr ""
"천문학에서의 위대한 발견에 대한 배경을 배우는 것은 스타들이 보는 순간을 더 의미 있게 만들 것입니다. 그것은 지구상에서 가장 오래된 "
"과학 중 하나이기 때문에 여러분 앞에서 이 별들을 본 역사의 위대한 인물들을 찾아보세요."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Leisure Facilities"
msgstr "레저 시설"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "LinkedIn"
msgstr "링크드인"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Local color is great but the hotel’s own restaurants and bars can play an "
"important part in your stay. You should be aware of choice, style and "
"whether or not they are smart or informal. A good hotel report should tell "
"you this, and particularly about breakfast facilities."
msgstr ""
"지역적인 색상은 좋지만, 호텔의 자체적인 식당과 바는 여러분이 머무는 동안 중요한 역할을 할 수 있습니다. 여러분은 선택, 스타일, "
"그리고 그것들이 똑똑한지 아니면 비공식적인지 알아야 합니다. 좋은 호텔 보고서는 특히 아침식사 시설에 대해 알려줄 것입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Location"
msgstr "위치"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_main_attachment_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_main_attachment_id
msgid "Main Attachment"
msgstr "주요 첨부 파일"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Marley"
msgstr "Marley"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
msgid "Maui helicopter tours"
msgstr "마우이 헬리콥터 관광"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to see the island from a different "
"perspective and have a fun adventure. If you have never been on a helicopter"
" before, this is a great place to do it."
msgstr ""
"마우이 헬리콥터 여행은 섬을 다른 시각으로 보고 즐거운 모험을 할 수 있는 좋은 방법입니다. 만약 여러분이 헬리콥터를 타본 적이 없다면,"
" 이곳은 그것을 하기에 좋은 장소입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to tour those places that can not be "
"reached on foot or by car. The tours last approximately one hour and range "
"from approximately one hundred eight five dollars to two hundred forty "
"dollars person. For many, this is a once in a lifetime opportunity to see "
"natural scenery that will not be available again. Taking cameras and videos "
"to capture the moments will also allow you to relive the tour again and "
"again as you reminisce throughout the years."
msgstr ""
"마우이 헬리콥터 여행은 도보나 차로 갈 수 없는 곳을 여행하는 좋은 방법입니다. 투어는 약 1시간 동안 진행되며, 1인당 1008달러에서"
" 240달러까지 다양합니다. 많은 사람들에게 이것은 평생에 한번은 다시 볼 수 없는 자연경관을 볼 수 있는 기회이다. 그 순간을 포착하기"
" 위해 카메라와 비디오를 촬영하는 것은 또한 여러분이 몇 년 동안 추억하면서 이 투어를 반복할 수 있게 해줄 것입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours will allow you to see all of these sights. Make sure "
"to take a camera or video with you when going on Maui helicopter tours to "
"capture the beauty of the scenery and to show friends and family at home all"
" the wonderful things you saw while on vacation."
msgstr ""
"마우이 헬리콥터 투어를 통해 이 모든 광경을 볼 수 있습니다. 마우이 헬리콥터 여행을 갈 때는 반드시 카메라나 비디오를 가지고 가서 "
"경치의 아름다움을 포착하고 휴가 중에 본 모든 멋진 것들을 집에서 친구들과 가족들에게 보여주세요."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Medium"
msgstr "중간"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "메시지"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Description"
msgstr "메타 설명"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Keywords"
msgstr "메타 핵심어"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Title"
msgstr "메타 제목"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Molokai Maui"
msgstr "몰로카이 마우이"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Molokai Maui helicopter tours will take you to a different island but one that is only nine miles away and easily accessible by air. This island has a very small population with a different culture and scenery. The entire coast of the northeast is lined with cliffs and remote beaches. They are completely inaccessible by any other means of transportation than air.\n"
"People who live on the island have never even seen this remarkable scenery unless they have taken Maui helicopter tours to view it. When the weather has been rainy and there is a lot of rainfall for he season you will see many astounding waterfalls."
msgstr ""
"몰로카이 마우이 헬리콥터 여행은 여러분을 다른 섬으로 데려다 줄 것이지만 9마일 밖에 떨어져 있지 않고 공기로 쉽게 접근할 수 있는 곳으로 데려다 줄 것입니다. 이 섬은 문화와 풍경이 다른 매우 적은 인구를 가지고 있습니다. 북동쪽 해안 전체가 절벽과 외딴 해변으로 늘어서 있습니다. 그것들은 공기가 아닌 다른 교통수단으로 완전히 접근할 수 없습니다.\n"
"이 섬에 사는 사람들은 마우이 헬리콥터 여행을 가서 보지 않으면 이 놀라운 광경을 본 적이 없어요. 날씨가 비가 오고 그가 계절에 맞는 많은 강우량이 있을 때, 여러분은 놀라운 폭포를 많이 볼 것입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"More important to the family traveler than the business traveler, you should"
" find out just how child friendly the hotel is from the directory and make "
"your decision from there. One thing worth looking for is whether the hotel "
"offers a baby sitters service. For the business traveler wishing to escape "
"children this is of course very relevant too – perhaps a hotel that is not "
"child friendly would be something more appropriate!"
msgstr ""
"출장 여행자보다 가족 여행자에게 더 중요한 것은, 그 명단에서 호텔이 얼마나 어린이 친화적인지 알아내고 거기서부터 결정을 내려야 한다는 "
"것입니다. 한 가지 찾아볼 만한 것은 호텔이 베이비 시터 서비스를 제공하는지 여부이다. 어린이를 탈출시키고자 하는 비즈니스 여행자들에게 "
"이것은 물론 매우 적절합니다 - 아마도 어린이 친화적이지 않은 호텔이 더 적절할 것입니다!"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_most_viewed_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_most_viewed_blog_posts
msgid "Most Viewed Blog Posts"
msgstr "가장 많이 본 블로그 게시물"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "이름"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "New Blog Post"
msgstr "새 블로그 게시물"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No blog post yet."
msgstr "아직 블로그 게시물이 없습니다."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr "조회수"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No results for \"%s\"."
msgstr "\"%s\"에 대한 결과가 없습니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No results found for '"
msgstr "' 에 대한 결과를 찾을 수 없습니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "No tags defined yet."
msgstr "정의된 태그가 없습니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "None"
msgstr "없음"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"None of this precludes you from moving forward with your plans to put "
"together an awesome telescope system. Just be sure you get quality advice "
"and training on how to configure your telescope to meet your needs. Using "
"these guidelines, you will enjoy hours of enjoyment stargazing at the "
"phenomenal sights in the night sky that are beyond the naked eye."
msgstr ""
"이 중 어떤 것도 여러분이 멋진 망원경 시스템을 만들려는 계획을 추진하는 것을 방해하지 않습니다. 여러분의 필요에 맞게 망원경을 구성하는"
" 방법에 대해 좋은 조언과 훈련을 받는 것을 잊지 마세요. 이 지침을 사용하면, 여러분은 육안으로는 볼 수 없는 밤하늘의 경이로운 "
"광경에서 몇 시간 동안 별빛을 볼 수 있습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Not only knowing the weather will make sure your star gazing is rewarding "
"but if you learn when the big meteor showers and other big astronomy events "
"will happen will make the excitement of astronomy come alive for you."
msgstr ""
"날씨를 아는 것은 여러분의 별을 바라보는 것을 보람있게 할 뿐만 아니라, 여러분이 큰 유성우와 다른 큰 천문학 사건이 언제 일어날지를 "
"알게 된다면 천문학의 흥분이 여러분에게 살아날 것입니다."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "조치가 필요한 메시지 수"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류 메시지 수"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread_counter
msgid "Number of unread messages"
msgstr "읽지 않은 메시지 수"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Of course, to take your moon worship to the ultimate, stepping your "
"equipment up to a good starter telescope will give you the most stunning "
"detail of the lunar surface. With each of these upgrades your knowledge and "
"the depth and scope of what you will be able to see will improve "
"geometrically. For many amateur astronomers, we sometimes cannot get enough "
"of what we can see on this our closest space object."
msgstr ""
"물론, 여러분의 달 예배를 궁극으로 받아들이기 위해서, 여러분의 장비를 좋은 출발 망원경으로 올려놓는 것은 여러분에게 달 표면의 가장 "
"놀라운 디테일을 줄 것입니다. 이러한 각각의 업그레이드를 통해 여러분의 지식과 여러분이 볼 수 있는 것의 깊이와 범위는 기하학적으로 "
"향상될 것입니다. 많은 아마추어 천문학자들에게, 우리는 때때로 우리가 가장 가까운 우주 물체에서 볼 수 있는 것을 충분히 얻을 수 "
"없습니다."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Once you have reviewed the content on mobile, close the preview."
msgstr "모바일에서 내용을 검토한 후 미리 보기를 닫습니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "Others"
msgstr "기타"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_blogs_display
msgid "Our blogs"
msgstr "우리의 블로그"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Anton Repponen, @repponen"
msgstr "사진 : Anton Repponen, @repponen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Photo by Arto Marttinen, @wandervisions"
msgstr "사진 : Arto Marttinen, @wandervisions"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by Boris Smokrovic, @borisworkshop"
msgstr "사진 : Boris Smokrovic, @borisworkshop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Denys Nevozhai, @dnevozhai"
msgstr "사진 : Denys Nevozhai, @dnevozhai"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Greg Rakozy, @grakozy"
msgstr "사진 : Greg Rakozy, @grakozy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Photo by Jason Briscoe, @jbriscoe"
msgstr "사진 : Jason Briscoe, @jbriscoe"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Jon Ly, @jonatron"
msgstr "사진 : Jon Ly, @jonatron"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Patrick Brinksma, @patrickbrinksma"
msgstr "사진 : Patrick Brinksma, @patrickbrinksma"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by PoloX Hernandez, @elpolox"
msgstr "사진 : PoloX Hernandez, @elpolox"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Photo by SpaceX, @spacex"
msgstr "사진 : SpaceX, @spacex"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Photo by Teddy Kelley, @teddykelley"
msgstr "사진 : Teddy Kelley, @teddykelley"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_count
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr "게시물"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "게시 날짜"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Published ("
msgstr "게시됨 ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr "게시한 날"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "게시한 게시물"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr "게시 선택 사항"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr "게시 날짜"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Read more"
msgstr "더 읽기"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Read more <i class=\"fa fa-chevron-right ml-2\"/>"
msgstr "더 읽기 <i class=\"fa fa-chevron-right ml-2\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Restaurants, Cafes and Bars"
msgstr "레스토랑, 카페 및 바"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict publishing to this website."
msgstr "이 웹 사이트로의 게시를 제한합니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "SEO"
msgstr "검색엔진 최적화"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO 최적화"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_big_picture
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_list
msgid "Sample"
msgstr "견본"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Satellites"
msgstr "위성"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Search for an image. (eg: type \"business\")"
msgstr "이미지를 검색합니다. (예: \"비즈니스\" 입력)"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seaside vs mountain side"
msgstr "해변 대 산악지대"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seeing the world from above"
msgstr "위에서 내려다본 세상"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "Select Blog"
msgstr "블로그 선택"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select the blog you want to add the post to."
msgstr "게시물을 추가할 블로그를 선택하십시오."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select this menu item to create a new blog post."
msgstr "새 블로그 게시물을 작성하려면 이 메뉴 항목을 선택하십시오."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__seo_name
msgid "Seo name"
msgstr "Seo 이름"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Separate every keyword with a comma"
msgstr "모든 키워드는 쉼표로 구분하십시오"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Set a blog post <b>cover</b>."
msgstr "블로그 게시물 <b>표지</b>를 설정합니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Several migratory and native birds, mammals and reptiles call Copper Canyon "
"their home. The exquisite fauna in this near-pristine land is also worth "
"checking out."
msgstr ""
"몇몇 철새와 토종새, 포유류 그리고 파충류는 구리 협곡을 그들의 집이라고 부릅니다. 거의 원시인 이 땅의 절묘한 동물도 확인해 볼 "
"만합니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Facebook"
msgstr "페이스북에서 공유하기"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on LinkedIn"
msgstr "링크드인에서 공유하기"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Twitter"
msgstr "트위터에서 공유하기"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share this post"
msgstr "이 게시물 공유하기"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
msgid "Sierra Tarahumara"
msgstr "시에라 타라우마라"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Sierra Tarahumara, popularly known as Copper Canyon is situated in Mexico. "
"The area is a favorite destination among those seeking an adventurous "
"vacation."
msgstr ""
"구리 협곡으로 널리 알려진 시에라 타라후마라는 멕시코에 위치해 있습니다. 그 지역은 모험적인 휴가를 원하는 사람들 사이에서 가장 인기 "
"있는 장소이다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Silly-Chico"
msgstr "Silly-Chico"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Skies"
msgstr "하늘"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So it is critically important that you get just the right telescope for "
"where you are and what your star gazing preferences are. To start with, "
"let’s discuss the three major kinds of telescopes and then lay down some "
"“Telescope 101″ concepts to increase your chances that you will buy the "
"right thing."
msgstr ""
"따라서 여러분이 어디에 있는지, 별을 바라보는 선호가 무엇인지 제대로 된 망원경을 얻는 것이 매우 중요합니다. 우선, 3가지 주요 "
"망원경에 대해 토론한 다음, 여러분이 올바른 것을 살 가능성을 증가시키기 위해 몇 가지 “텔레스코프 101″ 개념을 정하세요."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"So it might be that once a year vacation to a camping spot or a trip to a "
"relative’s house out in the country that we find ourselves outside when the "
"spender of the night sky suddenly decides to put on it’s spectacular show. "
"If you have had that kind of moment when you were literally struck "
"breathless by the spender the night sky can show to us, you can probably "
"remember that exact moment when you could say little else but “wow” at what "
"you saw."
msgstr ""
"그래서 1년에 한 번 캠핑장으로 휴가를 가거나 시골에 있는 친척 집으로 여행을 가는 것은 밤하늘의 소비자가 갑자기 멋진 쇼를 하기로 "
"결정했을 때 우리가 바깥에서 발견하는 것일 수도 있습니다. 만약 여러분이 말 그대로 밤하늘이 우리에게 보여줄 수 있는 소비자에 의해 숨이"
" 막힐 때 그런 순간이 있었다면, 여러분은 아마 여러분이 본 것에 대해 “우와” 말고는 거의 말할 수 없었던 바로 그 순간을 기억할 수 "
"있을 것입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So to select just the right kind of telescope, your objectives in using the "
"telescope are important. To really understand the strengths and weaknesses "
"not only of the lenses and telescope design but also in how the telescope "
"performs in various star gazing situations, it is best to do some homework "
"up front and get exposure to the different kinds. So before you make your "
"first purchase…"
msgstr ""
"그래서 적절한 종류의 망원경을 선택하기 위해서, 망원경을 사용하는 여러분의 목표는 중요합니다. 렌즈와 망원경 설계의 장단점뿐만 아니라 "
"다양한 별을 응시하는 상황에서 망원경이 어떻게 작동하는지 이해하기 위해서, 미리 숙제를 하고 다른 종류에 노출되는 것이 가장 좋습니다. "
"그래서 첫 구매를 하기 전에 말이죠..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"So you’re going abroad, you’ve chosen your destination and now you have to "
"choose a hotel."
msgstr "그래서 당신은 해외로 가고, 목적지를 선택했고, 이제 호텔을 선택해야 합니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#: model_terms:blog.post,content:website_blog.blog_post_3
#: model_terms:blog.post,content:website_blog.blog_post_4
#: model_terms:blog.post,content:website_blog.blog_post_5
#: model_terms:blog.post,content:website_blog.blog_post_6
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Someone famous in <cite title=\"Source Title\">Source Title</cite>"
msgstr "<cite title=\"Source Title\">소스 제목</cite>으로 유명한 사람"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Spotting the fauna"
msgstr "동물 관찰하기"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/models/website_blog.py:0
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Start writing here..."
msgstr "여기에서 글을 시작하십시오 ..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Style"
msgstr "스타일"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr "소제목"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Subtitle"
msgstr "소제목"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr "태그 범주"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr "태그 범주"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr "태그 범주 양식"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "태그 양식"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "태그 목록"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_category_name_uniq
msgid "Tag category already exists !"
msgstr "태그 범주가 이미 존재합니다!"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_name_uniq
msgid "Tag name already exists !"
msgstr "동일한 태그명이 존재합니다!"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags"
msgstr "태그"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Taking pictures in the dark"
msgstr "어두운 곳에서 사진 촬영하기"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
msgid "Teaser"
msgstr "티저"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr "티저 내용"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Ten years ago, you’d have probably visited your local travel agent and "
"trusted the face-to-face advice you were given by the so called ‘experts’. "
"The 21st Century way to select and book your hotel is of course on the "
"Internet, by using travel websites."
msgstr ""
"10년 전에는 현지 여행사를 찾아가 이른바 전문가들로부터 직접 받은 조언을 신뢰했을 것입니다. 여러분의 호텔을 선택하고 예약하는 21세기"
" 방법은 물론 여행 웹사이트를 통해 인터넷에서 이루어집니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"That “Wow” moment is what astrology is all about. For some, that wow moment "
"becomes a passion that leads to a career studying the stars. For a lucky "
"few, that wow moment because an all consuming obsession that leads to them "
"traveling to the stars in the space shuttle or on one of our early space "
"missions. But for most of us astrology may become a pastime or a regular "
"hobby. But we carry that wow moment with us for the rest of our lives and "
"begin looking for ways to look deeper and learn more about the spectacular "
"universe we see in the millions of stars above us each night."
msgstr ""
"그 “우와” 순간은 점성술의 전부이다. 어떤 사람들에게는, 그 와우 순간이 스타들을 연구하는 직업으로 이어지는 열정이 됩니다. 운 "
"좋게도, 그 순간은 우주 왕복선이나 우리의 초기 우주 임무 중 하나를 수행하면서 별들을 여행하게 만드는 모든 것을 소비하는 집착이 있기 "
"때문입니다. 하지만 우리 대부분에게 점성술은 유희이거나 평범한 취미가 될 수 있다. 하지만 우리는 그 와우 순간을 평생 간직하고 "
"있습니다. 그리고 매일 밤 우리 위에 있는 수백만 개의 별에서 볼 수 있는 놀라운 우주에 대해 더 깊이 들여다보고 배울 방법을 찾기 "
"시작합니다."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_5
msgid "The beauty of astronomy is that anybody can do it."
msgstr "천문학의 아름다움은 누구나 할 수 있다는 것입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"The best time to view the moon, obviously, is at night when there are few "
"clouds and the weather is accommodating for a long and lasting study. The "
"first quarter yields the greatest detail of study. And don’t be fooled but "
"the blotting out of part of the moon when it is not in full moon stage. The "
"phenomenon known as “earthshine” gives you the ability to see the darkened "
"part of the moon with some detail as well, even if the moon is only at "
"quarter or half display."
msgstr ""
"달을 보기 가장 좋은 시기는, 분명히, 구름이 거의 없고 날씨가 길고 지속적인 연구에 적합할 때이다. 1/4분기가 가장 상세한 연구 "
"결과를 낳습니다. 그리고 달이 보름달에 있지 않을 때 달의 일부가 깜빡이는 것 말고 속지 마세요. \"지광\"이라고 알려진 이 현상은 "
"달이 1/4 또는 절반만 전시되어 있더라도 달의 어두운 부분을 세부적으로 볼 수 있는 능력을 제공합니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "The best time to view the moon."
msgstr "달을 볼 수 있는 가장 좋은 시간입니다."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr "블로그 게시물이 게시된 것으로 설정된 경우 이 날짜 현재 방문자를 볼 수 있습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The cliffs in this region are among the highest in the world and to see "
"water cascading from the high peaks is simply breathtaking. The short jaunt "
"from Maui with Maui helicopter tours is well worth seeing the beauty of this"
" natural environment."
msgstr ""
"이 지역의 절벽은 세계에서 가장 높은 곳에 있고 높은 봉우리에서 물이 폭포처럼 쏟아지는 것을 보는 것은 정말 숨막힐 정도이다. 마우이 "
"헬리콥터 투어와 함께 하는 짧은 여행은 이 자연 환경의 아름다움을 볼 가치가 충분히 있습니다."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr "웹사이트를 통해 문서에 접근 하는 전체 URL입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"The next thing we naturally want to get is a good telescope. You may have "
"seen a hobbyist who is well along in their study setting up those really "
"cool looking telescopes on a hill somewhere. That excites the amateur "
"astronomer in you because that must be the logical next step in the growth "
"of your hobby. But how to buy a good telescope can be downright confusing "
"and intimidating."
msgstr ""
"다음으로 우리가 자연스레 얻고 싶은 것은 좋은 망원경입니다. 여러분은 아마 연구를 잘 하는 취미가 있는 사람이 어딘가의 언덕에 정말 "
"멋있어 보이는 망원경을 설치하는 것을 본 적이 있을 거예요. 그것은 여러분 안에 있는 아마추어 천문학자를 흥분시킵니다. 왜냐하면 그것이 "
"여러분의 취미가 성장하는 데 있어 논리적인 다음 단계여야 하기 때문입니다. 하지만 좋은 망원경을 사는 방법은 완전히 혼란스럽고 위협적일 "
"수 있습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"The site should offer a detailed analysis of leisure services within the "
"hotel – spa, pool, gym, sauna – as well as details of any other facilities "
"nearby such as golf courses. 7. Special Needs: the hotel directory site "
"should advise the visitor of each hotel’s special needs services and "
"accessibility policy. Whilst again this does not apply to every visitor, it "
"is absolutely vital to some."
msgstr ""
"이 사이트는 호텔 내 레저 서비스 – 온천, 수영장, 체육관, 사우나 – 와 골프장 등 인근 시설에 대한 세부 분석을 제공해야 합니다. "
"7. 특별 요구 사항 : 호텔 디렉토리 사이트는 각 호텔의 특별한 필요 서비스와 접근성 정책을 방문객에게 알려야 합니다. 모든 "
"방문객에게는 적용되지 않지만, 일부 방문객에게는 절대적으로 중요합니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"The tripod or other accessory decisions will change significantly with a "
"telescope that will live on your deck versus one that you plan to take to "
"many remote locations."
msgstr ""
"삼각대나 다른 부속품 결정은 여러분이 많은 외딴 곳에 데려가려고 계획한 망원경과 비교하여 여러분의 갑판에 살 망원경으로 크게 바뀔 "
"것입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The view of this is truly breathtaking and is a sight not to be missed. It "
"is also highly educational with a chance to see a dormant volcano up close, "
"something that can not be seen every day. On the northern and southern sides"
" of the volcano, you will see an incredible different view however. These "
"sides are lush and green and you will be able to see some beautiful "
"waterfalls and gorgeous brush. Tropical rainforests abound on this side of "
"the island and it is something that is not easily accessible by any other "
"means than by air."
msgstr ""
"이것의 경치는 정말 숨막힐 정도로 아름다우며 놓쳐서는 안 될 광경입니다. 휴화산을 가까이서 볼 수 있는 기회도 매우 교육적입니다. 매일 "
"볼 수 없는 것이죠. 하지만 화산의 북쪽과 남쪽에서는 믿을 수 없을 정도로 다른 광경을 볼 수 있습니다. 이 면들은 푸르고 푸르고 "
"여러분은 아름다운 폭포와 멋진 붓을 볼 수 있을 거예요. 열대 우림은 섬의 이쪽에 풍부하고, 그것은 공기가 아닌 다른 어떤 수단으로도 "
"쉽게 접근할 수 없는 것입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Then there’s the problem of the reviewer’s motivation. The more reviews you "
"read, the more you notice how they tend to cluster at the extremes of "
"opinion. On one end, you have angry reviewers with axes to grind; at the "
"other, you have delighted guests who lavish praise beyond belief. You’ll not"
" be surprised to learn that hotels sometimes post their own glowing reviews,"
" or that competitor’s line up for the chance to lambaste the competition "
"with bad reviews. It makes sense to consider what is really important to you"
" when selecting a hotel. You should then choose an online hotel directory "
"that gives up-to-date, independent, impartial information that really "
"matters."
msgstr ""
"그러면 검토자의 동기부여에 문제가 있습니다. 더 많은 리뷰를 읽을수록, 여러분은 그 리뷰들이 어떻게 극단적인 의견으로 군집하는지를 더 "
"많이 알게 됩니다. 한 쪽에는 성난 평론가가 있고, 다른 쪽에는 믿을 수 없을 정도로 칭찬을 아끼지 않는 손님들을 기쁘게 하였습니다. "
"호텔이 때로는 자체적인 열렬한 리뷰를 게시하거나, 경쟁업체들이 나쁜 리뷰로 경쟁 우위를 선점할 기회를 제공한다는 사실을 알게 되면 "
"여러분은 놀라지 않을 것입니다. 호텔을 고를 때 당신에게 정말 중요한 것이 무엇인지 생각해 보는 것이 이치에 맞습니다. 그런 다음 정말로"
" 중요한 최신 독립적이고 공정한 정보를 제공하는 온라인 호텔 디렉토리를 선택해야 합니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"There are other considerations to factor into your final purchase decision."
msgstr "최종 구매 결정 시 고려해야 할 다른 사항이 있습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"There is something timeless about the cosmos. The fact that the planets and "
"the moon and the stars beyond them have been there for ages does something "
"to our sense of our place in the universe. In fact, many of the stars we "
"“see” with our naked eye are actually light that came from that star "
"hundreds of thousands of years ago. That light is just now reaching the "
"earth. So in a very real way, looking up is like time travel."
msgstr ""
"우주에는 시대를 초월한 무언가가 있습니다. 행성, 달, 그리고 그들 너머의 별들이 오랫동안 그곳에 있었다는 사실은 우주에서 우리의 위치에"
" 대한 우리의 감각에 영향을 미칩니다. 사실, 우리가 육안으로 “보는” 많은 별들은 사실 수십만년 전에 그 별에서 온 빛입니다. 그 빛은"
" 지금 막 지구에 도달하고 있습니다. 그래서 실제로 위를 쳐다보는 것은 시간 여행과 같습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"These things really do matter and any decent hotel directory should give you"
" this sort of advice on bedrooms – not just the number of rooms which is the"
" usual option!"
msgstr ""
"이런 것들이 정말 중요하고, 괜찮은 호텔 안내 책자는 여러분에게 침실에 대한 이런 종류의 조언을 해 줄 것입니다. – 단지 일반적인 "
"방들의 숫자만이 아니라요!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "This box will not be visible to your visitors"
msgstr "이 상자는 방문자가 볼 수 없습니다."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "This tag already exists"
msgstr "이 태그는 이미 존재합니다"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Tiny"
msgstr "얇게"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Title"
msgstr "제목"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To gaze at the moon with the naked eye, making yourself familiar with the "
"lunar map will help you pick out the seas, craters and other geographic "
"phenomenon that others have already mapped to make your study more "
"enjoyable. Moon maps can be had from any astronomy shop or online and they "
"are well worth the investment."
msgstr ""
"육안으로 달을 바라보기 위해, 달 지도를 친숙하게 만드는 것은 여러분의 공부를 더 즐겁게 하기 위해 다른 사람들이 이미 매핑한 바다, "
"분화구, 그리고 다른 지리적 현상들을 선택하는 데 도움을 줄 것입니다. 달 지도는 어느 천문학 가게나 온라인에서나 구할 수 있으며 투자할"
" 가치가 충분히 있습니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"To get started in learning how to observe the stars much better, there are "
"some basic things we might need to look deeper, beyond just what we can see "
"with the naked eye and begin to study the stars as well as enjoy them. The "
"first thing you need isn’t equipment at all but literature. A good star map "
"will show you the major constellations, the location of the key stars we use"
" to navigate the sky and the planets that will appear larger than stars. And"
" if you add to that map some well done introductory materials into the hobby"
" of astronomy, you are well on your way."
msgstr ""
"별을 훨씬 더 잘 관찰하는 법을 배우기 시작하기 위해서, 우리가 육안으로 볼 수 있는 것 외에도 별을 즐길 뿐만 아니라 별을 연구하기 "
"시작하는 몇 가지 기본적인 것들이 있습니다. 가장 먼저 필요한 것은 문학을 제외하고는 장비가 아닙니다. 좋은 별자리 지도는 주요 별자리,"
" 우리가 하늘을 항해할 때 사용하는 주요 별들의 위치, 그리고 별들보다 더 크게 나타날 행성들을 보여줄 것입니다. 그리고 이 지도에 "
"천문학의 취미에 잘 만들어진 입문 자료를 더하면, 여러분은 잘 가고 있는 것입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To kick it up a notch, a good pair of binoculars can do wonders for the "
"detail you will see on the lunar surface. For best results, get a good wide "
"field in the binocular settings so you can take in the lunar landscape in "
"all its beauty. And because it is almost impossible to hold the binoculars "
"still for the length of time you will want to gaze at this magnificent body "
"in space, you may want to add to your equipment arsenal a good tripod that "
"you can affix the binoculars to so you can study the moon in comfort and "
"with a stable viewing platform."
msgstr ""
"그것을 한 단계 높이려고, 좋은 쌍안경 한 쌍은 여러분이 달 표면에서 볼 수 있는 세부사항에 놀라운 일을 할 수 있습니다. 최상의 결과를"
" 얻으려면, 쌍안경으로 좋은 넓은 들판을 얻어서 여러분이 달의 아름다운 경치를 볼 수 있도록 하세요. 그리고 이 쌍안경을 우주에서 이 "
"웅장한 몸을 응시하고 싶을 만큼 오랫동안 가만히 있는 것은 거의 불가능하기 때문에, 여러분은 여러분의 장비에 쌍안경을 부착할 수 있는 "
"좋은 삼각대를 추가해서 여러분이 편안하고 안정된 시야의 플랫폼으로 달을 연구할 수 있도록 하고 싶을 것입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To take it to a natural next level, you may want to take advantage of "
"partnerships with other astronomers or by visiting one of the truly great "
"telescopes that have been set up by professionals who have invested in "
"better techniques for eliminating atmospheric interference to see the moon "
"even better. The internet can give you access to the Hubble and many of the "
"huge telescopes that are pointed at the moon all the time. Further, many "
"astronomy clubs are working on ways to combine multiple telescopes, "
"carefully synchronized with computers for the best view of the lunar "
"landscape."
msgstr ""
"자연적인 다음 단계로 나아가기 위해서, 여러분은 다른 천문학자들과의 파트너십을 이용하거나, 달을 더 잘 보기 위해 더 나은 대기 간섭을 "
"제거하는 기술에 투자한 전문가들에 의해 세워진 정말로 훌륭한 망원경 중 하나를 방문함으로써 그것을 이용하고 싶을 지도 모릅니다. 인터넷은"
" 여러분에게 허블 망원경과 달을 항상 가리키는 많은 거대한 망원경에 접근할 수 있게 해 줄 수 있습니다. 게다가, 많은 천문학 클럽들은 "
"달 경관을 가장 잘 볼 수 있도록 컴퓨터와 세심하게 동기화된 여러 망원경을 결합하는 방법을 연구하고 있습니다."

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Travel"
msgstr "여행"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Twitter"
msgstr "트위터"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Unpublished ("
msgstr "게시 안함 ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread
msgid "Unread Messages"
msgstr "읽지 않은 메세지"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread_counter
msgid "Unread Messages Counter"
msgstr "읽지 않은 메세지 수"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "Untitled Post"
msgstr "제목없는 게시물"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr "이 아이콘을 사용하여 <b>모바일 장치</b>의 블로그 게시물을 미리 볼 수 있습니다."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "다음에서 사용 :"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Viewpoints"
msgstr "관점"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Views"
msgstr "화면"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.index
msgid "Visible in all blogs' pages"
msgstr "모든 블로그 페이지에 표시"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr "현재 웹 사이트에 공개"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "웹사이트"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "웹사이트 블로그"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "웹사이트 스니펫 필터"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "웹 사이트 URL"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "웹사이트 메타 설명"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "웹사이트 메타 핵심어"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "웹사이트 메타 제목"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "웹사이트 오픈그래프 이미지"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_5
msgid "What If They Let You Run The Hubble"
msgstr "그들이 허블을 달릴 수 있다면 어떨까요?"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"While anyone can look up and fall in love with the stars at any time, the "
"fun of astronomy is learning how to become more and more skilled and "
"equipped in star gazing that you see and understand more and more each time "
"you look up. Here are some steps you can take to make the moments you can "
"devote to your hobby of astronomy much more enjoyable."
msgstr ""
"누구나 언제라도 올려다보고 별들과 사랑에 빠질 수 있는 반면, 천문학은 여러분이 올려다볼 때마다 점점 더 많이 보고 이해할 수 있는 별을"
" 바라보는 것에 점점 더 능숙해지고 장비를 갖추는 방법을 배우고 있습니다. 여기 여러분이 천문학이라는 취미에 전념할 수 있는 순간들을 "
"훨씬 더 즐겁게 하기 위해 취할 수 있는 몇 가지 단계들이 있습니다."

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "With a View"
msgstr "보기 포함"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "Write a small text here to describe your blog or company."
msgstr "블로그나 회사를 설명하기 위해 여기에 작은 문구를 작성하십시오."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Write a title, the subtitle is optional."
msgstr "제목을 쓰십시오. 소제목은 선택 사항입니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"You should always carefully consider the type of facilities you need from "
"your bedroom and find the hotel that has those you consider important. The "
"hotel directory website should elaborate on matters such as: bed size, "
"Internet Access (its cost, whether there is WIFI or wired broadband "
"connection), Complimentary amenities, views from the room and luxury "
"offerings like a Pillow menu or Bath menu, choice of smoking or non smoking "
"rooms etc."
msgstr ""
"여러분은 항상 여러분이 침실에서 필요로 하는 시설의 종류를 신중히 고려해야 하고 여러분이 중요하게 여기는 호텔들을 찾아야 합니다. 호텔 "
"디렉토리 웹 사이트는 침대 크기, 인터넷 액세스(비용, WIFI 또는 유선 광대역 연결 여부), 무료 편의 시설, 실내에서 보는 뷰 및 "
"베스 메뉴와 같은 고급 서비스, 흡연 또는 비흡연실 선택 등의 문제에 대해 자세히 설명해야 합니다."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"You will see all the beauty that Maui has to offer and can have a great time"
" for the entire family. Tours are not too expensive and last from forty five"
" minutes to over an hour. You can see places that are typically inaccessible"
" with Maui helicopter tours. Places that are not available by foot or "
"vehicle can be seen by air. Breathtaking sights await those who are up for "
"some fun Maui helicopter tours. If you will be staying on the island for a "
"considerable amount of time, you may want to think about doing multiple Maui"
" helicopter tours."
msgstr ""
"여러분은 마우이가 제공하는 모든 아름다움을 보게 될 것이고 온 가족을 위해 즐거운 시간을 보낼 수 있을 거예요. 투어는 너무 비싸지 않고"
" 45분에서 1시간 이상 지속됩니다. 마우이 헬리콥터 투어를 통해 일반적으로 접근할 수 없는 장소를 볼 수 있습니다. 발이나 차량으로 "
"이용할 수 없는 장소는 공기로도 볼 수 있습니다. 즐거운 마우이 헬리콥터 여행을 위해 일어나는 사람들을 위해 숨 쉬는 광경이 기다리고 "
"있습니다. 만약 여러분이 이 섬에 상당 기간 머무르고 있다면, 여러분은 마우이 헬리콥터 여행을 여러 번 하는 것에 대해 생각하고 싶을 "
"지도 모릅니다."

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "adventure"
msgstr "모험"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr "블로그. 블로그에 접근하려면 여기를 클릭하십시오 :"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "breadcrumb"
msgstr "사이트 이동 경로"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "by"
msgstr "작성자"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_5
msgid "discovery"
msgstr "발견"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_3
msgid "guides"
msgstr "안내"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr "다음에 게시되었습니다 "

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "hotels"
msgstr "호텔"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "in"
msgstr "분류"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "in <i class=\"fa fa-folder-open text-muted\"/>"
msgstr "분류 <i class=\"fa fa-folder-open text-muted\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "in <i class=\"fa fa-folder-open text-white-75\"/>"
msgstr "분류 <i class=\"fa fa-folder-open text-white-75\"/>"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_4
msgid "telescopes"
msgstr "망원경"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "to leave a comment"
msgstr "의견을 남기기"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "unpublished"
msgstr "게시 안함"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid ""
"|\n"
"                            <i class=\"fa fa-comment text-muted mr-1\"/>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "| No comments yet"
msgstr "| 아직 댓글이 없습니다"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "⌙ Hover effect"
msgstr ""
