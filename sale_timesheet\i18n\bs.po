# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_timesheet
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>le <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account_invoice.py:32
#: code:addons/sale_timesheet/models/project.py:70
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__billable_type
msgid ""
"* At Project Rate: All timesheets on the project will be billed at the same rate\n"
"* At Employee Rate: Timesheets will be billed at a rate defined at employee level"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "<b>Total</b>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Time Billing</span>"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "<span class=\"o_label\">Overview</span>"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:53
#, python-format
msgid ""
"A billable project should be linked to a Sales Order Item having a Service "
"product."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:55
#, python-format
msgid ""
"A billable project should be linked to a Sales Order Item that does not come"
" from an expense or a vendor bill."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr ""

#. module: sale_timesheet
#: sql_constraint:project.create.sale.order.line:0
#: sql_constraint:project.sale.line.employee.map:0
msgid ""
"An employee cannot be selected more than once in the mapping. Please remove "
"duplicate(s) and try again."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__analytic_account_id
msgid "Analytic Account"
msgstr "Analitički konto"

#. module: sale_timesheet
#: selection:sale.order.line,qty_delivered_method:0
msgid "Analytic From Expenses"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitička stavka"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Retci analitike"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_assign_so_line_view_form
msgid "Assign"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_task_action_multi_assign_so_line
msgid "Assign Sale Order Item"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task_assign_sale
msgid "Assign Sale Order line to tasks"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_assign_so_line_view_form
msgid "Assign sale line to Tasks"
msgstr ""

#. module: sale_timesheet
#: selection:project.create.sale.order,billable_type:0
#: selection:project.project,billable_type:0
#: selection:project.task,billable_type:0
msgid "At Employee Rate"
msgstr ""

#. module: sale_timesheet
#: selection:project.create.sale.order,billable_type:0
msgid "At Project Rate"
msgstr ""

#. module: sale_timesheet
#: selection:project.project,billable_type:0
#: selection:project.task,billable_type:0
msgid "At Task Rate"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:60
#, python-format
msgid "At least one line should be filled."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:249
#, python-format
msgid "Before"
msgstr ""

#. module: sale_timesheet
#: selection:account.analytic.line,timesheet_invoice_type:0
msgid "Billable Fixed"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billable Hours"
msgstr ""

#. module: sale_timesheet
#: selection:account.analytic.line,timesheet_invoice_type:0
msgid "Billable Time"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billable_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__billable_type
msgid "Billable Type"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Billable fixed"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Billable time"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__billable_type
msgid ""
"Billable type implies:\n"
" - At task rate: each time spend on a task is billed at task rate.\n"
" - At employee rate: each employee log time billed at his rate.\n"
" - No Billable: track time without invoicing it"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr "Računi"

#. module: sale_timesheet
#: model:ir.filters,name:sale_timesheet.timesheet_filter_billing
msgid "Billing Rate"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__billable_type
msgid "Billing Type"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Rate"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_assign_so_line_view_form
msgid "Cancel"
msgstr "Otkaži"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Company"
msgstr "Kompanija"

#. module: sale_timesheet
#: model:ir.filters,name:sale_timesheet.ir_filter_project_profitability_report_costs_and_revenues
msgid "Costs and Revenues"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:355
#: code:addons/sale_timesheet/controllers/main.py:362
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
#, python-format
msgid "Create Invoice"
msgstr "Kreriaj Fakturu"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order_line
msgid "Create SO Line from project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order
msgid "Create SO from project"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:102
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
#, python-format
msgid "Create Sales Order"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:344
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
#, python-format
msgid "Create a Sales Order"
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_tracking:0
msgid "Create a new project but no task"
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_tracking:0
msgid "Create a task in a new project"
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_tracking:0
msgid "Create a task in an existing project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task_assign_sale__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task_assign_sale__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task_assign_sale__partner_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Customer"
msgstr "Kupac"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_order_timesheet
#: model:product.template,name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Customer Care (Prepaid Hours)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__partner_id
msgid "Customer of the sales order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task_assign_sale__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: sale_timesheet
#: selection:product.template,service_tracking:0
msgid "Don't create task"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:249
#: model:project.task.type,legend_done:sale_timesheet.project_stage_fixed
#, python-format
msgid "Done"
msgstr "Gotovo"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__employee_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr "Zaposleni"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__employee_id
msgid "Employee that has timesheets on the project."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_search
msgid "Exact name"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
#: model:project.task.type,name:sale_timesheet.project_stage_fixed
msgid "Fixed"
msgstr "Fiksno"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Fixed Price Projects"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__project_id
msgid "Generated Project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__task_id
msgid "Generated Task"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Group By"
msgstr "Grupiši po"

#. module: sale_timesheet
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_timesheet_1
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_timesheet_2
#: model:product.product,uom_name:sale_timesheet.product_service_order_timesheet
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_timesheet_1_product_template
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_timesheet_2_product_template
#: model:product.template,uom_name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Hour(s)"
msgstr "Sat(i)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Hours"
msgstr "Sati"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task_assign_sale__id
msgid "ID"
msgstr "ID"

#. module: sale_timesheet
#: model:project.task,legend_normal:sale_timesheet.project_task_internal
#: model:project.task.type,legend_normal:sale_timesheet.project_stage_fixed
msgid "In Progress"
msgstr "U Toku"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_invoice
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
msgid "Invoice"
msgstr "Faktura"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_invoice_line
msgid "Invoice Line"
msgstr "Stavka fakture"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Invoiced"
msgstr "Fakturisano"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:404
#, python-format
msgid "Invoices"
msgstr "Fakture"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr "Fakturisanje"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__is_service
msgid "Is a Service"
msgstr ""

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_timesheet_2
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_2_product_template
msgid "Junior Architect (Invoice on Timesheets)"
msgstr ""

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_manual
#: model:product.template,name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Kitchen Assembly (Milestones)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task_assign_sale____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task_assign_sale__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task_assign_sale__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__line_ids
msgid "Lines"
msgstr "Retci"

#. module: sale_timesheet
#: selection:sale.order.line,qty_delivered_method:0
msgid "Manual"
msgstr "Ručno"

#. module: sale_timesheet
#: selection:product.template,service_type:0
msgid "Manually set quantities on order"
msgstr "Ručno postavi količine na narudžbu"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_policy:0
msgid "Milestones (manually set quantities on order)"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "My Project"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:249
#, python-format
msgid "Name"
msgstr "Naziv:"

#. module: sale_timesheet
#: model:project.task.type,legend_blocked:sale_timesheet.project_stage_fixed
msgid "Need functional or technical help"
msgstr "Potrebna funkcionalna ili tehnička pomoć"

#. module: sale_timesheet
#: selection:project.project,billable_type:0
#: selection:project.task,billable_type:0
msgid "No Billable"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:208
#, python-format
msgid "No Sales Order"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:170
#: code:addons/sale_timesheet/controllers/main.py:178
#: code:addons/sale_timesheet/controllers/main.py:190
#, python-format
msgid "No Sales Order Line"
msgstr ""

#. module: sale_timesheet
#: selection:account.analytic.line,timesheet_invoice_type:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "No task found"
msgstr ""

#. module: sale_timesheet
#: selection:account.analytic.line,timesheet_invoice_type:0
msgid "Non Billable"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non Billable Hours"
msgstr ""

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/js/timesheet_plan.js:266
#, python-format
msgid "Non Billable Tasks"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Non billable"
msgstr ""

#. module: sale_timesheet
#: model:project.task,legend_blocked:sale_timesheet.project_task_internal
msgid "Not validated"
msgstr "Nije validirano"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_invoice__timesheet_count
msgid "Number of timesheets"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or "
"task. From those, you can track the service you are selling."
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_policy:0
msgid "Ordered quantities"
msgstr "Naručene količine"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_cost
msgid "Other Cost"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Other costs"
msgstr ""

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/js/timesheet_plan.js:29
#: model:ir.actions.client,name:sale_timesheet.project_timesheet_action_client_timesheet_plan
#, python-format
msgid "Overview"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__product_id
msgid "Product"
msgstr "Proizvod"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: sale_timesheet
#: model:ir.filters,name:sale_timesheet.ir_filter_project_profitability_report_manager_and_product
msgid "Product by Customer"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__product_id
msgid ""
"Product of the sales order item. Must be a service invoiced based on "
"timesheets on tasks."
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_fixed
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_milestone
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_time_based
msgid "Products"
msgstr "Proizvodi"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Profitability"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_graph
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Profitability Analysis"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:373
#: code:addons/sale_timesheet/controllers/main.py:424
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
#, python-format
msgid "Project"
msgstr "Projekat"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__company_id
msgid "Project Company"
msgstr ""

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_profitability_report_action
#: model:ir.ui.menu,name:sale_timesheet.menu_project_profitability_analysis
msgid "Project Costs and Revenues"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__currency_id
msgid "Project Currency"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__user_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Project Manager"
msgstr "Rukovodilac projekta"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_profitability_report
msgid "Project Profitability Report"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__project_id
msgid "Project for which we are creating a sales order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__project_id
msgid "Project generated by the sales order item"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "Project(s) Overview"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:101
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__project_ids
#, python-format
msgid "Projects"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Rates"
msgstr "Stope"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Re-invoiced costs"
msgstr ""

#. module: sale_timesheet
#: model:project.task,legend_done:sale_timesheet.project_task_internal
msgid "Ready for Next Stage"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:249
#, python-format
msgid "Remaining"
msgstr "Preostalo"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__sale_order_id
msgid "Sale Order"
msgstr "Prodajni nalog"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
msgid "Sale Order Item"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__sale_line_id
msgid "Sale Order Line"
msgstr "Stavka prodajnog naloga"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__sale_line_employee_ids
msgid "Sale line/Employee map"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_id
msgid ""
"Sale order line from which the project has been created. Used for "
"tracability."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task_assign_sale__sale_line_id
msgid "Sale order line to link to selected tasks"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_sale_service_inherit_form2
msgid "Sales Order"
msgstr "Prodajna narudžba"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__order_confirmation_date
msgid "Sales Order Confirmation Date"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_line_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task_assign_sale__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_sale_service_inherit_form2
msgid "Sales Order Item"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "Stavka prodajne narudžbe"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__is_service
msgid ""
"Sales Order item should generate a task and/or a project, depending on the "
"product settings."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:395
#, python-format
msgid "Sales Orders"
msgstr "Prodajni nalozi"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_template__project_id
msgid ""
"Select a non billable project on which tasks can be created. This setting "
"must be set for each company."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_template__project_template_id
msgid ""
"Select a non billable project to be the skeleton of the new created project "
"when selling the current product. Its stages and tasks will be duplicated."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task_assign_sale__task_ids
msgid ""
"Select the tasks to assign to the Sale Order Items. You can only choose task"
" (no sub tasks)."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid ""
"Sell services (e.g. project, maintenance), record time spent using "
"Timesheets app and invoice them based on a fixed price (ordered quantity) or"
" on the time spent (delivered quantity)"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr ""

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_timesheet_1
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_1_product_template
msgid "Senior Architect (Invoice on Timesheets)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__product_id
msgid "Service"
msgstr "Usluga"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_tracking
msgid "Service Tracking"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Setup your fixed price services"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Setup your milestone services"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Setup your time-based services"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:249
#, python-format
msgid "Sold"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Statistics"
msgstr "Statistika"

#. module: sale_timesheet
#: selection:sale.order.line,qty_delivered_method:0
msgid "Stock Moves"
msgstr "Kretanje zaliha"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
msgid "Task"
msgstr "Zadatak"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:177
#, python-format
msgid ""
"Task Created (%s): <a href=# data-oe-model=project.task data-oe-id=%d>%s</a>"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__task_id
msgid "Task generated by the sales order item"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:385
#: code:addons/sale_timesheet/controllers/main.py:447
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task_assign_sale__task_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
#: model:project.project,label_tasks:sale_timesheet.project_support
#, python-format
msgid "Tasks"
msgstr "Zadaci"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr ""

#. module: sale_timesheet
#: sql_constraint:project.project:0
msgid ""
"The Project should be linked to a Sale Order to select an Sale Order Items."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:67
#, python-format
msgid ""
"The Sales Order cannot be created because you did not enter some employees that entered timesheets on this project. Please list all the relevant employees before creating the Sales Order.\n"
"Missing employee(s): %s"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:68
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:64
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:66
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:25
#, python-format
msgid "The project is already billable."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:55
#, python-format
msgid "The project is already linked to a sales order item."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:72
#, python-format
msgid ""
"The sales order cannot be created because some timesheets of this project "
"are already linked to another sales order."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:51
#, python-format
msgid "The selected Sales Order should contain something to invoice."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "There is no timesheet for now."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.project_profitability_report_action
msgid ""
"This report allows you to analyse the profitability of your projects: "
"compare the amount to invoice, the ones already invoiced and the project "
"cost (via timesheet cost of your employees)."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:270
#, python-format
msgid ""
"This task has been created from: <a href=# data-oe-model=sale.order data-oe-"
"id=%d>%s</a> (%s)"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account.py:55
#, python-format
msgid ""
"This timesheet line cannot be billed: there is no Sale Order Item defined on"
" the task, nor on the project. Please define one to save your timesheet "
"line."
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Time by people"
msgstr ""

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/js/timesheet_plan.js:171
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_pivot_revenue
#, python-format
msgid "Timesheet"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__timesheet_cost
msgid "Timesheet Cost"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__timesheet_unit_amount
msgid "Timesheet Unit Amount"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_count
msgid "Timesheet activities"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_ids
msgid "Timesheet activities associated to this sale"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "Timesheet costs"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:379
#: code:addons/sale_timesheet/controllers/main.py:436
#: code:addons/sale_timesheet/models/account_invoice.py:26
#: model:ir.model.fields,field_description:sale_timesheet.field_account_invoice__timesheet_ids
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
#: selection:sale.order.line,qty_delivered_method:0
#, python-format
msgid "Timesheets"
msgstr "Vremenski listovi"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:64
#, python-format
msgid "Timesheets of %s"
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_type:0
msgid "Timesheets on project (one fare per SO/Project)"
msgstr ""

#. module: sale_timesheet
#: selection:product.template,service_policy:0
msgid "Timesheets on tasks"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_plan
msgid "To invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid ""
"Total amount to invoice on the sales order, including all items (services, "
"storables, expenses, ...)"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr "Usluga praćenja"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__price_unit
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__price_unit
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "Jedinična cijena"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__price_unit
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__price_unit
msgid "Unit price of the sales order item."
msgstr ""

#. module: sale_timesheet
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_manual
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Unit(s)"
msgstr "kom (komad)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__amount_untaxed_invoiced
msgid "Untaxed Amount Invoiced"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__amount_untaxed_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_amount_untaxed_to_invoice
msgid "Untaxed Amount to Re-invoiced"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_amount_untaxed_invoiced
msgid "Untaxed Re-invoiced Amount"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/main.py:258
#, python-format
msgid ""
"What is still to deliver based on sold hours and hours already done. Equals "
"to sold hours - done hours."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__wizard_id
msgid "Wizard"
msgstr "Čarobnjak"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task_assign_sale__partner_id
msgid "You can find a customer by its Name, TIN, Email or Internal Reference."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account.py:62
#, python-format
msgid ""
"You can not modify already invoiced timesheets (linked to a Sales order "
"items invoiced on Time and material)."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:18
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:19
#, python-format
msgid "You can only apply this action from a project."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:208
#, python-format
msgid ""
"You cannot link the order item %s - %s to this task because it is a re-"
"invoiced expense."
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:221
#, python-format
msgid ""
"You have to unlink the task from the sale order item in order to delete it."
msgstr ""

#. module: sale_timesheet
#: model:product.product,weight_uom_name:sale_timesheet.product_service_deliver_manual
#: model:product.product,weight_uom_name:sale_timesheet.product_service_deliver_timesheet_1
#: model:product.product,weight_uom_name:sale_timesheet.product_service_deliver_timesheet_2
#: model:product.product,weight_uom_name:sale_timesheet.product_service_order_timesheet
#: model:product.template,weight_uom_name:sale_timesheet.product_service_deliver_manual_product_template
#: model:product.template,weight_uom_name:sale_timesheet.product_service_deliver_timesheet_1_product_template
#: model:product.template,weight_uom_name:sale_timesheet.product_service_deliver_timesheet_2_product_template
#: model:product.template,weight_uom_name:sale_timesheet.product_service_order_timesheet_product_template
msgid "kg"
msgstr "kg"
