<?xml version="1.0"?>
<odoo>

    <!-- EVENTS TRACK STAGES-->
    <record id="event_track_stage_view_search" model="ir.ui.view">
        <field name="name">event.track.stage.view.search</field>
        <field name="model">event.track.stage</field>
        <field name="arch" type="xml">
            <search string="Track Stage">
               <field name="name" string="Track Stages"/>
            </search>
        </field>
    </record>

    <record id="event_track_stage_view_form" model="ir.ui.view">
        <field name="name">event.track.stage.view.form</field>
        <field name="model">event.track.stage</field>
        <field name="arch" type="xml">
            <form string="Track Stage">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="mail_template_id"/>
                            <field name="is_visible_in_agenda" attrs="{'readonly': [('is_cancel', '=', True)]}"/>
                            <field name="is_fully_accessible" attrs="{'readonly': [('is_cancel', '=', True)]}"/>
                            <field name="is_cancel"/>
                        </group>
                        <group>
                            <field name="fold"/>
                            <field name="color" widget="color_picker"/>
                        </group>
                    </group>
                    <group string="Stage Description and Tooltips">
                        <p class="text-muted" colspan="2">
                            Define labels explaining kanban state management.
                        </p>
                        <label for="legend_normal" string=" " class="o_status"
                            title="Task in progress. Click to block or set as done."
                            aria-label="Task in progress. Click to block or set as done." role="img"/>
                        <field name="legend_normal" nolabel="1"/>
                        <label for="legend_blocked" string=" " class="o_status o_status_red"
                            title="Task is blocked. Click to unblock or set as done."
                            aria-label="Task is blocked. Click to unblock or set as done." role="img"/>
                        <field name="legend_blocked" nolabel="1"/>
                        <label for="legend_done" string=" " class="o_status o_status_green"
                            title="This step is done. Click to block or set in progress."
                            aria-label="This step is done. Click to block or set in progress." role="img"/>
                        <field name="legend_done" nolabel="1"/>
                        <p class="text-muted" colspan="2">
                            Add a description to help your coworkers understand the meaning and purpose of the stage.
                        </p>
                        <field name="description" placeholder="Add a description..." nolabel="1" colspan="2"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="event_track_stage_view_tree" model="ir.ui.view">
        <field name="name">event.track.stage.view.tree</field>
        <field name="model">event.track.stage</field>
        <field name="arch" type="xml">
            <tree string="Track Stage">
                <field name="sequence" widget="handle" groups="event.group_event_manager"/>
                <field name="name"/>
                <field name="is_visible_in_agenda"/>
                <field name="is_fully_accessible"/>
                <field name="is_cancel"/>
                <field name="fold"/>
            </tree>
        </field>
    </record>

    <record id="view_event_track_stage_kanban" model="ir.ui.view">
        <field name="name">event.track.stage.kanban</field>
        <field name="model">event.track.stage</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="fold"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div>
                                <strong class="o_kanban_record_title"><field name="name"/></strong>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="event_track_stage_action" model="ir.actions.act_window">
        <field name="name">Track Stages</field>
        <field name="res_model">event.track.stage</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="view_id" ref="event_track_stage_view_tree"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Add a new stage in the task pipeline
          </p><p>
            Define the steps that will be used in the event from the
            creation of the track, up to the closing of the track.
            You will use these stages in order to track the progress in
            solving an event track.
          </p>
        </field>
    </record>

</odoo>
