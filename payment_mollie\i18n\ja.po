# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_mollie
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_mollie
#: model_terms:ir.ui.view,arch_db:payment_mollie.payment_acquirer_form
msgid "API Key"
msgstr "APIキー"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Canceled payment with status: %s"
msgstr "以下ステイタスの取消済支払: %s"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "APIと接続できませんでした。"

#. module: payment_mollie
#: model:account.payment.method,name:payment_mollie.payment_method_mollie
#: model:ir.model.fields.selection,name:payment_mollie.selection__payment_acquirer__provider__mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_acquirer__mollie_api_key
msgid "Mollie API Key"
msgstr "Mollie API キー"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "参照に一致する取引が見つかりません%s。"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "決済サービス"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_account_payment_method
msgid "Payment Methods"
msgstr "支払方法"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_transaction
msgid "Payment Transaction"
msgstr "決済トランザクション"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_acquirer__provider
msgid "Provider"
msgstr "プロバイダ"

#. module: payment_mollie
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "無効な支払ステータスのデータを受信しました: %s"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_acquirer__mollie_api_key
msgid ""
"The Test or Live API Key depending on the configuration of the acquirer"
msgstr ""
