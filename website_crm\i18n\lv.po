# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_crm
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <a<PERSON><PERSON>@allegro.lv>, 2022
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: website_crm
#: model:ir.model.fields,field_description:website_crm.field_website_visitor__lead_count
msgid "# Leads"
msgstr ""

#. module: website_crm
#: model:ir.model.fields,field_description:website_crm.field_crm_lead__visitor_page_count
msgid "# Page Views"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Contact Form</span>"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid ""
"<span class=\"s_website_form_label_content\">Email</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.contactus_form
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "Add a contact form in the"
msgstr ""

#. module: website_crm
#. openerp-web
#: code:addons/website_crm/static/src/js/website_crm_editor.js:0
#, python-format
msgid "Assign leads/opportunities to a sales team."
msgstr ""

#. module: website_crm
#. openerp-web
#: code:addons/website_crm/static/src/js/website_crm_editor.js:0
#, python-format
msgid "Assign leads/opportunities to a salesperson."
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "Communication"
msgstr "Saziņa"

#. module: website_crm
#: model:ir.model,name:website_crm.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurācijas iestatījumi"

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "Contact Us"
msgstr "Kontakti"

#. module: website_crm
#: model:ir.model.fields,field_description:website_crm.field_res_config_settings__crm_default_team_id
msgid "Default Sales Team"
msgstr ""

#. module: website_crm
#: model:ir.model.fields,help:website_crm.field_res_config_settings__crm_default_team_id
#: model:ir.model.fields,help:website_crm.field_website__crm_default_team_id
msgid "Default Sales Team for new leads created through the Contact Us form."
msgstr ""

#. module: website_crm
#: model:ir.model.fields,field_description:website_crm.field_website__crm_default_team_id
msgid "Default Sales Teams"
msgstr ""

#. module: website_crm
#: model:ir.model.fields,field_description:website_crm.field_res_config_settings__crm_default_user_id
#: model:ir.model.fields,field_description:website_crm.field_website__crm_default_user_id
msgid "Default Salesperson"
msgstr ""

#. module: website_crm
#: model:ir.model.fields,help:website_crm.field_res_config_settings__crm_default_user_id
#: model:ir.model.fields,help:website_crm.field_website__crm_default_user_id
msgid "Default salesperson for new leads created through the Contact Us form."
msgstr ""

#. module: website_crm
#: model:ir.model,name:website_crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Lead/Opportunity"

#. module: website_crm
#: model:ir.actions.act_window,name:website_crm.website_visitor_crm_lead_action
#: model:ir.model.fields,field_description:website_crm.field_website_visitor__lead_ids
#: model_terms:ir.ui.view,arch_db:website_crm.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_crm.website_visitor_view_search
msgid "Leads"
msgstr "Pavedieni"

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.website_visitor_view_kanban
msgid "Leads/Opportunities"
msgstr "Leads/Opportunities"

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "New messages are managed as leads or opportunities in your CRM app."
msgstr ""

#. module: website_crm
#: model_terms:ir.actions.act_window,help:website_crm.website_visitor_crm_lead_action
msgid "No lead linked for this visitor"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.crm_lead_view_form
msgid "Page views"
msgstr ""

#. module: website_crm
#. openerp-web
#: code:addons/website_crm/static/src/js/website_crm_editor.js:0
#, python-format
msgid "Phone Number"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid ""
"Please set a Sales Team for the website. Otherwise you can't follow related "
"events."
msgstr ""

#. module: website_crm
#. openerp-web
#: code:addons/website_crm/static/src/js/website_crm_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
#, python-format
msgid "Sales Team"
msgstr "Sales Team"

#. module: website_crm
#. openerp-web
#: code:addons/website_crm/static/src/js/website_crm_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
#, python-format
msgid "Salesperson"
msgstr "Pārdevējs"

#. module: website_crm
#. openerp-web
#: code:addons/website_crm/static/src/js/website_crm_editor.js:0
#, python-format
msgid "Subject"
msgstr "Priekšmets"

#. module: website_crm
#: model:ir.model.fields,field_description:website_crm.field_crm_lead__visitor_ids
msgid "Web Visitors"
msgstr ""

#. module: website_crm
#: model:ir.model,name:website_crm.model_website
msgid "Website"
msgstr "Tīkla vietne"

#. module: website_crm
#: model:ir.actions.act_url,name:website_crm.action_open_website
msgid "Website Contact Form"
msgstr ""

#. module: website_crm
#: model:ir.model,name:website_crm.model_website_visitor
msgid "Website Visitor"
msgstr ""

#. module: website_crm
#. openerp-web
#: code:addons/website_crm/static/src/js/website_crm_editor.js:0
#, python-format
msgid "Your Company"
msgstr ""

#. module: website_crm
#. openerp-web
#: code:addons/website_crm/static/src/js/website_crm_editor.js:0
#, python-format
msgid "Your Email"
msgstr "Jūsu e-pasts"

#. module: website_crm
#. openerp-web
#: code:addons/website_crm/static/src/js/website_crm_editor.js:0
#, python-format
msgid "Your Name"
msgstr "Jūsu vārds un uzvārds"

#. module: website_crm
#. openerp-web
#: code:addons/website_crm/static/src/js/website_crm_editor.js:0
#, python-format
msgid "Your Question"
msgstr ""

#. module: website_crm
#: model_terms:ir.ui.view,arch_db:website_crm.res_config_settings_view_form
msgid "page"
msgstr ""
