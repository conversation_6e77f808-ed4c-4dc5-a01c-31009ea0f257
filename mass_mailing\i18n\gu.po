# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * mass_mailing
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: Spellbound Soft Solutions <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "$18"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "$20"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
msgid ""
"% <br/>\n"
"                                                <strong>Clicks</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
msgid ""
"%<br/>\n"
"                                                <strong>Replied</strong>"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:606
#, python-format
msgid "%s (copy)"
msgstr "%s  (નકલ)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_tag_line
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_text_social
msgid "&amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-compass fa-2x text-delta\" role=\"img\" aria-label=\"Choose\" title=\"Choose\"/>\n"
"                                        &amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-credit-card fa-2x text-delta\" role=\"img\" aria-label=\"Order\" title=\"Order\"/>\n"
"                                        &amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-smile-o fa-2x text-delta\" role=\"img\" aria-label=\"Enjoy\" title=\"Enjoy\"/>\n"
"                                        &amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "20%"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "21 Jul"
msgstr ""

#. module: mass_mailing
#: model_terms:mail.mass_mailing.list,popup_content:mass_mailing.mass_mail_list_1
#: model_terms:mail.mass_mailing.list,popup_content:mass_mailing.mass_mail_list_2
msgid ""
"<font>7</font>\n"
"    <strong>Business Hacks</strong>\n"
"    <span> to<br>boost your marketing</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "<i class=\"fa fa-angle-double-down\"/> Footers"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "<i class=\"fa fa-angle-double-up\"/> Headers"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "<i class=\"fa fa-clone\"/> Body"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "<i class=\"fa fa-eyedropper\"/>Background Color"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "<i class=\"fa fa-plus\"/> Marketing Content"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid ""
"<small>\n"
"                                    <strong>Michael Fletcher</strong><br/>\n"
"                                    <small>Community Manager</small>\n"
"                                </small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "<small>Step 1:</small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "<small>Step 2:</small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "<small>Step 3:</small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<small>user / month (billed annually)</small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
msgid ""
"<span class=\"fa fa-copyright\" role=\"img\" aria-label=\"Copyright\" "
"title=\"Copyright\"/> 2016 All Rights Reserved"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid ""
"<span style=\"line-height: 30px;\"><small>CODE: </small></span><strong "
"class=\"o_code h3\">45A9E77DGW8455</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_form
msgid "<span> times</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>24/7 Support</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>Advanced</strong> features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>Fully customizable</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
msgid "<strong>Opened</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_form
msgid "<strong>This email could not be sent.</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>Total</strong> management"
msgstr ""

#. module: mass_mailing
#: sql_constraint:mail.mass_mailing.list_contact_rel:0
msgid "A contact cannot be subscribed multiple times to the same list!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_three_cols
msgid "A short description"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_text_image
msgid "A unique value"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__contact_ab_pc
msgid "A/B Testing percentage"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "ALL DAY"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_needaction
msgid "Action Needed"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__active
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__active
msgid "Active"
msgstr "સક્રિય"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mass_mailing_tag_action
msgid "Add a new tag"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__unique_ab_testing
msgid "Allow A/B Testing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__show_blacklist_buttons
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Allow the recipient to manage himself his state in the blacklist via the "
"unsubscription page."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Allow the recipient to manage himself his state in the blacklist via the "
"unsubscription page.                                 If the option is "
"active, the 'Blacklist Me' button is hidden on the unsubscription page."
"                                   The 'come Back' button will always be "
"visible in any case to allow leads and partners to re-subscribe."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:34
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:39
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:110
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:119
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:142
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:151
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:174
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:179
#, python-format
msgid "An error occured. Please try again later or contact us."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:79
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:84
#, python-format
msgid "An error occurred. Your changes have not been saved, try again later."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_tag_line
msgid "Apps That Help You Grow Your Business"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_sub
msgid "Apps That Help You Grow Your Business!"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge__archive_src_lists
msgid "Archive source mailing lists"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Archived"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Attach a file"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__attachment_ids
msgid "Attachments"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "Basic features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "Basic management"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__is_blacklisted
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__is_blacklisted
#: model:ir.ui.menu,name:mass_mailing.mail_blacklist_mm_menu
msgid "Blacklist"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "Blacklist Me"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__body_html
msgid "Body"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__bounced
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__bounced
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__bounced
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_bounce
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__message_bounce
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__bounced
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: selection:mail.mail.statistics,state:0
msgid "Bounced"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__bounced_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__bounced_ratio
msgid "Bounced Ratio"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Campaign"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__name
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Campaign Name"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_view_mass_mailing_stages
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Campaign Stages"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mass_mailing_tag_action
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_tag_menu
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_tag_view_form
msgid "Campaign Tags"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_email_campaigns
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_search
msgid "Campaigns"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_list_merge_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Cancel"
msgstr "રદ કરો"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/xml/mass_mailing.xml:5
#, python-format
msgid "Change Style"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_sub
msgid "Check this out!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "Choose"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid "Choose your mailing subscriptions"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__clicked
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__clicked
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__clicked
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
msgid "Clicked"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Clicks"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__color
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__color
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag__color
msgid "Color Index"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
msgid "Colour"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_three_cols
msgid "Column Title"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_two_cols
msgid "Column title"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "Come Back"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_test__email_to
msgid "Comma-separated list of email addresses."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__company_name
msgid "Company Name"
msgstr "કંપનીનું નામ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Company name"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_configuration
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_configuration
msgid "Configuration"
msgstr "રુપરેખાંકન"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__contact_id
msgid "Contact"
msgstr "સંપર્ક"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_form_simplified
msgid "Contact List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Copyright &amp;copy;"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact__message_bounce
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_list_contact_rel__message_bounce
msgid "Counter of the number of bounced emails for this contact."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__country_id
msgid "Country"
msgstr "દેશ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_form_simplified
msgid "Create"
msgstr "બનાવો"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Create a"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.open_create_mass_mailing_list
msgid "Create a Mass Mailing List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_campaigns
msgid ""
"Create a campaign to structure mailing and get analysis from email status."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid "Create a contact in your address book"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_ab_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid "Create a new mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_campaigns
msgid "Create a new mailing campaign"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid "Create a new mailing list"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_stages
msgid "Create a new mass mailing stage"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts_from_list
msgid "Create a new recipient"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_schedule_date__create_uid
msgid "Created by"
msgstr "બનાવનાર"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_schedule_date__create_date
msgid "Created on"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_list_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
msgid "Creation Date"
msgstr "સર્જન તારીખ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_search
msgid "Creation Period"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid ""
"Cyber-threats continue to increase.<br/>\n"
"                                The discussion will examine how to develop new norms and integrate them into EU"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "Cybersecurity"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "DEFAULT"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics__exception
msgid "Date of technical error leading to the email not being sent"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics__clicked
msgid "Date when customer clicked on at least one tracked link"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics__scheduled
msgid "Date when the email has been created"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics__ignored
msgid ""
"Date when the email has been invalidated. Invalid emails are blacklisted, "
"opted-out or invalid email format"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics__opened
msgid "Date when the email has been opened the first time"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics__sent
msgid "Date when the email has been sent"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics__replied
msgid "Date when this email has been replied for the first time."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics__bounced
msgid "Date when this email has bounced."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_kanban
msgid "Delete"
msgstr "કાઢી નાંખો"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__delivered
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__delivered
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__delivered
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delivered"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "Demo Signature"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing.stage,name:mass_mailing.campaign_stage_2
msgid "Design"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge__dest_list_id
msgid "Destination Mailing List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_schedule_date_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_form_simplified
msgid "Discard"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_schedule_date__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__res_id
msgid "Document ID"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__model
msgid "Document model"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__mailing_domain
msgid "Domain"
msgstr "શરતી અવકાશ"

#. module: mass_mailing
#: selection:mail.mass_mailing,state:0
#: selection:mail.statistics.report,state:0
msgid "Draft"
msgstr "ડ્રાફ્ટ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_kanban
msgid "Dropdown menu"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Duplicate"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_kanban
msgid "Edit"
msgstr "ફેરફાર કરો"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__email
msgid "Email"
msgstr "ઈ-મેઈલ"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Email Marketing"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mail_statistics
msgid "Email Statistics"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_thread
msgid "Email Thread"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mail_mail_statistics_mailing_list
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Emails Sent"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__statistics_ids
msgid "Emails Statistics"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "Enjoy!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "Enjoy,"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__exception
#: selection:mail.mail.statistics,state:0
msgid "Exception"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_list_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
msgid "Exclude Blacklisted"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_list_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
msgid "Exclude Bounced"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_list_contact_view_search
msgid "Exclude Invalid Emails"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_list_contact_view_search
msgid "Exclude Opt Out"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__expected
msgid "Expected"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Extended Filters..."
msgstr "વિસ્તૃત ગાળકો ..."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "FROM YOUR NEXT ORDER!"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__failed
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__failed
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
msgid "Failed"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:153
#, python-format
msgid "Feedback from %s: %s"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,name:mass_mailing.mass_mail_1
#: model:utm.source,name:mass_mailing.mass_mail_1_utm_source
msgid "First Newsletter"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_follower_ids
msgid "Followers"
msgstr "અનુયાયીઓ"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__email_from
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__email_from
msgid "From"
msgstr "તરફથી"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image_text
msgid ""
"Get your inside sales (CRM) fully integrated with online sales (eCommerce), "
"in-store sales (Point of Sale) and marketplaces like eBay and Amazon."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_list_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Group By"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Group By..."
msgstr "ગ્રુપ દ્વારા..."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage__id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag__id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test__id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__id
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge__id
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_schedule_date__id
msgid "ID"
msgstr "ઓળખ"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics__mail_mail_id_int
msgid ""
"ID of the related mail_mail. This field is an integer field because the "
"related mail_mail can be deleted separately from its statistics. However the"
" ID is needed for several action and controllers."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact__message_unread
msgid "If checked new messages require your attention."
msgstr "જો ચેક કરેલા નવા સંદેશા માટે તમારું ધ્યાન આવશ્યક છે"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact__message_needaction
msgid "If checked, new messages require your attention."
msgstr "જો ચેક કરેલું હોય, તો નવા સંદેશા માટે તમારું ધ્યાન આવશ્યક છે."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_campaign__unique_ab_testing
msgid ""
"If checked, recipients will be mailed only once for the whole campaign. This"
" lets you send different mailings to randomly selected recipients and test "
"the effectiveness of the mailings, without causing duplicate messages."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "જો ચેક કરેલું હોય, તો કેટલાક મેસેજીસમાં ડિલિવરીની ભૂલ છે."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact__is_blacklisted
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_list_contact_rel__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__ignored
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__ignored
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__ignored
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: selection:mail.mail.statistics,state:0
msgid "Ignored"
msgstr ""

#. module: mass_mailing
#: selection:mail.mass_mailing,state:0
msgid "In Queue"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__is_email_valid
msgid "Is Email Valid"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__is_public
msgid "Is Public"
msgstr ""

#. module: mass_mailing
#: model_terms:mail.mass_mailing.list,popup_content:mass_mailing.mass_mail_list_1
#: model_terms:mail.mass_mailing.list,popup_content:mass_mailing.mass_mail_list_2
msgid ""
"Join our Marketing newsletter and get <strong>this white paper "
"instantly</strong>"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__keep_archives
msgid "Keep Archives"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "LOGIN"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_schedule_date____last_update
msgid "Last Modified on"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
msgid "Last State Update"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_schedule_date__write_uid
msgid "Last Updated by"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_schedule_date__write_date
msgid "Last Updated on"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics__state_update
msgid "Last state update of the mail"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker
msgid "Link Tracker"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker_click
msgid "Link Tracker Click"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__links_click_ids
msgid "Links click"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__mail_mail_id
msgid "Mail"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Mail Body"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__mail_mail_id_int
msgid "Mail ID (tech)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__mail_server_id
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_mail_server_id
msgid "Mail Server"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mail_mail_statistics
#: model:ir.actions.act_window,name:mass_mailing.action_view_mail_mail_statistics_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mail_stat_id
#: model:ir.ui.menu,name:mass_mailing.menu_email_statistics
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_tree
msgid "Mail Statistics"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test__mass_mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_graph
msgid "Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Mailing Campaign"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_campaigns
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_tree
msgid "Mailing Campaigns"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_list
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mailing_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__list_id
#: model:ir.model.fields,field_description:mass_mailing.field_survey_mail_compose_message__mailing_list_ids
msgid "Mailing List"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_contacts
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_contacts
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_list_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_tree
msgid "Mailing List Contacts"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_list_contact_rel_view_form
msgid "Mailing List contacts"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_lists
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__contact_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__contact_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge__src_list_ids
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_mailing_list_menu
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_lists
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_list_contact_rel_list_contact_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_tree
msgid "Mailing Lists"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__user_id
msgid "Mailing Manager"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_stages
msgid "Mailing Stages"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_links_tree
msgid "Mailing Statistics of Clicks"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "Mailing Subscriptions"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mail_mass_mailing_test
msgid "Mailing Test"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_create_ab_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.action_create_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailings
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailings_from_campaign
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__total_mailings
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_stage_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_stage_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Mailings"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailings that are assigned to me"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_mass_mailing_campaign
msgid "Manage Mass Mailing Campaigns"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Manage campaigns of mass emails with process stages"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing.tag,name:mass_mailing.mass_mail_tag_1
msgid "Marketing"
msgstr "પ્રચાર"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__name
msgid "Mass Mail"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__campaign
msgid "Mass Mail Campaign"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_schedule_date__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_survey_mail_compose_message__mass_mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
msgid "Mass Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mail_statistics_report
msgid "Mass Mailing Analysis"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_campaign
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker__mass_mailing_campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mass_mailing_campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__mass_mailing_campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__mass_mailing_campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_survey_mail_compose_message__mass_mailing_campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Mass Mailing Campaign"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_stage
msgid "Mass Mailing Campaign Stage"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid "Mass Mailing Campaigns"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_contact
msgid "Mass Mailing Contact"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_name
#: model:ir.model.fields,field_description:mass_mailing.field_survey_mail_compose_message__mass_mailing_name
msgid "Mass Mailing Name"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mass_mailing_schedule_date
msgid "Mass Mailing Scheduling"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_statistics_report
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Mass Mailing Statistics"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_mail_statistics_report
msgid ""
"Mass Mailing Statistics allows you to check different mailing related "
"information like number of bounced mails, opened mails, replied mails. You "
"can sort out your analysis by different groups to get accurate grained "
"analysis."
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_list_contact_rel
msgid "Mass Mailing Subscription Information"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_tag
msgid "Mass Mailing Tag"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.server,name:mass_mailing.ir_cron_mass_mailing_queue_ir_actions_server
#: model:ir.cron,cron_name:mass_mailing.ir_cron_mass_mailing_queue
#: model:ir.cron,name:mass_mailing.ir_cron_mass_mailing_queue
msgid "Mass Mailing: Process queue"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__mass_mailing_ids
msgid "Mass Mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__medium_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__medium_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Medium"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_list_merge_view_form
msgid "Merge"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mass_mailing_list_merge
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_list_merge_view_form
msgid "Merge Mass Mailing List"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge__merge_options
msgid "Merge Option"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mass_mailing_list_merge_action
msgid "Merge Selected Mailing Lists"
msgstr ""

#. module: mass_mailing
#: selection:mass.mailing.list.merge,merge_options:0
msgid "Merge into a new mailing list"
msgstr ""

#. module: mass_mailing
#: selection:mass.mailing.list.merge,merge_options:0
msgid "Merge into an existing mailing list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__message_id
msgid "Message-ID"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_ids
msgid "Messages"
msgstr "સંદેશાઓ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "More"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "More Info"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_tag_line
msgid "My Account"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_text_social
msgid "My Company"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "My Mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage__name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag__name
msgid "Name"
msgstr "નામ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
msgid "Name / Email"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_list_merge__new_list_name
msgid "New Mailing List Name"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing.campaign,name:mass_mailing.mass_mail_campaign_1
#: model:utm.campaign,name:mass_mailing.mass_mail_campaign_1_utm_campaign
msgid "Newsletter"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "No customization"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_ab_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid ""
"No need of importing your mailing lists, you can easily\n"
"                send emails to any contact saved in other Odoo apps."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "No support"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__clicks_ratio
msgid "Number of Clicks"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__contact_nbr
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__contact_count
msgid "Number of Contacts"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_list_merge_view_form
msgid "Number of Recipients"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_kanban
msgid "Number of bounced email."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__clicks_ratio
msgid "Number of clicks"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_has_error_counter
msgid "Number of error"
msgstr "ભૂલની સંખ્યા"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "સંદેશાઓની સંખ્યા જે ક્રિયા માટે જરૂરી છે"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "વિતરણ ભૂલ સાથે સંદેશાઓની સંખ્યા"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact__message_unread_counter
msgid "Number of unread messages"
msgstr "ન વાંચેલા સંદેશાની સંખ્યા"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "OFF"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "OFF YOUR NEXT ORDER!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Odoo"
msgstr ""

#. module: mass_mailing
#: model_terms:mail.mass_mailing.list,popup_content:mass_mailing.mass_mail_list_1
#: model_terms:mail.mass_mailing.list,popup_content:mass_mailing.mass_mail_list_2
msgid "Odoo Presents"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid "Odoo helps you to easily track all activities related to a customer."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image_text
msgid "Omnichannel sales"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_list_contact_rel_list_contact_view_tree
msgid "Open Contact"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
msgid "Open Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__opened
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__opened
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__opened
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__opened
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
#: selection:mail.mail.statistics,state:0
msgid "Opened"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__opened_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__opened_ratio
msgid "Opened Ratio"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__opt_out
msgid "Opt Out"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Options"
msgstr "વિકલ્પો"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "Order"
msgstr ""

#. module: mass_mailing
#: selection:mail.mail.statistics,state:0
msgid "Outgoing"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mail
msgid "Outgoing Mails"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "PRO"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing__contact_ab_pc
msgid ""
"Percentage of the contacts that will be mailed. Recipients will be taken "
"randomly."
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/wizard/mass_mailing_schedule_date.py:19
#, python-format
msgid "Please select a date equal/or greater than the current date."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing__reply_to
msgid "Preferred Reply-To Address"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_text_image
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_three_cols
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_two_cols
msgid "Read More..."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Received"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__received_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__received_ratio
msgid "Received Ratio"
msgstr ""

#. module: mass_mailing
#: selection:mail.mass_mailing,reply_to_mode:0
msgid "Recipient Followers"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__email
msgid "Recipient email address"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_contacts_from_list
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test__email_to
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_form
msgid "Recipients"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__mailing_model_id
msgid "Recipients Model"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__mailing_model_name
msgid "Recipients Model Name"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__mailing_model_real
msgid "Recipients Real Model"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "Redeem Discount!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "Registration"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Related Mailing(s)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Related Mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__replied
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__replied
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__replied
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__replied
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
#: selection:mail.mail.statistics,state:0
msgid "Replied"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__replied_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__replied_ratio
msgid "Replied Ratio"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
msgid "Reply Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__reply_to
msgid "Reply To"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__reply_to_mode
msgid "Reply-To Mode"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_report
msgid "Reporting"
msgstr "અહેવાલીકરણ"

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:68
#, python-format
msgid "Requested blacklisting via unsubscribe link."
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:127
#, python-format
msgid "Requested blacklisting via unsubscription page."
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:139
#, python-format
msgid "Requested de-blacklisting via unsubscription page."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__user_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_search
msgid "Responsible"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Retry"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_test
msgid "Sample Mail Wizard"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_schedule_date_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model:mail.mass_mailing.stage,name:mass_mailing.campaign_stage_1
msgid "Schedule"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__schedule_date
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_schedule_date__schedule_date
msgid "Schedule in the Future"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__scheduled
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__scheduled
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__scheduled
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
msgid "Scheduled"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__scheduled_date
msgid "Scheduled Date"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Scheduled Period"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__next_departure
msgid "Scheduled date"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Select a template"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists..."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists:"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid "Send"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Send Now"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send Sample Mail"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a Sample Mail"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid ""
"Send a sample of this mailing to the above of email addresses for test "
"purpose."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Send new A/B Testing Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Send new Mailing"
msgstr ""

#. module: mass_mailing
#: selection:mail.mass_mailing,state:0
msgid "Sending"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__sent
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__sent
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__sent
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: selection:mail.mail.statistics,state:0 selection:mail.mass_mailing,state:0
#: model:mail.mass_mailing.stage,name:mass_mailing.campaign_stage_3
#: selection:mail.statistics.report,state:0
msgid "Sent"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Sent By"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__sent_date
msgid "Sent Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__sent
msgid "Sent Emails"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent Period"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage__sequence
msgid "Sequence"
msgstr "ક્રમ"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mass_mailing_configuration
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_global_settings
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
msgid "Settings"
msgstr "સુયોજનો"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__show_blacklist_buttons
msgid "Show blacklist buttons on unsubscribe page"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__source_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Source"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__name
msgid "Source Name"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
msgid "Specific Mail Server"
msgstr ""

#. module: mass_mailing
#: selection:mail.mass_mailing,reply_to_mode:0
msgid "Specified Email Address"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__stage_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_search
msgid "Stage"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__state
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "State"
msgstr "અવસ્થા"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics__state_update
msgid "State Update"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__statistics_ids
msgid "Statistics"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_links_statistics
#: model:ir.actions.act_window,name:mass_mailing.dropdb snipp
msgid "Statistics of Clicks"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__state
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report__state
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Status"
msgstr "સ્થિતિ"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__source_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Subject"
msgstr "વિષય"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__subscription_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list__subscription_contact_ids
msgid "Subscription Information"
msgstr ""

#. module: mass_mailing
#: sql_constraint:mail.mass_mailing.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__tag_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__tag_ids
msgid "Tags"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_schedule_date_view_form
msgid "Take Future Schedule Date"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Test"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:657
#, python-format
msgid "Test Mailing"
msgstr ""

#. module: mass_mailing
#: selection:mail.statistics.report,state:0
msgid "Tested"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "Thank you for joining us!"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:169
#, python-format
msgid "Thank you! Your feedback has been sent successfully!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
msgid "That way, Odoo evolves much faster than any other solution."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_list_contact_rel__opt_out
msgid "The contact has chosen not to receive mails anymore from this list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_list__is_public
msgid ""
"The mailing list can be accessible by recipient in the unsubscription page "
"to allows him to update his subscription preferences."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of developers and\n"
"                                business experts to build hundreds of apps in just a few years."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_text_image
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of "
"developers and business experts to build hundreds of apps in just a few "
"years."
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:624
#, python-format
msgid "The recipient <strong>subscribed to %s</strong> mailing list(s)"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:623
#, python-format
msgid "The recipient <strong>unsubscribed from %s</strong> mailing list(s)"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:823
#, python-format
msgid "There is no recipients selected."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing__medium_id
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_campaign__medium_id
msgid "This is the delivery method, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing__source_id
msgid ""
"This is the link source, e.g. Search Engine, another domain, or name of "
"email list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_campaign__source_id
msgid ""
"This is the link source, e.g. Search Engine, another domain,or name of email"
" list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid ""
"This is useful if your marketing campaigns are composed of several emails"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"This is useful if your marketing campaigns are composed of several emails."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "This mailing is scheduled for"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing__campaign_id
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_campaign__campaign_id
msgid ""
"This name helps you tracking your different campaign efforts, e.g. "
"Fall_Drive, Christmas_Special"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"This tool is advised if your marketing campaign is composed of several "
"emails."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"This will send the email to all recipients. Do you still want to proceed ?"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__title_id
msgid "Title"
msgstr "શીર્ષક"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing__total
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__total
msgid "Total"
msgstr "કુલ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Tracking"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_unread
msgid "Unread Messages"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_basic_template
msgid "Unsubscribe"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_rel__unsubscription_date
msgid "Unsubscription Date"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/xml/mass_mailing.xml:17
#, python-format
msgid "Upgrade theme"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
msgid ""
"Usability improvements made on Odoo will automatically apply to all\n"
"                                of our fully integrated apps."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "Use This Promo Code BEFORE 1st of August"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Use a specific mail server for mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing__mail_server_id
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Use a specific mail server in priority. Otherwise Odoo relies on the first "
"outgoing mail server available (based on their sequencing) as it does for "
"normal mails."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "Use now"
msgstr ""

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_mass_mailing_user
msgid "User"
msgstr "વપરાશકર્તા"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mail_mass_mailing_list_contact_view_search
msgid "Valid Recipients"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid ""
"We are continuing to grow and we miss seeing you be a part of it! We've "
"increased store hours and have lot's of new brands available. To welcome you"
" back please accept this 20% discount on you next purchase by clicking the "
"button."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid ""
"We want to take this opportunity to welcome you to our ever-growing "
"community!<br/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid ""
"We would appreciate if you provide feedback about why you updated<br/>your "
"subscriptions"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mass_mailing_schedule_date_action
msgid "When do you want to send your mailing?"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
msgid ""
"With strong technical foundations, Odoo's framework is unique.\n"
"                                It provides top notch usability that scales across all apps."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image
msgid ""
"With strong technical foundations, Odoo's framework is unique. It provides "
"<strong>top notch usability that scales across all apps</strong>."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_two_cols
msgid ""
"Write one paragraph describing your product,\n"
"                                    services or a specific feature. To be successful\n"
"                                    your content needs to be useful to your readers."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:71
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:98
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:130
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:165
#, python-format
msgid "You are not authorized to do this!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid "You are not subscribed to any of our mailing list."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:49
#, python-format
msgid "You have been <strong>successfully unsubscribed from </strong>."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:52
#, python-format
msgid "You have been <strong>successfully unsubscribed</strong>."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:104
#, python-format
msgid ""
"You have been successfully <strong>added to our blacklist</strong>. You will"
" not be contacted anymore by our services."
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:136
#, python-format
msgid ""
"You have been successfully <strong>removed from our blacklist</strong>. You "
"are now able to be contacted by our services."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "You have been successfully <strong>unsubscribed</strong>!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_banner
msgid "Your Banner Image"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_logo
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_default_template
msgid "Your Logo"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_text_image
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_three_cols
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_two_cols
msgid "Your Picture"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:75
#, python-format
msgid "Your changes have been saved."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid ""
"Your platform is ready for work. It will help you reduce the costs of "
"digital signage, attract new customers and increase sales."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "and save $20 on your next order!"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign__campaign_id
msgid "campaign_id"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_form_simplified
msgid "e.g. Consumer Newsletter"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_form
msgid "e.g. John Smith"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "email(s) are in queue and will be sent soon."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "email(s) could not be sent."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "email(s) have been ignored and will not be sent."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "free website"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "with"
msgstr ""
