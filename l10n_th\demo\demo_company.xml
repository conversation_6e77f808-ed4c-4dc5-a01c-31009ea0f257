<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_th" model="res.partner">
        <field name="name">TH Company</field>
        <field name="vat"></field>
        <field name="street">A5</field>
        <field name="city">บ้านสันทราย</field>
        <field name="country_id" ref="base.th"/>
        
        <field name="zip">50220</field>
        <field name="phone">+66 81 234 5678</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.thexample.com</field>
    </record>

    <record id="demo_company_th" model="res.company">
        <field name="name">TH Company</field>
        <field name="partner_id" ref="partner_demo_company_th"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_th')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_th.demo_company_th'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_th.chart')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_th.demo_company_th')"/>
    </function>
</odoo>
