<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="chart_at_template_transfer_288" model="account.account.template">
            <field name="name">Schwebende Geldbewegungen</field>
            <field name="code">2880</field>
            <field name="reconcile" eval="True"/>
            <field name="user_type_id" ref="account.data_account_type_current_assets" />
        </record>
        <!-- Vorlage: Kontenp<PERSON>re<PERSON> (Basis: EKR 2000) -->
        <record id="l10n_at_chart_template" model="account.chart.template">
            <field name="name">Einheitskontenrahmen Österreich 2010</field>
            <field name="code_digits">4</field>
            <field name="bank_account_code_prefix">280</field>
            <field name="cash_account_code_prefix">270</field>
            <field name="transfer_account_code_prefix">288</field>
            <field name="currency_id" ref="base.EUR"/>
            <field name="country_id" ref="base.at"/>
        </record>
        <record id="chart_at_template_transfer_288" model="account.account.template">
            <field name="chart_template_id" ref="l10n_at_chart_template"/>
        </record>

        <record id="chart_at_template_0010" model="account.account.template">
          <field name="name">Aufwendungen für das Ingangsetzen und Erweitern eines Betriebes</field>
          <field name="code">0010</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAI1')])]" />
        </record>
        <record id="chart_at_template_0090" model="account.account.template">
          <field name="name">Kumulierte Abschreibungen</field>
          <field name="code">0090</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
        </record>
        <record id="chart_at_template_0100" model="account.account.template">
          <field name="name">Konzessionen</field>
          <field name="code">0100</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAI1')])]" />
        </record>
        <record id="chart_at_template_0110" model="account.account.template">
          <field name="name">Patentrechte und Lizenzen</field>
          <field name="code">0110</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAI1')])]" />
        </record>
        <record id="chart_at_template_0120" model="account.account.template">
          <field name="name">Datenverarbeitungsprogramme</field>
          <field name="code">0120</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAI1')])]" />
        </record>
        <record id="chart_at_template_0121" model="account.account.template">
          <field name="name">Geringwertige Datenverarbeitungsprogramme</field>
          <field name="code">0121</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAI1')])]" />
        </record>
        <record id="chart_at_template_0130" model="account.account.template">
          <field name="name">Marken, Warenzeichen und Musterschutzrechte, sonstige Urheberrechte</field>
          <field name="code">0130</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAI1')])]" />
        </record>
        <record id="chart_at_template_0140" model="account.account.template">
          <field name="name">Pacht- und Mietrechte</field>
          <field name="code">0140</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAI1')])]" />
        </record>
        <record id="chart_at_template_0150" model="account.account.template">
          <field name="name">Geschäfts(Firmen)wert</field>
          <field name="code">0150</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAI2')])]" />
        </record>
        <record id="chart_at_template_0180" model="account.account.template">
          <field name="name">Geleistete Anzahlungen</field>
          <field name="code">0180</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAI3')])]" />
        </record>
        <record id="chart_at_template_019" model="account.account.template">
          <field name="name">Kumulierte Abschreibungen</field>
          <field name="code">0190</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
        </record>
        <record id="chart_at_template_0200" model="account.account.template">
          <field name="name">Unbebaute Grundstücke</field>
          <field name="code">0200</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_0210" model="account.account.template">
          <field name="name">Bebaute Grundstücke (Grundwert)</field>
          <field name="code">0210</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_022" model="account.account.template">
          <field name="name">Grundstücksgleiche Rechte</field>
          <field name="code">0220</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_0300" model="account.account.template">
          <field name="name">Betriebs- und Geschäftsgebäude auf eigenem Grund</field>
          <field name="code">0300</field>
          <field name="note">KZ 9320 (Bilanzierer gemäß §§ 4 Abs. 1 oder 5)</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_0310" model="account.account.template">
          <field name="name">Wohn- und Sozialgebäude auf eigenem Grund</field>
          <field name="code">0310</field>
          <field name="note">KZ 9320 (Bilanzierer gemäß §§ 4 Abs. 1 oder 5)</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_0320" model="account.account.template">
          <field name="name">Betriebs- und Geschäftsgebäude auf fremdem Grund</field>
          <field name="code">0320</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_0330" model="account.account.template">
          <field name="name">Wohn- und Sozialgebäude auf fremdem Grund</field>
          <field name="code">0330</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_0340" model="account.account.template">
          <field name="name">Grundstückseinrichtungen auf eigenem Grund</field>
          <field name="code">0340</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_0350" model="account.account.template">
          <field name="name">Grundstückseinrichtungen auf fremdem Grund</field>
          <field name="code">0350</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_0360" model="account.account.template">
          <field name="name">Bauliche Investitionen in fremden (gepachteten) Betriebs- und Geschäftsgebäuden</field>
          <field name="code">0360</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_0370" model="account.account.template">
          <field name="name">Bauliche Investitionen in fremden (gepachteten) Wohn- und Sozialgebäuden</field>
          <field name="code">0370</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_0390" model="account.account.template">
          <field name="name">Kumulierte Abschreibungen</field>
          <field name="code">0390</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII1')])]" />
        </record>
        <record id="chart_at_template_0400" model="account.account.template">
          <field name="name">Fertigungsmaschinen</field>
          <field name="code">0400</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII2')])]" />
        </record>
        <record id="chart_at_template_0410" model="account.account.template">
          <field name="name">Antriebsmaschinen</field>
          <field name="code">0410</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII2')])]" />
        </record>
        <record id="chart_at_template_0420" model="account.account.template">
          <field name="name">Energieversorgungsanlagen</field>
          <field name="code">0420</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII2')])]" />
        </record>
        <record id="chart_at_template_0430" model="account.account.template">
          <field name="name">Transportanlagen</field>
          <field name="code">0430</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII2')])]" />
        </record>
        <record id="chart_at_template_0500" model="account.account.template">
          <field name="name">Maschinenwerkzeuge</field>
          <field name="code">0500</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII2')])]" />
        </record>
        <record id="chart_at_template_0510" model="account.account.template">
          <field name="name">Allgemeine Werkzeuge und Handwerkzeuge</field>
          <field name="code">0510</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII2')])]" />
        </record>
        <record id="chart_at_template_0520" model="account.account.template">
          <field name="name">Vorrichtungen, Formen und Modelle</field>
          <field name="code">0520</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII2')])]" />
        </record>
        <record id="chart_at_template_0530" model="account.account.template">
          <field name="name">Andere Erzeugungshilfsmittel</field>
          <field name="code">0530</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII2')])]" />
        </record>
        <record id="chart_at_template_0540" model="account.account.template">
          <field name="name">Hebezeuge und Montageanlagen</field>
          <field name="code">0540</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII2')])]" />
        </record>
        <record id="chart_at_template_0550" model="account.account.template">
          <field name="name">Geringwertige Vermögensgegenstände, soweit im Erzeugungsprozeß verwendet</field>
          <field name="code">0550</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII2')])]" />
        </record>
        <record id="chart_at_template_0600" model="account.account.template">
          <field name="name">Beheizungs- und Beleuchtungsanlagen</field>
          <field name="code">0600</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0610" model="account.account.template">
          <field name="name">Nachrichten- und Kontrollanlagen</field>
          <field name="code">0610</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0620" model="account.account.template">
          <field name="name">Büromaschinen, EDV-Anlagen</field>
          <field name="code">0620</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0630" model="account.account.template">
          <field name="name">PKW</field>
          <field name="code">0630</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0640" model="account.account.template">
          <field name="name">LKW</field>
          <field name="code">0640</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0650" model="account.account.template">
          <field name="name">Andere Beförderungsmittel</field>
          <field name="code">0650</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0660" model="account.account.template">
          <field name="name">Andere Betriebs- und Geschäftsausstattung</field>
          <field name="code">0660</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0670" model="account.account.template">
          <field name="name">Gebinde</field>
          <field name="code">0670</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0680" model="account.account.template">
          <field name="name">Geringwertige Büromaschinen, EDV-Anlagen</field>
          <field name="code">0680</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0681" model="account.account.template">
          <field name="name">Geringwertige Betriebs- und Geschäftsausstattung</field>
          <field name="code">0681</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0692" model="account.account.template">
          <field name="name">Kumulierte Abschreibungen zu Büromaschinen, EDV-Anlagen</field>
          <field name="code">0692</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0693" model="account.account.template">
          <field name="name">Kumulierte Abschreibungen zu PKW und Kombis</field>
          <field name="code">0693</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0694" model="account.account.template">
          <field name="name">Kumulierte Abschreibungen zu LKW</field>
          <field name="code">0694</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0696" model="account.account.template">
          <field name="name">Kumulierte Abschreibungen zur Betriebs- und Geschäftsausstattung</field>
          <field name="code">0696</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII3')])]" />
        </record>
        <record id="chart_at_template_0700" model="account.account.template">
          <field name="name">Geleistete Anzahlungen</field>
          <field name="code">0700</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_prepayments" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII4')])]" />
        </record>
        <record id="chart_at_template_0710" model="account.account.template">
          <field name="name">Anlagen in Bau</field>
          <field name="code">0710</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII4')])]" />
        </record>
        <record id="chart_at_template_0790" model="account.account.template">
          <field name="name">Kumulierte Abschreibungen</field>
          <field name="code">0790</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_fixed_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAII4')])]" />
        </record>
        <record id="chart_at_template_0800" model="account.account.template">
          <field name="name">Anteile an verbundenen Unternehmen</field>
          <field name="code">0800</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII1')])]" />
        </record>
        <record id="chart_at_template_0810" model="account.account.template">
          <field name="name">Beteiligungen an Gemeinschaftsunternehmen</field>
          <field name="code">0810</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII1')])]" />
        </record>
        <record id="chart_at_template_0820" model="account.account.template">
          <field name="name">Beteiligungen an angeschlossenen (assoziierten) Unternehmen</field>
          <field name="code">0820</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII1')])]" />
        </record>
        <record id="chart_at_template_0830" model="account.account.template">
          <field name="name">Sonstige Beteiligungen</field>
          <field name="code">0830</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII1')])]" />
        </record>
        <record id="chart_at_template_0840" model="account.account.template">
          <field name="name">Ausleihungen an verbundene Unternehmen</field>
          <field name="code">0840</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII2')])]" />
        </record>
        <record id="chart_at_template_0850" model="account.account.template">
          <field name="name">Ausleihungen an Unternehmen, mit denen ein Beteiligungsverhältnis besteht</field>
          <field name="code">0850</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII4')])]" />
        </record>
        <record id="chart_at_template_0860" model="account.account.template">
          <field name="name">Sonstige Ausleihungen</field>
          <field name="code">0860</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII6')])]" />
        </record>
        <record id="chart_at_template_0870" model="account.account.template">
          <field name="name">Anteile an Kapitalgesellschaften ohne Beteiligungscharakter</field>
          <field name="code">0870</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII5')])]" />
        </record>
        <record id="chart_at_template_0880" model="account.account.template">
          <field name="name">Anteile an Personengesellschaften ohne Beteiligungscharakter</field>
          <field name="code">0880</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII5')])]" />
        </record>
        <record id="chart_at_template_0900" model="account.account.template">
          <field name="name">Genossenschaftsanteile ohne Beteiligungscharakter</field>
          <field name="code">0900</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII5')])]" />
        </record>
        <record id="chart_at_template_0910" model="account.account.template">
          <field name="name">Anteile an Investmentfonds</field>
          <field name="code">0910</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII5')])]" />
        </record>
        <record id="chart_at_template_0980" model="account.account.template">
          <field name="name">Geleistete Anzahlungen</field>
          <field name="code">0980</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_prepayments" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII5')])]" />
        </record>
        <record id="chart_at_template_0990" model="account.account.template">
          <field name="name">Kumulierte Abschreibungen</field>
          <field name="code">0990</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AAIII5')])]" />
        </record>
        <record id="chart_at_template_1600" model="account.account.template">
          <field name="name">Handelswaren</field>
          <field name="code">1600</field>
          <field name="note">KZ 9340 (Bilanzierer gemäß §§ 4 Abs. 1 oder 5)</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABI3')])]" />
        </record>
        <record id="chart_at_template_1800" model="account.account.template">
          <field name="name">Geleistete Anzahlungen</field>
          <field name="code">1800</field>
          <field name="note">KZ 9340 (Bilanzierer gemäß §§ 4 Abs. 1 oder 5)</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_prepayments" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABI5')])]" />
        </record>
        <record id="chart_at_template_2000" model="account.account.template">
          <field name="name">Forderungen von Partnern im Inland ohne eigenes Debitorenkonto</field>
          <field name="code">2000</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_receivable" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII1')])]" />
        </record>
        <record id="chart_at_template_2099" model="account.account.template">
          <field name="name">Forderungen von Partnern (Point Of Sale)</field>
          <field name="code">2099</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_receivable" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII1')])]" />
        </record>
        <record id="chart_at_template_2080" model="account.account.template">
          <field name="name">Einzelwertberichtigungen zu Forderungen aus Lieferungen und Leistungen Inland</field>
          <field name="code">2080</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII1')])]" />
        </record>
        <record id="chart_at_template_2090" model="account.account.template">
          <field name="name">Pauschalwertberichtigungen zu Forderungen aus Lieferungen und Leistungen Inland</field>
          <field name="code">2090</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII1')])]" />
        </record>
        <record id="chart_at_template_2100" model="account.account.template">
          <field name="name">Forderungen von Partnern im EU Raum ohne eigenes Debitorenkonto</field>
          <field name="code">2100</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_receivable" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII1')])]" />
        </record>
        <record id="chart_at_template_2130" model="account.account.template">
          <field name="name">Einzelwertberichtigungen zu Forderungen aus Lieferungen und Leistungen Währungsunion</field>
          <field name="code">2130</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII1')])]" />
        </record>
        <record id="chart_at_template_2140" model="account.account.template">
          <field name="name">Pauschalwertberichtigungen zu Forderungen aus Lieferungen und Leistungen Währungsunion</field>
          <field name="code">2140</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII1')])]" />
        </record>
        <record id="chart_at_template_2150" model="account.account.template">
          <field name="name">Forderungen von Partnern in Drittstaaten ohne eigenes Debitorenkonto</field>
          <field name="code">2150</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_receivable" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII1')])]" />
        </record>
        <record id="chart_at_template_2180" model="account.account.template">
          <field name="name">Einzelwertberichtigungen zu Forderungen aus Lieferungen und Leistungen sonstiges Ausland</field>
          <field name="code">2180</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII1')])]" />
        </record>
        <record id="chart_at_template_2190" model="account.account.template">
          <field name="name">Pauschalwertberichtigungen zu Forderungen aus Lieferungen und Leistungen sonstiges Ausland</field>
          <field name="code">2190</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII1')])]" />
        </record>
        <record id="chart_at_template_2230" model="account.account.template">
          <field name="name">Einzelwertberichtigungen zu Forderungen gegenüber verbundenen Unternehmen</field>
          <field name="code">2230</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII2')])]" />
        </record>
        <record id="chart_at_template_2240" model="account.account.template">
          <field name="name">Pauschalwertberichtigungen zu Forderungen gegenüber verbundenen Unternehmen</field>
          <field name="code">2240</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII2')])]" />
        </record>
        <record id="chart_at_template_2280" model="account.account.template">
          <field name="name">Einzelwertberichtigungen zu Forderungen gegenüber Unternehmen, mit denen ein Beteiligungsverhältnis besteht</field>
          <field name="code">2280</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII3')])]" />
        </record>
        <record id="chart_at_template_2290" model="account.account.template">
          <field name="name">Pauschalwertberichtigungen zu Forderungen gegenüber Unternehmen, mit denen ein Beteiligungsverhältnis besteht</field>
          <field name="code">2290</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII3')])]" />
        </record>
        <record id="chart_at_template_2470" model="account.account.template">
          <field name="name">Eingeforderte, aber noch nicht eingezahlte Einlagen</field>
          <field name="code">2470</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII3')])]" />
        </record>
        <record id="chart_at_template_2480" model="account.account.template">
          <field name="name">Einzelwertberichtigungen zu sonstigen Forderungen und Vermögensgegenständen</field>
          <field name="code">2480</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2490" model="account.account.template">
          <field name="name">Pauschalwertberichtigungen zu sonstigen Forderungen und Vermögensgegenständen</field>
          <field name="code">2490</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2500" model="account.account.template">
          <field name="name">Vorsteuern 20%</field>
          <field name="code">2500</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2501" model="account.account.template">
          <field name="name">Vorsteuern 10%</field>
          <field name="code">2501</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2502" model="account.account.template">
          <field name="name">Vorsteuern 13%</field>
          <field name="code">2502</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2505" model="account.account.template">
          <field name="name">Sonstige Vorsteuern</field>
          <field name="code">2505</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2506" model="account.account.template">
          <field name="name">Vorsteuern RC 20%</field>
          <field name="code">2506</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2507" model="account.account.template">
          <field name="name">Vorsteuern RC 10%</field>
          <field name="code">2507</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2510" model="account.account.template">
          <field name="name">Vorsteuern RC EU 20%</field>
          <field name="code">2510</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2511" model="account.account.template">
          <field name="name">Vorsteuern IGE 20%</field>
          <field name="code">2511</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2512" model="account.account.template">
          <field name="name">Vorsteuern IGE 10%</field>
          <field name="code">2512</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2513" model="account.account.template">
          <field name="name">Vorsteuern IGE 13%</field>
          <field name="code">2513</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2515" model="account.account.template">
          <field name="name">Vorsteuern 20% (aus EUSt.)</field>
          <field name="code">2515</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABII4')])]" />
        </record>
        <record id="chart_at_template_2600" model="account.account.template">
          <field name="name">Eigene Anteile</field>
          <field name="code">2600</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABIII')])]" />
        </record>
        <record id="chart_at_template_2610" model="account.account.template">
          <field name="name">Anteile an verbundenen Unternehmen</field>
          <field name="code">2610</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABIII1')])]" />
        </record>
        <record id="chart_at_template_2620" model="account.account.template">
          <field name="name">Sonstige Anteile</field>
          <field name="code">2620</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABIII2')])]" />
        </record>
        <record id="chart_at_template_2680" model="account.account.template">
          <field name="name">Besitzwechsel, soweit dem Unternehmen nicht die der Ausstellung zugrundeliegenden Forderungen zustehen</field>
          <field name="code">2680</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABIII2')])]" />
        </record>
        <record id="chart_at_template_2690" model="account.account.template">
          <field name="name">Wertberichtigungen</field>
          <field name="code">2690</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABIII2')])]" />
        </record>
        <record id="chart_at_template_2730" model="account.account.template">
          <field name="name">Postwertzeichen</field>
          <field name="code">2730</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABIV')])]" />
        </record>
        <record id="chart_at_template_2740" model="account.account.template">
          <field name="name">Stempelmarken</field>
          <field name="code">2740</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABIV')])]" />
        </record>
        <record id="chart_at_template_2780" model="account.account.template">
          <field name="name">Schecks in Inlandswährung</field>
          <field name="code">2780</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABIV')])]" />
        </record>
        <record id="chart_at_template_transfer_288" model="account.account.template">
        </record>
        <record id="chart_at_template_2890" model="account.account.template">
          <field name="name">Wertberichtigungen</field>
          <field name="code">2890</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_ABIV')])]" />
        </record>
        <record id="chart_at_template_2900" model="account.account.template">
          <field name="name">Aktive Rechnungsabgrenzungsposten</field>
          <field name="code">2900</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AC')])]" />
        </record>
        <record id="chart_at_template_2950" model="account.account.template">
          <field name="name">Disagio</field>
          <field name="code">2950</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AC')])]" />
        </record>
        <record id="chart_at_template_2960" model="account.account.template">
          <field name="name">Unterschiedsbetrag zur gebotenen Pensionsrückstellung</field>
          <field name="code">2960</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PBI')])]" />
        </record>
        <record id="chart_at_template_2970" model="account.account.template">
          <field name="name">Unterschiedsbetrag gem. Abschnitt XII Pensionskassengesetz</field>
          <field name="code">2970</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PBII')])]" />
        </record>
        <record id="chart_at_template_2980" model="account.account.template">
          <field name="name">Steuerabgrenzung</field>
          <field name="code">2980</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_AC')])]" />
        </record>
        <record id="chart_at_template_3000" model="account.account.template">
          <field name="name">Rückstellungen für Abfertigungen</field>
          <field name="code">3000</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PBI')])]" />
        </record>
        <record id="chart_at_template_3010" model="account.account.template">
          <field name="name">Rückstellungen für Pensionen</field>
          <field name="code">3010</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_non_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PBII')])]" />
        </record>
        <record id="chart_at_template_3100" model="account.account.template">
          <field name="name">Anleihen (einschließlich konvertibler)</field>
          <field name="code">3100</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCI')])]" />
        </record>
        <record id="chart_at_template_3200" model="account.account.template">
          <field name="name">Erhaltene Anzahlungen auf Bestellungen</field>
          <field name="code">3200</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_assets" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCIII')])]" />
        </record>
        <record id="chart_at_template_3210" model="account.account.template">
          <field name="name">Umsatzsteuer-Evidenzkonto für erhaltene Anzahlungen auf Bestellungen</field>
          <field name="code">3210</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3300" model="account.account.template">
          <field name="name">Verbindlichkeiten bei Partnern im Inland ohne eigenes Kreditorenkonto</field>
          <field name="code">3300</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_payable" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCIV')])]" />
        </record>
        <record id="chart_at_template_3360" model="account.account.template">
          <field name="name">Verbindlichkeiten bei Partnern im EU Raum ohne eigenes Kreditorenkonto</field>
          <field name="code">3360</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_payable" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCIV')])]" />
        </record>
        <record id="chart_at_template_3370" model="account.account.template">
          <field name="name">Verbindlichkeiten bei Partnern in Drittstaaten ohne eigenes Kreditorenkonto</field>
          <field name="code">3370</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_payable" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCIV')])]" />
        </record>
        <record id="chart_at_template_3480" model="account.account.template">
          <field name="name">Verbindlichkeiten gegenüber Gesellschaftern</field>
          <field name="code">3480</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII')])]" />
        </record>
        <record id="chart_at_template_3500" model="account.account.template">
          <field name="name">Umsatzsteuer 20%</field>
          <field name="code">3500</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3501" model="account.account.template">
          <field name="name">Umsatzsteuer 10%</field>
          <field name="code">3501</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3502" model="account.account.template">
          <field name="name">Umsatzsteuer 13%</field>
          <field name="code">3502</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3505" model="account.account.template">
          <field name="name">Sonstige Umsatzsteuer</field>
          <field name="code">3505</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3510" model="account.account.template">
          <field name="name">Umsatzsteuer RC EU 20%</field>
          <field name="code">3510</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3511" model="account.account.template">
          <field name="name">Umsatzsteuer IGE 20%</field>
          <field name="code">3511</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3512" model="account.account.template">
          <field name="name">Umsatzsteuer IGE 10%</field>
          <field name="code">3512</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3513" model="account.account.template">
          <field name="name">Umsatzsteuer IGE 13%</field>
          <field name="code">3513</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3515" model="account.account.template">
          <field name="name">Einfuhrumsatzsteuer 20%</field>
          <field name="code">3515</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3520" model="account.account.template">
          <field name="name">Ust. Zahllast</field>
          <field name="code">3520</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3530" model="account.account.template">
          <field name="name">Verrechnungskonto Finanzamt</field>
          <field name="code">3530</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_payable" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3540" model="account.account.template">
          <field name="name">Verrechnung Lohnsteuer</field>
          <field name="code">3540</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3541" model="account.account.template">
          <field name="name">Verrechnung Dienstgeberbeitrag</field>
          <field name="code">3541</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3542" model="account.account.template">
          <field name="name">Verrechnung Dienstgeberzuschlag</field>
          <field name="code">3542</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3550" model="account.account.template">
          <field name="name">Verrechnung Kommunalsteuer</field>
          <field name="code">3550</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3551" model="account.account.template">
          <field name="name">Verrechnung Wiener Dienstgeberabgabe</field>
          <field name="code">3551</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII1')])]" />
        </record>
        <record id="chart_at_template_3600" model="account.account.template">
          <field name="name">Verrechungskonto Sozialversicherung</field>
          <field name="code">3600</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_payable" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII2')])]" />
        </record>
        <record id="chart_at_template_3610" model="account.account.template">
          <field name="name">Verrechnungskonto Magistrat/Gemeinde (KoSt, U-Bahn, etc.)</field>
          <field name="code">3610</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_payable" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PCVIII2')])]" />
        </record>
        <record id="chart_at_template_3740" model="account.account.template">
          <field name="name">WERE Verrechnungskonto</field>
          <field name="code">3740</field>
          <field name="reconcile" eval="True"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_payable" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_PD')])]" />
        </record>
        <record id="chart_at_template_4000" model="account.account.template">
          <field name="name">Brutto-Umsatzerlöse im Inland (20%)</field>
          <field name="code">4000</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_revenue" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT1')])]" />
        </record>
        <record id="chart_at_template_4001" model="account.account.template">
          <field name="name">Brutto-Umsatzerlöse im Inland (10%)</field>
          <field name="code">4001</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_revenue" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT1')])]" />
        </record>
        <record id="chart_at_template_4100" model="account.account.template">
          <field name="name">Brutto-Umsatzerlöse im EU Raum (RC 20%)</field>
          <field name="code">4100</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_revenue" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT1')])]" />
        </record>
        <record id="chart_at_template_4110" model="account.account.template">
          <field name="name">Brutto-Umsatzerlöse im EU Raum (RC 10%)</field>
          <field name="code">4110</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_revenue" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT1')])]" />
        </record>
        <record id="chart_at_template_4200" model="account.account.template">
          <field name="name">Brutto-Umsatzerlöse in Drittstaaten (0%)</field>
          <field name="code">4200</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_revenue" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT1')])]" />
        </record>
        <record id="chart_at_template_4860" model="account.account.template">
          <field name="name">Kursgewinne aus Fremdwährungstransaktionen</field>
          <field name="code">4860</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_other_income" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT1')])]" />
        </record>
        <record id="chart_at_template_5000" model="account.account.template">
          <field name="name">Wareneinkauf</field>
          <field name="code">5000</field>
          <field name="note">KZ 9100</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT5I')])]" />
        </record>
        <record id="chart_at_template_5050" model="account.account.template">
          <field name="name">Wareneinkauf EU</field>
          <field name="code">5050</field>
          <field name="note">KZ 9100</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT5I')])]" />
        </record>
        <record id="chart_at_template_5090" model="account.account.template">
          <field name="name">Wareneinkauf 0%</field>
          <field name="code">5090</field>
          <field name="note">KZ 9100</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT5I')])]" />
        </record>
        <record id="chart_at_template_5800" model="account.account.template">
          <field name="name">Skontoerträge auf Materialaufwand</field>
          <field name="code">5800</field>
          <field name="note">KZ 9100</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT5I')])]" />
        </record>
        <record id="chart_at_template_5810" model="account.account.template">
          <field name="name">Skontoerträge auf sonstige bezogene Herstellungsleistungen</field>
          <field name="code">5810</field>
          <field name="note">KZ 9110</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT5II')])]" />
        </record>
        <record id="chart_at_template_5900" model="account.account.template">
          <field name="name">Aufwandsstellenrechnung</field>
          <field name="code">5900</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating')])]" />
        </record>
        <record id="chart_at_template_6200" model="account.account.template">
          <field name="name">Gehälter (Angestellte)</field>
          <field name="code">6200</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6I')])]" />
        </record>
        <record id="chart_at_template_6205" model="account.account.template">
          <field name="name">Geschäftsführerbezug</field>
          <field name="code">6205</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6I')])]" />
        </record>
        <record id="chart_at_template_6242" model="account.account.template">
          <field name="name">Urlaubsabfindung (Angestellte)</field>
          <field name="code">6242</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6I')])]" />
        </record>
        <record id="chart_at_template_6260" model="account.account.template">
          <field name="name">Sonstige Bezüge (Angestellte)</field>
          <field name="code">6260</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6I')])]" />
        </record>
        <record id="chart_at_template_6270" model="account.account.template">
          <field name="name">Sachbezug (Angestellte)</field>
          <field name="code">6270</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6I')])]" />
        </record>
        <record id="chart_at_template_6271" model="account.account.template">
          <field name="name">Sachbezug (Geschäftsführer)</field>
          <field name="code">6271</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6I')])]" />
        </record>
        <record id="chart_at_template_6310" model="account.account.template">
          <field name="name">Grundgehälter (Überstunden)</field>
          <field name="code">6310</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6I')])]" />
        </record>
        <record id="chart_at_template_6330" model="account.account.template">
          <field name="name">Gehälter (Überstundenzuschläge)</field>
          <field name="code">6330</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6I')])]" />
        </record>
        <record id="chart_at_template_6340" model="account.account.template">
          <field name="name">Veränderung noch nicht konsumierter Urlaub</field>
          <field name="code">6340</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6I')])]" />
        </record>
        <record id="chart_at_template_6400" model="account.account.template">
          <field name="name">Beiträge für betriebliche Mitarbeitervorsorgekasse</field>
          <field name="code">6400</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6II')])]" />
        </record>

        <record id="chart_at_template_6560" model="account.account.template">
          <field name="name">Gesetzlicher Sozialaufwand</field>
          <field name="code">6560</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6II')])]" />
        </record>

        <record id="chart_at_template_6660" model="account.account.template">
          <field name="name">Kommunalsteuer (KoSt)</field>
          <field name="code">6660</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6II')])]" />
        </record>
        <record id="chart_at_template_6661" model="account.account.template">
          <field name="name">Dienstgeberbeitrag zum Familienlastenausgleichsfonds (DB)</field>
          <field name="code">6661</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6II')])]" />
        </record>
        <record id="chart_at_template_6662" model="account.account.template">
          <field name="name">Zuschlag zum Dienstnehmerbeitrag (DZ)</field>
          <field name="code">6662</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6II')])]" />
        </record>
        <record id="chart_at_template_6640" model="account.account.template">
          <field name="name">Dienstgeberabgabe der Gemeinde Wien (U-Bahn Steuer)</field>
          <field name="code">6663</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6II')])]" />
        </record>

        <record id="chart_at_template_6700" model="account.account.template">
          <field name="name">Sonstiger freiwilliger Sozialaufwand</field>
          <field name="code">6700</field>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT6II')])]" />
        </record>

        <record id="chart_at_template_6900" model="account.account.template">
          <field name="name">Aufwandsstellenrechnung</field>
          <field name="code">6900</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
        </record>
        <record id="chart_at_template_7000" model="account.account.template">
          <field name="name">Abschreibungen auf aktivierte Aufwendungen für das Ingangsetzen und Erweitern eines Betriebes</field>
          <field name="code">7000</field>
          <field name="note">KZ 9130</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_depreciation" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_EBIT7I')])]" />
        </record>
        <record id="chart_at_template_7090" model="account.account.template">
          <field name="name">Abschreibungen vom Umlaufvermögen, soweit diese die im Unternehmen üblichen Abschreibungen übersteigen</field>
          <field name="code">7090</field>
          <field name="note">KZ 9140 (Bilanzierer gemäß §§ 4 Abs. 1 oder 5)</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_depreciation" />
          <field name="tag_ids" eval="[(6, 0, [ref('l10n_at.account_tag_l10n_at_EBIT7II')])]" />
        </record>
        <record id="chart_at_template_7600" model="account.account.template">
          <field name="name">Büromaterial und Drucksorten</field>
          <field name="code">7600</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT8')])]" />
        </record>
        <record id="chart_at_template_763" model="account.account.template">
          <field name="name">Fachliteratur und Zeitungen</field>
          <field name="code">7630</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT8')])]" />
        </record>
        <record id="chart_at_template_7690" model="account.account.template">
          <field name="name">Spenden und Trinkgelder</field>
          <field name="code">7690</field>
          <field name="note">KZ 9200</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT8')])]" />
        </record>
        <record id="chart_at_template_7770" model="account.account.template">
          <field name="name">Aus- und Fortbildung</field>
          <field name="code">7770</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT8')])]" />
        </record>
        <record id="chart_at_template_7780" model="account.account.template">
          <field name="name">Mitgliedsbeiträge</field>
          <field name="code">7780</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT8')])]" />
        </record>
        <record id="chart_at_template_7790" model="account.account.template">
          <field name="name">Spesen des Geldverkehrs</field>
          <field name="code">7790</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT8')])]" />
        </record>
        <record id="chart_at_template_7820" model="account.account.template">
          <field name="name">Buchwert abgegangener Anlagen, ausgenommen Finanzanlagen</field>
          <field name="code">7820</field>
          <field name="note">KZ 9210</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT8')])]" />
        </record>
        <record id="chart_at_template_7830" model="account.account.template">
          <field name="name">Verluste aus dem Abgang vom Anlagevermögen, ausgenommen Finanzanlagen</field>
          <field name="code">7830</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT8')])]" />
        </record>
        <record id="chart_at_template_7860" model="account.account.template">
          <field name="name">Kursverluste aus Fremdwährungstransaktionen</field>
          <field name="code">7860</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT8')])]" />
        </record>
        <record id="chart_at_template_7890" model="account.account.template">
          <field name="name">Skontoerträge auf sonstige betriebliche Aufwendungen</field>
          <field name="code">7890</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT8')])]" />
        </record>
        <record id="chart_at_template_7900" model="account.account.template">
          <field name="name">Aufwandsstellenrechnung</field>
          <field name="code">7900</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
        </record>
        <record id="chart_at_template_7960" model="account.account.template">
          <field name="name">Herstellungskosten der zur Erzielung der Umsatzerlöse erbrachten Leistungen</field>
          <field name="code">7960</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
        </record>
        <record id="chart_at_template_7970" model="account.account.template">
          <field name="name">Vertriebskosten</field>
          <field name="code">7970</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
        </record>
        <record id="chart_at_template_7980" model="account.account.template">
          <field name="name">Verwaltungskosten</field>
          <field name="code">7980</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
        </record>
        <record id="chart_at_template_7990" model="account.account.template">
          <field name="name">Sonstige betriebliche Aufwendungen</field>
          <field name="code">7990</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_expenses" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_operating'), ref('l10n_at.account_tag_l10n_at_EBIT8')])]" />
        </record>
        <record id="chart_at_template_8140" model="account.account.template">
          <field name="name">Erlöse aus dem Abgang von Beteiligungen</field>
          <field name="code">8140</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_other_income" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_FIN10')])]" />
        </record>
        <record id="chart_at_template_8150" model="account.account.template">
          <field name="name">Erlöse aus dem Abgang von sonstigen Finanzanlagen</field>
          <field name="code">8150</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_other_income" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_FIN10')])]" />
        </record>
        <record id="chart_at_template_8160" model="account.account.template">
          <field name="name">Erlöse aus dem Abgang von Wertpapieren des Umlaufvermögens</field>
          <field name="code">8160</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_other_income" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_FIN10')])]" />
        </record>
        <record id="chart_at_template_8170" model="account.account.template">
          <field name="name">Buchwert abgegangener Beteiligungen</field>
          <field name="code">8170</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_other_income" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_FIN10')])]" />
        </record>
        <record id="chart_at_template_8180" model="account.account.template">
          <field name="name">Buchwert abgegangener sonstiger Finanzanlagen</field>
          <field name="code">8180</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_other_income" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_FIN11')])]" />
        </record>
        <record id="chart_at_template_8190" model="account.account.template">
          <field name="name">Buchwert abgegangener Wertpapiere des Umlaufvermögens</field>
          <field name="code">8190</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_other_income" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_FIN11')])]" />
        </record>
        <record id="chart_at_template_8200" model="account.account.template">
          <field name="name">Erträge aus dem Abgang von und der Zuschreibung zu Finanzanlagen</field>
          <field name="code">8200</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_other_income" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_FIN10')])]" />
        </record>
        <record id="chart_at_template_8210" model="account.account.template">
          <field name="name">Erträge aus dem Abgang von und der Zuschreibung zu Wertpapieren des Umlaufvermögens</field>
          <field name="code">8210</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_other_income" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_FIN11')])]" />
        </record>
        <record id="chart_at_template_8350" model="account.account.template">
          <field name="name">Nicht ausgenützte Lieferantenskonti</field>
          <field name="code">8350</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_other_income" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_FIN12')])]" />
        </record>
        <record id="chart_at_template_8990" model="account.account.template">
          <field name="name">Gewinnabfuhr bzw. Verlustüberrechnung aus Ergebnisabführungsverträgen</field>
          <field name="code">8990</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_other_income" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_FIN12')])]" />
        </record>
        <record id="chart_at_template_9190" model="account.account.template">
          <field name="name">Nicht eingeforderte ausstehende Einlagen</field>
          <field name="code">9190</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_equity" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_PAI')])]" />
        </record>
        <record id="chart_at_template_9390" model="account.account.template">
          <field name="name">Bilanzgewinn (-verlust)</field>
          <field name="code">9390</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_equity" />
          <field name="tag_ids" eval="[(6, 0, [ref('account.account_tag_financing'), ref('l10n_at.account_tag_l10n_at_PAIV')])]" />
        </record>
        <record id="chart_at_template_9800" model="account.account.template">
          <field name="name">Eröffnungsbilanz</field>
          <field name="code">9800</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_equity" />
        </record>
        <record id="chart_at_template_9850" model="account.account.template">
          <field name="name">Schlussbilanz</field>
          <field name="code">9850</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_equity" />
        </record>
        <record id="chart_at_template_9890" model="account.account.template">
          <field name="name">Gewinn- und Verlustrechnung</field>
          <field name="code">9890</field>
          <field name="reconcile" eval="False"/>
          <field name="chart_template_id" ref="l10n_at_chart_template"/>
          <field name="user_type_id" ref="account.data_account_type_equity" />
        </record>
    </data>
</odoo>
