<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record model="ir.module.category" id="base.module_category_marketing_surveys">
            <field name="description">Helps you manage your survey for review of different-different users.</field>
            <field name="sequence">20</field>
        </record>

        <!-- Survey users -->
        <record model="res.groups" id="group_survey_user">
            <field name="name">User</field>
            <field name="category_id" ref="base.module_category_marketing_surveys"/>
        </record>

        <!-- Survey managers -->
        <record model="res.groups" id="group_survey_manager">
            <field name="name">Administrator</field>
            <field name="category_id" ref="base.module_category_marketing_surveys"/>
            <field name="implied_ids" eval="[(4, ref('group_survey_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <record id="base.default_user" model="res.users">
            <field name="groups_id" eval="[(4,ref('group_survey_manager'))]"/>
        </record>

        <!-- SURVEY: SURVEY, PAGE, STAGE, QUESTION, LABEL -->
        <record id="survey_survey_rule_survey_manager" model="ir.rule">
            <field name="name">Survey: manager: all</field>
            <field name="model_id" ref="survey.model_survey_survey"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_survey_manager'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
        </record>
        <record id="survey_survey_rule_survey_user_read" model="ir.rule">
            <field name="name">Survey: officer: read all</field>
            <field name="model_id" ref="survey.model_survey_survey"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_survey_user'))]"/>
            <field name="perm_unlink" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
        </record>
        <record id="survey_survey_rule_survey_user_cwu" model="ir.rule">
            <field name="name">Survey: officer: create/write/unlink own only</field>
            <field name="model_id" ref="survey.model_survey_survey"/>
            <field name="domain_force">[('create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_survey_user'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="0"/>
            <field name="perm_create" eval="1"/>
        </record>

        <record id="survey_question_rule_survey_manager" model="ir.rule">
            <field name="name">Survey question: manager: all</field>
            <field name="model_id" ref="survey.model_survey_question"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_survey_manager'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
        </record>
        <record id="survey_question_rule_survey_user_read" model="ir.rule">
            <field name="name">Survey question: officer: read all</field>
            <field name="model_id" ref="survey.model_survey_question"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_survey_user'))]"/>
            <field name="perm_unlink" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
        </record>
        <record id="survey_question_rule_survey_user_cw" model="ir.rule">
            <field name="name">Survey question: officer: create/write/unlink linked to own survey only</field>
            <field name="model_id" ref="survey.model_survey_question"/>
            <field name="domain_force">[('survey_id.create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_survey_user'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="0"/>
            <field name="perm_create" eval="1"/>
        </record>

        <record id="survey_question_answer_rule_survey_manager" model="ir.rule">
            <field name="name">Survey question answer: manager: all</field>
            <field name="model_id" ref="survey.model_survey_question_answer"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_survey_manager'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
        </record>
        <record id="survey_question_answer_rule_survey_user_read" model="ir.rule">
            <field name="name">Survey question answer: officer: read all</field>
            <field name="model_id" ref="survey.model_survey_question_answer"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_survey_user'))]"/>
            <field name="perm_unlink" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
        </record>
        <record id="survey_question_answer_rule_survey_user_cw" model="ir.rule">
            <field name="name">Survey question answer: officer: create/write/unlink linked to own survey only</field>
            <field name="model_id" ref="survey.model_survey_question_answer"/>
            <field name="domain_force">['|', ('question_id.survey_id.create_uid', '=', user.id), ('matrix_question_id.survey_id.create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_survey_user'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="0"/>
            <field name="perm_create" eval="1"/>
        </record>

        <!-- SURVEY: USER_INPUT, USER_INPUT_LINE -->
        <record id="survey_user_input_rule_survey_manager" model="ir.rule">
            <field name="name">Survey user input: manager: all</field>
            <field name="model_id" ref="survey.model_survey_user_input"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_survey_manager'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
        </record>
        <record id="survey_user_input_rule_survey_user_read" model="ir.rule">
            <field name="name">Survey user input: officer: read all</field>
            <field name="model_id" ref="survey.model_survey_user_input"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_survey_user'))]"/>
            <field name="perm_unlink" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
        </record>
        <record id="survey_user_input_rule_survey_user_cw" model="ir.rule">
            <field name="name">Survey user input: officer: create/write/unlink linked to own survey only</field>
            <field name="model_id" ref="survey.model_survey_user_input"/>
            <field name="domain_force">[('survey_id.create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_survey_user'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="0"/>
            <field name="perm_create" eval="1"/>
        </record>

        <record id="survey_user_input_line_rule_survey_manager" model="ir.rule">
            <field name="name">Survey user input line: manager: all</field>
            <field name="model_id" ref="survey.model_survey_user_input_line"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_survey_manager'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
        </record>
        <record id="survey_user_input_line_rule_survey_user_read" model="ir.rule">
            <field name="name">Survey user input line: officer: read all</field>
            <field name="model_id" ref="survey.model_survey_user_input_line"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_survey_user'))]"/>
            <field name="perm_unlink" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="0"/>
        </record>
        <record id="survey_user_input_line_rule_survey_user_cw" model="ir.rule">
            <field name="name">Survey user input line: officer: create/write/unlink linked to own survey only</field>
            <field name="model_id" ref="survey.model_survey_user_input_line"/>
            <field name="domain_force">[('user_input_id.survey_id.create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_survey_user'))]"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="0"/>
            <field name="perm_create" eval="1"/>
        </record>
    </data>
</odoo>
