# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fetchmail_gmail
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-25 14:36+0000\n"
"PO-Revision-Date: 2022-03-03 13:46+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Connect your Gmail account"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Verbinden Sie Ihr Gmail-Konto"

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid ""
"<i class=\"fa fa-cog\"/>\n"
"                        Edit Settings"
msgstr "Einstellungen ändern"

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '=', False)]}\" class=\"badge badge-success\">\n"
"                        Gmail Token Valid\n"
"                    </span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '=', False)]}\" class=\"badge badge-success\">\n"
"                        Gmail-Token gültig\n"
"                    </span>"

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid "Gmail"
msgstr "Gmail"

#. module: fetchmail_gmail
#: code:addons/fetchmail_gmail/models/fetchmail_server.py:0
#, python-format
msgid "Gmail authentication only supports IMAP server type."
msgstr "Die Gmail-Authentifizierung unterstützt nur den Servertyp IMAP."

#. module: fetchmail_gmail
#: model:ir.model,name:fetchmail_gmail.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr "Server für eingehender E-Mails"

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid ""
"Setup your Gmail API credentials in the general settings to link a Gmail "
"account."
msgstr ""
"Richten Sie Ihre Gmail-API-Anmeldedaten in den allgemeinen Einstellungen "
"ein, um ein Gmail-Konto zu verknüpfen."
