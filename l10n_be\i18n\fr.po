# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON>t <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-05 14:50+0000\n"
"PO-Revision-Date: 2022-04-08 12:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-00
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-00
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-00
msgid "0% Biens d'investissement"
msgstr "0% Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-G
msgid "0% Biens divers"
msgstr "0% Biens divers"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-CC
msgid "0% Cocont."
msgstr "0% Cocont."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-00-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-00-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-00-CC
msgid "0% Cocont. - Biens d'investissement"
msgstr "0% Cocont. - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-00-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-00-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-00-CC
msgid "0% Cocont. M."
msgstr "0% Cocont. M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-CC
msgid "0% Cocont. S."
msgstr "0% Cocont. S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-00-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-00-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-00-EU
msgid "0% EU - Biens d'investissement"
msgstr "0% EU - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-EU-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-EU-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-EU-G
msgid "0% EU - Biens divers"
msgstr "0% EU - Biens divers"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-00-EU
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-EU-L
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-00-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-EU-L
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-00-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-EU-L
msgid "0% EU M."
msgstr "0% EU M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-EU-S
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-EU-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-EU-S
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-EU-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-EU-S
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-EU-S
msgid "0% EU S."
msgstr "0% EU S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-EU-T
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-EU-T
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-EU-T
msgid "0% EU T."
msgstr "0% EU T."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-00
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-00
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-00
msgid "0% M."
msgstr "0% M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-ROW
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-ROW
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-ROW
msgid "0% Non EU"
msgstr "0% Non EU"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-00-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-00-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-00-ROW-CC
msgid "0% Non EU - Biens d'investissement"
msgstr "0% Non EU - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-00-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-00-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-00-ROW-CC
msgid "0% Non EU M."
msgstr "0% Non EU M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-ROW-CC
msgid "0% Non EU S."
msgstr "0% Non EU S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-S
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-S
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-S
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-S
msgid "0% S."
msgstr "0% S."

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_00
msgid "00"
msgstr "00"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_00
msgid "00 - Opérations soumises à un régime particulier"
msgstr "00 - Opérations soumises à un régime particulier"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_01
msgid "01"
msgstr "01"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_01
msgid "01 - Opérations avec TVA à 6%"
msgstr "01 - Opérations avec TVA à 6%"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_02
msgid "02"
msgstr "02"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_02
msgid "02 - Opérations avec TVA à 12%"
msgstr "02 - Opérations avec TVA à 12%"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_03
msgid "03"
msgstr "03"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_03
msgid "03 - Opérations avec TVA à 21%"
msgstr "03 - Opérations avec TVA à 21%"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-12-L
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-12-L
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-12-L
msgid "12%"
msgstr "12%"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-12
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-12
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-12
msgid "12% Biens d'investissement"
msgstr "12% Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-G
msgid "12% Biens divers"
msgstr "12% Biens divers"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-12-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-12-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-12-CC
msgid "12% Cocont. - Biens d'investissement"
msgstr "12% Cocont. - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-12-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-12-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-12-CC
msgid "12% Cocont. M."
msgstr "12% Cocont. M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-CC
msgid "12% Cocont. S."
msgstr "12% Cocont. S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-12-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-12-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-12-EU
msgid "12% EU - Biens d'investissement"
msgstr "12% EU - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-EU-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-EU-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-EU-G
msgid "12% EU - Biens divers"
msgstr "12% EU - Biens divers"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-12-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-12-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-12-EU
msgid "12% EU M."
msgstr "12% EU M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-EU-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-EU-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-EU-S
msgid "12% EU S."
msgstr "12% EU S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-12
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-12
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-12
msgid "12% M."
msgstr "12% M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-12-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-12-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-12-ROW-CC
msgid "12% Non EU - Biens d'investissement"
msgstr "12% Non EU - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-12-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-12-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-12-ROW-CC
msgid "12% Non EU M."
msgstr "12% Non EU M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-ROW-CC
msgid "12% Non EU S."
msgstr "12% Non EU S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-S
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-12-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-S
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-12-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-S
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-12-S
msgid "12% S."
msgstr "12% S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-21-L
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-21-L
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-21-L
msgid "21%"
msgstr "21%"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-21
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-21
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-21
msgid "21% Biens d'investissement"
msgstr "21% Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-G
msgid "21% Biens divers"
msgstr "21% Biens divers"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-CC
msgid "21% Cocont .S."
msgstr "21% Cocont .S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-21-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-21-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-21-CC
msgid "21% Cocont. - Biens d'investissement"
msgstr "21% Cocont. - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-21-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-21-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-21-CC
msgid "21% Cocont. M."
msgstr "21% Cocont. M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-21-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-21-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-21-EU
msgid "21% EU - Biens d'investissement"
msgstr "21% EU - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-EU-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-EU-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-EU-G
msgid "21% EU - Biens divers"
msgstr "21% EU - Biens divers"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-21-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-21-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-21-EU
msgid "21% EU M."
msgstr "21% EU M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-EU-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-EU-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-EU-S
msgid "21% EU S."
msgstr "21% EU S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-21
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-21
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-21
msgid "21% M."
msgstr "21% M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-21-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-21-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-21-ROW-CC
msgid "21% Non EU - Biens d'investissement"
msgstr "21% Non EU - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-21-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-21-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-21-ROW-CC
msgid "21% Non EU M."
msgstr "21% Non EU M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-ROW-CC
msgid "21% Non EU S."
msgstr "21% Non EU S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-S
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-21-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-S
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-21-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-S
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-21-S
msgid "21% S."
msgstr "21% S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_TVA-21-inclus-dans-prix
#: model:account.tax,name:l10n_be.2_attn_TVA-21-inclus-dans-prix
#: model:account.tax.template,name:l10n_be.attn_TVA-21-inclus-dans-prix
msgid "21% S. TTC"
msgstr "21% S. TTC"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_44
msgid "44"
msgstr "44"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_44
msgid "44 - Services intra-communautaires"
msgstr "44 - Services intracommunautaires"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_45
msgid "45"
msgstr "45"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_45
msgid "45 - Opérations avec TVA due par le cocontractant"
msgstr "45 - Opérations avec TVA due par le cocontractant"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_operations_sortie_46
msgid "46 - Livraisons intra-communautaires exemptées"
msgstr "46 - Livraisons intracommunautaires exemptées"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_46L
msgid "46L"
msgstr "46L"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_46L
msgid "46L - Livraisons biens intra-communautaires exemptées"
msgstr "46L - Livraisons biens intracommunautaires exemptées"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_46T
msgid "46T"
msgstr "46T"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_46T
msgid "46T - Livraisons biens intra-communautaire exemptées"
msgstr "46T - Livraisons biens intracommunautaires exemptées"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_47
msgid "47"
msgstr "47"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_47
msgid "47 - Autres opérations exemptées"
msgstr "47 - Autres opérations exemptées"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_operations_sortie_48
msgid "48 - Notes de crédit aux opérations grilles [44] et [46]"
msgstr ""
"48 - Notes de crédit relatives aux opérations inscrites en grilles [44] et "
"[46]"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_48s44
msgid "48s44"
msgstr "48s44"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_48s44
msgid "48s44 - Notes de crédit aux opérations grilles [44]"
msgstr ""
"48s44 - Notes de crédit relatives aux opérations inscrites en grille [44]"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_48s46L
msgid "48s46L"
msgstr "48s46L"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_48s46L
msgid "48s46L - Notes de crédit aux opérations grilles [46L]"
msgstr ""
"48s46L - Notes de crédit relatives aux opérations inscrites en grille [46L]"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_48s46T
msgid "48s46T"
msgstr "48s46T"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_48s46T
msgid "48s46T - Notes de crédit aux opérations grilles [46T]"
msgstr ""
"48s46T - Notes de crédit relatives aux opérations inscrites en grille [46T]"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_49
msgid "49"
msgstr "49"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_49
msgid "49 - Notes de crédit aux opérations du point II"
msgstr "49 - Notes de crédit relatives aux opérations reprises au point II"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-CAR-EXC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-CAR-EXC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-CAR-EXC
msgid "50% Non Déductible - Frais de voiture (Prix Excl.)"
msgstr "50% Non Déductible - Frais de voiture (Prix Excl.)"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_54
msgid "54"
msgstr "54"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_54
msgid "54 - TVA sur opérations des grilles [01], [02], [03]"
msgstr "54 - TVA sur les opérations déclarées en grilles [01], [02], [03]"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_55
msgid "55"
msgstr "55"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_55
msgid "55 - TVA sur opérations des grilles [86] et [88]"
msgstr "55 - TVA sur les opérations déclarées en grilles [86] et [88]"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_56
msgid "56"
msgstr "56"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_56
msgid "56 - TVA sur opérations de la grille [87]"
msgstr "56 - TVA sur les opérations déclarées en grille [87]"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_57
msgid "57"
msgstr "57"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_57
msgid "57 - TVA relatives aux importations"
msgstr "57 - TVA relative aux importations"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_59
msgid "59"
msgstr "59"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_59
msgid "59 - TVA déductible"
msgstr "59 - TVA déductible"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-06
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-06
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-06
msgid "6% Biens d'investissement"
msgstr "6% Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-G
msgid "6% Biens divers"
msgstr "6% Biens divers"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-06-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-06-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-06-CC
msgid "6% Cocont. - Biens d'investissement"
msgstr "6% Cocont. - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-06-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-06-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-06-CC
msgid "6% Cocont. M."
msgstr "6% Cocont. M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-CC
msgid "6% Cocont. S."
msgstr "6% Cocont. S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-06-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-06-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-06-EU
msgid "6% EU - Biens d'investissement"
msgstr "6% EU - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-EU-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-EU-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-EU-G
msgid "6% EU - Biens divers"
msgstr "6% EU - Biens divers"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-06-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-06-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-06-EU
msgid "6% EU M."
msgstr "6% EU M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-EU-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-EU-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-EU-S
msgid "6% EU S."
msgstr "6% EU S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-06
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-06
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-06
msgid "6% M."
msgstr "6% M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-06-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-06-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-06-ROW-CC
msgid "6% Non EU - Biens d'investissement"
msgstr "6% Non EU - Biens d'investissement"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-06-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-06-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-06-ROW-CC
msgid "6% Non EU M."
msgstr "6% Non EU M."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-ROW-CC
msgid "6% Non EU S."
msgstr "6% Non EU S."

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-S
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-06-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-S
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-06-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-S
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-06-S
msgid "6% S."
msgstr "6% S."

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_61
msgid "61"
msgstr "61"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_61
msgid "61 - Diverses régularisations en faveur de l'Etat"
msgstr "61 - Diverses régularisations en faveur de l'État"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_62
msgid "62"
msgstr "62"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_62
msgid "62 - Diverses régularisations en faveur du déclarant"
msgstr "62 - Diverses régularisations en faveur du déclarant"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_63
msgid "63"
msgstr "63"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_63
msgid "63 - TVA à reverser sur notes de crédit recues"
msgstr "63 - TVA à reverser sur notes de crédit reçues"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_64
msgid "64"
msgstr "64"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_64
msgid "64 - TVA à récupérer sur notes de crédit delivrées"
msgstr "64 - TVA à récupérer sur notes de crédit delivrées"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_71
msgid "71 - Taxes dues à l'état"
msgstr "71 - Taxes dues à l'État"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_72
msgid "72 - Somme due par l'état"
msgstr "72 - Somme due par l'État"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_81
msgid "81"
msgstr "81"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_81
msgid "81 - Marchandises, matières premières et auxiliaires"
msgstr "81 - Marchandises, matières premières et auxiliaires"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_82
msgid "82"
msgstr "82"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_82
msgid "82 - Services et biens divers"
msgstr "82 - Services et biens divers"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_83
msgid "83"
msgstr "83"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_83
msgid "83 - Biens d'investissement"
msgstr "83 - Biens d'investissement"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_84
msgid "84"
msgstr "84"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_84
msgid "84 - Notes de crédits sur opérations case [86] et [88]"
msgstr ""
"84 - Notes de crédits relatives aux opérations inscrites en grilles [86] et "
"[88]"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_85
msgid "85"
msgstr "85"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_85
msgid "85 - Notes de crédits autres opérations"
msgstr "85 - Notes de crédits relatives aux autres opérations"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_86
msgid "86"
msgstr "86"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_86
msgid "86 - Acquisition intra-communautaires et ventes ABC"
msgstr "86 - Acquisitions intracommunautaires et ventes ABC"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_87
msgid "87"
msgstr "87"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_87
msgid "87 - Autres opérations"
msgstr "87 - Autres opérations"

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_88
msgid "88"
msgstr "88"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_88
msgid "88 - Acquisition services intra-communautaires"
msgstr "88 - Acquisition services intracommunautaires"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modèle de plan comptable"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a052
#: model:account.account,name:l10n_be.2_a052
#: model:account.account.template,name:l10n_be.a052
msgid "Accounts receivable for assignment commitments"
msgstr "Débiteurs pour engagements de cession"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a010
#: model:account.account,name:l10n_be.2_a010
#: model:account.account.template,name:l10n_be.a010
msgid "Accounts receivable for commitments on bills in circulation"
msgstr "Débiteurs pour engagements sur effets en circulation"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a066
#: model:account.account,name:l10n_be.2_a066
#: model:account.account.template,name:l10n_be.a066
msgid "Accounts receivable for currencies sold forward"
msgstr "Débiteurs pour devises vendues à terme"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a062
#: model:account.account,name:l10n_be.2_a062
#: model:account.account.template,name:l10n_be.a062
msgid "Accounts receivable for goods sold forward"
msgstr "Débiteurs pour marchandises vendues à terme"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a012
#: model:account.account,name:l10n_be.2_a012
#: model:account.account.template,name:l10n_be.a012
msgid "Accounts receivable for other personal guarantees"
msgstr "Débiteurs pour autres garanties personnelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a492
#: model:account.account,name:l10n_be.2_a492
#: model:account.account.template,name:l10n_be.a492
msgid "Accrued charges"
msgstr "Charges à imputer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a491
#: model:account.account,name:l10n_be.2_a491
#: model:account.account.template,name:l10n_be.a491
msgid "Accrued income"
msgstr "Produits acquis"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_605
#: model:account.group,name:l10n_be.2_be_group_605
#: model:account.group.template,name:l10n_be.be_group_605
msgid "Achats d'immeubles destinés à la vente"
msgstr "Achats d'immeubles destinés à la vente"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_601
#: model:account.group,name:l10n_be.2_be_group_601
#: model:account.group.template,name:l10n_be.be_group_601
msgid "Achats de fournitures"
msgstr "Achats de fournitures"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_604
#: model:account.group,name:l10n_be.2_be_group_604
#: model:account.group.template,name:l10n_be.be_group_604
msgid "Achats de marchandises"
msgstr "Achats de marchandises"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_600
#: model:account.group,name:l10n_be.2_be_group_600
#: model:account.group.template,name:l10n_be.be_group_600
msgid "Achats de matières premières"
msgstr "Achats de matières premières"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_602
#: model:account.group,name:l10n_be.2_be_group_602
#: model:account.group.template,name:l10n_be.be_group_602
msgid "Achats de services, travaux et études"
msgstr "Achats de services, travaux et études"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_19
#: model:account.group,name:l10n_be.2_be_group_19
#: model:account.group.template,name:l10n_be.be_group_19
msgid "Acompte aux associés sur le partage de l'actif net (-)"
msgstr "Acompte aux associés sur le partage de l'actif net (-)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_176
#: model:account.group,name:l10n_be.1_be_group_46
#: model:account.group,name:l10n_be.2_be_group_176
#: model:account.group,name:l10n_be.2_be_group_46
#: model:account.group.template,name:l10n_be.be_group_176
#: model:account.group.template,name:l10n_be.be_group_46
msgid "Acomptes reçus sur commandes"
msgstr "Acomptes reçus sur commandes"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_213
#: model:account.group,name:l10n_be.1_be_group_360
#: model:account.group,name:l10n_be.1_be_group_406
#: model:account.group,name:l10n_be.2_be_group_213
#: model:account.group,name:l10n_be.2_be_group_360
#: model:account.group,name:l10n_be.2_be_group_406
#: model:account.group.template,name:l10n_be.be_group_213
#: model:account.group.template,name:l10n_be.be_group_360
#: model:account.group.template,name:l10n_be.be_group_406
msgid "Acomptes versés"
msgstr "Acomptes versés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_36
#: model:account.group,name:l10n_be.2_be_group_36
#: model:account.group.template,name:l10n_be.be_group_36
msgid "Acomptes versés sur achats pour stocks"
msgstr "Acomptes versés sur achats pour stocks"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a050
#: model:account.account,name:l10n_be.2_a050
#: model:account.account.template,name:l10n_be.a050
msgid "Acquisition commitments"
msgstr "Engagements d’acquisition"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_5100
#: model:account.group,name:l10n_be.1_be_group_5110
#: model:account.group,name:l10n_be.1_be_group_5190
#: model:account.group,name:l10n_be.2_be_group_5100
#: model:account.group,name:l10n_be.2_be_group_5110
#: model:account.group,name:l10n_be.2_be_group_5190
#: model:account.group.template,name:l10n_be.be_group_5100
#: model:account.group.template,name:l10n_be.be_group_5110
#: model:account.group.template,name:l10n_be.be_group_5190
msgid "Actions et parts"
msgstr "Actions et parts"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_50
#: model:account.group,name:l10n_be.2_be_group_50
#: model:account.group.template,name:l10n_be.be_group_50
msgid "Actions propres"
msgstr "Actions propres"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_51
#: model:account.group,name:l10n_be.2_be_group_51
#: model:account.group.template,name:l10n_be.be_group_51
msgid ""
"Actions, parts et placements de trésorerie autres que placements à revenu "
"fixe"
msgstr ""
"Actions, parts et placements de trésorerie autres que placements à revenu "
"fixe"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a021
#: model:account.account,name:l10n_be.2_a021
#: model:account.account.template,name:l10n_be.a021
msgid "Actual guarantees established for own account"
msgstr "Garanties réelles constituées pour compte propre"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7711
#: model:account.account,name:l10n_be.2_a7711
#: model:account.account.template,name:l10n_be.a7711
msgid "Adjustment of Belgian income taxes - Estimated taxes"
msgstr "Régularisations d'impôts belges sur le résultat - Impôts estimés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7712
#: model:account.account,name:l10n_be.2_a7712
#: model:account.account.template,name:l10n_be.a7712
msgid "Adjustment of Belgian income taxes - Tax provisions written back"
msgstr ""
"Régularisations d'impôts belges sur le résultat - Reprises de provisions "
"fiscales"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7710
#: model:account.account,name:l10n_be.2_a7710
#: model:account.account.template,name:l10n_be.a7710
msgid "Adjustment of Belgian income taxes - Taxes due or paid"
msgstr ""
"Régularisations d'impôts belges sur le résultat - Impôts dus ou versés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a773
#: model:account.account,name:l10n_be.2_a773
#: model:account.account.template,name:l10n_be.a773
msgid "Adjustment of foreign income taxes"
msgstr "Régularisations d'impôts étrangers sur le résultat"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a77
#: model:account.account,name:l10n_be.2_a77
#: model:account.account.template,name:l10n_be.a77
msgid "Adjustment of income taxes and write-back of tax provisions"
msgstr "Régularisations d'impôts et reprises de provisions fiscales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_695
#: model:account.group,name:l10n_be.2_be_group_695
#: model:account.group.template,name:l10n_be.be_group_695
msgid "Administrateurs ou gérants"
msgstr "Administrateurs ou gérants"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a360
#: model:account.account,name:l10n_be.2_a360
#: model:account.account.template,name:l10n_be.a360
msgid "Advance payments on purchases for stocks - Acquisition value"
msgstr "Acomptes versés sur achats pour stocks - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a369
#: model:account.account,name:l10n_be.2_a369
#: model:account.account.template,name:l10n_be.a369
msgid "Advance payments on purchases for stocks - amounts written down"
msgstr "Acomptes versés sur achats pour stocks - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a426
#: model:account.account,name:l10n_be.2_a426
#: model:account.account.template,name:l10n_be.a426
msgid ""
"Advance payments received on contract in progress payable after more than "
"one year falling due within one year"
msgstr "Acomptes reçus sur commandes à plus d'un an échéant dans l'année"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a19
#: model:account.account,name:l10n_be.2_a19
#: model:account.account.template,name:l10n_be.a19
msgid "Advance to associates on the sharing out of the assets"
msgstr "Acompte aux associés sur le partage de l'actif net"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a461
#: model:account.account,name:l10n_be.2_a461
#: model:account.account.template,name:l10n_be.a461
msgid "Advances received"
msgstr "Avances et acomptes reçus"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a176
#: model:account.account,name:l10n_be.2_a176
#: model:account.account.template,name:l10n_be.a176
msgid "Advances received on contracts in progress (more than one year)"
msgstr "Acomptes reçus sur commandes à plus d'un an"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a460
#: model:account.account,name:l10n_be.2_a460
#: model:account.account.template,name:l10n_be.a460
msgid "Advances to be received within one year"
msgstr "Avances et acomptes à recevoir à un an au plus"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_691
#: model:account.group,name:l10n_be.2_be_group_691
#: model:account.group.template,name:l10n_be.be_group_691
msgid "Affectations au capital et à la prime d'émission"
msgstr "Affectations au capital et à la prime d'émission"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_69
#: model:account.group,name:l10n_be.1_be_group_79
#: model:account.group,name:l10n_be.2_be_group_69
#: model:account.group,name:l10n_be.2_be_group_79
#: model:account.group.template,name:l10n_be.be_group_69
#: model:account.group.template,name:l10n_be.be_group_79
msgid "Affectations et prélèvements"
msgstr "Affectations et prélèvements"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_660
#: model:account.group,name:l10n_be.2_be_group_660
#: model:account.group.template,name:l10n_be.be_group_660
msgid "Amortissements et réductions de valeur non récurrents (dotations)"
msgstr "Amortissements et réductions de valeur non récurrents (dotations)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_63
#: model:account.group,name:l10n_be.2_be_group_63
#: model:account.group.template,name:l10n_be.be_group_63
msgid ""
"Amortissements, réductions de valeur et provisions pour risques et charges"
msgstr ""
"Amortissements, réductions de valeur et provisions pour risques et charges"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a653
#: model:account.account,name:l10n_be.2_a653
#: model:account.account.template,name:l10n_be.a653
msgid ""
"Amount of the discount borne by the enterprise, as a result of negotiating "
"amounts receivable"
msgstr ""
"Montant de l'escompte à charge de la société sur la négociation de créances"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a428
#: model:account.account,name:l10n_be.2_a428
#: model:account.account.template,name:l10n_be.a428
msgid ""
"Amounts payable after more than one year falling due within one year - "
"Guarantees received in cash"
msgstr ""
"Dettes à plus d'un an échéant dans l'année - Cautionnements reçus en "
"numéraire"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4232
#: model:account.account,name:l10n_be.2_a4232
#: model:account.account.template,name:l10n_be.a4232
msgid ""
"Amounts payable after more than one year falling due within one year to "
"credit institutions - Bank acceptances"
msgstr ""
"Dettes à plus d'un an échéant dans l'année envers des établissements de "
"crédit - Crédits d'acceptation"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4230
#: model:account.account,name:l10n_be.2_a4230
#: model:account.account.template,name:l10n_be.a4230
msgid ""
"Amounts payable after more than one year falling due within one year to "
"credit institutions - Current account payable"
msgstr ""
"Dettes à plus d'un an échéant dans l'année envers des établissements de "
"crédit - Dettes en compte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4231
#: model:account.account,name:l10n_be.2_a4231
#: model:account.account.template,name:l10n_be.a4231
msgid ""
"Amounts payable after more than one year falling due within one year to "
"credit institutions - Promissory notes"
msgstr ""
"Dettes à plus d'un an échéant dans l'année envers des établissements de "
"crédit - Promesses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4250
#: model:account.account,name:l10n_be.2_a4250
#: model:account.account.template,name:l10n_be.a4250
msgid ""
"Amounts payable after more than one year falling due within one year to "
"suppliers"
msgstr ""
"Dettes commerciales à plus d'un an échéant dans l'année - Fournisseurs"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1732
#: model:account.account,name:l10n_be.2_a1732
#: model:account.account.template,name:l10n_be.a1732
msgid ""
"Amounts payable to credit institutions with a remaining term of more than "
"one year - Bank acceptances"
msgstr ""
"Dettes à plus d'un an envers des établissements de crédit - Crédits "
"d'acceptation"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1730
#: model:account.account,name:l10n_be.2_a1730
#: model:account.account.template,name:l10n_be.a1730
msgid ""
"Amounts payable to credit institutions with a remaining term of more than "
"one year - Current account payable"
msgstr ""
"Dettes à plus d'un an envers des établissements de crédit - Dettes en compte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1731
#: model:account.account,name:l10n_be.2_a1731
#: model:account.account.template,name:l10n_be.a1731
msgid ""
"Amounts payable to credit institutions with a remaining term of more than "
"one year - Promissory notes"
msgstr "Dettes à plus d'un an envers des établissements de crédit - Promesses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a178
#: model:account.account,name:l10n_be.2_a178
#: model:account.account.template,name:l10n_be.a178
msgid ""
"Amounts payable with a remaining term of more than one year - Guarantees "
"received in cash"
msgstr "Dettes à plus d'un an - Cautionnements reçus en numéraire"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a432
#: model:account.account,name:l10n_be.2_a432
#: model:account.account.template,name:l10n_be.a432
msgid ""
"Amounts payable within one year to credit institutions - Bank acceptances"
msgstr ""
"Dettes à un an au plus envers des établissements de crédit - Crédits "
"d'acceptation"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a433
#: model:account.account,name:l10n_be.2_a433
#: model:account.account.template,name:l10n_be.a433
msgid ""
"Amounts payable within one year to credit institutions - Current account "
"payable"
msgstr ""
"Dettes à un an au plus envers des établissements de crédit - Dettes en "
"compte courant"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a430
#: model:account.account,name:l10n_be.2_a430
#: model:account.account.template,name:l10n_be.a430
msgid ""
"Amounts payable within one year to credit institutions - Fixed term loans"
msgstr ""
"Dettes à un an au plus envers des établissements de crédit - Emprunts en "
"compte à terme fixe"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a431
#: model:account.account,name:l10n_be.2_a431
#: model:account.account.template,name:l10n_be.a431
msgid ""
"Amounts payable within one year to credit institutions - Promissory notes"
msgstr ""
"Dettes à un an au plus envers des établissements de crédit - Promesses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2819
#: model:account.account,name:l10n_be.2_a2819
#: model:account.account.template,name:l10n_be.a2819
msgid "Amounts receivable from affiliated enterprises - Amounts written down"
msgstr "Créances sur des entreprises liées - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2811
#: model:account.account,name:l10n_be.2_a2811
#: model:account.account.template,name:l10n_be.a2811
msgid "Amounts receivable from affiliated enterprises - Bills receivable"
msgstr "Créances sur des entreprises liées - Effets à recevoir"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2810
#: model:account.account,name:l10n_be.2_a2810
#: model:account.account.template,name:l10n_be.a2810
msgid "Amounts receivable from affiliated enterprises - Current account"
msgstr "Créances sur des entreprises liées - Créances en compte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2812
#: model:account.account,name:l10n_be.2_a2812
#: model:account.account.template,name:l10n_be.a2812
msgid ""
"Amounts receivable from affiliated enterprises - Fixed income securities"
msgstr "Créances sur des entreprises liées - Titres à revenu fixe"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2839
#: model:account.account,name:l10n_be.2_a2839
#: model:account.account.template,name:l10n_be.a2839
msgid ""
"Amounts receivable from other enterprises linked by participating interests "
"- Amounts written down"
msgstr ""
"Créances sur des entreprises avec lesquelles il existe un lien de "
"participation - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2831
#: model:account.account,name:l10n_be.2_a2831
#: model:account.account.template,name:l10n_be.a2831
msgid ""
"Amounts receivable from other enterprises linked by participating interests "
"- Bills receivable"
msgstr ""
"Créances sur des entreprises avec lesquelles il existe un lien de "
"participation - Effets à recevoir"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2830
#: model:account.account,name:l10n_be.2_a2830
#: model:account.account.template,name:l10n_be.a2830
msgid ""
"Amounts receivable from other enterprises linked by participating interests "
"- Current account"
msgstr ""
"Créances sur des entreprises avec lesquelles il existe un lien de "
"participation - Créances en compte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2837
#: model:account.account,name:l10n_be.2_a2837
#: model:account.account.template,name:l10n_be.a2837
msgid ""
"Amounts receivable from other enterprises linked by participating interests "
"- Doubtful amounts"
msgstr ""
"Créances sur des entreprises avec lesquelles il existe un lien de "
"participation - Créances douteuses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2832
#: model:account.account,name:l10n_be.2_a2832
#: model:account.account.template,name:l10n_be.a2832
msgid ""
"Amounts receivable from other enterprises linked by participating interests "
"- Fixed income securities"
msgstr ""
"Créances sur des entreprises avec lesquelles il existe un lien de "
"participation - Titres à revenu fixe"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6320
#: model:account.account,name:l10n_be.2_a6320
#: model:account.account.template,name:l10n_be.a6320
msgid "Amounts written off contracts in progress - Appropriations"
msgstr "Réductions de valeur sur commandes en cours d'exécution - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6321
#: model:account.account,name:l10n_be.2_a6321
#: model:account.account.template,name:l10n_be.a6321
msgid "Amounts written off contracts in progress - Write-backs"
msgstr "Réductions de valeur sur commandes en cours d'exécution - Reprises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6510
#: model:account.account,name:l10n_be.2_a6510
#: model:account.account.template,name:l10n_be.a6510
msgid ""
"Amounts written off current assets except stocks, contracts in progress and "
"trade debtors - Appropriations"
msgstr ""
"Réductions de valeur sur actifs circulants autres que stocks, commandes en "
"cours d'exécution et créances commerciales - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6511
#: model:account.account,name:l10n_be.2_a6511
#: model:account.account.template,name:l10n_be.a6511
msgid ""
"Amounts written off current assets except stocks, contracts in progress and "
"trade debtors - Write-backs"
msgstr ""
"Réductions de valeur sur actifs circulants autres que stocks, commandes en "
"cours d'exécution et créances commerciales - Reprises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a661
#: model:account.account,name:l10n_be.2_a661
#: model:account.account.template,name:l10n_be.a661
msgid "Amounts written off financial fixed assets"
msgstr "Réductions de valeur sur immobilisations financières"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6308
#: model:account.account,name:l10n_be.2_a6308
#: model:account.account.template,name:l10n_be.a6308
msgid "Amounts written off intangible fixed assets"
msgstr "Dotations aux réductions de valeur sur immobilisations incorporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6310
#: model:account.account,name:l10n_be.2_a6310
#: model:account.account.template,name:l10n_be.a6310
msgid "Amounts written off stocks - Appropriations"
msgstr "Réductions de valeur sur stocks - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6311
#: model:account.account,name:l10n_be.2_a6311
#: model:account.account.template,name:l10n_be.a6311
msgid "Amounts written off stocks - Write-backs"
msgstr "Réductions de valeur sur stocks - Reprises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6309
#: model:account.account,name:l10n_be.2_a6309
#: model:account.account.template,name:l10n_be.a6309
msgid "Amounts written off tangible fixed assets"
msgstr "Dotations aux réductions de valeur sur immobilisations corporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6330
#: model:account.account,name:l10n_be.2_a6330
#: model:account.account.template,name:l10n_be.a6330
msgid ""
"Amounts written off trade debtors (more than one year) - Appropriations"
msgstr ""
"Réductions de valeur sur créances commerciales à plus d'un an - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6331
#: model:account.account,name:l10n_be.2_a6331
#: model:account.account.template,name:l10n_be.a6331
msgid "Amounts written off trade debtors (more than one year) - Write-backs"
msgstr ""
"Réductions de valeur sur créances commerciales à plus d'un an - Reprises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6340
#: model:account.account,name:l10n_be.2_a6340
#: model:account.account.template,name:l10n_be.a6340
msgid "Amounts written off trade debtors (within one year) - Appropriations"
msgstr ""
"Réductions de valeur sur créances commerciales à un an au plus - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6341
#: model:account.account,name:l10n_be.2_a6341
#: model:account.account.template,name:l10n_be.a6341
msgid "Amounts written off trade debtors (within one year) - Write-backs"
msgstr ""
"Réductions de valeur sur créances commerciales à un an au plus - Reprises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a691
#: model:account.account,name:l10n_be.2_a691
#: model:account.account.template,name:l10n_be.a691
msgid "Appropriations to capital and share premium account"
msgstr "Affectations à l'apport"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6920
#: model:account.account,name:l10n_be.2_a6920
#: model:account.account.template,name:l10n_be.a6920
msgid "Appropriations to legal reserve"
msgstr "Dotation à la réserve légale"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6921
#: model:account.account,name:l10n_be.2_a6921
#: model:account.account.template,name:l10n_be.a6921
msgid "Appropriations to other reserves"
msgstr "Dotation aux autres réserves"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_31
#: model:account.group,name:l10n_be.2_be_group_31
#: model:account.group.template,name:l10n_be.be_group_31
msgid "Approvisionnements - Fournitures"
msgstr "Approvisionnements - Fournitures"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_30
#: model:account.group,name:l10n_be.2_be_group_30
#: model:account.group.template,name:l10n_be.be_group_30
msgid "Approvisionnements - Matières premières"
msgstr "Approvisionnements - Matières premières"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_60
#: model:account.group,name:l10n_be.2_be_group_60
#: model:account.group.template,name:l10n_be.be_group_60
msgid "Approvisionnements et marchandises"
msgstr "Approvisionnements et marchandises"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_284
#: model:account.group,name:l10n_be.2_be_group_284
#: model:account.group.template,name:l10n_be.be_group_284
msgid "Autres actions et parts"
msgstr "Autres actions et parts"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_473
#: model:account.group,name:l10n_be.2_be_group_473
#: model:account.group.template,name:l10n_be.be_group_473
msgid "Autres allocataires"
msgstr "Autres allocataires"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_697
#: model:account.group,name:l10n_be.2_be_group_697
#: model:account.group.template,name:l10n_be.be_group_697
msgid "Autres applications"
msgstr "Autres applications"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_64
#: model:account.group,name:l10n_be.2_be_group_64
#: model:account.group.template,name:l10n_be.be_group_64
msgid "Autres charges d'exploitation"
msgstr "Autres charges d'exploitation"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_664
#: model:account.group,name:l10n_be.2_be_group_664
#: model:account.group.template,name:l10n_be.be_group_664
msgid "Autres charges d'exploitation non récurrentes"
msgstr "Autres charges d'exploitation non récurrentes"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_668
#: model:account.group,name:l10n_be.2_be_group_668
#: model:account.group.template,name:l10n_be.be_group_668
msgid "Autres charges financières non récurrentes"
msgstr "Autres charges financières non récurrentes"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_285
#: model:account.group,name:l10n_be.1_be_group_291
#: model:account.group,name:l10n_be.1_be_group_41
#: model:account.group,name:l10n_be.2_be_group_285
#: model:account.group,name:l10n_be.2_be_group_291
#: model:account.group,name:l10n_be.2_be_group_41
#: model:account.group.template,name:l10n_be.be_group_285
#: model:account.group.template,name:l10n_be.be_group_291
#: model:account.group.template,name:l10n_be.be_group_41
msgid "Autres créances"
msgstr "Autres créances"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_489
#: model:account.group,name:l10n_be.2_be_group_489
#: model:account.group.template,name:l10n_be.be_group_489
msgid "Autres dettes diverses"
msgstr "Autres dettes diverses"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_459
#: model:account.group,name:l10n_be.2_be_group_459
#: model:account.group.template,name:l10n_be.be_group_459
msgid "Autres dettes sociales"
msgstr "Autres dettes sociales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_223
#: model:account.group,name:l10n_be.2_be_group_223
#: model:account.group.template,name:l10n_be.be_group_223
msgid "Autres droits réels sur des immeubles"
msgstr "Autres droits réels sur des immeubles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_174
#: model:account.group,name:l10n_be.1_be_group_439
#: model:account.group,name:l10n_be.2_be_group_174
#: model:account.group,name:l10n_be.2_be_group_439
#: model:account.group.template,name:l10n_be.be_group_174
#: model:account.group.template,name:l10n_be.be_group_439
msgid "Autres emprunts"
msgstr "Autres emprunts"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_202
#: model:account.group,name:l10n_be.2_be_group_202
#: model:account.group.template,name:l10n_be.be_group_202
msgid "Autres frais d'établissement"
msgstr "Autres frais d'établissement"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_623
#: model:account.group,name:l10n_be.2_be_group_623
#: model:account.group.template,name:l10n_be.be_group_623
msgid "Autres frais de personnel"
msgstr "Autres frais de personnel"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_26
#: model:account.group,name:l10n_be.2_be_group_26
#: model:account.group.template,name:l10n_be.be_group_26
msgid "Autres immobilisations corporelles"
msgstr "Autres immobilisations corporelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_74
#: model:account.group,name:l10n_be.2_be_group_74
#: model:account.group.template,name:l10n_be.be_group_74
msgid "Autres produits d'exploitation"
msgstr "Autres produits d'exploitation"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_764
#: model:account.group,name:l10n_be.2_be_group_764
#: model:account.group.template,name:l10n_be.be_group_764
msgid "Autres produits d'exploitation non récurrents"
msgstr "Autres produits d'exploitation non récurrents"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_769
#: model:account.group,name:l10n_be.2_be_group_769
#: model:account.group.template,name:l10n_be.be_group_769
msgid "Autres produits financiers non récurrents"
msgstr "Autres produits financiers non récurrents"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a133
#: model:account.account,name:l10n_be.2_a133
#: model:account.account.template,name:l10n_be.a133
msgid "Available reserves"
msgstr "Réserves disponibles"

#. module: l10n_be
#: model:account.chart.template,name:l10n_be.l10nbe_chart_template
msgid "Belgian PCMN"
msgstr "PCMN belge"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6703
#: model:account.account,name:l10n_be.2_a6703
#: model:account.account.template,name:l10n_be.a6703
msgid "Belgian and foreign income taxes - Income taxes - Other income taxes"
msgstr ""
"Impôts belges et étrangers - Impôts sur le résultat - Autres impôts sur le "
"résultat"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6701
#: model:account.account,name:l10n_be.2_a6701
#: model:account.account.template,name:l10n_be.a6701
msgid ""
"Belgian and foreign income taxes - Income taxes - Withholding taxes on "
"immovables"
msgstr ""
"Impôts belges et étrangers - Impôts sur le résultat - Précompte immobilier"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6702
#: model:account.account,name:l10n_be.2_a6702
#: model:account.account.template,name:l10n_be.a6702
msgid ""
"Belgian and foreign income taxes - Income taxes - Withholding taxes on "
"investment income"
msgstr ""
"Impôts belges et étrangers - Impôts sur le résultat - Précompte mobilier"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6711
#: model:account.account,name:l10n_be.2_a6711
#: model:account.account.template,name:l10n_be.a6711
msgid ""
"Belgian income taxes on the result of prior periods - Additional charges for"
" estimated income taxes"
msgstr ""
"Impôts belges sur le résultat d'exercices antérieurs - Suppléments d'impôts "
"estimés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6710
#: model:account.account,name:l10n_be.2_a6710
#: model:account.account.template,name:l10n_be.a6710
msgid ""
"Belgian income taxes on the result of prior periods - Additional charges for"
" income taxes due or paid"
msgstr ""
"Impôts belges sur le résultat d'exercices antérieurs - Suppléments d'impôts "
"dus ou versés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6712
#: model:account.account,name:l10n_be.2_a6712
#: model:account.account.template,name:l10n_be.a6712
msgid ""
"Belgian income taxes on the result of prior periods - Additional charges for"
" income taxes provided for"
msgstr ""
"Impôts belges sur le résultat d'exercices antérieurs - Provisions fiscales "
"constituées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6700
#: model:account.account,name:l10n_be.2_a6700
#: model:account.account.template,name:l10n_be.a6700
msgid ""
"Belgian income taxes on the result of the current period - Income taxes paid"
" and withholding taxes due or paid"
msgstr ""
"Impôts belges sur le résultat de l'exercice - Impôts et précomptes dus ou "
"versés"

#. module: l10n_be
#: model:ir.model.fields.selection,name:l10n_be.selection__account_journal__invoice_reference_model__be
#: model:ir.ui.menu,name:l10n_be.account_reports_be_statements_menu
msgid "Belgium"
msgstr "Belgique"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_07
#: model:account.group,name:l10n_be.2_be_group_07
#: model:account.group.template,name:l10n_be.be_group_07
msgid "Biens et valeurs de tiers détenus par l'entreprise"
msgstr "Biens et valeurs de tiers détenus par l'entreprise"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_072
#: model:account.group,name:l10n_be.2_be_group_072
#: model:account.group.template,name:l10n_be.be_group_072
msgid "Biens et valeurs de tiers reçus en dépôt, en consignation ou à façon"
msgstr "Biens et valeurs de tiers reçus en dépôt, en consignation ou à façon"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_04
#: model:account.group,name:l10n_be.1_be_group_041
#: model:account.group,name:l10n_be.2_be_group_04
#: model:account.group,name:l10n_be.2_be_group_041
#: model:account.group.template,name:l10n_be.be_group_04
#: model:account.group.template,name:l10n_be.be_group_041
msgid ""
"Biens et valeurs détenus par des tiers en leur nom mais aux risques et "
"profits de l'entreprise"
msgstr ""
"Biens et valeurs détenus par des tiers en leur nom mais aux risques et "
"profits de l'entreprise"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_074
#: model:account.group,name:l10n_be.2_be_group_074
#: model:account.group.template,name:l10n_be.be_group_074
msgid ""
"Biens et valeurs détenus pour compte ou aux risques et profits de tiers"
msgstr ""
"Biens et valeurs détenus pour compte ou aux risques et profits de tiers"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1751
#: model:account.account,name:l10n_be.2_a1751
#: model:account.account.template,name:l10n_be.a1751
msgid "Bills of exchange payable after more than one year"
msgstr "Dettes commerciales à plus d'un an - Effets à payer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4251
#: model:account.account,name:l10n_be.2_a4251
#: model:account.account.template,name:l10n_be.a4251
msgid ""
"Bills of exchange payable after more than one year falling due within one "
"year"
msgstr ""
"Dettes commerciales à plus d'un an échéant dans l'année - Effets à payer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a441
#: model:account.account,name:l10n_be.2_a441
#: model:account.account.template,name:l10n_be.a441
msgid "Bills of exchange payable within one year"
msgstr "Effets à payer à un an au plus"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2211
#: model:account.account,name:l10n_be.2_a2211
#: model:account.account.template,name:l10n_be.a2211
msgid "Building owned by the association or the foundation in full property"
msgstr ""
"Constructions appartenant à l'association ou à la fondation en pleine "
"propriété"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a221
#: model:account.account,name:l10n_be.2_a221
#: model:account.account.template,name:l10n_be.a221
msgid "Buildings"
msgstr "Constructions"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2221
#: model:account.account,name:l10n_be.2_a2221
#: model:account.account.template,name:l10n_be.a2221
msgid ""
"Built-up lands owned by the association or the foundation in full property"
msgstr ""
"Terrains bâtis appartenant à l'association ou à la fondation en pleine "
"propriété"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_371
#: model:account.group,name:l10n_be.2_be_group_371
#: model:account.group.template,name:l10n_be.be_group_371
msgid "Bénéfice pris en compte"
msgstr "Bénéfice pris en compte"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_790
#: model:account.group,name:l10n_be.2_be_group_790
#: model:account.group.template,name:l10n_be.be_group_790
msgid "Bénéfice reporté de l'exercice précédent"
msgstr "Bénéfice reporté de l'exercice précédent"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_14
#: model:account.group,name:l10n_be.2_be_group_14
#: model:account.group.template,name:l10n_be.be_group_14
msgid "Bénéfice reporté ou Perte reportée (–)"
msgstr "Bénéfice reporté ou perte reportée (–)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_693
#: model:account.group,name:l10n_be.2_be_group_693
#: model:account.group.template,name:l10n_be.be_group_693
msgid "Bénéfice à reporter"
msgstr "Bénéfice à reporter"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_57
#: model:account.group,name:l10n_be.2_be_group_57
#: model:account.group.template,name:l10n_be.be_group_57
msgid "Caisses"
msgstr "Caisses"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_570
#: model:account.group,name:l10n_be.2_be_group_570
#: model:account.group.template,name:l10n_be.be_group_570
msgid "Caisses-espèces"
msgstr "Caisses-espèces"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_578
#: model:account.group,name:l10n_be.2_be_group_578
#: model:account.group.template,name:l10n_be.be_group_578
msgid "Caisses-timbres"
msgstr "Caisses-timbres"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a410
#: model:account.account,name:l10n_be.2_a410
#: model:account.account.template,name:l10n_be.a410
msgid "Called up capital, unpaid"
msgstr "Capital ou apport appelé, non versé"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_10
#: model:account.group,name:l10n_be.2_be_group_10
#: model:account.group.template,name:l10n_be.be_group_10
msgid "Capital"
msgstr "Capital"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_410
#: model:account.group,name:l10n_be.2_be_group_410
#: model:account.group.template,name:l10n_be.be_group_410
msgid "Capital appelé, non versé"
msgstr "Capital appelé, non versé"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7631
#: model:account.account,name:l10n_be.2_a7631
#: model:account.account.template,name:l10n_be.a7631
msgid "Capital gains on disposal of financial fixed assets"
msgstr "Plus-values sur réalisation d'actifs immobilisés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7630
#: model:account.account,name:l10n_be.2_a7630
#: model:account.account.template,name:l10n_be.a7630
msgid "Capital gains on disposal of intangible and tangible fixed asset"
msgstr ""
"Plus-values sur réalisation d'immobilisations incorporelles et corporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6631
#: model:account.account,name:l10n_be.2_a6631
#: model:account.account.template,name:l10n_be.a6631
msgid "Capital losses on disposal of financial fixed assets"
msgstr "Moins-values sur réalisation d'immobilisations financières"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6630
#: model:account.account,name:l10n_be.2_a6630
#: model:account.account.template,name:l10n_be.a6630
msgid "Capital losses on disposal of intangible and tangible fixed assets"
msgstr ""
"Moins-values sur réalisation d'immobilisations incorporelles et corporelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_101
#: model:account.group,name:l10n_be.2_be_group_101
#: model:account.group.template,name:l10n_be.be_group_101
msgid "Capital non appelé (–)"
msgstr "Capital non appelé (–)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_100
#: model:account.group,name:l10n_be.2_be_group_100
#: model:account.group.template,name:l10n_be.be_group_100
msgid "Capital souscrit"
msgstr "Capital souscrit"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6503
#: model:account.account,name:l10n_be.2_a6503
#: model:account.account.template,name:l10n_be.a6503
msgid "Capitalized Interests"
msgstr "Intérêts intercalaires portés à l'actif"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a54
#: model:account.account,name:l10n_be.2_a54
#: model:account.account.template,name:l10n_be.a54
msgid "Cash at bank - Amounts overdue and in the process of collection"
msgstr "Valeurs échues à l'encaissement"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a55
#: model:account.account,name:l10n_be.2_a55
#: model:account.account.template,name:l10n_be.a55
msgid "Cash at bank - Credit institutions"
msgstr "Etablissements de crédit"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a560
#: model:account.account,name:l10n_be.2_a560
#: model:account.account.template,name:l10n_be.a560
msgid "Cash at bank - Giro account - Bank account"
msgstr "Office des chèques postaux - Compte courant"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a561
#: model:account.account,name:l10n_be.2_a561
#: model:account.account.template,name:l10n_be.a561
msgid "Cash at bank - Giro account - Cheques issued"
msgstr "Office des chèques postaux - Chèques émis"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a58
#: model:account.account,name:l10n_be.2_a58
#: model:account.account.template,name:l10n_be.a58
msgid "Cash at bank and in hand - Internal transfers of funds"
msgstr "Virements internes"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a57
#: model:account.account,name:l10n_be.2_a57
#: model:account.account.template,name:l10n_be.a57
msgid "Cash in hand"
msgstr "Caisses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a578
#: model:account.account,name:l10n_be.2_a578
#: model:account.account.template,name:l10n_be.a578
msgid "Cash in hand - Stamps"
msgstr "Caisses-timbres"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_178
#: model:account.group,name:l10n_be.1_be_group_488
#: model:account.group,name:l10n_be.2_be_group_178
#: model:account.group,name:l10n_be.2_be_group_488
#: model:account.group.template,name:l10n_be.be_group_178
#: model:account.group.template,name:l10n_be.be_group_488
msgid "Cautionnements reçus en numéraire"
msgstr "Cautionnements reçus en numéraire"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_288
#: model:account.group,name:l10n_be.1_be_group_418
#: model:account.group,name:l10n_be.2_be_group_288
#: model:account.group,name:l10n_be.2_be_group_418
#: model:account.group.template,name:l10n_be.be_group_288
#: model:account.group.template,name:l10n_be.be_group_418
msgid "Cautionnements versés en numéraire"
msgstr "Cautionnements versés en numéraire"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_6
#: model:account.group,name:l10n_be.2_be_group_6
#: model:account.group.template,name:l10n_be.be_group_6
msgid "Charges"
msgstr "Frais"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_653
#: model:account.group,name:l10n_be.2_be_group_653
#: model:account.group.template,name:l10n_be.be_group_653
msgid "Charges d'escompte de créances"
msgstr "Charges d'escompte de créances"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_643
#: model:account.group,name:l10n_be.2_be_group_643
#: model:account.group.template,name:l10n_be.be_group_643
msgid "Charges d'exploitation diverses"
msgstr "Charges d'exploitation diverses"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_66
#: model:account.group,name:l10n_be.2_be_group_66
#: model:account.group.template,name:l10n_be.be_group_66
msgid "Charges d'exploitation ou financières non récurrentes"
msgstr "Charges d'exploitation ou financières non récurrentes"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_649
#: model:account.group,name:l10n_be.2_be_group_649
#: model:account.group.template,name:l10n_be.be_group_649
msgid ""
"Charges d'exploitation portées à l'actif au titre de frais de "
"restructuration (–)"
msgstr ""
"Charges d'exploitation portées à l'actif au titre de frais de "
"restructuration (–)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_650
#: model:account.group,name:l10n_be.2_be_group_650
#: model:account.group.template,name:l10n_be.be_group_650
msgid "Charges des dettes"
msgstr "Charges des dettes"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_65
#: model:account.group,name:l10n_be.2_be_group_65
#: model:account.group.template,name:l10n_be.be_group_65
msgid "Charges financières"
msgstr "Charges financières"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_657
#: model:account.group,name:l10n_be.2_be_group_657
#: model:account.group.template,name:l10n_be.be_group_657
msgid "Charges financières diverses"
msgstr "Charges financières diverses"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_659
#: model:account.group,name:l10n_be.2_be_group_659
#: model:account.group.template,name:l10n_be.be_group_659
msgid ""
"Charges financières portées à l'actif au titre de frais de restructuration"
msgstr ""
"Charges financières portées à l'actif au titre de frais de restructuration"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_640
#: model:account.group,name:l10n_be.2_be_group_640
#: model:account.group.template,name:l10n_be.be_group_640
msgid "Charges fiscales d'exploitation"
msgstr "Charges fiscales d'exploitation"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_669
#: model:account.group,name:l10n_be.2_be_group_669
#: model:account.group.template,name:l10n_be.be_group_669
msgid "Charges portées à l'actif au titre de frais de restructuration (-)"
msgstr "Charges portées à l'actif au titre de frais de restructuration (-)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_492
#: model:account.group,name:l10n_be.2_be_group_492
#: model:account.group.template,name:l10n_be.be_group_492
msgid "Charges à imputer"
msgstr "Charges à imputer"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_490
#: model:account.group,name:l10n_be.2_be_group_490
#: model:account.group.template,name:l10n_be.be_group_490
msgid "Charges à reporter"
msgstr "Charges à reporter"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_70
#: model:account.group,name:l10n_be.2_be_group_70
#: model:account.group.template,name:l10n_be.be_group_70
msgid "Chiffre d'affaires"
msgstr "Chiffre d'affaires"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_561
#: model:account.group,name:l10n_be.2_be_group_561
#: model:account.group.template,name:l10n_be.be_group_561
msgid "Chèques émis (–)"
msgstr "Chèques émis (–)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_400
#: model:account.group,name:l10n_be.2_be_group_400
#: model:account.group.template,name:l10n_be.be_group_400
msgid "Clients"
msgstr "Clients"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_37
#: model:account.group,name:l10n_be.2_be_group_37
#: model:account.group.template,name:l10n_be.be_group_37
msgid "Commandes en cours d'exécution"
msgstr "Commandes en cours d'exécution"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_073
#: model:account.group,name:l10n_be.2_be_group_073
#: model:account.group.template,name:l10n_be.be_group_073
msgid "Commettants et déposants de biens et de valeurs"
msgstr "Commettants et déposants de biens et de valeurs"

#. module: l10n_be
#: model:ir.model.fields,field_description:l10n_be.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr "Standard de communication"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_res_company
msgid "Companies"
msgstr "Entreprises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a020
#: model:account.account,name:l10n_be.2_a020
#: model:account.account.template,name:l10n_be.a020
msgid "Company creditors, beneficiaries of real guarantees"
msgstr "Créanciers de l’entreprise, bénéficiaires de garanties réelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a000
#: model:account.account,name:l10n_be.2_a000
#: model:account.account.template,name:l10n_be.a000
msgid "Company creditors, beneficiaries of third party guarantees"
msgstr "Créanciers de l’entreprise, bénéficiaires de garanties de tiers"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a738
#: model:account.account,name:l10n_be.2_a738
#: model:account.account.template,name:l10n_be.a738
msgid "Compensatory amounts meant to reduce wage costs"
msgstr "Montants compensatoires destinés à réduire le coût salarial"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_560
#: model:account.group,name:l10n_be.2_be_group_560
#: model:account.group.template,name:l10n_be.be_group_560
msgid "Compte courant"
msgstr "Compte courant"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_499
#: model:account.group,name:l10n_be.2_be_group_499
#: model:account.group.template,name:l10n_be.be_group_499
msgid "Comptes d'attente"
msgstr "Comptes d'attente"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_49
#: model:account.group,name:l10n_be.2_be_group_49
#: model:account.group.template,name:l10n_be.be_group_49
msgid "Comptes de régularisation et comptes d'attente"
msgstr "Comptes de régularisation et comptes d'attente"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_550
#: model:account.group,name:l10n_be.2_be_group_550
#: model:account.group.template,name:l10n_be.be_group_550
msgid "Comptes ouverts auprès des divers établissements, à subdiviser en :"
msgstr "Comptes ouverts auprès des divers établissements, à subdiviser en :"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_211
#: model:account.group,name:l10n_be.2_be_group_211
#: model:account.group.template,name:l10n_be.be_group_211
msgid ""
"Concessions, brevets, licences, savoir-faire, marques et droits similaires"
msgstr ""
"Concessions, brevets, licences, savoir-faire, marques et droits similaires"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a211
#: model:account.account,name:l10n_be.2_a211
#: model:account.account.template,name:l10n_be.a211
msgid "Concessions, patents, licences, know-how, brands and similar rights"
msgstr ""
"Concessions, brevets, licences, savoir-faire, marques et droits similaires"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a091
#: model:account.account,name:l10n_be.2_a091
#: model:account.account.template,name:l10n_be.a091
msgid "Concordat resolution claims"
msgstr "Créances de résolution de concordat"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a090
#: model:account.account,name:l10n_be.2_a090
#: model:account.account.template,name:l10n_be.a090
msgid "Concordat resolution commitments"
msgstr "Engagements de résolution de concordat"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_033
#: model:account.group,name:l10n_be.2_be_group_033
#: model:account.group.template,name:l10n_be.be_group_033
msgid "Constituants de garanties"
msgstr "Constituants de garanties"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a033
#: model:account.account,name:l10n_be.2_a033
#: model:account.account.template,name:l10n_be.a033
msgid "Constituents of guarantees"
msgstr "Constituants de garanties"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_221
#: model:account.group,name:l10n_be.2_be_group_221
#: model:account.group.template,name:l10n_be.be_group_221
msgid "Constructions"
msgstr "Constructions"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a310
#: model:account.account,name:l10n_be.2_a310
#: model:account.account.template,name:l10n_be.a310
msgid "Consumables - Acquisition value"
msgstr "Fournitures - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a319
#: model:account.account,name:l10n_be.2_a319
#: model:account.account.template,name:l10n_be.a319
msgid "Consumables - amounts written down"
msgstr "Fournitures - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a370
#: model:account.account,name:l10n_be.2_a370
#: model:account.account.template,name:l10n_be.a370
msgid "Contracts in progress - Acquisition value"
msgstr "Commandes en cours d'exécution - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a371
#: model:account.account,name:l10n_be.2_a371
#: model:account.account.template,name:l10n_be.a371
msgid "Contracts in progress - Profit recognised"
msgstr "Commandes en cours d'exécution - Bénéfice pris en compte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a379
#: model:account.account,name:l10n_be.2_a379
#: model:account.account.template,name:l10n_be.a379
msgid "Contracts in progress - amounts written down"
msgstr "Commandes en cours d'exécution - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a730
#: model:account.account,name:l10n_be.2_a730
#: model:account.account.template,name:l10n_be.a730
msgid "Contributions from effective members"
msgstr "Cotisation (versement) membres associés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a731
#: model:account.account,name:l10n_be.2_a731
#: model:account.account.template,name:l10n_be.a731
msgid "Contributions from members"
msgstr "Cotisation (versement) membres adhérents"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a736
#: model:account.account,name:l10n_be.2_a736
#: model:account.account.template,name:l10n_be.a736
msgid ""
"Contributions, gifts, legacies and grants - Investment grants and interest "
"subsidies"
msgstr ""
"Cotisations, dons, legs et subsides - Subsides en capital et en intérêts"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a617
#: model:account.account,name:l10n_be.2_a617
#: model:account.account.template,name:l10n_be.a617
msgid ""
"Costs of hired temporary staff and persons placed at the enterprise's "
"disposal"
msgstr ""
"Frais de personnel intérimaire et de personnes mises à la disposition de "
"l'entreprise"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_621
#: model:account.group,name:l10n_be.2_be_group_621
#: model:account.group.template,name:l10n_be.be_group_621
msgid "Cotisations patronales d'assurances sociales"
msgstr "Cotisations patronales d'assurances sociales"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a065
#: model:account.account,name:l10n_be.2_a065
#: model:account.account.template,name:l10n_be.a065
msgid "Creditors for forward currency purchases"
msgstr "Créanciers pour devises achetées à terme"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a061
#: model:account.account,name:l10n_be.2_a061
#: model:account.account.template,name:l10n_be.a061
msgid "Creditors for goods purchased at term"
msgstr "Créanciers pour marchandises achetées à terme"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a051
#: model:account.account,name:l10n_be.2_a051
#: model:account.account.template,name:l10n_be.a051
msgid "Creditors of acquisition commitments"
msgstr "Créanciers d’engagements d’acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a0110
#: model:account.account,name:l10n_be.2_a0110
#: model:account.account.template,name:l10n_be.a0110
msgid ""
"Creditors of commitments on bills in circulation - Bids ceded by the company"
" under its backing"
msgstr ""
"Créanciers d’engagements sur effets en circulation - Effets cédés par "
"l’entreprise sous son endos"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a0111
#: model:account.account,name:l10n_be.2_a0111
#: model:account.account.template,name:l10n_be.a0111
msgid ""
"Creditors of commitments on notes in circulation - Other commitments on "
"notes in circulation"
msgstr ""
"Créanciers d’engagements sur effets en circulation - Autres engagements sur "
"effets en circulation"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a013
#: model:account.account,name:l10n_be.2_a013
#: model:account.account.template,name:l10n_be.a013
msgid "Creditors of other personal guarantees"
msgstr "Créanciers d’autres garanties personnelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a095
#: model:account.account,name:l10n_be.2_a095
#: model:account.account.template,name:l10n_be.a095
msgid "Creditors of pending litigation"
msgstr "Créanciers de litiges en cours"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a075
#: model:account.account,name:l10n_be.2_a075
#: model:account.account.template,name:l10n_be.a075
msgid ""
"Creditors of property and securities held on behalf of third parties or at "
"their risk and profit"
msgstr ""
"Créanciers de biens et de valeurs détenus pour compte de tiers ou à leurs "
"risques et profits"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a022
#: model:account.account,name:l10n_be.2_a022
#: model:account.account.template,name:l10n_be.a022
msgid "Creditors of third parties, beneficiaries of real guarantees"
msgstr "Créanciers de tiers, bénéficiaires de garanties réelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a092
#: model:account.account,name:l10n_be.2_a092
#: model:account.account.template,name:l10n_be.a092
msgid "Creditors under debt restructuring conditions"
msgstr "Créanciers sous conditions concordataires"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_290
#: model:account.group,name:l10n_be.1_be_group_40
#: model:account.group,name:l10n_be.2_be_group_290
#: model:account.group,name:l10n_be.2_be_group_40
#: model:account.group.template,name:l10n_be.be_group_290
#: model:account.group.template,name:l10n_be.be_group_40
msgid "Créances commerciales"
msgstr "Créances commerciales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_416
#: model:account.group,name:l10n_be.2_be_group_416
#: model:account.group.template,name:l10n_be.be_group_416
msgid "Créances diverses"
msgstr "Créances diverses"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_407
#: model:account.group,name:l10n_be.1_be_group_417
#: model:account.group,name:l10n_be.2_be_group_407
#: model:account.group,name:l10n_be.2_be_group_417
#: model:account.group.template,name:l10n_be.be_group_407
#: model:account.group.template,name:l10n_be.be_group_417
msgid "Créances douteuses"
msgstr "Créances douteuses"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_4
#: model:account.group,name:l10n_be.2_be_group_4
#: model:account.group.template,name:l10n_be.be_group_4
msgid "Créances et dettes à un an au plus"
msgstr "Créances et dettes à un an au plus"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_283
#: model:account.group,name:l10n_be.2_be_group_283
#: model:account.group.template,name:l10n_be.be_group_283
msgid ""
"Créances sur des entreprises avec lesquelles il existe un lien de "
"participation"
msgstr ""
"Créances sur des entreprises avec lesquelles il existe un lien de "
"participation"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_281
#: model:account.group,name:l10n_be.2_be_group_281
#: model:account.group.template,name:l10n_be.be_group_281
msgid "Créances sur des entreprises liées"
msgstr "Créances sur des entreprises liées"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_29
#: model:account.group,name:l10n_be.2_be_group_29
#: model:account.group.template,name:l10n_be.be_group_29
msgid "Créances à plus d'un an"
msgstr "Créances à plus d'un an"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_013
#: model:account.group,name:l10n_be.2_be_group_013
#: model:account.group.template,name:l10n_be.be_group_013
msgid "Créanciers d'autres garanties personnelles"
msgstr "Créanciers d'autres garanties personnelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_051
#: model:account.group,name:l10n_be.2_be_group_051
#: model:account.group.template,name:l10n_be.be_group_051
msgid "Créanciers d'engagements d'acquisition"
msgstr "Créanciers d'engagements d'acquisition"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_011
#: model:account.group,name:l10n_be.2_be_group_011
#: model:account.group.template,name:l10n_be.be_group_011
msgid "Créanciers d'engagements sur effets en circulation"
msgstr "Créanciers d'engagements sur effets en circulation"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_075
#: model:account.group,name:l10n_be.2_be_group_075
#: model:account.group.template,name:l10n_be.be_group_075
msgid ""
"Créanciers de biens et valeurs détenus pour compte de tiers ou à leurs "
"risques et profits"
msgstr ""
"Créanciers de biens et valeurs détenus pour compte de tiers ou à leurs "
"risques et profits"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_000
#: model:account.group,name:l10n_be.2_be_group_000
#: model:account.group.template,name:l10n_be.be_group_000
msgid "Créanciers de l'entreprise, bénéficiaires de garanties de tiers"
msgstr "Créanciers de l'entreprise, bénéficiaires de garanties de tiers"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_020
#: model:account.group,name:l10n_be.2_be_group_020
#: model:account.group.template,name:l10n_be.be_group_020
msgid "Créanciers de l'entreprise, bénéficiaires de garanties réelles"
msgstr "Créanciers de l'entreprise, bénéficiaires de garanties réelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_071
#: model:account.group,name:l10n_be.2_be_group_071
#: model:account.group.template,name:l10n_be.be_group_071
msgid "Créanciers de loyers et redevances"
msgstr "Créanciers de loyers et redevances"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_022
#: model:account.group,name:l10n_be.2_be_group_022
#: model:account.group.template,name:l10n_be.be_group_022
msgid "Créanciers de tiers, bénéficiaires de garanties réelles"
msgstr "Créanciers de tiers, bénéficiaires de garanties réelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_065
#: model:account.group,name:l10n_be.2_be_group_065
#: model:account.group.template,name:l10n_be.be_group_065
msgid "Créanciers pour devises achetées à terme"
msgstr "Créanciers pour devises achetées à terme"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_061
#: model:account.group,name:l10n_be.2_be_group_061
#: model:account.group.template,name:l10n_be.be_group_061
msgid "Créanciers pour marchandises achetées à terme"
msgstr "Créanciers pour marchandises achetées à terme"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a509
#: model:account.account,name:l10n_be.2_a509
#: model:account.account.template,name:l10n_be.a509
msgid ""
"Current investments other than shares, fixed income securities and term "
"accounts - Amounts written down"
msgstr ""
"Placements de trésorerie autres que des actions et parts, titres à revenu "
"fixe et dépôts à terme - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a500
#: model:account.account,name:l10n_be.2_a500
#: model:account.account.template,name:l10n_be.a500
msgid ""
"Current investments other than shares, fixed income securities and term "
"accounts - Cost"
msgstr ""
"Placements de trésorerie autres que des actions et parts, titres à revenu "
"fixe et dépôts à terme - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4001
#: model:account.account,name:l10n_be.2_a4001
#: model:account.account.template,name:l10n_be.a4001
msgid "Customer (POS)"
msgstr "Clients (POS)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_532
#: model:account.group,name:l10n_be.2_be_group_532
#: model:account.group.template,name:l10n_be.be_group_532
msgid "D'un mois au plus"
msgstr "D'un mois au plus"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_530
#: model:account.group,name:l10n_be.2_be_group_530
#: model:account.group.template,name:l10n_be.be_group_530
msgid "De plus d'un an"
msgstr "De plus d'un an"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_531
#: model:account.group,name:l10n_be.2_be_group_531
#: model:account.group.template,name:l10n_be.be_group_531
msgid "De plus d'un mois et à un an au plus"
msgstr "De plus d'un mois et à un an au plus"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a096
#: model:account.account,name:l10n_be.2_a096
#: model:account.account.template,name:l10n_be.a096
msgid "Debtors on technical guarantees"
msgstr "Débiteurs sur garanties techniques"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6095
#: model:account.account,name:l10n_be.2_a6095
#: model:account.account.template,name:l10n_be.a6095
msgid "Decrease (increase) in immovable property for resale"
msgstr "Variations des stocks d'immeubles achetés destinés à la vente"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6091
#: model:account.account,name:l10n_be.2_a6091
#: model:account.account.template,name:l10n_be.a6091
msgid "Decrease (increase) in stocks of consumables"
msgstr "Variations des stocks de fournitures"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6094
#: model:account.account,name:l10n_be.2_a6094
#: model:account.account.template,name:l10n_be.a6094
msgid "Decrease (increase) in stocks of goods purchased for resale"
msgstr "Variations des stocks des stocks de marchandises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6090
#: model:account.account,name:l10n_be.2_a6090
#: model:account.account.template,name:l10n_be.a6090
msgid "Decrease (increase) in stocks of raw materials"
msgstr "Variations des stocks de matières premières"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a124
#: model:account.account,name:l10n_be.2_a124
#: model:account.account.template,name:l10n_be.a124
msgid "Decrease in amounts written down current investments"
msgstr "Reprises de réductions de valeur sur placements de trésorerie"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a490
#: model:account.account,name:l10n_be.2_a490
#: model:account.account.template,name:l10n_be.a490
msgid "Deferred charges"
msgstr "Charges à reporter"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a493
#: model:account.account,name:l10n_be.2_a493
#: model:account.account.template,name:l10n_be.a493
msgid "Deferred income"
msgstr "Produits à reporter"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1681
#: model:account.account,name:l10n_be.2_a1681
#: model:account.account.template,name:l10n_be.a1681
msgid "Deferred taxes on gain on disposal of intangible fixed assets"
msgstr ""
"Impôts différés afférents à des plus-values réalisées sur immobilisations "
"incorporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1687
#: model:account.account,name:l10n_be.2_a1687
#: model:account.account.template,name:l10n_be.a1687
msgid ""
"Deferred taxes on gain on disposal of securities issued by Belgian public "
"authorities"
msgstr ""
"Impôts différés afférents à des plus-values réalisées sur titres émis par le"
" secteur public belge"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1682
#: model:account.account,name:l10n_be.2_a1682
#: model:account.account.template,name:l10n_be.a1682
msgid "Deferred taxes on gain on disposal of tangible fixed assets"
msgstr ""
"Impôts différés afférents à des plus-values réalisées sur immobilisations "
"corporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1680
#: model:account.account,name:l10n_be.2_a1680
#: model:account.account.template,name:l10n_be.a1680
msgid "Deferred taxes on investment grants"
msgstr "Impôts différés afférents à des subsides en capital"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6300
#: model:account.account,name:l10n_be.2_a6300
#: model:account.account.template,name:l10n_be.a6300
msgid "Depreciation of formation expenses"
msgstr "Dotations aux amortissements sur frais d'établissement"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6301
#: model:account.account,name:l10n_be.2_a6301
#: model:account.account.template,name:l10n_be.a6301
msgid "Depreciation of intangible fixed assets"
msgstr "Dotations aux amortissements sur immobilisations incorporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6501
#: model:account.account,name:l10n_be.2_a6501
#: model:account.account.template,name:l10n_be.a6501
msgid "Depreciation of loan issue expenses"
msgstr "Amortissement des frais d'émission d'emprunts"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6302
#: model:account.account,name:l10n_be.2_a6302
#: model:account.account.template,name:l10n_be.a6302
msgid "Depreciation of tangible fixed assets"
msgstr "Dotations aux amortissements sur immobilisations corporelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_717
#: model:account.group,name:l10n_be.2_be_group_717
#: model:account.group.template,name:l10n_be.be_group_717
msgid "Des commandes en cours d'exécution"
msgstr "Des commandes en cours d'exécution"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_712
#: model:account.group,name:l10n_be.2_be_group_712
#: model:account.group.template,name:l10n_be.be_group_712
msgid "Des en-cours de fabrication"
msgstr "Des en-cours de fabrication"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_715
#: model:account.group,name:l10n_be.2_be_group_715
#: model:account.group.template,name:l10n_be.be_group_715
msgid "Des immeubles construits destinés à la vente"
msgstr "Des immeubles construits destinés à la vente"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_713
#: model:account.group,name:l10n_be.2_be_group_713
#: model:account.group.template,name:l10n_be.be_group_713
msgid "Des produits finis"
msgstr "Des produits finis"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_175
#: model:account.group,name:l10n_be.1_be_group_44
#: model:account.group,name:l10n_be.2_be_group_175
#: model:account.group,name:l10n_be.2_be_group_44
#: model:account.group.template,name:l10n_be.be_group_175
#: model:account.group.template,name:l10n_be.be_group_44
msgid "Dettes commerciales"
msgstr "Dettes commerciales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_172
#: model:account.group,name:l10n_be.2_be_group_172
#: model:account.group.template,name:l10n_be.be_group_172
msgid "Dettes de location-financement et dettes assimilées"
msgstr "Dettes de location-financement et dettes assimilées"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_179
#: model:account.group,name:l10n_be.1_be_group_48
#: model:account.group,name:l10n_be.2_be_group_179
#: model:account.group,name:l10n_be.2_be_group_48
#: model:account.group.template,name:l10n_be.be_group_179
#: model:account.group.template,name:l10n_be.be_group_48
msgid "Dettes diverses"
msgstr "Dettes diverses"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_47
#: model:account.group,name:l10n_be.2_be_group_47
#: model:account.group.template,name:l10n_be.be_group_47
msgid "Dettes découlant de l'affectation du résultat"
msgstr "Dettes découlant de l'affectation du résultat"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_43
#: model:account.group,name:l10n_be.2_be_group_43
#: model:account.group.template,name:l10n_be.be_group_43
msgid "Dettes financières"
msgstr "Dettes financières"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_450
#: model:account.group,name:l10n_be.2_be_group_450
#: model:account.group.template,name:l10n_be.be_group_450
msgid "Dettes fiscales estimées"
msgstr "Dettes fiscales estimées"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_45
#: model:account.group,name:l10n_be.2_be_group_45
#: model:account.group.template,name:l10n_be.be_group_45
msgid "Dettes fiscales, salariales et sociales"
msgstr "Dettes fiscales, salariales et sociales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_17
#: model:account.group,name:l10n_be.2_be_group_17
#: model:account.group.template,name:l10n_be.be_group_17
msgid "Dettes à plus d'un an"
msgstr "Dettes à plus d'un an"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_42
#: model:account.group,name:l10n_be.2_be_group_42
#: model:account.group.template,name:l10n_be.be_group_42
msgid ""
"Dettes à plus d'un an échéant dans l'année 16 (même subdivision que le 17)"
msgstr ""
"Dettes à plus d'un an échéant dans l'année (même subdivision que le compte "
"17)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a222
#: model:account.account,name:l10n_be.2_a222
#: model:account.account.template,name:l10n_be.a222
msgid "Developed land"
msgstr "Terrains bâtis"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_064
#: model:account.group,name:l10n_be.2_be_group_064
#: model:account.group.template,name:l10n_be.be_group_064
msgid "Devises achetées à terme - à recevoir"
msgstr "Devises achetées à terme - à recevoir"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_067
#: model:account.group,name:l10n_be.2_be_group_067
#: model:account.group.template,name:l10n_be.be_group_067
msgid "Devises vendues à terme - à livrer"
msgstr "Devises vendues à terme - à livrer"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_654
#: model:account.group,name:l10n_be.1_be_group_754
#: model:account.group,name:l10n_be.2_be_group_654
#: model:account.group,name:l10n_be.2_be_group_754
#: model:account.group.template,name:l10n_be.be_group_654
#: model:account.group.template,name:l10n_be.be_group_754
msgid "Différences de change"
msgstr "Différences de change"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a472
#: model:account.account,name:l10n_be.2_a472
#: model:account.account.template,name:l10n_be.a472
msgid "Director's fees - Current financial period"
msgstr "Tantièmes de l'exercice"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a695
#: model:account.account,name:l10n_be.2_a695
#: model:account.account.template,name:l10n_be.a695
msgid "Directors' or managers' entitlements"
msgstr "Distribution aux administrateurs ou gérants"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a608
#: model:account.account,name:l10n_be.2_a608
#: model:account.account.template,name:l10n_be.a608
msgid ""
"Discounts, allowance and rebates received on purchase of raw materials, "
"consumables"
msgstr ""
"Remises, ristournes et rabais obtenus sur achat de marchandises et "
"fournitures"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a708
#: model:account.account,name:l10n_be.2_a708
#: model:account.account.template,name:l10n_be.a708
msgid "Discounts, allowances and rebates allowed"
msgstr "Remises, ristournes et rabais accordés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_471
#: model:account.group,name:l10n_be.2_be_group_471
#: model:account.group.template,name:l10n_be.be_group_471
msgid "Dividendes de l'exercice"
msgstr "Dividendes de l'exercice"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_470
#: model:account.group,name:l10n_be.2_be_group_470
#: model:account.group.template,name:l10n_be.be_group_470
msgid "Dividendes et tantièmes d'exercices antérieurs"
msgstr "Dividendes et tantièmes d'exercices antérieurs"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a694
#: model:account.account,name:l10n_be.2_a694
#: model:account.account.template,name:l10n_be.a694
msgid "Dividends"
msgstr "Rémunération de l'apport"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a471
#: model:account.account,name:l10n_be.2_a471
#: model:account.account.template,name:l10n_be.a471
msgid "Dividends - Current financial period"
msgstr "Dividendes de l'exercice"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a470
#: model:account.account,name:l10n_be.2_a470
#: model:account.account.template,name:l10n_be.a470
msgid "Dividends and director's fees relating to prior financial periods"
msgstr "Dividendes et tantièmes d'exercices antérieurs"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_692
#: model:account.group,name:l10n_be.2_be_group_692
#: model:account.group.template,name:l10n_be.be_group_692
msgid "Dotation aux réserves"
msgstr "Dotation aux réserves"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_630
#: model:account.group,name:l10n_be.2_be_group_630
#: model:account.group.template,name:l10n_be.be_group_630
msgid ""
"Dotations aux amortissements et aux réductions de valeur sur immobilisations"
msgstr ""
"Dotations aux amortissements et aux réductions de valeur sur immobilisations"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_070
#: model:account.group,name:l10n_be.2_be_group_070
#: model:account.group.template,name:l10n_be.be_group_070
msgid "Droits d'usage à long terme"
msgstr "Droits d'usage à long terme"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_09
#: model:account.group,name:l10n_be.2_be_group_09
#: model:account.group.template,name:l10n_be.be_group_09
msgid "Droits et engagements divers"
msgstr "Droits et engagements divers"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_0
#: model:account.group,name:l10n_be.2_be_group_0
#: model:account.group.template,name:l10n_be.be_group_0
msgid "Droits et engagements hors bilan"
msgstr "Droits et engagements hors bilan"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a093
#: model:account.account,name:l10n_be.2_a093
#: model:account.account.template,name:l10n_be.a093
msgid "Duties on loan conditions"
msgstr "Droits sur conditions concordataires"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_012
#: model:account.group,name:l10n_be.2_be_group_012
#: model:account.group.template,name:l10n_be.be_group_012
msgid "Débiteurs pour autres garanties personnelles"
msgstr "Débiteurs pour autres garanties personnelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_066
#: model:account.group,name:l10n_be.2_be_group_066
#: model:account.group.template,name:l10n_be.be_group_066
msgid "Débiteurs pour devises vendues à terme"
msgstr "Débiteurs pour devises vendues à terme"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_052
#: model:account.group,name:l10n_be.2_be_group_052
#: model:account.group.template,name:l10n_be.be_group_052
msgid "Débiteurs pour engagements de cession"
msgstr "Débiteurs pour engagements de cession"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_010
#: model:account.group,name:l10n_be.2_be_group_010
#: model:account.group.template,name:l10n_be.be_group_010
msgid "Débiteurs pour engagements sur effets en circulation"
msgstr "Débiteurs pour engagements sur effets en circulation"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_062
#: model:account.group,name:l10n_be.2_be_group_062
#: model:account.group.template,name:l10n_be.be_group_062
msgid "Débiteurs pour marchandises vendues à terme"
msgstr "Débiteurs pour marchandises vendues à terme"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_031
#: model:account.group,name:l10n_be.2_be_group_031
#: model:account.group.template,name:l10n_be.be_group_031
msgid "Déposants statutaires"
msgstr "Déposants statutaires"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_030
#: model:account.group,name:l10n_be.2_be_group_030
#: model:account.group.template,name:l10n_be.be_group_030
msgid "Dépôts statutaires"
msgstr "Dépôts statutaires"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_53
#: model:account.group,name:l10n_be.2_be_group_53
#: model:account.group.template,name:l10n_be.be_group_53
msgid "Dépôts à terme"
msgstr "Dépôts à terme"

#. module: l10n_be
#: model:account.fiscal.position,name:l10n_be.1_fiscal_position_template_5
#: model:account.fiscal.position,name:l10n_be.2_fiscal_position_template_5
#: model:account.fiscal.position.template,name:l10n_be.fiscal_position_template_5
msgid "EU privé"
msgstr "EU privé"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_655
#: model:account.group,name:l10n_be.2_be_group_655
#: model:account.group.template,name:l10n_be.be_group_655
msgid "Ecarts de conversion des devises"
msgstr "Ecarts de conversion des devises"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_441
#: model:account.group,name:l10n_be.2_be_group_441
#: model:account.group.template,name:l10n_be.be_group_441
msgid "Effets à payer"
msgstr "Effets à payer"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_401
#: model:account.group,name:l10n_be.2_be_group_401
#: model:account.group.template,name:l10n_be.be_group_401
msgid "Effets à recevoir"
msgstr "Effets à recevoir"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a696
#: model:account.account,name:l10n_be.2_a696
#: model:account.account.template,name:l10n_be.a696
msgid "Employees' entitlements"
msgstr "Distribution aux employés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a621
#: model:account.account,name:l10n_be.2_a621
#: model:account.account.template,name:l10n_be.a621
msgid "Employers' contribution for social security"
msgstr "Cotisations patronales d'assurances sociales"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a622
#: model:account.account,name:l10n_be.2_a622
#: model:account.account.template,name:l10n_be.a622
msgid "Employers' premiums for extra statutory insurance"
msgstr "Primes patronales pour assurances extralégales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_696
#: model:account.group,name:l10n_be.2_be_group_696
#: model:account.group.template,name:l10n_be.be_group_696
msgid "Employés"
msgstr "Employés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_171
#: model:account.group,name:l10n_be.2_be_group_171
#: model:account.group.template,name:l10n_be.be_group_171
msgid "Emprunts obligataires non subordonnés"
msgstr "Emprunts obligataires non subordonnés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_170
#: model:account.group,name:l10n_be.2_be_group_170
#: model:account.group.template,name:l10n_be.be_group_170
msgid "Emprunts subordonnés"
msgstr "Emprunts subordonnés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_32
#: model:account.group,name:l10n_be.2_be_group_32
#: model:account.group.template,name:l10n_be.be_group_32
msgid "En-cours de fabrication"
msgstr "En-cours de fabrication"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_050
#: model:account.group,name:l10n_be.2_be_group_050
#: model:account.group.template,name:l10n_be.be_group_050
msgid "Engagements d'acquisition"
msgstr "Engagements d'acquisition"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_05
#: model:account.group,name:l10n_be.2_be_group_05
#: model:account.group.template,name:l10n_be.be_group_05
msgid "Engagements d'acquisition et de cession d'immobilisations"
msgstr "Engagements d'acquisition et de cession d'immobilisations"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_053
#: model:account.group,name:l10n_be.2_be_group_053
#: model:account.group.template,name:l10n_be.be_group_053
msgid "Engagements de cession"
msgstr "Engagements de cession"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a450
#: model:account.account,name:l10n_be.2_a450
#: model:account.account.template,name:l10n_be.a450
msgid "Estimated taxes payable"
msgstr "Dettes fiscales estimées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4508
#: model:account.account,name:l10n_be.2_a4508
#: model:account.account.template,name:l10n_be.a4508
msgid "Estimated taxes payable - Foreign taxes"
msgstr "Dettes fiscales estimées - Impôts et taxes étrangers"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_173
#: model:account.group,name:l10n_be.1_be_group_55
#: model:account.group,name:l10n_be.2_be_group_173
#: model:account.group,name:l10n_be.2_be_group_55
#: model:account.group.template,name:l10n_be.be_group_173
#: model:account.group.template,name:l10n_be.be_group_55
msgid "Etablissements de crédit"
msgstr "Etablissements de crédit"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_432
#: model:account.group,name:l10n_be.2_be_group_432
#: model:account.group.template,name:l10n_be.be_group_432
msgid "Etablissements de crédit - Crédits d'acceptation"
msgstr "Etablissements de crédit - Crédits d'acceptation"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_433
#: model:account.group,name:l10n_be.2_be_group_433
#: model:account.group.template,name:l10n_be.be_group_433
msgid "Etablissements de crédit - Dettes en compte courant"
msgstr "Etablissements de crédit - Dettes en compte courant"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_430
#: model:account.group,name:l10n_be.2_be_group_430
#: model:account.group.template,name:l10n_be.be_group_430
msgid "Etablissements de crédit - Emprunts en compte à terme fixe"
msgstr "Etablissements de crédit - Emprunts en compte à terme fixe"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_431
#: model:account.group,name:l10n_be.2_be_group_431
#: model:account.group.template,name:l10n_be.be_group_431
msgid "Etablissements de crédit - Promesses"
msgstr "Etablissements de crédit - Promesses"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_444
#: model:account.group,name:l10n_be.2_be_group_444
#: model:account.group.template,name:l10n_be.be_group_444
msgid "Factures à recevoir"
msgstr "Factures à recevoir"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a654
#: model:account.account,name:l10n_be.2_a654
#: model:account.account.template,name:l10n_be.a654
msgid "Financial charges - Exchange differences"
msgstr "Charges financières - Différences de change"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a655
#: model:account.account,name:l10n_be.2_a655
#: model:account.account.template,name:l10n_be.a655
msgid "Financial charges - Foreign currency translation differences"
msgstr "Charges financières - Écarts de conversion des devises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a659
#: model:account.account,name:l10n_be.2_a659
#: model:account.account.template,name:l10n_be.a659
msgid "Financial charges carried to assets as restructuring costs"
msgstr ""
"Charges financières portées à l'actif au titre de frais de restructuration"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a754
#: model:account.account,name:l10n_be.2_a754
#: model:account.account.template,name:l10n_be.a754
msgid "Financial income - Exchange differences"
msgstr "Produits financiers - Différences de change"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a755
#: model:account.account,name:l10n_be.2_a755
#: model:account.account.template,name:l10n_be.a755
msgid "Financial income - Foreign currency translation differences"
msgstr "Produits financiers - Écarts de conversion des devises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a330
#: model:account.account,name:l10n_be.2_a330
#: model:account.account.template,name:l10n_be.a330
msgid "Finished goods - Acquisition value"
msgstr "Produits finis - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a339
#: model:account.account,name:l10n_be.2_a339
#: model:account.account.template,name:l10n_be.a339
msgid "Finished goods - amounts written down"
msgstr "Produits finis - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a520
#: model:account.account,name:l10n_be.2_a520
#: model:account.account.template,name:l10n_be.a520
msgid "Fixed income securities - Acquisition value"
msgstr "Titres à revenu fixe - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a529
#: model:account.account,name:l10n_be.2_a529
#: model:account.account.template,name:l10n_be.a529
msgid "Fixed income securities - Amounts written down"
msgstr "Titres à revenu fixe - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a539
#: model:account.account,name:l10n_be.2_a539
#: model:account.account.template,name:l10n_be.a539
msgid "Fixed term deposit - Amounts written down"
msgstr "Dépôts à terme - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a531
#: model:account.account,name:l10n_be.2_a531
#: model:account.account.template,name:l10n_be.a531
msgid "Fixed term deposit between one month and one year"
msgstr "Dépôts à terme de plus d'un mois et à un an au plus"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a530
#: model:account.account,name:l10n_be.2_a530
#: model:account.account.template,name:l10n_be.a530
msgid "Fixed term deposit over one year"
msgstr "Dépôts à terme de plus d'un an"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a532
#: model:account.account,name:l10n_be.2_a532
#: model:account.account.template,name:l10n_be.a532
msgid "Fixed term deposit up to one month"
msgstr "Dépôts à terme d'un mois au plus"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_1
#: model:account.group,name:l10n_be.2_be_group_1
#: model:account.group.template,name:l10n_be.be_group_1
msgid ""
"Fonds propres, provisions pour risques et charges et dettes à plus d'un an"
msgstr ""
"Fonds propres, provisions pour risques et charges et dettes à plus d'un an"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a496
#: model:account.account,name:l10n_be.2_a496
#: model:account.account.template,name:l10n_be.a496
msgid "Foreign currency translation differences - Assets"
msgstr "Écarts de conversion négatifs sur devises - Actif"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a497
#: model:account.account,name:l10n_be.2_a497
#: model:account.account.template,name:l10n_be.a497
msgid "Foreign currency translation differences - Liabilities"
msgstr "Écarts de conversion positifs sur devises - Passif"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1688
#: model:account.account,name:l10n_be.2_a1688
#: model:account.account.template,name:l10n_be.a1688
msgid "Foreign deferred taxes"
msgstr "Impôts différés étrangers"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a673
#: model:account.account,name:l10n_be.2_a673
#: model:account.account.template,name:l10n_be.a673
msgid "Foreign income taxes on the result of prior periods"
msgstr "Impôts étrangers sur le résultat d'exercices antérieurs"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a672
#: model:account.account,name:l10n_be.2_a672
#: model:account.account.template,name:l10n_be.a672
msgid "Foreign income taxes on the result of the current period"
msgstr "Impôts étrangers sur le résultat de l'exercice"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a200
#: model:account.account,name:l10n_be.2_a200
#: model:account.account.template,name:l10n_be.a200
msgid "Formation or capital increase expenses"
msgstr "Frais de constitution et d'augmentation de capital"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a064
#: model:account.account,name:l10n_be.2_a064
#: model:account.account.template,name:l10n_be.a064
msgid "Forward transactions - Currencies purchased (to be received)"
msgstr "Marché à terme - Devises achetées (à recevoir)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a067
#: model:account.account,name:l10n_be.2_a067
#: model:account.account.template,name:l10n_be.a067
msgid "Forward transactions - Currencies sold (to be delivered)"
msgstr "Marché à terme - Devises vendues (à livrer)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a060
#: model:account.account,name:l10n_be.2_a060
#: model:account.account.template,name:l10n_be.a060
msgid "Forward transactions - Goods purchased (to be received)"
msgstr "Marché à terme - Marchandises achetées (à recevoir)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a063
#: model:account.account,name:l10n_be.2_a063
#: model:account.account.template,name:l10n_be.a063
msgid "Forward transactions - Goods sold (to be delivered)"
msgstr "Marché à terme - Marchandises vendues (à livrer)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_440
#: model:account.group,name:l10n_be.2_be_group_440
#: model:account.group.template,name:l10n_be.be_group_440
msgid "Fournisseurs"
msgstr "Fournisseurs"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_201
#: model:account.group,name:l10n_be.2_be_group_201
#: model:account.group.template,name:l10n_be.be_group_201
msgid "Frais d'émission d'emprunts"
msgstr "Frais d'émission d'emprunts"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_20
#: model:account.group,name:l10n_be.2_be_group_20
#: model:account.group.template,name:l10n_be.be_group_20
msgid "Frais d'établissement"
msgstr "Frais d'établissement"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_2
#: model:account.group,name:l10n_be.2_be_group_2
#: model:account.group.template,name:l10n_be.be_group_2
msgid "Frais d'établissement, actifs immobilisés et créances à plus d'un an"
msgstr "Frais d'établissement, actifs immobilisés et créances à plus d'un an"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_200
#: model:account.group,name:l10n_be.2_be_group_200
#: model:account.group.template,name:l10n_be.be_group_200
msgid "Frais de constitution et d'augmentation de capital"
msgstr "Frais de constitution et d'augmentation de capital"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_210
#: model:account.group,name:l10n_be.2_be_group_210
#: model:account.group.template,name:l10n_be.be_group_210
msgid "Frais de recherche et de développement"
msgstr "Frais de recherche et de développement"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_204
#: model:account.group,name:l10n_be.2_be_group_204
#: model:account.group.template,name:l10n_be.be_group_204
msgid "Frais de restructuration"
msgstr "Frais de restructuration"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a24
#: model:account.account,name:l10n_be.2_a24
#: model:account.account.template,name:l10n_be.a24
msgid "Furniture and vehicles"
msgstr "Mobilier et matériel roulant"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a241
#: model:account.account,name:l10n_be.2_a241
#: model:account.account.template,name:l10n_be.a241
msgid ""
"Furniture and vehicles owned by the association or the foundation in full "
"property"
msgstr ""
"Mobilier et matériel roulant appartenant à l'association ou à la fondation "
"en pleine propriété"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a752
#: model:account.account,name:l10n_be.2_a752
#: model:account.account.template,name:l10n_be.a752
msgid "Gain on disposal of current assets"
msgstr "Plus-values sur réalisation d'actifs circulants"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a741
#: model:account.account,name:l10n_be.2_a741
#: model:account.account.template,name:l10n_be.a741
msgid "Gain on ordinary disposal of tangible fixed assets"
msgstr "Plus-values sur réalisations courantes d'immobilisations corporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a742
#: model:account.account,name:l10n_be.2_a742
#: model:account.account.template,name:l10n_be.a742
msgid "Gain on ordinary disposal of trade debtors"
msgstr "Plus-values sur réalisation de créances commerciales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_00
#: model:account.group,name:l10n_be.2_be_group_00
#: model:account.group.template,name:l10n_be.be_group_00
msgid "Garanties constituées par des tiers pour compte de l'entreprise"
msgstr "Garanties constituées par des tiers pour compte de l'entreprise"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_01
#: model:account.group,name:l10n_be.2_be_group_01
#: model:account.group.template,name:l10n_be.be_group_01
msgid "Garanties personnelles constituées pour compte de tiers"
msgstr "Garanties personnelles constituées pour compte de tiers"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_03
#: model:account.group,name:l10n_be.1_be_group_032
#: model:account.group,name:l10n_be.2_be_group_03
#: model:account.group,name:l10n_be.2_be_group_032
#: model:account.group.template,name:l10n_be.be_group_03
#: model:account.group.template,name:l10n_be.be_group_032
msgid "Garanties reçues"
msgstr "Garanties reçues"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_023
#: model:account.group,name:l10n_be.2_be_group_023
#: model:account.group.template,name:l10n_be.be_group_023
msgid "Garanties réelles constituées pour compte de tiers"
msgstr "Garanties réelles constituées pour compte de tiers"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_021
#: model:account.group,name:l10n_be.2_be_group_021
#: model:account.group.template,name:l10n_be.be_group_021
msgid "Garanties réelles constituées pour compte propre"
msgstr "Garanties réelles constituées pour compte propre"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_02
#: model:account.group,name:l10n_be.2_be_group_02
#: model:account.group.template,name:l10n_be.be_group_02
msgid "Garanties réelles constituées sur avoirs propres"
msgstr "Garanties réelles constituées sur avoirs propres"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a733
#: model:account.account,name:l10n_be.2_a733
#: model:account.account.template,name:l10n_be.a733
msgid "Gifts with a recovery right"
msgstr "Dons avec droit de reprise"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a732
#: model:account.account,name:l10n_be.2_a732
#: model:account.account.template,name:l10n_be.a732
msgid "Gifts without any recovery right"
msgstr "Dons sans droit de reprise"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a041
#: model:account.account,name:l10n_be.2_a041
#: model:account.account.template,name:l10n_be.a041
msgid ""
"Goods and securities held by third parties on their behalf but at the risk "
"and profit of the company"
msgstr ""
"Biens et valeurs détenus par des tiers en leur nom mais aux risques et "
"profits de l’entreprise"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a074
#: model:account.account,name:l10n_be.2_a074
#: model:account.account.template,name:l10n_be.a074
msgid ""
"Goods and securities held for accounts or at the risk and profit of third "
"parties"
msgstr ""
"Biens et valeurs détenus pour comptes ou aux risques et profits de tiers"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a072
#: model:account.account,name:l10n_be.2_a072
#: model:account.account.template,name:l10n_be.a072
msgid ""
"Goods and values ​​from third parties received on deposit, consignment or "
"custom"
msgstr "Biens et valeurs de tiers reçus en dépôt, en consignation ou à façon"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a340
#: model:account.account,name:l10n_be.2_a340
#: model:account.account.template,name:l10n_be.a340
msgid "Goods purchased for resale - Acquisition value"
msgstr "Marchandises - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a349
#: model:account.account,name:l10n_be.2_a349
#: model:account.account.template,name:l10n_be.a349
msgid "Goods purchased for resale - amounts written down"
msgstr "Marchandises - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a212
#: model:account.account,name:l10n_be.2_a212
#: model:account.account.template,name:l10n_be.a212
#: model:account.group,name:l10n_be.1_be_group_212
#: model:account.group,name:l10n_be.2_be_group_212
#: model:account.group.template,name:l10n_be.be_group_212
msgid "Goodwill"
msgstr "Goodwill"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a413
#: model:account.account,name:l10n_be.2_a413
#: model:account.account.template,name:l10n_be.a413
msgid "Grants receivable"
msgstr "Subsides à recevoir"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a032
#: model:account.account,name:l10n_be.2_a032
#: model:account.account.template,name:l10n_be.a032
msgid "Guarantees received"
msgstr "Garanties reçues"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a098
#: model:account.account,name:l10n_be.2_a098
#: model:account.account.template,name:l10n_be.a098
msgid "Holders of options (buying or selling securities)"
msgstr "Titulaires d’options (d’achat ou de vente sur titres)"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_operations_sortie
msgid "II A la sortie"
msgstr "II À la sortie"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_operations_entree
msgid "III A l'entrée"
msgstr "III À l'entrée"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_taxes_dues
msgid "IV Dues"
msgstr "IV Dues"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_35
#: model:account.group,name:l10n_be.2_be_group_35
#: model:account.group.template,name:l10n_be.be_group_35
msgid "Immeubles destinés à la vente"
msgstr "Immeubles destinés à la vente"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_21
#: model:account.group,name:l10n_be.2_be_group_21
#: model:account.group.template,name:l10n_be.be_group_21
msgid "Immobilisation incorporelles"
msgstr "Immobilisation incorporelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_27
#: model:account.group,name:l10n_be.2_be_group_27
#: model:account.group.template,name:l10n_be.be_group_27
msgid "Immobilisations corporelles en cours et acomptes versés"
msgstr "Immobilisations corporelles en cours et acomptes versés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_25
#: model:account.group,name:l10n_be.2_be_group_25
#: model:account.group.template,name:l10n_be.be_group_25
msgid "Immobilisations détenues en location-financement et droits similaires"
msgstr "Immobilisations détenues en location-financement et droits similaires"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_28
#: model:account.group,name:l10n_be.2_be_group_28
#: model:account.group.template,name:l10n_be.be_group_28
msgid "Immobilisations financières"
msgstr "Immobilisations financières"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a350
#: model:account.account,name:l10n_be.2_a350
#: model:account.account.template,name:l10n_be.a350
msgid "Immovable property intended for sale - Acquisition value"
msgstr "Immeubles destinés à la vente - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a359
#: model:account.account,name:l10n_be.2_a359
#: model:account.account.template,name:l10n_be.a359
msgid "Immovable property intended for sale - amounts written down"
msgstr "Immeubles destinés à la vente - Réductions de valeur actées"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_771
#: model:account.group,name:l10n_be.2_be_group_771
#: model:account.group.template,name:l10n_be.be_group_771
msgid "Impôts belges sur le résultat"
msgstr "Impôts belges sur le résultat"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_671
#: model:account.group,name:l10n_be.2_be_group_671
#: model:account.group.template,name:l10n_be.be_group_671
msgid "Impôts belges sur le résultat d'exercices antérieurs"
msgstr "Impôts belges sur le résultat d'exercices antérieurs"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_670
#: model:account.group,name:l10n_be.2_be_group_670
#: model:account.group.template,name:l10n_be.be_group_670
msgid "Impôts belges sur le résultat de l'exercice"
msgstr "Impôts belges sur le résultat de l'exercice"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_168
#: model:account.group,name:l10n_be.2_be_group_168
#: model:account.group.template,name:l10n_be.be_group_168
msgid "Impôts différés"
msgstr "Impôts différés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_412
#: model:account.group,name:l10n_be.2_be_group_412
#: model:account.group.template,name:l10n_be.be_group_412
msgid "Impôts et précomptes à récupérer"
msgstr "Impôts et précomptes à récupérer"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_452
#: model:account.group,name:l10n_be.2_be_group_452
#: model:account.group.template,name:l10n_be.be_group_452
msgid "Impôts et taxes à payer"
msgstr "Impôts et taxes à payer"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_67
#: model:account.group,name:l10n_be.2_be_group_67
#: model:account.group.template,name:l10n_be.be_group_67
msgid "Impôts sur le résultat"
msgstr "Impôts sur le résultat"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_773
#: model:account.group,name:l10n_be.2_be_group_773
#: model:account.group.template,name:l10n_be.be_group_773
msgid "Impôts étrangers sur le résultat"
msgstr "Impôts étrangers sur le résultat"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_673
#: model:account.group,name:l10n_be.2_be_group_673
#: model:account.group.template,name:l10n_be.be_group_673
msgid "Impôts étrangers sur le résultat d'exercices antérieurs"
msgstr "Impôts étrangers sur le résultat d'exercices antérieurs"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_672
#: model:account.group,name:l10n_be.2_be_group_672
#: model:account.group.template,name:l10n_be.be_group_672
msgid "Impôts étrangers sur le résultat de l'exercice"
msgstr "Impôts étrangers sur le résultat de l'exercice"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a751
#: model:account.account,name:l10n_be.2_a751
#: model:account.account.template,name:l10n_be.a751
msgid "Income from current assets"
msgstr "Produits des actifs circulants"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a750
#: model:account.account,name:l10n_be.2_a750
#: model:account.account.template,name:l10n_be.a750
msgid "Income from financial fixed assets"
msgstr "Produits des immobilisations financières"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7170
#: model:account.account,name:l10n_be.2_a7170
#: model:account.account.template,name:l10n_be.a7170
msgid "Increase (decrease) in contracts in progress - Acquisition value"
msgstr "Variation des commandes en cours d'exécution - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7171
#: model:account.account,name:l10n_be.2_a7171
#: model:account.account.template,name:l10n_be.a7171
msgid "Increase (decrease) in contracts in progress - Profit recognized"
msgstr ""
"Variation des commandes en cours d'exécution - Bénéfice pris en compte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a713
#: model:account.account,name:l10n_be.2_a713
#: model:account.account.template,name:l10n_be.a713
msgid "Increase (decrease) in stocks of finished goods"
msgstr "Variation des stocks de produits finis"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a71
#: model:account.account,name:l10n_be.2_a71
#: model:account.account.template,name:l10n_be.a71
msgid ""
"Increase (decrease) in stocks of finished goods and work and contracts in "
"progress"
msgstr "Variation des stocks et des commandes en cours d'exécution"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a715
#: model:account.account,name:l10n_be.2_a715
#: model:account.account.template,name:l10n_be.a715
msgid ""
"Increase (decrease) in stocks of immovable property constructed for resale"
msgstr "Variation des stocks des immeubles construits destinés à la vente"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a712
#: model:account.account,name:l10n_be.2_a712
#: model:account.account.template,name:l10n_be.a712
msgid "Increase (decrease) in work in progress"
msgstr "Augmentation (diminution) des stocks d'en-cours de fabrication"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_23
#: model:account.group,name:l10n_be.1_be_group_251
#: model:account.group,name:l10n_be.2_be_group_23
#: model:account.group,name:l10n_be.2_be_group_251
#: model:account.group.template,name:l10n_be.be_group_23
#: model:account.group.template,name:l10n_be.be_group_251
msgid "Installations, machines et outillage"
msgstr "Installations, machines et outillage"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a213
#: model:account.account,name:l10n_be.2_a213
#: model:account.account.template,name:l10n_be.a213
msgid "Intangible fixed assets - Advance payments"
msgstr "Immobilisations incorporelles - Acomptes versés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6500
#: model:account.account,name:l10n_be.2_a6500
#: model:account.account.template,name:l10n_be.a6500
msgid "Interests, commissions and other charges relating to debts"
msgstr "Intérêts, commissions et frais afférents aux dettes"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_794
#: model:account.group,name:l10n_be.2_be_group_794
#: model:account.group.template,name:l10n_be.be_group_794
msgid "Intervention d'associés (ou du propriétaire) dans la perte"
msgstr "Intervention d'associés (ou du propriétaire) dans la perte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a15
#: model:account.account,name:l10n_be.2_a15
#: model:account.account.template,name:l10n_be.a15
msgid "Investment grants"
msgstr "Subsides en capital"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a753
#: model:account.account,name:l10n_be.2_a753
#: model:account.account.template,name:l10n_be.a753
msgid "Investment grants and interest subsidies"
msgstr "Subsides en capital et en intérêts"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a151
#: model:account.account,name:l10n_be.2_a151
#: model:account.account.template,name:l10n_be.a151
msgid "Investment grants received in cash"
msgstr "Subsides en capital reçus en espèces"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a152
#: model:account.account,name:l10n_be.2_a152
#: model:account.account.template,name:l10n_be.a152
msgid "Investment grants received in kind"
msgstr "Subsides en capital reçus en nature"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a444
#: model:account.account,name:l10n_be.2_a444
#: model:account.account.template,name:l10n_be.a444
msgid "Invoices to be received payable within one year"
msgstr "Factures à recevoir à un an au plus"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a100
#: model:account.account,name:l10n_be.2_a100
#: model:account.account.template,name:l10n_be.a100
msgid "Issued capital"
msgstr "Capital souscrit"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_move
msgid "Journal Entry"
msgstr "Écriture comptable"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a220
#: model:account.account,name:l10n_be.2_a220
#: model:account.account.template,name:l10n_be.a220
msgid "Land"
msgstr "Terrains"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2201
#: model:account.account,name:l10n_be.2_a2201
#: model:account.account.template,name:l10n_be.a2201
msgid "Land owned by the association or the foundation in full property"
msgstr ""
"Terrains appartenant à l'association ou à la fondation en pleine propriété"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a422
#: model:account.account,name:l10n_be.2_a422
#: model:account.account.template,name:l10n_be.a422
msgid ""
"Leasing and similar obligations payable after more than one year falling due"
" within one year"
msgstr ""
"Dettes de location-financement et dettes assimilées à plus d'un an échéant "
"dans l'année"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a252
#: model:account.account,name:l10n_be.2_a252
#: model:account.account.template,name:l10n_be.a252
msgid "Leasing and similar rights - Furniture and vehicles"
msgstr ""
"Location-financement et droits similaires - Mobilier et matériel roulant"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a250
#: model:account.account,name:l10n_be.2_a250
#: model:account.account.template,name:l10n_be.a250
msgid "Leasing and similar rights - Land and buildings"
msgstr "Location-financement et droits similaires - Terrains et constructions"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a251
#: model:account.account,name:l10n_be.2_a251
#: model:account.account.template,name:l10n_be.a251
msgid "Leasing and similar rights - Plant, machinery and equipment"
msgstr ""
"Location-financement et droits similaires - Installations, machines et "
"outillage"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a735
#: model:account.account,name:l10n_be.2_a735
#: model:account.account.template,name:l10n_be.a735
msgid "Legacies with a recovery right"
msgstr "Legs avec droit de reprise"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a734
#: model:account.account,name:l10n_be.2_a734
#: model:account.account.template,name:l10n_be.a734
msgid "Legacies without any recovery right"
msgstr "Legs sans droit de reprise"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a130
#: model:account.account,name:l10n_be.2_a130
#: model:account.account.template,name:l10n_be.a130
msgid "Legal reserve"
msgstr "Réserve légale"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a487
#: model:account.account,name:l10n_be.2_a487
#: model:account.account.template,name:l10n_be.a487
msgid "Lent securities to return"
msgstr "Titres empruntés à restituer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_l10nbe_chart_template_liquidity_transfer
#: model:account.account,name:l10n_be.2_l10nbe_chart_template_liquidity_transfer
#: model:account.account.template,name:l10n_be.l10nbe_chart_template_liquidity_transfer
msgid "Liquidity Transfer"
msgstr "Transfert de liquidités"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a201
#: model:account.account,name:l10n_be.2_a201
#: model:account.account.template,name:l10n_be.a201
msgid "Loan issue expenses"
msgstr "Frais d'émission d'emprunts"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a0702
#: model:account.account,name:l10n_be.2_a0702
#: model:account.account.template,name:l10n_be.a0702
msgid "Long-term usage rights - On furniture and rolling stock"
msgstr "Droits d’usage à long terme - Sur mobilier et matériel roulant"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a0701
#: model:account.account,name:l10n_be.2_a0701
#: model:account.account.template,name:l10n_be.a0701
msgid "Long-term usage rights - On installations, machines and tools"
msgstr ""
"Droits d’usage à long terme - Sur installations, machines et outillage"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a0700
#: model:account.account,name:l10n_be.2_a0700
#: model:account.account.template,name:l10n_be.a0700
msgid "Long-term usage rights - On land and buildings"
msgstr "Droits d’usage à long terme - Sur terrains et constructions"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a690
#: model:account.account,name:l10n_be.2_a690
#: model:account.account.template,name:l10n_be.a690
msgid "Loss brought forward from previous year"
msgstr "Perte reportée de l'exercice précédent"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a141
#: model:account.account,name:l10n_be.2_a141
#: model:account.account.template,name:l10n_be.a141
msgid "Loss carried forward"
msgstr "Perte reportée"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a641
#: model:account.account,name:l10n_be.2_a641
#: model:account.account.template,name:l10n_be.a641
msgid "Loss on ordinary disposal of tangible fixed assets"
msgstr "Moins-values sur réalisations courantes d'immobilisations corporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a642
#: model:account.account,name:l10n_be.2_a642
#: model:account.account.template,name:l10n_be.a642
msgid "Loss on ordinary disposal of trade debtors"
msgstr "Moins-values sur réalisations courantes de créances commerciales"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a652
#: model:account.account,name:l10n_be.2_a652
#: model:account.account.template,name:l10n_be.a652
msgid "Losses on disposal of current assets"
msgstr "Moins-values sur réalisations d'actifs circulants"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a793
#: model:account.account,name:l10n_be.2_a793
#: model:account.account.template,name:l10n_be.a793
msgid "Losses to be carried forward"
msgstr "Perte à reporter"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_34
#: model:account.group,name:l10n_be.2_be_group_34
#: model:account.group.template,name:l10n_be.be_group_34
msgid "Marchandises"
msgstr "Marchandises"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_060
#: model:account.group,name:l10n_be.2_be_group_060
#: model:account.group.template,name:l10n_be.be_group_060
msgid "Marchandises achetées à terme - à recevoir"
msgstr "Marchandises achetées à terme - à recevoir"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_063
#: model:account.group,name:l10n_be.2_be_group_063
#: model:account.group.template,name:l10n_be.be_group_063
msgid "Marchandises vendues à terme - à livrer"
msgstr "Marchandises vendues à terme - à livrer"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_06
#: model:account.group,name:l10n_be.2_be_group_06
#: model:account.group.template,name:l10n_be.be_group_06
msgid "Marchés à terme"
msgstr "Marchés à terme"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a429
#: model:account.account,name:l10n_be.2_a429
#: model:account.account.template,name:l10n_be.a429
msgid ""
"Miscellaneous amounts payable after more than one year falling due within "
"one year"
msgstr "Dettes diverses à plus d'un an échéant dans l'année"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1792
#: model:account.account,name:l10n_be.2_a1792
#: model:account.account.template,name:l10n_be.a1792
msgid ""
"Miscellaneous amounts payable with a remaining term of more than one year - "
"Cash Deposit"
msgstr "Dettes diverses à plus d'un an - Cautionnements reçus en numéraire"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1790
#: model:account.account,name:l10n_be.2_a1790
#: model:account.account.template,name:l10n_be.a1790
msgid ""
"Miscellaneous amounts payable with a remaining term of more than one year - "
"Interest-bearing"
msgstr "Dettes diverses à plus d'un an - Productives d'intérêts"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1791
#: model:account.account,name:l10n_be.2_a1791
#: model:account.account.template,name:l10n_be.a1791
msgid ""
"Miscellaneous amounts payable with a remaining term of more than one year - "
"Non interest-bearing or with an abnormally low interest rate"
msgstr ""
"Dettes diverses à plus d'un an - Non productives d'intérêts ou assorties "
"d'un intérêt anormalement faible"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a480
#: model:account.account,name:l10n_be.2_a480
#: model:account.account.template,name:l10n_be.a480
msgid ""
"Miscellaneous amounts payable within one year - Debentures and matured "
"coupons"
msgstr "Dettes diverses à un an au plus - Obligations et coupons échus"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a483
#: model:account.account,name:l10n_be.2_a483
#: model:account.account.template,name:l10n_be.a483
msgid "Miscellaneous amounts payable within one year - Grants to repay"
msgstr "Dettes diverses à un an au plus - Subsides à rembourser"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a488
#: model:account.account,name:l10n_be.2_a488
#: model:account.account.template,name:l10n_be.a488
msgid ""
"Miscellaneous amounts payable within one year - Guarantees received in cash"
msgstr "Dettes diverses à un an au plus - Cautionnements reçus en numéraire"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4890
#: model:account.account,name:l10n_be.2_a4890
#: model:account.account.template,name:l10n_be.a4890
msgid ""
"Miscellaneous amounts payable within one year - Sundry interest-bearing "
"amounts payable"
msgstr ""
"Dettes diverses à un an au plus - Autres dettes productives d'intérêts"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4891
#: model:account.account,name:l10n_be.2_a4891
#: model:account.account.template,name:l10n_be.a4891
msgid ""
"Miscellaneous amounts payable within one year - Sundry non interest-bearing "
"amounts payable or with an abnormally low interest rate"
msgstr ""
"Dettes diverses à un an au plus - Autres dettes non productives d'intérêts "
"ou assorties d'un intérêt anormalement faible"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_24
#: model:account.group,name:l10n_be.1_be_group_252
#: model:account.group,name:l10n_be.2_be_group_24
#: model:account.group,name:l10n_be.2_be_group_252
#: model:account.group.template,name:l10n_be.be_group_24
#: model:account.group.template,name:l10n_be.be_group_252
msgid "Mobilier et matériel roulant"
msgstr "Mobilier et matériel roulant"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_652
#: model:account.group,name:l10n_be.2_be_group_652
#: model:account.group.template,name:l10n_be.be_group_652
msgid "Moins-values sur réalisation d'actifs circulants"
msgstr "Moins-values sur réalisation d'actifs circulants"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_663
#: model:account.group,name:l10n_be.2_be_group_663
#: model:account.group.template,name:l10n_be.be_group_663
msgid "Moins-values sur réalisation d'actifs immobilisés"
msgstr "Moins-values sur réalisation d'actifs immobilisés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_641
#: model:account.group,name:l10n_be.2_be_group_641
#: model:account.group.template,name:l10n_be.be_group_641
msgid "Moins-values sur réalisations courantes d'immobilisations corporelles"
msgstr "Moins-values sur réalisations courantes d'immobilisations corporelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_642
#: model:account.group,name:l10n_be.2_be_group_642
#: model:account.group.template,name:l10n_be.be_group_642
msgid "Moins-values sur réalisations de créances commerciales"
msgstr "Moins-values sur réalisations de créances commerciales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_511
#: model:account.group,name:l10n_be.2_be_group_511
#: model:account.group.template,name:l10n_be.be_group_511
msgid "Montants non appelés (-)"
msgstr "Montants non appelés (-)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a64012
#: model:account.account,name:l10n_be.2_a64012
#: model:account.account.template,name:l10n_be.a64012
msgid "Non deductible taxes"
msgstr "TVA non déductible"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2915
#: model:account.account,name:l10n_be.2_a2915
#: model:account.account.template,name:l10n_be.a2915
msgid ""
"Non interest-bearing amounts receivable after more than one year or with an "
"abnormally low interest rate"
msgstr ""
"Créances à plus d'un an non productives d'intérêts ou assorties d'un intérêt"
" anormalement faible"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a415
#: model:account.account,name:l10n_be.2_a415
#: model:account.account.template,name:l10n_be.a415
msgid ""
"Non interest-bearing amounts receivable within one year or with an "
"abnormally low interest rate"
msgstr ""
"Créances à un an au plus non productives d'intérêts ou assorties d'un "
"intérêt anormalement faible"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6600
#: model:account.account,name:l10n_be.2_a6600
#: model:account.account.template,name:l10n_be.a6600
msgid ""
"Non-recurring depreciation of and amounts written off formation expenses"
msgstr ""
"Amortissements et réductions de valeur non récurrents sur frais "
"d'établissement"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6601
#: model:account.account,name:l10n_be.2_a6601
#: model:account.account.template,name:l10n_be.a6601
msgid ""
"Non-recurring depreciation of and amounts written off intangible fixed "
"assets"
msgstr ""
"Amortissements et réductions de valeur non récurrents sur immobilisations "
"incorporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6602
#: model:account.account,name:l10n_be.2_a6602
#: model:account.account.template,name:l10n_be.a6602
msgid ""
"Non-recurring depreciation of and amounts written off tangible fixed assets"
msgstr ""
"Amortissements et réductions de valeur non récurrents sur immobilisations "
"corporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6691
#: model:account.account,name:l10n_be.2_a6691
#: model:account.account.template,name:l10n_be.a6691
msgid ""
"Non-recurring financial charges carried to assets as restructuring costs"
msgstr ""
"Charges financières non récurrentes portées à l'actif au titre de frais de "
"restructuration"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6690
#: model:account.account,name:l10n_be.2_a6690
#: model:account.account.template,name:l10n_be.a6690
msgid ""
"Non-recurring operating charges carried to assets as restructuring costs"
msgstr ""
"Charges d'exploitation non récurrentes portées à l'actif au titre de frais "
"de restructuration"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_480
#: model:account.group,name:l10n_be.2_be_group_480
#: model:account.group.template,name:l10n_be.be_group_480
msgid "Obligations et coupons échus"
msgstr "Obligations et coupons échus"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_56
#: model:account.group,name:l10n_be.2_be_group_56
#: model:account.group.template,name:l10n_be.be_group_56
msgid "Office des chèques postaux"
msgstr "Office des chèques postaux"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_454
#: model:account.group,name:l10n_be.2_be_group_454
#: model:account.group.template,name:l10n_be.be_group_454
msgid "Office national de la sécurité sociale"
msgstr "Office national de la Sécurité sociale"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a094
#: model:account.account,name:l10n_be.2_a094
#: model:account.account.template,name:l10n_be.a094
msgid "Ongoing litigation"
msgstr "Litiges en cours"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a737
#: model:account.account,name:l10n_be.2_a737
#: model:account.account.template,name:l10n_be.a737
msgid "Operating Subsidies"
msgstr "Subsides d'exploitation"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a643
#: model:account.account,name:l10n_be.2_a643
#: model:account.account.template,name:l10n_be.a643
msgid "Operating charges - Gifts"
msgstr "Charges d'exploitation - Dons"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6431
#: model:account.account,name:l10n_be.2_a6431
#: model:account.account.template,name:l10n_be.a6431
msgid "Operating charges - Gifts with a recovery right"
msgstr "Charges d'exploitation - Dons avec droit de reprise"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6432
#: model:account.account,name:l10n_be.2_a6432
#: model:account.account.template,name:l10n_be.a6432
msgid "Operating charges - Gifts without any recovery right"
msgstr "Charges d'exploitation - Dons sans droit de reprise"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a649
#: model:account.account,name:l10n_be.2_a649
#: model:account.account.template,name:l10n_be.a649
msgid "Operating charges carried to assets as restructuring costs"
msgstr ""
"Charges d'exploitation portées à l'actif au titre de frais de "
"restructuration"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a740
#: model:account.account,name:l10n_be.2_a740
#: model:account.account.template,name:l10n_be.a740
msgid "Operating subsidies and compensatory amounts"
msgstr "Subsides d'exploitation et montants compensatoires"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a099
#: model:account.account,name:l10n_be.2_a099
#: model:account.account.template,name:l10n_be.a099
msgid "Options (buy or sell) on securities issued."
msgstr "Options (d’achat ou de vente) sur titres émis."

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_operations
msgid "Opérations"
msgstr "Opérations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a668
#: model:account.account,name:l10n_be.2_a668
#: model:account.account.template,name:l10n_be.a668
msgid "Other  non-recurring financial charges"
msgstr "Autres charges financières non récurrentes"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a769
#: model:account.account,name:l10n_be.2_a769
#: model:account.account.template,name:l10n_be.a769
msgid "Other  non-recurring financial income"
msgstr "Autres produits financiers non récurrents"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a473
#: model:account.account,name:l10n_be.2_a473
#: model:account.account.template,name:l10n_be.a473
msgid "Other allocations"
msgstr "Autres allocations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a697
#: model:account.account,name:l10n_be.2_a697
#: model:account.account.template,name:l10n_be.a697
msgid "Other allocations entitlements"
msgstr "Distribution aux autres allocataires"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2919
#: model:account.account,name:l10n_be.2_a2919
#: model:account.account.template,name:l10n_be.a2919
msgid ""
"Other amounts receivable after more than one year - Amounts written down"
msgstr "Autres créances à plus d'un an - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2911
#: model:account.account,name:l10n_be.2_a2911
#: model:account.account.template,name:l10n_be.a2911
msgid "Other amounts receivable after more than one year - Bills receivable"
msgstr "Autres créances à plus d'un an - Effets à recevoir"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2910
#: model:account.account,name:l10n_be.2_a2910
#: model:account.account.template,name:l10n_be.a2910
msgid "Other amounts receivable after more than one year - Current account"
msgstr "Autres créances à plus d'un an - Créances en compte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2917
#: model:account.account,name:l10n_be.2_a2917
#: model:account.account.template,name:l10n_be.a2917
msgid "Other amounts receivable after more than one year - Doubtful amounts"
msgstr "Autres créances à plus d'un an - Créances douteuses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2817
#: model:account.account,name:l10n_be.2_a2817
#: model:account.account.template,name:l10n_be.a2817
msgid ""
"Other amounts receivable from affiliated enterprises - Doubtful amounts"
msgstr "Créances dans les entreprises liées - Créances douteuses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a419
#: model:account.account,name:l10n_be.2_a419
#: model:account.account.template,name:l10n_be.a419
msgid "Other amounts receivable within one year - Amounts written down"
msgstr "Autres créances à un an au plus - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a417
#: model:account.account,name:l10n_be.2_a417
#: model:account.account.template,name:l10n_be.a417
msgid "Other amounts receivable within one year - Doubtful amounts"
msgstr "Autres créances à un an au plus - Créances douteuses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a418
#: model:account.account,name:l10n_be.2_a418
#: model:account.account.template,name:l10n_be.a418
msgid "Other amounts receivable within one year - Guarantees paid in cash"
msgstr "Autres créances à un an au plus - Cautionnements versés en numéraire"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a414
#: model:account.account,name:l10n_be.2_a414
#: model:account.account.template,name:l10n_be.a414
msgid "Other amounts receivable within one year - Income receivable"
msgstr "Autres créances à un an au plus - Produits à recevoir"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a416
#: model:account.account,name:l10n_be.2_a416
#: model:account.account.template,name:l10n_be.a416
msgid "Other amounts receivable within one year - Sundry amounts"
msgstr "Autres créances à un an au plus - Créances diverses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2212
#: model:account.account,name:l10n_be.2_a2212
#: model:account.account.template,name:l10n_be.a2212
msgid "Other building"
msgstr "Autres constructions"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2222
#: model:account.account,name:l10n_be.2_a2222
#: model:account.account.template,name:l10n_be.a2222
msgid "Other built-up lands"
msgstr "Autres terrains bâtis"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6502
#: model:account.account,name:l10n_be.2_a6502
#: model:account.account.template,name:l10n_be.a6502
msgid "Other debt charges"
msgstr "Autres charges des dettes"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2859
#: model:account.account,name:l10n_be.2_a2859
#: model:account.account.template,name:l10n_be.a2859
msgid "Other financial assets - Amounts written down"
msgstr ""
"Autres immobilisations financières - Créances - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2851
#: model:account.account,name:l10n_be.2_a2851
#: model:account.account.template,name:l10n_be.a2851
msgid "Other financial assets - Bills receivable"
msgstr "Autres immobilisations financières - Effets à recevoir"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a288
#: model:account.account,name:l10n_be.2_a288
#: model:account.account.template,name:l10n_be.a288
msgid "Other financial assets - Cash Guarantees"
msgstr ""
"Autres immobilisations financières - Cautionnements versés en numéraire"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2850
#: model:account.account,name:l10n_be.2_a2850
#: model:account.account.template,name:l10n_be.a2850
msgid "Other financial assets - Current account"
msgstr "Autres immobilisations financières - Créances en compte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2857
#: model:account.account,name:l10n_be.2_a2857
#: model:account.account.template,name:l10n_be.a2857
msgid "Other financial assets - Doubtful amounts"
msgstr "Autres immobilisations financières - Créances douteuses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2852
#: model:account.account,name:l10n_be.2_a2852
#: model:account.account.template,name:l10n_be.a2852
msgid "Other financial assets - Fixed income securities"
msgstr "Autres immobilisations financières - Titres à revenu fixe"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a202
#: model:account.account,name:l10n_be.2_a202
#: model:account.account.template,name:l10n_be.a202
msgid "Other formation expenses"
msgstr "Autres frais d'établissement"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a242
#: model:account.account,name:l10n_be.2_a242
#: model:account.account.template,name:l10n_be.a242
msgid "Other furniture and vehicles"
msgstr "Autre mobilier et matériel roulant"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2202
#: model:account.account,name:l10n_be.2_a2202
#: model:account.account.template,name:l10n_be.a2202
msgid "Other land"
msgstr "Autres terrains"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a424
#: model:account.account,name:l10n_be.2_a424
#: model:account.account.template,name:l10n_be.a424
msgid ""
"Other loans payable after more than one year falling due within one year"
msgstr "Autres emprunts à plus d'un an échéant dans l'année"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a439
#: model:account.account,name:l10n_be.2_a439
#: model:account.account.template,name:l10n_be.a439
msgid "Other loans payable within one year"
msgstr "Autres emprunts à un an au plus"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a174
#: model:account.account,name:l10n_be.2_a174
#: model:account.account.template,name:l10n_be.a174
msgid "Other loans with a remaining term of more than one year"
msgstr "Autres emprunts à plus d'un an"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2840
#: model:account.account,name:l10n_be.2_a2840
#: model:account.account.template,name:l10n_be.a2840
msgid "Other participating interests and shares - Acquisition value"
msgstr "Autres participations, actions et parts - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2849
#: model:account.account,name:l10n_be.2_a2849
#: model:account.account.template,name:l10n_be.a2849
msgid "Other participating interests and shares - Amounts written down"
msgstr "Autres participations, actions et parts - Réductions de valeur"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2848
#: model:account.account,name:l10n_be.2_a2848
#: model:account.account.template,name:l10n_be.a2848
msgid "Other participating interests and shares - Revaluation surpluses"
msgstr "Autres participations, actions et parts - Plus-values"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2841
#: model:account.account,name:l10n_be.2_a2841
#: model:account.account.template,name:l10n_be.a2841
msgid "Other participating interests and shares - Uncalled amounts"
msgstr "Autres participations, actions et parts - Montants non appelés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a623
#: model:account.account,name:l10n_be.2_a623
#: model:account.account.template,name:l10n_be.a623
msgid "Other personnel costs"
msgstr "Autres frais de personnel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a232
#: model:account.account,name:l10n_be.2_a232
#: model:account.account.template,name:l10n_be.a232
msgid "Other plant, machinery and equipment"
msgstr "Autres installations, machines et outillage"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1311
#: model:account.account,name:l10n_be.2_a1311
#: model:account.account.template,name:l10n_be.a1311
msgid "Other reserves not available"
msgstr "Autres réserves indisponibles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a223
#: model:account.account,name:l10n_be.2_a223
#: model:account.account.template,name:l10n_be.a223
msgid "Other rights to immovable property"
msgstr "Autres droits réels sur des immeubles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2232
#: model:account.account,name:l10n_be.2_a2232
#: model:account.account.template,name:l10n_be.a2232
msgid "Other rights to immovable property - Other"
msgstr "Autres droits réels sur des immeubles - Autres"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2231
#: model:account.account,name:l10n_be.2_a2231
#: model:account.account.template,name:l10n_be.a2231
msgid ""
"Other rights to immovable property belonging to the association or the "
"foundation in full property"
msgstr ""
"Autres droits réels sur des immeubles appartenant à l'association ou à la "
"fondation en pleine propriété"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a26
#: model:account.account,name:l10n_be.2_a26
#: model:account.account.template,name:l10n_be.a26
msgid "Other tangible fixed assets"
msgstr "Autres immobilisations corporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a262
#: model:account.account,name:l10n_be.2_a262
#: model:account.account.template,name:l10n_be.a262
msgid "Other tangible fixed assets - Other"
msgstr "Autres immobilisations corporelles - Autres"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a261
#: model:account.account,name:l10n_be.2_a261
#: model:account.account.template,name:l10n_be.a261
msgid ""
"Other tangible fixed assets owned by the association or the foundation in "
"full property"
msgstr ""
"Autres immobilisations corporelles appartenant à l'association ou à la "
"fondation en pleine propriété"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a72
#: model:account.account,name:l10n_be.2_a72
#: model:account.account.template,name:l10n_be.a72
msgid "Own work capitalised"
msgstr "Production immobilisée"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a794
#: model:account.account,name:l10n_be.2_a794
#: model:account.account.template,name:l10n_be.a794
msgid "Owners' contribution in respect of losses"
msgstr "Intervention d'associés dans la perte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2800
#: model:account.account,name:l10n_be.2_a2800
#: model:account.account.template,name:l10n_be.a2800
msgid ""
"Participating interests and shares in associated enterprises - Acquisition "
"value"
msgstr ""
"Participations, actions et parts dans des entreprises liées - Valeur "
"d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2809
#: model:account.account,name:l10n_be.2_a2809
#: model:account.account.template,name:l10n_be.a2809
msgid ""
"Participating interests and shares in associated enterprises - Amounts "
"written down"
msgstr ""
"Participations, actions et parts dans des entreprises liées - Réductions de "
"valeur"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2808
#: model:account.account,name:l10n_be.2_a2808
#: model:account.account.template,name:l10n_be.a2808
msgid ""
"Participating interests and shares in associated enterprises - Revaluation "
"surpluses"
msgstr ""
"Participations, actions et parts dans des entreprises liées - Plus-values"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2801
#: model:account.account,name:l10n_be.2_a2801
#: model:account.account.template,name:l10n_be.a2801
msgid ""
"Participating interests and shares in associated enterprises - Uncalled "
"amounts"
msgstr ""
"Participations, actions et parts dans des entreprises liées - Montants non "
"appelés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2820
#: model:account.account,name:l10n_be.2_a2820
#: model:account.account.template,name:l10n_be.a2820
msgid ""
"Participating interests and shares in enterprises linked by a participating "
"interest - Acquisition value"
msgstr ""
"Participations, actions et parts dans des entreprises avec lesquelles il "
"existe un lien de participation - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2829
#: model:account.account,name:l10n_be.2_a2829
#: model:account.account.template,name:l10n_be.a2829
msgid ""
"Participating interests and shares in enterprises linked by a participating "
"interest - Amounts written down"
msgstr ""
"Participations, actions et parts dans des entreprises avec lesquelles il "
"existe un lien de participation - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2828
#: model:account.account,name:l10n_be.2_a2828
#: model:account.account.template,name:l10n_be.a2828
msgid ""
"Participating interests and shares in enterprises linked by a participating "
"interest - Revaluation surpluses"
msgstr ""
"Participations, actions et parts dans des entreprises avec lesquelles il "
"existe un lien de participation - Plus-values actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2821
#: model:account.account,name:l10n_be.2_a2821
#: model:account.account.template,name:l10n_be.a2821
msgid ""
"Participating interests and shares in enterprises linked by a participating "
"interest - Uncalled amounts"
msgstr ""
"Participations, actions et parts dans des entreprises avec lesquelles il "
"existe un lien de participation - Montants non appelés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_282
#: model:account.group,name:l10n_be.2_be_group_282
#: model:account.group.template,name:l10n_be.be_group_282
msgid ""
"Participations dans des entreprises avec lesquelles il existe un lien de "
"participation"
msgstr ""
"Participations dans des entreprises avec lesquelles il existe un lien de "
"participation"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_280
#: model:account.group,name:l10n_be.2_be_group_280
#: model:account.group.template,name:l10n_be.be_group_280
msgid "Participations dans des entreprises liées"
msgstr "Participations dans des entreprises liées"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_624
#: model:account.group,name:l10n_be.2_be_group_624
#: model:account.group.template,name:l10n_be.be_group_624
msgid "Pensions de retraite et de survie"
msgstr "Pensions de retraite et de survie"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_617
#: model:account.group,name:l10n_be.2_be_group_617
#: model:account.group.template,name:l10n_be.be_group_617
msgid ""
"Personnel intérimaire et personnes mises à la disposition de l'entreprise"
msgstr ""
"Personnel intérimaire et personnes mises à la disposition de l'entreprise"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_690
#: model:account.group,name:l10n_be.2_be_group_690
#: model:account.group.template,name:l10n_be.be_group_690
msgid "Perte reportée de l'exercice précédent"
msgstr "Perte reportée de l'exercice précédent"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_793
#: model:account.group,name:l10n_be.2_be_group_793
#: model:account.group.template,name:l10n_be.be_group_793
msgid "Perte à reporter"
msgstr "Perte à reporter"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_5101
#: model:account.group,name:l10n_be.1_be_group_5191
#: model:account.group,name:l10n_be.2_be_group_5101
#: model:account.group,name:l10n_be.2_be_group_5191
#: model:account.group.template,name:l10n_be.be_group_5101
#: model:account.group.template,name:l10n_be.be_group_5191
msgid "Placements de trésorerie autres que placements à revenu fixe"
msgstr "Placements de trésorerie autres que placements à revenu fixe"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_5
#: model:account.group,name:l10n_be.2_be_group_5
#: model:account.group.template,name:l10n_be.be_group_5
msgid "Placements de trésorerie et valeurs disponibles"
msgstr "Placements de trésorerie et valeurs disponibles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a23
#: model:account.account,name:l10n_be.2_a23
#: model:account.account.template,name:l10n_be.a23
msgid "Plant, machinery and equipment"
msgstr "Installations, machines et outillage"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a231
#: model:account.account,name:l10n_be.2_a231
#: model:account.account.template,name:l10n_be.a231
msgid ""
"Plant, machinery and equipment owned by the association or the foundation in"
" full property"
msgstr ""
"Installations, machines et outillage appartenant à l'association ou à la "
"fondation en pleine propriété"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_12
#: model:account.group,name:l10n_be.2_be_group_12
#: model:account.group.template,name:l10n_be.be_group_12
msgid "Plus-values de réévaluation"
msgstr "Plus-values de réévaluation"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_121
#: model:account.group,name:l10n_be.2_be_group_121
#: model:account.group.template,name:l10n_be.be_group_121
msgid "Plus-values de réévaluation sur immobilisations corporelles"
msgstr "Plus-values de réévaluation sur immobilisations corporelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_122
#: model:account.group,name:l10n_be.2_be_group_122
#: model:account.group.template,name:l10n_be.be_group_122
msgid "Plus-values de réévaluation sur immobilisations financières"
msgstr "Plus-values de réévaluation sur immobilisations financières"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_120
#: model:account.group,name:l10n_be.2_be_group_120
#: model:account.group.template,name:l10n_be.be_group_120
msgid "Plus-values de réévaluation sur immobilisations incorporelles"
msgstr "Plus-values de réévaluation sur immobilisations incorporelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_123
#: model:account.group,name:l10n_be.2_be_group_123
#: model:account.group.template,name:l10n_be.be_group_123
msgid "Plus-values de réévaluation sur stocks"
msgstr "Plus-values de réévaluation sur stocks"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_752
#: model:account.group,name:l10n_be.2_be_group_752
#: model:account.group.template,name:l10n_be.be_group_752
msgid "Plus-values sur réalisation d'actifs circulants"
msgstr "Plus-values sur réalisation d'actifs circulants"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_763
#: model:account.group,name:l10n_be.2_be_group_763
#: model:account.group.template,name:l10n_be.be_group_763
msgid "Plus-values sur réalisation d'actifs immobilisés"
msgstr "Plus-values sur réalisation d'actifs immobilisés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_742
#: model:account.group,name:l10n_be.2_be_group_742
#: model:account.group.template,name:l10n_be.be_group_742
msgid "Plus-values sur réalisation de créances commerciales"
msgstr "Plus-values sur réalisation de créances commerciales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_741
#: model:account.group,name:l10n_be.2_be_group_741
#: model:account.group.template,name:l10n_be.be_group_741
msgid "Plus-values sur réalisations courantes d'immobilisations corporelles"
msgstr "Plus-values sur réalisations courantes d'immobilisations corporelles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_11
#: model:account.group,name:l10n_be.2_be_group_11
#: model:account.group.template,name:l10n_be.be_group_11
msgid "Primes d'émission"
msgstr "Primes d'émission"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_622
#: model:account.group,name:l10n_be.2_be_group_622
#: model:account.group.template,name:l10n_be.be_group_622
msgid "Primes patronales pour assurances extra-légales"
msgstr "Primes patronales pour assurances extra-légales"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a073
#: model:account.account,name:l10n_be.2_a073
#: model:account.account.template,name:l10n_be.a073
msgid "Principals and depositors of goods and securities"
msgstr "Commettants et déposants de biens et de valeurs"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_72
#: model:account.group,name:l10n_be.2_be_group_72
#: model:account.group.template,name:l10n_be.be_group_72
msgid "Production immobilisée"
msgstr "Production immobilisée"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_7
#: model:account.group,name:l10n_be.2_be_group_7
#: model:account.group.template,name:l10n_be.be_group_7
msgid "Produits"
msgstr "Produits"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_491
#: model:account.group,name:l10n_be.2_be_group_491
#: model:account.group.template,name:l10n_be.be_group_491
msgid "Produits acquis"
msgstr "Produits acquis"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_743
#: model:account.group,name:l10n_be.2_be_group_743
#: model:account.group.template,name:l10n_be.be_group_743
msgid "Produits d'exploitation divers"
msgstr "Produits d'exploitation divers"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_76
#: model:account.group,name:l10n_be.2_be_group_76
#: model:account.group.template,name:l10n_be.be_group_76
msgid "Produits d'exploitation ou financiers non récurrents"
msgstr "Produits d'exploitation ou financiers non récurrents"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_751
#: model:account.group,name:l10n_be.2_be_group_751
#: model:account.group.template,name:l10n_be.be_group_751
msgid "Produits des actifs circulants"
msgstr "Produits des actifs circulants"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_750
#: model:account.group,name:l10n_be.2_be_group_750
#: model:account.group.template,name:l10n_be.be_group_750
msgid "Produits des immobilisations financières"
msgstr "Produits des immobilisations financières"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_75
#: model:account.group,name:l10n_be.2_be_group_75
#: model:account.group.template,name:l10n_be.be_group_75
msgid "Produits financiers"
msgstr "Produits financiers"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_756
#: model:account.group,name:l10n_be.2_be_group_756
#: model:account.group.template,name:l10n_be.be_group_756
msgid "Produits financiers divers"
msgstr "Produits financiers divers"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_33
#: model:account.group,name:l10n_be.1_be_group_330
#: model:account.group,name:l10n_be.2_be_group_33
#: model:account.group,name:l10n_be.2_be_group_330
#: model:account.group.template,name:l10n_be.be_group_33
#: model:account.group.template,name:l10n_be.be_group_330
msgid "Produits finis"
msgstr "Produits finis"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_404
#: model:account.group,name:l10n_be.1_be_group_414
#: model:account.group,name:l10n_be.2_be_group_404
#: model:account.group,name:l10n_be.2_be_group_414
#: model:account.group.template,name:l10n_be.be_group_404
#: model:account.group.template,name:l10n_be.be_group_414
msgid "Produits à recevoir"
msgstr "Produits à recevoir"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_493
#: model:account.group,name:l10n_be.2_be_group_493
#: model:account.group.template,name:l10n_be.be_group_493
msgid "Produits à reporter"
msgstr "Produits à reporter"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a790
#: model:account.account,name:l10n_be.2_a790
#: model:account.account.template,name:l10n_be.a790
msgid "Profit brought forward from previous year"
msgstr "Bénéfice reporté de l'exercice précédent"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a140
#: model:account.account,name:l10n_be.2_a140
#: model:account.account.template,name:l10n_be.a140
msgid "Profit carried forward"
msgstr "Bénéfice reporté"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a693
#: model:account.account,name:l10n_be.2_a693
#: model:account.account.template,name:l10n_be.a693
msgid "Profits to be carried forward"
msgstr "Bénéfice à reporter"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6360
#: model:account.account,name:l10n_be.2_a6360
#: model:account.account.template,name:l10n_be.a6360
msgid "Provision for major repairs and maintenance - Appropriations"
msgstr "Provisions pour grosses réparations et gros entretien - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6361
#: model:account.account,name:l10n_be.2_a6361
#: model:account.account.template,name:l10n_be.a6361
msgid "Provision for major repairs and maintenance - Uses and write-backs"
msgstr ""
"Provisions pour grosses réparations et gros entretien - Utilisations et "
"reprises"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_16
#: model:account.group,name:l10n_be.2_be_group_16
#: model:account.group.template,name:l10n_be.be_group_16
msgid "Provisions et impôts différés"
msgstr "Provisions et impôts différés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a163
#: model:account.account,name:l10n_be.2_a163
#: model:account.account.template,name:l10n_be.a163
msgid "Provisions for environmental obligations"
msgstr "Provisions pour obligations environnementales"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a162
#: model:account.account,name:l10n_be.2_a162
#: model:account.account.template,name:l10n_be.a162
msgid "Provisions for major repairs and maintenance"
msgstr "Provisions pour grosses réparations et gros entretien"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a66210
#: model:account.account,name:l10n_be.2_a66210
#: model:account.account.template,name:l10n_be.a66210
msgid ""
"Provisions for non-recurring financial liabilities and charges - "
"Appropriations"
msgstr ""
"Provisions pour risques et charges financiers non récurrents - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a66211
#: model:account.account,name:l10n_be.2_a66211
#: model:account.account.template,name:l10n_be.a66211
msgid "Provisions for non-recurring financial liabilities and charges - Uses"
msgstr ""
"Provisions pour risques et charges financiers non récurrents - Utilisation"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a66200
#: model:account.account,name:l10n_be.2_a66200
#: model:account.account.template,name:l10n_be.a66200
msgid ""
"Provisions for non-recurring operating liabilities and charges - "
"Appropriations"
msgstr ""
"Provisions pour risques et charges d'exploitation non récurrents - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a66201
#: model:account.account,name:l10n_be.2_a66201
#: model:account.account.template,name:l10n_be.a66201
msgid "Provisions for non-recurring operating liabilities and charges - Uses"
msgstr ""
"Provisions pour risques et charges d'exploitation non récurrents - "
"Utilisation"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6370
#: model:account.account,name:l10n_be.2_a6370
#: model:account.account.template,name:l10n_be.a6370
msgid "Provisions for other risks and charges - Appropriations"
msgstr "Provisions pour autres risques et charges - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6380
#: model:account.account,name:l10n_be.2_a6380
#: model:account.account.template,name:l10n_be.a6380
msgid ""
"Provisions for other risks and charges - Provisions for environmental "
"obligations excluded - Appropriations"
msgstr ""
"Provisions pour autres risques et charges - Obligations environnementales "
"exclues - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6381
#: model:account.account,name:l10n_be.2_a6381
#: model:account.account.template,name:l10n_be.a6381
msgid ""
"Provisions for other risks and charges - Provisions for environmental "
"obligations excluded - Uses (write-back)"
msgstr ""
"Provisions pour autres risques et charges - Obligations environnementales "
"exclues - Utilisations et reprises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6371
#: model:account.account,name:l10n_be.2_a6371
#: model:account.account.template,name:l10n_be.a6371
msgid "Provisions for other risks and charges - Uses (write-back)"
msgstr "Provisions pour autres risques et charges - Utilisations et reprises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a160
#: model:account.account,name:l10n_be.2_a160
#: model:account.account.template,name:l10n_be.a160
msgid "Provisions for pensions and similar obligations"
msgstr "Provisions pour pensions et obligations similaires"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6350
#: model:account.account,name:l10n_be.2_a6350
#: model:account.account.template,name:l10n_be.a6350
msgid "Provisions for pensions and similar obligations - Appropriations"
msgstr "Provisions pour pensions et obligations similaires - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6351
#: model:account.account,name:l10n_be.2_a6351
#: model:account.account.template,name:l10n_be.a6351
msgid "Provisions for pensions and similar obligations - Uses and write-backs"
msgstr ""
"Provisions pour pensions et obligations similaires - Utilisations et "
"reprises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a161
#: model:account.account,name:l10n_be.2_a161
#: model:account.account.template,name:l10n_be.a161
msgid "Provisions for taxation"
msgstr "Provisions pour charges fiscales"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6560
#: model:account.account,name:l10n_be.2_a6560
#: model:account.account.template,name:l10n_be.a6560
msgid "Provisions of a financial nature - Appropriations"
msgstr "Provisions à caractère financier - Dotations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6561
#: model:account.account,name:l10n_be.2_a6561
#: model:account.account.template,name:l10n_be.a6561
msgid "Provisions of a financial nature - Uses and write-backs"
msgstr "Provisions à caractère financier - Utilisations et reprises"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_164
#: model:account.group,name:l10n_be.1_be_group_638
#: model:account.group,name:l10n_be.2_be_group_164
#: model:account.group,name:l10n_be.2_be_group_638
#: model:account.group.template,name:l10n_be.be_group_164
#: model:account.group.template,name:l10n_be.be_group_638
msgid "Provisions pour autres risques et charges"
msgstr "Provisions pour autres risques et charges"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_161
#: model:account.group,name:l10n_be.2_be_group_161
#: model:account.group.template,name:l10n_be.be_group_161
msgid "Provisions pour charges fiscales"
msgstr "Provisions pour charges fiscales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_162
#: model:account.group,name:l10n_be.1_be_group_636
#: model:account.group,name:l10n_be.2_be_group_162
#: model:account.group,name:l10n_be.2_be_group_636
#: model:account.group.template,name:l10n_be.be_group_162
#: model:account.group.template,name:l10n_be.be_group_636
msgid "Provisions pour grosses réparations et gros entretien"
msgstr "Provisions pour grosses réparations et gros entretien"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_163
#: model:account.group,name:l10n_be.1_be_group_637
#: model:account.group,name:l10n_be.2_be_group_163
#: model:account.group,name:l10n_be.2_be_group_637
#: model:account.group.template,name:l10n_be.be_group_163
#: model:account.group.template,name:l10n_be.be_group_637
msgid "Provisions pour obligations environnementales"
msgstr "Provisions pour obligations environnementales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_160
#: model:account.group,name:l10n_be.1_be_group_635
#: model:account.group,name:l10n_be.2_be_group_160
#: model:account.group,name:l10n_be.2_be_group_635
#: model:account.group.template,name:l10n_be.be_group_160
#: model:account.group.template,name:l10n_be.be_group_635
msgid "Provisions pour pensions et obligations similaires"
msgstr "Provisions pour pensions et obligations similaires"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_662
#: model:account.group,name:l10n_be.2_be_group_662
#: model:account.group.template,name:l10n_be.be_group_662
msgid "Provisions pour risques et charges non récurrents"
msgstr "Provisions pour risques et charges non récurrents"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_656
#: model:account.group,name:l10n_be.2_be_group_656
#: model:account.group.template,name:l10n_be.be_group_656
msgid "Provisions à caractère financier"
msgstr "Provisions à caractère financier"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_453
#: model:account.group,name:l10n_be.2_be_group_453
#: model:account.group.template,name:l10n_be.be_group_453
msgid "Précomptes retenus"
msgstr "Précomptes retenus"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_791
#: model:account.group,name:l10n_be.2_be_group_791
#: model:account.group.template,name:l10n_be.be_group_791
msgid "Prélèvement sur le capital et les primes d'émission"
msgstr "Prélèvement sur le capital et les primes d'émission"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_792
#: model:account.group,name:l10n_be.2_be_group_792
#: model:account.group.template,name:l10n_be.be_group_792
msgid "Prélèvement sur les réserves"
msgstr "Prélèvement sur les réserves"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_780
#: model:account.group,name:l10n_be.2_be_group_780
#: model:account.group.template,name:l10n_be.be_group_780
msgid "Prélèvements sur les impôts différés"
msgstr "Prélèvements sur les impôts différés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_789
#: model:account.group,name:l10n_be.2_be_group_789
#: model:account.group.template,name:l10n_be.be_group_789
msgid "Prélèvements sur les réserves immunisées"
msgstr "Prélèvements sur les réserves immunisées"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_78
#: model:account.group,name:l10n_be.2_be_group_78
#: model:account.group.template,name:l10n_be.be_group_78
msgid "Prélèvements sur les réserves immunisées et les impôts différés"
msgstr "Prélèvements sur les réserves immunisées et les impôts différés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a601
#: model:account.account,name:l10n_be.2_a601
#: model:account.account.template,name:l10n_be.a601
msgid "Purchases of consumables"
msgstr "Achats de fournitures"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a604
#: model:account.account,name:l10n_be.2_a604
#: model:account.account.template,name:l10n_be.a604
msgid "Purchases of goods for resale"
msgstr "Achats de marchandises"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a605
#: model:account.account,name:l10n_be.2_a605
#: model:account.account.template,name:l10n_be.a605
msgid "Purchases of immovable property for resale"
msgstr "Achats d'immeubles destinés à la vente"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a600
#: model:account.account,name:l10n_be.2_a600
#: model:account.account.template,name:l10n_be.a600
msgid "Purchases of raw materials"
msgstr "Achats de matières premières"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a602
#: model:account.account,name:l10n_be.2_a602
#: model:account.account.template,name:l10n_be.a602
msgid "Purchases of services, works and studies"
msgstr "Achats de services, travaux et études"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_456
#: model:account.group,name:l10n_be.2_be_group_456
#: model:account.group.template,name:l10n_be.be_group_456
msgid "Pécules de vacances"
msgstr "Pécules de vacances"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a300
#: model:account.account,name:l10n_be.2_a300
#: model:account.account.template,name:l10n_be.a300
msgid "Raw materials - Acquisition value"
msgstr "Matières premières - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a309
#: model:account.account,name:l10n_be.2_a309
#: model:account.account.template,name:l10n_be.a309
msgid "Raw materials - amounts written down"
msgstr "Matières premières - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a023
#: model:account.account,name:l10n_be.2_a023
#: model:account.account.template,name:l10n_be.a023
msgid "Real guarantees provided on behalf of third parties"
msgstr "Garanties réelles constituées pour compte de tiers"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_708
#: model:account.group,name:l10n_be.2_be_group_708
#: model:account.group.template,name:l10n_be.be_group_708
msgid "Remises, ristournes et rabais accordés (–)"
msgstr "Remises, ristournes et rabais accordés (–)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_608
#: model:account.group,name:l10n_be.2_be_group_608
#: model:account.group.template,name:l10n_be.be_group_608
msgid "Remises, ristournes et rabais obtenus (–)"
msgstr "Remises, ristournes et rabais obtenus (–)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6200
#: model:account.account,name:l10n_be.2_a6200
#: model:account.account.template,name:l10n_be.a6200
msgid "Remuneration and direct social benefits - Directors and managers"
msgstr ""
"Rémunérations et avantages sociaux directs - Administrateurs ou gérants"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6202
#: model:account.account,name:l10n_be.2_a6202
#: model:account.account.template,name:l10n_be.a6202
msgid "Remuneration and direct social benefits - Employees"
msgstr "Rémunérations et avantages sociaux directs - Employés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6201
#: model:account.account,name:l10n_be.2_a6201
#: model:account.account.template,name:l10n_be.a6201
msgid "Remuneration and direct social benefits - Executive"
msgstr "Rémunérations et avantages sociaux directs - Personnel de direction"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6203
#: model:account.account,name:l10n_be.2_a6203
#: model:account.account.template,name:l10n_be.a6203
msgid "Remuneration and direct social benefits - Manual workers"
msgstr "Rémunérations et avantages sociaux directs - Ouvriers"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6204
#: model:account.account,name:l10n_be.2_a6204
#: model:account.account.template,name:l10n_be.a6204
msgid "Remuneration and direct social benefits - Other staff members"
msgstr ""
"Rémunérations et avantages sociaux directs - Autres membres du personnel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a456
#: model:account.account,name:l10n_be.2_a456
#: model:account.account.template,name:l10n_be.a456
msgid "Remuneration and social security - Holiday pay"
msgstr "Dettes salariales et sociales - Pécules de vacances"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a454
#: model:account.account,name:l10n_be.2_a454
#: model:account.account.template,name:l10n_be.a454
msgid "Remuneration and social security - National Social Security Office"
msgstr "Dettes salariales et sociales - Office National de Sécurité Sociale"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a459
#: model:account.account,name:l10n_be.2_a459
#: model:account.account.template,name:l10n_be.a459
msgid "Remuneration and social security - Other social obligations"
msgstr "Dettes salariales et sociales - Autres dettes sociales"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a455
#: model:account.account,name:l10n_be.2_a455
#: model:account.account.template,name:l10n_be.a455
msgid "Remuneration and social security - Remuneration"
msgstr "Dettes salariales et sociales - Rémunérations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a618
#: model:account.account,name:l10n_be.2_a618
#: model:account.account.template,name:l10n_be.a618
msgid ""
"Remuneration, premiums for extra statutory insurance, pensions of the "
"directors, or the management staff which are not allowed following the "
"contract"
msgstr ""
"Rémunérations, primes pour assurances extralégales, pensions de retraite et "
"de survie des administrateurs, gérants et associés actifs qui ne sont pas "
"attribuées en vertu d'un contrat de travail"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a071
#: model:account.account,name:l10n_be.2_a071
#: model:account.account.template,name:l10n_be.a071
msgid "Rent and royalty creditors"
msgstr "Créanciers de loyers et redevances"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_760
#: model:account.group,name:l10n_be.2_be_group_760
#: model:account.group.template,name:l10n_be.be_group_760
msgid "Reprises d'amortissements et de réductions de valeur"
msgstr "Reprises d'amortissements et de réductions de valeur"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_762
#: model:account.group,name:l10n_be.2_be_group_762
#: model:account.group.template,name:l10n_be.be_group_762
msgid "Reprises de provisions pour risques et charges non récurrents"
msgstr "Reprises de provisions pour risques et charges non récurrents"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_761
#: model:account.group,name:l10n_be.2_be_group_761
#: model:account.group.template,name:l10n_be.be_group_761
msgid "Reprises de réductions de valeur sur immobilisations financières"
msgstr "Reprises de réductions de valeur sur immobilisations financières"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_124
#: model:account.group,name:l10n_be.2_be_group_124
#: model:account.group.template,name:l10n_be.be_group_124
msgid "Reprises de réductions de valeur sur placements de trésorerie"
msgstr "Reprises de réductions de valeur sur placements de trésorerie"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a210
#: model:account.account,name:l10n_be.2_a210
#: model:account.account.template,name:l10n_be.a210
msgid "Research and development costs"
msgstr "Frais de recherche et de développement"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1310
#: model:account.account,name:l10n_be.2_a1310
#: model:account.account.template,name:l10n_be.a1310
msgid "Reserves not available in respect of own shares held"
msgstr "Réserve pour actions propres"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a204
#: model:account.account,name:l10n_be.2_a204
#: model:account.account.template,name:l10n_be.a204
msgid "Restructuring costs"
msgstr "Frais de restructuration"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6240
#: model:account.account,name:l10n_be.2_a6240
#: model:account.account.template,name:l10n_be.a6240
msgid "Retirement and survivors' pensions - Directors and managers"
msgstr "Pensions de retraite et de survie - Administrateurs ou gérants"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6241
#: model:account.account,name:l10n_be.2_a6241
#: model:account.account.template,name:l10n_be.a6241
msgid "Retirement and survivors' pensions - Personnel"
msgstr "Pensions de retraite et de survie - Personnel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a122
#: model:account.account,name:l10n_be.2_a122
#: model:account.account.template,name:l10n_be.a122
msgid "Revaluation surpluses on financial fixed assets"
msgstr "Plus-values de réévaluation sur immobilisations financières"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a120
#: model:account.account,name:l10n_be.2_a120
#: model:account.account.template,name:l10n_be.a120
msgid "Revaluation surpluses on intangible fixed assets"
msgstr "Plus-values de réévaluation sur immobilisations incorporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a123
#: model:account.account,name:l10n_be.2_a123
#: model:account.account.template,name:l10n_be.a123
msgid "Revaluation surpluses on stocks"
msgstr "Plus-values de réévaluation sur stocks"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a121
#: model:account.account,name:l10n_be.2_a121
#: model:account.account.template,name:l10n_be.a121
msgid "Revaluation surpluses on tangible fixed assets"
msgstr "Plus-values de réévaluation sur immobilisations corporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a097
#: model:account.account,name:l10n_be.2_a097
#: model:account.account.template,name:l10n_be.a097
msgid "Rights on technical guarantees"
msgstr "Droits sur garanties techniques"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_519
#: model:account.group,name:l10n_be.2_be_group_519
#: model:account.group.template,name:l10n_be.be_group_519
msgid "Réductions de valeur actées (-)"
msgstr "Réductions de valeur actées (-)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_309
#: model:account.group,name:l10n_be.1_be_group_319
#: model:account.group,name:l10n_be.1_be_group_329
#: model:account.group,name:l10n_be.1_be_group_339
#: model:account.group,name:l10n_be.1_be_group_349
#: model:account.group,name:l10n_be.1_be_group_359
#: model:account.group,name:l10n_be.1_be_group_369
#: model:account.group,name:l10n_be.1_be_group_379
#: model:account.group,name:l10n_be.1_be_group_409
#: model:account.group,name:l10n_be.1_be_group_419
#: model:account.group,name:l10n_be.1_be_group_529
#: model:account.group,name:l10n_be.1_be_group_539
#: model:account.group,name:l10n_be.2_be_group_309
#: model:account.group,name:l10n_be.2_be_group_319
#: model:account.group,name:l10n_be.2_be_group_329
#: model:account.group,name:l10n_be.2_be_group_339
#: model:account.group,name:l10n_be.2_be_group_349
#: model:account.group,name:l10n_be.2_be_group_359
#: model:account.group,name:l10n_be.2_be_group_369
#: model:account.group,name:l10n_be.2_be_group_379
#: model:account.group,name:l10n_be.2_be_group_409
#: model:account.group,name:l10n_be.2_be_group_419
#: model:account.group,name:l10n_be.2_be_group_529
#: model:account.group,name:l10n_be.2_be_group_539
#: model:account.group.template,name:l10n_be.be_group_309
#: model:account.group.template,name:l10n_be.be_group_319
#: model:account.group.template,name:l10n_be.be_group_329
#: model:account.group.template,name:l10n_be.be_group_339
#: model:account.group.template,name:l10n_be.be_group_349
#: model:account.group.template,name:l10n_be.be_group_359
#: model:account.group.template,name:l10n_be.be_group_369
#: model:account.group.template,name:l10n_be.be_group_379
#: model:account.group.template,name:l10n_be.be_group_409
#: model:account.group.template,name:l10n_be.be_group_419
#: model:account.group.template,name:l10n_be.be_group_529
#: model:account.group.template,name:l10n_be.be_group_539
msgid "Réductions de valeur actées (–)"
msgstr "Réductions de valeur actées (–)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_651
#: model:account.group,name:l10n_be.2_be_group_651
#: model:account.group.template,name:l10n_be.be_group_651
msgid "Réductions de valeur sur actifs circulants"
msgstr "Réductions de valeur sur actifs circulants"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_632
#: model:account.group,name:l10n_be.2_be_group_632
#: model:account.group.template,name:l10n_be.be_group_632
msgid "Réductions de valeur sur commandes en cours"
msgstr "Réductions de valeur sur commandes en cours"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_633
#: model:account.group,name:l10n_be.2_be_group_633
#: model:account.group.template,name:l10n_be.be_group_633
msgid "Réductions de valeur sur créances commerciales à plus d'un an"
msgstr "Réductions de valeur sur créances commerciales à plus d'un an"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_634
#: model:account.group,name:l10n_be.2_be_group_634
#: model:account.group.template,name:l10n_be.be_group_634
msgid "Réductions de valeur sur créances commerciales à un an au plus"
msgstr "Réductions de valeur sur créances commerciales à un an au plus"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_661
#: model:account.group,name:l10n_be.2_be_group_661
#: model:account.group.template,name:l10n_be.be_group_661
msgid "Réductions de valeur sur immobilisations financières (dotations)"
msgstr "Réductions de valeur sur immobilisations financières (dotations)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_631
#: model:account.group,name:l10n_be.2_be_group_631
#: model:account.group.template,name:l10n_be.be_group_631
msgid "Réductions de valeur sur stocks"
msgstr "Réductions de valeur sur stocks"

#. module: l10n_be
#: model:account.fiscal.position,name:l10n_be.1_fiscal_position_template_4
#: model:account.fiscal.position,name:l10n_be.2_fiscal_position_template_4
#: model:account.fiscal.position.template,name:l10n_be.fiscal_position_template_4
msgid "Régime Cocontractant"
msgstr "Régime Cocontractant"

#. module: l10n_be
#: model:account.fiscal.position,name:l10n_be.1_fiscal_position_template_2
#: model:account.fiscal.position,name:l10n_be.2_fiscal_position_template_2
#: model:account.fiscal.position.template,name:l10n_be.fiscal_position_template_2
msgid "Régime Extra-Communautaire"
msgstr "Régime Extracommunautaire"

#. module: l10n_be
#: model:account.fiscal.position,name:l10n_be.1_fiscal_position_template_3
#: model:account.fiscal.position,name:l10n_be.2_fiscal_position_template_3
#: model:account.fiscal.position.template,name:l10n_be.fiscal_position_template_3
msgid "Régime Intra-Communautaire"
msgstr "Régime Intracommunautaire"

#. module: l10n_be
#: model:account.fiscal.position,name:l10n_be.1_fiscal_position_template_1
#: model:account.fiscal.position,name:l10n_be.2_fiscal_position_template_1
#: model:account.fiscal.position.template,name:l10n_be.fiscal_position_template_1
msgid "Régime National"
msgstr "Régime National"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_77
#: model:account.group,name:l10n_be.2_be_group_77
#: model:account.group.template,name:l10n_be.be_group_77
msgid "Régularisations d'impôts et reprises de provisions fiscales"
msgstr "Régularisations d'impôts et reprises de provisions fiscales"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_694
#: model:account.group,name:l10n_be.2_be_group_694
#: model:account.group.template,name:l10n_be.be_group_694
msgid "Rémunération du capital"
msgstr "Rémunération du capital"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_455
#: model:account.group,name:l10n_be.2_be_group_455
#: model:account.group.template,name:l10n_be.be_group_455
msgid "Rémunérations"
msgstr "Rémunérations"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_620
#: model:account.group,name:l10n_be.2_be_group_620
#: model:account.group.template,name:l10n_be.be_group_620
msgid "Rémunérations et avantages sociaux directs"
msgstr "Rémunérations et avantages sociaux directs"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_62
#: model:account.group,name:l10n_be.2_be_group_62
#: model:account.group.template,name:l10n_be.be_group_62
msgid "Rémunérations, charges sociales et pensions"
msgstr "Rémunérations, charges sociales et pensions"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_618
#: model:account.group,name:l10n_be.2_be_group_618
#: model:account.group.template,name:l10n_be.be_group_618
msgid ""
"Rémunérations, primes pour assurances extralégales, pensions de retraite et "
"de survie des administrateurs, gérants et associés actifs qui ne sont pas "
"attribuées en vertu d'un contrat de travail"
msgstr ""
"Rémunérations, primes pour assurances extralégales, pensions de retraite et "
"de survie des administrateurs, gérants et associés actifs qui ne sont pas "
"attribuées en vertu d'un contrat de travail"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_130
#: model:account.group,name:l10n_be.2_be_group_130
#: model:account.group.template,name:l10n_be.be_group_130
msgid "Réserve légale"
msgstr "Réserve légale"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_13
#: model:account.group,name:l10n_be.2_be_group_13
#: model:account.group.template,name:l10n_be.be_group_13
msgid "Réserves"
msgstr "Réserves"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_133
#: model:account.group,name:l10n_be.2_be_group_133
#: model:account.group.template,name:l10n_be.be_group_133
msgid "Réserves disponibles"
msgstr "Réserves disponibles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_132
#: model:account.group,name:l10n_be.2_be_group_132
#: model:account.group.template,name:l10n_be.be_group_132
msgid "Réserves immunisées"
msgstr "Réserves immunisées"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_131
#: model:account.group,name:l10n_be.2_be_group_131
#: model:account.group.template,name:l10n_be.be_group_131
msgid "Réserves indisponibles"
msgstr "Réserves indisponibles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a053
#: model:account.account,name:l10n_be.2_a053
#: model:account.account.template,name:l10n_be.a053
msgid "Sale commitment"
msgstr "Engagements de cession"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7012
#: model:account.account,name:l10n_be.2_a7012
#: model:account.account.template,name:l10n_be.a7012
msgid "Sales rendered for export (finished goods)"
msgstr "Ventes à l’export (produits finis)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7002
#: model:account.account,name:l10n_be.2_a7002
#: model:account.account.template,name:l10n_be.a7002
msgid "Sales rendered for export (marchandises)"
msgstr "Ventes à l’export (marchandises)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7010
#: model:account.account,name:l10n_be.2_a7010
#: model:account.account.template,name:l10n_be.a7010
msgid "Sales rendered in Belgium (finished goods)"
msgstr "Ventes en Belgique (produits finis)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7000
#: model:account.account,name:l10n_be.2_a7000
#: model:account.account.template,name:l10n_be.a7000
msgid "Sales rendered in Belgium (marchandises)"
msgstr "Ventes en Belgique (marchandises)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7011
#: model:account.account,name:l10n_be.2_a7011
#: model:account.account.template,name:l10n_be.a7011
msgid "Sales rendered in E.E.C. (finished goods)"
msgstr "Ventes dans les pays membres de la C.E.E. (produits finis)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7001
#: model:account.account,name:l10n_be.2_a7001
#: model:account.account.template,name:l10n_be.a7001
msgid "Sales rendered in E.E.C. (marchandises)"
msgstr "Ventes dans les pays membres de la C.E.E. (marchandises)"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a61
#: model:account.account,name:l10n_be.2_a61
#: model:account.account.template,name:l10n_be.a61
msgid "Services and other goods"
msgstr "Services et biens divers"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_61
#: model:account.group,name:l10n_be.2_be_group_61
#: model:account.group.template,name:l10n_be.be_group_61
msgid "Services et biens divers"
msgstr "Services et biens divers"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7052
#: model:account.account,name:l10n_be.2_a7052
#: model:account.account.template,name:l10n_be.a7052
msgid "Services rendered for export"
msgstr "Prestations de services en vue de l'exportation"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7050
#: model:account.account,name:l10n_be.2_a7050
#: model:account.account.template,name:l10n_be.a7050
msgid "Services rendered in Belgium"
msgstr "Prestations de services en Belgique"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7051
#: model:account.account,name:l10n_be.2_a7051
#: model:account.account.template,name:l10n_be.a7051
msgid "Services rendered in E.E.C."
msgstr "Prestations de services dans les pays membres de la C.E.E."

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a11
#: model:account.account,name:l10n_be.2_a11
#: model:account.account.template,name:l10n_be.a11
msgid "Share premium account"
msgstr "Primes d'émission"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a510
#: model:account.account,name:l10n_be.2_a510
#: model:account.account.template,name:l10n_be.a510
msgid ""
"Shares and current investments other than fixed income investments - "
"Acquisition value"
msgstr ""
"Actions, parts et placements de trésorerie autres que placements à revenu "
"fixe - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a519
#: model:account.account,name:l10n_be.2_a519
#: model:account.account.template,name:l10n_be.a519
msgid ""
"Shares and current investments other than fixed income investments - Amounts"
" written down"
msgstr ""
"Actions, parts et placements de trésorerie autres que placements à revenu "
"fixe - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a511
#: model:account.account,name:l10n_be.2_a511
#: model:account.account.template,name:l10n_be.a511
msgid ""
"Shares and current investments other than fixed income investments - "
"Uncalled amount"
msgstr ""
"Actions, parts et placements de trésorerie autres que placements à revenu "
"fixe - Montant non appelé"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_603
#: model:account.group,name:l10n_be.2_be_group_603
#: model:account.group.template,name:l10n_be.be_group_603
msgid "Sous-traitances générales"
msgstr "Sous-traitances générales"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a031
#: model:account.account,name:l10n_be.2_a031
#: model:account.account.template,name:l10n_be.a031
msgid "Statutory applicants"
msgstr "Déposants statutaires"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a030
#: model:account.account,name:l10n_be.2_a030
#: model:account.account.template,name:l10n_be.a030
msgid "Statutory deposits"
msgstr "Dépôts statutaires"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_3
#: model:account.group,name:l10n_be.2_be_group_3
#: model:account.group.template,name:l10n_be.be_group_3
msgid "Stocks et commandes en cours d'exécution"
msgstr "Stocks et commandes en cours d'exécution"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a603
#: model:account.account,name:l10n_be.2_a603
#: model:account.account.template,name:l10n_be.a603
msgid "Sub-contracting"
msgstr "Sous-traitances générales"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4200
#: model:account.account,name:l10n_be.2_a4200
#: model:account.account.template,name:l10n_be.a4200
msgid ""
"Subordinated loans payable after more than one year falling due within one "
"year - Convertible"
msgstr ""
"Emprunts subordonnés à plus d'un an échéant dans l'année - Convertibles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4201
#: model:account.account,name:l10n_be.2_a4201
#: model:account.account.template,name:l10n_be.a4201
msgid ""
"Subordinated loans payable after more than one year falling due within one "
"year - Non convertible"
msgstr ""
"Emprunts subordonnés à plus d'un an échéant dans l'année - Non convertibles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1700
#: model:account.account,name:l10n_be.2_a1700
#: model:account.account.template,name:l10n_be.a1700
msgid ""
"Subordinated loans with a remaining term of more than one year - Convertible"
" bonds"
msgstr "Emprunts subordonnés à plus d'un an - Convertibles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1701
#: model:account.account,name:l10n_be.2_a1701
#: model:account.account.template,name:l10n_be.a1701
msgid ""
"Subordinated loans with a remaining term of more than one year - Non "
"convertible bonds"
msgstr "Emprunts subordonnés à plus d'un an - Non convertibles"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_740
#: model:account.group,name:l10n_be.2_be_group_740
#: model:account.group.template,name:l10n_be.be_group_740
msgid "Subsides d'exploitation et montants compensatoires"
msgstr "Subsides d'exploitation et montants compensatoires"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_15
#: model:account.group,name:l10n_be.2_be_group_15
#: model:account.group.template,name:l10n_be.be_group_15
msgid "Subsides en capital"
msgstr "Subsides en capital"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_753
#: model:account.group,name:l10n_be.2_be_group_753
#: model:account.group.template,name:l10n_be.be_group_753
msgid "Subsides en capital et en intérêts"
msgstr "Subsides en capital et en intérêts"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1750
#: model:account.account,name:l10n_be.2_a1750
#: model:account.account.template,name:l10n_be.a1750
msgid "Suppliers (more than one year)"
msgstr "Dettes commerciales à plus d'un an - Fournisseurs"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a440
#: model:account.account,name:l10n_be.2_a440
#: model:account.account.template,name:l10n_be.a440
msgid "Suppliers payable within one year"
msgstr "Fournisseurs à un an au plus"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a499
#: model:account.account,name:l10n_be.2_a499
#: model:account.account.template,name:l10n_be.a499
msgid "Suspense account"
msgstr "Compte d'attente"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_451
#: model:account.group,name:l10n_be.2_be_group_451
#: model:account.group.template,name:l10n_be.be_group_451
msgid "T.V.A. à payer"
msgstr "TVA à payer"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_411
#: model:account.group,name:l10n_be.2_be_group_411
#: model:account.group.template,name:l10n_be.be_group_411
msgid "T.V.A. à récupérer"
msgstr "TVA à récupérer"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-00
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-00
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-L
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-00
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-00
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-L
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-S
#: model:account.tax.group,name:l10n_be.tax_group_tva_0
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-00
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-00
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-L
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-S
msgid "TVA 0%"
msgstr "TVA 0%"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-00-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-00-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-00-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-00-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-00-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-00-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-CC
msgid "TVA 0% Cocont."
msgstr "TVA 0% Cocont."

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-00-EU
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-EU-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-EU-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-00-EU
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-EU-L
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-EU-S
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-EU-T
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-00-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-EU-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-EU-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-00-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-EU-L
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-EU-S
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-EU-T
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-00-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-EU-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-EU-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-00-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-EU-L
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-EU-S
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-EU-T
msgid "TVA 0% EU"
msgstr "TVA 0% EU"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-00-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-00-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-ROW
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-00-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-00-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-ROW
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-00-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-00-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-ROW
msgid "TVA 0% Non EU"
msgstr "TVA 0% Non EU"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-12
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-12
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-12-L
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-12-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-12
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-12
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-12-L
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-12-S
#: model:account.tax.group,name:l10n_be.tax_group_tva_12
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-12
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-12
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-12-L
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-12-S
msgid "TVA 12%"
msgstr "TVA 12%"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-12-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-12-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-12-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-12-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-12-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-12-CC
msgid "TVA 12% Cocont."
msgstr "TVA 12% Cocont."

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-12-EU
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-EU-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-EU-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-12-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-12-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-EU-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-EU-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-12-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-12-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-EU-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-EU-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-12-EU
msgid "TVA 12% EU"
msgstr "TVA 12% EU"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-12-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-12-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-12-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-12-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-12-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-12-ROW-CC
msgid "TVA 12% Non EU"
msgstr "TVA 12% Non EU"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-21
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-21
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-21-L
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-21-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-21
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-21
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-21-L
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-21-S
#: model:account.tax.group,name:l10n_be.tax_group_tva_21
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-21
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-21
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-21-L
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-21-S
msgid "TVA 21%"
msgstr "TVA 21%"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-21-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-21-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-21-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-21-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-21-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-21-CC
msgid "TVA 21% Cocont."
msgstr "TVA 21% Cocont."

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-21-EU
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-EU-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-EU-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-21-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-21-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-EU-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-EU-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-21-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-21-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-EU-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-EU-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-21-EU
msgid "TVA 21% EU"
msgstr "TVA 21% EU"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-21-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-21-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-21-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-21-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-21-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-21-ROW-CC
msgid "TVA 21% Non EU"
msgstr "TVA 21% Non EU"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_TVA-21-inclus-dans-prix
#: model:account.tax,description:l10n_be.2_attn_TVA-21-inclus-dans-prix
#: model:account.tax.template,description:l10n_be.attn_TVA-21-inclus-dans-prix
msgid "TVA 21% TTC"
msgstr "TVA 21% TTC"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-CAR-EXC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-CAR-EXC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-CAR-EXC
msgid "TVA 50% Non Déductible - Frais de voiture (Prix Excl.)"
msgstr "TVA 50% Non Déductible - Frais de voiture (Prix Excl.)"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-06
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-06
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-06-L
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-06-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-06
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-06
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-06-L
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-06-S
#: model:account.tax.group,name:l10n_be.tax_group_tva_6
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-06
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-06
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-06-L
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-06-S
msgid "TVA 6%"
msgstr "TVA 6%"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-06-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-06-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-06-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-06-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-06-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-06-CC
msgid "TVA 6% Cocont."
msgstr "TVA 6% Cocont."

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-06-EU
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-EU-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-EU-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-06-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-06-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-EU-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-EU-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-06-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-06-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-EU-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-EU-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-06-EU
msgid "TVA 6% EU"
msgstr "TVA 6% EU"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-06-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-06-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-06-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-06-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-06-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-06-ROW-CC
msgid "TVA 6% Non EU"
msgstr "TVA 6% Non EU"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a27
#: model:account.account,name:l10n_be.2_a27
#: model:account.account.template,name:l10n_be.a27
msgid "Tangible fixed assets under construction and advance payments"
msgstr "Immobilisations corporelles en cours et acomptes versés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_472
#: model:account.group,name:l10n_be.2_be_group_472
#: model:account.group.template,name:l10n_be.be_group_472
msgid "Tantièmes de l'exercice"
msgstr "Tantièmes de l'exercice"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_taxes
msgid "Taxes"
msgstr "Taxes"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a412
#: model:account.account,name:l10n_be.2_a412
#: model:account.account.template,name:l10n_be.a412
msgid "Taxes and withholdings taxes to be recovered"
msgstr "Impôts et précomptes à récupérer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4128
#: model:account.account,name:l10n_be.2_a4128
#: model:account.account.template,name:l10n_be.a4128
msgid "Taxes and withholdings taxes to be recovered - Foreign taxes"
msgstr "Impôts et précomptes à récupérer - Impôts et taxes étrangers"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a452
#: model:account.account,name:l10n_be.2_a452
#: model:account.account.template,name:l10n_be.a452
msgid "Taxes payable"
msgstr "Impôts et taxes à payer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4528
#: model:account.account,name:l10n_be.2_a4528
#: model:account.account.template,name:l10n_be.a4528
msgid "Taxes payable - Foreign taxes"
msgstr "Impôts et taxes à payer - Impôts et taxes étrangers"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a640
#: model:account.account,name:l10n_be.2_a640
#: model:account.account.template,name:l10n_be.a640
msgid "Taxes related to operation"
msgstr "Impôts et taxes relatifs à l'exploitation"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a453
#: model:account.account,name:l10n_be.2_a453
#: model:account.account.template,name:l10n_be.a453
msgid "Taxes withheld"
msgstr "Précomptes retenus"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_220
#: model:account.group,name:l10n_be.2_be_group_220
#: model:account.group.template,name:l10n_be.be_group_220
msgid "Terrains"
msgstr "Terrains"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_222
#: model:account.group,name:l10n_be.2_be_group_222
#: model:account.group.template,name:l10n_be.be_group_222
msgid "Terrains bâtis"
msgstr "Terrains bâtis"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_250
#: model:account.group,name:l10n_be.2_be_group_250
#: model:account.group.template,name:l10n_be.be_group_250
msgid "Terrains et construction"
msgstr "Terrains et constructions"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_22
#: model:account.group,name:l10n_be.2_be_group_22
#: model:account.group.template,name:l10n_be.be_group_22
msgid "Terrains et constructions"
msgstr "Terrains et constructions"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a040
#: model:account.account,name:l10n_be.2_a040
#: model:account.account.template,name:l10n_be.a040
msgid ""
"Third parties, holders in their name but at the risks and profits of the "
"business of goods and values"
msgstr ""
"Tiers, détenteurs en leur nom mais aux risques et profits de l’entreprise de"
" biens et de valeurs"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a001
#: model:account.account,name:l10n_be.2_a001
#: model:account.account.template,name:l10n_be.a001
msgid "Third party guarantees on behalf of the company"
msgstr "Tiers constituants de garanties pour compte de l’entreprise"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_001
#: model:account.group,name:l10n_be.2_be_group_001
#: model:account.group.template,name:l10n_be.be_group_001
msgid "Tiers constituants de garanties pour compte de l'entreprise"
msgstr "Tiers constituants de garanties pour compte de l'entreprise"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_040
#: model:account.group,name:l10n_be.2_be_group_040
#: model:account.group.template,name:l10n_be.be_group_040
msgid ""
"Tiers, détenteurs en leur nom mais aux risques et profits de l'entreprise de"
" biens et de valeurs"
msgstr ""
"Tiers, détenteurs en leur nom mais aux risques et profits de l'entreprise de"
" biens et de valeurs"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_52
#: model:account.group,name:l10n_be.2_be_group_52
#: model:account.group.template,name:l10n_be.be_group_52
msgid "Titres à revenu fixe"
msgstr "Titres à revenu fixe"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2906
#: model:account.account,name:l10n_be.2_a2906
#: model:account.account.template,name:l10n_be.a2906
msgid "Trade debtors after more than one year - Advance payments"
msgstr "Créances commerciales à plus d'un an - Acomptes versés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2909
#: model:account.account,name:l10n_be.2_a2909
#: model:account.account.template,name:l10n_be.a2909
msgid "Trade debtors after more than one year - Amounts written down"
msgstr "Créances commerciales à plus d'un an - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2901
#: model:account.account,name:l10n_be.2_a2901
#: model:account.account.template,name:l10n_be.a2901
msgid "Trade debtors after more than one year - Bills receivable"
msgstr "Créances commerciales à plus d'un an - Effets à recevoir"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2900
#: model:account.account,name:l10n_be.2_a2900
#: model:account.account.template,name:l10n_be.a2900
msgid "Trade debtors after more than one year - Customer"
msgstr "Créances commerciales à plus d'un an - Clients"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2907
#: model:account.account,name:l10n_be.2_a2907
#: model:account.account.template,name:l10n_be.a2907
msgid "Trade debtors after more than one year - Doubtful amounts"
msgstr "Créances commerciales à plus d'un an - Créances douteuses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a406
#: model:account.account,name:l10n_be.2_a406
#: model:account.account.template,name:l10n_be.a406
msgid "Trade debtors within one year - Advance payments"
msgstr "Créances commerciales à un an au plus - Acomptes versés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a409
#: model:account.account,name:l10n_be.2_a409
#: model:account.account.template,name:l10n_be.a409
msgid "Trade debtors within one year - Amounts written down"
msgstr "Créances commerciales à un an au plus - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a401
#: model:account.account,name:l10n_be.2_a401
#: model:account.account.template,name:l10n_be.a401
msgid "Trade debtors within one year - Bills receivable"
msgstr "Créances commerciales à un an au plus - Effets à recevoir"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a400
#: model:account.account,name:l10n_be.2_a400
#: model:account.account.template,name:l10n_be.a400
msgid "Trade debtors within one year - Customer"
msgstr "Créances commerciales à un an au plus - Clients"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a407
#: model:account.account,name:l10n_be.2_a407
#: model:account.account.template,name:l10n_be.a407
msgid "Trade debtors within one year - Doubtful amounts"
msgstr "Créances commerciales à un an au plus - Créances douteuses"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a404
#: model:account.account,name:l10n_be.2_a404
#: model:account.account.template,name:l10n_be.a404
msgid "Trade debtors within one year - Income receivable"
msgstr "Créances commerciales à un an au plus - Produits à recevoir"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a780
#: model:account.account,name:l10n_be.2_a780
#: model:account.account.template,name:l10n_be.a780
msgid "Transfer from deferred taxes"
msgstr "Prélèvements sur les impôts différés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a789
#: model:account.account,name:l10n_be.2_a789
#: model:account.account.template,name:l10n_be.a789
msgid "Transfer from untaxed reserves"
msgstr "Prélèvements sur les réserves immunisées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a680
#: model:account.account,name:l10n_be.2_a680
#: model:account.account.template,name:l10n_be.a680
msgid "Transfer to deferred taxes"
msgstr "Transfert aux impôts différés"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a689
#: model:account.account,name:l10n_be.2_a689
#: model:account.account.template,name:l10n_be.a689
msgid "Transfer to untaxed reserves"
msgstr "Transfert aux réserves immunisées"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_680
#: model:account.group,name:l10n_be.2_be_group_680
#: model:account.group.template,name:l10n_be.be_group_680
msgid "Transferts aux impôts différés"
msgstr "Transferts aux impôts différés"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_68
#: model:account.group,name:l10n_be.2_be_group_68
#: model:account.group.template,name:l10n_be.be_group_68
msgid "Transferts aux impôts différés et aux réserves immunisées"
msgstr "Transferts aux impôts différés et aux réserves immunisées"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_689
#: model:account.group,name:l10n_be.2_be_group_689
#: model:account.group.template,name:l10n_be.be_group_689
msgid "Transferts aux réserves immunisées"
msgstr "Transferts aux réserves immunisées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a101
#: model:account.account,name:l10n_be.2_a101
#: model:account.account.template,name:l10n_be.a101
msgid "Uncalled capital"
msgstr "Capital non appelé"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4210
#: model:account.account,name:l10n_be.2_a4210
#: model:account.account.template,name:l10n_be.a4210
msgid ""
"Unsubordinated debentures payable after more than one year falling due "
"within one year - Convertible"
msgstr ""
"Emprunts obligataires non subordonnés à plus d'un an échéant dans l'année - "
"Convertibles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4211
#: model:account.account,name:l10n_be.2_a4211
#: model:account.account.template,name:l10n_be.a4211
msgid ""
"Unsubordinated debentures payable after more than one year falling due "
"within one year - Non convertible"
msgstr ""
"Emprunts obligataires non subordonnés à plus d'un an échéant dans l'année - "
"Non convertibles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1710
#: model:account.account,name:l10n_be.2_a1710
#: model:account.account.template,name:l10n_be.a1710
msgid ""
"Unsubordinated debentures with a remaining term of more than one year - "
"Convertible bonds"
msgstr "Emprunts obligataires non subordonnés à plus d'un an - Convertibles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1711
#: model:account.account,name:l10n_be.2_a1711
#: model:account.account.template,name:l10n_be.a1711
msgid ""
"Unsubordinated debentures with a remaining term of more than one year - Non "
"convertible bonds"
msgstr ""
"Emprunts obligataires non subordonnés à plus d'un an - Non convertibles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a132
#: model:account.account,name:l10n_be.2_a132
#: model:account.account.template,name:l10n_be.a132
msgid "Untaxed reserves"
msgstr "Réserves immunisées"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_taxes_deductibles
msgid "V Déductibles"
msgstr "V Déductibles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4512
#: model:account.account,name:l10n_be.2_a4512
#: model:account.account.template,name:l10n_be.a4512
msgid "VAT due - Current Account"
msgstr "TVA à payer - Compte courant"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451
#: model:account.account,name:l10n_be.2_a451
#: model:account.account.template,name:l10n_be.a451
msgid "VAT payable"
msgstr "TVA à payer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451055
#: model:account.account,name:l10n_be.2_a451055
#: model:account.account.template,name:l10n_be.a451055
msgid "VAT payable - Intracommunity acquisitions - box 55"
msgstr "TVA à payer sur opérations intracommunautaires - grille 55"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451054
#: model:account.account,name:l10n_be.2_a451054
#: model:account.account.template,name:l10n_be.a451054
msgid "VAT payable - compartment 54"
msgstr "TVA à payer - grille 54"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451063
#: model:account.account,name:l10n_be.2_a451063
#: model:account.account.template,name:l10n_be.a451063
msgid "VAT payable - credit notes - compartment 63"
msgstr "TVA à payer sur notes de crédit reçues - grille 63"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451056
#: model:account.account,name:l10n_be.2_a451056
#: model:account.account.template,name:l10n_be.a451056
msgid "VAT payable - reverse charge (cocontracting) - compartment 56"
msgstr "TVA à payer - Cocontractant - grille 56"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451057
#: model:account.account,name:l10n_be.2_a451057
#: model:account.account.template,name:l10n_be.a451057
msgid "VAT payable - reverse charge (import) - compartment 57"
msgstr "TVA à payer sur importations avec report - grille 57"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451830
#: model:account.account,name:l10n_be.2_a451830
#: model:account.account.template,name:l10n_be.a451830
msgid "VAT payable - revisions"
msgstr "TVA à payer - Régularisations"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451800
#: model:account.account,name:l10n_be.2_a451800
#: model:account.account.template,name:l10n_be.a451800
msgid "VAT payable - revisions insufficiencies"
msgstr "TVA à payer - Taxation insuffisante"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451820
#: model:account.account,name:l10n_be.2_a451820
#: model:account.account.template,name:l10n_be.a451820
msgid "VAT payable - revisions of deductions"
msgstr "TVA à payer - Révision des déductions"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a411
#: model:account.account,name:l10n_be.2_a411
#: model:account.account.template,name:l10n_be.a411
msgid "VAT recoverable"
msgstr "TVA à récupérer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4112
#: model:account.account,name:l10n_be.2_a4112
#: model:account.account.template,name:l10n_be.a4112
msgid "VAT recoverable - Current Account"
msgstr "TVA à récupérer - Compte courant"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_taxes_soldes
msgid "VI Soldes"
msgstr "VI Soldes"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_300
#: model:account.group,name:l10n_be.1_be_group_310
#: model:account.group,name:l10n_be.1_be_group_320
#: model:account.group,name:l10n_be.1_be_group_340
#: model:account.group,name:l10n_be.1_be_group_350
#: model:account.group,name:l10n_be.1_be_group_370
#: model:account.group,name:l10n_be.1_be_group_510
#: model:account.group,name:l10n_be.1_be_group_520
#: model:account.group,name:l10n_be.2_be_group_300
#: model:account.group,name:l10n_be.2_be_group_310
#: model:account.group,name:l10n_be.2_be_group_320
#: model:account.group,name:l10n_be.2_be_group_340
#: model:account.group,name:l10n_be.2_be_group_350
#: model:account.group,name:l10n_be.2_be_group_370
#: model:account.group,name:l10n_be.2_be_group_510
#: model:account.group,name:l10n_be.2_be_group_520
#: model:account.group.template,name:l10n_be.be_group_300
#: model:account.group.template,name:l10n_be.be_group_310
#: model:account.group.template,name:l10n_be.be_group_320
#: model:account.group.template,name:l10n_be.be_group_340
#: model:account.group.template,name:l10n_be.be_group_350
#: model:account.group.template,name:l10n_be.be_group_370
#: model:account.group.template,name:l10n_be.be_group_510
#: model:account.group.template,name:l10n_be.be_group_520
msgid "Valeur d'acquisition"
msgstr "Valeur d'acquisition"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_54
#: model:account.group,name:l10n_be.2_be_group_54
#: model:account.group.template,name:l10n_be.be_group_54
msgid "Valeurs échues à l'encaissement"
msgstr "Valeurs échues à l'encaissement"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_71
#: model:account.group,name:l10n_be.2_be_group_71
#: model:account.group.template,name:l10n_be.be_group_71
msgid "Variation des stocks et des commandes en cours d'exécution"
msgstr "Variation des stocks et des commandes en cours d'exécution"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_609
#: model:account.group,name:l10n_be.2_be_group_609
#: model:account.group.template,name:l10n_be.be_group_609
msgid "Variations des stocks"
msgstr "Variations des stocks"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_700
#: model:account.group,name:l10n_be.2_be_group_700
#: model:account.group.template,name:l10n_be.be_group_700
msgid "Ventes et prestations de services"
msgstr "Ventes et prestations de services"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_58
#: model:account.group,name:l10n_be.2_be_group_58
#: model:account.group.template,name:l10n_be.be_group_58
msgid "Virements internes"
msgstr "Virements internes"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a792
#: model:account.account,name:l10n_be.2_a792
#: model:account.account.template,name:l10n_be.a792
msgid "Withdrawal from allocated funds"
msgstr "Prélèvements sur les réserves"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a791
#: model:account.account,name:l10n_be.2_a791
#: model:account.account.template,name:l10n_be.a791
msgid "Withdrawal from the association or foundation funds"
msgstr "Prélèvements sur les fonds de l'association ou de la fondation"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a320
#: model:account.account,name:l10n_be.2_a320
#: model:account.account.template,name:l10n_be.a320
msgid "Work in progress - Acquisition value"
msgstr "En-cours de fabrication - Valeur d'acquisition"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a329
#: model:account.account,name:l10n_be.2_a329
#: model:account.account.template,name:l10n_be.a329
msgid "Work in progress - amounts written down"
msgstr "En-cours de fabrication - Réductions de valeur actées"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a761
#: model:account.account,name:l10n_be.2_a761
#: model:account.account.template,name:l10n_be.a761
msgid "Write-back of amounts written down financial fixed assets"
msgstr "Reprises de réductions de valeur sur immobilisations financières"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7600
#: model:account.account,name:l10n_be.2_a7600
#: model:account.account.template,name:l10n_be.a7600
msgid ""
"Write-back of depreciation and of amounts written off intangible fixed "
"assets"
msgstr ""
"Reprises d'amortissements et de réductions de valeur sur immobilisations "
"incorporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7601
#: model:account.account,name:l10n_be.2_a7601
#: model:account.account.template,name:l10n_be.a7601
msgid ""
"Write-back of depreciation and of amounts written off tangible fixed assets"
msgstr ""
"Reprises d'amortissements et de réductions de valeur sur immobilisations "
"corporelles"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7621
#: model:account.account,name:l10n_be.2_a7621
#: model:account.account.template,name:l10n_be.a7621
msgid ""
"Write-back of provisions for non-recurring financial liabilities and charges"
msgstr ""
"Reprises de provisions pour risques et charges financiers non récurrents"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7620
#: model:account.account,name:l10n_be.2_a7620
#: model:account.account.template,name:l10n_be.a7620
msgid ""
"Write-back of provisions for non-recurring operating liabilities and charges"
msgstr ""
"Reprises de provisions pour risques et charges d'exploitation non récurrents"

#. module: l10n_be
#: model:ir.model.fields,help:l10n_be.field_account_journal__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr ""
"Vous pouvez choisir différents modèles par type de référence. Le modèle par "
"défaut est la référence Odoo."

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_755
#: model:account.group,name:l10n_be.2_be_group_755
#: model:account.group.template,name:l10n_be.be_group_755
msgid "Écart de conversion des devises"
msgstr "Écart de conversion des devises"
