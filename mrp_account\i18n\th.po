# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_account
#: code:addons/mrp_account/models/mrp_production.py:0
#: model:ir.model,name:mrp_account.model_account_analytic_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_account_id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_account_id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_id
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
#, python-format
msgid "Analytic Account"
msgstr "บัญชีวิเคราะห์"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "ไลน์การวิเคราะห์"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_production__analytic_account_id
msgid ""
"Analytic account in which cost and revenue entries will take        place "
"for financial management of the manufacturing order."
msgstr ""
"บัญชีวิเคราะห์รายการต้นทุนและรายได้จะเกิดขึ้นสำหรับการจัดการทางการเงินของใบสั่งผลิต"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_bom__analytic_account_id
msgid ""
"Analytic account in which cost and revenue entries will take place for "
"financial management of the manufacturing order."
msgstr ""
"บัญชีวิเคราะห์รายการต้นทุนและรายได้จะเกิดขึ้นสำหรับการจัดการทางการเงินของใบสั่งผลิต"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_bom
msgid "Bill of Material"
msgstr "บิลวัสดุ"

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
#, python-format
msgid "Bills of Materials"
msgstr "บิลวัสดุ"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr "จำนวน BoM"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr "หมวดหมู่"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr "คำนวณราคาจาก BoM"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""
"คำนวณราคาของสินค้าโดยใช้ผลิตภัณฑ์และการปฏิบัติของบิลวัสดุที่เกี่ยวข้อง "
"สำหรับผลิตภัณฑ์ที่ผลิตเท่านั้น"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__cost_already_recorded
msgid "Cost Recorded"
msgstr "ต้นทุนที่ถูกบันทึก"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Cost"
msgstr "ต้นทุนเพิ่ม"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_production__extra_cost
msgid "Extra cost per produced unit"
msgstr "ต้นทุนเพิ่มเติมต่อหน่วยผลิต"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__costs_hour_account_id
msgid ""
"Fill this only if you want automatic analytic accounting entries on "
"production orders."
msgstr ""
"กรอกข้อมูลนี้เฉพาะในกรณีที่คุณต้องการรายการบัญชีวิเคราะห์อัตโนมัติในคำสั่งผลิต"

#. module: mrp_account
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr "ใบสั่งผลิต"

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
#, python-format
msgid "Manufacturing Orders"
msgstr "ใบสั่งผลิต"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
msgid "Manufacturing Orders Count"
msgstr "จำนวนใบสั่งผลิต"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_id
msgid "Mo Analytic Account Line"
msgstr "ไลน์บัญชีวิเคราะห์ Mo"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product"
msgstr "สินค้า"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
msgid "Production Order"
msgstr "คำสั่งผลิต"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr "แสดงการประเมินมูลค่า"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr "ย้ายสต๊อก"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_rule
msgid "Stock Rule"
msgstr "กฎสต๊อก"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter_productivity__cost_already_recorded
msgid ""
"Technical field automatically checked when a ongoing production posts "
"journal entries for its costs. This way, we can record one production's cost"
" multiple times and only consider new entries in the work centers time "
"lines."
msgstr ""
"ฟิลด์เทคนิคจะตรวจสอบโดยอัตโนมัติเมื่อการผลิตต่อเนื่องลงรายการบันทึกประจำวันสำหรับต้นทุน"
" ด้วยวิธีนี้ เราสามารถบันทึกต้นทุนการผลิตได้หลายครั้ง "
"และพิจารณาเฉพาะรายการใหม่ในไทม์ไลน์ของศูนย์งาน"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "Valuation"
msgstr "การประเมินมูลค่า"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_id
msgid "Wc Analytic Account Line"
msgstr "ไลน์บัญชีวิเคราห์ Wc"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "ศูนย์งาน"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr "คำสั่งงาน"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr "จำนวนคำสั่งงาน"

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#, python-format
msgid "Work Orders"
msgstr "คำสั่งงาน"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "บันทึกผลผลิตของศูนย์งาน"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenters"
msgstr "ศูนย์งาน"

#. module: mrp_account
#: code:addons/mrp_account/models/stock_move.py:0
#, python-format
msgid "[Raw] %s"
msgstr "[Raw] %s"

#. module: mrp_account
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
#, python-format
msgid "[WC] %s"
msgstr "[WC] %s"
