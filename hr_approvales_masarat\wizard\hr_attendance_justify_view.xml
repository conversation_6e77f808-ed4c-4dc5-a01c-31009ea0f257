<?xml version="1.0"?>
<odoo>

    <record id="adhoc_attendance_tree_view" model="ir.ui.view">
        <field name="name">hr.attendance.tree.x1000</field>
        <field name="model">hr.attendance</field>
        <field eval="100000" name="priority"/>
        <field name="arch" type="xml">
            <tree>
                <field name="employee_id" readonly="1"/>
                <field name="attendance_type_id" readonly="1"/>
                <field name="attendance_date" readonly="1"/>
                <field name="check_in" readonly="1"/>
                <field name="check_out" readonly="1"/>
                <field name="computed_latency" readonly="1" decoration-danger="computed_latency != 0"/>
                <field name="computed_latency_note" readonly="1"/>
            </tree>
        </field>
    </record>

    <record id="hr_attendance_justify_form" model="ir.ui.view">
        <field name="name">hr.attendance.justify.wizard</field>
        <field name="model">hr.attendance.justify</field>
        <field name="arch" type="xml">
            <form string="تبرير الحضور والانصراف">
                <group>
                    <group>
                        <field name="attendance_ids"
                               widget="many2many"
                               context="{'tree_view_ref': 'hr_approvales_masarat.adhoc_attendance_tree_view'}"
                               options="{'no_open': True,'no_create':True, 'no_create_edit':True, 'no_quick_create':True}">
                            <tree editable="bottom">
                                <field name="employee_id" readonly="1"/>
                                <field name="attendance_type_id" readonly="1"/>
                                <field name="attendance_date" readonly="1"/>
                                <field name="check_in" readonly="1"/>
                                <field name="check_out" readonly="1"/>
                                <field name="computed_latency" readonly="1" decoration-danger="computed_latency != 0"/>
                                <field name="computed_latency_note" readonly="1"/>
                            </tree>
                        </field>
                    </group>
                </group>
                <footer>
                    <button name="make_justify" string="تبرير" type="object" class="btn-primary"/>
                    <button string="الغاء الامر" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_hr_attendance_justify_view" model="ir.actions.act_window">
        <field name="name">تبرير الحضور والانصراف</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.attendance.justify</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_attendance_justify_form"/>
        <field name="target">new</field>
    </record>

    <menuitem
            id="menu_hr_attendance_admin_masarat"
            name="ادارة الحضور والانصراف"
            parent="hr_attendance.menu_hr_attendance_root"
            groups="hr_approvales_masarat.group_hr_approvales_masarat"
            sequence="300"/>


    <menuitem
            id="menu_hr_attendance_justify"
            name="تبرير حضور والانصراف"
            parent="hr_approvales_masarat.menu_hr_attendance_admin_masarat"
            groups="hr_approvales_masarat.group_hr_approvales_masarat"
            action="action_hr_attendance_justify_view"
            sequence="1"/>

</odoo>