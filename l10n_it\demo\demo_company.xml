<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_it" model="res.partner">
        <field name="name">IT Company</field>
        <field name="vat">*************</field>
        <field name="street">Almenweg</field>
        <field name="city">Kastelbell-Tschars - Castelbello-Ciardes</field>
        <field name="country_id" ref="base.it"/>
        <field name="state_id" ref="base.state_it_vt"/>
        <field name="zip">39020</field>
        <field name="phone">+39 ************</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.itexample.com</field>
    </record>

    <record id="demo_company_it" model="res.company">
        <field name="name">IT Company</field>
        <field name="partner_id" ref="partner_demo_company_it"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_it')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_it.demo_company_it'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_it.l10n_it_chart_template_generic')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_it.demo_company_it')"/>
    </function>
</odoo>
